
package br.com.pacto.webservice.jaxws;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;
import br.com.pacto.bean.professor.ProfessorSintetico;

@XmlRootElement(name = "sincronizarProfessor", namespace = "http://webservice.pacto.com.br/")
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "sincronizarProfessor", namespace = "http://webservice.pacto.com.br/", propOrder = {
    "key",
    "professor",
    "empresa"
})
public class SincronizarProfessor {

    @XmlElement(name = "key", namespace = "")
    private String key;
    @XmlElement(name = "professor", namespace = "")
    private Professor<PERSON>int<PERSON><PERSON> professor;
    @XmlElement(name = "empresa", namespace = "")
    private Integer empresa;

    /**
     * 
     * @return
     *     returns String
     */
    public String getKey() {
        return this.key;
    }

    /**
     * 
     * @param key
     *     the value for the key property
     */
    public void setKey(String key) {
        this.key = key;
    }

    /**
     * 
     * @return
     *     returns ProfessorSintetico
     */
    public ProfessorSintetico getProfessor() {
        return this.professor;
    }

    /**
     * 
     * @param professor
     *     the value for the professor property
     */
    public void setProfessor(ProfessorSintetico professor) {
        this.professor = professor;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }
    
    

}
