
package br.com.pacto.webservice.jaxws;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;
import servicos.integracao.zw.beans.ClienteZW;

@XmlRootElement(name = "sincronizarAluno", namespace = "http://webservice.pacto.com.br/")
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "sincronizarAluno", namespace = "http://webservice.pacto.com.br/", propOrder = {
    "key",
    "aluno"
})
public class SincronizarAluno {

    @XmlElement(name = "key", namespace = "")
    private String key;
    @XmlElement(name = "aluno", namespace = "")
    private ClienteZW aluno;

    /**
     * 
     * @return
     *     returns String
     */
    public String getKey() {
        return this.key;
    }

    /**
     * 
     * @param key
     *     the value for the key property
     */
    public void setKey(String key) {
        this.key = key;
    }

    /**
     * 
     * @return
     *     returns ClienteZW
     */
    public ClienteZW getAluno() {
        return this.aluno;
    }

    /**
     * 
     * @param aluno
     *     the value for the aluno property
     */
    public void setAluno(ClienteZW aluno) {
        this.aluno = aluno;
    }

}
