
package br.com.pacto.webservice.jaxws;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;
import servicos.integracao.zw.beans.UsuarioZW;

@XmlRootElement(name = "sincronizarUsuario", namespace = "http://webservice.pacto.com.br/")
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "sincronizarUsuario", namespace = "http://webservice.pacto.com.br/", propOrder = {
    "key",
    "usuario"
})
public class SincronizarUsuario {

    @XmlElement(name = "key", namespace = "")
    private String key;
    @XmlElement(name = "usuario", namespace = "")
    private UsuarioZW usuario;

    /**
     * 
     * @return
     *     returns String
     */
    public String getKey() {
        return this.key;
    }

    /**
     * 
     * @param key
     *     the value for the key property
     */
    public void setKey(String key) {
        this.key = key;
    }

    /**
     * 
     * @return
     *     returns UsuarioZW
     */
    public UsuarioZW getUsuario() {
        return this.usuario;
    }

    /**
     * 
     * @param usuario
     *     the value for the usuario property
     */
    public void setUsuario(UsuarioZW usuario) {
        this.usuario = usuario;
    }

}
