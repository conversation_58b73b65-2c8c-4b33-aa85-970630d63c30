
package br.com.pacto.webservice.jaxws;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;
import br.com.pacto.bean.cliente.ClienteSintetico;

@XmlRootElement(name = "atualizarAluno", namespace = "http://webservice.pacto.com.br/")
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "atualizarAluno", namespace = "http://webservice.pacto.com.br/", propOrder = {
    "key",
    "aluno"
})
public class AtualizarAluno {

    @XmlElement(name = "key", namespace = "")
    private String key;
    @XmlElement(name = "aluno", namespace = "")
    private ClienteSintetico aluno;

    /**
     * 
     * @return
     *     returns String
     */
    public String getKey() {
        return this.key;
    }

    /**
     * 
     * @param key
     *     the value for the key property
     */
    public void setKey(String key) {
        this.key = key;
    }

    /**
     * 
     * @return
     *     returns ClienteSintetico
     */
    public ClienteSintetico getAluno() {
        return this.aluno;
    }

    /**
     * 
     * @param aluno
     *     the value for the aluno property
     */
    public void setAluno(ClienteSintetico aluno) {
        this.aluno = aluno;
    }

}
