package br.com.pacto.swagger;

import com.google.common.base.Predicates;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.AuthorizationScope;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;
import springfox.documentation.service.ApiKey;
import springfox.documentation.service.SecurityReference;
import springfox.documentation.service.SecurityScheme;
import springfox.documentation.spi.service.contexts.SecurityContext;
import springfox.documentation.service.Parameter;
import springfox.documentation.builders.ParameterBuilder;
import springfox.documentation.schema.ModelRef;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

@Configuration
@EnableSwagger2
public class SwaggerConfig {
    @Bean
    public Docket api() {
        List<Parameter> globalHeaders = Arrays.asList(
                new ParameterBuilder()
                        .name("chave")
                        .description("Chave da empresa")
                        .modelRef(new ModelRef("string"))
                        .parameterType("header")
                        .required(true)
                        .build(),
                new ParameterBuilder()
                        .name("empresaId")
                        .description("Código da empresa")
                        .modelRef(new ModelRef("string"))
                        .parameterType("header")
                        .required(true)
                        .build()
        );

        return new Docket(DocumentationType.SWAGGER_2)
                .securitySchemes(Collections.singletonList(apiKey()))
                .securityContexts(Collections.singletonList(securityContext()))
                .select()
                .apis(RequestHandlerSelectors.basePackage("br.com.pacto.controller.json"))
                .paths( Predicates.or(
                        PathSelectors.ant("/psec/alunos/**"),
                        PathSelectors.ant("/psec/perfil/**"),
                        PathSelectors.ant("/psec/agenda-cards/**")
                ))
                .build()
                .apiInfo(new ApiInfoBuilder()
                        .title("TreinoWeb")
                        .description("Documentação da API do Treino")
                        .version("1.0")
                        .build())
                .globalOperationParameters(globalHeaders);
    }

    private ApiKey apiKey() {
        return new ApiKey("Authorization", "Authorization", "header");
    }

    private SecurityContext securityContext() {
        return SecurityContext.builder()
                .securityReferences(defaultAuth())
                .forPaths(PathSelectors.any())
                .build();
    }

    private List<SecurityReference> defaultAuth() {
        AuthorizationScope authorizationScope =
                new AuthorizationScope("global", "Acesso total à API");
        AuthorizationScope[] authorizationScopes = new AuthorizationScope[]{authorizationScope};
        return Arrays.asList(new SecurityReference("Authorization", authorizationScopes));
    }
}