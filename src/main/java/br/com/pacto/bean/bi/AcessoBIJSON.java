/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.bi;

import br.com.pacto.controller.json.base.SuperJSON;
import br.com.pacto.objeto.Uteis;

/**
 *
 * <AUTHOR>
 */
public class AcessoBIJSON extends SuperJSON{
    private Integer matricula;
    private String nome;
    private Integer codigopessoa;
    private String dthrentrada;

    public Integer getMatricula() {
        return matricula;
    }

    public void setMatricula(Integer matricula) {
        this.matricula = matricula;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Integer getCodigopessoa() {
        return codigopessoa;
    }

    public void setCodigopessoa(Integer codigoPessoa) {
        this.codigopessoa = codigoPessoa;
    }

    public String getDthrentrada() {
        return dthrentrada;
    }

    public void setDthrentrada(String dthrentrada) {
        this.dthrentrada = dthrentrada;
    }
    
    public String getNomeAbreviado() {
        if (nome != null) {
            return Uteis.getNomeAbreviandoSomentePrimeiroSobrenome(nome);
        } else {
            return "";
        }
    }
    
}
