package br.com.pacto.bean.bi;

import br.com.pacto.bean.professor.ProfessorSintetico;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.ManyToOne;
import java.io.Serializable;

@Entity
public class ProfessorRankingModalidade implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    @ManyToOne
    private GeracaoRankingProfessores geracao;
    @ManyToOne
    private ProfessorSintetico professor;
    private Integer codigoModalidade;

    public Integer getCodigo() { return codigo; }

    public void setCodigo(Integer codigo) { this.codigo = codigo; }

    public GeracaoRankingProfessores getGeracao() { return geracao; }

    public void setGeracao(GeracaoRankingProfessores geracao) { this.geracao = geracao; }

    public ProfessorSintetico getProfessor() { return professor; }

    public void setProfessor(Professor<PERSON><PERSON><PERSON><PERSON> professor) { this.professor = professor; }

    public Integer getCodigoModalidade() { return codigoModalidade; }

    public void setCodigoModalidade(Integer codigoModalidade) { this.codigoModalidade = codigoModalidade; }
}
