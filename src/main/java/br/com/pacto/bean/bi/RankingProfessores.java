package br.com.pacto.bean.bi;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.io.Serializable;
import java.util.Date;

@Entity
public class RankingProfessores implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private Integer dia;
    private Integer mes;
    private Integer ano;
    private Integer codigoProfessor;
    private Integer empresa = 0;
    private Integer totalAlunosCancelados = 0;
    private Date fimProcessamento;
    private Integer execucoesApp;
    private Integer execucoesFichaImpressa;
    private Integer execucoesProfessor;
    private Integer execucoesFichaImpressaConcluida;
    @Temporal(TemporalType.TIMESTAMP)
    private Date inicioProcessamento;
    @Temporal(TemporalType.TIMESTAMP)

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getDia() {
        return dia;
    }

    public void setDia(Integer dia) {
        this.dia = dia;
    }

    public Integer getMes() {
        return mes;
    }

    public void setMes(Integer mes) {
        this.mes = mes;
    }

    public Integer getAno() {
        return ano;
    }

    public void setAno(Integer ano) {
        this.ano = ano;
    }

    public Integer getCodigoProfessor() {
        return codigoProfessor;
    }

    public void setCodigoProfessor(Integer codigoProfessor) {
        this.codigoProfessor = codigoProfessor;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public Integer getTotalAlunosCancelados() {
        return totalAlunosCancelados;
    }

    public void setTotalAlunosCancelados(Integer totalAlunosCancelados) {
        this.totalAlunosCancelados = totalAlunosCancelados;
    }

    public Date getFimProcessamento() {
        return fimProcessamento;
    }

    public void setFimProcessamento(Date fimProcessamento) {
        this.fimProcessamento = fimProcessamento;
    }

    public Integer getExecucoesApp() {
        return execucoesApp;
    }

    public void setExecucoesApp(Integer execucoesApp) {
        this.execucoesApp = execucoesApp;
    }

    public Integer getExecucoesFichaImpressa() {
        return execucoesFichaImpressa;
    }

    public void setExecucoesFichaImpressa(Integer execucoesFichaImpressa) {
        this.execucoesFichaImpressa = execucoesFichaImpressa;
    }

    public Integer getExecucoesProfessor() {
        return execucoesProfessor;
    }

    public void setExecucoesProfessor(Integer execucoesProfessor) {
        this.execucoesProfessor = execucoesProfessor;
    }

    public Integer getExecucoesFichaImpressaConcluida() {
        return execucoesFichaImpressaConcluida;
    }

    public void setExecucoesFichaImpressaConcluida(Integer execucoesFichaImpressaConcluida) {
        this.execucoesFichaImpressaConcluida = execucoesFichaImpressaConcluida;
    }

    public Date getInicioProcessamento() {
        return inicioProcessamento;
    }

    public void setInicioProcessamento(Date inicioProcessamento) {
        this.inicioProcessamento = inicioProcessamento;
    }
}
