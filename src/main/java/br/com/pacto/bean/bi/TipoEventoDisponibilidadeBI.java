/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.bi;

import br.com.pacto.controller.json.base.SuperJSON;
import br.com.pacto.enumerador.agenda.TipoAgendamentoEnum;
import java.io.Serializable;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;

/**
 *
 * <AUTHOR>
 */
@Entity
public class TipoEventoDisponibilidadeBI  implements Serializable{
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private Integer mes;
    private Integer ano;
    private Integer professor;
    private Integer empresa;
    @Enumerated(EnumType.ORDINAL)
    private TipoAgendamentoEnum tipo;
    private Long disponibilidade = 0l;
    private Long executaram = 0l;
    private Long cancelaram = 0l;
    private Long faltaram = 0l;
    private Long aguardandoConfirmacao = 0l;
    private Long confirmado = 0l;
    private Long reagendado = 0L;

    public TipoEventoDisponibilidadeBI() {
        
    }
    public TipoEventoDisponibilidadeBI(DashboardBI dash, TipoAgendamentoEnum tipo) {
        this.mes = dash.getMes();
        this.ano = dash.getAno();
        this.professor = dash.getCodigoProfessor();
        this.tipo = tipo;
        this.empresa = dash.getEmpresa();
    }
    
    public TipoAgendamentoEnum getTipo() {
        return tipo;
    }

    public void setTipo(TipoAgendamentoEnum tipo) {
        this.tipo = tipo;
    }

    public Long getDisponibilidade() {
        return disponibilidade;
    }

    public void setDisponibilidade(Long disponibilidade) {
        this.disponibilidade = disponibilidade;
    }

    public Long getExecutaram() {
        return executaram;
    }

    public void setExecutaram(Long executaram) {
        this.executaram = executaram;
    }

    public Long getCancelaram() {
        return cancelaram;
    }

    public void setCancelaram(Long cancelaram) {
        this.cancelaram = cancelaram;
    }

    public Long getFaltaram() {
        return faltaram;
    }

    public void setFaltaram(Long faltaram) {
        this.faltaram = faltaram;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getMes() {
        return mes;
    }

    public void setMes(Integer mes) {
        this.mes = mes;
    }

    public Integer getAno() {
        return ano;
    }

    public void setAno(Integer ano) {
        this.ano = ano;
    }

    public Integer getProfessor() {
        return professor;
    }

    public void setProfessor(Integer professor) {
        this.professor = professor;
    }
    
    public Integer getHorasDisponibilidade(){
        try {
            return disponibilidade.intValue()/60;
        } catch (Exception e) {
            return 0;
        }
    }
    
    public Integer getHorasExecutaram(){
        try {
            return executaram.intValue()/60;
        } catch (Exception e) {
            return 0;
        }
    }
    public Integer getHorasConfirmado(){
        try {
            return confirmado.intValue()/60;
        } catch (Exception e) {
            return 0;
        }
    }
    public Integer getHorasAguardando(){
        try {
            return aguardandoConfirmacao.intValue()/60;
        } catch (Exception e) {
            return 0;
        }
    }
    
    public Integer getHorasCancelaram(){
        try {
            return cancelaram.intValue()/60;
        } catch (Exception e) {
            return 0;
        }
    }
    
    public Integer getHorasFaltaram(){
        try {
            return faltaram.intValue()/60;
        } catch (Exception e) {
            return 0;
        }
    }

    public Long getAguardandoConfirmacao() {
        return aguardandoConfirmacao;
    }

    public void setAguardandoConfirmacao(Long aguardandoConfirmacao) {
        this.aguardandoConfirmacao = aguardandoConfirmacao;
    }

    public Long getConfirmado() {
        return confirmado;
    }

    public void setConfirmado(Long confirmado) {
        this.confirmado = confirmado;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public Long getReagendado() {
        return reagendado;
    }

    public void setReagendado(Long reagendado) {
        this.reagendado = reagendado;
    }
}
