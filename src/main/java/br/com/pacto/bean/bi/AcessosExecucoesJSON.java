/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.bi;

import br.com.pacto.controller.json.base.SuperJSON;
import br.com.pacto.objeto.Uteis;

/**
 *
 * <AUTHOR>
 */
public class AcessosExecucoesJSON extends SuperJSON{
    private String dia = "";
    private Integer smartphone = 0;
    private Integer acessotreino = 0;
    private Integer execucoes = 0;
    private Integer acessos = 0;

    public AcessosExecucoesJSON() {
    }

    public AcessosExecucoesJSON(AcessosExecucoesBI a) {
        dia = Uteis.getDataAplicandoFormatacao(a.getDia(), "yyyy-MM-dd");
        smartphone = a.getSmartphone();
        acessos = a.getAcessos();
        acessotreino = a.getAcessotreino();
        execucoes = a.getExecucoes();
    }
    
    

    public String getDia() {
        return dia;
    }

    public void setDia(String dia) {
        this.dia = dia;
    }

    public Integer getSmartphone() {
        return smartphone;
    }

    public void setSmartphone(Integer smartphone) {
        this.smartphone = smartphone;
    }

    public Integer getAcessotreino() {
        return acessotreino;
    }

    public void setAcessotreino(Integer acessotreino) {
        this.acessotreino = acessotreino;
    }

    public Integer getExecucoes() {
        return execucoes;
    }

    public void setExecucoes(Integer execucoes) {
        this.execucoes = execucoes;
    }

    public Integer getAcessos() {
        return acessos;
    }

    public void setAcessos(Integer acessos) {
        this.acessos = acessos;
    }
    
    
    
    
}
