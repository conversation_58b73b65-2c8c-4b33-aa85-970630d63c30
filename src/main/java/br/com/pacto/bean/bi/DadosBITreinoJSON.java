/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.bi;

import br.com.pacto.controller.json.base.SuperJSON;

/**
 *
 * <AUTHOR>
 */
public class DadosBITreinoJSON extends SuperJSON {
    private Integer alunosAtivosForaTreino = 0;
    private Integer mediaPermanenciaCarteira = 0;
    private Integer maiorPermanencia = 0;
    private String nomeClienteMaiorPermanencia;
    private String matriculaClienteMaiorPermanencia;
    private Integer menorPermanencia = 0;
    private String nomeClienteMenorPermanencia;
    private String matriculaClienteMenorPermanencia;
    private Integer novosNaCarteira = 0;
    private Integer novosNaCarteiraTrocaram = 0;
    private Integer novosNaCarteiraNovos = 0;
    private Integer trocasCarteira = 0;
    private Integer aVencer = 0;
    private Integer renovados = 0;
    private Integer naoRenovados = 0;
    private Integer alunosAcessaram = 0;

    private String codsRenovados;
    private String codsNaoRenovados;
    private String codsAVencer;
    private String codsNovosCarteira;
    private String codsNovosCarteiraNovos;
    private String codsNovosCarteiraTrocaram;
    private String codsTrocasCarteira;
    private String codsAlunosAcessaram;
    private String codsAlunosAtivosForaTreino;

    public Integer getAlunosAtivosForaTreino() {
        return alunosAtivosForaTreino;
    }

    public void setAlunosAtivosForaTreino(Integer alunosAtivosForaTreino) {
        this.alunosAtivosForaTreino = alunosAtivosForaTreino;
    }

    public Integer getMediaPermanenciaCarteira() {
        return mediaPermanenciaCarteira;
    }

    public void setMediaPermanenciaCarteira(Integer mediaPermanenciaCarteira) {
        this.mediaPermanenciaCarteira = mediaPermanenciaCarteira;
    }

    public Integer getMaiorPermanencia() {
        return maiorPermanencia;
    }

    public void setMaiorPermanencia(Integer maiorPermanencia) {
        this.maiorPermanencia = maiorPermanencia;
    }

    public String getNomeClienteMaiorPermanencia() {
        return nomeClienteMaiorPermanencia;
    }

    public void setNomeClienteMaiorPermanencia(String nomeClienteMaiorPermanencia) {
        this.nomeClienteMaiorPermanencia = nomeClienteMaiorPermanencia;
    }

    public String getMatriculaClienteMaiorPermanencia() {
        return matriculaClienteMaiorPermanencia;
    }

    public void setMatriculaClienteMaiorPermanencia(String matriculaClienteMaiorPermanencia) {
        this.matriculaClienteMaiorPermanencia = matriculaClienteMaiorPermanencia;
    }

    public Integer getMenorPermanencia() {
        return menorPermanencia;
    }

    public void setMenorPermanencia(Integer menorPermanencia) {
        this.menorPermanencia = menorPermanencia;
    }

    public String getNomeClienteMenorPermanencia() {
        return nomeClienteMenorPermanencia;
    }

    public void setNomeClienteMenorPermanencia(String nomeClienteMenorPermanencia) {
        this.nomeClienteMenorPermanencia = nomeClienteMenorPermanencia;
    }

    public String getMatriculaClienteMenorPermanencia() {
        return matriculaClienteMenorPermanencia;
    }

    public void setMatriculaClienteMenorPermanencia(String matriculaClienteMenorPermanencia) {
        this.matriculaClienteMenorPermanencia = matriculaClienteMenorPermanencia;
    }

    public Integer getNovosNaCarteira() {
        return novosNaCarteira;
    }

    public void setNovosNaCarteira(Integer novosNaCarteira) {
        this.novosNaCarteira = novosNaCarteira;
    }

    public Integer getTrocasCarteira() {
        return trocasCarteira;
    }

    public void setTrocasCarteira(Integer trocasCarteira) {
        this.trocasCarteira = trocasCarteira;
    }

    public Integer getaVencer() {
        return aVencer;
    }

    public void setaVencer(Integer aVencer) {
        this.aVencer = aVencer;
    }

    public Integer getRenovados() {
        return renovados;
    }

    public void setRenovados(Integer renovados) {
        this.renovados = renovados;
    }

    public Integer getNaoRenovados() {
        return naoRenovados;
    }

    public void setNaoRenovados(Integer naoRenovados) {
        this.naoRenovados = naoRenovados;
    }

    public String getCodsRenovados() {
        return codsRenovados;
    }

    public void setCodsRenovados(String codsRenovados) {
        this.codsRenovados = codsRenovados;
    }

    public String getCodsNaoRenovados() {
        return codsNaoRenovados;
    }

    public void setCodsNaoRenovados(String codsNaoRenovados) {
        this.codsNaoRenovados = codsNaoRenovados;
    }

    public String getCodsAVencer() {
        return codsAVencer;
    }

    public void setCodsAVencer(String codsAVencer) {
        this.codsAVencer = codsAVencer;
    }

    public String getCodsNovosCarteira() {
        return codsNovosCarteira;
    }

    public void setCodsNovosCarteira(String codsNovosCarteira) {
        this.codsNovosCarteira = codsNovosCarteira;
    }

    public String getCodsTrocasCarteira() {
        return codsTrocasCarteira;
    }

    public void setCodsTrocasCarteira(String codsTrocasCarteira) {
        this.codsTrocasCarteira = codsTrocasCarteira;
    }

    public Integer getNovosNaCarteiraTrocaram() {
        return novosNaCarteiraTrocaram;
    }

    public void setNovosNaCarteiraTrocaram(Integer novosNaCarteiraTrocaram) {
        this.novosNaCarteiraTrocaram = novosNaCarteiraTrocaram;
    }

    public Integer getNovosNaCarteiraNovos() {
        return novosNaCarteiraNovos;
    }

    public void setNovosNaCarteiraNovos(Integer novosNaCarteiraNovos) {
        this.novosNaCarteiraNovos = novosNaCarteiraNovos;
    }

    public String getCodsNovosCarteiraNovos() {
        return codsNovosCarteiraNovos;
    }

    public void setCodsNovosCarteiraNovos(String codsNovosCarteiraNovos) {
        this.codsNovosCarteiraNovos = codsNovosCarteiraNovos;
    }

    public String getCodsNovosCarteiraTrocaram() {
        return codsNovosCarteiraTrocaram;
    }

    public void setCodsNovosCarteiraTrocaram(String codsNovosCarteiraTrocaram) {
        this.codsNovosCarteiraTrocaram = codsNovosCarteiraTrocaram;
    }

    public Integer getAlunosAcessaram() {
        return alunosAcessaram;
    }

    public void setAlunosAcessaram(Integer alunosAcessaram) {
        this.alunosAcessaram = alunosAcessaram;
    }

    public String getCodsAlunosAcessaram() {
        return codsAlunosAcessaram;
    }

    public void setCodsAlunosAcessaram(String codsAlunosAcessaram) {
        this.codsAlunosAcessaram = codsAlunosAcessaram;
    }

    public String getCodsAlunosAtivosForaTreino() {
        return codsAlunosAtivosForaTreino;
    }

    public void setCodsAlunosAtivosForaTreino(String codsAlunosAtivosForaTreino) {
        this.codsAlunosAtivosForaTreino = codsAlunosAtivosForaTreino;
    }
}
