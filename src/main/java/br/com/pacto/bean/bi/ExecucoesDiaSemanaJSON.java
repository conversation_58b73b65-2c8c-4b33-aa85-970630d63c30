/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.bi;

import br.com.pacto.controller.json.base.SuperJSON;

/**
 *
 * <AUTHOR>
 */
public class ExecucoesDiaSemanaJSON extends SuperJSON {
    
    private String label = "";
    private Integer manha = 0;
    private Integer tarde = 0;
    private Integer noite = 0;
    private Integer total = 0;

    public ExecucoesDiaSemanaJSON(){
        
    }
    public ExecucoesDiaSemanaJSON(DiasSemanaDashboardBI diasBI){
        label = diasBI.getDiaSemana().getMin();
        manha = diasBI.getMediaManha();
        tarde = diasBI.getMediaTarde();
        noite = diasBI.getMediaNoite();
        total = manha + tarde + noite;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public Integer getManha() {
        return manha;
    }

    public void setManha(Integer manha) {
        this.manha = manha;
    }

    public Integer getTarde() {
        return tarde;
    }

    public void setTarde(Integer tarde) {
        this.tarde = tarde;
    }

    public Integer getNoite() {
        return noite;
    }

    public void setNoite(Integer noite) {
        this.noite = noite;
    }

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }
    
}
