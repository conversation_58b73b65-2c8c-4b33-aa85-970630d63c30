/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.bi;

import br.com.pacto.objeto.Uteis;
import java.io.Serializable;
import java.util.Date;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

/**
 *
 * <AUTHOR>
 */
@Entity
public class AcessosExecucoesBI implements Serializable{
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private Integer mes;
    private Integer ano;
    private Integer professor;
    private Integer empresa;
    @Temporal(TemporalType.DATE)
    private Date dia;
    private Integer smartphone = 0;
    private Integer acessotreino = 0;
    private Integer execucoes = 0;
    private Integer acessos = 0;

    public AcessosExecucoesBI() {
    }
    
    public AcessosExecucoesBI(AcessosExecucoesJSON json, DashboardBI dash) throws Exception {
        dia = Uteis.getDate(json.getDia(), "yyyy-MM-dd");
        acessotreino = json.getAcessotreino();
        acessos = json.getAcessos();
        mes = Uteis.getMesData(dia);
        ano = Uteis.getAnoData(dia);
        professor = dash.getCodigoProfessor();
        empresa = dash.getEmpresa();
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getMes() {
        return mes;
    }

    public void setMes(Integer mes) {
        this.mes = mes;
    }

    public Integer getAno() {
        return ano;
    }

    public void setAno(Integer ano) {
        this.ano = ano;
    }

    public Integer getProfessor() {
        return professor;
    }

    public void setProfessor(Integer professor) {
        this.professor = professor;
    }

    public Date getDia() {
        return dia;
    }

    public void setDia(Date dia) {
        this.dia = dia;
    }


    public Integer getSmartphone() {
        return smartphone;
    }

    public void setSmartphone(Integer smartphone) {
        this.smartphone = smartphone;
    }

    public Integer getAcessotreino() {
        return acessotreino;
    }

    public void setAcessotreino(Integer acessotreino) {
        this.acessotreino = acessotreino;
    }

    public Integer getExecucoes() {
        return execucoes;
    }

    public void setExecucoes(Integer execucoes) {
        this.execucoes = execucoes;
    }

    public Integer getAcessos() {
        return acessos;
    }

    public void setAcessos(Integer acessos) {
        this.acessos = acessos;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }
    
    
}
