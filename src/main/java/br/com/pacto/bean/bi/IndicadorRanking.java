package br.com.pacto.bean.bi;

import javax.persistence.*;

@Entity
public class IndicadorRanking {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    @Enumerated(EnumType.ORDINAL)
    private IndicadorDashboardEnum indicador;
    private String label;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public IndicadorDashboardEnum getIndicador() {
        return indicador;
    }

    public void setIndicador(IndicadorDashboardEnum indicador) {
        this.indicador = indicador;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }
}
