/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.bi;

import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Transient;

/**
 *
 * <AUTHOR>
 */
@Entity
public class ConfiguracaoRankingProfessores {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    @Enumerated(EnumType.ORDINAL)
    private IndicadorDashboardEnum indicador;
    private Double peso = 1.0; 
    private Boolean positivo = Boolean.TRUE;
    private Integer empresa;
    private Boolean ativo;
    @Transient
    private String nome;
    
    public String getLabel(){
        return "RANKING_"+indicador.name();
    }

    public ConfiguracaoRankingProfessores() {
    }

    public ConfiguracaoRankingProfessores(IndicadorDashboardEnum indicador, Integer empresa) {
        this.indicador = indicador;
        this.empresa = empresa;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public IndicadorDashboardEnum getIndicador() {
        return indicador;
    }

    public void setIndicador(IndicadorDashboardEnum indicador) {
        this.indicador = indicador;
    }

    public Double getPeso() {
        return peso;
    }

    public String getPesoStr() {
        return peso == null ? "0" : peso % 1.0 == 0.0 ? String.valueOf(peso.intValue()) : peso.toString().replace(".", ",");
    }
    
    public void setPesoStr(String str) {
        peso = Double.valueOf(str);
    }

    public Integer getPesoInt() {
        return peso.intValue();
    }

    public void setPeso(Double peso) {
        this.peso = peso;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }
    
    public Boolean getPositivo() {
        if(positivo == null){
            positivo = Boolean.TRUE;
        }
        return positivo;
    }
    
    public void setPositivo(Boolean positivo) {
        this.positivo = positivo;
}

    public Boolean getAtivo() {
        if(ativo == null){
            ativo = true;
        }
        return ativo;
    }

    public void setAtivo(Boolean ativo) {
        this.ativo = ativo;
    }
}
