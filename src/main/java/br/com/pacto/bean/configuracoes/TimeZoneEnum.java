/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.configuracoes;

import java.util.ArrayList;
import java.util.List;
import javax.faces.model.SelectItem;

/**
 *
 * <AUTHOR>
 */
public enum TimeZoneEnum {
    Brazil_East("Brazil/East", "BRT", "Brasilia Time","GMT-3"),//padrão
    Brazil_Acre("Brazil/Acre", "AMT", "Amazon Time","GMT-5"),
    Brazil_DeNoronha("Brazil/DeNoronha", "FNT", "Fernando de Noronha Time","GMT-2"),
    Brazil_West("Brazil/West", "AMT", "Amazon Time","GMT-4"),
    Brazil_GTM_3("GMT-3", "GMT-3", "GMT-3","GMT-3"),
    Brazil_GTM_4("GMT-4", "GMT-4", "GMT-4","GMT-4");
    private String id;
    private String sigla;
    private String descricao;
    private String timeZoneGmt;

    private TimeZoneEnum(String id, String sigla, String descricao,String timeZoneGmt) {
        this.id = id;
        this.sigla = sigla;
        this.descricao = descricao;
        this.timeZoneGmt = timeZoneGmt;
    }

    public static List<SelectItem> getSelectListTimeZone() {
        List<SelectItem> temp = new ArrayList<SelectItem>();
        for (int i = 0; i < TimeZoneEnum.values().length; i++) {
            TimeZoneEnum obj = TimeZoneEnum.values()[i];
            temp.add(new SelectItem(obj.getId(), obj.getId() + " - " + obj.getDescricao()));
        }
        return temp;
    }

    public static TimeZoneEnum obterPorId(String id){
        for(TimeZoneEnum obj : values()){
            if(obj.getId().equals(id)){
                return obj;
            }
        }
        return TimeZoneEnum.Brazil_East;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getSigla() {
        return sigla;
    }

    public void setSigla(String sigla) {
        this.sigla = sigla;
    }

    public String getTimeZoneGmt() {
        return timeZoneGmt;
    }

    public void setTimeZoneGmt(String timeZoneGmt) {
        this.timeZoneGmt = timeZoneGmt;
    }
}
