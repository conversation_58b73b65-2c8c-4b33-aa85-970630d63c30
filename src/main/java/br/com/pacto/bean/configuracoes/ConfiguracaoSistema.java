/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.pacto.bean.configuracoes;

import br.com.pacto.bean.notificacao.TipoNotificacaoEnum;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import javax.faces.model.SelectItem;
import javax.persistence.*;


/**
 *
 * <AUTHOR>
 */
@Entity
@Table
public class ConfiguracaoSistema implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    @Enumerated(EnumType.STRING)
    private ConfiguracoesEnum configuracao;
    @Enumerated(EnumType.ORDINAL)
    private TipoNotificacaoEnum tipoNotificacao;
    @Column(columnDefinition = "text", length = 9999)
    private String valor;
    @Transient
    private String valorMinutos;
    @Transient
    private List<ConfiguracaoSistema> configsFilhas;

    public ConfiguracaoSistema(){
    }
    
    public String getNr(){
        try {
            return String.valueOf(configuracao.ordinal());
        } catch (Exception e) {
            return "";
        }
    }

    public boolean isApresentar(){
        try {
            return configuracao.isAparecer();
        } catch (Exception e) {
            return true;
        }
    }
    
    public List<SelectItem> getListaItens(){
        return (configuracao != null 
                && configuracao.getItens() != null
                && !configuracao.getItens().isEmpty()) ? configuracao.getItens() : new ArrayList<SelectItem>();
    }
    
    public Boolean getValorAsBoolean(){
        try {
            return Boolean.valueOf(valor);
        } catch (Exception e) {
            return null;
        }
    }
    public Double getValorAsDouble(){
        try {
            return Double.valueOf(valor);
        } catch (Exception e) {
            return 0.0;
        }
    }
    public Integer getValorAsInteger(){
        try {
            return Integer.valueOf(valor);
        } catch (Exception e) {
            return 0;
        }
    }
    
    public void setValorAsBoolean(Boolean valor){
        setValor(valor.toString());
    }
    public void setValorAsDouble(Double valor){
        setValor(valor.toString());
    }
    public void setValorAsInteger(Integer valor){
        try {
            valor = valor == null ? 0 : valor;
            setValor(valor.toString());
        }catch (Exception e){
            setValor("");
        }

    }

    public String getNome(){
        try {
            return configuracao.name();
        } catch (Exception e) {
            return "";
        }
    }

    public String getNomeNotificacao(){
        try {
            return tipoNotificacao.name();
        } catch (Exception e) {
            return "";
        }
    }
    
    public boolean getTpString() {
        try {
            return configuracao.getTipo().equals(ConfiguracaoTipoEnum.STRING);
        } catch (Exception e) {
            return false;
        }

    }

    public boolean getTpInteger() {
        try {
            return configuracao.getTipo().equals(ConfiguracaoTipoEnum.INTEGER);
        } catch (Exception e) {
            return false;
        }
    }

    public boolean getTpDouble() {
        try {
            return configuracao.getTipo().equals(ConfiguracaoTipoEnum.DOUBLE);
        } catch (Exception e) {
            return false;
        }
    }

    public boolean getExibirOpcoesDoGrupo() {
        try {
            return configuracao.ehGrupoDeConfigs() && Boolean.valueOf(valor);
        } catch (Exception e) {
            return false;
        }
    }

    public List<ConfiguracaoSistema> getConfigsFilhas() {
        return this.configsFilhas;
    }

    public void setConfigsFilhas(List<ConfiguracaoSistema> configsFilhas) {
        this.configsFilhas = configsFilhas;
    }

    public boolean getTpBoolean() {
        try {
            return configuracao.getTipo().equals(ConfiguracaoTipoEnum.BOOLEAN) ||
                    configuracao.getTipo().equals(ConfiguracaoTipoEnum.GROUP) ;
        } catch (Exception e) {
            return false;
        }
    }
    
    public boolean getTpSenha() {
        try {
            return configuracao.getTipo().equals(ConfiguracaoTipoEnum.SENHA);
        } catch (Exception e) {
            return false;
        }
    }
    
    public boolean getTpCombo() {
        try {
            return configuracao.getTipo().equals(ConfiguracaoTipoEnum.COMBO);
        } catch (Exception e) {
            return false;
        }
    }
    
    public boolean getTpRadio() {
        try {
            return configuracao.getTipo().equals(ConfiguracaoTipoEnum.RADIO);
        } catch (Exception e) {
            return false;
        }
    }
    
    public boolean getTpMinutos() {
        try {
            return configuracao.getTipo().equals(ConfiguracaoTipoEnum.MINUTOS);
        } catch (Exception e) {
            return false;
        }
    }
    public ConfiguracaoSistema(final ConfiguracoesEnum configuracao){
        this.configuracao = configuracao;
        this.valor = configuracao.getValorPadrao();
    }
    
    public ConfiguracaoSistema(final TipoNotificacaoEnum tpNot){
        this.configuracao = ConfiguracoesEnum.NOTIFICACAO;
        this.valor = configuracao.getValorPadrao();
        this.tipoNotificacao = tpNot;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public ConfiguracoesEnum getConfiguracao() {
        return configuracao;
    }

    public void setConfiguracao(ConfiguracoesEnum configuracao) {
        this.configuracao = configuracao;
    }

    public String getValor() {
        return valor;
    }

    public void setValor(String valor) {
        this.valor = valor;
    }

    public TipoNotificacaoEnum getTipoNotificacao() {
        return tipoNotificacao;
    }

    public void setTipoNotificacao(TipoNotificacaoEnum tipoNotificacao) {
        this.tipoNotificacao = tipoNotificacao;
    }

    public String getValorMinutos() {
        if(valorMinutos == null || valorMinutos.isEmpty()){
            if(getValorAsInteger() < 60){
                valorMinutos = getValorAsInteger() + " minuto" + (getValorAsInteger() > 1 ? "s" : "");
            }else if(getValorAsInteger() < (60*24)){
                int horas = (getValorAsInteger()/60);
                valorMinutos =  horas + " hora"+(horas > 1 ? "s" : "");
            }else {
                int dias = (getValorAsInteger()/(60*24));
                valorMinutos =  dias + " dia"+(dias > 1 ? "s" : "");
            }
        }
        return valorMinutos;
    }
    public void setValorMinutos(String valorMinutos) {
        this.valorMinutos = valorMinutos;

    }
    
    public String iconeSVG(final ConfiguracaoSistema conf, final ConfiguracaoSistema confModuloPrincipal) {
        boolean enable = conf.getConfiguracao().getIcone().getConfigName().contains(
                confModuloPrincipal.getValor().toUpperCase());
        return conf.getConfiguracao().getIcone().getSvg().replace("STATUS_BTN",
                enable ? conf.getConfiguracao().getIcone().getClassActive()
                : conf.getConfiguracao().getIcone().getClassInactive());
    }
    
}
