package br.com.pacto.bean.configuracoes;


import br.com.pacto.bean.avaliacao.PerimetriaEnum;
import java.io.Serializable;

import javax.persistence.*;

@Entity
@Table
public class ConfiguracaoPerimetro implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    @Enumerated(EnumType.ORDINAL)
    private PerimetriaEnum esquerda;
    @Enumerated(EnumType.ORDINAL)
    private PerimetriaEnum direita;
    private Integer ordem;
    private String label;
    private Integer tabIndexEsq;
    private Integer tabIndexDir;

    public ConfiguracaoPerimetro() {
    }

    public ConfiguracaoPerimetro(String label, Integer ordem, PerimetriaEnum esquerda, PerimetriaEnum direita) {
        this.esquerda = esquerda;
        this.direita = direita;
        this.ordem = ordem;
        this.label = label;
    }

    public ConfiguracaoPerimetro(Integer codigo, String label, Integer tabIndexEsq, PerimetriaEnum esquerda) {
        this.codigo = codigo;
        this.esquerda = esquerda;
        this.label = label;
        this.tabIndexEsq = tabIndexEsq;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public PerimetriaEnum getEsquerda() {
        return esquerda;
    }

    public void setEsquerda(PerimetriaEnum esquerda) {
        this.esquerda = esquerda;
    }

    public PerimetriaEnum getDireita() {
        return direita;
    }

    public void setDireita(PerimetriaEnum direita) {
        this.direita = direita;
    }

    public Integer getOrdem() {
        return ordem;
    }

    public void setOrdem(Integer ordem) {
        this.ordem = ordem;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public Integer getTabIndexEsq() {
        if (tabIndexEsq == null) {
            tabIndexEsq = 0;
        }
        return tabIndexEsq;
    }

    public void setTabIndexEsq(Integer tabIndexEsq) {
        this.tabIndexEsq = tabIndexEsq;
    }

    public Integer getTabIndexDir() {
        if (tabIndexDir == null) {
            tabIndexDir = 0;
        }
        return tabIndexDir;
    }

    public void setTabIndexDir(Integer tabIndexDir) {
        this.tabIndexDir = tabIndexDir;
    }
}
