package br.com.pacto.bean.configuracoes;


import br.com.pacto.bean.avaliacao.DobrasEnum;
import br.com.pacto.util.enumeradores.ProtocolosAvaliacaoFisicaEnum;

import javax.persistence.*;
import java.io.Serializable;


/**
 * Created by <PERSON><PERSON> on 06/03/2019.
 */

@Entity
@Table
public class ConfiguracaoDobras implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    @Enumerated(EnumType.ORDINAL)
    private DobrasEnum dobrasEnum;
    private Integer ordem;

    public ConfiguracaoDobras() {
    }

    public ConfiguracaoDobras(Integer ordem, DobrasEnum dobrasEnum) {
        this.dobrasEnum = dobrasEnum;
        this.ordem = ordem;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getOrdem() {
        return ordem;
    }

    public void setOrdem(Integer ordem) {
        this.ordem = ordem;
    }

    public String getLabel() {
        return getDobrasEnum().getLabel();
    }

    public DobrasEnum getDobrasEnum() {
        return dobrasEnum;
    }

    public void setDobrasEnum(DobrasEnum dobrasEnum) {
        this.dobrasEnum = dobrasEnum;
    }
}
