package br.com.pacto.bean.configuracoes;

import javax.persistence.*;

@Entity
@Table
public class NotificacoesAcessoUsuario {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private Integer usuario;
    private Boolean semProgramaTreino;
    private Boolean faltosos;
    private Boolean quatrosDiasSemAcesso;
    private Boolean treinoVencido;
    private Boolean treinoVencer;
    private Boolean alunoSemVinculoProfessor;
    private Boolean parcelasAtrasadas;
    private Boolean avaliacaoFisicaAtrasada;
    private Boolean semAssinaturaContrato;
    private Boolean cadastroIncompleto;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getUsuario() {
        return usuario;
    }

    public void setUsuario(Integer usuario) {
        this.usuario = usuario;
    }

    public Boolean getSemProgramaTreino() {
        return semProgramaTreino;
    }

    public void setSemProgramaTreino(Boolean semProgramaTreino) {
        this.semProgramaTreino = semProgramaTreino;
    }

    public Boolean getFaltosos() {
        return faltosos;
    }

    public void setFaltosos(Boolean faltosos) {
        this.faltosos = faltosos;
    }

    public Boolean getQuatrosDiasSemAcesso() {
        return quatrosDiasSemAcesso;
    }

    public void setQuatrosDiasSemAcesso(Boolean quatrosDiasSemAcesso) {
        this.quatrosDiasSemAcesso = quatrosDiasSemAcesso;
    }

    public Boolean getTreinoVencido() {
        return treinoVencido;
    }

    public void setTreinoVencido(Boolean treinoVencido) {
        this.treinoVencido = treinoVencido;
    }

    public Boolean getTreinoVencer() {
        return treinoVencer;
    }

    public void setTreinoVencer(Boolean treinoVencer) {
        this.treinoVencer = treinoVencer;
    }

    public Boolean getAlunoSemVinculoProfessor() {
        return alunoSemVinculoProfessor;
    }

    public void setAlunoSemVinculoProfessor(Boolean alunoSemVinculoProfessor) {
        this.alunoSemVinculoProfessor = alunoSemVinculoProfessor;
    }

    public Boolean getParcelasAtrasadas() {
        return parcelasAtrasadas;
    }

    public void setParcelasAtrasadas(Boolean parcelasAtrasadas) {
        this.parcelasAtrasadas = parcelasAtrasadas;
    }

    public Boolean getAvaliacaoFisicaAtrasada() {
        return avaliacaoFisicaAtrasada;
    }

    public void setAvaliacaoFisicaAtrasada(Boolean avaliacaoFisicaAtrasada) {
        this.avaliacaoFisicaAtrasada = avaliacaoFisicaAtrasada;
    }

    public Boolean getSemAssinaturaContrato() {
        return semAssinaturaContrato;
    }

    public void setSemAssinaturaContrato(Boolean semAssinaturaContrato) {
        this.semAssinaturaContrato = semAssinaturaContrato;
    }

    public Boolean getCadastroIncompleto() {
        return cadastroIncompleto;
    }

    public void setCadastroIncompleto(Boolean cadastroIncompleto) {
        this.cadastroIncompleto = cadastroIncompleto;
    }
}
