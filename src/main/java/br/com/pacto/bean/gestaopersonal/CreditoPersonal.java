/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.pacto.bean.gestaopersonal;

import br.com.pacto.bean.professor.ProfessorSintetico;
import br.com.pacto.objeto.Uteis;
import java.io.Serializable;
import java.util.Date;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.ManyToOne;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table
public class CreditoPersonal implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    @Temporal(TemporalType.TIMESTAMP)
    private Date dataLancamento;
    @ManyToOne
    private Professor<PERSON>int<PERSON><PERSON> professor;
    private Integer unidades;
    private Integer reciboZW;
    @OneToOne
    private AulaPersonal aulaPersonal;
    @Enumerated(EnumType.ORDINAL)
    private TipoOperacaoPersonalEnum tipoOperacao;
    private Integer saldoPersonal;
    private Integer empresaZW;
    private boolean prePago= false;
    private Integer vendaAvulsa;
    @Temporal(TemporalType.TIMESTAMP)
    private Date dataExpiracao; 
    private Boolean expirado = Boolean.FALSE;

    public Integer getEmpresaZW() {
        return empresaZW;
    }

    public void setEmpresaZW(Integer empresaZW) {
        this.empresaZW = empresaZW;
    }
    
    
    public boolean getCompra(){
        try {
            return tipoOperacao.equals(TipoOperacaoPersonalEnum.COMPRA_CREDITOS);
        } catch (Exception e) {
            return false;
        }
        
    }

    public String getSaldoApresentar(){
        return saldoPersonal > 0 ? "+"+saldoPersonal : saldoPersonal.toString();
    }
    
    public String getUnidadesApresentar(){
        if(tipoOperacao != null 
                && (tipoOperacao.equals(TipoOperacaoPersonalEnum.GASTO_CREDITOS))
                || tipoOperacao.equals(TipoOperacaoPersonalEnum.CREDITOS_EXPIRADOS)){
            return "-"+unidades;
        }
        return "+"+unidades;
    }
    
    public String getDataApresentar(){
        return Uteis.getDataAplicandoFormatacao(dataLancamento, "dd/MM");
    }
    
    public String getDataExpiracaoApresentar(){
        return dataExpiracao == null ? "" : "Expira em "+Uteis.getDataAplicandoFormatacao(dataExpiracao, "dd/MM/yyyy");
    }
    
    public String getSaldoCor(){
        return saldoPersonal < 0 ? "red" : "green";
    }
    public String getUnidadeCor(){
        if(tipoOperacao != null 
                && (tipoOperacao.equals(TipoOperacaoPersonalEnum.GASTO_CREDITOS)
                || tipoOperacao.equals(TipoOperacaoPersonalEnum.CREDITOS_EXPIRADOS))){
            return "red";
        }
        return "green";
    }
    
    public Integer getUnidades() {
        return unidades;
    }

    public void setUnidades(Integer unidades) {
        this.unidades = unidades;
    }
   
    public CreditoPersonal(){
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Date getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public ProfessorSintetico getProfessor() {
        return professor;
    }

    public void setProfessor(ProfessorSintetico professor) {
        this.professor = professor;
    }

    public Integer getReciboZW() {
        return reciboZW;
    }

    public void setReciboZW(Integer reciboZW) {
        this.reciboZW = reciboZW;
    }

    public AulaPersonal getAulaPersonal() {
        return aulaPersonal;
    }

    public void setAulaPersonal(AulaPersonal aulaPersonal) {
        this.aulaPersonal = aulaPersonal;
    }

    public TipoOperacaoPersonalEnum getTipoOperacao() {
        return tipoOperacao;
    }

    public void setTipoOperacao(TipoOperacaoPersonalEnum tipoOperacao) {
        this.tipoOperacao = tipoOperacao;
    }

    public Integer getSaldoPersonal() {
        return saldoPersonal;
    }

    public void setSaldoPersonal(Integer saldoPersonal) {
        this.saldoPersonal = saldoPersonal;
    }

    public boolean isPrePago() {
        return prePago;
    }

    public void setPrePago(boolean prePago) {
        this.prePago = prePago;
    }

    public Integer getVendaAvulsa() {
        return vendaAvulsa;
    }

    public void setVendaAvulsa(Integer vendaAvulsa) {
        this.vendaAvulsa = vendaAvulsa;
    }

    public Date getDataExpiracao() {
        return dataExpiracao;
    }

    public void setDataExpiracao(Date dataExpiracao) {
        this.dataExpiracao = dataExpiracao;
    }

    public Boolean getExpirado() {
        return expirado;
    }

    public void setExpirado(Boolean expirado) {
        this.expirado = expirado;
    }
    
}
