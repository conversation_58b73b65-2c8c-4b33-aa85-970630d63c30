/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.gestaopersonal;

import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.professor.ProfessorSintetico;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import java.io.Serializable;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.persistence.CascadeType;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table
public class AulaPersonal implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    @Temporal(TemporalType.TIMESTAMP)
    private Date checkIn;
    @Temporal(TemporalType.TIMESTAMP)
    private Date checkOut;
    @OneToOne
    private CreditoPersonal credito;
    @ManyToOne
    private ProfessorSintetico professor;
    @OneToMany(cascade = {CascadeType.PERSIST, CascadeType.MERGE, CascadeType.REMOVE}, fetch = FetchType.LAZY, mappedBy = "aula", targetEntity = AlunoAulaPersonal.class)
    private List<AlunoAulaPersonal> alunos = new ArrayList<AlunoAulaPersonal>();
    private Integer empresaZW;
    private Boolean checkOutAutomatico;
    @Transient
    private String nomesAlunos = null;
    @Transient
    private String nomesTodosAlunos = null;
    @Transient
    private String horaCheckIn = "";
    @Transient
    private String horaCheckOut = "";
    
    public void setarHorasString(){
        if(checkIn != null){
            horaCheckIn = Uteis.getDataAplicandoFormatacao(checkIn, "dd/MM/yyyy HH:mm");
        }
        if(checkOut != null){
            horaCheckOut = Uteis.getDataAplicandoFormatacao(checkOut, "dd/MM/yyyy HH:mm");
        }
    }
    
    public void setarHorasDate() throws ParseException{
        SimpleDateFormat formatter = new SimpleDateFormat("dd/MM/yyyy HH:mm");
        if(horaCheckIn != null && !horaCheckIn.isEmpty()){
            checkIn = formatter.parse(horaCheckIn);
        }
        if(horaCheckOut != null && !horaCheckOut.isEmpty()){
            checkOut = formatter.parse(horaCheckOut);
        }
    }

    public String getHoraCheckIn() {
        return horaCheckIn;
    }

    public void setHoraCheckIn(String horaCheckIn) {
        this.horaCheckIn = horaCheckIn;
    }

    public String getHoraCheckOut() {
        return horaCheckOut;
    }

    public void setHoraCheckOut(String horaCheckOut) {
        this.horaCheckOut = horaCheckOut;
    }
    
    public String getNomesAlunos() {
        if (nomesAlunos == null) {
            int i = 0;
            if (alunos == null || alunos.isEmpty()) {
                nomesAlunos = " - ";
            } else {
                String nomes = "";
                for (AlunoAulaPersonal aluno : alunos) {
                    if(i < 5){
                        nomes += ", " + Uteis.getPrimeiroNome(aluno.getNome());
                    }else{
                        nomes += "...";
                        break;
                    }
                    i++;
                }
                nomesAlunos = nomes.replaceFirst(",", "");
            }
        }
        return nomesAlunos;
    }
    public String getNomesTodosAlunos() {
        if (nomesTodosAlunos == null) {
            if (alunos == null || alunos.isEmpty()) {
                nomesTodosAlunos = " - ";
            } else {
                String nomes = "";
                for (AlunoAulaPersonal aluno : alunos) {
                        nomes += ", " + Uteis.getPrimeiroNome(aluno.getNome());
                }
                nomesTodosAlunos = nomes.replaceFirst(",", "");
            }
        }
        return nomesTodosAlunos;
    }

    public Integer getEmpresaZW() {
        return empresaZW;
    }

    public void setEmpresaZW(Integer empresaZW) {
        this.empresaZW = empresaZW;
    }

    public AulaPersonal() {
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Date getCheckIn() {
        return checkIn;
    }

    public String getCheckInApresentarHoras() {
        try {
            return Uteis.getDataAplicandoFormatacao(checkIn, "HH:mm:ss");
        } catch (Exception e) {
            return "00:00";
        }
    }

    public void setCheckIn(Date checkIn) {
        this.checkIn = checkIn;
    }

    public Date getCheckOut() {
        return checkOut;
    }

    public void setCheckOut(Date checkOut) {
        this.checkOut = checkOut;
    }

    public CreditoPersonal getCredito() {
        return credito;
    }

    public void setCredito(CreditoPersonal credito) {
        this.credito = credito;
    }

    public ProfessorSintetico getProfessor() {
        return professor;
    }

    public void setProfessor(ProfessorSintetico professor) {
        this.professor = professor;
    }

    public List<AlunoAulaPersonal> getAlunos() {
        return alunos;
    }

    public void setAlunos(List<AlunoAulaPersonal> alunos) {
        this.alunos = alunos;
        getNomesAlunos();
    }

    public String getCheckInApresentar() {
        try {
            return Uteis.getDataAplicandoFormatacao(getCheckIn(), "dd/MM - HH:mm");
        } catch (Exception e) {
            return "";
        }
    }

    public String getCheckOutApresentar() {
        try {
            if (checkOut == null) {
                return "";
            }
            if (Calendario.getDataComHoraZerada(getCheckIn()).equals(Calendario.getDataComHoraZerada(getCheckOut()))) {
                return Uteis.getDataAplicandoFormatacao(getCheckOut(), "HH:mm");
            }
            return Uteis.getDataAplicandoFormatacao(getCheckOut(), "dd/MM - HH:mm");
        } catch (Exception e) {
            return "";
        }
    }

    public boolean isCheckOutAutomatico() {
        if(checkOutAutomatico == null){
            return Boolean.FALSE;
        }
        return checkOutAutomatico;
    }

    public void setCheckOutAutomatico(boolean checkOutAutomatico) {
        this.checkOutAutomatico = checkOutAutomatico;
    }
    
    
}
