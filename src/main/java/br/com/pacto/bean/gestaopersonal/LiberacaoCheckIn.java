/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.pacto.bean.gestaopersonal;

import br.com.pacto.bean.professor.ProfessorSintetico;
import br.com.pacto.bean.usuario.Usuario;
import java.io.Serializable;
import java.util.Date;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.ManyToOne;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

/**
 *
 * <AUTHOR>
 */
@Entity
public class LiberacaoCheckIn implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private Integer usuarioLiberou_codigo;
    @ManyToOne
    private AulaPersonal aulaLiberada;
    @ManyToOne
    private ProfessorSintetico personal;
    @Temporal(TemporalType.TIMESTAMP)
    private Date dataLiberacao;
    @Enumerated(EnumType.ORDINAL)
    private MotivoBloqueioEnum motivo;
    private String observacao;

    public Integer getUsuarioLiberou_codigo() {
        return usuarioLiberou_codigo;
    }

    public void setUsuarioLiberou_codigo(Integer usuarioLiberou) {
        this.usuarioLiberou_codigo = usuarioLiberou;
    }

    public AulaPersonal getAulaLiberada() {
        return aulaLiberada;
    }

    public void setAulaLiberada(AulaPersonal aulaLiberada) {
        this.aulaLiberada = aulaLiberada;
    }

    public Date getDataLiberacao() {
        return dataLiberacao;
    }

    public void setDataLiberacao(Date dataLiberacao) {
        this.dataLiberacao = dataLiberacao;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }
    
    public LiberacaoCheckIn(){
    }

    
    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public MotivoBloqueioEnum getMotivo() {
        return motivo;
    }

    public void setMotivo(MotivoBloqueioEnum motivo) {
        this.motivo = motivo;
    }

    public ProfessorSintetico getPersonal() {
        return personal;
    }

    public void setPersonal(ProfessorSintetico personal) {
        this.personal = personal;
    }
    

}
