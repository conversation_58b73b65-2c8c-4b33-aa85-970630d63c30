/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.pacto.bean.gestaopersonal;

import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.professor.ProfessorSintetico;
import br.com.pacto.objeto.Uteis;
import java.io.Serializable;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table
public class AlunoAulaPersonal implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    @ManyToOne(fetch = FetchType.LAZY)
    private AulaPersonal aula;
    @ManyToOne(fetch = FetchType.LAZY)
    private ClienteSintetico cliente;
    @ManyToOne(fetch = FetchType.LAZY)
    private ProfessorSintetico colaborador;

    public Integer getCodigoPessoa(){
        if(colaborador == null || colaborador.getCodigoPessoa() == null || colaborador.getCodigoPessoa() == 0){
            return cliente == null || cliente.getCodigoPessoa() == null || cliente.getCodigoPessoa() == 0 ? 0 : cliente.getCodigoPessoa();
        }
        return colaborador.getCodigoPessoa();
    }
    
    public String getNome(){
        if(colaborador == null || colaborador.getNome() == null || colaborador.getNome().isEmpty()){
            return cliente == null || cliente.getNome() == null || cliente.getNome().isEmpty() ? "" : cliente.getNome();
        }
        return colaborador.getNome();
    }
    
    public AlunoAulaPersonal(){
    }

    public AlunoAulaPersonal(AulaPersonal aula, ClienteSintetico cliente) {
        this.aula = aula;
        this.cliente = cliente;
    }
    
    public AlunoAulaPersonal(AulaPersonal aula, ProfessorSintetico professor) {
        this.aula = aula;
        this.colaborador = professor;
    }
    public AlunoAulaPersonal(ClienteSintetico cliente) {
        this.cliente = cliente;
    }
    public AlunoAulaPersonal(ProfessorSintetico professor) {
        this.colaborador = professor;
    }
    
    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public AulaPersonal getAula() {
        return aula;
    }

    public void setAula(AulaPersonal aula) {
        this.aula = aula;
    }

    public ClienteSintetico getCliente() {
        return cliente;
    }

    public void setCliente(ClienteSintetico cliente) {
        this.cliente = cliente;
    }

    public ProfessorSintetico getColaborador() {
        return colaborador;
    }

    public void setColaborador(ProfessorSintetico colaborador) {
        this.colaborador = colaborador;
    }
    
    public Integer getCodigoCliente(){
        return getCliente() == null || getCliente().getCodigo() == null || getCliente().getCodigo() == 0 ? null : getCliente().getCodigo();
    }
    
    public Integer getCodigoColaborador(){
        return getColaborador() == null || getColaborador().getCodigo() == null || getColaborador().getCodigo() == 0 ? null : getColaborador().getCodigo();
    }
    
    public String getNomeMinusculo() {
        if (getNome() != null) {
            return getNome().toLowerCase();
        } else {
            return "";
        }
    }

    public String getNomeAbreviado() {
        if (getNome() != null) {
            return Uteis.getNomeAbreviandoSomentePrimeiroSobrenome(getNome());
        } else {
            return "";
        }
    }
    
    public String getPrimeiroNome() {
        if (getNome() != null) {
            return Uteis.getPrimeiroNome(getNome());
        } else {
            return "";
        }
    }

    public String getNomeAbreviadoMinusculo() {
        if (getNome() != null) {
            return Uteis.getNomeAbreviandoSomentePrimeiroSobrenome(getNome()).toLowerCase();
        } else {
            return "";
        }
    }

}
