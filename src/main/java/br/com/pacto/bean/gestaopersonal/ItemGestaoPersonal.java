/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.gestaopersonal;

import br.com.pacto.bean.professor.ProfessorSintetico;
import br.com.pacto.objeto.Uteis;
import java.io.Serializable;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class ItemGestaoPersonal implements Serializable{
    
    private TipoItemGestaoPersonal tipo;
    private ProfessorSintetico personal;
    private LiberacaoCheckIn liberacao;
    private AulaPersonal aula = new AulaPersonal();
    private CreditoPersonal credito = new CreditoPersonal();
    private Integer unidades = 0;
    private Date data;

    public TipoItemGestaoPersonal getTipo() {
        return tipo;
    }

    public boolean getCheckIn(){
        return tipo.equals(TipoItemGestaoPersonal.CHECKIN);
    }
    
    public boolean getCheckOut(){
        return tipo.equals(TipoItemGestaoPersonal.CHECKIN);
    }
    
    public boolean getConsumo(){
        return tipo.equals(TipoItemGestaoPersonal.CONS_POS_PAGO)
                || tipo.equals(TipoItemGestaoPersonal.CONS_PRE_PAGO);
    }
    
    public boolean getPagamento(){
        return tipo.equals(TipoItemGestaoPersonal.PAGAMENTO);
    }
    
    public void setTipo(TipoItemGestaoPersonal tipo) {
        this.tipo = tipo;
    }
    
    

    public ProfessorSintetico getPersonal() {
        return personal;
    }

    public void setPersonal(ProfessorSintetico personal) {
        this.personal = personal;
    }
    
    
    public AulaPersonal getAula() {
        return aula;
    }

    public void setAula(AulaPersonal aula) {
        this.aula = aula;
    }

    public CreditoPersonal getCredito() {
        return credito;
    }

    public void setCredito(CreditoPersonal credito) {
        this.credito = credito;
    }

    public Integer getUnidades() {
        return unidades;
    }

    public void setUnidades(Integer unidades) {
        this.unidades = unidades;
    }

    public Date getData() {
        return data;
    }
    
    public String getDataApresentar(){
        if(data != null){
            return Uteis.getDataAplicandoFormatacao(data, "dd/MM/yyyy - HH:mm");
        }
        return "";
    }

    public void setData(Date data) {
        this.data = data;
    }

    public LiberacaoCheckIn getLiberacao() {
        return liberacao;
    }

    public void setLiberacao(LiberacaoCheckIn liberacao) {
        this.liberacao = liberacao;
    }
    
    
}
