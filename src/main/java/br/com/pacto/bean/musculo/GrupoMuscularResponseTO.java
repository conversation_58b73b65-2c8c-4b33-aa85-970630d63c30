package br.com.pacto.bean.musculo;

import br.com.pacto.bean.atividade.AtividadeGrupoMuscular;
import br.com.pacto.bean.atividade.AtividadeGrupoMuscularResponseTO;
import br.com.pacto.bean.avaliacao.PerimetriaEnum;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by ulisses on 03/08/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Grupos musculares da atividade")
public class GrupoMuscularResponseTO {

    @ApiModelProperty(value = "ID do grupo muscular", example = "1")
    private Integer id;
    @ApiModelProperty(value = "Nome do grupo muscular", example = "Grupo muscular 1")
    private String nome;
    @ApiModelProperty(value = "Perimetria", example = "TORAX")
    private List<PerimetriaEnum> perimetros = new ArrayList<>();
    @ApiModelProperty(value = "Atividades de grupo muscular")
    private List<AtividadeGrupoMuscularResponseTO> atividades = new ArrayList<>();
    @ApiModelProperty(value = "Músculo grupo")
    private List<MusculoGrupoResponseTO> musculos = new ArrayList<>();


    public GrupoMuscularResponseTO(){

    }

    public GrupoMuscularResponseTO(GrupoMuscular grupoMuscular){
        this.id = grupoMuscular.getCodigo();
        this.nome = grupoMuscular.getNome();
        if (grupoMuscular.getPerimetros() != null && grupoMuscular.getPerimetros().size() > 0){
            List<PerimetriaEnum> perimetros = new ArrayList<>();
            for (PerimetriaEnum perimetriaEnum : grupoMuscular.getPerimetros()) {
                perimetros.add(perimetriaEnum);
            }
            this.perimetros = perimetros;
        }
        if (grupoMuscular.getAtividades() != null){
            for (AtividadeGrupoMuscular atividadeGrupoMuscular: grupoMuscular.getAtividades()){
                getAtividades().add(new AtividadeGrupoMuscularResponseTO(atividadeGrupoMuscular.getAtividade().getCodigo(), atividadeGrupoMuscular.getAtividade().getNome()));
            }
        }
        if (grupoMuscular.getMusculos() != null){
            for (MusculoGrupoMuscular musculoGrupoMuscular: grupoMuscular.getMusculos()){
                getMusculos().add(new MusculoGrupoResponseTO(musculoGrupoMuscular.getMusculo().getCodigo(), musculoGrupoMuscular.getMusculo().getNome()));
            }
        }
    }

    public GrupoMuscularResponseTO(GrupoMuscular grupoMuscular, boolean resumido) {
        if (resumido) {
            this.id = grupoMuscular.getCodigo();
            this.nome = grupoMuscular.getNome();
        }
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public List<PerimetriaEnum> getPerimetros() {
        return perimetros;
    }

    public void setPerimetros(List<PerimetriaEnum> perimetros) {
        this.perimetros = perimetros;
    }

    public List<AtividadeGrupoMuscularResponseTO> getAtividades() {
        return atividades;
    }

    public void setAtividades(List<AtividadeGrupoMuscularResponseTO> atividades) {
        this.atividades = atividades;
    }

    public List<MusculoGrupoResponseTO> getMusculos() {
        return musculos;
    }

    public void setMusculos(List<MusculoGrupoResponseTO> musculos) {
        this.musculos = musculos;
    }
}
