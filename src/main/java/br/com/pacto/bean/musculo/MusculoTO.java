package br.com.pacto.bean.musculo;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class MusculoTO {

    private Integer id;
    private String nome;
    private List<Integer> atividadeIds;
    private List<Integer> grupoMuscularIds;


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public List<Integer> getAtividadeIds() {
        return atividadeIds;
    }

    public void setAtividadeIds(List<Integer> atividadeIds) {
        this.atividadeIds = atividadeIds;
    }

    public List<Integer> getGrupoMuscularIds() { return grupoMuscularIds; }

    public void setGrupoMuscularIds(List<Integer> grupoMuscularIds) { this.grupoMuscularIds = grupoMuscularIds; }
}
