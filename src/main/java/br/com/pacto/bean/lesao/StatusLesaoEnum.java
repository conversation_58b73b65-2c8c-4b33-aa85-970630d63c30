package br.com.pacto.bean.lesao;

public enum StatusLesaoEnum {

    LEVE(1, "Em recuperação"),
    MODERADA(2, "Em recuperação 2"),
    GRAVE(3, "Em recuperação 3"),
    RECUPERADA(4, "Recuperada");
    private Integer id;
    private String descricao;


    private StatusLesaoEnum(Integer codigo,String descricao) {
        this.id = codigo;
        this.descricao = descricao;
    }


    public static StatusLesaoEnum obterPorID(Integer id) {
        for (StatusLesaoEnum status : StatusLesaoEnum.values()) {
            if (status.getId().equals(id)) {
                return status;
            }
        }
        return null;
    }


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}

