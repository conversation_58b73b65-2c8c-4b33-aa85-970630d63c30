package br.com.pacto.bean.lesao;

import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class AlunoLesaoDTO {

    private Integer matricula;
    private String nome;
    private String dataLesao;
    private String professor;
    private String regiaoLesao;
    private String gravidade;
    private String observacao;

    public AlunoLesaoDTO() { }

    public AlunoLesaoDTO(Integer matricula, String nome, String dataLesao, String professor, String regiaoLesao, String gravidade, String observacao) {
        this.matricula = matricula;
        this.nome = nome;
        this.dataLesao = dataLesao;
        this.professor = professor;
        this.regiaoLesao = regiaoLesao;
        this.gravidade = gravidade;
        this.observacao = observacao;
    }

    public Integer getMatricula() {
        return matricula;
    }

    public void setMatricula(Integer matricula) {
        this.matricula = matricula;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getDataLesao() {
        return dataLesao;
    }

    public void setDataLesao(String dataLesao) {
        this.dataLesao = dataLesao;
    }

    public String getProfessor() {
        return professor;
    }

    public void setProfessor(String professor) {
        this.professor = professor;
    }

    public String getRegiaoLesao() {
        return regiaoLesao;
    }

    public void setRegiaoLesao(String regiaoLesao) {
        this.regiaoLesao = regiaoLesao;
    }

    public String getGravidade() {
        return gravidade;
    }

    public void setGravidade(String gravidade) {
        this.gravidade = gravidade;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

}
