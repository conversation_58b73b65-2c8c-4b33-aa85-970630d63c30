package br.com.pacto.bean.lesao;

import org.apache.commons.lang3.StringUtils;

public enum GravidadeLesaoEnum {

    LEVE(1, "Leve", "Isso inclui coisas como contusões, pequenas distensões musculares ou articulares, bolhas nas mãos devido ao uso frequente de barras e outros equipamentos."),
    MODERADA(2, "Moderada", "Estas podem envolver distensões musculares mais significativas, entorses, pequenas lacerações, ou até mesmo lesões articulares mais graves como tendinites."),
    GRAVE(3, "Grave", "Lesões mais graves podem incluir rupturas musculares, lesões ligamentares significativas (como um ligamento cruzado rompido no joelho), fraturas de estresse de ossos devido à sobrecarga repetitiva, ou até mesmo lesões graves na coluna vertebral.");
    private Integer id;
    private String nome;
    private String descricao;


    private GravidadeLesaoEnum(Integer codigo, String nome, String descricao) {
        this.id = codigo;
        this.nome = nome;
        this.descricao = descricao;
    }


    public static GravidadeLesaoEnum obterPorID(Integer id) {
        for (GravidadeLesaoEnum gravidade : GravidadeLesaoEnum.values()) {
            if (gravidade.getId().equals(id)) {
                return gravidade;
            }
        }
        return null;
    }

    public static GravidadeLesaoEnum obterPorNome(String name) {
        if (StringUtils.isBlank(name)) {
            return null;
        }

        for (GravidadeLesaoEnum gravidade : GravidadeLesaoEnum.values()) {
            if (gravidade.getNome().equalsIgnoreCase(name)) {
                return gravidade;
            }
        }

        return null;
    }


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}

