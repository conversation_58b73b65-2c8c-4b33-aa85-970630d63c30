package br.com.pacto.bean.lesao;

import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class IndiceLesaoDTO {

    private Integer totalLesoesLeves;
    private Integer totalLesoesModeradas;
    private Integer totalLesoesGraves;
    private Integer totalLesoesAoAno;

    public IndiceLesaoDTO() { }

    public IndiceLesaoDTO(Integer totalLesoesLeves, Integer totalLesoesModeradas, Integer totalLesoesGraves, Integer totalLesoesAoAno) {
        this.totalLesoesLeves = totalLesoesLeves;
        this.totalLesoesModeradas = totalLesoesModeradas;
        this.totalLesoesGraves = totalLesoesGraves;
        this.totalLesoesAoAno = totalLesoesAoAno;
    }

    public Integer getTotalLesoesLeves() {
        return totalLesoesLeves;
    }

    public void setTotalLesoesLeves(Integer totalLesoesLeves) {
        this.totalLesoesLeves = totalLesoesLeves;
    }

    public Integer getTotalLesoesModeradas() {
        return totalLesoesModeradas;
    }

    public void setTotalLesoesModeradas(Integer totalLesoesModeradas) {
        this.totalLesoesModeradas = totalLesoesModeradas;
    }

    public Integer getTotalLesoesGraves() {
        return totalLesoesGraves;
    }

    public void setTotalLesoesGraves(Integer totalLesoesGraves) {
        this.totalLesoesGraves = totalLesoesGraves;
    }

    public Integer getTotalLesoesAoAno() {
        return totalLesoesAoAno;
    }

    public void setTotalLesoesAoAno(Integer totalLesoesAoAno) {
        this.totalLesoesAoAno = totalLesoesAoAno;
    }

}
