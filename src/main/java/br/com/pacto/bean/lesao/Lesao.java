package br.com.pacto.bean.lesao;

import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.service.exception.ServiceException;
import org.hibernate.annotations.Type;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Lob;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import java.text.ParseException;
import java.util.Date;

@Entity
@Table
public class Lesao {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "codigo", nullable = false)
    private Integer codigo;
    @ManyToOne(fetch = FetchType.LAZY)
    private ClienteSintetico cliente;
    @Enumerated(EnumType.STRING)
    private GravidadeLesaoEnum gravidade;
    @Lob
    @Type(type="org.hibernate.type.StringClobType")
    @Column(columnDefinition = "text", length = 9999)
    private String observacao;
    private String regiaoLesao;
    @Enumerated(EnumType.STRING)
    private StatusLesaoEnum status;
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    private Date dataLesao;
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    private Date dataRegistro;
    private Integer usuarioLancou_codigo;
    private Boolean app;
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    private Date dataAtualizacao;
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    private Date dataRecuperacao;

    public Boolean getApp() {
        return app;
    }

    public void setApp(Boolean app) {
        this.app = app;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public ClienteSintetico getCliente() {
        return cliente;
    }

    public void setCliente(ClienteSintetico cliente) {
        this.cliente = cliente;
    }

    public GravidadeLesaoEnum getGravidade() {
        return gravidade;
    }

    public void setGravidade(GravidadeLesaoEnum gravidade) {
        this.gravidade = gravidade;
    }

    public StatusLesaoEnum getStatus() {
        return status;
    }

    public void setStatus(StatusLesaoEnum status) {
        this.status = status;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public String getRegiaoLesao() {
        return regiaoLesao;
    }

    public void setRegiaoLesao(String regiaoLesao) {
        this.regiaoLesao = regiaoLesao;
    }

    public Date getDataLesao() {
        return dataLesao;
    }

    public void setDataLesao(Date dataLesao) {
        this.dataLesao = dataLesao;
    }

    public Date getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(Date dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public Lesao() {
    }

    public Integer getUsuarioLancou_codigo() {
        return usuarioLancou_codigo;
    }

    public void setUsuarioLancou_codigo(Integer usuarioLancou_codigo) {
        this.usuarioLancou_codigo = usuarioLancou_codigo;
    }

    public Lesao(LesaoDTO lesao, ClienteSintetico cliente) throws ServiceException {
        try {
            this.dataLesao = Calendario.getDate("yyyy-MM-dd", lesao.getDataLesao());
        } catch (ParseException e) {
            throw new ServiceException("O formato da data está incorreto.");
        }
        this.codigo = lesao.getId();
        this.cliente = cliente;
        this.gravidade = lesao.getGravidade();
        this.observacao = lesao.getObservacao();
        this.regiaoLesao = lesao.getRegiaoLesao();
        this.status = lesao.getStatus();
    }

    public Date getDataAtualizacao() {
        return dataAtualizacao;
    }

    public void setDataAtualizacao(Date dataAtualizacao) {
        this.dataAtualizacao = dataAtualizacao;
    }

    public Date getDataRecuperacao() {
        return dataRecuperacao;
    }

    public void setDataRecuperacao(Date dataRecuperacao) {
        this.dataRecuperacao = dataRecuperacao;
    }

    @Override
    public String toString() {
        return "Lesao{" +
                "codigo=" + codigo +
                ", cliente=" + cliente +
                ", gravidade=" + gravidade +
                ", observacao='" + observacao + '\'' +
                ", regiaoLesao='" + regiaoLesao + '\'' +
                ", status=" + status +
                ", dataLesao=" + dataLesao +
                ", dataRegistro=" + dataRegistro +
                ", usuarioLancou_codigo=" + usuarioLancou_codigo +
                ", app=" + app +
                ", dataAtualizacao=" + dataAtualizacao +
                ", dataRecuperacao=" + dataRecuperacao +
                '}';
    }
}
