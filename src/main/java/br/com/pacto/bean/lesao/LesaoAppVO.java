package br.com.pacto.bean.lesao;

import br.com.pacto.objeto.Calendario;

public class LesaoAppVO {
    private Integer id;
    private String observacao;
    private String regiaoLesao;
    private GravidadeLesaoEnum gravidade;
    private StatusLesaoEnum statusAtual;
    private String descricaoStatus;
    private String dataRegistro; // "yyyy-MM-dd hh:mm:ss"
    private String dataLesao;
    private String dataRecuperacao; // "yyyy-MM-dd "

    public LesaoAppVO(Lesao lesao) {
        this.id = lesao.getCodigo();
        this.observacao = lesao.getObservacao();
        this.regiaoLesao = lesao.getRegiaoLesao();
        this.dataRegistro = Calendario.getData(lesao.getDataRegistro(), "yyyy-MM-dd hh:mm:ss");
        this.dataLesao = Calendario.getData(lesao.getDataLesao(), "yyyy-MM-dd");
        this.statusAtual = lesao.getStatus();
        this.gravidade = lesao.getGravidade();
        String data = lesao.getDataRecuperacao() != null ? Calendario.getData(lesao.getDataRecuperacao(), "yyyy-MM-dd") : null;
        this.dataRecuperacao = data;
    }

    public LesaoAppVO() {
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public String getRegiaoLesao() {
        return regiaoLesao;
    }

    public void setRegiaoLesao(String regiaoLesao) {
        this.regiaoLesao = regiaoLesao;
    }

    public GravidadeLesaoEnum getGravidade() {
        return gravidade;
    }

    public void setGravidade(GravidadeLesaoEnum gravidade) {
        this.gravidade = gravidade;
    }

    public StatusLesaoEnum getStatusAtual() {
        return statusAtual;
    }

    public void setStatusAtual(StatusLesaoEnum statusAtual) {
        this.statusAtual = statusAtual;
    }

    public String getDescricaoStatus() {
        return descricaoStatus;
    }

    public void setDescricaoStatus(String descricaoStatus) {
        this.descricaoStatus = descricaoStatus;
    }

    public String getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(String dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public String getDataLesao() {
        return dataLesao;
    }

    public void setDataLesao(String dataLesao) {
        this.dataLesao = dataLesao;
    }

    public String getDataRecuperacao() {
        return dataRecuperacao;
    }

    public void setDataRecuperacao(String dataRecuperacao) {
        this.dataRecuperacao = dataRecuperacao;
    }
}
