package br.com.pacto.bean.lesao;

import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class LesaoAppDTO {
    private String observacao;
    private String regiaoLesao;
    private GravidadeLesaoEnum gravidade;
    private StatusLesaoEnum statusAtual;
    private String dataLesao; // "yyyy-MM-dd "
    private String dataRecuperacao; // "yyyy-MM-dd "

    public LesaoAppDTO(Lesao lesao) {
        this.observacao = lesao.getObservacao();
        this.regiaoLesao = lesao.getRegiaoLesao();
        this.gravidade = lesao.getGravidade();
        this.statusAtual = lesao.getStatus();
        this.dataLesao = lesao.getDataLesao() != null ? lesao.getDataLesao().toString() : null;
        this.dataRecuperacao = lesao.getDataRecuperacao() != null ? lesao.getDataRecuperacao().toString() : null;
    }

    public LesaoAppDTO() {
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public String getRegiaoLesao() {
        return regiaoLesao;
    }

    public void setRegiaoLesao(String regiaoLesao) {
        this.regiaoLesao = regiaoLesao;
    }

    public GravidadeLesaoEnum getGravidade() {
        return gravidade;
    }

    public void setGravidade(GravidadeLesaoEnum gravidade) {
        this.gravidade = gravidade;
    }

    public StatusLesaoEnum getStatusAtual() {
        return statusAtual;
    }

    public void setStatusAtual(StatusLesaoEnum statusAtual) {
        this.statusAtual = statusAtual;
    }

    public String getDataLesao() {
        return dataLesao;
    }

    public void setDataLesao(String dataLesao) {
        this.dataLesao = dataLesao;
    }

    public String getDataRecuperacao() {
        return dataRecuperacao;
    }

    public void setDataRecuperacao(String dataRecuperacao) {
        this.dataRecuperacao = dataRecuperacao;
    }
}
