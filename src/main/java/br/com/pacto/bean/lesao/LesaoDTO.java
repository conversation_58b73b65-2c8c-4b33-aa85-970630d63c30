package br.com.pacto.bean.lesao;

import br.com.pacto.objeto.Calendario;
import br.com.pacto.security.dto.UsuarioSimplesDTO;
import br.com.pacto.util.json.ClienteSintenticoJson;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.text.ParseException;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class LesaoDTO {
    private Integer id;
    private Integer matricula;
    private ClienteSintenticoJson aluno;
    private String observacao;
    private String regiaoLesao;
    private GravidadeLesaoEnum gravidade;
    private StatusLesaoEnum status;
    private String descricaoStatus;
    private String dataRegistro; // "yyyy-MM-dd hh:mm:ss"
    private String dataLesao; // "yyyy-MM-dd "
    private UsuarioSimplesDTO usuario;

    public LesaoDTO() {
    }

    public LesaoDTO(Lesao lesao, ClienteSintenticoJson clienteSintenticoJson, String username) throws ParseException {
        this.id = lesao.getCodigo();
        this.matricula = clienteSintenticoJson.getMatricula();
        this.aluno = clienteSintenticoJson;
        this.observacao = lesao.getObservacao();
        this.regiaoLesao = lesao.getRegiaoLesao();
        this.dataRegistro = Calendario.getData(lesao.getDataRegistro(), "yyyy-MM-dd hh:mm:ss");
        this.dataLesao = Calendario.getData(lesao.getDataLesao(), "yyyy-MM-dd");
        this.status = lesao.getStatus();
        this.gravidade = lesao.getGravidade();
        UsuarioSimplesDTO usuarioSimplesDTO = new UsuarioSimplesDTO();
        usuarioSimplesDTO.setId(lesao.getUsuarioLancou_codigo());
        usuarioSimplesDTO.setUsername(username);
        this.usuario = usuarioSimplesDTO;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getMatricula() {
        return matricula;
    }

    public void setMatricula(Integer matricula) {
        this.matricula = matricula;
    }

    public ClienteSintenticoJson getAluno() {
        return aluno;
    }

    public void setAluno(ClienteSintenticoJson aluno) {
        this.aluno = aluno;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public String getRegiaoLesao() {
        return regiaoLesao;
    }

    public void setRegiaoLesao(String regiaoLesao) {
        this.regiaoLesao = regiaoLesao;
    }

    public GravidadeLesaoEnum getGravidade() {
        return gravidade;
    }

    public void setGravidade(GravidadeLesaoEnum gravidade) {
        this.gravidade = gravidade;
    }

    public StatusLesaoEnum getStatus() {
        return status;
    }

    public void setStatus(StatusLesaoEnum status) {
        this.status = status;
    }

    public String getDescricaoStatus() {
        return descricaoStatus;
    }

    public void setDescricaoStatus(String descricaoStatus) {
        this.descricaoStatus = descricaoStatus;
    }

    public String getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(String dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public String getDataLesao() {
        return dataLesao;
    }

    public void setDataLesao(String dataLesao) {
        this.dataLesao = dataLesao;
    }

    public UsuarioSimplesDTO getUsuario() {
        return usuario;
    }

    public void setUsuario(UsuarioSimplesDTO usuario) {
        this.usuario = usuario;
    }

}
