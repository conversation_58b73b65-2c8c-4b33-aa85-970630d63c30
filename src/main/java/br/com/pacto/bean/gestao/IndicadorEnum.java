/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.gestao;

import br.com.pacto.enumerador.agenda.StatusAgendamentoEnum;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public enum IndicadorEnum {
    

    N_AGENDADOS(0, "Agendados", CategoriaIndicadorEnum.AGENDA, null, CategoriaIndicadorEnum.COLUNAS_AGENDA , "nomeAluno", false),
    N_AGEND_EXECUTADOS(1, "Executados", CategoriaIndicadorEnum.AGENDA, StatusAgendamentoEnum.EXECUTADO, CategoriaIndicadorEnum.COLUNAS_AGENDA , "nomeAluno", false),
    N_AGEND_CANCELADOS(2, "Cancelados", CategoriaIndicadorEnum.AGENDA, StatusAgendamentoEnum.CANCELADO, CategoriaIndicadorEnum.COLUNAS_AGENDA , "nomeAluno", false),
    N_AGEND_FALTOU(3, "Faltas", CategoriaIndicadorEnum.AGENDA, StatusAgendamentoEnum.FALTOU, CategoriaIndicadorEnum.COLUNAS_AGENDA, "nomeAluno", false),
    N_PROF_CARTEIRA(4, "Carteira", CategoriaIndicadorEnum.CARTEIRAS_PROF, null, CategoriaIndicadorEnum.COLUNAS_ALUNOS, "nome", false),
    N_PROF_CARTEIRA_SEM_TREINO(5, "SemTreino", CategoriaIndicadorEnum.CARTEIRAS_PROF, null, CategoriaIndicadorEnum.COLUNAS_ALUNOS, "nome", false),
    N_PROF_TREINO_NOVO(6, "Tr.novos", CategoriaIndicadorEnum.ATIVIDADES_PROF, null, CategoriaIndicadorEnum.COLUNAS_NOVOS_RENOVADOS, "nomeAluno", false),
    N_PROF_TREINO_RENOVADO(7, "Tr.renovados", CategoriaIndicadorEnum.ATIVIDADES_PROF, null, CategoriaIndicadorEnum.COLUNAS_NOVOS_RENOVADOS, "nomeAluno", false),
    N_PROF_TREINO_REVISADO(8, "Tr.revisados", CategoriaIndicadorEnum.ATIVIDADES_PROF, null, CategoriaIndicadorEnum.COLUNAS_REVISADOS, "nomeAluno", false),
    N_PROF_TREINO_ACOMPANHADO(9, "Tr.acompanhados", CategoriaIndicadorEnum.ATIVIDADES_PROF, null, CategoriaIndicadorEnum.COLUNAS_ACOMPANHADOS, "nomeAluno", false),
    N_PROF_CARTEIRA_PROX_VENCIMENTO(11, "Prox.Vencimento", CategoriaIndicadorEnum.CARTEIRAS_PROF, null, CategoriaIndicadorEnum.COLUNAS_ALUNOS, "nome", false),
    N_PROF_CARTEIRA_VENCIDOS(10, "Vencidos", CategoriaIndicadorEnum.CARTEIRAS_PROF, null, CategoriaIndicadorEnum.COLUNAS_ALUNOS, "nome", false),
    N_PROF_CARTEIRA_MEDIA_AVALIACAO(12, "Avaliacao", CategoriaIndicadorEnum.CARTEIRAS_PROF, null, CategoriaIndicadorEnum.COLUNAS_AVALIACAO_PER, "nomeAluno", true),
    N_PROF_CARTEIRA_2_ESTRELAS(13, "DuasEstrelas", CategoriaIndicadorEnum.CARTEIRAS_PROF, null, CategoriaIndicadorEnum.COLUNAS_AVALIACAO_NOTA, "nomeAluno", false),
    N_PROF_TREINO_REVISAR(14, "Tr.Revisar", CategoriaIndicadorEnum.ATIVIDADES_PROF, null, CategoriaIndicadorEnum.COLUNAS_NOVOS_RENOVADOS, "nomeAluno", false),
    N_ATIVIDADE_PROF_TREINO_ACOMPANHADO(15, "Tr.Atividade_Acompanhada", CategoriaIndicadorEnum.ATIVIDADES_PROF, null, CategoriaIndicadorEnum.COLUNAS_ATIVIDADE_ACOMPANHADA, "nomeAluno", false),
    ;
    private Integer id;
    private String descricao;
    private CategoriaIndicadorEnum categoria;
    private StatusAgendamentoEnum status;
    private String colunas;
    private String orderBy;
    private boolean media;
    private List classeColunas;


    public List getClasseColunas() {
        return classeColunas;
    }

    public void setClasseColunas(List classeColunas) {
        this.classeColunas = classeColunas;
    }

    private IndicadorEnum(Integer id, final String descricao, CategoriaIndicadorEnum categoria, StatusAgendamentoEnum status,
            String colunas, String orderBy, boolean media) {
        this.id = id;
        this.descricao = descricao;
        this.categoria = categoria;
        this.status = status;
        this.colunas = colunas;
        this.orderBy = orderBy;
        this.media = media;
        montaListaClasseColunas();
    }
    public void montaListaClasseColunas(){
        if(colunas ==CategoriaIndicadorEnum.COLUNAS_AGENDA)
            classeColunas = Arrays.asList(String.class,String.class,Date.class,String.class);
        else if(colunas ==CategoriaIndicadorEnum.COLUNAS_ALUNOS)
            classeColunas = Arrays.asList(int.class,String.class,String.class,Date.class,Date.class);
        else if(colunas ==CategoriaIndicadorEnum.COLUNAS_AVALIACAO_PER)
            classeColunas = Arrays.asList(String.class,Date.class,int.class);
        else if(colunas ==CategoriaIndicadorEnum.COLUNAS_AVALIACAO_NOTA)
            classeColunas = Arrays.asList(String.class,Date.class,int.class);
        else if(colunas ==CategoriaIndicadorEnum.COLUNAS_NOVOS_RENOVADOS)
            classeColunas = Arrays.asList(int.class,String.class,String.class,Date.class,Date.class,Date.class);
        else if(colunas ==CategoriaIndicadorEnum.COLUNAS_REVISADOS)
            classeColunas = Arrays.asList(int.class,String.class,String.class,Date.class,Date.class,Date.class);
        else if(colunas ==CategoriaIndicadorEnum.COLUNAS_ACOMPANHADOS)
            classeColunas = Arrays.asList(int.class,String.class,String.class,Date.class,Date.class);
    }

    public String getOrderBy() {
        return orderBy;
    }

    public void setOrderBy(String orderBy) {
        this.orderBy = orderBy;
    }

    
    public String getColunas() {
        return colunas;
    }

    public void setColunas(String colunas) {
        this.colunas = colunas;
    }
    
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getDescricao() {
        return descricao;
    }
    public String getName() {
        return name();
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public CategoriaIndicadorEnum getCategoria() {
        return categoria;
    }

    public void setCategoria(CategoriaIndicadorEnum categoria) {
        this.categoria = categoria;
    }

    public StatusAgendamentoEnum getStatus() {
        return status;
    }

    public void setStatus(StatusAgendamentoEnum status) {
        this.status = status;
    }

    public boolean isMedia() {
        return media;
    }

    public void setMedia(boolean media) {
        this.media = media;
    }
    
    
}
