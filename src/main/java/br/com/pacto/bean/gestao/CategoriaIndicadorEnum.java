/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.gestao;

/**
 *
 * <AUTHOR>
 */
public enum CategoriaIndicadorEnum {

    AGENDA(0, "Agenda"),
    TREINO(1, "Treino"),
    CARTEIRAS_PROF(2, "Carteiras dos professores"),
    ATIVIDADES_PROF(3, "Atividades dos professores");
    private Integer id;
    private String descricao;
    private static final Integer colSpan = IndicadorEnum.values().length;
    public final static String COLUNAS_AGENDA = "nomeAluno>nomeAluno|evento>evento|inicio<Horário>horario|situacao<Situação>situacao";
    public final static String COLUNAS_ALUNOS = "matriculaLINK<Matrícula>matricula|nomeLINK>nome|situacao<Situação>situacao|dataVigenciaAteAjustada<Término Contrato>dataVigenciaAteAjustadaApresentar|dataPrograma<Término do programa vigente>dataProgramaApresentar(dataPrograma)";
    public final static String COLUNAS_AVALIACAO_PER = "nomeAlunoLINK>nomeAluno|dataInicio<Data>dataInicioApresentar|notaPorcentagem<% Nota>notaPorcentagem";
    public final static String COLUNAS_AVALIACAO_NOTA = "nomeAlunoLINK>nomeAluno|dataInicio<Data>dataInicioApresentar|nota>nota";
    public final static String COLUNAS_NOVOS_RENOVADOS = "matriculaLINK<Matrícula>matricula|nomeAlunoLINK>nomeAluno|situacaoAluno<Situação>situacaoAluno|dataVigenciaAteAjustada<Término Contrato>dataVigenciaAteAjustadaApresentar|dataTerminoPrevisto<Término do programa>dataPrograma|dataLancamento<Data Lançamento>dataLancamentoApresentar";
    public final static String COLUNAS_REVISADOS =       "matriculaLINK<Matrícula>matricula|nomeAlunoLINK>nomeAluno|situacaoAluno<Situação>situacaoAluno|dataVigenciaAteAjustada<Término Contrato>dataVigenciaAteAjustadaApresentar|dataPrograma<Término do programa>dataProgramaApresentar(dataPrograma)|dataRegistro<Data Revisão>dataRegistroApresentar";
    public final static String COLUNAS_ACOMPANHADOS = "matriculaLINK<Matrícula>matricula|nomeAlunoLINK>nomeAluno|situacaoAluno<Situação>situacaoAluno|dataVigenciaAteAjustada<Término Contrato>dataVigenciaAteAjustadaApresentar|inicio<Data Acompanhamento>dataInicioApresentar";
    public final static String COLUNAS_ALUNOS_SIMPLES = "matriculaLINK<Matrícula>matricula|nomeLINK>nome|situacao<Situação>situacao|nomeProfessor<Professor>nomeProfessor";

    public final static String COLUNAS_ALUNOS_DASHBOARD = "codigoLINK<Codigo>codigo|matriculaLINK<Matrícula>matricula|nomeAbreviadoLINK<Nome>nomeAbreviado|nomeProfessorLINK<Professor>nomeProfessor|fotoKeyApp<Foto>fotoKeyApp|situacaoLINK<Situação>situacao";
    public final static String COLUNAS_ALUNOS_FOTOS_DASHBOARD = "codigoLINK<Codigo>codigo|urlFotoLINK<urlFoto>urlFoto|matriculaLINK<Matrícula>matricula|nomeAbreviadoLINK<Nome>nomeAbreviado|nomeProfessorLINK<Professor>nomeProfessor";
    public final static String COLUNAS_TREINO_DASHBOARD = "matriculaLINK<Matrícula>matricula|nomeAbreviadoLINK<Nome>nomeAbreviado|nomeProfessorLINK<Professor>nomeProfessor|dataPrograma<Venc. programa>dataProgramaApresentar(dataPrograma)|dataUltimoacessoLINK<Ultimo. Acesso>dataUltimoacessoApresentar|fotoKeyApp<Foto>fotoKeyApp";
    public final static String COLUNAS_AVALIACAO_TREINO_DASHBOARD = "matriculaLINK<Matrícula>matricula|nomeAlunoLINK<Nome>nomeAluno|dataHoraInicioApresentar<Hora>dataHoraInicioApresentar|comentario<Comentário>comentario|professorCarteiraApresentar<Professor>professorCarteiraApresentar|nota<Nota>nota";
    public final static String COLUNAS_TREINO_REALIZADO_DASHBOARD = "matriculaLINK<Matrícula>matricula|nomeAlunoLINK<Nome>nomeAluno|nomeProfessorLINK<Professor>nomeProfessor|dataHoraInicioApresentar<Hora>dataHoraInicioApresentar";
    public final static String COLUNAS_VENCIMENTO_DASHBOARD = "matriculaLINK<Matrícula>matricula|nomeLINK<Nome>nome|nomeProfessorLINK<Professor>nomeProfessor|dataVigenciaAteAjustadaApresentarLINK<Venc. contrato>dataVigenciaAteAjustadaApresentar(dataVigenciaAteAjustada)";
    public final static String COLUNAS_ACESSO_DASHBOARD = "matricula<Matrícula>matricula|nome<Nome>nome|dthrentrada<Hr. acesso>dthrentrada";
    public final static String COLUNAS_PROGRAMA_REVISAO_DASHBOARD = "matriculaLINK<Matrícula>matricula|nomeAlunoLINK<Nome>nomeAluno|dataPrograma<Venc. programa>dataProgramaApresentar(dataPrograma)|dataRegistro<Data Revisão>dataRegistroApresentar";
    public final static String COLUNAS_PROGRAMA_DASHBOARD = "matriculaLINK<Matrícula>matricula|nomeAlunoLINK<Nome>nomeAluno|dataPrograma<Venc. programa>dataPrograma|dataLancamento<Lançamento>dataLancamentoApresentar";
    public final static String COLUNAS_PROFESSORES_DASHBOARD = "codigo<Código>codigo|nome>nome";
    public final static String COLUNAS_AGENDA_DASHBOARD = "evento>evento|matricula<matricula>|nomeAluno<Nome>nomeAluno|nomeProfessor<Professor>nomeProfessor|inicio<Horário>horario|nomeUsuarioAlterou<Usuario Alterou>nomeUsuarioAlterou|ultimaAlteracaoStr<Últ. alteração>ultimaAlteracaoStr|codigo<Código>codigo|inicioComHora<inicioComHora>inicioComHora|fonteCancelamento<fonteCancelamento>fonteCancelamento";
    
    public final static String COLUNAS_TVGESTOR = "matricula<Matrícula>matricula|nome<Nome>nome|turma>turma|horario<Horário>horario|horaAcesso<Acesso>horaAcesso";
    public final static String COLUNAS_FAVORITOS = "matricula<Matrícula>matricula|nome<Nome>nome";
    public final static String COLUNAS_AVALIACAO_FISICA = "matricula<Matrícula>matricula|nome<Nome>nome";
    public final static String COLUNAS_ATIVIDADE_ACOMPANHADA ="matriculaLINK<Matrícula>matricula|nomeAlunoLINK>nomeAluno|situacaoAluno<Situação>situacaoAluno|dataVigenciaAteAjustada<Término Contrato>dataVigenciaAteAjustadaApresentar|dataPrograma<Término do programa>dataProgramaApresentar(dataPrograma)|dataRegistro<Data Revisão>dataRegistroApresentar";
    
    

    private CategoriaIndicadorEnum(Integer id, String descricao) {
        this.id = id;
        this.descricao = descricao;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Integer getColSpan() {
        return colSpan;
    }
}
