/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.gestao;

import java.util.EnumMap;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public enum GrupoIndicadoresGraficoEnum {
    AGENDA("principal.agenda"),
    AVALIACAO_FISICA("AVALIACAO_FISICA"),
    AVALIACAO("BI_AVALIACAO_TREINO"),
    CARTEIRA("gestao.carteira"),
    PERCENTUAL("percentuais"),
    TIPOS_AGENDA("tipos.agendamento"),
    QTD_AVALIACAO("totalizador.avaliacao"),
    TREINO("TREINO");
    
    private String title;
    
    public Map<IndicadorGraficoEnum, TipoGraficoEnum> getMapa(){
        Map<IndicadorGraficoEnum, TipoGraficoEnum> mapa = new EnumMap<IndicadorGraficoEnum, TipoGraficoEnum>(IndicadorGraficoEnum.class);
        switch(this){
            case AGENDA:
                mapa.put(IndicadorGraficoEnum.IND_AGENDAMENTO_PROFESSORES, TipoGraficoEnum.COLUNA);
                mapa.put(IndicadorGraficoEnum.IND_HORAS_DISPONIBILIDADE, TipoGraficoEnum.LINHA);
                mapa.put(IndicadorGraficoEnum.IND_HORAS_EXECUTADAS, TipoGraficoEnum.LINHA);
                mapa.put(IndicadorGraficoEnum.IND_PERC_OCUPACAO, TipoGraficoEnum.LINHA);
                break;
            case TIPOS_AGENDA:
                mapa.put(IndicadorGraficoEnum.IND_AGENDAMENTO_NOVOS_TREINOS, TipoGraficoEnum.LINHA);
                mapa.put(IndicadorGraficoEnum.IND_AGENDAMENTO_TREINO_RENOVADOS, TipoGraficoEnum.LINHA);
                mapa.put(IndicadorGraficoEnum.IND_AGENDAMENTO_TREINO_REVISADOS, TipoGraficoEnum.LINHA);
                mapa.put(IndicadorGraficoEnum.IND_AGENDAMENTO_AVALIACOES_FISICAS, TipoGraficoEnum.LINHA);
                break;
            case AVALIACAO:
                mapa.put(IndicadorGraficoEnum.IND_MEDIA_AVALIACOES, TipoGraficoEnum.COLUNA);
                mapa.put(IndicadorGraficoEnum.IND_NR_AVALIACOES, TipoGraficoEnum.LINHA);
                break;
            case AVALIACAO_FISICA:
                mapa.put(IndicadorGraficoEnum.IND_COM_AVALIACAO, TipoGraficoEnum.COLUNA);
                mapa.put(IndicadorGraficoEnum.IND_SEM_AVALIACAO, TipoGraficoEnum.COLUNA);
                break;
            case QTD_AVALIACAO:
                mapa.put(IndicadorGraficoEnum.IND_ESTRELA_1, TipoGraficoEnum.COLUNA);
                mapa.put(IndicadorGraficoEnum.IND_ESTRELA_2, TipoGraficoEnum.COLUNA);
                mapa.put(IndicadorGraficoEnum.IND_ESTRELA_3, TipoGraficoEnum.COLUNA);
                mapa.put(IndicadorGraficoEnum.IND_ESTRELA_4, TipoGraficoEnum.COLUNA);
                mapa.put(IndicadorGraficoEnum.IND_ESTRELA_5, TipoGraficoEnum.COLUNA);
                break;
            case PERCENTUAL:
                mapa.put(IndicadorGraficoEnum.IND_PERC_TREINO_EM_DIA, TipoGraficoEnum.COLUNA);
                mapa.put(IndicadorGraficoEnum.IND_PERC_TREINO_VENCIDOS, TipoGraficoEnum.COLUNA);
                break;
            case CARTEIRA:
                mapa.put(IndicadorGraficoEnum.IND_ATIVOS, TipoGraficoEnum.LINHA);
                mapa.put(IndicadorGraficoEnum.IND_TOTAL_ALUNOS, TipoGraficoEnum.LINHA);
                mapa.put(IndicadorGraficoEnum.IND_INATIVOS, TipoGraficoEnum.LINHA);
                mapa.put(IndicadorGraficoEnum.IND_RENOVADOS, TipoGraficoEnum.LINHA);
                mapa.put(IndicadorGraficoEnum.IND_NAO_RENOVADOS, TipoGraficoEnum.LINHA);
                break;
            case TREINO:
                mapa.put(IndicadorGraficoEnum.IND_ATIVOS_TREINO, TipoGraficoEnum.LINHA);
                mapa.put(IndicadorGraficoEnum.IND_EM_DIA, TipoGraficoEnum.LINHA);
                mapa.put(IndicadorGraficoEnum.IND_VENCIDOS, TipoGraficoEnum.LINHA);
                mapa.put(IndicadorGraficoEnum.IND_RENOVAR, TipoGraficoEnum.LINHA);
                break;
        }
        return mapa;
    }

    private GrupoIndicadoresGraficoEnum(String title) {
        this.title = title;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }
    
    
}
