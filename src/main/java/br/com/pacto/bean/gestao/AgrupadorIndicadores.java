/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.gestao;

import br.com.pacto.objeto.Uteis;
import java.util.List;
import com.google.common.base.Optional;
import com.google.common.base.Predicate;
import com.google.common.collect.Iterables;
import java.io.Serializable;
import java.util.ArrayList;

/**
 *
 * <AUTHOR>
 */
public class AgrupadorIndicadores implements Serializable{

    private Integer codProfessor;
    private String nomeProfessor;
    private List<Indicador> indicadores;

    public AgrupadorIndicadores(Integer codProfessor, String nomeProfessor) {
        this.codProfessor = codProfessor;
        this.nomeProfessor = nomeProfessor;
        indicadores = new ArrayList<Indicador>();
    }

    public Integer getCodProfessor() {
        return codProfessor;
    }

    public void setCodProfessor(Integer codProfessor) {
        this.codProfessor = codProfessor;
    }

    public String getNomeProfessor() {
        return nomeProfessor;
    }
    
     public String getNomeAbreviado() {
        try {
            return Uteis.getNomeAbreviandoSomentePrimeiroSobrenome(nomeProfessor);
        } catch (Exception e) {
            return "";
        }
        
    }

    public void setNomeProfessor(String nomeProfessor) {
        this.nomeProfessor = nomeProfessor;
    }

    public List<Indicador> getIndicadores() {
        return indicadores;
    }

    public void setIndicadores(List<Indicador> indicadores) {
        this.indicadores = indicadores;
    }

    public Indicador indicador(final String name) {
        final IndicadorEnum ind = IndicadorEnum.valueOf(name);
        Optional<Indicador> i = Iterables.tryFind(indicadores, new Predicate<Object>() {
            public boolean apply(Object t) {
                return t != null && ((Indicador) t).getTipo() == ind;
            }
        });
        if (i.isPresent()) {
            return i.get();
        } else {
            return new Indicador(codProfessor, nomeProfessor, ind, null, null);
        }
    }
}
