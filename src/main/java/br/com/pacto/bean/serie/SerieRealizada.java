/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.serie;

import br.com.pacto.bean.atividade.AtividadeFicha;
import br.com.pacto.bean.atividade.TipoAtividadeEnum;
import br.com.pacto.bean.notificacao.TipoNotificacaoEnum;
import br.com.pacto.objeto.Uteis;
import java.io.Serializable;
import java.util.Date;
import javax.persistence.CascadeType;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.ManyToOne;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;

/**
 *
 * <AUTHOR>
 */
@Entity
public class SerieRealizada implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo = 0;
    @ManyToOne(cascade = {CascadeType.ALL})
    private TreinoRealizado treinoRealizado;
    @ManyToOne
    private Serie serie;
    @ManyToOne
    private AtividadeFicha atividadeFicha;
    private Integer ordem = 0;
    private Integer repeticao = 0;//
    private Double carga = 0.0;//gramas
    private Integer duracao = 0;//minutos
    private Integer distancia = 0; //metros
    private Double velocidade = 0.0;//km/h
    @Temporal(TemporalType.TIMESTAMP)
    private Date dataInicio;
    @Temporal(TemporalType.TIMESTAMP)
    private Date dataFim;
    @Enumerated(EnumType.ORDINAL)
    private TipoNotificacaoEnum tipoNotificacao;
    @Transient
    private boolean forcarAtualizacaoFicha = false;
    @Transient
    private SerieTO serieAtualizar;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public TreinoRealizado getTreinoRealizado() {
        return treinoRealizado;
    }

    public void setTreinoRealizado(TreinoRealizado treinoRealizado) {
        this.treinoRealizado = treinoRealizado;
    }

    public Serie getSerie() {
        return serie;
    }

    public void setSerie(Serie serie) {
        this.serie = serie;
    }

    public AtividadeFicha getAtividadeFicha() {
        return atividadeFicha;
    }

    public void setAtividadeFicha(AtividadeFicha atividadeFicha) {
        this.atividadeFicha = atividadeFicha;
    }

    public Integer getOrdem() {
        return ordem;
    }

    public void setOrdem(Integer ordem) {
        this.ordem = ordem;
    }

    public Integer getRepeticao() {
        return repeticao;
    }

    public void setRepeticao(Integer repeticao) {
        this.repeticao = repeticao;
    }

    public Double getCarga() {
        return carga;
    }

    public void setCarga(Double carga) {
        this.carga = carga;
    }

    public Integer getDuracao() {
        return duracao;
    }

    public void setDuracao(Integer duracao) {
        this.duracao = duracao;
    }

    public Integer getDistancia() {
        return distancia;
    }

    public void setDistancia(Integer distancia) {
        this.distancia = distancia;
    }

    public Double getVelocidade() {
        return velocidade;
    }

    public void setVelocidade(Double velocidade) {
        this.velocidade = velocidade;
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Date getDataFim() {
        return dataFim;
    }

    public void setDataFim(Date dataFim) {
        this.dataFim = dataFim;
    }

    public TipoNotificacaoEnum getTipoNotificacao() {
        return tipoNotificacao;
    }

    public void setTipoNotificacao(TipoNotificacaoEnum tipoNotificacao) {
        this.tipoNotificacao = tipoNotificacao;
    }

    public boolean isForcarAtualizacaoFicha() {
        return forcarAtualizacaoFicha;
    }

    public void setForcarAtualizacaoFicha(boolean forcarAtualizacaoFicha) {
        this.forcarAtualizacaoFicha = forcarAtualizacaoFicha;
    }

    public SerieTO getSerieAtualizar() {
        return serieAtualizar;
    }

    public void setSerieAtualizar(SerieTO serieAtualizar) {
        this.serieAtualizar = serieAtualizar;
    }
    
    public void ajustarDadosPorTipo(final String valor1, final String valor2) {
        if (atividadeFicha != null) {
            if (atividadeFicha.getAtividade().getTipo().equals(TipoAtividadeEnum.AEROBICO)) {
                if (valor1.contains(":")) {
                    this.setDuracao(Uteis.converterMinutosEmSegundos(valor1));
                } else if (serie.getDuracao().intValue() > 0) {
                    this.setDuracao(Integer.valueOf(valor1));
                } else {
                    this.setDistancia(Integer.valueOf(valor1));
                }
                this.setVelocidade(Double.valueOf(valor2));
            } else if (atividadeFicha.getAtividade().getTipo().equals(TipoAtividadeEnum.ANAEROBICO)) {
                this.setRepeticao(Integer.valueOf(valor1));
                this.setCarga(Double.valueOf(valor2));
            }
        }
    }
    
    private void initSerieTO(boolean reset){
        if (reset) {
            serieAtualizar = null;
        }
        if (serieAtualizar == null) {
            serieAtualizar = new SerieTO(serie, atividadeFicha);
        }
    }

    public void verificarSeDeveAtualizarDadosNaFicha(final String valor1, final String valor2) {
        String dur = "00:00";
        Double veloc = 0.0;
        Integer rep = 0;
        Double carg = 0.0;
        if (atividadeFicha != null) {
            if (atividadeFicha.getAtividade().getTipo().equals(TipoAtividadeEnum.AEROBICO)) {
                dur = valor1;
                veloc = Double.valueOf(valor2);
            } else if (atividadeFicha.getAtividade().getTipo().equals(TipoAtividadeEnum.ANAEROBICO)) {
                rep = Integer.valueOf(valor1);
                carg = Double.valueOf(valor2);
            }
            initSerieTO(true);
            if (getSerie()!= null) {
                if ((getSerie().getRepeticao().intValue() == 0 || forcarAtualizacaoFicha) && rep.intValue() > 0) {
                    initSerieTO(false);
                    getSerieAtualizar().setRepeticao(rep);
                }
                if ((getSerie().getCarga().doubleValue() == 0.0 || forcarAtualizacaoFicha) && carg.doubleValue() > 0.0) {
                    initSerieTO(false);
                    getSerieAtualizar().setCarga(carg);
                }
                if ((getSerie().getDistancia() == 0 || forcarAtualizacaoFicha) && Uteis.converterMinutosEmSegundos(dur) > 0) {
                    initSerieTO(false);
                    getSerieAtualizar().setDuracaoStr(dur);
                }
                if ((getSerie().getVelocidade() == 0.0 || forcarAtualizacaoFicha) && veloc > 0.0) {
                    initSerieTO(false);
                    getSerieAtualizar().setVelocidade(veloc);
                }
            }
        }
    }

    public Double getDescobrirCarga() {
        if (atividadeFicha != null) {
            if (atividadeFicha.getAtividade().getTipo().equals(TipoAtividadeEnum.AEROBICO)) {
                return this.getVelocidade();
            } else if (atividadeFicha.getAtividade().getTipo().equals(TipoAtividadeEnum.ANAEROBICO)) {
                return new Double(this.getCarga());
            }
        }
        return 0.0;
    }

    public String getTextoCarga() {
        if (atividadeFicha != null) {
            double c = getDescobrirCarga();
            if (atividadeFicha.getAtividade().getTipo() == TipoAtividadeEnum.ANAEROBICO) {
                return String.format(" %s  repetições de %s Kg", new Object[]{
                    this.getRepeticao(),
                    //Uteis.arrendondarForcando2CadasDecimaisComVirgula(c > 1.0 ? c / 1000 : 0.0)});
                    Uteis.arrendondarForcando2CadasDecimaisComVirgula(c > 1.0 ? c : 0.0)});
            } else {
                return String.format(" %s minutos à %s Km/h", new Object[]{this.getDuracao(),
                    this.getVelocidade()});
            }
        }
        return "";
    }
}
