/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.serie;

import br.com.pacto.bean.atividade.AtividadeFicha;
import br.com.pacto.bean.atividade.TipoAtividadeEnum;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.util.UteisValidacao;
import servicos.integracao.zw.client.SuperTO;

/**
 *
 * <AUTHOR>
 */
public class SerieTO extends SuperTO {

    private Integer codigo;
    private Integer ordem;
    private Integer repeticao = 0;
    private Double carga = 0.0;//gramas
    private String cargaComp;//generico para carga
    private Integer duracao = 0;//minutos
    private Integer distancia = 0;//metros
    private Double velocidade = 0.0;//km/h    
    private String duracaoStr = null;
    private TipoAtividadeEnum tipo;
    private String repeticaoConcatenado;
    private String cargaConcatenado;
    private Boolean adicionarDescanso = false;
    private String distanciaConcatenado;
    private String velocidadeContatenado;
    private Integer descanso = 0;//segundos
    private String complemento;
    private String repeticaoComp;//generico para repeticao
    private String complementoConcatenado;
    private String setId;
    private String complementoFormat;
    private Integer codigoAtvFicha;
    private boolean serieBiSet = false;
    private String nomeTransient;
    private String cadencia;
    private boolean comDescanso;
    private String cargaApp;
    private String repeticaoApp;
    private Boolean atualizadoApp;
    private Boolean serieRealizada;

    public SerieTO() {
    }

    public SerieTO(final Serie s, AtividadeFicha atividadeFicha) {
        this.codigo = s.getCodigo();
        this.ordem = s.getOrdem();
        this.repeticao = s.getRepeticao();
        this.repeticaoComp = s.getRepeticaoComp();
        this.repeticaoApp = s.getRepeticaoApp();
        this.carga = s.getCarga();
        this.cargaComp = s.getCargaComp();
        this.cargaApp = s.getCargaApp();
        this.atualizadoApp = s.getAtualizadoApp();
        this.duracao = s.getDuracao();
        this.distancia = s.getDistancia();
        this.velocidade = s.getVelocidade();
        this.tipo = atividadeFicha.getAtividade().getTipo();
        this.cadencia = s.getCadencia();
        //
        this.complemento = s.getComplemento();
        this.descanso = s.getDescanso();
        this.adicionarDescanso = s.getAdicionarDescanso();
        this.repeticaoConcatenado = s.getRepeticaoConcatenado();
        this.cargaConcatenado = s.getCargaConcatenado();
        this.setId = atividadeFicha.getSetId();
        this.complementoFormat = s.getComplementoFormat();
        this.serieBiSet = atividadeFicha.getAtividadeAssociada1() != null
                || atividadeFicha.getAtividadeAssociada2() != null;
        this.nomeTransient = atividadeFicha.getNomeTransient();
    }

    public void inicializarNulos() {
        this.duracao = duracao == null ? 0 : duracao;
        this.distancia = distancia == null ? 0 : distancia;
        this.velocidade = velocidade == null ? 0.0 : velocidade;
        this.repeticao = repeticao == null ? 0 : repeticao;
        this.carga = carga == null ? 0 : carga;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getOrdem() {
        return ordem;
    }

    public void setOrdem(Integer ordem) {
        this.ordem = ordem;
    }

    public Integer getRepeticao() {
        if (repeticao == null) {
            repeticao = 0;
        }
        return repeticao;
    }

    public void setRepeticao(Integer repeticao) {
        this.repeticao = repeticao;
    }

    public Double getCarga() {
        if (carga == null) {
            carga = 0.0;
        }
        return carga;
    }

    public void setCarga(Double carga) {
        this.carga = carga;
    }

    public Integer getDuracao() {
        if (duracao == null) {
            duracao = 0;
        }
        return duracao;
    }

    public void setDuracao(Integer duracao) {
        this.duracao = duracao;
    }

    public Integer getDistancia() {
        if (distancia == null) {
            distancia = 0;
        }
        return distancia;
    }

    public void setDistancia(Integer distancia) {
        this.distancia = distancia;
    }

    public Double getVelocidade() {
        if (velocidade == null) {
            velocidade = 0.0;
        }
        return velocidade;
    }

    public void setVelocidade(Double velocidade) {
        this.velocidade = velocidade;
    }

    public String getCargaComp() {
        return cargaComp;
    }

    public void setCargaComp(String cargaComp) {
        this.cargaComp = cargaComp;
    }

    public String getDuracaoStr() {
        if (duracaoStr == null) {
            duracaoStr = Uteis.converterSegundosEmMinutos(duracao);
        }
        return duracaoStr;
    }

    public void setDuracaoStr(String duracaoStr) {
        this.duracaoStr = duracaoStr;
    }

    public TipoAtividadeEnum getTipo() {
        return tipo;
    }

    public void setTipo(TipoAtividadeEnum tipo) {
        this.tipo = tipo;
    }

    public String getRepeticaoConcatenado() {
        return UteisValidacao.emptyString(repeticaoConcatenado) ? getRepeticao().toString() : repeticaoConcatenado;
    }

    public void setRepeticaoConcatenado(String repeticaoConcatenado) {
        this.repeticaoConcatenado = repeticaoConcatenado;
    }

    public String getCargaConcatenado() {
        return UteisValidacao.emptyString(cargaConcatenado) ? getCarga().toString() : cargaConcatenado;
    }

    public void setCargaConcatenado(String cargaConcatenado) {
        this.cargaConcatenado = cargaConcatenado;
    }

    public Boolean getAdicionarDescanso() {
        return adicionarDescanso;
    }

    public void setAdicionarDescanso(Boolean adicionarDescanso) {
        this.adicionarDescanso = adicionarDescanso;
    }

    public String getDistanciaConcatenado() {
        return UteisValidacao.emptyString(distanciaConcatenado) ? getDistancia().toString() : distanciaConcatenado;
    }

    public void setDistanciaConcatenado(String distanciaConcatenado) {
        this.distanciaConcatenado = distanciaConcatenado;
    }

    public String getVelocidadeContatenado() {
        return UteisValidacao.emptyString(velocidadeContatenado) ? getVelocidade().toString() : velocidadeContatenado;
    }

    public void setVelocidadeContatenado(String velocidadeContatenado) {
        this.velocidadeContatenado = velocidadeContatenado;
    }

    public Integer getDescanso() {
        return descanso;
    }

    public void setDescanso(Integer descanso) {
        this.descanso = descanso;
    }

    public String getComplemento() {
        return complemento;
    }

    public void setComplemento(String complemento) {
        this.complemento = complemento;
    }

    public String getRepeticaoComp() {
        return repeticaoComp;
    }

    public void setRepeticaoComp(String repeticaoComp) {
        this.repeticaoComp = repeticaoComp;
    }

    public String getComplementoConcatenado() {
        return complementoConcatenado;
    }

    public void setComplementoConcatenado(String complementoConcatenado) {
        this.complementoConcatenado = complementoConcatenado;
    }

    public String getSetId() {
        return setId;
    }

    public void setSetId(String setId) {
        this.setId = setId;
    }

    public String getComplementoFormat() {
        return complementoFormat;
    }

    public void setComplementoFormat(String complementoFormat) {
        this.complementoFormat = complementoFormat;
    }

    public Integer getCodigoAtvFicha() {
        return codigoAtvFicha;
    }

    public void setCodigoAtvFicha(Integer codigoAtvFicha) {
        this.codigoAtvFicha = codigoAtvFicha;
    }

    public boolean isSerieBiSet() {
        return serieBiSet;
    }

    public void setSerieBiSet(boolean serieBiSet) {
        this.serieBiSet = serieBiSet;
    }

    public String getNomeTransient() {
        return nomeTransient;
    }

    public void setNomeTransient(String nomeTransient) {
        this.nomeTransient = nomeTransient;
    }

    public String getCadencia() {
        return cadencia;
    }

    public void setCadencia(String cadencia) {
        this.cadencia = cadencia;
    }

    public boolean isComDescanso() {
        return comDescanso;
    }

    public void setComDescanso(boolean comDescanso) {
        this.comDescanso = comDescanso;
    }

    public String getValor1(final TipoAtividadeEnum tipo) {
        if (TipoAtividadeEnum.ANAEROBICO.equals(tipo)) {
            return (getRepeticaoConcatenado() != null ? getRepeticaoConcatenado().toString() : "");
        } else {
            return (getDuracaoStr() != null && !getDuracaoStr().equals("00:00")
                    ? getDuracaoStr() : getDistanciaConcatenado());
        }
    }

    public String getValor2(final TipoAtividadeEnum tipo) {
        if (tipo == TipoAtividadeEnum.ANAEROBICO) {
            return (getCargaConcatenado());
        } else {
            return (getVelocidadeContatenado());
        }
    }

    public String getValor3(final TipoAtividadeEnum tipo) {
        if (tipo == TipoAtividadeEnum.ANAEROBICO) {
            return "";
        } else {
            return (getDuracaoStr() != null && !getDuracaoStr().equals("00:00")
                    ? (getDistanciaConcatenado() == null ? "" : getDistanciaConcatenado()) : "00:00");
        }
    }

    public void ajustarDadosPorTipo(final String valor1, final String valor2) {
        if (TipoAtividadeEnum.AEROBICO.equals(tipo)) {
            if (valor1.contains(":")) {
                this.setDuracao(Uteis.converterMinutosEmSegundos(valor1));
            } else if (getDuracao().intValue() > 0) {
                this.setDuracao(Integer.valueOf(valor1));
            } else {
                this.setDistancia(Integer.valueOf(valor1));
            }
            this.setVelocidade(Double.valueOf(valor2));
        } else if (TipoAtividadeEnum.ANAEROBICO.equals(tipo)) {
            this.setRepeticao(Integer.valueOf(valor1));
            this.setCarga(Double.valueOf(valor2));
        }
    }

    private static String concatenarString(Object valor1, Object valor2, Object valor3) {
        StringBuilder i = new StringBuilder();
        String lig = "";
        if (valor1 != null && !valor1.equals("")) {
            i.append(valor1);
            lig = "/";
        }
        if (valor2 != null && !valor2.equals("")) {
            i.append(lig);
            i.append(valor2);
        }
        if (valor3 != null && !valor3.equals("")) {
            i.append(lig);
            i.append(valor3);
        }
        return i.toString();
    }

    public static SerieTO concatenarSeries(SerieTO serie1, SerieTO serie2, SerieTO serie3) {
        SerieTO serie = serie1 == null ? (serie2 == null ? new SerieTO() : serie2) : serie1;
        if (serie1.getAdicionarDescanso() && serie1.getDescanso() > 0) {
            serie.setDescanso(serie1.getDescanso());
        } else if (serie2 != null && serie2.getAdicionarDescanso() && serie2.getDescanso() > 0) {
            serie.setDescanso(serie2.getDescanso());
        } else if (serie3 != null && serie3.getAdicionarDescanso() && serie3.getDescanso() > 0) {
            serie.setDescanso(serie3.getDescanso());
        }
        serie.setCargaComp(concatenarString(serie == null ? null : serie.getCargaComp(), serie2 == null ? null : serie2.getCargaComp(), serie3 == null ? null : serie3.getCargaComp()));
        serie.setRepeticaoComp(concatenarString(serie == null ? null : serie.getRepeticaoComp(), serie2 == null ? null : serie2.getRepeticaoComp(), serie3 == null ? null : serie3.getRepeticaoComp()));
        serie.setCargaApp(concatenarString(serie == null ? null : serie.getCargaApp(), serie2 == null ? null : serie2.getCargaApp(), serie3 == null ? null : serie3.getCargaApp()));
        serie.setRepeticaoApp(concatenarString(serie == null ? null : serie.getRepeticaoApp(), serie2 == null ? null : serie2.getRepeticaoApp(), serie3 == null ? null : serie3.getRepeticaoApp()));
        serie.setCargaConcatenado(concatenarString(serie == null ? null : serie.getCarga(), serie2 == null ? null : serie2.getCarga(), serie3 == null ? null : serie3.getCarga()));

        serie.setRepeticaoConcatenado(concatenarString(
                serie == null ? null
                : serie.getValor1(serie.getTipo()),
                serie2 == null ? null : serie2.getValor1(serie2.getTipo()),
                serie3 == null ? null : serie3.getValor1(serie3.getTipo())));

        serie.setVelocidadeContatenado(
                concatenarString(
                serie == null ? null : serie.getValor2(serie.getTipo()),
                serie2 == null ? null : serie2.getValor2(serie2.getTipo()),
                serie3 == null ? null : serie3.getValor2(serie3.getTipo())));
        serie.setDistanciaConcatenado(concatenarString(
                serie == null ? null : (serie.getTipo().equals(TipoAtividadeEnum.ANAEROBICO) ? "" : serie.getValor3(serie.getTipo())),
                serie2 == null ? null : (serie2.getTipo().equals(TipoAtividadeEnum.ANAEROBICO) ? "0" : serie2.getValor3(serie2.getTipo())),
                serie3 == null ? null : (serie3.getTipo().equals(TipoAtividadeEnum.ANAEROBICO) ? "0" : serie3.getValor3(serie3.getTipo()))));
        serie.setDuracaoStr(serie.getRepeticaoConcatenado());
        serie.setComplementoConcatenado(concatenarString(serie == null ? null : UteisValidacao.emptyString(serie.getComplemento()) ? "" : serie.getComplemento(),
                serie2 == null ? null : UteisValidacao.emptyString(serie2.getComplemento()) ? "" : serie2.getComplemento(),
                serie3 == null ? null : UteisValidacao.emptyString(serie3.getComplemento()) ? "" : serie3.getComplemento()));
        return serie;
    }

    public String getCargaApp() {
        return cargaApp;
    }

    public void setCargaApp(String cargaApp) {
        this.cargaApp = cargaApp;
    }

    public String getRepeticaoApp() {
        return repeticaoApp;
    }

    public void setRepeticaoApp(String repeticaoApp) {
        this.repeticaoApp = repeticaoApp;
    }

    public Boolean getAtualizadoApp() {
        if (atualizadoApp == null)
            atualizadoApp = false;
        return atualizadoApp;
    }

    public void setAtualizadoApp(Boolean atualizadoApp) {
        this.atualizadoApp = atualizadoApp;
    }

    public Boolean getSerieRealizada() {
        if (serieRealizada == null){
            serieRealizada = false;
        }
        return serieRealizada;
    }

    public void setSerieRealizada(Boolean serieRealizada) { this.serieRealizada = serieRealizada; }
}
