/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.serie;

import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.professor.ProfessorSintetico;
import br.com.pacto.bean.programa.OrigemExecucaoEnum;
import br.com.pacto.bean.programa.ProgramaTreinoFicha;
import br.com.pacto.objeto.Uteis;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.persistence.*;

import br.com.pacto.util.UteisValidacao;
import org.hibernate.annotations.Type;

/**
 *
 * <AUTHOR>
 */
@Entity
public class TreinoRealizado implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    @ManyToOne
    private ClienteSintetico cliente;
    @Temporal(TemporalType.TIMESTAMP)
    private Date dataInicio;
    @Temporal(TemporalType.TIMESTAMP)
    private Date dataFim;
    @ManyToOne(fetch = FetchType.LAZY)
    private ProgramaTreinoFicha programaTreinoFicha;
    @ManyToOne(fetch = FetchType.LAZY)
    private ProfessorSintetico professor;
    private Integer tempoUtil;
    private String nota;
    @ManyToOne(fetch = FetchType.LAZY)
    private ProfessorSintetico professorAcompanhamento;
    @Enumerated(EnumType.ORDINAL)
    private OrigemExecucaoEnum origem;
    @Lob
    @Type(type="org.hibernate.type.StringClobType")
    @Column(columnDefinition = "text", length = 9999)
    private String comentario;
    //Este flag identifica se a ficha executada foi a ficha do dia ou
    // aluno selecionou outra ficha
    @Column(columnDefinition ="boolean default true")
    private Boolean executadoFichaDia = Boolean.TRUE;
    private String unidadeExecucao;
    private String chaveExecucao;

    public Integer getNotaInt(){
        try {
            return Integer.valueOf(nota);
        } catch (Exception e) {
            return 0;
        }
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public ClienteSintetico getCliente() {
        return cliente;
    }

    public void setCliente(ClienteSintetico cliente) {
        this.cliente = cliente;
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public String getDataInicioApresentar() {
        try {
            return Uteis.getData(getDataInicio());
        } catch (Exception e) {
            return "";
        }
    }

    public String getDataHoraInicioApresentar() {
        try {
            return Uteis.getDataAplicandoFormatacao(getDataInicio(), "dd/MM/yy HH:mm");
        } catch (Exception e) {
            return "";
        }
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Date getDataFim() {
        return dataFim;
    }

    public void setDataFim(Date dataFim) {
        this.dataFim = dataFim;
    }

    public ProgramaTreinoFicha getProgramaTreinoFicha() {
        return programaTreinoFicha;
    }

    public void setProgramaTreinoFicha(ProgramaTreinoFicha programaTreinoFicha) {
        this.programaTreinoFicha = programaTreinoFicha;
    }

    public ProfessorSintetico getProfessor() {
        return professor;
    }

    public void setProfessor(ProfessorSintetico professor) {
        this.professor = professor;
    }

    public Integer getTempoUtil() {
        return tempoUtil;
    }

    public void setTempoUtil(Integer tempoUtil) {
        this.tempoUtil = tempoUtil;
    }

    public String getNota() {
        return nota;
    }

    public void setNota(String nota) {
        this.nota = nota;
    }

    public ProfessorSintetico getProfessorAcompanhamento() {
        return professorAcompanhamento;
    }

    public void setProfessorAcompanhamento(ProfessorSintetico professorAcompanhamento) {
        this.professorAcompanhamento = professorAcompanhamento;
    }

    public OrigemExecucaoEnum getOrigem() {
        return origem;
    }

    public void setOrigem(OrigemExecucaoEnum origem) {
        this.origem = origem;
    }

    public String getNomeAluno() {
        return getCliente().getNomeAbreviado();
    }
    public String getMatricula() {
        return getCliente().getMatriculaString();
    }

    public String getNotaPorcentagem() {
        try {
            return nota == null ? "0%" : new Integer(Integer.valueOf(nota) * 100 / 5).toString() + "%";
        } catch (Exception e) {
            return "0%";
        }
    }

    public Integer getChaveLink() {
        return getCliente().getCodigo();
    }

    public List<String> getEstrelas() {
        List<String> stars = new ArrayList<String>();
        try {
            Integer mediaValorAvaliacao = Integer.valueOf(nota);
            for (int i = 1; i <= 5; i++) {
                if (i > mediaValorAvaliacao && (i + 1) < mediaValorAvaliacao) {
                    stars.add("icon-star-half-empty");
                } else if (i <= mediaValorAvaliacao) {
                    stars.add("icon-star");
                } else if (i > mediaValorAvaliacao) {
                    stars.add("icon-star-empty ");
                }
            }
        } catch (Exception e) {
        }

        return stars;
    }

    public String getNomeProfessor(){
        try {
            return getProfessor().getNomeAbreviado();
        } catch (Exception e) {
            return "";
        }
    }

    public String monteSubtitulo() {
        return getProgramaTreinoFicha().getNomeFicha() + (getExecutadoFichaDia() ?  " (ficha do dia) - selecionado automaticamente" : " (não é a ficha do dia) - selecionada pelo aluno") +
                (getOrigem() != null ? " - " + getOrigem().getNome() : "");
    }

    public Boolean getExecutadoFichaDia() {
        return executadoFichaDia;
    }

    public void setExecutadoFichaDia(Boolean executadoFichaDia) {
        this.executadoFichaDia = executadoFichaDia;
    }

    public String getComentario() {
        return comentario;
    }
    public String getComentarioApresentar(){
        return UteisValidacao.emptyString(comentario) ? "" :  comentario.substring(0,comentario.length() > 15 ? 15 : comentario.length()) + (comentario.length() > 15 ? "..." : "");
    }
    public void setComentario(String comentario) {
        this.comentario = comentario;
    }


    public String getProfessorCarteiraApresentar(){
        if(getProfessor() == null){
            return "";
        }
        return getProfessor().getNomeAbreviado();
    }
    
    public static void preencherListaAtributoValor(TreinoRealizado tr,
            List p, List v) {
        if (tr.getDataInicio() != null) {
            p.add("dataInicio");
        }
        v.add(tr.getDataInicio());
        if (tr.getDataFim() != null) {
            p.add("dataFim");
            v.add(tr.getDataFim());
        }
        if (tr.getComentario() != null) {
            p.add("comentario");
            v.add(tr.getComentario());
        }
        if (tr.getExecutadoFichaDia() != null) {
            p.add("executadofichadia");
            v.add(tr.getExecutadoFichaDia());
        }
        if (tr.getNota() != null) {
            p.add("nota");
            v.add(tr.getNota());
        }
        if (tr.getOrigem() != null) {
            p.add("origem");
            v.add(tr.getOrigem().getId());
        }
        if (tr.getTempoUtil() != null) {
            p.add("tempoutil");
            v.add(tr.getTempoUtil());
        }
        if (tr.getCliente() != null && tr.getCliente().getCodigo() != null) {
            p.add("cliente_codigo");
            v.add(tr.getCliente().getCodigo());
        }
        if (tr.getProfessor() != null && tr.getProfessor().getCodigo() != null) {
            p.add("professor_codigo");
            v.add(tr.getProfessor().getCodigo());
        }
        if (tr.getProfessorAcompanhamento() != null && tr.getProfessorAcompanhamento().getCodigo() != null) {
            p.add("professoracompanhamento_codigo");
            v.add(tr.getProfessorAcompanhamento().getCodigo());
        }
        if (tr.getProgramaTreinoFicha()!= null && tr.getProgramaTreinoFicha().getCodigo() != null) {
            p.add("programatreinoficha_codigo");
            v.add(tr.getProgramaTreinoFicha().getCodigo());
        }
    }

    public String getUnidadeExecucao() {
        return unidadeExecucao;
    }

    public void setUnidadeExecucao(String unidadeExecucao) {
        this.unidadeExecucao = unidadeExecucao;
    }

    public String getChaveExecucao() {
        return chaveExecucao;
    }

    public void setChaveExecucao(String chaveExecucao) {
        this.chaveExecucao = chaveExecucao;
    }
}
