/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.aula;

import br.com.pacto.bean.usuario.Usuario;

import javax.persistence.*;
import java.util.Date;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table
public class ProfessorSubstituido {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private Integer codigoHorarioTurma;
    private Integer codigoProfessorOriginal;
    private Integer codigoProfessorSubstituto;
    @Temporal(TemporalType.TIMESTAMP)
    private Date diaAula;
    @Temporal(TemporalType.TIMESTAMP)
    private Date dataSubstituicao;
    private Integer usuarioSubstituiu_codigo;
    private String justificativa;
    @Transient
    private String nomeProfessorOrigem;
    @Transient
    private String nomeProfessorSubstituto;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getCodigoProfessorOriginal() {
        return codigoProfessorOriginal;
    }

    public void setCodigoProfessorOriginal(Integer codigoProfessorOriginal) {
        this.codigoProfessorOriginal = codigoProfessorOriginal;
    }

    public Integer getCodigoProfessorSubstituto() {
        return codigoProfessorSubstituto;
    }

    public void setCodigoProfessorSubstituto(Integer codigoProfessorSubstituto) {
        this.codigoProfessorSubstituto = codigoProfessorSubstituto;
    }

    public Date getDiaAula() {
        return diaAula;
    }

    public void setDiaAula(Date diaAula) {
        this.diaAula = diaAula;
    }

    public Date getDataSubstituicao() {
        return dataSubstituicao;
    }

    public void setDataSubstituicao(Date dataSubstituicao) {
        this.dataSubstituicao = dataSubstituicao;
    }

    public Integer getUsuarioSubstituiu_codigo() {
        return usuarioSubstituiu_codigo;
    }

    public void setUsuarioSubstituiu_codigo(Integer usuarioSubstituiu) {
        this.usuarioSubstituiu_codigo = usuarioSubstituiu;
    }


    public String getJustificativa() {
        return justificativa;
    }

    public void setJustificativa(String justificativa) {
        this.justificativa = justificativa;
    }

    public Integer getCodigoHorarioTurma() {
        return codigoHorarioTurma;
    }

    public void setCodigoHorarioTurma(Integer codigoHorarioTurma) {
        this.codigoHorarioTurma = codigoHorarioTurma;
    }

    public String getNomeProfessorOrigem() {
        return nomeProfessorOrigem;
    }

    public void setNomeProfessorOrigem(String nomeProfessorOrigem) {
        this.nomeProfessorOrigem = nomeProfessorOrigem;
    }

    public String getNomeProfessorSubstituto() {
        return nomeProfessorSubstituto;
    }

    public void setNomeProfessorSubstituto(String nomeProfessorSubstituto) {
        this.nomeProfessorSubstituto = nomeProfessorSubstituto;
    }
}
