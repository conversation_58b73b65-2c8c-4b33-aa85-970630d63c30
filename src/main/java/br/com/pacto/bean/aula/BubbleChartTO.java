/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.aula;

/**
 *
 * <AUTHOR>
 */
public class BubbleChartTO {
    
    private String nome;
    private Integer modalidade;
    private Integer capacidade = 0;
    private Integer capacidadeTotal = 0;
    private Integer ocupacao = 0;
    private Integer percentualOcupacao = 0;

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Integer getModalidade() {
        return modalidade;
    }

    public void setModalidade(Integer modalidade) {
        this.modalidade = modalidade;
    }
    

    public Integer getCapacidade() {
        return capacidade;
    }

    public void setCapacidade(Integer capacidade) {
        this.capacidade = capacidade;
    }

    public Integer getPercentualOcupacao() {
        return percentualOcupacao;
    }

    public void setPercentualOcupacao(Integer percentualOcupacao) {
        this.percentualOcupacao = percentualOcupacao;
    }

    public Integer getOcupacao() {
        return ocupacao;
    }

    public void setOcupacao(Integer ocupacao) {
        this.ocupacao = ocupacao;
    }

    public Integer getCapacidadeTotal() {
        return capacidadeTotal;
    }

    public void setCapacidadeTotal(Integer capacidadeTotal) {
        this.capacidadeTotal = capacidadeTotal;
    }
    
    
    
}
