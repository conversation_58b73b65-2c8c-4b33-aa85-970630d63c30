package br.com.pacto.bean.aula;

import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.locacao.LocacaoHorario;
import br.com.pacto.controller.json.locacao.AgendamentoLocacaoDTO;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table
public class AgendamentoLocacao implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    @ManyToOne(fetch = FetchType.LAZY)
    private ClienteSintetico cliente;
    @ManyToOne
    @JoinColumn(name = "LocacaoHorario", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private LocacaoHorario locacaoHorario;
    private Integer ambiente;
    private Integer vendaAvulsa;
    private Integer usuarioZW;
    private Integer usuarioResponsavelZW;
    @Temporal(TemporalType.TIMESTAMP)
    private Date inicio;
    @Temporal(TemporalType.TIMESTAMP)
    private Date fim;
    private Double valorLocacao;
    private Double valorTotal;
    private Boolean isCancelado;
    private String justificativa;
    @Temporal(TemporalType.TIMESTAMP)
    private Date dataCancelamento;
    private Integer usuarioCancelou;
    private Boolean isFinalizado;
    private Boolean modificadoPeloAluno;

    public AgendamentoLocacao() {
    }

    public AgendamentoLocacao(AgendamentoLocacaoDTO agendamentoDTO,
                              ClienteSintetico cliente,
                              LocacaoHorario locacaoHorario) {
        this.cliente = cliente;
        this.locacaoHorario = locacaoHorario;
        this.ambiente = agendamentoDTO.getAmbiente();
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public ClienteSintetico getCliente() {
        return cliente;
    }

    public void setCliente(ClienteSintetico cliente) {
        this.cliente = cliente;
    }

    public LocacaoHorario getLocacaoHorario() {
        return locacaoHorario;
    }

    public void setLocacaoHorario(LocacaoHorario locacaoHorario) {
        this.locacaoHorario = locacaoHorario;
    }

    public Integer getAmbiente() {
        return ambiente;
    }

    public void setAmbiente(Integer ambiente) {
        this.ambiente = ambiente;
    }

    public Integer getVendaAvulsa() {
        return vendaAvulsa;
    }

    public void setVendaAvulsa(Integer vendaAvulsa) {
        this.vendaAvulsa = vendaAvulsa;
    }

    public Integer getUsuarioZW() {
        return usuarioZW;
    }

    public void setUsuarioZW(Integer usuarioZW) {
        this.usuarioZW = usuarioZW;
    }

    public Date getInicio() {
        return inicio;
    }

    public void setInicio(Date inicio) {
        this.inicio = inicio;
    }

    public Date getFim() {
        return fim;
    }

    public void setFim(Date fim) {
        this.fim = fim;
    }

    public Double getValorTotal() {
        return valorTotal;
    }

    public void setValorTotal(Double valorTotal) {
        this.valorTotal = valorTotal;
    }

    public Integer getUsuarioResponsavelZW() {
        return usuarioResponsavelZW;
    }

    public void setUsuarioResponsavelZW(Integer usuarioResponsavelZW) {
        this.usuarioResponsavelZW = usuarioResponsavelZW;
    }

    public Double getValorLocacao() {
        return valorLocacao;
    }

    public void setValorLocacao(Double valorLocacao) {
        this.valorLocacao = valorLocacao;
    }

    public Boolean getCancelado() {
        if (isCancelado == null) {
            return false;
        }
        return isCancelado;
    }

    public void setCancelado(Boolean cancelado) {
        isCancelado = cancelado;
    }

    public String getJustificativa() {
        return justificativa;
    }

    public void setJustificativa(String justificativa) {
        this.justificativa = justificativa;
    }

    public Date getDataCancelamento() {
        return dataCancelamento;
    }

    public void setDataCancelamento(Date dataCancelamento) {
        this.dataCancelamento = dataCancelamento;
    }

    public Integer getUsuarioCancelou() {
        return usuarioCancelou;
    }

    public void setUsuarioCancelou(Integer usuarioCancelou) {
        this.usuarioCancelou = usuarioCancelou;
    }

    public Boolean getFinalizado() {
        if (isFinalizado == null) {
            return false;
        }
        return isFinalizado;
    }

    public void setFinalizado(Boolean finalizado) {
        isFinalizado = finalizado;
    }

    public Boolean getModificadoPeloAluno() {
        return modificadoPeloAluno;
    }

    public void setModificadoPeloAluno(Boolean modificadoPeloAluno) {
        this.modificadoPeloAluno = modificadoPeloAluno;
    }
}
