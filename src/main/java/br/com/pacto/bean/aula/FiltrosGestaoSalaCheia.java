/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.aula;

import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.faces.model.SelectItem;
import javax.persistence.Temporal;

/**
 *
 * <AUTHOR>
 */
public class FiltrosGestaoSalaCheia implements Serializable{
    
    private List<ClienteSintetico> alunos = new ArrayList<ClienteSintetico>();
    private List<SelectItem> itensProfessores = new ArrayList<SelectItem>();
    private List<String> professoresSelecionados = new ArrayList<String>();
    private List<SelectItem> itensDiasSemana = new ArrayList<SelectItem>();
    private List<SelectItem> itensTurmas = new ArrayList<SelectItem>();
    private List<SelectItem> itensHorarios = new ArrayList<SelectItem>();
    private List<String> diasSemanaSelecionados = new ArrayList<String>();
    private List<String> turmasSelecionadas = new ArrayList<String>();
    private List<SelectItem> itensModalidades = new ArrayList<SelectItem>();
    private List<String> modalidadesSelecionadas = new ArrayList<String>();
    private List<String> horariosSelecionadas = new ArrayList<String>();
    private List<String> ambientesSelecionados = new ArrayList<String>();
    private List<SelectItem> itensAmbientes = new ArrayList<SelectItem>();
    private Boolean mostrarAulasZeradas = Boolean.FALSE;
    private Date inicio;
    private Date fim;
    private Integer dias;
    
    public String getPeriodo(){
        return "Periodo: "+Uteis.getData(inicio)+" a "+Uteis.getData(fim);
    }

    public FiltrosGestaoSalaCheia(Integer dias) {
        this.dias = dias;
        inicio = Uteis.somarDias(Calendario.hoje(), -this.dias);
        fim = Calendario.hoje();
    }
    
    public static void copiarValores(FiltrosGestaoSalaCheia original, FiltrosGestaoSalaCheia copia){
        copia.setAlunos(original.getAlunos());
        copia.setModalidadesSelecionadas(original.getModalidadesSelecionadas());
        copia.setAmbientesSelecionados(original.getAmbientesSelecionados());
        copia.setProfessoresSelecionados(original.getProfessoresSelecionados());
        copia.setDiasSemanaSelecionados(original.getDiasSemanaSelecionados());
        copia.setHorariosSelecionadas(original.getHorariosSelecionadas());
        copia.setTurmasSelecionadas(original.getTurmasSelecionadas());
        copia.setInicio(original.getInicio());
        copia.setFim(original.getFim());
    }

    public List<ClienteSintetico> getAlunos() {
        return alunos;
    }

    public void setAlunos(List<ClienteSintetico> alunos) {
        this.alunos = alunos;
    }

    public List<SelectItem> getItensProfessores() {
        return itensProfessores;
    }

    public void setItensProfessores(List<SelectItem> itensProfessores) {
        this.itensProfessores = itensProfessores;
    }

    public List<String> getProfessoresSelecionados() {
        return professoresSelecionados;
    }

    public void setProfessoresSelecionados(List<String> professoresSelecionados) {
        this.professoresSelecionados = professoresSelecionados;
    }

    public List<SelectItem> getItensDiasSemana() {
        return itensDiasSemana;
    }

    public List<SelectItem> getItensTurmas() {
        return itensTurmas;
    }

    public void setItensTurmas(List<SelectItem> itensTurmas) {
        this.itensTurmas = itensTurmas;
    }

    public void setItensDiasSemana(List<SelectItem> itensDiasSemana) {
        this.itensDiasSemana = itensDiasSemana;
    }

    public List<String> getDiasSemanaSelecionados() {
        return diasSemanaSelecionados;
    }

    public List<String> getTurmasSelecionadas() {
        return turmasSelecionadas;
    }

    public List<SelectItem> getItensHorarios() {
        return itensHorarios;
    }

    public void setItensHorarios(List<SelectItem> itensHorarios) {
        this.itensHorarios = itensHorarios;
    }

    public void setTurmasSelecionadas(List<String> turmasSelecionadas) {
        this.turmasSelecionadas = turmasSelecionadas;
    }

    public void setDiasSemanaSelecionados(List<String> diasSemanaSelecionados) {
        this.diasSemanaSelecionados = diasSemanaSelecionados;
    }

    public List<SelectItem> getItensModalidades() {
        return itensModalidades;
    }

    public void setItensModalidades(List<SelectItem> itensModalidades) {
        this.itensModalidades = itensModalidades;
    }

    public List<String> getModalidadesSelecionadas() {
        return modalidadesSelecionadas;
    }

    public void setModalidadesSelecionadas(List<String> modalidadesSelecionadas) {
        this.modalidadesSelecionadas = modalidadesSelecionadas;
    }

    public List<String> getHorariosSelecionadas() {
        if(horariosSelecionadas == null){
            horariosSelecionadas = new ArrayList<String>();
        }
        return horariosSelecionadas;
    }

    public void setHorariosSelecionadas(List<String> horariosSelecionadas) {
        this.horariosSelecionadas = horariosSelecionadas;
    }

    public Date getInicio() {
        return inicio;
    }

    public void setInicio(Date inicio) {
        this.inicio = inicio;
    }

    public Date getFim() {

        return fim;
    }

    public void setFim(Date fim) {
        this.fim = fim;
    }

    public List<String> getAmbientesSelecionados() {
        return ambientesSelecionados;
    }

    public void setAmbientesSelecionados(List<String> ambientesSelecionados) {
        this.ambientesSelecionados = ambientesSelecionados;
    }

    public List<SelectItem> getItensAmbientes() {
        return itensAmbientes;
    }

    public void setItensAmbientes(List<SelectItem> itensAmbientes) {
        this.itensAmbientes = itensAmbientes;
    }

    public Boolean getMostrarAulasZeradas() {
        return mostrarAulasZeradas;
    }

    public void setMostrarAulasZeradas(Boolean mostrarAulasZeradas) {
        this.mostrarAulasZeradas = mostrarAulasZeradas;
    }

    public Integer getDias() {
        return dias;
    }

    public void setDias(Integer dias) {
        this.dias = dias;
    }
    
    
}
