/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.aula;

import br.com.pacto.bean.professor.ProfessorSintetico;
import br.com.pacto.util.enumeradores.PaletaCoresEnum;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class GestaoSalaCheiaTO implements Serializable{
    
    private Integer valor;
    private IndicadorGestaoSalaCheiaEnum indicador;
    private Map<ProfessorSintetico,Integer> mapaProfessores = new HashMap<ProfessorSintetico, Integer>();
    private Map<Integer,Map<String,BubbleChartTO>> mapaBubbleChart = new HashMap<Integer,Map<String,BubbleChartTO>>();
    
    
    public String getNome(){
        try {
            return indicador.name();
        } catch (Exception e) {
            return "";
        }
    }
    
    public String getCor(){
        try {
            return indicador.getCor().getCor();
        } catch (Exception e) {
            return PaletaCoresEnum.AZUL_ESCURO_A.getCor();
        }
    }
    public Integer getOrdinal(){
        try {
            return indicador.ordinal();
        } catch (Exception e) {
            return -1;
        }
    }

    public GestaoSalaCheiaTO(Integer valor, IndicadorGestaoSalaCheiaEnum indicador) {
        this.valor = valor;
        this.indicador = indicador;
    }
    
    private List<AulaDiaExcecao> excecoes = new ArrayList<AulaDiaExcecao>();
    private List<AulaAluno> alunos = new ArrayList<AulaAluno>();
    private List<AulaDia> aulas = new ArrayList<AulaDia>();

    public List<AulaDiaExcecao> getExcecoes() {
        return excecoes;
    }

    public void setExcecoes(List<AulaDiaExcecao> excecoes) {
        this.excecoes = excecoes;
    }

    public List<AulaAluno> getAlunos() {
        return alunos;
    }

    public void setAlunos(List<AulaAluno> alunos) {
        this.alunos = alunos;
    }
    
    public Integer getValor() {
        return valor;
    }

    public void setValor(Integer valor) {
        this.valor = valor;
    }

    public IndicadorGestaoSalaCheiaEnum getIndicador() {
        return indicador;
    }

    public void setIndicador(IndicadorGestaoSalaCheiaEnum indicador) {
        this.indicador = indicador;
    }

    public List<AulaDia> getAulas() {
        return aulas;
    }

    public void setAulas(List<AulaDia> aulas) {
        this.aulas = aulas;
    }

    public Map<ProfessorSintetico, Integer> getMapaProfessores() {
        return mapaProfessores;
    }

    public void setMapaProfessores(Map<ProfessorSintetico, Integer> mapaProfessores) {
        this.mapaProfessores = mapaProfessores;
    }

    public Map<Integer, Map<String, BubbleChartTO>> getMapaBubbleChart() {
        return mapaBubbleChart;
    }

    public void setMapaBubbleChart(Map<Integer, Map<String, BubbleChartTO>> mapaBubbleChart) {
        this.mapaBubbleChart = mapaBubbleChart;
    }
    
    
}
