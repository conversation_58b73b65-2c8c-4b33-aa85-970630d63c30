/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.aula;

/**
 *
 * <AUTHOR>
 */
public enum FrequenciaEnum {
    ALTA_FREQUENCIA,
    BAIXA_FREQUENCIA;
    
    public static FrequenciaEnum getFromOrdinal(Integer ordinal){
        if(ordinal == null){
            return null;
        }
        for(FrequenciaEnum f : values()){
            if(f.ordinal() == ordinal){
                return f;
            }
        }
        return null;
    }

}
