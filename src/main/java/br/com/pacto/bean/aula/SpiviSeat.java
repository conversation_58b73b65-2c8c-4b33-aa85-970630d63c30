package br.com.pacto.bean.aula;


import javax.persistence.*;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table
public class SpiviSeat {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private String matricula;
    private String idzw;
    @Temporal(TemporalType.TIMESTAMP)
    private Date dia;
    private Integer spiviSeatID;
    private Integer spiviEventID;
    private Integer spiviClientID;

    public String getIdzw() {
        return idzw;
    }

    public void setIdzw(String idzw) {
        this.idzw = idzw;
    }

    public Date getDia() {
        return dia;
    }

    public void setDia(Date dia) {
        this.dia = dia;
    }

    public Integer getSpiviSeatID() {
        return spiviSeatID;
    }

    public void setSpiviSeatID(Integer spiviSeatID) {
        this.spiviSeatID = spiviSeatID;
    }

    public Integer getSpiviEventID() {
        return spiviEventID;
    }

    public void setSpiviEventID(Integer spiviEventID) {
        this.spiviEventID = spiviEventID;
    }

    public Integer getSpiviClientID() {
        return spiviClientID;
    }

    public void setSpiviClientID(Integer spiviClientID) {
        this.spiviClientID = spiviClientID;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }
}
