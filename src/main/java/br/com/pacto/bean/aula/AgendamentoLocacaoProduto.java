package br.com.pacto.bean.aula;

import javax.persistence.*;
import java.io.Serializable;

@Entity
@Table
public class AgendamentoLocacaoProduto implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private Integer produto;
    private Integer quantidade;
    private Boolean obrigatorio = Boolean.FALSE;
    private Double valorUnitario;
    private Double valorTotal;
    @ManyToOne(fetch = FetchType.LAZY)
    private AgendamentoLocacao agendamento;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getProduto() {
        return produto;
    }

    public void setProduto(Integer produto) {
        this.produto = produto;
    }

    public Integer getQuantidade() {
        return quantidade;
    }

    public void setQuantidade(Integer quantidade) {
        this.quantidade = quantidade;
    }

    public Boolean getObrigatorio() {
        return obrigatorio;
    }

    public void setObrigatorio(Boolean obrigatorio) {
        this.obrigatorio = obrigatorio;
    }

    public Double getValorUnitario() {
        return valorUnitario;
    }

    public void setValorUnitario(Double valorUnitario) {
        this.valorUnitario = valorUnitario;
    }

    public Double getValorTotal() {
        return valorTotal;
    }

    public void setValorTotal(Double valorTotal) {
        this.valorTotal = valorTotal;
    }

    public AgendamentoLocacao getAgendamento() {
        return agendamento;
    }

    public void setAgendamento(AgendamentoLocacao agendamento) {
        this.agendamento = agendamento;
    }
}
