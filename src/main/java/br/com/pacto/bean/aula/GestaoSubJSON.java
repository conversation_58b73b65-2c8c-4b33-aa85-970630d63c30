/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.aula;

import br.com.pacto.controller.json.base.SuperJSON;

/**
 *
 * <AUTHOR>
 */
public class GestaoSubJSON extends SuperJSON {
    
    private String label;
    private Integer valor;

    public GestaoSubJSON(String label, Integer value) {
        this.label = label;
        this.valor = value;
    }
    
    

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public Integer getValor() {
        return valor;
    }

    public void setValor(Integer value) {
        this.valor = value;
    }

    
}
