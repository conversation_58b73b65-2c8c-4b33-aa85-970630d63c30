/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.aula;

import java.util.Date;
import br.com.pacto.controller.to.DefaultScheduleEvent;

/**
 *
 * <AUTHOR>
 */
public class AgendaAulaTO extends DefaultScheduleEvent {

    private AulaDia aulaDiaria;
    private Date inicio;
    private Date fim;

    public AgendaAulaTO(String title, Date start, Date end, String styleClass, AulaDia aula) {
        super.setTitle(title);
        super.setStartDate(start);
        super.setEndDate(end);
        super.setStyleClass(styleClass);
        this.aulaDiaria = aula;
        this.inicio = new Date(start.getTime());
        this.fim = new Date(end.getTime());
    }

    public AulaDia getAulaDiaria() {
        return aulaDiaria;
    }

    public void setAulaDiaria(AulaDia aulaDiaria) {
        this.aulaDiaria = aulaDiaria;
    }

    public Date getInicio() {
        return inicio;
    }

    public void setInicio(Date inicio) {
        this.inicio = inicio;
    }

    public Date getFim() {
        return fim;
    }

    public void setFim(Date fim) {
        this.fim = fim;
    }
}
