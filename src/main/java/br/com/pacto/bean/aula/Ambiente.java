/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.aula;

import java.io.Serializable;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table
public class Ambiente implements Serializable{
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private Integer codigoZW;
    private String nome;
    private Integer capacidade;
    @Transient
    private Boolean escolhido = Boolean.TRUE;

    public Ambiente() {
    }

    public Ambiente(Integer codigoZW, String nome, Integer capacidade) {
        this.codigoZW = codigoZW;
        this.nome = nome;
        this.capacidade = capacidade;
    }

    public Ambiente(Integer codigo) {
        this.codigo = codigo;
    }
    
    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getCodigoZW() {
        return codigoZW;
    }

    public void setCodigoZW(Integer codigoZW) {
        this.codigoZW = codigoZW;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Integer getCapacidade() {
        return capacidade;
    }

    public void setCapacidade(Integer capacidade) {
        this.capacidade = capacidade;
    }

    public Boolean getEscolhido() {
        return escolhido;
    }

    public void setEscolhido(Boolean escolhido) {
        this.escolhido = escolhido;
    }
    
    
}
