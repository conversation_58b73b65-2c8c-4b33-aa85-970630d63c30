/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.aula;

import java.io.Serializable;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table
public class AulaHorario implements Serializable {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private String inicio;
    private String fim;
    @ManyToOne
    private Aula aula;
    private Boolean ativo = true;

    public AulaHorario() {
    }

    public AulaHorario(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getInicio() {
        return inicio;
    }

    public void setInicio(String inicio) {
        this.inicio = inicio;
    }

    public String getFim() {
        return fim;
    }

    public void setFim(String fim) {
        this.fim = fim;
    }

    public Aula getAula() {
        return aula;
    }

    public void setAula(Aula aula) {
        this.aula = aula;
    }
    
    @Override
    public boolean equals(Object obj) {
        if (obj == null) {
            return false;
        }
        if (!(obj instanceof AulaHorario)) {
            return false;
        }
        final AulaHorario other = (AulaHorario) obj;
        if (!(this.getAula().getCodigo().equals(other.getAula().getCodigo())) || !(this.getInicio().equals(other.getInicio())) || !(this.getFim().equals(other.getFim()))) {
            return false;
        }
        return true;
    }
    
    @Override
    public int hashCode() {
        return (this.getInicio()+this.getFim()).hashCode();
    }

    public Boolean getAtivo() {
        if(ativo == null){
            ativo = Boolean.TRUE;
        }
        return ativo;
    }

    public void setAtivo(Boolean ativo) {
        this.ativo = ativo;
    }
}
