package br.com.pacto.bean.wod;

import br.com.pacto.bean.aparelho.AparelhoWod;
import br.com.pacto.bean.atividade.AtividadeWod;
import br.com.pacto.bean.benchmark.Benchmark;
import br.com.pacto.bean.crossfit.EventoCrossfit;
import br.com.pacto.bean.tipowod.TipoWod;
import br.com.pacto.controller.json.crossfit.AparelhoWodJSON;
import br.com.pacto.controller.json.crossfit.AtividadeWodJSON;
import br.com.pacto.controller.json.crossfit.RankingJSON;
import br.com.pacto.enumerador.crossfit.OrigemExercicio;
import br.com.pacto.enumerador.crossfit.TipoWodEnum;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.util.impl.UtilReflection;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import org.hibernate.annotations.Type;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Lob;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

@Entity
@Table
public class WodAvaliacao implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private Integer wod;
    private Integer usuario;
    private Integer empresa;
    private String comentario;
    private Integer nota;
    private Integer percepcaoEsforco;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getWod() {
        return wod;
    }

    public void setWod(Integer wod) {
        this.wod = wod;
    }

    public Integer getUsuario() {
        return usuario;
    }

    public void setUsuario(Integer usuario) {
        this.usuario = usuario;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public String getComentario() {
        return comentario;
    }

    public void setComentario(String comentario) {
        this.comentario = comentario;
    }

    public Integer getNota() {
        return nota;
    }

    public void setNota(Integer nota) {
        this.nota = nota;
    }

    public Integer getPercepcaoEsforco() {
        return percepcaoEsforco;
    }

    public void setPercepcaoEsforco(Integer percepcaoEsforco) {
        this.percepcaoEsforco = percepcaoEsforco;
    }
}