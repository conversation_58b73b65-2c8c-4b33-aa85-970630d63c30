package br.com.pacto.bean.wod;

import br.com.pacto.bean.aula.AulaHorario;
import br.com.pacto.controller.json.agendamento.EventoAulaDTO;
import br.com.pacto.enumerador.crossfit.TipoWodEnum;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import org.jsoup.Connection;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;

import java.sql.ResultSet;
import java.util.Arrays;
import java.util.Date;
import java.util.Random;

/**
 * Created by <PERSON><PERSON> Alcides on 17/02/2017.
 */
public class GerarScore {


    public static void main(String ... args){
        try {
            Date data = Uteis.getDate("03/01/2019");

            if(Calendario.menor(data, Uteis.getDate("01/01/2019"))){
                StringBuilder url = new StringBuilder("https://www.crossfit.com/workout/");
                url.append(Uteis.getDataAplicandoFormatacao(data, "yyyy/MM/dd"));
                Connection con = Jsoup.connect(url.toString());
                con.timeout(10000);
                Document doc = con.get();
                Element el = doc.select(".body .content").first();
                StringBuilder text = new StringBuilder();
                for (Element element : el.select("p:not(:last-child)")) {
                    text.append(element.text()).append("\n");
                }
                if (el == null || el.text() == null) {
                    throw new Exception("Não encontrado Wod do dia especificado.");
                }
                System.out.println(text.toString());
            }else{
                StringBuilder url = new StringBuilder("https://www.crossfit.com/");
                url.append(Uteis.getDataAplicandoFormatacao(data, "yyMMdd"));
                Connection con = Jsoup.connect(url.toString());
                con.timeout(10000);
                Document doc = con.get();
                Element el = doc.select("#app").first();
                StringBuilder text = new StringBuilder();
                for (Element element : el.select(".OaBj0k3WXCWtsYfZtNNuS")) {
                    text.append(element.text().split(" Post ")[0]).append("\n");
                }
                if (el == null || el.text() == null) {
                    throw new Exception("Não encontrado Wod do dia especificado.");
                }
                System.out.println(text.toString());
            }


        }catch (Exception e){
            e.printStackTrace();
        }
    }

    public static void gerarProWod(String dia, Integer codigoWOD, java.sql.Connection con, TipoWodEnum tipo) throws Exception{
        Random gerador = new Random();
        ResultSet rs = con.prepareStatement("SELECT codigo FROM clientesintetico limit "+(gerador.nextInt(30)+20)).executeQuery();
        while(rs.next()){
            Boolean rx = gerador.nextInt(2) == 1;
            Integer codigoUsuario = 0;
            ResultSet rsuser = con.prepareStatement("SELECT codigo FROM usuario where cliente_codigo = " + rs.getInt("codigo")).executeQuery();
            if(rsuser.next()){
                codigoUsuario = rsuser.getInt("codigo");
            }

            Double peso = Arrays.asList(tipo.getCamposResultado()).contains("peso") ?
                    Integer.valueOf(gerador.nextInt(50)).doubleValue() : 0.0;

            Integer repeticoes = Arrays.asList(tipo.getCamposResultado()).contains("repeticoes") ?
                    gerador.nextInt(20) : 0;

            Integer round = Arrays.asList(tipo.getCamposResultado()).contains("rounds") ?
                    gerador.nextInt(10) : 0;

            Integer tempo = Arrays.asList(tipo.getCamposResultado()).contains("tempo") ?
                    gerador.nextInt(120) : 0;
            StringBuilder insert = new StringBuilder();
            insert.append("INSERT INTO scoretreino (comentario, lancamento, peso, repeticoes, ");
            insert.append("rounds, rx, tempo, cliente_codigo, wod_codigo, usuario_codigo)");
            insert.append(" VALUES ('bom', '"+dia+"', ").append(peso).append(",");
            insert.append(repeticoes).append(",").append(round).append(",");
            insert.append(rx).append(",").append(tempo).append(",");
            insert.append(rs.getInt("codigo")).append(",").append(codigoWOD).append(",").append(codigoUsuario).append(")");

            con.prepareStatement(insert.toString()).execute();

        }
    }


}
