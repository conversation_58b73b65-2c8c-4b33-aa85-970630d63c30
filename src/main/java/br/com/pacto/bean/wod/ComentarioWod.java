package br.com.pacto.bean.wod;

import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.objeto.Uteis;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/*
 * Created by <PERSON><PERSON>
 */
@Entity
public class ComentarioWod implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    @ManyToOne
    private Wod wod;
    @Column(columnDefinition = "text", length = 9999)
    private String comentario;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(columnDefinition = "timestamp without time zone DEFAULT current_timestamp")
    private Date dataRegistro;
    @Transient
    private Integer qtdLike;
    @Transient
    private boolean clienteDeuLike = false;
    @Transient
    private boolean podeExcluir = false;
    @Enumerated(EnumType.ORDINAL)
    private NivelCrossfitEnum nivelCrossfit;
    @ManyToOne
    private Usuario usuario;
    @Transient
    private String nome;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Wod getWod() {
        return wod;
    }

    public void setWod(Wod wod) {
        this.wod = wod;
    }

    public String getComentario() {
        if (comentario == null) {
            comentario = "";
        }
        return comentario;
    }

    public void setComentario(String comentario) {
        this.comentario = comentario;
    }

    public Date getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(Date dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public String getDataRegistroApresentar() {
        return Uteis.getDataAplicandoFormatacao(getDataRegistro(), "dd/MM/yyyy HH:mm:ss");
    }

    public Integer getQtdLike() {
        if (qtdLike == null) {
            qtdLike = 0;
        }
        return qtdLike;
    }

    public void setQtdLike(Integer qtdLike) {
        this.qtdLike = qtdLike;
    }

    public boolean isClienteDeuLike() {
        return clienteDeuLike;
    }

    public void setClienteDeuLike(boolean clienteDeuLike) {
        this.clienteDeuLike = clienteDeuLike;
    }

    public boolean isPodeExcluir() {
        return podeExcluir;
    }

    public void setPodeExcluir(boolean podeExcluir) {
        this.podeExcluir = podeExcluir;
    }

    public NivelCrossfitEnum getNivelCrossfit() {
        return nivelCrossfit;
    }

    public void setNivelCrossfit(NivelCrossfitEnum nivelCrossfit) {
        this.nivelCrossfit = nivelCrossfit;
    }

    public Usuario getUsuario() {
        if (usuario == null) {
            usuario = new Usuario();
        }
        return usuario;
    }

    public void setUsuario(Usuario usuario) {
        this.usuario = usuario;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }
}
