package br.com.pacto.bean.cliente;

import br.com.pacto.util.UteisValidacao;
import org.json.JSONException;
import org.json.JSONObject;

import javax.persistence.*;
import java.io.Serializable;

@Entity
@Table(uniqueConstraints = {
        @UniqueConstraint(name = "clientepesquisa_matricula_key", columnNames = {"matricula"})})
public class ClientePesquisa implements Serializable {
    private String nome;
    private String nomeConsulta;
    private Integer empresa;
    @Id
    private Integer matricula;
    private Integer codigoExterno; //para integrado, codigo do ZW. Para independente codigo clientesintetico
    private String emails;
    private String fotokey;
    @Transient
    private String urlFoto;

    public ClientePesquisa (JSONObject o) throws JSONException {
        this.nome = o.getString("nome");
        this.nomeConsulta = o.getString("nomeconsulta");
        this.empresa = o.getInt("empresa");
        this.matricula = o.getInt("matricula");
        this.codigoExterno = o.getInt("codigo");
        this.emails = o.optString("email");
        this.fotokey = o.optString("fotokey");
    }

    public ClientePesquisa (String nome, String nomeConsulta, Integer empresa, Integer matricula, Integer codigo, String emails, String fotokey) throws Exception {
        this.nome = nome;
        this.nomeConsulta = nomeConsulta;
        this.empresa = empresa;
        this.matricula = matricula;
        this.codigoExterno = codigo;
        this.emails = emails;
        this.fotokey = fotokey;
    }

    public ClientePesquisa (ClienteSintetico clienteSintetico) throws Exception {
        populateByClienteSintetico(clienteSintetico);
    }

    public ClientePesquisa() {
    }

    public void populateByClienteSintetico(ClienteSintetico clienteSintetico) {
        this.nome = clienteSintetico.getNome();
        if (!UteisValidacao.emptyString(clienteSintetico.getNomeConsulta())) {
            this.nomeConsulta = clienteSintetico.getNomeConsulta();
        }
        this.empresa = clienteSintetico.getEmpresa();
        this.matricula = UteisValidacao.emptyNumber(clienteSintetico.getMatricula()) ? clienteSintetico.getCodigo() : clienteSintetico.getMatricula();
        this.codigoExterno = UteisValidacao.emptyNumber(clienteSintetico.getCodigoCliente()) ? clienteSintetico.getCodigo() : clienteSintetico.getCodigoCliente();
        this.emails = clienteSintetico.getEmail();
        this.fotokey = clienteSintetico.getPessoa().getFotoKey();
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getNomeConsulta() {
        return nomeConsulta;
    }

    public void setNomeConsulta(String nomeConsulta) {
        this.nomeConsulta = nomeConsulta;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public Integer getMatricula() {
        return matricula;
    }

    public void setMatricula(Integer matricula) {
        this.matricula = matricula;
    }

    public Integer getCodigoExterno() {
        return codigoExterno;
    }

    public void setCodigoExterno(Integer codigoExterno) {
        this.codigoExterno = codigoExterno;
    }

    public String getEmails() {
        return emails;
    }

    public void setEmails(String emails) {
        this.emails = emails;
    }

    public String getFotokey() {
        return fotokey;
    }

    public void setFotokey(String fotokey) {
        this.fotokey = fotokey;
    }

    public String getUrlFoto() {
        return urlFoto;
    }

    public void setUrlFoto(String urlFoto) {
        this.urlFoto = urlFoto;
    }
}
