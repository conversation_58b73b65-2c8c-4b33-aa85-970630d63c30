package br.com.pacto.bean.cliente;

import br.com.pacto.bean.sincronizacao.TipoRevisaoEnum;

public enum AcaoAlunoEnum {
    //    Alteração de professor treino web
    ALTERACAO_PROFESSOR_NIVEL(TipoRevisaoEnum.UPDATE),
    //    Novo programa
    NOVO_PROGRAMA(TipoRevisaoEnum.INSERT),
    //    Renovou programa
    RENOVOU_PROGRAMA(TipoRevisaoEnum.UPDATE),
    //    Excluiu programa
    EXCLUIU_PROGRAMA(TipoRevisaoEnum.DELETE),
    //    Criou avaliação
    CRIOU_AVALIACAO(TipoRevisaoEnum.INSERT),
    //    Excluiu avaliação
    EXCLUIU_AVALIACAO(TipoRevisaoEnum.DELETE),
    //    Adicionar observação
    ADICIONAR_OBSERVACAO(TipoRevisaoEnum.INSERT),
    //    Excluir observação
    EXCLUIR_OBSERVACAO(TipoRevisaoEnum.DELETE),
    //    Adicionar anexo
    ADICIONAR_ANEXO(TipoRevisaoEnum.INSERT),
    //    Excluir anexo
    EXCLUIR_ANEXO(TipoRevisaoEnum.DELETE),
    //    Agendou um serviço
    AGENDOU_SERVICO(TipoRevisaoEnum.INSERT),
    //    Alterou um agendamento de serviço
    ALTEROU_SERVICO(TipoRevisaoEnum.UPDATE),
    //    Excluiu um agendamento de serviço
    EXCLUIU_SERVICO(TipoRevisaoEnum.DELETE);

    TipoRevisaoEnum tipo;

    AcaoAlunoEnum(TipoRevisaoEnum tipo) {
        this.tipo = tipo;
    }

    public TipoRevisaoEnum getTipo() {
        return tipo;
    }
}
