package br.com.pacto.bean.cliente;

import br.com.pacto.bean.nivel.NivelResponseTO;
import br.com.pacto.bean.programa.ProfessorResponseTO;
import br.com.pacto.bean.programa.ProgramaTreino;
import br.com.pacto.bean.programa.ProgramaTreinoResponseTO;
import br.com.pacto.bean.usuario.Usuario;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by ulisses on 16/08/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AlunoCatalogoResponseTO {

    private Integer id;
    private String nome;
    private NivelResponseTO nivel;
    private Professor<PERSON>esponseTO professor;
    private Date dataNascimento;
    private String sexo;
    private List<String> emails = new ArrayList<String>();
    private List<String> fones = new ArrayList<String>();
    private Boolean usarApp;
    private String appUsername;
    private ProgramaTreinoResponseTO programaAtual;
    private  String alunoImagiUri;
    private String codigoExterno;
    private Boolean resultadoParq;

    public AlunoCatalogoResponseTO(ClienteSintetico cliente, Usuario usuarioCliente, Usuario usuarioProfessorCliente, ProgramaTreino programaTreinoAtual,
                                   Usuario usuarioProfessorTreino, Boolean resultadoParq, Boolean treinoIndependente) {
        this.id = cliente.getCodigo();
        this.nome = cliente.getNome();
        if (cliente.getNivelAluno() != null) {
            this.nivel = new NivelResponseTO(cliente.getNivelAluno());
        }
        if (usuarioProfessorCliente != null) {
            this.professor = new ProfessorResponseTO(usuarioProfessorCliente, treinoIndependente);
        }
        this.dataNascimento = cliente.getDataNascimento();
        this.sexo = cliente.getSexo();
        this.emails = cliente.getListaEmails();
        this.fones = cliente.getListaTelefones();
        if (usuarioCliente != null) {
            this.usarApp = true;
            this.appUsername = usuarioCliente.getUserName();
        } else {
            this.usarApp = false;
            this.appUsername = null;
        }
        if (programaTreinoAtual != null) {
            this.programaAtual = new ProgramaTreinoResponseTO(programaTreinoAtual, usuarioProfessorTreino,
                    programaTreinoAtual.getProgramaFichas(),
                    treinoIndependente);
        }
        this.alunoImagiUri = cliente.getUrlFoto();
        this.codigoExterno = cliente.getCodigoExterno();
        this.resultadoParq = resultadoParq;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public NivelResponseTO getNivel() {
        return nivel;
    }

    public void setNivel(NivelResponseTO nivel) {
        this.nivel = nivel;
    }

    public ProfessorResponseTO getProfessor() {
        return professor;
    }

    public void setProfessor(ProfessorResponseTO professor) {
        this.professor = professor;
    }

    public Date getDataNascimento() {
        return dataNascimento;
    }

    public void setDataNascimento(Date dataNascimento) {
        this.dataNascimento = dataNascimento;
    }

    public String getSexo() {
        return sexo;
    }

    public void setSexo(String sexo) {
        this.sexo = sexo;
    }

    public List<String> getEmails() {
        return emails;
    }

    public void setEmails(List<String> emails) {
        this.emails = emails;
    }

    public List<String> getFones() {
        return fones;
    }

    public void setFones(List<String> fones) {
        this.fones = fones;
    }

    public Boolean getUsarApp() {
        return usarApp;
    }

    public void setUsarApp(Boolean usarApp) {
        this.usarApp = usarApp;
    }

    public String getAppUsername() {
        return appUsername;
    }

    public void setAppUsername(String appUsername) {
        this.appUsername = appUsername;
    }

    public ProgramaTreinoResponseTO getProgramaAtual() {
        return programaAtual;
    }

    public void setProgramaAtual(ProgramaTreinoResponseTO programaAtual) {
        this.programaAtual = programaAtual;
    }

    public String getAlunoImagiUri() {
        return alunoImagiUri;
    }

    public void setAlunoImagiUri(String alunoImagiUri) {
        this.alunoImagiUri = alunoImagiUri;
    }

    public String getCodigoExterno() {
        return codigoExterno;
    }

    public void setCodigoExterno(String codigoExterno) {
        this.codigoExterno = codigoExterno;
    }

    public Boolean getResultadoParq() {
        return resultadoParq;
    }

    public void setResultadoParq(Boolean resultadoParq) {
        this.resultadoParq = resultadoParq;
    }
}
