package br.com.pacto.bean.cliente;

import br.com.pacto.bean.ficha.FichaResponseTO;
import br.com.pacto.bean.programa.ProgramaTreino;

import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class PessoaPrescricaoDTO {

    private Integer codigoCliente;
    private Integer codigoPrograma;
    private Integer codigoColaborador;
    private String tipo;
    private String nome;
    private String matricula;
    private String programa;
    private String situacao;
    private String situacaoContrato;
    private String professor;
    private Integer andamento;
    private String inicio;
    private String fim;
    private String situacaoTreino;
    private String indicacaoMedica;
    private String urlFoto;
    private boolean permissaoCriar;
    private boolean permissaoRevisar;
    private boolean permissaoRenovar;
    private String ultimoAcesso;
    private boolean totalpass;
    private boolean gympass;
    private boolean freepass;
    private boolean dependente;
    private boolean diaria;
    private boolean aulaAvulsa;
    private boolean atestado;
    private boolean avencer;
    private boolean cancelado;
    private boolean desistente;
    private boolean ferias;
    private boolean trancado;
    private boolean vencido;
    private boolean geradoPorIA;
    private boolean isRevisadoProfessor;
    @Temporal(TemporalType.TIMESTAMP)
    private Date datalancamento;

    public PessoaPrescricaoDTO() {
    }

    public PessoaPrescricaoDTO(Integer codigoCliente, Integer codigoColaborador,
                               String tipo, String nome, String matricula, String programa,
                               Integer andamento, String inicio, String fim, String situacaoTreino,
                               String indicacaoMedica, String urlFoto, String ultimoAcesso) {
        this.codigoCliente = codigoCliente;
        this.codigoColaborador = codigoColaborador;
        this.tipo = tipo;
        this.nome = nome;
        this.matricula = matricula;
        this.programa = programa;
        this.andamento = andamento;
        this.inicio = inicio;
        this.fim = fim;
        this.situacaoTreino = situacaoTreino;
        this.indicacaoMedica = indicacaoMedica;
        this.urlFoto = urlFoto;
        this.ultimoAcesso = ultimoAcesso;
    }

    public Integer getCodigoCliente() {
        return codigoCliente;
    }

    public void setCodigoCliente(Integer codigoCliente) {
        this.codigoCliente = codigoCliente;
    }

    public Integer getCodigoColaborador() {
        return codigoColaborador;
    }

    public void setCodigoColaborador(Integer codigoColaborador) {
        this.codigoColaborador = codigoColaborador;
    }

    public String getTipo() {
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public String getPrograma() {
        return programa;
    }

    public void setPrograma(String programa) {
        this.programa = programa;
    }

    public Integer getAndamento() {
        return andamento;
    }

    public void setAndamento(Integer andamento) {
        this.andamento = andamento;
    }

    public String getInicio() {
        return inicio;
    }

    public void setInicio(String inicio) {
        this.inicio = inicio;
    }

    public String getFim() {
        return fim;
    }

    public void setFim(String fim) {
        this.fim = fim;
    }

    public String getSituacaoTreino() {
        return situacaoTreino;
    }

    public void setSituacaoTreino(String situacaoTreino) {
        this.situacaoTreino = situacaoTreino;
    }

    public String getIndicacaoMedica() {
        return indicacaoMedica;
    }

    public void setIndicacaoMedica(String indicacaoMedica) {
        this.indicacaoMedica = indicacaoMedica;
    }

    public String getUrlFoto() {
        return urlFoto;
    }

    public void setUrlFoto(String urlFoto) {
        this.urlFoto = urlFoto;
    }

    public Integer getCodigoPrograma() {
        return codigoPrograma;
    }

    public void setCodigoPrograma(Integer codigoPrograma) {
        this.codigoPrograma = codigoPrograma;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public String getSituacaoContrato() {
        return situacaoContrato;
    }

    public void setSituacaoContrato(String situacaoContrato) {
        this.situacaoContrato = situacaoContrato;
    }

    public String getProfessor() {
        return professor;
    }

    public void setProfessor(String professor) {
        this.professor = professor;
    }

    public boolean isPermissaoCriar() {
        return permissaoCriar;
    }

    public void setPermissaoCriar(boolean permissaoCriar) {
        this.permissaoCriar = permissaoCriar;
    }

    public boolean isPermissaoRevisar() {
        return permissaoRevisar;
    }

    public void setPermissaoRevisar(boolean permissaoRevisar) {
        this.permissaoRevisar = permissaoRevisar;
    }

    public boolean isPermissaoRenovar() {
        return permissaoRenovar;
    }

    public void setPermissaoRenovar(boolean permissaoRenovar) {
        this.permissaoRenovar = permissaoRenovar;
    }

    public String getUltimoAcesso() {return ultimoAcesso; }

    public void setUltimoAcesso(String ultimoAcesso) {this.ultimoAcesso = ultimoAcesso;  }

    public boolean isGeradoPorIA() {
        return geradoPorIA;
    }

    public void setGeradoPorIA(boolean geradoPorIA) {
        this.geradoPorIA = geradoPorIA;
    }

    public boolean isRevisadoProfessor() {
        return isRevisadoProfessor;
    }

    public void setRevisadoProfessor(boolean revisadoProfessor) {
        isRevisadoProfessor = revisadoProfessor;
    }

    public Date getDatalancamento() {
        return datalancamento;
    }

    public void setDatalancamento(Date datalancamento) {
        this.datalancamento = datalancamento;
    }

    public boolean isTotalpass() {return totalpass;}

    public void setTotalpass(boolean totalpass) {this.totalpass = totalpass;}

    public boolean isGympass() {return gympass;}

    public void setGympass(boolean gympass) {this.gympass = gympass;}

    public boolean isFreepass() {return freepass;}

    public void setFreepass(boolean freepass) {this.freepass = freepass;}

    public boolean isDependente() {return dependente;}

    public void setDependente(boolean dependente) {this.dependente = dependente;}

    public boolean isDiaria() {return diaria;}

    public void setDiaria(boolean diaria) {this.diaria = diaria;}

    public boolean isAulaAvulsa() {
        return aulaAvulsa;
    }
    public void setAulaAvulsa(boolean aulaAvulsa) {
        this.aulaAvulsa = aulaAvulsa;
    }

    public boolean isAtestado() {return atestado;}

    public void setAtestado(boolean atestado) {this.atestado = atestado;}

    public boolean isAvencer() {
        return avencer;
    }

    public void setAvencer(boolean avencer) {
        this.avencer = avencer;
    }

    public boolean isCancelado() {
        return cancelado;
    }

    public void setCancelado(boolean cancelado) {
        this.cancelado = cancelado;
    }

    public boolean isDesistente() {
        return desistente;
    }

    public void setDesistente(boolean desistente) {
        this.desistente = desistente;
    }
    public boolean isFerias() {
        return ferias;
    }
    public void setFerias(boolean ferias) {
        this.ferias = ferias;
    }

    public boolean isTrancado() {
        return trancado;
    }

    public void setTrancado(boolean trancado) {
        this.trancado = trancado;
    }

    public boolean isVencido() {
        return vencido;
    }

    public void setVencido(boolean vencido) {
        this.vencido = vencido;
    }
}
