package br.com.pacto.bean.cliente;

import java.sql.ResultSet;

public class ClienteConsulta {

    private String nome;
    private String programaAtual;
    private String nivel;
    private Integer codigo;
    private Integer codigoCliente;
    private Integer codigoPessoa;

    public ClienteConsulta() {
    }

    public ClienteConsulta(ResultSet rs) throws Exception{
        this.nome = rs.getString("nome").toLowerCase();
        this.nivel = rs.getString("nivel");
        this.codigo = rs.getInt("codigo");
        this.codigoCliente = rs.getInt("codigoCliente");
        this.codigoPessoa = rs.getInt("codigoPessoa");
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getProgramaAtual() {
        return programaAtual;
    }

    public void setProgramaAtual(String programaAtual) {
        this.programaAtual = programaAtual;
    }

    public String getNivel() {
        return nivel;
    }

    public void setNivel(String nivel) {
        this.nivel = nivel;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getCodigoCliente() {
        return codigoCliente;
    }

    public void setCodigoCliente(Integer codigoCliente) {
        this.codigoCliente = codigoCliente;
    }

    public Integer getCodigoPessoa() {
        return codigoPessoa;
    }

    public void setCodigoPessoa(Integer codigoPessoa) {
        this.codigoPessoa = codigoPessoa;
    }
}
