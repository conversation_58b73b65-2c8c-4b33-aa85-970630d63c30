package br.com.pacto.bean.cliente;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import java.io.Serializable;
import java.util.Date;

@Entity
public class PerfilDISC implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private Double estabilidade;
    private Double dominancia;
    private Double influencia;
    private Double conformidade;
    private Date dataCadastro;
    @ManyToOne
    @JoinColumn(name = "cliente")
    private ClienteSintetico cliente;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Double getEstabilidade() {
        return estabilidade;
    }

    public void setEstabilidade(Double estabilidade) {
        this.estabilidade = estabilidade;
    }

    public Double getDominancia() {
        return dominancia;
    }

    public void setDominancia(Double dominancia) {
        this.dominancia = dominancia;
    }

    public Double getInfluencia() {
        return influencia;
    }

    public void setInfluencia(Double influencia) {
        this.influencia = influencia;
    }

    public Double getConformidade() {
        return conformidade;
    }

    public void setConformidade(Double conformidade) {
        this.conformidade = conformidade;
    }

    public ClienteSintetico getCliente() {
        return cliente;
    }

    public void setCliente(ClienteSintetico cliente) {
        this.cliente = cliente;
    }

    public Date getDataCadastro() {
        return dataCadastro;
    }

    public void setDataCadastro(Date dataCadastro) {
        this.dataCadastro = dataCadastro;
    }
}
