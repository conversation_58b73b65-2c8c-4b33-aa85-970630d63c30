package br.com.pacto.bean.cliente;

import br.com.pacto.controller.json.base.SuperJSON;
import br.com.pacto.controller.json.cliente.ClienteJSON;
import br.com.pacto.controller.json.professor.ProfessorJSON;

import java.sql.Timestamp;

public class ClienteAcompanhamentoJSON extends SuperJSON {

    private Integer codigo;
    private ClienteJSON cliente;
    private Professor<PERSON><PERSON><PERSON> professor;
    private Timestamp inicio;
    private Timestamp fim;

    public ClienteAcompanhamentoJSON(ClienteAcompanhamento obj) {
        if(obj != null) {
            this.codigo = obj.getCodigo();
            this.cliente = obj.getCliente() != null ? new ClienteJSON(obj.getCliente()) : null;
            this.professor = obj.getProfessor() != null ? new ProfessorJSON(obj.getProfessor()) : null;
            this.inicio = obj.getInicio() != null ? new  Timestamp(obj.getInicio().getTime()) : null;
            this.fim = obj.getFim() != null ? new  Timestamp(obj.getFim().getTime()) : null;
        }
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public ClienteJSON getCliente() {
        return cliente;
    }

    public void setCliente(ClienteJSON cliente) {
        this.cliente = cliente;
    }

    public ProfessorJSON getProfessor() {
        return professor;
    }

    public void setProfessor(ProfessorJSON professor) {
        this.professor = professor;
    }

    public Timestamp getInicio() {
        return inicio;
    }

    public void setInicio(Timestamp inicio) {
        this.inicio = inicio;
    }

    public Timestamp getFim() {
        return fim;
    }

    public void setFim(Timestamp fim) {
        this.fim = fim;
    }
}
