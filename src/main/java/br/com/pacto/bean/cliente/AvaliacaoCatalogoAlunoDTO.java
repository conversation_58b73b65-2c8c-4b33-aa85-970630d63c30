package br.com.pacto.bean.cliente;


import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.Date;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class AvaliacaoCatalogoAlunoDTO {

    private Integer id;
    private String imageUri;
    private String nome;
    private String sexo;
    private String nivelNome;
    private Date dataNascimento;
    private Boolean resultadoParq;

    public AvaliacaoCatalogoAlunoDTO() {

    }
    public AvaliacaoCatalogoAlunoDTO(ClienteSintetico cliente, Boolean resultadoPositivoParq) {
        this.id = cliente.getCodigo();
        this.imageUri = cliente.getUrlFoto();
        this.nome = cliente.getNome();
        this.sexo = cliente.getSexo();
        this.nivelNome = cliente.getNivelAluno() == null ? null : cliente.getNivelAluno().getNome();
        this.dataNascimento = cliente.getDataNascimento();
        this.resultadoParq = resultadoPositivoParq;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getImageUri() {
        return imageUri;
    }

    public void setImageUri(String imageUri) {
        this.imageUri = imageUri;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getNivelNome() {
        return nivelNome;
    }

    public void setNivelNome(String nivelNome) {
        this.nivelNome = nivelNome;
    }

    public String getSexo() {
        return sexo;
    }

    public void setSexo(String sexo) {
        this.sexo = sexo;
    }

    public Date getDataNascimento() {
        return dataNascimento;
    }

    public void setDataNascimento(Date dataNascimento) {
        this.dataNascimento = dataNascimento;
    }

    public Boolean getResultadoParq() {
        return resultadoParq;
    }

    public void setResultadoParq(Boolean resultadoParq) {
        this.resultadoParq = resultadoParq;
    }
}
