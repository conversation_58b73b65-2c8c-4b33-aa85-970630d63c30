/**
 *
 * <AUTHOR>
 */

package br.com.pacto.bean.cliente;

import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.base.SuperJSON;

import java.sql.Timestamp;

public class ClienteObservacaoJSON extends SuperJSON {
    private Integer codigo;
    private Boolean importante;
    private String userName;
    private String nome;
    private Timestamp dataLong;
    private String observacao;
    private String foto;

    public ClienteObservacaoJSON(ClienteObservacao co, Usuario usu)
    {
        if(co != null) {
            this.codigo = co.getCodigo();
            this.importante = co.getImportante();
            this.observacao = co.getObservacao();
            this.dataLong = co.getDataObservacao() != null ? new  Timestamp(co.getDataObservacao().getTime()) : null;
            if(co.getUsuario_codigo() != null)
            {
                this.userName = usu.getUserName();
                if(usu.getProfessor() != null) {
                    this.nome = usu.getProfessor().getNome();
                }
            }
        }
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Boolean getImportante() {
        return importante;
    }

    public void setImportante(Boolean importante) {
        this.importante = importante;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public Timestamp getDataLong() {
        return dataLong;
    }

    public void setDataLong(Timestamp dataLong) {
        this.dataLong = dataLong;
    }

    public String getFoto() {
        return foto;
    }

    public void setFoto(String foto) {
        this.foto = foto;
    }
}
