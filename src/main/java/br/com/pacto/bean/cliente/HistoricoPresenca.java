package br.com.pacto.bean.cliente;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.ManyToOne;
import java.io.Serializable;
import java.util.Date;

@Entity
public class HistoricoPresenca implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;

    private Integer totalAulasRealizadas;
    private Integer aulasMesAtual;
    private Integer semanasConsecutivas ;
    private Date dataAtualizacao;
    @ManyToOne
    private ClienteSintetico cliente;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getTotalAulasRealizadas() {
        return totalAulasRealizadas;
    }

    public void setTotalAulasRealizadas(Integer totalAulasRealizadas) {
        this.totalAulasRealizadas = totalAulasRealizadas;
    }

    public Integer getAulasMesAtual() {
        return aulasMesAtual;
    }

    public void setAulasMesAtual(Integer aulasMesAtual) {
        this.aulasMesAtual = aulasMesAtual;
    }

    public Integer getSemanasConsecutivas() {
        return semanasConsecutivas;
    }

    public void setSemanasConsecutivas(Integer semanasConsecutivas) {
        this.semanasConsecutivas = semanasConsecutivas;
    }

    public Date getDataAtualizacao() {
        return dataAtualizacao;
    }

    public void setDataAtualizacao(Date dataAtualizacao) {
        this.dataAtualizacao = dataAtualizacao;
    }

    public ClienteSintetico getCliente() {
        return cliente;
    }

    public void setCliente(ClienteSintetico cliente) {
        this.cliente = cliente;
    }
}
