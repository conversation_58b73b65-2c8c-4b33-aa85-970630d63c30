package br.com.pacto.bean.cliente;

import br.com.pacto.bean.ficha.FichaResponseTO;
import br.com.pacto.bean.programa.ProgramaTreino;

import java.util.ArrayList;
import java.util.List;

public class ClienteAcompanhamentoResponseTO {

    private String nomeAluno;
    private String nivelAluno;
    private String imagemUri;
    private ProgramaTreino programa;
    private List<FichaResponseTO> fichas = new ArrayList<FichaResponseTO>();

    public ClienteAcompanhamentoResponseTO() {
    }

    public String getNomeAluno() { return nomeAluno; }

    public void setNomeAluno(String nomeAluno) { this.nomeAluno = nomeAluno; }

    public String getNivelAluno() { return nivelAluno; }

    public void setNivelAluno(String nivelAluno) { this.nivelAluno = nivelAluno; }

    public ProgramaTreino getPrograma() { return programa; }

    public void setPrograma(ProgramaTreino programa) { this.programa = programa; }

    public List<FichaResponseTO> getFichas() {
        return fichas;
    }

    public void setFichas(List<FichaResponseTO> fichas) {
        this.fichas = fichas;
    }

    public String getImagemUri() { return imagemUri; }

    public void setImagemUri(String imagemUri) { this.imagemUri = imagemUri; }
}
