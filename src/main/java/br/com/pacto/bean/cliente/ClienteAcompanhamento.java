/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.pacto.bean.cliente;

import br.com.pacto.bean.professor.ProfessorSintetico;
import br.com.pacto.objeto.Uteis;
import java.io.Serializable;
import java.util.Date;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table
public class ClienteAcompanhamento implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    @ManyToOne
    private ClienteSintetico cliente;
    @ManyToOne
    private ProfessorSintetico professor;
    @Temporal(TemporalType.TIMESTAMP)
    private Date inicio;
    @Temporal(TemporalType.TIMESTAMP)
    private Date fim;
    private Integer tipoAcompanhamentoCliente;

    public ClienteAcompanhamento(){
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public ClienteSintetico getCliente() {
        return cliente;
    }

    public void setCliente(ClienteSintetico cliente) {
        this.cliente = cliente;
    }

    public ProfessorSintetico getProfessor() {
        return professor;
    }

    public void setProfessor(ProfessorSintetico professor) {
        this.professor = professor;
    }

    public Date getInicio() {
        return inicio;
    }

    public void setInicio(Date inicio) {
        this.inicio = inicio;
    }

    public Date getFim() {
        return fim;
    }

    public void setFim(Date fim) {
        this.fim = fim;
    }
    
     public String getMatricula(){
        return getCliente().getMatriculaString();
    }
    
    public String getNomeAluno(){
        return getCliente().getNome();
    }
   
    public String getSituacaoAluno(){
        return getCliente().getSituacao();
    }
   
    public String getDataVigenciaAteAjustadaApresentar() {
        try {
            return Uteis.getData(getCliente().getDataVigenciaAteAjustada());
        } catch (Exception e) {
            return "";
        }
        
    }
    
    public Date getDataVigenciaAteAjustada() {
        return getCliente().getDataVigenciaAteAjustada();
    }
    
    
    public String getDataInicioApresentar() {
        try {
            return Uteis.getData(getInicio());
        } catch (Exception e) {
            return "";
        }
        
    }
    
    public Integer getChaveLink(){
        return this.getCliente().getCodigo();
    }

    public Integer getTipoAcompanhamentoCliente() {
        return tipoAcompanhamentoCliente;
    }

    public void setTipoAcompanhamentoCliente(Integer tipoAcompanhamentoCliente) {
        this.tipoAcompanhamentoCliente = tipoAcompanhamentoCliente;
    }
}
