package br.com.pacto.bean.cliente;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

public enum OrigemCliente {
    CONVERSAS_IA,
    TREINO,
    ADM;


    @JsonValue
    public String toValue() {
        return this.name();
    }

    @JsonCreator
    public static OrigemCliente fromValue(String value) {
        for (OrigemCliente origem : OrigemCliente.values()) {
            if (origem.name().equalsIgnoreCase(value)) {
                return origem;
            }
        }
        return null;
    }
}