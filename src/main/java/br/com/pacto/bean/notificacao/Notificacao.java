/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.notificacao;

import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.professor.ProfessorSintetico;
import br.com.pacto.bean.programa.ProgramaTreino;
import br.com.pacto.bean.serie.SerieRealizada;
import br.com.pacto.controller.json.notificacao.NotificacaoJSON;
import br.com.pacto.objeto.Calendario;
import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

/**
 *
 * <AUTHOR>
 */
@Entity
public class Notificacao implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private String nome;
    @Temporal(TemporalType.TIMESTAMP)
    private Date dataRegistro;
    @ManyToOne(fetch = FetchType.LAZY)
    private ClienteSintetico cliente;
    @ManyToOne(fetch = FetchType.LAZY)
    private Professor<PERSON><PERSON><PERSON><PERSON> professor;
    @Enumerated(EnumType.ORDINAL)
    private TipoNotificacaoEnum tipo;
    @ManyToOne(fetch = FetchType.LAZY)
    private SerieRealizada serieRealizada;
    @ManyToOne(fetch = FetchType.LAZY)
    private ProgramaTreino programa;
    private boolean lida = false;
    private Boolean pushEnviado = false;
    private Boolean smsEnviado = false;
    private String opcoes;
    private String resposta;
    private String textoCRM;
    @Column(columnDefinition = "boolean DEFAULT true")
    private Boolean ativa = true;
    

    public Notificacao() {
    }

    public Notificacao(final String nome) {
        this.nome = nome;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public ClienteSintetico getCliente() {
        return cliente;
    }

    public void setCliente(ClienteSintetico cliente) {
        this.cliente = cliente;
    }

    public TipoNotificacaoEnum getTipo() {
        return tipo;
    }

    public void setTipo(TipoNotificacaoEnum tipo) {
        this.tipo = tipo;
    }

    public ProfessorSintetico getProfessor() {
        return professor;
    }

    public void setProfessor(ProfessorSintetico professor) {
        this.professor = professor;
    }

    public Date getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(Date data) {
        this.dataRegistro = data;
    }

    public SerieRealizada getSerieRealizada() {
        return serieRealizada;
    }

    public void setSerieRealizada(SerieRealizada serieRealizada) {
        this.serieRealizada = serieRealizada;
    }

    public boolean isLida() {
        return lida;
    }

    public void setLida(boolean lida) {
        this.lida = lida;
    }

    public Boolean getPushEnviado() {
        return pushEnviado;
    }

    public void setPushEnviado(Boolean pushEnviado) {
        this.pushEnviado = pushEnviado;
    }

    public Boolean getSmsEnviado() {
        return smsEnviado;
    }

    public void setSmsEnviado(Boolean smsEnviado) {
        this.smsEnviado = smsEnviado;
    }

    public String toJSON() {
        NotificacaoJSON notfJSON = new NotificacaoJSON(
                codigo,
                tipo != null && tipo.equals(TipoNotificacaoEnum.CONTATO_CRM) ? this.getNome() : tipo.getDescricao(),
                tipo != null && tipo.equals(TipoNotificacaoEnum.CONTATO_CRM) ? textoCRM : nome,
                tipo.name(),
                Calendario.getData(dataRegistro, "dd/MM/yyyy HH:mm"),
                professor != null ? professor.getCodigo() : 0,
                cliente != null ? cliente.getCodigo() : null,
                cliente.getTelefones(),                
                tipo.getGravidade().getHexa(),
                opcoes,
                resposta,
                lida,
                pushEnviado);
        return notfJSON.toJSON();
    }

    public NotificacaoJSON toJSONObject() {
        NotificacaoJSON notfJSON = new NotificacaoJSON(
                codigo,
                tipo != null && tipo.equals(TipoNotificacaoEnum.CONTATO_CRM) ? this.getNome() : tipo.getDescricao(),
                tipo != null && tipo.equals(TipoNotificacaoEnum.CONTATO_CRM) ? textoCRM : nome,
                tipo.name(),
                Calendario.getData(dataRegistro, "dd/MM/yyyy HH:mm"),
                professor != null ? professor.getCodigo() : 0,
                cliente != null ? cliente.getCodigo() : null,
                cliente.getTelefones(),                
                tipo.getGravidade().getHexa(),
                opcoes,
                resposta,
                lida,
                pushEnviado);
        return notfJSON;
    }

    public String getTexto() {
        StringBuilder texto = new StringBuilder(getTipo().getDescricao());
        if (tipo == TipoNotificacaoEnum.AUMENTOU_CARGA || tipo == TipoNotificacaoEnum.DIMINUIU_CARGA) {
            texto.append(" para ").append(this.getSerieRealizada().getTextoCarga());
        }
        return texto.toString();
    }

    public String getDataApresentar() {
        return Calendario.getData(dataRegistro, "dd/MM/yyyy HH:mm");
    }

    public ProgramaTreino getPrograma() {
        return programa;
    }

    public void setPrograma(ProgramaTreino programa) {
        this.programa = programa;
    }

    public String getOpcoes() {
        return opcoes;
    }

    public void setOpcoes(String opcoes) {
        this.opcoes = opcoes;
    }

    public String getResposta() {
        return resposta;
    }

    public void setResposta(String resposta) {
        this.resposta = resposta;
    }

    public String getTextoCRM() {
        return textoCRM;
    }

    public void setTextoCRM(String textoCRM) {
        this.textoCRM = textoCRM;
    }
    
    public boolean getCrm(){
        try {
            return tipo.equals(TipoNotificacaoEnum.CONTATO_CRM);
        } catch (Exception e) {
            return false;
        }
    }

    public Boolean getAtiva() {
        return ativa;
    }

    public void setAtiva(Boolean ativa) {
        this.ativa = ativa;
    }
}