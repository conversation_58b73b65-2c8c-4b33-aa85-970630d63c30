package br.com.pacto.bean.notificacao;


import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table
public class NotificacaoFimTreino implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private Integer usuario;
    @Column(name = "ultimanotificacao")
    private Date ultimaNotificacao;

    public NotificacaoFimTreino() {}

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Date getUltimaNotificacao() {
        return ultimaNotificacao;
    }

    public void setUltimaNotificacao(Date ultimaNotificacao) {
        this.ultimaNotificacao = ultimaNotificacao;
    }

    public Integer getUsuario() {
        return usuario;
    }

    public void setUsuario(Integer usuario) {
        this.usuario = usuario;
    }
}
