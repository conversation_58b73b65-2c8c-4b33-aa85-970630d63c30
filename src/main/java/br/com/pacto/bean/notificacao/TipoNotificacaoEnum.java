/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.notificacao;

import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public enum TipoNotificacaoEnum {

    INICIOU_TREINO(0, "Iniciou o treino", "Habilita/Desabilita as notificações para o professor no TreinoWeb de quando o aluno acaba de iniciar seu treino,seja pelo aplicativo do smartphone ou pela retirada de ficha", GravidadeNotificacaoEnum.LEVE, VisibilidadeNotificacaoEnum.PROFESSOR, true),
    AUMENTOU_CARGA(1, "Aumentou carga", "Habilita/Desabilita as notificações para o professor no TreinoWeb de quando o aluno aumenta a carga em algum dos exercícios que o mesmo executou pelo aplicativo do smartphone.", GravidadeNotificacaoEnum.GRAVE, VisibilidadeNotificacaoEnum.PROFESSOR, true),
    DIMINUIU_CARGA(2, "Diminuiu carga", "<PERSON><PERSON>ita/Desabilita as notificações para o professor no TreinoWeb de quando o aluno diminui a carga em algum dos exercícios que o mesmo executou pelo aplicativo do smartphone.", GravidadeNotificacaoEnum.MEDIA, VisibilidadeNotificacaoEnum.PROFESSOR, true),
    CONCLUIU_TREINO(3, "Concluiu treino", "teste", GravidadeNotificacaoEnum.LEVE, VisibilidadeNotificacaoEnum.PROFESSOR, false),
    CHAMAR_ATENCAO_PROFESSOR(4, "Chamou professor", "teste", GravidadeNotificacaoEnum.MEDIA, VisibilidadeNotificacaoEnum.PROFESSOR, false),
    CONCLUIU_SERIE(5, "Concluiu uma série", "teste", GravidadeNotificacaoEnum.LEVE, VisibilidadeNotificacaoEnum.PROFESSOR, false),
    EDITOU_SERIE(6, "Editou uma série", "teste", GravidadeNotificacaoEnum.LEVE, VisibilidadeNotificacaoEnum.PROFESSOR, false),
    ALUNO_CHEGOU(7, "Chegou", "Habilita/Desabilita as notificações para o professor no TreinoWeb de quando o aluno acabou de chegar na academia(Passou na catraca) para realizar seu treino.", GravidadeNotificacaoEnum.LEVE, VisibilidadeNotificacaoEnum.PROFESSOR, true),
    ALUNO_SOLICITA_REAGENDA(8, "Deseja reagendar", "Habilita/Desabilita as notificações para o professor no TreinoWeb e para o aluno no aplicativo de quando o aluno necessita agendar um treino.", GravidadeNotificacaoEnum.GRAVE, VisibilidadeNotificacaoEnum.PROFESSOR, false),
    AGENDA(9, "Lembrete agendamento", "Habilita/Desabilita as notificações para o professor no TreinoWeb e para o aluno no aplicativo de quando o aluno possui algum agendamento marcado no TreinoWeb.", GravidadeNotificacaoEnum.MEDIA, VisibilidadeNotificacaoEnum.ALUNO, true),
    AGENDAMENTO_CONFIRMADO(10, "Agendamento confirmado", "Habilita/Desabilita as notificações para o professor no TreinoWeb e para o aluno no aplicativo de quando o aluno confirma algum agendamento marcado, pelo aplicativo, ou quando algum professor confirma um agendamento marcado, pelo TreinoWeb.", GravidadeNotificacaoEnum.MEDIA, VisibilidadeNotificacaoEnum.PROFESSOR, true),
    AGENDAMENTO_CANCELADO(11, "Agendamento cancelado", "teste", GravidadeNotificacaoEnum.GRAVE, VisibilidadeNotificacaoEnum.PROFESSOR, false),
    SOLICITAR_RENOVACAO(12, "Solicitar Renovar Treino", "Habilita/Desabilita as notificações para o aluno quando o aluno precisa renovar seu treino", GravidadeNotificacaoEnum.MEDIA, VisibilidadeNotificacaoEnum.ALUNO, true),
    LEMBRAR_ALUNO_COMPROMISSO(13, "Lembrete Compromisso", "Habilita/Desabilita as notificações para o aluno via SMS de quando o aluno passou muito tempo sem Treinar, para que o mesmo retome suas atividades", GravidadeNotificacaoEnum.MEDIA, VisibilidadeNotificacaoEnum.TODOS, true),
    AGENDAMENTO_NOVO(14, "Novo Compromisso", "teste", GravidadeNotificacaoEnum.MEDIA, VisibilidadeNotificacaoEnum.ALUNO, false),
    AGENDAMENTO_ALTERADO(15, "Compromisso Alterado", "Habilita/Desabilita as notificações para o professor no TreinoWeb e para o aluno no aplicativo de quando o aluno possui algum agendamento que fora reagendado ou remarcado.", GravidadeNotificacaoEnum.MEDIA, VisibilidadeNotificacaoEnum.ALUNO, true),
    ALUNO_EM_RISCO(16, "Aluno chegou e possui Índice de Risco", "Habilita/Desabilita as notificações para o professor no TreinoWeb de quando o aluno sobe de n�vel no grupo de risco.", GravidadeNotificacaoEnum.GRAVE, VisibilidadeNotificacaoEnum.PROFESSOR, true),
    ALUNO_AGENDOU(17, "Aluno reagendou", "Habilita/Desabilita as notificações para o professor no TreinoWeb de quando o aluno reagendou o agendamento pelo aplicativo de smartphone.", GravidadeNotificacaoEnum.GRAVE, VisibilidadeNotificacaoEnum.PROFESSOR, true),
    CONTATO_CRM(18, "Contato CRM", "teste", GravidadeNotificacaoEnum.MEDIA, VisibilidadeNotificacaoEnum.ALUNO, false),
    FEED_APP_LIKE(19, "Like", "teste", GravidadeNotificacaoEnum.MEDIA, VisibilidadeNotificacaoEnum.ALUNO, false),
    FEED_APP_COMENTARIO(20, "Comentário", "teste", GravidadeNotificacaoEnum.MEDIA, VisibilidadeNotificacaoEnum.ALUNO, false),
    DICAS_NUTRI_RESPOSTA(21, "Resposta Comentário", "teste", GravidadeNotificacaoEnum.MEDIA, VisibilidadeNotificacaoEnum.ALUNO, false),
    ALUNO_DA_FILA_ENTROU_NA_AULA(21, "A vaga para sua aula foi liberada", "teste", GravidadeNotificacaoEnum.MEDIA, VisibilidadeNotificacaoEnum.ALUNO, false),
    AGENDAMENTO_LOCACAO_CANCELADO(22, "Agendamento da locação cancelado", "", GravidadeNotificacaoEnum.MEDIA, VisibilidadeNotificacaoEnum.TODOS, true);

    private Integer id;
    private String descricao;
    private GravidadeNotificacaoEnum gravidade;
    private VisibilidadeNotificacaoEnum visibilidade;
    private boolean configurado;
    private String textoReferencia;

    private TipoNotificacaoEnum(Integer id, String descricao, String textoReferencia, GravidadeNotificacaoEnum gravidade, VisibilidadeNotificacaoEnum visibilidade, boolean configurado) {
        this.id = id;
        this.descricao = descricao;
        this.textoReferencia = textoReferencia;
        this.gravidade = gravidade;
        this.visibilidade = visibilidade;
        this.configurado = configurado;


    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getTextoReferencia() {
        return textoReferencia;
    }

    public void setTextoReferencia(String textoReferencia) {
        this.textoReferencia = textoReferencia;
    }

    public String getStyle() {
        return gravidade.getStyle();
    }

    public GravidadeNotificacaoEnum getGravidade() {
        return gravidade;
    }

    public void setGravidade(GravidadeNotificacaoEnum gravidade) {
        this.gravidade = gravidade;
    }

    public VisibilidadeNotificacaoEnum getVisibilidade() {
        return visibilidade;
    }

    public void setVisibilidade(VisibilidadeNotificacaoEnum visibilidade) {
        this.visibilidade = visibilidade;
    }

    public static List<TipoNotificacaoEnum> getTiposVisiveis(VisibilidadeNotificacaoEnum visibilidadeNotificacao) {
        TipoNotificacaoEnum[] tipos = TipoNotificacaoEnum.values();
        List<TipoNotificacaoEnum> lista = new ArrayList<TipoNotificacaoEnum>();
        for (int i = 0; i < tipos.length; i++) {
            TipoNotificacaoEnum tipo = tipos[i];
            if (tipo.getVisibilidade() == visibilidadeNotificacao || tipo.getVisibilidade() == VisibilidadeNotificacaoEnum.TODOS) {
                lista.add(tipo);
            }
        }
        return lista;
    }

    public boolean isConfigurado() {
        return configurado;
    }

    public void setConfigurado(boolean configurado) {
        this.configurado = configurado;
    }

    public static List<TipoNotificacaoEnum> getAConfigurar() {
        List<TipoNotificacaoEnum> lista = new ArrayList<TipoNotificacaoEnum>();
        for (TipoNotificacaoEnum tipo : TipoNotificacaoEnum.values()) {
            if (tipo.isConfigurado()) {
                lista.add(tipo);
            }
        }
        return lista;
    }

    public static TipoNotificacaoEnum obterPorID(Integer id) {
        for (TipoNotificacaoEnum tipo : TipoNotificacaoEnum.values()) {
            if (tipo.getId().equals(id)) {
                return tipo;
            }
        }
        return null;
    }
}
