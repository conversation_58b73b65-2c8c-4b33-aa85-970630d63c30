/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.notificacao;

/**
 *
 * <AUTHOR>
 */
public enum GravidadeNotificacaoEnum {
    
    LEVE("leve", 1, "#70c061"),
    MEDIA("media", 2, "#FDA902"),
    GRAVE("grave", 3, "#DA2D3D");
    
    private String style;
    private Integer ordem;
    private String hexa;
    
    private GravidadeNotificacaoEnum(final String style, final Integer ordem, final String hexa){
        this.style = style;
        this.ordem = ordem;
        this.hexa = hexa;
    }

    public String getStyle() {
        return style;
    }

    public void setStyle(String style) {
        this.style = style;
    }

    public Integer getOrdem() {
        return ordem;
    }

    public void setOrdem(Integer ordem) {
        this.ordem = ordem;
    }

    public String getHexa() {
        return hexa;
    }

    public void setHexa(String hexa) {
        this.hexa = hexa;
    }
    
    
    
}
