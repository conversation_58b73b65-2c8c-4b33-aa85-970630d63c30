package br.com.pacto.bean.perguntaucp;

import servicos.integracao.zw.client.SuperTO;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by luiz on 08/08/2016.
 */
public class UCPTagTO extends SuperTO {

    private Integer codigo;
    private String nome;
    private Integer qtdConhecimentos;
    private List<PerguntaUcpTO> perguntas;

    public Integer getQtdConhecimentos() {
        if (qtdConhecimentos == null) {
            qtdConhecimentos = 0;
        }
        return qtdConhecimentos;
    }

    public void setQtdConhecimentos(Integer qtdConhecimentos) {
        this.qtdConhecimentos = qtdConhecimentos;
    }

    public List<PerguntaUcpTO> getPerguntas() {
        if(perguntas == null){
            perguntas = new ArrayList<PerguntaUcpTO>();
        }
        return perguntas;
    }

    public void setPerguntas(List<PerguntaUcpTO> perguntas) {
        this.perguntas = perguntas;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }
}
