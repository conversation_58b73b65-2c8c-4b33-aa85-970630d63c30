package br.com.pacto.bean.benchmark;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * Created by ulisses on 23/08/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TipoBenchmarkResponseTO {

    private Integer id;
    private String nome;

    public TipoBenchmarkResponseTO(TipoBenchmark tipoBenchmark){
        this.id = tipoBenchmark.getCodigo();
        this.nome = tipoBenchmark.getNome();
    }


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }


}
