package br.com.pacto.bean.benchmark;


import javax.persistence.*;
import java.io.Serializable;

/**
 * Created by <PERSON> on 11/07/2016.
 */
@Entity
@Table
public class TipoBenchmark implements Serializable{

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private String nome;
    @Enumerated(EnumType.ORDINAL)
    private OrigemTipoBenchmark origem = OrigemTipoBenchmark.BOX;

    public TipoBenchmark(String nome){
        this.nome = nome;
        this.origem = OrigemTipoBenchmark.SISTEMA;
    }
    public TipoBenchmark(){
    }
    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public OrigemTipoBenchmark getOrigem() {
        return origem;
    }

    public boolean isOrigemBox(){
        return origem.equals(OrigemTipoBenchmark.BOX);
    }

    public void setOrigem(OrigemTipoBenchmark origem) {
        this.origem = origem;
    }
}
