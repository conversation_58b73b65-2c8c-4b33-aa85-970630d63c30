package br.com.pacto.bean.processo;


import javax.persistence.*;
import java.util.Date;

@Entity
@Table()
public class Sincronizacao {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    @Temporal(TemporalType.TIMESTAMP)
    private Date inicio;
    @Temporal(TemporalType.TIMESTAMP)
    private Date fim;
    private String empresa;
    private Integer ativosTR;
    private Integer ativosZW;
    @Column(columnDefinition = "text")
    private String log;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Date getInicio() {
        return inicio;
    }

    public void setInicio(Date inicio) {
        this.inicio = inicio;
    }

    public Date getFim() {
        return fim;
    }

    public void setFim(Date fim) {
        this.fim = fim;
    }

    public String getEmpresa() {
        return empresa;
    }

    public void setEmpresa(String empresa) {
        this.empresa = empresa;
    }

    public Integer getAtivosTR() {
        return ativosTR;
    }

    public void setAtivosTR(Integer ativosTR) {
        this.ativosTR = ativosTR;
    }

    public Integer getAtivosZW() {
        return ativosZW;
    }

    public void setAtivosZW(Integer ativosZW) {
        this.ativosZW = ativosZW;
    }

    public String getLog() {
        return log;
    }

    public void setLog(String log) {
        this.log = log;
    }
}
