/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.empresa;

import br.com.pacto.bean.configuracoes.TimeZoneEnum;
import servicos.integracao.adm.client.EmpresaWS;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Entity
@Table(uniqueConstraints =
@UniqueConstraint(columnNames = {"nome"}))
public class Empresa implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private Integer codZW;
    private String nome;
    private String tokenSMS;
    private String urlSite;
    private String timeZoneDefault = TimeZoneEnum.Brazil_East.getId();
    @Column(columnDefinition = "boolean DEFAULT true")
    private Boolean usaMobile = true;
    private String email;
    private IdiomaBancoEnum idiomaBanco;
    @Column(name = "cod_empresafinanceiro")
    private Integer codFinanceiro;
    @Column(name = "tokensmsshortcode")
    private String tokenSMSShortcode;
    private String keyImgEmpresa;
    @Column(name = "dataexpiracao")
    private Date dataExpiracao;
    @Column(name = "tokenmqv")
    private String tokenMqv;

    public String getKeyImgEmpresa() { return keyImgEmpresa; }

    public void setKeyImgEmpresa(String keyImgEmpresa) { this.keyImgEmpresa = keyImgEmpresa; }

    public Empresa() {
    }

    public Empresa(Integer codigo) {
        this.codigo = codigo;
    }

    public Empresa(final String nome) {
        this.nome = nome;
    }

    public static Empresa copyAttributes(Empresa e, EmpresaWS eWS) {
        e.setCodZW(eWS.getCodigo());
        e.setNome(eWS.getNome());
        e.setTimeZoneDefault(eWS.getTimeZoneDefault());
        e.setTokenSMS(eWS.getTokenSMS());
        e.setUrlSite(eWS.getSite());
        e.setEmail(eWS.getEmail());
        e.setCodFinanceiro(eWS.getCodigoFinanceiro());
        e.setTokenSMSShortcode(eWS.getTokenShortcode());
        return e;
    }

    public String getTimeZoneDefault() {
        return timeZoneDefault;
    }

    public void setTimeZoneDefault(String timeZoneDefault) {
        this.timeZoneDefault = timeZoneDefault;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Integer getCodZW() {
        return codZW;
    }

    public void setCodZW(Integer codZW) {
        this.codZW = codZW;
    }

    public String getTokenSMS() {
        return tokenSMS;
    }

    public void setTokenSMS(String tokenSMS) {
        this.tokenSMS = tokenSMS;
    }

    public Boolean getUsaMobile() {
        return usaMobile;
    }

    public void setUsaMobile(Boolean usaMobile) {
        this.usaMobile = usaMobile;
    }

    public String getNomeSufixo() {
        String nomeTmp = nome;
        if (nomeTmp != null) {
            if (nomeTmp.contains("-")) {
                try {
                    nomeTmp = nomeTmp.substring(nomeTmp.indexOf("-") + 1).trim();
                } catch (Exception ignored) {
                }
            }
        }
        return nomeTmp;
    }

    public String getUrlSite() {
        return urlSite;
    }

    public void setUrlSite(String urlSite) {
        this.urlSite = urlSite;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public IdiomaBancoEnum getIdiomaBanco() {
        return idiomaBanco;
    }

    public void setIdiomaBanco(IdiomaBancoEnum idiomaBanco) {
        this.idiomaBanco = idiomaBanco;
    }

    public Integer getCodFinanceiro() {
        return codFinanceiro;
    }

    public void setCodFinanceiro(Integer codFinanceiro) {
        this.codFinanceiro = codFinanceiro;
    }

    public String getTokenSMSShortcode() {
        return tokenSMSShortcode;
    }

    public void setTokenSMSShortcode(String tokenSMSShortcode) {
        this.tokenSMSShortcode = tokenSMSShortcode;
    }

    public Date getDataExpiracao() {
        return dataExpiracao;
    }

    public void setDataExpiracao(Date dataExpiracao) {
        this.dataExpiracao = dataExpiracao;
    }

    public String getTokenMqv() {
        return tokenMqv;
    }

    public void setTokenMqv(String tokenMqv) {
        this.tokenMqv = tokenMqv;
    }
}
