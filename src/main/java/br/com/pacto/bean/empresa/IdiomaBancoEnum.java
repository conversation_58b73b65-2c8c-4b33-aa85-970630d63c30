package br.com.pacto.bean.empresa;

/**
 * <AUTHOR> 05/02/2019
 */
public enum IdiomaBancoEnum {
    PT("Português"),
    ES("Espanhol"),
    EN("Inglês");

    private String descricao;

    private IdiomaBancoEnum(String descricao) {
        this.descricao = descricao;
    }

    public static IdiomaBancoEnum getFromOrdinal(Integer cod) {
        for (IdiomaBancoEnum cfg : IdiomaBancoEnum.values()) {
            if (cod != null && cfg.ordinal() == cod) {
                return cfg;
            }
        }
        return null;

    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}
