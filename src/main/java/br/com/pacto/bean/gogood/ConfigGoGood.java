package br.com.pacto.bean.gogood;

import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.gogood.ConfigGoGoodDTO;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
public class ConfigGoGood implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    @ManyToOne
    private Empresa empresa;
    private String nome;
    private String tokenAcademyGoGood;
    private Integer usuarioLancou_codigo;
    private Boolean ativo;
    private Date dataLancamento;

    public ConfigGoGood() {
    }

    public ConfigGoGood(ConfigGoGoodDTO dto, Empresa empresa, Usuario usuario, Date dataLancamento) {
        this.codigo = dto.getCodigo();
        this.empresa = empresa;
        this.nome = empresa.getNome();
        this.tokenAcademyGoGood = dto.getTokenAcademyGoGood();
        this.usuarioLancou_codigo = usuario.getCodigo();
        this.dataLancamento = dataLancamento;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Empresa getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getTokenAcademyGoGood() {
        return tokenAcademyGoGood;
    }

    public void setTokenAcademyGoGood(String tokenAcademyGoGood) {
        this.tokenAcademyGoGood = tokenAcademyGoGood;
    }

    public Integer getUsuarioLancou_codigo() {
        return usuarioLancou_codigo;
    }

    public void setUsuarioLancou_codigo(Integer usuarioLancou) {
        this.usuarioLancou_codigo = usuarioLancou;
    }

    public Boolean getAtivo() {
        return ativo;
    }

    public void setAtivo(Boolean ativo) {
        this.ativo = ativo;
    }

    public Date getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }
}
