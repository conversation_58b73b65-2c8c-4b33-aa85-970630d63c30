package br.com.pacto.bean.atividade;

import br.com.pacto.bean.aparelho.Aparelho;
import br.com.pacto.bean.empresa.Empresa;
import com.fasterxml.jackson.annotation.JsonInclude;
import flex.messaging.io.ArrayCollection;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * Created by ulisses on 22/08/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AtividadeCompletaResponseTO {

    private Integer id;
    private String nome;
    private Integer idIA2;
    private String nomeOriginalIA;
    private String editadoPor;
    private Long ultimaEdicao;
    private Boolean ativa;
    private Boolean usarNaPrescricao;
    private Boolean estaNoTreino;
    private Boolean serieApenasDuracao;
    private TipoAtividadeEndpointEnum tipo;
    private String descricao;
    private List<AtividadeEmpresaResponseTO> empresas = new ArrayList<>();
    private String videoUri;
    private List<AtividadeImagemResponseTO> images = new ArrayList<>();
    private List<AtividadeCategoriaAtividadeResponseTO> categoriasAtividade = new ArrayList<>();
    private List<AtividadeAparelhoResponseTO> aparelhos = new ArrayList<>();
    private List<AtividadeGrupoMuscularResponseTO> gruposMusculares = new ArrayList<>();
    private List<AtividadeMusculoResponseTO> musculos = new ArrayList<>();
    private List<AtividadeNivelResponseTO> niveis = new ArrayList<>();
    private List<AtividadeRelacionadaResponseTO> atividadesRelacionadas = new ArrayList<>();
    private List<AtividadeVideoTO> linkVideos = new ArrayList<>();

    public AtividadeCompletaResponseTO() {
    }

    public AtividadeCompletaResponseTO(Atividade atividade) {
        this(atividade, false);
    }
    public AtividadeCompletaResponseTO(Atividade atividade, Boolean estaNoTreino) {
        this.id = atividade.getCodigo();
        this.nome = atividade.getNome();
        this.idIA2 = atividade.getIdIA2();
        this.nomeOriginalIA = atividade.getNomeOriginalIA();
        this.editadoPor = atividade.getEditadoPor();
        this.ultimaEdicao = atividade.getUltimaEdicao();
        this.estaNoTreino = estaNoTreino;
        this.ativa = atividade.isAtivo();
        this.serieApenasDuracao = atividade.getSeriesApenasDuracao();
        if(atividade.getTipo() != null){
            this.tipo = atividade.getTipo().getTipoAtividadeEndpointEnum();
        }
        this.descricao = atividade.getDescricao();
        if (atividade.getEmpresasHabilitadas() != null) {
            for (AtividadeEmpresa ae : atividade.getEmpresasHabilitadas()) {
                this.empresas.add(new AtividadeEmpresaResponseTO(ae));
            }
        }
        this.videoUri = atividade.getLinkVideo();
        this.usarNaPrescricao = atividade.getUsarNaPrescricao();
        if (atividade.getAnimacoes() != null) {
            for (AtividadeAnimacao aa : atividade.getAnimacoes()) {
                this.images.add(new AtividadeImagemResponseTO(aa));
            }
        }
        if (atividade.getCategorias() != null) {
            for (AtividadeCategoriaAtividade aca : atividade.getCategorias()) {
                this.categoriasAtividade.add(new AtividadeCategoriaAtividadeResponseTO(aca));
            }
        }
        if (!atividade.getAparelhos().isEmpty()) {
            List<AtividadeAparelho> aparelho = new ArrayList<AtividadeAparelho>();
            for( int i = 0; i < atividade.getAparelhos().size(); i++){
                if(atividade.getAparelhos().get(i).getAparelho() != null){
                   aparelho.add(atividade.getAparelhos().get(i));

                }
            }
            for (AtividadeAparelho aa : aparelho) {
                this.aparelhos.add(new AtividadeAparelhoResponseTO(aa));
            }
        }
        if (atividade.getGruposMusculares() != null) {
            for (AtividadeGrupoMuscular agm : atividade.getGruposMusculares()) {
                this.gruposMusculares.add(new AtividadeGrupoMuscularResponseTO(agm));
            }
        }
        if (atividade.getMusculos() != null) {
            for (AtividadeMusculo am : atividade.getMusculos()) {
                this.musculos.add(new AtividadeMusculoResponseTO(am));
            }
        }
        if (atividade.getNiveis() != null) {
            for (AtividadeNivel an : atividade.getNiveis()) {
                this.niveis.add(new AtividadeNivelResponseTO(an));
            }
        }
        if (atividade.getLinkVideos() != null) {
            for (AtividadeVideo aa : atividade.getLinkVideos()) {
                this.linkVideos.add(new AtividadeVideoTO(aa));
            }
        }
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Boolean getAtiva() {
        return ativa;
    }

    public void setAtiva(Boolean ativa) {
        this.ativa = ativa;
    }

    public Boolean getSerieApenasDuracao() {
        return serieApenasDuracao;
    }

    public void setSerieApenasDuracao(Boolean serieApenasDuracao) {
        this.serieApenasDuracao = serieApenasDuracao;
    }

    public TipoAtividadeEndpointEnum getTipo() {
        return tipo;
    }

    public void setTipo(TipoAtividadeEndpointEnum tipo) {
        this.tipo = tipo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public List<AtividadeEmpresaResponseTO> getEmpresas() {
        return empresas;
    }

    public void setEmpresas(List<AtividadeEmpresaResponseTO> empresas) {
        this.empresas = empresas;
    }

    public String getVideoUri() {
        return videoUri;
    }

    public void setVideoUri(String videoUri) {
        this.videoUri = videoUri;
    }

    public List<AtividadeImagemResponseTO> getImages() {
        return images;
    }

    public void setImages(List<AtividadeImagemResponseTO> images) {
        this.images = images;
    }

    public List<AtividadeCategoriaAtividadeResponseTO> getCategoriasAtividade() {
        return categoriasAtividade;
    }

    public void setCategoriasAtividade(List<AtividadeCategoriaAtividadeResponseTO> categoriasAtividade) {
        this.categoriasAtividade = categoriasAtividade;
    }

    public List<AtividadeAparelhoResponseTO> getAparelhos() {
        return aparelhos;
    }

    public void setAparelhos(List<AtividadeAparelhoResponseTO> aparelhos) {
        this.aparelhos = aparelhos;
    }

    public List<AtividadeGrupoMuscularResponseTO> getGruposMusculares() {
        return gruposMusculares;
    }

    public void setGruposMusculares(List<AtividadeGrupoMuscularResponseTO> gruposMusculares) {
        this.gruposMusculares = gruposMusculares;
    }

    public List<AtividadeMusculoResponseTO> getMusculos() {
        return musculos;
    }

    public void setMusculos(List<AtividadeMusculoResponseTO> musculos) {
        this.musculos = musculos;
    }

    public List<AtividadeNivelResponseTO> getNiveis() {
        return niveis;
    }

    public Boolean getEstaNoTreino() {
        return estaNoTreino;
    }

    public void setEstaNoTreino(Boolean estaNoTreino) {
        this.estaNoTreino = estaNoTreino;
    }

    public void setNiveis(List<AtividadeNivelResponseTO> niveis) {
        this.niveis = niveis;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        AtividadeCompletaResponseTO that = (AtividadeCompletaResponseTO) o;
        return id.equals(that.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    public Integer getIdIA2() {
        return idIA2;
    }

    public void setIdIA2(Integer idIA2) {
        this.idIA2 = idIA2;
    }

    public String getNomeOriginalIA() {
        return nomeOriginalIA;
    }

    public void setNomeOriginalIA(String nomeOriginalIA) {
        this.nomeOriginalIA = nomeOriginalIA;
    }

    public String getEditadoPor() {
        return editadoPor;
    }

    public void setEditadoPor(String editadoPor) {
        this.editadoPor = editadoPor;
    }

    public Long getUltimaEdicao() {
        return ultimaEdicao;
    }

    public void setUltimaEdicao(Long ultimaEdicao) {
        this.ultimaEdicao = ultimaEdicao;
    }

    public List<AtividadeRelacionadaResponseTO> getAtividadesRelacionadas() {
        return atividadesRelacionadas;
    }

    public void setAtividadesRelacionadas(List<AtividadeRelacionadaResponseTO> atividadesRelacionadas) {
        this.atividadesRelacionadas = atividadesRelacionadas;
    }

    public Boolean getUsarNaPrescricao() {
        return usarNaPrescricao;
    }

    public void setUsarNaPrescricao(Boolean usarNaPrescricao) {
        this.usarNaPrescricao = usarNaPrescricao;
    }

    public List<AtividadeVideoTO> getLinkVideos() {
        return linkVideos;
    }

    public void setLinkVideos(List<AtividadeVideoTO> linkVideos) {
        this.linkVideos = linkVideos;
    }
}
