/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.atividade;

import java.io.Serializable;
import java.util.Objects;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.ManyToOne;

/**
 *
 * <AUTHOR>
 */
@Entity
public class AtividadeCategoriaAtividade implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    @ManyToOne
    private Atividade atividade;
    @ManyToOne
    private CategoriaAtividade categoriaAtividade;

    public AtividadeCategoriaAtividade() {
    }

    public AtividadeCategoriaAtividade(CategoriaAtividade categoriaAtividade, Atividade atividade) {
        this.categoriaAtividade = categoriaAtividade;
        this.atividade = atividade;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public CategoriaAtividade getCategoriaAtividade() {
        return categoriaAtividade;
    }

    public void setCategoriaAtividade(CategoriaAtividade categoriaAtividade) {
        this.categoriaAtividade = categoriaAtividade;
    }

    public Atividade getAtividade() {
        return atividade;
    }

    public void setAtividade(Atividade atividade) {
        this.atividade = atividade;
    }

    @Override
    public String toString() {
        return this.atividade.toString() + this.categoriaAtividade.toString();
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        AtividadeCategoriaAtividade that = (AtividadeCategoriaAtividade) o;
        return Objects.equals(codigo, that.codigo);
    }

    @Override
    public int hashCode() {
        return Objects.hash(codigo);
    }
}
