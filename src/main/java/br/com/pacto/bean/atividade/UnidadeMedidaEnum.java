/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.atividade;

/**
 * <AUTHOR>
 */
public enum UnidadeMedidaEnum {

    NENHUM          (0, "Nenhum"),
    REPS            (1, "Reps"),
    TIME            (2, "Time"),
    DISTANCE        (3, "Distance"),
    WEIGHT          (4, "Weight"),
    CALS            (5, "Cals"),
    REPSFORTIME     (6, "Reps For Time"),
    ROUNDS          (7, "Rounds"),
    ROUNDS_REPS     (8, "Rounds and Reps");

    private Integer id;
    private String descricao;

    private UnidadeMedidaEnum(Integer codigo, String descricao) {
        this.id = codigo;
        this.descricao = descricao;
    }

    public static UnidadeMedidaEnum getFromId(Integer codigo) {
        for (UnidadeMedidaEnum tipo : UnidadeMedidaEnum.values()) {
            if (tipo.getId().equals(codigo)) {
                return tipo;
            }
        }
        return null;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}
