/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.atividade;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import javax.persistence.CascadeType;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(uniqueConstraints =
        @UniqueConstraint(columnNames = {"nome"}))
public class CategoriaAtividade implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private String nome;
    @OneToMany(targetEntity = AtividadeCategoriaAtividade.class, cascade = {CascadeType.PERSIST, CascadeType.MERGE, CascadeType.REMOVE},orphanRemoval = true, fetch = FetchType.LAZY, mappedBy = "categoriaAtividade")
    private List<AtividadeCategoriaAtividade> atividades = new ArrayList<AtividadeCategoriaAtividade>();

    public List<AtividadeCategoriaAtividade> getAtividades() {
        return atividades;
    }

    public void setAtividades(List<AtividadeCategoriaAtividade> atividades) {
        this.atividades = atividades;
    }

    public CategoriaAtividade() {
    }

    public CategoriaAtividade(final String nome) {
        this.nome = nome;
    }
    
    public CategoriaAtividade(final Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String toString() {
        return this.hashCode() + " - " + this.nome;
    }
}
