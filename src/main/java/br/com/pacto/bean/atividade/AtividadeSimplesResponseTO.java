package br.com.pacto.bean.atividade;

import br.com.pacto.bean.musculo.GrupoMuscularResponseTO;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * Created by ulisses on 22/08/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AtividadeSimplesResponseTO {

    private Integer id;
    private String nome;
    private Boolean estaNoTreino;
    private String image;
    private String imagePequena;
    private String imageMiniatura;
    private TipoAtividadeEndpointEnum tipo;
    private List<GrupoMuscularResponseTO> gruposMusculares;

    public AtividadeSimplesResponseTO(Atividade atividade, Boolean estaNoTreino) {
        this.id = atividade.getCodigo();
        this.nome = atividade.getNome();
        this.estaNoTreino = estaNoTreino;
        if(atividade.getTipo() != null){
            this.tipo = atividade.getTipo().getTipoAtividadeEndpointEnum();
        }
        if(atividade.getAnimacoes() != null && !atividade.getAnimacoes().isEmpty()){
            this.image = new AtividadeImagemResponseTO(atividade.getAnimacoes().get(0)).getUri();
            this.imagePequena = new AtividadeImagemResponseTO(atividade.getAnimacoes().get(0)).getFotoKeyPequena();
            this.imageMiniatura = new AtividadeImagemResponseTO(atividade.getAnimacoes().get(0)).getFotoKeyMiniatura();
        }
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Boolean getEstaNoTreino() {
        return estaNoTreino;
    }

    public void setEstaNoTreino(Boolean estaNoTreino) {
        this.estaNoTreino = estaNoTreino;
    }

    public String getImage() {
        return image;
    }

    public void setImage(String image) {
        this.image = image;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        AtividadeSimplesResponseTO that = (AtividadeSimplesResponseTO) o;
        return id.equals(that.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    public TipoAtividadeEndpointEnum getTipo() {
        return tipo;
    }

    public void setTipo(TipoAtividadeEndpointEnum tipo) {
        this.tipo = tipo;
    }

    public String getImagePequena() {
        return imagePequena;
    }

    public void setImagePequena(String imagePequena) {
        this.imagePequena = imagePequena;
    }

    public String getImageMiniatura() {
        return imageMiniatura;
    }

    public void setImageMiniatura(String imageMiniatura) {
        this.imageMiniatura = imageMiniatura;
    }

    public List<GrupoMuscularResponseTO> getGruposMusculares() {
        return gruposMusculares;
    }

    public void setGruposMusculares(List<GrupoMuscularResponseTO> gruposMusculares) {
        this.gruposMusculares = gruposMusculares;
    }

}
