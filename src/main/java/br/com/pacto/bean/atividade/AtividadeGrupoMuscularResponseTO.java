package br.com.pacto.bean.atividade;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Created by ulisses on 03/08/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Atividade do grupo de músculo")
public class AtividadeGrupoMuscularResponseTO {

    @ApiModelProperty(value = "ID da atividade de grupo muscular", example = "1")
    private Integer id;
    @ApiModelProperty(value = "Nome da atividade de grupo muscular", example = "Atividade de grupo muscular 1")
    private String nome;

    public AtividadeGrupoMuscularResponseTO(){

    }

    public AtividadeGrupoMuscularResponseTO(AtividadeGrupoMuscular agm){
        this.id = agm.getGrupoMuscular().getCodigo();
        this.nome = agm.getGrupoMuscular().getNome();
    }

    public AtividadeGrupoMuscularResponseTO(Integer id, String nome){
        this.id = id;
        this.nome = nome;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }
}


