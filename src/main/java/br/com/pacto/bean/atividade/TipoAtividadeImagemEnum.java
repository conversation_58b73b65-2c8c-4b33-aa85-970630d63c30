package br.com.pacto.bean.atividade;

/**
 * Created by ulisses on 22/08/2018.
 */
public enum TipoAtividadeImagemEnum {

    CATALOG(0, "Imagem de catálogo"),
    UPLOAD(1, "Imagem enviada pelo usuário");

    private Integer id;
    private String descricao;

    TipoAtividadeImagemEnum(Integer codigo, String descricao) {
        this.id = codigo;
        this.descricao = descricao;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

}
