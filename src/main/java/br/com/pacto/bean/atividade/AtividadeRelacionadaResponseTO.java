package br.com.pacto.bean.atividade;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.List;

/**
 * Created by ulisses on 27/08/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AtividadeRelacionadaResponseTO {

    private Integer id;
    private String nome;
    private List<AtividadeImagemResponseTO> imagem;

    public AtividadeRelacionadaResponseTO() {

    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public List<AtividadeImagemResponseTO> getImagem() {
        return imagem;
    }

    public void setImagem(List<AtividadeImagemResponseTO> imagem) {
        this.imagem = imagem;
    }
}
