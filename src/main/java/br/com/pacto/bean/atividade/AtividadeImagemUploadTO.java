package br.com.pacto.bean.atividade;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * Created by ulisses on 23/08/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AtividadeImagemUploadTO {

    private byte[] data;
    private String nome;
    private Boolean professor;

    public byte[] getData() {
        return data;
    }

    public void setData(byte[] data) {
        this.data = data;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Boolean getProfessor() {
        return professor;
    }

    public void setProfessor(<PERSON><PERSON>an professor) {
        this.professor = professor;
    }
}
