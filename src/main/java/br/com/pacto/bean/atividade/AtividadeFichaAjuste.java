/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.atividade;

import java.io.Serializable;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.ManyToOne;

/**
 *
 * <AUTHOR>
 */
@Entity
public class AtividadeFichaAjuste implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private String nome;
    private String valor;
    @ManyToOne
    private AtividadeFicha atividadeFicha;

    public AtividadeFichaAjuste() {
    }

    public AtividadeFichaAjuste(AtividadeFicha atividadeFicha, final String nome, final String valor) {
        this.atividadeFicha = atividadeFicha;
        this.nome = nome;
        this.valor = valor;
    }
    
    public AtividadeFichaAjuste(final String nome, final String valor) {
        this.nome = nome;
        this.valor = valor;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getValor() {
        return valor;
    }

    public void setValor(String valor) {
        this.valor = valor;
    }

    public AtividadeFicha getAtividadeFicha() {
        return atividadeFicha;
    }

    public void setAtividadeFicha(AtividadeFicha atividadeFicha) {
        this.atividadeFicha = atividadeFicha;
    }
    
    @Override
    public int hashCode() { 
        return this.getNome() != null && !this.getNome().isEmpty() ? 
                this.getNome().hashCode() : super.hashCode();
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null) {
            return false;
        }
        if (!(obj instanceof AtividadeFichaAjuste)) {
            return false;
        }
        final AtividadeFichaAjuste other = (AtividadeFichaAjuste) obj;
        return this.getNome() != null && other.getNome() != null && this.getNome().equals(other.getNome());
    }
    
}
