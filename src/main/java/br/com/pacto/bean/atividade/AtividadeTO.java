package br.com.pacto.bean.atividade;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.List;

/**
 * Created by ulisses on 22/08/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AtividadeTO {

    private Integer id;
    private String nome;
    private Boolean ativa;
    private Boolean serieApenasDuracao;
    private TipoAtividadeEndpointEnum tipo;
    private String descricao;
    private List<AtividadeEmpresaTO> empresas;
    private String videoUri;
    private List<AtividadeVideoTO> linkVideos;
    private List<AtividadeImagemTO> images;
    private List<AtividadeImagemUploadTO> imageUploads;
    private List<Integer> atividadesRelacionadas;
    private List<Integer> categoriaAtividadeIds;
    private List<Integer> aparelhoIds;
    private List<Integer> grupoMuscularIds;
    private List<Integer> musculoIds;
    private List<Integer> nivelIds;
    private Boolean crossfit = false;
    private Boolean usarNaPrescricao = Boolean.TRUE;

    public List<Integer> getAtividadesRelacionadas() {
        return atividadesRelacionadas;
    }

    public void setAtividadesRelacionadas(List<Integer> atividadesRelacionadas) {
        this.atividadesRelacionadas = atividadesRelacionadas;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Boolean getAtiva() {
        return ativa;
    }

    public void setAtiva(Boolean ativa) {
        this.ativa = ativa;
    }

    public Boolean getSerieApenasDuracao() {
        return serieApenasDuracao;
    }

    public void setSerieApenasDuracao(Boolean serieApenasDuracao) {
        this.serieApenasDuracao = serieApenasDuracao;
    }

    public TipoAtividadeEndpointEnum getTipo() {
        return tipo;
    }

    public void setTipo(TipoAtividadeEndpointEnum tipo) {
        this.tipo = tipo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public List<AtividadeEmpresaTO> getEmpresas() {
        return empresas;
    }

    public void setEmpresas(List<AtividadeEmpresaTO> empresas) {
        this.empresas = empresas;
    }

    public String getVideoUri() {
        return videoUri;
    }

    public void setVideoUri(String videoUri) {
        this.videoUri = videoUri;
    }

    public List<AtividadeImagemTO> getImages() {
        return images;
    }

    public void setImages(List<AtividadeImagemTO> images) {
        this.images = images;
    }

    public List<AtividadeImagemUploadTO> getImageUploads() {
        return imageUploads;
    }

    public void setImageUploads(List<AtividadeImagemUploadTO> imageUploads) {
        this.imageUploads = imageUploads;
    }

    public List<Integer> getCategoriaAtividadeIds() {
        return categoriaAtividadeIds;
    }

    public void setCategoriaAtividadeIds(List<Integer> categoriaAtividadeIds) {
        this.categoriaAtividadeIds = categoriaAtividadeIds;
    }

    public List<Integer> getAparelhoIds() {
        return aparelhoIds;
    }

    public void setAparelhoIds(List<Integer> aparelhoIds) {
        this.aparelhoIds = aparelhoIds;
    }

    public List<Integer> getGrupoMuscularIds() {
        return grupoMuscularIds;
    }

    public void setGrupoMuscularIds(List<Integer> grupoMuscularIds) {
        this.grupoMuscularIds = grupoMuscularIds;
    }

    public List<Integer> getMusculoIds() {
        return musculoIds;
    }

    public void setMusculoIds(List<Integer> musculoIds) {
        this.musculoIds = musculoIds;
    }

    public List<Integer> getNivelIds() {
        return nivelIds;
    }

    public void setNivelIds(List<Integer> nivelIds) {
        this.nivelIds = nivelIds;
    }

    public Boolean getCrossfit() {
        return crossfit;
    }

    public void setCrossfit(Boolean crossfit) {
        this.crossfit = crossfit;
    }

    public Boolean getUsarNaPrescricao() {
        return usarNaPrescricao;
    }

    public void setUsarNaPrescricao(Boolean usarNaPrescricao) {
        this.usarNaPrescricao = usarNaPrescricao;
    }

    public List<AtividadeVideoTO> getLinkVideos() {
        return linkVideos;
    }

    public void setLinkVideos(List<AtividadeVideoTO> linkVideos) {
        this.linkVideos = linkVideos;
    }
}
