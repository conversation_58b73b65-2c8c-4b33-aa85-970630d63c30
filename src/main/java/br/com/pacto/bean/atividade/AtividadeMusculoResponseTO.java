package br.com.pacto.bean.atividade;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * Created by ulisses on 27/08/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AtividadeMusculoResponseTO {

    private Integer id;
    private String nome;

    public AtividadeMusculoResponseTO() {

    }

    public AtividadeMusculoResponseTO(AtividadeMusculo am) {
        this.id = am.getMusculo().getCodigo();
        this.nome = am.getMusculo().getNome();
    }

    public AtividadeMusculoResponseTO(Integer id, String nome){
        this.id = id;
        this.nome = nome;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }
}
