package br.com.pacto.bean.atividade;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * Created by ulisses on 23/08/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AtividadeImagemTO {

    private TipoAtividadeImagemEnum type;
    private String id;
    private Boolean professor;

    public TipoAtividadeImagemEnum getType() {
        return type;
    }

    public void setType(TipoAtividadeImagemEnum type) {
        this.type = type;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Boolean getProfessor() {
        return professor;
    }

    public void setProfessor(Boolean professor) {
        this.professor = professor;
    }
}
