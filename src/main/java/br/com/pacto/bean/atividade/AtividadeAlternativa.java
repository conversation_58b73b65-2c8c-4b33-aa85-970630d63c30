/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.atividade;

import javax.persistence.*;
import java.io.Serializable;

@Entity
public class AtividadeAlternativa implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    @ManyToOne
    private Atividade atividade;
    private Integer atividadeAlternativa;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Atividade getAtividade() {
        return atividade;
    }

    public void setAtividade(Atividade atividade) {
        this.atividade = atividade;
    }

    public Integer getAtividadeAlternativa() {
        return atividadeAlternativa;
    }

    public void setAtividadeAlternativa(Integer atividadeAlternativa) {
        this.atividadeAlternativa = atividadeAlternativa;
    }
}
