/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.atividade;

/**
 *
 * <AUTHOR>
 */
public enum CadastrosAuxiliaresEnum {
    
    APARELHO(1, "cadastros.aparelhos", "AtividadeAparelho", "apar<PERSON><PERSON>", "APA", "aparelhos"),
    CATEGORIA_ATIVIDADE(2,"cadastros.categoriaatividade", "AtividadeCategoriaAtividade", "categoriaAtividade", "CAT", "categorias"),
    GRUPOS_MUSCULARES(3,"cadastros.gruposmusculares", "AtividadeGrupoMuscular", "grupoMuscular", "GRM", "gruposMusculares"),
    MUSCULOS(4, "cadastros.musculos", "AtividadeMusculo","musculo", "MUS", "musculos"),
    NIVEL(5,"cadastros.nivel", "AtividadeNivel", "nivel", "NIV", "niveis"),
    EMPRESA(6,"cadastros.empresa", "AtividadeEmpresa", "AtividadeEmpresa", "EMP", "empresasHabilitadas");

    private CadastrosAuxiliaresEnum() {
    }
    
    private Integer id;
    private String label;
    private String nomeEntidade;
    private String nomeAtributo;
    private String prefixo;
    private String nomeListas;
    
    
    private CadastrosAuxiliaresEnum(int id, String label, String nomeEntidade, String nomeAtributo, String prefixo, String nomeListas){
        this.id = id;
        this.nomeAtributo = nomeAtributo;
        this.label = label;
        this.nomeEntidade = nomeEntidade;
        this.prefixo = prefixo;
        this.nomeListas = nomeListas;
    }

    public static CadastrosAuxiliaresEnum obterPorPrefixo(String prefixo){
        for(CadastrosAuxiliaresEnum obj : CadastrosAuxiliaresEnum.values()){
            if(obj.getPrefixo().equals(prefixo)){
                return obj;
            }
        }
        return null;
    }

    public String getNomeListas() {
        return nomeListas;
    }

    public void setNomeListas(String nomeListas) {
        this.nomeListas = nomeListas;
    }
    
    public String getPrefixo() {
        return prefixo;
    }

    public void setPrefixo(String prefixo) {
        this.prefixo = prefixo;
    }

    public String getNomeAtributo() {
        return nomeAtributo;
    }

    public void setNomeAtributo(String nomeAtributo) {
        this.nomeAtributo = nomeAtributo;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getNomeEntidade() {
        return nomeEntidade;
    }

    public void setNomeEntidade(String nomeEntidade) {
        this.nomeEntidade = nomeEntidade;
    }
    
}
