package br.com.pacto.bean.atividade;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Created by ulisses on 22/08/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Empresas da atividade")
public class AtividadeEmpresaResponseTO {

    @ApiModelProperty(value = "ID da empresa", example = "1")
    private Integer id;
    @ApiModelProperty(value = "Identificador")
    private String identificador;
    @ApiModelProperty(value = "Identificador")
    private EmpresaBasicaResponseTO empresa;

    public AtividadeEmpresaResponseTO() {

    }
    public AtividadeEmpresaResponseTO(AtividadeEmpresa ae) {
        this.id = ae.getCodigo();
        this.identificador = ae.getIdentificador();
        if (ae.getEmpresa() != null) {
            this.empresa = new EmpresaBasicaResponseTO(ae.getEmpresa());
        }
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getIdentificador() {
        return identificador;
    }

    public void setIdentificador(String identificador) {
        this.identificador = identificador;
    }

    public EmpresaBasicaResponseTO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaBasicaResponseTO empresa) {
        this.empresa = empresa;
    }

}
