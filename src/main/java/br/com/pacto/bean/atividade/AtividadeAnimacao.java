/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.atividade;

import br.com.pacto.bean.animacao.Animacao;
import java.io.Serializable;
import java.util.Calendar;
import java.util.Objects;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.ManyToOne;

import br.com.pacto.objeto.Aplicacao;

/**
 *
 * <AUTHOR>
 */
@Entity
public class AtividadeAnimacao implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    @ManyToOne(fetch = FetchType.LAZY)
    private Atividade atividade;
    @ManyToOne(fetch = FetchType.LAZY)
    private Animacao animacao;
    private String fotoKey;
    private String fotoKeyPequena;
    private String fotoKeyMiniatura;
    @Column(columnDefinition = "boolean DEFAULT false")
    private <PERSON><PERSON><PERSON> professor  = Boolean.FALSE;

    public AtividadeAnimacao() {
    }

    public AtividadeAnimacao(Animacao animacao, Atividade atividade) {
        this.atividade = atividade;
        this.animacao = animacao;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Atividade getAtividade() {
        return atividade;
    }

    public void setAtividade(Atividade atividade) {
        this.atividade = atividade;
    }

    public Animacao getAnimacao() {
        return animacao;
    }

    public void setAnimacao(Animacao animacao) {
        this.animacao = animacao;
    }

    public String getFotoKey() {
        return fotoKey;
    }

    public String getUrlFoto(){
        Calendar calendar = Calendar.getInstance();
        return fotoKey == null ? "" : Aplicacao.obterUrlFotoDaNuvem(fotoKey)+"?"+calendar.get(Calendar.WEEK_OF_YEAR);
    }

    public String getUrlFotoMin(){
        return fotoKeyMiniatura == null ? "" : Aplicacao.obterUrlFotoDaNuvem(fotoKeyMiniatura);
    }

    public String getUrlFotoPeq(){
        return fotoKeyPequena== null ? "" : Aplicacao.obterUrlFotoDaNuvem(fotoKeyPequena);
    }

    public void setFotoKey(String fotoKey) {
        this.fotoKey = fotoKey;
    }

    public String getFotoKeyPequena() {
        return fotoKeyPequena;
    }

    public void setFotoKeyPequena(String fotoKeyPequena) {
        this.fotoKeyPequena = fotoKeyPequena;
    }

    public String getFotoKeyMiniatura() {
        return fotoKeyMiniatura;
    }

    public void setFotoKeyMiniatura(String fotoKeyMiniatura) {
        this.fotoKeyMiniatura = fotoKeyMiniatura;
    }

    public Boolean getProfessor() {return professor;}

    public void setProfessor(Boolean professor) {this.professor = professor;}
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        AtividadeAnimacao that = (AtividadeAnimacao) o;
        return Objects.equals(codigo, that.codigo);
    }

    @Override
    public int hashCode() {
        return Objects.hash(codigo);
    }
}
