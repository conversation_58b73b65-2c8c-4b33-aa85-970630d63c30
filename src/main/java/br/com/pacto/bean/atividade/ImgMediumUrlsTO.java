package br.com.pacto.bean.atividade;

import com.fasterxml.jackson.annotation.JsonInclude;


/**
 * Created by ulisses on 23/08/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ImgMediumUrlsTO {
    private String linkImg;
    private Boolean professor = false;

    public ImgMediumUrlsTO(){
    }

    public String getLinkImg() {
        return linkImg;
    }

    public void setLinkImg(String linkImg) {
        this.linkImg = linkImg;
    }

    public Boolean getProfessor() {
        return professor;
    }

    public void setProfessor(Boolean professor) {
        this.professor = professor;
    }

}
