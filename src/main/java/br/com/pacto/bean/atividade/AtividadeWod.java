/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.atividade;

import br.com.pacto.bean.wod.Wod;

import javax.persistence.*;
import java.io.Serializable;

/*
 * <AUTHOR>
 */
@Entity
public class AtividadeWod implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private Integer ordem;
    @ManyToOne
    private Atividade atividade;
    @ManyToOne
    private Wod wod;

    public AtividadeWod() {
    }

    public AtividadeWod(Wod wod, Atividade atividade) {
        this.wod = wod;
        this.atividade = atividade;
    }

    public Integer getCodigo() {
        if (codigo == null) {
            codigo = 0;
        }
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getOrdem() {
        if (ordem == null) {
            ordem = 0;
        }
        return ordem;
    }

    public void setOrdem(Integer ordem) {
        this.ordem = ordem;
    }

    public Atividade getAtividade() {
        if (atividade == null) {
            atividade = new Atividade();
        }
        return atividade;
    }

    public void setAtividade(Atividade atividade) {
        this.atividade = atividade;
    }

    public Wod getWod() {
        if (wod == null) {
            wod = new Wod();
        }
        return wod;
    }

    public void setWod(Wod wod) {
        this.wod = wod;
    }
}
