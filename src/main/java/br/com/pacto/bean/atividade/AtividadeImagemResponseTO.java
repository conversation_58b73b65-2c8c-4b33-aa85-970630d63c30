package br.com.pacto.bean.atividade;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Created by ulisses on 22/08/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Imagem da atividade")
public class AtividadeImagemResponseTO {

    @ApiModelProperty(value = "Tipo da imagem da atividade", example = "0")
    private TipoAtividadeImagemEnum type;
    @ApiModelProperty(value = "ID da imagem", example = "1")
    private Integer id;
    @ApiModelProperty(value = "URI da imagem da atividade")
    private String uri;
    @ApiModelProperty(value = "Nome da imagem da atividade", example = "Imagem 1")
    private String nome;
    @ApiModelProperty(value = "Foto key pequena")
    private String fotoKeyPequena;
    @ApiModelProperty(value = "Foto key miniatura")
    private String fotoKeyMiniatura;
    @ApiModelProperty(value = "Professor")
    private Boolean professor = false;

    public AtividadeImagemResponseTO() {

    }
    public AtividadeImagemResponseTO(AtividadeAnimacao aa) {

        this.type = aa.getFotoKey() == null ? TipoAtividadeImagemEnum.CATALOG : TipoAtividadeImagemEnum.UPLOAD;
        this.professor = aa.getProfessor();
        if (aa.getAnimacao() == null) {
            this.id = aa.getCodigo();
            this.uri = aa.getUrlFoto();
            this.fotoKeyPequena = aa.getUrlFotoPeq();
            this.fotoKeyMiniatura = aa.getUrlFotoMin();
            this.nome= "";
        }else{
            this.id = aa.getAnimacao().getCodigo();
            this.uri = "http://app.pactosolucoes.com.br/midias/DEFAULT/Large/Square/" + aa.getAnimacao().getUrl();
            this.nome = aa.getAnimacao().getTitulo();
            this.fotoKeyPequena = "http://app.pactosolucoes.com.br/midias/DEFAULT/Medium/Square/" + aa.getAnimacao().getUrl();;
            this.fotoKeyMiniatura = "http://app.pactosolucoes.com.br/midias/DEFAULT/Small/Square/" + aa.getAnimacao().getUrl();;
        }
    }

    public TipoAtividadeImagemEnum getType() {
        return type;
    }

    public void setType(TipoAtividadeImagemEnum type) {
        this.type = type;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getUri() {
        return uri;
    }

    public void setUri(String uri) {
        this.uri = uri;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getFotoKeyPequena() {
        return fotoKeyPequena;
    }

    public void setFotoKeyPequena(String fotoKeyPequena) {
        this.fotoKeyPequena = fotoKeyPequena;
    }

    public String getFotoKeyMiniatura() {
        return fotoKeyMiniatura;
    }

    public void setFotoKeyMiniatura(String fotoKeyMiniatura) {
        this.fotoKeyMiniatura = fotoKeyMiniatura;
    }
    public Boolean getProfessor() {
        return professor;
    }

    public void setProfessor(Boolean professor) {
        this.professor = professor;
    }
}
