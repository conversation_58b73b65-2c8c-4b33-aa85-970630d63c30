/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.atividade;



import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.ManyToOne;
import java.io.Serializable;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
@Entity
public class AtividadeVideo implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    @ManyToOne(fetch = FetchType.LAZY)
    private Atividade atividade;

    private String linkvideo;

    @Column(columnDefinition = "boolean DEFAULT false")
    private Bo<PERSON><PERSON> professor  = Boolean.FALSE;

    public AtividadeVideo() {
    }

    public AtividadeVideo(AtividadeVideoTO atividadeVideoTO, Atividade atividade) {
        this.codigo = atividadeVideoTO.getId();
        this.atividade = atividade;
        this.linkvideo = atividadeVideoTO.getLinkVideo();
        this.professor = atividadeVideoTO.getProfessor();
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Atividade getAtividade() {
        return atividade;
    }

    public void setAtividade(Atividade atividade) {
        this.atividade = atividade;
    }

    public String getLinkvideo() {
        return linkvideo;
    }

    public void setLinkvideo(String linkvideo) {
        this.linkvideo = linkvideo;
    }

    public Boolean getProfessor() {
        return professor;
    }

    public void setProfessor(Boolean professor) {
        this.professor = professor;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        AtividadeVideo that = (AtividadeVideo) o;
        return Objects.equals(codigo, that.codigo);
    }

    @Override
    public int hashCode() {
        return Objects.hash(codigo);
    }
}
