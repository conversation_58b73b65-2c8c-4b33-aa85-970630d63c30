package br.com.pacto.bean.atividade;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * Created by ulisses on 22/08/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AtividadeEmpresaTO {

    private Integer id;
    private String identificador;
    private EmpresaBasicaTO empresa;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getIdentificador() {
        return identificador;
    }

    public void setIdentificador(String identificador) {
        this.identificador = identificador;
    }

    public EmpresaBasicaTO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaBasicaTO empresa) {
        this.empresa = empresa;
    }

}
