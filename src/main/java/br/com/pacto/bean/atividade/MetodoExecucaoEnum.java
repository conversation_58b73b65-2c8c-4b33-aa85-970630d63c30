/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.atividade;

/**
 *
 * <AUTHOR>
 */
public enum MetodoExecucaoEnum {

    PIRAMIDE_DECRESCENTE(0, "Pirâmide Decrescente"),
    PIRAMIDE_CRESCENTE(1, "Pirâmide Crescente"),
    CIRCUITO(2, "Circuito"),
    ISOMETRICO(3, "Isométrico"),
    SUPER_SERIE(4, "Super-Série"),
    BI_SET(5, "BI-Set"),
    TRI_SET(6, "TRI-Set"),
    DROP_SET(7, "Drop-Set"),
    ONDULATORIO(8, "Ondulatório"),
    PROGRESSAO_DUPLA(9,"Progressão Dupla"),
    DE_LORME(11,"<PERSON> Lorme"),
    ERPAD(12,"Erpad"),
    PARCELADO(13,"Parcela<PERSON>"),
    DUPLAMENTE_PARCELADO(14,"<PERSON><PERSON><PERSON><PERSON>"),
    TRIPLADO_PARCELADO(15,"Triplamente Parcelado"),
    PUXE_EMPURRE(16,"Puxe-Empurre"),
    REPETICAO_ROUBADA(17,"Repetição Roubada"),
    REPETICAO_FORCADA(18,"Repetição Forçada"),
    DTA(19,"D.T.A."),
    REPETICAO_PARCIAL(20,"Repetição Parcial"),
    PICO_CONTRACAO(21,"Pico de Contração"),
    TENSAO_LENTA_CONTINUA(22,"Tensão Lenta e Contínua"),
    SET_DESCENDENTE(23,"Set Descendente"),
    ISOLAMENTO(24,"Isolamento"),
    SUPER_SET(25,"Super-Set"),
    SERIE_COMPOSTA(26,"Série Composta"),
    SUPERSET_MULTIPLO(27,"Super-Set Múltiplo"),
    PRE_EXAUSTAO(28,"Pré-Exaustão"),
    SERIE_GIGANTE(29,"Série Gigante"),
    PHA(30,"P.H.A."),
    SUPER_CIRCUITO(31,"Super-Circuito"),
    MUSCULACAO_INTERVALADA(32,"Musculação Intervalada"),
    PLIOMETRICO(33,"Pliométrico"),
    REPETICAO_NEGATIVA(34,"Repetição Negativa"),
    NAUTILUS(35,"Nautilus"),
    HEAVY_DUTY(36,"Heavy-Duty"),
    REST_PAUSE (37, "Rest Pause"),
    POS_EXAUSTAO (38,"Pós-Exaustão"),
    EXAUSTAO (39,"Exaustão"),
    STRIP_SET (40,"Strip Set"),
    SET_21 (41,"Set 21"),
    SUPER_DROP_SET (42,"Super Drop Set"),
    FLUSHING (43,"Flushing"),
    CONTRACAO_ISOMETRICA(44,"Contração Isométrica"),
    CONTINUO(45,"Contínuo"),
    COMBINADO(46,"Combinado"),
    ALTERNADO(47,"Alternado"),
    ALTERNADO_SIMULTANEO(48,"Alternado + Simultâneo"),
    FST(49,"FST-7"),
    SST(50,"SST"),
    HIIT(51,"HIIT"),
    TABATA(52,"Tabata"),
    MET_6_20(53,"6/20"),
    TEMPOS_2(54,"2 Tempos"),
    TEMPOS_3(55,"3 Tempos"),
    NONSTOP(56,"Nonstop"),
    CLUSTER(57,"Cluster"),
    PONTO_ZERO(58,"Ponto Zero"),
    NAO_ATRIBUIDO(999,"Não atribuído"),
    DEADSTOP(59,"Deadstop"),
    ;
    
    
    
    private Integer id;
    private String descricao;

    private MetodoExecucaoEnum(Integer id, final String descricao) {
        this.id = id;
        this.descricao = descricao;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public static MetodoExecucaoEnum getFromId(Integer id) {
        for (MetodoExecucaoEnum tipo : values()) {
            if (tipo.getId().equals(id)) {
                return tipo;
            }
        }
        return null;
    }

    public static MetodoExecucaoEnum getFromOrdinal(Integer id) {
        for (MetodoExecucaoEnum tipo : values()) {
            if (tipo.ordinal() == id) {
                return tipo;
            }
        }
        return null;
    }
}
