package br.com.pacto.bean.usuario;

public class UsuarioCacheTO {

    private static final long HORAS_ADICIONAR = 6L * 3600000;
    private final long dataExpiracaoCache;
    private final Usuario usuario;

    public UsuarioCacheTO(Usuario usuario) {
        this.usuario = usuario;
        this.dataExpiracaoCache = System.currentTimeMillis() + HORAS_ADICIONAR;
    }

    public Usuario getUsuario() {
        return usuario;
    }

    public boolean isTokenExpirado() {
        return System.currentTimeMillis() >= dataExpiracaoCache;
    }
}
