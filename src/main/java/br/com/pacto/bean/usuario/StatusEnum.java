/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.usuario;

/**
 *
 * <AUTHOR>
 */
public enum StatusEnum {

    ATIVO(0, "Ativo"),
    INATIVO(1, "Inativo"),
    AGUARDANDO(3, "Aguardando ativação");
    private Integer id;
    private String descricao;

    private StatusEnum(Integer codigo, String descricao) {
        this.id = codigo;
        this.descricao = descricao;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public static StatusEnum setStatus(String str) {
        for (StatusEnum statusEnum : StatusEnum.values()) {
            if (statusEnum.name().equalsIgnoreCase(str)) {
                return statusEnum;
            }
        }
        return null;
    }

    public static StatusEnum getFromID(int id){
        for(StatusEnum statusEnum : StatusEnum.values()){
            if(statusEnum.getId() == id){
                return statusEnum;
            }
        }
        return null;
    }
}
