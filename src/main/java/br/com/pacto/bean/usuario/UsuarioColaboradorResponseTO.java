package br.com.pacto.bean.usuario;

import br.com.pacto.bean.colaborador.ColaboradorSimplesTO;
import br.com.pacto.bean.colaborador.PerfilUsuarioResponseTO;
import br.com.pacto.bean.colaborador.SituacaoColaboradorEnum;
import br.com.pacto.bean.colaborador.TipoUsuarioColaboradorEnum;
import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class UsuarioColaboradorResponseTO {

    private String email;
    private Integer id;
    private ColaboradorSimplesTO colaborador;
    private String appUserName;
    private String appPassword;
    private String empresas;
    private TipoUsuarioColaboradorEnum tipoUsuario;
    private PerfilUsuarioResponseTO perfilUsuarioPermissoes;
    private String imagemData;
    private String tipoImagem;
    private SituacaoColaboradorEnum situacao;
    private Boolean emailVerificado;

    public UsuarioColaboradorResponseTO(Usuario usuario, Boolean treinoIndependente, String empresas) {
        this.id = usuario.getCodigo();
        this.empresas = empresas;
        this.colaborador = new ColaboradorSimplesTO(usuario.getProfessor(), treinoIndependente);
        this.appUserName = usuario.getUserName();
        this.tipoUsuario = usuario.getTipo().obterTipoUsuarioColaborador();
        this.perfilUsuarioPermissoes = new PerfilUsuarioResponseTO(usuario.getPerfil());
        this.email = usuario.getEmail();
        this.situacao = usuario.getStatus() == null ? SituacaoColaboradorEnum.INATIVO : SituacaoColaboradorEnum.valueOf(usuario.getStatus().name());
        this.emailVerificado = usuario.getUsuarioEmail() != null ? usuario.getUsuarioEmail().getVerificado() : false;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public ColaboradorSimplesTO getColaborador() {
        return colaborador;
    }

    public void setColaborador(ColaboradorSimplesTO colaborador) {
        this.colaborador = colaborador;
    }

    public String getAppUserName() {
        return appUserName;
    }

    public void setAppUserName(String appUserName) {
        this.appUserName = appUserName;
    }

    public String getAppPassword() {
        return appPassword;
    }

    public void setAppPassword(String appPassword) {
        this.appPassword = appPassword;
    }

    public TipoUsuarioColaboradorEnum getTipoUsuario() {
        return tipoUsuario;
    }

    public void setTipoUsuario(TipoUsuarioColaboradorEnum tipoUsuario) {
        this.tipoUsuario = tipoUsuario;
    }

    public PerfilUsuarioResponseTO getPerfilUsuarioPermissoes() {
        return perfilUsuarioPermissoes;
    }

    public void setPerfilUsuarioPermissoes(PerfilUsuarioResponseTO perfilUsuarioPermissoes) {
        this.perfilUsuarioPermissoes = perfilUsuarioPermissoes;
    }

    public String getImagemData() {
        return imagemData;
    }

    public void setImagemData(String imagemData) {
        this.imagemData = imagemData;
    }

    public String getTipoImagem() {
        return tipoImagem;
    }

    public void setTipoImagem(String tipoImagem) {
        this.tipoImagem = tipoImagem;
    }

    public SituacaoColaboradorEnum getSituacao() {
        return situacao;
    }

    public void setSituacao(SituacaoColaboradorEnum situacao) {
        this.situacao = situacao;
    }

    public String getEmpresas() {
        return empresas;
    }

    public void setEmpresas(String empresas) {
        this.empresas = empresas;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Boolean getEmailVerificado() {
        return emailVerificado;
    }

    public void setEmailVerificado(Boolean emailVerificado) {
        this.emailVerificado = emailVerificado;
    }
}
