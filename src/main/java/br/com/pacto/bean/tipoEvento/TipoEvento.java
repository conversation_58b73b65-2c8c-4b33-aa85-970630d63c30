/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.tipoEvento;


import static br.com.pacto.util.enumeradores.PaletaCoresEnum.getFromHex;

import br.com.pacto.controller.json.tipoEvento.TipoAgendamentoCadDTO;
import br.com.pacto.controller.json.tipoEvento.TipoAgendamentoDTO;
import br.com.pacto.enumerador.agenda.TipoAgendamentoEnum;
import br.com.pacto.enumerador.agenda.TipoDuracaoEvento;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.util.UtilContext;
import br.com.pacto.util.ViewUtils;
import br.com.pacto.util.enumeradores.PaletaCoresEnum;
import br.com.pacto.util.impl.UtilReflection;

import java.io.Serializable;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.persistence.UniqueConstraint;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(uniqueConstraints =
        @UniqueConstraint(columnNames = {"nome"}))
public class TipoEvento implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private String nome;
    private Boolean ativo = Boolean.TRUE;
    @Enumerated(EnumType.ORDINAL)
    private TipoAgendamentoEnum comportamento;
    @Enumerated(EnumType.ORDINAL)
    private PaletaCoresEnum cor;
    @Enumerated(EnumType.ORDINAL)
    private TipoDuracaoEvento duracao;
    @Transient
    private Boolean escolhido = Boolean.TRUE;
    private Integer nrAgendamentos = null;
    private Integer dias = null;
    private Boolean apenasAlunosCarteira = true;
    private Integer duracaoMinutosMin = 0;
    private Integer duracaoMinutosMax = 0;
    @Transient
    private String duracaoMinutosMinStr = null;
    @Transient
    private String duracaoMinutosMaxStr = null;
    private Integer intervaloMinimoFalta = null;
    private Boolean permitirApp = Boolean.FALSE;

    public TipoEvento() {
    }

    public TipoEvento(Integer codigo) {
        this.codigo = codigo;
    }

    public TipoEvento(final String nome) {
        this.nome = nome;
    }



    public TipoEvento(TipoAgendamentoCadDTO tipoAgendamento){
        String duracaoMax = "0000";
        String duracaoMin = "0000";
        if(tipoAgendamento.getDuracao_intervalo_superior() != null){
            duracaoMax = (tipoAgendamento.getDuracao_intervalo_superior()).replaceAll(":","");
        }
        if(tipoAgendamento.getDuracao_intervalo_inferior() != null){
            duracaoMin = (tipoAgendamento.getDuracao_intervalo_inferior()).replaceAll(":","");
        }
        if(tipoAgendamento.getDuracao_fixa() != null){
            duracaoMax = (tipoAgendamento.getDuracao_fixa()).replaceAll(":","");
            duracaoMin = (tipoAgendamento.getDuracao_fixa()).replaceAll(":","");
        }
        this.codigo = tipoAgendamento.getId();
        this.nome = tipoAgendamento.getNome().trim();
        this.ativo = tipoAgendamento.getAtivo();
        this.comportamento = tipoAgendamento.getComportamento();
        this.cor = getFromHex(tipoAgendamento.getCor());
        this.duracao = tipoAgendamento.getTipo_duracao();
        this.nrAgendamentos = tipoAgendamento.getNumero_agendamentos();
        this.dias = tipoAgendamento.getDias();
        this.apenasAlunosCarteira = tipoAgendamento.getSomente_carteira_professor();
        this.intervaloMinimoFalta = tipoAgendamento.getIntervalo_minimo_caso_falta();
        Integer horasMax = Integer.parseInt(duracaoMax.substring(0,2));
        Integer minMax = Integer.parseInt(duracaoMax.substring(2,4));
        this.duracaoMinutosMax = (horasMax * 60) + minMax;
        Integer horasMin = Integer.parseInt(duracaoMin.substring(0,2));
        Integer minMin = Integer.parseInt(duracaoMin.substring(2,4));
        this.duracaoMinutosMin = (horasMin * 60) + minMin;
        this.permitirApp = tipoAgendamento.getPermitir_app();
    }


    public TipoEvento(TipoAgendamentoEnum comportamento, PaletaCoresEnum cor,
            final String nome,
            Integer dias, Integer nrAgendamentos, boolean apenasAlunosCarteira,
            TipoDuracaoEvento duracao, Integer duracaoMinutosMax, Integer duracaoMinutosMin,
            Integer intervaloMinimoFalta) {
        this.comportamento = comportamento;
        this.cor = cor;
        this.nome = nome;
        this.dias = dias;
        this.nrAgendamentos = nrAgendamentos;
        this.apenasAlunosCarteira = apenasAlunosCarteira;
        this.duracao = duracao;
        this.duracaoMinutosMax = duracaoMinutosMax;
        this.duracaoMinutosMin = duracaoMinutosMin;
        this.intervaloMinimoFalta = intervaloMinimoFalta;
    }

    public TipoEvento(TipoAgendamentoDTO tipoAgendamento){
        String duracaoMax = "0000";
        String duracaoMin = "0000";
        if(tipoAgendamento.getDuracao_intervalo_superior() != null){
             duracaoMax = (tipoAgendamento.getDuracao_intervalo_superior()).replaceAll(":","");
        }
        if(tipoAgendamento.getDuracao_intervalo_inferior() != null){
             duracaoMin = (tipoAgendamento.getDuracao_intervalo_inferior()).replaceAll(":","");
        }
        this.codigo = tipoAgendamento.getId();
        this.nome = tipoAgendamento.getNome();
        this.ativo = tipoAgendamento.getAtivo();
        this.comportamento = tipoAgendamento.getComportamentoEnum();
        this.cor = tipoAgendamento.getCorEnum();
        this.duracao = tipoAgendamento.getTipo_duracao();
        this.nrAgendamentos = tipoAgendamento.getNumero_agendamentos();
        this.dias = tipoAgendamento.getDias();
        this.apenasAlunosCarteira = tipoAgendamento.getSomente_carteira_professor();
        this.intervaloMinimoFalta = tipoAgendamento.getIntervalo_minimo_caso_falta();
        Integer horasMax = Integer.parseInt(duracaoMax.substring(0,2));
        Integer minMax = Integer.parseInt(duracaoMax.substring(2,4));
        this.duracaoMinutosMax = (horasMax * 60) + minMax;
        Integer horasMin = Integer.parseInt(duracaoMin.substring(0,2));
        Integer minMin = Integer.parseInt(duracaoMin.substring(2,4));
        this.duracaoMinutosMin = (horasMin * 60) + minMin;
    }

    public String getDuracaoMinutosMinStr() {
        if (duracaoMinutosMinStr == null) {
            duracaoMinutosMinStr = Uteis.converterSegundosEmMinutos(duracaoMinutosMin);
        }
        return duracaoMinutosMinStr;
    }

    public void setarSegundos() {
        if (duracaoMinutosMinStr != null) {
            duracaoMinutosMin = Uteis.converterMinutosEmSegundos(duracaoMinutosMinStr);
        }
        if (duracaoMinutosMaxStr != null) {
            duracaoMinutosMax = Uteis.converterMinutosEmSegundos(duracaoMinutosMaxStr);
        }
    }

    public void setDuracaoMinutosMinStr(String duracaoMinutosMinStr) {
        this.duracaoMinutosMinStr = duracaoMinutosMinStr;
    }

    public String getDuracaoMinutosMaxStr() {
        if (duracaoMinutosMaxStr == null) {
            duracaoMinutosMaxStr = Uteis.converterSegundosEmMinutos(duracaoMinutosMax);
        }
        return duracaoMinutosMaxStr;
    }

    public void setDuracaoMinutosMaxStr(String duracaoMinutosMaxStr) {
        this.duracaoMinutosMaxStr = duracaoMinutosMaxStr;
    }

    public TipoDuracaoEvento getDuracao() {
        if (duracao == null) {
            duracao = TipoDuracaoEvento.DURACAO_LIVRE;
        }
        return duracao;
    }

    public void setDuracao(TipoDuracaoEvento duracao) {
        this.duracao = duracao;
    }

    public Boolean getEscolhido() {
        return escolhido;
    }

    public void setEscolhido(Boolean escolhido) {
        this.escolhido = escolhido;
    }

    public Integer getCodigoComportamento() {
        return comportamento == null ? 999 : comportamento.getId();
    }

    public void setCodigoComportamento(Integer codigoComportamento) {
        comportamento = codigoComportamento == null ? null : TipoAgendamentoEnum.getFromId(codigoComportamento);
    }

    public Integer getCodigoDuracao() {
        return duracao == null ? TipoDuracaoEvento.DURACAO_LIVRE.ordinal() : duracao.ordinal();
    }

    public void setCodigoDuracao(Integer codigoDuracao) {
        duracao = codigoDuracao == null ? TipoDuracaoEvento.DURACAO_LIVRE : TipoDuracaoEvento.getFromId(codigoDuracao);
    }

    public Boolean getAtivo() {
        return ativo;
    }

    public void setAtivo(Boolean ativo) {
        this.ativo = ativo;
    }

    public PaletaCoresEnum getCor() {
        return cor;
    }

    public void setCor(PaletaCoresEnum cor) {
        this.cor = cor;
    }

    public TipoAgendamentoEnum getComportamento() {
        return comportamento;
    }

    public void setComportamento(TipoAgendamentoEnum comportamento) {
        this.comportamento = comportamento;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getNomeCss() {
        return Uteis.retirarAcentuacaoRegex(nome).replaceAll(" ", "");
    }

    public String getDescricaoCor() {
        try {
            return cor.getDescricao();
        } catch (Exception e) {
            return "";
        }
    }

    public String getDescricaoComportamento() {
        try {
            return comportamento.getDescricao();
        } catch (Exception e) {
            return "";
        }
    }

    public Integer getNrAgendamentos() {
        return nrAgendamentos;
    }

    public void setNrAgendamentos(Integer nrAgendamentos) {
        this.nrAgendamentos = nrAgendamentos;
    }

    public Integer getDias() {
        return dias;
    }

    public void setDias(Integer dias) {
        this.dias = dias;
    }

    public Boolean getApenasAlunosCarteira() {
        return apenasAlunosCarteira;
    }

    public void setApenasAlunosCarteira(Boolean apenasAlunosCarteira) {
        this.apenasAlunosCarteira = apenasAlunosCarteira;
    }

    public Integer getDuracaoMinutosMin() {
        return duracaoMinutosMin;
    }

    public void setDuracaoMinutosMin(Integer duracaoMinutosMin) {
        this.duracaoMinutosMin = duracaoMinutosMin;
    }

    public Integer getDuracaoMinutosMax() {
        return duracaoMinutosMax;
    }

    public void setDuracaoMinutosMax(Integer duracaoMinutosMax) {
        this.duracaoMinutosMax = duracaoMinutosMax;
    }

    public boolean getDuracaoPreDefinida() {
        return duracao != null && duracao.equals(TipoDuracaoEvento.DURACAO_PREDEFINIDA);
    }

    public boolean getDuracaoIntervalo() {
        return duracao != null && duracao.equals(TipoDuracaoEvento.INTERVALO_DE_TEMPO);
    }

    public Integer getIntervaloMinimoFalta() {
        return intervaloMinimoFalta;
    }

    public void setIntervaloMinimoFalta(Integer intervaloMinimoFalta) {
        this.intervaloMinimoFalta = intervaloMinimoFalta;
    }

    public String getDescricaoAtivo() {
        try {
            ViewUtils view = ((ViewUtils) UtilContext.getBean(ViewUtils.class));
            return ativo ? view.getLabel("cadastros.sim") : view.getLabel("cadastros.nao");
        } catch (Exception e) {
            return "";
        }
    }

    public Boolean getPermitirApp() {
        return permitirApp;
    }

    public void setPermitirApp(Boolean permitirApp) {
        this.permitirApp = permitirApp;
    }

    public String getDescricaoParaLog(TipoEvento v2) {
        try {
            StringBuilder log = new StringBuilder();
            log.append(UtilReflection.difference(this, v2));
            return log.toString();
        } catch (Exception ex) {
            ex.printStackTrace();
            return "ERRO gerar log";
        }
    }

}
