/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.agenda;

/**
 *
 * <AUTHOR>
 */
public enum TipoLembreteEnum {

    QUINZE_MINUTOS(0, 15),
    DUAS_HORAS(1, 120),
    UM_DIA(2, 1440),
    SETE_DIAS(3, 10080),
    AGENDAMENTO_NOVO(4, 15),
    AGENDAMENTO_ALTERADO(5, 15);
    
    private Integer id;
    private Integer nMinutos;

    private TipoLembreteEnum(Integer id, Integer nMinutos) {
        this.id = id;
        this.nMinutos = nMinutos;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getnMinutos() {
        return nMinutos;
    }

    public void setnMinutos(Integer nMinutos) {
        this.nMinutos = nMinutos;
    }
}
