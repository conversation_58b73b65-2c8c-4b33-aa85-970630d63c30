package br.com.pacto.bean.agenda;

import br.com.pacto.bean.colaborador.ColaboradorSimplesTO;
import br.com.pacto.bean.professor.ProfessorSintetico;
import br.com.pacto.bean.tipoEvento.TipoEvento;
import br.com.pacto.controller.json.agendamento.AgendaDiaSemana;
import br.com.pacto.controller.json.agendamento.DisponibilidadeConfigDTO;
import br.com.pacto.controller.json.agendamento.DisponibilidadeConfigGeradoraDTO;
import br.com.pacto.controller.json.tipoEvento.TipoAgendamentoDTO;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.enumeradores.DiasSemana;

import javax.persistence.*;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Entity
public class ConfigDisponibilidade implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    @ManyToOne(fetch = FetchType.EAGER)
    private ProfessorSintetico professor = new ProfessorSintetico();
    @ManyToOne(fetch = FetchType.EAGER)
    private TipoEvento tipoEvento = new TipoEvento();
    private Long dia;
    private Long dataLimite;
    private Integer inicio;//em minutos
    private Integer fim;//em minutos
    private String diasSemana;

    public ConfigDisponibilidade() {
    }

    public ConfigDisponibilidade(Integer codigoProfessor,
                                 Integer codigoTipoEvento,
                                 DisponibilidadeConfigGeradoraDTO geradoraDTO) {
        this.codigo = UteisValidacao.emptyNumber(geradoraDTO.getId()) ? null : geradoraDTO.getId();
        this.professor = new ProfessorSintetico();
        this.professor.setCodigo(codigoProfessor);
        this.tipoEvento = new TipoEvento();
        this.tipoEvento.setCodigo(codigoTipoEvento);
        this.dia = geradoraDTO.getDia();
        this.dataLimite = geradoraDTO.getRepetirDataLimite();
        this.inicio = geradoraDTO.getHorarioInicial();
        this.fim = geradoraDTO.getHorarioFinal();
        if(!UteisValidacao.emptyList(geradoraDTO.getRepetirDias())){
            this.diasSemana = "";
            for(AgendaDiaSemana a : geradoraDTO.getRepetirDias()){
                diasSemana += (a.name() + "|");
            }
        }

    }

    public String getDiaHoraFim(){
        String result = "00:00";
        if (!UteisValidacao.emptyNumber(this.fim)) {
            result = calcularDiaHoraFim();
        } else if (!UteisValidacao.emptyNumber(this.dataLimite)){
            setFimHoraFormatada(Calendario.getHora(new Date(this.dataLimite), "HH:MM"));
            if (!UteisValidacao.emptyNumber(this.fim)) {
                result = calcularDiaHoraFim();
            }
        }
        return result;
    }

    private String calcularDiaHoraFim() {
        Integer hora = this.fim / 60;
        Integer minuto = this.fim - (hora * 60);
        return (hora < 10 ? "0" : "") + hora.toString() + ":" + (minuto < 10 ? "0" : "")  + minuto.toString();
    }

    public String getDiaHoraInicio(){
        String result = "00:00";
        if (!UteisValidacao.emptyNumber(this.inicio)) {
            result = calcularDiaHoraInicio();
        } else if (!UteisValidacao.emptyNumber(this.dia)){
            setInicioHoraFormatada(Calendario.getHora(new Date(this.dia), "HH:MM"));
            if (!UteisValidacao.emptyNumber(this.inicio)) {
                result = calcularDiaHoraInicio();
            }
        }
        return result;
    }

    private String calcularDiaHoraInicio() {
        Integer hora = this.inicio / 60;
        Integer minuto = this.inicio - (hora * 60);
        return (hora < 10 ? "0" : "") + hora.toString() + ":" + (minuto < 10 ? "0" : "")  + minuto.toString();
    }

    public DisponibilidadeConfigDTO toDTO(Boolean treinoIndependente){
        DisponibilidadeConfigDTO config = new DisponibilidadeConfigDTO();
        config.setId(this.codigo);
        config.setProfessor(this.getProfessor() != null ? new ColaboradorSimplesTO(this.getProfessor(), treinoIndependente) : null);
        config.setTipoAgendamento(new TipoAgendamentoDTO(this.getTipoEvento()));
        config.setDia(this.dia);
        config.setHorarioInicial(this.getDiaHoraInicio());
        config.setHorarioFinal(this.getDiaHoraFim());
        config.setRepetirDias(this.getListaDias());
        config.setRepetirDataLimite(this.dataLimite);
        return config;
    }

    public List<AgendaDiaSemana> getListaDias(){
        if(UteisValidacao.emptyString(this.diasSemana)){
            return new ArrayList<AgendaDiaSemana>();
        }

        List<AgendaDiaSemana> diaslist = new ArrayList<AgendaDiaSemana>();
        if(!UteisValidacao.emptyString(this.diasSemana)){
            String[] dias = this.diasSemana.split("\\|");
            for(String d : dias){
                if(!UteisValidacao.emptyString(d)){
                    diaslist.add(AgendaDiaSemana.valueOf(d));
                }
            }
        }
        return diaslist;
    }

    public void setInicioHoraFormatada(String horaFormatada) {
        String[] hora = horaFormatada.split(":");
        Integer inicio = (Integer.parseInt(hora[0]) * 60) + Integer.parseInt(hora[1]);

        setInicio(inicio);
    }

    public void setFimHoraFormatada(String horaFormatada) {
        String[] hora = horaFormatada.split(":");
        Integer fim = (Integer.parseInt(hora[0]) * 60) + Integer.parseInt(hora[1]);

        setFim(fim);
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public ProfessorSintetico getProfessor() {
        return professor;
    }

    public void setProfessor(ProfessorSintetico professor) {
        this.professor = professor;
    }

    public TipoEvento getTipoEvento() {
        return tipoEvento;
    }

    public void setTipoEvento(TipoEvento tipoEvento) {
        this.tipoEvento = tipoEvento;
    }

    public Long getDia() {
        return dia;
    }

    public void setDia(Long dia) {
        this.dia = dia;
    }

    public Long getDataLimite() {
        return dataLimite;
    }

    public void setDataLimite(Long dataLimite) {
        this.dataLimite = dataLimite;
    }

    public String getDiasSemana() {
        return diasSemana;
    }

    public void setDiasSemana(String diasSemana) {
        this.diasSemana = diasSemana;
    }

    public Integer getInicio() {
        return inicio;
    }

    public void setInicio(Integer inicio) {
        this.inicio = inicio;
    }

    public Integer getFim() {
        return fim;
    }

    public void setFim(Integer fim) {
        this.fim = fim;
    }
}
