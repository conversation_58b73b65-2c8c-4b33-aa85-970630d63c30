/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.agenda;

import br.com.pacto.bean.Agendamento;
import br.com.pacto.bean.notificacao.TipoNotificacaoEnum;
import br.com.pacto.bean.programa.ProgramaTreino;
import br.com.pacto.objeto.Calendario;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
@Entity
public class LembreteAgendamento implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    @Enumerated(EnumType.ORDINAL)
    private TipoLembreteEnum tipo;
    @Enumerated(EnumType.ORDINAL)
    private TipoNotificacaoEnum tipoNotificacao;
    @ManyToOne(cascade = CascadeType.REMOVE)
    private Agendamento agendamento;
    @ManyToOne(cascade = CascadeType.REMOVE)
    private ProgramaTreino programa;
    @Temporal(TemporalType.TIMESTAMP)
    private Date dataCriacao = Calendario.hoje();

    public LembreteAgendamento() {
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public TipoLembreteEnum getTipo() {
        return tipo;
    }

    public void setTipo(TipoLembreteEnum tipo) {
        this.tipo = tipo;
    }

    public Agendamento getAgendamento() {
        return agendamento;
    }

    public void setAgendamento(Agendamento agendamento) {
        this.agendamento = agendamento;
    }

    public TipoNotificacaoEnum getTipoNotificacao() {
        return tipoNotificacao;
    }

    public void setTipoNotificacao(TipoNotificacaoEnum tipoNotificacao) {
        this.tipoNotificacao = tipoNotificacao;
    }

    public ProgramaTreino getPrograma() {
        return programa;
    }

    public void setPrograma(ProgramaTreino programa) {
        this.programa = programa;
    }

    public Date getDataCriacao() {
        return dataCriacao;
    }

    public void setDataCriacao(Date dataCriacao) {
        this.dataCriacao = dataCriacao;
    }
}
