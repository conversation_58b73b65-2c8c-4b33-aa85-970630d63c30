/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.gympass;

import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.util.UteisValidacao;
import org.json.JSONObject;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;
import java.util.Locale;

public class LogGymPassTO implements Serializable {

    private Long dataRegistro;
    private String booking;
    private String bookingNumber;
    private String evento;
    private Integer codigo;
    private String aluno;
    private String matricula;
    private String email;
    private String classId;
    private boolean sucesso;

    public LogGymPassTO(LogGymPass log) {
        this.dataRegistro = log.getDataRegistro().getTime();
        this.codigo = log.getCodigo();
        this.classId = log.getIdTurma();
        this.evento = log.getLog();
        this.sucesso = log.getLog().toLowerCase().startsWith("turma");
    }

    public LogGymPassTO(BookingGymPass log, String aluno, String matricula) {
        try {
            this.codigo = log.getCodigo();
            this.booking = log.getBooking();
            this.sucesso = log.isSucesso();
            this.aluno = aluno;
            this.matricula = matricula;
            this.bookingNumber = log.getBookingNumber();
            this.evento = log.getOperacao();
            if(!UteisValidacao.emptyString(log.getBooking())){
                JSONObject jsonObject = new JSONObject(log.getBooking()).getJSONObject("event_data");
                Date timestamp = new Date(jsonObject.optLong("timestamp"));
                if(Calendario.menor(timestamp, Uteis.getDate("01/01/2020"))){
                    this.dataRegistro = jsonObject.optLong("timestamp") * 1000l;
                }else{
                    this.dataRegistro = jsonObject.optLong("timestamp");
                }
                if(jsonObject.has("user") && jsonObject.getJSONObject("user").has("email")){
                    this.email = jsonObject.getJSONObject("user").optString("email");
                }
                if(jsonObject.has("slot") && jsonObject.getJSONObject("slot").has("class_id")){
                    this.classId = String.valueOf(jsonObject.getJSONObject("slot").optInt("class_id"));
                }
            }
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    public Long getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(Long dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public String getBooking() {
        return booking;
    }

    public void setBooking(String booking) {
        this.booking = booking;
    }

    public String getBookingNumber() {
        return bookingNumber;
    }

    public void setBookingNumber(String bookingNumber) {
        this.bookingNumber = bookingNumber;
    }

    public String getEvento() {
        return evento;
    }

    public void setEvento(String evento) {
        this.evento = evento;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getAluno() {
        return aluno;
    }

    public void setAluno(String aluno) {
        this.aluno = aluno;
    }

    public boolean isSucesso() {
        return sucesso;
    }

    public void setSucesso(boolean sucesso) {
        this.sucesso = sucesso;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getClassId() {
        return classId;
    }

    public void setClassId(String classId) {
        this.classId = classId;
    }

    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }
}
