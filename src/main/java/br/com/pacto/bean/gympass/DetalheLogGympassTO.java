package br.com.pacto.bean.gympass;

import org.json.JSONObject;

import java.util.List;

public class DetalheLogGympassTO {

    private LogGymPassTO logGymPass;
    private List<BookingGymPass> eventos;
    private List<LogGymPass> logs;
    private DadosAulaDetalheLogBookingTO dados;

    public LogGymPassTO getLogGymPass() {
        return logGymPass;
    }

    public void setLogGymPass(LogGymPassTO logGymPass) {
        this.logGymPass = logGymPass;
    }

    public List<BookingGymPass> getEventos() {
        return eventos;
    }

    public void setEventos(List<BookingGymPass> eventos) {
        this.eventos = eventos;
    }

    public List<LogGymPass> getLogs() {
        return logs;
    }

    public void setLogs(List<LogGymPass> logs) {
        this.logs = logs;
    }

    public DadosAulaDetalheLogBookingTO getDados() {
        return dados;
    }

    public void setDados(DadosAulaDetalheLogBookingTO dados) {
        this.dados = dados;
    }
}
