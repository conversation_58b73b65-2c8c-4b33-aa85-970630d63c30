package br.com.pacto.bean.gympass;

import org.json.JSONObject;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties
public class AulaDetalheLogBookingTO {

    private Long dia;
    private String bookingid;
    private String horainicial;
    private String identificadorturma;

    public AulaDetalheLogBookingTO(JSONObject json) {
        this.dia = json.optLong("dia");
        this.bookingid = json.optString("bookingid");
        this.horainicial = json.optString("horainicial");
        this.identificadorturma = json.optString("identificadorturma");
    }

    public Long getDia() {
        return dia;
    }

    public void setDia(Long dia) {
        this.dia = dia;
    }

    public String getBookingid() {
        return bookingid;
    }

    public void setBookingid(String bookingid) {
        this.bookingid = bookingid;
    }

    public String getHorainicial() {
        return horainicial;
    }

    public void setHorainicial(String horainicial) {
        this.horainicial = horainicial;
    }

    public String getIdentificadorturma() {
        return identificadorturma;
    }

    public void setIdentificadorturma(String identificadorturma) {
        this.identificadorturma = identificadorturma;
    }
}
