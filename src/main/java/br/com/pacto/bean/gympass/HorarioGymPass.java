/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.gympass;


import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 06/04/2020
 */
@Entity
@Table()
public class HorarioGymPass implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private Integer idTurma;
    private Integer codigoHorario;
    @Temporal(TemporalType.DATE)
    private Date dia;
    private String idReferencia; //código do horario e dia
    private Integer idClassGymPass;
    private Integer idSlotGymPass;
    @Column(columnDefinition = "boolean default true")
    private boolean ativo = true;
    @Temporal(TemporalType.TIMESTAMP)
    private Date inicioHorario;

    public HorarioGymPass() {
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getIdReferencia() {
        return idReferencia;
    }

    public void setIdReferencia(String idReferencia) {
        this.idReferencia = idReferencia;
    }

    public Integer getIdSlotGymPass() {
        return idSlotGymPass;
    }

    public void setIdSlotGymPass(Integer idSlotGymPass) {
        this.idSlotGymPass = idSlotGymPass;
    }

    public Integer getIdTurma() {
        return idTurma;
    }

    public void setIdTurma(Integer idTurma) {
        this.idTurma = idTurma;
    }

    public Date getDia() {
        return dia;
    }

    public void setDia(Date dia) {
        this.dia = dia;
    }

    public Integer getCodigoHorario() {
        return codigoHorario;
    }

    public void setCodigoHorario(Integer codigoHorario) {
        this.codigoHorario = codigoHorario;
    }

    public Integer getIdClassGymPass() {
        return idClassGymPass;
    }

    public void setIdClassGymPass(Integer idClassGymPass) {
        this.idClassGymPass = idClassGymPass;
    }

    public boolean isAtivo() {
        return ativo;
    }

    public void setAtivo(boolean ativo) {
        this.ativo = ativo;
    }

    public Date getInicioHorario() {
        return inicioHorario;
    }

    public void setInicioHorario(Date inicioHorario) {
        this.inicioHorario = inicioHorario;
    }
}
