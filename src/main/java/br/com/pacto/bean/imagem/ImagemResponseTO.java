package br.com.pacto.bean.imagem;

import br.com.pacto.bean.animacao.Animacao;
import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * Created by ulisses on 29/08/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ImagemResponseTO {

    private String id;
    private String nome;
    private String uri;

    public ImagemResponseTO(Animacao animacao) {
        this.id = animacao.getUrl();
        this.nome = animacao.getTitulo();
        this.uri = "http://app.pactosolucoes.com.br/midias/DEFAULT/Large/Square/" + animacao.getUrl();
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getUri() {
        return uri;
    }

    public void setUri(String uri) {
        this.uri = uri;
    }
}
