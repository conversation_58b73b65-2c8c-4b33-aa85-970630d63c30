package br.com.pacto.bean.crossfit;

import br.com.pacto.bean.wod.NivelCrossfitEnum;

import javax.persistence.*;

@Entity
@Table
public class PontuacaoGameCrossfit {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    @Enumerated(EnumType.ORDINAL)
    private NivelCrossfitEnum nivel;
    @ManyToOne
    private EventoCrossfit game;
    private Integer posicao;
    private Integer pontuacao;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public NivelCrossfitEnum getNivel() {
        return nivel;
    }

    public void setNivel(NivelCrossfitEnum nivel) {
        this.nivel = nivel;
    }

    public EventoCrossfit getGame() {
        return game;
    }

    public void setGame(EventoCrossfit game) {
        this.game = game;
    }

    public Integer getPosicao() {
        return posicao;
    }

    public void setPosicao(Integer posicao) {
        this.posicao = posicao;
    }

    public Integer getPontuacao() {
        return pontuacao;
    }

    public void setPontuacao(Integer pontuacao) {
        this.pontuacao = pontuacao;
    }
}
