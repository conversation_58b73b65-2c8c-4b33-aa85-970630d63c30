package br.com.pacto.bean.crossfit;

import br.com.pacto.bean.nivelwod.NivelWod;
import br.com.pacto.bean.wod.NivelCrossfitEnum;
import br.com.pacto.util.UteisValidacao;

import javax.persistence.*;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Entity
@Table
public class EquipeEvento {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo = 0;
    private String nome;
    private String sexo;
    private Boolean taxaPaga = false;
    private Integer parcela;
    @ManyToOne
    private NivelWod nivelWod;
    @ManyToOne
    private EventoCrossfit evento;
    @Transient
    private Boolean gerarParcela = false;
    @Transient
    private Integer maxParticipantes;
    @Transient
    private ParticipanteEquipeEvento capitao;
    @Transient
    private List<ParticipanteEquipeEvento> participantes;
    @Transient
    private Map<Integer, Integer> posicoesWods = new HashMap<Integer, Integer>();
    @Transient
    private Map<Integer, Integer> pontosWods = new HashMap<Integer, Integer>();
    @Transient
    private Integer totalPontos = 0;
    @Transient
    private Integer posicaoFinal = 0;

    public String getSexoApresentar() {
        return sexo == null ? "" : sexo.equals("F") ? "(Feminina)" : "(Masculina)";
    }

    public String getSexo() {
        if(UteisValidacao.emptyString(sexo)){
            sexo = "M";
        }
        return sexo;
    }

    public void setSexo(String sexo) {
        this.sexo = sexo;
    }

    public boolean getCheia(){
        return participantes != null && maxParticipantes != null
                && maxParticipantes <= participantes.size();
    }

    public EventoCrossfit getEvento() {
        return evento;
    }

    public void setEvento(EventoCrossfit evento) {
        this.evento = evento;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Boolean getTaxaPaga() {
        return taxaPaga;
    }

    public void setTaxaPaga(Boolean taxaPaga) {
        this.taxaPaga = taxaPaga;
    }

    public NivelWod getNivelWod() {
        return nivelWod;
    }

    public void setNivelWod(NivelWod nivelWod) {
        this.nivelWod = nivelWod;
    }

    public Integer getParcela() {
        return parcela;
    }

    public void setParcela(Integer parcela) {
        this.parcela = parcela;
    }

    public ParticipanteEquipeEvento getCapitao() {
        return capitao;
    }

    public void setCapitao(ParticipanteEquipeEvento capitao) {
        this.capitao = capitao;
    }

    public List<ParticipanteEquipeEvento> getParticipantes() {
        return participantes;
    }

    public void setParticipantes(List<ParticipanteEquipeEvento> participantes) {
        this.participantes = participantes;
    }

    public Integer getMaxParticipantes() {
        return maxParticipantes;
    }

    public void setMaxParticipantes(Integer maxParticipantes) {
        this.maxParticipantes = maxParticipantes;
    }

    public Boolean getGerarParcela() {
        return gerarParcela;
    }

    public void setGerarParcela(Boolean gerarParcela) {
        this.gerarParcela = gerarParcela;
    }

    public Map<Integer, Integer> getPosicoesWods() {
        return posicoesWods;
    }

    public void setPosicoesWods(Map<Integer, Integer> posicoesWods) {
        this.posicoesWods = posicoesWods;
    }

    public Map<Integer, Integer> getPontosWods() {
        return pontosWods;
    }

    public void setPontosWods(Map<Integer, Integer> pontosWods) {
        this.pontosWods = pontosWods;
    }

    public Integer getTotalPontos() {
        return totalPontos;
    }

    public void setTotalPontos(Integer totalPontos) {
        this.totalPontos = totalPontos;
    }

    public Integer getPosicaoFinal() {
        return posicaoFinal;
    }

    public void setPosicaoFinal(Integer posicaoFinal) {
        this.posicaoFinal = posicaoFinal;
    }
}
