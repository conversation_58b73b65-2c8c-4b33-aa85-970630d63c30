package br.com.pacto.bean.crossfit;

import br.com.pacto.objeto.Uteis;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.hibernate.annotations.Type;

import javax.persistence.*;
import java.util.Date;

@Entity
@Table
public class EventoCrossfit {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private String nome;
    @Lob
    @Type(type="org.hibernate.type.StringClobType")
    @Column(columnDefinition = "text", length = 9999)
    private String descricao;
    private Boolean rankingSeparadoSexo = true;
    private String urlFoto;
    @JsonFormat(shape=JsonFormat.Shape.STRING, pattern="dd/MM/yyyy")
    @Temporal(javax.persistence.TemporalType.DATE)
    private Date inicio;
    @Temporal(javax.persistence.TemporalType.DATE)
    private Date termino;
    @Temporal(javax.persistence.TemporalType.DATE)
    private Date limiteInscricaoApp;
    @Temporal(TemporalType.TIMESTAMP)
    private Date dataLancamento;
    private Double valorPorParticipante;
    private Integer nrPartScaled = 1;
    private Integer nrPartIniciante = 1;
    private Integer nrPartAvancado = 1;
    private Integer nrPartAmador = 1;

    public Integer getNrPartScaled() {
        return nrPartScaled;
    }

    public void setNrPartScaled(Integer nrPartScaled) {
        this.nrPartScaled = nrPartScaled;
    }

    public Integer getNrPartIniciante() {
        return nrPartIniciante;
    }

    public void setNrPartIniciante(Integer nrPartIniciante) {
        this.nrPartIniciante = nrPartIniciante;
    }

    public Integer getNrPartAvancado() {
        return nrPartAvancado;
    }

    public void setNrPartAvancado(Integer nrPartAvancado) {
        this.nrPartAvancado = nrPartAvancado;
    }

    public Integer getNrPartAmador() {
        return nrPartAmador;
    }

    public void setNrPartAmador(Integer nrPartAmador) {
        this.nrPartAmador = nrPartAmador;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Date getInicio() {
        return inicio;
    }

    public void setInicio(Date inicio) {
        this.inicio = inicio;
    }

    public Date getTermino() {
        return termino;
    }

    public void setTermino(Date termino) {
        this.termino = termino;
    }

    public Date getLimiteInscricaoApp() {
        return limiteInscricaoApp;
    }

    public void setLimiteInscricaoApp(Date limiteInscricaoApp) {
        this.limiteInscricaoApp = limiteInscricaoApp;
    }

    public Date getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public Double getValorPorParticipante() {
        return valorPorParticipante;
    }

    public void setValorPorParticipante(Double valorPorParticipante) {
        this.valorPorParticipante = valorPorParticipante;
    }

    public String getInicioApresentar(){
        return Uteis.getData(this.getInicio());
    }

    public String getTerminoApresentar(){
        return Uteis.getData(this.getTermino());
    }

    public long getInicioLong(){
        return inicio == null ? 0L : inicio.getTime();
    }

    public long getTerminoLong(){
        return termino == null ? 0L : termino.getTime();
    }

    public String getUrlFoto() {
        return urlFoto;
    }

    public void setUrlFoto(String urlFoto) {
        this.urlFoto = urlFoto;
    }

    public Boolean getRankingSeparadoSexo() {
        if(rankingSeparadoSexo == null){
            rankingSeparadoSexo = Boolean.TRUE;
        }
        return rankingSeparadoSexo;
    }

    public void setRankingSeparadoSexo(Boolean rankingSeparadoSexo) {
        this.rankingSeparadoSexo = rankingSeparadoSexo;
    }
}
