package br.com.pacto.bean.crossfit;

import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.cliente.Prospect;
import br.com.pacto.bean.usuario.Usuario;

import javax.persistence.*;

@Entity
@Table
public class ParticipanteEquipeEvento {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private Boolean capitao;
    @ManyToOne
    private EquipeEvento equipe;
    @ManyToOne
    private Usuario usuario;
    @OneToOne
    private Prospect prospect;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public EquipeEvento getEquipe() {
        return equipe;
    }

    public void setEquipe(EquipeEvento equipe) {
        this.equipe = equipe;
    }

    public Usuario getUsuario() {
        return usuario;
    }

    public boolean getFeminino(){
        try {
            return getUsuario() == null ?
                    getProspect().getSexo().toLowerCase().startsWith("F") :
                    (!getUsuario().getCliente().isSexoMasculino());
        }catch (Exception e){
            //ignore
        }
        return false;
    }

    public void setUsuario(Usuario usuario) {
        this.usuario = usuario;
    }

    public String getNomeMinusculo(){
        try {
            return prospect == null ? usuario.getSuperNome().toLowerCase() : prospect.getNome().toLowerCase();
        }catch (Exception e){
            return "";
        }
    }

    public boolean getCapitao() {
        return capitao == null ? false : capitao;
    }

    public void setCapitao(Boolean capitao) {
        this.capitao = capitao;
    }

    public Prospect getProspect() {
        return prospect;
    }

    public void setProspect(Prospect prospect) {
        this.prospect = prospect;
    }
}
