package br.com.pacto.bean.colaborador;

import br.com.pacto.bean.usuario.TipoUsuarioEnum;

/**
 * Created by ulisses on 27/08/2018.
 */
public enum TipoUsuarioColaboradorEnum {

    PROFESSOR(1, "Professor"),
    COORDENADOR(2, "Coordenador"),
    CONSULTOR(3, "Consultor");

    private Integer codigo;
    private String descricao;

    private TipoUsuarioColaboradorEnum(Integer codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public TipoUsuarioEnum obterTipoUsuario() {
        switch (this) {
            case CONSULTOR: return TipoUsuarioEnum.CONSULTOR;
            case COORDENADOR: return TipoUsuarioEnum.COORDENADOR;
            default: return TipoUsuarioEnum.PROFESSOR;
        }
    }

    public Integer getCodigo() { return codigo; }

    public void setCodigo(Integer codigo) { this.codigo = codigo; }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }


}
