package br.com.pacto.bean.colaborador;

import br.com.pacto.bean.pessoa.Telefone;
import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * Created by ulisses on 27/08/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TelefoneResponseTO {

    private Integer id;
    private String phone;

    public TelefoneResponseTO() {
    }

    public TelefoneResponseTO(Telefone telefone) {
        this.id = telefone.getCodigo();
        this.phone = telefone.getTelefone();
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

}
