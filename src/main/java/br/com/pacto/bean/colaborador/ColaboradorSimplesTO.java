package br.com.pacto.bean.colaborador;

import br.com.pacto.bean.professor.ProfessorSintetico;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Created by paulo on 17/10/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Detalhes do colaborador")
public class ColaboradorSimplesTO {

    @ApiModelProperty(value = "ID do colaborador", example = "1")
    private Integer id;
    @ApiModelProperty(value = "Código do colaborador", example = "1")
    private Integer codigoColaborador;
    @ApiModelProperty(value = "Nome do colaborador", example = "João Silva")
    private String nome;
    @ApiModelProperty(value = "URL da imagem")
    private String imageUri;

    @ApiModelProperty(value = "Cref")
    private String cref;

    public ColaboradorSimplesTO() {
    }

    public ColaboradorSimplesTO(Integer id, Integer codigoColaborador, String nome, String imageUri) {
        this.id = id;
        this.codigoColaborador = codigoColaborador;
        this.nome = nome;
        this.imageUri = imageUri;
    }

    public ColaboradorSimplesTO(ProfessorSintetico ps, Boolean useCodTreino) {
        this.id = useCodTreino ? ps.getCodigo() : ps.getCodigoColaborador();
        this.codigoColaborador = ps.getCodigoColaborador();
        this.nome = ps.getNome();
        this.imageUri = ps.getUriImagem();
        this.cref = ps.getCref();
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getImageUri() {
        return imageUri;
    }

    public void setImageUri(String imageUri) {
        this.imageUri = imageUri;
    }

    public Integer getCodigoColaborador() {
        return codigoColaborador;
    }

    public void setCodigoColaborador(Integer codigoColaborador) {
        this.codigoColaborador = codigoColaborador;
    }

    public String getCref() {
        return cref;
    }

    public void setCref(String cref) {
        this.cref = cref;
    }
}
