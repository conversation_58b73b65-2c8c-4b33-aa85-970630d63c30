package br.com.pacto.bean.colaborador;

import br.com.pacto.bean.pessoa.Email;
import br.com.pacto.bean.pessoa.Telefone;
import br.com.pacto.bean.professor.ProfessorSintetico;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.bean.pessoa.SexoEnum;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by ulisses on 27/08/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ColaboradorResponseTO {

    private Integer id;
    private String nome;
    private SituacaoColaboradorEnum situacao;
    private Date dataNascimento;
    private SexoEnum sexo;
    private List<EmailResponseTO> emails = new ArrayList<>();
    private List<TelefoneResponseTO> fones = new ArrayList<>();
    private Boolean usarApp;
    private String appUserName;
    private String appPassword;
    private TipoUsuarioColaboradorEnum tipoUsuario;
    private PerfilUsuarioResponseTO perfilUsuario;
    private String uriImagem;
    private Integer codigoColaborador;

    public ColaboradorResponseTO() {
    }


    public ColaboradorResponseTO(ProfessorSintetico ps, Usuario usuario, boolean treinoIndependente) {
        this.id = treinoIndependente ? ps.getCodigo() : ps.getCodigoColaborador();
        this.nome = ps.getNome();
        this.situacao = SituacaoColaboradorEnum.fromBoolean(ps.isAtivo());
        this.codigoColaborador = ps.getCodigoColaborador();
        this.uriImagem = ps.getUriImagem();

        this.dataNascimento = ps.getPessoa().getDataNascimento();
        if (ps.getPessoa().getSexo() != null && !ps.getPessoa().getSexo().isEmpty()) {
            try {
                this.sexo = SexoEnum.valueOf(ps.getPessoa().getSexo().substring(0, 1).toUpperCase());
            } catch (IllegalArgumentException e) {
                this.sexo = SexoEnum.N;
            }
        } else {
            this.sexo = SexoEnum.N;
        }
        for (Email email : ps.getPessoa().getEmails()) {
            this.emails.add(new EmailResponseTO(email));
        }
        for (Telefone telefone : ps.getPessoa().getTelefones()) {
            this.fones.add(new TelefoneResponseTO(telefone));
        }
        this.usarApp = usuario != null;
        if (this.usarApp) {
            this.appUserName = usuario.getUserName();
            this.appPassword = usuario.getSenha();
            this.tipoUsuario = usuario.getTipo().obterTipoUsuarioColaborador();
            this.perfilUsuario = new PerfilUsuarioResponseTO(usuario.getPerfil());
        }
    }

    public ColaboradorResponseTO(Integer codigo, Integer codigoColaborador, String nome, boolean treinoIndependente, String urlImagem) {
        this.id = treinoIndependente ? codigo : codigoColaborador;
        this.codigoColaborador = codigoColaborador;
        this.nome = nome;
        this.uriImagem = urlImagem;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() { return nome; }

    public void setNome(String nome) { this.nome = nome; }

    public SituacaoColaboradorEnum getSituacao() {
        return situacao;
    }

    public void setSituacao(SituacaoColaboradorEnum situacao) {
        this.situacao = situacao;
    }

    public Date getDataNascimento() {
        return dataNascimento;
    }

    public void setDataNascimento(Date dataNascimento) {
        this.dataNascimento = dataNascimento;
    }

    public SexoEnum getSexo() {
        return sexo;
    }

    public void setSexo(SexoEnum sexo) {
        this.sexo = sexo;
    }

    public List<EmailResponseTO> getEmails() {
        return emails;
    }

    public void setEmails(List<EmailResponseTO> emails) {
        this.emails = emails;
    }

    public List<TelefoneResponseTO> getFones() {
        return fones;
    }

    public void setFones(List<TelefoneResponseTO> fones) {
        this.fones = fones;
    }

    public Boolean getUsarApp() {
        return usarApp;
    }

    public void setUsarApp(Boolean usarApp) {
        this.usarApp = usarApp;
    }

    public String getAppUserName() {
        return appUserName;
    }

    public void setAppUserName(String appUserName) {
        this.appUserName = appUserName;
    }

    public String getAppPassword() {
        return appPassword;
    }

    public void setAppPassword(String appPassword) {
        this.appPassword = appPassword;
    }

    public TipoUsuarioColaboradorEnum getTipoUsuario() {
        return tipoUsuario;
    }

    public void setTipoUsuario(TipoUsuarioColaboradorEnum tipoUsuario) {
        this.tipoUsuario = tipoUsuario;
    }

    public PerfilUsuarioResponseTO getPerfilUsuario() {
        return perfilUsuario;
    }

    public void setPerfilUsuario(PerfilUsuarioResponseTO perfilUsuario) {
        this.perfilUsuario = perfilUsuario;
    }

    public String getUriImagem() {
        return uriImagem;
    }

    public void setUriImagem(String uriImagem) {
        this.uriImagem = uriImagem;
    }

    public Integer getCodigoColaborador() {
        return codigoColaborador;
    }

    public void setCodigoColaborador(Integer codigoColaborador) {
        this.codigoColaborador = codigoColaborador;
    }
}
