package br.com.pacto.bean.colaborador;

import br.com.pacto.bean.pessoa.Email;
import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * Created by ulisses on 27/08/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EmailResponseTO {

    private Integer id;
    private String email;

    public EmailResponseTO(Integer id, String email) {
        this.id = id;
        this.email = email;
    }

    public EmailResponseTO(Email email) {
        this.id = email.getCodigo();
        this.email = email.getEmail();
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }
}
