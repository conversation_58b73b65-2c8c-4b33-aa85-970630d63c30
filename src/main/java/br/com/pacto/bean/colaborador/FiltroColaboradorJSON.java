package br.com.pacto.bean.colaborador;

import br.com.pacto.controller.json.base.SuperJSON;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.enumeradores.TipoColaboradorZWEnum;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

public class FiltroColaboradorJSON extends SuperJSON {

    private Boolean nome = false;
    private Boolean userName = false;
    private List<SituacaoColaboradorEnum> situacoes = new ArrayList<>();
    private String parametros;
    private TipoColaboradorZWEnum tipoColaborador;

    public FiltroColaboradorJSON(JSONObject filters) throws JSONException {
        if (filters != null) {
            this.parametros = filters.optString("quicksearchValue");
            JSONArray situacoes = filters.optJSONArray("situacoes");
            if (situacoes != null) {
                for (int i = 0; i < situacoes.length(); i++) {
                    getSituacoes().add(SituacaoColaboradorEnum.valueOf(situacoes.get(i).toString()));
                }
            }
            //verifica se o tipo de colaborador informado é valido, se naõ for retorna erro
            this.tipoColaborador = TipoColaboradorZWEnum.getTipo(filters.optString("tipoColaborador"));
            if (this.tipoColaborador == null && !UteisValidacao.emptyString(filters.optString("tipoColaborador"))) {
                throw new JSONException("Tipo de colaborador inválido");
            }
            JSONArray colunasVisiveis = filters.optJSONArray("quicksearchFields");
            if (colunasVisiveis != null) {
                for (int i = 0; i < colunasVisiveis.length(); i++) {
                    if (colunasVisiveis.get(i).equals("nome")) {
                        this.nome = true;
                    }
                    if (colunasVisiveis.get(i).equals("userName")) {
                        this.userName = true;
                    }
                }

            }
        }
    }

    public Boolean getNome() {
        return nome;
    }

    public void setNome(Boolean nome) {
        this.nome = nome;
    }

    public Boolean getUserName() {
        return userName;
    }

    public void setUserName(Boolean userName) {
        this.userName = userName;
    }

    public List<SituacaoColaboradorEnum> getSituacoes() {
        return situacoes;
    }

    public void setSituacoes(List<SituacaoColaboradorEnum> situacoes) {
        this.situacoes = situacoes;
    }

    public String getParametros() {
        return parametros;
    }

    public void setParametros(String parametros) {
        this.parametros = parametros;
    }

    public TipoColaboradorZWEnum getTipoColaborador() {
        return tipoColaborador;
    }

    public void setTipoColaborador(TipoColaboradorZWEnum tipoColaborador) {
        this.tipoColaborador = tipoColaborador;
    }
}
