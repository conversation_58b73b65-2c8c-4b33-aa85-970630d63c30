/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.pacto.bean.badge;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table
public class Badge implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    @Enumerated(EnumType.ORDINAL)
    private TipoBadgeEnum tipo;
    private Integer parametro1;
    private Integer parametro2;
    private Boolean ativo = Boolean.TRUE;
    private String nome = "";
    @Temporal(javax.persistence.TemporalType.DATE)
    private Date dataCriacao;

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }
    
    public Badge(TipoBadgeEnum tipo, Integer parametro,Integer parametro2, String nome, Date dataCriacao){
        this.tipo = tipo;
        this.parametro1 = parametro;
        this.parametro2 = parametro2;
        this.nome = nome;
        this.dataCriacao = dataCriacao;
    }
    public Badge(){
    }

    public Integer getParametro1() {
        return parametro1;
    }

    public void setParametro1(Integer parametro) {
        this.parametro1 = parametro;
    }

    public Integer getParametro2() {
        return parametro2;
    }

    public void setParametro2(Integer parametro2) {
        this.parametro2 = parametro2;
    }
    
    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public TipoBadgeEnum getTipo() {
        return tipo;
    }

    public void setTipo(TipoBadgeEnum tipo) {
        this.tipo = tipo;
    }

    public Boolean getAtivo() {
        return ativo;
    }

    public void setAtivo(Boolean ativo) {
        this.ativo = ativo;
    }

    public Date getDataCriacao() {
        return dataCriacao;
    }

    public void setDataCriacao(Date dataCriacao) {
        this.dataCriacao = dataCriacao;
    }
    
    @Override
    public String toString() {
        return this.hashCode() + " - " + this.nome;
    }
    
    @Override
    public int hashCode() { 
        return this.getCodigo() != null && this.getCodigo() > 0 ? 
                this.getCodigo().hashCode() : super.hashCode();
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null) {
            return false;
        }
        if (!(obj instanceof Badge)) {
            return false;
        }
        final Badge other = (Badge) obj;
        return other.getTipo().equals(this.getTipo());
    }
    
    public String getDescricao(){
        switch (tipo) {
            case CARGA:
                return tipo.getDescricao1()+tipo.getDescricao2();
            case DISTANCIA:
                return tipo.getDescricao1()+parametro1+tipo.getDescricao2();
            case FREQUENCIA:
                return tipo.getDescricao1()+parametro1+tipo.getDescricao2()+parametro2+tipo.getDescricao3();
        }
        return "";
    }
    
}
