/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.sincronizacao;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import org.hibernate.annotations.Index;

/**
 *
 * <AUTHOR>
 */
@Entity
public class HistoricoRevisao implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    @Enumerated(EnumType.ORDINAL)
    @Index(columnNames = {"entidade", "dataRegistro"}, name = "idx_historicorevisao_dataregistro")
    private TipoClassSincronizarEnum entidade;
    @Temporal(TemporalType.TIMESTAMP)
    private Date dataRegistro;
    @Enumerated(EnumType.ORDINAL)
    private TipoRevisaoEnum tipoRevisao;
    @Index(columnNames = {"chaveprimaria", "entidade"}, name = "idx_historicorevisao_chaveprimaria")
    private Integer chavePrimaria;
    private String userName;

    public HistoricoRevisao() {
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public TipoClassSincronizarEnum getEntidade() {
        return entidade;
    }

    public void setEntidade(TipoClassSincronizarEnum entidade) {
        this.entidade = entidade;
    }

    public Date getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(Date dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public TipoRevisaoEnum getTipoRevisao() {
        return tipoRevisao;
    }

    public void setTipoRevisao(TipoRevisaoEnum tipoRevisao) {
        this.tipoRevisao = tipoRevisao;
    }

    public Integer getChavePrimaria() {
        return chavePrimaria;
    }

    public void setChavePrimaria(Integer chavePrimaria) {
        this.chavePrimaria = chavePrimaria;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }
}