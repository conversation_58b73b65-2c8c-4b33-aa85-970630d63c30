package br.com.pacto.bean.impressao;

/**
 * Created by <PERSON> on 09/02/2017.
 */
public enum TipoImpressaoEnum {
    AVALIACAO_FISICA(1,"av");

    private int codigo;
    private String sigla;

    private TipoImpressaoEnum(int codigo, String sigla) {
        this.codigo = codigo;
        this.sigla = sigla;
    }
    public static TipoImpressaoEnum obterPorSigla(String s){
        for(TipoImpressaoEnum tipo : values()){
            if(tipo.getSigla().equals(s)){
                return tipo;
            }
        }
        return null;
    }

    public int getCodigo() {
        return codigo;
    }

    public void setCodigo(int codigo) {
        this.codigo = codigo;
    }

    public String getSigla() {
        return sigla;
    }

    public void setSigla(String sigla) {
        this.sigla = sigla;
    }
}
