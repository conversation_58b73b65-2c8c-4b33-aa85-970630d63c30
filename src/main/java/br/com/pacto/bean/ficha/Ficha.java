/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.ficha;

import br.com.pacto.bean.atividade.AtividadeFicha;
import br.com.pacto.bean.atividade.AtividadeGrupoMuscular;
import br.com.pacto.bean.nivel.Nivel;
import br.com.pacto.bean.pessoa.SexoEnum;
import br.com.pacto.bean.programa.ProgramaTreinoFicha;
import br.com.pacto.bean.programa.TipoExecucaoEnum;
import br.com.pacto.bean.serie.Serie;
import br.com.pacto.util.bean.NumeroTO;
import br.com.pacto.util.impl.Ordenacao;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import javax.persistence.*;

import br.com.pacto.util.impl.UtilReflection;
import com.fasterxml.jackson.annotation.JsonIgnore;

import static java.util.Objects.isNull;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table
public class Ficha implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private String nome;
    @Column(columnDefinition = "text", length = 9999)
    private String mensagemAluno;
    @ManyToOne
    private CategoriaFicha categoria;
    @ManyToOne
    private Nivel nivel;
    private boolean usarComoPredefinida = false;
    private Integer versao;
    @JsonIgnore
    @OneToMany(cascade = {CascadeType.MERGE, CascadeType.REMOVE}, fetch = FetchType.LAZY, mappedBy = "ficha", targetEntity = AtividadeFicha.class)
    private List<AtividadeFicha> atividades = new ArrayList<AtividadeFicha>();
    @JsonIgnore
    @OneToMany(cascade = {CascadeType.MERGE, CascadeType.REMOVE}, orphanRemoval = true, fetch = FetchType.LAZY, mappedBy = "ficha", targetEntity = ProgramaTreinoFicha.class)
    private List<ProgramaTreinoFicha> programas = new ArrayList<ProgramaTreinoFicha>();
    private Boolean ativo = true;
    @Temporal(TemporalType.TIMESTAMP)
    private Date ultimaExecucao;
    @Transient
    private ProgramaTreinoFicha fichaAntesAlteracao;
    @Transient
    private Integer qtdSeriesInserir = 0;
    @Transient
    private Integer qtdRepeticoesSeriesInserir = 0;
    @Transient
    private String descansoRepetir = "";
    @Transient
    private Integer nrAtividades;
    @Transient
    private boolean selecionado = false;
    @Transient
    private NumeroTO repeticaoSpin;
    @Transient
    private NumeroTO qtdSeriesSpin;
    @Enumerated(EnumType.ORDINAL)
    private SexoEnum sexo;

    public Ficha() {
    }

    public Ficha(Ficha ficha, List<AtividadeFicha> atvFichas, boolean criarFichaPre) {
        this.nome = ficha.getNome();
        this.mensagemAluno = ficha.getMensagemAluno();
        this.categoria = ficha.getCategoria();
        this.usarComoPredefinida = criarFichaPre;

        for (AtividadeFicha atividadeFicha : atvFichas) {
            AtividadeFicha atvFc = new AtividadeFicha(atividadeFicha.getCodigo(),
                                                      atividadeFicha.getNome(),
                                                      atividadeFicha.getSetId(),
                                                      atividadeFicha.getAtividade(),
                    this,
                    atividadeFicha.getOrdem(),
                    atividadeFicha.getMetodoExecucao(),
                    atividadeFicha.getAjustes(),
                    atividadeFicha.getNomeAtividadeAlteradoManualmente(),
                    atividadeFicha.getComplementoNomeAtividade());
            atvFc.setMetodoExecucao(atividadeFicha.getMetodoExecucao());
            atvFc.setSetId(atividadeFicha.getSetId());
            for (Serie s : atividadeFicha.getSeries()) {
                Serie serie = new Serie(atvFc, s);
                atvFc.getSeries().add(serie);
            }
            this.atividades.add(atvFc);
        }

    }

    public Ficha(Integer codigo) {
        this.codigo = codigo;
    }

    public ProgramaTreinoFicha getFichaAntesAlteracao() {
        return fichaAntesAlteracao;
    }

    public void setFichaAntesAlteracao(ProgramaTreinoFicha fichaAntesAlteracao) {
        this.fichaAntesAlteracao = fichaAntesAlteracao;
    }

    public Integer getVersao() {
        return versao;
    }

    public void setVersao(Integer versao) {
        this.versao = versao;
    }

    public List<ProgramaTreinoFicha> getProgramas() {
        if (programas == null) {
            programas = new ArrayList<>();
        }
        return programas;
    }

    public void setProgramas(List<ProgramaTreinoFicha> programas) {
        if (this.programas == null) {
            this.programas = programas;
        } else if(this.programas != programas) {
            this.programas.clear();
            if(programas != null){
                this.programas.addAll(programas);
            }
        }
    }

    public Ficha(final String nome, final String mensagemAluno, CategoriaFicha categoria, boolean usarComoPrefefinida) {
        this.nome = nome;
        this.mensagemAluno = mensagemAluno;
        this.categoria = categoria;
        this.usarComoPredefinida = usarComoPrefefinida;
    }

    public Integer getCodigo() {
        if (codigo != null && codigo.equals(0)) {
            codigo = null;
        }
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public String getNomeApresentar() {
        if (nome.contains("#@")){
            String[] split = nome.split(" #@ ");
            nome = split[0];
        }
        return nome;
    }

    public String getNomeAbreviado() {
        if (nome.contains("#@")){
            String[] split = nome.split(" #@ ");
            nome = split[0];
        }
        return nome.length() > 30 ? nome.substring(0, 30) + "..." : nome;
    }

    public String getDescricaoTipoExecucao() {
        if (getProgramas() != null && getProgramas().size() > 0) {
            ProgramaTreinoFicha progFicha = getProgramas().get(0);
            TipoExecucaoEnum tipo = progFicha.getTipoExecucao();
            if (tipo != null) {
                if (tipo == TipoExecucaoEnum.DIAS_SEMANA) {
                    return progFicha.getDiaSemanaAsString();
                } else {
                    return tipo.getDescricao();
                }
            }
        }
        return "";
    }

    public String getNomeConcatenado() {
        StringBuilder s = new StringBuilder(getNomeApresentar());
        try {
            if (getProgramas() != null && getProgramas().size() > 0) {
                ProgramaTreinoFicha progFicha = getProgramas().get(0);
                String[] split = getProgramas().get(0).getFicha().getNome().split(" #@ ");
                String nomeficha = split[0];
                getProgramas().get(0).getFicha().setNome(nomeficha);

                TipoExecucaoEnum tipo = progFicha.getTipoExecucao();
                if (tipo != null) {
                    if (tipo == TipoExecucaoEnum.DIAS_SEMANA) {
                        s.append(" :: ").append(progFicha.getDiaSemanaAsString());
                    } else {
                        s.append(" :: ").append(tipo.getDescricao());
                    }
                }
            }
            if (!getNomeCategoria().equalsIgnoreCase("")) {
                String[] split = s.toString().split(" #@ ");
                String nomeficha = split[0];
                s.delete(0,s.length());
                s.append(nomeficha);
                s.append(" - ").append(getNomeCategoria());
            }
        } catch (Exception e) {
        }
        return s.toString();
    }

    public String getNomeCategoria(){
        if (getCategoria() != null && getCategoria().getNome() != null && !getCategoria().getNome().isEmpty()) {
            return getCategoria().getNome();
        }
        return "";
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getMensagemAluno() {
        return mensagemAluno;
    }

    public void setMensagemAluno(String mensagemAluno) {
        this.mensagemAluno = mensagemAluno;
    }

    public CategoriaFicha getCategoria() {
        return categoria;
    }

    public void setCategoria(CategoriaFicha categoria) {
        this.categoria = categoria;
    }

    public boolean isUsarComoPredefinida() {
        return usarComoPredefinida;
    }

    public void setUsarComoPredefinida(boolean usarComoPredefinida) {
        this.usarComoPredefinida = usarComoPredefinida;
    }

    public List<AtividadeFicha> getAtividades() {
        if (atividades == null) {
            atividades = new ArrayList<>();
        }
        return atividades;
    }

    public void setAtividades(List<AtividadeFicha> atividades) {
        getAtividades().clear();
        getAtividades().addAll(atividades);
    }

    public void setAtividadesPadrao(List<AtividadeFicha> atividades) {
        this.atividades = atividades;
    }

    public Integer getMaiorOrdem() {
        List<AtividadeFicha> ordenarLista = Ordenacao.ordenarLista(getAtividades(), "ordem");
        if (ordenarLista == null || ordenarLista.isEmpty()) {
            return 0;
        }
        return ordenarLista.get(ordenarLista.size() - 1).getOrdem();

    }

    public boolean isPodeGerarSeries() {
        //return (series == null || (!series.isEmpty() && series.size() == 1&& series.get(0).getCodigo() == null));
        return atividades != null && !atividades.isEmpty();
    }

    public Integer getQtdSeriesInserir() {
        return qtdSeriesInserir;
    }

    public void setQtdSeriesInserir(Integer qtdSeriesInserir) {
        this.qtdSeriesInserir = qtdSeriesInserir;
    }

    public Integer getQtdRepeticoesSeriesInserir() {
        return qtdRepeticoesSeriesInserir;
    }

    public void setQtdRepeticoesSeriesInserir(Integer qtdRepeticoesSeriesInserir) {
        this.qtdRepeticoesSeriesInserir = qtdRepeticoesSeriesInserir;
    }

    public String getDescansoRepetir() {
        return descansoRepetir;
    }

    public void setDescansoRepetir(String descansoRepetir) {
        this.descansoRepetir = descansoRepetir;
    }

    public Boolean getAtivo() {
        return ativo;
    }

    public void setAtivo(Boolean ativo) {
        this.ativo = ativo;
    }

    public String getStatus() {
        if (ativo != null) {
            return ativo ? "Ativa" : "Inativa";
        }
        return "";
    }

    public Date getUltimaExecucao() {
        return ultimaExecucao;
    }

    public void setUltimaExecucao(Date ultimaExecucao) {
        this.ultimaExecucao = ultimaExecucao;
    }

    public Integer getNrAtividades() {
        return nrAtividades;
    }

    public void setNrAtividades(Integer nrAtividades) {
        this.nrAtividades = nrAtividades;
    }

    public boolean isSelecionado() {
        return selecionado;
    }

    public void setSelecionado(boolean selecionado) {
        this.selecionado = selecionado;
    }

    public String getName() {
        return isSelecionado() ? "atividadeSelecionada" : "ficha" + getCodigo();
    }

    public NumeroTO getRepeticaoSpin() {
        if (repeticaoSpin == null) {
            repeticaoSpin = new NumeroTO(qtdRepeticoesSeriesInserir);
        }
        return repeticaoSpin;
    }

    public void setRepeticaoSpin(NumeroTO repeticaoSpin) {
        this.repeticaoSpin = repeticaoSpin;
    }

    public NumeroTO getQtdSeriesSpin() {
        if (qtdSeriesSpin == null) {
            qtdSeriesSpin = new NumeroTO(qtdSeriesInserir);
        }
        return qtdSeriesSpin;
    }

    public void setQtdSeriesSpin(NumeroTO qtdSeriesSpin) {
        this.qtdSeriesSpin = qtdSeriesSpin;
    }

    public Nivel getNivel() {
        return nivel;
    }

    public void setNivel(Nivel nivel) {
        this.nivel = nivel;
    }


    public List<String> getGrupos(){

        List<String> grupos = new ArrayList<String>();
        try {
            for(AtividadeFicha atf : atividades){
                for(AtividadeGrupoMuscular ag : atf.getAtividade().getGruposMusculares()){
                    if(!grupos.contains(ag.getGrupoMuscular().getNome().toLowerCase())){
                        grupos.add(ag.getGrupoMuscular().getNome().toLowerCase());
                    }
                }
            }
        }catch (Exception e){

        }

        Collections.sort(grupos);
        return grupos;
    }

    public SexoEnum getSexo() {
        return sexo;
    }

    public void setSexo(SexoEnum sexoEnum) {
        this.sexo = sexoEnum;
    }

    public Integer getCodigoGenero() {
        return sexo == null ? SexoEnum.N.ordinal() : sexo.ordinal();
    }

    public void setCodigoGenero(Integer codigoGenero) {
        sexo = codigoGenero == null ? SexoEnum.N : SexoEnum.getFromId(codigoGenero);
    }

    public String getDescricaoParaLog(Ficha v2) {
        try {
            StringBuilder log = new StringBuilder();
            log.append(UtilReflection.difference(this, v2));

            if (!isNull(this.getAtividades()) && !this.getAtividades().isEmpty() && isNull(v2)) {
                for (AtividadeFicha atividadeFicha : this.getAtividades()) {
                    log.append(UtilReflection.difference(atividadeFicha, null));
                    atividadeFicha.getSeries().forEach(s -> {
                        log.append(UtilReflection.difference(s, null));
                    });
                }
            } else {
                for (AtividadeFicha atividadeFicha : this.getAtividades()) {
                    for (AtividadeFicha atividadeFicha2 : v2.getAtividades()) {
                        if (atividadeFicha2.getCodigo().equals(atividadeFicha.getCodigo())) {
                            log.append(UtilReflection.difference(atividadeFicha, atividadeFicha2));
                            break;
                        }
                    }

                    for (Serie serie : atividadeFicha.getSeries()) {
                        for (AtividadeFicha atividadeFicha2 : v2.getAtividades()) {
                            boolean achou = false;
                            for (Serie serie2 : atividadeFicha2.getSeries()) {
                                if (serie2.getCodigo().equals(serie.getCodigo())) {
                                    log.append(UtilReflection.difference(serie, serie2));
                                    achou = true;
                                    break;
                                }
                            }
                            if (achou) {
                                break;
                            }
                        }
                    }
                }
            }
            return log.toString();
        } catch (Exception ex) {
            ex.printStackTrace();
            return "ERRO gerar log";
        }
    }
}
