/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.ficha;

import java.io.Serializable;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(uniqueConstraints =
        @UniqueConstraint(columnNames = {"nome"}))
public class CategoriaFicha implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private String nome;

    public CategoriaFicha() {
    }

    public CategoriaFicha(final String nome) {
        this.nome = nome;
    }
    public CategoriaFicha(final Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }
    
    public static boolean diferente(CategoriaFicha cat1, CategoriaFicha cat2){
        return  (cat1 == null && cat2 != null)
                    || (cat1 != null && cat2 == null)
                    || (cat1 != null && cat2 != null && cat1.getNome() != null && cat2.getNome() != null && 
                        !cat1.getNome().equals(cat2.getNome()));
    }
}
