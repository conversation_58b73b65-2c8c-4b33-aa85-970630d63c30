package br.com.pacto.bean.ficha;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.List;

/**
 * Created by ulisses on 12/08/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AtividadeFichaPadraoSeriesTO {

    private List<Integer> atividadesFichaId;
    private Integer numeroSeries;
    private Integer repeticoes;
    private Double carga;
    private Integer descanso;

    public List<Integer> getAtividadesFichaId() {
        return atividadesFichaId;
    }

    public void setAtividadesFichaId(List<Integer> atividadesFichaId) {
        this.atividadesFichaId = atividadesFichaId;
    }

    public Integer getNumeroSeries() {
        return numeroSeries;
    }

    public void setNumeroSeries(Integer numeroSeries) {
        this.numeroSeries = numeroSeries;
    }

    public Integer getRepeticoes() {
        return repeticoes;
    }

    public void setRepeticoes(Integer repeticoes) {
        this.repeticoes = repeticoes;
    }

    public Double getCarga() {
        return carga;
    }

    public void setCarga(Double carga) {
        this.carga = carga;
    }

    public Integer getDescanso() {
        return descanso;
    }

    public void setDescanso(Integer descanso) {
        this.descanso = descanso;
    }

}
