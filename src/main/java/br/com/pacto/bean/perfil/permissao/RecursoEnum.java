/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.perfil.permissao;

import org.apache.commons.lang3.StringUtils;

/**
 *
 * <AUTHOR>
 */
public enum RecursoEnum {

    MUSCULOS(0, "Cadastro de Músculos", null, CategoriaRecursoEnum.CADASTROS_AUXILIARES, TipoRecurso.ENTIDADE),
    GRUPOS_MUSCULARES(1, "Grupos Musculares", null, CategoriaRecursoEnum.CADASTROS_AUXILIARES, TipoRecurso.ENTIDADE),
    APARELHOS(2, "Aparelhos", null, CategoriaRecursoEnum.CADASTROS_AUXILIARES, TipoRecurso.ENTIDADE),
    ATIVIDADES(3, "Atividades", null, CategoriaRecursoEnum.CADASTROS_AUXILIARES, TipoRecurso.ENTIDADE),
    FICHAS_PRE_DEFINIDAS(4, "Fichas pré-definidas", null, CategoriaRecursoEnum.CADASTROS_AUXILIARES, TipoRecurso.ENTIDADE),
    CATEGORIA_ATIVIDADE(5, "Categoria Atividade", null, CategoriaRecursoEnum.CADASTROS_AUXILIARES, TipoRecurso.ENTIDADE),
    CATEGORIA_FICHAS(6, "Categoria Ficha", null, CategoriaRecursoEnum.CADASTROS_AUXILIARES, TipoRecurso.ENTIDADE),
    NIVEIS(7, "Níveis", null, CategoriaRecursoEnum.CADASTROS_AUXILIARES, TipoRecurso.ENTIDADE),
    OBJETIVOS(8, "Objetivos pré-definidos", null, CategoriaRecursoEnum.CADASTROS_AUXILIARES, TipoRecurso.ENTIDADE),
    IMAGENS(9, "Imagens", null, CategoriaRecursoEnum.CADASTROS_AUXILIARES, TipoRecurso.ENTIDADE),
    ALUNOS(10, "Alunos", null, CategoriaRecursoEnum.ALUNO, TipoRecurso.ENTIDADE),
    GESTAO(11, "Gestão", null, CategoriaRecursoEnum.GERAL, TipoRecurso.ENTIDADE),
    NOTIFICACOES(12, "Notificações", null, CategoriaRecursoEnum.GERAL, TipoRecurso.ENTIDADE),
    ACOMPANHAR(13, "Acompanhar", null, CategoriaRecursoEnum.ALUNO, TipoRecurso.ENTIDADE),
    CONFIGURACOES_EMPRESA(14, "Configurações Empresa", null, CategoriaRecursoEnum.GERAL, TipoRecurso.ENTIDADE),
    BADGES(15, "Badges", null, CategoriaRecursoEnum.CADASTROS_AUXILIARES, TipoRecurso.ENTIDADE),
    ADD_ALUNO(16, "Adicionar aluno", null, CategoriaRecursoEnum.ALUNO, TipoRecurso.ENTIDADE),
    TIPO_EVENTO(17, "Tipo de evento", null, CategoriaRecursoEnum.CADASTROS_AUXILIARES, TipoRecurso.ENTIDADE),
    PERFIL_USUARIO(18, "Perfis de usuário", null, CategoriaRecursoEnum.GERAL, TipoRecurso.ENTIDADE),
    AGENDA_DISPONIBILIDADE(19, "Disponibilidade", null, CategoriaRecursoEnum.AGENDA, TipoRecurso.ENTIDADE),
    CONTATO_INTERPESSOAL(20, "Contato Interpessoal", null, CategoriaRecursoEnum.AGENDA, TipoRecurso.ENTIDADE),
    PRESCRICAO_TREINO(21, "Prescrição de Treino", null, CategoriaRecursoEnum.AGENDA, TipoRecurso.ENTIDADE),
    REVISAO_TREINO(22, "Revisão de Treino", null, CategoriaRecursoEnum.AGENDA, TipoRecurso.ENTIDADE),
    RENOVAR_TREINO(23, "Renovar Treino", null, CategoriaRecursoEnum.AGENDA, TipoRecurso.ENTIDADE),
    AVALIACAO_FISICA(24, "Avaliação Física", null, CategoriaRecursoEnum.AVALIACAO_FISICA, TipoRecurso.ENTIDADE),
    ALTERAR_PROFESSOR_TREINO(25, "Alterar professor Montou Treino", "Permite o usuário alterar o professor que Montou o Treino do Aluno", CategoriaRecursoEnum.ALUNO, TipoRecurso.FUNCIONALIDADE),
    USUARIOS(26, "Usuários", null, CategoriaRecursoEnum.GERAL, TipoRecurso.ENTIDADE),
    VISUALIZAR_DADOS_CONTATO_ALUNO(27, "Visualizar dados de contato do aluno", "Exibir informações de Telefone/Email/Endereço, etc.", CategoriaRecursoEnum.ALUNO, TipoRecurso.FUNCIONALIDADE),
    PROGRAMA_TREINO(28, "Programa de treino", null, CategoriaRecursoEnum.ALUNO, TipoRecurso.ENTIDADE, TipoPermissaoEnum.INCLUIR, TipoPermissaoEnum.EDITAR),
    VER_ALUNOS_OUTRAS_CARTEIRAS(29, "Visualizar alunos de outras carteiras", "Permite que qualquer professor veja todos alunos na Empresa Logada", CategoriaRecursoEnum.ALUNO, TipoRecurso.FUNCIONALIDADE),
    VER_AGENDA_OUTROS_PROFESSORES(30, "Visualizar a Agenda de outros professores", "Permite que qualquer professor veja a Agenda de outro", CategoriaRecursoEnum.AGENDA, TipoRecurso.FUNCIONALIDADE),
    VER_AUDITORIA_ENTIDADE(31, "Visualizar Log de Alterações", "Permite que o usuário veja dados de auditoria de alterações", CategoriaRecursoEnum.GERAL, TipoRecurso.FUNCIONALIDADE),


    //GESTAO DE PERSONAL
    GESTAO_PERSONAL(32, "Gestão de Personal", "Visualizar e fazer as operações da gestão de personal", CategoriaRecursoEnum.PERSONAL, TipoRecurso.FUNCIONALIDADE),
    LIBERAR_CHECK_IN(33, "Forçar Check-In", "Permite que o usuário force o Check-In de personais externos que não tem crédito", CategoriaRecursoEnum.PERSONAL, TipoRecurso.FUNCIONALIDADE),
    ACOMPANHAR_PERSONAL(34, "Acompanhar personal", "Permite que o usuário acompanhe os personais que fizeram check-in", CategoriaRecursoEnum.PERSONAL, TipoRecurso.FUNCIONALIDADE),
    GESTAO_CREDITOS_PERSONAL(35, "Visualizar a gestão de créditos", "Permite que o usuário utilize a gestão de créditos de personal", CategoriaRecursoEnum.PERSONAL, TipoRecurso.FUNCIONALIDADE),
    EDITAR_EXCLUIR_AULA(36, "Editar / excluir aula", "Permite que o usuário edite e exclua uma aula de personal", CategoriaRecursoEnum.PERSONAL, TipoRecurso.FUNCIONALIDADE),
    TORNAR_FICHA_PREDEFINIDA(37, "Tornar uma Ficha Predefinida", "Permite que o usuário transforme uma Ficha de um Programa de Treino existente em Ficha Predefinida", CategoriaRecursoEnum.GERAL, TipoRecurso.FUNCIONALIDADE),
    FAZER_CHECK_OUT(38, "Fazer Check-out", "Permite que o usuário realize check-out", CategoriaRecursoEnum.PERSONAL, TipoRecurso.FUNCIONALIDADE),
    COLABORADORES(39, "Cadastrar colaboradores", "Permite que o usuário cadastre colaboradores", CategoriaRecursoEnum.GERAL, TipoRecurso.FUNCIONALIDADE),
    CADASTRO_AULAS(40, "Cadastrar aulas", "Permite que o usuário cadastre aulas", CategoriaRecursoEnum.AULAS, TipoRecurso.FUNCIONALIDADE),
    EXCLUIR_AULAS_DIA(41, "Excluir aula diária", "Permite que o usuário exclua aula", CategoriaRecursoEnum.AULAS, TipoRecurso.FUNCIONALIDADE),
    INSERIR_ALUNO(42, "Inserir aluno na aula", "Permite que o usuário insira um aluno", CategoriaRecursoEnum.AULAS, TipoRecurso.FUNCIONALIDADE),
    EXCLUIR_ALUNO(43, "Excluir aluno da aula", "Permite que o usuário exclua um aluno", CategoriaRecursoEnum.AULAS, TipoRecurso.FUNCIONALIDADE),
    AGENDA(44, "Agenda de aulas", "Permite que o usuário visualize a agenda de aulas", CategoriaRecursoEnum.AULAS, TipoRecurso.FUNCIONALIDADE),
    INSERIR_ALUNO_AULA_INICIADA(45, "Inserir aluno numa aula que já está iniciada/realizada",
            "Permite que o usuário insira um aluno numa aula que já está iniciada/realizada", CategoriaRecursoEnum.AULAS, TipoRecurso.FUNCIONALIDADE),
    GESTAO_AULA_CHEIA(46, "Gestão Aula Cheia", "Permite que o usuário visualize a gestão do Módulo Aula Cheia", CategoriaRecursoEnum.AULAS, TipoRecurso.FUNCIONALIDADE),
    ALTERAR_PROFESSOR_AULA(47, "Alterar professor aula", "Permite que o usuário altere o professor da aula", CategoriaRecursoEnum.AULAS, TipoRecurso.FUNCIONALIDADE),
    ALTERAR_PROFESSOR_AULA_INICIADA(48, "Alterar professor aula iniciada", "Permite que o usuário altere o professor da aula já iniciada", CategoriaRecursoEnum.AULAS, TipoRecurso.FUNCIONALIDADE),
    EMPRESA(49, "Empresa", null, CategoriaRecursoEnum.CADASTROS_AUXILIARES, TipoRecurso.ENTIDADE),
    VER_GESTAO_GERAL(50, "Visualizar gestão geral",
            "Permite que o usuário veja dados da gestão da academia toda", CategoriaRecursoEnum.GERAL, TipoRecurso.FUNCIONALIDADE),
    TROCAR_PROFESSOR(51, "Trocar professor do aluno",
            "Permite que o usuário troque o professor do aluno no TreinoWeb", CategoriaRecursoEnum.ALUNO, TipoRecurso.FUNCIONALIDADE),
    ALTERAR_SENHA_USUARIOS(52,"Alterar senha Usuários", "Permite que altere a senha de outros usários", CategoriaRecursoEnum.GERAL, TipoRecurso.FUNCIONALIDADE),
    PERMISSAO_EXCLUIR_ALUNO(53,"Excluir Cadastro do Aluno","Permite que o usuário exclua o cadastro dos alunos", CategoriaRecursoEnum.ALUNO, TipoRecurso.FUNCIONALIDADE),
    VER_BI_OUTROS_PROFESSORES(54,"Permitir ver BI de outros Professores","Permite visualize o desempenho de outro Professores", CategoriaRecursoEnum.GERAL, TipoRecurso.FUNCIONALIDADE),
    EDITAR_NOME_ATIVIDADE_FICHA(55,"Permitir a edição do nome da atividade na ficha","Permitir a edição do nome da atividade na ficha", CategoriaRecursoEnum.GERAL, TipoRecurso.FUNCIONALIDADE),
    GRAFICO_BI(56,"Permitir a visualização do Gráfico de Gestão","Permitir a visualização do Gráfico de Gestão", CategoriaRecursoEnum.GERAL, TipoRecurso.FUNCIONALIDADE),
    RANKING_PROFESSORES(57,"Permitir a visualização do Ranking de professores","Permitir a visualização do Ranking de professores", CategoriaRecursoEnum.GERAL, TipoRecurso.FUNCIONALIDADE),
    TVGESTOR(58,"Permitir a visualização da TV Gestor","Permitir a visualização da TV Gestor", CategoriaRecursoEnum.AULAS, TipoRecurso.FUNCIONALIDADE),

    //CROSS FIT
    BENCHMARK(59, "Benchmark", null, CategoriaRecursoEnum.CROSSFIT, TipoRecurso.FUNCIONALIDADE),
    TIPO_BENCHMARK(60, "Tipo Benchmark", null, CategoriaRecursoEnum.CROSSFIT, TipoRecurso.FUNCIONALIDADE),
    WOD(61,"Wod",null,CategoriaRecursoEnum.CROSSFIT, TipoRecurso.FUNCIONALIDADE),
    RANKING_WOD(62,"Ranking Crossfit",null,CategoriaRecursoEnum.CROSSFIT, TipoRecurso.FUNCIONALIDADE),
    ALTERAR_HORARIO_INICIO_CHECK_PERSONAL(63,"Alterar data/horário de início/fim do check-in/checkout",null,CategoriaRecursoEnum.PERSONAL,TipoRecurso.FUNCIONALIDADE),
    ANAMNESE(64, "Anamnese", null, CategoriaRecursoEnum.CADASTROS_AUXILIARES, TipoRecurso.ENTIDADE),

    //GERAL
    //Joao Alcides: Alterei essa permissão para que ela faça o papel de alterar a avaliação física como um todo
    //isso porque o sistema foi alterado para permitir essa funcionalidade, e não podemos adicionar mais uma permissão desnecessária
    LANCAR_AVALICAO_RETROATIVA(65,"Alterar Avaliação Fisica","Permitir alterar a avaliação física já lançada e lançar uma nova com data retroativa",CategoriaRecursoEnum.GERAL,TipoRecurso.ENTIDADE),

    //CROSS FIT
    CADASTRAR_TIPO_WOD(66,"Cadastrar Tipo Wod",null,CategoriaRecursoEnum.CROSSFIT,TipoRecurso.FUNCIONALIDADE),
    EXCLUIR_ANEXO_ALUNO(69,"Permitir excluir anexo da avaliação física",null,CategoriaRecursoEnum.GERAL,TipoRecurso.FUNCIONALIDADE),

    ATIVIDADES_WOD(67,"Cadastrar Atividades Wod",null,CategoriaRecursoEnum.CROSSFIT,TipoRecurso.FUNCIONALIDADE),
    APARELHOS_WOD(68,"Cadastrar Aparelho Wod",null,CategoriaRecursoEnum.CROSSFIT,TipoRecurso.FUNCIONALIDADE),
    PERMISSAO_EXCLUIR_ALUNO_VINCULADO(70, "Excluir Cadastro do Aluno ignorando vínculo", "Permite que o usuário exclua o cadastro dos alunos, mesmo que estes possuam vínculo.", CategoriaRecursoEnum.ALUNO, TipoRecurso.FUNCIONALIDADE),
    TROCAR_METODO_EXECUCAO(71, "Método de execução", "Trocar o método de execução da ficha", CategoriaRecursoEnum.GERAL, TipoRecurso.FUNCIONALIDADE),


    MODALIDADES(72, "Cadastrar modalidades", "Permite que o usuário cadastre modalidades", CategoriaRecursoEnum.AULAS, TipoRecurso.FUNCIONALIDADE),
    SERIE(73, "Cadastrar series", "Permite que o usuário cadastre series", CategoriaRecursoEnum.AULAS, TipoRecurso.FUNCIONALIDADE),
    AMBIENTES(74, "Cadastrar ambientes", "Permite que o usuário cadastre ambientes", CategoriaRecursoEnum.AULAS, TipoRecurso.FUNCIONALIDADE),
    LOCAL_RETIRA_FICHA(75, "Cadastrar Local Retira Ficha", "Permite que o usuário cadastre um local para receber o histórico de alteração do Retira Ficha.", CategoriaRecursoEnum.CADASTROS_AUXILIARES, TipoRecurso.FUNCIONALIDADE),
    VISUALIZAR_TODAS_AULAS_APP(76, "Visualizar aulas e turmas de todos os professores no APP", "Permite o usuário a visualizar todas as aulas da academia no APP.", CategoriaRecursoEnum.AULAS, TipoRecurso.FUNCIONALIDADE),
    USUARIO_MARCAR_ANTECEDENCIA(77,"Marcar e desmarcar aula com antecedência","Marcar e desmarcar aula com antecedência", CategoriaRecursoEnum.AULAS, TipoRecurso.FUNCIONALIDADE),
    PROGRAMAS_PREDEFINIDOS(78, "Programas predefinidos", null, CategoriaRecursoEnum.CADASTROS_AUXILIARES, TipoRecurso.ENTIDADE),
    TORNAR_PROGRAMA_PREDEFINIDO(79, "Tornar um Programa Predefinido", "Permite que o usuário transforme um Programa de Treino existente em Programa Predefinido", CategoriaRecursoEnum.GERAL, TipoRecurso.FUNCIONALIDADE),
    ATRIBUIR_PROGRAMA_TREINO_PRE_DEFINIDO(80, "Atribuir Programa Treino Pré Definido", "Permite que o usuario atribua program de treino pre definido ao aluno", CategoriaRecursoEnum.ALUNO, TipoRecurso.FUNCIONALIDADE),
    DESVINCULAR_USUARIO(81, "Desvincular Usuário da Academia", "Permite a remoção do acesso de um usuário a academia removendo os dados de acessos (e-mail e senha) do mesmo à academia.", CategoriaRecursoEnum.GERAL, TipoRecurso.FUNCIONALIDADE),
    TELA_PRESCRICAO_TREINO(82, "Tela de prescrição de treino", "Permite que o usuário tenha acesso a tela de prescrição de treino", CategoriaRecursoEnum.GERAL, TipoRecurso.FUNCIONALIDADE),
    AVALIACAO_MEDIA_ALUNOS(83, "Visualizar detalhes de Avaliação média do treino dos alunos", "Permite o usuário visualizar a avaliação média dos alunos no APP.",CategoriaRecursoEnum.GERAL, TipoRecurso.FUNCIONALIDADE),
    VISUALIZAR_WOD_OUTRAS_UNIDADES(84, "Visualizar WOD de outras unidades", "Permite que o usuário visualize os WODs de outras unidades", CategoriaRecursoEnum.CROSSFIT, TipoRecurso.FUNCIONALIDADE),
    BI_CROSS(85, "BI Cross", "Permite que o usuário acesse os relatórios do BI relacionados ao CrossFit", CategoriaRecursoEnum.GERAL, TipoRecurso.FUNCIONALIDADE),
    MONITOR(86, "Monitor", "Permite que o usuário monitore as atividades e processos em execução", CategoriaRecursoEnum.GERAL, TipoRecurso.FUNCIONALIDADE),
    ATIVIDADES_GRADUACAO(87, "Atividades Graduação", "Permite que o usuário visualize e gerencie as atividades de graduação", CategoriaRecursoEnum.GRADUACAO, TipoRecurso.FUNCIONALIDADE),
    FICHA_TECNICA_GRADUACAO(88, "Ficha Técnica Graduação", "Permite que o usuário acesse e gerencie as fichas técnicas de graduação", CategoriaRecursoEnum.GRADUACAO, TipoRecurso.FUNCIONALIDADE),
    AVALIACAO_DE_PROGRESSO_GRADUACAO(89, "Avaliação de Progresso Graduação", "Permite que o usuário realize avaliações de progresso dos alunos de graduação", CategoriaRecursoEnum.GRADUACAO, TipoRecurso.FUNCIONALIDADE),
    BI_AGENDA(90, "BI Agenda", "Permite que o usuário acesse os relatórios do BI relacionados à agenda", CategoriaRecursoEnum.AGENDA, TipoRecurso.FUNCIONALIDADE),
    AGENDA_DE_AULAS(91, "Agenda de Aulas", "Permite que o usuário visualize e gerencie a agenda de aulas", CategoriaRecursoEnum.AGENDA, TipoRecurso.FUNCIONALIDADE),
    AGENDA_DE_SERVICOS(92, "Agenda de Serviços", "Permite que o usuário visualize e gerencie a agenda de serviços", CategoriaRecursoEnum.AGENDA, TipoRecurso.FUNCIONALIDADE),
    RELATORIOS_AGENDA(93, "Relatórios Agenda", "Permite que o usuário acesse os relatórios relacionados à agenda", CategoriaRecursoEnum.AGENDA, TipoRecurso.FUNCIONALIDADE),
    BI_AVALIACAO_FISICA(94, "BI Avaliação Física", "Permite que o usuário acesse os relatórios do BI relacionados à avaliação física", CategoriaRecursoEnum.AVALIACAO_FISICA, TipoRecurso.FUNCIONALIDADE),
    CADASTROS_AVALIACAO_FISICA(95, "Cadastros Avaliação Física", "Permite que o usuário realize cadastros relacionados à avaliação física", CategoriaRecursoEnum.AVALIACAO_FISICA, TipoRecurso.FUNCIONALIDADE),
    CRIAR_AVALIACAO_FISICA(96, "Criar Avaliação Física", "Permite que o usuário crie novas avaliações físicas", CategoriaRecursoEnum.AVALIACAO_FISICA, TipoRecurso.FUNCIONALIDADE),
    VISUALIZAR_AVALIACAO_FISICA(97, "Visualizar Avaliação Física", "Permite que o usuário visualize as avaliações físicas existentes", CategoriaRecursoEnum.AVALIACAO_FISICA, TipoRecurso.FUNCIONALIDADE),
    RELATORIOS_TREINO(98, "Relatórios Treino", "Permite que o usuário acesse relatórios relacionados aos treinos", CategoriaRecursoEnum.TREINO, TipoRecurso.FUNCIONALIDADE),
    UTILIZAR_MODULO_CROSS(99, "Utilizar Módulo Cross", null, CategoriaRecursoEnum.CROSSFIT, TipoRecurso.FUNCIONALIDADE),
    UTILIZAR_MODULO_GRADUACAO(100, "Utilizar Módulo Graduação", null, CategoriaRecursoEnum.GRADUACAO, TipoRecurso.FUNCIONALIDADE),
    UTILIZAR_MODULO_AGENDA(101, "Utilizar Módulo Agenda", null, CategoriaRecursoEnum.AGENDA, TipoRecurso.FUNCIONALIDADE),
    UTILIZAR_MODULO_AVALIACAO_FISICA(102, "Utilizar Módulo Avaliação Física", null, CategoriaRecursoEnum.AVALIACAO_FISICA, TipoRecurso.FUNCIONALIDADE),
    CADASTROS_AGENDA(103, "Cadastros Agenda", "Permite que o usuário realize cadastros relacionados à agenda", CategoriaRecursoEnum.AGENDA, TipoRecurso.FUNCIONALIDADE),
    ENVIAR_TREINO_EM_MASSA(104, "Enviar treino em massa", "Permite que o usuário envie treino em massa", CategoriaRecursoEnum.TREINO, TipoRecurso.FUNCIONALIDADE),
    TREINO_EM_CASA(105, "Habilitar treino em casa", "Habilitar ou desabilitar o recurso", CategoriaRecursoEnum.TREINO, TipoRecurso.FUNCIONALIDADE),
    PRESCRICAO_DE_TREINO(106, "Habilitar prescrição de treino", "Habilitar ou desabilitar o recurso", CategoriaRecursoEnum.TREINO, TipoRecurso.FUNCIONALIDADE),
    CADASTROS_TREINO(107, "Cadastros treino", "Permite que o usuário acesse cadastros relacionados aos treinos", CategoriaRecursoEnum.TREINO, TipoRecurso.FUNCIONALIDADE),
    PRESCRICAO_DE_TREINO_POR_IA(108, "Habilitar prescrição de treino POR I.A.", "Habilitar ou desabilitar o recurso", CategoriaRecursoEnum.TREINO, TipoRecurso.FUNCIONALIDADE),
    EDITAR_AULAS_DIA(109, "Permitir editar aulas", "Permite que o usuário edite aulas", CategoriaRecursoEnum.AULAS, TipoRecurso.FUNCIONALIDADE),
    CONFIGURACOES_DO_RANKING(110,"Permitir acessar as configurações do Ranking","Permitir acessar as configurações do Ranking", CategoriaRecursoEnum.GERAL, TipoRecurso.FUNCIONALIDADE);

    private Integer id;
    private String nome;
    private CategoriaRecursoEnum categoria;
    private TipoRecurso tipo;
    private TipoPermissaoEnum[] tiposConjuntos;
    private String descricao;

    private RecursoEnum(Integer id, String nome, String descricao, CategoriaRecursoEnum categoria,
            TipoRecurso tipo, TipoPermissaoEnum... tiposConjuntos) {
        this.id = id;
        this.nome = nome;
        this.categoria = categoria;
        this.tipo = tipo;
        this.tiposConjuntos = tiposConjuntos;
        this.descricao = descricao;
    }

    public TipoPermissaoEnum[] getTiposConjuntos() {
        return tiposConjuntos;
    }

    public void setTiposConjuntos(TipoPermissaoEnum[] tiposConjuntos) {
        this.tiposConjuntos = tiposConjuntos;
    }

    public CategoriaRecursoEnum getCategoria() {
        return categoria;
    }

    public void setCategoria(CategoriaRecursoEnum categoria) {
        this.categoria = categoria;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public static RecursoEnum getFromId(int id) {
        for (RecursoEnum recurso : values()) {
            if (recurso.getId().intValue() == id) {
                return recurso;
            }
        }
        return null;
    }

    /**
     * Calcula o recurso pelo seu nome
     *
     * @param name O nome do recurso, perceba que se trata do próprio nome do enum e não de um marcador
     * @return O recurso encontrado ou <b>null</b> caso nada seja encontrado
     * @see Enum#name()
     */
    public static RecursoEnum fromName(String name) {
        if (StringUtils.isBlank(name)) {
            return null;
        }

        for (RecursoEnum categoriaRecursoEnum : RecursoEnum.values()) {
            if (categoriaRecursoEnum.getNome().equalsIgnoreCase(name)) {
                return categoriaRecursoEnum;
            }
        }

        return null;
    }

    public TipoRecurso getTipo() {
        return tipo;
    }

    public void setTipo(TipoRecurso tipo) {
        this.tipo = tipo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}
