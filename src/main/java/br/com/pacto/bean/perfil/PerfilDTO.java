package br.com.pacto.bean.perfil;

import br.com.pacto.bean.perfil.permissao.Permissao;
import br.com.pacto.bean.perfil.permissao.TipoRecurso;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by paulo on 22/07/2019.
 */
public class PerfilDTO {

    private Integer id;
    private String nome;
    private String tipo;
    private RecursoEntidadeDTO recursos;
    private RecursoFuncionalidadeDTO funcionalidades;

    public PerfilDTO (Perfil perfil) {
        this.id = perfil.getCodigo();
        this.nome = perfil.getNome();
        this.tipo = perfil.getTipo();
        Map<String, List> recursoMap = new HashMap<>();
        Map<String, Boolean> funcionalidadeMap = new HashMap<>();
        ObjectMapper mapper = new ObjectMapper();
        for (Permissao permissao : perfil.getPermissoes()) {
            if (permissao.getRecurso().getTipo().equals(TipoRecurso.ENTIDADE)) {
                recursoMap.put(permissao.getRecurso().name().toLowerCase(), new ArrayList(permissao.getTipoPermissoes()));
            } else {
                funcionalidadeMap.put(permissao.getRecurso().name().toLowerCase(), (permissao.getTipoPermissoes() != null && permissao.getTipoPermissoes().size() > 0));
            }
        }
        this.recursos = mapper.convertValue(recursoMap, RecursoEntidadeDTO.class);
        this.funcionalidades = mapper.convertValue(funcionalidadeMap, RecursoFuncionalidadeDTO.class);
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public RecursoEntidadeDTO getRecursos() {
        return recursos;
    }

    public void setRecursos(RecursoEntidadeDTO recursos) {
        this.recursos = recursos;
    }

    public RecursoFuncionalidadeDTO getFuncionalidades() {
        return funcionalidades;
    }

    public void setFuncionalidades(RecursoFuncionalidadeDTO funcionalidades) {
        this.funcionalidades = funcionalidades;
    }

    public String getTipo() {
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }
}
