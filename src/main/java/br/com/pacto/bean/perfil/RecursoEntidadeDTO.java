package br.com.pacto.bean.perfil;

import br.com.pacto.bean.perfil.permissao.TipoPermissaoEnum;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by paulo on 22/07/2019.
 */
public class RecursoEntidadeDTO {

    private List<TipoPermissaoEnum> musculos = new ArrayList<>();
    private List<TipoPermissaoEnum> grupos_musculares = new ArrayList<>();
    private List<TipoPermissaoEnum> aparelhos = new ArrayList<>();
    private List<TipoPermissaoEnum> atividades = new ArrayList<>();
    private List<TipoPermissaoEnum> fichas_pre_definidas = new ArrayList<>();
    private List<TipoPermissaoEnum> programas_predefinidos = new ArrayList<>();
    private List<TipoPermissaoEnum> categoria_atividade = new ArrayList<>();
    private List<TipoPermissaoEnum> categoria_fichas = new ArrayList<>();
    private List<TipoPermissaoEnum> niveis = new ArrayList<>();
    private List<TipoPermissaoEnum> objetivos = new ArrayList<>();
    private List<TipoPermissaoEnum> imagens = new ArrayList<>();
    private List<TipoPermissaoEnum> alunos = new ArrayList<>();
    private List<TipoPermissaoEnum> gestao = new ArrayList<>();
    private List<TipoPermissaoEnum> notificacoes = new ArrayList<>();
    private List<TipoPermissaoEnum> acompanhar = new ArrayList<>();
    private List<TipoPermissaoEnum> configuracoes_empresa = new ArrayList<>();
    private List<TipoPermissaoEnum> lancar_avalicao_retroativa = new ArrayList<>();
    private List<TipoPermissaoEnum> badges = new ArrayList<>();
    private List<TipoPermissaoEnum> add_aluno = new ArrayList<>();
    private List<TipoPermissaoEnum> tipo_evento = new ArrayList<>();
    private List<TipoPermissaoEnum> perfil_usuario = new ArrayList<>();
    private List<TipoPermissaoEnum> agenda_disponibilidade = new ArrayList<>();
    private List<TipoPermissaoEnum> contato_interpessoal = new ArrayList<>();
    private List<TipoPermissaoEnum> prescricao_treino = new ArrayList<>();
    private List<TipoPermissaoEnum> revisao_treino = new ArrayList<>();
    private List<TipoPermissaoEnum> renovar_treino = new ArrayList<>();
    private List<TipoPermissaoEnum> avaliacao_fisica = new ArrayList<>();
    private List<TipoPermissaoEnum> usuarios = new ArrayList<>();
    private List<TipoPermissaoEnum> programa_treino = new ArrayList<>();
    private List<TipoPermissaoEnum> empresa = new ArrayList<>();
    private List<TipoPermissaoEnum> anamnese = new ArrayList<>();

    public RecursoEntidadeDTO() {

    }

    public List<TipoPermissaoEnum> getMusculos() {
        return musculos;
    }

    public void setMusculos(List<TipoPermissaoEnum> musculos) {
        this.musculos = musculos;
    }

    public List<TipoPermissaoEnum> getGrupos_musculares() {
        return grupos_musculares;
    }

    public void setGrupos_musculares(List<TipoPermissaoEnum> grupos_musculares) {
        this.grupos_musculares = grupos_musculares;
    }

    public List<TipoPermissaoEnum> getAparelhos() {
        return aparelhos;
    }

    public void setAparelhos(List<TipoPermissaoEnum> aparelhos) {
        this.aparelhos = aparelhos;
    }

    public List<TipoPermissaoEnum> getAtividades() {
        return atividades;
    }

    public void setAtividades(List<TipoPermissaoEnum> atividades) {
        this.atividades = atividades;
    }

    public List<TipoPermissaoEnum> getFichas_pre_definidas() {
        return fichas_pre_definidas;
    }

    public void setFichas_pre_definidas(List<TipoPermissaoEnum> fichas_pre_definidas) {
        this.fichas_pre_definidas = fichas_pre_definidas;
    }

    public List<TipoPermissaoEnum> getCategoria_atividade() {
        return categoria_atividade;
    }

    public void setCategoria_atividade(List<TipoPermissaoEnum> categoria_atividade) {
        this.categoria_atividade = categoria_atividade;
    }

    public List<TipoPermissaoEnum> getCategoria_fichas() {
        return categoria_fichas;
    }

    public void setCategoria_fichas(List<TipoPermissaoEnum> categoria_fichas) {
        this.categoria_fichas = categoria_fichas;
    }

    public List<TipoPermissaoEnum> getNiveis() {
        return niveis;
    }

    public void setNiveis(List<TipoPermissaoEnum> niveis) {
        this.niveis = niveis;
    }

    public List<TipoPermissaoEnum> getObjetivos() {
        return objetivos;
    }

    public void setObjetivos(List<TipoPermissaoEnum> objetivos) {
        this.objetivos = objetivos;
    }

    public List<TipoPermissaoEnum> getImagens() {
        return imagens;
    }

    public void setImagens(List<TipoPermissaoEnum> imagens) {
        this.imagens = imagens;
    }

    public List<TipoPermissaoEnum> getAlunos() {
        return alunos;
    }

    public void setAlunos(List<TipoPermissaoEnum> alunos) {
        this.alunos = alunos;
    }

    public List<TipoPermissaoEnum> getGestao() {
        return gestao;
    }

    public void setGestao(List<TipoPermissaoEnum> gestao) {
        this.gestao = gestao;
    }

    public List<TipoPermissaoEnum> getNotificacoes() {
        return notificacoes;
    }

    public void setNotificacoes(List<TipoPermissaoEnum> notificacoes) {
        this.notificacoes = notificacoes;
    }

    public List<TipoPermissaoEnum> getAcompanhar() {
        return acompanhar;
    }

    public void setAcompanhar(List<TipoPermissaoEnum> acompanhar) {
        this.acompanhar = acompanhar;
    }

    public List<TipoPermissaoEnum> getConfiguracoes_empresa() {
        return configuracoes_empresa;
    }

    public void setConfiguracoes_empresa(List<TipoPermissaoEnum> configuracoes_empresa) {
        this.configuracoes_empresa = configuracoes_empresa;
    }

    public List<TipoPermissaoEnum> getLancar_avalicao_retroativa() { return lancar_avalicao_retroativa; }

    public void setLancar_avalicao_retroativa(List<TipoPermissaoEnum> lancar_avalicao_retroativa) {
        this.lancar_avalicao_retroativa = lancar_avalicao_retroativa;
    }

    public List<TipoPermissaoEnum> getBadges() {
        return badges;
    }

    public void setBadges(List<TipoPermissaoEnum> badges) {
        this.badges = badges;
    }

    public List<TipoPermissaoEnum> getAdd_aluno() {
        return add_aluno;
    }

    public void setAdd_aluno(List<TipoPermissaoEnum> add_aluno) {
        this.add_aluno = add_aluno;
    }

    public List<TipoPermissaoEnum> getTipo_evento() {
        return tipo_evento;
    }

    public void setTipo_evento(List<TipoPermissaoEnum> tipo_evento) {
        this.tipo_evento = tipo_evento;
    }

    public List<TipoPermissaoEnum> getPerfil_usuario() {
        return perfil_usuario;
    }

    public void setPerfil_usuario(List<TipoPermissaoEnum> perfil_usuario) {
        this.perfil_usuario = perfil_usuario;
    }

    public List<TipoPermissaoEnum> getAgenda_disponibilidade() {
        return agenda_disponibilidade;
    }

    public void setAgenda_disponibilidade(List<TipoPermissaoEnum> agenda_disponibilidade) {
        this.agenda_disponibilidade = agenda_disponibilidade;
    }

    public List<TipoPermissaoEnum> getContato_interpessoal() {
        return contato_interpessoal;
    }

    public void setContato_interpessoal(List<TipoPermissaoEnum> contato_interpessoal) {
        this.contato_interpessoal = contato_interpessoal;
    }

    public List<TipoPermissaoEnum> getPrescricao_treino() {
        return prescricao_treino;
    }

    public void setPrescricao_treino(List<TipoPermissaoEnum> prescricao_treino) {
        this.prescricao_treino = prescricao_treino;
    }

    public List<TipoPermissaoEnum> getRevisao_treino() {
        return revisao_treino;
    }

    public void setRevisao_treino(List<TipoPermissaoEnum> revisao_treino) {
        this.revisao_treino = revisao_treino;
    }

    public List<TipoPermissaoEnum> getRenovar_treino() {
        return renovar_treino;
    }

    public void setRenovar_treino(List<TipoPermissaoEnum> renovar_treino) {
        this.renovar_treino = renovar_treino;
    }

    public List<TipoPermissaoEnum> getAvaliacao_fisica() {
        return avaliacao_fisica;
    }

    public void setAvaliacao_fisica(List<TipoPermissaoEnum> avaliacao_fisica) {
        this.avaliacao_fisica = avaliacao_fisica;
    }

    public List<TipoPermissaoEnum> getUsuarios() {
        return usuarios;
    }

    public void setUsuarios(List<TipoPermissaoEnum> usuarios) {
        this.usuarios = usuarios;
    }

    public List<TipoPermissaoEnum> getPrograma_treino() {
        return programa_treino;
    }

    public void setPrograma_treino(List<TipoPermissaoEnum> programa_treino) {
        this.programa_treino = programa_treino;
    }

    public List<TipoPermissaoEnum> getEmpresa() {
        return empresa;
    }

    public void setEmpresa(List<TipoPermissaoEnum> empresa) {
        this.empresa = empresa;
    }

    public List<TipoPermissaoEnum> getAnamnese() {
        return anamnese;
    }

    public void setAnamnese(List<TipoPermissaoEnum> anamnese) {
        this.anamnese = anamnese;
    }

    public List<TipoPermissaoEnum> getProgramas_predefinidos() {
        return programas_predefinidos;
    }

    public void setProgramas_predefinidos(List<TipoPermissaoEnum> programas_predefinidos) {
        this.programas_predefinidos = programas_predefinidos;
    }
}
