/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.perfil.permissao;

import br.com.pacto.bean.perfil.Perfil;
import java.io.Serializable;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;
import javax.persistence.ElementCollection;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.ManyToOne;

/**
 *
 * <AUTHOR>
 */
@Entity
public class Permissao implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private Integer codigo;
    @Enumerated(EnumType.ORDINAL)
    private RecursoEnum recurso;
    @ElementCollection(fetch = FetchType.EAGER)
    private Set<TipoPermissaoEnum> tipoPermissoes;
    @ManyToOne(fetch = FetchType.EAGER)
    private Perfil perfil;
    
    public Permissao() {
        this.tipoPermissoes = new HashSet<>();
    }

    public Permissao(RecursoEnum recurso, TipoPermissaoEnum[] tipoPermissoes, Perfil perfil) {
        this.recurso = recurso;
        HashSet<TipoPermissaoEnum> s = new HashSet<TipoPermissaoEnum>();
        if (tipoPermissoes != null && tipoPermissoes.length > 0) {
            s.addAll(Arrays.asList(tipoPermissoes));
        }
        this.tipoPermissoes = s;
        this.perfil = perfil;
    }
    
    public Permissao(RecursoEnum recurso, Set<TipoPermissaoEnum> tipoPermissoes, Perfil perfil) {
        this.recurso = recurso;
        this.tipoPermissoes = tipoPermissoes;
        this.perfil = perfil;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public RecursoEnum getRecurso() {
        return recurso;
    }

    public void setRecurso(RecursoEnum recurso) {
        this.recurso = recurso;
    }

    public Set<TipoPermissaoEnum> getTipoPermissoes() {
        return tipoPermissoes;
    }

    public void setTipoPermissoes(Set<TipoPermissaoEnum> tipoPermissoes) {
        this.tipoPermissoes = tipoPermissoes;
    }

    public Perfil getPerfil() {
        return perfil;
    }

    public void setPerfil(Perfil perfil) {
        this.perfil = perfil;
    }

    @Override
    public String toString() {
        return String.format("%s %s %s", new Object[]{codigo, recurso, tipoPermissoes.toString()});
    }
}
