/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.perfil;

import br.com.pacto.bean.perfil.permissao.CategoriaRecursoEnum;
import br.com.pacto.bean.perfil.permissao.Permissao;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.bean.perfil.permissao.TipoPermissaoEnum;
import br.com.pacto.bean.perfil.permissao.TipoRecurso;
import br.com.pacto.util.bean.CategoriaRecursosTO;
import br.com.pacto.util.bean.GenericoTO;
import br.com.pacto.util.bean.RecursoTO;
import br.com.pacto.util.impl.Ordenacao;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.persistence.CascadeType;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.persistence.UniqueConstraint;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(uniqueConstraints =
        @UniqueConstraint(columnNames = {"nome"}))
public class Perfil implements Serializable {

    public static final String NOME_PERFIL_COORDENADOR = "COORDENADOR";
    public static final String NOME_PERFIL_PROFESSOR = "PROFESSOR";
    public static final String NOME_PERFIL_CONSULTOR = "CONSULTOR";
    public static final String NOME_PERFIL_GESTAO_PERSONAL = "GESTÃO PERSONAL";
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private String nome;
    @OneToMany(targetEntity = Permissao.class, cascade = {CascadeType.ALL}, orphanRemoval = true, fetch = FetchType.EAGER, mappedBy = "perfil")
    private List<Permissao> permissoes = new ArrayList<Permissao>();
    @Transient
    private List<CategoriaRecursosTO> categorias;
    @Transient
    private boolean permiteExclusao = false;
    private String tipo;

    public boolean isPermiteExclusao() {
        return permiteExclusao;
    }

    public void setPermiteExclusao(boolean permiteExclusao) {
        this.permiteExclusao = permiteExclusao;
    }

    public List<CategoriaRecursosTO> getCategorias() {
        return categorias;
    }

    public void setCategorias(List<CategoriaRecursosTO> categorias) {
        this.categorias = categorias;
    }
    
    public Perfil() {
    }

    public Perfil(final String nome) {
        this.nome = nome;
    }

    public void addPermissao(Permissao permissao) {
        getPermissoes().add(permissao);
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public List<Permissao> getPermissoes() {
        if (permissoes == null) {
            permissoes = new ArrayList<>();
        }
        return permissoes;
    }

    public void setPermissoes(List<Permissao> permissoes) {
        this.permissoes = permissoes;
    }

    public boolean isItemHabilitado(final String name, final String tipoPermissao) {
        //
        RecursoEnum recursoEnum = RecursoEnum.valueOf(name.toUpperCase());
        TipoPermissaoEnum tipoPermissaoEnum = TipoPermissaoEnum.valueOf(tipoPermissao.toUpperCase());
        List<Permissao> l = permissoes;
        for (Permissao permissao : l) {
            if (permissao.getRecurso() == recursoEnum) {
                if (permissao.getTipoPermissoes().contains(tipoPermissaoEnum)) {
                    return true;
                } else if (permissao.getTipoPermissoes().contains(TipoPermissaoEnum.TOTAL)) {
                    return true;
                } else if( permissao.getTipoPermissoes().contains(TipoPermissaoEnum.TOTAL_EXCETO_EXCLUIR)
                        && !tipoPermissaoEnum.equals(TipoPermissaoEnum.EXCLUIR)){
                    return true;

                }
            }
        }
        return false;
    }
    
    public boolean isItemHabilitado(final RecursoEnum rec, final TipoPermissaoEnum tipo) {
        List<Permissao> l = permissoes;
        for (Permissao permissao : l) {
            if (permissao.getRecurso() == rec) {
                if (permissao.getTipoPermissoes().contains(tipo)) {
                    return true;
                } else if (permissao.getTipoPermissoes().contains(TipoPermissaoEnum.TOTAL)) {
                    return true;
                } else if( permissao.getTipoPermissoes().contains(TipoPermissaoEnum.TOTAL_EXCETO_EXCLUIR)
                        && !tipo.equals(TipoPermissaoEnum.EXCLUIR)){
                    return true;

                }
            }
        }
        return false;
    }

    public RecursoTO getFromPermissao(int codigoRecurso, List<RecursoTO> recursos) {
        for (RecursoTO recurso : recursos) {
            if (recurso.getCodigo() != null && recurso.getCodigo().intValue() == codigoRecurso) {
                return recurso;
            }
        }
        return null;
    }
    
    public Permissao getPermissao(int codigoRecurso){
        for(Permissao permissao : getPermissoes()){
            if(codigoRecurso == permissao.getRecurso().getId().intValue()){
                return permissao;
            }
        }
        return null;
    }

    public GenericoTO getTipoFromRecurso(int tipo, RecursoTO recurso) {
        for (GenericoTO tipoGen : recurso.getTiposPermissoes()) {
            if (tipoGen.getCodigo().intValue() == tipo) {
                return tipoGen;
            }
        }
        return null;
    }

    private List<GenericoTO> getListaTiposPermissoes(TipoRecurso tipo) {
        List<GenericoTO> lista = new ArrayList<GenericoTO>();
        if(tipo.equals(TipoRecurso.FUNCIONALIDADE)){
            lista.add(new GenericoTO(TipoPermissaoEnum.TOTAL.getId(), TipoPermissaoEnum.TOTAL.getNome()));
            return lista;
        }
        for (TipoPermissaoEnum tipoPer : TipoPermissaoEnum.values()) {
            lista.add(new GenericoTO(tipoPer.getId(), tipoPer.getNome()));
        }
        return lista;
    }

    public void povoarRecursos() {
        setCategorias(new ArrayList<CategoriaRecursosTO>());
        Map<CategoriaRecursoEnum, List<RecursoTO>> mapaCategorias = new HashMap<CategoriaRecursoEnum, List<RecursoTO>>();
        for (RecursoEnum re : RecursoEnum.values()) {
            List<RecursoTO> lista = mapaCategorias.get(re.getCategoria());
            if (lista == null) {
                lista = new ArrayList<RecursoTO>();
                mapaCategorias.put(re.getCategoria(), lista);
            }
            lista.add(new RecursoTO(re.getId(), re.getNome(), re.getDescricao(), getListaTiposPermissoes(re.getTipo()), re.getTipo(), re.getTiposConjuntos()));
        }
        for (CategoriaRecursoEnum cre : mapaCategorias.keySet()) {
            CategoriaRecursosTO categoria = new CategoriaRecursosTO();
            categoria.setCategoria(cre.name());
            for(RecursoTO recurso : mapaCategorias.get(cre)){
                if(recurso.getTipo().equals(TipoRecurso.ENTIDADE)){
                    categoria.getRecursos().add(recurso);
                }else{
                    categoria.getFuncionalidades().add(recurso);
                }
            }
            Ordenacao.ordenarLista(categoria.getRecursos(), "label");
            if(categoria.getRecursos().size() > 1){
                categoria.getRecursos().add(0, new RecursoTO(null, "Todos", null, getListaTiposPermissoes(TipoRecurso.ENTIDADE), 
                        TipoRecurso.ENTIDADE, null));
            }
            Ordenacao.ordenarLista(categoria.getFuncionalidades(), "label");
            if(categoria.getFuncionalidades().size() > 1){
                categoria.getFuncionalidades().add(0, new RecursoTO(null, "Todos", null, getListaTiposPermissoes(TipoRecurso.FUNCIONALIDADE), 
                        TipoRecurso.FUNCIONALIDADE, null));
            }
            //iterar nas permissoes
            for (Permissao permissao : getPermissoes()) {
                RecursoTO recurso = getFromPermissao(permissao.getRecurso().getId(), categoria.getRecursos());
                if (recurso == null) {
                    recurso = getFromPermissao(permissao.getRecurso().getId(), categoria.getFuncionalidades());
                    if (recurso == null) {
                        continue;
                    }
                }
                //iterar nos tipos de permissao
                for (TipoPermissaoEnum tipo : permissao.getTipoPermissoes()) {
                    GenericoTO tipoFromRecurso = getTipoFromRecurso(tipo.getId(), recurso);
                    if (tipoFromRecurso == null) {
                        continue;
                    }
                    tipoFromRecurso.setEscolhido(true);

                }
            }
            verificarTodosMarcados(TipoPermissaoEnum.TOTAL.getId(), categoria.getFuncionalidades());
            verificarTodosMarcados(TipoPermissaoEnum.CONSULTAR.getId(), categoria.getRecursos());
            verificarTodosMarcados(TipoPermissaoEnum.INCLUIR.getId(), categoria.getRecursos());
            verificarTodosMarcados(TipoPermissaoEnum.EDITAR.getId(), categoria.getRecursos());
            verificarTodosMarcados(TipoPermissaoEnum.EXCLUIR.getId(), categoria.getRecursos());
            verificarTodosMarcados(TipoPermissaoEnum.TOTAL.getId(), categoria.getRecursos());
            verificarTodosMarcados(TipoPermissaoEnum.TOTAL_EXCETO_EXCLUIR.getId(), categoria.getRecursos());
            getCategorias().add(categoria);
        }
        Ordenacao.ordenarLista(getCategorias(), "categoria");
    }
    public void verificarTodosMarcados(Integer tipo, List<RecursoTO> recursos){
        if(recursos == null || recursos.isEmpty() || recursos.size() == 1){
            return;
        }
        boolean todos = true;
        for(int i = 1; i < recursos.size(); i++){
            if(!getTipoFromRecurso(tipo, recursos.get(i)).getEscolhido()){
                todos = false;
                break;
            }
        }
        getTipoFromRecurso(tipo, recursos.get(0)).setEscolhido(todos);
    }

    public String getTipo() {
        return tipo;
    }

    public void setTipo(String tipoColabSigla) {
        this.tipo = tipoColabSigla;
    }
}
