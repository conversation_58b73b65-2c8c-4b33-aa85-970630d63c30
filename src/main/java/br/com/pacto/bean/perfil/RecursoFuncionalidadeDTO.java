package br.com.pacto.bean.perfil;

/**
 * Created by paulo on 22/07/2019.
 */
public class RecursoFuncionalidadeDTO {

    private Boolean alterar_professor_treino = false;
    private Boolean visualizar_dados_contato_aluno = false;
    private Boolean ver_alunos_outras_carteiras = false;
    private <PERSON><PERSON><PERSON> ver_agenda_outros_professores = false;
    private <PERSON><PERSON>an ver_auditoria_entidade = false;
    private Boolean gestao_personal = false;
    private <PERSON>olean liberar_check_in = false;
    private Boolean acompanhar_personal = false;
    private Boolean gestao_creditos_personal = false;
    private Boolean editar_excluir_aula = false;
    private Boolean tornar_ficha_predefinida = false;
    private Boolean tela_prescricao_treino = false;
    private Boolean fazer_check_out = false;
    private Boolean colaboradores = false;
    private <PERSON>ole<PERSON> cadastro_aulas = false;
    private Boolean excluir_aulas_dia = false;
    private <PERSON><PERSON><PERSON> inserir_aluno = false;
    private Boolean excluir_aluno = false;
    private Boolean agenda = false;
    private Boolean inserir_aluno_aula_iniciada = false;
    private <PERSON><PERSON><PERSON> gestao_aula_cheia = false;
    private <PERSON><PERSON><PERSON> alterar_professor_aula = false;
    private <PERSON>olean alterar_professor_aula_iniciada = false;
    private Boolean ver_gestao_geral = false;
    private Boolean trocar_professor = false;
    private Boolean alterar_senha_usuarios = false;
    private Boolean permissao_excluir_aluno = false;
    private Boolean ver_bi_outros_professores = false;
    private Boolean editar_nome_atividade_ficha = false;
    private Boolean grafico_bi = false;
    private Boolean ranking_professores = false;
    private Boolean tvgestor = false;
    private Boolean benchmark = false;
    private Boolean tipo_benchmark = false;
    private Boolean wod = false;
    private Boolean ranking_wod = false;
    private Boolean alterar_horario_inicio_check_personal = false;
    private Boolean lancar_avalicao_retroativa = false;
    private Boolean cadastrar_tipo_wod = false;
    private Boolean excluir_anexo_aluno = false;
    private Boolean atividades_wod = false;
    private Boolean aparelhos_wod = false;
    private Boolean permissao_excluir_aluno_vinculado = false;
    private Boolean trocar_metodo_execucao = false;
    private Boolean modalidades = false;
    private Boolean serie = false;
    private Boolean ambientes = false;
    private Boolean local_retira_ficha = false;
    private Boolean visualizar_todas_aulas_app = false;
    private Boolean usuario_marcar_antecedencia = false;
    private Boolean tornar_programa_predefinido = false;
    private Boolean atribuir_programa_treino_pre_definido = false;
    private Boolean desvincular_usuario = false;
    private Boolean avaliacao_media_alunos = false;
    private Boolean visualizar_wod_outras_unidades = false;
    private Boolean bi_cross = false;
    private Boolean monitor = false;
    private Boolean atividades_graduacao = false;
    private Boolean ficha_tecnica_graduacao = false;
    private Boolean avaliacao_de_progresso_graduacao = false;
    private Boolean bi_agenda = false;
    private Boolean agenda_de_aulas = false;
    private Boolean agenda_de_servicos = false;
    private Boolean relatorios_agenda = false;
    private Boolean bi_avaliacao_fisica = false;
    private Boolean cadastros_avaliacao_fisica = false;
    private Boolean criar_avaliacao_fisica = false;
    private Boolean visualizar_avaliacao_fisica = false;
    private Boolean relatorios_treino = false;
    private Boolean cadastros_agenda = false;
    private Boolean utilizar_modulo_cross = false;
    private Boolean utilizar_modulo_graduacao = false;
    private Boolean utilizar_modulo_agenda = false;
    private Boolean utilizar_modulo_avaliacao_fisica = false;
    private Boolean enviar_treino_em_massa = false;
    private Boolean treino_em_casa = false;
    private Boolean cadastros_treino = false;
    private Boolean prescricao_de_treino = false;
    private Boolean prescricao_de_treino_por_ia = false;
    private Boolean editar_aulas_dia = false;
    private Boolean configuracoes_do_ranking = false;

    public RecursoFuncionalidadeDTO() {
    }

    public Boolean getAlterar_professor_treino() {
        return alterar_professor_treino;
    }

    public void setAlterar_professor_treino(Boolean alterar_professor_treino) {
        this.alterar_professor_treino = alterar_professor_treino;
    }

    public Boolean getVisualizar_dados_contato_aluno() {
        return visualizar_dados_contato_aluno;
    }

    public void setVisualizar_dados_contato_aluno(Boolean visualizar_dados_contato_aluno) {
        this.visualizar_dados_contato_aluno = visualizar_dados_contato_aluno;
    }

    public Boolean getVer_alunos_outras_carteiras() {
        return ver_alunos_outras_carteiras;
    }

    public void setVer_alunos_outras_carteiras(Boolean ver_alunos_outras_carteiras) {
        this.ver_alunos_outras_carteiras = ver_alunos_outras_carteiras;
    }

    public Boolean getVer_agenda_outros_professores() {
        return ver_agenda_outros_professores;
    }

    public void setVer_agenda_outros_professores(Boolean ver_agenda_outros_professores) {
        this.ver_agenda_outros_professores = ver_agenda_outros_professores;
    }

    public Boolean getVer_auditoria_entidade() {
        return ver_auditoria_entidade;
    }

    public void setVer_auditoria_entidade(Boolean ver_auditoria_entidade) {
        this.ver_auditoria_entidade = ver_auditoria_entidade;
    }

    public Boolean getGestao_personal() {
        return gestao_personal;
    }

    public void setGestao_personal(Boolean gestao_personal) {
        this.gestao_personal = gestao_personal;
    }

    public Boolean getLiberar_check_in() {
        return liberar_check_in;
    }

    public void setLiberar_check_in(Boolean liberar_check_in) {
        this.liberar_check_in = liberar_check_in;
    }

    public Boolean getAcompanhar_personal() {
        return acompanhar_personal;
    }

    public void setAcompanhar_personal(Boolean acompanhar_personal) {
        this.acompanhar_personal = acompanhar_personal;
    }

    public Boolean getGestao_creditos_personal() {
        return gestao_creditos_personal;
    }

    public void setGestao_creditos_personal(Boolean gestao_creditos_personal) {
        this.gestao_creditos_personal = gestao_creditos_personal;
    }

    public Boolean getEditar_excluir_aula() {
        return editar_excluir_aula;
    }

    public void setEditar_excluir_aula(Boolean editar_excluir_aula) {
        this.editar_excluir_aula = editar_excluir_aula;
    }

    public Boolean getTornar_ficha_predefinida() {
        return tornar_ficha_predefinida;
    }

    public void setTornar_ficha_predefinida(Boolean tornar_ficha_predefinida) {
        this.tornar_ficha_predefinida = tornar_ficha_predefinida;
    }

    public Boolean getTela_prescricao_treino() {
        return tela_prescricao_treino;
    }

    public void setTela_prescricao_treino(Boolean tela_prescricao_treino) {
        this.tela_prescricao_treino = tela_prescricao_treino;
    }

    public Boolean getFazer_check_out() {
        return fazer_check_out;
    }

    public void setFazer_check_out(Boolean fazer_check_out) {
        this.fazer_check_out = fazer_check_out;
    }

    public Boolean getColaboradores() {
        return colaboradores;
    }

    public void setColaboradores(Boolean colaboradores) {
        this.colaboradores = colaboradores;
    }

    public Boolean getCadastro_aulas() {
        return cadastro_aulas;
    }

    public void setCadastro_aulas(Boolean cadastro_aulas) {
        this.cadastro_aulas = cadastro_aulas;
    }

    public Boolean getExcluir_aulas_dia() {
        return excluir_aulas_dia;
    }

    public void setExcluir_aulas_dia(Boolean excluir_aulas_dia) {
        this.excluir_aulas_dia = excluir_aulas_dia;
    }

    public Boolean getInserir_aluno() {
        return inserir_aluno;
    }

    public void setInserir_aluno(Boolean inserir_aluno) {
        this.inserir_aluno = inserir_aluno;
    }

    public Boolean getExcluir_aluno() {
        return excluir_aluno;
    }

    public void setExcluir_aluno(Boolean excluir_aluno) {
        this.excluir_aluno = excluir_aluno;
    }

    public Boolean getAgenda() {
        return agenda;
    }

    public void setAgenda(Boolean agenda) {
        this.agenda = agenda;
    }

    public Boolean getInserir_aluno_aula_iniciada() {
        return inserir_aluno_aula_iniciada;
    }

    public void setInserir_aluno_aula_iniciada(Boolean inserir_aluno_aula_iniciada) {
        this.inserir_aluno_aula_iniciada = inserir_aluno_aula_iniciada;
    }

    public Boolean getGestao_aula_cheia() {
        return gestao_aula_cheia;
    }

    public void setGestao_aula_cheia(Boolean gestao_aula_cheia) {
        this.gestao_aula_cheia = gestao_aula_cheia;
    }

    public Boolean getAlterar_professor_aula() {
        return alterar_professor_aula;
    }

    public void setAlterar_professor_aula(Boolean alterar_professor_aula) {
        this.alterar_professor_aula = alterar_professor_aula;
    }

    public Boolean getAlterar_professor_aula_iniciada() {
        return alterar_professor_aula_iniciada;
    }

    public void setAlterar_professor_aula_iniciada(Boolean alterar_professor_aula_iniciada) {
        this.alterar_professor_aula_iniciada = alterar_professor_aula_iniciada;
    }

    public Boolean getVer_gestao_geral() {
        return ver_gestao_geral;
    }

    public void setVer_gestao_geral(Boolean ver_gestao_geral) {
        this.ver_gestao_geral = ver_gestao_geral;
    }

    public Boolean getTrocar_professor() {
        return trocar_professor;
    }

    public void setTrocar_professor(Boolean trocar_professor) {
        this.trocar_professor = trocar_professor;
    }

    public Boolean getAlterar_senha_usuarios() {
        return alterar_senha_usuarios;
    }

    public void setAlterar_senha_usuarios(Boolean alterar_senha_usuarios) {
        this.alterar_senha_usuarios = alterar_senha_usuarios;
    }

    public Boolean getPermissao_excluir_aluno() {
        return permissao_excluir_aluno;
    }

    public void setPermissao_excluir_aluno(Boolean permissao_excluir_aluno) {
        this.permissao_excluir_aluno = permissao_excluir_aluno;
    }

    public Boolean getVer_bi_outros_professores() {
        return ver_bi_outros_professores;
    }

    public void setVer_bi_outros_professores(Boolean ver_bi_outros_professores) {
        this.ver_bi_outros_professores = ver_bi_outros_professores;
    }

    public Boolean getEditar_nome_atividade_ficha() {
        return editar_nome_atividade_ficha;
    }

    public void setEditar_nome_atividade_ficha(Boolean editar_nome_atividade_ficha) {
        this.editar_nome_atividade_ficha = editar_nome_atividade_ficha;
    }

    public Boolean getGrafico_bi() {
        return grafico_bi;
    }

    public void setGrafico_bi(Boolean grafico_bi) {
        this.grafico_bi = grafico_bi;
    }

    public Boolean getRanking_professores() {
        return ranking_professores;
    }

    public void setRanking_professores(Boolean ranking_professores) {
        this.ranking_professores = ranking_professores;
    }

    public Boolean getTvgestor() {
        return tvgestor;
    }

    public void setTvgestor(Boolean tvgestor) {
        this.tvgestor = tvgestor;
    }

    public Boolean getBenchmark() {
        return benchmark;
    }

    public void setBenchmark(Boolean benchmark) {
        this.benchmark = benchmark;
    }

    public Boolean getTipo_benchmark() {
        return tipo_benchmark;
    }

    public void setTipo_benchmark(Boolean tipo_benchmark) {
        this.tipo_benchmark = tipo_benchmark;
    }

    public Boolean getWod() {
        return wod;
    }

    public void setWod(Boolean wod) {
        this.wod = wod;
    }

    public Boolean getRanking_wod() {
        return ranking_wod;
    }

    public void setRanking_wod(Boolean ranking_wod) {
        this.ranking_wod = ranking_wod;
    }

    public Boolean getAlterar_horario_inicio_check_personal() {
        return alterar_horario_inicio_check_personal;
    }

    public void setAlterar_horario_inicio_check_personal(Boolean alterar_horario_inicio_check_personal) {
        this.alterar_horario_inicio_check_personal = alterar_horario_inicio_check_personal;
    }

    public Boolean getLancar_avalicao_retroativa() {
        return lancar_avalicao_retroativa;
    }

    public void setLancar_avalicao_retroativa(Boolean lancar_avalicao_retroativa) {
        this.lancar_avalicao_retroativa = lancar_avalicao_retroativa;
    }

    public Boolean getCadastrar_tipo_wod() {
        return cadastrar_tipo_wod;
    }

    public void setCadastrar_tipo_wod(Boolean cadastrar_tipo_wod) {
        this.cadastrar_tipo_wod = cadastrar_tipo_wod;
    }

    public Boolean getExcluir_anexo_aluno() {
        return excluir_anexo_aluno;
    }

    public void setExcluir_anexo_aluno(Boolean excluir_anexo_aluno) {
        this.excluir_anexo_aluno = excluir_anexo_aluno;
    }

    public Boolean getAtividades_wod() {
        return atividades_wod;
    }

    public void setAtividades_wod(Boolean atividades_wod) {
        this.atividades_wod = atividades_wod;
    }

    public Boolean getAparelhos_wod() {
        return aparelhos_wod;
    }

    public void setAparelhos_wod(Boolean aparelhos_wod) {
        this.aparelhos_wod = aparelhos_wod;
    }

    public Boolean getPermissao_excluir_aluno_vinculado() {
        return permissao_excluir_aluno_vinculado;
    }

    public void setPermissao_excluir_aluno_vinculado(Boolean permissao_excluir_aluno_vinculado) {
        this.permissao_excluir_aluno_vinculado = permissao_excluir_aluno_vinculado;
    }

    public Boolean getTrocar_metodo_execucao() {
        return trocar_metodo_execucao;
    }

    public void setTrocar_metodo_execucao(Boolean trocar_metodo_execucao) {
        this.trocar_metodo_execucao = trocar_metodo_execucao;
    }

    public Boolean getModalidades() {
        return modalidades;
    }

    public void setModalidades(Boolean modalidades) {
        this.modalidades = modalidades;
    }

    public Boolean getSerie() {
        return serie;
    }

    public void setSerie(Boolean serie) {
        this.serie = serie;
    }

    public Boolean getAmbientes() {
        return ambientes;
    }

    public void setAmbientes(Boolean ambientes) {
        this.ambientes = ambientes;
    }

    public Boolean getLocal_retira_ficha() {
        return local_retira_ficha;
    }

    public void setLocal_retira_ficha(Boolean local_retira_ficha) {
        this.local_retira_ficha = local_retira_ficha;
    }

    public Boolean getVisualizar_todas_aulas_app() {
        return visualizar_todas_aulas_app;
    }

    public void setVisualizar_todas_aulas_app(Boolean visualizar_todas_aulas_app) {
        this.visualizar_todas_aulas_app = visualizar_todas_aulas_app;
    }

    public Boolean getUsuario_marcar_antecedencia() {
        return usuario_marcar_antecedencia;
    }

    public void setUsuario_marcar_antecedencia(Boolean usuario_marcar_antecedencia) {
        this.usuario_marcar_antecedencia = usuario_marcar_antecedencia;
    }

    public Boolean getTornar_programa_predefinido() {
        return tornar_programa_predefinido;
    }

    public void setTornar_programa_predefinido(Boolean tornar_programa_predefinido) {
        this.tornar_programa_predefinido = tornar_programa_predefinido;
    }

    public Boolean getAtribuir_programa_treino_pre_definido() {
        return atribuir_programa_treino_pre_definido;
    }

    public void setAtribuir_programa_treino_pre_definido(Boolean atribuir_programa_treino_pre_definido) {
        this.atribuir_programa_treino_pre_definido = atribuir_programa_treino_pre_definido;
    }

    public Boolean getDesvincular_usuario() {
        return desvincular_usuario;
    }

    public void setDesvincular_usuario(Boolean desvincular_usuario) {
        this.desvincular_usuario = desvincular_usuario;
    }

    public Boolean getAvaliacao_media_alunos() {
        return avaliacao_media_alunos;
    }

    public void setAvaliacao_media_alunos(Boolean avaliacao_media_alunos) {
        this.avaliacao_media_alunos = avaliacao_media_alunos;
    }
    public Boolean getVisualizar_wod_outras_unidades() {
        return visualizar_wod_outras_unidades;
    }

    public void setVisualizar_wod_outras_unidades(Boolean visualizar_wod_outras_unidades) {
        this.visualizar_wod_outras_unidades = visualizar_wod_outras_unidades;
    }

    public Boolean getBi_cross() {
        return bi_cross;
    }

    public void setBi_cross(Boolean bi_cross) {
        this.bi_cross = bi_cross;
    }

    public Boolean getMonitor() {
        return monitor;
    }

    public void setMonitor(Boolean monitor) {
        this.monitor = monitor;
    }

    public Boolean getAtividades_graduacao() {
        return atividades_graduacao;
    }

    public void setAtividades_graduacao(Boolean atividades_graduacao) {
        this.atividades_graduacao = atividades_graduacao;
    }

    public Boolean getFicha_tecnica_graduacao() {
        return ficha_tecnica_graduacao;
    }

    public void setFicha_tecnica_graduacao(Boolean ficha_tecnica_graduacao) {
        this.ficha_tecnica_graduacao = ficha_tecnica_graduacao;
    }

    public Boolean getAvaliacao_de_progresso_graduacao() {
        return avaliacao_de_progresso_graduacao;
    }

    public void setAvaliacao_de_progresso_graduacao(Boolean avaliacao_de_progresso_graduacao) {
        this.avaliacao_de_progresso_graduacao = avaliacao_de_progresso_graduacao;
    }

    public Boolean getBi_agenda() {
        return bi_agenda;
    }

    public void setBi_agenda(Boolean bi_agenda) {
        this.bi_agenda = bi_agenda;
    }

    public Boolean getAgenda_de_aulas() {
        return agenda_de_aulas;
    }

    public void setAgenda_de_aulas(Boolean agenda_de_aulas) {
        this.agenda_de_aulas = agenda_de_aulas;
    }

    public Boolean getAgenda_de_servicos() {
        return agenda_de_servicos;
    }

    public void setAgenda_de_servicos(Boolean agenda_de_servicos) {
        this.agenda_de_servicos = agenda_de_servicos;
    }

    public Boolean getRelatorios_agenda() {
        return relatorios_agenda;
    }

    public void setRelatorios_agenda(Boolean relatorios_agenda) {
        this.relatorios_agenda = relatorios_agenda;
    }

    public Boolean getBi_avaliacao_fisica() {
        return bi_avaliacao_fisica;
    }

    public void setBi_avaliacao_fisica(Boolean bi_avaliacao_fisica) {
        this.bi_avaliacao_fisica = bi_avaliacao_fisica;
    }

    public Boolean getCadastros_avaliacao_fisica() {
        return cadastros_avaliacao_fisica;
    }

    public void setCadastros_avaliacao_fisica(Boolean cadastros_avaliacao_fisica) {
        this.cadastros_avaliacao_fisica = cadastros_avaliacao_fisica;
    }

    public Boolean getCriar_avaliacao_fisica() {
        return criar_avaliacao_fisica;
    }

    public void setCriar_avaliacao_fisica(Boolean criar_avaliacao_fisica) {
        this.criar_avaliacao_fisica = criar_avaliacao_fisica;
    }

    public Boolean getVisualizar_avaliacao_fisica() {
        return visualizar_avaliacao_fisica;
    }

    public void setVisualizar_avaliacao_fisica(Boolean visualizar_avaliacao_fisica) {
        this.visualizar_avaliacao_fisica = visualizar_avaliacao_fisica;
    }

    public Boolean getRelatorios_treino() {
        return relatorios_treino;
    }

    public void setRelatorios_treino(Boolean relatorios_treino) {
        this.relatorios_treino = relatorios_treino;
    }

    public Boolean getCadastros_agenda() {
        return cadastros_agenda;
    }

    public void setCadastros_agenda(Boolean cadastros_agenda) {
        this.cadastros_agenda = cadastros_agenda;
    }

    public Boolean getUtilizar_modulo_cross() {
        return utilizar_modulo_cross;
    }

    public void setUtilizar_modulo_cross(Boolean utilizar_modulo_cross) {
        this.utilizar_modulo_cross = utilizar_modulo_cross;
    }

    public Boolean getUtilizar_modulo_graduacao() {
        return utilizar_modulo_graduacao;
    }

    public void setUtilizar_modulo_graduacao(Boolean utilizar_modulo_graduacao) {
        this.utilizar_modulo_graduacao = utilizar_modulo_graduacao;
    }

    public Boolean getUtilizar_modulo_agenda() {
        return utilizar_modulo_agenda;
    }

    public void setUtilizar_modulo_agenda(Boolean utilizar_modulo_agenda) {
        this.utilizar_modulo_agenda = utilizar_modulo_agenda;
    }

    public Boolean getUtilizar_modulo_avaliacao_fisica() {
        return utilizar_modulo_avaliacao_fisica;
    }

    public void setUtilizar_modulo_avaliacao_fisica(Boolean utilizar_modulo_avaliacao_fisica) {
        this.utilizar_modulo_avaliacao_fisica = utilizar_modulo_avaliacao_fisica;
    }

    public Boolean getEnviar_treino_em_massa() {
        return enviar_treino_em_massa;
    }

    public void setEnviar_treino_em_massa(Boolean enviar_treino_em_massa) {
        this.enviar_treino_em_massa = enviar_treino_em_massa;
    }

    public Boolean getTreino_em_casa() {
        return treino_em_casa;
    }

    public void setTreino_em_casa(Boolean treino_em_casa) {
        this.treino_em_casa = treino_em_casa;
    }

    public Boolean getCadastros_treino() {
        return cadastros_treino;
    }

    public void setCadastros_treino(Boolean cadastros_treino) {
        this.cadastros_treino = cadastros_treino;
    }

    public Boolean getPrescricao_de_treino() {
        return prescricao_de_treino;
    }

    public void setPrescricao_de_treino(Boolean prescricao_de_treino) {
        this.prescricao_de_treino = prescricao_de_treino;
    }

    public Boolean getPrescricao_de_treino_por_ia() {
        return prescricao_de_treino_por_ia;
    }

    public void setPrescricao_de_treino_por_ia(Boolean prescricao_de_treino_por_ia) {
        this.prescricao_de_treino_por_ia = prescricao_de_treino_por_ia;
    }

    public Boolean getEditar_aulas_dia() {
        return editar_aulas_dia;
    }

    public void setEditar_aulas_dia(Boolean editar_aulas_dia) {
        this.editar_aulas_dia = editar_aulas_dia;
    }

    public Boolean getConfiguracoes_do_ranking() {
        return configuracoes_do_ranking;
    }

    public void setConfiguracoes_do_ranking(Boolean configuracoes_do_ranking) {
        this.configuracoes_do_ranking = configuracoes_do_ranking;
    }
}
