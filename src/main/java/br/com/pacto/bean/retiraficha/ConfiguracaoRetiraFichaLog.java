package br.com.pacto.bean.retiraficha;

import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.retiraFicha.ConfiguracaoRetiraFichaJson;

import javax.persistence.*;
import java.util.Date;

@Entity
public class ConfiguracaoRetiraFichaLog {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;

    @ManyToOne(fetch = FetchType.EAGER)
    private Usuario usuario;

    @Column(name = "configuracao_retira_ficha", length=5000)
    private String configuracaoRetiraFicha;

    @Column(name = "data_alteracao")
    private Date dataAlteracao;



    @Column(name = "codigo_Configuracao_retira_ficha")
    private Integer codigoConfiguracaoRetiraFicha;

    public ConfiguracaoRetiraFichaLog() {
    }

    public ConfiguracaoRetiraFichaLog(Usuario usuario, ConfiguracaoRetiraFichaJson configuracaoRetiraFichaJson) {
        this.usuario = usuario;
        this.configuracaoRetiraFicha = configuracaoRetiraFichaJson.toJSON();
        this.dataAlteracao = new Date();
        this.codigoConfiguracaoRetiraFicha = configuracaoRetiraFichaJson.getCodigo();
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Usuario getUsuario() {
        return usuario;
    }

    public void setUsuario(Usuario usuario) {
        this.usuario = usuario;
    }

    public String getConfiguracaoRetiraFicha() {
        return configuracaoRetiraFicha;
    }

    public void setConfiguracaoRetiraFicha(String configuracaoRetiraFicha) {
        this.configuracaoRetiraFicha = configuracaoRetiraFicha;
    }

    public Date getDataAlteracao() {
        return dataAlteracao;
    }

    public void setDataAlteracao(Date dataAlteracao) {
        this.dataAlteracao = dataAlteracao;
    }
    public Integer getCodigoConfiguracaoRetiraFicha() {
        return codigoConfiguracaoRetiraFicha;
    }

    public void setCodigoConfiguracaoRetiraFicha(Integer codigoConfiguracaoRetiraFicha) {
        this.codigoConfiguracaoRetiraFicha = codigoConfiguracaoRetiraFicha;
    }
}
