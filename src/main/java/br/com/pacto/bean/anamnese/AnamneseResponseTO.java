package br.com.pacto.bean.anamnese;

import br.com.pacto.objeto.Uteis;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by ulisses on 04/08/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AnamneseResponseTO {

    private Integer id;
    private String nome;
    private Boolean ativa;
    private List<PerguntaResponseTO> perguntas = new ArrayList<>();
    private boolean emUso;
    private Integer tipo;
    private String datalancamento;
    public AnamneseResponseTO(){

    }

    public AnamneseResponseTO(Anamnese anamnese, boolean emUso){
        this.id = anamnese.getCodigo();
        this.nome = anamnese.getDescricao();
        this.ativa = anamnese.getAtiva();
        this.tipo = anamnese.getTipo() != null ? anamnese.getTipo().ordinal() : 0;
        this.perguntas.clear();
        if (anamnese.getPerguntas() != null) {
            for (PerguntaAnamnese pergunta : anamnese.getPerguntas()) {
                perguntas.add(new PerguntaResponseTO(pergunta.getPergunta(), pergunta.getCodigo()));
            }
        }
        this.datalancamento = Uteis.getData(anamnese.getDataLancamento());

        this.emUso = emUso;
    }

    public AnamneseResponseTO(Integer id, String nome, Boolean ativa, Integer tipo){
        this.id = id;
        this.nome = nome;
        this.ativa = ativa;
        this.tipo = tipo;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Boolean getAtiva() { return ativa; }

    public void setAtiva(Boolean ativa) { this.ativa = ativa; }

    public List<PerguntaResponseTO> getPerguntas() {
        return perguntas;
    }

    public void setPerguntas(List<PerguntaResponseTO> perguntas) {
        this.perguntas = perguntas;
    }

    public boolean isEmUso() {
        return emUso;
    }

    public void setEmUso(boolean emUso) {
        this.emUso = emUso;
    }

    public Integer getTipo() {
        return tipo;
    }

    public void setTipo(Integer tipo) {
        this.tipo = tipo;
    }

    public String getDatalancamento() {
        return datalancamento;
    }

    public void setDatalancamento(String datalancamento) {
        this.datalancamento = datalancamento;
    }
}
