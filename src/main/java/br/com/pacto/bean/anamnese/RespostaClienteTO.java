package br.com.pacto.bean.anamnese;

import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class RespostaClienteTO {
    private Integer codigo;
    private Integer codigoCliente;
    private Integer codigoRespostaClienteParq;
    private Integer codigoPergunta;
    private String resposta;
    private String obs;

    public RespostaClienteTO() {}

    public RespostaClienteTO(Integer codigo, Integer codigoCliente, Integer codigoRespostaClienteParq, Integer codigoPergunta, String resposta, String obs) {
        this.codigo = codigo;
        this.codigoCliente = codigoCliente;
        this.codigoRespostaClienteParq = codigoRespostaClienteParq;
        this.codigoPergunta = codigoPergunta;
        this.resposta = resposta;
        this.obs = obs;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getCodigoCliente() {
        return codigoCliente;
    }

    public void setCodigoCliente(Integer codigoCliente) {
        this.codigoCliente = codigoCliente;
    }

    public Integer getCodigoRespostaClienteParq() {
        return codigoRespostaClienteParq;
    }

    public void setCodigoRespostaClienteParq(Integer codigoRespostaClienteParq) {
        this.codigoRespostaClienteParq = codigoRespostaClienteParq;
    }

    public Integer getCodigoPergunta() {
        return codigoPergunta;
    }

    public void setCodigoPergunta(Integer codigoPergunta) {
        this.codigoPergunta = codigoPergunta;
    }

    public String getResposta() {
        return resposta;
    }

    public void setResposta(String resposta) {
        this.resposta = resposta;
    }

    public String getObs() {
        return obs;
    }

    public void setObs(String obs) {
        this.obs = obs;
    }
}
