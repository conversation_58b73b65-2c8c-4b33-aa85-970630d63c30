package br.com.pacto.bean.anamnese;

import br.com.pacto.enumerador.anamnese.TiposPerguntaEnum;

/**
 * <AUTHOR> 31/01/2019
 */
public enum TipoPerguntaDTOEnum {

    ESCOLHA_MULTIPLA,
    ESCOLHA_UNICA,
    SIM_NAO,
    TEXTO;

    public TiposPerguntaEnum getTiposPerguntaEnum() {
        switch (this) {
            case ESCOLHA_MULTIPLA: return TiposPerguntaEnum.MULTIPLA_ESCOLHA;
            case ESCOLHA_UNICA: return TiposPerguntaEnum.SIMPLES_ESCOLHA;
            case SIM_NAO: return TiposPerguntaEnum.SIM_NAO;
            case TEXTO: return TiposPerguntaEnum.TEXTUAL;
            default: return TiposPerguntaEnum.TEXTUAL;
        }
    }
}
