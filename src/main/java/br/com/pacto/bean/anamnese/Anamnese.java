package br.com.pacto.bean.anamnese;

import br.com.pacto.util.impl.UtilReflection;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static java.util.Objects.isNull;

/**
 * Created by Joao Alcides on 04/05/2017.
 */
@Entity
@Table
public class Anamnese {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private Boolean ativa = Boolean.TRUE;
    private Boolean parq = Boolean.FALSE;
    @Column(columnDefinition = "boolean default false")
    private Boolean parqpadraorj;
    private String descricao;
    @Enumerated(EnumType.ORDINAL)
    private TipoAnamneseEnum tipo = TipoAnamneseEnum.PADRAO;
    private Integer responsavelLancamento;
    @Temporal(TemporalType.TIMESTAMP)
    private Date dataLancamento;

    @Transient
    private Date respondidaEm;
    @Transient
    private List<PerguntaAnamnese> perguntas;

    public Anamnese() {
    }

    public Anamnese(Anamnese a) {
        this.ativa = a.ativa;
        this.codigo = a.codigo;
        this.parq = a.parq;
        this.descricao = a.descricao;
        this.tipo = a.tipo;
        this.responsavelLancamento = a.responsavelLancamento;
        this.dataLancamento = a.dataLancamento;
        this.respondidaEm = a.respondidaEm;
        this.perguntas = a.perguntas;
        this.parqpadraorj = a.parqpadraorj;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Boolean getAtiva() {
        return ativa;
    }

    public void setAtiva(Boolean ativa) {
        this.ativa = ativa;
    }

    public String getDescricao() {
        return descricao;
    }

    public Boolean getParq() {
        return parq;
    }

    public void setParq(Boolean parq) {
        this.parq = parq;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Integer getResponsavelLancamento() {
        return responsavelLancamento;
    }

    public void setResponsavelLancamento(Integer responsavelLancamento) {
        this.responsavelLancamento = responsavelLancamento;
    }

    public Date getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public List<PerguntaAnamnese> getPerguntas() {
        return perguntas;
    }

    public void setPerguntas(List<PerguntaAnamnese> perguntas) {
        this.perguntas = perguntas;
    }

    public TipoAnamneseEnum getTipo() {
        return tipo;
    }

    public void setTipo(TipoAnamneseEnum tipo) {
        this.tipo = tipo;
    }

    public Date getRespondidaEm() {
        return respondidaEm;
    }

    public void setRespondidaEm(Date respondidaEm) {
        this.respondidaEm = respondidaEm;
    }

    public String getDescricaoParaLog(Anamnese a2) {
        try {
            StringBuilder log = new StringBuilder();
            log.append(UtilReflection.difference(this, a2));

            if (isNull(a2)) {
                log.append(ComparePerguntas(this.getPerguntas(), null));
                return log.toString();
            }
            log.append(ComparePerguntas(this.getPerguntas(), a2.getPerguntas()));
            return log.toString();

        } catch (Exception ex) {
            ex.printStackTrace();
            return "ERRO gerar log";
        }
    }

    private String ComparePerguntas(List<PerguntaAnamnese> listaPerguntas1, List<PerguntaAnamnese> listaPerguntas2) {

        StringBuilder log = new StringBuilder();

        int tamanhoLista1 = listaPerguntas1 != null ? listaPerguntas1.size() : 0;
        int tamanhoLista2 = listaPerguntas2 != null ? listaPerguntas2.size() : 0;
        int tamanhoMaximo = Math.max(tamanhoLista1, tamanhoLista2);

        for (int contador = 0; contador < tamanhoMaximo; contador++) {
            Pergunta pergunta1 = contador < tamanhoLista1 ? listaPerguntas1.get(contador).getPergunta() : null;
            Pergunta pergunta2 = contador < tamanhoLista2 ? listaPerguntas2.get(contador).getPergunta() : null;

            List<String> respostas1 = contador < tamanhoLista1 ? listaPerguntas1.get(contador).getRespostas() : null;
            List<String> respostas2 = contador < tamanhoLista2 ? listaPerguntas2.get(contador).getRespostas() : null;

            log.append(UtilReflection.difference(pergunta1, pergunta2));
            log.append(CompareRespostas(respostas1, respostas2));
            log.append(
                    compareOpcoes(
                            pergunta1 == null ? null : pergunta1.getOpcoes(),
                            pergunta2 == null ? null : pergunta2.getOpcoes()
                    )
            );
        }
        return log.toString();
    }
    private String CompareRespostas(List<String> r1, List<String> r2) {

        StringBuilder log = new StringBuilder();

        int tamanhoLista1 = r1 != null ? r1.size() : 0;
        int tamanhoLista2 = r2 != null ? r2.size() : 0;

        int tamanhoMaximo = Math.max(tamanhoLista1, tamanhoLista2);

        for (int contador = 0; contador < tamanhoMaximo; contador++) {
            String elementoLista1 = contador < tamanhoLista1 ? r1.get(contador) : null;
            String elementoLista2 = contador < tamanhoLista2 ? r2.get(contador) : null;

            log.append(UtilReflection.difference(elementoLista1, elementoLista2));
        }

        return log.toString();

    }

    private String compareOpcoes(List<OpcaoPergunta> opcoes1, List<OpcaoPergunta> opcoes2) {
        StringBuilder log = new StringBuilder();

        int tamanhoLista1 = opcoes1 != null ? opcoes1.size() : 0;
        int tamanhoLista2 = opcoes2 != null ? opcoes2.size() : 0;
        int tamanhoMaximo = Math.max(tamanhoLista1, tamanhoLista2);

        for (int contador = 0; contador < tamanhoMaximo; contador++) {
            OpcaoPergunta elementoLista1 = contador < tamanhoLista1 ? opcoes1.get(contador) : null;
            OpcaoPergunta elementoLista2 = contador < tamanhoLista2 ? opcoes2.get(contador) : null;
            log.append(UtilReflection.difference(elementoLista1, elementoLista2));
        }

        return log.toString();
    }

    public Boolean getParqpadraorj() { return parqpadraorj; }

    public void setParqpadraorj(Boolean parqpadraorj) { this.parqpadraorj = parqpadraorj; }
}


