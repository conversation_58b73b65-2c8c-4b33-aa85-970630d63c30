package br.com.pacto.bean.anamnese;

public enum Movimento3DEnum {
    cadeia_anterior(TipoMovimento3DEnum.mobilidade),
    cadeia_posterior(TipoMovimento3DEnum.mobilidade),
    cadeia_lateral(TipoMovimento3DEnum.mobilidade),
    cadeia_rotacional(TipoMovimento3DEnum.mobilidade),
    controle(TipoMovimento3DEnum.estabilidade_controle),
    cadeia_fechamento(TipoMovimento3DEnum.estabilidade_controle),
    cadeia_abertura(TipoMovimento3DEnum.estabilidade_controle);

    Movimento3DEnum(TipoMovimento3DEnum tipo) {
        this.tipo = tipo;
    }

    private TipoMovimento3DEnum tipo;

    public TipoMovimento3DEnum getTipo() {
        return tipo;
    }
}
