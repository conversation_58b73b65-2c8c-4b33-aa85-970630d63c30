package br.com.pacto.bean.anamnese;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * Created by ulisses on 05/08/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OpcaoPerguntaResponseTO {

    private Integer id;
    private String nome;

    public OpcaoPerguntaResponseTO(OpcaoPergunta opcao){
        this.id = opcao.getCodigo();
        this.nome = opcao.getOpcao();
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }
}
