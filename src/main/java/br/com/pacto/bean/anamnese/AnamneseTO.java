package br.com.pacto.bean.anamnese;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by ulisses on 04/08/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AnamneseTO {

    private Integer id;
    private String nome;
    private boolean ativa;
    private List<PerguntaTO> perguntas = new ArrayList<PerguntaTO>();
    private TipoAnamneseEnum tipo;
    private Date datalancamento;

    public AnamneseTO(){

    }

    public AnamneseTO(Anamnese anamnese){
        this.id = anamnese.getCodigo();
        this.nome = anamnese.getDescricao();
        this.ativa = anamnese.getAtiva();
        this.perguntas = new ArrayList<PerguntaTO>();
        for(PerguntaAnamnese p : anamnese.getPerguntas()){
            perguntas.add(new PerguntaTO(p.getPergunta()));
        }
        this.tipo = anamnese.getTipo();
    }

    public AnamneseTO(Integer id, String nome){
        this.id = id;
        this.nome = nome;

    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public boolean isAtiva() {
        return ativa;
    }

    public void setAtiva(boolean ativa) {
        this.ativa = ativa;
    }

    public List<PerguntaTO> getPerguntas() {
        return perguntas;
    }

    public void setPerguntas(List<PerguntaTO> perguntas) {
        this.perguntas = perguntas;
    }

    public TipoAnamneseEnum getTipo() {
        return tipo;
    }

    public void setTipo(TipoAnamneseEnum tipo) {
        this.tipo = tipo;
    }

    public Date getDatalancamento() {return datalancamento;}

    public void setDatalancamento(Date datalancamento) {this.datalancamento = datalancamento;}
}
