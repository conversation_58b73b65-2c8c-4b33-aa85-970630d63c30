package br.com.pacto.bean.anamnese;

public enum ResultadoMovimentoEnum {
    FRACA(0.0,6.0),
    MEDIA(6.1,10.0),
    BOA(10.1,14.0),
    MUITO_BOA(14.1,16.0),
    EXCELENTE(16.1,18.0);


    ResultadoMovimentoEnum(Double minimo, Double maximo) {
        this.minimo = minimo;
        this.maximo = maximo;
    }

    private Double minimo;
    private Double maximo;

    public Double getMinimo() {
        return minimo;
    }

    public Double getMaximo() {
        return maximo;
    }

    public static ResultadoMovimentoEnum getResult(Double r){
        for(ResultadoMovimentoEnum resultadoMovimentoEnum : values()){
            if(resultadoMovimentoEnum.getMinimo() <= r
                    && resultadoMovimentoEnum.getMaximo() >= r){
                return resultadoMovimentoEnum;
            }
        }
        return null;
    }

    public String getTitle(){
        return "mov".concat(name());
    }
}
