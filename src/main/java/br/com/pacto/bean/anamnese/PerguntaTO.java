package br.com.pacto.bean.anamnese;

import br.com.pacto.util.UteisValidacao;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by ulisses on 05/08/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PerguntaTO {

    private Integer id;
    private String pergunta;
    private TipoPerguntaDTOEnum tipo;
    private List<OpcaoPerguntaTO> opcoes;
    private Integer ordem;

    public PerguntaTO() {

    }
    public PerguntaTO(Integer codigo) {
        this.id = codigo;
    }
    public PerguntaTO(Pergunta p) {
        this.id = p.getCodigo();
        this.pergunta = p.getDescricao();
        this.tipo = p.getTipoPergunta().getTipoPerguntaDTOEnum();
        this.ordem = p.getOrdem();
        if(!UteisValidacao.emptyList(p.getOpcoes())){
            this.opcoes = new ArrayList<OpcaoPerguntaTO>();
            for(OpcaoPergunta op : p.getOpcoes()){
                OpcaoPerguntaTO opt = new OpcaoPerguntaTO();
                opt.setId(op.getCodigo());
                opt.setNome(op.getOpcao());
                this.opcoes.add(opt);
            }
        }

    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getPergunta() {
        return pergunta;
    }

    public void setPergunta(String pergunta) {
        this.pergunta = pergunta;
    }

    public TipoPerguntaDTOEnum getTipo() {
        return tipo;
    }

    public void setTipo(TipoPerguntaDTOEnum tipo) {
        this.tipo = tipo;
    }

    public List<OpcaoPerguntaTO> getOpcoes() {
        return opcoes;
    }

    public void setOpcoes(List<OpcaoPerguntaTO> opcoes) {
        this.opcoes = opcoes;
    }

    public Integer getOrdem() {
        return ordem;
    }

    public void setOrdem(Integer ordem) {
        this.ordem = ordem;
    }
}
