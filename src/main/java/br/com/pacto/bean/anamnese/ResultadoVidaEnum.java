package br.com.pacto.bean.anamnese;

public enum ResultadoVidaEnum {
    FRACA(0,5),
    MEDIA(6,8),
    B<PERSON>(9,11),
    MUITO_BOA(12,13),
    EXCELENTE(14,15);


    ResultadoVidaEnum(Integer minimo, Integer maximo) {
        this.minimo = minimo;
        this.maximo = maximo;
    }

    private Integer minimo;
    private Integer maximo;

    public Integer getMinimo() {
        return minimo;
    }

    public Integer getMaximo() {
        return maximo;
    }
    public static ResultadoVidaEnum getResult(Integer r){
        for(ResultadoVidaEnum resultadoVida : values()){
            if(resultadoVida.getMinimo() <= r
                    && resultadoVida.getMaximo() >= r){
                return resultadoVida;
            }
        }
        return null;
    }
    public String getTitle(){
        return "vida".concat(name());
    }

}
