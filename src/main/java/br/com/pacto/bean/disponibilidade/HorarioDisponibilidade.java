package br.com.pacto.bean.disponibilidade;

import br.com.pacto.bean.aula.Ambiente;
import br.com.pacto.bean.professor.ProfessorSintetico;

import javax.persistence.*;
import java.io.Serializable;

@Entity
public class HorarioDisponibilidade implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    @ManyToOne
    @JoinColumn(name = "disponibilidade", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private Disponibilidade disponibilidade;
    @ManyToOne
    private ProfessorSintetico professor;
    @ManyToOne
    private Ambiente ambiente;
    private String horaInicial;
    private String horaFinal;
    private String diaSemana;
    private Boolean permieAgendarAppTreino;
    private Boolean apenasAlunosCarteira;
    private Boolean ativo;
    @Transient
    private Boolean escolhido = Boolean.TRUE;
    @Transient
    private Integer codigoAgendamentoOriginou;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Disponibilidade getDisponibilidade() {
        return disponibilidade;
    }

    public void setDisponibilidade(Disponibilidade disponibilidade) {
        this.disponibilidade = disponibilidade;
    }

    public ProfessorSintetico getProfessor() {
        return professor;
    }

    public void setProfessor(ProfessorSintetico professor) {
        this.professor = professor;
    }

    public Ambiente getAmbiente() {
        return ambiente;
    }

    public void setAmbiente(Ambiente ambiente) {
        this.ambiente = ambiente;
    }

    public String getHoraInicial() {
        return horaInicial;
    }

    public void setHoraInicial(String horaInicial) {
        this.horaInicial = horaInicial;
    }

    public String getHoraFinal() {
        return horaFinal;
    }

    public void setHoraFinal(String horaFinal) {
        this.horaFinal = horaFinal;
    }

    public String getDiaSemana() {
        return diaSemana;
    }

    public void setDiaSemana(String diaSemana) {
        this.diaSemana = diaSemana;
    }

    public Boolean getPermieAgendarAppTreino() {
        return permieAgendarAppTreino;
    }

    public void setPermieAgendarAppTreino(Boolean permieAgendarAppTreino) {
        this.permieAgendarAppTreino = permieAgendarAppTreino;
    }

    public Boolean getApenasAlunosCarteira() {
        if (apenasAlunosCarteira == null) {
            apenasAlunosCarteira = Boolean.FALSE;
        }
        return apenasAlunosCarteira;
    }

    public void setApenasAlunosCarteira(Boolean apenasAlunosCarteira) {
        this.apenasAlunosCarteira = apenasAlunosCarteira;
    }

    public Boolean getAtivo() {
        return ativo;
    }

    public void setAtivo(Boolean ativo) {
        this.ativo = ativo;
    }

    public Boolean getEscolhido() {
        return escolhido;
    }

    public void setEscolhido(Boolean escolhido) {
        this.escolhido = escolhido;
    }

    public Integer getCodigoDisponibilidade() {
        return disponibilidade.getCodigo();
    }

    public Integer getCodigoAgendamentoOriginou() {
        return codigoAgendamentoOriginou;
    }

    public void setCodigoAgendamentoOriginou(Integer codigoAgendamentoOriginou) {
        this.codigoAgendamentoOriginou = codigoAgendamentoOriginou;
    }
}
