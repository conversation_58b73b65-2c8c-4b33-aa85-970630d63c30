package br.com.pacto.bean.professor;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.LinkedHashMap;
import java.util.Map;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class IndicadorAtividadeProfessorAcumuladoVO {
    private Integer professorId;
    private String nome;
    private LinkedHashMap<String, Integer> indicadores;

    public Integer getProfessorId() {
        return professorId;
    }

    public void setProfessorId(Integer professorId) {
        this.professorId = professorId;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public LinkedHashMap<String, Integer> getIndicadores() {
        return indicadores;
    }

    public void setIndicadores(LinkedHashMap<String, Integer> indicadores) {
        this.indicadores = indicadores;
    }
}
