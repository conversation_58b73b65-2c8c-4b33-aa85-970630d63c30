package br.com.pacto.bean.professor;

import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class ProfessorDTO {

    private Integer codigoProfessorsintetico;
    private String nome;
    private boolean ativo = true;
    private Integer codigoPessoa;
    private Integer codigoColaborador;
    private boolean professorTW = false;
    private Integer empresaTW;
    private Integer empresaZW;

    public ProfessorDTO() {
    }

    public ProfessorDTO(ProfessorSintetico obj) {
        this.codigoProfessorsintetico = obj.getCodigo();
        this.nome = obj.getNome();
        this.ativo = obj.isAtivo();
        this.codigoPessoa = obj.getCodigoPessoa();
        this.codigoColaborador = obj.getCodigoColaborador();
        this.professorTW = obj.getProfessorTW() != null && obj.getProfessorTW();
        this.empresaTW = obj.getEmpresa().getCodigo();
        this.empresaZW = obj.getEmpresa().getCodZW();
    }

    public Integer getCodigoProfessorsintetico() {
        return codigoProfessorsintetico;
    }

    public void setCodigoProfessorsintetico(Integer codigoProfessorsintetico) {
        this.codigoProfessorsintetico = codigoProfessorsintetico;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public boolean isAtivo() {
        return ativo;
    }

    public void setAtivo(boolean ativo) {
        this.ativo = ativo;
    }

    public Integer getCodigoPessoa() {
        return codigoPessoa;
    }

    public void setCodigoPessoa(Integer codigoPessoa) {
        this.codigoPessoa = codigoPessoa;
    }

    public Integer getCodigoColaborador() {
        return codigoColaborador;
    }

    public void setCodigoColaborador(Integer codigoColaborador) {
        this.codigoColaborador = codigoColaborador;
    }

    public boolean isProfessorTW() {
        return professorTW;
    }

    public void setProfessorTW(boolean professorTW) {
        this.professorTW = professorTW;
    }

    public Integer getEmpresaTW() {
        return empresaTW;
    }

    public void setEmpresaTW(Integer empresaTW) {
        this.empresaTW = empresaTW;
    }

    public Integer getEmpresaZW() {
        return empresaZW;
    }

    public void setEmpresaZW(Integer empresaZW) {
        this.empresaZW = empresaZW;
    }
}
