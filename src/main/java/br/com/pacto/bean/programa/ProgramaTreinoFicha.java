/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.programa;

import br.com.pacto.bean.ficha.CategoriaFicha;
import br.com.pacto.bean.ficha.Ficha;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.util.enumeradores.DiasSemana;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.persistence.ElementCollection;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.ManyToOne;
import javax.persistence.Transient;

import br.com.pacto.util.impl.UtilReflection;
import com.fasterxml.jackson.annotation.JsonIgnore;

/**
 *
 * <AUTHOR>
 */
@Entity
public class ProgramaTreinoFicha implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    @JsonIgnore
    @ManyToOne(fetch = FetchType.LAZY)
    private ProgramaTreino programa;
    @ManyToOne(fetch = FetchType.EAGER)
    private Ficha ficha;


    private TipoExecucaoEnum tipoExecucao = TipoExecucaoEnum.ALTERNADO;
    @ElementCollection(fetch = FetchType.EAGER)
    private List<String> diaSemana = new ArrayList<String>();//<List>DiasSemana.class
    private Integer versao;
    @Transient
    private Date ultimaVezTreinada;
    @Transient
    private ProgramaTreinoFicha fichaAntesAlteracao;

    public ProgramaTreinoFicha manterAntesAlteracao() {
        fichaAntesAlteracao = new ProgramaTreinoFicha();
        fichaAntesAlteracao.setFicha(new Ficha());
        fichaAntesAlteracao.getFicha().setNome(this.getFicha().getNome());
        fichaAntesAlteracao.getFicha().setMensagemAluno(this.getFicha().getMensagemAluno());
        fichaAntesAlteracao.getFicha().setCategoria(this.getFicha().getCategoria() == null ? null : new CategoriaFicha(this.getFicha().getCategoria().getNome()));
        fichaAntesAlteracao.setVersao(this.getVersao());
        fichaAntesAlteracao.setDiaSemana(this.getDiaSemana());
        fichaAntesAlteracao.setTipoExecucao(this.getTipoExecucao());
        return fichaAntesAlteracao;
    }

    public String getNomeFicha() {
        try {
            return getFicha().getNome();
        }catch (Exception e){
            e.printStackTrace();
            return e.getMessage();
        }

    }

    public ProgramaTreinoFicha() {
    }

    public ProgramaTreinoFicha(ProgramaTreino programa, Ficha ficha, TipoExecucaoEnum tipoExecucao, List<String> diaSemana) {
        this.programa = programa;
        this.ficha = ficha;
        this.tipoExecucao = tipoExecucao;
        this.diaSemana = diaSemana;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public ProgramaTreino getPrograma() {
        return programa;
    }

    public void setPrograma(ProgramaTreino programa) {
        this.programa = programa;
    }

    public Ficha getFicha() {
        return ficha;
    }

    public void setFicha(Ficha ficha) {
        this.ficha = ficha;
    }

    public TipoExecucaoEnum getTipoExecucao() {
        if (tipoExecucao == null) {
            tipoExecucao = TipoExecucaoEnum.ALTERNADO;
        }
        return tipoExecucao;
    }

    public void setTipoExecucao(TipoExecucaoEnum tipoExecucao) {
        this.tipoExecucao = tipoExecucao;
    }

    public Integer getVersao() {
        return versao == null ? 0 : versao;
    }

    public void setVersao(Integer versao) {
        this.versao = versao;
    }

    public List<String> getDiaSemana() {
        if (diaSemana == null) {
            diaSemana = new ArrayList<>();
        }
        return diaSemana;
    }
    
    public String getDiaSemanaAsString() {
        if (diaSemana != null) {
            return Uteis.splitFromList("codigo", DiasSemana.getList(diaSemana), false);
        }
        return "";
    }

    public void setDiaSemana(List<String> diaSemana) {
        this.diaSemana = diaSemana;
    }

    public Date getUltimaVezTreinada() {
        return ultimaVezTreinada;
    }

    public void setUltimaVezTreinada(Date ultimaVezTreinada) {
        this.ultimaVezTreinada = ultimaVezTreinada;
    }

    public ProgramaTreinoFicha getFichaAntesAlteracao() {
        return fichaAntesAlteracao;
    }

    public void setFichaAntesAlteracao(ProgramaTreinoFicha fichaAntesAlteracao) {
        this.fichaAntesAlteracao = fichaAntesAlteracao;
    }

    public boolean isFichaDeHoje(Date diaAtual) {
        if (diaSemana != null && !diaSemana.isEmpty()) {
            for (final String dia : diaSemana) {
                DiasSemana ds = DiasSemana.getDiaSemana(dia);
                if (ds.equals_ISO8601(diaAtual)) {
                    return true;
                }
            }
        }
        return false;
    }

    public String getDescricaoParaLog(ProgramaTreinoFicha programaTreinoFichaAnterior) {
        try {
            StringBuilder log = new StringBuilder();
            log.append(UtilReflection.difference(this, programaTreinoFichaAnterior));
            return log.toString();
        } catch (Exception ex) {
            ex.printStackTrace();
            return "ERRO gerar log";
        }
    }

}
