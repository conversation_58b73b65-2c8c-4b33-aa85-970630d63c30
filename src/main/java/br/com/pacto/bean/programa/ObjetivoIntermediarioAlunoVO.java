package br.com.pacto.bean.programa;

import br.com.pacto.bean.usuario.StatusObjetivoAlunoEnum;
import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class ObjetivoIntermediarioAlunoVO {

    private Integer codigo;
    private String objetivoPredefinido;
    private String dataInicio;
    private String dataFinal;
    private String descricao;
    private StatusObjetivoAlunoEnum status;
    private String categoria;
    private String objetivoFinal;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getObjetivoPredefinido() {
        return objetivoPredefinido;
    }

    public void setObjetivoPredefinido(String objetivoPredefinido) {
        this.objetivoPredefinido = objetivoPredefinido;
    }

    public String getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(String dataInicio) {
        this.dataInicio = dataInicio;
    }

    public String getDataFinal() {
        return dataFinal;
    }

    public void setDataFinal(String dataFinal) {
        this.dataFinal = dataFinal;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public StatusObjetivoAlunoEnum getStatus() {
        return status;
    }

    public void setStatus(StatusObjetivoAlunoEnum status) {
        this.status = status;
    }

    public String getCategoria() {
        return categoria;
    }

    public void setCategoria(String categoria) {
        this.categoria = categoria;
    }

    public String getObjetivoFinal() {
        return objetivoFinal;
    }

    public void setObjetivoFinal(String objetivoFinal) {
        this.objetivoFinal = objetivoFinal;
    }
}
