package br.com.pacto.bean.programa;

import br.com.pacto.bean.bi.DashboardBI;
import br.com.pacto.bean.professor.ProfessorSintetico;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.gestao.BITreinoTreinamentoDTO;
import com.fasterxml.jackson.annotation.JsonInclude;


/**
 * Created by ulisses on 13/08/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ProfessorBIResponseTO {

    private ProfessorResponseTO professor;
    private BITreinoTreinamentoDTO biTreinoTreinamentoDTO;

    public ProfessorBIResponseTO() {
    }

    public ProfessorResponseTO getProfessor() {
        return professor;
    }

    public void setProfessor(ProfessorR<PERSON><PERSON>nseTO professor) {
        this.professor = professor;
    }

    public BITreinoTreinamentoDTO getBiTreinoTreinamentoDTO() {
        return biTreinoTreinamentoDTO;
    }

    public void setBiTreinoTreinamentoDTO(BITreinoTreinamentoDTO biTreinoTreinamentoDTO) {
        this.biTreinoTreinamentoDTO = biTreinoTreinamentoDTO;
    }
}
