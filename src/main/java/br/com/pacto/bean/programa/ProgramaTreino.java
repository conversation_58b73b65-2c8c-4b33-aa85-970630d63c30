/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.programa;

import br.com.pacto.bean.agenda.LembreteAgendamento;
import br.com.pacto.bean.annotation.Controle;
import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.nivel.Nivel;
import br.com.pacto.bean.professor.Professor<PERSON><PERSON><PERSON>;
import br.com.pacto.bean.sincronizacao.HistoricoRevisao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.bean.GenericoTO;
import java.io.Serializable;
import java.util.*;
import javax.persistence.*;

import br.com.pacto.util.impl.UtilReflection;
import com.fasterxml.jackson.annotation.JsonIgnore;

import static java.util.Objects.isNull;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

/**
 *
 * <AUTHOR>
 */
@Entity
@Controle(nome = "ProgramaControle")
public class ProgramaTreino implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private String nome;
    @Temporal(TemporalType.TIMESTAMP)
    private Date dataLancamento;
    @Temporal(TemporalType.TIMESTAMP)
    private Date dataInicio;
    @Temporal(TemporalType.TIMESTAMP)
    private Date dataProximaRevisao;
    @Temporal(TemporalType.TIMESTAMP)
    private Date dataTerminoPrevisto;
    private Integer diasPorSemana;
    private Integer totalAulasPrevistas;
    private ProgramaSituacaoEnum situacao = ProgramaSituacaoEnum.ATIVO;
    private Integer versao;
    private Integer codigoColaborador;
    private Boolean preDefinido;
    private Boolean treinoRapido;
    private Boolean isGeradoPorIA = Boolean.FALSE;
    private String workoutIDIA; // guardar o id do programa que foi gerado na IA
    private Boolean emRevisaoProfessor = Boolean.FALSE;
    private Integer nrTreinosRealizados;
    private Integer programaTreinoRenovacao;
    private Integer programaTreinoRenovado;
    @ManyToOne
    private Nivel nivel;
    private String genero;
    private String idOperacaoEmMassa;

    @ElementCollection(fetch = FetchType.EAGER)
    private Set<RestricoesEnum> restricoesTreino = new HashSet<RestricoesEnum>();
    @Temporal(TemporalType.TIMESTAMP)
    private Date dataRenovacao;
    private Integer origem;
    @ManyToOne
    @JsonIgnore
    private ClienteSintetico cliente;
    @JsonIgnore
    @ManyToOne
    private ProfessorSintetico professorMontou;
    @ManyToOne
    private ProfessorSintetico professorCarteira;
    @OneToMany(cascade = {CascadeType.PERSIST, CascadeType.MERGE, CascadeType.REMOVE}, fetch = FetchType.LAZY, mappedBy = "programa", targetEntity = Compromisso.class)
    private List<Compromisso> compromissos = new ArrayList<Compromisso>();
    @JsonIgnore
    @OneToMany(cascade = {CascadeType.PERSIST, CascadeType.MERGE, CascadeType.REMOVE}, fetch = FetchType.LAZY, mappedBy = "programa", targetEntity = ProgramaTreinoFicha.class)
    private List<ProgramaTreinoFicha> programaFichas = new ArrayList<ProgramaTreinoFicha>();
    @OneToMany(cascade = {CascadeType.PERSIST, CascadeType.MERGE, CascadeType.REMOVE}, fetch = FetchType.LAZY, mappedBy = "programa", targetEntity = ObjetivoPrograma.class)
    private List<ObjetivoPrograma> objetivos = new ArrayList<ObjetivoPrograma>();
    @Transient
    private ProgramaTreino programaAntesAlteracao;
    @Transient
    private List<GenericoTO> objetivosPredef = new ArrayList<GenericoTO>();
    @Transient
    private List<GenericoTO> restricoes = new ArrayList<GenericoTO>();
    @Transient
    private List<HistoricoRevisaoProgramaTreino> historicoRevicoes;
    @OneToMany(mappedBy = "programa", cascade = CascadeType.REMOVE)
    private List<HistoricoRevisaoProgramaTreino> historicosRevisoesJPA;
    @Temporal(TemporalType.TIMESTAMP)
    private Date dataUltimaAtualizacao;
    @Transient
    private List<HistoricoRevisao> historicoRevicoesSincronizacao;
    @Transient
    private Boolean usarNovaMontagemFicha;



    public void manterAntesAlteracao() {
        programaAntesAlteracao = new ProgramaTreino();
        programaAntesAlteracao.setNome(this.nome);
        programaAntesAlteracao.setDataInicio(this.dataInicio);
        programaAntesAlteracao.setDataProximaRevisao(this.dataProximaRevisao);
        programaAntesAlteracao.setDataTerminoPrevisto(this.dataTerminoPrevisto);
        programaAntesAlteracao.setDiasPorSemana(this.diasPorSemana);
        programaAntesAlteracao.setVersao(this.versao);
    }

    public ProgramaTreino(ProgramaTreino programaTreino, ProfessorSintetico professorCriador) {
        dataLancamento = Calendario.hoje();
        nome = "NOVOPROG " + Calendario.getData(Calendario.hoje(), "ddMMyyHHmm");
        dataInicio = Uteis.somarDias(programaTreino.dataTerminoPrevisto, 1);
        if (Calendario.menor(dataInicio, Calendario.hoje())) {
            dataInicio = Calendario.hoje();
        }

        dataProximaRevisao = Calendario.proximoDeveSerUtil(dataInicio, 7);
        if (programaTreino.getDataInicio() != null && programaTreino.getDataTerminoPrevisto() != null) {
            long duracaoEmDias = (programaTreino.getDataTerminoPrevisto().getTime() - programaTreino.getDataInicio().getTime()) / (1000 * 60 * 60 * 24);
            diasPorSemana = programaTreino.getDiasPorSemana();
            totalAulasPrevistas = programaTreino.getTotalAulasPrevistas();
            long diasParaSomar = duracaoEmDias - 1;
            dataTerminoPrevisto = new Date(dataInicio.getTime() + (diasParaSomar * 24 * 60 * 60 * 1000));
        } else {
            totalAulasPrevistas = programaTreino.getTotalAulasPrevistas();
            diasPorSemana = programaTreino.getDiasPorSemana();
        }
        cliente = programaTreino.getCliente();
        professorCarteira = programaTreino.getCliente().getProfessorSintetico();
        professorMontou = professorCriador;
        programaTreinoRenovacao = programaTreino.getCodigo();

    }

    public ProgramaTreino() {
    }

    public ProgramaTreino(ProgramaTreino pt, boolean clonar) {
        this.nome = pt.nome;
        this.dataLancamento = pt.dataLancamento;
        this.diasPorSemana = pt.diasPorSemana;
        if(clonar) {
            this.codigo = pt.getCodigo();
            this.dataInicio = pt.getDataInicio();
            this.dataTerminoPrevisto = pt.getDataTerminoPrevisto();
            this.totalAulasPrevistas = pt.getTotalAulasPrevistas();
            this.professorMontou = pt.getProfessorMontou();
            this.isGeradoPorIA = pt.getGeradoPorIA();
        }
    }


    public ProgramaTreino(Date termino) {
        dataTerminoPrevisto = termino;
    }

    public ProgramaTreino getProgramaAntesAlteracao() {
        return programaAntesAlteracao;
    }

    public void setProgramaAntesAlteracao(ProgramaTreino programaAntesAlteracao) {
        this.programaAntesAlteracao = programaAntesAlteracao;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Date getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Date getDataProximaRevisao() {
        return dataProximaRevisao;
    }

    public void setDataProximaRevisao(Date dataProximaRevisao) {
        this.dataProximaRevisao = dataProximaRevisao;
    }

    public Date getDataTerminoPrevisto() {
        return dataTerminoPrevisto;
    }

    public void setDataTerminoPrevisto(Date dataTerminoPrevisto) {
        this.dataTerminoPrevisto = dataTerminoPrevisto;
    }

    public Integer getDiasPorSemana() {
        return diasPorSemana;
    }

    public void setDiasPorSemana(Integer diasPorSemana) {
        this.diasPorSemana = diasPorSemana;
    }

    public Integer getTotalAulasPrevistas() {
        return totalAulasPrevistas;
    }

    public void setTotalAulasPrevistas(Integer totalAulasPrevistas) {
        this.totalAulasPrevistas = totalAulasPrevistas;
    }

    public ProgramaSituacaoEnum getSituacao() {
        return situacao;
    }

    public void setSituacao(ProgramaSituacaoEnum situacao) {
        this.situacao = situacao;
    }

    public ClienteSintetico getCliente() {
        return cliente;
    }

    public Boolean getGeradoPorIA() {
        if (isGeradoPorIA == null) {
            isGeradoPorIA = false;
        }
        return isGeradoPorIA;
    }

    public void setGeradoPorIA(Boolean geradoPorIA) {
        isGeradoPorIA = geradoPorIA;
    }

    public String getWorkoutIDIA() {
        return workoutIDIA;
    }

    public void setWorkoutIDIA(String workoutIDIA) {
        this.workoutIDIA = workoutIDIA;
    }

    public Boolean getEmRevisaoProfessor() {
        if (emRevisaoProfessor == null) {
            emRevisaoProfessor = false;
        }
        return emRevisaoProfessor;
    }

    public void setEmRevisaoProfessor(Boolean emRevisaoProfessor) {
        this.emRevisaoProfessor = emRevisaoProfessor;
    }

    public void setCliente(ClienteSintetico cliente) {
        this.cliente = cliente;
    }

    public ProfessorSintetico getProfessorMontou() {
        return professorMontou;
    }

    public void setProfessorMontou(ProfessorSintetico professorMontou) {
        this.professorMontou = professorMontou;
    }

    public ProfessorSintetico getProfessorCarteira() {
        return professorCarteira;
    }

    public void setProfessorCarteira(ProfessorSintetico professorCarteira) {
        this.professorCarteira = professorCarteira;
    }

    public Integer getVersao() {
        return versao == null ? 0 : versao;
    }

    public void setVersao(Integer versao) {
        this.versao = versao;
    }

    public List<Compromisso> getCompromissos() {
        return compromissos;
    }

    public void setCompromissos(List<Compromisso> compromissos) {
        this.compromissos = compromissos;
    }

    public List<ProgramaTreinoFicha> getProgramaFichas() {
        return programaFichas;
    }

    public void setProgramaFichas(List<ProgramaTreinoFicha> fichas) {
        this.programaFichas = fichas;
    }

    public List<ObjetivoPrograma> getObjetivos() {
        return objetivos;
    }

    public void setObjetivos(List<ObjetivoPrograma> objetivos) {
        this.objetivos = objetivos;
    }

    public Integer getNrTreinosRealizados() {
        return nrTreinosRealizados;
    }
    public Integer getNrTreinosRealizadosApresentar() {
        return nrTreinosRealizados == null ? 0 : nrTreinosRealizados;
    }

    public void setNrTreinosRealizados(Integer nrTreinosRealizados) {
        this.nrTreinosRealizados = nrTreinosRealizados;
    }

    public List<GenericoTO> getObjetivosPredef() {
        return objetivosPredef;
    }



    public void setObjetivosPredef(List<GenericoTO> objetivosPredef) {
        this.objetivosPredef = objetivosPredef;
    }

    public Integer getProgramaTreinoRenovacao() {
        return programaTreinoRenovacao;
    }

    public void setProgramaTreinoRenovacao(Integer programaTreinoRenovacao) {
        this.programaTreinoRenovacao = programaTreinoRenovacao;
    }

    public Integer getProgramaTreinoRenovado() {
        return programaTreinoRenovado;
    }

    public void setProgramaTreinoRenovado(Integer programaTreinoRenovado) {
        this.programaTreinoRenovado = programaTreinoRenovado;
    }

    public Date getDataRenovacao() {
        return dataRenovacao;
    }

    public void setDataRenovacao(Date dataRenovacao) {
        this.dataRenovacao = dataRenovacao;
    }

    public List<HistoricoRevisaoProgramaTreino> getHistoricoRevicoes() {
        return historicoRevicoes;
    }

    public void setHistoricoRevicoes(List<HistoricoRevisaoProgramaTreino> historicoRevicoes) {
        this.historicoRevicoes = historicoRevicoes;
    }

    public String getMatricula(){
        try {
            return getCliente().getMatriculaString();
        } catch (Exception e) {
            return "";
        }

    }

    public String getNomeAluno(){
        try {
            return getCliente().getNomeAbreviado();
        } catch (Exception e) {
            return "";
        }

    }

    public String getSituacaoAluno(){
        return getCliente().getSituacao();
    }

    public String getDataVigenciaAteAjustadaApresentar() {
        try {
            return Uteis.getData(getCliente().getDataVigenciaAteAjustada());
        } catch (Exception e) {
            return "";
        }

    }

    public Date getDataVigenciaAteAjustada() {
        return getCliente().getDataVigenciaAteAjustada();
    }

    public String getDataPrograma() {
        try {
            return Uteis.getData(getDataTerminoPrevisto());
        } catch (Exception e) {
            return "";
        }

    }
    public String getDataLancamentoApresentar() {
        try {
            return Uteis.getData(getDataLancamento());
        } catch (Exception e) {
            return "";
        }

    }

    public Integer getChaveLink(){
        return this.getCliente().getCodigo();
    }

    public Date getDataUltimaAtualizacao() {
        return dataUltimaAtualizacao;
    }

    public void setDataUltimaAtualizacao(Date dataUltimaAtualizacao) {
        this.dataUltimaAtualizacao = dataUltimaAtualizacao;
    }


    public String getDataUltimaAtualizacaoApresentar() {
        try {
            return Uteis.getDataComHHMM(getDataUltimaAtualizacao());
        } catch (Exception e) {
            return "";
        }

    }

    public Double getAndamento(){
        try {
            if (getTotalAulasPrevistas() == null || getTotalAulasPrevistas().equals(0)
                    || getNrTreinosRealizados() == null || getNrTreinosRealizados().equals(0)) {
                return 0.0;
            }
            return getNrTreinosRealizados().doubleValue() / getTotalAulasPrevistas().doubleValue() * 100;
        }catch (Exception e){
            return 0.0;
        }

    }

    public String getAndamentoTreino() {
        Double and = getAndamento();
        return (Uteis.arredondarForcando2CasasDecimais(and)) + "%";
    }

    public Boolean getTreinoRapido() {
        return treinoRapido;
    }

    public void setTreinoRapido(Boolean treinoRapido) {
        this.treinoRapido = treinoRapido;
    }

    public List<HistoricoRevisao> getHistoricoRevicoesSincronizacao() {
        return historicoRevicoesSincronizacao;
    }

    public void setHistoricoRevicoesSincronizacao(List<HistoricoRevisao> historicoRevicoesSincronizacao) {
        this.historicoRevicoesSincronizacao = historicoRevicoesSincronizacao;
    }

    public List<GenericoTO> getRestricoes() {
        return restricoes;
    }

    public void setRestricoes(List<GenericoTO> restricoes) {
        this.restricoes = restricoes;
    }

    public Set<RestricoesEnum> getRestricoesTreino() {
        return restricoesTreino;
    }

    public void setRestricoesTreino(Set<RestricoesEnum> restricoesTreino) {
        this.restricoesTreino = restricoesTreino;
    }

    public Boolean isRevisado() {
        if (this.getDataProximaRevisao() != null) {
            return !(Calendario.maiorOuIgual(Calendario.hoje(), this.getDataProximaRevisao())
                    && Calendario.maior(this.getDataTerminoPrevisto(), Calendario.hoje()));
        }else {
            return !(Calendario.maior(this.getDataTerminoPrevisto(), Calendario.hoje()));
        }
   }

    public Boolean getPreDefinido() {
        return preDefinido;
    }

    public void setPreDefinido(Boolean preDefinido) {
        this.preDefinido = preDefinido;
    }

    public Nivel getNivel() {
        return nivel;
    }

    public void setNivel(Nivel nivel) {
        this.nivel = nivel;
    }

    public Boolean getUsarNovaMontagemFicha() {
        return usarNovaMontagemFicha;
    }

    public void setUsarNovaMontagemFicha(Boolean usarNovaMontagemFicha) {
        this.usarNovaMontagemFicha = usarNovaMontagemFicha;
    }

    public String getGenero() {
        return genero;
    }

    public void setGenero(String genero) {
        this.genero = genero;
    }

    public Integer getCodigoColaborador() {
        return codigoColaborador;
    }

    public void setCodigoColaborador(Integer codigoColaborador) {
        this.codigoColaborador = codigoColaborador;
    }

    public String getDescricaoParaLog(ProgramaTreino p2) {
        try {
            StringBuilder log = new StringBuilder();
            log.append(UtilReflection.difference(this, p2));
            return log.toString();
        } catch (Exception ex) {
            ex.printStackTrace();
            return "ERRO gerar log";
        }
    }

    public String getIdOperacaoEmMassa() {
        return idOperacaoEmMassa;
    }

    public void setIdOperacaoEmMassa(String idOperacaoEmMassa) {
        this.idOperacaoEmMassa = idOperacaoEmMassa;
    }

    public Integer getOrigem() {
        return origem;
    }

    public void setOrigem(Integer origem) {
        this.origem = origem;
    }
}
