package br.com.pacto.bean.programa;

import br.com.pacto.bean.professor.ProfessorSintetico;
import br.com.pacto.bean.usuario.Usuario;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;


/**
 * Created by ulisses on 13/08/2018.
 */
@ApiModel(description = "DTO para representar a resposta de um professor.")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ProfessorResponseTO {

    @ApiModelProperty(value = "Identificador único do professor", example = "1")
    private Integer id;

    @ApiModelProperty(value = "Código sintético do professor", example = "1001")
    private Integer codigoProfessorsintetico;

    @ApiModelProperty(value = "Nome de usuário do professor", example = "prof.joao")
    private String username;

    @ApiModelProperty(value = "Nome completo do professor", example = "<PERSON>")
    private String nome;

    @ApiModelProperty(value = "URI da imagem do professor", example = "http://example.com/imagens/professor1.jpg")
    private String imageUri;

    @ApiModelProperty(value = "Código da pessoa associada ao professor", example = "2001")
    private Integer codigoPessoa;

    @ApiModelProperty(value = "Código do colaborador associado ao professor", example = "3001")
    private Integer codigoColaborador;

    @ApiModelProperty(value = "Número do CREF do professor", example = "CREF12345")
    private String cref;

    public ProfessorResponseTO() {
    }

    public ProfessorResponseTO(Integer id, String nome) {
        this.id = id;
        this.nome = nome;
    }

    public ProfessorResponseTO(Usuario usuario, Boolean treinoIndependente) {
        this.id = treinoIndependente ? usuario.getProfessor().getCodigo() : usuario.getProfessor().getCodigoColaborador();
        this.username = usuario.getUserName();
        this.nome = usuario.getProfessor().getNome();
        this.imageUri = usuario.getProfessor().getUriImagem();
        this.codigoPessoa = treinoIndependente ? usuario.getProfessor().getPessoa().getCodigo() : usuario.getProfessor().getCodigoPessoa();

    }

    public ProfessorResponseTO(ProfessorSintetico professor, Boolean treinoIndependente, Boolean validarNoTreino) {
        if (validarNoTreino != null && validarNoTreino) {
            this.id = professor.getCodigo();
            this.codigoColaborador = professor.getCodigoColaborador();
        } else {
            this.id = treinoIndependente ? professor.getCodigo() : professor.getCodigoColaborador();
        }
        this.nome = professor.getNome();
        this.codigoPessoa = treinoIndependente ? professor.getPessoa().getCodigo() : professor.getCodigoPessoa();
        this.codigoProfessorsintetico = professor.getCodigo();

    }

    public ProfessorResponseTO(ProfessorSintetico professor, Boolean treinoIndependente, Integer codigoProfessorsintetico) {
        this.id = treinoIndependente ? professor.getCodigo() : professor.getCodigoColaborador();
        this.nome = professor.getNome();
        this.codigoPessoa = treinoIndependente ? professor.getPessoa().getCodigo() : professor.getCodigoPessoa();
        this.codigoProfessorsintetico = codigoProfessorsintetico;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getImageUri() {
        return imageUri;
    }

    public void setImageUri(String imageUri) {
        this.imageUri = imageUri;
    }

    public Integer getCodigoPessoa() {
        return codigoPessoa;
    }

    public void setCodigoPessoa(Integer codigoPessoa) {
        this.codigoPessoa = codigoPessoa;
    }

    public String getCref() {
        return cref;
    }

    public void setCref(String cref) {
        this.cref = cref;
    }

    public Integer getCodigoColaborador() {
        return codigoColaborador;
    }

    public void setCodigoColaborador(Integer codigoColaborador) {
        this.codigoColaborador = codigoColaborador;
    }

    public Integer getCodigoProfessorsintetico() {
        return codigoProfessorsintetico;
    }

    public void setCodigoProfessorsintetico(Integer codigoProfessorsintetico) {
        this.codigoProfessorsintetico = codigoProfessorsintetico;
    }

}
