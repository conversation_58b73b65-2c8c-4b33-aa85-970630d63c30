/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.programa;

/**
 *
 * <AUTHOR>
 */
public class ProgramaTreinoResumo {

    private Integer programas = 0;
    private Integer atividades = 0;
    private Integer diasTreinamento = 0;
    private Integer diasPresencaSemana = 0;
    private Integer diasProgramaAtual = 0;
    private Integer expecativaSemana = 0;
    private Integer faltasSemana = 0;
    private Integer diasProgramaAtualTotal = 0;

    public ProgramaTreinoResumo() {
    }

    public Integer getProgramas() {
        return programas;
    }

    public void setProgramas(Integer programas) {
        this.programas = programas;
    }

    public Integer getAtividades() {
        return atividades;
    }

    public void setAtividades(Integer atividades) {
        this.atividades = atividades;
    }

    public Integer getDiasTreinamento() {
        return diasTreinamento;
    }

    public void setDiasTreinamento(Integer diasTreinamento) {
        this.diasTreinamento = diasTreinamento;
    }

    public Integer getDiasPresencaSemana() {
        return diasPresencaSemana;
    }

    public void setDiasPresencaSemana(Integer diasPresencaSemana) {
        this.diasPresencaSemana = diasPresencaSemana;
    }

    public Integer getDiasProgramaAtual() {
        return diasProgramaAtual;
    }

    public void setDiasProgramaAtual(Integer diasProgramaAtual) {
        this.diasProgramaAtual = diasProgramaAtual;
    }

    public Integer getExpecativaSemana() {
        return expecativaSemana;
    }

    public void setExpecativaSemana(Integer expecativaSemana) {
        this.expecativaSemana = expecativaSemana;
    }

    public Integer getFaltasSemana() {
        return faltasSemana;
    }

    public void setFaltasSemana(Integer faltasSemana) {
        this.faltasSemana = faltasSemana;
    }

    public Integer getDiasProgramaAtualTotal() {
        return diasProgramaAtualTotal;
    }

    public void setDiasProgramaAtualTotal(Integer diasProgramaAtualTotal) {
        this.diasProgramaAtualTotal = diasProgramaAtualTotal;
    }
}
