/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.programa;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import java.io.Serializable;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
@Entity
public class ObjetivoIntermediarioAluno implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private Date datainicio;
    private Date datafinal;
    private String descricao;
    private String objetivofinal;
    private String categoria;
    @ManyToOne
    @JoinColumn(name = "objetivopredefinido")
    private ObjetivoPredefinido objetivopredefinido;
    @ManyToOne
    @JoinColumn(name = "objetivoaluno")
    private ObjetivoAluno objetivoaluno;
    private Integer status;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Date getDatainicio() {
        return datainicio;
    }

    public void setDatainicio(Date datainicio) {
        this.datainicio = datainicio;
    }

    public Date getDatafinal() {
        return datafinal;
    }

    public void setDatafinal(Date datafinal) {
        this.datafinal = datafinal;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getObjetivofinal() {
        return objetivofinal;
    }

    public void setObjetivofinal(String objetivofinal) {
        this.objetivofinal = objetivofinal;
    }

    public ObjetivoPredefinido getObjetivopredefinido() {
        return objetivopredefinido;
    }

    public void setObjetivopredefinido(ObjetivoPredefinido objetivo) {
        this.objetivopredefinido = objetivo;
    }

    public ObjetivoAluno getObjetivoaluno() {
        return objetivoaluno;
    }

    public void setObjetivoaluno(ObjetivoAluno objetivoaluno) {
        this.objetivoaluno = objetivoaluno;
    }

    public String getCategoria() {
        return categoria;
    }

    public void setCategoria(String categoria) {
        this.categoria = categoria;
    }
}
