/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.programa;

import br.com.pacto.bean.cliente.ClienteSintetico;

import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
@Entity
public class ObjetivoAluno implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    @ManyToOne
    @JoinColumn(name = "cliente")
    private ClienteSintetico clienteSintetico;
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "objetivo")
    private ObjetivoPredefinido objetivoPredefinido;
    private Boolean primario;
    private Date datainicio;
    private Date datafinal;
    private String descricao;
    private Integer status;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public boolean isPrimario() {
        return primario;
    }

    public void setPrimario(boolean primario) {
        this.primario = primario;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int objetivoconcluido) {
        this.status = objetivoconcluido;
    }

    public ClienteSintetico getClienteSintetico() {
        return clienteSintetico;
    }

    public void setClienteSintetico(ClienteSintetico cliente) {
        this.clienteSintetico = cliente;
    }
    public Date getDatainicio() {
        return datainicio;
    }

    public void setDatainicio(Date datainicio) {
        this.datainicio = datainicio;
    }

    public Date getDatafinal() {
        return datafinal;
    }

    public void setDatafinal(Date datafinal) {
        this.datafinal = datafinal;
    }

    public ObjetivoPredefinido getObjetivoPredefinido() {
        return objetivoPredefinido;
    }

    public void setObjetivoPredefinido(ObjetivoPredefinido objetivoPredefinido) {
        this.objetivoPredefinido = objetivoPredefinido;
    }

    public Boolean getPrimario() {
        return primario;
    }

    public void setPrimario(Boolean primario) {
        this.primario = primario;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
}
