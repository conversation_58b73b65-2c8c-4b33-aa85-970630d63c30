package br.com.pacto.bean.programa;

import br.com.pacto.bean.usuario.StatusObjetivoAlunoEnum;
import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class ObjetivoIntermediarioAlunoDTO {
    private Integer objetivoPredefinido;
    private Integer objetivoAluno;
    private String dataInicio;
    private String dataFinal;
    private String descricao;
    private String objetivoFinal;
    private String categoria;
    private StatusObjetivoAlunoEnum status;

    public Integer getObjetivoPredefinido() {
        return objetivoPredefinido;
    }

    public void setObjetivoPredefinido(Integer objetivoPredefinido) {
        this.objetivoPredefinido = objetivoPredefinido;
    }

    public Integer getObjetivoAluno() {
        return objetivoAluno;
    }

    public void setObjetivoAluno(Integer objetivoAluno) {
        this.objetivoAluno = objetivoAluno;
    }

    public String getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(String dataInicio) {
        this.dataInicio = dataInicio;
    }

    public String getDataFinal() {
        return dataFinal;
    }

    public void setDataFinal(String dataFinal) {
        this.dataFinal = dataFinal;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getObjetivoFinal() {
        return objetivoFinal;
    }

    public void setObjetivoFinal(String objetivoFinal) {
        this.objetivoFinal = objetivoFinal;
    }

    public String getCategoria() {
        return categoria;
    }

    public void setCategoria(String categoria) {
        this.categoria = categoria;
    }

    public StatusObjetivoAlunoEnum getStatus() {
        return status;
    }

    public void setStatus(StatusObjetivoAlunoEnum status) {
        this.status = status;
    }
}
