/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.programa;

import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.professor.ProfessorSintetico;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import java.io.Serializable;
import java.util.Date;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table
public class HistoricoRevisaoProgramaTreino implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    @ManyToOne
    private ProgramaTreino programa;
    @ManyToOne
    private ClienteSintetico cliente;
    @Temporal(TemporalType.TIMESTAMP)
    private Date dataRegistro = Calendario.hoje();
    @ManyToOne
    private ProfessorSintetico professor<PERSON>ev<PERSON><PERSON>;
    private String justificativa;
    @Temporal(TemporalType.DATE)
    private Date proximaRevisao;

    public HistoricoRevisaoProgramaTreino() {
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public ProgramaTreino getPrograma() {
        return programa;
    }

    public void setPrograma(ProgramaTreino programa) {
        this.programa = programa;
    }

    public Date getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(Date dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public ProfessorSintetico getProfessorRevisou() {
        return professorRevisou;
    }

    public void setProfessorRevisou(ProfessorSintetico professorRevisou) {
        this.professorRevisou = professorRevisou;
    }

    public ClienteSintetico getCliente() {
        return cliente;
    }

    public void setCliente(ClienteSintetico cliente) {
        this.cliente = cliente;
    }

    public String getJustificativa() {
        return justificativa;
    }

    public void setJustificativa(String justificativa) {
        this.justificativa = justificativa;
    }

    public Date getProximaRevisao() {
        return proximaRevisao;
    }

    public void setProximaRevisao(Date proximaRevisao) {
        this.proximaRevisao = proximaRevisao;
    }
    
     public String getMatricula(){
        return getCliente().getMatriculaString();
    }
    
    public String getNomeAluno(){
        return getCliente().getNome();
    }
   
    public String getSituacaoAluno(){
        return getCliente().getSituacao();
    }
   
    public String getDataVigenciaAteAjustadaApresentar() {
        try {
            return Uteis.getData(getCliente().getDataVigenciaAteAjustada());
        } catch (Exception e) {
            return "";
        }
        
    }
    
    public Date getDataVigenciaAteAjustada() {
        return getCliente().getDataVigenciaAteAjustada();
        
    }
    
    public String getDataProgramaApresentar() {
        try {
            return Uteis.getData(getPrograma().getDataTerminoPrevisto());
        } catch (Exception e) {
            return "";
        }
        
    }
    
    public Date getDataPrograma() {
        return getPrograma().getDataTerminoPrevisto();
    }
    
    public String getDataRegistroApresentar() {
        try {
            return Uteis.getData(getDataRegistro());
        } catch (Exception e) {
            return "";
        }
        
    }
    
    public Integer getChaveLink(){
        return this.getCliente().getCodigo();
    }
}
