package br.com.pacto.bean.programa;

import br.com.pacto.bean.colaborador.ColaboradorSimplesTO;
import br.com.pacto.bean.ficha.FichaResponseTO;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.util.UteisValidacao;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by ulisses on 13/08/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ProgramaTreinoResponseTO {

    private Integer id;
    private String nome;
    private Integer alunoId;
    private ProfessorResponseTO professor;
    private Date dataLancamento;
    private Date inicio;
    private Date termino;
    private Integer totalTreinos;
    private Integer qtdDiasSemana;
    private Date revisao;
    private Integer treinosConcluidos;
    private List<FichaResponseTO> fichas = new ArrayList<FichaResponseTO>();
    private String alunoImagiUri;
    private ColaboradorSimplesTO professorMontou;
    private String genero;
    private ProgramaSituacaoEnum situacao = ProgramaSituacaoEnum.ATIVO;
    private Boolean predefinido;
    private String chaveOrigem;
    private Integer colaboradorId;
    private Boolean isGeradoPorIA;
    private Boolean emRevisaoProfessor;

    public ProgramaTreinoResponseTO() {
    }

    public ProgramaTreinoResponseTO(ProgramaTreino pt, Usuario usuarioProfessor,
                                    List<ProgramaTreinoFicha> programaFichas,
                                    Boolean treinoIndependente) {
        this.id = pt.getCodigo();
        this.nome = pt.getNome();
        this.genero = pt.getGenero();
        this.situacao = pt.getSituacao();
        this.predefinido = pt.getPreDefinido();
        this.dataLancamento = pt.getDataLancamento();
        this.emRevisaoProfessor = pt.getEmRevisaoProfessor();
        this.isGeradoPorIA = pt.getGeradoPorIA();
        if (pt.getCliente() != null) {
            this.alunoId = pt.getCliente().getCodigo();
        }
        if (usuarioProfessor != null) {
            this.setProfessor(new ProfessorResponseTO(usuarioProfessor, treinoIndependente));
        }
        if (pt.getDataInicio() != null) {
            this.inicio = pt.getDataInicio();
        }
        if (pt.getDataTerminoPrevisto() != null) {
            this.termino = pt.getDataTerminoPrevisto();
        }
        this.totalTreinos = pt.getTotalAulasPrevistas();
        this.qtdDiasSemana = pt.getDiasPorSemana();
        if (pt.getDataProximaRevisao()  != null) {
            this.revisao = pt.getDataProximaRevisao();
        }
        if (pt.getNrTreinosRealizados() != null) {
            this.treinosConcluidos = pt.getNrTreinosRealizados();
        }
        if (!UteisValidacao.emptyList(programaFichas)) {
            for (ProgramaTreinoFicha ptf : programaFichas) {
                this.fichas.add(new FichaResponseTO(ptf.getFicha(), ptf.getDiaSemana()));
            }
        }
        if (pt.getCliente() != null) {
            if (pt.getCliente().getPessoa().getFotoKey() == null) {
                this.alunoImagiUri = null;
            } else {
                this.alunoImagiUri = pt.getCliente().getPessoa().getFotoKey();
            }
        }
        if (pt.getProfessorMontou() != null) {
            this.professorMontou = new ColaboradorSimplesTO(pt.getProfessorMontou(), treinoIndependente);
        }
        if (pt.getDataLancamento() != null) {
            this.dataLancamento = pt.getDataLancamento();
        }
        if (pt.getCodigoColaborador() != null) {
            this.colaboradorId = pt.getCodigoColaborador();
        }
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Integer getAlunoId() {
        return alunoId;
    }

    public void setAlunoId(Integer alunoId) {
        this.alunoId = alunoId;
    }

    public ProfessorResponseTO getProfessor() {
        return professor;
    }

    public void setProfessor(ProfessorResponseTO professor) {
        this.professor = professor;
    }

    public Date getInicio() {
        return inicio;
    }

    public void setInicio(Date inicio) {
        this.inicio = inicio;
    }

    public Date getTermino() {
        return termino;
    }

    public void setTermino(Date termino) {
        this.termino = termino;
    }

    public Date getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public Integer getTotalTreinos() {
        return totalTreinos;
    }

    public void setTotalTreinos(Integer totalTreinos) {
        this.totalTreinos = totalTreinos;
    }

    public Integer getQtdDiasSemana() {
        return qtdDiasSemana;
    }

    public void setQtdDiasSemana(Integer qtdDiasSemana) {
        this.qtdDiasSemana = qtdDiasSemana;
    }

    public Boolean getGeradoPorIA() {
        return isGeradoPorIA;
    }

    public void setGeradoPorIA(Boolean geradoPorIA) {
        isGeradoPorIA = geradoPorIA;
    }

    public Boolean getEmRevisaoProfessor() {
        return emRevisaoProfessor;
    }

    public void setEmRevisaoProfessor(Boolean emRevisaoProfessor) {
        this.emRevisaoProfessor = emRevisaoProfessor;
    }

    public Date getRevisao() {
        return revisao;
    }

    public void setRevisao(Date revisao) {
        this.revisao = revisao;
    }

    public Integer getTreinosConcluidos() {
        return treinosConcluidos;
    }

    public void setTreinosConcluidos(Integer treinosConcluidos) {
        this.treinosConcluidos = treinosConcluidos;
    }

    public List<FichaResponseTO> getFichas() {
        return fichas;
    }

    public void setFichas(List<FichaResponseTO> fichas) {
        this.fichas = fichas;
    }

    public String getAlunoImagiUri() {
        return alunoImagiUri;
    }

    public void setAlunoImagiUri(String alunoImagiUri) {
        this.alunoImagiUri = alunoImagiUri;
    }

    public ColaboradorSimplesTO getProfessorMontou() {
        return professorMontou;
    }

    public void setProfessorMontou(ColaboradorSimplesTO professorMontou) {
        this.professorMontou = professorMontou;
    }

    public String getGenero() {
        return genero;
    }

    public void setGenero(String genero) {
        this.genero = genero;
    }

    public ProgramaSituacaoEnum getSituacao() {
        return situacao;
    }

    public void setSituacao(ProgramaSituacaoEnum situacao) {
        this.situacao = situacao;
    }

    public Boolean getPredefinido() {
        return predefinido;
    }

    public void setPredefinido(Boolean predefinido) {
        this.predefinido = predefinido;
    }

    public String getChaveOrigem() {
        return chaveOrigem;
    }

    public void setChaveOrigem(String chaveOrigem) {
        this.chaveOrigem = chaveOrigem;
    }

    public Integer getColaboradorId() { return colaboradorId; }

    public void setColaboradorId(Integer colaboradorId) { this.colaboradorId = colaboradorId; }
}
