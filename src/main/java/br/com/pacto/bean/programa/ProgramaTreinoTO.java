package br.com.pacto.bean.programa;

import br.com.pacto.objeto.Calendario;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.sql.Timestamp;
import java.text.ParseException;
import java.util.Calendar;
import java.util.Date;

/**
 * Created by ulisses on 13/08/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ProgramaTreinoTO {

    private Integer id;
    private Integer alunoId;
    private Integer colaboradorId;
    private String nome;
    private Integer professorId;
    private Long inicio;
    private Long termino;
    private Integer totalTreinos;
    private Integer qtdDiasSemana;
    private String genero;
    private Boolean predefinido;
    private Boolean emRevisaoProfessor = false;
    private Boolean isGeradoPorIA = false;
    private Integer professor<PERSON><PERSON><PERSON>;

    public Integer getId() { return id; }

    public void setId(Integer id) { this.id = id; }

    public Integer getAlunoId() {
        return alunoId;
    }

    public void setAlunoId(Integer alunoId) {
        this.alunoId = alunoId;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Integer getProfessorId() {
        return professorId;
    }

    public void setProfessorId(Integer professorId) {
        this.professorId = professorId;
    }

    public Long getInicio() {
        return inicio;
    }

    public Date getInicioComoData() throws ParseException {
        return new Date(this.inicio);
    }

    public void setInicio(Long inicio) {
        this.inicio = inicio;
    }

    public Long getTermino() {
        return termino;
    }

    public Date getTerminoComoData() throws ParseException {
        return new Date(this.termino);
    }

    public void setTermino(Long termino) {
        this.termino = termino;
    }

    public Integer getTotalTreinos() {
        return totalTreinos;
    }

    public void setTotalTreinos(Integer totalTreinos) {
        this.totalTreinos = totalTreinos;
    }

    public Integer getQtdDiasSemana() {
        return qtdDiasSemana;
    }

    public void setQtdDiasSemana(Integer qtdDiasSemana) {
        this.qtdDiasSemana = qtdDiasSemana;
    }

    public String getGenero() {
        return genero;
    }

    public void setGenero(String genero) {
        this.genero = genero;
    }

    public Boolean getPredefinido() {
        return predefinido;
    }

    public void setPredefinido(Boolean predefinido) {
        this.predefinido = predefinido;
    }

    public Integer getColaboradorId() {
        return colaboradorId;
    }

    public void setColaboradorId(Integer colaboradorId) {
        this.colaboradorId = colaboradorId;
    }

    public Boolean getEmRevisaoProfessor() {
        return emRevisaoProfessor;
    }

    public Boolean getGeradoPorIA() {
        return isGeradoPorIA;
    }

    public void setGeradoPorIA(Boolean geradoPorIA) {
        isGeradoPorIA = geradoPorIA;
    }

    public void setEmRevisaoProfessor(Boolean emRevisaoProfessor) {
        this.emRevisaoProfessor = emRevisaoProfessor;
    }

    public Integer getProfessorMontou() { return professorMontou; }

    public void setProfessorMontou(Integer professorMontou) { this.professorMontou = professorMontou; }
}
