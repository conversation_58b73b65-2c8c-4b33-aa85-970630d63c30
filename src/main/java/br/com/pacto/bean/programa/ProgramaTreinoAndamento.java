/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.programa;

import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;


/**
 *
 * <AUTHOR>
 */
@Entity
@Table(uniqueConstraints={@UniqueConstraint(name = "programatreinoandamento_programa_codigo_key",columnNames={"programa_codigo"})}) 
public class ProgramaTreinoAndamento implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private Integer distancia = 0;
    private Integer duracao = 0;
    private Integer repeticao = 0;
    private Double velocidade = 0.0;
    private Double carga = 0.0;
    private Integer nrVelocidade = 0;
    @ManyToOne
    private ProgramaTreino programa;
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    private Date ultimoTreino;
    private Double percentualAndamento = 0.0;
    private Integer nrTreinos = 0;
    @Transient
    private Integer totalAulasPrevistas;



    public void setVelocidade(Double velocidade) {
        this.velocidade = velocidade;
    }

    public Date getUltimoTreino() {
        return ultimoTreino;
    }

    public void setUltimoTreino(Date ultimoTreino) {
        this.ultimoTreino = ultimoTreino;
    }

    public ProgramaTreinoAndamento() {
    }

    public ProgramaTreinoAndamento(Integer nrTreinos, Integer totalAulasPrevistas) {
        this.nrTreinos = nrTreinos;
        this.totalAulasPrevistas = totalAulasPrevistas;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getDistancia() {
        return distancia;
    }

    public void setDistancia(Integer distancia) {
        this.distancia = distancia;
    }

    public Integer getDuracao() {
        return duracao;
    }

    public void setDuracao(Integer duracao) {
        this.duracao = duracao;
    }

    public Integer getRepeticao() {
        return repeticao;
    }

    public void setRepeticao(Integer repeticao) {
        this.repeticao = repeticao;
    }

    public Double getVelocidade() {
        return velocidade;
    }

    public void setVelocidade(double velocidade) {
        this.velocidade = velocidade;
    }

    public Double getCarga() {
        return carga;
    }

    public void setCarga(Double carga) {
        this.carga = carga;
    }

    public ProgramaTreino getPrograma() {
        return programa;
    }

    public void setPrograma(ProgramaTreino programa) {
        this.programa = programa;
    }

    public Integer getNrVelocidade() {
        return nrVelocidade;
    }

    public void setNrVelocidade(Integer nrVelocidade) {
        this.nrVelocidade = nrVelocidade;
    }

    public Double getPercentualAndamento() {
        return percentualAndamento;
    }

    public void setPercentualAndamento(Double percentualAndamento) {
        this.percentualAndamento = percentualAndamento;
    }

    public Integer getNrTreinos() {
        return nrTreinos;
    }

    public void setNrTreinos(Integer nrTreinos) {
        this.nrTreinos = nrTreinos;
    }

    public Double getPercentualFrequenciaAteHoje() {
        if (programa != null && programa.getDataInicio() != null && programa.getDataTerminoPrevisto() != null && programa.getDiasPorSemana() != null) {
            //numero de aulas previstas até a data de hoje
            Long dias = Uteis.nrDiasEntreDatas(programa.getDataInicio(), programa.getDataTerminoPrevisto());
            int nrAulasPrevistasAteHoje = (int) ((dias.doubleValue() / 7) * programa.getDiasPorSemana());
            //numero de treinos realizados pelo aluno nesse programa até hoje/ nr de aulas previstas
            if (this.getNrTreinos() > 0 && nrAulasPrevistasAteHoje > 0) {
                Double freq = (this.getNrTreinos() /(double) nrAulasPrevistasAteHoje) * 100;
                return Uteis.arredondarForcando2CasasDecimais(freq);
            }
        }
        return 0.0;
    }

    public Double getPercentualExecucoesFrequenciaAteHoje(Integer quantidadeExecucoes) {
        if (programa != null && programa.getDataInicio() != null && programa.getDataTerminoPrevisto() != null && programa.getDiasPorSemana() != null) {
            //numero de aulas previstas até a data de hoje
            Long dias = Uteis.nrDiasEntreDatas(programa.getDataInicio(), programa.getDataTerminoPrevisto());
            int nrAulasPrevistasAteHoje = (int) Math.round(((dias.doubleValue() / 7) * programa.getDiasPorSemana()));
            //numero de treinos realizados pelo aluno nesse programa até hoje/ nr de aulas previstas
            if (quantidadeExecucoes > 0 && nrAulasPrevistasAteHoje > 0) {
                Double freq = (quantidadeExecucoes /(double) nrAulasPrevistasAteHoje) * 100;
                return Uteis.arredondarForcando2CasasDecimais(freq);
            }
        }
        return 0.0;
    }

    public Double getPercentualExecucoesFrequencia(Integer quantidadeExecucoes, Integer quantidadeAulasPrevistas) {
        try {
            Double freq = (quantidadeExecucoes /(double) quantidadeAulasPrevistas) * 100;
            return Uteis.arredondarForcando2CasasDecimais(freq);
        } catch (Exception e) {
            return getPercentualExecucoesFrequenciaAteHoje(quantidadeExecucoes);
        }
    }

    public String getFrequenciaPercent(){
        return Uteis.arrendondarForcando2CadasDecimaisComVirgula(getPercentualFrequenciaAteHoje()) + "%";
    }

    public Integer getTotalAulasPrevistas() {
        return totalAulasPrevistas;
    }

    public void setTotalAulasPrevistas(Integer totalAulasPrevistas) {
        this.totalAulasPrevistas = totalAulasPrevistas;
    }
}
