/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.programa;

import java.io.Serializable;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.ManyToOne;

/**
 *
 * <AUTHOR>
 */
@Entity
public class ObjetivoPrograma implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    @ManyToOne(fetch = FetchType.LAZY)
    private ObjetivoPredefinido objetivo;
    @ManyToOne(fetch = FetchType.LAZY)
    private ProgramaTreino programa;

    public ObjetivoPrograma() {
    }

    public ObjetivoPrograma(ObjetivoPredefinido objetivo, ProgramaTreino programa) {
        this.objetivo = objetivo;
        this.programa = programa;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public ObjetivoPredefinido getObjetivo() {
        return objetivo;
    }

    public void setObjetivo(ObjetivoPredefinido objetivo) {
        this.objetivo = objetivo;
    }

    public ProgramaTreino getPrograma() {
        return programa;
    }

    public void setPrograma(ProgramaTreino programa) {
        this.programa = programa;
    }
}
