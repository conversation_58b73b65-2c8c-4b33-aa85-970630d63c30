    /*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.programa;

/**
 *
 * <AUTHOR>
 */
public enum MetodoTreinoEnum {

    CIRCUITO(0, "Circuito", "É a forma tradicional do treinamento de musculação, sendo mais indicado à iniciantes e/ou na complementação de outras atividades."),
    EXAUSTIVO(1, "Exaustivo", "No método intensivo os exercícios são exigidos de tal forma que em um tempo de 10 a 15 segundos executam-se entre 8 a 12 repetições"),
    CONCENTRICO(2, "Concêntrico", "este método só deve \n"
    + "ser utilizado por atletas excepcionalmente bem preparados ao nível das suas capacidades de \n"
    + "produção  de  força.  Em  cada  sessão  faz‐se  continuamente  (5  séries)  uma  tentativa  (1 \n"
    + "repetição)  de  aumentar  o máximo  individual (1  RM)."),
    ALTERNADO(3, "Alternado", "É a forma tradicional do treinamento de musculação, sendo mais indicado à iniciantes e/ou na complementação de outras atividades.");

    private MetodoTreinoEnum(Integer id, final String descricao, final String detalhe) {
        this.id = id;
        this.descricao = descricao;
        this.detalhe = detalhe;
    }
    private Integer id;
    private String descricao;
    private String detalhe;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getDetalhe() {
        return detalhe;
    }

    public void setDetalhe(String detalhe) {
        this.detalhe = detalhe;
    }
    
    public static MetodoTreinoEnum getFromCodigo(Integer codigo){
        for(MetodoTreinoEnum tipo : MetodoTreinoEnum.values()){
            if(tipo.getId().equals(codigo)){
                return tipo;
            }
        }
        return null;
    }
}
