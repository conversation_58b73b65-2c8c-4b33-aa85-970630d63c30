/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.programa;

import java.io.Serializable;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.ManyToOne;

/**
 *
 * <AUTHOR>
 */
@Entity
public class Compromisso implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private Integer diaSemana;//DayOfWeek
    @ManyToOne
    private ProgramaTreino programa;
    private String horario;//00:00

    public Compromisso() {
    }
    
    public Compromisso(int diaSemana, final String horario, ProgramaTreino programa){
        this.programa = programa;
        this.diaSemana = diaSemana;
        this.horario = horario;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getDiaSemana() {
        return diaSemana;
    }

    public void setDiaSemana(Integer diaSemana) {
        this.diaSemana = diaSemana;
    }

    public ProgramaTreino getPrograma() {
        return programa;
    }

    public void setPrograma(ProgramaTreino programa) {
        this.programa = programa;
    }

    public String getHorario() {
        return horario;
    }

    public void setHorario(String horario) {
        this.horario = horario;
    }
}
