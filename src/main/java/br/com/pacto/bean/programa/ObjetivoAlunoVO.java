package br.com.pacto.bean.programa;

import br.com.pacto.bean.usuario.StatusObjetivoAlunoEnum;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class ObjetivoAlunoVO {

    private Integer codigo;
    private ObjetivoPredefinidoDTO objetivoPredefinido;
    private Boolean primario;
    private String dataInicio;
    private String dataFinal;
    private String descricao;
    private StatusObjetivoAlunoEnum status;
    private List<ObjetivoIntermediarioAlunoVO> objetivosIntermediarios;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public ObjetivoPredefinidoDTO getObjetivoPredefinido() {
        return objetivoPredefinido;
    }

    public void setObjetivoPredefinido(ObjetivoPredefinidoDTO objetivoPredefinido) {
        this.objetivoPredefinido = objetivoPredefinido;
    }

    public Boolean getPrimario() {
        return primario;
    }

    public void setPrimario(Boolean primario) {
        this.primario = primario;
    }

    public String getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(String dataInicio) {
        this.dataInicio = dataInicio;
    }

    public String getDataFinal() {
        return dataFinal;
    }

    public void setDataFinal(String dataFinal) {
        this.dataFinal = dataFinal;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public StatusObjetivoAlunoEnum getStatus() {
        return status;
    }

    public void setStatus(StatusObjetivoAlunoEnum status) {
        this.status = status;
    }

    public List<ObjetivoIntermediarioAlunoVO> getObjetivosIntermediarios() {
        return objetivosIntermediarios;
    }

    public void setObjetivosIntermediarios(List<ObjetivoIntermediarioAlunoVO> objetivosIntermediarios) {
        this.objetivosIntermediarios = objetivosIntermediarios;
    }
}
