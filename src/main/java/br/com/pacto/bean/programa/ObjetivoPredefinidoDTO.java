package br.com.pacto.bean.programa;

import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class ObjetivoPredefinidoDTO {

    private Integer id;
    private String nome;

    public ObjetivoPredefinidoDTO() {

    }
    public ObjetivoPredefinidoDTO(ObjetivoPredefinido obj) {
        this.id = obj.getCodigo();
        this.nome = obj.getNome();
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }
}
