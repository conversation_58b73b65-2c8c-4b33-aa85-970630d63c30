package br.com.pacto.bean.locacao;

import java.io.Serializable;

public class LocacaoPlayCanceladaFinalizadaDTO implements Serializable {

    private Integer responsavelLancamento;
    private Integer ambiente;
    private Integer locacaoHorario;
    private String dia;
    private String justificativa;
    private Boolean cancelada;
    private Boolean finalizada;

    public Integer getResponsavelLancamento() {
        return responsavelLancamento;
    }

    public void setResponsavelLancamento(Integer responsavelLancamento) {
        this.responsavelLancamento = responsavelLancamento;
    }

    public Integer getAmbiente() {
        return ambiente;
    }

    public void setAmbiente(Integer ambiente) {
        this.ambiente = ambiente;
    }

    public Integer getLocacaoHorario() {
        return locacaoHorario;
    }

    public void setLocacaoHorario(Integer locacaoHorario) {
        this.locacaoHorario = locacaoHorario;
    }

    public String getDia() {
        return dia;
    }

    public void setDia(String dia) {
        this.dia = dia;
    }

    public String getJustificativa() {
        return justificativa;
    }

    public void setJustificativa(String justificativa) {
        this.justificativa = justificativa;
    }

    public Boolean getCancelada() {
        return cancelada;
    }

    public void setCancelada(Boolean cancelada) {
        this.cancelada = cancelada;
    }

    public Boolean getFinalizada() {
        return finalizada;
    }

    public void setFinalizada(Boolean finalizada) {
        this.finalizada = finalizada;
    }
}
