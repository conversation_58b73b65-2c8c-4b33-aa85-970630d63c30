package br.com.pacto.bean.locacao;

import br.com.pacto.controller.json.locacao.ItemValidacaoLocacaoTO;

import javax.persistence.*;
import java.io.Serializable;

@Entity
public class ItemValidacaoLocacao implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    @ManyToOne
    @JoinColumn(name = "locacao", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private Locacao locacao;
    private Integer plano;
    private Integer produto;
    private String descricao;
    private Double valorFinal;

    public ItemValidacaoLocacao() {}

    public ItemValidacaoLocacao(ItemValidacaoLocacaoTO itemValidacaoLocacaoTO) {
        this.codigo = itemValidacaoLocacaoTO.getCodigo();
        this.plano = itemValidacaoLocacaoTO.getPlano();
        this.produto = itemValidacaoLocacaoTO.getProduto();
        this.descricao = itemValidacaoLocacaoTO.getDescricao();
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Locacao getLocacao() {
        return locacao;
    }

    public void setLocacao(Locacao locacao) {
        this.locacao = locacao;
    }

    public Integer getPlano() {
        return plano;
    }

    public void setPlano(Integer plano) {
        this.plano = plano;
    }

    public Integer getProduto() {
        return produto;
    }

    public void setProduto(Integer produto) {
        this.produto = produto;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Double getValorFinal() {
        return valorFinal;
    }

    public void setValorFinal(Double valorFinal) {
        this.valorFinal = valorFinal;
    }
}
