package br.com.pacto.bean.locacao;

import br.com.pacto.controller.json.locacao.LocacaoProdutoSugeridoTO;

import javax.persistence.*;
import java.io.Serializable;

@Entity
@Table
public class LocacaoProdutoSugerido implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private Integer codigoProduto;
    private Double valor;
    private Boolean obrigatorio;

    @ManyToOne
    @JoinColumn(name = "locacao", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private Locacao locacao;

    public LocacaoProdutoSugerido() {}
    public LocacaoProdutoSugerido(LocacaoProdutoSugeridoTO locacaoProdutoSugeridoTO) {
        this.codigo = locacaoProdutoSugeridoTO.getCodigo();
        this.codigoProduto = locacaoProdutoSugeridoTO.getProduto().getCodigo();
        this.valor = locacaoProdutoSugeridoTO.getValor();
        this.obrigatorio = locacaoProdutoSugeridoTO.getObrigatorio();
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getCodigoProduto() {
        return codigoProduto;
    }

    public void setCodigoProduto(Integer produto) {
        this.codigoProduto = produto;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public Boolean getObrigatorio() {
        return obrigatorio;
    }

    public void setObrigatorio(Boolean obrigatorio) {
        this.obrigatorio = obrigatorio;
    }

    public Locacao getLocacao() {
        return locacao;
    }

    public void setLocacao(Locacao locacao) {
        this.locacao = locacao;
    }
}
