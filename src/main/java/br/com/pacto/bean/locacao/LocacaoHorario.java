package br.com.pacto.bean.locacao;

import br.com.pacto.bean.aula.Ambiente;
import br.com.pacto.controller.json.locacao.LocacaoHorarioTO;

import javax.persistence.*;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Entity
@Table
public class LocacaoHorario implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private String diaSemana;
    private String horaInicio;
    private String horaFim;
    private Boolean permiteAgendarPeloAppTreino;
    private Boolean ativo;
    private Integer responsavel;
    private Integer tempoMinimoMinutos;

    @ManyToOne
    @JoinColumn(name = "locacao", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private Locacao locacao;
    @OneToMany(mappedBy = "locacaoHorario", cascade = CascadeType.ALL, orphanRemoval = true, targetEntity = AmbienteHorarioLocacao.class)
    private List<AmbienteHorarioLocacao> ambientes = new ArrayList<>();

    public LocacaoHorario() {}
    public LocacaoHorario(LocacaoHorarioTO locacaoHorarioTO) {
        this.codigo = locacaoHorarioTO.getCodigo();
        this.diaSemana = locacaoHorarioTO.getDiaSemana();
        this.horaInicio = locacaoHorarioTO.getHoraInicio();
        this.horaFim = locacaoHorarioTO.getHoraFim();
        this.permiteAgendarPeloAppTreino = locacaoHorarioTO.getPermiteAgendarPeloAppTreino();
        this.responsavel = locacaoHorarioTO.getResponsavel().getCodigo();
        if (locacaoHorarioTO.getAmbientes() != null) {
            locacaoHorarioTO.getAmbientes().forEach(a -> {
                AmbienteHorarioLocacao ambienteHorarioLocacao = new AmbienteHorarioLocacao();
                ambienteHorarioLocacao.setCodigo(a.getCodigo());
                ambienteHorarioLocacao.setAmbiente(new Ambiente());
                ambienteHorarioLocacao.getAmbiente().setCodigo(a.getAmbiente().getCodigo());
                ambienteHorarioLocacao.setLocacaoHorario(this);
                this.getAmbientes().add(ambienteHorarioLocacao);
            });
        }
        this.ativo = locacaoHorarioTO.getAtivo();
        this.tempoMinimoMinutos = locacaoHorarioTO.getTempoMinimoMinutos();
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDiaSemana() {
        return diaSemana;
    }

    public void setDiaSemana(String diaSemana) {
        this.diaSemana = diaSemana;
    }

    public String getHoraInicio() {
        return horaInicio;
    }

    public void setHoraInicio(String horaInicio) {
        this.horaInicio = horaInicio;
    }

    public String getHoraFim() {
        return horaFim;
    }

    public void setHoraFim(String horaFim) {
        this.horaFim = horaFim;
    }

    public Boolean getPermiteAgendarPeloAppTreino() {
        return permiteAgendarPeloAppTreino;
    }

    public void setPermiteAgendarPeloAppTreino(Boolean permiteAgendarPeloAppTreino) {
        this.permiteAgendarPeloAppTreino = permiteAgendarPeloAppTreino;
    }

    public Locacao getLocacao() {
        return locacao;
    }

    public void setLocacao(Locacao locacao) {
        this.locacao = locacao;
    }

    public Integer getResponsavel() {
        return responsavel;
    }

    public void setResponsavel(Integer responsavel) {
        this.responsavel = responsavel;
    }

    public List<AmbienteHorarioLocacao> getAmbientes() {
        return ambientes;
    }

    public void setAmbientes(List<AmbienteHorarioLocacao> ambientes) {
        this.ambientes = ambientes;
    }

    public Boolean getAtivo() {
        return ativo;
    }

    public void setAtivo(Boolean ativo) {
        this.ativo = ativo;
    }

    public Integer getTempoMinimoMinutos() {
        return tempoMinimoMinutos;
    }

    public void setTempoMinimoMinutos(Integer tempoMinimoMinutos) {
        this.tempoMinimoMinutos = tempoMinimoMinutos;
    }
}
