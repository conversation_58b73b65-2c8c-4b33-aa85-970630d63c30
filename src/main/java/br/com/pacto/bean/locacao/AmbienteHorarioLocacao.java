package br.com.pacto.bean.locacao;

import br.com.pacto.bean.aula.Ambiente;

import javax.persistence.*;
import java.io.Serializable;

@Entity
@Table
public class AmbienteHorarioLocacao implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    @ManyToOne
    @JoinColumn(name = "ambiente", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private Ambiente ambiente;
    @ManyToOne
    @JoinColumn(name = "locacaoHorario", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private LocacaoHorario locacaoHorario;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Ambiente getAmbiente() {
        return ambiente;
    }

    public void setAmbiente(Ambiente ambiente) {
        this.ambiente = ambiente;
    }

    public LocacaoHorario getLocacaoHorario() {
        return locacaoHorario;
    }

    public void setLocacaoHorario(LocacaoHorario locacaoHorario) {
        this.locacaoHorario = locacaoHorario;
    }
}
