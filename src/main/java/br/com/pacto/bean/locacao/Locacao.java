package br.com.pacto.bean.locacao;

import br.com.pacto.controller.json.locacao.LocacaoTO;
import br.com.pacto.controller.json.locacao.TipoHorarioLocacaoEnum;
import br.com.pacto.util.UteisValidacao;

import javax.persistence.*;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Entity
@Table
public class Locacao implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private String nome;
    private String descricao;
    private Boolean ativo;
    @Enumerated(EnumType.ORDINAL)
    private TipoHorarioLocacaoEnum tipoHorario;
    private String cor;
    private Integer duracaoMinutos;
    private Integer produto;
    private Integer tempoMinimoMinutos;
    private String fotoKey;
    private String identificadorFotoKey;
    private Date dataInicial;
    private Date dataFinal;
    private Integer tipoValidacao;
    private Integer empresa;

    @OneToMany(mappedBy = "locacao", cascade = CascadeType.ALL, orphanRemoval = true, targetEntity = LocacaoHorario.class)
    private List<LocacaoHorario> horarios = new ArrayList<>();

    @OneToMany(mappedBy = "locacao", cascade = CascadeType.ALL, orphanRemoval = true, targetEntity = LocacaoProdutoSugerido.class)
    private List<LocacaoProdutoSugerido> produtosSugeridos = new ArrayList<>();

    @OneToMany(fetch = FetchType.EAGER, mappedBy = "locacao", cascade = CascadeType.ALL, orphanRemoval = true, targetEntity = ItemValidacaoLocacao.class)
    private List<ItemValidacaoLocacao> itensValidacao = new ArrayList<>();

    public Locacao() {}

    public Locacao(Locacao entity, LocacaoTO locacaoTO) {
        this.codigo = locacaoTO.getCodigo();
        this.nome = locacaoTO.getNome();
        this.descricao = locacaoTO.getDescricao();
        this.ativo = locacaoTO.getAtivo();
        this.tipoHorario = TipoHorarioLocacaoEnum.getByCodigo(locacaoTO.getTipoHorario());
        this.cor = locacaoTO.getCor();
        if (this.tipoHorario.getCodigo().equals(TipoHorarioLocacaoEnum.LIVRE.getCodigo())
                || this.tipoHorario.getCodigo().equals(TipoHorarioLocacaoEnum.PRE_DEFINIDO.getCodigo())) {
            this.duracaoMinutos = locacaoTO.getDuracaoMinutos();
            this.tempoMinimoMinutos = locacaoTO.getTempoMinimoMinutos();
        } else {
            this.tipoValidacao = locacaoTO.getTipoValidacao();
        }
        if (locacaoTO.getProduto() != null && UteisValidacao.notEmptyNumber(locacaoTO.getProduto().getCodigo())) {
            this.produto = locacaoTO.getProduto().getCodigo();
        }
        this.dataInicial = locacaoTO.getDataInicial();
        this.dataFinal = locacaoTO.getDataFinal();
        if (locacaoTO.getHorarios() != null) {
            locacaoTO.getHorarios().forEach(locacaoHorarioTO -> {
                LocacaoHorario locacaoHorarioEntity = new LocacaoHorario(locacaoHorarioTO);
                locacaoHorarioEntity.setLocacao(this);
                this.getHorarios().add(locacaoHorarioEntity);
            });
        }
        if (locacaoTO.getProdutosSugeridos() != null) {
            locacaoTO.getProdutosSugeridos().forEach(locacaoProdutoSugeridoTO -> {
                LocacaoProdutoSugerido locacaoProdutoSugeridoEntity = new LocacaoProdutoSugerido(locacaoProdutoSugeridoTO);
                locacaoProdutoSugeridoEntity.setLocacao(this);
                this.getProdutosSugeridos().add(locacaoProdutoSugeridoEntity);
            });
        }
        if (locacaoTO.getItensValidacao() != null) {
            locacaoTO.getItensValidacao().forEach(pv -> {
                ItemValidacaoLocacao itemValidacaoLocacaoEntity = new ItemValidacaoLocacao(pv);
                itemValidacaoLocacaoEntity.setLocacao(this);
                this.getItensValidacao().add(itemValidacaoLocacaoEntity);
            });
        }

        if (entity != null) {
            setIdentificadorFotoKey(entity.getIdentificadorFotoKey());
            setFotoKey(entity.getFotoKey());
        }
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Boolean getAtivo() {
        return ativo;
    }

    public void setAtivo(Boolean ativo) {
        this.ativo = ativo;
    }

    public TipoHorarioLocacaoEnum getTipoHorario() {
        return tipoHorario;
    }

    public void setTipoHorario(TipoHorarioLocacaoEnum tipoHorario) {
        this.tipoHorario = tipoHorario;
    }

    public String getCor() {
        return cor;
    }

    public void setCor(String cor) {
        this.cor = cor;
    }

    public Integer getDuracaoMinutos() {
        return duracaoMinutos;
    }

    public void setDuracaoMinutos(Integer duracaoMinutos) {
        this.duracaoMinutos = duracaoMinutos;
    }

    public Integer getTempoMinimoMinutos() {
        return tempoMinimoMinutos;
    }

    public void setTempoMinimoMinutos(Integer tempoMinimoMinutos) {
        this.tempoMinimoMinutos = tempoMinimoMinutos;
    }

    public String getFotoKey() {
        return fotoKey;
    }

    public void setFotoKey(String fotoKey) {
        this.fotoKey = fotoKey;
    }

    public Date getDataInicial() {
        return dataInicial;
    }

    public void setDataInicial(Date dataInicial) {
        this.dataInicial = dataInicial;
    }

    public Date getDataFinal() {
        return dataFinal;
    }

    public void setDataFinal(Date dataFinal) {
        this.dataFinal = dataFinal;
    }

    public Integer getTipoValidacao() {
        return tipoValidacao;
    }

    public void setTipoValidacao(Integer tipoValidacao) {
        this.tipoValidacao = tipoValidacao;
    }

    public List<LocacaoHorario> getHorarios() {
        if (horarios == null) {
            horarios = new ArrayList<>();
        }
        return horarios;
    }

    public void setHorarios(List<LocacaoHorario> horarios) {
        this.horarios = horarios;
    }

    public List<LocacaoProdutoSugerido> getProdutosSugeridos() {
        if (produtosSugeridos == null) {
            produtosSugeridos = new ArrayList<>();
        }
        return produtosSugeridos;
    }

    public void setProdutosSugeridos(List<LocacaoProdutoSugerido> produtoSugeridos) {
        this.produtosSugeridos = produtoSugeridos;
    }

    public List<ItemValidacaoLocacao> getItensValidacao() {
        return itensValidacao;
    }

    public void setItensValidacao(List<ItemValidacaoLocacao> itensValidacao) {
        this.itensValidacao = itensValidacao;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public Integer getProduto() {
        return produto;
    }

    public void setProduto(Integer produto) {
        this.produto = produto;
    }

    public String getIdentificadorFotoKey() {
        return identificadorFotoKey;
    }

    public void setIdentificadorFotoKey(String identificadorFotoKey) {
        this.identificadorFotoKey = identificadorFotoKey;
    }
}
