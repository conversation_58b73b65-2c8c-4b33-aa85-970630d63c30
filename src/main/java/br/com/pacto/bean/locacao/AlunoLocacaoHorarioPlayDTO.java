package br.com.pacto.bean.locacao;

import java.io.Serializable;

public class AlunoLocacaoHorarioPlayDTO implements Serializable {

    private Integer codigo;
    private Integer responsavelLancamento;
    private Integer empresa;
    private Integer cliente;
    private Integer locacaoHorario;
    private Integer ambiente;
    private String dia;
    private Boolean checkin;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getResponsavelLancamento() {
        return responsavelLancamento;
    }

    public void setResponsavelLancamento(Integer responsavelLancamento) {
        this.responsavelLancamento = responsavelLancamento;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public Integer getCliente() {
        return cliente;
    }

    public void setCliente(Integer cliente) {
        this.cliente = cliente;
    }

    public Integer getLocacaoHorario() {
        return locacaoHorario;
    }

    public void setLocacaoHorario(Integer locacaoHorario) {
        this.locacaoHorario = locacaoHorario;
    }

    public Integer getAmbiente() {
        return ambiente;
    }

    public void setAmbiente(Integer ambiente) {
        this.ambiente = ambiente;
    }

    public String getDia() {
        return dia;
    }

    public void setDia(String dia) {
        this.dia = dia;
    }

    public Boolean getCheckin() {
        return checkin;
    }

    public void setCheckin(Boolean checkin) {
        this.checkin = checkin;
    }
}
