package br.com.pacto.bean.locacao;

import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table
public class AlunoLocacaoHorarioPlay implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private Date dataLancamento;
    private Integer responsavelLancamento;
    @ManyToOne(fetch = FetchType.EAGER)
    private Empresa empresa;
    @ManyToOne(fetch = FetchType.EAGER)
    private ClienteSintetico cliente;
    @ManyToOne(fetch = FetchType.EAGER)
    private LocacaoHorario locacaoHorario;
    private Integer ambiente;
    private Date dia;
    private Boolean checkin;

    public AlunoLocacaoHorarioPlay() {
    }

    public AlunoLocacaoHorarioPlay(AlunoLocacaoHorarioPlayDTO alunoLocacaoHorarioPlayDTO) throws Exception {
        this.setResponsavelLancamento(alunoLocacaoHorarioPlayDTO.getResponsavelLancamento());
        this.setEmpresa(new Empresa());
        this.getEmpresa().setCodigo(alunoLocacaoHorarioPlayDTO.getEmpresa());
        this.setCliente(new ClienteSintetico());
        this.getCliente().setCodigo(alunoLocacaoHorarioPlayDTO.getCliente());
        this.setLocacaoHorario(new LocacaoHorario());
        this.getLocacaoHorario().setCodigo(alunoLocacaoHorarioPlayDTO.getLocacaoHorario());
        this.setAmbiente(alunoLocacaoHorarioPlayDTO.getAmbiente());
        this.setDia(Uteis.getDate(alunoLocacaoHorarioPlayDTO.getDia(), "yyyyMMdd"));
        this.setDataLancamento(Calendario.hoje());
        this.setCheckin(alunoLocacaoHorarioPlayDTO.getCheckin());
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Date getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public Integer getResponsavelLancamento() {
        return responsavelLancamento;
    }

    public void setResponsavelLancamento(Integer responsavelLancamento) {
        this.responsavelLancamento = responsavelLancamento;
    }

    public Empresa getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }

    public ClienteSintetico getCliente() {
        return cliente;
    }

    public void setCliente(ClienteSintetico cliente) {
        this.cliente = cliente;
    }

    public LocacaoHorario getLocacaoHorario() {
        return locacaoHorario;
    }

    public void setLocacaoHorario(LocacaoHorario locacaoHorario) {
        this.locacaoHorario = locacaoHorario;
    }

    public Integer getAmbiente() {
        return ambiente;
    }

    public void setAmbiente(Integer ambiente) {
        this.ambiente = ambiente;
    }

    public Date getDia() {
        return dia;
    }

    public void setDia(Date dia) {
        this.dia = dia;
    }

    public Boolean getCheckin() {
        return checkin;
    }

    public void setCheckin(Boolean checkin) {
        this.checkin = checkin;
    }
}
