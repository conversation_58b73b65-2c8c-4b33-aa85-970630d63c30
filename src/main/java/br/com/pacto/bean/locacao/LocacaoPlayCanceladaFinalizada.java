package br.com.pacto.bean.locacao;

import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table
public class LocacaoPlayCanceladaFinalizada implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private Date dataLancamento;
    private Integer responsavelLancamento;
    private Integer ambiente;
    @ManyToOne(fetch = FetchType.EAGER)
    private LocacaoHorario locacaoHorario;
    private Date dia;
    private String justificativa;
    private Boolean cancelada;
    private Boolean finalizada;

    public LocacaoPlayCanceladaFinalizada() {
    }

    public LocacaoPlayCanceladaFinalizada(LocacaoPlayCanceladaFinalizadaDTO locacaoPlayCanceladaFinalizadaDTO) throws Exception {
        this.setResponsavelLancamento(locacaoPlayCanceladaFinalizadaDTO.getResponsavelLancamento());
        this.setAmbiente(locacaoPlayCanceladaFinalizadaDTO.getAmbiente());
        this.setLocacaoHorario(new LocacaoHorario());
        this.getLocacaoHorario().setCodigo(locacaoPlayCanceladaFinalizadaDTO.getLocacaoHorario());
        this.setDia(Uteis.getDate(locacaoPlayCanceladaFinalizadaDTO.getDia(), "yyyyMMdd"));
        this.setDataLancamento(Calendario.hoje());
        this.setJustificativa(locacaoPlayCanceladaFinalizadaDTO.getJustificativa());
        this.setCancelada(locacaoPlayCanceladaFinalizadaDTO.getCancelada());
        this.setFinalizada(locacaoPlayCanceladaFinalizadaDTO.getFinalizada());
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Date getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public Integer getResponsavelLancamento() {
        return responsavelLancamento;
    }

    public void setResponsavelLancamento(Integer responsavelLancamento) {
        this.responsavelLancamento = responsavelLancamento;
    }

    public Integer getAmbiente() {
        return ambiente;
    }

    public void setAmbiente(Integer ambiente) {
        this.ambiente = ambiente;
    }

    public LocacaoHorario getLocacaoHorario() {
        return locacaoHorario;
    }

    public void setLocacaoHorario(LocacaoHorario locacaoHorario) {
        this.locacaoHorario = locacaoHorario;
    }

    public Date getDia() {
        return dia;
    }

    public void setDia(Date dia) {
        this.dia = dia;
    }

    public String getJustificativa() {
        return justificativa;
    }

    public void setJustificativa(String justificativa) {
        this.justificativa = justificativa;
    }

    public Boolean getCancelada() {
        return cancelada;
    }

    public void setCancelada(Boolean cancelada) {
        this.cancelada = cancelada;
    }

    public Boolean getFinalizada() {
        return finalizada;
    }

    public void setFinalizada(Boolean finalizada) {
        this.finalizada = finalizada;
    }
}
