package br.com.pacto.bean.locacao;

import br.com.pacto.objeto.Uteis;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table
public class AlunoLocacaoHorarioPlayCheckinCheckout implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private Date horaCheckin;
    private Date horaCheckout;
    @ManyToOne(fetch = FetchType.EAGER)
    private AlunoLocacaoHorarioPlay alunoLocacaoHorarioPlay;

    public AlunoLocacaoHorarioPlayCheckinCheckout() {
    }

    public AlunoLocacaoHorarioPlayCheckinCheckout(AlunoLocacaoHorarioPlayCheckinCheckoutDTO dto) throws Exception {
        if(dto.getCheckin()){
            this.horaCheckin = Uteis.getDate(dto.getDataHora(), "yyyy/MM/dd HH:mm:ss");
        }else{
            this.horaCheckout = Uteis.getDate(dto.getDataHora(), "yyyy/MM/dd HH:mm:ss");
        }
        setAlunoLocacaoHorarioPlay(new AlunoLocacaoHorarioPlay());
        getAlunoLocacaoHorarioPlay().setCodigo(dto.getAlunoLocacaoHorarioPlay());
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Date getHoraCheckin() {
        return horaCheckin;
    }

    public void setHoraCheckin(Date horaCheckin) {
        this.horaCheckin = horaCheckin;
    }

    public Date getHoraCheckout() {
        return horaCheckout;
    }

    public void setHoraCheckout(Date horaCheckout) {
        this.horaCheckout = horaCheckout;
    }

    public AlunoLocacaoHorarioPlay getAlunoLocacaoHorarioPlay() {
        return alunoLocacaoHorarioPlay;
    }

    public void setAlunoLocacaoHorarioPlay(AlunoLocacaoHorarioPlay alunoLocacaoHorarioPlay) {
        this.alunoLocacaoHorarioPlay = alunoLocacaoHorarioPlay;
    }
}
