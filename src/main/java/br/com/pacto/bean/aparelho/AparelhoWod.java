/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.aparelho;

import br.com.pacto.bean.atividade.Atividade;
import br.com.pacto.bean.wod.Wod;

import javax.persistence.*;
import java.io.Serializable;

/*
 * <AUTHOR>
 */
@Entity
public class AparelhoWod implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private Integer ordem;
    @ManyToOne
    private Aparelho aparelho;
    @ManyToOne
    private Wod wod;

    public AparelhoWod() {
    }

    public AparelhoWod(Wod wod, Aparelho aparelho) {
        this.wod = wod;
        this.aparelho = aparelho;
    }

    public Integer getCodigo() {
        if (codigo == null) {
            codigo = 0;
        }
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getOrdem() {
        if (ordem == null) {
            ordem = 0;
        }
        return ordem;
    }

    public void setOrdem(Integer ordem) {
        this.ordem = ordem;
    }

    public Wod getWod() {
        if (wod == null) {
            wod = new Wod();
        }
        return wod;
    }

    public void setWod(Wod wod) {
        this.wod = wod;
    }

    public Aparelho getAparelho() {
        if (aparelho == null) {
            aparelho = new Aparelho();
        }
        return aparelho;
    }

    public void setAparelho(Aparelho aparelho) {
        this.aparelho = aparelho;
    }
}
