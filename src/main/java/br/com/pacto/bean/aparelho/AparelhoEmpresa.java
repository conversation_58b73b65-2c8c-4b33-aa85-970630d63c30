/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.aparelho;

import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.service.intf.empresa.EmpresaService;
import java.io.Serializable;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.ManyToOne;

/**
 *
 * <AUTHOR>
 */

@Entity
public class AparelhoEmpresa implements Serializable{
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    @ManyToOne
    private Aparelho aparelho = new Aparelho();
    @ManyToOne
    private Empresa empresa = new Empresa();
    private String identificador;
    
    public AparelhoEmpresa(){
        
    }
    
    public AparelhoEmpresa(Aparelho aparelho, Empresa empresa){
        this.aparelho = aparelho;
        this.empresa = empresa;
    
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Aparelho getAparelho() {
        return aparelho;
    }

    public void setAparelho(Aparelho aparelho) {
        this.aparelho = aparelho;
    }

    public Empresa getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }

    public String getIdentificador() {
        return identificador;
    }

    public void setIdentificador(String identificador) {
        this.identificador = identificador;
    }
    
}
