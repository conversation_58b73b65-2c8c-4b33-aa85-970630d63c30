/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.aparelho;

import br.com.pacto.bean.atividade.AtividadeAparelho;
import br.com.pacto.bean.aparelho.AparelhoWod;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.util.impl.UtilReflection;
import org.apache.commons.lang3.SerializationUtils;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import static java.util.Objects.isNull;

/**
 * <AUTHOR>
 */
@Entity
@Table
public class Aparelho implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private String nome;
    @OneToMany(targetEntity = AtividadeAparelho.class, cascade = {CascadeType.PERSIST, CascadeType.MERGE, CascadeType.REMOVE}, orphanRemoval = true, fetch = FetchType.LAZY, mappedBy = "aparelho")
    private List<AtividadeAparelho> atividades = new ArrayList<AtividadeAparelho>();
    @OneToMany(targetEntity = AparelhoAjuste.class, cascade = {CascadeType.PERSIST, CascadeType.MERGE, CascadeType.REMOVE}, orphanRemoval = true, fetch = FetchType.LAZY, mappedBy = "aparelho")
    private List<AparelhoAjuste> ajustes = new ArrayList<AparelhoAjuste>();
    @OneToMany(targetEntity = AparelhoEmpresa.class, cascade = {CascadeType.PERSIST, CascadeType.MERGE, CascadeType.REMOVE}, orphanRemoval = true, fetch = FetchType.LAZY, mappedBy = "aparelho")
    private List<AparelhoEmpresa> empresasHabilitadas = new ArrayList<AparelhoEmpresa>();
    @OneToMany(targetEntity = AparelhoWod.class, cascade = {CascadeType.PERSIST, CascadeType.MERGE, CascadeType.REMOVE}, orphanRemoval = true, fetch = FetchType.LAZY, mappedBy = "aparelho")
    private List<AparelhoWod> aparelhosWod = new ArrayList<AparelhoWod>();
    private Boolean todasEmpresas = true;
    @Column(columnDefinition = "boolean DEFAULT false")
    private Boolean crossfit = Boolean.FALSE;
    private String sigla;
    private String icone;
    private Boolean usarEmReservaEquipamentos = Boolean.FALSE;
    private String sensorSelfloops;


    public List<AparelhoAjuste> getAjustes() {
        return ajustes;
    }

    public void setAjustes(List<AparelhoAjuste> ajustes) {
        this.ajustes = ajustes;
    }

    public Aparelho(Integer codigo) {
        this.codigo = codigo;
    }

    public Aparelho(final String nome) {
        this.nome = nome;
    }

    public String obterNome(Integer empresa) {
        try {
            for (AparelhoEmpresa e : getEmpresasHabilitadas()) {
                if (e.getEmpresa().getCodZW().equals(empresa)) {
                    return e.getAparelho().getNome();
                }
            }
            return nome;
        } catch (Exception e) {
            return nome;
        }
    }

    public Aparelho() {
    }

    public List<AtividadeAparelho> getAtividades() {
        return atividades;
    }

    public void setAtividades(List<AtividadeAparelho> atividades) {
        this.atividades = atividades;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public List<AparelhoEmpresa> getEmpresasHabilitadas() {
        return empresasHabilitadas;
    }

    public void setEmpresasHabilitadas(List<AparelhoEmpresa> empresasHabilitadas) {
        this.empresasHabilitadas = empresasHabilitadas;
    }

    public Boolean getTodasEmpresas() {
        return todasEmpresas;
    }

    public void setTodasEmpresas(Boolean todasEmpresas) {
        this.todasEmpresas = todasEmpresas;
    }

    public List<AparelhoWod> getAparelhosWod() {
        return aparelhosWod;
    }

    public void setAparelhosWod(List<AparelhoWod> aparelhosWod) {
        this.aparelhosWod = aparelhosWod;
    }

    public String getArrayNomeEmpresas() {
        List<String> tmp = new ArrayList<String>();
        for (AparelhoEmpresa atve : empresasHabilitadas) {
            String nomeTmp = atve.getEmpresa().getNome();
            if (nomeTmp.contains("-")) {
                try {
                    nomeTmp = nomeTmp.substring(nomeTmp.indexOf("-") + 1).trim();
                } catch (Exception e) {
                }
            }
            tmp.add(nomeTmp);
        }
        if (tmp.isEmpty()) {
            return "Todas";
        } else {
            return Uteis.splitFromArray(tmp.toArray(), false);
        }
    }

    public Boolean getCrossfit() {
        return crossfit;
    }

    public void setCrossfit(Boolean crossfit) {
        this.crossfit = crossfit;
    }

    public String getSigla() {
        return sigla;
    }

    public void setSigla(String sigla) {
        this.sigla = sigla;
    }

    public String getIcone() {
        return icone;
    }

    public void setIcone(String icone) {
        this.icone = icone;
    }

    public Boolean getUsarEmReservaEquipamentos() {
        if (usarEmReservaEquipamentos == null) {
            return Boolean.FALSE;
        }
        return usarEmReservaEquipamentos;
    }

    public void setUsarEmReservaEquipamentos(Boolean usaremreservaequipamentos) {
        this.usarEmReservaEquipamentos = usaremreservaequipamentos;
    }

    public String getSensorSelfloops() {
        return sensorSelfloops;
    }

    public void setSensorSelfloops(String sensorSelfloops) {
        this.sensorSelfloops = sensorSelfloops;
    }

    public String getDescricaoParaLog(Aparelho v2) {
        try {
            StringBuilder log = new StringBuilder();
            if (v2 == null) {
                v2 = new Aparelho();
            }
            // Compara nome e código
            log.append(UtilReflection.difference(this, v2));

            //Compara as Atividades Aparelho
            if (!isNull(this.getAtividades()) && !isNull(v2.getAtividades())) {

                Iterator<AtividadeAparelho> ListaV1 = this.getAtividades().iterator();
                Iterator<AtividadeAparelho> ListaV2 = v2.getAtividades().iterator();

                while (ListaV1.hasNext() || ListaV2.hasNext()) {
                    AtividadeAparelho v1Atividade = ListaV1.hasNext() ? ListaV1.next() : null;
                    AtividadeAparelho v2Atividade = ListaV2.hasNext() ? ListaV2.next() : null;

                    log.append(UtilReflection.difference(v1Atividade, v2Atividade));
                }
            }
            //Compara o Aparelho Ajuste
            if (!isNull(this.getAjustes()) && !isNull(v2.getAjustes())) {

                Iterator<AparelhoAjuste> ListaV1 = this.getAjustes().iterator();
                Iterator<AparelhoAjuste> ListaV2 = v2.getAjustes().iterator();

                while (ListaV1.hasNext() || ListaV2.hasNext()) {
                    AparelhoAjuste v1Ajuste = ListaV1.hasNext() ? ListaV1.next() : null;
                    AparelhoAjuste v2Ajuste = ListaV2.hasNext() ? ListaV2.next() : null;

                    log.append(UtilReflection.difference(v1Ajuste, v2Ajuste));
                }
            }
            return log.toString();
        } catch (Exception ex) {
            ex.printStackTrace();
            return "ERRO gerar log";
        }
    }

    @Override
    public Aparelho clone() {
        try {
            Aparelho cloned = SerializationUtils.clone(this);

            cloned.setAjustes(new ArrayList<AparelhoAjuste>(this.getAjustes()));
            cloned.setAtividades(new ArrayList<AtividadeAparelho>(this.getAtividades()));
            cloned.setEmpresasHabilitadas(new ArrayList<AparelhoEmpresa>(this.getEmpresasHabilitadas()));
            cloned.setAparelhosWod(new ArrayList<AparelhoWod>(this.getAparelhosWod()));

            return cloned;
        } catch (Exception ex) {
            throw new RuntimeException("Erro ao clonar o objeto Aparelho");
        }
    }
}