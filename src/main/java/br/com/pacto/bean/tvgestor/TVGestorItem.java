/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.tvgestor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.persistence.CascadeType;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;

/**
 *
 * <AUTHOR>
 */
@Entity
public class TVGestorItem implements Serializable{
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    @Temporal(TemporalType.TIMESTAMP)
    private Date dataProcessamento;
    @Temporal(TemporalType.TIMESTAMP)
    private Date dia;
    private Integer hora;
    private Integer alunosEsperados;
    private Integer acessos;
    private Integer empresa;
    @OneToMany(cascade = {CascadeType.PERSIST, CascadeType.MERGE, CascadeType.REMOVE}, fetch = FetchType.LAZY, mappedBy = "item", targetEntity = TVGestorItemFavorito.class)
    private List<TVGestorItemFavorito> favoritos = new ArrayList<TVGestorItemFavorito>();
    @Transient
    private List<Integer> favoritoscods = new ArrayList<Integer>();
    @Transient
    private boolean contemFavoritos = false;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Date getDataProcessamento() {
        return dataProcessamento;
    }

    public void setDataProcessamento(Date dataProcessamento) {
        this.dataProcessamento = dataProcessamento;
    }

    public Date getDia() {
        return dia;
    }

    public void setDia(Date dia) {
        this.dia = dia;
    }

    public Integer getHora() {
        return hora;
    }

    public void setHora(Integer hora) {
        this.hora = hora;
    }

    public Integer getAlunosEsperados() {
        return alunosEsperados;
    }

    public void setAlunosEsperados(Integer alunosEsperados) {
        this.alunosEsperados = alunosEsperados;
    }

    public Integer getAcessos() {
        return acessos;
    }

    public void setAcessos(Integer acessos) {
        this.acessos = acessos;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public List<TVGestorItemFavorito> getFavoritos() {
        if(favoritos == null){
           favoritos = new ArrayList<TVGestorItemFavorito>();
        }
        return favoritos;
    }

    public void setFavoritos(List<TVGestorItemFavorito> favoritos) {
        this.favoritos = favoritos;
    }

    public List<Integer> getFavoritoscods() {
        return favoritoscods;
    }

    public void setFavoritoscods(List<Integer> favoritoscods) {
        this.favoritoscods = favoritoscods;
    }

    public boolean isContemFavoritos() {
        return contemFavoritos;
    }

    public void setContemFavoritos(boolean contemFavoritos) {
        this.contemFavoritos = contemFavoritos;
    }
    
    
}
