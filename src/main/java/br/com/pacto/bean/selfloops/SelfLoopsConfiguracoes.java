package br.com.pacto.bean.selfloops;

import br.com.pacto.bean.empresa.Empresa;

import javax.persistence.*;
import java.io.Serializable;

@Entity
public class SelfLoopsConfiguracoes implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private String codeSelfloops;
    private String empresaSelfloops;
    private String refreshToken;
    @ManyToOne
    private Empresa empresa;

    public SelfLoopsConfiguracoes() {
    }

    public boolean isIntegracaoRelizadaSucesso() {
        return codeSelfloops != null && !codeSelfloops.isEmpty() && refreshToken != null && !refreshToken.isEmpty() && empresaSelfloops != null && !empresaSelfloops.isEmpty();
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getCodeSelfloops() {
        return codeSelfloops;
    }

    public void setCodeSelfloops(String codeSlefloops) {
        this.codeSelfloops = codeSlefloops;
    }

    public String getEmpresaSelfloops() {
        return empresaSelfloops;
    }

    public void setEmpresaSelfloops(String empresaSelfloops) {
        this.empresaSelfloops = empresaSelfloops;
    }

    public String getRefreshToken() {
        return refreshToken;
    }

    public void setRefreshToken(String refreshToken) {
        this.refreshToken = refreshToken;
    }

    public Empresa getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }
}
