package br.com.pacto.bean;

import br.com.pacto.bean.agenda.LembreteAgendamento;
import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.disponibilidade.HorarioDisponibilidade;
import br.com.pacto.bean.professor.ProfessorSintetico;
import br.com.pacto.bean.tipoEvento.TipoEvento;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.enumerador.agenda.StatusAgendamentoEnum;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.util.bean.AgendaTO;
import br.com.pacto.util.bean.GenericoTO;
import br.com.pacto.util.enumeradores.DiasSemana;
import br.com.pacto.util.enumeradores.PaletaCoresEnum;
import br.com.pacto.util.impl.UtilReflection;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;
import org.hibernate.annotations.Type;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import static java.util.Objects.isNull;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table
public class Agendamento implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    @ManyToOne(fetch = FetchType.LAZY)
    private ProfessorSintetico professor = new ProfessorSintetico();
    private Integer diaSemana;
    @Temporal(TemporalType.TIMESTAMP)
    private Date inicio;
    @Temporal(TemporalType.TIMESTAMP)
    private Date fim;
    @ManyToOne(fetch = FetchType.LAZY)
    private ClienteSintetico cliente = new ClienteSintetico();
    private Boolean diaTodo = Boolean.FALSE;
    @ManyToOne(fetch = FetchType.LAZY)
    @NotFound(action = NotFoundAction.IGNORE)
    private TipoEvento tipoEvento;
    @ManyToOne(fetch = FetchType.EAGER)
    private HorarioDisponibilidade horarioDisponibilidade;
    @Enumerated(EnumType.ORDINAL)
    private StatusAgendamentoEnum status;
    private Integer usuarioLancou_codigo;
    private Integer usuarioUltAlteracao_codigo;
    @OneToMany(mappedBy = "agendamento", cascade = CascadeType.REMOVE)
    private List<LembreteAgendamento> lembretesAgendamento;
    @Temporal(TemporalType.TIMESTAMP)
    private Date ultimaAlteracao = Calendario.hoje();
    @Temporal(TemporalType.TIMESTAMP)
    private Date dataLancamento = Calendario.hoje();
    @Transient
    private String horaInicio;
    @Transient
    private String minutoInicio;
    @Transient
    private String horaFim;
    @Transient
    private String minutoFim;
    @Transient
    private Boolean horarioComercial = Boolean.FALSE;
    @Transient
    private Boolean repetir = Boolean.FALSE;
    @Transient
    private Date repetirAte;
    private Boolean disponibilidade = Boolean.FALSE;
    private Integer nsu;
    @Transient
    private Integer professorCod;
    @Transient
    private Integer alunoCod;
    @Transient
    private Integer tipoEventoCod;
    @Transient
    private Integer disponibilidadeConfigCod;
    @Transient
    private Date dataAlterar;
    @Transient
    private Agendamento antesAlteracao;
    @Transient
    private List<Agendamento> disponibilidades;
    @Transient
    private Double width;
    @Transient
    private Double left = null;
    @Lob
    @Type(type = "org.hibernate.type.StringClobType")
    @Column(columnDefinition = "text", length = 99999)
    private String observacao;
    private String tipos;
    private Integer operacaoEmMassa;
    @Temporal(TemporalType.TIMESTAMP)
    private Date fimExclusao;
    private Boolean posteriores;
    @Transient
    private Usuario clienteUltAlteracao;
    private Boolean migradoDisponibilidade;

    public Agendamento getClone(Date inicio, Date fim, boolean codigo) {
        Agendamento clone = new Agendamento();
        clone.setCodigo(codigo ? this.getCodigo() : null);
        clone.setProfessor(this.getProfessor() == null ? null : this.getProfessor());
        clone.setCliente(this.getCliente() == null ? null : this.getCliente());
        clone.setDiaSemana(this.getDiaSemana());
        clone.setInicio(inicio == null ? new Date(this.getInicio().getTime()) : inicio);
        clone.setFim(fim == null ? new Date(this.getFim().getTime()) : fim);
        clone.setTipoEvento(this.getTipoEvento());
        clone.setDiaTodo(this.getDiaTodo());
        clone.setStatus(this.getStatus());
        clone.setarTransients();
        clone.setHorarioComercial(this.getHorarioComercial());
        clone.setRepetir(this.getRepetir());
        clone.setRepetirAte(this.getRepetirAte());
        clone.setDisponibilidade(this.getDisponibilidade());
        clone.setNsu(this.getNsu());
        clone.setDataAlterar(this.getDataAlterar());
        clone.setDataLancamento(this.getDataLancamento());
        clone.setObservacao(this.getObservacao());
        return clone;
    }

    public void atualizarInicioFim() {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(dataAlterar);
        if (horaInicio != null && !horaInicio.isEmpty()) {
            calendar.set(Calendar.HOUR_OF_DAY, Integer.valueOf(horaInicio));
            calendar.set(Calendar.SECOND, Integer.valueOf(00));
        }
        if (minutoInicio != null && !minutoInicio.isEmpty()) {
            calendar.set(Calendar.MINUTE, Integer.valueOf(minutoInicio));
        }
        inicio = calendar.getTime();
        calendar.setTime(dataAlterar);
        if (horaFim != null && !horaFim.isEmpty()) {
            calendar.set(Calendar.HOUR_OF_DAY, Integer.valueOf(horaFim));
            calendar.set(Calendar.SECOND, Integer.valueOf(00));
        }
        if (minutoFim != null && !minutoFim.isEmpty()) {
            calendar.set(Calendar.MINUTE, Integer.valueOf(minutoFim));
        }
        fim = calendar.getTime();
    }

    public void setarTransients() {
        setarHoras();

        tipoEventoCod = tipoEvento == null || tipoEvento.getCodigo() == null ? 0 : tipoEvento.getCodigo();
        dataAlterar = new Date(inicio.getTime());

        professorCod = professor == null || professor.getCodigo() == null ? 0 : professor.getCodigo();

    }

    public void manterAntesAlteracoes() {
        antesAlteracao = getClone(null, null, false);
    }

    public Agendamento getAntesAlteracao() {
        return antesAlteracao;
    }

    public void setAntesAlteracao(Agendamento antesAlteracao) {
        this.antesAlteracao = antesAlteracao;
    }

    public void prepararCods(ClienteSintetico clienteAgendamento) {
        alunoCod = clienteAgendamento == null ? (getCliente() == null ? 0 : getCliente().getCodigo()) : clienteAgendamento.getCodigo();
        professorCod = clienteAgendamento == null ? (getProfessor() == null ? 0 : getProfessor().getCodigo()) : clienteAgendamento.getProfessorSintetico().getCodigo();
    }

    public ClienteSintetico getCliente() {
        return cliente;
    }

    public void setCliente(ClienteSintetico cliente) {
        this.cliente = cliente;
    }

    public Boolean getDiaTodo() {
        return diaTodo;
    }

    public void setDiaTodo(Boolean diaTodo) {
        this.diaTodo = diaTodo;
    }

    public TipoEvento getTipoEvento() {
        return tipoEvento;
    }

    public void setTipoEvento(TipoEvento tipoAgendamento) {
        this.tipoEvento = tipoAgendamento;
    }

    public void ajusteTipoEvento(GenericoTO genericoTO) {
        this.tipoEventoCod = genericoTO.getCodigo();
        this.tipoEvento.setCor(PaletaCoresEnum.getFromCss(genericoTO.getCss()));
    }

    public Integer getStatusCod() {
        return status == null ? null : status.getId();
    }

    public void setStatusCod(Integer id) {
        this.status = StatusAgendamentoEnum.getFromId(id);
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public ProfessorSintetico getProfessor() {
        return professor;
    }

    public void setProfessor(ProfessorSintetico professor) {
        this.professor = professor;
    }

    public Integer getDiaSemana() {
        return diaSemana;
    }

    public void setDiaSemana(Integer diaSemana) {
        this.diaSemana = diaSemana;
    }

    public Date getInicio() {
        return inicio;
    }

    public String getDiaMes() {
        if (inicio != null) {
            return String.valueOf(Calendario.getInstance(inicio).get(Calendar.DAY_OF_MONTH));
        }
        return "";
    }

    public String getNomeDiaSemana() {
        if (inicio != null) {
            DiasSemana d = DiasSemana.getDiaSemanaNumeral(Calendario.getInstance(inicio).get(Calendar.DAY_OF_WEEK));
            return d != null ? d.getChave() : "";
        }
        return "";
    }

    public String getCodigoDiaSemana() {
        if (inicio != null) {
            DiasSemana d = DiasSemana.getDiaSemanaNumeral(Calendario.getInstance(inicio).get(Calendar.DAY_OF_WEEK));
            return d != null ? d.getCodigo() : "";
        }
        return "";
    }

    public void setInicio(Date inicio) {
        this.inicio = inicio;
    }

    public Date getFim() {
        return fim;
    }

    public void setFim(Date fim) {
        this.fim = fim;
    }

    public boolean getMostrarAluno() {
        return !disponibilidade && (getCodigo() == null || getCodigo().intValue() == 0);
    }

    public AgendaTO toAgenda(Boolean exibirNomeProfessor, Boolean exibirTipo, int mesAtual) {
        return new AgendaTO(this.getText(exibirNomeProfessor, exibirTipo),
                this.getInicio(), this.getFim(),
                " " + (Uteis.getMesData(this.getInicio()) == mesAtual ? "mesA" : "mesNaoA") + " " + ((exibirTipo || !this.getDisponibilidade()) ? this.getTipoEvento().getCor().getDescricao() : "") + " "
                + ("_profe_" + this.professor.getCodigo())
                + " _cod_" + this.getCodigo()
                + (this.getDisponibilidade() ? " " : (" _largura"+this.getWidth()+"%"))
                + (this.getDisponibilidade() ? " " : (" _margem"+this.getLeft()+"%"))
                + " _tipoCod_" + this.getTipoEvento().getCodigo()
                + " " + ((exibirTipo || !this.getDisponibilidade()) ? this.getTipoEvento().getCor().getClasseCor() : "")
                + " "
                + ((this.getDisponibilidade() ? "disponibilidade" : this.getTipoEvento().getNomeCss())
                + " _dia_" + Uteis.getDiaMesData(this.getInicio())), this, false);
    }

    public String getText(Boolean exibirNomeProfessor, Boolean exibirTipo) {
        return (exibirTipo
                ? this.getTipoEvento().getNome() : "")
                + (exibirNomeProfessor
                ? "\n" + this.getProfessor().getNomeAbreviado() : "")
                + ((this.getCliente() != null
                && this.getCliente().getCodigo() != null
                && this.getCliente().getCodigo() > 0) ? "\n" + (this.getCliente().getNomeAbreviadoMinusculo()+" ("+this.getCliente().getMatricula()+")") : "");
    }

    public StatusAgendamentoEnum getStatus() {
        return status;
    }

    public void setStatus(StatusAgendamentoEnum status) {
        this.status = status;
    }

    public String getHoraInicio() {
        return horaInicio;
    }

    public void setHoraInicio(String horaInicio) {
        this.horaInicio = horaInicio;
    }

    public String getMinutoInicio() {
        return minutoInicio;
    }

    public void setMinutoInicio(String minutoInicio) {
        this.minutoInicio = minutoInicio;
    }

    public String getHoraFim() {
        return horaFim;
    }

    public void setHoraFim(String horaFim) {
        this.horaFim = horaFim;
    }

    public String getMinutoFim() {
        return minutoFim;
    }

    public void setMinutoFim(String minutoFim) {
        this.minutoFim = minutoFim;
    }

    public Boolean getHorarioComercial() {
        return horarioComercial;
    }

    public void setHorarioComercial(Boolean horarioComercial) {
        this.horarioComercial = horarioComercial;
    }

    public Boolean getRepetir() {
        return repetir;
    }

    public void setRepetir(Boolean repetir) {
        this.repetir = repetir;
    }

    public Date getRepetirAte() {
        return repetirAte;
    }

    public void setRepetirAte(Date repetirAte) {
        this.repetirAte = repetirAte;
    }

    @Override
    public int hashCode() {
        return this.getCodigo() != null && this.getCodigo() > 0
                ? this.getCodigo().hashCode() : super.hashCode();
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null) {
            return false;
        }
        if (!(obj instanceof Agendamento)) {
            return false;
        }
        final Agendamento other = (Agendamento) obj;
        if (this.getCodigo() != other.getCodigo() && (this.getCodigo() == null || !this.getCodigo().equals(other.getCodigo()))) {
            return false;
        }
        return true;
    }

    public String getNomeProfessor() {
        try {
            return getProfessor().getNomeAbreviado();
        } catch (Exception e) {
            return "";
        }

    }

    public String getNomeAluno() {
        try {
            return getCliente().getNomeAbreviado();
        } catch (Exception e) {
            return "";
        }

    }
    public String getMatricula() {
        try {
            return getCliente().getMatriculaString();
        } catch (Exception e) {
            return "";
        }

    }

    public Boolean getDisponibilidade() {
        return disponibilidade;
    }

    public void setDisponibilidade(Boolean disponibilidade) {
        this.disponibilidade = disponibilidade;
    }

    public Integer getNsu() {
        return nsu;
    }

    public void setNsu(Integer nsu) {
        this.nsu = nsu;
    }

    public Integer getProfessorCod() {
        return professorCod;
    }

    public void setProfessorCod(Integer professorCod) {
        this.professorCod = professorCod;
    }

    public Integer getAlunoCod() {
        return alunoCod;
    }

    public void setAlunoCod(Integer alunoCod) {
        this.alunoCod = alunoCod;
    }

    public Integer getTipoEventoCod() {
        return tipoEventoCod;
    }

    public void setTipoEventoCod(Integer tipoEventoCod) {
        this.tipoEventoCod = tipoEventoCod;
    }

    public Date getDataAlterar() {
        return dataAlterar;
    }

    public void setDataAlterar(Date dataAlterar) {
        this.dataAlterar = dataAlterar;
    }

    public String getData() {
        return Calendario.getData(inicio, "EEE, d MMM");
    }
    public String getHoraInicioApresentar() {
        return Calendario.getData(inicio, Calendario.MASC_HORA);
    }
    public String getHoraFimApresentar() {
        return Calendario.getData(fim, Calendario.MASC_HORA);
    }

    public String getDataApresentar() {
        setarTransients();
        return Uteis.getData(inicio) + " " + horaInicio + ":" + minutoInicio + " - " + horaFim + ":" + minutoFim;
    }

    public String getDataApresentar2() {
        setarHoras();
        return Uteis.getData(inicio) + " " + horaInicio + ":" + minutoInicio + " - " + horaFim + ":" + minutoFim;
    }

    public String getEvento() {
            if(tipoEvento!=null){
            return tipoEvento.getNome();
        }else{
            return "";
        }
    }

    public String getHorario() {
        return Calendario.getData(inicio, Calendario.MASC_DATAHORA);
    }

    public String getSituacao() {
        return status.getDescricao();
    }

    public Integer getUsuarioLancou_codigo() {
        return usuarioLancou_codigo;
    }

    public void setUsuarioLancou_codigo(Integer usuarioLancou) {
        this.usuarioLancou_codigo = usuarioLancou;
    }

    public Integer getUsuarioUltAlteracao_codigo() {
        return usuarioUltAlteracao_codigo;
    }

    public void setUsuarioUltAlteracao_codigo(Integer usuarioUltAlteracao) {
        this.usuarioUltAlteracao_codigo = usuarioUltAlteracao;
    }

    public Date getUltimaAlteracao() {
        return ultimaAlteracao;
    }

    public String getUltimaAlteracaoStr() {
        return ultimaAlteracao == null ? "" : Uteis.getDataAplicandoFormatacao(ultimaAlteracao, "dd/MM/yyyy HH:mm");
    }

    public String getInicioComHora() {
        return inicio == null ? "" : Uteis.getDataAplicandoFormatacao(inicio, "dd/MM/yyyy HH:mm");
    }

    public String getFonteCancelamento() {
        String origem = "Web";
        if (getObservacao() != null && getObservacao().equals("Aplicativo")) {
            origem = "App";
        }
        return StatusAgendamentoEnum.CANCELADO.equals(status) ? origem : origem;
    }

    public void setUltimaAlteracao(Date ultimaAlteracao) {
        this.ultimaAlteracao = ultimaAlteracao;
    }

    public Date getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public void setarHoras() {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(inicio);
        horaInicio = Uteis.addZeroEsquerda(calendar.get(Calendar.HOUR_OF_DAY));
        minutoInicio = Uteis.addZeroEsquerda(calendar.get(Calendar.MINUTE));

        calendar.setTime(fim);
        horaFim = Uteis.addZeroEsquerda(calendar.get(Calendar.HOUR_OF_DAY));
        minutoFim = Uteis.addZeroEsquerda(calendar.get(Calendar.MINUTE));
    }

    public Integer getChaveLink() {
        return getCliente().getCodigo();
    }

    public boolean isMudouDatas() {
        if (antesAlteracao == null) {
            return true;
        } else {
            return !inicio.equals(antesAlteracao.inicio)
                    || !fim.equals(antesAlteracao.fim);
        }
    }

    public String getNomeTipo(){
        try {
            return getTipoEvento().getNome();
        } catch (Exception e) {
            return "";
        }
    }

    public List<Agendamento> getDisponibilidades() {
        return disponibilidades;
    }

    public void setDisponibilidades(List<Agendamento> disponibilidades) {
        this.disponibilidades = disponibilidades;
    }

    public Double getWidth() {
        return width;
    }

    public void setWidth(Double width) {
        this.width = width;
    }

    public Double getLeft() {
        return left;
    }

    public void setLeft(Double left) {
        this.left = left;
    }


    public String getObservacao() {
        if (observacao == null) {
            observacao = "";
        }
        return observacao;
    }

    public void setObservacao(String observacao) { this.observacao = observacao; }

    public Integer getMinutosFim(){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(fim);
        return (calendar.get(Calendar.HOUR_OF_DAY) * 60) + calendar.get(Calendar.MINUTE);
    }

    public Integer getMinutosInicio(){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(inicio);
        return (calendar.get(Calendar.HOUR_OF_DAY) * 60) + calendar.get(Calendar.MINUTE);
    }

    public String getKeyAgrup(){
        return getProfessor().getCodigo() + "_" +
                Calendario.getData(getInicio(), "yyyyMMddHHmm") + "_" +
                Calendario.getData(getFim(), "HHmm");
    }

    public String getTipos() {
        return tipos;
    }

    public void setTipos(String tipos) {
        this.tipos = tipos;
    }

    public Date getFimExclusao() {
        return fimExclusao;
    }

    public void setFimExclusao(Date fimExclusao) {
        this.fimExclusao = fimExclusao;
    }

    public Boolean getPosteriores() {
        return posteriores;
    }

    public void setPosteriores(Boolean posteriores) {
        this.posteriores = posteriores;
    }

    public List<LembreteAgendamento> getLembretesAgendamento() {
        return lembretesAgendamento;
    }

    public void setLembretesAgendamento(List<LembreteAgendamento> lembretesAgendamento) {
        this.lembretesAgendamento = lembretesAgendamento;
    }

    public Integer getOperacaoEmMassa() {
        return operacaoEmMassa;
    }

    public void setOperacaoEmMassa(Integer operacaoEmMassa) {
        this.operacaoEmMassa = operacaoEmMassa;
    }


    public String getDescricaoParaLog(Agendamento v2) {
        try {
            StringBuilder log = new StringBuilder();
            log.append(UtilReflection.difference(this, v2,
                    "status",  "observacao", "inicio", "fim", "repetir"));
            if(!isNull(this.getCliente())) {
                log.append(UtilReflection.difference(this.getCliente(), v2 == null ? null : v2.getCliente(), "nome"));
            }
            if(!isNull(this.getTipoEvento())) {
                log.append(UtilReflection.difference(this.getTipoEvento(),  v2 == null ? null : v2.getTipoEvento(), "nome"));
            }
            if(!isNull(this.getProfessor())) {
                log.append(UtilReflection.difference(this.getProfessor(),  v2 == null ? null : v2.getProfessor(), "nome"));
            }
            if(!isNull(this.getRepetir())) {
                log.append(UtilReflection.difference(this.getRepetir(),  v2 == null ? null : v2.getRepetir(), "nome"));
            }
            return log.toString();
        } catch (Exception ex) {
            ex.printStackTrace();
            return "ERRO gerar log";
        }
    }

    public Usuario getClienteUltAlteracao() {
        return clienteUltAlteracao;
    }

    public void setClienteUltAlteracao(Usuario clienteUltAlteracao) {
        this.clienteUltAlteracao = clienteUltAlteracao;
    }
    public Integer getDisponibilidadeConfigCod() {
        return disponibilidadeConfigCod;
    }

    public void setDisponibilidadeConfigCod(Integer disponibilidadeConfigCod) {
        this.disponibilidadeConfigCod = disponibilidadeConfigCod;
    }

    public HorarioDisponibilidade getHorarioDisponibilidade() {
        return horarioDisponibilidade;
    }

    public void setHorarioDisponibilidade(HorarioDisponibilidade horarioDisponibilidadeConfig) {
        this.horarioDisponibilidade = horarioDisponibilidadeConfig;
    }

    public Boolean getMigradoDisponibilidade() {
        return migradoDisponibilidade;
    }

    public void setMigradoDisponibilidade(Boolean migradoDisponibilidade) {
        this.migradoDisponibilidade = migradoDisponibilidade;
    }

    public Agendamento clone() {
        try {
            return (Agendamento) super.clone();
        } catch (CloneNotSupportedException e) {
            e.printStackTrace();
            throw new RuntimeException("Erro ao clonar Agendamento");
        }
    }
}
