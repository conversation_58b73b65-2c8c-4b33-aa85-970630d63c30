package br.com.pacto.bean.avaliacao;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class AvaliacaoPosturaDTO {

    private AvaliacaoPosturaImageDTO frenteImageUri;
    private AvaliacaoPosturaImageDTO direitaImageUri;
    private AvaliacaoPosturaImageDTO esquerdaImageUri;
    private AvaliacaoPosturaImageDTO costasImageUri;
    private AnamnesePosturaLateralDTO visaoLateral;
    private AnamnesePosturalPosteriorDTO visaoPosterior;
    private AnamnesePosturalAnteriorDTO visaoAnterior;
    private AnamnesePosturalAssimetriasDTO assimetrias;
    private String observacao;

    public AvaliacaoPosturaDTO(AvaliacaoPostural postural, List<ItemAvaliacaoPostural> itens) throws Exception{
        this.frenteImageUri = new AvaliacaoPosturaImageDTO(postural.getKeyImgPosterior());
        this.direitaImageUri = new AvaliacaoPosturaImageDTO(postural.getKeyImgDireita());
        this.esquerdaImageUri = new AvaliacaoPosturaImageDTO(postural.getKeyImgEsquerda());
        this.costasImageUri = new AvaliacaoPosturaImageDTO(postural.getKeyImgAnterior());
        this.visaoLateral = new AnamnesePosturaLateralDTO(itens);
        this.visaoPosterior = new AnamnesePosturalPosteriorDTO(itens);
        this.visaoAnterior = new AnamnesePosturalAnteriorDTO(itens);
        this.assimetrias = new AnamnesePosturalAssimetriasDTO(postural.getOmbros(), postural.getQuadril());
        this.observacao = postural.getObservacao();
    }

    public AvaliacaoPosturaImageDTO getFrenteImageUri() {
        return frenteImageUri;
    }

    public void setFrenteImageUri(AvaliacaoPosturaImageDTO frenteImageUri) {
        this.frenteImageUri = frenteImageUri;
    }

    public AvaliacaoPosturaImageDTO getDireitaImageUri() {
        return direitaImageUri;
    }

    public void setDireitaImageUri(AvaliacaoPosturaImageDTO direitaImageUri) {
        this.direitaImageUri = direitaImageUri;
    }

    public AvaliacaoPosturaImageDTO getEsquerdaImageUri() {
        return esquerdaImageUri;
    }

    public void setEsquerdaImageUri(AvaliacaoPosturaImageDTO esquerdaImageUri) {
        this.esquerdaImageUri = esquerdaImageUri;
    }

    public AvaliacaoPosturaImageDTO getCostasImageUri() {
        return costasImageUri;
    }

    public void setCostasImageUri(AvaliacaoPosturaImageDTO costasImageUri) {
        this.costasImageUri = costasImageUri;
    }

    public AnamnesePosturaLateralDTO getVisaoLateral() {
        return visaoLateral;
    }

    public void setVisaoLateral(AnamnesePosturaLateralDTO visaoLateral) {
        this.visaoLateral = visaoLateral;
    }

    public AnamnesePosturalPosteriorDTO getVisaoPosterior() {
        return visaoPosterior;
    }

    public void setVisaoPosterior(AnamnesePosturalPosteriorDTO visaoPosterior) {
        this.visaoPosterior = visaoPosterior;
    }

    public AnamnesePosturalAnteriorDTO getVisaoAnterior() {
        return visaoAnterior;
    }

    public void setVisaoAnterior(AnamnesePosturalAnteriorDTO visaoAnterior) {
        this.visaoAnterior = visaoAnterior;
    }

    public AnamnesePosturalAssimetriasDTO getAssimetrias() {
        return assimetrias;
    }

    public void setAssimetrias(AnamnesePosturalAssimetriasDTO assimetrias) {
        this.assimetrias = assimetrias;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }
}
