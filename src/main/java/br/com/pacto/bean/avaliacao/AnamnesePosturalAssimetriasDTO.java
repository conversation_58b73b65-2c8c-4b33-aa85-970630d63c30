package br.com.pacto.bean.avaliacao;

import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class AnamnesePosturalAssimetriasDTO {

    private AssimetriaEnum ombrosAssimetricos;
    private AssimetriaEnum assimetriaQuadril;

    public AnamnesePosturalAssimetriasDTO() {
    }

    public AnamnesePosturalAssimetriasDTO(AssimetriaEnum ombrosAssimetricos, AssimetriaEnum assimetriaQuadril) {
        this.ombrosAssimetricos = ombrosAssimetricos;
        this.assimetriaQuadril = assimetriaQuadril;
    }

    public AssimetriaEnum getOmbrosAssimetricos() {
        return ombrosAssimetricos;
    }

    public void setOmbrosAssimetricos(AssimetriaEnum ombrosAssimetricos) {
        this.ombrosAssimetricos = ombrosAssimetricos;
    }

    public AssimetriaEnum getAssimetriaQuadril() {
        return assimetriaQuadril;
    }

    public void setAssimetriaQuadril(AssimetriaEnum assimetriaQuadril) {
        this.assimetriaQuadril = assimetriaQuadril;
    }
}
