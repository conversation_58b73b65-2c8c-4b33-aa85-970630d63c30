package br.com.pacto.bean.avaliacao;

import javax.sql.rowset.serial.SerialException;

/**
 * <AUTHOR> 29/01/2019
 */
public enum CaminhadaCorridaColunaEnum {

    ENTRE_13_19("escolhido vo_1"),
    ENTREO_20_29("escolhido vo_2"),
    NTREO_30_39("escolhido vo_3"),
    ENTREO_40_49("escolhido vo_4"),
    ENTREO_50_59("escolhido vo_5"),
    MAIOR_60("escolhido vo_6");

    private String descricao;

    CaminhadaCorridaColunaEnum(String descricao) {
        this.descricao = descricao;
    }

    public String getDescricao() {
        return descricao;
    }

    public static CaminhadaCorridaColunaEnum getInstance(final String str) throws SerialException {
        for (CaminhadaCorridaColunaEnum coluna : CaminhadaCorridaColunaEnum.values()) {
            if (str.equalsIgnoreCase(coluna.getDescricao())) {
                return coluna;
            }
        }
        return ENTRE_13_19;
    }

}
