package br.com.pacto.bean.avaliacao;

import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.ALWAYS)
public class AvaliacaoBioimpedanciaDTO {
    private String data;
    private Double altura;
    private Double peso;
    private Double resistencia;
    private Double reatancia;
    private Double perimetroAbdominal;
    private Double aguaCorporalPercentual;
    private Double massaLivreKg;
    private Double massaLivrePercentual;
    private Double gorduraCorporalKg;
    private Double gorduraCorporalPercentual;
    private Double massaMuscularEsqueleticaKg;
    private Double imc;

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public Double getAltura() {
        return altura;
    }

    public void setAltura(Double altura) {
        this.altura = altura;
    }

    public Double getPeso() {
        return peso;
    }

    public void setPeso(Double peso) {
        this.peso = peso;
    }

    public Double getResistencia() {
        return resistencia;
    }

    public void setResistencia(Double resistencia) {
        this.resistencia = resistencia;
    }

    public Double getReatancia() {
        return reatancia;
    }

    public void setReatancia(Double reatancia) {
        this.reatancia = reatancia;
    }

    public Double getPerimetroAbdominal() {
        return perimetroAbdominal;
    }

    public void setPerimetroAbdominal(Double perimetroAbdominal) {
        this.perimetroAbdominal = perimetroAbdominal;
    }

    public Double getAguaCorporalPercentual() {
        return aguaCorporalPercentual;
    }

    public void setAguaCorporalPercentual(Double aguaCorporalPercentual) {
        this.aguaCorporalPercentual = aguaCorporalPercentual;
    }

    public Double getMassaLivreKg() {
        return massaLivreKg;
    }

    public void setMassaLivreKg(Double massaLivreKg) {
        this.massaLivreKg = massaLivreKg;
    }

    public Double getMassaLivrePercentual() {
        return massaLivrePercentual;
    }

    public void setMassaLivrePercentual(Double massaLivrePercentual) {
        this.massaLivrePercentual = massaLivrePercentual;
    }

    public Double getGorduraCorporalKg() {
        return gorduraCorporalKg;
    }

    public void setGorduraCorporalKg(Double gorduraCorporalKg) {
        this.gorduraCorporalKg = gorduraCorporalKg;
    }

    public Double getGorduraCorporalPercentual() {
        return gorduraCorporalPercentual;
    }

    public void setGorduraCorporalPercentual(Double gorduraCorporalPercentual) {
        this.gorduraCorporalPercentual = gorduraCorporalPercentual;
    }

    public Double getMassaMuscularEsqueleticaKg() {
        return massaMuscularEsqueleticaKg;
    }

    public void setMassaMuscularEsqueleticaKg(Double massaMuscularEsqueleticaKg) {
        this.massaMuscularEsqueleticaKg = massaMuscularEsqueleticaKg;
    }

    public Double getImc() {
        return imc;
    }

    public void setImc(Double imc) {
        this.imc = imc;
    }
}
