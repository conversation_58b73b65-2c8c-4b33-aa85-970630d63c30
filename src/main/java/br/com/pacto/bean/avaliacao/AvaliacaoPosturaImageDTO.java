package br.com.pacto.bean.avaliacao;

import br.com.pacto.objeto.Aplicacao;
import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class AvaliacaoPosturaImageDTO {

    private Integer id;
    private String url;
    private final String URL_PATH = Aplicacao.getProp(Aplicacao.urlFotosNuvem) + "/";

    public AvaliacaoPosturaImageDTO(String url) {
        url = url != null && !url.equalsIgnoreCase("") ? URL_PATH + url : null;
        this.url = url;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }
}
