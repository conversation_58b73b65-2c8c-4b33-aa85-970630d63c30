package br.com.pacto.bean.avaliacao;

import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class AvaliacaoComposicaoDTO {

    private Double pesoGordura;
    private Double pesoResidual;
    private Double pesoMuscular;
    private Double pesoOsseo;

    public AvaliacaoComposicaoDTO() {
    }

    public AvaliacaoComposicaoDTO(PesoOsseo pesoOsseo) {
        this.pesoGordura = pesoOsseo.getPesoGordura();
        this.pesoResidual = pesoOsseo.getPesoResidual();
        this.pesoMuscular = pesoOsseo.getPesoMuscular();
        this.pesoOsseo = pesoOsseo.getPesoOsseo();
    }

    public Double getPesoGordura() {
        return pesoGordura;
    }

    public void setPesoGordura(Double pesoGordura) {
        this.pesoGordura = pesoGordura;
    }

    public Double getPesoResidual() {
        return pesoResidual;
    }

    public void setPesoResidual(Double pesoResidual) {
        this.pesoResidual = pesoResidual;
    }

    public Double getPesoMuscular() {
        return pesoMuscular;
    }

    public void setPesoMuscular(Double pesoMuscular) {
        this.pesoMuscular = pesoMuscular;
    }

    public Double getPesoOsseo() {
        return pesoOsseo;
    }

    public void setPesoOsseo(Double pesoOsseo) {
        this.pesoOsseo = pesoOsseo;
    }
}
