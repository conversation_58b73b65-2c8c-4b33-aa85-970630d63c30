package br.com.pacto.bean.avaliacao;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.io.Serializable;

/**
 * <AUTHOR> Si<PERSON>ira 22/01/2019
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AvaliacaoRMLDTOUpdate implements Serializable {

    private Integer flexoesBracos;
    private Integer abdominais;

    public Integer getFlexoesBracos() {
        return flexoesBracos;
    }

    public void setFlexoesBracos(Integer flexoesBracos) {
        this.flexoesBracos = flexoesBracos;
    }

    public Integer getAbdominais() {
        return abdominais;
    }

    public void setAbdominais(Integer abdominais) {
        this.abdominais = abdominais;
    }
}
