package br.com.pacto.bean.avaliacao;

import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class AvaliacaoRMLDTO {

    private Integer flexoesBracos;
    private Integer abdominais;
    private CategoriaColuna rmlFlexoesColuna;
    private RMLLinha rmlFlexoesLinha;
    private CategoriaColuna rmlAbominaisColuna;
    private RMLLinha rmlAbominaisLinha;

    public AvaliacaoRMLDTO(Integer flexoes,
                           Integer abdominais,
                           RMLLinha rmlLinha,
                           CategoriaColuna rmlFlexoesColuna,
                           CategoriaColuna rmlAbominaisColuna) {
        this.flexoesBracos = flexoes;
        this.abdominais = abdominais;
        this.rmlFlexoesColuna = rmlFlexoesColuna;
        this.rmlFlexoesLinha = rmlLinha;
        this.rmlAbominaisColuna = rmlAbominaisColuna;
        this.rmlAbominaisLinha = rmlLinha;
    }

    public Integer getFlexoesBracos() {
        return flexoesBracos;
    }

    public void setFlexoesBracos(Integer flexoesBracos) {
        this.flexoesBracos = flexoesBracos;
    }

    public Integer getAbdominais() {
        return abdominais;
    }

    public void setAbdominais(Integer abdominais) {
        this.abdominais = abdominais;
    }

    public CategoriaColuna getRmlFlexoesColuna() {
        return rmlFlexoesColuna;
    }

    public void setRmlFlexoesColuna(CategoriaColuna rmlFlexoesColuna) {
        this.rmlFlexoesColuna = rmlFlexoesColuna;
    }

    public RMLLinha getRmlFlexoesLinha() {
        return rmlFlexoesLinha;
    }

    public void setRmlFlexoesLinha(RMLLinha rmlFlexoesLinha) {
        this.rmlFlexoesLinha = rmlFlexoesLinha;
    }

    public CategoriaColuna getRmlAbominaisColuna() {
        return rmlAbominaisColuna;
    }

    public void setRmlAbominaisColuna(CategoriaColuna rmlAbominaisColuna) {
        this.rmlAbominaisColuna = rmlAbominaisColuna;
    }

    public RMLLinha getRmlAbominaisLinha() {
        return rmlAbominaisLinha;
    }

    public void setRmlAbominaisLinha(RMLLinha rmlAbominaisLinha) {
        this.rmlAbominaisLinha = rmlAbominaisLinha;
    }
}
