package br.com.pacto.bean.avaliacao;

import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.objeto.Aplicacao;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.util.Date;

@Entity
@Table
public class RespostaClienteParQ {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    @OneToOne
    private ClienteSintetico cliente;
    @Temporal(TemporalType.TIMESTAMP)
    private Date dataResposta;
    @Column(columnDefinition = "text", length = 99999)
    private String urlAssinatura;
    private Integer usuario_codigo;
    private Boolean ativo;
    private Boolean parqPositivo;
    @Temporal(TemporalType.TIMESTAMP)
    private Date dataUltimaEdicao;

    public RespostaClienteParQ() {}

    public RespostaClienteParQ(Integer codigo,
                               ClienteSintetico cliente,
                               Date dataResposta,
                               String urlAssinatura,
                               Usuario usuario,
                               Boolean ativo,
                               Boolean parqPositivo,
                               Date dataUltimaEdicao) {
        this.codigo = codigo;
        this.cliente = cliente;
        this.dataResposta = dataResposta;
        this.urlAssinatura = urlAssinatura;
        this.usuario_codigo = usuario.getCodigo();
        this.ativo = ativo;
        this.parqPositivo = parqPositivo;
        this.dataUltimaEdicao = dataUltimaEdicao;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public ClienteSintetico getCliente() {
        return cliente;
    }

    public void setCliente(ClienteSintetico cliente) {
        this.cliente = cliente;
    }

    public Date getDataResposta() {
        return dataResposta;
    }

    public void setDataResposta(Date dataResposta) {
        this.dataResposta = dataResposta;
    }

    public String getUrlAssinatura() {
        return urlAssinatura;
    }

    public void setUrlAssinatura(String urlAssinatura) {
        this.urlAssinatura = urlAssinatura;
    }

    public Integer getUsuario_codigo() {
        return usuario_codigo;
    }

    public void setUsuario_codigo(Integer usuario_codigo) {
        this.usuario_codigo = usuario_codigo;
    }

    public String getFullUrlAssinatura() {
        return urlAssinatura == null ? "" : (Aplicacao.obterUrlFotoDaNuvem(urlAssinatura) + "?v=" + new Date().getTime());
    }

    public Boolean getAtivo() {
        if (ativo == null) {
            ativo = false;
        }
        return ativo;
    }

    public void setAtivo(Boolean ativo) {
        this.ativo = ativo;
    }

    public Boolean getParqPositivo() {
        if (parqPositivo == null) {
            parqPositivo = false;
        }
        return parqPositivo;
    }

    public void setParqPositivo(Boolean parqPositivo) {
        this.parqPositivo = parqPositivo;
    }

    public Date getDataUltimaEdicao() {
        return dataUltimaEdicao;
    }

    public void setDataUltimaEdicao(Date dataUltimaEdicao) {
        this.dataUltimaEdicao = dataUltimaEdicao;
    }
}
