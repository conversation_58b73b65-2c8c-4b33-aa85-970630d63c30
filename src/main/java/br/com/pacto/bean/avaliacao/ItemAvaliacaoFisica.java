/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.avaliacao;

import br.com.pacto.bean.anamnese.Anamnese;
import br.com.pacto.bean.anamnese.Movimento3D;
import br.com.pacto.bean.anamnese.ResultadoMovimentoEnum;
import br.com.pacto.bean.anamnese.ResultadoVidaEnum;
import br.com.pacto.bean.cliente.ClienteSintetico;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.persistence.*;

import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.util.impl.UtilReflection;
import org.json.JSONObject;
import edu.emory.mathcs.backport.java.util.Arrays;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table
public class ItemAvaliacaoFisica {
        
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private String result;
    private Integer responsavelLancamento_codigo;
    @ManyToOne
    private ClienteSintetico cliente;
    @ManyToOne
    private AvaliacaoFisica avaliacaoFisica;
    @ManyToOne
    private Anamnese anamnese;
    @OneToMany(cascade = CascadeType.REMOVE, mappedBy = "item")
    private List<Movimento3D> movimentos3D;
    @OneToOne
    private Ventilometria ventilometria;
    @Temporal(TemporalType.TIMESTAMP)
    private Date dataLancamento;
    @Temporal(TemporalType.TIMESTAMP)
    private Date dataResposta = Calendario.hoje();
    @Enumerated(EnumType.ORDINAL)
    private ItemAvaliacaoFisicaEnum item;
    private Integer somaMobilidadeDir = 0;
    private Integer somaEstabilidadeDir = 0;
    private Double mediaMobilidadeDir = 0.0;
    private Double mediaEstabilidadeDir = 0.0;
    private Integer somaMobilidadeEsq = 0;
    private Integer somaEstabilidadeEsq = 0;
    private Double mediaMobilidadeEsq = 0.0;
    private Double mediaEstabilidadeEsq = 0.0;
    @Enumerated(EnumType.ORDINAL)
    private ResultadoVidaEnum qualidadeVida;
    @Enumerated(EnumType.ORDINAL)
    private ResultadoMovimentoEnum qualidadeMovimento;
    private Double somaQualidadeMovimento;
    private Integer somaQualidadeVida;
    @Transient
    private List<Movimento3D> mobilidade;
    @Transient
    private List<Movimento3D> estabilidade;
    @Transient
    private Boolean selecionado = false;
    @Transient
    private Boolean editado = false;

    public ItemAvaliacaoFisica() {
    }

    public ItemAvaliacaoFisica(ItemAvaliacaoFisicaEnum i, String result, Date data, ClienteSintetico cliente, Integer responsavelLancamento, AvaliacaoFisica avaliacaoFisica) {
        this.item = i;
        this.result = result;
        this.responsavelLancamento_codigo = responsavelLancamento;
        this.cliente = cliente;
        this.dataLancamento = data;
        this.avaliacaoFisica = avaliacaoFisica;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getResult() {
        if(result == null){
            result = "0";
        }
        return result;
    }

    public void setResult(String pressao) {
        this.result = pressao;
    }


    public Boolean getResultBoolean() {
        try {
            return Boolean.valueOf(result);
        }catch (Exception e){
            return false;
        }
    }

    public void setResultBoolean(Boolean pressao) {
        this.result = pressao.toString();
    }

    public Integer getResponsavelLancamento_codigo() {
        return responsavelLancamento_codigo;
    }

    public void setResponsavelLancamento_codigo(Integer responsavelLancamento) {
        this.responsavelLancamento_codigo = responsavelLancamento;
    }

    public String getDataLancamentoApresentar(){
        return dataLancamento == null ? "" : Uteis.getDataAplicandoFormatacao(dataLancamento,"dd/MM/yyyy HH:mm");
    }

    public ClienteSintetico getCliente() {
        return cliente;
    }

    public void setCliente(ClienteSintetico cliente) {
        this.cliente = cliente;
    }

    public Date getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public ItemAvaliacaoFisicaEnum getItem() {
        return item;
    }

    public void setItem(ItemAvaliacaoFisicaEnum item) {
        this.item = item;
    }

    public List<String> getObjs(){
        if(result == null || result.isEmpty()){
            return new ArrayList<String>();
        }
        return Arrays.asList(result.split("\\|\\#\\|"));
    }

    public Anamnese getAnamnese() {
        return anamnese;
    }

    public void setAnamnese(Anamnese anamnese) {
        this.anamnese = anamnese;
    }

    public List<Movimento3D> getMovimentos3D() {
        if (movimentos3D == null) {
            movimentos3D = new ArrayList<>();
        }
        return movimentos3D;
    }

    public Ventilometria getVentilometria() {
        return ventilometria;
    }

    public void setVentilometria(Ventilometria ventilometria) {
        this.ventilometria = ventilometria;
    }

    public Integer getSomaMobilidadeDir() {
        return somaMobilidadeDir;
    }

    public void setSomaMobilidadeDir(Integer somaMobilidadeDir) {
        this.somaMobilidadeDir = somaMobilidadeDir;
    }

    public Integer getSomaEstabilidadeDir() {
        return somaEstabilidadeDir;
    }

    public void setSomaEstabilidadeDir(Integer somaEstabilidadeDir) {
        this.somaEstabilidadeDir = somaEstabilidadeDir;
    }

    public Double getMediaMobilidadeDir() {
        return mediaMobilidadeDir;
    }

    public void setMediaMobilidadeDir(Double mediaMobilidadeDir) {
        this.mediaMobilidadeDir = mediaMobilidadeDir;
    }

    public Double getMediaEstabilidadeDir() {
        return mediaEstabilidadeDir;
    }

    public void setMediaEstabilidadeDir(Double mediaEstabilidadeDir) {
        this.mediaEstabilidadeDir = mediaEstabilidadeDir;
    }

    public Integer getSomaMobilidadeEsq() {
        return somaMobilidadeEsq;
    }

    public void setSomaMobilidadeEsq(Integer somaMobilidadeEsq) {
        this.somaMobilidadeEsq = somaMobilidadeEsq;
    }

    public Integer getSomaEstabilidadeEsq() {
        return somaEstabilidadeEsq;
    }

    public void setSomaEstabilidadeEsq(Integer somaEstabilidadeEsq) {
        this.somaEstabilidadeEsq = somaEstabilidadeEsq;
    }

    public Double getMediaMobilidadeEsq() {
        return mediaMobilidadeEsq;
    }

    public void setMediaMobilidadeEsq(Double mediaMobilidadeEsq) {
        this.mediaMobilidadeEsq = mediaMobilidadeEsq;
    }

    public Double getMediaEstabilidadeEsq() {
        return mediaEstabilidadeEsq;
    }

    public void setMediaEstabilidadeEsq(Double mediaEstabilidadeEsq) {
        this.mediaEstabilidadeEsq = mediaEstabilidadeEsq;
    }

    public ResultadoVidaEnum getQualidadeVida() {
        return qualidadeVida;
    }

    public void setQualidadeVida(ResultadoVidaEnum qualidadeVida) {
        this.qualidadeVida = qualidadeVida;
    }

    public ResultadoMovimentoEnum getQualidadeMovimento() {
        return qualidadeMovimento;
    }

    public void setQualidadeMovimento(ResultadoMovimentoEnum qualidadeMovimento) {
        this.qualidadeMovimento = qualidadeMovimento;
    }

    public Double getSomaQualidadeMovimento() {
        return somaQualidadeMovimento;
    }

    public void setSomaQualidadeMovimento(Double somaQualidadeMovimento) {
        this.somaQualidadeMovimento = somaQualidadeMovimento;
    }

    public Integer getSomaQualidadeVida() {
        return somaQualidadeVida;
    }

    public void setSomaQualidadeVida(Integer somaQualidadeVida) {
        this.somaQualidadeVida = somaQualidadeVida;
    }

    public Date getDataResposta() {
        return dataResposta;
    }

    public void setDataResposta(Date dataResposta) {
        this.dataResposta = dataResposta;
    }

    public List<Movimento3D> getMobilidade() {
        return mobilidade;
    }

    public void setMobilidade(List<Movimento3D> mobilidade) {
        this.mobilidade = mobilidade;
    }

    public List<Movimento3D> getEstabilidade() {
        return estabilidade;
    }

    public void setEstabilidade(List<Movimento3D> estabilidade) {
        this.estabilidade = estabilidade;
    }

    public String getLinkPrint() throws Exception{
        JSONObject j = new JSONObject();
        j.put("i", getCodigo());
        return "../avaliacaointegrada?j=".concat(Uteis.encriptar(j.toString(), "cript0p4r4msint"));
    }

    public AvaliacaoFisica getAvaliacaoFisica() {
        return avaliacaoFisica;
    }

    public void setAvaliacaoFisica(AvaliacaoFisica avaliacaoFisica) {
        this.avaliacaoFisica = avaliacaoFisica;
    }

    public Boolean getSelecionado() {
        return selecionado;}

    public void setSelecionado(Boolean selecionado) {this.selecionado = selecionado;}

    public Boolean getEditado() { return editado; }

    public void setEditado(Boolean editado) { this.editado = editado; }

    public String getDescricaoParaLog(ItemAvaliacaoFisica iAf2) {
        try {
            StringBuilder log = new StringBuilder();
            log.append(UtilReflection.difference(this, iAf2));
            return log.toString();
        } catch (Exception ex) {
            ex.printStackTrace();
            return "ERRO gerar log";
        }
    }
}
