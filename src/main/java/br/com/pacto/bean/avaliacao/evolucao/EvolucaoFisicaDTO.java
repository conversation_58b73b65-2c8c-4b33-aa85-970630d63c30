package br.com.pacto.bean.avaliacao.evolucao;

import br.com.pacto.bean.avaliacao.EvolucaoFisica;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collection;

/**
 * <AUTHOR> Si<PERSON>ira 29/01/2019
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EvolucaoFisicaDTO implements Serializable {

    private Integer numeroAvaliacoes;
    private Long numeroDiasConsiderados;
    private Long treinosPeriodos;
    private Double mediaTreinoSemana;
    private Collection<HistoricoImcDTO> historicoImc;
    private Double percentualGordura;
    private Boolean percentualGorduraAumentado;
    private Double massaGordaInicial;
    private Double massaGordaPenultima;
    private Double massaGordaFinal;
    private Double percentualMassaMagra;
    private Boolean percentualMassaMagraAumentando;
    private Double massaMagraInicial;
    private Double massaMagraPenultima;
    private Double massaMagraFinal;
    private EvolucaoGrupoTrabalhadoDTO gruposTrabalhados;
    private EvolucaoPerimetriaDTO perimetria;
    private Collection<EvolucaoProporcaoPesoGorduraPontoDTO> proporcaoPesoGordura;
    private Collection<EvolucaoDobraItemDTO> dobras;
    private double porcentagemExerciciosGrupos;


    public EvolucaoFisicaDTO() {
    }

    public Integer getNumeroAvaliacoes() {
        return numeroAvaliacoes;
    }

    public void setNumeroAvaliacoes(Integer numeroAvaliacoes) {
        this.numeroAvaliacoes = numeroAvaliacoes;
    }

    public Long getNumeroDiasConsiderados() {
        return numeroDiasConsiderados;
    }

    public void setNumeroDiasConsiderados(Long numeroDiasConsiderados) {
        this.numeroDiasConsiderados = numeroDiasConsiderados;
    }

    public Long getTreinosPeriodos() {
        return treinosPeriodos;
    }

    public void setTreinosPeriodos(Long treinosPeriodos) {
        this.treinosPeriodos = treinosPeriodos;
    }

    public Double getMediaTreinoSemana() {
        return mediaTreinoSemana;
    }

    public void setMediaTreinoSemana(Double mediaTreinoSemana) {
        this.mediaTreinoSemana = mediaTreinoSemana;
    }

    public Collection<HistoricoImcDTO> getHistoricoImc() {
        return historicoImc;
    }

    public void setHistoricoImc(Collection<HistoricoImcDTO> historicoImc) {
        this.historicoImc = historicoImc;
    }

    public Double getPercentualGordura() {
        return percentualGordura;
    }

    public void setPercentualGordura(Double percentualGordura) {
        this.percentualGordura = percentualGordura;
    }

    public Boolean getPercentualGorduraAumentado() {
        return percentualGorduraAumentado;
    }

    public void setPercentualGorduraAumentado(Boolean percentualGorduraAumentado) {
        this.percentualGorduraAumentado = percentualGorduraAumentado;
    }

    public Double getMassaGordaInicial() {
        return massaGordaInicial;
    }

    public void setMassaGordaInicial(Double massaGordaInicial) {
        this.massaGordaInicial = massaGordaInicial;
    }

    public Double getMassaGordaFinal() {
        return massaGordaFinal;
    }

    public void setMassaGordaFinal(Double massaGordaFinal) {
        this.massaGordaFinal = massaGordaFinal;
    }

    public Double getPercentualMassaMagra() {
        return percentualMassaMagra;
    }

    public void setPercentualMassaMagra(Double percentualMassaMagra) {
        this.percentualMassaMagra = percentualMassaMagra;
    }

    public Boolean getPercentualMassaMagraAumentando() {
        return percentualMassaMagraAumentando;
    }

    public void setPercentualMassaMagraAumentando(Boolean percentualMassaMagraAumentando) {
        this.percentualMassaMagraAumentando = percentualMassaMagraAumentando;
    }

    public Double getMassaMagraInicial() {
        return massaMagraInicial;
    }

    public void setMassaMagraInicial(Double massaMagraInicial) {
        this.massaMagraInicial = massaMagraInicial;
    }

    public Double getMassaMagraFinal() {
        return massaMagraFinal;
    }

    public void setMassaMagraFinal(Double massaMagraFinal) {
        this.massaMagraFinal = massaMagraFinal;
    }

    public EvolucaoGrupoTrabalhadoDTO getGruposTrabalhados() {
        return gruposTrabalhados;
    }

    public void setGruposTrabalhados(EvolucaoGrupoTrabalhadoDTO gruposTrabalhados) {
        this.gruposTrabalhados = gruposTrabalhados;
    }

    public EvolucaoPerimetriaDTO getPerimetria() {
        return perimetria;
    }

    public void setPerimetria(EvolucaoPerimetriaDTO perimetria) {
        this.perimetria = perimetria;
    }

    public Collection<EvolucaoProporcaoPesoGorduraPontoDTO> getProporcaoPesoGordura() {
        return proporcaoPesoGordura;
    }

    public void setProporcaoPesoGordura(Collection<EvolucaoProporcaoPesoGorduraPontoDTO> proporcaoPesoGordura) {
        this.proporcaoPesoGordura = proporcaoPesoGordura;
    }

    public Collection<EvolucaoDobraItemDTO> getDobras() {
        return dobras;
    }

    public void setDobras(Collection<EvolucaoDobraItemDTO> dobras) {
        this.dobras = dobras;
    }

    public double getPorcentagemExerciciosGrupos() {
        return porcentagemExerciciosGrupos;
    }

    public void setPorcentagemExerciciosGrupos(double porcentagemExerciciosGrupos) {
        this.porcentagemExerciciosGrupos = porcentagemExerciciosGrupos;
    }

    public Double getMassaGordaPenultima() {
        return massaGordaPenultima;
    }

    public void setMassaGordaPenultima(Double massaGordaPenultima) {
        this.massaGordaPenultima = massaGordaPenultima;
    }

    public Double getMassaMagraPenultima() {
        return massaMagraPenultima;
    }

    public void setMassaMagraPenultima(Double massaMagraPenultima) {
        this.massaMagraPenultima = massaMagraPenultima;
    }
}
