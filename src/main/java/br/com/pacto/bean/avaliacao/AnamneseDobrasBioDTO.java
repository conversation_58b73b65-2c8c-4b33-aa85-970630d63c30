package br.com.pacto.bean.avaliacao;

public class AnamneseDobrasBioDTO {

    private Double imc;
    private Double massaGorda;
    private Double percentMassaMagra;
    private Double residuos;
    private Double gorduraIdeal;
    private Double reatancia;
    private Double necFisica;
    private Double necCalorica;
    private Double gorduraVisceral;
    private Double percentMassaGorda;
    private Double massaMagra;
    private Double ossos;
    private Double musculos;
    private Double resistencia;
    private Double percentAgua;
    private Double tmb;
    private Double idadeMetabolica;

    public AnamneseDobrasBioDTO() {
    }

    public AnamneseDobrasBioDTO(AvaliacaoFisica avaliacaoFisica) {
        this.imc = avaliacaoFisica.getImc();
        this.massaGorda = avaliacaoFisica.getMassaGorda();
        this.percentMassaMagra = avaliacaoFisica.getPercentualMassaMagra();
        this.residuos = avaliacaoFisica.getResidual();
        this.gorduraIdeal = avaliacaoFisica.getGorduraIdeal();
        this.reatancia = avaliacaoFisica.getReatancia();
        this.necFisica = avaliacaoFisica.getNecessidadeFisica();
        this.necCalorica = avaliacaoFisica.getNecessidadeCalorica();
        this.gorduraVisceral = avaliacaoFisica.getGorduraVisceral();
        this.percentMassaGorda = avaliacaoFisica.getPercentualGordura();
        this.massaMagra = avaliacaoFisica.getMassaMagra();
        this.ossos = avaliacaoFisica.getPesoOsseo();
        this.musculos = avaliacaoFisica.getPesoMuscular();
        this.resistencia = avaliacaoFisica.getResistencia();
        this.percentAgua = avaliacaoFisica.getPercentualAgua();
        this.tmb = avaliacaoFisica.getTmb();
        this.idadeMetabolica = avaliacaoFisica.getIdadeMetabolica();
    }

    public Double getImc() {
        return imc;
    }

    public void setImc(Double imc) {
        this.imc = imc;
    }

    public Double getMassaGorda() {
        return massaGorda;
    }

    public void setMassaGorda(Double massaGorda) {
        this.massaGorda = massaGorda;
    }

    public Double getPercentMassaMagra() {
        return percentMassaMagra;
    }

    public void setPercentMassaMagra(Double percentMassaMagra) {
        this.percentMassaMagra = percentMassaMagra;
    }

    public Double getResiduos() {
        return residuos;
    }

    public void setResiduos(Double residuos) {
        this.residuos = residuos;
    }

    public Double getGorduraIdeal() {
        return gorduraIdeal;
    }

    public void setGorduraIdeal(Double gorduraIdeal) {
        this.gorduraIdeal = gorduraIdeal;
    }

    public Double getReatancia() {
        return reatancia;
    }

    public void setReatancia(Double reatancia) {
        this.reatancia = reatancia;
    }

    public Double getNecFisica() {
        return necFisica;
    }

    public void setNecFisica(Double necFisica) {
        this.necFisica = necFisica;
    }

    public Double getNecCalorica() {
        return necCalorica;
    }

    public void setNecCalorica(Double necCalorica) {
        this.necCalorica = necCalorica;
    }

    public Double getGorduraVisceral() {
        return gorduraVisceral;
    }

    public void setGorduraVisceral(Double gorduraVisceral) {
        this.gorduraVisceral = gorduraVisceral;
    }

    public Double getPercentMassaGorda() {
        return percentMassaGorda;
    }

    public void setPercentMassaGorda(Double percentMassaGorda) {
        this.percentMassaGorda = percentMassaGorda;
    }

    public Double getMassaMagra() {
        return massaMagra;
    }

    public void setMassaMagra(Double massaMagra) {
        this.massaMagra = massaMagra;
    }

    public Double getOssos() {
        return ossos;
    }

    public void setOssos(Double ossos) {
        this.ossos = ossos;
    }

    public Double getMusculos() {
        return musculos;
    }

    public void setMusculos(Double musculos) {
        this.musculos = musculos;
    }

    public Double getResistencia() {
        return resistencia;
    }

    public void setResistencia(Double resistencia) {
        this.resistencia = resistencia;
    }

    public Double getPercentAgua() {
        return percentAgua;
    }

    public void setPercentAgua(Double percentAgua) {
        this.percentAgua = percentAgua;
    }

    public Double getTmb() {
        return tmb;
    }

    public void setTmb(Double tmb) {
        this.tmb = tmb;
    }

    public Double getIdadeMetabolica() {
        return idadeMetabolica;
    }

    public void setIdadeMetabolica(Double idadeMetabolica) {
        this.idadeMetabolica = idadeMetabolica;
    }
}
