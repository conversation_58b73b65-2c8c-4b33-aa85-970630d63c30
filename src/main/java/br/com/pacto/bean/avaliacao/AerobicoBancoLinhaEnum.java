package br.com.pacto.bean.avaliacao;

/**
 * <AUTHOR> 29/01/2019
 */
public enum AerobicoBancoLinhaEnum {

    CONDICAO_ATLETICA("condicao.atletica"),
    FAIXA_RECOMENDAVEL("faixa.recomendavel"),
    BAIXA_APTIDAO("baixa.aptidao"),
    CONDICAO_RISCO("condicao.risco");

    private String descricao;

    AerobicoBancoLinhaEnum(String descricao) {
        this.descricao = descricao;
    }

    public String getDescricao() {
        return descricao;
    }


    public static AerobicoBancoLinhaEnum getInstance(final String str)  {
        for (AerobicoBancoLinhaEnum linha : AerobicoBancoLinhaEnum.values()){
            if (str.equalsIgnoreCase(linha.getDescricao())){
                return linha;
            }
        }
        return BAIXA_APTIDAO;
    }
}
