package br.com.pacto.bean.avaliacao;

import br.com.pacto.util.enumeradores.ProtocolosAvaliacaoFisicaEnum;
import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class AvaliacaoDobrasDTO {

    private ProtocolosAvaliacaoFisicaEnum protocolo;
    private Double abdominal;
    private Double peitoral;
    private Double coxaMedial;
    private Double subescapular;
    private Double supraEspinhal;
    private Double supraIliaca;
    private Double triceps;
    private Double biceps;
    private Double axilarMedia;
    private Double panturrilha;
    private AnamneseDobrasBioDTO bioimpedancia;
    private String logBalanca;

    public AvaliacaoDobrasDTO() {
    }

    public AvaliacaoDobrasDTO(AvaliacaoFisica avaliacaoFisica) {
        this.protocolo = avaliacaoFisica.getProtocolo();
        this.abdominal = avaliacaoFisica.getAbdominal();
        this.peitoral = avaliacaoFisica.getPeitoral();
        this.coxaMedial = avaliacaoFisica.getCoxaMedial();
        this.subescapular = avaliacaoFisica.getSubescapular();
        this.supraEspinhal = avaliacaoFisica.getSupraEspinhal();
        this.supraIliaca = avaliacaoFisica.getSupraIliaca();
        this.triceps = avaliacaoFisica.getTriceps();
        this.biceps = avaliacaoFisica.getBiceps();
        this.axilarMedia = avaliacaoFisica.getAxilarMedia();
        this.panturrilha = avaliacaoFisica.getPanturrilha();
        this.logBalanca = avaliacaoFisica.getLogBalanca();
        if(ProtocolosAvaliacaoFisicaEnum.BIOIMPEDANCIA.equals(this.protocolo)){
            this.bioimpedancia = new AnamneseDobrasBioDTO(avaliacaoFisica);
        }

    }

    public AnamneseDobrasBioDTO getBioimpedancia() {
        return bioimpedancia;
    }

    public void setBioimpedancia(AnamneseDobrasBioDTO bioimpedancia) {
        this.bioimpedancia = bioimpedancia;
    }

    public ProtocolosAvaliacaoFisicaEnum getProtocolo() {
        return protocolo;
    }

    public void setProtocolo(ProtocolosAvaliacaoFisicaEnum protocolo) {
        this.protocolo = protocolo;
    }

    public Double getAbdominal() {
        return abdominal;
    }

    public void setAbdominal(Double abdominal) {
        this.abdominal = abdominal;
    }

    public Double getPeitoral() {
        return peitoral;
    }

    public void setPeitoral(Double peitoral) {
        this.peitoral = peitoral;
    }

    public Double getCoxaMedial() {
        return coxaMedial;
    }

    public void setCoxaMedial(Double coxaMedial) {
        this.coxaMedial = coxaMedial;
    }

    public Double getSubescapular() {
        return subescapular;
    }

    public void setSubescapular(Double subescapular) {
        this.subescapular = subescapular;
    }

    public Double getSupraEspinhal() {
        return supraEspinhal;
    }

    public void setSupraEspinhal(Double supraEspinhal) {
        this.supraEspinhal = supraEspinhal;
    }

    public Double getSupraIliaca() {
        return supraIliaca;
    }

    public void setSupraIliaca(Double supraIliaca) {
        this.supraIliaca = supraIliaca;
    }

    public Double getTriceps() {
        return triceps;
    }

    public void setTriceps(Double triceps) {
        this.triceps = triceps;
    }

    public Double getBiceps() {
        return biceps;
    }

    public void setBiceps(Double biceps) {
        this.biceps = biceps;
    }

    public Double getAxilarMedia() {
        return axilarMedia;
    }

    public void setAxilarMedia(Double axilarMedia) {
        this.axilarMedia = axilarMedia;
    }

    public Double getPanturrilha() {
        return panturrilha;
    }

    public void setPanturrilha(Double panturrilha) {
        this.panturrilha = panturrilha;
    }

    public String getLogBalanca() {
        return logBalanca;
    }

    public void setLogBalanca(String logBalanca) {
        this.logBalanca = logBalanca;
    }
}
