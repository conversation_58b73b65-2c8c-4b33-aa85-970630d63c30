package br.com.pacto.bean.avaliacao;

import br.com.pacto.bean.anamnese.AnamneseTO;
import br.com.pacto.bean.anamnese.ResultadoMovimentoEnum;
import br.com.pacto.bean.anamnese.ResultadoVidaEnum;
import br.com.pacto.controller.json.avaliacao.AvaliadorDTO;
import com.fasterxml.jackson.annotation.JsonInclude;

import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class AvaliacaoIntegradaDTO {

    private Integer id;
    private Long dataLancamento;
    private AvaliadorDTO avaliador;
    private Double somaQualidadeMovimento;
    private Integer somaQualidadeVida;
    @Enumerated(EnumType.ORDINAL)
    private ResultadoVidaEnum qualidadeVida;
    @Enumerated(EnumType.ORDINAL)
    private ResultadoMovimentoEnum qualidadeMovimento;
    private AnamneseTO anamneseSelecionada;
    private List<AnamnesePerguntaRespostaDTO> anamneseRespostas;
    private List<Movimento3DDTO> mobilidade;
    private List<Movimento3DDTO> estabilidade;
    private Integer somaMobilidadeDir;
    private Integer somaEstabilidadeDir;
    private Double mediaMobilidadeDir;
    private Double mediaEstabilidadeDir;
    private Integer somaMobilidadeEsq;
    private Integer somaEstabilidadeEsq;
    private Double mediaMobilidadeEsq;
    private Double mediaEstabilidadeEsq;

    public AvaliacaoIntegradaDTO() { }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Long getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Long dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public AvaliadorDTO getAvaliador() {
        return avaliador;
    }

    public void setAvaliador(AvaliadorDTO avaliador) {
        this.avaliador = avaliador;
    }

    public Double getSomaQualidadeMovimento() {
        return somaQualidadeMovimento;
    }

    public void setSomaQualidadeMovimento(Double somaQualidadeMovimento) {
        this.somaQualidadeMovimento = somaQualidadeMovimento;
    }

    public Integer getSomaQualidadeVida() {
        return somaQualidadeVida;
    }

    public void setSomaQualidadeVida(Integer somaQualidadeVida) {
        this.somaQualidadeVida = somaQualidadeVida;
    }

    public ResultadoVidaEnum getQualidadeVida() {
        return qualidadeVida;
    }

    public void setQualidadeVida(ResultadoVidaEnum qualidadeVida) {
        this.qualidadeVida = qualidadeVida;
    }

    public ResultadoMovimentoEnum getQualidadeMovimento() {
        return qualidadeMovimento;
    }

    public void setQualidadeMovimento(ResultadoMovimentoEnum qualidadeMovimento) {
        this.qualidadeMovimento = qualidadeMovimento;
    }

    public AnamneseTO getAnamneseSelecionada() {
        return anamneseSelecionada;
    }

    public void setAnamneseSelecionada(AnamneseTO anamneseSelecionada) {
        this.anamneseSelecionada = anamneseSelecionada;
    }

    public List<AnamnesePerguntaRespostaDTO> getAnamneseRespostas() {
        return anamneseRespostas;
    }

    public void setAnamneseRespostas(List<AnamnesePerguntaRespostaDTO> anamneseRespostas) {
        this.anamneseRespostas = anamneseRespostas;
    }

    public List<Movimento3DDTO> getMobilidade() {
        return mobilidade;
    }

    public void setMobilidade(List<Movimento3DDTO> mobilidade) {
        this.mobilidade = mobilidade;
    }

    public List<Movimento3DDTO> getEstabilidade() {
        return estabilidade;
    }

    public void setEstabilidade(List<Movimento3DDTO> estabilidade) {
        this.estabilidade = estabilidade;
    }

    public Integer getSomaMobilidadeDir() {
        return somaMobilidadeDir;
    }

    public void setSomaMobilidadeDir(Integer somaMobilidadeDir) {
        this.somaMobilidadeDir = somaMobilidadeDir;
    }

    public Integer getSomaEstabilidadeDir() {
        return somaEstabilidadeDir;
    }

    public void setSomaEstabilidadeDir(Integer somaEstabilidadeDir) {
        this.somaEstabilidadeDir = somaEstabilidadeDir;
    }

    public Double getMediaMobilidadeDir() {
        return mediaMobilidadeDir;
    }

    public void setMediaMobilidadeDir(Double mediaMobilidadeDir) {
        this.mediaMobilidadeDir = mediaMobilidadeDir;
    }

    public Double getMediaEstabilidadeDir() {
        return mediaEstabilidadeDir;
    }

    public void setMediaEstabilidadeDir(Double mediaEstabilidadeDir) {
        this.mediaEstabilidadeDir = mediaEstabilidadeDir;
    }

    public Integer getSomaMobilidadeEsq() {
        return somaMobilidadeEsq;
    }

    public void setSomaMobilidadeEsq(Integer somaMobilidadeEsq) {
        this.somaMobilidadeEsq = somaMobilidadeEsq;
    }

    public Integer getSomaEstabilidadeEsq() {
        return somaEstabilidadeEsq;
    }

    public void setSomaEstabilidadeEsq(Integer somaEstabilidadeEsq) {
        this.somaEstabilidadeEsq = somaEstabilidadeEsq;
    }

    public Double getMediaMobilidadeEsq() {
        return mediaMobilidadeEsq;
    }

    public void setMediaMobilidadeEsq(Double mediaMobilidadeEsq) {
        this.mediaMobilidadeEsq = mediaMobilidadeEsq;
    }

    public Double getMediaEstabilidadeEsq() {
        return mediaEstabilidadeEsq;
    }

    public void setMediaEstabilidadeEsq(Double mediaEstabilidadeEsq) {
        this.mediaEstabilidadeEsq = mediaEstabilidadeEsq;
    }

}
