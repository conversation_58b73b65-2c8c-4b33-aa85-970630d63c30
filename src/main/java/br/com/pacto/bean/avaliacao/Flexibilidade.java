/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.avaliacao;

import javax.persistence.*;

import org.hibernate.annotations.Type;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table
public class Flexibilidade {
    
     @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private Integer alcance;
    private Integer mobilidadeTornozeloEsquerdo;
    private Integer mobilidadeTornozeloDireito;
    @Enumerated(EnumType.ORDINAL)
    private ClassificacaoEnum classificacao;
    @Lob
    @Type(type="org.hibernate.type.StringClobType")
    @Column(columnDefinition = "text", length = 99999)
    private String observacao;
    @Lob
    @Type(type="org.hibernate.type.StringClobType")
    @Column(columnDefinition = "text", length = 99999)
    private String observacaoOmbro;
    @Lob
    @Type(type="org.hibernate.type.StringClobType")
    @Column(columnDefinition = "text", length = 99999)
    private String observacaoQuadril;
    @Lob
    @Type(type="org.hibernate.type.StringClobType")
    @Column(columnDefinition = "text", length = 99999)
    private String observacaoJoelho;
    @Lob
    @Type(type="org.hibernate.type.StringClobType")
    @Column(columnDefinition = "text", length = 99999)
    private String observacaoTornozelo;

    @OneToOne
    private AvaliacaoFisica avaliacao;
    @Enumerated(EnumType.ORDINAL)
    private ClassificacaoEnum mobilidadeOmbroEsquerdo;
    @Enumerated(EnumType.ORDINAL)
    private ClassificacaoEnum mobilidadeOmbroDireito;
    @Enumerated(EnumType.ORDINAL)
    private ClassificacaoEnum mobilidadeJoelhoEsquerdo;
    @Enumerated(EnumType.ORDINAL)
    private ClassificacaoEnum mobilidadeJoelhoDireito;
    @Enumerated(EnumType.ORDINAL)
    private ClassificacaoEnum mobilidadeQuadrilEsquerdo;
    @Enumerated(EnumType.ORDINAL)
    private ClassificacaoEnum mobilidadeQuadrilDireito;

    public Flexibilidade() {
    }

    public Flexibilidade(final AvaliacaoFlexibilidadeDTO dto) {
        this.alcance = dto.getAlcanceMaximo();
        this.classificacao = dto.getIndicadorColuna();
        this.observacao = dto.getObservacao();
        this.observacaoOmbro = dto.getObservacaoOmbro();
        this.observacaoQuadril = dto.getObservacaoQuadril();
        this.observacaoJoelho = dto.getObservacaoJoelho();
        this.observacaoTornozelo = dto.getObservacaoTornozelo();
        this.mobilidadeOmbroEsquerdo = dto.getMobilidadeOmbroEsquerdo();
        this.mobilidadeOmbroDireito = dto.getMobilidadeOmbroDireito();
        this.mobilidadeQuadrilDireito = dto.getMobilidadeQuadrilDireito();
        this.mobilidadeQuadrilEsquerdo = dto.getMobilidadeQuadrilEsquerdo();
        this.mobilidadeJoelhoEsquerdo = dto.getMobilidadeJoelhoEsquerdo();
        this.mobilidadeJoelhoDireito = dto.getMobilidadeJoelhoDireito();
        this.mobilidadeTornozeloEsquerdo = dto.getMobilidadeTornozeloEsquerdo();
        this.mobilidadeTornozeloDireito = dto.getMobilidadeTornozeloDireito();
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getAlcance() {
        return alcance;
    }

    public void setAlcance(Integer alcance) {
        this.alcance = alcance;
    }

    public ClassificacaoEnum getClassificacao() {
        return classificacao;
    }

    public void setClassificacao(ClassificacaoEnum classificacao) {
        this.classificacao = classificacao;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public AvaliacaoFisica getAvaliacao() {
        return avaliacao;
    }

    public void setAvaliacao(AvaliacaoFisica avaliacao) {
        this.avaliacao = avaliacao;
    }

    public ClassificacaoEnum getMobilidadeOmbroEsquerdo() {
        return mobilidadeOmbroEsquerdo;
    }

    public void setMobilidadeOmbroEsquerdo(ClassificacaoEnum mobilidadeOmbroEsquerdo) {
        this.mobilidadeOmbroEsquerdo = mobilidadeOmbroEsquerdo;
    }

    public ClassificacaoEnum getMobilidadeOmbroDireito() {
        return mobilidadeOmbroDireito;
    }

    public void setMobilidadeOmbroDireito(ClassificacaoEnum mobilidadeOmbroDireito) {
        this.mobilidadeOmbroDireito = mobilidadeOmbroDireito;
    }

    public ClassificacaoEnum getMobilidadeJoelhoEsquerdo() {
        return mobilidadeJoelhoEsquerdo;
    }

    public void setMobilidadeJoelhoEsquerdo(ClassificacaoEnum mobilidadeJoelhoEsquerdo) {
        this.mobilidadeJoelhoEsquerdo = mobilidadeJoelhoEsquerdo;
    }

    public ClassificacaoEnum getMobilidadeJoelhoDireito() {
        return mobilidadeJoelhoDireito;
    }

    public void setMobilidadeJoelhoDireito(ClassificacaoEnum mobilidadeJoelhoDireito) {
        this.mobilidadeJoelhoDireito = mobilidadeJoelhoDireito;
    }

    public ClassificacaoEnum getMobilidadeQuadrilEsquerdo() {
        return mobilidadeQuadrilEsquerdo;
    }

    public void setMobilidadeQuadrilEsquerdo(ClassificacaoEnum mobilidadeQuadrilEsquerdo) {
        this.mobilidadeQuadrilEsquerdo = mobilidadeQuadrilEsquerdo;
    }

    public ClassificacaoEnum getMobilidadeQuadrilDireito() {
        return mobilidadeQuadrilDireito;
    }

    public void setMobilidadeQuadrilDireito(ClassificacaoEnum mobilidadeQuadrilDireito) {
        this.mobilidadeQuadrilDireito = mobilidadeQuadrilDireito;
    }

    public String getObservacaoOmbro() {
        return observacaoOmbro;
    }

    public void setObservacaoOmbro(String observacaoOmbro) {
        this.observacaoOmbro = observacaoOmbro;
    }

    public String getObservacaoQuadril() {
        return observacaoQuadril;
    }

    public void setObservacaoQuadril(String observacaoQuadril) {
        this.observacaoQuadril = observacaoQuadril;
    }

    public String getObservacaoJoelho() {
        return observacaoJoelho;
    }

    public void setObservacaoJoelho(String observacaoJoelho) {
        this.observacaoJoelho = observacaoJoelho;
    }

    public String getObservacaoTornozelo() {
        return observacaoTornozelo;
    }

    public void setObservacaoTornozelo(String observacaoTornozelo) {
        this.observacaoTornozelo = observacaoTornozelo;
    }

    public Integer getMobilidadeTornozeloEsquerdo() {
        return mobilidadeTornozeloEsquerdo;
    }

    public void setMobilidadeTornozeloEsquerdo(Integer mobilidadeTornozeloEsquerdo) {
        this.mobilidadeTornozeloEsquerdo = mobilidadeTornozeloEsquerdo;
    }

    public Integer getMobilidadeTornozeloDireito() {
        return mobilidadeTornozeloDireito;
    }

    public void setMobilidadeTornozeloDireito(Integer mobilidadeTornozeloDireito) {
        this.mobilidadeTornozeloDireito = mobilidadeTornozeloDireito;
    }

}
