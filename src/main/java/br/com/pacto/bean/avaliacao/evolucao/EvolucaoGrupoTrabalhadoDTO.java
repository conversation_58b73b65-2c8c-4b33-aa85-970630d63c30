package br.com.pacto.bean.avaliacao.evolucao;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collection;

/**
 * <AUTHOR> Siqueira 30/01/2019
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EvolucaoGrupoTrabalhadoDTO implements Serializable {

    private Collection<GrupoTrabalhadoItemDTO> programaAtual;
    private Collection<GrupoTrabalhadoItemDTO> durantePeriodo;

    public EvolucaoGrupoTrabalhadoDTO() {
        this.programaAtual = new ArrayList<GrupoTrabalhadoItemDTO>();
        this.durantePeriodo = new ArrayList<GrupoTrabalhadoItemDTO>();
    }

    public Collection<GrupoTrabalhadoItemDTO> getProgramaAtual() {
        return programaAtual;
    }

    public void setProgramaAtual(Collection<GrupoTrabalhadoItemDTO> programaAtual) {
        this.programaAtual = programaAtual;
    }

    public Collection<GrupoTrabalhadoItemDTO> getDurantePeriodo() {
        return durantePeriodo;
    }

    public void setDurantePeriodo(Collection<GrupoTrabalhadoItemDTO> durantePeriodo) {
        this.durantePeriodo = durantePeriodo;
    }
}
