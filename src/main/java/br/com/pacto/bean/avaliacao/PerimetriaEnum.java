package br.com.pacto.bean.avaliacao;

/**
 * Created by alcides on 27/09/2017.
 */
public enum PerimetriaEnum {
    ANTEBRACO_ESQ("antebracoEsq"),
    ANTEBRACO_DIR("antebracoDir"),
    BRACO_RELAXADO_ESQ("bracoRelaxadoEsq"),
    BRACO_RELAXADO_DIR("bracoRelaxadoDir"),
    BRACO_CONTRAIDO_ESQ("bracoContraidoEsq"),
    BRACO_CONTRAIDO_DIR("bracoContraidoDir"),
    COXA_DISTAL_ESQ("coxaDistalEsq"),
    COXA_DISTAL_DIR("coxaDistalDir"),
    COXA_MEDIAL_DIR("coxaMediaDir"),
    COXA_MEDIAL_ESQ("coxaMediaEsq"),
    COXA_PROXIMAL_DIR("coxaProximalDir"),
    COXA_PROXIMAL_ESQ("coxaProximalEsq"),
    PANTURRILHA_DIR("panturrilhaDir"),
    PANTURRILHA_ESQ("panturrilhaEsq"),
    PESCOCO("pescoco"),
    OMBRO("ombro"),
    TORAX("toraxBusto"),
    QUADRIL("quadril"),
    CINTURA("cintura"),
    CIRCUNFERENCIA_ABDOMINAL("circunferenciaAbdominal"),
    GLUTEO("gluteo");

    private String field;
    private String css;

    PerimetriaEnum(String field) {
        this.field = field;
        this.css = ".campo"+field;
    }
    PerimetriaEnum(String field, String css) {
        this.css = css;
        this.field = field;
    }

    public String getCss() {
        return css;
    }

    public String getField() {
        return field;
    }

    public static PerimetriaEnum getFromOrdinal(int o){
        for(PerimetriaEnum p : values()){
            if(p.ordinal() == o){
                return p;
            }
        }
        return null;
    }
}
