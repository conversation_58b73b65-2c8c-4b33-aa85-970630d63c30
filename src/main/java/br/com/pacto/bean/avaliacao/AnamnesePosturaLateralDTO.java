package br.com.pacto.bean.avaliacao;

import br.com.pacto.util.impl.UtilReflection;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class AnamnesePosturaLateralDTO {

    private Boolean anterversaoQuadril;
    private Boolean hipercifoseToracica;
    private Boolean hiperlordoseCervical;
    private Boolean hiperlordoseLombar;
    private Boolean joelhoFlexo;
    private Boolean joelhoRecurvado;
    private Boolean protusaoAbdominal;
    private Boolean peCalcaneo;
    private Boolean peCavo;
    private Boolean peEquino;
    private Boolean pePlano;
    private Boolean retificacaoCervical;
    private Boolean retificacaoLombar;
    private Boolean retroversaoQuadril;
    private Boolean rotacaoInternaOmbros;

    public AnamnesePosturaLateralDTO() {
    }

    public AnamnesePosturaLateralDTO(List<ItemAvaliacaoPostural> itemsAvaliacaoPostural) throws Exception{
        List<String> attributes = UtilReflection.getListAttributes(AnamnesePosturaLateralDTO.class);
        for(ItemAvaliacaoPostural i : itemsAvaliacaoPostural){
            if(attributes.contains(i.getItem().getField())){
                UtilReflection.setValor(this, i.getSelecionado(), i.getItem().getField());
            }
        }
    }

    public Boolean getAnterversaoQuadril() {
        return anterversaoQuadril;
    }

    public void setAnterversaoQuadril(Boolean anterversaoQuadril) {
        this.anterversaoQuadril = anterversaoQuadril;
    }

    public Boolean getHipercifoseToracica() {
        return hipercifoseToracica;
    }

    public void setHipercifoseToracica(Boolean hipercifoseToracica) {
        this.hipercifoseToracica = hipercifoseToracica;
    }

    public Boolean getHiperlordoseCervical() {
        return hiperlordoseCervical;
    }

    public void setHiperlordoseCervical(Boolean hiperlordoseCervical) {
        this.hiperlordoseCervical = hiperlordoseCervical;
    }

    public Boolean getHiperlordoseLombar() {
        return hiperlordoseLombar;
    }

    public void setHiperlordoseLombar(Boolean hiperlordoseLombar) {
        this.hiperlordoseLombar = hiperlordoseLombar;
    }

    public Boolean getJoelhoFlexo() {
        return joelhoFlexo;
    }

    public void setJoelhoFlexo(Boolean joelhoFlexo) {
        this.joelhoFlexo = joelhoFlexo;
    }

    public Boolean getJoelhoRecurvado() {
        return joelhoRecurvado;
    }

    public void setJoelhoRecurvado(Boolean joelhoRecurvado) {
        this.joelhoRecurvado = joelhoRecurvado;
    }

    public Boolean getProtusaoAbdominal() {
        return protusaoAbdominal;
    }

    public void setProtusaoAbdominal(Boolean protusaoAbdominal) {
        this.protusaoAbdominal = protusaoAbdominal;
    }

    public Boolean getPeCalcaneo() {
        return peCalcaneo;
    }

    public void setPeCalcaneo(Boolean peCalcaneo) {
        this.peCalcaneo = peCalcaneo;
    }

    public Boolean getPeCavo() {
        return peCavo;
    }

    public void setPeCavo(Boolean peCavo) {
        this.peCavo = peCavo;
    }

    public Boolean getPeEquino() {
        return peEquino;
    }

    public void setPeEquino(Boolean peEquino) {
        this.peEquino = peEquino;
    }

    public Boolean getPePlano() {
        return pePlano;
    }

    public void setPePlano(Boolean pePlano) {
        this.pePlano = pePlano;
    }

    public Boolean getRetificacaoCervical() {
        return retificacaoCervical;
    }

    public void setRetificacaoCervical(Boolean retificacaoCervical) {
        this.retificacaoCervical = retificacaoCervical;
    }

    public Boolean getRetificacaoLombar() {
        return retificacaoLombar;
    }

    public void setRetificacaoLombar(Boolean retificacaoLombar) {
        this.retificacaoLombar = retificacaoLombar;
    }

    public Boolean getRetroversaoQuadril() {
        return retroversaoQuadril;
    }

    public void setRetroversaoQuadril(Boolean retroversaoQuadril) {
        this.retroversaoQuadril = retroversaoQuadril;
    }

    public Boolean getRotacaoInternaOmbros() {
        return rotacaoInternaOmbros;
    }

    public void setRotacaoInternaOmbros(Boolean rotacaoInternaOmbros) {
        this.rotacaoInternaOmbros = rotacaoInternaOmbros;
    }
}
