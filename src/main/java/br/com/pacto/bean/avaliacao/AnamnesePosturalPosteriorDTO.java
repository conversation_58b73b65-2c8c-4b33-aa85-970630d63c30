package br.com.pacto.bean.avaliacao;

import br.com.pacto.util.impl.UtilReflection;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class AnamnesePosturalPosteriorDTO {

    private Boolean depressaoEscapular;
    private Boolean encurtamentoTrapezio;
    private Boolean escolioseCervical;
    private Boolean escolioseLombar;
    private Boolean escolioseToracica;
    private Boolean protacaoEscapular;
    private Boolean peValgo;
    private Boolean peVaro;
    private Boolean retracaoEscapular;

    public AnamnesePosturalPosteriorDTO() {
    }

    public AnamnesePosturalPosteriorDTO(List<ItemAvaliacaoPostural> itemsAvaliacaoPostural) throws Exception{
        List<String> attributes = UtilReflection.getListAttributes(AnamnesePosturalPosteriorDTO.class);
        for(ItemAvaliacaoPostural i : itemsAvaliacaoPostural){
            if(attributes.contains(i.getItem().getField())){
                UtilReflection.setValor(this, i.getSelecionado(), i.getItem().getField());
            }
        }
    }

    public Boolean getDepressaoEscapular() {
        return depressaoEscapular;
    }

    public void setDepressaoEscapular(Boolean depressaoEscapular) {
        this.depressaoEscapular = depressaoEscapular;
    }

    public Boolean getEncurtamentoTrapezio() {
        return encurtamentoTrapezio;
    }

    public void setEncurtamentoTrapezio(Boolean encurtamentoTrapezio) {
        this.encurtamentoTrapezio = encurtamentoTrapezio;
    }

    public Boolean getEscolioseCervical() {
        return escolioseCervical;
    }

    public void setEscolioseCervical(Boolean escolioseCervical) {
        this.escolioseCervical = escolioseCervical;
    }

    public Boolean getEscolioseLombar() {
        return escolioseLombar;
    }

    public void setEscolioseLombar(Boolean escolioseLombar) {
        this.escolioseLombar = escolioseLombar;
    }

    public Boolean getEscolioseToracica() {
        return escolioseToracica;
    }

    public void setEscolioseToracica(Boolean escolioseToracica) {
        this.escolioseToracica = escolioseToracica;
    }

    public Boolean getProtacaoEscapular() {
        return protacaoEscapular;
    }

    public void setProtacaoEscapular(Boolean protacaoEscapular) {
        this.protacaoEscapular = protacaoEscapular;
    }

    public Boolean getPeValgo() {
        return peValgo;
    }

    public void setPeValgo(Boolean peValgo) {
        this.peValgo = peValgo;
    }

    public Boolean getPeVaro() {
        return peVaro;
    }

    public void setPeVaro(Boolean peVaro) {
        this.peVaro = peVaro;
    }

    public Boolean getRetracaoEscapular() {
        return retracaoEscapular;
    }

    public void setRetracaoEscapular(Boolean retracaoEscapular) {
        this.retracaoEscapular = retracaoEscapular;
    }
}
