package br.com.pacto.bean.avaliacao;

import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class AvaliacaoPesoAlturaDTO {

    private Double peso;
    private Double altura;
    private String pressaoArterial;
    private String pressaoDiastolica;
    private String pressaoSistolica;
    private Integer frequencia;
    private Double frequenciaMax;

    public AvaliacaoPesoAlturaDTO() {
    }

    public AvaliacaoPesoAlturaDTO(AvaliacaoFisica avaliacaoFisica,
                                  String pressaoArterial,
                                  String pressaoSistolica,
                                  String pressaoDiastolica,
                                  Integer fc) {
        this.peso = avaliacaoFisica.getPeso();
        this.altura = avaliacaoFisica.getAltura();
        this.pressaoArterial = pressaoArterial;
        this.pressaoSistolica = pressaoSistolica;
        this.pressaoDiastolica = pressaoDiastolica;
        this.frequencia = fc;
        this.frequenciaMax = avaliacaoFisica.getFcMaxima();
    }

    public Double getPeso() {
        return peso;
    }

    public void setPeso(Double peso) {
        this.peso = peso;
    }

    public Double getAltura() {
        return altura;
    }

    public void setAltura(Double altura) {
        this.altura = altura;
    }

    public String getPressaoArterial() {
        return pressaoArterial;
    }

    public void setPressaoArterial(String pressaoArterial) {
        this.pressaoArterial = pressaoArterial;
    }

    public String getPressaoDiastolica() {
        return pressaoDiastolica;
    }

    public void setPressaoDiastolica(String pressaoDiastolica) {
        this.pressaoDiastolica = pressaoDiastolica;
    }

    public String getPressaoSistolica() {
        return pressaoSistolica;
    }

    public void setPressaoSistolica(String pressaoSistolica) {
        this.pressaoSistolica = pressaoSistolica;
    }

    public Integer getFrequencia() {
        if (frequencia == null) return 0;
        return frequencia;
    }

    public void setFrequencia(Integer frequencia) {
        this.frequencia = frequencia;
    }

    public Double getFrequenciaMax() {
        return frequenciaMax;
    }

    public void setFrequenciaMax(Double frequenciaMax) {
        this.frequenciaMax = frequenciaMax;
    }
}
