package br.com.pacto.bean.avaliacao;

import br.com.pacto.bean.anamnese.AnamneseTO;
import br.com.pacto.controller.json.aluno.ClienteSinteticoDTO;
import br.com.pacto.controller.json.avaliacao.AvaliadorDTO;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.List;
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AvaliacaoFisicaDTO {

    private Integer id;
    private Long dataAvaliacao;
    private Long dataProxima;
    private AnamneseTO anamneseSelecionada;
    private List<String> objetivos;
    private AvaliacaoAlunoBIDTO alunoBI;
    private List<String> telefones;
    private List<String> emails;
    private List<AnamnesePerguntaRespostaDTO> anamneseRespostas;
    private List<AnamnesePerguntaRespostaDTO> parQRespostas;
    private AvaliacaoPesoAlturaDTO pesoAltura;
    private AvaliacaoDobrasDTO dobras;
    private AvaliacaoPerimetriaDTO perimetria;
    private AvaliacaoComposicaoDTO composicao;
    private AvaliacaoFlexibilidadeDTO flexibilidade;
    private AvaliacaoPosturaDTO postura;
    private AvaliacaoRMLDTO rml;
    private AvaliacaoVo2DTO vo2;
    private AvaliacaoSomatotipiaDTO somatotipia;
    private AvaliacaoMetaRecomendacoesDTO metaRecomendacoes;
    private Boolean resultadoParq;
    private AvaliadorDTO avaliador;
    private String composicaoNota;
    private String imcNota;
    private String cardioNota;
    private ClienteSinteticoDTO cliente;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Long getDataAvaliacao() {
        return dataAvaliacao;
    }

    public void setDataAvaliacao(Long dataAvaliacao) {
        this.dataAvaliacao = dataAvaliacao;
    }

    public Long getDataProxima() {
        return dataProxima;
    }

    public void setDataProxima(Long dataProxima) {
        this.dataProxima = dataProxima;
    }

    public AnamneseTO getAnamneseSelecionada() {
        return anamneseSelecionada;
    }

    public void setAnamneseSelecionada(AnamneseTO anamneseSelecionada) {
        this.anamneseSelecionada = anamneseSelecionada;
    }

    public List<String> getObjetivos() {
        return objetivos;
    }

    public void setObjetivos(List<String> objetivos) {
        this.objetivos = objetivos;
    }

    public AvaliacaoAlunoBIDTO getAlunoBI() {
        return alunoBI;
    }

    public void setAlunoBI(AvaliacaoAlunoBIDTO alunoBI) {
        this.alunoBI = alunoBI;
    }

    public List<AnamnesePerguntaRespostaDTO> getAnamneseRespostas() {
        return anamneseRespostas;
    }

    public void setAnamneseRespostas(List<AnamnesePerguntaRespostaDTO> anamneseRespostas) {
        this.anamneseRespostas = anamneseRespostas;
    }

    public List<AnamnesePerguntaRespostaDTO> getParQRespostas() {
        return parQRespostas;
    }

    public void setParQRespostas(List<AnamnesePerguntaRespostaDTO> parQRespostas) {
        this.parQRespostas = parQRespostas;
    }

    public AvaliacaoPesoAlturaDTO getPesoAltura() {
        return pesoAltura;
    }

    public void setPesoAltura(AvaliacaoPesoAlturaDTO pesoAltura) {
        this.pesoAltura = pesoAltura;
    }

    public AvaliacaoDobrasDTO getDobras() {
        return dobras;
    }

    public void setDobras(AvaliacaoDobrasDTO dobras) {
        this.dobras = dobras;
    }

    public AvaliacaoPerimetriaDTO getPerimetria() {
        return perimetria;
    }

    public void setPerimetria(AvaliacaoPerimetriaDTO perimetria) {
        this.perimetria = perimetria;
    }

    public AvaliacaoComposicaoDTO getComposicao() {
        return composicao;
    }

    public void setComposicao(AvaliacaoComposicaoDTO composicao) {
        this.composicao = composicao;
    }

    public AvaliacaoFlexibilidadeDTO getFlexibilidade() {
        return flexibilidade;
    }

    public void setFlexibilidade(AvaliacaoFlexibilidadeDTO flexibilidade) {
        this.flexibilidade = flexibilidade;
    }

    public AvaliacaoPosturaDTO getPostura() {
        return postura;
    }

    public void setPostura(AvaliacaoPosturaDTO postura) {
        this.postura = postura;
    }

    public AvaliacaoRMLDTO getRml() {
        return rml;
    }

    public void setRml(AvaliacaoRMLDTO rml) {
        this.rml = rml;
    }

    public AvaliacaoVo2DTO getVo2() {
        return vo2;
    }

    public void setVo2(AvaliacaoVo2DTO vo2) {
        this.vo2 = vo2;
    }

    public AvaliacaoSomatotipiaDTO getSomatotipia() {
        return somatotipia;
    }

    public void setSomatotipia(AvaliacaoSomatotipiaDTO somatotipia) {
        this.somatotipia = somatotipia;
    }

    public AvaliacaoMetaRecomendacoesDTO getMetaRecomendacoes() {
        return metaRecomendacoes;
    }

    public void setMetaRecomendacoes(AvaliacaoMetaRecomendacoesDTO metaRecomendacoes) {
        this.metaRecomendacoes = metaRecomendacoes;
    }

    public Boolean getResultadoParq() {
        return resultadoParq;
    }

    public void setResultadoParq(Boolean resultadoParq) {
        this.resultadoParq = resultadoParq;
    }

    public List<String> getTelefones() {
        return telefones;
    }

    public void setTelefones(List<String> telefones) {
        this.telefones = telefones;
    }

    public List<String> getEmails() {
        return emails;
    }

    public void setEmails(List<String> emails) {
        this.emails = emails;
    }

    public AvaliadorDTO getAvaliador() {
        return avaliador;
    }

    public void setAvaliador(AvaliadorDTO avaliador) {
        this.avaliador = avaliador;
    }

    public String getComposicaoNota() {return composicaoNota;}

    public void setComposicaoNota(String composicaoNota) {this.composicaoNota = composicaoNota;}

    public String getImcNota() {return imcNota;}

    public void setImcNota(String imcNota) {this.imcNota = imcNota;}

    public String getCardioNota() {
        return cardioNota;
    }

    public void setCardioNota(String cardioNota) {
        this.cardioNota = cardioNota;
    }

    public ClienteSinteticoDTO getCliente() {
        return cliente;
    }

    public void setCliente(ClienteSinteticoDTO cliente) {
        this.cliente = cliente;
    }
}
