package br.com.pacto.bean.avaliacao;

/**
 * Created by <PERSON> on 08/10/2016.
 */
public enum CategoriaPercentualGordura {
    NENHUM(0,"categoria.avaliacao.percentualGordura.nenhum"),
    EXCELENTE(1,"categoria.avaliacao.percentualGordura.excelente"),
    B<PERSON>(2,"categoria.avaliacao.percentualGordura.bom"),
    ACIMA_MEDIA(3,"categoria.avaliacao.percentualGordura.acimaMedia"),
    MEDIA(4,"categoria.avaliacao.percentualGordura.media"),
    ABAIXO_MEDIA(5,"categoria.avaliacao.percentualGordura.abaixoMedia"),
    RUIM(6,"categoria.avaliacao.percentualGordura.ruim"),
    MUITO_RUIM(7,"categoria.avaliacao.percentualGordura.muitoRuim"),
    OBESIDADE(8,"categoria.avaliacao.percentualGordura.obesidade"),
    ALTO(9,"categoria.avaliacao.percentualGordura.alto");

    private int codigo;
    private String descricao;

    CategoriaPercentualGordura(int codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public int getCodigo() {
        return codigo;
    }

    public void setCodigo(int codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}
