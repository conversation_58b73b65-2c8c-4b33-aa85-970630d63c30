package br.com.pacto.bean.avaliacao;

import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class Movimento3DDTO {

    private Integer codigo;
    private Integer item;
    protected Integer movimento;
    private Integer esquerda = 0;
    private Integer direita = 0;

    public Movimento3DDTO() { }

    public Movimento3DDTO(Integer codigo, Integer item, Integer movimento, Integer esquerda, Integer direita) {
        this.codigo = codigo;
        this.item = item;
        this.movimento = movimento;
        this.esquerda = esquerda;
        this.direita = direita;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getItem() {
        return item;
    }

    public void setItem(Integer codItem) {
        this.item = codItem;
    }

    public Integer getMovimento() {
        return movimento;
    }

    public void setMovimento(Integer movimento) {
        this.movimento = movimento;
    }

    public Integer getEsquerda() {
        return esquerda;
    }

    public void setEsquerda(Integer esquerda) {
        this.esquerda = esquerda;
    }

    public Integer getDireita() {
        return direita;
    }

    public void setDireita(Integer direita) {
        this.direita = direita;
    }

}
