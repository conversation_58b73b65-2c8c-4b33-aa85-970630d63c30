package br.com.pacto.bean.avaliacao;

public enum R<PERSON><PERSON>ha {

    CAT_15_19("15 - 19"),
    CAT_20_29("20 - 29"),
    CAT_30_39("30 - 39"),
    CAT_40_49("40 - 49"),
    CAT_50_59("50 - 59"),
    CAT_60_69("60+");

    private String faixaIdade;

    R<PERSON>inha(String faixaIdade) {
        this.faixaIdade = faixaIdade;
    }

    public static R<PERSON>inha obterPorIdade(Integer idade ){
        if(idade == null){
            return null;
        }
        if(idade <= 19){
            return CAT_15_19;
        }
        if(idade > 19 && idade <= 29){
            return CAT_20_29;
        }
        if(idade > 29 && idade <= 39){
            return CAT_30_39;
        }
        if(idade > 39 && idade <= 49){
            return CAT_40_49;
        }
        if(idade > 49 && idade <= 59){
            return CAT_50_59;
        }
        if(idade > 59){
            return CAT_60_69;
        }

        return null;
    }

    public String getFaixaIdade() {
        return faixaIdade;
    }
}
