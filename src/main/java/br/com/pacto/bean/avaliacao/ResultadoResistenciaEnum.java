package br.com.pacto.bean.avaliacao;

import br.com.pacto.util.enumeradores.PaletaCoresEnum;

/**
 * Created by Joao Alcides on 01/05/2017.
 */
public enum ResultadoResistenciaEnum {
    EXCELENTE(PaletaCoresEnum.VERDE_J, CategoriaColuna.EXCELENTE),
    ACIMA_MEDIA(PaletaCoresEnum.VERDE_LIMAO_E, CategoriaColuna.ACIMA_MEDIA),
    NA_MEDIA(PaletaCoresEnum.LARANJA_E, CategoriaColuna.MEDIA),
    ABAIXO_MEDIA(PaletaCoresEnum.ROXO_E, CategoriaColuna.ABAIXO_MEDIA),
    FRACO(PaletaCoresEnum.VERMELHO_J, CategoriaColuna.FRACO);

    ResultadoResistenciaEnum(PaletaCoresEnum cor, CategoriaColuna coluna) {
        this.cor = cor;
        this.coluna = coluna;
    }

    private PaletaCoresEnum cor;
    private CategoriaColuna coluna;

    public PaletaCoresEnum getCor() {
        return cor;
    }

    public void setCor(PaletaCoresEnum cor) {
        this.cor = cor;
    }

    public String getName(){
        return name();
    }

    public CategoriaColuna getColuna() {
        return coluna;
    }
}
