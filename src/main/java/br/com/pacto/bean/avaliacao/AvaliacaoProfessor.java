/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.avaliacao;

import br.com.pacto.bean.professor.ProfessorSintetico;
import br.com.pacto.bean.usuario.Usuario;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.ManyToOne;
import java.io.Serializable;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
@Entity
public class AvaliacaoProfessor implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private Date dataRegistro;
    private Date dataUpdate;
    @ManyToOne
    private Usuario usuario;
    @ManyToOne
    private ProfessorSintetico professorSintetico;
    private Integer notaAvaliada;
    private Integer empresa;
    private String comentario;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public Date getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(Date dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public Date getDataUpdate() {
        return dataUpdate;
    }

    public void setDataUpdate(Date dataUpdate) {
        this.dataUpdate = dataUpdate;
    }

    public Usuario getUsuario() {
        return usuario;
    }

    public void setUsuario(Usuario usuario) {
        this.usuario = usuario;
    }

    public ProfessorSintetico getProfessorSintetico() {
        return professorSintetico;
    }

    public void setProfessorSintetico(ProfessorSintetico professorSintetico) {
        this.professorSintetico = professorSintetico;
    }

    public Integer getNotaAvaliada() {
        return notaAvaliada;
    }

    public void setNotaAvaliada(Integer notaAvaliada) {
        this.notaAvaliada = notaAvaliada;
    }

    public String getComentario() {
        return comentario;
    }

    public void setComentario(String comentario) {
        this.comentario = comentario;
    }
}
