package br.com.pacto.bean.avaliacao;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.io.Serializable;

/**
 * <AUTHOR> 22/01/2019
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AvaliacaoPosturaDTOUpdate implements Serializable {

    private String frenteImageId;
    private String direitaImageId;
    private String esquerdaImageId;
    private String costasImageId;

    // Caso uma imagem for enviada nos campos correspondentes,
    // a mesma deve substituir a anterior.
    private String frenteImageUpload;
    private String direitaImageUpload;
    private String esquerdaImageUpload;
    private String costasImageUpload;
    private AnamnesePosturaLateralDTO visaoLateral;
    private AnamnesePosturalPosteriorDTO visaoPosterior;
    private AnamnesePosturalAnteriorDTO visaoAnterior;
    private AnamnesePosturalAssimetriasDTO assimetrias;
    private String observacao;

    public String getFrenteImageId() {
        return frenteImageId;
    }

    public void setFrenteImageId(String frenteImageId) {
        this.frenteImageId = frenteImageId;
    }

    public String getDireitaImageId() {
        return direitaImageId;
    }

    public void setDireitaImageId(String direitaImageId) {
        this.direitaImageId = direitaImageId;
    }

    public String getEsquerdaImageId() {
        return esquerdaImageId;
    }

    public void setEsquerdaImageId(String esquerdaImageId) {
        this.esquerdaImageId = esquerdaImageId;
    }

    public String getCostasImageId() {
        return costasImageId;
    }

    public void setCostasImageId(String costasImageId) {
        this.costasImageId = costasImageId;
    }

    public String getFrenteImageUpload() {
        return frenteImageUpload;
    }

    public void setFrenteImageUpload(String frenteImageUpload) {
        this.frenteImageUpload = frenteImageUpload;
    }

    public String getDireitaImageUpload() {
        return direitaImageUpload;
    }

    public void setDireitaImageUpload(String direitaImageUpload) {
        this.direitaImageUpload = direitaImageUpload;
    }

    public String getEsquerdaImageUpload() {
        return esquerdaImageUpload;
    }

    public void setEsquerdaImageUpload(String esquerdaImageUpload) {
        this.esquerdaImageUpload = esquerdaImageUpload;
    }

    public String getCostasImageUpload() {
        return costasImageUpload;
    }

    public void setCostasImageUpload(String costasImageUpload) {
        this.costasImageUpload = costasImageUpload;
    }

    public AnamnesePosturaLateralDTO getVisaoLateral() {
        return visaoLateral;
    }

    public void setVisaoLateral(AnamnesePosturaLateralDTO visaoLateral) {
        this.visaoLateral = visaoLateral;
    }

    public AnamnesePosturalPosteriorDTO getVisaoPosterior() {
        return visaoPosterior;
    }

    public void setVisaoPosterior(AnamnesePosturalPosteriorDTO visaoPosterior) {
        this.visaoPosterior = visaoPosterior;
    }

    public AnamnesePosturalAnteriorDTO getVisaoAnterior() {
        return visaoAnterior;
    }

    public void setVisaoAnterior(AnamnesePosturalAnteriorDTO visaoAnterior) {
        this.visaoAnterior = visaoAnterior;
    }

    public AnamnesePosturalAssimetriasDTO getAssimetrias() {
        return assimetrias;
    }

    public void setAssimetrias(AnamnesePosturalAssimetriasDTO assimetrias) {
        this.assimetrias = assimetrias;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }
}
