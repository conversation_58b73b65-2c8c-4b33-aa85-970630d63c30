package br.com.pacto.bean.avaliacao;

import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class AvaliacaoSomatotipiaDTO {

    private Double endomorfia;
    private Double mesomorfia;
    private Double ectomorfia;
    private Double dobraTriceps;
    private Double dobraSupraEspinhal;
    private Double dobraSubescapular;
    private Double dobraPanturrilha;
    private Double perimetroPanturrilhaDireita;
    private Double perimetroBracoContraidoDireito;
    private Double diametroPunho;
    private Double diametroJoelho;
    private Double diametroCotovelo;
    private Double diametroTornozelo;

    public AvaliacaoSomatotipiaDTO() {
    }

    public AvaliacaoSomatotipiaDTO(AvaliacaoFisica avaliacaoFisica, PesoOsseo po) {
        this.endomorfia = avaliacaoFisica.getEndomorfia();
        this.mesomorfia = avaliacaoFisica.getMesomorfia();
        this.ectomorfia = avaliacaoFisica.getEctomorfia();
        this.dobraTriceps = avaliacaoFisica.getTriceps();
        this.dobraSupraEspinhal = avaliacaoFisica.getSupraEspinhal();
        this.dobraSubescapular = avaliacaoFisica.getSubescapular();
        this.dobraPanturrilha = avaliacaoFisica.getPanturrilha();
        this.perimetroPanturrilhaDireita = avaliacaoFisica.getPanturrilhaDir();
        this.perimetroBracoContraidoDireito = avaliacaoFisica.getBracoContraidoDir();
        this.diametroPunho = po.getDiametroPunho();
        this.diametroJoelho = po.getDiametroFemur();
        this.diametroCotovelo = po.getDiametroCotovelo();
        this.diametroTornozelo = po.getDiametroTornozelo();
    }

    public Double getEndomorfia() {
        return endomorfia;
    }

    public void setEndomorfia(Double endomorfia) {
        this.endomorfia = endomorfia;
    }

    public Double getMesomorfia() {
        return mesomorfia;
    }

    public void setMesomorfia(Double mesomorfia) {
        this.mesomorfia = mesomorfia;
    }

    public Double getEctomorfia() {
        return ectomorfia;
    }

    public void setEctomorfia(Double ectomorfia) {
        this.ectomorfia = ectomorfia;
    }

    public Double getDobraTriceps() {
        return dobraTriceps;
    }

    public void setDobraTriceps(Double dobraTriceps) {
        this.dobraTriceps = dobraTriceps;
    }

    public Double getDobraSupraEspinhal() {
        return dobraSupraEspinhal;
    }

    public void setDobraSupraEspinhal(Double dobraSupraEspinhal) {
        this.dobraSupraEspinhal = dobraSupraEspinhal;
    }

    public Double getDobraSubescapular() {
        return dobraSubescapular;
    }

    public void setDobraSubescapular(Double dobraSubescapular) {
        this.dobraSubescapular = dobraSubescapular;
    }

    public Double getDobraPanturrilha() {
        return dobraPanturrilha;
    }

    public void setDobraPanturrilha(Double dobraPanturrilha) {
        this.dobraPanturrilha = dobraPanturrilha;
    }

    public Double getPerimetroPanturrilhaDireita() {
        return perimetroPanturrilhaDireita;
    }

    public void setPerimetroPanturrilhaDireita(Double perimetroPanturrilhaDireita) {
        this.perimetroPanturrilhaDireita = perimetroPanturrilhaDireita;
    }

    public Double getPerimetroBracoContraidoDireito() {
        return perimetroBracoContraidoDireito;
    }

    public void setPerimetroBracoContraidoDireito(Double perimetroBracoContraidoDireito) {
        this.perimetroBracoContraidoDireito = perimetroBracoContraidoDireito;
    }

    public Double getDiametroPunho() {
        return diametroPunho;
    }

    public void setDiametroPunho(Double diametroPunho) {
        this.diametroPunho = diametroPunho;
    }

    public Double getDiametroJoelho() {
        return diametroJoelho;
    }

    public void setDiametroJoelho(Double diametroJoelho) {
        this.diametroJoelho = diametroJoelho;
    }

    public Double getDiametroCotovelo() {
        return diametroCotovelo;
    }

    public void setDiametroCotovelo(Double diametroCotovelo) {
        this.diametroCotovelo = diametroCotovelo;
    }

    public Double getDiametroTornozelo() {
        return diametroTornozelo;
    }

    public void setDiametroTornozelo(Double diametroTornozelo) {
        this.diametroTornozelo = diametroTornozelo;
    }
}
