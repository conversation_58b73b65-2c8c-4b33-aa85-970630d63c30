package br.com.pacto.bean.avaliacao;

import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class AvaliacaoMetaRecomendacoesDTO {

    private Double percentualGorduraAnterior;
    private Double percentualGorduraProxima;
    private String observacoesAvaliador;

    public AvaliacaoMetaRecomendacoesDTO() {
    }

    public AvaliacaoMetaRecomendacoesDTO(AvaliacaoFisica avaliacaoFisica) {
        this.percentualGorduraAnterior = avaliacaoFisica.getMetaPercentualGorduraAnterior();
        this.percentualGorduraProxima = avaliacaoFisica.getMetaPercentualGordura();
        this.observacoesAvaliador = avaliacaoFisica.getRecomendacoes();
    }

    public Double getPercentualGorduraAnterior() {
        return percentualGorduraAnterior;
    }

    public void setPercentualGorduraAnterior(Double percentualGorduraAnterior) {
        this.percentualGorduraAnterior = percentualGorduraAnterior;
    }

    public Double getPercentualGorduraProxima() {
        return percentualGorduraProxima;
    }

    public void setPercentualGorduraProxima(Double percentualGorduraProxima) {
        this.percentualGorduraProxima = percentualGorduraProxima;
    }

    public String getObservacoesAvaliador() {
        return observacoesAvaliador;
    }

    public void setObservacoesAvaliador(String observacoesAvaliador) {
        this.observacoesAvaliador = observacoesAvaliador;
    }
}
