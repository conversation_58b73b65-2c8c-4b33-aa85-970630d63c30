package br.com.pacto.bean.avaliacao;

/**
 * Created by <PERSON><PERSON> on 06/03/2019.
 */
public enum DobrasEnum {

    ABDOMINAL("abdominal", "cadastros.aluno.abdominal", "obrigatorio abdominal", "tudo lbl abdominal"),
    SUPRA_ILIACA("supraIliaca", "cadastros.aluno.suprailiaca", "obrigatorio suprailiaca direita", "tudo lbl suprailiaca"),
    PEITORAL("peitoral", "cadastros.aluno.peitoral", "obrigatorio torax", "tudo lbl torax"),
    TRICEPS("triceps", "cadastros.aluno.triceps", "obrigatorio triceps direita", "tudo lbl triceps"),
    COXA_MEDIAL("coxaMedial", "cadastros.aluno.coxa_medial", "obrigatorio coxa", "tudo lbl coxa"),
    BICEPS("biceps", "cadastros.aluno.biceps", "obrigatorio biceps direita", "tudo lbl biceps"),
    SUBESCAPULAR("subescapular", "cadastros.aluno.subescapular", "obrigatorio subescapular", "tudo lbl subescapular"),
    AXILARMEDIA("axilarMedia", "cadastros.aluno.axilarMedia", "obrigatorio axilarMedia direita", "tudo lbl axilarMedia"),
    SUPRA_ESPINHAL("supraEspinhal", "cadastros.aluno.supraespinhal", "obrigatorio supraespinhal", "tudo lbl supraespinhal"),
    PANTURRILHA("panturrilha", "cadastros.aluno.panturrilha", "obrigatorio panturrilha direita", "tudo lbl panturrilha");

    private String field;
    private String inputCss;
    private String label;
    private String obrigatorioCss;
    private String outputCss;

    DobrasEnum(String field, String label, String obrigatorioCss, String outputCss) {
        this.field = field;
        this.inputCss = "id-avaliacaoFisica-"+field;
        this.label = label;
        this.obrigatorioCss = obrigatorioCss;
        this.outputCss = outputCss;
    }

    public String getField() {
        return field;
    }

    public static DobrasEnum getFromOrdinal(int o){
        for(DobrasEnum p : values()){
            if(p.ordinal() == o){
                return p;
            }
        }
        return null;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getInputCss() {
        return inputCss;
    }

    public void setInputCss(String inputCss) {
        this.inputCss = inputCss;
    }

    public String getOutputCss() {
        return outputCss;
    }

    public void setOutputCss(String outputCss) {
        this.outputCss = outputCss;
    }

    public String getObrigatorioCss() {
        return obrigatorioCss;
    }

    public void setObrigatorioCss(String obrigatorioCss) {
        this.obrigatorioCss = obrigatorioCss;
    }
}
