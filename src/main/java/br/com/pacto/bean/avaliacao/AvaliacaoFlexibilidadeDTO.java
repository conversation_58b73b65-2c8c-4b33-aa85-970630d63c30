package br.com.pacto.bean.avaliacao;

import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class AvaliacaoFlexibilidadeDTO {

    private Integer alcanceMaximo;
    private Integer mobilidadeTornozeloEsquerdo;
    private Integer mobilidadeTornozeloDireito;
    private String observacao;
    private ClassificacaoEnum indicadorColuna;
    private ClassificacaoEnum mobilidadeOmbroEsquerdo;
    private ClassificacaoEnum mobilidadeOmbroDireito;
    private ClassificacaoEnum mobilidadeJoelhoEsquerdo;
    private ClassificacaoEnum mobilidadeJoelhoDireito;
    private ClassificacaoEnum mobilidadeQuadrilEsquerdo;
    private ClassificacaoEnum mobilidadeQuadrilDireito;
    private String observacaoOmbro;
    private String observacaoQuadril;
    private String observacaoTornozelo;
    private String observacaoJoelho;

    public AvaliacaoFlexibilidadeDTO(Flexibilidade flexibilidade) {
       this.alcanceMaximo = flexibilidade.getAlcance();
        this.observacao = flexibilidade.getObservacao();
        this.indicadorColuna = flexibilidade.getClassificacao();
        this.mobilidadeOmbroEsquerdo = flexibilidade.getMobilidadeOmbroEsquerdo();
        this.mobilidadeOmbroDireito = flexibilidade.getMobilidadeOmbroDireito();
        this.mobilidadeQuadrilEsquerdo = flexibilidade.getMobilidadeQuadrilEsquerdo();
        this.mobilidadeQuadrilDireito = flexibilidade.getMobilidadeQuadrilDireito();
        this.mobilidadeJoelhoEsquerdo = flexibilidade.getMobilidadeJoelhoEsquerdo();
        this.mobilidadeJoelhoDireito = flexibilidade.getMobilidadeJoelhoDireito();
        this.mobilidadeTornozeloEsquerdo = flexibilidade.getMobilidadeTornozeloEsquerdo();
        this.mobilidadeTornozeloDireito = flexibilidade.getMobilidadeTornozeloDireito();
        this.observacaoJoelho = flexibilidade.getObservacaoJoelho();
        this.observacaoTornozelo = flexibilidade.getObservacaoTornozelo();
        this.observacaoOmbro = flexibilidade.getObservacaoOmbro();
        this.observacaoQuadril = flexibilidade.getObservacaoQuadril();
    }

    public Integer getAlcanceMaximo() {
        return alcanceMaximo;
    }

    public void setAlcanceMaximo(Integer alcanceMaximo) {
        this.alcanceMaximo = alcanceMaximo;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public ClassificacaoEnum getIndicadorColuna() {
        return indicadorColuna;
    }

    public void setIndicadorColuna(ClassificacaoEnum indicadorColuna) {
        this.indicadorColuna = indicadorColuna;
    }

    public ClassificacaoEnum getMobilidadeOmbroEsquerdo() {
        return mobilidadeOmbroEsquerdo;
    }

    public void setMobilidadeOmbroEsquerdo(ClassificacaoEnum mobilidadeOmbroEsquerdo) {
        this.mobilidadeOmbroEsquerdo = mobilidadeOmbroEsquerdo;
    }

    public ClassificacaoEnum getMobilidadeOmbroDireito() {
        return mobilidadeOmbroDireito;
    }

    public void setMobilidadeOmbroDireito(ClassificacaoEnum mobilidadeOmbroDireito) {
        this.mobilidadeOmbroDireito = mobilidadeOmbroDireito;
    }

    public ClassificacaoEnum getMobilidadeJoelhoEsquerdo() {
        return mobilidadeJoelhoEsquerdo;
    }

    public void setMobilidadeJoelhoEsquerdo(ClassificacaoEnum mobilidadeJoelhoEsquerdo) {
        this.mobilidadeJoelhoEsquerdo = mobilidadeJoelhoEsquerdo;
    }

    public ClassificacaoEnum getMobilidadeJoelhoDireito() {
        return mobilidadeJoelhoDireito;
    }

    public void setMobilidadeJoelhoDireito(ClassificacaoEnum mobilidadeJoelhoDireito) {
        this.mobilidadeJoelhoDireito = mobilidadeJoelhoDireito;
    }

    public Integer getMobilidadeTornozeloEsquerdo() {
        return mobilidadeTornozeloEsquerdo;
    }

    public void setMobilidadeTornozeloEsquerdo(Integer mobilidadeTornozeloEsquerdo) {
        this.mobilidadeTornozeloEsquerdo = mobilidadeTornozeloEsquerdo;
    }

    public Integer getMobilidadeTornozeloDireito() {
        return mobilidadeTornozeloDireito;
    }

    public void setMobilidadeTornozeloDireito(Integer mobilidadeTornozeloDireito) {
        this.mobilidadeTornozeloDireito = mobilidadeTornozeloDireito;
    }


    public ClassificacaoEnum getMobilidadeQuadrilEsquerdo() {
        return mobilidadeQuadrilEsquerdo;
    }

    public void setMobilidadeQuadrilEsquerdo(ClassificacaoEnum mobilidadeQuadrilEsquerdo) {
        this.mobilidadeQuadrilEsquerdo = mobilidadeQuadrilEsquerdo;
    }

    public ClassificacaoEnum getMobilidadeQuadrilDireito() {
        return mobilidadeQuadrilDireito;
    }

    public void setMobilidadeQuadrilDireito(ClassificacaoEnum mobilidadeQuadrilDireito) {
        this.mobilidadeQuadrilDireito = mobilidadeQuadrilDireito;
    }

    public String getObservacaoQuadril() {
        return observacaoQuadril;
    }

    public void setObservacaoQuadril(String observacaoQuadril) {
        this.observacaoQuadril = observacaoQuadril;
    }

    public String getObservacaoTornozelo() {
        return observacaoTornozelo;
    }

    public void setObservacaoTornozelo(String observacaoTornozelo) {
        this.observacaoTornozelo = observacaoTornozelo;
    }

    public String getObservacaoJoelho() {
        return observacaoJoelho;
    }

    public void setObservacaoJoelho(String observacaoJoelho) {
        this.observacaoJoelho = observacaoJoelho;
    }

    public String getObservacaoOmbro() {
        return observacaoOmbro;
    }

    public void setObservacaoOmbro(String observacaoOmbro) {
        this.observacaoOmbro = observacaoOmbro;
    }

}
