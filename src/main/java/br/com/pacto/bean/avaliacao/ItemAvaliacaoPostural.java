/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.avaliacao;

import javax.persistence.*;


/**
 *
 * <AUTHOR>
 */
@Entity
@Table
public class ItemAvaliacaoPostural {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    @Enumerated(EnumType.ORDINAL)
    private ItemPosturaEnum item;
    private Boolean selecionado = Boolean.FALSE;
    @ManyToOne
    private AvaliacaoPostural avaliacaoPostural;
    @Transient
    private String descricao;

    public ItemAvaliacaoPostural() {
    }

    public ItemAvaliacaoPostural(ItemPosturaEnum item, Boolean selecionado, AvaliacaoPostural avaliacaoPostural, String descricao) {
        this.item = item;
        this.selecionado = selecionado;
        this.avaliacaoPostural = avaliacaoPostural;
        this.descricao = descricao;
    }

    public ItemAvaliacaoPostural(ItemPosturaEnum item, String descricao) {
        this.item = item;
        this.descricao = descricao;
    }

    public ItemAvaliacaoPostural(ItemPosturaEnum item, Boolean selecionado) {
        this.item = item;
        this.selecionado = selecionado;
    }

    public Boolean getSelecionado() {
        return selecionado;
    }

    public void setSelecionado(Boolean selecionado) {
        this.selecionado = selecionado;
    }
    
    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public ItemPosturaEnum getItem() {
        return item;
    }

    public void setItem(ItemPosturaEnum item) {
        this.item = item;
    }

    public AvaliacaoPostural getAvaliacaoPostural() {
        return avaliacaoPostural;
    }

    public void setAvaliacaoPostural(AvaliacaoPostural avaliacaoPostural) {
        this.avaliacaoPostural = avaliacaoPostural;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}
