package br.com.pacto.bean.avaliacao;

import br.com.pacto.bean.anamnese.Movimento3D;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class AvaliacaoIntegradaDTOUptade {

    private Integer id;
    private Long dataAvaliacao;
    private Integer anamneseSelecionadaId;
    private List<AnamnesePerguntaRespostaDTOUpdate> anamneseRespostas;
    private List<Movimento3D> mobilidade;
    private List<Movimento3D> estabilidade;
    private Integer somaMobilidadeDir;
    private Integer somaEstabilidadeDir;
    private Double mediaMobilidadeDir;
    private Double mediaEstabilidadeDir;
    private Integer somaMobilidadeEsq;
    private Integer somaEstabilidadeEsq;
    private Double mediaMobilidadeEsq;
    private Double mediaEstabilidadeEsq;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Long getDataAvaliacao() {
        return dataAvaliacao;
    }

    public void setDataAvaliacao(Long dataAvaliacao) {
        this.dataAvaliacao = dataAvaliacao;
    }

    public Integer getAnamneseSelecionadaId() {
        return anamneseSelecionadaId;
    }

    public void setAnamneseSelecionadaId(Integer anamneseSelecionadaId) {
        this.anamneseSelecionadaId = anamneseSelecionadaId;
    }

    public List<AnamnesePerguntaRespostaDTOUpdate> getAnamneseRespostas() {
        return anamneseRespostas;
    }

    public void setAnamneseRespostas(List<AnamnesePerguntaRespostaDTOUpdate> anamneseRespostas) {
        this.anamneseRespostas = anamneseRespostas;
    }

    public List<Movimento3D> getMobilidade() {
        return mobilidade;
    }

    public void setMobilidade(List<Movimento3D> mobilidade) {
        this.mobilidade = mobilidade;
    }

    public List<Movimento3D> getEstabilidade() {
        return estabilidade;
    }

    public void setEstabilidade(List<Movimento3D> estabilidade) {
        this.estabilidade = estabilidade;
    }

    public Integer getSomaMobilidadeDir() {
        return somaMobilidadeDir;
    }

    public void setSomaMobilidadeDir(Integer somaMobilidadeDir) {
        this.somaMobilidadeDir = somaMobilidadeDir;
    }

    public Integer getSomaEstabilidadeDir() {
        return somaEstabilidadeDir;
    }

    public void setSomaEstabilidadeDir(Integer somaEstabilidadeDir) {
        this.somaEstabilidadeDir = somaEstabilidadeDir;
    }

    public Double getMediaMobilidadeDir() {
        return mediaMobilidadeDir;
    }

    public void setMediaMobilidadeDir(Double mediaMobilidadeDir) {
        this.mediaMobilidadeDir = mediaMobilidadeDir;
    }

    public Double getMediaEstabilidadeDir() {
        return mediaEstabilidadeDir;
    }

    public void setMediaEstabilidadeDir(Double mediaEstabilidadeDir) {
        this.mediaEstabilidadeDir = mediaEstabilidadeDir;
    }

    public Integer getSomaMobilidadeEsq() {
        return somaMobilidadeEsq;
    }

    public void setSomaMobilidadeEsq(Integer somaMobilidadeEsq) {
        this.somaMobilidadeEsq = somaMobilidadeEsq;
    }

    public Integer getSomaEstabilidadeEsq() {
        return somaEstabilidadeEsq;
    }

    public void setSomaEstabilidadeEsq(Integer somaEstabilidadeEsq) {
        this.somaEstabilidadeEsq = somaEstabilidadeEsq;
    }

    public Double getMediaMobilidadeEsq() {
        return mediaMobilidadeEsq;
    }

    public void setMediaMobilidadeEsq(Double mediaMobilidadeEsq) {
        this.mediaMobilidadeEsq = mediaMobilidadeEsq;
    }

    public Double getMediaEstabilidadeEsq() {
        return mediaEstabilidadeEsq;
    }

    public void setMediaEstabilidadeEsq(Double mediaEstabilidadeEsq) {
        this.mediaEstabilidadeEsq = mediaEstabilidadeEsq;
    }

}
