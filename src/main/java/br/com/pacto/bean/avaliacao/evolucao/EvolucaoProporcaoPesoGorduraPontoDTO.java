package br.com.pacto.bean.avaliacao.evolucao;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.io.Serializable;

/**
 * <AUTHOR> Siqueira 30/01/2019
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EvolucaoProporcaoPesoGorduraPontoDTO implements Serializable {

    private Long data;
    private Double peso;
    private Double massaGorda;

    public EvolucaoProporcaoPesoGorduraPontoDTO(Long data, Double peso, Double massaGorda) {
        this.data = data;
        this.peso = peso;
        this.massaGorda = massaGorda;
    }

    public Long getData() {
        return data;
    }

    public void setData(Long data) {
        this.data = data;
    }

    public Double getPeso() {
        return peso;
    }

    public void setPeso(Double peso) {
        this.peso = peso;
    }

    public Double getMassaGorda() {
        return massaGorda;
    }

    public void setMassaGorda(Double massaGorda) {
        this.massaGorda = massaGorda;
    }
}
