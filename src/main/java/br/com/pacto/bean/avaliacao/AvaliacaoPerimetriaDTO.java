package br.com.pacto.bean.avaliacao;

import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class AvaliacaoPerimetriaDTO {

    private Double pescoco;
    private Double ombro;
    private Double toraxBustoRelaxado;
    private Double bracoRelaxadoEsq;
    private Double bracoRelaxadoDir;
    private Double bracoContraidoEsq;
    private Double bracoContraidoDir;
    private Double antebracoEsq;
    private Double antebracoDir;
    private Double cintura;
    private Double circunferenciaAbdominal;
    private Double quadril;
    private Double coxaProximalEsq;
    private Double coxaProximalDir;
    private Double coxaMediaEsq;
    private Double coxaMediaDir;
    private Double coxaDistalEsq;
    private Double coxaDistalDir;
    private Double panturrilhaEsq;
    private Double panturrilhaDir;
    private Double diametroPunho;
    private Double diametroJoelho;
    private Double diametroCotovelo;
    private Double diametroTornozelo;

    public AvaliacaoPerimetriaDTO() {
    }

    public AvaliacaoPerimetriaDTO(AvaliacaoFisica avaliacaoFisica, PesoOsseo pesoOsseo) {
        this.pescoco = avaliacaoFisica.getPescoco();
        this.ombro = avaliacaoFisica.getOmbro();
        this.toraxBustoRelaxado = avaliacaoFisica.getToraxBusto();
        this.bracoRelaxadoEsq = avaliacaoFisica.getBracoRelaxadoEsq();
        this.bracoRelaxadoDir = avaliacaoFisica.getBracoRelaxadoDir();
        this.bracoContraidoEsq = avaliacaoFisica.getBracoContraidoEsq();
        this.bracoContraidoDir = avaliacaoFisica.getBracoContraidoDir();
        this.antebracoEsq = avaliacaoFisica.getAntebracoEsq();
        this.antebracoDir = avaliacaoFisica.getAntebracoDir();
        this.cintura = avaliacaoFisica.getCintura();
        this.circunferenciaAbdominal = avaliacaoFisica.getCircunferenciaAbdominal();
        this.quadril = avaliacaoFisica.getQuadril();
        this.coxaProximalEsq = avaliacaoFisica.getCoxaProximalEsq();
        this.coxaProximalDir = avaliacaoFisica.getCoxaProximalDir();
        this.coxaMediaEsq = avaliacaoFisica.getCoxaMediaEsq();
        this.coxaMediaDir = avaliacaoFisica.getCoxaMediaDir();
        this.coxaDistalEsq = avaliacaoFisica.getCoxaDistalEsq();
        this.coxaDistalDir = avaliacaoFisica.getCoxaDistalDir();
        this.panturrilhaEsq = avaliacaoFisica.getPanturrilhaEsq();
        this.panturrilhaDir = avaliacaoFisica.getPanturrilhaDir();
        this.diametroPunho = pesoOsseo.getDiametroPunho();
        this.diametroJoelho = pesoOsseo.getDiametroFemur();
        this.diametroCotovelo = pesoOsseo.getDiametroCotovelo();
        this.diametroTornozelo = pesoOsseo.getDiametroTornozelo();
    }

    public Double getPescoco() {
        return pescoco;
    }

    public void setPescoco(Double pescoco) {
        this.pescoco = pescoco;
    }

    public Double getOmbro() {
        return ombro;
    }

    public void setOmbro(Double ombro) {
        this.ombro = ombro;
    }

    public Double getToraxBustoRelaxado() {
        return toraxBustoRelaxado;
    }

    public void setToraxBustoRelaxado(Double toraxBustoRelaxado) {
        this.toraxBustoRelaxado = toraxBustoRelaxado;
    }

    public Double getBracoRelaxadoEsq() {
        return bracoRelaxadoEsq;
    }

    public void setBracoRelaxadoEsq(Double bracoRelaxadoEsq) {
        this.bracoRelaxadoEsq = bracoRelaxadoEsq;
    }

    public Double getBracoRelaxadoDir() {
        return bracoRelaxadoDir;
    }

    public void setBracoRelaxadoDir(Double bracoRelaxadoDir) {
        this.bracoRelaxadoDir = bracoRelaxadoDir;
    }

    public Double getBracoContraidoEsq() {
        return bracoContraidoEsq;
    }

    public void setBracoContraidoEsq(Double bracoContraidoEsq) {
        this.bracoContraidoEsq = bracoContraidoEsq;
    }

    public Double getBracoContraidoDir() {
        return bracoContraidoDir;
    }

    public void setBracoContraidoDir(Double bracoContraidoDir) {
        this.bracoContraidoDir = bracoContraidoDir;
    }

    public Double getAntebracoEsq() {
        return antebracoEsq;
    }

    public void setAntebracoEsq(Double antebracoEsq) {
        this.antebracoEsq = antebracoEsq;
    }

    public Double getAntebracoDir() {
        return antebracoDir;
    }

    public void setAntebracoDir(Double antebracoDir) {
        this.antebracoDir = antebracoDir;
    }

    public Double getCintura() {
        return cintura;
    }

    public void setCintura(Double cintura) {
        this.cintura = cintura;
    }

    public Double getCircunferenciaAbdominal() {
        return circunferenciaAbdominal;
    }

    public void setCircunferenciaAbdominal(Double circunferenciaAbdominal) {
        this.circunferenciaAbdominal = circunferenciaAbdominal;
    }

    public Double getQuadril() {
        return quadril;
    }

    public void setQuadril(Double quadril) {
        this.quadril = quadril;
    }

    public Double getCoxaProximalEsq() {
        return coxaProximalEsq;
    }

    public void setCoxaProximalEsq(Double coxaProximalEsq) {
        this.coxaProximalEsq = coxaProximalEsq;
    }

    public Double getCoxaProximalDir() {
        return coxaProximalDir;
    }

    public void setCoxaProximalDir(Double coxaProximalDir) {
        this.coxaProximalDir = coxaProximalDir;
    }

    public Double getCoxaMediaEsq() {
        return coxaMediaEsq;
    }

    public void setCoxaMediaEsq(Double coxaMediaEsq) {
        this.coxaMediaEsq = coxaMediaEsq;
    }

    public Double getCoxaMediaDir() {
        return coxaMediaDir;
    }

    public void setCoxaMediaDir(Double coxaMediaDir) {
        this.coxaMediaDir = coxaMediaDir;
    }

    public Double getCoxaDistalEsq() {
        return coxaDistalEsq;
    }

    public void setCoxaDistalEsq(Double coxaDistalEsq) {
        this.coxaDistalEsq = coxaDistalEsq;
    }

    public Double getCoxaDistalDir() {
        return coxaDistalDir;
    }

    public void setCoxaDistalDir(Double coxaDistalDir) {
        this.coxaDistalDir = coxaDistalDir;
    }

    public Double getPanturrilhaEsq() {
        return panturrilhaEsq;
    }

    public void setPanturrilhaEsq(Double panturrilhaEsq) {
        this.panturrilhaEsq = panturrilhaEsq;
    }

    public Double getPanturrilhaDir() {
        return panturrilhaDir;
    }

    public void setPanturrilhaDir(Double panturrilhaDir) {
        this.panturrilhaDir = panturrilhaDir;
    }

    public Double getDiametroPunho() {
        return diametroPunho;
    }

    public void setDiametroPunho(Double diametroPunho) {
        this.diametroPunho = diametroPunho;
    }

    public Double getDiametroJoelho() {
        return diametroJoelho;
    }

    public void setDiametroJoelho(Double diametroJoelho) {
        this.diametroJoelho = diametroJoelho;
    }

    public Double getDiametroCotovelo() {
        return diametroCotovelo;
    }

    public void setDiametroCotovelo(Double diametroCotovelo) {
        this.diametroCotovelo = diametroCotovelo;
    }

    public Double getDiametroTornozelo() {
        return diametroTornozelo;
    }

    public void setDiametroTornozelo(Double diametroTornozelo) {
        this.diametroTornozelo = diametroTornozelo;
    }
}
