package br.com.pacto.bean.avaliacao;

import br.com.pacto.bean.Agendamento;
import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.enumeradores.ProtocolosAvaliacaoFisicaEnum;
import br.com.pacto.util.impl.UtilReflection;
import com.fasterxml.jackson.annotation.JsonIgnore;
import org.hibernate.annotations.Type;

import javax.persistence.*;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by Rafael on 08/10/2016.
 */
@Entity
@Table
public class AvaliacaoFisica implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    @Enumerated(EnumType.ORDINAL)
    private ProtocolosVo2MaxEnum protocoloVo = ProtocolosVo2MaxEnum.VO_CAMINHADA_CORRIDA_12_MINUTOS;
    @Enumerated(EnumType.ORDINAL)
    private ProtocolosAvaliacaoFisicaEnum protocolo = ProtocolosAvaliacaoFisicaEnum.POLLOCK_3_DOBRAS;
    @ManyToOne
    @JsonIgnore
    private ClienteSintetico cliente;
    private Double peso = 0.0;
    private Double altura = 0.0;
    @Lob
    @Type(type = "org.hibernate.type.StringClobType")
    @Column(columnDefinition = "text", length = 99999)
    private String urlAssinatura;
    @Lob
    @Type(type = "org.hibernate.type.StringClobType")
    @Column(columnDefinition = "text", length = 99999)
    private String logBalanca;
    @Transient
    private String assinatura;
    @OneToOne(mappedBy = "avaliacao", cascade = CascadeType.REMOVE, fetch = FetchType.LAZY)
    private Flexibilidade flexibilidadeEntity;
    @Transient
    private AvaliacaoPostural avaliacaoPosturual;
    //Dobras cutâneas

    private Double abdominal = 0.0;
    private Double peitoral = 0.0;
    private Double coxaMedial = 0.0;
    private Double supraIliaca = 0.0;
    private Double triceps = 0.0;
    private Double totalDobras = 0.0;
    private Double subescapular = 0.0;
    private Double biceps = 0.0;
    private Double axilarMedia = 0.0;
    private Double supraEspinhal = 0.0;
    private Double panturrilha = 0.0;
    private Double fcMaxima = 0.0;
    private Double distancia12 = 0.0;
    private String tempo2400 = "";
    private Double vo2Max12 = 0.0;
    private Double vo2Max2400 = 0.0;
    private Double voMaxAerobico = 0.0;
    private Double fcAstrand4 = 0.0;
    private Double fcAstrand5 = 0.0;
    private Double fcQueens = 0.0;
    private Double cargaAstrand = 0.0;
    private Double vo2Astrand = 0.0;
    private Double vo2MaxAstrand = 0.0;
    private Double vo2MaxQueens = 0.0;
    //bioimpedancia
    private Double percentualAgua = 0.0;
    private Double tmb = 0.0;
    private Double resistencia = 0.0;
    private Double reatancia = 0.0;
    private Double gorduraIdeal = 0.0;
    private Double necessidadeFisica = 0.0;
    private Double necessidadeCalorica = 0.0;
    private Double idadeMetabolica = 0.0;
    private Double gorduraVisceral = 0.0;

    //somatotipia
    private Double ectomorfia = 0.0;
    private Double mesomorfia = 0.0;
    private Double endomorfia = 0.0;


    //Perimetria
    private Double antebracoEsq = 0.0;
    private Double bracoRelaxadoEsq = 0.0;
    private Double bracoContraidoEsq = 0.0;
    private Double coxaMediaEsq = 0.0;
    private Double panturrilhaEsq = 0.0;
    private Double punho = 0.0;
    private Double quadril = 0.0;
    private Double ombro = 0.0;
    private Double pescoco = 0.0;

    private Integer movProduto;
    private Integer venda;

    private Double coxaDistalDir = 0.0;
    private Double coxaDistalEsq = 0.0;

    private Double coxaProximalDir = 0.0;
    private Double coxaProximalEsq = 0.0;

    private Double antebracoDir = 0.0;
    private Double bracoRelaxadoDir = 0.0;
    private Double bracoContraidoDir = 0.0;
    private Double coxaMediaDir = 0.0;
    private Double panturrilhaDir = 0.0;
    private Double toraxBusto = 0.0;
    private Double cintura = 0.0;
    private Double gluteo = 0.0;
    private Double flexibilidade = 0.0;
    private Double circunferenciaAbdominal = 0.0;
    private Double totalPerimetria = 0.0;
    //Resultado
    @Column(columnDefinition = "text", length = 9999)
    private String recomendacoes;
    private Double percentualGordura = 0.0;
    private Double metaPercentualGordura = 0.0;
    private Double metaPercentualGorduraAnterior = 0.0;
    private Double percentualMassaMagra = 0.0;
    @Enumerated(EnumType.ORDINAL)
    private CategoriaPercentualGordura categoriaPercentualGordura = CategoriaPercentualGordura.NENHUM;
    private Double massaMagra = 0.0;
    private Double massaGorda = 0.0;
    private Double pesoOsseo = 0.0;
    private Double pesoMuscular = 0.0;
    private Double residual = 0.0;
    private Double imc = 0.0;
    @Enumerated(EnumType.ORDINAL)
    private CategoriaAvaliacaoIMC categoriaAvaliacaoIMC = CategoriaAvaliacaoIMC.NENHUM;
    @Temporal(TemporalType.TIMESTAMP)
    private Date dataAvaliacao;
    @Temporal(TemporalType.TIMESTAMP)
    private Date dataProxima;
    @JsonIgnore
    private Integer responsavelLancamento_codigo;
    @OneToOne(cascade = CascadeType.REMOVE, orphanRemoval = true)
    @JsonIgnore
    private Agendamento agendamentoReavaliacao;
    private Boolean aumentoPercentualGordura;
    private String idAvaliacaoFisicaApi;
    @Transient
    private List<String> dobrasUsadas;
    @Transient
    private Boolean selecionado = false;
    @Transient
    private Map<String, Integer> valores = new HashMap<String, Integer>();


    @Transient
    private Double diametroPunho;
    @Transient
    private Double diametroJoelho;
    @Transient
    private Double diametroCotovelo;
    @Transient
    private Double diametroTornozelo;

    public AvaliacaoFisica() {
    }

    public String getCategoriaApresentar() {
        return categoriaAvaliacaoIMC == null ? "" : categoriaAvaliacaoIMC.getDescricao();
    }

    public String getCategoriaPercGordApresentar() {
        return categoriaPercentualGordura == null ? "" : categoriaPercentualGordura.getDescricao();
    }

    public String getPercentualGorduraApresentar() {
        return percentualGordura == null ? "0" : Uteis.formatarValorNumerico(Uteis.arredondarForcando2CasasDecimais(percentualGordura));
    }

    public String getPercentualMassaMagraApresentar() {
        return percentualMassaMagra == null ? "0" : Uteis.formatarValorNumerico(Uteis.arredondarForcando2CasasDecimais(percentualMassaMagra));
    }

    public List<String> getDobrasUsadas() {
        if (dobrasUsadas == null) {
            if (cliente != null && cliente.isMenorIdade() && protocolo != null && protocolo.equals(ProtocolosAvaliacaoFisicaEnum.POLLOCK_3_DOBRAS)) {
                dobrasUsadas = Arrays.asList("coxa", "triceps");
                return dobrasUsadas;
            }
            dobrasUsadas = (cliente == null || cliente.getSexo() == null
                    || cliente.getSexo().equals("M")
                    || getProtocolo().getCamposMulher() == null
                    || getProtocolo().getCamposMulher().length == 0) ? Arrays.asList(getProtocolo().getCamposHomem())
                    : Arrays.asList(getProtocolo().getCamposMulher());
        }
        return dobrasUsadas;
    }

    public boolean getUsouAbdominal() {
        return !UteisValidacao.emptyNumber(getAbdominal());
    }

    public boolean getUsouSuprailiaca() {
        return !UteisValidacao.emptyNumber(getSupraIliaca());
    }

    public boolean getUsouTorax() {
        return !UteisValidacao.emptyNumber(getToraxBusto());
    }

    public boolean getUsouTriceps() {
        return !UteisValidacao.emptyNumber(getTriceps());
    }

    public boolean getUsouCoxa() {
        return !UteisValidacao.emptyNumber(getCoxaMedial());
    }

    public boolean getUsouBiceps() {
        return !UteisValidacao.emptyNumber(getBiceps());
    }

    public boolean getUsouPeso() {
        return !UteisValidacao.emptyNumber(getPeso());
    }

    public boolean getUsouSubescapular() {
        return !UteisValidacao.emptyNumber(getSubescapular());
    }

    public boolean getUsouAxilarMedia() {
        return !UteisValidacao.emptyNumber(getAxilarMedia());
    }

    public boolean getUsouAltura() {
        return !UteisValidacao.emptyNumber(getAltura());
    }

    public boolean getUsouPanturrilha() {
        return !UteisValidacao.emptyNumber(getPanturrilha());
    }

    public boolean getUsouSupraespinhal() {
        return !UteisValidacao.emptyNumber(getSupraEspinhal());
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public ClienteSintetico getCliente() {
        return cliente;
    }

    public void setCliente(ClienteSintetico cliente) {
        this.cliente = cliente;
    }

    public Double inicializarValor(Double valor) {
        if (valor == null) {
            valor = 0.0;
        }
        return valor;
    }

    public Double getAbdominal() {
        return inicializarValor(abdominal);
    }

    public void setAbdominal(Double abdominal) {
        this.abdominal = abdominal;
    }

    public Double getPeitoral() {
        return inicializarValor(peitoral);
    }

    public void setPeitoral(Double peitoral) {
        this.peitoral = peitoral;
    }

    public Double getCoxaMedial() {
        return inicializarValor(coxaMedial);
    }

    public void setCoxaMedial(Double coxaMedial) {
        this.coxaMedial = coxaMedial;
    }

    public Double getAntebracoEsq() {
        return inicializarValor(antebracoEsq);
    }

    public void setAntebracoEsq(Double antebracoEsq) {
        this.antebracoEsq = antebracoEsq;
    }

    public Double getBracoRelaxadoEsq() {
        return inicializarValor(bracoRelaxadoEsq);
    }

    public void setBracoRelaxadoEsq(Double bracoRelaxadoEsq) {
        this.bracoRelaxadoEsq = bracoRelaxadoEsq;
    }

    public Double getBracoContraidoEsq() {
        return inicializarValor(bracoContraidoEsq);
    }

    public void setBracoContraidoEsq(Double bracoContraidoEsq) {
        this.bracoContraidoEsq = bracoContraidoEsq;
    }

    public Double getCoxaMediaEsq() {
        return inicializarValor(coxaMediaEsq);
    }

    public void setCoxaMediaEsq(Double coxaMediaEsq) {
        this.coxaMediaEsq = coxaMediaEsq;
    }

    public Double getPanturrilhaEsq() {
        return inicializarValor(panturrilhaEsq);
    }

    public void setPanturrilhaEsq(Double panturrilhaEsq) {
        this.panturrilhaEsq = panturrilhaEsq;
    }

    public Double getAntebracoDir() {
        return inicializarValor(antebracoDir);
    }

    public void setAntebracoDir(Double antebracoDir) {
        this.antebracoDir = antebracoDir;
    }

    public Double getBracoRelaxadoDir() {
        return inicializarValor(bracoRelaxadoDir);
    }

    public void setBracoRelaxadoDir(Double bracoRelaxadoDir) {
        this.bracoRelaxadoDir = bracoRelaxadoDir;
    }

    public Double getBracoContraidoDir() {
        return inicializarValor(bracoContraidoDir);
    }

    public void setBracoContraidoDir(Double bracoContraidoDir) {
        this.bracoContraidoDir = bracoContraidoDir;
    }

    public Double getCoxaMediaDir() {
        return inicializarValor(coxaMediaDir);
    }

    public void setCoxaMediaDir(Double coxaMediaDir) {
        this.coxaMediaDir = coxaMediaDir;
    }

    public Double getPanturrilhaDir() {
        return inicializarValor(panturrilhaDir);
    }

    public void setPanturrilhaDir(Double panturrilhaDir) {
        this.panturrilhaDir = panturrilhaDir;
    }

    public Double getToraxBusto() {
        return inicializarValor(toraxBusto);
    }

    public void setToraxBusto(Double toraxBusto) {
        this.toraxBusto = toraxBusto;
    }

    public Double getCintura() {
        return inicializarValor(cintura);
    }

    public void setCintura(Double cintura) {
        this.cintura = cintura;
    }

    public Double getGluteo() {
        return inicializarValor(gluteo);
    }

    public void setGluteo(Double gluteo) {
        this.gluteo = gluteo;
    }

    public Double getFlexibilidade() {
        return inicializarValor(flexibilidade);
    }

    public void setFlexibilidade(Double flexibilidade) {
        this.flexibilidade = flexibilidade;
    }

    public Double getPercentualGordura() {
        return inicializarValor(percentualGordura);
    }

    public void setPercentualGordura(Double percentualGordura) {
        this.percentualGordura = percentualGordura;
    }

    public CategoriaPercentualGordura getCategoriaPercentualGordura() {
        return categoriaPercentualGordura;
    }

    public void setCategoriaPercentualGordura(CategoriaPercentualGordura categoriaPercentualGordura) {
        this.categoriaPercentualGordura = categoriaPercentualGordura;
    }

    public Double getMassaMagra() {
        return inicializarValor(massaMagra);
    }

    public void setMassaMagra(Double massaMagra) {
        this.massaMagra = massaMagra;
    }

    public Double getMassaGorda() {
        return inicializarValor(massaGorda);
    }

    public void setMassaGorda(Double massaGorda) {
        this.massaGorda = massaGorda;
    }

    public Double getImc() {
        return inicializarValor(imc);
    }

    public String getImcApresentar() {
        return imc == null ? " - " : Uteis.formatarValorNumerico(Uteis.arredondarForcando2CasasDecimais(imc));
    }

    public String getMassaGordaApresentar() {
        return massaGorda == null ? " - " : Uteis.formatarValorNumerico(massaGorda);
    }

    public String getMassaMagraApresentar() {
        return massaMagra == null ? " - " : Uteis.formatarValorNumerico(massaMagra);
    }

    public void setImc(Double imc) {
        this.imc = imc;
    }

    public CategoriaAvaliacaoIMC getCategoriaAvaliacaoIMC() {
        return categoriaAvaliacaoIMC;
    }

    public void setCategoriaAvaliacaoIMC(CategoriaAvaliacaoIMC categoriaAvaliacaoIMC) {
        this.categoriaAvaliacaoIMC = categoriaAvaliacaoIMC;
    }

    public Date getDataAvaliacao() {
        return dataAvaliacao;
    }

    public void setDataAvaliacao(Date dataAvaliacao) {
        this.dataAvaliacao = dataAvaliacao;
    }

    public Integer getResponsavelLancamento_codigo() {
        return responsavelLancamento_codigo;
    }

    public void setResponsavelLancamento_codigo(Integer responsavelLancamento) {
        this.responsavelLancamento_codigo = responsavelLancamento;
    }

    public Double getTotalDobras() {
        return inicializarValor(totalDobras);
    }

    public void setTotalDobras(Double totalDobras) {
        this.totalDobras = totalDobras;
    }

    public Double getTotalPerimetria() {
        return inicializarValor(totalPerimetria);
    }

    public void setTotalPerimetria(Double totalPerimetria) {
        this.totalPerimetria = totalPerimetria;
    }

    public String getDataAvaliacao_Apresentar() {
        return Uteis.getData(getDataAvaliacao());
    }

    public String getDataProxima_Apresentar() {
        return Uteis.getData(getDataProxima());
    }

    public Double getPeso() {
        return inicializarValor(peso);
    }

    public void setPeso(Double peso) {
        this.peso = peso;
    }

    public Double getAltura() {
        return inicializarValor(altura);
    }

    public void setAlturaString(String d) {
        altura = new Double(d.replace(",", "."));

    }

    public String getAlturaString() {
        try {
            return altura.toString().replace(".", ",");
        } catch (Exception e) {
            return "0,00";
        }
    }

    public void setAltura(Double altura) {
        this.altura = altura;
    }

    public Boolean getAumentoPercentualGordura() {
        return aumentoPercentualGordura;
    }

    public void setAumentoPercentualGordura(Boolean aumentoPercentualGordura) {
        this.aumentoPercentualGordura = aumentoPercentualGordura;
    }

    public Double getSupraIliaca() {
        return inicializarValor(supraIliaca);
    }

    public void setSupraIliaca(Double supraIliaca) {
        this.supraIliaca = supraIliaca;
    }

    public Double getTriceps() {
        return inicializarValor(triceps);
    }

    public void setTriceps(Double triceps) {
        this.triceps = triceps;
    }

    public Boolean getSelecionado() {
        return selecionado;
    }

    public void setSelecionado(Boolean selecionado) {
        this.selecionado = selecionado;
    }

    public Double getSubescapular() {
        return inicializarValor(subescapular);
    }

    public void setSubescapular(Double subescapular) {
        this.subescapular = subescapular;
    }

    public Double getBiceps() {
        return inicializarValor(biceps);
    }

    public void setBiceps(Double biceps) {
        this.biceps = biceps;
    }

    public Double getAxilarMedia() {
        return inicializarValor(axilarMedia);
    }

    public void setAxilarMedia(Double axilarMedia) {
        this.axilarMedia = axilarMedia;
    }

    public Double getSupraEspinhal() {
        return inicializarValor(supraEspinhal);
    }

    public void setSupraEspinhal(Double supraEspinhal) {
        this.supraEspinhal = supraEspinhal;
    }

    public Double getPanturrilha() {
        return inicializarValor(panturrilha);
    }

    public void setPanturrilha(Double panturrilha) {
        this.panturrilha = panturrilha;
    }


    public Double getPunho() {
        return inicializarValor(punho);
    }

    public void setPunho(Double punho) {
        this.punho = punho;
    }

    public Double getQuadril() {
        return inicializarValor(quadril);
    }

    public void setQuadril(Double quadril) {
        this.quadril = quadril;
    }

    public Double getPercentualMassaMagra() {
        return inicializarValor(percentualMassaMagra);
    }

    public void setPercentualMassaMagra(Double percentualMassaMagra) {
        this.percentualMassaMagra = percentualMassaMagra;
    }

    public Double getPesoOsseo() {
        return inicializarValor(pesoOsseo);
    }

    public void setPesoOsseo(Double pesoOsseo) {
        this.pesoOsseo = pesoOsseo;
    }

    public Double getPesoMuscular() {
        return inicializarValor(Uteis.forcarCasasDecimais(2, pesoMuscular == null ? 0.0 : pesoMuscular));
    }

    public void setPesoMuscular(Double pesoMuscular) {
        this.pesoMuscular = pesoMuscular;
    }

    public Double getResidual() {
        return inicializarValor(residual);
    }

    public void setResidual(Double residual) {
        this.residual = residual;
    }

    public ProtocolosAvaliacaoFisicaEnum getProtocolo() {
        if (protocolo == null) {
            protocolo = ProtocolosAvaliacaoFisicaEnum.POLLOCK_3_DOBRAS;
        }
        return protocolo;
    }

    public void setProtocolo(ProtocolosAvaliacaoFisicaEnum protocolo) {
        this.protocolo = protocolo;
    }

    public String getNameProtocolo() {
        return getProtocolo().name();
    }

    public Double getPercentualAgua() {
        return inicializarValor(percentualAgua);
    }

    public void setPercentualAgua(Double percentualAgua) {
        this.percentualAgua = percentualAgua;
    }

    public Double getTmb() {
        return inicializarValor(tmb);
    }

    public void setTmb(Double tmb) {
        this.tmb = tmb;
    }

    public Double getResistencia() {
        return inicializarValor(resistencia);
    }

    public void setResistencia(Double resistencia) {
        this.resistencia = resistencia;
    }

    public Double getReatancia() {
        return inicializarValor(reatancia);
    }

    public void setReatancia(Double reatancia) {
        this.reatancia = reatancia;
    }

    public Double getGorduraIdeal() {
        return inicializarValor(gorduraIdeal);
    }

    public void setGorduraIdeal(Double gorduraIdeal) {
        this.gorduraIdeal = gorduraIdeal;
    }

    public Double getNecessidadeFisica() {
        return inicializarValor(necessidadeFisica);
    }

    public void setNecessidadeFisica(Double necessidadeFisica) {
        this.necessidadeFisica = necessidadeFisica;
    }

    public Double getNecessidadeCalorica() {
        return inicializarValor(necessidadeCalorica);
    }

    public void setNecessidadeCalorica(Double necessidadeCalorica) {
        this.necessidadeCalorica = necessidadeCalorica;
    }

    public Double getCircunferenciaAbdominal() {
        return inicializarValor(circunferenciaAbdominal);
    }

    public void setCircunferenciaAbdominal(Double circunferenciaAbdominal) {
        this.circunferenciaAbdominal = circunferenciaAbdominal;
    }


    public String getResultadoRiscoCA() {
        if (UteisValidacao.emptyNumber(circunferenciaAbdominal)) {
            return "avaliacao.circunferencia.normal";
        }
        double limiteAumentado = getCliente() == null
                || getCliente().isSexoMasculino() ? 94.0 : 80.0;
        double limiteAumentadoSubstancialmente = getCliente() == null
                || getCliente().isSexoMasculino() ? 102 : 88.0;
        return circunferenciaAbdominal >= limiteAumentadoSubstancialmente ? "avaliacao.circunferencia.aumentado.substancialmente" :
                circunferenciaAbdominal >= limiteAumentado ? "avaliacao.circunferencia.aumentado" : "avaliacao.circunferencia.normal";
    }

    public void setDobrasUsadas(List<String> dobrasUsadas) {
        this.dobrasUsadas = dobrasUsadas;
    }

    public Map<String, Integer> getValores() {
        return valores;
    }

    public void setValores(Map<String, Integer> valores) {
        this.valores = valores;
    }

    public List<String> getNiveis() {
        ArrayList<String> strings = new ArrayList<String>(valores.keySet());
        Collections.sort(strings);
        return strings;
    }

    public Double getFcMaxima() {
        return inicializarValor(fcMaxima);
    }

    public void setFcMaxima(Double fcMaxima) {
        this.fcMaxima = fcMaxima;
    }

    public Double getDistancia12() {
        return inicializarValor(distancia12);
    }

    public void setDistancia12(Double distancia12) {
        this.distancia12 = distancia12;
    }

    public Double getVo2Max12() {
        return inicializarValor(vo2Max12);
    }

    public void setVo2Max12(Double vo2Max12) {
        this.vo2Max12 = vo2Max12;
    }

    public Double getVoMaxAerobico() {
        return voMaxAerobico;
    }

    public void setVoMaxAerobico(Double voMaxAerobico) {
        this.voMaxAerobico = voMaxAerobico;
    }

    public Double getOmbro() {
        return inicializarValor(ombro);
    }

    public void setOmbro(Double ombro) {
        this.ombro = ombro;
    }

    public Double getPescoco() {
        return inicializarValor(pescoco);
    }

    public void setPescoco(Double pescoco) {
        this.pescoco = pescoco;
    }

    public Double getCoxaDistalDir() {
        return inicializarValor(coxaDistalDir);
    }

    public void setCoxaDistalDir(Double coxaDistalDir) {
        this.coxaDistalDir = coxaDistalDir;
    }

    public Double getCoxaDistalEsq() {
        return inicializarValor(coxaDistalEsq);
    }

    public void setCoxaDistalEsq(Double coxaDistalEsq) {
        this.coxaDistalEsq = coxaDistalEsq;
    }

    public Double getCoxaProximalDir() {
        return inicializarValor(coxaProximalDir);
    }

    public void setCoxaProximalDir(Double coxaProximalDir) {
        this.coxaProximalDir = coxaProximalDir;
    }

    public Double getCoxaProximalEsq() {
        return inicializarValor(coxaProximalEsq);
    }

    public void setCoxaProximalEsq(Double coxaProximalEsq) {
        this.coxaProximalEsq = coxaProximalEsq;
    }

    public String getAlturaApresentar() {
        return altura == null ? " - " : Uteis.formatarValorNumerico(altura);
    }

    public String getPesoApresentar() {
        return peso == null ? " - " : Uteis.formatarValorNumerico(peso);
    }

    public String getMetaApresentar() {
        return UteisValidacao.emptyNumber(metaPercentualGordura) ? " - " : Uteis.formatarValorNumerico(metaPercentualGordura);
    }

    public String getMetaAnteriorApresentar() {
        return UteisValidacao.emptyNumber(metaPercentualGorduraAnterior) ? " - " : Uteis.formatarValorNumerico(metaPercentualGorduraAnterior);
    }

    public String getTempo2400() {
        return tempo2400;
    }

    public void setTempo2400(String tempo2400) {
        this.tempo2400 = tempo2400;
    }

    public Double getVo2Max2400() {
        return inicializarValor(vo2Max2400);
    }

    public void setVo2Max2400(Double vo2Max2400) {
        this.vo2Max2400 = vo2Max2400;
    }

    public Date getDataProxima() {
        return dataProxima;
    }

    public void setDataProxima(Date dataProxima) {
        this.dataProxima = dataProxima;
    }

    public Agendamento getAgendamentoReavaliacao() {
        return agendamentoReavaliacao;
    }

    public void setAgendamentoReavaliacao(Agendamento agendamentoReavaliacao) {
        this.agendamentoReavaliacao = agendamentoReavaliacao;
    }

    public Integer getMovProduto() {
        return movProduto;
    }

    public void setMovProduto(Integer movProduto) {
        this.movProduto = movProduto;
    }

    public Integer getVenda() {
        return venda;
    }

    public void setVenda(Integer venda) {
        this.venda = venda;
    }

    public String getAssinatura() {
        return assinatura;
    }

    public void setAssinatura(String assinatura) {
        this.assinatura = assinatura;
    }

    public String getRecomendacoes() {
        return recomendacoes;
    }

    public void setRecomendacoes(String recomendacoes) {
        this.recomendacoes = recomendacoes;
    }

    public ProtocolosVo2MaxEnum getProtocoloVo() {
        if (protocoloVo == null) {
            protocoloVo = ProtocolosVo2MaxEnum.VO_CAMINHADA_CORRIDA_12_MINUTOS;
        }
        return protocoloVo;
    }


    public String getUrlProntaAssinatura() {
        return urlAssinatura == null ? "" : (Aplicacao.obterUrlFotoDaNuvem(urlAssinatura) + "?v=" + new Date().getTime());
    }

    public String getUrlAssinatura() {
        return urlAssinatura;
    }

    public void setUrlAssinatura(String urlAssinatura) {
        this.urlAssinatura = urlAssinatura;
    }

    public void setProtocoloVo(ProtocolosVo2MaxEnum protocoloVo) {
        this.protocoloVo = protocoloVo;
    }

    public Double getEctomorfia() {
        return inicializarValor(ectomorfia);
    }

    public void setEctomorfia(Double ectomorfia) {
        this.ectomorfia = ectomorfia;
    }

    public Double getMesomorfia() {
        return inicializarValor(mesomorfia);
    }

    public void setMesomorfia(Double mesomorfia) {
        this.mesomorfia = mesomorfia;
    }

    public Double getEndomorfia() { return inicializarValor(endomorfia); }

    public void setEndomorfia(Double endomorfia) {
        this.endomorfia = endomorfia;
    }

    public String getEctomorfiaApresentar() {
        return ectomorfia == null ? "0" : Uteis.formatarValorNumerico(Uteis.forcarCasasDecimais(3, ectomorfia));
    }

    public String getEndomorfiaApresentar() {
        return endomorfia == null ? "0" : Uteis.formatarValorNumerico(Uteis.forcarCasasDecimais(3, endomorfia));
    }

    public String getMesomorfiaApresentar() {
        return mesomorfia == null ? "0" : Uteis.formatarValorNumerico(Uteis.forcarCasasDecimais(3, mesomorfia));
    }

    public Double getFcAstrand4() { return inicializarValor(fcAstrand4); }

    public void setFcAstrand4(Double fcAstrand4) {
        this.fcAstrand4 = fcAstrand4;
    }

    public Double getFcAstrand5() { return inicializarValor(fcAstrand5); }

    public void setFcAstrand5(Double fcAstrand5) { this.fcAstrand5 = fcAstrand5; }

    public Double getCargaAstrand() {
        return inicializarValor(cargaAstrand);
    }

    public void setCargaAstrand(Double cargaAstrand) {
        this.cargaAstrand = cargaAstrand;
    }

    public Double getVo2Astrand() {
        return inicializarValor(vo2Astrand);
    }

    public void setVo2Astrand(Double vo2Astrand) {
        this.vo2Astrand = vo2Astrand;
    }

    public Double getVo2MaxAstrand() {
        return inicializarValor(vo2MaxAstrand);
    }

    public void setVo2MaxAstrand(Double vo2MaxAstrand) {
        this.vo2MaxAstrand = vo2MaxAstrand;
    }

    public Double getIdadeMetabolica() {
        return inicializarValor(idadeMetabolica);
    }

    public void setIdadeMetabolica(Double idadeMetabolica) {
        this.idadeMetabolica = idadeMetabolica;
    }

    public Double getGorduraVisceral() {
        return inicializarValor(gorduraVisceral);
    }

    public void setGorduraVisceral(Double gorduraVisceral) {
        this.gorduraVisceral = gorduraVisceral;
    }

    public Double getMetaPercentualGordura() {
        return inicializarValor(metaPercentualGordura);
    }

    public void setMetaPercentualGordura(Double metaPercentualGordura) {
        this.metaPercentualGordura = metaPercentualGordura;
    }

    public Double getMetaPercentualGorduraAnterior() {
        return inicializarValor(metaPercentualGorduraAnterior);
    }

    public void setMetaPercentualGorduraAnterior(Double metaPercentualGorduraAnterior) {
        this.metaPercentualGorduraAnterior = metaPercentualGorduraAnterior;
    }

    public Double getFcQueens() {
        return inicializarValor(fcQueens);
    }

    public void setFcQueens(Double fcQueens) {
        this.fcQueens = fcQueens;
    }

    public Double getVo2MaxQueens() {
        return inicializarValor(vo2MaxQueens);
    }

    public void setVo2MaxQueens(Double vo2MaxQueens) {
        this.vo2MaxQueens = vo2MaxQueens;
    }

    public Double getDiametroPunho() {
        return diametroPunho;
    }

    public void setDiametroPunho(Double diametroPunho) {
        this.diametroPunho = diametroPunho;
    }

    public Double getDiametroJoelho() {
        return diametroJoelho;
    }

    public void setDiametroJoelho(Double diametroJoelho) {
        this.diametroJoelho = diametroJoelho;
    }

    public Double getDiametroCotovelo() {
        return diametroCotovelo;
    }

    public void setDiametroCotovelo(Double diametroCotovelo) {
        this.diametroCotovelo = diametroCotovelo;
    }

    public Double getDiametroTornozelo() {
        return diametroTornozelo;
    }

    public void setDiametroTornozelo(Double diametroTornozelo) {
        this.diametroTornozelo = diametroTornozelo;
    }

    public AvaliacaoPostural getAvaliacaoPosturual() {
        return avaliacaoPosturual;
    }

    public void setAvaliacaoPosturual(AvaliacaoPostural avaliacaoPosturual) {
        this.avaliacaoPosturual = avaliacaoPosturual;
    }

    public String getLogBalanca() {
        return logBalanca;
    }

    public void setLogBalanca(String logBalanca) {
        this.logBalanca = logBalanca;
    }

    public String getDescricaoParaLog(AvaliacaoFisica a2) {
        try {
            StringBuilder log = new StringBuilder();
            log.append(UtilReflection.difference(this, a2));
            return log.toString();
        } catch (Exception ex) {
            ex.printStackTrace();
            return "ERRO gerar log";
        }
    }

    public String getIdAvaliacaoFisicaApi() {
        return idAvaliacaoFisicaApi;
    }

    public void setIdAvaliacaoFisicaApi(String idAvaliacaoFisicaApi) {
        this.idAvaliacaoFisicaApi = idAvaliacaoFisicaApi;
    }
}
