package br.com.pacto.bean.avaliacao;

/**
 * Created by <PERSON><PERSON> on 01/05/2017.
 */
public enum ItemAvaliacaoFisicaEnum {
    PRESSAO_ARTERIAL("FREQUENCIA_CARDIACA"),
    RESISTENCIA_MUSCULAR_ABDOMEN("RESISTENCIA_MUSCULAR_BRACO"),
    RESISTENCIA_MUSCULAR_BRACO,
    ALTURA,
    PESO,
    OBJETIVOS,
    PARQ,
    FREQUENCIA_CARDIACA,
    PESO_OSSEO,
    ANAMNESE,
    VENTILOMETRIA,
    FLEXIBILIDADE,
    AVALIACAO_INTEGRADA,
    PRESSAO_ARTERIAL_SISTOLICA,
    PRESSAO_ARTERIAL_DIASTOLICA,
    RESISTENCIA_MUSCULAR_ABDOMEN_HOMEM,
    RESISTENCIA_MUSCULAR_ABDOMEN_MULHER,
    RESISTENCIA_MUSCULAR_BRACO_HOMEM,
    RESISTENCIA_MUSCULAR_BRACO_MULHER,
    ;

    private String[] historicoConjunto;

    ItemAvaliacaoFisicaEnum(String... historicoConjunto) {
        this.historicoConjunto = historicoConjunto;
    }

    public String getLabel(){
        return "lbl".concat(name());
    }

    public String[] getHistoricoConjunto() {
        return historicoConjunto;
    }

    public void setHistoricoConjunto(String[] historicoConjunto) {
        this.historicoConjunto = historicoConjunto;
    }
}
