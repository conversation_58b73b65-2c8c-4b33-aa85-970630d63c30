package br.com.pacto.bean.avaliacao;

import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class AvaliacaoVo2DTO {

    private Double vo2Max;
    private Double limiarVentilatorioI;
    private Double limiarVentilatorioII;
    /**
     * Protocolo I
     */
    private Double caminhadaCorridaDistancia;
    /**
     * Protocolo II
     */
    private Double teste2400MetrosTempo;
    /**
     * Protocolo III
     */
    private Double aerobicoBancoFrequencia;
    /**
     * TESTE SUBMÁXIMO DE ASTRAND
     */
    private Double astrandFrequencia4;
    private Double astrandFrequencia5;
    private Double astrandCarga;
    private Double astrandVo2Estimado;
    private Double astrandVo2Maximo;
    /**
     * TESTE SUBMÁXIMO DE COLLEGE
     */
    private Double collegeFrequencia;
    private Double collegeVo2Maximo;

    private CaminhadaCorridaColunaEnum caminhadaCorridaColuna;
    private CaminhadaCorridaLinhaEnum caminhadaCorridaLinha;
    private Teste2400MetrosColunaEnum teste2400MetrosColuna;
    private Teste2400MetrosLinhaEnum teste2400MetrosLinha;
    private AerobicobancoColunaEnum aerobicobancoColuna;
    private AerobicoBancoLinhaEnum aerobicoBancoLinha;


    private ProtocolosVo2MaxEnum protocolo;
    private Double aerobicoBancoVo2Max;
    private Double teste2400MetrosVo2Max;
    private Double caminhadaCorridaVo2Max;

    public AvaliacaoVo2DTO() {
    }

    public AvaliacaoVo2DTO(AvaliacaoFisica avaliacaoFisica, Ventilometria ventilometria) {
        if (ventilometria != null) {
            this.vo2Max = ventilometria.getVo2max();
            this.limiarVentilatorioI = ventilometria.getLimiarVentilatorio();
            this.limiarVentilatorioII = ventilometria.getLimiarVentilatorio2();
        }
        this.caminhadaCorridaDistancia = avaliacaoFisica.getDistancia12();
        this.teste2400MetrosTempo = avaliacaoFisica.getVo2Max2400();
        this.aerobicoBancoFrequencia = avaliacaoFisica.getVoMaxAerobico();
        this.astrandFrequencia4 = avaliacaoFisica.getFcAstrand4();
        this.astrandFrequencia5 = avaliacaoFisica.getFcAstrand5();
        this.astrandCarga = avaliacaoFisica.getCargaAstrand();
        this.collegeFrequencia = avaliacaoFisica.getFcQueens();
        this.astrandVo2Estimado = avaliacaoFisica.getVo2Astrand();
        this.astrandVo2Maximo = avaliacaoFisica.getVo2MaxAstrand();
        this.collegeVo2Maximo = avaliacaoFisica.getVo2MaxQueens();
    }

    public AvaliacaoVo2DTO(AvaliacaoFisica avaliacaoFisica) {
        this.vo2Max = 0.0;
        this.limiarVentilatorioI = 0.0;
        this.limiarVentilatorioII = 0.0;
        this.caminhadaCorridaDistancia = avaliacaoFisica.getDistancia12();
        this.teste2400MetrosTempo = avaliacaoFisica.getVo2Max2400();
        this.aerobicoBancoFrequencia = avaliacaoFisica.getVoMaxAerobico();
        this.astrandFrequencia4 = avaliacaoFisica.getFcAstrand4();
        this.astrandFrequencia5 = avaliacaoFisica.getFcAstrand5();
        this.astrandCarga = avaliacaoFisica.getCargaAstrand();
        this.collegeFrequencia = avaliacaoFisica.getFcQueens();
        this.astrandVo2Estimado = avaliacaoFisica.getVo2Astrand();
        this.astrandVo2Maximo = avaliacaoFisica.getVo2MaxAstrand();
        this.collegeVo2Maximo = avaliacaoFisica.getVo2MaxQueens();
    }

    public Double getVo2Max() {
        return vo2Max;
    }

    public void setVo2Max(Double vo2Max) {
        this.vo2Max = vo2Max;
    }

    public Double getLimiarVentilatorioI() {
        return limiarVentilatorioI;
    }

    public void setLimiarVentilatorioI(Double limiarVentilatorioI) {
        this.limiarVentilatorioI = limiarVentilatorioI;
    }

    public Double getLimiarVentilatorioII() {
        return limiarVentilatorioII;
    }

    public void setLimiarVentilatorioII(Double limiarVentilatorioII) {
        this.limiarVentilatorioII = limiarVentilatorioII;
    }

    public Double getCaminhadaCorridaDistancia() {
        return caminhadaCorridaDistancia;
    }

    public void setCaminhadaCorridaDistancia(Double caminhadaCorridaDistancia) {
        this.caminhadaCorridaDistancia = caminhadaCorridaDistancia;
    }

    public Double getTeste2400MetrosTempo() {
        return teste2400MetrosTempo;
    }

    public void setTeste2400MetrosTempo(Double teste2400MetrosTempo) {
        this.teste2400MetrosTempo = teste2400MetrosTempo;
    }

    public Double getAerobicoBancoFrequencia() {
        return aerobicoBancoFrequencia;
    }

    public void setAerobicoBancoFrequencia(Double aerobicoBancoFrequencia) {
        this.aerobicoBancoFrequencia = aerobicoBancoFrequencia;
    }

    public Double getAstrandFrequencia4() {
        return astrandFrequencia4;
    }

    public void setAstrandFrequencia4(Double astrandFrequencia4) {
        this.astrandFrequencia4 = astrandFrequencia4;
    }

    public Double getAstrandFrequencia5() { return astrandFrequencia5; }

    public void setAstrandFrequencia5(Double astrandFrequencia5) { this.astrandFrequencia5 = astrandFrequencia5; }

    public Double getAstrandCarga() {
        return astrandCarga;
    }

    public void setAstrandCarga(Double astrandCarga) {
        this.astrandCarga = astrandCarga;
    }

    public ProtocolosVo2MaxEnum getProtocolo() {
        return protocolo;
    }

    public void setProtocolo(ProtocolosVo2MaxEnum protocolo) {
        this.protocolo = protocolo;
    }

    public Teste2400MetrosColunaEnum getTeste2400MetrosColuna() {
        return teste2400MetrosColuna;
    }

    public void setTeste2400MetrosColuna(Teste2400MetrosColunaEnum teste2400MetrosColuna) {
        this.teste2400MetrosColuna = teste2400MetrosColuna;
    }

    public Teste2400MetrosLinhaEnum getTeste2400MetrosLinha() {
        return teste2400MetrosLinha;
    }

    public void setTeste2400MetrosLinha(Teste2400MetrosLinhaEnum teste2400MetrosLinha) {
        this.teste2400MetrosLinha = teste2400MetrosLinha;
    }

    public AerobicobancoColunaEnum getAerobicobancoColuna() {
        return aerobicobancoColuna;
    }

    public void setAerobicobancoColuna(AerobicobancoColunaEnum aerobicobancoColuna) {
        this.aerobicobancoColuna = aerobicobancoColuna;
    }

    public AerobicoBancoLinhaEnum getAerobicoBancoLinha() {
        return aerobicoBancoLinha;
    }

    public void setAerobicoBancoLinha(AerobicoBancoLinhaEnum aerobicoBancoLinha) {
        this.aerobicoBancoLinha = aerobicoBancoLinha;
    }

    public CaminhadaCorridaColunaEnum getCaminhadaCorridaColuna() {
        return caminhadaCorridaColuna;
    }

    public void setCaminhadaCorridaColuna(CaminhadaCorridaColunaEnum caminhadaCorridaColuna) {
        this.caminhadaCorridaColuna = caminhadaCorridaColuna;
    }

    public CaminhadaCorridaLinhaEnum getCaminhadaCorridaLinha() {
        return caminhadaCorridaLinha;
    }

    public void setCaminhadaCorridaLinha(CaminhadaCorridaLinhaEnum caminhadaCorridaLinha) {
        this.caminhadaCorridaLinha = caminhadaCorridaLinha;
    }

    public Double getAerobicoBancoVo2Max() {
        return aerobicoBancoVo2Max;
    }

    public void setAerobicoBancoVo2Max(Double aerobicoBancoVo2Max) {
        this.aerobicoBancoVo2Max = aerobicoBancoVo2Max;
    }

    public Double getTeste2400MetrosVo2Max() {
        return teste2400MetrosVo2Max;
    }

    public void setTeste2400MetrosVo2Max(Double teste2400MetrosVo2Max) {
        this.teste2400MetrosVo2Max = teste2400MetrosVo2Max;
    }

    public Double getCaminhadaCorridaVo2Max() {
        return caminhadaCorridaVo2Max;
    }

    public void setCaminhadaCorridaVo2Max(Double caminhadaCorridaVo2Max) {
        this.caminhadaCorridaVo2Max = caminhadaCorridaVo2Max;
    }

    public Double getCollegeFrequencia() {
        return collegeFrequencia;
    }

    public void setCollegeFrequencia(Double collegeFrequencia) {
        this.collegeFrequencia = collegeFrequencia;
    }

    public Double getAstrandVo2Estimado() {
        return astrandVo2Estimado;
    }

    public void setAstrandVo2Estimado(Double astrandVo2Estimado) {
        this.astrandVo2Estimado = astrandVo2Estimado;
    }

    public Double getAstrandVo2Maximo() {
        return astrandVo2Maximo;
    }

    public void setAstrandVo2Maximo(Double astrandVo2Maximo) {
        this.astrandVo2Maximo = astrandVo2Maximo;
    }

    public Double getCollegeVo2Maximo() {
        return collegeVo2Maximo;
    }

    public void setCollegeVo2Maximo(Double collegeVo2Maximo) {
        this.collegeVo2Maximo = collegeVo2Maximo;
    }
}
