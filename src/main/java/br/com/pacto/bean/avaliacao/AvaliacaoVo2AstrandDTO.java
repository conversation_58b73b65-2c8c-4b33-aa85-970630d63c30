package br.com.pacto.bean.avaliacao;

import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class AvaliacaoVo2AstrandDTO {

    private Double astrandFrequencia4;
    private Double astrandFrequencia5;
    private Double astrandCarga;

    public Double getAstrandFrequencia4() {
        return astrandFrequencia4;
    }

    public void setAstrandFrequencia4(Double astrandFrequencia4) {
        this.astrandFrequencia4 = astrandFrequencia4;
    }

    public Double getAstrandFrequencia5() { return astrandFrequencia5; }

    public void setAstrandFrequencia5(Double astrandFrequencia5) { this.astrandFrequencia5 = astrandFrequencia5; }

    public Double getAstrandCarga() {
        return astrandCarga;
    }

    public void setAstrandCarga(Double astrandCarga) {
        this.astrandCarga = astrandCarga;
    }
}
