package br.com.pacto.bean.avaliacao;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.io.Serializable;

/**
 * <AUTHOR> <PERSON> 22/01/2019
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AnamnesePerguntaRespostaDTOUpdate implements Serializable {

    private Integer anamnesePerguntaId;
    private String resposta;
    private String observacao;

    public Integer getAnamnesePerguntaId() {
        return anamnesePerguntaId;
    }

    public void setAnamnesePerguntaId(Integer anamnesePerguntaId) {
        this.anamnesePerguntaId = anamnesePerguntaId;
    }

    public String getResposta() {
        return resposta;
    }

    public void setResposta(String resposta) {
        this.resposta = resposta;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

}
