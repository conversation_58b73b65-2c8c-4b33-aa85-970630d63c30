package br.com.pacto.service.analisador;

import br.com.pacto.base.util.ExecuteRequestHttpService;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.impl.gympass.dto.SlotsDTO;
import br.com.pacto.service.telegram.TelegramService;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.ResponseHandler;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPatch;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.BasicResponseHandler;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;
import org.json.JSONArray;
import org.json.JSONObject;

import java.util.*;

public class AnalisadorGympass {
    private static final String APPLICATION_FORM = "application/x-www-form-urlencoded";
    private static final String HEADER_AUTHORIZATION = "Authorization";
    private static final String HEADER_CONTENT_TYPE = "Content-Type";
    private static final String APPLICATION_JSON = "application/json";
    private static final String CHARSET_UTF8 = "UTF-8";
    private static final String ENDPOINT_SLOTS_PATCH = "/booking/v1/gyms/%s/classes/%s/slots/%s";
    public List<Date> dias = new ArrayList<>();
    public List<Cfgs> cfgs = new ArrayList<>();

    public JSONArray obterAulasPacto(Cfgs cfg, Date dia) throws Exception {
        String url = cfg.urlTreino + "/psec/agenda/turmas/all?ref=" +
                Uteis.getDataAplicandoFormatacao(dia, "yyyyMMdd")
                + "&periodo=DIA&filtros=%7B%22tipo%22:null,%22professoresIds%22:%5B%5D,%22ambientesIds%22:%5B%5D,%22modalidadesIds%22:%5B%5D%7D";
        return new JSONObject(get(url, cfg.token, cfg.empresa)).getJSONObject("content").optJSONArray(Uteis.getDataAplicandoFormatacao(dia, "yyyyMMdd"));
    }

    class Cfgs {

        public Cfgs(String urlTreino, String chave, String gymPass_id, Integer empresa) {
            this.token = token;
            this.urlTreino = urlTreino;
            this.chave = chave;
            this.gymPass_id = gymPass_id;
            this.empresa = empresa;
        }

        public String token = "";
        public String urlTreino = "";
        public String chave = "";
        public String gymPass_id = "";
        public Integer empresa = 0;
    }

    private String obterTokenPactoBr(Cfgs cfgs) throws Exception{
        CloseableHttpClient client = ExecuteRequestHttpService.createConnectorCloseable();
        HttpPost httpPost = new HttpPost(cfgs.urlTreino + "/login/app");
        httpPost.setHeader("Content-Type", "application/json");
        JSONObject dados = new JSONObject();
        dados.put("chave", cfgs.chave);
        dados.put("username", "pactobr");
        dados.put("senha", "");
        StringEntity entity = new StringEntity(dados.toString(), CHARSET_UTF8);
        httpPost.setEntity(entity);

        CloseableHttpResponse response = client.execute(httpPost);
        ResponseHandler<String> handler = new BasicResponseHandler();
        String body = handler.handleResponse(response);
        client.close();
        return "Bearer " + new JSONObject(body).getString("content");
    }


    public AnalisadorGympass() throws Exception{


        dias.add(Uteis.getDate("20/04/2022"));
        dias.add(Uteis.getDate("22/04/2022"));

        Cfgs c67f6ab171119549036f6e4d07647d062 = new Cfgs("https://zw55.pactosolucoes.com.br/TreinoWeb/prest",
                "67f6ab171119549036f6e4d07647d062", "479630", 1
        );
        c67f6ab171119549036f6e4d07647d062.token = obterTokenPactoBr(c67f6ab171119549036f6e4d07647d062);
        this.cfgs.add(c67f6ab171119549036f6e4d07647d062);

        Cfgs cfgs1 = new Cfgs("https://zw36.pactosolucoes.com.br/TreinoWeb/prest",
                "c4f0ab01248752b846def0b4c8cba16c", "373912", 1);
        cfgs1.token = obterTokenPactoBr(cfgs1);
        this.cfgs.add(cfgs1);


        Cfgs cfgs2 = new Cfgs("https://zw36.pactosolucoes.com.br/TreinoWeb/prest",
                "654a93bcd57f411e0ce5a456376cef53", "464469", 1
        );
        cfgs2.token = obterTokenPactoBr(cfgs2);
        this.cfgs.add(cfgs2);


    }

    public void reset(Cfgs cfg) throws Exception {
        List<Integer> ids = new ArrayList(){{}};
        for(Integer id : ids){
            System.out.println("resetando aula " + id + " chave " + cfg.chave);
            CloseableHttpClient client = ExecuteRequestHttpService.createConnectorCloseable();
            HttpGet httpGet = new HttpGet(cfg.urlTreino + "/psec/aulas/reset/gympass/" + id);
            httpGet.setHeader("Authorization", cfg.token);
            CloseableHttpResponse response = client.execute(httpGet);
            ResponseHandler<String> handler = new BasicResponseHandler();
            String body = handler.handleResponse(response);
            client.close();
            System.out.println(body);
        }

    }

    public List<JSONObject> obterAulasGympass(Cfgs cfg, Date dia) throws Exception {
        String url = cfg.urlTreino + "/gympass/" + cfg.chave + "/horarios/" + cfg.empresa + "/" + Uteis.getDataAplicandoFormatacao(dia, "ddMMyyyy");
        JSONObject json = new JSONObject(get(url, null, null)).getJSONObject("content");
        Iterator<String> keys = json.keys();
        List<JSONObject> jsons = new ArrayList<>();
        while (keys.hasNext()) {

            String next = keys.next();
            if(next.contains("-INATIVA")){
                continue;
            }
            JSONArray objects = json.optJSONArray(next);
            for (int i = 0; i < objects.length(); i++) {
                JSONObject jsonObject = objects.getJSONObject(i);
                jsonObject.put("nome", next);
                jsons.add(jsonObject);
            }
        }
        return jsons;
    }

    public static void main(String[] args) throws Exception {
        AnalisadorGympass analisadorGympass = new AnalisadorGympass();

//        analisadorGympass.reset(analisadorGympass.cfgs.get(0));

       while (true) {

            try {
                for(Date dia : analisadorGympass.dias){
                    System.out.println("##################");
                    for (Cfgs cfg : analisadorGympass.cfgs) {
                        System.out.println(cfg.chave + " - " + Uteis.getDataAplicandoFormatacao(dia, "dd/MM/yyyy"));
                        JSONArray objects = analisadorGympass.obterAulasPacto(cfg, dia);
                        Map<String, JSONObject> aulasProfPacto = new HashMap<>();
                        for (int i = 0; i < objects.length(); i++) {
                            JSONObject jsonObject = objects.getJSONObject(i);
                            if(!cfg.chave.equals("67f6ab171119549036f6e4d07647d062") || jsonObject.getString("nome").toUpperCase().contains("GYMPASS")){
                                aulasProfPacto.put(jsonObject.getJSONObject("professor").getString("nome") + " - " + jsonObject.getString("horarioInicio"), jsonObject);
                            }
                        }
                        Map<String, JSONObject> aulasProfGympass = new HashMap<>();
                        System.out.println("consultar na gympass");
                        List<JSONObject> objectsGympass = analisadorGympass.obterAulasGympass(cfg, dia);
                        for (JSONObject jsonObject : objectsGympass) {
                            aulasProfGympass.put(jsonObject.getJSONArray("instructors").getJSONObject(0).getString("name") +
                                    " - " + jsonObject.getString("occur_date").split(" ")[1], jsonObject);
                        }
                        List<String> strings = new ArrayList<>(aulasProfPacto.keySet());
                        Collections.sort(strings);
                        int zerados = 0;
                        int naoAchei = 0;
                        int bate = 0;
                        int naoBate = 0;
                        for (String keys : strings) {
                            JSONObject pacto = aulasProfPacto.get(keys);
                            JSONObject gympass = aulasProfGympass.get(keys);

                            if (gympass == null) {
                                naoAchei++;
//                                System.out.println(keys);
//                                System.out.println(pacto.getString("horarioInicio"));
//                                System.out.println(pacto.getString("nome"));
//                                System.out.println("Não achei a aula na Gympass");
                            } else if (gympass.getInt("total_booked") != pacto.getInt("numeroAlunos")) {
                                naoBate++;
//                                System.out.println(" ");
//                                System.out.println(keys);
//                                System.out.println(pacto.getString("horarioInicio"));
//                                System.out.println(pacto.getString("nome"));
//                                System.out.println("capacidade: " + pacto.getInt("capacidade"));
//                                System.out.println("numeroAlunos: " + pacto.getInt("numeroAlunos"));
//                                System.out.println(gympass.getString("nome"));
//                                System.out.println("total_capacity: " + gympass.getInt("total_capacity"));
//                                System.out.println("total_booked: " + gympass.getInt("total_booked"));
                                Integer class_id = Integer.valueOf(gympass.getString("nome").split("\\(id:")[1].replace(")", ""));

                                analisadorGympass.patchSlots(cfg.chave, class_id, String.valueOf(gympass.getInt("id")),
                                        gympass.getInt("total_capacity"), pacto.getInt("numeroAlunos"), cfg.gymPass_id);
                            } else if (pacto.getInt("numeroAlunos") == 0) {
                                zerados++;
                            } else {
                                bate++;
                            }

                        }


                        System.out.println("bate:" + bate);
                        System.out.println("nao bate:" + naoBate);
//                        System.out.println("zerados:" + zerados);
//                        System.out.println("nao achei:" + naoAchei);
                        System.out.println("-------------------------------");
                    }
                }

            }catch (Exception e){
                e.printStackTrace();
            }


        }


    }

    public void patchSlots(String ctx, Integer class_id, String idSlot,
                           Integer total_capacity, Integer total_booked, String gymPass_id) throws Exception {
        try {
            HttpPatch httpPatch = new HttpPatch("https://api.partners.gympass.com" + String.format("/booking/v1/gyms/%s/classes/%s/slots/%s",
                    gymPass_id.trim(), class_id, idSlot));
            JSONObject dto = new JSONObject();
            dto.put("total_capacity", total_capacity);
            dto.put("total_booked", total_booked);
            dto.put("virtual_class_url", "");
            StringEntity entity = new StringEntity(dto.toString(), "UTF-8");
            httpPatch.setEntity(entity);
            httpPatch.setHeader("Content-Type", "application/json");
            httpPatch.setHeader("Authorization", "Bearer " + obterToken());
            HttpClient client = ExecuteRequestHttpService.createConnectorCloseable();
            HttpResponse response = client.execute(httpPatch);

            int statusCode = response.getStatusLine().getStatusCode();
            if (statusCode == 204) {
                System.out.println("sincronizado com sucesso!!!");
                try {
                    TelegramService.enviarMsg(ctx, "patchSlots : params: " + dto + " - sucesso ");
                } catch (Exception e) {
                    e.printStackTrace();
                }
            } else {
                String responseBody = EntityUtils.toString(response.getEntity());
                System.out.println(responseBody);
                throw new Exception(responseBody);
            }


        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    public String obterToken() throws Exception {
        try {

            String data = "grant_type=client_credentials&client_id=pacto&client_secret=f3677289-46ca-4c0f-a270-3aeb262db4af";

            StringEntity entity = new StringEntity(data, CHARSET_UTF8);

            String urlTokenGymPass = Aplicacao.getProp(Aplicacao.urlAPIGymPassBookingToken);

            HttpPost httpPost = new HttpPost(urlTokenGymPass);
            httpPost.setEntity(entity);
            httpPost.setHeader(HEADER_CONTENT_TYPE, APPLICATION_FORM);
            HttpClient client = ExecuteRequestHttpService.createConnectorCloseable();
            HttpResponse response = client.execute(httpPost);

            int statusCode = response.getStatusLine().getStatusCode();
            String responseBody = EntityUtils.toString(response.getEntity());
            if (statusCode != 200) {
                throw new Exception(responseBody);
            }

            JSONObject json = new JSONObject(responseBody);
            if (json.has("access_token")) {
                return json.optString("access_token");
            } else {
                throw new Exception(responseBody);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    private String get(String uri, String token, Integer empresaID) throws Exception {
        CloseableHttpClient client = ExecuteRequestHttpService.createConnectorCloseable();
        HttpGet httpGet = new HttpGet(uri);
        if (token != null) {
            httpGet.setHeader("Authorization", token);
        }
        if (empresaID != null) {
            httpGet.setHeader("empresaId", empresaID.toString());
        }

        CloseableHttpResponse response = client.execute(httpGet);
        ResponseHandler<String> handler = new BasicResponseHandler();
        String body = handler.handleResponse(response);
        client.close();
        return body;
    }
}
