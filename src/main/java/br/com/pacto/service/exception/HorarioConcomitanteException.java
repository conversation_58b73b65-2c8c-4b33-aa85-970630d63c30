package br.com.pacto.service.exception;

public class HorarioConcomitanteException extends ServiceException {
    public HorarioConcomitanteException(String mensagem) {
        super(mensagem);
    }

    public HorarioConcomitanteException(String chaveExcecao, String mensagem) {
        super(chaveExcecao, mensagem);
    }

    public HorarioConcomitanteException(Throwable causa) {
        super(causa);
    }

    public HorarioConcomitanteException(String mensagem, Throwable causa) {
        super(mensagem, causa);
    }

    public HorarioConcomitanteException(String chaveExcecao, String mensagem, Throwable causa) {
        super(chaveExcecao, mensagem, causa);
    }

    public HorarioConcomitanteException(ExcecaoSistema excecaoSistema) {
        super(excecaoSistema);
    }

    public HorarioConcomitanteException(ExcecaoSistema excecaoSistema, Throwable causa) {
        super(excecaoSistema, causa);
    }
}
