package br.com.pacto.service.exception;

@SuppressWarnings("serial")
public class ServiceException extends Exception {

    private String chaveExcecao;
    private Integer codigoError = 0;

    public ServiceException(String mensagem, int... codigoErros) {
        super(mensagem);
        if (codigoErros.length > 0) {
            this.codigoError = codigoErros[0];
        } else {
            this.codigoError = 0;
        }
    }

    /**
     * @param chaveExcecao Chave da exceção mapeada pela implementação de {@link ExcecaoSistema}
     * @param mensagem     Mensagem detalhada da exceção lançada
     */
    public ServiceException(String chaveExcecao, String mensagem) {
        this(mensagem);
        this.chaveExcecao = chaveExcecao;
    }

    public ServiceException(Throwable causa, int... codigoErros) {
        super(causa.getMessage());
        if (codigoErros.length > 0) {
            this.codigoError = codigoErros[0];
        } else {
            this.codigoError = 0;
        }
    }

    public ServiceException(String mensagem, Throwable causa) {
        super(mensagem, causa);
    }

    public ServiceException(String mensagem, Throwable causa, int... codigoErros) {
        super(mensagem, causa);
        if (codigoErros.length > 0) {
            this.codigoError = codigoErros[0];
        } else {
            this.codigoError = 0;
        }
    }

    /**
     * @param chaveExcecao Chave da exceção mapeada pela implementação de {@link ExcecaoSistema}
     * @param mensagem     Mensagem detalhada da exceção lançada
     * @param causa        A causa, o objeto lançado
     */
    public ServiceException(String chaveExcecao, String mensagem, Throwable causa) {
        this(mensagem, causa);
        this.chaveExcecao = chaveExcecao;
    }

    /**
     * @param excecaoSistema Objeto com os dados da exceção lançada
     */
    public ServiceException(ExcecaoSistema excecaoSistema) {
        this(excecaoSistema.getChaveExcecao(), excecaoSistema.getDescricaoExcecao());
    }

    /**
     * @param excecaoSistema Objeto com os dados da exceção lançada
     * @param causa          Causa da exceção
     */
    public ServiceException(ExcecaoSistema excecaoSistema, Throwable causa) {
        this(excecaoSistema.getChaveExcecao(), excecaoSistema.getDescricaoExcecao(), causa);
    }

    public String getChaveExcecao() {
        return chaveExcecao;
    }

    public Integer getCodigoError() {
        return codigoError;
    }
}
