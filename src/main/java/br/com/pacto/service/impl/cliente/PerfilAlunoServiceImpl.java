package br.com.pacto.service.impl.cliente;

import br.com.pacto.bean.avaliacao.PerimetriaEnum;
import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.ficha.Ficha;
import br.com.pacto.bean.ficha.FichaDTO;
import br.com.pacto.bean.programa.ProgramaTreino;
import br.com.pacto.bean.programa.ProgramaTreinoAndamento;
import br.com.pacto.bean.programa.ProgramaTreinoFicha;
import br.com.pacto.bean.serie.TreinoRealizado;
import br.com.pacto.controller.json.aluno.DetalheTreinoAlunoDTO;
import br.com.pacto.controller.json.aluno.FichaExecutada;
import br.com.pacto.dao.intf.cliente.ClienteSinteticoDao;
import br.com.pacto.dao.intf.programa.ProgramaTreinoAndamentoDao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.cliente.perfil.*;
import br.com.pacto.service.intf.cliente.ClienteSinteticoService;
import br.com.pacto.service.intf.cliente.PerfilAlunoService;
import br.com.pacto.service.intf.ficha.FichaService;
import br.com.pacto.service.intf.programa.ProgramaTreinoService;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.ResultSet;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.TextStyle;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;

@Service
public class PerfilAlunoServiceImpl implements PerfilAlunoService {

    @Autowired
    ClienteSinteticoService clienteSinteticoService;
    @Autowired
    SessaoService sessaoService;
    @Autowired
    private ClienteSinteticoDao clienteSinteticoDao;
    @Autowired
    private ProgramaTreinoService programaTreinoService;
    @Autowired
    private FichaService fichaService;

    public PerfilAlunoAvaliacaoDTO avaliacaoFisica(Integer matricula) throws ServiceException{
        PerfilAlunoAvaliacaoDTO perfilAlunoAvaliacaoDTO =
                new PerfilAlunoAvaliacaoDTO(3, 90,
                        20, 1649703163l,
                        31.34, 68.66,
                        38.95, 37.99,
                        25.9,
                        40.99, 45.87,
                        23.22, 31.66,
                        11.66,
                        new ArrayList(){{
                            add(new TreinoGruposMuscularesDTO("Deltóide", 155, 16));
                            add(new TreinoGruposMuscularesDTO("Abdomen", 155, 16));
                            add(new TreinoGruposMuscularesDTO("Anterior da Coxa", 155, 16));
                            add(new TreinoGruposMuscularesDTO("Peitoral", 155, 16));
                            add(new TreinoGruposMuscularesDTO("Triceps", 155, 16));
                            add(new TreinoGruposMuscularesDTO("Panturrilha", 155, 16));
                            add(new TreinoGruposMuscularesDTO("Adutores", 155, 16));
                        }},
                        new ArrayList(){{
                            add(new PesoMassaGordaHistoricoDTO(1649703163l, 68.9, 30.8));
                            add(new PesoMassaGordaHistoricoDTO(1649703163l, 68.9, 30.8));
                            add(new PesoMassaGordaHistoricoDTO(1649703163l, 68.9, 30.8));
                            add(new PesoMassaGordaHistoricoDTO(1649703163l, 68.9, 30.8));
                        }},
                        new ArrayList(){{

                        }}
                        );
        return perfilAlunoAvaliacaoDTO;
    }

    public List<PerimetriaHistoricoAlunoDTO> historicoPerimetria(Integer matricula, PerimetriaEnum perimetria) throws ServiceException{
        return new ArrayList<>();
    }

    @Override
    public FichaDoDiaDTO fichaDoDia(Integer matricula, Boolean buscarNaoVigente, String ctx) throws ServiceException {
        return getFichaDoDiaDTO(matricula, buscarNaoVigente, ctx);
    }


    public ProgramaAtualDTO programaAtual(Integer matricula) throws ServiceException{
        return programaAtual(matricula, true);
    }
    public ProgramaAtualDTO programaAtual(Integer matricula, boolean buscarUltimoSeNenhumVigente) throws ServiceException{
        ProgramaAtualDTO atual = new ProgramaAtualDTO();
        String ctx = sessaoService.getUsuarioAtual().getChave();
        ClienteSintetico cs = clienteSinteticoService.consultarSimplesPorMatricula(ctx, matricula);

        try {
            StringBuilder sqlprograma = new StringBuilder();
            sqlprograma.append("SELECT p.codigo, p.nome, p.totalaulasprevistas \n");
            sqlprograma.append("FROM programatreino p \n");
            sqlprograma.append("LEFT JOIN programatreinoandamento pa ON pa.programa_codigo = p.codigo \n");
            sqlprograma.append("WHERE p.cliente_codigo = ").append(cs.getCodigo()).append(" \n");
            if(buscarUltimoSeNenhumVigente){
                sqlprograma.append("AND '");
                sqlprograma.append(Uteis.getDataAplicandoFormatacao(Calendario.hoje(), "yyyy-MM-dd"));
                sqlprograma.append("' BETWEEN p.datainicio AND p.dataterminoprevisto \n");
            }
            sqlprograma.append("ORDER BY p.dataTerminoPrevisto desc \n");
            sqlprograma.append("LIMIT 1 ");

            try (ResultSet rs = clienteSinteticoDao.createStatement(ctx, sqlprograma.toString())) {
                if (rs.next()) {
                    ProgramaTreinoAndamento pta = UtilContext.getBean(ProgramaTreinoAndamentoDao.class).findObjectByAttributes(ctx, new String[]{"programa.codigo"}, new Object[]{rs.getInt("codigo")}, "codigo");
                    Integer qtExecucoes = programaTreinoService.obterQuantidadeExecucoesTreinoRealizados(ctx, rs.getInt("codigo"));
                    qtExecucoes = qtExecucoes != 0 ? qtExecucoes : (pta != null ? pta.getNrTreinos() : 0);
                    Integer qtFichas = programaTreinoService.obterQuantidadeFichasPorPrograma(ctx, rs.getInt("codigo"));
                    atual.setId(rs.getInt("codigo"));
                    atual.setFichas(qtFichas);
                    Double freq = pta == null ? 0 : pta.getPercentualExecucoesFrequencia(qtExecucoes, rs.getInt("totalaulasprevistas"));
                    atual.setPercentual(freq);
                    atual.setRealizadas(qtExecucoes);
                    atual.setPrevistas(rs.getInt("totalaulasprevistas"));
                    atual.setNome(rs.getString("nome"));
                } else if (buscarUltimoSeNenhumVigente) {
                    atual = programaAtual(matricula, false);
                }
            }
        } catch (Exception e) {
            throw new ServiceException("Erro ao obter o programa atual", e);
        }

        return atual;
    }

    public FichaDoDiaDTO fichaDoDia(Integer matricula, Boolean buscarNaoVigente) throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        return getFichaDoDiaDTO(matricula, buscarNaoVigente, ctx);
    }

        private FichaDoDiaDTO getFichaDoDiaDTO(Integer matricula, Boolean buscarNaoVigente, String ctx) throws ServiceException {
            try {
                ClienteSintetico cs = clienteSinteticoService.consultarSimplesPorMatricula(ctx, matricula);
                ProgramaTreino pt = programaTreinoService.obterProgramaVigente(ctx, cs);

                if (buscarNaoVigente != null && buscarNaoVigente && pt == null) {
                    pt = programaTreinoService.obterUltimoProgramaVigente(ctx, cs);
                }

                FichaDoDiaDTO fichaDoDia = new FichaDoDiaDTO();
                fichaDoDia.setNome("-");
                fichaDoDia.setVezes(0);

                if (pt == null) return fichaDoDia;

                List<ProgramaTreinoFicha> programaFichas = programaTreinoService.obterFichaPorPrograma(ctx, pt.getCodigo(), null, true);

                if (programaFichas == null || programaFichas.isEmpty()) return fichaDoDia;

                Date hoje = Uteis.getDataComHoraZerada(new Date());
                String diaSemana = Uteis.getDiaDaSemana(hoje);

                ProgramaTreinoFicha fichaSelecionada = null;
                ProgramaTreinoFicha fichaMaisAntiga = null;

                for (ProgramaTreinoFicha ptf : programaFichas) {
                    Ficha fichaTemp = ptf.getFicha();
                    if (fichaTemp == null) continue;

                    Date ultimaExecucao = fichaTemp.getUltimaExecucao();
                    String dataExecucao = ultimaExecucao != null ? Uteis.getData(ultimaExecucao, "bd") : null;
                    String dataHoje = Uteis.getData(hoje, "bd");

                    // Prioridade 1: executada hoje
                    if (dataExecucao != null && dataExecucao.equals(dataHoje)) {
                        if (fichaSelecionada == null ||
                                (fichaSelecionada.getFicha().getUltimaExecucao() != null &&
                                        ultimaExecucao != null &&
                                        ultimaExecucao.after(fichaSelecionada.getFicha().getUltimaExecucao()))) {
                            fichaSelecionada = ptf;
                        }
                    }
                    // Prioridade 2: dia da semana atual
                    if (fichaSelecionada == null && ptf.getDiaSemana() != null) {
                        for (String dia : ptf.getDiaSemana()) {
                            if (dia != null && dia.trim().toLowerCase().startsWith(diaSemana.toLowerCase())) {
                                fichaSelecionada = ptf;
                                break;
                            }
                        }
                    }
                    // Prioridade 3: ficha mais antiga ou nunca executada
                    if (fichaMaisAntiga == null ||
                            (ultimaExecucao != null &&
                                    (fichaMaisAntiga.getFicha().getUltimaExecucao() == null ||
                                            ultimaExecucao.before(fichaMaisAntiga.getFicha().getUltimaExecucao())))) {
                        fichaMaisAntiga = ptf;
                    }
                }
                // Se nenhuma ficha executada hoje ou da semana, usa a mais antiga
                if (fichaSelecionada == null) {
                    fichaSelecionada = fichaMaisAntiga;
                }
                if (fichaSelecionada != null) {
                    Ficha ficha = fichaSelecionada.getFicha();
                    fichaDoDia.setId(ficha.getCodigo());
                    fichaDoDia.setNome(ficha.getNome());
                    fichaDoDia.setVezes(programaTreinoService.obterQuantidadeExecucoesFichaAluno(ctx, ficha.getCodigo()));
                }
                return fichaDoDia;
            } catch (Exception ex) {
                throw new ServiceException("Erro ao determinar a ficha do dia: " + ex.getMessage(), ex);
            }
        }

    public FichasRelacionadasDTO fichasRelacionadas(Integer matricula) throws ServiceException{
        String ctx = sessaoService.getUsuarioAtual().getChave();
        ClienteSintetico cs = clienteSinteticoService.consultarPorMatricula(ctx, matricula.toString());
        FichasRelacionadasDTO relacionadasDTO = new FichasRelacionadasDTO();

        relacionadasDTO.setIdProxima(null);
        relacionadasDTO.setNomeProxima("-");
        relacionadasDTO.setIdUltima(null);
        relacionadasDTO.setNomeUltima("-");

        List<TreinoRealizado> ultimoTreinoRealizado = programaTreinoService.obterUltimosTreinosRealizadosCliente(ctx, cs.getMatricula(), 1);
        if (!UteisValidacao.emptyList(ultimoTreinoRealizado)) {
            FichaExecutada ultima = new FichaExecutada(ultimoTreinoRealizado.get(0).getProgramaTreinoFicha().getFicha());
            relacionadasDTO.setIdUltima(ultima.getId());
            relacionadasDTO.setNomeUltima(ultima.getNome());
        }

        ProgramaTreino pt = programaTreinoService.obterProgramaVigente(ctx, cs);
        if (pt != null && pt.getCodigo() != null) {
            Ficha proximaFicha = obterProximaFicha(ctx, pt);
            if (proximaFicha != null) {
                relacionadasDTO.setIdProxima(proximaFicha.getCodigo());
                relacionadasDTO.setNomeProxima(proximaFicha.getNome());
            }
        }

        return relacionadasDTO;
    }

    public Ficha obterProximaFicha(String ctx, ProgramaTreino pt) throws ServiceException {
        Integer fichaId = programaTreinoService.obterFichaAtual(ctx, pt.getCodigo(), null);
        if (!UteisValidacao.emptyNumber(fichaId)) {
            Ficha fichaAtual = fichaService.obterPorId(ctx, fichaId);
            List<ProgramaTreinoFicha> fichas = programaTreinoService.obterFichaPorPrograma(ctx, pt.getCodigo(), null, false);

            // 1º se a lista só tem 1 ficha, essa ficha deve ser retornada como próxima
            if (fichas.size() == 1) {
                return fichas.get(0).getFicha();
            }
            for (int i = 0; i < fichas.size(); i++) {
                // 2º a próxima deve ser a ficha de código maior na lista de fichas do programa em relação a ficha atual
                if (fichas.get(i).getFicha().getCodigo() > fichaAtual.getCodigo()) {
                    return fichas.get(i).getFicha();
                }
                // 3º se chegar na última ficha da lista, retorna a primeira ficha da lista como a próxima
                if (i == (fichas.size() - 1)) {
                    return fichas.get(0).getFicha();
                }
            }
        }
        // 4º se não chegou a nenhum return acima é porque não tem ficha
        return null;
    }

    @Override
    public DiasQueTreinouProgramaAtualDTO diasQueTreinouProgramaAtual(Integer matricula) throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        ClienteSintetico cs = clienteSinteticoService.consultarPorMatricula(ctx, matricula.toString());
        DetalheTreinoAlunoDTO detalheTreinoAlunoDTO = programaTreinoService.detalheTreinamentoCliente(cs.getCodigo());
        DiasQueTreinouProgramaAtualDTO dias = new DiasQueTreinouProgramaAtualDTO();
        Integer totalTreinoRealizado = detalheTreinoAlunoDTO.getTotalTreinoRealizado() != null ? detalheTreinoAlunoDTO.getTotalTreinoRealizado() : 0;
        dias.setDomingo(detalheTreinoAlunoDTO.getDomingo().getPorcentagemExecutada() * totalTreinoRealizado);
        dias.setSegunda(detalheTreinoAlunoDTO.getSegunda().getPorcentagemExecutada() * totalTreinoRealizado);
        dias.setTerca(detalheTreinoAlunoDTO.getTerca().getPorcentagemExecutada() * totalTreinoRealizado);
        dias.setQuarta(detalheTreinoAlunoDTO.getQuarta().getPorcentagemExecutada() * totalTreinoRealizado);
        dias.setQuinta(detalheTreinoAlunoDTO.getQuinta().getPorcentagemExecutada() * totalTreinoRealizado);
        dias.setSexta(detalheTreinoAlunoDTO.getSexta().getPorcentagemExecutada() * totalTreinoRealizado);
        dias.setSabado(detalheTreinoAlunoDTO.getSabado().getPorcentagemExecutada() * totalTreinoRealizado);
        dias.setTreinosExecutadosPeriodo(totalTreinoRealizado);

        return dias;
    }

    @Override
    public HorariosQueTreinouProgramaAtual horariosQueTreinouProgramaAtual(Integer matricula) throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        HorariosQueTreinouProgramaAtual horariosTreinos = programaTreinoService.obterHorariosQueTreinouProgramaAtual(ctx, matricula);

        return horariosTreinos;
    }

}
