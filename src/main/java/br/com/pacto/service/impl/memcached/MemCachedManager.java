package br.com.pacto.service.impl.memcached;

import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.intf.memcached.CachedManagerInterfaceFacade;
import br.com.pacto.util.UteisValidacao;
import net.spy.memcached.AddrUtil;
import net.spy.memcached.ConnectionFactoryBuilder;
import net.spy.memcached.FailureMode;
import net.spy.memcached.MemcachedClient;
import net.spy.memcached.MemcachedNode;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

@Service
public final class MemCachedManager implements CachedManagerInterfaceFacade {

    private static MemCachedManager INSTANCE;
    private MemcachedClient memcachedClient;
    private static final int TEMPOEXPIRAR = 10800;

    private MemCachedManager() {
        instanciarMemcachedClient();
    }

    private void instanciarMemcachedClient() {
        try {
            String ipServidorMemcached = Aplicacao.getProp(Aplicacao.ipServidoresMemCached);
            if (UteisValidacao.emptyString(ipServidorMemcached)
                    || ipServidorMemcached.equalsIgnoreCase("DISABLED")
                    || ipServidorMemcached.equalsIgnoreCase("@SERVIDOR_MEMCACHED@")) {
                Uteis.logar(null, "SERVIDOR MEMCACHED NÃO CONFIGURADO. FAÇA A CONFIGURAÇÃO DA TAG ipServidoresMemCached EM cfgBD.xml ");
            } else {
                memcachedClient = new MemcachedClient(
                        new ConnectionFactoryBuilder().setDaemon(true).
                                setOpTimeout(5000).
                                setFailureMode(FailureMode.Retry).build(),
                        AddrUtil.getAddresses(ipServidorMemcached));
                gravar("ON", "-", TEMPOEXPIRAR, "USO_MEMCACHED_STATUS", null, null);
            }
        } catch (java.lang.Exception e) {
            Logger.getLogger(MemCachedManager.class.getName()).log(Level.SEVERE, "Não foi possível carregar a configuração dos servidores menCached. Erro:" + e.getMessage(), e);
        }
    }

    public void gravar(String key, String identificador, Object obj, EntidadeCacheEnum entidade, String matricula) {
        gravar(key, obj, TEMPOEXPIRAR, identificador, entidade, matricula);
    }
    public void gravar(String key, String identificador, Object obj, EntidadeCacheEnum entidade) {
        gravar(key, obj, TEMPOEXPIRAR, identificador, entidade, null);
    }

    public void gravar(String key, String identificador, Object obj) {
        gravar(key, obj, TEMPOEXPIRAR, identificador, null, null);
    }

    public void gravar(String key, Object obj, int tempoExpirar, String identificador, EntidadeCacheEnum entidade, String matricula) {
        try {
            String chaveComIdentificador = getChaveComIdentificador(identificador, key);
            if (this.memcachedClient != null && obj != null) {
                this.memcachedClient.set(chaveComIdentificador, tempoExpirar, obj);
            }
            if(entidade != null){
                lembrarIdentificador(key, identificador, entidade, matricula);
            }
        } catch (Exception ex) {
            Logger.getLogger(MemCachedManager.class.getName())
                    .log(Level.WARNING, "Falha ao gravar objeto na MemCached", ex);
        }
    }

    private void lembrarIdentificador(String key, String identificador, EntidadeCacheEnum entidade, String matricula){
        try {

            if (this.memcachedClient != null) {
                Map<String, Map<EntidadeCacheEnum, List<String>>> chavesEntidades = new HashMap<>();
                Object mapKeys = ler("-", "mapKeys");
                if(mapKeys != null){
                    chavesEntidades = (Map<String, Map<EntidadeCacheEnum, List<String>>>) mapKeys;
                }
                String composedKey = key + (matricula == null ? "" : ("-" + matricula));
                Map<EntidadeCacheEnum, List<String>> entidadeCacheEnumListMap = chavesEntidades.get(composedKey);
                if(entidadeCacheEnumListMap == null){
                    entidadeCacheEnumListMap = new HashMap<>();
                    chavesEntidades.put(composedKey, entidadeCacheEnumListMap);
                }
                List<String> chaves = entidadeCacheEnumListMap.get(entidade);
                if(chaves == null){
                    chaves = new ArrayList<>();
                    entidadeCacheEnumListMap.put(entidade, chaves);
                }
                chaves.add(identificador);
                this.memcachedClient.set(getChaveComIdentificador("mapKeys", "-"), TEMPOEXPIRAR, chavesEntidades);
            }
        } catch (Exception ex) {
            Logger.getLogger(MemCachedManager.class.getName())
                    .log(Level.WARNING, "Falha ao gravar objeto na MemCached", ex);
        }
    }

    private void esquecerIdentificador(String key, String identificador, EntidadeCacheEnum entidade, String matricula){
        try {
            if (this.memcachedClient != null) {
                Map<String, Map<EntidadeCacheEnum, List<String>>> chavesEntidades = new HashMap<>();
                Object mapKeys = ler("-", "mapKeys");
                if(mapKeys != null){
                    chavesEntidades = (Map<String, Map<EntidadeCacheEnum, List<String>>>) mapKeys;
                }
                String composedKey = key + (matricula == null ? "" : ("-" + matricula));
                Map<EntidadeCacheEnum, List<String>> entidadeCacheEnumListMap = chavesEntidades.get(composedKey);
                if(entidadeCacheEnumListMap == null){
                    entidadeCacheEnumListMap = new HashMap<>();
                    chavesEntidades.put(composedKey, entidadeCacheEnumListMap);
                }
                List<String> chaves = entidadeCacheEnumListMap.get(entidade);
                if(chaves == null){
                    chaves = new ArrayList<>();
                    entidadeCacheEnumListMap.put(entidade, chaves);
                }
                chaves.remove(identificador);
                this.memcachedClient.set(getChaveComIdentificador("mapKeys", "-"), TEMPOEXPIRAR, chavesEntidades);
            }
        } catch (Exception ex) {
            Logger.getLogger(MemCachedManager.class.getName())
                    .log(Level.WARNING, "Falha ao gravar objeto na MemCached", ex);
        }
    }

    public <T> T ler(String key, String identificador) {
        try {
            if (this.memcachedClient != null) {
                return (T) this.memcachedClient.get(getChaveComIdentificador(identificador, key));
            }
        } catch (Exception ex) {
            Logger.getLogger(MemCachedManager.class.getName())
                    .log(Level.WARNING, "Falha ao ler objeto da MemCached", ex);
        }
        return null;
    }

    public void removerMatricula(String key, String matricula) {
        try {
            if (this.memcachedClient != null) {
                Map<String, Map<EntidadeCacheEnum, List<String>>> chavesEntidades = new HashMap<>();
                Object mapKeys = ler("-", "mapKeys");
                if(mapKeys != null){
                    chavesEntidades = (Map<String, Map<EntidadeCacheEnum, List<String>>>) mapKeys;
                }
                Map<EntidadeCacheEnum, List<String>> entidadeCacheEnumListMap = chavesEntidades.get(key + "-" + matricula);
                if(entidadeCacheEnumListMap == null){
                    entidadeCacheEnumListMap = new HashMap<>();
                    chavesEntidades.put(key, entidadeCacheEnumListMap);
                }
                for(EntidadeCacheEnum entidade :entidadeCacheEnumListMap.keySet()){
                    List<String> chaves = entidadeCacheEnumListMap.get(entidade);
                    for(String chave : chaves){
                        remover(key, chave, entidade, matricula);
                    }
                }
            }
        } catch (Exception ex) {
            Logger.getLogger(MemCachedManager.class.getName())
                    .log(Level.WARNING, "Falha ao remover objeto da MemCached", ex);
        }
    }

    public void removerEntidade(String key, EntidadeCacheEnum entidade) {
        try {
            if (this.memcachedClient != null) {
                Map<String, Map<EntidadeCacheEnum, List<String>>> chavesEntidades = new HashMap<>();
                Object mapKeys = ler("-", "mapKeys");
                if(mapKeys != null){
                    chavesEntidades = (Map<String, Map<EntidadeCacheEnum, List<String>>>) mapKeys;
                }
                Map<EntidadeCacheEnum, List<String>> entidadeCacheEnumListMap = chavesEntidades.get(key);
                if(entidadeCacheEnumListMap == null){
                    entidadeCacheEnumListMap = new HashMap<>();
                    chavesEntidades.put(key, entidadeCacheEnumListMap);
                }
                List<String> chaves = entidadeCacheEnumListMap.get(entidade);
                if(chaves == null){
                    chaves = new ArrayList<>();
                    entidadeCacheEnumListMap.put(entidade, chaves);
                }
                for(String chave : chaves){
                    remover(key, chave, entidade, null);
                }
                for(String chave : chavesEntidades.keySet()){
                    if(chave.startsWith(key)){
                        entidadeCacheEnumListMap = chavesEntidades.get(chave);
                        List<String> chavesMtr = entidadeCacheEnumListMap.get(entidade);
                        if(chaves == null){
                            chaves = new ArrayList<>();
                            entidadeCacheEnumListMap.put(entidade, chaves);
                        }
                        for(String chaveMtr : chavesMtr){
                            remover(chave, chaveMtr, entidade, null);
                        }
                    }
                }
            }
        } catch (Exception ex) {
            Logger.getLogger(MemCachedManager.class.getName())
                    .log(Level.WARNING, "Falha ao remover objeto da MemCached", ex);
        }
    }

    public void remover(String key, String identificador) {
        remover(key, identificador, null, null);
    }

    public void remover(String key, String identificador, EntidadeCacheEnum entidade, String matricula) {
        try {
            if (this.memcachedClient != null) {
                this.memcachedClient.delete(getChaveComIdentificador(identificador, key));
            }
            if(entidade != null){
                esquecerIdentificador(key, identificador, entidade, matricula);
            }
        } catch (Exception ex) {
            Logger.getLogger(MemCachedManager.class.getName())
                    .log(Level.WARNING, "Falha ao remover objeto da MemCached", ex);
        }
    }

    public boolean getMemcachedOn(){
        try {
           for(MemcachedNode node : getInstance().getMemcachedClient().getNodeLocator().getAll()){
               if(node.isActive()){
                   return true;
               }
           }
            return false;
        }catch (Exception e){
            return false;
        }
    }


    private String getChaveComIdentificador(String identificador, String key) {
        return "["+key +"]["+ identificador+"]";
    }

    public static MemCachedManager getInstance(){
        if (INSTANCE == null)
            INSTANCE = new MemCachedManager();
        return INSTANCE;
    }

    public MemcachedClient getMemcachedClient() {
        return memcachedClient;
    }

    public Map<String, Map<EntidadeCacheEnum, List<String>>> mapaCache() {
        try {
            if (this.memcachedClient != null) {
                Map<String, Map<EntidadeCacheEnum, List<String>>> chavesEntidades = new HashMap<>();
                Object mapKeys = ler("-", "mapKeys");
                if(mapKeys != null){
                    chavesEntidades = (Map<String, Map<EntidadeCacheEnum, List<String>>>) mapKeys;
                }
                return chavesEntidades;
            }
        } catch (Exception ex) {
            Logger.getLogger(MemCachedManager.class.getName())
                    .log(Level.WARNING, "Falha ao remover objeto da MemCached", ex);
        }
        return new HashMap<>();
    }

    public void invalidarCacheAgenda(String key){
        invalidarCache(key, EntidadeCacheEnum.TURMA_MODALIDADE_ALUNO, null);
        invalidarCache(key, EntidadeCacheEnum.PROXIMAS_AULAS, null);
        invalidarCache(key, EntidadeCacheEnum.AGENDAMENTOS, null);
        invalidarCache(key, EntidadeCacheEnum.PROXIMAS_AULAS_COLETIVAS, null);
    }

    public void invalidarCacheAgendados(String key){
        invalidarCache(key, EntidadeCacheEnum.AGENDADOS, null);
        invalidarCache(key, EntidadeCacheEnum.REPOSICOES, null);
        invalidarCache(key, EntidadeCacheEnum.DESMARCADOS, null);
        invalidarCache(key, EntidadeCacheEnum.AGENDAMENTOS, null);
        invalidarCache(key, EntidadeCacheEnum.PROXIMAS_AULAS_COLETIVAS, null);
    }

    public void invalidarCache(String key, EntidadeCacheEnum entidadeCacheEnum, Integer matricula){
        try {
            if(entidadeCacheEnum != null){
                removerEntidade(key, entidadeCacheEnum);
            }
            if(matricula != null){
                removerMatricula(key, matricula.toString());
            }
        }catch (Exception e){
            Uteis.logar(e, MemCachedManager.class);
        }
    }

    public void clear(){
        if (this.memcachedClient != null) {
            this.memcachedClient.flush().isDone();
        }
    }

    public void enable(){
        instanciarMemcachedClient();
    }

    public void disable(){
        this.memcachedClient = null;
    }

}
