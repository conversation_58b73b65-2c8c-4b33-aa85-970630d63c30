package br.com.pacto.service.impl.notificacao.excecao;

import br.com.pacto.service.exception.ExcecaoSistema;

/**
 * Created by paulo 09/11/2018
 */
public enum VentilometriaExcecoes implements ExcecaoSistema {

    ERRO_BUSCAR_VENTILOMETRIA("erro_buscar_ventilometris", "Erro ao buscar ventilometria")
    ;

    private String chave;
    private String descricao;

    VentilometriaExcecoes(String chave, String descricao) {
        this.chave = chave;
        this.descricao = descricao;
    }

    @Override
    public String getChaveExcecao() {
        return chave;
    }

    @Override
    public String getDescricaoExcecao() {
        return descricao;
    }
}
