package br.com.pacto.service.impl.avaliacao;

import br.com.pacto.bean.avaliacao.AvaliacaoFisica;
import br.com.pacto.objeto.Uteis;

import java.math.BigDecimal;

/**
 * Created by <PERSON><PERSON> Alcides on 06/06/2017.
 */
public class ProtocoloWeltmanObeso {

    public static AvaliacaoFisica calculaDados(AvaliacaoFisica avaliacao){
        if (!isCalcularDados(avaliacao)) {
            return avaliacao;
        }
        Double percGordura;
        if (avaliacao.getCliente().isSexoMasculino()) {
            //Homens: G% = 0.31457 x (circunf. abdominal) – 0.10969 x (massa corporal) + 10.8336
            percGordura = (0.31457 * avaliacao.getCircunferenciaAbdominal()) - (0.10969 * avaliacao.getPeso()) + 10.8336;
        } else {
            //Mulheres: G% = 0.11077 x (circunf. abdominal) – 0.17666 x (estatura) + 0.14354 x (massa corporal) + 51.03301
            percGordura = (0.11077 * (avaliacao.getCircunferenciaAbdominal())) - (0.17666 * (avaliacao.getAltura() * 100)) + (0.14354 * (avaliacao.getPeso())) + 51.03301;
        }
        percGordura = percGordura < 0.0 ? 0.0 : percGordura;
        Double massaGorda = (avaliacao.getPeso()*percGordura)/100;
        Double massaMagra = avaliacao.getPeso() - massaGorda;
        avaliacao.setPercentualGordura(Uteis.forcarCasasDecimais(2, new BigDecimal(percGordura)));
        avaliacao.setMassaGorda(Uteis.forcarCasasDecimais(2, new BigDecimal(massaGorda)));
        avaliacao.setMassaMagra(Uteis.forcarCasasDecimais(2, new BigDecimal(massaMagra)));
        avaliacao.setTotalDobras(avaliacao.getCircunferenciaAbdominal());
        return avaliacao;
    }



    public static boolean isCalcularDados(AvaliacaoFisica a) {
        if (a.getCliente().isSexoMasculino()) {
            return a.getCircunferenciaAbdominal() > 0.0 && a.getPeso() > 0.0;
        } else {
            return a.getCircunferenciaAbdominal() > 0.0 && a.getPeso() > 0.0 && a.getAltura() > 0.0;
        }
    }
}
