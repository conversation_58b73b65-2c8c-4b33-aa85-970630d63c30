package br.com.pacto.service.impl.atividade;

public class SerieDTO {
    private String valor1;
    private String repeticaoComp;
    private String valor2;
    private String valor3;
    private String cargaComp;
    private String descanso;
    private Integer codSerie;
    private String complemento;
    private String cadencia;
    private Integer ordem;
    private Integer repeticao;
    private Double carga;
    private Integer duracao;
    private Integer distancia;
    private Double velocidade;
    private Integer codAtividade;
    private Integer tipoAtividade;
    private String cargaApp;
    private String repeticaoApp;

    public String getValor1() {
        return valor1;
    }

    public void setValor1(String valor1) {
        this.valor1 = valor1;
    }

    public String getRepeticaoComp() {
        return repeticaoComp;
    }

    public void setRepeticaoComp(String repeticaoComp) {
        this.repeticaoComp = repeticaoComp;
    }

    public String getValor2() {
        return valor2;
    }

    public void setValor2(String valor2) {
        this.valor2 = valor2;
    }

    public String getValor3() {
        return valor3;
    }

    public void setValor3(String valor3) {
        this.valor3 = valor3;
    }

    public String getCargaComp() {
        return cargaComp;
    }

    public void setCargaComp(String cargaComp) {
        this.cargaComp = cargaComp;
    }

    public String getDescanso() {
        return descanso;
    }

    public void setDescanso(String descanso) {
        this.descanso = descanso;
    }

    public Integer getCodSerie() {
        return codSerie;
    }

    public void setCodSerie(Integer codSerie) {
        this.codSerie = codSerie;
    }

    public String getComplemento() {
        return complemento;
    }

    public void setComplemento(String complemento) {
        this.complemento = complemento;
    }

    public String getCadencia() {
        return cadencia;
    }

    public void setCadencia(String cadencia) {
        this.cadencia = cadencia;
    }

    public Integer getOrdem() {
        return ordem;
    }

    public void setOrdem(Integer ordem) {
        this.ordem = ordem;
    }

    public Integer getRepeticao() {
        return repeticao;
    }

    public void setRepeticao(Integer repeticao) {
        this.repeticao = repeticao;
    }

    public Double getCarga() {
        return carga;
    }

    public void setCarga(Double carga) {
        this.carga = carga;
    }

    public Integer getDuracao() {
        return duracao;
    }

    public void setDuracao(Integer duracao) {
        this.duracao = duracao;
    }

    public Integer getDistancia() {
        return distancia;
    }

    public void setDistancia(Integer distancia) {
        this.distancia = distancia;
    }

    public Double getVelocidade() {
        return velocidade;
    }

    public void setVelocidade(Double velocidade) {
        this.velocidade = velocidade;
    }

    public Integer getCodAtividade() {
        return codAtividade;
    }

    public void setCodAtividade(Integer codAtividade) {
        this.codAtividade = codAtividade;
    }

    public Integer getTipoAtividade() {
        return tipoAtividade;
    }

    public void setTipoAtividade(Integer tipoAtividade) {
        this.tipoAtividade = tipoAtividade;
    }

    public String getCargaApp() {
        return cargaApp;
    }

    public void setCargaApp(String cargaApp) {
        this.cargaApp = cargaApp;
    }

    public String getRepeticaoApp() {
        return repeticaoApp;
    }

    public void setRepeticaoApp(String repeticaoApp) {
        this.repeticaoApp = repeticaoApp;
    }
}
