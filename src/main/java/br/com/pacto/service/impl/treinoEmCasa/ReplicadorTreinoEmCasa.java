package br.com.pacto.service.impl.treinoEmCasa;

import br.com.pacto.base.util.ExecuteRequestHttpService;
import br.com.pacto.service.intf.treinoEmCasa.TreinoEmCasaReplicadoJSON;
import org.apache.http.client.ResponseHandler;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.BasicResponseHandler;
import org.apache.http.impl.client.CloseableHttpClient;
import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

public class ReplicadorTreinoEmCasa {

    public List<TreinoEmCasaReplicadoJSON> replicar(final String chave, final String keyTreino){
        try {
            List<TreinoEmCasaReplicadoJSON> unidades = obterRede(chave);
//            List<TreinoEmCasaReplicadoJSON> unidades = new ArrayList<>();
//            TreinoEmCasaReplicadoJSON unidade = new TreinoEmCasaReplicadoJSON();
//            unidade.setUnidade("TREINAMENTO");
//            unidade.setChave("7ed9cc4b88b571c9382066b559c42b25");
//            unidades.add(unidade);
            JSONObject infoApp = getInfoApp(chave);
            JSONObject treinoEmCasa = treinoEmCasa(infoApp.getString("documentID"), keyTreino);
            treinoEmCasa.put("nomeProfessor", treinoEmCasa.get("nomeProfessor") + " (" + infoApp.getString("nomeDoApp") + ")");
            for(TreinoEmCasaReplicadoJSON u : unidades){
                gravarTreino(getInfoApp(u.getChave()).getString("documentID"), treinoEmCasa);
            }
            return unidades;
        }catch (Exception e){
            e.printStackTrace();
        }
        return null;
    }

    private void gravarTreino(final String documentId, final JSONObject treino) throws Exception{
        treino.put("clienteApp", documentId);
        String documentKeyLocal = documentKeyLocal(documentId, treino.getString("nomeDoTreino"));
        if(documentKeyLocal == null){
            treino.remove("documentKey");
        } else {
            treino.put("documentKey", documentKeyLocal);
        }
        post("https://us-central1-app-do-aluno-unificado.cloudfunctions.net/treinoSimples/salvarTreinoSimples",
                treino);
    }

    private String documentKeyLocal(final String documentId, final String nomeTreino) throws Exception{
        String get = get("https://us-central1-app-do-aluno-unificado.cloudfunctions.net/treinoSimples/consultarTreinosSimples?refUsuario" +
                "&clienteApp="+documentId);
        JSONArray treinos = new JSONObject(get).optJSONArray("sucesso");
        for(int i = 0; i < treinos.length(); i++){
            JSONObject treino = treinos.getJSONObject(i);
            if(nomeTreino.equals(treino.getString("nomeDoTreino"))){
                return treino.getString("documentKey");
            }
        }
        return null;
    }
    public JSONObject treinoEmCasa(final String documentId, final String keyTreino) throws Exception{
        String get = get("https://us-central1-app-do-aluno-unificado.cloudfunctions.net/treinoSimples/consultarTreinosSimples?refUsuario" +
                "&clienteApp="+documentId);
        JSONArray treinos = new JSONObject(get).optJSONArray("sucesso");
        for(int i = 0; i < treinos.length(); i++){
            JSONObject treino = treinos.getJSONObject(i);
            if(keyTreino.equals(treino.getString("documentKey"))){
                System.out.println(treino);
                return treino;
            }
        }
        return null;
    }


    private List<TreinoEmCasaReplicadoJSON> obterRede(final String chave) throws Exception{
        List<TreinoEmCasaReplicadoJSON> rede = new ArrayList<>();
        System.out.println("buscar rede.");
        String post = post("https://app.pactosolucoes.com.br/oamd/prest/empresa/dadosRede?chave=" + chave, null);

        JSONObject postJSON = new JSONObject(post);
        JSONArray empresas = postJSON.getJSONArray("empresas");
        for(int i = 0; i < empresas.length(); i++){
            try {
                JSONObject unidade = empresas.getJSONObject(i);
                if(!unidade.getString("chave").equals(chave)){
                    TreinoEmCasaReplicadoJSON tr = new TreinoEmCasaReplicadoJSON();
                    tr.setChave(unidade.getString("chave"));
                    tr.setUnidade(unidade.getString("nome"));
                    System.out.println(unidade.getString("chave") + " - " + unidade.getString("nome"));
                    rede.add(tr);
                }
            }catch (Exception e){
                e.printStackTrace();
            }
        }
        System.out.println("rede obtida.");
        return rede;
    }

    private JSONObject getInfoApp(String chave) throws Exception{
        String s = get("https://us-central1-app-do-aluno-unificado.cloudfunctions.net/getDocumentKey?key=" + chave);
        return new JSONObject(s).getJSONObject("sucesso");
    }


    public static void main(String[] args) {
        ReplicadorTreinoEmCasa replicador = new ReplicadorTreinoEmCasa();
        replicador.replicar("28e67e434e532d0057a64b7471551cf9", "YgFrTpbSFGrvLr6Vwt1h");
    }

    private String get(String uri) throws Exception {
        CloseableHttpClient client = ExecuteRequestHttpService.createConnectorCloseable();
        HttpGet httpGet = new HttpGet(uri);
        CloseableHttpResponse response = client.execute(httpGet);
        ResponseHandler<String> handler = new BasicResponseHandler();
        String body = handler.handleResponse(response);
        client.close();
        return body;
    }

    private String post(String uri, final JSONObject corpo) throws Exception {
        CloseableHttpClient client = ExecuteRequestHttpService.createConnectorCloseable();
        HttpPost httpPost = new HttpPost(uri);
        httpPost.setHeader("Content-type", "application/json");
        if(corpo != null){
            StringEntity entity = new StringEntity(corpo.toString(), "UTF-8");
            httpPost.setEntity(entity);
        }
        CloseableHttpResponse response = client.execute(httpPost);
        ResponseHandler<String> handler = new BasicResponseHandler();
        String body = handler.handleResponse(response);
        client.close();
        return body;
    }


}
