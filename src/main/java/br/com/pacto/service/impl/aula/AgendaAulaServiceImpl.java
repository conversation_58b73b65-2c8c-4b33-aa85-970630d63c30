/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.impl.aula;

import br.com.pacto.bean.aula.AgendaAulaTO;
import br.com.pacto.bean.aula.AulaDia;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.dao.intf.aula.AulaDao;
import br.com.pacto.dao.intf.aula.AulaDiaDao;
import br.com.pacto.dao.intf.aula.AulaHorarioDao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.aula.AgendaAulaService;
import br.com.pacto.service.intf.cliente.ClienteSinteticoService;
import br.com.pacto.util.bean.FiltrosAgendaTO;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import br.com.pacto.controller.to.ScheduleModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.test.context.ContextConfiguration;

/**
 *
 * <AUTHOR>
 */
@Service
@ContextConfiguration(locations = {"/applicationContext.xml"})
public class AgendaAulaServiceImpl implements AgendaAulaService{
    
    @Autowired
    private AulaDao aulaDao;
    @Autowired
    private AulaDiaDao aulaDiaDao;
    @Autowired
    private AulaHorarioDao aulaHorarioDao;
    @Autowired
    private ClienteSinteticoService clienteService;
    
    public AulaHorarioDao getAulaHorarioDao() {
        return aulaHorarioDao;
    }
    
    public AulaDao getAulaDao() {
        return aulaDao;
    }

    public AulaDiaDao getAulaDiaDao() {
        return aulaDiaDao;
    }
    
    
    @Override
    public Map<Date, List<AulaDia>> montarAgenda(String ctx, ScheduleModel eventModel, Usuario usuario, FiltrosAgendaTO filtros,
            Map<Date, List<AulaDia>> aulas) throws ServiceException {
        try {
            eventModel.clear();
            String codigosProfessores = filtros.getCodigoProfessorSelecionado() == null || filtros.getCodigoProfessorSelecionado() == 0 
                    ? ""
                    : filtros.getCodigoProfessorSelecionado().toString();
            List<AulaDia> consultarPorData = consultarPorData(ctx, Calendario.getDataComHoraZerada(filtros.getInicio()),
                    Calendario.getDataComHora(filtros.getFim(), "23:59:59"),usuario.getEmpresaZW(),codigosProfessores, 
                    filtros.getModalidades(), filtros.getAmbientes(), null);
            aulas = montarMapaAgendamentos(consultarPorData);
            for(AulaDia aula : consultarPorData){
                eventModel.addEvent(new AgendaAulaTO(aula.getTitle(filtros.isVisaoMensal()),
                        aula.getInicio(), aula.getFim(), aula.getAula().getModalidade().getNomeCss(), aula));
            }
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
        return aulas;
    }
    
    public List<AulaDia> consultarPorData(final String ctx, Date inicio, Date fim,
            final Integer empresaZW, final String professores,
            final String modalidades, 
            final String ambientes, 
            final String orderBy) throws ServiceException {
        try {
            HashMap<String, Object> p = new HashMap<String, Object>();
            StringBuilder query = new StringBuilder();
            query.append(" select obj from AulaDia obj where obj.inicio >= :inicio and obj.fim <= :fim");
            if (professores != null && !professores.isEmpty()) {
                query.append(" and obj.aula.professor.codigo IN (").append(professores).append(") ");
            }
            
            if (modalidades != null && !modalidades.isEmpty()) {
                query.append(" and obj.aula.modalidade.codigo IN (").append(modalidades).append(") ");
            }
            
            if (ambientes != null && !ambientes.isEmpty()) {
                query.append(" and obj.aula.ambiente.codigo IN (").append(ambientes).append(") ");
            }
            query.append(orderBy == null ? " order by obj.inicio " : query.append(" order by ").append(orderBy));
            p.put("inicio", inicio);
            p.put("fim", fim);
            return obterPorParam(ctx, query.toString(), p);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }
    
    public List<AulaDia> obterPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException {
        try {
            return getAulaDiaDao().findByParam(ctx, query, params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }
    
    public Map<Date, List<AulaDia>> montarMapaAgendamentos(List<AulaDia> listaAula) {
        Map<Date, List<AulaDia>> mapaAulas = new HashMap<Date, List<AulaDia>>();
        for (AulaDia aula : listaAula) {
            adicionarAoMapaAgendamentos(mapaAulas, aula);
        }
        return mapaAulas;
    }
    
    private void adicionarAoMapaAgendamentos(Map<Date, List<AulaDia>> aulas, AulaDia aula) {
        List<AulaDia> lista = aulas.get(Calendario.getDataComHoraZerada(aula.getInicio()));
        if (lista == null) {
            lista = new ArrayList<AulaDia>();
            lista.add(aula);
            aulas.put(Calendario.getDataComHoraZerada(aula.getInicio()), lista);
        } else {
            lista.add(aula);
        }
    }
 
}
