package br.com.pacto.service.impl.agenda;

import br.com.pacto.service.intf.empresa.EmpresaVO;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.json.JSONObject;

@JsonIgnoreProperties(ignoreUnknown = true)
public class AutorizacaoAcessoGrupoEmpresarialVO {

    private String codAcesso;
    private String codigoAutorizacao;
    private Integer codigo;
    private Integer codigoMatricula;
    private String tipoPessoa = "";
    private String nomePessoa;
    private Integer codigoPessoa;
    private Integer codigoGenerico = 0;
    private String senhaAcesso = "";
    private String codAcessoAlternativo = "";
    private String fotoKey = "";
    private String assinaturaBiometriaFacial;
    private String assinaturaBiometriaDigital;
    private String cpf;
    private IntegracaoAcessoGrupoEmpresarialVO integracao;

    public AutorizacaoAcessoGrupoEmpresarialVO(){}

    public AutorizacaoAcessoGrupoEmpresarialVO(JSONObject autorizacaoJson){
        setCodAcesso(autorizacaoJson.getString("codAcesso"));
        setCodigoAutorizacao(autorizacaoJson.getString("codigoAutorizacao"));
        setCodigo(autorizacaoJson.getInt("codigo"));
        setCodigoMatricula(autorizacaoJson.getInt("codigoMatricula"));
        setTipoPessoa(autorizacaoJson.getString("tipoPessoa"));
        setNomePessoa(autorizacaoJson.getString("nomePessoa"));
        setCodigoPessoa(autorizacaoJson.getInt("codigoPessoa"));
        setCodigoGenerico(autorizacaoJson.getInt("codigoGenerico"));
        setSenhaAcesso(autorizacaoJson.getString("senhaAcesso"));
        setFotoKey(autorizacaoJson.getString("fotoKey"));
        setAssinaturaBiometriaDigital(autorizacaoJson.getString("assinaturaBiometriaDigital"));
        setCpf(autorizacaoJson.getString("cpf"));

        setIntegracao(new IntegracaoAcessoGrupoEmpresarialVO());
        JSONObject integracaoJson = autorizacaoJson.getJSONObject("integracao");
        getIntegracao().setCodigo(integracaoJson.getInt("codigo"));
        getIntegracao().setUrlZillyonWeb(integracaoJson.getString("urlZillyonWeb"));
        getIntegracao().setChave(integracaoJson.getString("chave"));
    }

    public String getNomePessoa() {
        return nomePessoa;
    }

    public void setNomePessoa(String nomePessoa) {
        this.nomePessoa = nomePessoa;
    }

    public IntegracaoAcessoGrupoEmpresarialVO getIntegracao() {
        return integracao;
    }

    public void setIntegracao(IntegracaoAcessoGrupoEmpresarialVO integracao) {
        this.integracao = integracao;
    }

    public Integer getCodigoMatricula() {
        return codigoMatricula;
    }

    public void setCodigoMatricula(Integer codigoMatricula) {
        this.codigoMatricula = codigoMatricula;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getCodAcesso() {
        return codAcesso;
    }

    public void setCodAcesso(String codAcesso) {
        this.codAcesso = codAcesso;
    }

    public String getCodigoAutorizacao() {
        return codigoAutorizacao;
    }

    public void setCodigoAutorizacao(String codigoAutorizacao) {
        this.codigoAutorizacao = codigoAutorizacao;
    }

    public Integer getCodigoPessoa() {
        return codigoPessoa;
    }

    public void setCodigoPessoa(Integer codigoPessoa) {
        this.codigoPessoa = codigoPessoa;
    }

    public String getTipoPessoa() {
        return tipoPessoa;
    }

    public void setTipoPessoa(String tipoPessoa) {
        this.tipoPessoa = tipoPessoa;
    }

    public Integer getCodigoGenerico() {
        return codigoGenerico;
    }

    public void setCodigoGenerico(Integer codigoGenerico) {
        this.codigoGenerico = codigoGenerico;
    }

    public String getSenhaAcesso() {
        return senhaAcesso;
    }

    public void setSenhaAcesso(String senhaAcesso) {
        this.senhaAcesso = senhaAcesso;
    }

    public String getCodAcessoAlternativo() {
        return codAcessoAlternativo;
    }

    public void setCodAcessoAlternativo(String codAcessoAlternativo) {
        this.codAcessoAlternativo = codAcessoAlternativo;
    }

    public String getFotoKey() {
        return fotoKey;
    }

    public void setFotoKey(String fotoKey) {
        this.fotoKey = fotoKey;
    }

    public String getAssinaturaBiometriaFacial() {
        return assinaturaBiometriaFacial;
    }

    public void setAssinaturaBiometriaFacial(String assinaturaBiometriaFacial) {
        this.assinaturaBiometriaFacial = assinaturaBiometriaFacial;
    }

    public String getAssinaturaBiometriaDigital() {
        return assinaturaBiometriaDigital;
    }

    public void setAssinaturaBiometriaDigital(String assinaturaBiometriaDigital) {
        this.assinaturaBiometriaDigital = assinaturaBiometriaDigital;
    }

    public String getCpf() {
        return cpf;
    }

    public void setCpf(String cpf) {
        this.cpf = cpf;
    }
}
