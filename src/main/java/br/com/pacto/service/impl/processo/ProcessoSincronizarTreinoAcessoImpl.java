package br.com.pacto.service.impl.processo;

import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.controller.json.aulaDia.AulaDiaJSONControle;
import br.com.pacto.dao.intf.cliente.ClienteSinteticoDao;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.impl.conexao.ConexaoZWServiceImpl;
import br.com.pacto.service.intf.processo.ProcessoSincronizarTreinoAcesso;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.ui.ModelMap;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

@Service
public class ProcessoSincronizarTreinoAcessoImpl implements ProcessoSincronizarTreinoAcesso {

    @Autowired
    private ConexaoZWServiceImpl conexaoZWService;
    @Autowired
    private ClienteSinteticoDao clienteSinteticoDao;

    private static List<String> logs = new ArrayList<>();

    public void start(String ctx, String dataInicio, String dataFim, ModelMap mm) {
        List<ClienteSintetico> listaClienteTW = new ArrayList<ClienteSintetico>();
        List<ClienteSintetico> listaClienteZW = new ArrayList<ClienteSintetico>();
        List<Integer> listaCodigosClientesTW = new ArrayList<>();
        List<Integer> listaCodigosClientesZW = new ArrayList<>();
        try {
            logs = new ArrayList<>();

            try (Connection conBDTW = clienteSinteticoDao.getConnection(ctx)) {
                Date dtInicio = Uteis.getDate(dataInicio);
                Date dtFim = Uteis.getDate(dataFim);
                listaClienteTW = buscaTreinoRealizadoParaSincronizarAcesso(dtInicio, dtFim, conBDTW);

                for (ClienteSintetico c : listaClienteTW){
                    listaCodigosClientesTW.add(c.getCodigoCliente());
                }
                if(!listaCodigosClientesTW.isEmpty()){
                    try (Connection conBDZW = conexaoZWService.conexaoZw(ctx)) {
                        String sql = "select distinct (  dthrentrada::date), cliente  from acessocliente " +
                                " where cliente in (" +
                                listaCodigosClientesTW.toString().replace("[","").replace("]","")+
                                " ) " +
                                "and (dthrentrada::date) between ('"+dtInicio+"'::date) and ( '"+dtFim+"'::date) ";
                        ResultSet rsZW = ConexaoZWServiceImpl.criarConsulta(sql,
                                conBDZW
                        );

                        while (rsZW.next()) {
                            ClienteSintetico clienteZW = new ClienteSintetico();
                            clienteZW.setCodigoCliente(rsZW.getInt("cliente"));
                            clienteZW.setDia(rsZW.getDate("dthrentrada"));
                            listaClienteZW.add(clienteZW);
                            listaCodigosClientesZW.add(rsZW.getInt("cliente"));
                        }

                        for (ClienteSintetico ctw : listaClienteTW){
                            boolean validador = true;

                            for(ClienteSintetico czw : listaClienteZW){

                                if(ctw.getCodigoCliente().equals(czw.getCodigoCliente()) && ctw.getDia().equals(czw.getDia())){
                                    validador = false;
                                }
                            }

                            if(validador){
                                logs.add("SincronizarAcesso " + ctw.getNome().toUpperCase() + " estão sincronizados");
                                salvaAcesso(conBDZW, ctw);
                            }
                        }
                    }
                    mm.addAttribute("SUCESSO", "vinculos sincronizados com sucesso!");
                    mm.addAttribute("logs", logs);
                }else{
                    mm.addAttribute("SUCESSO", "Não foi encontrado registro de treinos finalizados para sincronizar para o periodo "+dataInicio +" -- "+dataFim+" ");
                    mm.addAttribute("logs", logs);
                }

            }


        } catch (Exception ex) {
            mm.addAttribute("ERRO", ex.getMessage());
            mm.addAttribute("logs", logs);
            Logger.getLogger(AulaDiaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public List<ClienteSintetico> buscaTreinoRealizadoParaSincronizarAcesso(Date dataInicio, Date dataFim, Connection conBDTW) throws Exception {
        String sql =
                " select distinct ( " +
                        " t.datainicio::date), c.codigocliente as codigocliente, c.nome "+
                        " from treinorealizado t    " +
                        " inner join clientesintetico c on c.codigo = t.cliente_codigo " +
                        " where  (  t.datainicio::date) " +
                        " between   (  '"+dataInicio+"'::date)  and   (  '"+dataFim+"'::date) ";

        List<ClienteSintetico> listaCodigos = new ArrayList<ClienteSintetico>();

        ResultSet rs = conBDTW.prepareStatement(sql).executeQuery();

        while(rs.next()) {
            ClienteSintetico clienteSintetico = new ClienteSintetico();
            clienteSintetico.setCodigoCliente(rs.getInt("codigocliente"));
            clienteSintetico.setDia(rs.getDate("datainicio"));
            clienteSintetico.setNome(rs.getString("nome"));
            listaCodigos.add(clienteSintetico);
        }
        return listaCodigos;
    }

    public void salvaAcesso(Connection conBDZW,  ClienteSintetico cliente) throws SQLException {
        String queryUsuario = "select codigo  from usuario where username ='PACTOBR'";
        Integer codigoUsuario=0;
        try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(queryUsuario.toString(), conBDZW)) {

            while (rs.next()) {
                codigoUsuario = rs.getInt("codigo");
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        String query ="insert into acessocliente (cliente, sentido, situacao, localacesso, coletor, usuario, dthrentrada, dataregistro, meioidentificacaoentrada) \n" +
                "values ("+cliente.getCodigoCliente()+", 'E', 'RV_LIBACESSOAUTORIZADO', 1, 1, "+codigoUsuario+", '"+cliente.getDia()+"','"+cliente.getDia()+"', 15)";
        StringBuilder sql = new StringBuilder();
        sql.append(query);

        PreparedStatement stm = conBDZW.prepareStatement(query);
        stm.execute();

    }
}
