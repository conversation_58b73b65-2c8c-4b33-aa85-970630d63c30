/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.impl.configuracoes;

import br.com.pacto.bean.configuracoes.LinkPreDefinido;
import br.com.pacto.dao.intf.configuracoes.LinkPredefinidoDao;
import br.com.pacto.service.intf.Validacao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.configuracoes.LinkPredefinidoService;
import br.com.pacto.util.ViewUtils;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR>
 */
@Service
public class LinkPreDefinidoServiceImpl implements LinkPredefinidoService{
    
    @Autowired
    private ViewUtils viewUtils;
    @Autowired
    private Validacao validacao;
    @Autowired
    private LinkPredefinidoDao linkDao;
    

    public ViewUtils getViewUtils() {
        return viewUtils;
    }

    public void setViewUtils(ViewUtils viewUtils) {
        this.viewUtils = viewUtils;
    }

    public Validacao getValidacao() {
        return validacao;
    }

    public void setValidacao(Validacao validacao) {
        this.validacao = validacao;
    }

    public LinkPredefinidoDao getLinkDao() {
        return linkDao;
    }

    public void setLinkDao(LinkPredefinidoDao linkDao) {
        this.linkDao = linkDao;
    }

    public LinkPreDefinido alterar(final String ctx, LinkPreDefinido object) throws ServiceException {
        try {
            return getLinkDao().update(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public void excluir(final String ctx, LinkPreDefinido object) throws ServiceException {
        try {
            getLinkDao().delete(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public LinkPreDefinido inserir(final String ctx, LinkPreDefinido object) throws ServiceException {
        try {
            return getLinkDao().insert(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public LinkPreDefinido obterObjetoPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException {
        try {
            return getLinkDao().findObjectByParam(ctx, query, params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public LinkPreDefinido obterPorId(final String ctx, Integer id) throws ServiceException {
        try {
            return getLinkDao().findById(ctx, id);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<LinkPreDefinido> obterPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException {
        try {
            return getLinkDao().findByParam(ctx, query, params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public List<LinkPreDefinido> obterTodos(String ctx) throws ServiceException {
        try {
            return getLinkDao().findAll(ctx);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public List<LinkPreDefinido> obterPorParam(String ctx, String query, Map<String, Object> params, int max, int index) throws ServiceException {
        try {
            return getLinkDao().findByParam(ctx, query, params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public void deleteInativos(String ctx, Integer empresa) throws ServiceException {
        List<LinkPreDefinido> todos = obterTodos(ctx);
        for(LinkPreDefinido link : todos){
            if(!link.getAtivo() && link.getEmpresa().equals(empresa)){
                excluir(ctx, link);
            }
        }
    }

    @Override
    public List<LinkPreDefinido> obterAtivos(String ctx, Integer empresa) throws ServiceException {
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("empresa", empresa);
        String sql = "SELECT obj FROM LinkPreDefinido obj WHERE obj.empresa = :empresa AND obj.ativo IS TRUE";
        return obterPorParam(ctx, sql, params);
    }
    
}
