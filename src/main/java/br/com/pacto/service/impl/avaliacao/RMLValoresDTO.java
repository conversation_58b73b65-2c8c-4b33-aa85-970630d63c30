package br.com.pacto.service.impl.avaliacao;

import br.com.pacto.bean.avaliacao.RMLLinha;

public class RMLValoresDTO {

    private RMLLinha linha;
    private String faixaIdade;
    private String excelente;
    private String acimaMedia;
    private String media;
    private String abaixoMedia;
    private String fraco;

    public RMLLinha getLinha() {
        return linha;
    }

    public void setLinha(RMLLinha linha) {
        this.linha = linha;
    }

    public String getFaixaIdade() {
        return faixaIdade;
    }

    public void setFaixaIdade(String faixaIdade) {
        this.faixaIdade = faixaIdade;
    }

    public String getExcelente() {
        return excelente;
    }

    public void setExcelente(String excelente) {
        this.excelente = excelente;
    }

    public String getAcimaMedia() {
        return acimaMedia;
    }

    public void setAcimaMedia(String acimaMedia) {
        this.acimaMedia = acimaMedia;
    }

    public String getMedia() {
        return media;
    }

    public void setMedia(String media) {
        this.media = media;
    }

    public String getAbaixoMedia() {
        return abaixoMedia;
    }

    public void setAbaixoMedia(String abaixoMedia) {
        this.abaixoMedia = abaixoMedia;
    }

    public String getFraco() {
        return fraco;
    }

    public void setFraco(String fraco) {
        this.fraco = fraco;
    }
}
