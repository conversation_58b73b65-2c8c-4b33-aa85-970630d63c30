package br.com.pacto.service.impl.locacao;

import br.com.pacto.bean.locacao.AlunoLocacaoHorarioPlayCheckinCheckout;
import br.com.pacto.bean.locacao.LocacaoPlayCanceladaFinalizada;
import br.com.pacto.bean.locacao.LocacaoPlayCanceladaFinalizadaDTO;
import br.com.pacto.dao.intf.locacao.LocacaoPlayCanceladaFinalizadaDao;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.locacao.LocacaoPlayCanceladaFinalizadaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.test.context.ContextConfiguration;

import java.util.HashMap;
import java.util.Map;

@Service
@ContextConfiguration(locations = {"/applicationContext.xml"})
public class LocacaoPlayCanceladaFinalizadaServiceImpl implements LocacaoPlayCanceladaFinalizadaService {

    private final LocacaoPlayCanceladaFinalizadaDao locacaoPlayCanceladaFinalizadaDao;
    private final SessaoService sessaoService;

    @Autowired
    public LocacaoPlayCanceladaFinalizadaServiceImpl(LocacaoPlayCanceladaFinalizadaDao locacaoPlayCanceladaFinalizadaDao, SessaoService sessaoService) {
        this.locacaoPlayCanceladaFinalizadaDao = locacaoPlayCanceladaFinalizadaDao;
        this.sessaoService = sessaoService;
    }

    @Override
    public String cancelaLocacaoHorarioPlay(LocacaoPlayCanceladaFinalizadaDTO locacaoPlayCanceladaFinalizadaDTO) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            LocacaoPlayCanceladaFinalizada locacaoPlayCanceladaFinalizada = new LocacaoPlayCanceladaFinalizada(locacaoPlayCanceladaFinalizadaDTO);

            String query = "SELECT obj FROM LocacaoPlayCanceladaFinalizada obj\n" +
                    "WHERE obj.cancelada = true\n" +
                    "AND obj.locacaoHorario.codigo = :locacaoHorarioCodigo\n" +
                    "ORDER BY obj.codigo DESC LIMIT 1";
            Map<String, Object> params = new HashMap<>();
            params.put("locacaoHorarioCodigo", locacaoPlayCanceladaFinalizadaDTO.getLocacaoHorario());
            LocacaoPlayCanceladaFinalizada cancelamento = locacaoPlayCanceladaFinalizadaDao.findObjectByParam(ctx, query, params);

            if (cancelamento == null) {
                locacaoPlayCanceladaFinalizadaDao.insert(ctx, locacaoPlayCanceladaFinalizada);
                return ((locacaoPlayCanceladaFinalizadaDTO.getFinalizada() != null && locacaoPlayCanceladaFinalizada.getFinalizada()) ?
                        "locacaoHorarioFinalizadaId" : "locacaoHorarioCanceladoId");
            } else {
                return "locacaoHorarioNaoCancelado";
            }
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }
}
