package br.com.pacto.service.impl.agenda;

import br.com.pacto.objeto.Uteis;

import java.util.Date;

public class HistoricoProfessorTurmaVO {

    private Integer codigo;
    private HorarioTurmaVO horarioTurma;
    private Colaborador<PERSON> professor;
    private Date lancamento;
    private Date inicio;
    private Date fim;
    private  String fotoKey;


    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public HorarioTurmaVO getHorarioTurma() {
        return horarioTurma;
    }

    public void setHorarioTurma(HorarioTurmaVO horarioTurma) {
        this.horarioTurma = horarioTurma;
    }

    public ColaboradorVO getProfessor() {
        return professor;
    }

    public void setProfessor(Colaborador<PERSON> professor) {
        this.professor = professor;
    }

    public Date getLancamento() {
        return lancamento;
    }

    public void setLancamento(Date lancamento) {
        this.lancamento = lancamento;
    }

    public Date getInicio() {
        return inicio;
    }

    public void setInicio(Date inicio) {
        this.inicio = inicio;
    }

    public Date getFim() {
        return fim;
    }

    public void setFim(Date fim) {
        this.fim = fim;
    }

    public String getFotoKey() {
        return fotoKey;
    }

    public void setFotoKey(String fotoKey) {
        this.fotoKey = fotoKey;
    }

    public String getUrlFoto() {
        try {
            return Uteis.getPaintFotoDaNuvem(getFotoKey());
        } catch (Exception e) {
            return "";
        }
    }
}
