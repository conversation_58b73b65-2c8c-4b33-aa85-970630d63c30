package br.com.pacto.service.impl.notificacao.excecao;

import br.com.pacto.service.exception.ExcecaoSistema;

public enum AulaExcecoes implements ExcecaoSistema {

    ERRO_INCLUIR("erro_incluir_aula", "Ocorreu um erro ao incluir a aula informada"),
    ERRO_BUSCAR("erro_buscar_aula", "Ocorreu um erro ao obter a aula"),
    ERRO_lISTAR("erro_listar_aulas", "Ocorreu um erro ao listar as aulas"),
    ERRO_ALTERAR("erro_alterar_aula", "Ocorreu um erro ao alterar as aula"),
    ERRO_EXCLUIR("erro_excluir_aula", "Ocorreu um erro ao excluir a aula"),
    AULA_NAO_ENCONTRADA("aula_nao_encontrada", "Aula não encontrada"),

    ERRO_ID_NAO_INFORMADA("erro_id_nao_informada", "O id da aula não foi informado!"),
    ERRO_NOME_NAO_INFORMADO("erro_nome_nao_informado", "O nome da aula não foi informado!"),
    ERRO_aula_NAO_EXISTE("erro_aula_nao_existe", "A aula informada não existe"),
    HORARIO_NAO_INFORMADO("horario_nao_informado","horario_nao_informado"),
    ERRO_AULA_URL_VIRTUAL("erro_aula_url_virtual","Informe a URL da aula virtual"),
    HORARIO_NAO_PODE_SER_EXCLUIDO("horario_nao_pode_ser_excluido","horario_sendo_usado")
    ;

    private String chave;
    private String descricao;

    AulaExcecoes(String chave, String descricao) {
        this.chave = chave;
        this.descricao = descricao;
    }

    @Override
    public String getChaveExcecao() {
        return chave;
    }
    @Override
    public String getDescricaoExcecao() {
        return descricao;
    }
}
