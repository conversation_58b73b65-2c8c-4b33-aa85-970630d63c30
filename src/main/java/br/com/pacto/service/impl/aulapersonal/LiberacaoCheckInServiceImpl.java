/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.pacto.service.impl.aulapersonal;

import br.com.pacto.bean.gestaopersonal.AulaPersonal;
import br.com.pacto.bean.gestaopersonal.LiberacaoCheckIn;
import br.com.pacto.dao.intf.aulapersonal.LiberacaoCheckInDao;
import br.com.pacto.service.intf.Validacao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.aulapersonal.LiberacaoCheckInService;
import br.com.pacto.util.ViewUtils;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



/**
 *
 * <AUTHOR>
 */
@Service
public class LiberacaoCheckInServiceImpl implements LiberacaoCheckInService {

    @Autowired
    private ViewUtils viewUtils;
    @Autowired
    private Validacao validacao;
    @Autowired
    private LiberacaoCheckInDao liberacaoCheckInDao;

    public ViewUtils getViewUtils() {
        return viewUtils;
    }

    public void setViewUtils(ViewUtils viewUtils) {
        this.viewUtils = viewUtils;
    }

    public Validacao getValidacao() {
        return validacao;
    }

    public void setValidacao(Validacao validacao) {
        this.validacao = validacao;
    }

    public LiberacaoCheckInDao getLiberacaoCheckInDao(){
        return this.liberacaoCheckInDao;
    }

    public void setLiberacaoCheckInDao(LiberacaoCheckInDao liberacaoCheckInDao){
       this.liberacaoCheckInDao = liberacaoCheckInDao; 
    }


    public LiberacaoCheckIn alterar(final String ctx, LiberacaoCheckIn object) throws ServiceException {
        try {
            return getLiberacaoCheckInDao().update(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public void excluir(final String ctx, LiberacaoCheckIn object) throws ServiceException {
        try {
            getLiberacaoCheckInDao().delete(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public LiberacaoCheckIn inserir(final String ctx, LiberacaoCheckIn object) throws ServiceException {
        try {
            return getLiberacaoCheckInDao().insert(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public LiberacaoCheckIn obterObjetoPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException {
        try {
            return getLiberacaoCheckInDao().findObjectByParam(ctx, query, params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public LiberacaoCheckIn obterPorId(final String ctx, Integer id) throws ServiceException {
        try {
            return getLiberacaoCheckInDao().findById(ctx, id);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<LiberacaoCheckIn> obterPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException {
        try {
            return getLiberacaoCheckInDao().findByParam(ctx, query, params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<LiberacaoCheckIn> obterPorParam(final String ctx, String query,
            Map<String, Object> params, int max, int index)
            throws ServiceException {
        try {
            return getLiberacaoCheckInDao().findByParam(ctx, query, params, max, index);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<LiberacaoCheckIn> obterTodos(final String ctx) throws ServiceException {
        try {
            return getLiberacaoCheckInDao().findAll(ctx);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }
    
    @Override
    public LiberacaoCheckIn obterLiberacao(String ctx, AulaPersonal aula) throws ServiceException {
        try {
            final String query = "select obj from LiberacaoCheckIn obj "
                    + "where obj.aulaLiberada.codigo = :aula ";
            HashMap<String, Object> p = new HashMap<String, Object>();
            p.put("aula", aula.getCodigo());
            return getLiberacaoCheckInDao().findObjectByParam(ctx, query, p);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    }