package br.com.pacto.service.impl.avaliacao;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.avaliacao.ItemAvaliacaoFisicaEnum;
import br.com.pacto.bean.bi.IndicadorAvaliacaoFisicaEnum;
import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.bean.professor.ProfessorSintetic<PERSON>;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.avaliacao.BIAvaliacaoFisicaDTO;
import br.com.pacto.controller.json.avaliacao.bi_enum.TipoBIAvaliacaoEnum;
import br.com.pacto.controller.json.base.SuperControle;
import br.com.pacto.dao.intf.avaliacao.AvaliacaoFisicaDao;
import br.com.pacto.dao.intf.cliente.ClienteSinteticoDao;
import br.com.pacto.dao.intf.professor.Professor<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.conexao.ConexaoZWServiceImpl;
import br.com.pacto.service.intf.avaliacao.BIAvaliacaoFisicaService;
import br.com.pacto.service.intf.usuario.UsuarioService;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.enumeradores.PaletaCoresEnum;
import br.com.pacto.util.impl.Ordenacao;
import org.json.JSONArray;
import org.json.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Created by alcides on 14/10/2017.
 */
@Service
public class BIAvaliacaoFisicaServiceImpl implements BIAvaliacaoFisicaService {
    private static final int MAXIMO_CONSULTAR = 30;
    @Autowired
    public AvaliacaoFisicaDao avaliacaoDao;
    @Autowired
    public ClienteSinteticoDao clienteSinteticoDao;
    @Autowired
    public ProfessorSinteticoDao professorSinteticoDao;
    @Autowired
    public UsuarioService usuarioService;
    @Autowired
    private ConexaoZWServiceImpl conexaoZWService;

    private String verificaModalidades(String resultSetModalidadeString) {
        if(resultSetModalidadeString == null) {
            return "SEM MODALIDADE CADASTRADA";
        } else {
            return resultSetModalidadeString;
        }
    }

    @Override
    public List<ItemAvaliacaoFisicaTO> listaBI(String ctx, Integer codigoEmpresa, IndicadorAvaliacaoFisicaEnum indicador, final Date inicio, final Date fim, final String obj, Integer codigoProfessor, String parametro, PaginadorDTO paginadorDTO, Integer codigoAvaliador) throws ServiceException {
        try {
            if (codigoAvaliador == null) {
                codigoAvaliador = -1;
            }
            List<ItemAvaliacaoFisicaTO> listaBI = new ArrayList<>();
            ResultSet rs = null;
            paginadorDTO = paginadorDTO == null ? new PaginadorDTO() : paginadorDTO;
            switch (indicador) {
                case IND_AF_TODAS:
                    rs = sqlAvaliacoes(ctx, codigoEmpresa, true, inicio, fim, false, false, codigoProfessor, parametro, paginadorDTO, codigoAvaliador);
                    paginadorDTO.setQuantidadeTotalElementos(new Long(countTodas(ctx, codigoEmpresa, inicio, fim, false, false, codigoProfessor, null, codigoAvaliador)));
                    break;
                case IND_AF_NOVAS:
//                    rs = sqlAvaliacoes(ctx, codigoEmpresa, true, inicio, fim, true, false, codigoProfessor, parametro, paginadorDTO, codigoAvaliador);
                    rs = sqlAvaliacoesNovas(ctx, inicio, fim, codigoProfessor, codigoAvaliador, codigoEmpresa,true, paginadorDTO, true, parametro);
                    while (rs.next()) {
                        listaBI.add(montarItem(rs, montarAvaliador(ctx, indicador, rs)));
                    }
                    return listaBI;
                case IND_AF_REAVALIACOES:
                    rs = sqlAvaliacoesNovas(ctx, inicio, fim, codigoProfessor, codigoAvaliador, codigoEmpresa,true, paginadorDTO, false, parametro);
                    while (rs.next()) {
                        listaBI.add(montarItem(rs, montarAvaliador(ctx, indicador, rs)));
                    }
                    break;
                case IND_AF_PREVISTAS:
                    rs = sqlPrevistas(ctx, codigoEmpresa, true, inicio, fim, true, false, false, false, codigoProfessor, parametro, paginadorDTO, codigoAvaliador);
                    paginadorDTO.setQuantidadeTotalElementos(new Long(countReavaliacoes(ctx, codigoEmpresa, inicio, fim,true, false, false, false, codigoProfessor, null, codigoAvaliador)));
                    break;
                case IND_AF_REALIZADAS:
                    rs = sqlPrevistas(ctx, codigoEmpresa, true, inicio, fim,false, true, false, false, codigoProfessor, parametro, paginadorDTO, codigoAvaliador);
                    paginadorDTO.setQuantidadeTotalElementos(new Long(countReavaliacoes(ctx, codigoEmpresa, inicio, fim, false, true, false, false, codigoProfessor, null, codigoAvaliador)));
                    break;
                case IND_AF_ATRASADAS:
                    rs = sqlPrevistas(ctx, codigoEmpresa, true, inicio, fim, false, false, true, false, codigoProfessor, parametro, paginadorDTO, codigoAvaliador);
                    paginadorDTO.setQuantidadeTotalElementos(new Long(countReavaliacoes(ctx, codigoEmpresa, inicio, fim, false,false, true, false, codigoProfessor, null, codigoAvaliador)));
                    break;
                case IND_AF_FUTURAS:
                    rs = sqlPrevistas(ctx, codigoEmpresa, true, inicio, fim, false, false, false, true, codigoProfessor, parametro, paginadorDTO, codigoAvaliador);
                    paginadorDTO.setQuantidadeTotalElementos(new Long(countReavaliacoes(ctx, codigoEmpresa, inicio, fim, false,false, false, true, codigoProfessor, null, codigoAvaliador)));
                    break;
                case IND_AF_ATIVOS_SEM:
                    ResultSet rs3 = sqlSemAvaliacao(ctx, codigoEmpresa, true, codigoProfessor, parametro, paginadorDTO, codigoAvaliador);
                    while (rs3.next()) {
                        listaBI.add(new ItemAvaliacaoFisicaTO(rs3.getInt("codigo"), Uteis.getNomeAbreviandoSomentePrimeiroSobrenome(rs3.getString("nome")), rs3.getString("matricula"), verificaModalidades(rs3.getString("nome_modalidades"))));
                    }
                    if(paginadorDTO == null){
                        listaBI = Ordenacao.ordenarLista(listaBI, "nome");
                    }
                    paginadorDTO.setQuantidadeTotalElementos(new Long(countSemAvaliacao(ctx, codigoEmpresa,codigoProfessor, null, codigoAvaliador)));
                    return listaBI;
                case IND_AF_ATIVOS_AVALIACAO_ATRASADA:
                    ResultSet rs4 = sqlAtrasadosNova(ctx, codigoEmpresa,  codigoProfessor,codigoAvaliador, parametro,paginadorDTO);
                    while (rs4.next()) {
                        listaBI.add(new ItemAvaliacaoFisicaTO(rs4.getInt("codigo"), Uteis.getNomeAbreviandoSomentePrimeiroSobrenome(rs4.getString("nome")), rs4.getString("matricula")));
                    }
                    if(paginadorDTO == null){
                        listaBI = Ordenacao.ordenarLista(listaBI, "nome");
                    }
                    return listaBI;
                case IND_AF_PERDERAM_PERCENTUAL:
                    List<ClienteSintetico> alunosPerderamPercGordura = montarResultadosAlunos(ctx, codigoEmpresa, indicador, codigoProfessor, parametro, paginadorDTO);
                    for (ClienteSintetico clienteSintetico: alunosPerderamPercGordura){
                        Double percFirst = clienteSintetico.getPercentualGorduraInicio();
                        Double percLast = clienteSintetico.getPercentualGorduraAtual();
                        listaBI.add(new ItemAvaliacaoFisicaTO(clienteSintetico.getCodigo(),
                                clienteSintetico.getMatricula().toString(),
                                Uteis.getNomeAbreviandoSomentePrimeiroSobrenome(clienteSintetico.getNome()),
                                "Inicial: " + Uteis.formatarValorNumerico(percFirst) + "% - Atual: " + Uteis.formatarValorNumerico(percLast) + "%",
                                Uteis.formatarValorNumerico(percFirst - percLast)));
                    }
                    if(paginadorDTO == null){
                        listaBI = Ordenacao.ordenarLista(listaBI, "diff");
                        Collections.reverse(listaBI);
                    }
                    paginadorDTO.setQuantidadeTotalElementos(new Long(countResultadosAlunos(ctx,codigoEmpresa,codigoProfessor, IndicadorAvaliacaoFisicaEnum.IND_AF_PERDERAM_PERCENTUAL)));
                    return listaBI;
                case IND_AF_PERDERAM_PESO:
                    List<ClienteSintetico> alunosPerderamPeso = montarResultadosAlunos(ctx, codigoEmpresa, indicador, codigoProfessor, parametro, paginadorDTO);
                    for (ClienteSintetico clienteSintetico : alunosPerderamPeso){
                        Double pesoFirst = clienteSintetico.getPesoInicio();
                        Double pesoLast = clienteSintetico.getPesoAtual();

                        listaBI.add(new ItemAvaliacaoFisicaTO(clienteSintetico.getCodigo(),
                                clienteSintetico.getMatricula().toString(),
                                Uteis.getNomeAbreviandoSomentePrimeiroSobrenome(clienteSintetico.getNome()),
                                "Inicial: " + Uteis.formatarValorNumerico(pesoFirst) + "kg - Atual: " + Uteis.formatarValorNumerico(pesoLast) + "kg",
                                Uteis.formatarValorNumerico(pesoFirst - pesoLast)));
                    }
                    if(paginadorDTO == null){
                        listaBI = Ordenacao.ordenarLista(listaBI, "diff");
                        Collections.reverse(listaBI);
                    }
                    paginadorDTO.setQuantidadeTotalElementos(new Long(countResultadosAlunos(ctx,codigoEmpresa,codigoProfessor, IndicadorAvaliacaoFisicaEnum.IND_AF_PERDERAM_PESO)));
                    return listaBI;
                case IND_AF_GANHARAM_MASSA:
                    List<ClienteSintetico> alunosGanharamMassaMagra = montarResultadosAlunos(ctx, codigoEmpresa, indicador, codigoProfessor, parametro, paginadorDTO);
                    for (ClienteSintetico clienteSintetico : alunosGanharamMassaMagra){
                        Double massaFirst = clienteSintetico.getMassaMagraInicio();
                        Double massaLast = clienteSintetico.getMassaMagraAtual();

                        listaBI.add(new ItemAvaliacaoFisicaTO(clienteSintetico.getCodigo(),
                                clienteSintetico.getMatricula().toString(),
                                Uteis.getNomeAbreviandoSomentePrimeiroSobrenome(clienteSintetico.getNome()),
                                "Inicial: " + Uteis.formatarValorNumerico(massaFirst) + "kg - Atual: " + Uteis.formatarValorNumerico(massaLast) + "kg",
                                Uteis.formatarValorNumerico(massaLast - massaFirst)));
                    }
                    if(paginadorDTO == null){
                        listaBI = Ordenacao.ordenarLista(listaBI, "diff");
                        Collections.reverse(listaBI);
                    }
                    paginadorDTO.setQuantidadeTotalElementos(new Long(countResultadosAlunos(ctx,codigoEmpresa,codigoProfessor, IndicadorAvaliacaoFisicaEnum.IND_AF_GANHARAM_MASSA)));
                    return listaBI;
                case IND_AF_PARQ_POSITIVO:

                    ResultSet rsparq = sqlParq(ctx, codigoEmpresa, true, codigoProfessor, parametro, paginadorDTO,codigoAvaliador);
                    while (rsparq.next()) {
                        listaBI.add(new ItemAvaliacaoFisicaTO(rsparq.getInt("codigo"), Uteis.getNomeAbreviandoSomentePrimeiroSobrenome(rsparq.getString("nome")), rsparq.getString("matricula")));
                    }
                    if(paginadorDTO == null){
                        listaBI = Ordenacao.ordenarLista(listaBI, "nome");
                    }
                    paginadorDTO.setQuantidadeTotalElementos(new Long(countParq(ctx, codigoEmpresa, codigoProfessor, null, codigoAvaliador)));
                    return listaBI;
                case IND_AF_OBJETIVOS:

                    ResultSet rsobjetivos = sqlObjetivos(ctx, codigoEmpresa, true, obj, codigoProfessor, parametro, paginadorDTO,codigoAvaliador);
                    while (rsobjetivos.next()) {
                        ItemAvaliacaoFisicaTO i = new ItemAvaliacaoFisicaTO(rsobjetivos.getInt("codigo"),
                                Uteis.getNomeAbreviandoSomentePrimeiroSobrenome(rsobjetivos.getString("nome")),
                                rsobjetivos.getString("matricula"));
                        i.setObs(obj);
                        listaBI.add(i);
                    }
                    if(paginadorDTO == null){
                        listaBI = Ordenacao.ordenarLista(listaBI, "nome");
                    }
                    return listaBI;
            }
            while (rs.next()) {
                listaBI.add(montarItem(rs, montarAvaliador(ctx, indicador, rs)));
            }
            if(paginadorDTO == null){
                listaBI = Ordenacao.ordenarLista(listaBI, "nome");
            }
            return listaBI;
        }catch (Exception e){
            throw new ServiceException(e);
        }
    }

    private String montarAvaliador(String ctx, IndicadorAvaliacaoFisicaEnum indicador, ResultSet rs) throws SQLException {
        String avaliador = "";
        try {
            if (indicador == IndicadorAvaliacaoFisicaEnum.IND_AF_TODAS ||
                    indicador == IndicadorAvaliacaoFisicaEnum.IND_AF_NOVAS ||
                    indicador == IndicadorAvaliacaoFisicaEnum.IND_AF_REAVALIACOES ||
                    indicador == IndicadorAvaliacaoFisicaEnum.IND_AF_REALIZADAS ||
                    indicador == IndicadorAvaliacaoFisicaEnum.IND_AF_PREVISTAS ||
                    indicador == IndicadorAvaliacaoFisicaEnum.IND_AF_ATRASADAS ||
                    indicador == IndicadorAvaliacaoFisicaEnum.IND_AF_FUTURAS) {
                if (!SuperControle.independente(ctx)) {
                    Usuario us = usuarioService.obterPorId(ctx, rs.getInt("avaliadorCodigo"), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    if (us != null) {
                        avaliador = us.getProfessor().getNome();
                    }
                } else {
                    avaliador = rs.getString("ava");
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return avaliador;
    }

    @Override
    public BIAvaliacaoFisicaDTO getBI(String key, Integer codigoEmpresa, Date inicio, Date fim, Integer codigoProfessor, TipoBIAvaliacaoEnum tipoBIAvaliacaoEnum, Integer codigoAvaliador) throws ServiceException {
        try {
            if(codigoAvaliador == null) codigoAvaliador=-1;
            if ((codigoProfessor == null || codigoProfessor == 0 ) && tipoBIAvaliacaoEnum != null){
                codigoProfessor = tipoBIAvaliacaoEnum.getId();
            } else if (codigoProfessor == null || codigoProfessor == 0) {
                codigoProfessor = TipoBIAvaliacaoEnum.TODOS_PROFESSORES.getId();
            }
            if (codigoProfessor != 0 && tipoBIAvaliacaoEnum == null){
                ProfessorSintetico professorSintetico = professorSinteticoDao.obterPorIdColaborador(key,codigoProfessor);
                codigoProfessor = professorSintetico == null ? 0 : professorSintetico.getCodigo();
            }
            BIAvaliacaoFisicaTO result = montarBI(key, codigoEmpresa, inicio, fim, codigoProfessor, codigoAvaliador);
            ArrayList<BIAvaliacaoFisicaDTO.GraficoDTO> graficos = new ArrayList<>();
            for (int i = 0; i < result.getGrafico().length() ; i++) {
                JSONObject obj =(result.getGrafico().getJSONObject(i));
                if (!obj.isNull("obj") && !obj.isNull("valor") ) {
                    graficos.add(new BIAvaliacaoFisicaDTO.GraficoDTO(obj.getString("obj"),
                                    obj.optInt("valor")));
                }
            }
            return new BIAvaliacaoFisicaDTO(result, graficos);
        } catch (Exception e) {
           throw new ServiceException(e);
        }
    }

    private ResultSet sqlSemAvaliacao(String ctx, Integer empresa, boolean lista, Integer codigoProfessor, String parametro, PaginadorDTO paginator, Integer codigoAvaliador) throws Exception {
        StringBuilder sqlSemAvaliacao = new StringBuilder();
        if (lista) {
            sqlSemAvaliacao.append("select cli.codigo, cli.matricula, cli.nome, ")
                    .append("(select case when count(mod_id) > 1 then string_agg(m.nome, ',') else max(m.nome) end ")
                    .append(" from unnest(string_to_array(cli.modalidades, '|')) as mod_id ")
                    .append(" join modalidade m on m.codigozw = mod_id::int) as nome_modalidades ")
                    .append("from clientesintetico cli \n");
        } else {
            sqlSemAvaliacao.append("select count(cli.codigo) as alunos from clientesintetico cli \n");
        }

        sqlSemAvaliacao.append("left join professorsintetico prof on cli.professorsintetico_codigo = prof.codigo \n");
        if(codigoProfessor == Integer.MAX_VALUE){
            sqlSemAvaliacao.append(" left join empresa emp on cli.empresa = emp.codigo ");
        } else {
            sqlSemAvaliacao.append(" left join empresa emp on prof.empresa_codigo = emp.codigo ");
        }
        sqlSemAvaliacao.append("where cli.codigo not in (select cliente_codigo from avaliacaofisica) \n")
                .append("and situacao like 'AT' \n");

        if(empresa != null && empresa > 0){
            sqlSemAvaliacao.append(" and emp.codZW = ").append(empresa);
        }

        if(codigoProfessor > 0){
            if(codigoProfessor == Integer.MAX_VALUE){
                sqlSemAvaliacao.append("and prof.codigo is null ");
            }else{
                if(codigoProfessor == Integer.MAX_VALUE - 1){
                    sqlSemAvaliacao.append("and prof.ativo = false ");
                }else{
                    sqlSemAvaliacao.append("and prof.codigo = ").append(codigoProfessor);
                }
            }
        }
        if(null != codigoAvaliador && codigoAvaliador > 0){
            sqlSemAvaliacao.append(" and prof.codigocolaborador = ").append(codigoAvaliador);
        }

        if(StringUtils.isNotBlank(parametro)){
            int matricula;
            try{
                matricula = Integer.parseInt(parametro);
            }catch (Exception e){
                matricula = 0;
            }
            sqlSemAvaliacao.append(" and (cli.nome ilike '%").append(parametro).append("%' ");
            if(matricula > 0){
                sqlSemAvaliacao.append(" or cli.matricula = ").append(matricula);
            }
            sqlSemAvaliacao.append(" ) ");
        }

        if(paginator != null && (paginator.getPage() != null && paginator.getSize() != null)) {
            paginator.setQuantidadeTotalElementos((long) countSemAvaliacao(ctx, empresa, codigoProfessor, parametro, codigoAvaliador));
            sqlSemAvaliacao.append(montarPaginacao(paginator));
        }

        return avaliacaoDao.createStatement(ctx, sqlSemAvaliacao.toString());
    }

    private ResultSet sqlAtrasados(String ctx, Integer empresa, boolean lista, Integer codigoProfessor, String parametro, PaginadorDTO paginator, Integer codigoAvaliador) throws Exception {
        StringBuilder sqlAtrasados = new StringBuilder();
        List<Object> params = new ArrayList<>();

        if (lista) {
            sqlAtrasados.append("SELECT cli.codigo, cli.matricula, cli.nome FROM clientesintetico cli \n");
        } else {
            sqlAtrasados.append("SELECT COUNT(cli.codigo) AS alunos FROM clientesintetico cli ");
        }

        sqlAtrasados.append("LEFT JOIN professorsintetico prof ON cli.professorsintetico_codigo = prof.codigo \n");
        sqlAtrasados.append("LEFT JOIN empresa emp ON ");
        if (codigoProfessor == Integer.MAX_VALUE) {
            sqlAtrasados.append("cli.empresa = emp.codigo ");
        } else {
            sqlAtrasados.append("prof.empresa_codigo = emp.codigo ");
        }
        sqlAtrasados.append("WHERE cli.codigo IN (SELECT cliente_codigo FROM avaliacaofisica av WHERE dataproxima < ? ");
        sqlAtrasados.append("AND (SELECT COUNT(codigo) FROM avaliacaofisica aa WHERE av.cliente_codigo = aa.cliente_codigo AND aa.dataavaliacao > av.dataavaliacao) = 0) ");
        sqlAtrasados.append("AND situacao LIKE 'AT' \n");

        String dataProximaString = Uteis.getDataAplicandoFormatacao(Calendario.hoje(), "yyyy-MM-dd");
        Timestamp dataProximaTimestamp = Timestamp.valueOf(dataProximaString + " 00:00:00");
        params.add(dataProximaTimestamp);

        if (empresa != null && empresa > 0) {
            sqlAtrasados.append("AND emp.codZW = ?");
            params.add(empresa);
        }

        if (codigoProfessor != null && codigoProfessor > 0) {
            if (codigoProfessor == Integer.MAX_VALUE) {
                sqlAtrasados.append("AND prof.codigo IS NULL ");
            } else if (codigoProfessor == Integer.MAX_VALUE - 1) {
                sqlAtrasados.append("AND prof.ativo = false ");
            } else {
                sqlAtrasados.append("AND prof.codigo = ?");
                params.add(codigoProfessor);
            }
        }

        if (codigoAvaliador != null && codigoAvaliador > 0) {
            sqlAtrasados.append("AND prof.codigocolaborador = ?");
            params.add(codigoAvaliador);
        }

        if (StringUtils.isNotBlank(parametro)) {
            int matricula;
            try {
                matricula = Integer.parseInt(parametro);
            } catch (NumberFormatException e) {
                matricula = 0;
            }
            sqlAtrasados.append("AND (cli.nome ILIKE ? ");
            params.add("%" + parametro + "%");
            if (matricula > 0) {
                sqlAtrasados.append("OR cli.matricula = ?");
                params.add(matricula);
            }
            sqlAtrasados.append(") ");
        }

        if (paginator != null && paginator.getPage() != null && paginator.getSize() != null) {
            paginator.setQuantidadeTotalElementos((long) countAvaliacaoAtrasada(ctx, empresa, codigoProfessor, parametro, codigoAvaliador));
            sqlAtrasados.append(montarPaginacao(paginator));
        }

        return avaliacaoDao.createPreparedStatement(ctx, sqlAtrasados.toString(), params.toArray());
    }

    private ResultSet sqlAtrasadosNova(String ctx, Integer empresa, Integer codigoProfessor, Integer codigoAvaliador, String parametro,PaginadorDTO paginator) throws Exception {
        StringBuilder sqlAtrasados = new StringBuilder();
        List<Object> params = new ArrayList<>();
        String dataProximaString = Uteis.getDataAplicandoFormatacao(Calendario.hoje(), "yyyy-MM-dd");
        Timestamp dataProximaTimestamp = Timestamp.valueOf(dataProximaString + " 00:00:00");
        sqlAtrasados.append("SELECT cli.codigo, cli.matricula, cli.nome FROM clientesintetico cli ");
        sqlAtrasados.append("LEFT JOIN professorsintetico prof ON cli.professorsintetico_codigo = prof.codigo ");
        sqlAtrasados.append("LEFT JOIN empresa emp ON ");
        if (codigoProfessor == Integer.MAX_VALUE) {
            sqlAtrasados.append("cli.empresa = emp.codigo ");
        } else {
            sqlAtrasados.append("prof.empresa_codigo = emp.codigo ");
        }
        sqlAtrasados.append("WHERE cli.codigo IN ( ");
        sqlAtrasados.append("select distinct cliente_codigo FROM avaliacaofisica av ");
        sqlAtrasados.append("inner join clientesintetico cli  on cli.codigo = av.cliente_codigo ");
        sqlAtrasados.append("WHERE dataproxima < '"+dataProximaTimestamp+"' ");
        sqlAtrasados.append("and cli.situacao LIKE 'AT' ");
        if (empresa != null && empresa > 0) {
            sqlAtrasados.append(" AND cli.empresa = "+empresa+" ");
        }
        sqlAtrasados.append("and av.dataAvaliacao is not null ");
        sqlAtrasados.append("and cliente_codigo ");
        sqlAtrasados.append("not in ( SELECT distinct cliente_codigo FROM avaliacaofisica av ");
        sqlAtrasados.append("inner join clientesintetico cli  on cli.codigo = av.cliente_codigo ");
        sqlAtrasados.append("WHERE dataproxima > '"+dataProximaTimestamp+"' ");
        sqlAtrasados.append("and cli.situacao LIKE 'AT' ");
        if (empresa != null && empresa > 0) {
            sqlAtrasados.append("AND cli.empresa = "+empresa+" ");
        }
        sqlAtrasados.append("and av.dataAvaliacao is not null ");
        sqlAtrasados.append("))");

        if (codigoProfessor != null && codigoProfessor > 0) {
            if (codigoProfessor == Integer.MAX_VALUE) {
                sqlAtrasados.append("AND prof.codigo IS NULL ");
            } else if (codigoProfessor == Integer.MAX_VALUE - 1) {
                sqlAtrasados.append("AND prof.ativo = false ");
            } else {
                sqlAtrasados.append("AND prof.codigo = ? ");
                params.add(codigoProfessor);
            }
        }

        if (StringUtils.isNotBlank(parametro)) {
            int matricula;
            try {
                matricula = Integer.parseInt(parametro);
            } catch (NumberFormatException e) {
                matricula = 0;
            }
            sqlAtrasados.append("AND (cli.nome ILIKE ? ");
            params.add("%" + parametro + "%");
            if (matricula > 0) {
                sqlAtrasados.append("OR cli.matricula = ? ");
                params.add(matricula);
            }
            sqlAtrasados.append(") ");
        }

        if (codigoAvaliador != null && codigoAvaliador > 0) {
            sqlAtrasados.append("AND prof.codigocolaborador = ? ");
            params.add(codigoAvaliador);
        }

        if (paginator != null && paginator.getPage() != null && paginator.getSize() != null) {
            paginator.setQuantidadeTotalElementos((long) countAvaliacaoAtrasadaNova(ctx, empresa, codigoProfessor, codigoAvaliador, parametro));
            sqlAtrasados.append(montarPaginacao(paginator));
        }
        return avaliacaoDao.createPreparedStatement(ctx, sqlAtrasados.toString(), params.toArray());
    }


    private ResultSet sqlParq(String ctx, Integer empresa, boolean lista, Integer codigoProfessor, String parametro, PaginadorDTO paginator, Integer codigoAvaliador) throws Exception {
        StringBuilder sqlParqAssinadoPositivo = new StringBuilder();
        sqlParqAssinadoPositivo.append("WITH MaxCodigoPorCliente AS ( \n");
        sqlParqAssinadoPositivo.append("    SELECT cliente_codigo, MAX(codigo) AS max_codigo \n");
        sqlParqAssinadoPositivo.append("    FROM respostaclienteparq \n");
        sqlParqAssinadoPositivo.append("    GROUP BY cliente_codigo \n");
        sqlParqAssinadoPositivo.append(") \n");
        if (lista) {
            sqlParqAssinadoPositivo.append("SELECT DISTINCT(cli.codigo), cli.nome, cli.matricula \n");
        } else {
            sqlParqAssinadoPositivo.append("SELECT COUNT( DISTINCT(cli.codigo)) AS pq \n");
        }
        sqlParqAssinadoPositivo.append("FROM clientesintetico cli \n");
        sqlParqAssinadoPositivo.append("INNER JOIN pessoa p ON p.codigo = cli.pessoa_codigo \n");
        sqlParqAssinadoPositivo.append("INNER JOIN MaxCodigoPorCliente mcp ON mcp.cliente_codigo = cli.codigo \n");
        sqlParqAssinadoPositivo.append("INNER JOIN respostaclienteparq rcp ON rcp.codigo = mcp.max_codigo \n");
        sqlParqAssinadoPositivo.append("INNER JOIN respostacliente rc ON rc.respostaclienteparq_codigo = rcp.codigo \n");
        sqlParqAssinadoPositivo.append("LEFT JOIN professorsintetico prof ON cli.professorsintetico_codigo = prof.codigo \n");
        sqlParqAssinadoPositivo.append("WHERE cli.empresa = ").append(empresa).append(" \n");
        sqlParqAssinadoPositivo.append("AND rc.resposta = 'SIM' \n");
        sqlParqAssinadoPositivo.append("AND rcp.urlassinatura IS NOT NULL \n");
        sqlParqAssinadoPositivo.append("AND rcp.urlassinatura <> '' \n");

        if (StringUtils.isNotBlank(parametro) && parametro.matches("[0-9]*")) {
            sqlParqAssinadoPositivo.append("AND cli.matricula = ").append(parametro).append(" \n");
        } else if (StringUtils.isNotBlank(parametro)) {
            sqlParqAssinadoPositivo.append("AND UPPER(cli.nome) LIKE UPPER('%").append(parametro).append("%') \n");
        }

        if (codigoProfessor != null && codigoProfessor > 0) {
            if (codigoProfessor == Integer.MAX_VALUE) {
                sqlParqAssinadoPositivo.append("AND prof.codigo is null \n");
            } else {
                if (codigoProfessor == Integer.MAX_VALUE - 1) {
                    sqlParqAssinadoPositivo.append("AND prof.ativo = false \n");
                } else {
                    sqlParqAssinadoPositivo.append("AND prof.codigo = " + codigoProfessor + " \n");
                }
            }
        }

        if(paginator != null && (paginator.getPage() != null && paginator.getSize() != null)){
            paginator.setQuantidadeTotalElementos((long) countParq(ctx, empresa, codigoProfessor, parametro, codigoAvaliador));
            sqlParqAssinadoPositivo.append(montarPaginacao(paginator));
        }

        return avaliacaoDao.createStatement(ctx, sqlParqAssinadoPositivo.toString());
    }

    private ResultSet sqlObjetivos (String ctx, Integer empresa, boolean lista, String obj, Integer codigoProfessor, String parametro, PaginadorDTO paginator, Integer codigoAvaliador) throws Exception {
        //String sqlparq = "select count(codigo) as pq from clientesintetico  where parq ";

        StringBuilder sqlobjetivos = new StringBuilder();
        if (lista) {
            sqlobjetivos.append(" select cli.codigo, cli.matricula, cli.nome from itemavaliacaofisica i \n");
        } else {
            sqlobjetivos.append(" select result from itemavaliacaofisica i \n ");
        }

        sqlobjetivos.append(" inner join clientesintetico cli on cli.codigo = i.cliente_codigo \n");
        sqlobjetivos.append(" inner join avaliacaofisica av on av.codigo = i.avaliacaofisica_codigo \n");
        sqlobjetivos.append(" left join professorsintetico prof on cli.professorsintetico_codigo = prof.codigo \n");
        if(codigoProfessor == Integer.MAX_VALUE){
            sqlobjetivos.append(" left join empresa emp on cli.empresa = emp.codigo ");
        } else {
            sqlobjetivos.append(" left join empresa emp on prof.empresa_codigo = emp.codigo ");
        }
        sqlobjetivos.append(" where item = " + ItemAvaliacaoFisicaEnum.OBJETIVOS.ordinal()+" and i.avaliacaofisica_codigo is not null ");
        sqlobjetivos.append(" and result not in ('') and result is not null ");

        if(empresa != null && empresa > 0){
            sqlobjetivos.append(" and emp.codZW = ").append(empresa);
        }

        if(StringUtils.isNotBlank(obj)){
            sqlobjetivos.append("and result ilike '%"+obj+"%'" );
        }

        if(null != codigoProfessor && codigoProfessor > 0){
            if(codigoProfessor == Integer.MAX_VALUE){
                sqlobjetivos.append("and prof.codigo is null ");
            }else{
                if(codigoProfessor == Integer.MAX_VALUE - 1){
                    sqlobjetivos.append("and prof.ativo = false ");
                }else{
                    sqlobjetivos.append("and prof.codigo = ").append(codigoProfessor);
                }
            }
        }
        if(null != codigoAvaliador && codigoAvaliador > 0){
            sqlobjetivos.append(" and av.responsavelLancamento_codigo = ").append(codigoAvaliador);
        }
        if(StringUtils.isNotBlank(parametro)){
            int matricula;
            try{
                matricula = Integer.parseInt(parametro);
            }catch (Exception e){
                matricula = 0;
            }
            sqlobjetivos.append(" and (cli.nome ilike '%").append(parametro).append("%' ");
            if(matricula > 0){
                sqlobjetivos.append(" or cli.matricula = ").append(matricula);
            }
            sqlobjetivos.append(" ) ");
        }
        if(paginator != null && (paginator.getPage() != null && paginator.getSize() != null)){
            ResultSet rsobjetivos = sqlObjetivos(ctx, empresa, false, obj, codigoProfessor, parametro, null, codigoAvaliador);
            Long qtde = 0l;
            while (rsobjetivos.next()) {
                qtde++;
            }
            paginator.setQuantidadeTotalElementos(qtde);
            sqlobjetivos.append(montarPaginacao(paginator));
        }

        return avaliacaoDao.createStatement(ctx, sqlobjetivos.toString());
    }

    public ItemAvaliacaoFisicaTO montarItem(ResultSet rs, String avaliador) throws Exception {
        return new ItemAvaliacaoFisicaTO(
                rs.getInt("cliente_codigo"), Uteis.getNomeAbreviandoSomentePrimeiroSobrenome(rs.getString("nome")),
                rs.getDate("dataavaliacao"),
                rs.getDate("dataproxima"),
                Uteis.getNomeAbreviandoSomentePrimeiroSobrenome(avaliador),
                Uteis.getNomeAbreviandoSomentePrimeiroSobrenome(rs.getString("professor")),
                rs.getString("matricula"));
    }


    public ResultSet sqlPrevistas(String ctx, Integer empresa, boolean lista, Date inicio, Date fim,
                                  boolean previstas,
                                  boolean realizadas,
                                  boolean atrasadas,
                                  boolean futuras,
                                  Integer codigoProfessor,
                                  String parametro,
                                  PaginadorDTO paginator,
                                  Integer codigoAvaliador) throws Exception {
        StringBuilder sqlAvaliacoes = new StringBuilder();
        sqlAvaliacoes.append("SELECT ");
        if (lista) {
            sqlAvaliacoes.append("av.cliente_codigo, cli.nome, prof.nome as professor, av.dataproxima as dataavaliacao, av.dataproxima, cli.matricula ");
            if (SuperControle.independente(ctx)) {
                sqlAvaliacoes.append(", avaliador.nome as ava ");
            } else {
                sqlAvaliacoes.append(", av.responsavelLancamento_codigo as avaliadorCodigo, avaliador.nome ");
            }
        } else {
            sqlAvaliacoes.append("COUNT(av.codigo) as avaliacoes ");
        }
        sqlAvaliacoes.append("FROM avaliacaofisica av ");
        sqlAvaliacoes.append("INNER JOIN (SELECT usuariozw, MIN(codigo) AS min_codigo FROM usuario WHERE professor_codigo IS NOT NULL GROUP BY usuariozw ) u_min ON u_min.usuariozw = av.responsavellancamento_codigo ");
        sqlAvaliacoes.append("INNER JOIN usuario u ON u.codigo = u_min.min_codigo ");
        sqlAvaliacoes.append("INNER JOIN professorsintetico avaliador on avaliador.codigo = u.professor_codigo ");
        sqlAvaliacoes.append("INNER JOIN clientesintetico cli ON cli.codigo = av.cliente_codigo ");
        if (SuperControle.independente(ctx)) {
            sqlAvaliacoes.append("LEFT JOIN usuario us ON av.responsavelLancamento_codigo = us.codigo ");
            sqlAvaliacoes.append("LEFT JOIN professorsintetico avaliador ON us.professor_codigo = avaliador.codigo ");
        }
        sqlAvaliacoes.append("LEFT JOIN professorsintetico prof ON cli.professorsintetico_codigo = prof.codigo ");
        if (codigoProfessor == Integer.MAX_VALUE) {
            sqlAvaliacoes.append("LEFT JOIN empresa emp ON cli.empresa = emp.codigo ");
        } else {
            sqlAvaliacoes.append("LEFT JOIN empresa emp ON prof.empresa_codigo = emp.codigo ");
        }
        sqlAvaliacoes.append("WHERE dataproxima BETWEEN '").append(Uteis.getDataAplicandoFormatacao(inicio, "yyyy-MM-dd 00:00:00"));
        sqlAvaliacoes.append("' AND '").append(Uteis.getDataAplicandoFormatacao(fim, "yyyy-MM-dd 23:59:59")).append("' ");

//        if (previstas) {
//            sqlAvaliacoes.append("AND dataproxima BETWEEN CURRENT_DATE AND '").append(Uteis.getDataAplicandoFormatacao(fim, "yyyy-MM-dd 23:59:59")).append("' ");
//        }

        if (realizadas) {
            sqlAvaliacoes.append("AND EXISTS ( ");
            sqlAvaliacoes.append("    SELECT codigo FROM avaliacaofisica aa ");
            sqlAvaliacoes.append("    WHERE aa.cliente_codigo = av.cliente_codigo ");
            sqlAvaliacoes.append("    AND aa.dataavaliacao > av.dataavaliacao ");
            sqlAvaliacoes.append(") ");
        }
        if (atrasadas) {
            sqlAvaliacoes.append("AND dataproxima < CURRENT_DATE ");
            sqlAvaliacoes.append("AND NOT EXISTS ( ");
            sqlAvaliacoes.append("    SELECT codigo FROM avaliacaofisica aa ");
            sqlAvaliacoes.append("    WHERE aa.cliente_codigo = av.cliente_codigo ");
            sqlAvaliacoes.append("    AND (aa.dataavaliacao > av.dataproxima OR aa.dataavaliacao > av.dataavaliacao) ");
            sqlAvaliacoes.append(") ");
        }
        if (futuras) {
            sqlAvaliacoes.append("AND dataproxima > CURRENT_DATE ");
            sqlAvaliacoes.append("AND NOT EXISTS ( ");
            sqlAvaliacoes.append("    SELECT codigo FROM avaliacaofisica aa ");
            sqlAvaliacoes.append("    WHERE aa.cliente_codigo = av.cliente_codigo ");
            sqlAvaliacoes.append("    AND aa.dataavaliacao > av.dataproxima ");
            sqlAvaliacoes.append(") ");
        }

        if (empresa != null && empresa > 0) {
            sqlAvaliacoes.append("AND cli.empresa = ").append(empresa).append(" ");
        }

        if (codigoProfessor != null && codigoProfessor > 0) {
            if (codigoProfessor == Integer.MAX_VALUE) {
                sqlAvaliacoes.append("AND prof.codigo IS NULL ");
            } else {
                if (codigoProfessor == Integer.MAX_VALUE - 1) {
                    sqlAvaliacoes.append("AND prof.ativo = FALSE ");
                } else {
                    sqlAvaliacoes.append("AND prof.codigo = ").append(codigoProfessor).append(" ");
                }
            }
        }
        if (SuperControle.independente(ctx) && UteisValidacao.notEmptyNumber(codigoAvaliador) && codigoAvaliador > 0) {
            sqlAvaliacoes.append("AND avaliador.codigopessoa IN (SELECT p2.codigopessoa FROM professorsintetico p2 WHERE codigocolaborador = ").append(codigoAvaliador).append(") ");
        } else if (UteisValidacao.notEmptyNumber(codigoAvaliador) && codigoAvaliador > 0) {
            sqlAvaliacoes.append("AND av.responsavelLancamento_codigo = ").append(codigoAvaliador).append(" ");
        }

        if (StringUtils.isNotBlank(parametro)) {
            if (SuperControle.independente(ctx)) {
                sqlAvaliacoes.append(montarFiltro(parametro));
            } else {
                String filtro = montarFiltro(parametro);
                filtro = filtro.replace(" or avaliador.nome ilike '%" + parametro + "%'", "");
                sqlAvaliacoes.append(filtro);
            }
        }

        if(paginator != null && paginator.getSize() != null) {
            paginator.setQuantidadeTotalElementos((long) countReavaliacoes(ctx, empresa, inicio, fim, previstas, realizadas, atrasadas, futuras, codigoProfessor, parametro, codigoAvaliador));
            sqlAvaliacoes.append(montarPaginacao(paginator));
        }

        return avaliacaoDao.createStatement(ctx, sqlAvaliacoes.toString());
    }

    private String buildSqlPrevistasQuery(String ctx, Integer empresa, boolean lista, Date inicio, Date fim,
                                          boolean previstas, boolean realizadas, boolean atrasadas, boolean futuras,
                                          Integer codigoProfessor, String parametro, Integer codigoAvaliador) throws Exception {
        StringBuilder sqlAvaliacoes = new StringBuilder();
        sqlAvaliacoes.append("SELECT ");
        if (lista) {
            sqlAvaliacoes.append("av.cliente_codigo, cli.nome, prof.nome as professor, av.dataproxima as dataavaliacao, av.dataproxima, cli.matricula ");
            if (SuperControle.independente(ctx)) {
                sqlAvaliacoes.append(", avaliador.nome as ava ");
            } else {
                sqlAvaliacoes.append(", av.responsavelLancamento_codigo as avaliadorCodigo ");
            }
        } else {
            sqlAvaliacoes.append("COUNT(av.codigo) as avaliacoes ");
        }
        sqlAvaliacoes.append("FROM avaliacaofisica av ");
        sqlAvaliacoes.append("INNER JOIN (SELECT usuariozw, MIN(codigo) AS min_codigo FROM usuario WHERE professor_codigo IS NOT NULL GROUP BY usuariozw ) u_min ON u_min.usuariozw = av.responsavellancamento_codigo ");
        sqlAvaliacoes.append("INNER JOIN usuario u ON u.codigo = u_min.min_codigo ");
        sqlAvaliacoes.append("INNER JOIN professorsintetico avaliador on avaliador.codigo = u.professor_codigo ");
        sqlAvaliacoes.append("INNER JOIN clientesintetico cli ON cli.codigo = av.cliente_codigo ");
        if (SuperControle.independente(ctx)) {
            sqlAvaliacoes.append("LEFT JOIN usuario us ON av.responsavelLancamento_codigo = us.codigo ");
            sqlAvaliacoes.append("LEFT JOIN professorsintetico avaliador ON us.professor_codigo = avaliador.codigo ");
        }
        sqlAvaliacoes.append("LEFT JOIN professorsintetico prof ON cli.professorsintetico_codigo = prof.codigo ");
        if (codigoProfessor == Integer.MAX_VALUE) {
            sqlAvaliacoes.append("LEFT JOIN empresa emp ON cli.empresa = emp.codigo ");
        } else {
            sqlAvaliacoes.append("LEFT JOIN empresa emp ON prof.empresa_codigo = emp.codigo ");
        }
        sqlAvaliacoes.append("WHERE dataproxima BETWEEN '").append(Uteis.getDataAplicandoFormatacao(inicio, "yyyy-MM-dd 00:00:00"));
        sqlAvaliacoes.append("' AND '").append(Uteis.getDataAplicandoFormatacao(fim, "yyyy-MM-dd 23:59:59")).append("' ");

//        if (previstas) {
//            sqlAvaliacoes.append("AND dataproxima BETWEEN CURRENT_DATE AND '").append(Uteis.getDataAplicandoFormatacao(fim, "yyyy-MM-dd 23:59:59")).append("' ");
//        }
        if (realizadas) {
            sqlAvaliacoes.append("AND EXISTS ( ");
            sqlAvaliacoes.append("    SELECT codigo FROM avaliacaofisica aa ");
            sqlAvaliacoes.append("    WHERE aa.cliente_codigo = av.cliente_codigo ");
            sqlAvaliacoes.append("    AND aa.dataavaliacao > av.dataavaliacao ");
            sqlAvaliacoes.append(") ");
        }
        if (atrasadas) {
            sqlAvaliacoes.append("AND dataproxima < CURRENT_DATE ");
            sqlAvaliacoes.append("AND NOT EXISTS ( ");
            sqlAvaliacoes.append("    SELECT codigo FROM avaliacaofisica aa ");
            sqlAvaliacoes.append("    WHERE aa.cliente_codigo = av.cliente_codigo ");
            sqlAvaliacoes.append("    AND (aa.dataavaliacao > av.dataproxima OR aa.dataavaliacao > av.dataavaliacao) ");
            sqlAvaliacoes.append(") ");
        }
        if (futuras) {
            sqlAvaliacoes.append("AND dataproxima > CURRENT_DATE ");
            sqlAvaliacoes.append("AND NOT EXISTS ( ");
            sqlAvaliacoes.append("    SELECT codigo FROM avaliacaofisica aa ");
            sqlAvaliacoes.append("    WHERE aa.cliente_codigo = av.cliente_codigo ");
            sqlAvaliacoes.append("    AND aa.dataavaliacao > av.dataproxima ");
            sqlAvaliacoes.append(") ");
        }

        if (empresa != null && empresa > 0) {
            sqlAvaliacoes.append("AND cli.empresa = ").append(empresa).append(" ");
        }

        if (codigoProfessor != null && codigoProfessor > 0) {
            if (codigoProfessor == Integer.MAX_VALUE) {
                sqlAvaliacoes.append("AND prof.codigo IS NULL ");
            } else {
                if (codigoProfessor == Integer.MAX_VALUE - 1) {
                    sqlAvaliacoes.append("AND prof.ativo = FALSE ");
                } else {
                    sqlAvaliacoes.append("AND prof.codigo = ").append(codigoProfessor).append(" ");
                }
            }
        }
        if (SuperControle.independente(ctx) && UteisValidacao.notEmptyNumber(codigoAvaliador) && codigoAvaliador > 0) {
            sqlAvaliacoes.append("AND avaliador.codigopessoa IN (SELECT p2.codigopessoa FROM professorsintetico p2 WHERE codigocolaborador = ").append(codigoAvaliador).append(") ");
        } else if (UteisValidacao.notEmptyNumber(codigoAvaliador) && codigoAvaliador > 0) {
            sqlAvaliacoes.append("AND av.responsavelLancamento_codigo = ").append(codigoAvaliador).append(" ");
        }

        if (StringUtils.isNotBlank(parametro)) {
            sqlAvaliacoes.append(montarFiltro(parametro));
        }

        return sqlAvaliacoes.toString();
    }

    public int countReavaliacoesNova(final String key, Integer empresa, final Date inicio, final Date fim,
                                 boolean previstas, boolean realizadas, boolean atrasadas, boolean futuras,
                                 Integer codigoProfessor, String parametro, Integer codigoAvaliador) throws Exception {
        String query = buildSqlPrevistasQuery(key, empresa, false, inicio, fim, previstas, realizadas, atrasadas, futuras, codigoProfessor, parametro, codigoAvaliador);
        ResultSet rs = avaliacaoDao.createStatement(key, query);
        if (rs.next()) {
            return rs.getInt("avaliacoes");
        }
        return 0;
    }

    public ResultSet sqlPrevistasNova(String ctx, Integer empresa, boolean lista, Date inicio, Date fim,
                                  boolean previstas, boolean realizadas, boolean atrasadas, boolean futuras,
                                  Integer codigoProfessor, String parametro, PaginadorDTO paginator,
                                  Integer codigoAvaliador) throws Exception {
        String query = buildSqlPrevistasQuery(ctx, empresa, lista, inicio, fim, previstas, realizadas, atrasadas, futuras, codigoProfessor, parametro, codigoAvaliador);
        if (paginator != null && paginator.getSize() != null) {
            paginator.setQuantidadeTotalElementos((long) countReavaliacoesNova(ctx, empresa, inicio, fim, previstas, realizadas, atrasadas, futuras, codigoProfessor, parametro, codigoAvaliador));
            query += montarPaginacao(paginator);
        }
        return avaliacaoDao.createStatement(ctx, query);
    }




    public ResultSet sqlAvaliacoes(String ctx, Integer empresa, boolean lista, Date inicio, Date fim, boolean novas, boolean reavaliacoes, Integer codigoProfessor, String parametro, PaginadorDTO paginator, Integer codigoAvaliador) throws Exception {
        StringBuilder sqlAvaliacoes = new StringBuilder();
        sqlAvaliacoes.append("SELECT ");
        if (lista) {
            sqlAvaliacoes.append("av.cliente_codigo, cli.nome, cli.matricula, prof.nome AS professor, av.dataavaliacao, av.dataproxima");
            if (SuperControle.independente(ctx)) {
                sqlAvaliacoes.append(", avaliador.nome AS ava ");
            } else {
                sqlAvaliacoes.append(", av.responsavelLancamento_codigo AS avaliadorCodigo, avaliador.nome ");
            }
        } else {
            sqlAvaliacoes.append("COUNT(av.codigo) AS avaliacoes ");
        }
        sqlAvaliacoes.append("FROM avaliacaofisica av ");
        sqlAvaliacoes.append("INNER JOIN (SELECT usuariozw, MIN(codigo) AS min_codigo FROM usuario WHERE professor_codigo IS NOT NULL GROUP BY usuariozw ) u_min ON u_min.usuariozw = av.responsavellancamento_codigo ");
        sqlAvaliacoes.append("INNER JOIN usuario u ON u.codigo = u_min.min_codigo ");
        sqlAvaliacoes.append("INNER JOIN professorsintetico avaliador on avaliador.codigo = u.professor_codigo ");
        sqlAvaliacoes.append("INNER JOIN clientesintetico cli ON cli.codigo = av.cliente_codigo ");
        if (SuperControle.independente(ctx)) {
            sqlAvaliacoes.append("LEFT JOIN usuario us ON av.responsavelLancamento_codigo = us.codigo ");
            sqlAvaliacoes.append("LEFT JOIN professorsintetico avaliador ON us.professor_codigo = avaliador.codigo ");
        }
        sqlAvaliacoes.append("LEFT JOIN professorsintetico prof ON cli.professorsintetico_codigo = prof.codigo ");
        sqlAvaliacoes.append("LEFT JOIN empresa emp ON cli.empresa = emp.codzw ");
        sqlAvaliacoes.append("WHERE dataavaliacao BETWEEN '").append(Uteis.getDataAplicandoFormatacao(inicio, "yyyy-MM-dd"));
        sqlAvaliacoes.append(" 00:00:00' AND '").append(Uteis.getDataAplicandoFormatacao(fim, "yyyy-MM-dd"));
        sqlAvaliacoes.append(" 23:59:59' ");
        if (novas) {
            sqlAvaliacoes.append("AND av.codigo IN (SELECT codigo FROM avaliacaofisica aa WHERE aa.cliente_codigo = av.cliente_codigo ORDER BY codigo ASC LIMIT 1) ");
        }
        if (reavaliacoes) {
            sqlAvaliacoes.append("AND av.codigo IN (SELECT codigo FROM avaliacaofisica aa WHERE aa.cliente_codigo = av.cliente_codigo ORDER BY codigo ASC OFFSET 1)");
        }
        if (empresa != null && empresa > 0) {
            sqlAvaliacoes.append(" AND emp.codZW = ").append(empresa);
        }

        if (codigoProfessor != null && codigoProfessor > 0 && codigoAvaliador != null && codigoAvaliador > 0) {
            sqlAvaliacoes.append(" AND (prof.codigo = ").append(codigoProfessor).append(" OR av.responsavelLancamento_codigo = ").append(codigoAvaliador).append(") ");
        } else if (codigoProfessor != null && codigoProfessor > 0) {
            if (codigoProfessor == Integer.MAX_VALUE) {
                sqlAvaliacoes.append("AND prof.codigo IS NULL ");
            } else if (codigoProfessor == Integer.MAX_VALUE - 1) {
                sqlAvaliacoes.append("AND prof.ativo = FALSE ");
            } else {
                sqlAvaliacoes.append("AND prof.codigo = ").append(codigoProfessor);
            }
        } else if (UteisValidacao.notEmptyNumber(codigoAvaliador) && codigoAvaliador > 0) {
            if (SuperControle.independente(ctx)) {
                sqlAvaliacoes.append(" AND avaliador.pessoa_codigo IN (SELECT p2.pessoa_codigo FROM professorsintetico p2 WHERE codigo = ").append(codigoAvaliador).append(") ");
            } else {
                sqlAvaliacoes.append(" AND av.responsavelLancamento_codigo = ").append(codigoAvaliador).append(" ");
            }
        }

        if (StringUtils.isNotBlank(parametro)) {
            sqlAvaliacoes.append(montarFiltro(parametro));
        }
        if (paginator != null && paginator.getSize() != null) {
            paginator.setQuantidadeTotalElementos((long) countTodas(ctx, empresa, inicio, fim, novas, reavaliacoes, codigoProfessor, parametro, codigoAvaliador));
            sqlAvaliacoes.append(montarPaginacao(paginator));
        }
        return avaliacaoDao.createStatement(ctx, sqlAvaliacoes.toString());
    }

    public ResultSet sqlAvaliacoesNovas(String key, Date inicio, Date fim, Integer codigoProfessor, Integer codigoAvaliador,Integer codigoEmpresa, boolean lista,PaginadorDTO paginadorDTO, boolean novaOuReavaliacao, String parametro) throws Exception {
        int maxResults = MAXIMO_CONSULTAR;
        int indiceInicial=0;

        if (paginadorDTO != null) {
            maxResults = paginadorDTO.getSize() == null ? maxResults : paginadorDTO.getSize().intValue();
            indiceInicial = paginadorDTO.getPage() == null || paginadorDTO.getSize() == null ? indiceInicial : paginadorDTO.getPage().intValue() * paginadorDTO.getSize().intValue();
        }
        StringBuilder sql = new StringBuilder();
        StringBuilder sqlColunas = new StringBuilder();
        sqlColunas.append("SELECT ");
        if (lista) {
            sqlColunas.append("av.cliente_codigo, cli.nome, cli.matricula, prof.nome AS professor, av.dataavaliacao, av.dataproxima");
            if (SuperControle.independente(key)) {
                sqlColunas.append(", avaliador.nome AS ava ");
            } else {
                sqlColunas.append(", av.responsavelLancamento_codigo AS avaliadorCodigo, avaliador.nome ");
            }
        } else {
            sqlColunas.append("COUNT(av.codigo) AS avaliacoes ");
        }
        sql.append("FROM avaliacaofisica av ");
        sql.append("INNER JOIN (SELECT usuariozw, MIN(codigo) AS min_codigo FROM usuario WHERE professor_codigo IS NOT NULL GROUP BY usuariozw ) u_min ON u_min.usuariozw = av.responsavellancamento_codigo ");
        sql.append("INNER JOIN usuario u ON u.codigo = u_min.min_codigo ");
        sql.append("INNER JOIN professorsintetico avaliador on avaliador.codigo = u.professor_codigo ");
        sql.append("INNER JOIN (");
        sql.append("  SELECT cliente_codigo, MIN(codigo) AS primeira_avaliacao ");
        sql.append("  FROM avaliacaofisica ");
        sql.append("  GROUP BY cliente_codigo ");
        sql.append(") AS primeiras_avaliacoes ON av.cliente_codigo = primeiras_avaliacoes.cliente_codigo ");
        if(novaOuReavaliacao){
            //Avaliacao nova
            sql.append(" AND av.codigo = primeiras_avaliacoes.primeira_avaliacao ");
        }else{
            //Reavaliacao
            sql.append(" AND av.codigo <> primeiras_avaliacoes.primeira_avaliacao ");
        }
        sql.append("INNER JOIN clientesintetico cli ON cli.codigo = av.cliente_codigo ");
        if (SuperControle.independente(key)) {
            sql.append("LEFT JOIN usuario us ON av.responsavelLancamento_codigo = us.codigo ");
            sql.append("LEFT JOIN professorsintetico avaliador ON us.professor_codigo = avaliador.codigo ");
        }
        sql.append("LEFT JOIN professorsintetico prof ON cli.professorsintetico_codigo = prof.codigo ");
        sql.append("LEFT JOIN empresa emp ON cli.empresa = emp.codzw ");
        sql.append("WHERE av.dataavaliacao BETWEEN '").append(Uteis.getDataAplicandoFormatacao(inicio, "yyyy-MM-dd 00:00:00")).append("' AND '").append(Uteis.getDataAplicandoFormatacao(fim, "yyyy-MM-dd 23:59:59")).append("' ");
        sql.append("AND emp.codZW = "+codigoEmpresa+" ");
        if (codigoProfessor != null && codigoProfessor > 0 && codigoAvaliador != null && codigoAvaliador > 0) {
            sql.append(" AND (prof.codigo = ").append(codigoProfessor).append(" OR av.responsavelLancamento_codigo = ").append(codigoAvaliador).append(") ");
        } else if (codigoProfessor != null && codigoProfessor > 0) {
            sql.append(" AND prof.codigo = ").append(codigoProfessor).append(" ");
        } else if (codigoAvaliador != null && codigoAvaliador > 0) {
            sql.append(" AND av.responsavelLancamento_codigo = ").append(codigoAvaliador).append(" ");
        }

        if (parametro != null && !parametro.equals("")) {
            int matricula = 0;
            try {
                matricula = Integer.parseInt(parametro);
            } catch (Exception e) {
            }
            if (matricula == 0) {
                sql.append(" AND cli.nomeconsulta LIKE remove_acento_upper('").append(parametro).append("%') or cli.nome LIKE ('").append(parametro.toUpperCase()).append("%')");
            } else {
                sql.append(" AND cli.matricula = ").append(matricula).append(" ");
            }

        }

        if (paginadorDTO != null) {
            StringBuilder sqlCount = new StringBuilder();
            sqlCount.append("select count(av.cliente_codigo) as  count ");
            ResultSet rsTotal = avaliacaoDao.createStatement(key, sqlCount.append(sql).toString());
            Long totalCount = Long.valueOf(rsTotal.next() ? rsTotal.getInt("count") : 0);
            paginadorDTO.setQuantidadeTotalElementos(totalCount);
            sql.append(montarPaginacao(paginadorDTO));
        }


            return avaliacaoDao.createStatement(key,sqlColunas.append(sql).toString());
    }


    private String montarPaginacao(PaginadorDTO paginator) throws Exception {
        StringBuilder sql = new StringBuilder();
        int index = paginator.getSize().intValue()*paginator.getPage().intValue();

        if(paginator.getSort() == null){
            paginator.setSort("nome,ASC");
        }

        paginator.setSort(getIndexToOrderColunm(paginator.getSort()));
        sql.append(paginator.getSQLOrderBy());

        if(paginator.getQuantidadeTotalElementos() < index){
            index = 0;
        }
        sql.append(" LIMIT ").append(paginator.getSize());
        sql.append(" OFFSET ").append(index);
        return sql.toString();
    }

    public String obterUsuariosZwOrdenados(String ctx, String ordenacao) {
        String codigosOrdenados = "";
        if (!SuperControle.independente(ctx)) {
            try {
                try (Connection con = conexaoZWService.conexaoZw(ctx)) {
                    try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta("SELECT u.codigo FROM usuario u ORDER BY u.nome " + ordenacao, con)) {
                        while (rs.next()) {
                            codigosOrdenados += "," + rs.getString("codigo");
                        }
                    }
                }
            } catch (Exception ex) {
                ex.printStackTrace();
                Uteis.logarDebug("Erro ao obterUsuariosZwOrdenados: " + ex.getMessage());
            }
        }
        if (!UteisValidacao.emptyString(codigosOrdenados)) {
            codigosOrdenados = codigosOrdenados.substring(1);
        }
        return codigosOrdenados;
    }

    private String getIndexToOrderColunm(String nomeColuna){
        String[] orderColuna = nomeColuna.split(",");
        switch (orderColuna[0]){
            case "matricula":
                return "cli.matricula,"+orderColuna[1];
            case "nome":
                return "cli.nome,"+orderColuna[1];
            case "dataAvaliacao":
                return "av.dataavaliacao,"+orderColuna[1];
            case "avaliador":
                return "avaliador.nome,"+orderColuna[1];
            case "professor":
                return "prof.nome,"+orderColuna[1];
            default:
                return nomeColuna;
        }
    }

    private String  montarFiltro(String parametro) {
        StringBuilder sqlAvaliacoes = new StringBuilder();
        int matricula;
        try{
            matricula = Integer.parseInt(parametro);
        }catch (Exception e){
            matricula = 0;
        }
        sqlAvaliacoes.append(" and (prof.nome ilike '%").append(parametro).append("%' ");
        if(matricula > 0){
            sqlAvaliacoes.append(" or cli.matricula = ").append(matricula);
        }
        sqlAvaliacoes.append(" or cli.nome ilike '%").append(parametro).append("%' ");
        sqlAvaliacoes.append(" or avaliador.nome ilike '%").append(parametro).append("%' ");
        sqlAvaliacoes.append(")");
        return sqlAvaliacoes.toString();
    }


    public int countTodas(final String key, Integer empresa, final Date inicio, final Date fim, boolean novas, boolean reavaliacoes, Integer codigoProfessor, String parametro, Integer codigoAvaliador) throws Exception {
        ResultSet rs = sqlAvaliacoes(key, empresa,false, inicio, fim, novas, reavaliacoes, codigoProfessor, parametro, null, codigoAvaliador);
        if (rs.next()) {
            return rs.getInt("avaliacoes");
        }
        return 0;
    }

    public BIAvaliacaoFisicaTO obterDadosAvaliacoes(String key, Integer empresa, Date inicio, Date fim, Integer codigoProfessor, String parametro, Integer codigoAvaliador) throws Exception {
        BIAvaliacaoFisicaTO dados = new BIAvaliacaoFisicaTO();

        StringBuilder sql = new StringBuilder();
        sql.append("SELECT COUNT(av.codigo) AS total_avaliacoes ");
        sql.append("FROM avaliacaofisica av ");
        sql.append("INNER JOIN (SELECT usuariozw, MIN(codigo) AS min_codigo FROM usuario WHERE professor_codigo IS NOT NULL GROUP BY usuariozw ) u_min ON u_min.usuariozw = av.responsavellancamento_codigo ");
        sql.append("INNER JOIN usuario u ON u.codigo = u_min.min_codigo ");
        sql.append("INNER JOIN professorsintetico avaliador on avaliador.codigo = u.professor_codigo ");
        sql.append("INNER JOIN clientesintetico cli ON cli.codigo = av.cliente_codigo ");
        sql.append("LEFT JOIN empresa emp ON cli.empresa = emp.codzw ");
        sql.append("LEFT JOIN professorsintetico prof ON cli.professorsintetico_codigo = prof.codigo ");

        if (SuperControle.independente(key)) {
            sql.append("LEFT JOIN usuario us ON av.responsavelLancamento_codigo = us.codigo ");
            sql.append("LEFT JOIN professorsintetico avaliador ON us.professor_codigo = avaliador.codigo ");
        }

        sql.append("WHERE av.dataavaliacao BETWEEN '")
                .append(Uteis.getDataAplicandoFormatacao(inicio, "yyyy-MM-dd 00:00:00"))
                .append("' AND '")
                .append(Uteis.getDataAplicandoFormatacao(fim, "yyyy-MM-dd 23:59:59"))
                .append("' ");

        if (empresa != null && empresa > 0) {
            sql.append("AND emp.codZW = ").append(empresa).append(" ");
        }

        if (codigoProfessor != null && codigoProfessor > 0 && codigoAvaliador != null && codigoAvaliador > 0) {
            sql.append("AND (");
            if (SuperControle.independente(key)) {
                sql.append("avaliador.pessoa_codigo IN (SELECT p2.pessoa_codigo FROM professorsintetico p2 WHERE codigo = ").append(codigoAvaliador).append(") ");
            } else {
                sql.append("av.responsavelLancamento_codigo = ").append(codigoAvaliador).append(" ");
            }
            sql.append("OR prof.codigo = ").append(codigoProfessor).append(") ");
        } else if (codigoProfessor != null && codigoProfessor > 0) {
            if (codigoProfessor == Integer.MAX_VALUE) {
                sql.append("AND prof.codigo IS NULL ");
            } else if (codigoProfessor == Integer.MAX_VALUE - 1) {
                sql.append("AND prof.ativo = FALSE ");
            } else {
                sql.append("AND prof.codigo = ").append(codigoProfessor).append(" ");
            }
        } else if (codigoAvaliador != null && codigoAvaliador > 0) {
            if (SuperControle.independente(key)) {
                sql.append("AND avaliador.pessoa_codigo IN (SELECT p2.pessoa_codigo FROM professorsintetico p2 WHERE codigo = ").append(codigoAvaliador).append(") ");
            } else {
                sql.append("AND av.responsavelLancamento_codigo = ").append(codigoAvaliador).append(" ");
            }
        }

        if (StringUtils.isNotBlank(parametro)) {
            sql.append(montarFiltro(parametro));
        }

        try (ResultSet rs = avaliacaoDao.createStatement(key, sql.toString())) {
            if (rs.next()) {
                dados.setAvaliacoes(rs.getInt("total_avaliacoes"));
            }
        }

        return dados;
    }


    public int obterTotalNovasAvaliacoes(String key, Date inicio, Date fim, Integer codigoProfessor, Integer codigoAvaliador, Integer codigoEmpresa) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT COUNT(aa.codigo) AS total_novas ");
        sql.append("FROM avaliacaofisica aa ");
        sql.append("INNER JOIN (SELECT usuariozw, MIN(codigo) AS min_codigo FROM usuario WHERE professor_codigo IS NOT NULL GROUP BY usuariozw ) u_min ON u_min.usuariozw = aa.responsavellancamento_codigo ");
        sql.append("INNER JOIN usuario u ON u.codigo = u_min.min_codigo ");
        sql.append("INNER JOIN professorsintetico avaliador on avaliador.codigo = u.professor_codigo ");
        sql.append("INNER JOIN (");
        sql.append("  SELECT cliente_codigo, MIN(codigo) AS primeira_avaliacao ");
        sql.append("  FROM avaliacaofisica ");
        sql.append("  GROUP BY cliente_codigo ");
        sql.append(") AS primeiras_avaliacoes ON aa.cliente_codigo = primeiras_avaliacoes.cliente_codigo AND aa.codigo = primeiras_avaliacoes.primeira_avaliacao ");
        sql.append("INNER JOIN clientesintetico cli ON cli.codigo = aa.cliente_codigo ");
        sql.append("LEFT JOIN professorsintetico p ON cli.professorsintetico_codigo = p.codigo ");
        sql.append("LEFT JOIN empresa emp ON cli.empresa = emp.codzw ");
        sql.append("WHERE aa.dataavaliacao BETWEEN '").append(Uteis.getDataAplicandoFormatacao(inicio, "yyyy-MM-dd 00:00:00")).append("' AND '").append(Uteis.getDataAplicandoFormatacao(fim, "yyyy-MM-dd 23:59:59")).append("' ");
        sql.append("AND emp.codZW = +"+codigoEmpresa+" ");
        if (codigoProfessor != null && codigoProfessor > 0 && codigoAvaliador != null && codigoAvaliador > 0) {
            sql.append(" AND (p.codigo = ").append(codigoProfessor).append(" OR aa.responsavelLancamento_codigo = ").append(codigoAvaliador).append(") ");
        } else if (codigoProfessor != null && codigoProfessor > 0) {
            sql.append(" AND p.codigo = ").append(codigoProfessor).append(" ");
        } else if (codigoAvaliador != null && codigoAvaliador > 0) {
            sql.append(" AND aa.responsavelLancamento_codigo = ").append(codigoAvaliador).append(" ");
        }

        try (ResultSet rs = avaliacaoDao.createStatement(key, sql.toString())) {
            if (rs.next()) {
                return rs.getInt("total_novas");
            }
        }

        return 0;
    }

    public int obterTotalReavaliacoes(String key, Date inicio, Date fim, Integer codigoProfessor, Integer codigoAvaliador, Integer codigoEmpresa) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT COUNT(aa.codigo) AS total_reavaliacoes ");
        sql.append("FROM avaliacaofisica aa ");
        sql.append("INNER JOIN (SELECT usuariozw, MIN(codigo) AS min_codigo FROM usuario WHERE professor_codigo IS NOT NULL GROUP BY usuariozw ) u_min ON u_min.usuariozw = aa.responsavellancamento_codigo ");
        sql.append("INNER JOIN usuario u ON u.codigo = u_min.min_codigo ");
        sql.append("INNER JOIN professorsintetico avaliador on avaliador.codigo = u.professor_codigo ");
        sql.append("INNER JOIN (");
        sql.append("  SELECT cliente_codigo, MIN(codigo) AS primeira_avaliacao ");
        sql.append("  FROM avaliacaofisica ");
        sql.append("  GROUP BY cliente_codigo ");
        sql.append(") AS primeiras_avaliacoes ON aa.cliente_codigo = primeiras_avaliacoes.cliente_codigo AND aa.codigo <> primeiras_avaliacoes.primeira_avaliacao ");
        sql.append("INNER JOIN clientesintetico cli ON cli.codigo = aa.cliente_codigo ");
        sql.append("LEFT JOIN professorsintetico p ON cli.professorsintetico_codigo = p.codigo ");
        sql.append("LEFT JOIN empresa emp ON cli.empresa = emp.codzw  ");
        sql.append("WHERE aa.dataavaliacao BETWEEN '").append(Uteis.getDataAplicandoFormatacao(inicio, "yyyy-MM-dd 00:00:00")).append("' AND '").append(Uteis.getDataAplicandoFormatacao(fim, "yyyy-MM-dd 23:59:59")).append("' ");
        sql.append("AND emp.codZW = "+codigoEmpresa+" ");
        if (codigoProfessor != null && codigoProfessor > 0 && codigoAvaliador != null && codigoAvaliador > 0) {
            sql.append(" AND (p.codigo = ").append(codigoProfessor).append(" OR aa.responsavelLancamento_codigo = ").append(codigoAvaliador).append(") ");
        } else if (codigoProfessor != null && codigoProfessor > 0) {
            sql.append(" AND p.codigo = ").append(codigoProfessor).append(" ");
        } else if (codigoAvaliador != null && codigoAvaliador > 0) {
            sql.append(" AND aa.responsavelLancamento_codigo = ").append(codigoAvaliador).append(" ");
        }

        try (ResultSet rs = avaliacaoDao.createStatement(key, sql.toString())) {
            if (rs.next()) {
                return rs.getInt("total_reavaliacoes");
            }
        }

        return 0;
    }



    public int countReavaliacoes(final String key, Integer empresa, final Date inicio, final Date fim,
                                 boolean previstas,
                                 boolean realizadas,
                                 boolean atrasadas,
                                 boolean futuras,
                                 Integer codigoProfessor,
                                 String parametro,
                                 Integer codigoAvaliador) throws Exception {
        ResultSet rs = sqlPrevistas(key, empresa, false, inicio, fim, previstas, realizadas, atrasadas, futuras, codigoProfessor, parametro, null, codigoAvaliador);
        if (rs.next()) {
            return rs.getInt("avaliacoes");
        }
        return 0;
    }

    public int countSemAvaliacao(final String key, Integer codigoEmpresa, Integer codigoProfessor, String parametro, Integer codigoAvaliador) throws Exception {
        ResultSet rs3 = sqlSemAvaliacao(key, codigoEmpresa,false, codigoProfessor, parametro, null, codigoAvaliador);
        if (rs3.next()) {
            return rs3.getInt("alunos");
        }
        return 0;
    }

    public int countAvaliacaoAtrasada(final String key, Integer codigoEmpresa, Integer codigoProfessor, String parametro, Integer codigoAvaliador) throws Exception {
        ResultSet rs4 = sqlAtrasados(key, codigoEmpresa, false, codigoProfessor, parametro, null, codigoAvaliador);
        while (rs4.next()) {
            return rs4.getInt("alunos");
        }
        return 0;
    }

    public int countAvaliacaoAtrasadaNova(final String key, Integer codigoEmpresa, Integer codigoProfessor, Integer codigoAvaliador,String parametro) throws Exception {
        int count = 0;
        try (ResultSet rs4 = sqlAtrasadosNova(key, codigoEmpresa, codigoProfessor, codigoAvaliador, parametro, null)) {
            while (rs4.next()) {
                count ++;
            }
        }
        return count;
    }

    public int countParq(final String key, Integer codigoEmpresa, Integer codigoProfessor, String parametro, Integer codigoAvaliador) throws Exception {
        ResultSet rsparq = sqlParq(key, codigoEmpresa, false, codigoProfessor, parametro, null, codigoAvaliador);
        if (rsparq.next()) {
            return rsparq.getInt("pq");
        }
        return 0;
    }

    @Override
    public BIAvaliacaoFisicaTO montarBI(final String key, Integer codigoEmpresa, final Date inicio, final Date fim, Integer codigoProfessor, Integer codigoAvaliador) throws Exception {
        BIAvaliacaoFisicaTO dados = new BIAvaliacaoFisicaTO();

        // Obter dados de avaliações
        BIAvaliacaoFisicaTO avaliacoes = obterDadosAvaliacoes(key, codigoEmpresa, inicio, fim, codigoProfessor, null, codigoAvaliador);
        dados.setAvaliacoes(avaliacoes.getAvaliacoes());

        // Obter totais de avaliações
        dados.setNovas(obterTotalNovasAvaliacoes(key, inicio, fim, codigoProfessor, codigoAvaliador, codigoEmpresa));
        dados.setReavaliacoes(obterTotalReavaliacoes(key, inicio, fim, codigoProfessor, codigoAvaliador, codigoEmpresa));

        // Obter previsões
        dados.setPrevistas(countReavaliacoesNova(key, codigoEmpresa, inicio, fim, true, false, false, false, codigoProfessor, null, codigoAvaliador));
        dados.setRealizadas(countReavaliacoesNova(key, codigoEmpresa, inicio, fim, false, true, false, false, codigoProfessor, null, codigoAvaliador));
        dados.setAtrasadas(countReavaliacoesNova(key, codigoEmpresa, inicio, fim, false, false, true, false, codigoProfessor, null, codigoAvaliador));
        dados.setFuturas(countReavaliacoesNova(key, codigoEmpresa, inicio, fim, false, false, false, true, codigoProfessor, null, codigoAvaliador));

        // Obter outros dados
        dados.setSemAvaliacao(countSemAvaliacao(key, codigoEmpresa, codigoProfessor, null, codigoAvaliador));
        dados.setAtivosAtrasada(countAvaliacaoAtrasadaNova(key, codigoEmpresa, codigoProfessor, codigoAvaliador, null));
        dados.setAlunosParq(countParq(key, codigoEmpresa, codigoProfessor, null, codigoAvaliador));
        ArrayList<Map.Entry<String, Integer>> listaContaContaResultadosAlunos = contaResultadosAlunos(key, codigoEmpresa, codigoProfessor, IndicadorAvaliacaoFisicaEnum.IND_AF_PERDERAM_PERCENTUAL);
        for (Map.Entry<String, Integer> entrada : listaContaContaResultadosAlunos) {
            switch(entrada.getKey()) {
                case "ganharammassamagra":
                    dados.setGanharamMassaMagra(entrada.getValue());
                    break;
                case "perderamgordura":
                    dados.setPerderamGordura(entrada.getValue());
                    break;
                case "perderampeso":
                    dados.setPerderamPeso(entrada.getValue());
                    break;
            }
        }
        // Obter e processar objetivos
        ResultSet rsobjetivos = sqlObjetivos(key, codigoEmpresa, false, "", codigoProfessor, null, null, codigoAvaliador);
        processarObjetivos(rsobjetivos, dados);
        return dados;
    }

    private ArrayList<Map.Entry<String, Integer>> contaResultadosAlunos(String key, Integer codigoEmpresa, Integer codigoProfessor, IndicadorAvaliacaoFisicaEnum indAfPerderamPercentual) throws ServiceException {
        return clienteSinteticoDao.countResultadosAlunos(key, indAfPerderamPercentual, codigoEmpresa, codigoProfessor, null, null
        );
    }

    private void processarObjetivos(ResultSet rsobjetivos, BIAvaliacaoFisicaTO dados) throws SQLException {
        Map<String, Integer> objs = new HashMap<>();
        while (rsobjetivos.next()) {
            String result = rsobjetivos.getString("result");
            String[] splitObjs = result.split("\\|\\#\\|");
            for (String o : splitObjs) {
                objs.put(o.toLowerCase(), objs.getOrDefault(o.toLowerCase(), 0) + 1);
            }
        }

        List<PaletaCoresEnum> listaCores = getListaCores();
        List<String> nomes = new ArrayList<>(objs.keySet());
        Collections.sort(nomes);
        dados.setGrafico(new JSONArray());
        int i = 0;
        for (String o : nomes) {
            if (UteisValidacao.emptyString(o)) continue;
            JSONObject json = new JSONObject();
            Matcher matcher = Pattern.compile("\\((.*?)\\)(\\w+)").matcher(o);
            if (matcher.find()) {
                String nome = matcher.group(2);
                json.put("obj", Uteis.firstLetterUpper(nome));
            } else {
                json.put("obj", Uteis.firstLetterUpper(o));
            }
            json.put("valor", objs.get(o));
            json.put("cor", i < listaCores.size() ? listaCores.get(i++).getCor() : PaletaCoresEnum.randomCor(false).getCor());
            dados.getGrafico().put(json);
        }
    }

    private int countResultadosAlunos(String key, Integer empresa, Integer codigoProfessor, IndicadorAvaliacaoFisicaEnum indicador) throws Exception {
        List<ClienteSintetico> alunos = montarResultadosAlunos(key, empresa, indicador, codigoProfessor, null, null);
        return alunos.size();
    }

    public List<ClienteSintetico> montarResultadosAlunos(String key, Integer empresa, IndicadorAvaliacaoFisicaEnum ind, Integer codigoProfessor, String parametro, PaginadorDTO paginator) throws Exception {
        return clienteSinteticoDao.consultarResultadoEvolucaoBiPorIndicadorAvaliacaoFisica(
                key,
                ind,
                empresa,
                codigoProfessor,
                parametro,
                paginator
        );
    }


    public List<PaletaCoresEnum> getListaCores() {
        List<PaletaCoresEnum> l = new ArrayList<PaletaCoresEnum>();
        l.add(PaletaCoresEnum.VERMELHO_A);
        l.add(PaletaCoresEnum.LARANJA_A);
        l.add(PaletaCoresEnum.LARANJA_F);
        l.add(PaletaCoresEnum.LARANJA_J);
        l.add(PaletaCoresEnum.AMARELO_A);
        l.add(PaletaCoresEnum.VERDE_LIMAO_E);
        l.add(PaletaCoresEnum.VERDE_A);
        l.add(PaletaCoresEnum.AZUL_A);
        l.add(PaletaCoresEnum.AZUL_ESCURO_E);
        l.add(PaletaCoresEnum.AZUL_ESCURO_A);
        l.add(PaletaCoresEnum.ROXO_E);
        l.add(PaletaCoresEnum.MARROM_F);
        l.add(PaletaCoresEnum.VERMELHO_B);
        l.add(PaletaCoresEnum.LARANJA_E);
        l.add(PaletaCoresEnum.LARANJA_C);
        l.add(PaletaCoresEnum.LARANJA_H);
        l.add(PaletaCoresEnum.AMARELO_A);
        l.add(PaletaCoresEnum.VERDE_LIMAO_A);
        l.add(PaletaCoresEnum.VERDE_E);
        l.add(PaletaCoresEnum.AZUL_J);
        l.add(PaletaCoresEnum.AZUL_E);
        l.add(PaletaCoresEnum.AZUL_ESCURO_C);
        l.add(PaletaCoresEnum.ROXO_A);
        l.add(PaletaCoresEnum.MARROM_B);
        return l;
    }

    public void atualizarBase(final String key, Integer empresa){
        try {
            avaliacaoDao.executeNativeSQL(key, "update clientesintetico c\n" +
                    "        set massamagrainicio = (select massamagra from avaliacaofisica a2 where cliente_codigo = c.codigo order by dataavaliacao, codigo limit 1),\n" +
                    "        massamagraatual = (select massamagra from avaliacaofisica a2 where cliente_codigo = c.codigo order by dataavaliacao desc, codigo desc limit 1),\n" +
                    "        pesoinicio = (select peso from avaliacaofisica a2 where cliente_codigo = c.codigo order by dataavaliacao, codigo limit 1),\n" +
                    "        pesoatual = (select peso from avaliacaofisica a2 where cliente_codigo = c.codigo order by dataavaliacao desc, codigo desc limit 1),\n" +
                    "        percentualgordurainicio = (select percentualgordura from avaliacaofisica a2 where cliente_codigo = c.codigo order by dataavaliacao, codigo limit 1),\n" +
                    "        percentualgorduraatual = (select percentualgordura from avaliacaofisica a2 where cliente_codigo = c.codigo order by dataavaliacao desc, codigo desc limit 1)\n" +
                    "        where exists (select codigo from avaliacaofisica a where cliente_codigo = c.codigo  limit 1) and c.empresa = " + empresa
            );
        }catch (Exception e){
            Uteis.logar(e, BIAvaliacaoFisicaService.class);
        }

    }
}
