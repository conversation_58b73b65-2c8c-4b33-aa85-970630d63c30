package br.com.pacto.service.impl.retiraficha;

import br.com.pacto.bean.retiraficha.ConfiguracaoRetiraFicha;
import br.com.pacto.bean.retiraficha.ConfiguracaoRetiraFichaLog;
import br.com.pacto.dao.impl.base.DaoGenericoImpl;
import br.com.pacto.dao.intf.retiraficha.ConfiguracaoRetiraFichaLogDao;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;

@Repository
public class ConfiguracaoRetiraFichaLogDaoImpl extends DaoGenericoImpl<ConfiguracaoRetiraFichaLog, Integer> implements ConfiguracaoRetiraFichaLogDao {

    @Override
    public List<ConfiguracaoRetiraFichaLog> listarLogsAlteracoes(String contexto, Integer codigoConfiguracaoRetiraFicha) throws Exception {
        final String hql = new StringBuilder("SELECT configLog FROM ")
                .append(ConfiguracaoRetiraFichaLog.class.getSimpleName()).append(" configLog ")
                .append("where configLog.codigoConfiguracaoRetiraFicha  = :codigo_configuracao_retira_ficha ")
                .toString();

        HashMap<String, Object> param = new HashMap<String, Object>();

        param.put("codigo_configuracao_retira_ficha", codigoConfiguracaoRetiraFicha);
        return findByParam(contexto, hql, param);
    }
}
