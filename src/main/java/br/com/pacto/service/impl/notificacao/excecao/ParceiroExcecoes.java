package br.com.pacto.service.impl.notificacao.excecao;

import br.com.pacto.service.exception.ExcecaoSistema;

public enum ParceiroExcecoes implements ExcecaoSistema {
    ERRO_INCLUIR_PARCEIRO("erro_incluir_parceiro", "Ocorreu um erro ao incluir o parceiro"),
    ERRO_LISTAR_PARCEIROS("erro_listar_parceiros", "Ocorreu um erro ao listar os parceiros"),
    ERRO_ALTERAR_PARCEIRO("erro_alterar_parceiro", "Ocorreu um erro ao alterar o parceiro"),

    ERRO_SITUACAO_NAO_INFORMADA("erro_situacao_nao_informada", "A situacao do parceiro não foi informado!"),
    ERRO_ID_NAO_INFORMADA("erro_id_nao_informada", "O id do parceiro não foi informado!"),
    ERRO_PARCEIRO_NAO_EXISTE("erro_parceiro_nao_existe", "O parceiro não existe!");

    private String chave;
    private String descricao;

    ParceiroExcecoes(String chave, String descricao) {
        this.chave = chave;
        this.descricao = descricao;
    }

    @Override
    public String getChaveExcecao() {
        return chave;
    }
    @Override
    public String getDescricaoExcecao() {
        return descricao;
    }
}
