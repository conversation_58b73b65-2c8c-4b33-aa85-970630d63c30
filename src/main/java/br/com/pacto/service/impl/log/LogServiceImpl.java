package br.com.pacto.service.impl.log;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.cliente.AcaoAlunoEnum;
import br.com.pacto.bean.log.Log;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.bean.perfil.permissao.TipoPermissaoEnum;
import br.com.pacto.bean.perfil.permissao.TipoRecurso;
import br.com.pacto.bean.sincronizacao.TipoRevisaoEnum;
import br.com.pacto.controller.json.ambiente.TipoAmbienteResponseTO;
import br.com.pacto.controller.json.log.EntidadeLogEnum;
import br.com.pacto.controller.json.log.ItemExportacaoEnum;
import br.com.pacto.dao.intf.anamnese.AnamneseDao;
import br.com.pacto.dao.intf.agenda.AgendamentoDao;
import br.com.pacto.dao.intf.anamnese.AnamneseDao;
import br.com.pacto.dao.intf.aparelho.AparelhoDao;
import br.com.pacto.dao.intf.atividade.AtividadeDao;
import br.com.pacto.dao.intf.atividade.CategoriaAtividadeDao;
import br.com.pacto.dao.intf.avaliacao.AvaliacaoFisicaDao;
import br.com.pacto.dao.intf.benchmark.TipoBenchmarkDao;
import br.com.pacto.dao.intf.cliente.ClienteSinteticoDao;
import br.com.pacto.dao.intf.log.LogDao;
import br.com.pacto.dao.intf.musculo.GrupoMuscularDao;
import br.com.pacto.dao.intf.musculo.MusculoDao;
import br.com.pacto.dao.intf.perfil.PerfilDao;
import br.com.pacto.dao.intf.usuario.UsuarioDao;
import br.com.pacto.dao.intf.wod.WodDao;
import br.com.pacto.enumerador.agenda.StatusAgendamentoEnum;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.conexao.ConexaoZWServiceImpl;
import br.com.pacto.service.intf.conexao.ConexaoZWService;
import br.com.pacto.service.intf.log.LogProgramaService;
import br.com.pacto.service.intf.log.LogService;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;
import br.com.pacto.util.ViewUtils;
import br.com.pacto.util.enumeradores.DiasSemana;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.sql.ResultSet;
import java.util.*;

import static java.util.Objects.isNull;
import static org.apache.commons.lang3.StringUtils.isNotBlank;
import static org.apache.commons.lang3.StringUtils.isNumeric;


@Service
public class LogServiceImpl implements LogService {

    @Autowired
    private AgendamentoDao agendamentoDao;
    @Autowired
    private AtividadeDao atividadeDao;
    @Autowired
    private AparelhoDao aparelhoDao;
    @Autowired
    private ClienteSinteticoDao clienteDao;
    @Autowired
    private GrupoMuscularDao grupoMuscularDao;
    @Autowired
    private MusculoDao musculoDao;
    @Autowired
    private WodDao wodDao;
    @Autowired
    private CategoriaAtividadeDao categoriaAtividadeDao;
    @Autowired
    private TipoBenchmarkDao tipoBenchmarkDao;
    @Autowired
    private CategoriaAtividadeDao tiposWodDao;
    @Autowired
    private SessaoService sessaoService;
    @Autowired
    private PerfilDao perfilDao;
    @Autowired
    private AvaliacaoFisicaDao avaliacaoFisicaDao;
    @Autowired
    private AnamneseDao anamneseDao;
    @Autowired
    private LogDao logDao;
    @Autowired
    private final ConexaoZWService conexaoZWService;
    @Autowired
    private UsuarioDao usuarioDao;
    @Autowired
    public LogServiceImpl(ConexaoZWService conexaoZWService) {
        this.conexaoZWService = conexaoZWService;
    }

    public void incluir (String chave, Log log) throws Exception {
        logDao.insert(chave, log);
    }

    public List<LogTO> listarLogAvaliacaoObjetivos(JSONObject filtros, PaginadorDTO paginadorDTO) throws ServiceException{
        List<br.com.pacto.service.impl.log.LogTO> listarLog = new ArrayList<>();
        try {
            String quicksearchValue = filtros.optString("quicksearchValue");
            JSONArray tipos = filtros.optJSONArray("tipo");
            Long inicio = filtros.optLong("dataInicio");
            Long fim = filtros.optLong("dataFim");
            String ctx = sessaoService.getUsuarioAtual().getChave();

            StringBuilder sql = new StringBuilder();

            sql.append("select _campos_ \n");
            sql.append("from customrevisionentity c \n");
            sql.append("inner join objetivopredefinido_aud oa on oa.rev = c.id \n");

            if(inicio != null && inicio > 0l){
                sql.append(" and c.\"timestamp\" >= ").append(inicio);
            }
            if(fim != null && fim > 0l){
                sql.append(" and c.\"timestamp\" <= ").append(fim);
            }
            if(tipos != null && tipos.length() > 0){
                String tiposCod = "";
                for(int i = 0; i < tipos.length(); i++){
                    tiposCod += "," + TipoRevisaoEnum.valueOf(tipos.getString(i)).getId();
                }
                sql.append(" and ca.revtype in ( ").append(tiposCod.replaceFirst(",", "")).append(" )");
            }

            Integer maxResults = 10;
            Integer indiceInicial = 0;
            if (paginadorDTO != null) {
                maxResults = paginadorDTO.getSize() == null ? 10 : paginadorDTO.getSize().intValue();
                indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
            }
            try (ResultSet rs = avaliacaoFisicaDao.createStatement(ctx,
                    sql.toString().replace("_campos_", "c.id, c.ip, c.screenname, c.\"timestamp\", c.username, c.processo, \n" +
                                    "            oa.nome, oa.rev , oa.revtype, oa.codigo")
                            .concat("order by \"timestamp\" desc, id desc limit ".concat(maxResults.toString()))
                            .concat(" offset ".concat(indiceInicial.toString())))) {

                while (rs.next()) {
                    TipoRevisaoEnum revtype = TipoRevisaoEnum.getFromId(rs.getInt("revtype"));
                    Integer codigo = rs.getInt("codigo");
                    Long timestamp = rs.getLong("timestamp");
                    Long rev = rs.getLong("id");
                    String username = rs.getString("username");
                    LogTO logTO = new LogTO(codigo,
                            revtype.getDescricaoLog(),
                            rs.getString("nome"),
                            username,
                            Uteis.getDataAplicandoFormatacao(new Date(timestamp), "dd/MM/yyyy HH:mm"),
                            Uteis.getDataAplicandoFormatacao(new Date(timestamp), "HH:mm:ss"),
                            new ArrayList());

                    AlteracoesTO alteracoesTO = null;
                    if (revtype.equals(TipoRevisaoEnum.UPDATE) || revtype.equals(TipoRevisaoEnum.DELETE)) {
                        try (ResultSet rsAnterior = avaliacaoFisicaDao.createStatement(ctx,
                                "select oa.codigo, oa.revtype, oa.rev, " +
                                        " oa.nome from objetivopredefinido_aud oa where codigo = " + codigo +
                                        " and rev < " + rev +
                                        " order by rev desc limit 1 ")) {
                            if (rsAnterior.next()) {
                                logTO.setAlteracoes(new ArrayList<>());
                                alteracoesTO = new AlteracoesTO("nome", rsAnterior.getString("nome"),
                                        revtype.equals(TipoRevisaoEnum.DELETE) ? "" : rs.getString("nome"));
                                logTO.getAlteracoes().add(alteracoesTO);
                            } else {
                                alteracoesTO = new AlteracoesTO("nome", "",
                                        rs.getString("nome"));
                                logTO.getAlteracoes().add(alteracoesTO);
                            }
                        }
                    } else {
                        alteracoesTO = new AlteracoesTO("nome", "",
                                rs.getString("nome"));
                        logTO.getAlteracoes().add(alteracoesTO);
                    }

                    logTO.setDescricao(logTO.getDescricao() + ("[" + alteracoesTO.getCampo() + ":'"
                            + (UteisValidacao.emptyString(alteracoesTO.getValorAnterior()) ? "" : (alteracoesTO.getValorAnterior() + "' para '"))
                            + alteracoesTO.getValorAlterado() + "']<br/>"));
                    listarLog.add(logTO);
                }
            }
            try (ResultSet rsCount = atividadeDao.createStatement(ctx, sql.toString().replace("_campos_", " count(c.id) as cont "))) {
                if (rsCount.next()) {
                    paginadorDTO.setQuantidadeTotalElementos(rsCount.getLong("cont"));
                } else {
                    paginadorDTO.setQuantidadeTotalElementos(10l);
                }
            }
        }catch (Exception e){
            throw new ServiceException(e);
        }
        return listarLog;

    }

    public List<LogTO> listarLogWods(JSONObject filtros, PaginadorDTO paginadorDTO) throws ServiceException{
        List<LogTO> listarLog = new ArrayList<>();
        try {
            String quicksearchValue = filtros.optString("quicksearchValue");
            JSONArray tipos = filtros.optJSONArray("tipo");
            Long inicio = filtros.optLong("dataInicio");
            Long fim = filtros.optLong("dataFim");
            Integer codigoWod = null;
            try {
                codigoWod = quicksearchValue == null ? null : Integer.valueOf(quicksearchValue);
            }catch (Exception e){
                //ignore
            }
            String ctx = sessaoService.getUsuarioAtual().getChave();

            StringBuilder sql = new StringBuilder();

            sql.append("select _campos_ \n");
            sql.append("from customrevisionentity c \n");
            sql.append("inner join wod_aud ca on ca.rev = c.id \n");
            sql.append("where ca.codigo >0 \n");

            if (inicio != null && inicio > 0l) {
                Long dataInicio = Calendario.getDataComHoraZerada(new Date(inicio)).getTime();
                sql.append(" AND c.\"timestamp\" >= ").append(dataInicio).append(" \n");
            }
            if (fim != null && fim > 0l) {
                Long dataFim = Calendario.fimDoDia(new Date(fim)).getTime();
                sql.append(" AND c.\"timestamp\" <= ").append(dataFim).append(" \n");
            }

            if(tipos != null && tipos.length() > 0){
                String tiposCod = "";
                for(int i = 0; i < tipos.length(); i++){
                    tiposCod += "," + TipoRevisaoEnum.valueOf(tipos.getString(i)).getId();
                }
                sql.append(" and ca.revtype in ( ").append(tiposCod.replaceFirst(",", "")).append(" )");
            }

            sql.append(UteisValidacao.emptyString(quicksearchValue) ? "" : " and (");
            if (codigoWod != null){
                sql.append(" ca.codigo = ").append(codigoWod).append(" or \n");
            }
            if (!UteisValidacao.emptyString(quicksearchValue)) {
                sql.append("  upper(ca.nome) like '%").append(quicksearchValue.toUpperCase()).append("%' or \n");
                sql.append("  upper(c.username) like '%").append(quicksearchValue.toUpperCase()).append("%' \n");
            }
            sql.append(UteisValidacao.emptyString(quicksearchValue) ? "" : ")");

            Integer maxResults = 10;
            Integer indiceInicial = 0;
            if (paginadorDTO != null) {
                maxResults = paginadorDTO.getSize() == null ? 10 : paginadorDTO.getSize().intValue();
                indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
            }
            try (ResultSet rs = wodDao.createStatement(ctx,
                    sql.toString().replace("_campos_", "c.id, c.ip, c.screenname, c.\"timestamp\", c.username, c.processo, \n" +
                                    "            ca.nome, ca.rev , ca.revtype, ca.codigo")
                            .concat("order by \"timestamp\" desc, id desc limit ".concat(maxResults.toString()))
                            .concat(" offset ".concat(indiceInicial.toString())))) {

                while (rs.next()) {
                    TipoRevisaoEnum revtype = TipoRevisaoEnum.getFromId(rs.getInt("revtype"));
                    Integer codigo = rs.getInt("codigo");
                    Long timestamp = rs.getLong("timestamp");
                    Long rev = rs.getLong("id");
                    String username = rs.getString("username");
                    LogTO logTO = new LogTO(codigo, revtype.getDescricaoLog(), rs.getString("nome"),
                            username,
                            Uteis.getDataAplicandoFormatacao(new Date(timestamp), "dd/MM/yyyy HH:mm"),
                            Uteis.getDataAplicandoFormatacao(new Date(timestamp), "HH:mm:ss"),
                            new ArrayList());

                    AlteracoesTO alteracoesTO = null;
                    if (revtype.equals(TipoRevisaoEnum.UPDATE) || revtype.equals(TipoRevisaoEnum.DELETE)) {
                        try (ResultSet rsAnterior = wodDao.createStatement(ctx,
                                "select aa.codigo, aa.revtype, aa.rev, " +
                                        " aa.nome from wod_aud aa where codigo = " + codigo +
                                        " and rev < " + rev +
                                        " order by rev desc limit 1 ")) {
                            if (rsAnterior.next()) {
                                logTO.setAlteracoes(new ArrayList<>());
                                alteracoesTO = new AlteracoesTO("nome", rsAnterior.getString("nome"),
                                        revtype.equals(TipoRevisaoEnum.DELETE) ? "" : rs.getString("nome"));
                                logTO.getAlteracoes().add(alteracoesTO);
                            }
                        }
                    } else {
                        alteracoesTO = new AlteracoesTO("nome", "",
                                rs.getString("nome"));
                        logTO.getAlteracoes().add(alteracoesTO);
                    }

                    logTO.setDescricao(logTO.getDescricao() + ("[" + alteracoesTO.getCampo() + ":'"
                            + (UteisValidacao.emptyString(alteracoesTO.getValorAnterior()) ? "" : (alteracoesTO.getValorAnterior() + "' para '"))
                            + alteracoesTO.getValorAlterado() + "']<br/>"));
                    listarLog.add(logTO);
                }
            }
            try (ResultSet rsCount = wodDao.createStatement(ctx, sql.toString().replace("_campos_", " count(c.id) as cont "))) {
                if (rsCount.next()) {
                    paginadorDTO.setQuantidadeTotalElementos(rsCount.getLong("cont"));
                } else {
                    paginadorDTO.setQuantidadeTotalElementos(10l);
                }
            }


        }catch (Exception e){
            throw new ServiceException(e);
        }
        return listarLog;

    }


    public List<LogTO> listarLogCategoriaAtividade(JSONObject filtros, PaginadorDTO paginadorDTO) throws ServiceException{
        List<LogTO> listarLog = new ArrayList<>();
        try {
            String quicksearchValue = filtros.optString("quicksearchValue");
            JSONArray tipos = filtros.optJSONArray("tipo");
            Long inicio = filtros.optLong("dataInicio");
            Long fim = filtros.optLong("dataFim");
            Integer codigoCatAtv = null;
            try {
                codigoCatAtv = quicksearchValue == null ? null : Integer.valueOf(quicksearchValue);
            }catch (Exception e){
                //ignore
            }
            String ctx = sessaoService.getUsuarioAtual().getChave();

            StringBuilder sql = new StringBuilder();

            sql.append("select _campos_ \n");
            sql.append("from customrevisionentity c \n");
            sql.append("inner join categoriaatividade_aud ca on ca.rev = c.id \n");
            sql.append("left join atividadecategoriaatividade_aud aa2 on aa2.rev = ca.rev \n");
            sql.append("where aa2.codigo  is null \n");

            if(inicio != null && inicio > 0l){
                sql.append(" and c.\"timestamp\" >= ").append(inicio);
            }
            if(fim != null && fim > 0l){
                sql.append(" and c.\"timestamp\" <= ").append(fim);
            }
            if(tipos != null && tipos.length() > 0){
                String tiposCod = "";
                for(int i = 0; i < tipos.length(); i++){
                    tiposCod += "," + TipoRevisaoEnum.valueOf(tipos.getString(i)).getId();
                }
                sql.append(" and ca.revtype in ( ").append(tiposCod.replaceFirst(",", "")).append(" )");
            }

            Integer maxResults = 10;
            Integer indiceInicial = 0;
            if (paginadorDTO != null) {
                maxResults = paginadorDTO.getSize() == null ? 10 : paginadorDTO.getSize().intValue();
                indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
            }
            try (ResultSet rs = categoriaAtividadeDao.createStatement(ctx,
                    sql.toString().replace("_campos_", "c.id, c.ip, c.screenname, c.\"timestamp\", c.username, c.processo, \n" +
                                    "            ca.nome, ca.rev , ca.revtype, ca.codigo")
                            .concat("order by \"timestamp\" desc, id desc limit ".concat(maxResults.toString()))
                            .concat(" offset ".concat(indiceInicial.toString())))) {

                while (rs.next()) {
                    TipoRevisaoEnum revtype = TipoRevisaoEnum.getFromId(rs.getInt("revtype"));
                    Integer codigo = rs.getInt("codigo");
                    Long timestamp = rs.getLong("timestamp");
                    Long rev = rs.getLong("id");
                    String username = rs.getString("username");
                    LogTO logTO = new LogTO(codigo, revtype.getDescricaoLog(),
                            rs.getString("nome"),
                            username,
                            Uteis.getDataAplicandoFormatacao(new Date(timestamp), "dd/MM/yyyy HH:mm"),
                            Uteis.getDataAplicandoFormatacao(new Date(timestamp), "HH:mm:ss"),
                            new ArrayList());

                    AlteracoesTO alteracoesTO = new AlteracoesTO();
                    if (revtype.equals(TipoRevisaoEnum.UPDATE) || revtype.equals(TipoRevisaoEnum.DELETE)) {
                        try (ResultSet rsAnterior = atividadeDao.createStatement(ctx,
                                "select aa.codigo, aa.revtype, aa.rev, " +
                                        " aa.nome from categoriaatividade_aud aa where codigo = " + codigo +
                                        " and rev < " + rev +
                                        " order by rev desc limit 1 ")) {
                            if (rsAnterior.next()) {
                                logTO.setAlteracoes(new ArrayList<>());
                                alteracoesTO = new AlteracoesTO("nome", rsAnterior.getString("nome"),
                                        revtype.equals(TipoRevisaoEnum.DELETE) ? "" : rs.getString("nome"));
                                logTO.getAlteracoes().add(alteracoesTO);
                            } else {
                                alteracoesTO = new AlteracoesTO("nome", "",
                                        rs.getString("nome"));
                                logTO.getAlteracoes().add(alteracoesTO);
                            }
                        }
                    } else {
                        alteracoesTO = new AlteracoesTO("nome", "",
                                rs.getString("nome"));
                        logTO.getAlteracoes().add(alteracoesTO);
                    }

                    logTO.setDescricao(logTO.getDescricao() + ("[" + alteracoesTO.getCampo() + ":'"
                            + (UteisValidacao.emptyString(alteracoesTO.getValorAnterior()) ? "" : (alteracoesTO.getValorAnterior() + "' para '"))
                            + alteracoesTO.getValorAlterado() + "']<br/>"));
                    listarLog.add(logTO);
                }
            }
            try (ResultSet rsCount = atividadeDao.createStatement(ctx, sql.toString().replace("_campos_", " count(c.id) as cont "))) {
                if (rsCount.next()) {
                    paginadorDTO.setQuantidadeTotalElementos(rsCount.getLong("cont"));
                } else {
                    paginadorDTO.setQuantidadeTotalElementos(10l);
                }
            }


        }catch (Exception e){
            throw new ServiceException(e);
        }
        return listarLog;

    }

    public List<LogTO> listarLogAparelhos(JSONObject filtros, PaginadorDTO paginadorDTO, boolean isAparelhoCrosfit) throws ServiceException{
        List<LogTO> listarLog = new ArrayList<>();
        try {
            String quicksearchValue = filtros.optString("quicksearchValue");
            JSONArray tipos = filtros.optJSONArray("tipo");
            Long inicio = filtros.optLong("dataInicio");
            Long fim = filtros.optLong("dataFim");
            Integer codigoAparelho = null;
            try {
                codigoAparelho = quicksearchValue == null ? null : Integer.valueOf(quicksearchValue);
            }catch (Exception e){
                //ignore
            }
            String ctx = sessaoService.getUsuarioAtual().getChave();

            StringBuilder sql = new StringBuilder();

            sql.append("select _campos_ \n");
            sql.append("from customrevisionentity c \n");
            sql.append("inner join aparelho_aud ca on ca.rev = c.id  \n");
            sql.append("where c.processo like ('").append(isAparelhoCrosfit ? "ap-cross" : "ap-treino").append("%')");


            if (inicio != null && inicio > 0l) {
                Long dataInicio = Calendario.getDataComHoraZerada(new Date(inicio)).getTime();
                sql.append(" AND c.\"timestamp\" >= ").append(dataInicio).append(" \n");
            }
            if (fim != null && fim > 0l) {
                Long dataFim = Calendario.fimDoDia(new Date(fim)).getTime();
                sql.append(" AND c.\"timestamp\" <= ").append(dataFim).append(" \n");
            }

            if(tipos != null && tipos.length() > 0){
                String tiposCod = "";
                for(int i = 0; i < tipos.length(); i++){
                    tiposCod += "," + TipoRevisaoEnum.valueOf(tipos.getString(i)).getId();
                }
                sql.append(" and ca.revtype in ( ").append(tiposCod.replaceFirst(",", "")).append(" )");
            }
            sql.append(UteisValidacao.emptyString(quicksearchValue) ? "" : " and (");
            if (codigoAparelho != null){
                sql.append(" ca.codigo = ").append(codigoAparelho).append(" or \n");
            }
            if (!UteisValidacao.emptyString(quicksearchValue)) {
                sql.append(" ( upper(ca.nome) like '%").append(quicksearchValue.toUpperCase()).append("%' or \n");
                sql.append("  upper(c.processo) like '%").append(quicksearchValue.toUpperCase()).append("%' or \n");
                sql.append("  upper(c.username) like '%").append(quicksearchValue.toUpperCase()).append("%' ) \n");
            }
            sql.append(UteisValidacao.emptyString(quicksearchValue) ? "" : ")");

            Integer maxResults = 10;
            Integer indiceInicial = 0;
            if (paginadorDTO != null) {
                maxResults = paginadorDTO.getSize() == null ? 10 : paginadorDTO.getSize().intValue();
                indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
            }
            try (ResultSet rs = aparelhoDao.createStatement(ctx,
                    sql.toString().replace("_campos_", "c.id, c.ip, c.screenname, c.\"timestamp\", c.username, c.processo, \n" +
                                    "            ca.nome, ca.rev , ca.revtype, ca.codigo")
                            .concat("order by \"timestamp\" desc, id desc limit ".concat(maxResults.toString()))
                            .concat(" offset ".concat(indiceInicial.toString())))) {

                while (rs.next()) {
                    TipoRevisaoEnum revtype = TipoRevisaoEnum.getFromId(rs.getInt("revtype"));
                    Integer codigo = rs.getInt("codigo");
                    Long timestamp = rs.getLong("timestamp");
                    Long rev = rs.getLong("id");
                    Long revPrev = null;
                    String username = rs.getString("username");
                    LogTO logTO = new LogTO(codigo, revtype.getDescricaoLog(),
                            rs.getString("nome"),
                            username,
                            Uteis.getDataAplicandoFormatacao(new Date(timestamp), "dd/MM/yyyy HH:mm"),
                            Uteis.getDataAplicandoFormatacao(new Date(timestamp), "HH:mm:ss"),
                            new ArrayList());

                    Map<String, String> valoresAlterados = montarMapa(rs, "nome");
                    Map<String, String> valoresAnteriores = null;

                    AlteracoesTO alteracoesTO = null;
                    if (revtype.equals(TipoRevisaoEnum.UPDATE) || revtype.equals(TipoRevisaoEnum.DELETE)) {
                        try (ResultSet rsAnterior = aparelhoDao.createStatement(ctx,
                                "select aa.codigo, aa.revtype, aa.rev, " +
                                        " aa.nome from aparelho_aud aa where codigo = " + codigo +
                                        " and rev < " + rev +
                                        " order by rev desc limit 1 ")) {
                            if (rsAnterior.next()) {
                                revPrev = rsAnterior.getLong("rev");
                                valoresAnteriores = montarMapa(rsAnterior, "nome");
                            }
                        }
                    }
                    logTO.setAlteracoes(Uteis.compararMapas(valoresAnteriores, valoresAlterados));
                    logTO.getAlteracoes().addAll(logAparelhoRelacionados(ctx, "aparelhoajuste", "ajuste", rev, revPrev));

                    for (AlteracoesTO a : logTO.getAlteracoes()) {
                        logTO.setDescricao(logTO.getDescricao() + ("[" + a.getCampo() + ":'"
                                + (UteisValidacao.emptyString(a.getValorAnterior()) ? "" : (a.getValorAnterior() + "' para '"))
                                + a.getValorAlterado() + "']<br/>"));
                    }

                    listarLog.add(logTO);
                }
            }
            try (ResultSet rsCount = aparelhoDao.createStatement(ctx, sql.toString().replace("_campos_", " count(c.id) as cont "))) {
                if (rsCount.next()) {
                    paginadorDTO.setQuantidadeTotalElementos(rsCount.getLong("cont"));
                } else {
                    paginadorDTO.setQuantidadeTotalElementos(10l);
                }
            }


        }catch (Exception e){
            throw new ServiceException(e);
        }
        return listarLog;

    }

    private List<AlteracoesTO> logAparelhoRelacionados(String chave, String entidade, String desc, Long rev, Long revPrev) throws Exception{
        List<String> nomesAdd = new ArrayList<>();
        List<String> nomesDel = new ArrayList<>();
        StringBuilder sql = new StringBuilder();
        sql.append("select distinct nome , revtype from ").append(entidade).append("_aud aa ");
        sql.append("where rev = ").append(rev);
        ResultSet rs = atividadeDao.createStatement(chave, sql.toString());
        while (rs.next()){
            nomesAdd.add(rs.getString("nome"));
        }
        if(revPrev != null){
            sql = new StringBuilder();
            sql.append("select distinct  nome , revtype from ").append(entidade).append("_aud aa ");
            sql.append("where rev = ").append(revPrev);
            rs = atividadeDao.createStatement(chave, sql.toString());
            while (rs.next()){
                String nome = rs.getString("nome");
                if(nomesAdd.contains(nome)){
                    nomesAdd.remove(nome);
                }
            }
        }
        List<AlteracoesTO> alteracoes = new ArrayList<>();
        for(String n : nomesAdd){
            alteracoes.add(new AlteracoesTO(desc +" adicionado", "", n));
        }
        for(String n : nomesDel){
            alteracoes.add(new AlteracoesTO(desc +" removido", "", n));
        }
        return alteracoes;
    }

    public List<LogTO> listarTiposWod(JSONObject filtros, PaginadorDTO paginadorDTO) throws ServiceException{
        List<LogTO> listarLog = new ArrayList<>();
        try {
            String quicksearchValue = filtros.optString("quicksearchValue");
            JSONArray tipos = filtros.optJSONArray("tipo");
            Long inicio = filtros.optLong("dataInicio");
            Long fim = filtros.optLong("dataFim");
            String ctx = sessaoService.getUsuarioAtual().getChave();

            Integer codigoTipoWod = null;
            try {
                codigoTipoWod = quicksearchValue == null ? null : Integer.valueOf(quicksearchValue);
            }catch (Exception e){
                //ignore
            }

            StringBuilder sql = new StringBuilder();

            sql.append("select _campos_ \n");
            sql.append("from customrevisionentity c  \n");
            sql.append("inner join tipowod_aud ta on ta.rev = c.id \n");
            sql.append("where ta.codigo >0 \n");

            if (inicio != null && inicio > 0l) {
                Long dataInicio = Calendario.getDataComHoraZerada(new Date(inicio)).getTime();
                sql.append(" AND c.\"timestamp\" >= ").append(dataInicio).append(" \n");
            }
            if (fim != null && fim > 0l) {
                Long dataFim = Calendario.fimDoDia(new Date(fim)).getTime();
                sql.append(" AND c.\"timestamp\" <= ").append(dataFim).append(" \n");
            }

            if(tipos != null && tipos.length() > 0){
                String tiposCod = "";
                for(int i = 0; i < tipos.length(); i++){
                    tiposCod += "," + TipoRevisaoEnum.valueOf(tipos.getString(i)).getId();
                }
                sql.append(" and ta.revtype in ( ").append(tiposCod.replaceFirst(",", "")).append(" )");
            }

            sql.append(UteisValidacao.emptyString(quicksearchValue) ? "" : " and (");
            if (codigoTipoWod != null){
                sql.append(" ta.codigo = ").append(codigoTipoWod).append(" or \n");
            }
            if (!UteisValidacao.emptyString(quicksearchValue)) {
                sql.append("  upper(ta.nome) like '%").append(quicksearchValue.toUpperCase()).append("%' or \n");
                sql.append("  upper(c.username) like '%").append(quicksearchValue.toUpperCase()).append("%' \n");
            }
            sql.append(UteisValidacao.emptyString(quicksearchValue) ? "" : ")");
            Integer maxResults = 10;
            Integer indiceInicial = 0;
            if (paginadorDTO != null) {
                maxResults = paginadorDTO.getSize() == null ? 10 : paginadorDTO.getSize().intValue();
                indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
            }
            try (ResultSet rs = tiposWodDao.createStatement(ctx,
                    sql.toString().replace("_campos_", "c.id, c.ip, c.screenname, c.\"timestamp\", c.username, c.processo, \n" +
                                    "            ta.nome, ta.rev , ta.revtype, ta.codigo")
                            .concat("order by \"timestamp\" desc, id desc limit ".concat(maxResults.toString()))
                            .concat(" offset ".concat(indiceInicial.toString())))) {

                while (rs.next()) {
                    TipoRevisaoEnum revtype = TipoRevisaoEnum.getFromId(rs.getInt("revtype"));
                    Integer codigo = rs.getInt("codigo");
                    Long timestamp = rs.getLong("timestamp");
                    Long rev = rs.getLong("id");
                    String username = rs.getString("username");
                    LogTO logTO = new LogTO(codigo, revtype.getDescricaoLog(),
                            rs.getString("nome"),
                            username,
                            Uteis.getDataAplicandoFormatacao(new Date(timestamp), "dd/MM/yyyy HH:mm"),
                            Uteis.getDataAplicandoFormatacao(new Date(timestamp), "HH:mm:ss"),
                            new ArrayList());

                    AlteracoesTO alteracoesTO = null;
                    if (revtype.equals(TipoRevisaoEnum.UPDATE) || revtype.equals(TipoRevisaoEnum.DELETE)) {
                        try (ResultSet rsAnterior = tiposWodDao.createStatement(ctx,
                                "select aa.codigo, aa.revtype, aa.rev, " +
                                        " aa.nome from tipowod_aud aa where codigo = " + codigo +
                                        " and rev < " + rev +
                                        " order by rev desc limit 1 ")) {
                            if (rsAnterior.next()) {
                                logTO.setAlteracoes(new ArrayList<>());
                                alteracoesTO = new AlteracoesTO("nome", rsAnterior.getString("nome"),
                                        revtype.equals(TipoRevisaoEnum.DELETE) ? "" : rs.getString("nome"));
                                logTO.getAlteracoes().add(alteracoesTO);
                            }
                        }
                    } else {
                        alteracoesTO = new AlteracoesTO("nome", "",
                                rs.getString("nome"));
                        logTO.getAlteracoes().add(alteracoesTO);
                    }

                    logTO.setDescricao(logTO.getDescricao() + ("[" + alteracoesTO.getCampo() + ":'"
                            + (UteisValidacao.emptyString(alteracoesTO.getValorAnterior()) ? "" : (alteracoesTO.getValorAnterior() + "' para '"))
                            + alteracoesTO.getValorAlterado() + "']<br/>"));
                    listarLog.add(logTO);
                }
            }
            try (ResultSet rsCount = tiposWodDao.createStatement(ctx, sql.toString().replace("_campos_", " count(c.id) as cont "))) {
                if (rsCount.next()) {
                    paginadorDTO.setQuantidadeTotalElementos(rsCount.getLong("cont"));
                } else {
                    paginadorDTO.setQuantidadeTotalElementos(10l);
                }
            }


        }catch (Exception e){
            throw new ServiceException(e);
        }
        return listarLog;

    }

    public List<LogTO> listarLogGruposMusculares(JSONObject filtros, PaginadorDTO paginadorDTO) throws ServiceException{
        List<LogTO> listarLog = new ArrayList<>();
        try {
            String quicksearchValue = filtros.optString("quicksearchValue");
            JSONArray tipos = filtros.optJSONArray("tipo");
            Long inicio = filtros.optLong("dataInicio");
            Long fim = filtros.optLong("dataFim");
            Integer codigoGrupoMuscular = null;
            try {
                codigoGrupoMuscular = quicksearchValue == null ? null : Integer.valueOf(quicksearchValue);
            }catch (Exception e){
                //ignore
            }
            String ctx = sessaoService.getUsuarioAtual().getChave();

            StringBuilder sql = new StringBuilder();

            sql.append("select _campos_ \n");
            sql.append("from customrevisionentity c \n");
            sql.append("inner join grupomuscular_aud ca on ca.rev = c.id \n");
            sql.append("where ca.codigo > 0 \n");

            if (inicio != null && inicio > 0l) {
                Long dataInicio = Calendario.getDataComHoraZerada(new Date(inicio)).getTime();
                sql.append(" AND c.\"timestamp\" >= ").append(dataInicio).append(" \n");
            }
            if (fim != null && fim > 0l) {
                Long dataFim = Calendario.fimDoDia(new Date(fim)).getTime();
                sql.append(" AND c.\"timestamp\" <= ").append(dataFim).append(" \n");
            }
            if(tipos != null && tipos.length() > 0){
                String tiposCod = "";
                for(int i = 0; i < tipos.length(); i++){
                    tiposCod += "," + TipoRevisaoEnum.valueOf(tipos.getString(i)).getId();
                }
                sql.append(" and ca.revtype in ( ").append(tiposCod.replaceFirst(",", "")).append(" )");
            }

            sql.append(UteisValidacao.emptyString(quicksearchValue) ? "" : " and (");
            if (codigoGrupoMuscular != null){
                sql.append(" ca.codigo = ").append(codigoGrupoMuscular).append(" or \n");
            }
            if (!UteisValidacao.emptyString(quicksearchValue)) {
                sql.append("  upper(ca.nome) like '%").append(quicksearchValue.toUpperCase()).append("%' or \n");
                sql.append("  upper(c.username) like '%").append(quicksearchValue.toUpperCase()).append("%' \n");
            }
            sql.append(UteisValidacao.emptyString(quicksearchValue) ? "" : ")");

            Integer maxResults = 10;
            Integer indiceInicial = 0;
            if (paginadorDTO != null) {
                maxResults = paginadorDTO.getSize() == null ? 10 : paginadorDTO.getSize().intValue();
                indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
            }
            try (ResultSet rs = grupoMuscularDao.createStatement(ctx,
                    sql.toString().replace("_campos_", "c.id, c.ip, c.screenname, c.\"timestamp\", c.username, c.processo, \n" +
                                    "            ca.nome, ca.rev , ca.revtype, ca.codigo")
                            .concat("order by \"timestamp\" desc, id desc limit ".concat(maxResults.toString()))
                            .concat(" offset ".concat(indiceInicial.toString())))) {

                while (rs.next()) {
                    TipoRevisaoEnum revtype = TipoRevisaoEnum.getFromId(rs.getInt("revtype"));
                    Integer codigo = rs.getInt("codigo");
                    Long timestamp = rs.getLong("timestamp");
                    Long rev = rs.getLong("id");
                    String username = rs.getString("username");
                    LogTO logTO = new LogTO(codigo, revtype.getDescricaoLog(),
                            rs.getString("nome"),
                            username,
                            Uteis.getDataAplicandoFormatacao(new Date(timestamp), "dd/MM/yyyy HH:mm"),
                            Uteis.getDataAplicandoFormatacao(new Date(timestamp), "HH:mm:ss"),
                            new ArrayList());

                    AlteracoesTO alteracoesTO = null;
                    if (revtype.equals(TipoRevisaoEnum.UPDATE) || revtype.equals(TipoRevisaoEnum.DELETE)) {
                        try (ResultSet rsAnterior = grupoMuscularDao.createStatement(ctx,
                                "select aa.codigo, aa.revtype, aa.rev, " +
                                        " aa.nome from grupomuscular_aud aa where codigo = " + codigo +
                                        " and rev <= " + rev +
                                        " order by rev desc limit 1 ")) {
                            if (rsAnterior.next()) {
                                logTO.setAlteracoes(new ArrayList<>());
                                alteracoesTO = new AlteracoesTO("nome", rsAnterior.getString("nome"),
                                        revtype.equals(TipoRevisaoEnum.DELETE) ? "" : rs.getString("nome"));
                                logTO.getAlteracoes().add(alteracoesTO);
                            }
                        }
                    } else {
                        alteracoesTO = new AlteracoesTO("nome", "",
                                rs.getString("nome"));
                        logTO.getAlteracoes().add(alteracoesTO);
                    }

                    logTO.setDescricao(logTO.getDescricao() + ("[" + alteracoesTO.getCampo() + ":'"
                            + (UteisValidacao.emptyString(alteracoesTO.getValorAnterior()) ? "" : (alteracoesTO.getValorAnterior() + "' para '"))
                            + alteracoesTO.getValorAlterado() + "']<br/>"));
                    listarLog.add(logTO);
                }
            }
            try (ResultSet rsCount = grupoMuscularDao.createStatement(ctx, sql.toString().replace("_campos_", " count(c.id) as cont "))) {
                if (rsCount.next()) {
                    paginadorDTO.setQuantidadeTotalElementos(rsCount.getLong("cont"));
                } else {
                    paginadorDTO.setQuantidadeTotalElementos(10l);
                }
            }


        }catch (Exception e){
            throw new ServiceException(e);
        }
        return listarLog;

    }

    public List<LogTO> listarMusculos(JSONObject filtros, PaginadorDTO paginadorDTO) throws ServiceException{
        List<LogTO> listarLog = new ArrayList<>();
        try {
            String quicksearchValue = filtros.optString("quicksearchValue");
            JSONArray tipos = filtros.optJSONArray("tipo");
            Long inicio = filtros.optLong("dataInicio");
            Long fim = filtros.optLong("dataFim");
            Integer codigoMusculo = null;
            try {
                codigoMusculo = quicksearchValue == null ? null : Integer.valueOf(quicksearchValue);
            }catch (Exception e){
                //ignore
            }
            String ctx = sessaoService.getUsuarioAtual().getChave();

            StringBuilder sql = new StringBuilder();

            sql.append("select _campos_ \n");
            sql.append("from customrevisionentity c \n");
            sql.append("inner join musculo_aud ca on ca.rev = c.id \n");
            sql.append("where ca.codigo  > 0 \n");

            if (inicio != null && inicio > 0l) {
                Long dataInicio = Calendario.getDataComHoraZerada(new Date(inicio)).getTime();
                sql.append(" AND c.\"timestamp\" >= ").append(dataInicio).append(" \n");
            }
            if (fim != null && fim > 0l) {
                Long dataFim = Calendario.fimDoDia(new Date(fim)).getTime();
                sql.append(" AND c.\"timestamp\" <= ").append(dataFim).append(" \n");
            }
            if(tipos != null && tipos.length() > 0){
                String tiposCod = "";
                for(int i = 0; i < tipos.length(); i++){
                    tiposCod += "," + TipoRevisaoEnum.valueOf(tipos.getString(i)).getId();
                }
                sql.append(" and ca.revtype in ( ").append(tiposCod.replaceFirst(",", "")).append(" )");
            }

            sql.append(UteisValidacao.emptyString(quicksearchValue) ? "" : " and (");
            if (codigoMusculo != null){
                sql.append(" ca.codigo = ").append(codigoMusculo).append(" or \n");
            }
            if (!UteisValidacao.emptyString(quicksearchValue)) {
                sql.append("  upper(ca.nome) like '%").append(quicksearchValue.toUpperCase()).append("%' or \n");
                sql.append("  upper(c.username) like '%").append(quicksearchValue.toUpperCase()).append("%' \n");
            }
            sql.append(UteisValidacao.emptyString(quicksearchValue) ? "" : ")");

            Integer maxResults = 10;
            Integer indiceInicial = 0;
            if (paginadorDTO != null) {
                maxResults = paginadorDTO.getSize() == null ? 10 : paginadorDTO.getSize().intValue();
                indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
            }
            try (ResultSet rs = musculoDao.createStatement(ctx,
                    sql.toString().replace("_campos_", "c.id, c.ip, c.screenname, c.\"timestamp\", c.username, c.processo, \n" +
                                    "            ca.nome, ca.rev , ca.revtype, ca.codigo")
                            .concat("order by \"timestamp\" desc, id desc limit ".concat(maxResults.toString()))
                            .concat(" offset ".concat(indiceInicial.toString())))) {

                while (rs.next()) {
                    TipoRevisaoEnum revtype = TipoRevisaoEnum.getFromId(rs.getInt("revtype"));
                    Integer codigo = rs.getInt("codigo");
                    Long timestamp = rs.getLong("timestamp");
                    Long rev = rs.getLong("id");
                    String username = rs.getString("username");
                    LogTO logTO = new LogTO(codigo, revtype.getDescricaoLog(),
                            rs.getString("nome"),
                            username,
                            Uteis.getDataAplicandoFormatacao(new Date(timestamp), "dd/MM/yyyy HH:mm"),
                            Uteis.getDataAplicandoFormatacao(new Date(timestamp), "HH:mm:ss"),
                            new ArrayList());

                    AlteracoesTO alteracoesTO = null;
                    if (revtype.equals(TipoRevisaoEnum.UPDATE) || revtype.equals(TipoRevisaoEnum.DELETE)) {
                        try (ResultSet rsAnterior = musculoDao.createStatement(ctx,
                                "select aa.codigo, aa.revtype, aa.rev, " +
                                        " aa.nome from musculo_aud aa where codigo = " + codigo +
                                        " and rev < " + rev +
                                        " order by rev desc limit 1 ")) {
                            if (rsAnterior.next()) {
                                logTO.setAlteracoes(new ArrayList<>());
                                alteracoesTO = new AlteracoesTO("nome", rsAnterior.getString("nome"),
                                        revtype.equals(TipoRevisaoEnum.DELETE) ? "" : rs.getString("nome"));
                                logTO.getAlteracoes().add(alteracoesTO);
                            }
                        }
                    } else {
                        alteracoesTO = new AlteracoesTO("nome", "",
                                rs.getString("nome"));
                        logTO.getAlteracoes().add(alteracoesTO);
                    }

                    logTO.setDescricao(logTO.getDescricao() + ("[" + alteracoesTO.getCampo() + ":'"
                            + (UteisValidacao.emptyString(alteracoesTO.getValorAnterior()) ? "" : (alteracoesTO.getValorAnterior() + "' para '"))
                            + alteracoesTO.getValorAlterado() + "']<br/>"));
                    listarLog.add(logTO);
                }
            }
            try (ResultSet rsCount = musculoDao.createStatement(ctx, sql.toString().replace("_campos_", " count(c.id) as cont "))) {
                if (rsCount.next()) {
                    paginadorDTO.setQuantidadeTotalElementos(rsCount.getLong("cont"));
                } else {
                    paginadorDTO.setQuantidadeTotalElementos(10l);
                }
            }


        }catch (Exception e){
            throw new ServiceException(e);
        }
        return listarLog;

    }


    public List<LogTO> listarBanchmark(JSONObject filtros, PaginadorDTO paginadorDTO) throws ServiceException{
        List<LogTO> listarLog = new ArrayList<>();
        try {
            String quicksearchValue = filtros.optString("quicksearchValue");
            JSONArray tipos = filtros.optJSONArray("tipo");
            Long inicio = filtros.optLong("dataInicio");
            Long fim = filtros.optLong("dataFim");
            Integer codigoBanchmark = null;
            try {
                codigoBanchmark = quicksearchValue == null ? null : Integer.valueOf(quicksearchValue);
            }catch (Exception e){
                //ignore
            }
            String ctx = sessaoService.getUsuarioAtual().getChave();

            StringBuilder sql = new StringBuilder();

            sql.append("select _campos_ \n");
            sql.append("from customrevisionentity c \n");
            sql.append("inner join benchmark_aud ca on ca.rev = c.id \n");
            sql.append("where ca.codigo >0 \n");

            if (inicio != null && inicio > 0l) {
                Long dataInicio = Calendario.getDataComHoraZerada(new Date(inicio)).getTime();
                sql.append(" AND c.\"timestamp\" >= ").append(dataInicio).append(" \n");
            }
            if (fim != null && fim > 0l) {
                Long dataFim = Calendario.fimDoDia(new Date(fim)).getTime();
                sql.append(" AND c.\"timestamp\" <= ").append(dataFim).append(" \n");
            }
            if(tipos != null && tipos.length() > 0){
                String tiposCod = "";
                for(int i = 0; i < tipos.length(); i++){
                    tiposCod += "," + TipoRevisaoEnum.valueOf(tipos.getString(i)).getId();
                }
                sql.append(" and ca.revtype in ( ").append(tiposCod.replaceFirst(",", "")).append(" )");
            }

            sql.append(UteisValidacao.emptyString(quicksearchValue) ? "" : " and (");
            if (codigoBanchmark != null){
                sql.append(" ca.codigo = ").append(codigoBanchmark).append(" or \n");
            }
            if (!UteisValidacao.emptyString(quicksearchValue)) {
                sql.append("  upper(ca.nome) like '%").append(quicksearchValue.toUpperCase()).append("%' or \n");
                sql.append("  upper(c.username) like '%").append(quicksearchValue.toUpperCase()).append("%' \n");
            }
            sql.append(UteisValidacao.emptyString(quicksearchValue) ? "" : ")");

            Integer maxResults = 10;
            Integer indiceInicial = 0;
            if (paginadorDTO != null) {
                maxResults = paginadorDTO.getSize() == null ? 10 : paginadorDTO.getSize().intValue();
                indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
            }
            try (ResultSet rs = categoriaAtividadeDao.createStatement(ctx,
                    sql.toString().replace("_campos_", "c.id, c.ip, c.screenname, c.\"timestamp\", c.username, c.processo, \n" +
                                    "            ca.nome, ca.rev , ca.revtype, ca.codigo")
                            .concat("order by \"timestamp\" desc, id desc limit ".concat(maxResults.toString()))
                            .concat(" offset ".concat(indiceInicial.toString())))) {

                while (rs.next()) {
                    TipoRevisaoEnum revtype = TipoRevisaoEnum.getFromId(rs.getInt("revtype"));
                    Integer codigo = rs.getInt("codigo");
                    Long timestamp = rs.getLong("timestamp");
                    Long rev = rs.getLong("id");
                    String username = rs.getString("username");
                    LogTO logTO = new LogTO(codigo, revtype.getDescricaoLog(),
                            rs.getString("nome"),
                            username,
                            Uteis.getDataAplicandoFormatacao(new Date(timestamp), "dd/MM/yyyy HH:mm"),
                            Uteis.getDataAplicandoFormatacao(new Date(timestamp), "HH:mm:ss"),
                            new ArrayList());

                    AlteracoesTO alteracoesTO = null;
                    if (revtype.equals(TipoRevisaoEnum.UPDATE) || revtype.equals(TipoRevisaoEnum.DELETE)) {
                        try (ResultSet rsAnterior = atividadeDao.createStatement(ctx,
                                "select aa.codigo, aa.revtype, aa.rev, " +
                                        " aa.nome from benchmark_aud aa where codigo = " + codigo +
                                        " and rev < " + rev +
                                        " order by rev desc limit 1 ")) {
                            if (rsAnterior.next()) {
                                logTO.setAlteracoes(new ArrayList<>());
                                alteracoesTO = new AlteracoesTO("nome", rsAnterior.getString("nome"),
                                        revtype.equals(TipoRevisaoEnum.DELETE) ? "" : rs.getString("nome"));
                                logTO.getAlteracoes().add(alteracoesTO);
                            }
                        }
                    } else {
                        alteracoesTO = new AlteracoesTO("nome", "",
                                rs.getString("nome"));
                        logTO.getAlteracoes().add(alteracoesTO);
                    }

                    logTO.setDescricao(logTO.getDescricao() + ("[" + alteracoesTO.getCampo() + ":'"
                            + (UteisValidacao.emptyString(alteracoesTO.getValorAnterior()) ? "" : (alteracoesTO.getValorAnterior() + "' para '"))
                            + alteracoesTO.getValorAlterado() + "']<br/>"));
                    listarLog.add(logTO);
                }
            }
            try (ResultSet rsCount = atividadeDao.createStatement(ctx, sql.toString().replace("_campos_", " count(c.id) as cont "))) {
                if (rsCount.next()) {
                    paginadorDTO.setQuantidadeTotalElementos(rsCount.getLong("cont"));
                } else {
                    paginadorDTO.setQuantidadeTotalElementos(10l);
                }
            }


        }catch (Exception e){
            throw new ServiceException(e);
        }
        return listarLog;

    }


    @Override
    public List<LogTO> listarLogAtividade(JSONObject filtros, PaginadorDTO paginadorDTO, boolean isAtividadeCrosfit, Integer id) throws ServiceException {
        List<LogTO> listarLog = new ArrayList<>();
        try {
            String quicksearchValue = filtros.optString("quicksearchValue");
            JSONArray tipos = filtros.optJSONArray("tipo");
            Long inicio = filtros.optLong("dataInicio");
            Long fim = filtros.optLong("dataFim");
            Integer codigoAtv = null;
            try {
                codigoAtv = quicksearchValue == null ? null : Integer.valueOf(quicksearchValue);
            }catch (Exception e){
                //ignore
            }
            String ctx = sessaoService.getUsuarioAtual().getChave();
            StringBuilder sqlprincipal = new StringBuilder();
            sqlprincipal.append(" select _campos_ \n");
            sqlprincipal.append(" from customrevisionentity c \n");
            sqlprincipal.append(" inner join atividade_aud aa on aa.rev = c.id \n");
            sqlprincipal.append(" inner join usuario u on u.username = c.username \n");
            sqlprincipal.append(" left join atividadeficha_aud aa2 on aa2.rev = aa.rev \n");
            sqlprincipal.append(" where aa2.codigo  is null \n");
            if(id == null){
                sqlprincipal.append("  and aa.crossfit is ").append(isAtividadeCrosfit).append("\n");
            } else {
                sqlprincipal.append(" and aa.codigo = ").append(id).append("\n");
            }

            if (inicio != null && inicio > 0l) {
                Long dataInicio = Calendario.getDataComHoraZerada(new Date(inicio)).getTime();
                sqlprincipal.append(" AND c.\"timestamp\" >= ").append(dataInicio).append(" \n");
            }
            if (fim != null && fim > 0l) {
                Long dataFim = Calendario.fimDoDia(new Date(fim)).getTime();
                sqlprincipal.append(" AND c.\"timestamp\" <= ").append(dataFim).append(" \n");
            }

            if(tipos != null && tipos.length() > 0){
                String tiposCod = "";
                for(int i = 0; i < tipos.length(); i++){
                    tiposCod += "," + TipoRevisaoEnum.valueOf(tipos.getString(i)).getId();
                }
                sqlprincipal.append(" and aa.revtype in ( ").append(tiposCod.replaceFirst(",", "")).append(" )");
            }
            sqlprincipal.append(UteisValidacao.emptyString(quicksearchValue) ? "" : " and (");
            if (codigoAtv != null){
                sqlprincipal.append(" aa.codigo = ").append(codigoAtv).append(" or \n");
            }
            if (!UteisValidacao.emptyString(quicksearchValue)) {
                sqlprincipal.append("  upper(aa.nome) like '%").append(quicksearchValue.toUpperCase()).append("%' or \n");
                sqlprincipal.append("  upper(c.username) like '%").append(quicksearchValue.toUpperCase()).append("%' \n");
            }
            sqlprincipal.append(UteisValidacao.emptyString(quicksearchValue) ? "" : ")");
            Integer maxResults = 10;
            Integer indiceInicial = 0;
            if (paginadorDTO != null) {
                maxResults = paginadorDTO.getSize() == null ? 10 : paginadorDTO.getSize().intValue();
                indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
            }
            try (ResultSet rs = atividadeDao.createStatement(ctx,
                    sqlprincipal.toString().replace("_campos_", "c.id, c.ip, c.screenname, c.\"timestamp\", c.username, c.processo, aa.codigo, aa.revtype, " +
                                    " aa.ativo, aa.descricao, aa.nome, aa.seriesApenasDuracao, aa.tipo, aa.todasempresas, " +
                                    " aa.versao, aa.crossfit, aa.linkvideo, aa.categoriaatividadewod, aa.unidademedida")
                            .concat("order by \"timestamp\" desc, id desc limit ".concat(maxResults.toString()))
                            .concat(" offset ".concat(indiceInicial.toString())))) {
                while (rs.next()) {
                    TipoRevisaoEnum revtype = TipoRevisaoEnum.getFromId(rs.getInt("revtype"));
                    Integer codigo = rs.getInt("codigo");
                    Long timestamp = rs.getLong("timestamp");
                    Long rev = rs.getLong("id");
                    String username = rs.getString("username");
                    Map<String, String> valoresAlterados = montarMapa(rs, "ativo", "descricao", "nome", "seriesApenasDuracao", "tipo", "todasempresas",
                            "versao", "crossfit", "linkvideo", "categoriaatividadewod", "unidademedida");
                    Map<String, String> valoresAnteriores = null;
                    LogTO logTO = new LogTO(codigo, revtype.getDescricaoLog(),
                            rs.getString("nome"),
                            username,
                            Uteis.getDataAplicandoFormatacao(new Date(timestamp), "dd/MM/yyyy HH:mm"),
                            Uteis.getDataAplicandoFormatacao(new Date(timestamp), "HH:mm:ss"),
                            new ArrayList());
                    Long revPrev = null;
                    if (revtype.equals(TipoRevisaoEnum.UPDATE)) {
                        try (ResultSet rsAnterior = atividadeDao.createStatement(ctx,
                                "select aa.codigo, aa.revtype, aa.rev, " +
                                        " aa.ativo, aa.descricao, aa.nome, aa.seriesApenasDuracao, aa.tipo, aa.todasempresas, " +
                                        " aa.versao, aa.crossfit, aa.linkvideo, aa.categoriaatividadewod, aa.unidademedida " +
                                        " from atividade_aud aa where codigo = " + codigo +
                                        " and rev < " + rev +
                                        " order by rev desc limit 1 ")) {
                            if (rsAnterior.next()) {
                                revPrev = rsAnterior.getLong("rev");
                                valoresAnteriores = montarMapa(rsAnterior, "ativo", "descricao", "nome", "seriesApenasDuracao", "tipo", "todasempresas",
                                        "versao", "crossfit", "linkvideo", "categoriaatividadewod", "unidademedida");
                            }
                        }
                    }
                    logTO.setAlteracoes(compararMapas(valoresAnteriores, valoresAlterados));
                    logTO.getAlteracoes().addAll(logAtividadeRelacionados(ctx, "aparelho", "Aparelho", rev, revPrev));
                    logTO.getAlteracoes().addAll(logAtividadeRelacionados(ctx, "grupomuscular", "Grupo Muscular", rev, revPrev));
                    logTO.getAlteracoes().addAll(logAtividadeRelacionados(ctx, "categoriaatividade", "Categoria de Atividade ", rev, revPrev));
                    logTO.getAlteracoes().addAll(logAtividadeRelacionados(ctx, "musculo", "Músculo ", rev, revPrev));
                    logTO.getAlteracoes().addAll(logAtividadeRelacionados(ctx, "empresa", "Empresa ", rev, revPrev));

                    try (ResultSet rsImagem = atividadeDao.createStatement(ctx,
                            "select fotokey from atividadeanimacao_aud aa where revtype = 0 and rev = " + rev)) {
                        if (rsImagem.next()) {
                            logTO.getAlteracoes().add(new AlteracoesTO("Imagem", "", "Imagem adicionada"));
                        }
                    }

                    for (AlteracoesTO a : logTO.getAlteracoes()) {
                        logTO.setDescricao(logTO.getDescricao() + ("[" + a.getCampo() + ":'"
                                + (UteisValidacao.emptyString(a.getValorAnterior()) ? "" : (a.getValorAnterior() + "' para '"))
                                + a.getValorAlterado() + "']<br/>"));
                    }
                    listarLog.add(logTO);
                }
            }
            try (ResultSet rsCount = atividadeDao.createStatement(ctx, sqlprincipal.toString().replace("_campos_", " count(c.id) as cont "))) {
                if (rsCount.next()) {
                    paginadorDTO.setQuantidadeTotalElementos(rsCount.getLong("cont"));
                } else {
                    paginadorDTO.setQuantidadeTotalElementos(10l);
                }
            }
        }catch (Exception e){
            throw new ServiceException(e);
        }

        return listarLog;
    }

    private List<AlteracoesTO> logAtividadeRelacionados(String chave, String entidade, String desc, Long rev, Long revPrev) throws Exception{
        List<String> nomesAdd = new ArrayList<>();
        List<String> nomesDel = new ArrayList<>();
        StringBuilder sql = new StringBuilder();
        sql.append("select distinct a.nome , revtype from atividade").append(entidade).append("_aud aa ");
        sql.append("inner join ").append(entidade).append(" a on a.codigo = aa.").append(entidade).append("_codigo ");
        sql.append("where rev = ").append(rev);
        ResultSet rs = atividadeDao.createStatement(chave, sql.toString());
            while (rs.next()) {
                nomesAdd.add(rs.getString("nome"));
            }
            if (revPrev != null) {
                sql = new StringBuilder();
                sql.append("select distinct a.nome , revtype from atividade").append(entidade).append("_aud aa ");
                sql.append("inner join ").append(entidade).append(" a on a.codigo = aa.").append(entidade).append("_codigo ");
                sql.append("where rev = ").append(revPrev);
                rs = atividadeDao.createStatement(chave, sql.toString());
                while (rs.next()) {
                    String nome = rs.getString("nome");
                    if (nomesAdd.contains(nome)) {
                        nomesAdd.remove(nome);
                    } else {
                        nomesDel.add(nome);
                    }
                }
            }

        List<AlteracoesTO> alteracoes = new ArrayList<>();
        for(String n : nomesAdd){
            alteracoes.add(new AlteracoesTO(desc +" adicionado", "", n));
        }
        for(String n : nomesDel){
            alteracoes.add(new AlteracoesTO(desc +" removido", "", n));
        }
        return alteracoes;
    }

    private Map<String, String> montarMapa(ResultSet rs, String ... columns) throws Exception{
        return new HashMap(){{
            for(String c : columns){
                put(c, String.valueOf(rs.getObject(c)));
            }
        }};
    }

    private List<AlteracoesTO> compararMapas(Map<String, String> valoresAnteriores, Map<String, String> valoresAlterados){
        List<AlteracoesTO> alteracoes = new ArrayList<>();
        if(valoresAnteriores == null){
            for(String key : valoresAlterados.keySet()){
                if(!UteisValidacao.emptyString(valoresAlterados.get(key))){
                    alteracoes.add(new AlteracoesTO(key, "", valoresAlterados.get(key)));
                }
            }
        } else {
            for(String key : valoresAlterados.keySet()){
                String valor = valoresAnteriores.get(key);
                if(valor == null || !valor.equals(valoresAlterados.get(key))){
                    alteracoes.add(new AlteracoesTO(key, valor, valoresAlterados.get(key)));
                }
            }
        }
        return alteracoes;
    }

    @Override
    public List<LogTO> listarLogCliente(String matricula, JSONObject filtros, PaginadorDTO paginadorDTO) throws ServiceException {
        List<LogTO> listarLog = new ArrayList<>();
        try {
            String quicksearchValue = filtros.optString("quicksearchValue");
            List<TipoRevisaoEnum> tipos = new ArrayList(){{
                JSONArray array = filtros.optJSONArray("tipo");
                if(array != null){
                    for(int i = 0; i < array.length(); i++){
                        add(TipoRevisaoEnum.valueOf(array.getString(i)));
                    }
                }
            }};
            Long inicio = filtros.optLong("dataInicio");
            Long fim = filtros.optLong("dataFim");
            ViewUtils view = ((ViewUtils) UtilContext.getBean(ViewUtils.class));
            String configsTipo = "";
            String configsFiltro = "";
            for (AcaoAlunoEnum a : AcaoAlunoEnum.values()) {
                try {
                    String campo = view.getLabel(a.name());
                    if(!UteisValidacao.emptyString(quicksearchValue) && campo.toLowerCase().contains(quicksearchValue.toLowerCase())){
                        configsFiltro += ",'" + a.name() + "'";
                    }
                    if((!tipos.isEmpty() || tipos.contains(a.getTipo()))){
                        configsTipo += ",'" + a.name() + "'";
                    }
                }catch (Exception e){
                    e.printStackTrace();
                }
            }

            String ctx = sessaoService.getUsuarioAtual().getChave();
            StringBuilder sqlprincipal = new StringBuilder();
            sqlprincipal.append(" select u.username, c.\"timestamp\"/5000 as segundo, c.acaoaluno, c.matricula  \n");
            sqlprincipal.append(" from customrevisionentity c \n");
            sqlprincipal.append(" inner join usuario u on u.username = c.username \n");
            sqlprincipal.append(" where c.matricula = '").append(matricula);
            sqlprincipal.append("' and c.acaoaluno is not null ");
            sqlprincipal.append(" and c.matricula is not null ");
            if(inicio != null && inicio > 0l){
                sqlprincipal.append(" and c.\"timestamp\"/5000 >= ").append(inicio/5000);
            }
            if(fim != null && fim > 0l){
                sqlprincipal.append(" and c.\"timestamp\"/5000 <= ").append(fim/5000);
            }
            if (!UteisValidacao.emptyString(quicksearchValue)) {
                sqlprincipal.append(" and ( upper(c.username) like '%").append(quicksearchValue.toUpperCase()).append("%' \n");
                if(!configsFiltro.isEmpty()){
                    sqlprincipal.append(" or c.acaoaluno in (").append(configsFiltro.replaceFirst(",", "")).append(")");
                }
                sqlprincipal.append(") \n");
            }
            if(!tipos.isEmpty() && !configsTipo.isEmpty()){
                sqlprincipal.append(" and c.acaoaluno in (").append(configsTipo.replaceFirst(",", "")).append(")");
            }
            sqlprincipal.append(" group by 1,2,3,4 \n");

            Integer maxResults = 10;
            Integer indiceInicial = 0;
            if (paginadorDTO != null) {
                maxResults = paginadorDTO.getSize() == null ? 10 : paginadorDTO.getSize().intValue();
                indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
            }

            LogProgramaService lps = UtilContext.getBean(LogProgramaServiceImpl.class);
            try (ResultSet rs = clienteDao.createStatement(ctx,
                    sqlprincipal.toString()
                            .concat("order by \"timestamp\"/5000 desc limit ".concat(maxResults.toString()))
                            .concat(" offset ".concat(indiceInicial.toString())))) {
                while (rs.next()) {
                    AcaoAlunoEnum acao = AcaoAlunoEnum.valueOf(rs.getString("acaoAluno"));
                    Long timestamp = rs.getLong("segundo");
                    String username = rs.getString("username");

                    LogTO logTO = new LogTO(Integer.valueOf(matricula),
                            acao.getTipo().getDescricaoLog(),
                            view.getLabel(acao.name()),
                            username,
                            Uteis.getDataAplicandoFormatacao(new Date(timestamp * 5000), "dd/MM/yyyy HH:mm"),
                            Uteis.getDataAplicandoFormatacao(new Date(timestamp * 5000), "HH:mm:ss"),
                            new ArrayList());

                    try {
                        switch (acao) {
                            case EXCLUIU_SERVICO:
                            case ALTEROU_SERVICO:
                            case AGENDOU_SERVICO:
                                logTO.getAlteracoes().addAll(agendamento(ctx, matricula, timestamp, username, acao));
                                break;
                            case ALTERACAO_PROFESSOR_NIVEL:
                                logTO.getAlteracoes().addAll(mudancaProfessorOuNivel(ctx, matricula, timestamp, username));
                                break;
                            case ADICIONAR_OBSERVACAO:
                            case ADICIONAR_ANEXO:
                            case EXCLUIR_ANEXO:
                                logTO.getAlteracoes().addAll(addObservacao(ctx, matricula, timestamp, username, acao));
                                break;
                            case NOVO_PROGRAMA:
                            case RENOVOU_PROGRAMA:
                                ResultSet rsPrograma = lps.descobrirQuemFoiAlteradoNoMomento(ctx, timestamp, username, null, TipoRevisaoEnum.INSERT.getId());
                                if (rsPrograma.next()) {
                                    Map<String, String> mapaNovo = Uteis.montarMapa(rsPrograma, "datainicio", "dataterminoprevisto", "nome", "professormontou_codigo");
                                    logTO.getAlteracoes().addAll(Uteis.compararMapas(null, mapaNovo));
                                }
                                break;
                            case EXCLUIU_PROGRAMA:
                                ResultSet rsProgramaExcluido = lps.descobrirQuemFoiAlteradoNoMomento(ctx, timestamp, username, null, TipoRevisaoEnum.DELETE.getId());
                                if (rsProgramaExcluido.next()) {
                                    try (ResultSet rsNomePrograma = clienteDao.createStatement(ctx, "select nome from programatreino_aud where revtype <> 2 " +
                                            " and codigo = " + rsProgramaExcluido.getInt("codigo") +
                                            " order by rev desc limit 1")) {
                                        if (rsNomePrograma.next()) {
                                            String nomePrograma = rsNomePrograma.getString("nome");
                                            logTO.getAlteracoes().add(new AlteracoesTO("Programa", nomePrograma, ""));
                                        }
                                    }
                                }
                                break;
                        }
                    } catch (Exception e) {
                        Uteis.logar(e, this.getClass());
                    }
                    int limit = 0;
                    tratarNomes(view, ctx, logTO.getAlteracoes());
                    for (AlteracoesTO a : logTO.getAlteracoes()) {
                        logTO.setDescricao(logTO.getDescricao() + ("[" + a.getCampo() + ":'"
                                + (UteisValidacao.emptyString(a.getValorAnterior()) ? "" : (a.getValorAnterior() + "' para '"))
                                + a.getValorAlterado() + "']<br/>"));
                        if (limit > 5) {
                            logTO.setDescricao(logTO.getDescricao() + "<br/>...");
                            break;
                        }
                        limit++;
                    }
                    listarLog.add(logTO);
                }
            }
            try (ResultSet rsCount = clienteDao.createStatement(ctx,
                    "select COUNT(segundo) as cont from ( " + sqlprincipal + ") as f")) {
                if (rsCount.next()) {
                    paginadorDTO.setQuantidadeTotalElementos(rsCount.getLong("cont"));
                } else {
                    paginadorDTO.setQuantidadeTotalElementos(10l);
                }
            }
        }catch (Exception e){
            throw new ServiceException(e);
        }

        return listarLog;
    }

    private void tratarNomes(ViewUtils viewUtils, String ctx, List<AlteracoesTO> alteracoes) {
        LogProgramaService lps = UtilContext.getBean(LogProgramaServiceImpl.class);
        lps.tratarNomes(ctx, alteracoes, "professorsintetico_codigo",
                "select codigo, nome from professorsintetico where codigo in (_codigos#entidades_)",
                "Professor");

        lps.tratarNomes(ctx, alteracoes, "nivelaluno_codigo",
                "select codigo, nome from nivel where codigo in (_codigos#entidades_)",
                "Nivel");

        lps.tratarNomes(ctx, alteracoes, "tipoevento_codigo",
                "select codigo, nome from tipoevento where codigo in (_codigos#entidades_)",
                "Tipo");

        lps.tratarNomes(ctx, alteracoes, "professormontou_codigo",
                "select codigo, nome from professorsintetico where codigo in (_codigos#entidades_)",
                "Professor Montou");

        try {
            for(AlteracoesTO a : alteracoes){
                if(a.getCampo().equals("importante")){
                    a.setValorAlterado(UteisValidacao.emptyString(a.getValorAlterado()) ? "" :
                            (Boolean.valueOf(a.getValorAlterado()) ? viewUtils.getLabel("cadastros.sim") : viewUtils.getLabel("cadastros.nao")));

                    a.setValorAnterior(UteisValidacao.emptyString(a.getValorAnterior()) ? "" :
                            (Boolean.valueOf(a.getValorAnterior()) ? viewUtils.getLabel("cadastros.sim") : viewUtils.getLabel("cadastros.nao")));
                }
            }

        }catch (Exception e){
            Uteis.logar(e, this.getClass());
        }

    }

    private String sqlAlteracao(Long timestamp, String username, String matricula, boolean agora){
        return "select nivelaluno_codigo, professorsintetico_codigo from clientesintetico_aud ca\n" +
                "        inner join customrevisionentity c on c.id = ca.rev\n" +
                "        where c.\"timestamp\"/5000 "+(agora ? "=" : "<")+" "+ timestamp +
                (agora ? (" and c.username = '"+username+"'\n") : "") +
                "        and c.acaoaluno = '"+AcaoAlunoEnum.ALTERACAO_PROFESSOR_NIVEL+"'\n" +
                "        and c.matricula = '"+matricula+"'\n" +
                "        order by rev desc\n" +
                "        limit 1";
    }

    private String sqlAgendamento(Long timestamp, String username, String matricula, boolean agora, Integer codigo, AcaoAlunoEnum acaoAlunoEnum){
        return "select codigo, inicio, status, fim, professor_codigo as professorsintetico_codigo, tipoevento_codigo from agendamento_aud ca \n" +
                "        inner join customrevisionentity c on c.id = ca.rev\n" +
                "        where c.\"timestamp\"/5000 "+(agora ? "=" : "<")+" "+ timestamp +
                (codigo == null ? (" and c.username = '"+username+"' and c.acaoaluno = '"+acaoAlunoEnum+"' and c.matricula = '"+matricula+"'\n")
                        : (" and ca.codigo = " + codigo)) +
                "        order by rev desc\n" +
                "        limit 1";
    }

    private List<AlteracoesTO> agendamento(String ctx, String matricula, Long timestamp, String username, AcaoAlunoEnum acaoAlunoEnum) throws Exception{
        List<AlteracoesTO> alteracoes;
        try (ResultSet rsAgora = clienteDao.createStatement(ctx, sqlAgendamento(timestamp, username, matricula, true, null, acaoAlunoEnum))) {
            alteracoes = new ArrayList<>();
            while (rsAgora.next()) {
                Map<String, String> mapaNovo = Uteis.montarMapa(rsAgora, "inicio", "fim", "tipoevento_codigo", "professorsintetico_codigo");
                Date inicio = rsAgora.getTimestamp("inicio");
                Date fim = rsAgora.getTimestamp("fim");
                mapaNovo.put("dia", Uteis.getDataAplicandoFormatacao(inicio, "dd/MM/yyyy"));
                mapaNovo.put("inicio", Uteis.getDataAplicandoFormatacao(inicio, "HH:mm"));
                mapaNovo.put("fim", Uteis.getDataAplicandoFormatacao(fim, "HH:mm"));
                mapaNovo.put("status_agenda ", StatusAgendamentoEnum.getFromId(rsAgora.getInt("status")).getDescricao());

                Map<String, String> mapaAnterior;
                try (ResultSet rsAnterior = clienteDao.createStatement(ctx, sqlAgendamento(timestamp, username, matricula, false, rsAgora.getInt("codigo"), acaoAlunoEnum))) {
                    mapaAnterior = null;
                    if (rsAnterior.next()) {
                        mapaAnterior = Uteis.montarMapa(rsAnterior, "inicio", "fim", "tipoevento_codigo", "professorsintetico_codigo");
                        Date inicioA = rsAnterior.getTimestamp("inicio");
                        Date fimA = rsAnterior.getTimestamp("fim");
                        mapaAnterior.put("dia", Uteis.getDataAplicandoFormatacao(inicioA, "dd/MM/yyyy"));
                        mapaAnterior.put("inicio", Uteis.getDataAplicandoFormatacao(inicioA, "HH:mm"));
                        mapaAnterior.put("fim", Uteis.getDataAplicandoFormatacao(fimA, "HH:mm"));
                        mapaAnterior.put("status_agenda ", StatusAgendamentoEnum.getFromId(rsAnterior.getInt("status")).getDescricao());
                    }
                }
                alteracoes.addAll(Uteis.compararMapas(mapaAnterior, mapaNovo));
            }
        }
        return alteracoes;
    }

    private List<AlteracoesTO> mudancaProfessorOuNivel(String ctx, String matricula, Long timestamp, String username) throws Exception{
        List<AlteracoesTO> alteracoes;
        try (ResultSet rsAgora = clienteDao.createStatement(ctx, sqlAlteracao(timestamp, username, matricula, true))) {
            alteracoes = new ArrayList<>();
            while (rsAgora.next()) {
                Map<String, String> mapaNovo = Uteis.montarMapa(rsAgora, "nivelaluno_codigo", "professorsintetico_codigo");
                Map<String, String> mapaAnterior;
                try (ResultSet rsAnterior = clienteDao.createStatement(ctx, sqlAlteracao(timestamp, username, matricula, false))) {
                    mapaAnterior = null;
                    if (rsAnterior.next()) {
                        mapaAnterior = Uteis.montarMapa(rsAnterior, "nivelaluno_codigo", "professorsintetico_codigo");
                    }
                }
                alteracoes.addAll(Uteis.compararMapas(mapaAnterior, mapaNovo));
            }
        }
        return alteracoes;
    }


    private String sqlAnexo(Long timestamp, String username, String matricula, boolean agora, AcaoAlunoEnum acao, boolean anexo){
        StringBuilder sql = new StringBuilder();
        sql.append("select codigo, rev, ");
        if (anexo) {
            sql.append("anexokey,");
        }
        sql.append(" importante, observacao from clienteobservacao_aud ca\n");
        sql.append("        inner join customrevisionentity c on c.id = ca.rev\n");
        sql.append("        where c.\"timestamp\"/5000 ");
        sql.append(agora ? "=" : "<");
        sql.append(" ?\n");
        sql.append("        and c.username = ?\n");
        sql.append("        and c.acaoaluno = ?\n");
        sql.append("        and c.matricula = ?\n");
        sql.append("        order by rev desc ");
        if (!acao.equals(AcaoAlunoEnum.EXCLUIR_ANEXO)) {
            sql.append(" limit 1");
        }
        return sql.toString();
    }

    private List<AlteracoesTO> addObservacao(String ctx, String matricula, Long timestamp, String username, AcaoAlunoEnum acao) throws Exception{
        boolean anexo = acao.equals(AcaoAlunoEnum.ADICIONAR_ANEXO) || acao.equals(AcaoAlunoEnum.EXCLUIR_ANEXO);
        String sql = sqlAnexo(timestamp, username, matricula, true, acao, anexo);
        List<AlteracoesTO> alteracoes;
        try (ResultSet rsAgora = clienteDao.createPreparedStatement(ctx, sql, timestamp, username, acao.name(), matricula)) {
            alteracoes = new ArrayList<>();
            while (rsAgora.next()) {
                Map<String, String> mapaNovo = anexo ? Uteis.montarMapa(rsAgora, "importante", "observacao", "anexokey")
                        : Uteis.montarMapa(rsAgora, "importante", "observacao");
                Map<String, String> mapaAnterior = null;
                if (acao.equals(AcaoAlunoEnum.EXCLUIR_ANEXO)) {
                    String sqlAnterior = "select anexokey, importante, observacao " +
                            " from clienteobservacao_aud ca \n" +
                            " where codigo = ? " +
                            " and revtype <> 2 " +
                            " order by rev desc limit 1";
                    try (ResultSet rsAnterior = clienteDao.createPreparedStatement(ctx, sqlAnterior, rsAgora.getInt("codigo"))) {
                        if (rsAnterior.next()) {
                            mapaAnterior = Uteis.montarMapa(rsAnterior, "importante", "observacao", "anexokey");
                        }
                    }
                }
                alteracoes.addAll(Uteis.compararMapas(mapaAnterior, mapaNovo));
            }
        }
        return alteracoes;
    }

    //------------- log de disponibilidades
    public List<LogTO> listarLogDisponibilidades(JSONObject filtros, PaginadorDTO paginadorDTO, Boolean agendamento, Integer codigoAgendamento) throws ServiceException{
        List<LogTO> listarLog = new ArrayList<>();
        try {
            String quicksearchValue = filtros.optString("quicksearchValue");
            JSONArray tipos = filtros.optJSONArray("tipo");
            Long inicio = filtros.optLong("dataInicio");
            Long fim = filtros.optLong("dataFim");
            Integer cliente = filtros.optInt("cliente");
            String ctx = sessaoService.getUsuarioAtual().getChave();
            Integer codigoAg = null;
            try {
                codigoAg = quicksearchValue == null ? null : Integer.valueOf(quicksearchValue);
            }catch (Exception e){
                //ignore
            }
            StringBuilder sql = new StringBuilder();

            sql.append(" select _campos_ \n");
            sql.append(" from customrevisionentity c \n");
            sql.append(" inner join agendamento_aud aa on aa.rev = c.id \n");
            sql.append(" left join usuario u on aa.usuariolancou_codigo = u.codigo \n");
            sql.append(" left join tipoevento t on t.codigo = aa.tipoevento_codigo \n");
            sql.append(" left join professorsintetico p on p.codigo = aa.professor_codigo \n");
            sql.append(agendamento ? "left join clientesintetico cli on cli.codigo = aa.cliente_codigo " : "");
            sql.append(" where aa.revtype <> 2 \n");
            if (!UteisValidacao.emptyNumber(cliente)){
                sql.append(" and aa.cliente_codigo = ").append(cliente).append("\n");
            }
            if(agendamento){
                sql.append(" and not disponibilidade \n");
            } else if(UteisValidacao.emptyNumber(codigoAgendamento) && UteisValidacao.emptyNumber(codigoAg)){
                sql.append(" and disponibilidade and (aa.revtype = 1 or aa.nsu is null)\n");
            }

            if(!UteisValidacao.emptyNumber(codigoAgendamento)){
                sql.append("and aa.codigo =  ").append(codigoAgendamento).append(" \n");
            }

            if(inicio != null && inicio > 0l){
                sql.append(" and c.\"timestamp\" >= ").append(inicio);
            }
            if(fim != null && fim > 0l){
                Date campoSomado = Uteis.somarCampoData(new Date(fim), Calendar.MINUTE, 1439);
                sql.append(" and c.\"timestamp\" <= ").append(campoSomado.getTime());
            }
            if(tipos != null && tipos.length() > 0){
                String tiposCod = "";
                for(int i = 0; i < tipos.length(); i++){
                    tiposCod += "," + TipoRevisaoEnum.valueOf(tipos.getString(i)).getId();
                }
                sql.append(" and ((aa.operacaoEmMassa is null and aa.revtype in ( ").append(tiposCod.replaceFirst(",", "")).append(" ))");
                sql.append(" or aa.operacaoEmMassa in ( ").append(tiposCod.replaceFirst(",", "")).append(" ))");
            }

            if (codigoAg != null){
                sql.append(" and aa.codigo = ").append(codigoAg).append("\n");
            } else  if (!UteisValidacao.emptyString(quicksearchValue)) {
                sql.append(" and ( upper(c.username) like '%").append(quicksearchValue.toUpperCase()).append("%'  \n");
                if(agendamento){
                    sql.append(" or upper(cli.nome) like '%").append(quicksearchValue.toUpperCase()).append("%'  \n");
                }
                sql.append(" or  upper(t.nome) like '%").append(quicksearchValue.toUpperCase()).append("%'  \n");
                sql.append(" or upper(p.nome) like '%").append(quicksearchValue.toUpperCase()).append("%' ) \n");
            }

            Integer maxResults = 10;
            Integer indiceInicial = 0;
            if (paginadorDTO != null) {
                maxResults = paginadorDTO.getSize() == null ? 10 : paginadorDTO.getSize().intValue();
                indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
            }
            String camposAgendamento = agendamento ? ", cli.nome as cliente, aa.status, aa.observacao " : "";

            try (ResultSet rs = agendamentoDao.createStatement(ctx,
                    sql.toString().replace("_campos_", "c.id, c.ip, c.screenname, c.\"timestamp\", case \n" +
                                    "\twhen aa.revtype = 0 then (select case when us.cliente_codigo is not null then (us.username || ' (aluno)') else us.username end username from usuario us where us.codigo = aa.usuariolancou_codigo)\n" +
                                    "\twhen c.username is not null then (select case when us.username is null then null else us.username end as username from usuario us where us.username = c.username)\n" +
                                    "\twhen u.cliente_codigo is not null then (u.username || ' (aluno)')\n" +
                                    "\twhen c.username is null and u.cliente_codigo is null then (select case when us.cliente_codigo is not null then (us.username || ' (aluno)') else us.username end username from usuario us where us.cliente_codigo = aa.cliente_codigo)\n" +
                                    "end as username, c.processo, \n" +
                                    "aa.codigo as codigotabelaagenda, aa.revtype, aa.datalancamento, aa.diasemana, aa.inicio, aa.fim, p.nome professor,\n" +
                                    "t.nome tipoevento, aa.fimexclusao as removidoate, aa.tipos, aa.posteriores, aa.operacaoEmMassa  " + camposAgendamento)
                            .concat("order by \"timestamp\" desc, id desc ")
                            .concat(paginadorDTO != null ? " limit ".concat(maxResults.toString()) : "")
                            .concat(" offset ".concat(indiceInicial.toString())))) {

                while (rs.next()) {
                    Integer op = rs.getInt("operacaoEmMassa");
                    TipoRevisaoEnum revtype = TipoRevisaoEnum.getFromId(UteisValidacao.emptyNumber(op) ? rs.getInt("revtype") : op);
                    Integer codigo = rs.getInt("codigotabelaagenda");
                    Long timestamp = rs.getLong("timestamp");
                    Long rev = rs.getLong("id");
                    String username = rs.getString("username");
                    Map<String, String> valoresAlterados = montarMapa(rs, "professor", "tipoevento");
                    valoresAlterados.put("data", Uteis.getDataAplicandoFormatacao(rs.getTimestamp("inicio"), "dd/MM/yyyy"));
                    valoresAlterados.put("inicio", Uteis.getDataAplicandoFormatacao(rs.getTimestamp("inicio"), "HH:mm"));
                    valoresAlterados.put("fim", Uteis.getDataAplicandoFormatacao(rs.getTimestamp("fim"), "HH:mm"));
                    if (agendamento) {
                        valoresAlterados.put("cliente", rs.getString("cliente"));
                        valoresAlterados.put("observacao", rs.getString("observacao") == null ? "" : rs.getString("observacao"));
                        valoresAlterados.put("status", StatusAgendamentoEnum.getFromId(rs.getInt("status")).getDescricao());
                    }
                    if (revtype.equals(TipoRevisaoEnum.INSERT) && !agendamento) {
                        try (ResultSet repeteRS = agendamentoDao.createStatement(ctx, "select fim from agendamento where nsu = " + codigo + " order by fim desc limit 1")) {
                            if (repeteRS.next()) {
                                valoresAlterados.put("repete", "Sim");
                                valoresAlterados.put("repeteAte", Uteis.getDataAplicandoFormatacao(repeteRS.getTimestamp("fim"), "dd/MM/yyyy"));
                                String diasSemana;
                                try (ResultSet diasSemanaRS = agendamentoDao.createStatement(ctx, "select extract(isodow from a.inicio) as ds from agendamento a " +
                                        "where nsu = " + codigo + " group by 1 ")) {
                                    diasSemana = "";
                                    while (diasSemanaRS.next()) {
                                        DiasSemana ds = DiasSemana.getDiaSemanaCodPostgres(diasSemanaRS.getInt("ds"));
                                        diasSemana += "," + ds.getMin();
                                    }
                                }
                                valoresAlterados.put("dias_semana", diasSemana.replaceFirst(",", ""));
                                valoresAlterados.put("repete", "Sim");
                            } else {
                                valoresAlterados.put("repete", "Não");
                            }
                        }
                    }
                    String identificador = Uteis.getPrimeiroNome(rs.getString("professor")) + " - " + rs.getString("tipoevento") + " - " +
                            Uteis.getDataAplicandoFormatacao(rs.getTimestamp("inicio"),
                                    (revtype.equals(TipoRevisaoEnum.UPDATE)) ? "dd/MM/yyyy HH:mm" : "HH:mm") + " - " +
                            Uteis.getDataAplicandoFormatacao(rs.getTimestamp("fim"), "HH:mm");

                    if (!UteisValidacao.emptyNumber(op)) {
                        valoresAlterados.keySet().forEach(
                                k -> {
                                    valoresAlterados.put(k, "");
                                }
                        );
                    }

                    Map<String, String> valoresAnteriores = null;
                    LogTO logTO = new LogTO(codigo, revtype.getDescricaoLog(),
                            identificador,
                            username,
                            Uteis.getDataAplicandoFormatacao(new Date(timestamp), "dd/MM/yyyy HH:mm"),
                            Uteis.getDataAplicandoFormatacao(new Date(timestamp), "HH:mm:ss"),
                            new ArrayList());

                    if (revtype.equals(TipoRevisaoEnum.UPDATE) || revtype.equals(TipoRevisaoEnum.DELETE)) {
                        try (ResultSet rsAnterior = agendamentoDao.createStatement(ctx,
                                "select aa.datalancamento, aa.diasemana, aa.inicio, aa.fim, p.nome as professor, t.nome as tipoevento " +
                                        camposAgendamento +
                                        " from agendamento_aud aa " +
                                        " left join tipoevento t on t.codigo = aa.tipoevento_codigo " +
                                        " left join professorsintetico p on p.codigo = aa.professor_codigo " +
                                        (agendamento ? " left join clientesintetico cli on cli.codigo = aa.cliente_codigo " : "") +
                                        " where aa.codigo = " + codigo +
                                        " and rev < " + rev +
                                        " order by rev desc limit 1 ")) {
                            if (rsAnterior.next()) {
                                valoresAnteriores = montarMapa(rsAnterior, "professor", "tipoevento");
                                valoresAnteriores.put("data", Uteis.getDataAplicandoFormatacao(rsAnterior.getTimestamp("inicio"), "dd/MM/yyyy"));
                                valoresAnteriores.put("inicio", Uteis.getDataAplicandoFormatacao(rsAnterior.getTimestamp("inicio"), "HH:mm"));
                                valoresAnteriores.put("fim", Uteis.getDataAplicandoFormatacao(rsAnterior.getTimestamp("fim"), "HH:mm"));
                                if (!UteisValidacao.emptyNumber(op) && !UteisValidacao.emptyString(rs.getString("tipos"))) {
                                    valoresAnteriores.put("tipoevento", rs.getString("tipos"));
                                    if (rs.getBoolean("posteriores")) {
                                        valoresAlterados.put("removido_ate", "");
                                        valoresAnteriores.put("removido_ate", Uteis.getDataAplicandoFormatacao(rs.getDate("removidoate"), "dd/MM/yyyy"));
                                    }
                                }
                                if (agendamento) {
                                    valoresAnteriores.put("cliente", rsAnterior.getString("cliente"));
                                    valoresAnteriores.put("observacao", rsAnterior.getString("observacao") == null ? "" : rsAnterior.getString("observacao"));
                                    valoresAnteriores.put("status", StatusAgendamentoEnum.getFromId(rsAnterior.getInt("status")).getDescricao());
                                }
                            }
                        }
                    }
                    logTO.setAlteracoes(compararMapas(valoresAnteriores, valoresAlterados));
                    for (AlteracoesTO a : logTO.getAlteracoes()) {
                        logTO.setDescricao(logTO.getDescricao() + ("[" + a.getCampo() + ":'"
                                + (UteisValidacao.emptyString(a.getValorAnterior()) ? "" : (a.getValorAnterior() + "' para '"))
                                + a.getValorAlterado() + "']<br/>"));
                    }
                    listarLog.add(logTO);
                }
            }
            if (paginadorDTO != null) {
                try (ResultSet rsCount = agendamentoDao.createStatement(ctx, sql.toString().replace("_campos_", " count(c.id) as cont "))) {
                    if (rsCount.next()) {
                        paginadorDTO.setQuantidadeTotalElementos(rsCount.getLong("cont"));
                    } else {
                        paginadorDTO.setQuantidadeTotalElementos(10l);
                    }
                }
            }
        }catch (Exception e){
            throw new ServiceException(e);
        }
        return listarLog;
    }

    public List<LogTO> listarTiposbenchmarks(JSONObject filtros, PaginadorDTO paginadorDTO) throws ServiceException{
        List<LogTO> listarLog = new ArrayList<>();
        try {
            String quicksearchValue = filtros.optString("quicksearchValue");
            JSONArray tipos = filtros.optJSONArray("tipo");
            Long inicio = filtros.optLong("dataInicio");
            Long fim = filtros.optLong("dataFim");
            Integer codigoTpBenchmarks = null;
            try {
                codigoTpBenchmarks = quicksearchValue == null ? null : Integer.valueOf(quicksearchValue);
            }catch (Exception e){
                //ignore
            }
            String ctx = sessaoService.getUsuarioAtual().getChave();

            StringBuilder sql = new StringBuilder();

            sql.append("select _campos_ \n");
            sql.append("from customrevisionentity c \n");
            sql.append("inner join tipobenchmark_aud ca on ca.rev = c.id \n");
            sql.append(" where ca.codigo >0 \n");

            if (inicio != null && inicio > 0l) {
                Long dataInicio = Calendario.getDataComHoraZerada(new Date(inicio)).getTime();
                sql.append(" AND c.\"timestamp\" >= ").append(dataInicio).append(" \n");
            }
            if (fim != null && fim > 0l) {
                Long dataFim = Calendario.fimDoDia(new Date(fim)).getTime();
                sql.append(" AND c.\"timestamp\" <= ").append(dataFim).append(" \n");
            }

            if(tipos != null && tipos.length() > 0){
                String tiposCod = "";
                for(int i = 0; i < tipos.length(); i++){
                    tiposCod += "," + TipoRevisaoEnum.valueOf(tipos.getString(i)).getId();
                }
                sql.append(" and ca.revtype in ( ").append(tiposCod.replaceFirst(",", "")).append(" )");
            }

            sql.append(UteisValidacao.emptyString(quicksearchValue) ? "" : " and (");
            if (codigoTpBenchmarks != null){
                sql.append(" ca.codigo = ").append(codigoTpBenchmarks).append(" or \n");
            }
            if (!UteisValidacao.emptyString(quicksearchValue)) {
                sql.append("  upper(ca.nome) like '%").append(quicksearchValue.toUpperCase()).append("%' or \n");
                sql.append("  upper(c.username) like '%").append(quicksearchValue.toUpperCase()).append("%' \n");
            }
            sql.append(UteisValidacao.emptyString(quicksearchValue) ? "" : ")");

            Integer maxResults = 10;
            Integer indiceInicial = 0;
            if (paginadorDTO != null) {
                maxResults = paginadorDTO.getSize() == null ? 10 : paginadorDTO.getSize().intValue();
                indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
            }
            try (ResultSet rs = tipoBenchmarkDao.createStatement(ctx,
                    sql.toString().replace("_campos_", "c.id, c.ip, c.screenname, c.\"timestamp\", c.username, c.processo, \n" +
                                    "            ca.nome, ca.rev , ca.revtype, ca.codigo")
                            .concat("order by \"timestamp\" desc, id desc limit ".concat(maxResults.toString()))
                            .concat(" offset ".concat(indiceInicial.toString())))) {

                while (rs.next()) {
                    TipoRevisaoEnum revtype = TipoRevisaoEnum.getFromId(rs.getInt("revtype"));
                    Integer codigo = rs.getInt("codigo");
                    Long timestamp = rs.getLong("timestamp");
                    Long rev = rs.getLong("id");
                    String username = rs.getString("username");
                    LogTO logTO = new LogTO(codigo, revtype.getDescricaoLog(), rs.getString("nome"),
                            username,
                            Uteis.getDataAplicandoFormatacao(new Date(timestamp), "dd/MM/yyyy HH:mm"),
                            Uteis.getDataAplicandoFormatacao(new Date(timestamp), "HH:mm:ss"),
                            new ArrayList());

                    AlteracoesTO alteracoesTO = null;
                    if (revtype.equals(TipoRevisaoEnum.UPDATE) || revtype.equals(TipoRevisaoEnum.DELETE)) {
                        try (ResultSet rsAnterior = tipoBenchmarkDao.createStatement(ctx,
                                "select aa.codigo, aa.revtype, aa.rev, " +
                                        " aa.nome from tipobenchmark_aud aa where codigo = " + codigo +
                                        " and rev < " + rev +
                                        " order by rev desc limit 1 ")) {
                            if (rsAnterior.next()) {
                                logTO.setAlteracoes(new ArrayList<>());
                                alteracoesTO = new AlteracoesTO("nome", rsAnterior.getString("nome"),
                                        revtype.equals(TipoRevisaoEnum.DELETE) ? "" : rs.getString("nome"));
                                logTO.getAlteracoes().add(alteracoesTO);
                            }
                        }
                    } else {
                        alteracoesTO = new AlteracoesTO("nome", "",
                                rs.getString("nome"));
                        logTO.getAlteracoes().add(alteracoesTO);
                    }

                    logTO.setDescricao(logTO.getDescricao() + ("[" + alteracoesTO.getCampo() + ":'"
                            + (UteisValidacao.emptyString(alteracoesTO.getValorAnterior()) ? "" : (alteracoesTO.getValorAnterior() + "' para '"))
                            + alteracoesTO.getValorAlterado() + "']<br/>"));
                    listarLog.add(logTO);
                }
            }
            try (ResultSet rsCount = tipoBenchmarkDao.createStatement(ctx, sql.toString().replace("_campos_", " count(c.id) as cont "))) {
                if (rsCount.next()) {
                    paginadorDTO.setQuantidadeTotalElementos(rsCount.getLong("cont"));
                } else {
                    paginadorDTO.setQuantidadeTotalElementos(10l);
                }
            }


        }catch (Exception e){
            throw new ServiceException(e);
        }
        return listarLog;

    }

    public List<LogTO> listarColaboradoresUsuario(JSONObject filtros, PaginadorDTO paginadorDTO) throws ServiceException{
        List<LogTO> listarLog = new ArrayList<>();
        try {
            String quicksearchValue = filtros.optString("quicksearchValue");
            JSONArray tipos = filtros.optJSONArray("tipo");
            Long inicio = filtros.optLong("dataInicio");
            Long fim = filtros.optLong("dataFim");
            Integer codigoTpBenchmarks = null;
            try {
                codigoTpBenchmarks = quicksearchValue == null ? null : Integer.valueOf(quicksearchValue);
            }catch (Exception e){
                //ignore
            }
            String ctx = sessaoService.getUsuarioAtual().getChave();

            StringBuilder sql = new StringBuilder();

            sql.append("select _campos_ \n");
            sql.append("from customrevisionentity c \n");
            sql.append("inner join usuario_aud ca on ca.rev = c.id \n");
            sql.append("inner join perfil p on p.codigo = ca.perfil_codigo  \n");
            sql.append(" where ca.professor_codigo > 0 \n");

            if (inicio != null && inicio > 0l) {
                Long dataInicio = Calendario.getDataComHoraZerada(new Date(inicio)).getTime();
                sql.append(" AND c.\"timestamp\" >= ").append(dataInicio).append(" \n");
            }
            if (fim != null && fim > 0l) {
                Long dataFim = Calendario.fimDoDia(new Date(fim)).getTime();
                sql.append(" AND c.\"timestamp\" <= ").append(dataFim).append(" \n");
            }

            if(tipos != null && tipos.length() > 0){
                String tiposCod = "";
                for(int i = 0; i < tipos.length(); i++){
                    tiposCod += "," + TipoRevisaoEnum.valueOf(tipos.getString(i)).getId();
                }
                sql.append(" and ca.revtype in ( ").append(tiposCod.replaceFirst(",", "")).append(" )");
            }

            sql.append(UteisValidacao.emptyString(quicksearchValue) ? "" : " and (");
            if (codigoTpBenchmarks != null){
                sql.append(" ca.codigo = ").append(codigoTpBenchmarks).append(" or \n");
            }
            if (!UteisValidacao.emptyString(quicksearchValue)) {
                sql.append("  upper(ca.nome) like '%").append(quicksearchValue.toUpperCase()).append("%' or \n");
                sql.append("  upper(c.username) like '%").append(quicksearchValue.toUpperCase()).append("%' \n");
            }
            sql.append(UteisValidacao.emptyString(quicksearchValue) ? "" : ")");

            Integer maxResults = 10;
            Integer indiceInicial = 0;
            if (paginadorDTO != null) {
                maxResults = paginadorDTO.getSize() == null ? 10 : paginadorDTO.getSize().intValue();
                indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
            }
            try (ResultSet rs = tipoBenchmarkDao.createStatement(ctx,
                    sql.toString().replace("_campos_", "c.id, c.ip, c.screenname, c.\"timestamp\", c.username, c.processo, \n" +
                                    "            ca.nome, ca.rev , ca.revtype, ca.codigo, p.nome as perfil, ca.fotokeyapp, ca.senha, ca.username as username_u ")
                            .concat("order by \"timestamp\" desc, id desc limit ".concat(maxResults.toString()))
                            .concat(" offset ".concat(indiceInicial.toString())))) {

                while (rs.next()) {
                    TipoRevisaoEnum revtype = TipoRevisaoEnum.getFromId(rs.getInt("revtype"));
                    Integer codigo = rs.getInt("codigo");
                    Long timestamp = rs.getLong("timestamp");
                    Long rev = rs.getLong("id");
                    String username = rs.getString("username");
                    LogTO logTO = new LogTO(codigo, revtype.getDescricaoLog(), rs.getString("nome"),
                                UteisValidacao.emptyString(username) ? "serviço" : username,
                            Uteis.getDataAplicandoFormatacao(new Date(timestamp), "dd/MM/yyyy HH:mm"),
                            Uteis.getDataAplicandoFormatacao(new Date(timestamp), "HH:mm:ss"),
                            new ArrayList());
                    Map<String, String> valoresAlterados = montarMapa(rs, "nome", "perfil");
                    Map<String, String> valoresAnteriores = null;

                    String senhaNova = rs.getString("senha");
                    logTO.setAlteracoes(new ArrayList<>());
                    if (revtype.equals(TipoRevisaoEnum.UPDATE) || revtype.equals(TipoRevisaoEnum.DELETE)) {
                        try (ResultSet rsAnterior = tipoBenchmarkDao.createStatement(ctx,
                                "select aa.codigo, aa.revtype, aa.rev, aa.fotokeyapp, aa.senha,  " +
                                        " aa.nome, aa.username as username_u, p.nome as perfil from usuario_aud aa " +
                                        " inner join perfil p on p.codigo = aa.perfil_codigo" +
                                        " where aa.codigo = " + codigo +
                                        " and rev < " + rev +
                                        " order by rev desc limit 1 ")) {
                            if (rsAnterior.next()) {
                                valoresAnteriores = montarMapa(rsAnterior, "nome", "perfil", "fotokeyapp", "username_u");
                                if (senhaNova != null && !senhaNova.equals(rsAnterior.getString("senha"))) {
                                    logTO.getAlteracoes().add(new AlteracoesTO("senha", "", "senha alterada"));
                                }
                            }
                        }
                    }
                    logTO.getAlteracoes().addAll(compararMapas(valoresAnteriores, valoresAlterados));
                    for (AlteracoesTO a : logTO.getAlteracoes()) {
                        logTO.setDescricao(logTO.getDescricao() + ("[" + a.getCampo() + ":'"
                                + (UteisValidacao.emptyString(a.getValorAnterior()) ? "" : (a.getValorAnterior() + "' para '"))
                                + a.getValorAlterado() + "']<br/>"));
                    }
                    listarLog.add(logTO);
                }
            }
            try (ResultSet rsCount = tipoBenchmarkDao.createStatement(ctx, sql.toString().replace("_campos_", " count(c.id) as cont "))) {
                if (rsCount.next()) {
                    paginadorDTO.setQuantidadeTotalElementos(rsCount.getLong("cont"));
                } else {
                    paginadorDTO.setQuantidadeTotalElementos(10l);
                }
            }


        }catch (Exception e){
            throw new ServiceException(e);
        }
        return listarLog;

    }


    //------------- início log de perfil de acesso
    @Override
    public List<LogTO> listarLogPerfilAcesso(JSONObject filtros, PaginadorDTO paginadorDTO) throws ServiceException {
        List<LogTO> listarLog = new ArrayList<>();
        try {
            String quicksearchValue = filtros.optString("quicksearchValue");
            JSONArray tipos = filtros.optJSONArray("tipo");
            Long inicio = filtros.optLong("dataInicio");
            Long fim = filtros.optLong("dataFim");
            Integer codigoPerfil = null;
            try {
                codigoPerfil = quicksearchValue == null ? null : Integer.valueOf(quicksearchValue);
            } catch (Exception e) {
                //ignore
            }
            String ctx = sessaoService.getUsuarioAtual().getChave();
            StringBuilder sql = new StringBuilder();
            sql.append(" SELECT _campos_ \n");
            sql.append(" FROM customrevisionentity c \n");
            sql.append(" LEFT JOIN perfil_aud pa ON pa.rev = c.id \n");
            sql.append(" LEFT JOIN permissao_aud pe ON pe.rev = c.id \n");
            sql.append(" LEFT JOIN perfil p on p.codigo = pe.perfil_codigo  \n");
            sql.append(" where (pe.codigo is not null or pa.codigo is not null) \n");
            if (inicio != null && inicio > 01) {
                Long dataInicio = Calendario.getDataComHoraZerada(new Date(inicio)).getTime();
                sql.append(" AND c.\"timestamp\" >= ").append(dataInicio).append(" \n");
            }
            if (fim != null && fim > 01) {
                Long dataFim = Calendario.fimDoDia(new Date(fim)).getTime();
                sql.append(" AND c.\"timestamp\" <= ").append(dataFim).append(" \n");
            }
            if (tipos != null && tipos.length() > 0) {
                String tiposCod = "";
                for (int i = 0; i < tipos.length(); i++) {
                    tiposCod += "," + TipoRevisaoEnum.valueOf(tipos.getString(i)).getId();
                }
                sql.append(" AND pa.revtype in (").append(tiposCod.replaceFirst(",", "")).append(") \n");
            }
            sql.append(UteisValidacao.emptyString(quicksearchValue) ? "" : " AND (");
            if (codigoPerfil != null) {
                sql.append(" pa.codigo = ").append(codigoPerfil).append(" OR \n");
            }
            if (!UteisValidacao.emptyString(quicksearchValue)) {
                sql.append(" UPPER(pa.nome) LIKE '%").append(quicksearchValue.toUpperCase()).append("%' OR \n");
                sql.append(" UPPER(c.username) LIKE '%").append(quicksearchValue.toUpperCase()).append("%' \n");
            }
            sql.append(UteisValidacao.emptyString(quicksearchValue) ? "" : ")");
            Integer maxResults = 10;
            Integer indiceInicial = 0;
            if (paginadorDTO != null) {
                maxResults = paginadorDTO.getSize() == null ? 10 :paginadorDTO.getSize().intValue();
                indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
            }
            try (ResultSet rs = perfilDao.createStatement(
                    ctx,
                    sql.toString().replace("_campos_", "distinct c.id, c.ip, c.screenname, c.\"timestamp\", c.username, c.processo, " +
                                    "pa.codigo, pa.revtype, pa.nome, p.codigo as codigoaltrecurso, p.nome as nomePerfil")
                            .concat(" ORDER BY \"timestamp\" DESC, id DESC LIMIT ").concat(maxResults.toString())
                            .concat(" OFFSET ".concat(indiceInicial.toString()))
            )) {
                while (rs.next()) {
                    TipoRevisaoEnum revtype = TipoRevisaoEnum.getFromId(rs.getInt("revtype"));
                    Integer codigo = rs.getInt("codigo");
                    Long timestamp = rs.getLong("timestamp");
                    Long rev = rs.getLong("id");
                    String username = rs.getString("username");
                    if (UteisValidacao.emptyNumber(codigo)) {
                        revtype = TipoRevisaoEnum.UPDATE;
                        codigo = rs.getInt("codigoaltrecurso");
                    }
                    Map<String, String> valoresAlterados = montarMapa(rs, "nome");
                    Map<String, String> valoresAnteriores = null;
                    LogTO logTO = new LogTO(codigo,
                            revtype.getDescricaoLog(),
                            rs.getString("nome"),
                            username,
                            Uteis.getDataAplicandoFormatacao(new Date(timestamp), "dd/MM/yyyy HH:mm"),
                            Uteis.getDataAplicandoFormatacao(new Date(timestamp), "HH:mm:ss"),
                            new ArrayList());
                    if (revtype.equals(TipoRevisaoEnum.UPDATE)) {
                        try (ResultSet rsAnterior = perfilDao.createStatement(
                                ctx,
                                "SELECT pa.codigo, pa.revtype, pa.rev, pa.nome" +
                                        " FROM perfil_aud pa" +
                                        " WHERE codigo = " + codigo +
                                        " AND rev < " + rev +
                                        " ORDER BY rev DESC LIMIT 1"
                        )) {
                            if (rsAnterior.next()) {
                                valoresAnteriores = montarMapa(rsAnterior, "nome");
                            }
                        }
                    } else if (revtype.equals(TipoRevisaoEnum.DELETE)) {
                        try (ResultSet rsAnterior = perfilDao.createStatement(
                                ctx,
                                "SELECT pa.codigo, pa.revtype, pa.rev, pa.nome" +
                                        " FROM perfil_aud pa" +
                                        " WHERE codigo = " + codigo +
                                        " AND rev < " + rev +
                                        " ORDER BY rev DESC LIMIT 1"
                        )) {
                            if (rsAnterior.next()) {
                                logTO.setIdentificador(rsAnterior.getString("nome"));
                            }
                        }
                    }
                    if (!revtype.equals(TipoRevisaoEnum.DELETE)) {
                        if (rs.getInt("codigo") == 0) {
                            logTO.setIdentificador(rs.getString("nomePerfil"));
                            logTO.setAlteracoes(alteracoesRecursoPermissao(ctx, rev, null));
                        } else {
                            logTO.setAlteracoes(compararMapas(valoresAnteriores, valoresAlterados));
                            logTO.getAlteracoes().addAll(alteracoesRecursoPermissao(ctx, rev, null));
                        }
                    }
                    for (AlteracoesTO a : logTO.getAlteracoes()) {
                        logTO.setDescricao(
                                logTO.getDescricao() +
                                        ("[" + a.getCampo() + ":'"
                                                + (UteisValidacao.emptyString(a.getValorAnterior()) ? "" : (a.getValorAnterior() + "' para'"))
                                                + a.getValorAlterado() + "']<br/>")
                        );
                    }
                    logTO.setIdentificador(logTO.getIdentificador() == null ? obterIdentificadorUltimaAlteracao(ctx, rev) : logTO.getIdentificador());
                    listarLog.add(logTO);
                }
            }
            try (ResultSet rsCount = perfilDao.createStatement(ctx, sql.toString().replace("_campos_", " COUNT(distinct c.id) as count"))) {
                if (rsCount.next()) {
                    paginadorDTO.setQuantidadeTotalElementos(rsCount.getLong("count"));
                } else {
                    paginadorDTO.setQuantidadeTotalElementos(10l);
                }
            }
        } catch (Exception e) {
            throw new ServiceException(e);
        }
        return listarLog;

    }
    //------------- log de disponibilidades

    public String obterIdentificadorUltimaAlteracao(String ctx, Long rev) throws Exception {
        String nome;
        try (ResultSet rs = perfilDao.createStatement(ctx, "SELECT perfil_codigo FROM permissao_aud pa  WHERE pa.rev = " + rev)) {
            nome = "";
            if (rs.next()) {
                Integer codigo = rs.getInt("perfil_codigo");
                ResultSet rsAnterior = perfilDao.createStatement(
                        ctx,
                        "SELECT pa.nome" +
                                " FROM perfil_aud pa" +
                                " WHERE codigo = " + codigo +
                                " AND rev < " + rev +
                                " ORDER BY rev DESC LIMIT 1"
                );
                if (rsAnterior.next()) {
                    nome = rsAnterior.getString("nome");
                }
            }
        }
        return nome;
    }

    private List<AlteracoesTO> alteracoesRecursoPermissao(String chave, Long rev, Integer codigoPermissao) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT distinct pta.tipopermissoes, pa.codigo, pa.recurso, pta.rev, pta.revtype \n" +
                   " FROM permissao_aud pa \n" +
                   " INNER JOIN permissao_tipopermissoes_aud pta ON pta.permissao_codigo = pa.codigo \n" +
                   " WHERE pta.rev = " + rev + " AND pa.recurso IS NOT NULL ");
        sql.append(codigoPermissao == null ? "" : " AND pa.codigo = " + codigoPermissao);
        Map<RecursoEnum, Map<Integer, List<String>>> alteracoesRecurso;
        List<AlteracoesTO> alteracoesTOList;
        try (ResultSet rs = perfilDao.createStatement(chave, sql.toString())) {
            alteracoesRecurso = new HashMap<>();
            alteracoesTOList = new ArrayList<>();
            while (rs.next()) {
                Integer recurso = rs.getInt("recurso");
                RecursoEnum recursoEnum = RecursoEnum.getFromId(recurso);
                if (recursoEnum.getTipo().equals(TipoRecurso.FUNCIONALIDADE)) {
                    AlteracoesTO alteracoesTO = new AlteracoesTO();
                    alteracoesTO.setCampo(recursoEnum.getNome());
                    alteracoesTO.setValorAlterado(rs.getInt("revtype") == 2 ? "Desabilitada" : "Habilitada");
                    alteracoesTOList.add(alteracoesTO);
                    continue;
                }
                Map<Integer, List<String>> alteracoes = alteracoesRecurso.get(recursoEnum);
                if (alteracoes == null) {
                    alteracoes = new HashMap<>();
                    alteracoesRecurso.put(recursoEnum, alteracoes);
                }
                List<String> stringList = alteracoes.get(rs.getInt("revtype"));
                if (stringList == null) {
                    stringList = new ArrayList<>();
                    alteracoes.put(rs.getInt("revtype"), stringList);
                }
                stringList.add(TipoPermissaoEnum.getFromOrdinal(rs.getInt("tipopermissoes")).getNome());
            }
        }
        for(RecursoEnum recursoEnum : alteracoesRecurso.keySet()){
            Map<Integer,List<String>> alteracoes = alteracoesRecurso.get(recursoEnum);
            AlteracoesTO alteracoesTO = new AlteracoesTO();
            alteracoesTO.setCampo(recursoEnum.getNome());
            alteracoesTO.setValorAlterado(alteracaoDeLista(alteracoes.get(0), "Marcado", "Marcados", false));
            alteracoesTO.setValorAlterado(alteracoesTO.getValorAlterado() + alteracaoDeLista(alteracoes.get(2), "Desmarcado", "Desmarcados", alteracoes.get(0) != null && !alteracoes.get(0).isEmpty()));
            alteracoesTOList.add(alteracoesTO);
        }

        return alteracoesTOList;

    }

    public String alteracaoDeLista(List<String> lista, String labelSingular, String labelPlural, boolean quebra){
        if(lista == null || lista.isEmpty()){
            return "";
        }
        StringBuilder retorno = new StringBuilder(quebra ? "<br/>" : "");
        retorno.append(lista.size() == 1 ? labelSingular : labelPlural);
        retorno.append(": ");
        retorno.append(Uteis.concatenarListaVirgula(lista));
        return retorno.toString();
    }

    public List<LogTO> listarLogPerfilAcessoRecurso(JSONObject filtros, Integer perfilId, String categoriaRecurso, PaginadorDTO paginadorDTO) throws ServiceException {
        List<LogTO> listarLog = new ArrayList<>();
        try {
            String quicksearchValue = filtros.optString("quicksearchValue");
            JSONArray tipos = filtros.optJSONArray("tipo");
            Long inicio = filtros.optLong("dataInicio");
            Long fim = filtros.optLong("dataFim");
            Integer codigoPerfil = null;
            try {
                codigoPerfil = quicksearchValue == null ? null : Integer.valueOf(quicksearchValue);
            } catch (Exception e) {
                //ignore
            }
            String ctx = sessaoService.getUsuarioAtual().getChave();
            StringBuilder sql = new StringBuilder();
            sql.append(" SELECT _campos_ \n");
            sql.append(" FROM customrevisionentity c \n");
            sql.append(" INNER JOIN permissao_aud pa on pa.rev = c.id \n");
            sql.append(" WHERE pa.perfil_codigo = ").append(perfilId).append(" \n");
            sql.append(" AND pa.recurso in (").append(montarListaRecursosPorCategoria(categoriaRecurso, null)).append(") \n");
            if (inicio != null && inicio > 01) {
                Long dataInicio = Calendario.getDataComHoraZerada(new Date(inicio)).getTime();
                sql.append(" AND c.\"timestamp\" >= ").append(dataInicio).append(" \n");
            }
            if (fim != null && fim > 01) {
                Long dataFim = Calendario.fimDoDia(new Date(fim)).getTime();
                sql.append(" AND c.\"timestamp\" <= ").append(dataFim).append(" \n");
            }
            if (tipos != null && tipos.length() > 0) {
                String tiposCod = "";
                for (int i = 0; i < tipos.length(); i++) {
                    tiposCod += "," + TipoRevisaoEnum.valueOf(tipos.getString(i)).getId();
                }
                sql.append(" AND pa.revtype in (").append(tiposCod.replaceFirst(",", "")).append(") \n");
            }
            sql.append(UteisValidacao.emptyString(quicksearchValue) ? "" : " AND (");
            if (codigoPerfil != null) {
                sql.append(" pa.codigo = ").append(codigoPerfil).append(" OR \n");
            }
            if (!UteisValidacao.emptyString(quicksearchValue)) {
                sql.append(" UPPER(c.username) LIKE '%").append(quicksearchValue.toUpperCase()).append("%' \n");
            }
            sql.append(UteisValidacao.emptyString(quicksearchValue) ? "" : ")");
            Integer maxResults = 10;
            Integer indiceInicial = 0;
            if (paginadorDTO != null) {
                maxResults = paginadorDTO.getSize() == null ? 10 :paginadorDTO.getSize().intValue();
                indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
            }
            try (ResultSet rs = perfilDao.createStatement(
                    ctx,
                    sql.toString().replace("_campos_", "distinct c.id, c.ip, c.screenname, c.\"timestamp\", c.username, c.processo, " +
                                    "pa.codigo, pa.revtype, pa.perfil_codigo, pa.recurso")
                            .concat(" ORDER BY \"timestamp\" DESC, id DESC LIMIT ").concat(maxResults.toString())
                            .concat(" OFFSET ".concat(indiceInicial.toString()))
            )) {
                while (rs.next()) {
                    TipoRevisaoEnum revtype = TipoRevisaoEnum.getFromId(rs.getInt("revtype"));
                    Integer codigo = rs.getInt("codigo");
                    Long timestamp = rs.getLong("timestamp");
                    Long rev = rs.getLong("id");
                    String username = rs.getString("username");
                    if (UteisValidacao.emptyNumber(codigo)) {
                        revtype = TipoRevisaoEnum.UPDATE;
                        codigo = rs.getInt("perfil_codigo");
                    }
                    LogTO logTO = new LogTO(codigo,
                            revtype.getDescricaoLog(),
                            RecursoEnum.getFromId(rs.getInt("recurso")).getNome(),
                            username,
                            Uteis.getDataAplicandoFormatacao(new Date(timestamp), "dd/MM/yyyy HH:mm"),
                            Uteis.getDataAplicandoFormatacao(new Date(timestamp), "HH:mm:ss"),
                            new ArrayList());
                    logTO.setAlteracoes(alteracoesRecursoPermissao(ctx, rev, codigo));
                    for (AlteracoesTO a : logTO.getAlteracoes()) {
                        logTO.setDescricao(
                                logTO.getDescricao() +
                                        ("[" + a.getCampo() + ":'"
                                                + (UteisValidacao.emptyString(a.getValorAnterior()) ? "" : (a.getValorAnterior() + "' para'"))
                                                + a.getValorAlterado() + "']<br/>")
                        );
                    }
                    if (logTO.getAlteracoes().size() == 0) {
                        logTO.setDescricao(logTO.getDescricao() + ("[Sem Permissões]<br/>"));
                    }
                    listarLog.add(logTO);
                }
            }
            try (ResultSet rsCount = perfilDao.createStatement(ctx, sql.toString().replace("_campos_", " COUNT(c.id) as count"))) {
                if (rsCount.next()) {
                    paginadorDTO.setQuantidadeTotalElementos(rsCount.getLong("count"));
                } else {
                    paginadorDTO.setQuantidadeTotalElementos(10l);
                }
            }
        } catch (Exception e) {
            throw new ServiceException(e);
        }
        return listarLog;
    }

    public String montarListaRecursosPorCategoria(String categoriaRecurso, String nomeRecurso) throws ServiceException {
        String recursos = "";
        for (RecursoEnum re : RecursoEnum.values()) {
            if (re.getCategoria().name().equals(categoriaRecurso) || re.getNome().toUpperCase().equals(nomeRecurso)) {
                recursos += "," + re.ordinal();
            }
        }
        return recursos.replaceFirst(",", "");
    }
    //------------- fim log de perfil de acesso


    @Override
    public List<LogTO> listarLog(EntidadeLogEnum entidade, JSONObject filtros, PaginadorDTO paginadorDTO,
                                 String chavesPrimarias, String chavePrimariaEntidadeSubordinada,
                                 String descOperacao) throws ServiceException {
        return listarLog(entidade, filtros, paginadorDTO,
                chavesPrimarias, chavePrimariaEntidadeSubordinada,
                descOperacao, false);
    }
    public List<LogTO> listarLog(EntidadeLogEnum entidade, JSONObject filtros, PaginadorDTO paginadorDTO,
                                 String chavesPrimarias, String chavePrimariaEntidadeSubordinada,
                                 String descOperacao, boolean ia) throws ServiceException {
        List<LogTO> listarLog = new ArrayList<>();
        try {
            if (filtros == null) {
                filtros = new JSONObject();
            }
            String quicksearchValue = filtros.optString("quicksearchValue");
            JSONArray tipos = filtros.optJSONArray("tipo");
            Date inicio = null;
            if (filtros.optLong("dataInicio") > 0) {
                inicio = new Date(filtros.getLong("dataInicio"));
            }
            Date fim = null;
            if (filtros.optLong("dataFim") > 0) {
                fim = Calendario.fimDoDia(new Date(filtros.getLong("dataFim")));
            }
            Integer codigoAtv = null;
            try {
                codigoAtv = quicksearchValue == null ? null : Integer.valueOf(quicksearchValue);
            } catch (Exception e) {
                //ignore
            }
            Integer maxResults = 10;
            Integer indiceInicial = 0;
            if (paginadorDTO != null) {
                maxResults = paginadorDTO.getSize() == null ? 10 : paginadorDTO.getSize().intValue();
                indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
            }

            String ctx = sessaoService.getUsuarioAtual().getChave();

            Map<String, Object> params = new HashMap<>();
            StringBuilder hql = new StringBuilder();

            hql.append("SELECT obj FROM Log obj WHERE 1=1 ");
            if(entidade != null){
                hql.append(" AND LOWER(obj.nomeEntidade) = :entidade ");
                params.put("entidade", entidade.name().toLowerCase());
            }
            if (codigoAtv != null) {
                hql.append(" AND obj.chavePrimaria = :codigoAtv ");
                params.put("codigoAtv", codigoAtv.toString());
            }
            if (ia) {
                hql.append(" AND ia is true ");
            }

            if (!isNull(inicio) && !isNull(fim)) {
                hql.append(" AND obj.dataAlteracao between :inicio and :fim ");
                params.put("inicio", inicio);
                params.put("fim", fim);
            }
           if (isNotBlank(chavesPrimarias)) {
                hql.append(" AND obj.chavePrimaria in (").append(chavesPrimarias).append(")");
            }

            if (isNotBlank(chavePrimariaEntidadeSubordinada)) {
                hql.append(" AND obj.chavePrimariaEntidadeSubordinada in (").append(chavePrimariaEntidadeSubordinada).append(")");
            }

            if (isNotBlank(descOperacao)) {
                hql.append(" AND obj.descricao like '%").append(descOperacao.toUpperCase()).append("%'");
            }


            if (!isNull(tipos) && tipos.length() > 0) {
                hql.append(" AND obj.operacao in (");
                for (int i = 0; i < tipos.length(); i++) {
                    Object obj = tipos.get(i);
                    if (tipos.length() >= 1 && i == 0) {
                        hql.append("'").append(obj).append("'");
                    } else {
                        hql.append(",'").append(obj).append("'");
                    }
                }
                hql.append(")");
                hql = new StringBuilder(hql.toString().replace("INSERT", "INCLUSÃO").replace("UPDATE", "ALTERAÇÃO").replace("DELETE", "EXCLUSÃO"));
            }

            hql.append(" order by ");
            if (isNotBlank(paginadorDTO.getSort())) {
                String campo = paginadorDTO.getSort().split(",")[0];
                if (campo.equalsIgnoreCase("desc")) {
                    hql.append("obj.descricao " + paginadorDTO.getSort().split(",")[1]);
                } else if (campo.equalsIgnoreCase("data")) {
                    hql.append("obj.dataAlteracao " + paginadorDTO.getSort().split(",")[1]);
                } else if (campo.equalsIgnoreCase("operacao")) {
                    hql.append("obj.operacao " + paginadorDTO.getSort().split(",")[1]);
                } else if (campo.equalsIgnoreCase("usuario")) {
                    hql.append("obj.responsavelAlteracao " + paginadorDTO.getSort().split(",")[1]);
                } else if (campo.equalsIgnoreCase("id")) {
                    hql.append("obj.codigo " + paginadorDTO.getSort().split(",")[1]);
                } else {
                    hql.append("obj.dataAlteracao desc");
                }
            } else {
                hql.append("obj.dataAlteracao desc");
            }
            List<Log> logs = new ArrayList<>(logDao.findByParam(ctx, hql.toString(), params, maxResults, indiceInicial));

            for (Log log : logs) {
                LogTO logTO = new LogTO();
                logTO.setUsuario(log.getResponsavelAlteracao());
                logTO.setIdentificador(log.getNomeEntidade());
                logTO.setDescricao(log.getDescricao());
                AlteracoesTO alteracoesTO = new AlteracoesTO();
                alteracoesTO.setCampo("Campo(s)");
                alteracoesTO.setValorAnterior(log.getValorCampoAnterior());
                alteracoesTO.setValorAlterado(log.getValorCampoAlterado());
                logTO.setAlteracoes(new ArrayList<>());
                logTO.getAlteracoes().add(alteracoesTO);
                logTO.setChave(log.getChavePrimaria());
                logTO.setOperacao(log.getOperacao());
                logTO.setDia(Uteis.getDataAplicandoFormatacao(log.getDataAlteracao(), "dd/MM/yyyy - HH:mm:ss"));
                if(log.getDescricao().toLowerCase().contains("[Origem:".toLowerCase())){
                    String replace[] = log.getDescricao().toLowerCase().split("\\[origem:");
                    logTO.setOrigem(replace[1].replace("]", "").trim().toUpperCase());
                }
                listarLog.add(logTO);
            }
            try (ResultSet rsCount = logDao.createStatement(ctx,
                    " select count(obj) as cont from (" +
                            hql.toString()
                                    .replace(":inicio", "'" + Uteis.getDataFormatoBD(inicio) + "'")
                                    .replace(":codigoAtv", "'" + "'")
                                    .replace(":entidade", "'" + entidade.name().toLowerCase() + "'")
                                    .replace(":fim", "'" + Uteis.getDataFormatoBD(Calendario.fimDoDia(fim)) + "'") +
                            ") as t")) {
                if (rsCount.next()) {
                    paginadorDTO.setQuantidadeTotalElementos(rsCount.getLong("cont"));
                } else {
                    paginadorDTO.setQuantidadeTotalElementos(10l);
                }
            }

            return listarLog;
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    @Override
    public List<LogTO> listarLogExportacaoZW(ItemExportacaoEnum entidade, JSONObject filtros, PaginadorDTO paginadorDTO,
                                             String chavesPrimarias, String chavePrimariaEntidadeSubordinada,
                                             String descOperacao, boolean ia) throws ServiceException {
        List<LogTO> listarLog = new ArrayList<>();
        try {
            if (filtros == null) {
                filtros = new JSONObject();
            }
            String quicksearchValue = filtros.optString("quicksearchValue");
            JSONArray tipos = filtros.optJSONArray("tipo");
            Date inicio = null;
            if (filtros.optLong("dataInicio") > 0) {
                inicio = new Date(filtros.getLong("dataInicio"));
            }
            Date fim = null;
            if (filtros.optLong("dataFim") > 0) {
                fim = Calendario.fimDoDia(new Date(filtros.getLong("dataFim")));
            }
            Integer codigoAtv = null;
            try {
                codigoAtv = quicksearchValue == null ? null : Integer.valueOf(quicksearchValue);
            } catch (Exception e) {
                //ignore
            }
            Integer maxResults = 10;
            Integer indiceInicial = 0;
            if (paginadorDTO != null) {
                maxResults = paginadorDTO.getSize() == null ? 10 : paginadorDTO.getSize().intValue();
                indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
            }

            String ctx = sessaoService.getUsuarioAtual().getChave();

            Map<String, Object> params = new HashMap<>();
            StringBuilder hql = new StringBuilder();

            hql.append("SELECT * FROM Log obj WHERE 1=1 ");
            if(entidade != null){
                hql.append(" AND LOWER(obj.nomeEntidade) = '"+entidade.getDescricaoLog().toLowerCase()+"' ");
            }
            if (codigoAtv != null) {
                hql.append(" AND obj.chavePrimaria = '"+codigoAtv.toString()+"' ");
            }
            if (ia) {
                hql.append(" AND ia is true ");
            }

            if (!isNull(inicio) && !isNull(fim)) {
                hql.append(" AND obj.dataAlteracao between '"+inicio+"' and '"+fim+"' ");
            }
            if (isNotBlank(chavesPrimarias)) {
                hql.append(" AND obj.chavePrimaria in (").append(chavesPrimarias).append(")");
            }

            if (isNotBlank(chavePrimariaEntidadeSubordinada)) {
                hql.append(" AND obj.chavePrimariaEntidadeSubordinada in (").append(chavePrimariaEntidadeSubordinada).append(")");
            }

            if (isNotBlank(descOperacao)) {
                hql.append(" AND obj.descricao like '%").append(descOperacao.toUpperCase()).append("%'");
            }


            if (!isNull(tipos) && tipos.length() > 0) {
                hql.append(" AND obj.operacao in (");
                for (int i = 0; i < tipos.length(); i++) {
                    Object obj = tipos.get(i);
                    if (tipos.length() >= 1 && i == 0) {
                        hql.append("'").append(obj).append("'");
                    } else {
                        hql.append(",'").append(obj).append("'");
                    }
                }
                hql.append(")");
                hql = new StringBuilder(hql.toString().replace("INSERT", "INCLUSÃO").replace("UPDATE", "ALTERAÇÃO").replace("DELETE", "EXCLUSÃO"));
            }

            hql.append(" order by ");
            if (isNotBlank(paginadorDTO.getSort())) {
                String campo = paginadorDTO.getSort().split(",")[0];
                if (campo.equalsIgnoreCase("desc")) {
                    hql.append("obj.descricao " + paginadorDTO.getSort().split(",")[1]);
                } else if (campo.equalsIgnoreCase("data")) {
                    hql.append("obj.dataAlteracao " + paginadorDTO.getSort().split(",")[1]);
                } else if (campo.equalsIgnoreCase("operacao")) {
                    hql.append("obj.operacao " + paginadorDTO.getSort().split(",")[1]);
                } else if (campo.equalsIgnoreCase("usuario")) {
                    hql.append("obj.responsavelAlteracao " + paginadorDTO.getSort().split(",")[1]);
                } else if (campo.equalsIgnoreCase("id")) {
                    hql.append("obj.codigo " + paginadorDTO.getSort().split(",")[1]);
                } else {
                    hql.append("obj.dataAlteracao desc");
                }
            } else {
                hql.append("obj.dataAlteracao desc");
            }
            try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
                List<TipoAmbienteResponseTO> tipoAmbienteResponseTOS;
                try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta( hql.toString(), conZW)) {
                    List<Log> logs = new ArrayList<>();

                    while (rs.next()) {
                        LogTO logTO = new LogTO();
                        logTO.setUsuario(rs.getString("responsavelalteracao"));
                        logTO.setIdentificador(rs.getString("nomeentidade"));
                        logTO.setDescricao(rs.getString("nomeentidadedescricao")==null?"":rs.getString("nomeentidadedescricao"));
                        AlteracoesTO alteracoesTO = new AlteracoesTO();
                        alteracoesTO.setCampo("Campo(s)");
                        alteracoesTO.setValorAnterior(rs.getString("valorcampoanterior"));
                        alteracoesTO.setValorAlterado(rs.getString("valorcampoalterado"));
                        logTO.setAlteracoes(new ArrayList<>());
                        logTO.getAlteracoes().add(alteracoesTO);
                        logTO.setChave(rs.getString("chaveprimaria"));
                        logTO.setOperacao(rs.getString("operacao"));
                        String dtSplit[] = rs.getString("dataalteracao").split(" ");
                        logTO.setHora(dtSplit[1].substring(0,5));
                        logTO.setDia(Uteis.getData(rs.getDate("dataalteracao")).toString()+" "+dtSplit[1].substring(0,5));
                        listarLog.add(logTO);
                    }
                    String hqlCount = " select count(*) as cont from (" +
                            hql.toString()
                                    .replace(":inicio", "'" + Uteis.getDataFormatoBD(inicio) + "'")
                                    .replace(":codigoAtv", "'" + "'")
                                    .replace(":entidade", "'" + entidade.name().toLowerCase() + "'")
                                    .replace(":fim", "'" + Uteis.getDataFormatoBD(Calendario.fimDoDia(fim)) + "'") +
                            ") as t";

                    try(ResultSet rsCount = ConexaoZWServiceImpl.criarConsulta(hqlCount.toString(), conZW)){
                        if(rs.next()){
                            paginadorDTO.setQuantidadeTotalElementos(rsCount.getLong("cont"));
                        } else {
                            paginadorDTO.setQuantidadeTotalElementos(10l);
                        }
                    }

                }

                return listarLog;
            } catch (Exception e) {
                throw new ServiceException(e);
            }

        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

}
