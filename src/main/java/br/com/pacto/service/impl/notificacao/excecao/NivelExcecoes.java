package br.com.pacto.service.impl.notificacao.excecao;

import br.com.pacto.service.exception.ExcecaoSistema;

/**
 * Created by ulisses on 24/08/2018.
 */
public enum NivelExcecoes implements ExcecaoSistema {

    ERRO_BUSCAR_NIVEIS("erro_buscar_niveis", "Ocorreu um erro ao pesquisar os Níveis"),
    ERRO_BUSCAR_NIVEL("erro_buscar_nivel", "Ocorreu um erro ao pesquisar o Nível informado"),
    NIVEL_NAO_ENCONTRADO("nivel_nao_encontrado", "Nível informado não foi encontrado"),
    ERRO_INCLUIR_NIVEL("erro_incluir_nivel", "Ocorreu um erro ao incluir o nível informado"),
    VALIDACAO_ORDEM_INVALIDA("validacao_ordem_invalida", "A ordem informada e inválida"),
    VALIDACAO_NOME_NIVEL("validacao_nome_nivel", "Nome do nível não informado"),
    VALIDACAO_NIVEL_JA_EXISTE("validacao_nivel_ja_existe", "registro_duplicado"),
    ERRO_BUSCAR_NIVEL_MAIOR_ORDEM("erro_buscar_nivel_maior_ordem", "Ocorreu um erro ao pesquisar os nível de maior ordem"),
    ERRO_EXCLUIR_NIVEL("erro_excluir_nivel", "Ocorreu um erro ao excluir o nível informado"),
    ERRO_EXCLUIR_NIVEL_VINCULO("existe_aluno_vinculado", "existe_aluno_vinculado"),
    ERRO_ALTERAR_NIVEL("erro_alterar_nivel", "Ocorreu um erro ao alterar o nível");

    private String chave;
    private String descricao;

    NivelExcecoes(String chave, String descricao) {
        this.chave = chave;
        this.descricao = descricao;
    }

    @Override
    public String getChaveExcecao() {
        return chave;
    }

    @Override
    public String getDescricaoExcecao() {
        return descricao;
    }
}
