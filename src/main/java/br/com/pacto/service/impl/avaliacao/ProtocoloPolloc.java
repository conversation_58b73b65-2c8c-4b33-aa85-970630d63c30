package br.com.pacto.service.impl.avaliacao;

import br.com.pacto.bean.avaliacao.AvaliacaoFisica;
import br.com.pacto.bean.avaliacao.CategoriaAvaliacaoIMC;
import br.com.pacto.bean.avaliacao.CategoriaPercentualGordura;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.util.UteisValidacao;

import java.math.BigDecimal;

/**
 * Created by Rafael on 27/11/2016.
 */

public class ProtocoloPolloc {

    /**
     * Construtor
     * */
    public ProtocoloPolloc() {
        // TODO Auto-generated constructor stub
    }

    public static AvaliacaoFisica calculaDados(AvaliacaoFisica avaliacao){
        return calculaDados(avaliacao, false);
    }

    public static AvaliacaoFisica calculaDados(AvaliacaoFisica avaliacao, boolean forcarAdolescente){
        Boolean menorIdade = avaliacao.getCliente().getIdade() < 18 || forcarAdolescente;
        if (!isCalcularDados(avaliacao, menorIdade)) {
            return avaliacao;
        }

        Double massa_gorda;
        Double massa_magra;
        Double perc_gordura;

        Double imc = 0.0;
        if(!UteisValidacao.emptyNumber(avaliacao.getPeso()) && !UteisValidacao.emptyNumber(avaliacao.getAltura())) {
            imc = (avaliacao.getPeso() / (avaliacao.getAltura() * avaliacao.getAltura()));
        }

        Double densidadecorporal = 0.0;
        Double parametros;
        /**
         * Calculando a Densidade corporal Masculina
         *
         * */
        if (avaliacao.getCliente().getSexo().equalsIgnoreCase("M")){

            parametros = menorIdade ? avaliacao.getCoxaMedial() + avaliacao.getTriceps() : avaliacao.getAbdominal()+avaliacao.getCoxaMedial()+avaliacao.getPeitoral();

            densidadecorporal =  Uteis.forcarCasasDecimais(10,new BigDecimal(1.10938-(0.0008267 * (parametros))+(0.0000016 * Math.pow(parametros, 2))
                    - (0.0002574 * avaliacao.getCliente().getIdade())));
        } else {

            parametros = menorIdade ? avaliacao.getCoxaMedial() + avaliacao.getTriceps() : avaliacao.getSupraIliaca()+avaliacao.getCoxaMedial()+avaliacao.getTriceps();
            densidadecorporal =  Uteis.forcarCasasDecimais(10, new BigDecimal(1.099421 - (0.0009929 * (parametros))+(0.0000023 * Math.pow(parametros, 2))
                    - (0.0001393 * avaliacao.getCliente().getIdade())));

        }
        avaliacao.setTotalDobras(parametros);
        perc_gordura = percGordura(new BigDecimal(densidadecorporal).doubleValue());
        perc_gordura = perc_gordura < 0.0 ? 0.0 : perc_gordura;
        massa_gorda = (avaliacao.getPeso() * perc_gordura) /100;
        massa_magra = avaliacao.getPeso() - massa_gorda;


        avaliacao.setPercentualGordura(perc_gordura);
        avaliacao.setMassaGorda(Uteis.forcarCasasDecimais(2, new BigDecimal(massa_gorda)));
        avaliacao.setMassaMagra(Uteis.forcarCasasDecimais(2, new BigDecimal(massa_magra)));
        avaliacao.setImc(Uteis.forcarCasasDecimais(2, new BigDecimal(imc)));

        avaliacao.setCategoriaAvaliacaoIMC(classificacaoIMC(avaliacao.getImc()));
        avaliacao.setCategoriaPercentualGordura(classificacaoPercGodura(perc_gordura,avaliacao.getCliente().getSexo(), avaliacao.getCliente().getIdade()));

            avaliacao.setTotalPerimetria(avaliacao.getAntebracoDir()+avaliacao.getAntebracoEsq()+
                avaliacao.getBracoContraidoDir()+avaliacao.getBracoContraidoEsq()+
                avaliacao.getBracoRelaxadoDir()+avaliacao.getBracoRelaxadoEsq()+
                avaliacao.getCoxaMediaDir()+avaliacao.getCoxaMediaEsq()+
                avaliacao.getPanturrilhaDir()+avaliacao.getPanturrilhaEsq()+
                avaliacao.getToraxBusto()+avaliacao.getCintura()+avaliacao.getGluteo());
        //System.out.println("Calculo Dados Abd D: "+avaliacao.getAbdominal());

        return avaliacao;
    }


    public static CategoriaAvaliacaoIMC classificacaoIMC(Double imc) {

        CategoriaAvaliacaoIMC classificacao = null;
        if (imc == null || imc == 0) {
            classificacao = CategoriaAvaliacaoIMC.NENHUM;
        } else if (imc < 18.5) {
            classificacao = CategoriaAvaliacaoIMC.BAIXO;
        } else if (imc >= 18.5 && imc < 25) {
            classificacao = CategoriaAvaliacaoIMC.NORMAL;
        } else if (imc >= 25 && imc < 30) {
            classificacao = CategoriaAvaliacaoIMC.SOBREPESO;
        } else if (imc >= 30 && imc < 35) {
            classificacao = CategoriaAvaliacaoIMC.OBESIDADE_CLASSE_I;
        } else if (imc >= 35 && imc < 40) {
            classificacao = CategoriaAvaliacaoIMC.OBESIDADE_CLASSE_II;
        } else if (imc >= 40) {
            classificacao = CategoriaAvaliacaoIMC.OBESIDADE_CLASSE_III;
        }
        return classificacao;
    }

    public static CategoriaPercentualGordura classificacaoPercGodura(Double perc, String sexo, int idade){

        CategoriaPercentualGordura classificacao = null;

        if( sexo.equalsIgnoreCase("M") ){
            if (idade < 18) {
                if (perc < 6) {
                    classificacao = CategoriaPercentualGordura.BOM;
                } else if (perc < 10) {
                    classificacao = CategoriaPercentualGordura.ACIMA_MEDIA;
                } else if (perc < 20) {
                    classificacao = CategoriaPercentualGordura.MEDIA;
                } else if (perc < 25) {
                    classificacao = CategoriaPercentualGordura.ABAIXO_MEDIA;
                } else if (perc < 31) {
                    classificacao = CategoriaPercentualGordura.RUIM;
                } else {
                    classificacao = CategoriaPercentualGordura.MUITO_RUIM;
                }
            } else if( idade >= 18 && idade <= 25){

                if( perc < 8 )
                    classificacao = CategoriaPercentualGordura.EXCELENTE;
                else if( perc >= 8 && perc < 12 )
                    classificacao = CategoriaPercentualGordura.BOM;
                else if( perc >= 12 && perc < 14 )
                    classificacao = CategoriaPercentualGordura.ACIMA_MEDIA;
                else if( perc >= 14 && perc < 17 )
                    classificacao = CategoriaPercentualGordura.MEDIA;
                else if( perc >= 17 && perc < 21 )
                    classificacao = CategoriaPercentualGordura.ABAIXO_MEDIA;
                else if( perc >= 21 && perc < 26 )
                    classificacao = CategoriaPercentualGordura.RUIM;
                else if( perc >= 26 )
                    classificacao = CategoriaPercentualGordura.MUITO_RUIM;

            }
            else if(  idade >= 26 && idade <= 35){

                if( perc < 12 )
                    classificacao =  CategoriaPercentualGordura.EXCELENTE;
                else if( perc >= 12 && perc < 16 )
                    classificacao = CategoriaPercentualGordura.BOM;
                else if( perc >= 16 && perc < 19 )
                    classificacao =  CategoriaPercentualGordura.ACIMA_MEDIA;
                else if( perc >= 19 && perc < 22 )
                    classificacao =  CategoriaPercentualGordura.MEDIA;
                else if( perc >= 22 && perc < 25 )
                    classificacao = CategoriaPercentualGordura.ABAIXO_MEDIA;
                else if( perc >= 25 && perc < 28 )
                    classificacao = CategoriaPercentualGordura.RUIM;
                else if( perc >= 28 )
                    classificacao = CategoriaPercentualGordura.MUITO_RUIM;

            }
            else if( idade >= 36 && idade <= 45){

                if( perc < 16 )
                    classificacao = CategoriaPercentualGordura.EXCELENTE;
                else if( perc >= 16 && perc < 19 )
                    classificacao = CategoriaPercentualGordura.BOM;
                else if( perc >= 19 && perc < 22 )
                    classificacao = CategoriaPercentualGordura.ACIMA_MEDIA;
                else if( perc >= 22 && perc < 24 )
                    classificacao =  CategoriaPercentualGordura.MEDIA;
                else if( perc >= 24 && perc < 27 )
                    classificacao = CategoriaPercentualGordura.ABAIXO_MEDIA;
                else if( perc >= 27 && perc < 30 )
                    classificacao =  CategoriaPercentualGordura.RUIM;
                else if( perc >= 30 )
                    classificacao = CategoriaPercentualGordura.MUITO_RUIM;

            }
            else if( idade >= 46 && idade <= 55){

                if( perc < 18 )
                    classificacao = CategoriaPercentualGordura.EXCELENTE;
                else if( perc >= 18 && perc < 21 )
                    classificacao = CategoriaPercentualGordura.BOM;
                else if( perc >= 21 && perc < 24 )
                    classificacao = CategoriaPercentualGordura.ACIMA_MEDIA;
                else if( perc >= 24 && perc < 26 )
                    classificacao =  CategoriaPercentualGordura.MEDIA;
                else if( perc >= 26 && perc < 28 )
                    classificacao = CategoriaPercentualGordura.ABAIXO_MEDIA;
                else if( perc >= 28 && perc < 32 )
                    classificacao =  CategoriaPercentualGordura.RUIM;
                else if( perc >= 32 )
                    classificacao = CategoriaPercentualGordura.MUITO_RUIM;

            }
            else if( idade >= 56 ){

                if( perc < 20 )
                    classificacao = CategoriaPercentualGordura.EXCELENTE;
                else if( perc >= 20 && perc < 22 )
                    classificacao = CategoriaPercentualGordura.BOM;
                else if( perc >= 22 && perc < 24 )
                    classificacao = CategoriaPercentualGordura.ACIMA_MEDIA;
                else if( perc >= 24 && perc < 26 )
                    classificacao =  CategoriaPercentualGordura.MEDIA;
                else if( perc >= 26 && perc < 28 )
                    classificacao = CategoriaPercentualGordura.ABAIXO_MEDIA;
                else if( perc >= 28 && perc < 32 )
                    classificacao =  CategoriaPercentualGordura.RUIM;
                else if( perc >= 32 )
                    classificacao = CategoriaPercentualGordura.MUITO_RUIM;

            }
            else
                return null;

        }
		/*@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@*/
						/*CLASSIFICAÇÃO FEMININA*/
		/*@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@*/

        else if( sexo.equalsIgnoreCase("F") ){
            if(idade < 18 ){
                if (perc < 12) {
                    classificacao = CategoriaPercentualGordura.BOM;
                } else if (perc < 15) {
                    classificacao = CategoriaPercentualGordura.ACIMA_MEDIA;
                } else if (perc < 25) {
                    classificacao = CategoriaPercentualGordura.MEDIA;
                } else if (perc < 30) {
                    classificacao = CategoriaPercentualGordura.ABAIXO_MEDIA;
                } else if (perc < 36) {
                    classificacao = CategoriaPercentualGordura.RUIM;
                } else {
                    classificacao = CategoriaPercentualGordura.MUITO_RUIM;
                }
            } else if( idade >= 18 && idade <= 25){
                if( perc < 17 )
                    classificacao = CategoriaPercentualGordura.EXCELENTE;
                else if( perc >= 17 && perc < 20 )
                    classificacao = CategoriaPercentualGordura.BOM;
                else if( perc >= 20 && perc < 23 )
                    classificacao = CategoriaPercentualGordura.ACIMA_MEDIA;
                else if( perc >= 23 && perc < 26 )
                    classificacao =  CategoriaPercentualGordura.MEDIA;
                else if( perc >= 26 && perc < 29 )
                    classificacao = CategoriaPercentualGordura.ABAIXO_MEDIA;
                else if( perc >= 29 && perc < 32 )
                    classificacao =  CategoriaPercentualGordura.RUIM;
                else if( perc >= 33 )
                    classificacao = CategoriaPercentualGordura.MUITO_RUIM;
            }
            else if( idade >= 26 && idade <= 35){

                if( perc < 18 )
                    classificacao = CategoriaPercentualGordura.EXCELENTE;
                else if( perc >= 18 && perc < 21 )
                    classificacao = CategoriaPercentualGordura.BOM;
                else if( perc >= 21 && perc < 24 )
                    classificacao = CategoriaPercentualGordura.ACIMA_MEDIA;
                else if( perc >= 24 && perc < 27 )
                    classificacao =  CategoriaPercentualGordura.MEDIA;
                else if( perc >= 27 && perc < 31 )
                    classificacao = CategoriaPercentualGordura.ABAIXO_MEDIA;
                else if( perc >= 31 && perc < 36 )
                    classificacao =  CategoriaPercentualGordura.RUIM;
                else if( perc >= 36 )
                    classificacao = CategoriaPercentualGordura.MUITO_RUIM;

            }
            else if( idade >= 36 && idade <= 45){

                if( perc < 20 )
                    classificacao = CategoriaPercentualGordura.EXCELENTE;
                else if( perc >= 20 && perc < 24 )
                    classificacao = CategoriaPercentualGordura.BOM;
                else if( perc >= 24 && perc < 27 )
                    classificacao = CategoriaPercentualGordura.ACIMA_MEDIA;
                else if( perc >= 27 && perc < 30 )
                    classificacao =  CategoriaPercentualGordura.MEDIA;
                else if( perc >= 30 && perc < 33 )
                    classificacao = CategoriaPercentualGordura.ABAIXO_MEDIA;
                else if( perc >= 33 && perc < 38 )
                    classificacao =  CategoriaPercentualGordura.RUIM;
                else if( perc >= 38 )
                    classificacao = CategoriaPercentualGordura.MUITO_RUIM;

            }
            else if( idade >= 46 && idade <= 55){

                if( perc < 23 )
                    classificacao = CategoriaPercentualGordura.EXCELENTE;
                else if( perc >= 23 && perc < 26 )
                    classificacao = CategoriaPercentualGordura.BOM;
                else if( perc >= 26 && perc < 29 )
                    classificacao = CategoriaPercentualGordura.ACIMA_MEDIA;
                else if( perc >= 29 && perc < 32 )
                    classificacao =  CategoriaPercentualGordura.MEDIA;
                else if( perc >= 32 && perc < 35 )
                    classificacao = CategoriaPercentualGordura.ABAIXO_MEDIA;
                else if( perc >= 35 && perc < 39 )
                    classificacao =  CategoriaPercentualGordura.RUIM;
                else if( perc >= 39 )
                    classificacao = CategoriaPercentualGordura.MUITO_RUIM;

            }
            else if( idade >= 56 ){

                if( perc < 24 )
                    classificacao = CategoriaPercentualGordura.EXCELENTE;
                else if( perc >= 24 && perc < 27 )
                    classificacao = CategoriaPercentualGordura.BOM;
                else if( perc >= 27 && perc < 30 )
                    classificacao = CategoriaPercentualGordura.ACIMA_MEDIA;
                else if( perc >= 30 && perc < 33 )
                    classificacao =  CategoriaPercentualGordura.MEDIA;
                else if( perc >= 33 && perc < 36 )
                    classificacao = CategoriaPercentualGordura.ABAIXO_MEDIA;
                else if( perc >= 36 && perc < 39 )
                    classificacao =  CategoriaPercentualGordura.RUIM;
                else if( perc >= 39 )
                    classificacao = CategoriaPercentualGordura.MUITO_RUIM;

            }
            else
                classificacao = null;

        }

        return classificacao;
    }

    public static Double percGordura(Double dc){
        Double perc;

        perc = ((4.95 / dc ) - 4.5) * 100;

        return Uteis.forcarCasasDecimais(2, new BigDecimal(perc));
    }

    public static boolean isCalcularDados(AvaliacaoFisica a, boolean menorIdade) {
        if(menorIdade){
            return a.getCoxaMedial() > 0.0 && a.getTriceps() > 0.0;
        }else if (!a.getCliente().isSexoMasculino()) {
            return a.getTriceps() > 0.0 && a.getSupraIliaca() > 0.0 && a.getCoxaMedial() > 0.0;
        } else {
            return a.getAbdominal() > 0.0 && a.getCoxaMedial() > 0.0 && a.getPeitoral() > 0.0;
        }
    }
}
