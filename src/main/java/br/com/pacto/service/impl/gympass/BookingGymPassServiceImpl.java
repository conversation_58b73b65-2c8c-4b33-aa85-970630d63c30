/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.impl.gympass;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ExecuteRequestHttpService;
import br.com.pacto.bean.gympass.*;
import br.com.pacto.dao.intf.gympass.BookingGymPassDao;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.notificacao.excecao.AtividadeExcecoes;
import br.com.pacto.service.impl.cliente.ClienteSinteticoServiceImpl;
import br.com.pacto.service.intf.gympass.BookingGymPassService;
import br.com.pacto.service.intf.gympass.LogGymPassService;
import br.com.pacto.util.UteisValidacao;
import org.json.JSONArray;
import org.json.JSONObject;
import org.apache.http.NameValuePair;
import org.apache.http.client.ResponseHandler;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.BasicResponseHandler;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import java.util.HashMap;
import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 * User: Luiz Felipe
 * Date: 06/04/2020
 */
@Service
public class BookingGymPassServiceImpl implements BookingGymPassService {

    @Autowired
    private BookingGymPassDao dao;
    @Autowired
    private SessaoService sessaoService;
    @Autowired
    private LogGymPassService logGymPassService;

    public BookingGymPass obterPorId(final String ctx, Integer id) throws ServiceException {
        try {
            return dao.findById(ctx, id);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public BookingGymPass obterPorBookingId(final String ctx, String bid) throws ServiceException{
        try {
            String s = "select obj from BookingGymPass obj where bookingNumber = :bookingNumber and obj.operacao = 'booking-requested'";
            Map<String, Object> p = new HashMap();
            p.put("bookingNumber", bid);
            return dao.findObjectByParam(ctx, s, p);
        } catch (Exception ex) {
            Uteis.logar(ex, ClienteSinteticoServiceImpl.class);
            throw new ServiceException(ex.getMessage());
        }
    }

    public BookingGymPass incluir(final String ctx, BookingGymPass object) throws ServiceException {
        try {
            return dao.insert(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public BookingGymPass alterar(final String ctx, BookingGymPass object) throws ServiceException {
        try {
            return dao.update(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }


    @Override
    public List<LogGymPassTO> listarLog(PaginadorDTO paginadorDTO) throws Exception {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        int maxResults = 25;
        int indiceInicial = 0;
        if (paginadorDTO != null) {
            maxResults = paginadorDTO.getSize() == null ? 25 : paginadorDTO.getSize().intValue();
            indiceInicial = paginadorDTO.getPage() == null || paginadorDTO.getSize() == null ? indiceInicial : paginadorDTO.getPage().intValue() * paginadorDTO.getSize().intValue();

        }
        StringBuilder hql = new StringBuilder();
        StringBuilder where = new StringBuilder();
        Map<String, Object> param = new HashMap<String, Object>();

        hql.append("select obj from BookingGymPass obj ");
        if(UteisValidacao.emptyString(paginadorDTO.getSQLOrderByUse())){
            hql.append(" ORDER BY codigo DESC");
        }else{
            hql.append(paginadorDTO.getSQLOrderByUse());
        }
        if (paginadorDTO != null) {
            paginadorDTO.setQuantidadeTotalElementos(dao.countWithParam(ctx, "codigo", where, param).longValue());
        }
        final List<BookingGymPass> lista = dao.findByParam(ctx, hql.toString(), param, maxResults, indiceInicial);
        paginadorDTO.setSize((long) maxResults);
        paginadorDTO.setPage((long) indiceInicial);
        return new ArrayList(){{
            for(BookingGymPass bk : lista){
                String aluno = null;
                String matricula = null;
                if(bk.getClienteSintetico() != null){
                    try (ResultSet resultSet = dao.createStatement(ctx, "select nome, matricula from clientesintetico where codigo = " + bk.getClienteSintetico())) {
                        if (resultSet.next()) {
                            aluno = resultSet.getString("nome");
                            matricula = resultSet.getString("matricula");
                        }
                    }
                }
                LogGymPassTO log = new LogGymPassTO(bk, aluno, matricula);
                if(bk.getOperacao().equals("checkin-booking-occurred") || bk.getOperacao().equals("booking-canceled")){
                    BookingGymPass bkRequested = obterPorBookingId(ctx, bk.getBookingNumber());
                    log.setEmail(new LogGymPassTO(bkRequested, "", "").getEmail());
                }
                add(log);
            }
        }};
    }

    @Override
    public DetalheLogGympassTO listarLogDetalhe(Integer codigo) throws Exception{
        String ctx = sessaoService.getUsuarioAtual().getChave();
        DetalheLogGympassTO detalhes = new DetalheLogGympassTO();
        BookingGymPass bk = dao.findById(ctx, codigo);
        String aluno = null;
        String matricula = null;
        if(bk.getClienteSintetico() != null){
            try (ResultSet resultSet = dao.createStatement(ctx, "select nome, matricula from clientesintetico where codigo = " + bk.getClienteSintetico())) {
                if (resultSet.next()) {
                    aluno = resultSet.getString("nome");
                    matricula = resultSet.getString("matricula");
                }
            }
        }
        LogGymPassTO log = new LogGymPassTO(bk, aluno, matricula);
        if(bk.getOperacao().equals("checkin-booking-occurred") || bk.getOperacao().equals("booking-canceled")){
            BookingGymPass bkRequested = obterPorBookingId(ctx, bk.getBookingNumber());
            LogGymPassTO passTO = new LogGymPassTO(bkRequested, "", "");
            log.setEmail(passTO.getEmail());
            log.setClassId(passTO.getClassId());
        }
        if(!UteisValidacao.emptyString(log.getClassId())){
            detalhes.setDados(new DadosAulaDetalheLogBookingTO(dadosAula(ctx, log.getClassId(), matricula)));
        }
        detalhes.setLogGymPass(log);
        detalhes.setLogs(logGymPassService.listarLog(bk.getBookingNumber()));

        Map<String, Object> p = new HashMap();
        p.put("bookingNumber", bk.getBookingNumber());
        detalhes.setEventos(dao.findByParam(ctx, "select obj from BookingGymPass obj where bookingNumber = :bookingNumber order by codigo desc", p));

        return detalhes;
    }

    private JSONObject dadosAula(String ctx, String classid, String matricula) throws Exception{
        final String url = Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebInteg);
        CloseableHttpClient client = ExecuteRequestHttpService.createConnector();
        HttpPost httpPost = new HttpPost(url + "/prest/aulacheia/dados-booking");

        List<NameValuePair> params = new ArrayList<>();
        params.add(new BasicNameValuePair("chave", ctx));
        params.add(new BasicNameValuePair("matricula", matricula));
        params.add(new BasicNameValuePair("classid", classid));
        httpPost.setEntity(new UrlEncodedFormEntity(params));
        CloseableHttpResponse response = client.execute(httpPost);
        ResponseHandler<String> handler = new BasicResponseHandler();
        String body = handler.handleResponse(response);
        client.close();
        return new JSONObject(body);
    }

}
