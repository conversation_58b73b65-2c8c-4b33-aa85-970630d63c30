package br.com.pacto.service.impl.scoretreino;

import br.com.pacto.bean.wod.ScoreTreino;
import br.com.pacto.dao.intf.wod.ScoreTreinoDao;
import br.com.pacto.dao.intf.wod.WodDao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.scoretreino.ScoreTreinoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * Created by rafaelc on 09/06/2017.
 */
@Service
public class ScoreTreinoServiceImpl implements ScoreTreinoService {

    @Autowired
    private ScoreTreinoDao scoreTreinoDao;



    @Override
    public ScoreTreino obterPorId(String ctx, Integer id) throws ServiceException {
        try {
            return getScoreTreinoDao().findById(ctx, id);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public List<ScoreTreino> obterPorParam(String ctx, String query, Map<String, Object> params) throws ServiceException {
        try {
            return getScoreTreinoDao().findByParam(ctx, query, params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public ScoreTreinoDao getScoreTreinoDao() {
        return scoreTreinoDao;
    }
}
