package br.com.pacto.service.impl.agenda;

import br.com.pacto.bean.disponibilidade.HorarioDisponibilidade;
import br.com.pacto.dao.intf.disponibilidade.HorarioDisponibilidadeDao;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.agenda.HorarioDisponibilidadeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class HorarioDisponibilidadeServiceImpl implements HorarioDisponibilidadeService {

    @Autowired
    HorarioDisponibilidadeDao horarioDisponibilidadeDao;
    @Autowired
    private SessaoService sessaoService;

    @Override
    public HorarioDisponibilidade obterPorId(Integer id) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            return horarioDisponibilidadeDao.findById(ctx, id);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public HorarioDisponibilidade obterPorId(String ctx, Integer id) throws ServiceException {
        try {
            return horarioDisponibilidadeDao.findById(ctx, id);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public List<HorarioDisponibilidade> consultarHorarioDisponibilidadesNoMesmoNSU(String ctx, Integer nsu) throws ServiceException {
        try {
            String hql = "SELECT DISTINCT(horarioDisponibilidade) FROM Agendamento WHERE nsu = :nsu";
            Map<String, Object> params = new HashMap<String, Object>();
            params.put("nsu", nsu);
            return horarioDisponibilidadeDao.findByParam(ctx, hql, params);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }
}
