package br.com.pacto.service.impl.crossfit;

import java.util.Date;

/**
 * <AUTHOR> 04/04/2019
 */
public class DetalhesIndicadorBICrossfitDTO {

    private Integer matricula;
    private String nome;
    private String situacaoAluno;
    private Date dia;

    public DetalhesIndicadorBICrossfitDTO() {
    }

    public DetalhesIndicadorBICrossfitDTO(Integer matricula, String nome, String situacaoAluno, Date dia) {
        this.matricula = matricula;
        this.nome = nome;
        this.situacaoAluno = situacaoAluno;
        this.dia = dia;
    }

    public Integer getMatricula() {
        return matricula;
    }

    public void setMatricula(Integer matricula) {
        this.matricula = matricula;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getSituacaoAluno() {
        return situacaoAluno;
    }

    public void setSituacaoAluno(String situacaoAluno) {
        this.situacaoAluno = situacaoAluno;
    }

    public Date getdia() {
        return dia;
    }

    public void setdia(Date dia) {
        this.dia = dia;
    }
}
