package br.com.pacto.service.impl.notificacao.excecao;

import br.com.pacto.service.exception.ExcecaoSistema;

/**
 * Created by ulisses on 22/08/2018.
 */
public enum NivelWodExcecoes implements ExcecaoSistema {

    ERRO_CADASTRAR_NIVEL_NOME_EXISTE("erro_cadastrar_nivel_nome_existe", "Registro duplicado"),
    ERRO_BUSCAR_NIVEIS_WOD("erro_buscar_niveis_wod", "Ocorreu um erro ao buscar os Níveis de Wod"),
    ERRO_CADASTRO_NIVEIS_WOD("erro_cadastro_niveis_wod", "Ocorreu um erro ao cadastrar o Níveis de Wod"),
    ERRO_NIVEIS_WOD_NAO_ENCONTRADO("erro_niveis_wod_nao_encontrado", "Níveis de Wod não encontrado"),
    ERRO_EXCLUIR_NIVEL_WOD("erro_excluir_nivel_wod", "Ocorreu um erro ao excluir o Nível Wod informado"),
    ERRO_EXCLUIR_NIVEL_WOD_CATEGORIA("erro_excluir_nivel_wod_categoria", "Categoria do Nível não permite excluir");

    private String chave;
    private String descricao;

    NivelWodExcecoes(String chave, String descricao) {
        this.chave = chave;
        this.descricao = descricao;
    }

    @Override
    public String getChaveExcecao() {
        return chave;
    }
    @Override
    public String getDescricaoExcecao() {
        return descricao;
    }

}
