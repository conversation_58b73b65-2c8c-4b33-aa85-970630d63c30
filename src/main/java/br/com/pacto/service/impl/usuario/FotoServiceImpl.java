package br.com.pacto.service.impl.usuario;

import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.pessoa.Pessoa;
import br.com.pacto.bean.professor.Professor<PERSON><PERSON><PERSON><PERSON>;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.base.SuperControle;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.cliente.ClienteSinteticoService;
import br.com.pacto.service.intf.pessoa.PessoaService;
import br.com.pacto.service.intf.professor.ProfessorSinteticoService;
import br.com.pacto.service.intf.usuario.FotoService;
import br.com.pacto.service.intf.usuario.UsuarioService;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.context.ServletContextAware;
import servicos.integracao.zw.IntegracaoCadastrosWSConsumer;

import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.UnsupportedEncodingException;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Implementação do contrato {@link FotoService}
 *
 * <AUTHOR> Karlus
 * @since 24/08/2018
 */
@Service
public class FotoServiceImpl implements FotoService, ServletContextAware  {

    private ServletContext context;
    @Autowired
    private ClienteSinteticoService clienteSinteticoService;
    @Autowired
    private ProfessorSinteticoService professorSinteticoService;
    @Autowired
    private PessoaService pessoaService;
    @Autowired
    private SessaoService sessaoService;
    @Autowired
    private UsuarioService usuarioService;


    public static final String PATH_RESOURCE_IMG = "javax.faces.resource";
    public static final String SUFIXO_RESOURCE_JSF = ".xhtml?ln=img";

    @Override
    public String carregarFoto(String contexto, Usuario usuario, boolean forcarDownload, HttpServletRequest request) throws Exception {
        if (usuario == null) {
            return "";
        }

        return carregarFoto(contexto, usuario.getFotoKeyApp(), usuario.getIdPessoa(), forcarDownload, request);
    }

    @Override
    public String carregarFoto(String contexto, String fotoKeyApp, Integer codigoPessoa, boolean forcarDownload, HttpServletRequest request) throws Exception {
        if (StringUtils.isNotBlank(fotoKeyApp)) {
            return fotoKeyApp;
        }

        if (forcarDownload) {
            return Aplicacao.preencherFoto(contexto, codigoPessoa, true, false, false);
        } else if (request != null) {
            return SuperControle.defineUrlFotoJSON(contexto, codigoPessoa, request, context);
        }

        return "";
    }

    @Override
    public void migracaoFotoPessoa(ServletContext context, String key, Integer codigoPessoa, String fotokey) {
        try {
            List<Pessoa> pessoas = returnPessoas(key, codigoPessoa);
            if (!UteisValidacao.emptyList(pessoas)) {
                for (Pessoa pessoa : pessoas) {
                    pessoa.setFotoKey(fotokey+ "?time=" + Calendario.hoje().getTime());
                    pessoaService.alterarPessoa(key, pessoa);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private List<Pessoa> returnPessoas(String key, Integer codigoPessoa) throws ServiceException {
        try {
            List<Pessoa> pessoas = new ArrayList<>();
            ClienteSintetico cliente = clienteSinteticoService.obterPorCodigoPessoaZW(key, codigoPessoa);
            if (cliente != null && !UteisValidacao.emptyNumber(cliente.getCodigo())) {
                pessoas.add(cliente.getPessoa());
            } else {
                List<ProfessorSintetico> professores = professorSinteticoService.obterProfessoresPorCodigoPessoaZw(key, codigoPessoa);
                if (!UteisValidacao.emptyList(professores)) {
                    for (ProfessorSintetico professor : professores) {
                        if (professor.getPessoa() != null && !UteisValidacao.emptyNumber(professor.getPessoa().getCodigo())) {
                            pessoas.add(professor.getPessoa());
                        }
                    }
                }
            }
            return pessoas;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    private void atualizarFotoPessoa(ServletContext context, String ctx, String fotoKey, Boolean updateImage, Integer codigoPessoa, Boolean fotoPersonal) throws ServiceException {
        List<Pessoa> pessoas = returnPessoas(ctx, codigoPessoa);
        for (Pessoa pessoa : pessoas) {
            pessoa.setFotoKey(fotoKey);
            pessoaService.alterarPessoa(ctx, pessoa);
        }
    }

    @Override
    public String defineURLFotoPessoa(HttpServletRequest request, String fotoKey, Integer codigoPessoa, Boolean fotoPersonal, String key, Boolean verificarFotoKeyZW) {
        if(!UteisValidacao.emptyString(fotoKey)){
            return Aplicacao.obterUrlFotoDaNuvem(fotoKey);
        }
        URL urlRequisitada;
        String identificadorArquivo = "fotoPadrao.jpg";
        String finalUrl = "";
        try {
            if (request == null) {
                return "";
            }
            urlRequisitada = new URL(request.getRequestURL().toString());
            Uteis.logar(null, "URL REQUISITADA: " + urlRequisitada);
            String caminho = context.getRealPath("resources") + File.separator + "img";
            if (!StringUtils.isBlank(fotoKey) && !fotoKey.equals("fotoPadrao.jpg")) {
                String caminhoRelativo = caminho;

                caminhoRelativo += "/" + fotoKey + (fotoKey.contains(".jpg") ? "" : ".jpg");

                File f = new File(caminhoRelativo);
                if (f.exists()) {
                    Map<String, String> mapearUrl = mapearUrl(fotoKey);
                    identificadorArquivo = mapearUrl.get("identificadorArquivo");
                    finalUrl = mapearUrl.get("finalUrl");
                    Uteis.logar(null, "FINAL URL 1: " + finalUrl);
                } else if (!UteisValidacao.emptyNumber(codigoPessoa) && verificarFotoKeyZW) {
                    try {
                        IntegracaoCadastrosWSConsumer integracaoWS = UtilContext.getBean(IntegracaoCadastrosWSConsumer.class);
                        URL url = new URL(Aplicacao.getProp(key, Aplicacao.urlZillyonWebInteg));
                        String urlZillyon = Aplicacao.getProp(key, Aplicacao.urlZillyonWebInteg);
                        if (url.getPort() != -1) {
                            if (String.valueOf(url.getPort()).startsWith("90") && !urlZillyon.startsWith("https")) {
                                if (!servidorLocal(urlZillyon)) {
                                    urlZillyon = urlZillyon.replace("http", "https");
                                }
                            }
                        }
                        //nao precisa consultar fotokey no ZW se ela já está preenchida no Treino, pois sao identicas
                        if (fotoKey != null && !fotoKey.contains(key)) {
                            fotoKey = integracaoWS.consultarFotoKeyPessoa(urlZillyon, key, codigoPessoa);
                        }
                        atualizarFotoPessoa(context, key, fotoKey, true, codigoPessoa, fotoPersonal);
                        Map<String, String> mapearUrl = mapearUrl(fotoKey);
                        identificadorArquivo = mapearUrl.get("identificadorArquivo");
                        finalUrl = mapearUrl.get("finalUrl");
                        Uteis.logar(null, "FINAL URL 2: " + finalUrl);
                    } catch (Exception ex) {
                        Uteis.logar(null, "Não foi possível buscar a fotoKey no ZW -> " + key + " codigoPessoa " + codigoPessoa + " motivo: " + ex.getMessage());
                    }
                } else {
                    identificadorArquivo = "fotoPadrao.jpg";
                }
            } else if (!UteisValidacao.emptyNumber(codigoPessoa) && verificarFotoKeyZW && !SuperControle.independente(key)) {
                IntegracaoCadastrosWSConsumer integracaoWS = UtilContext.getBean(IntegracaoCadastrosWSConsumer.class);
                URL url = new URL(Aplicacao.getProp(key, Aplicacao.urlZillyonWebInteg));
                String urlZillyon = Aplicacao.getProp(key, Aplicacao.urlZillyonWebInteg);
                if (url.getPort() != -1) {
                    if (String.valueOf(url.getPort()).startsWith("90") && !urlZillyon.startsWith("https")) {
                        if (!servidorLocal(urlZillyon)) {
                            urlZillyon = urlZillyon.replace("http", "https");
                        }
                    }
                }
                //nao precisa consultar fotokey no ZW se ela já está preenchida no Treino, pois sao identicas
                if (fotoKey != null && !fotoKey.contains(key)) {
                    fotoKey = integracaoWS.consultarFotoKeyPessoa(urlZillyon, key, codigoPessoa);
                }

                if (!servidorLocal(urlZillyon)) {
                    atualizarFotoPessoa(context, key, fotoKey, true, codigoPessoa, fotoPersonal);
                    Map<String, String> mapearUrl = mapearUrl(fotoKey);
                    identificadorArquivo = mapearUrl.get("identificadorArquivo");
                    finalUrl = mapearUrl.get("finalUrl");
                    Uteis.logar(null, "FINAL URL 3: " + finalUrl);
                }
            }

            String urlZillyon = Aplicacao.getProp(key, Aplicacao.urlZillyonWebInteg);
            if (!servidorLocal(urlZillyon)) {
                final String uRetorno = urlRetorno(urlRequisitada, request, identificadorArquivo, finalUrl);
                Uteis.logar(null, "URL RETORNO 1: " + uRetorno);
                return uRetorno;
            }
            int pos = request.getRequestURL().indexOf("TreinoWeb") + 9;
            String url = request.getRequestURL().substring(0, pos);
            final String uRetorno = url.replace("TreinoWeb", "app") + "/zw-photos/"
                    + (fotoKey != null ? fotoKey : "fotoPadrao.jpg");
            Uteis.logar(null, "URL RETORNO 2: " + uRetorno);
            return uRetorno;
        } catch (Exception ex) {
            Logger.getLogger(SuperControle.class.getName()).log(Level.SEVERE, null, ex);
            try {
                urlRequisitada = new URL(request.getRequestURL().toString());
                final String uRetorno = urlRetorno(urlRequisitada, request, identificadorArquivo, finalUrl);
                Uteis.logar(null, "URL RETORN 3: " + uRetorno);
                return uRetorno;
            } catch (MalformedURLException e) {
                Logger.getLogger(SuperControle.class.getName()).log(Level.SEVERE, null, e);
                return "";
            }
        }
    }

    private boolean servidorLocal(final String url) {
        if (url != null) {
            return (url.contains("10.1.1") || url.contains("192.168") || url.contains("dyndns.org") || url.contains("localhost"));
        }
        return false;
    }

    private String urlRetorno(URL urlRequisitada, HttpServletRequest request, String identificadorArquivo, String finalUrl) {
        if (urlRequisitada.getPort() == -1) {
            return String.format("%s://%s%s/%s/%s%s%s", new Object[]{
                    urlRequisitada.getProtocol(),
                    urlRequisitada.getHost(),
                    request.getContextPath(),
                    PATH_RESOURCE_IMG,
                    identificadorArquivo,
                    SUFIXO_RESOURCE_JSF,
                    finalUrl
            });
        } else {
            String validHttps = String.valueOf(urlRequisitada.getPort());
            final String url = urlRequisitada.toString();
            return String.format("%s://%s:%s%s/%s/%s%s%s", new Object[]{
                    validHttps.startsWith("90") && !servidorLocal(url) && !urlRequisitada.getProtocol().equals("https") ? urlRequisitada.getProtocol() + "s" : urlRequisitada.getProtocol(),
                    urlRequisitada.getHost(),
                    urlRequisitada.getPort(),
                    request.getContextPath(),
                    PATH_RESOURCE_IMG,
                    identificadorArquivo,
                    SUFIXO_RESOURCE_JSF,
                    finalUrl
            });
        }
    }

    private Map<String, String> mapearUrl(String fotoKey) throws UnsupportedEncodingException {
        Map<String, String> ret = new HashMap<>();
        if (!StringUtils.isBlank(fotoKey)) {
            String[] quebrandoUrl = fotoKey.split("/");
            ret.put("identificadorArquivo", URLEncoder.encode(quebrandoUrl[quebrandoUrl.length - 1], StandardCharsets.UTF_8.toString()) + (fotoKey.contains(".jpg") ? "" : ".jpg"));
            String finalUrl = "";
            for (int i = 0; i < quebrandoUrl.length - 1; i++) {
                finalUrl += "/" + quebrandoUrl[i];
            }
            if (!StringUtils.isBlank(finalUrl)) {
                finalUrl = URLEncoder.encode(finalUrl, StandardCharsets.UTF_8.toString());
            }
            ret.put("finalUrl", finalUrl);
        } else {
            ret.put("identificadorArquivo", "fotoPadrao.jpg");
            ret.put("finalUrl", "");
        }

        return ret;
    }

    @Override
    public void setServletContext(ServletContext servletContext) {
        this.context = servletContext;
    }

    @Override
    public String carregarFotoClienteNuvem(String contexto, Usuario usuario) throws Exception {
        String urlFoto = "";
        if (usuario != null) {
            if (usuario.getCliente() != null) {
                if(usuario.getCliente().getPessoa() != null) {
                    if (StringUtils.isNotBlank(usuario.getCliente().getPessoa().getFotoKey())) {
                        urlFoto = usuario.getCliente().getPessoa().getFotoKey();
                    }
                }
                if (!StringUtils.isNotBlank(urlFoto)) {
                    urlFoto = usuarioService.carregaFotoPessoaZW(usuario.getCliente().getMatricula(), contexto);
                }
            }
            if (StringUtils.isNotBlank(usuario.getFotoKeyApp())) {
                urlFoto = usuario.getFotoKeyApp();
            }
        }

        if (StringUtils.isNotBlank(urlFoto) && !urlFoto.contains(Aplicacao.getProp(Aplicacao.urlFotosNuvem))) {
            urlFoto = Aplicacao.obterUrlFotoDaNuvem(urlFoto);
        }

        return urlFoto;
    }

}
