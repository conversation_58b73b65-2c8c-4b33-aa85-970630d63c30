package br.com.pacto.service.impl.notificacao.excecao;

import br.com.pacto.service.exception.ExcecaoSistema;

/**
 * Created by ulisses on 27/08/2018.
 */
public enum ColaboradoresExcecoes implements ExcecaoSistema {

    COLABORADOR_NAO_ENCONTRADO("colaborador_nao_encontrado", "Colaborador informado não encontrado"),
    PERFIL_USUARIO_NAO_ENCONTRADO("perfil_usuario_nao_encontrado", "Perfil Usuário informado não encontrado"),
    ERRO_BUSCAR_COLABORADOR("erro_buscar_colaborador", "Ocorreu um erro ao buscar o Colaborador informado"),
    ERRO_INCLUIR_COLABORADOR("erro_incluir_colaborador", "Ocorreu um erro ao incluir o Colaborador informado"),
    ERRO_ALTERAR_COLABORADOR("erro_alterar_colaborador", "Ocorreu um erro ao alterar o Colaborador informado"),
    ERRO_INSERIR_COLABORADOR("erro_alterar_colaborador", "Ocorreu um erro ao inserir o Colaborador informado"),
    ERRO_EXCLUIR_COLABORADOR("erro_excluir_colaborador", "Ocorreu um erro ao excluir o Colaborador informado"),
    ERRO_BUSCAR_COLABORADORES("erro_buscar_colaboradores", "Ocorreu um erro ao buscar os Colaboradores"),

    COLABORADOR_NOME_NAO_INFORMADO("colaborador_nome_nao_informado", "O nome do colaborador não foi informado!"),
    COLABORADOR_DATA_NASCIMENTO_NAO_ENCONTRADO("colaborador_data_nascimento_nao_encontrado", "A data de nascimento do colaborador não encontrado"),
    COLABORADOR_FONE_NAO_ENCONTRADO("colaborador_fone_nao_encontrado", "O fone do colaborador não encontrado"),
    COLABORADOR_USER_NAME_NAO_ENCONTRADO("colaborador_user_name_nao_encontrado", "O nome de usuário do colaborador não foi encontrado"),
    COLABORADOR_PASSWORD_NAO_ENCONTRADO("colaborador_password_nao_encontrado", "A senha do colaborador não foi encontrada"),
    COLABORADOR_EMPRESA_NAO_ENCONTRADO("colaborador_empresa_nao_encontrado", "A empresa do colaborador não foi encontrada"),
    COLABORADOR_EMPRESA_NAO_EXISTE("colaborador_empresa_nao_existe", "A empresa não existe"),
    COLABORADOR_PERFIL_NAO_ENCONTRADO("colaborador_perfil_nao_encontrado", "O perfil do colaborador não foi encontrado"),
    COLABORADOR_PERFIL_NAO_EXISTE("colaborador_perfil_nao_existe", "O perfil não existe"),
    COLABORADOR_PERFIL_JA_EXISTE("colaborador_perfil_ja_existe", "O nome de usuário do colaborador já existe"),
    COLABORADOR_JA_EXISTE("colaborador_ja_existe", "registro_duplicado");

    private String chave;
    private String descricao;

    ColaboradoresExcecoes(String chave, String descricao) {
        this.chave = chave;
        this.descricao = descricao;
    }

    @Override
    public String getChaveExcecao() {
        return chave;
    }
    @Override
    public String getDescricaoExcecao() {
        return descricao;
    }

}
