package br.com.pacto.service.impl.eventoUsuario;

import br.com.pacto.bean.eventoUsuario.EventoUsuario;
import br.com.pacto.dao.intf.eventoUsuario.EventoUsuarioDao;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.notificacao.excecao.EventoUsuarioExcecoes;
import br.com.pacto.service.intf.eventoUsuario.EventoUsuarioService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR> 19/02/2019
 */
@Service
public class EventoUsuarioServiceImpl implements EventoUsuarioService {

    @Autowired
    private EventoUsuarioDao eventoUsuarioDao;
    @Autowired
    private SessaoService sessaoService;

    @Override
    public EventoUsuario atualizarEventoUsuario(EventoUsuario eventoUsuario) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            if (!validarCampos(eventoUsuario)) {
                throw new ServiceException(EventoUsuarioExcecoes.ERRO_CAMPOS_OBRIGATORIOS_NAO_PREENCHIDO);
            }
            return eventoUsuarioDao.update(ctx, eventoUsuario);
        } catch (Exception e) {
            throw new ServiceException(EventoUsuarioExcecoes.ERRO_ATUALIZAR_EVENTO_USUARIO, e);
        }
    }

    @Override
    public List<EventoUsuario> obterTodosEventoUsuario() throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();

            return eventoUsuarioDao.findAll(ctx);
        } catch (Exception e) {
            throw new ServiceException(EventoUsuarioExcecoes.ERRO_BUSCAR_EVENTO_USUARIO, e);
        }
    }

    private boolean validarCampos(EventoUsuario eventoUsuario) {
        boolean valido = true;
        if (StringUtils.isBlank(eventoUsuario.getCodigo())) {
            valido = false;
        }
        if (StringUtils.isBlank(eventoUsuario.getEvento()) || eventoUsuario.getEvento().length() < 3) {
            valido = false;
        }

        return valido;
    }
}
