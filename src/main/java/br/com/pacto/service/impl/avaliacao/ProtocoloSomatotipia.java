package br.com.pacto.service.impl.avaliacao;

import br.com.pacto.bean.avaliacao.AvaliacaoFisica;
import br.com.pacto.bean.avaliacao.PesoOsseo;
import org.apache.log4j.Logger;

public class ProtocoloSomatotipia {


    public static void calcular(final AvaliacaoFisica avaliacao, final PesoOsseo pesoOsseo){
        avaliacao.setEndomorfia(endomorfia(avaliacao.getTriceps(),
                avaliacao.getSubescapular(), avaliacao.getSupraEspinhal(),
                avaliacao.getAltura()));
        avaliacao.setEctomorfia(ectomorfia(indicePonderal(avaliacao.getAltura(), avaliacao.getPeso())));
        avaliacao.setMesomorfia(mesomorfia(pesoOsseo.getDiametroCotovelo(),
                pesoOsseo.getDiametroFemur(),
                avaliacao.getBracoContraidoDir(),
                avaliacao.getTriceps(),
                avaliacao.getPanturrilhaDir(),
                avaliacao.getPanturrilha(),
                avaliacao.getAltura()));
    }
    /**
     * Método para calcular a endomorfia
     *
     * @param tricipital    - Dado equivalente ao tricipital
     * @param subescapular  - Dado equivalente ao subescapular
     * @param supraespinhal - Dado equivalente a supraespinhal
     * @param estatura      - Dado equivalente a estatura
     * @return endomorfia
     */
    public static Double endomorfia(double tricipital, double subescapular, double supraespinhal, double estatura) {

        if ((tricipital > 0) && (subescapular > 0) && (supraespinhal > 0) && (estatura > 0)) {
            // Endomorfia = [(tricipital + subescapular + supraespinhal) x 170.18] / (estatura x 100)
            double calculoEndomorfia = ((tricipital + subescapular + supraespinhal) * 170.18) / (estatura * 100);
            /*
             * Endomorfia = (calculoEndomorfia x 0.1451) - ((calculoEndomorfia  elevado a 2) x 0.00068) +
             * ((calculoEndomorfia elevado a 3) x 0.0000014) - 0.7182
             */
            double valorEndomorfia = (calculoEndomorfia * 0.1451) - ((Math.pow(calculoEndomorfia, 2)) * 0.00068)
                    + ((Math.pow(calculoEndomorfia, 3)) * 0.0000014) - 0.7182;

            return valorEndomorfia;
        }
        return null;
    }

    /**
     * Método para calcular a mesomorfia
     *
     * @param biepicondiliano    - Dado equivalente ao biepicondiliano
     * @param bicondiliano       - Dado equivalente ao bicondiliano
     * @param bracoDireito       - Dado equivalente ao braço direito
     * @param tricipital         - Dado equivalente ao tricipital
     * @param panturrilhaDireita - Dado equivalente a panturrilha direita
     * @param panturrilha        - Dado equivalente a panturrilha
     * @param estatura           - Dado equivalente a estatura
     * @return mesomorfia
     */
    public static Double mesomorfia(double biepicondiliano, double bicondiliano, double bracoDireito, double tricipital,
                             double panturrilhaDireita, double panturrilha, double estatura) {

        if ((biepicondiliano > 0) && (bicondiliano > 0) && (bracoDireito > 0) && (tricipital > 0) && (panturrilhaDireita > 0)
                && (panturrilha > 0) && (estatura > 0)) {
            /*
             * parte1 Mesomorfia = (0,858 x biepicondiliano) + (0,601 x bicondiliano) + [0,188 x {bracoDireito - (tricipital / 10)}];
             */
            double part1Mesomorfia = (0.858 * biepicondiliano) + (0.601 * bicondiliano)
                    + (0.188 * (bracoDireito - (tricipital / 10)));

            //parte2 Mesomorfia = [0,161 x {panturrilhaDireita -(panturrilha / 10)}] - (0,131 x estatura x 100) + 4,5;
            double part2Mesomorfia = (0.161 * (panturrilhaDireita - (panturrilha / 10))) - (0.131 * estatura * 100) + 4.5;

            // Mesomorfia = part1Mesomorfia + part2Mesomorfia
            double valorMesomorfia = part1Mesomorfia + part2Mesomorfia;
            return valorMesomorfia;
        }
        return null;
    }

    public static Double indicePonderal(double estatura, double peso) {
        // Se e peso e estatura forem maiores que 0
        if ((peso > 0) && (estatura > 0)) {
            // Índice Ponderal = (estatura x 100) / (peso elevado a (1/3))
            double valorIndicePonderal = (estatura * 100) / (Math.pow(peso, 0.333333));
            return valorIndicePonderal;
        }
        return null;
    }
    /**
     * Método para calcular o índice Ponderal
     *
     */
    public static Double ectomorfia(Double valorIndicePonderal) {
        if (valorIndicePonderal != null) {
            double valorEctomorfia;
            // Se índice ponderal for menor ou igual a 40.75
            if (valorIndicePonderal <= 40.75) {
                valorEctomorfia = (0.463 * valorIndicePonderal) - 17.63;
            } else {
                valorEctomorfia = (0.732 * valorIndicePonderal) - 28.58;
            }

            return valorEctomorfia;
        }
        return null;
    }

}
