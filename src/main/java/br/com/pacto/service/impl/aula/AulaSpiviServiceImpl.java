package br.com.pacto.service.impl.aula;

import br.com.pacto.base.util.ExecuteRequestHttpService;
import br.com.pacto.bean.aula.SpiviSeat;
import br.com.pacto.controller.json.spivi.EventJSON;
import br.com.pacto.dao.intf.aula.SpiviSeatDao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.service.intf.aula.AulaSpiviService;
import br.com.pacto.util.UtilContext;
import br.com.pacto.util.bean.AgendaTotalTO;
import br.com.pacto.util.bean.AgendadoTO;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.apache.http.NameValuePair;
import org.apache.http.client.ResponseHandler;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.BasicResponseHandler;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.test.context.ContextConfiguration;
import servicos.integracao.adm.client.EmpresaWS;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@ContextConfiguration(locations = {"/applicationContext.xml"})
public class AulaSpiviServiceImpl implements AulaSpiviService {
    @Autowired
    SpiviSeatDao seatDao;

    public SpiviSeat gravarBike(final String ctx, SpiviSeat bike) throws Exception{
        return seatDao.insert(ctx, bike);
    }

    public Map<Integer, SpiviSeat> bikesAula(final String ctx, final String idzw, final Date dia, final Integer matricula ) throws Exception{
        Map<String, Object> params = new HashMap<>();
        params.put("idzw", idzw);
        params.put("dia", Calendario.getDataComHoraZerada(dia));
        if(matricula != null){
            params.put("matricula", matricula.toString());
        }

        List<SpiviSeat> bikes = seatDao.findByParam(ctx,
                "select obj from SpiviSeat obj where idzw = :idzw and cast(dia as date) = :dia " + (matricula == null ? "" : " and matricula = :matricula"),
                params);
        Map<Integer, SpiviSeat> bikesAula = new HashMap<>();
        for(SpiviSeat b : bikes){
            bikesAula.put(Integer.valueOf(b.getMatricula()), b);
        }
        return bikesAula;
    }

    public void trocarBikeEventoSpivi(final String ctx,
                                      AgendaTotalTO agendamento,
                                      AgendadoTO agendadoSelecionado,
                                      EmpresaWS empresaWS,
                                      Integer seatId,
                                      Integer eventId,
                                      Integer scheduleID) throws Exception {

        removerClienteEventoSpivi(ctx, empresaWS, Integer.valueOf(agendadoSelecionado.getMatricula()),
                eventId, agendadoSelecionado.getSpiviClientID(), agendamento.getId(), agendamento.getStartDate());

        addClienteEventoSpivi(ctx,
                agendamento,
                agendadoSelecionado,
                empresaWS,
                seatId,
                eventId,
                scheduleID);
    }

    public void addClienteEventoSpivi(final String ctx,
                                       AgendaTotalTO agendamento,
                                       AgendadoTO agendadoSelecionado,
                                       EmpresaWS empresaWS,
                                       Integer seatId,
                                       Integer eventId,
                                       Integer scheduleID) throws Exception {

        List<NameValuePair> params = prepararParametrosAuthSpivi(empresaWS);
        params.add(new BasicNameValuePair("ClientIDs[0]", agendadoSelecionado.getSpiviClientID().toString()));
        params.add(new BasicNameValuePair("SeatIDs[0]", seatId.toString()));
        params.add(new BasicNameValuePair("EventIDs[0]", eventId.toString()));
        params.add(new BasicNameValuePair("ScheduleIDs[0]", scheduleID.toString()));

        JSONArray resultJson = requisicaoSpiviAPI("EventService/AddClientsToEvents", params, "Events");
        JSONObject event = (JSONObject) resultJson.get(0);

        SpiviSeat bike = new SpiviSeat();
        bike.setMatricula(agendadoSelecionado.getMatricula());
        bike.setDia(agendamento.getStartDate());
        bike.setSpiviSeatID(seatId);
        bike.setSpiviEventID(eventId);
        bike.setSpiviClientID(agendadoSelecionado.getSpiviClientID());
        bike.setIdzw(agendamento.getId());
        AulaSpiviService aulaSpiviService = UtilContext.getBean(AulaSpiviService.class);
        aulaSpiviService.gravarBike(ctx, bike);

    }

    public void removerClienteEventoSpivi(final String ctx,
                                          EmpresaWS empresaWS,
                                          Integer matricula,
                                          Integer eventID,
                                          Integer clientID,
                                          final String idzw,
                                          final Date dia) throws Exception {
        List<NameValuePair> params = prepararParametrosAuthSpivi(empresaWS);
        params.add(new BasicNameValuePair("ClientIDs[0]", clientID.toString()));
        params.add(new BasicNameValuePair("EventIDs[0]", eventID.toString()));

        JSONArray resultJson = requisicaoSpiviAPI("EventService/RemoveClientsFromEvents", params, "Events");
        JSONObject event = (JSONObject) resultJson.get(0);
        EventJSON eventJson =  new EventJSON(event);

        Map<Integer, SpiviSeat> bikes = bikesAula(ctx, idzw, dia, matricula);
        if(bikes.get(matricula) != null){
            seatDao.delete(ctx, bikes.get(matricula));
        }
    }

    private JSONArray requisicaoSpiviAPI(String endpoint, List<NameValuePair> params, String resultKey) throws IOException, JSONException {
        CloseableHttpClient client = ExecuteRequestHttpService.createConnectorCloseable();
        HttpPost httpPost = new HttpPost("https://api.spivi.com/"+endpoint);

        httpPost.setEntity(new UrlEncodedFormEntity(params));
        CloseableHttpResponse response = client.execute(httpPost);
        ResponseHandler<String> handler = new BasicResponseHandler();
        String body = handler.handleResponse(response);
        client.close();

        JSONObject result = new JSONObject(body);

        if( result.getInt("ErrorCode") != 200 ){
            throw new IOException("SPIVI API ERROR. Endpoint " + endpoint + "." +
                    "Erro code: " + result.getString("ErrorCode") + ". Error message: "+result.getJSONObject(resultKey).getString("message"));
        }

        return  result.getJSONArray(resultKey);
    }

    private List<NameValuePair> prepararParametrosAuthSpivi(EmpresaWS empresaWS) throws Exception {

        List<NameValuePair> params = new ArrayList();
        if( empresaWS.getIntegracaoSpiviSourceName() != null &&
                empresaWS.getIntegracaoSpiviPassword() != null &&
                empresaWS.getIntegracaoSpiviSiteID() != null ){

            params.add(new BasicNameValuePair("SourceCredentials[SourceName]", empresaWS.getIntegracaoSpiviSourceName()));
            params.add(new BasicNameValuePair("SourceCredentials[Password]", empresaWS.getIntegracaoSpiviPassword()));
            params.add(new BasicNameValuePair("SourceCredentials[SiteID]", empresaWS.getIntegracaoSpiviSiteID().toString() ));
            params.add(new BasicNameValuePair("ResponseFormat", "JSON"));
        }else{
            throw new Exception("Os parametros de acesso a API SPIVI não estão definidos na empresa");
        }

        return params;
    }
}
