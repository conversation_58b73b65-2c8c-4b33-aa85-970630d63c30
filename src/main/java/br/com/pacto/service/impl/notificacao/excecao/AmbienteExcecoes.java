package br.com.pacto.service.impl.notificacao.excecao;

import br.com.pacto.service.exception.ExcecaoSistema;

public enum  AmbienteExcecoes implements ExcecaoSistema {

    ERRO_INCLUIR_AMBIENTE("erro_incluir_ambiente", "Ocorreu um erro ao incluir o ambiente"),
    ERRO_LISTAR_AMBIENTES("erro_listar_ambientes", "Ocorreu um erro ao listar os ambientes"),
    ERRO_ALTERAR_AMBIENTE("erro_alterar_ambiente", "Ocorreu um erro ao alterar o ambiente"),

    ERRO_NOME_NAO_INFORMADA("erro_nome_nao_informada", "O nome do ambiente não foi informado!"),
    ERRO_CAPACIDADE_NAO_INFORMADA("erro_capacidade_nao_informada", "A capacidade do ambiente não foi informado!"),
    ERRO_ID_NAO_INFORMADA("erro_id_nao_informada", "O id do ambiente não foi informado!"),
    ERRO_ACAO_PROIBIDA("erro_acao_proibida", "Ação não pode ser requirida quando se tem integração com o ZW"),

    ERRO_AMBIENTE_NAO_EXISTE("erro_ambiente_nao_existe", "O ambiente não existe"),
    ERRO_AMBIENTE_JA_EXISTE("erro_ambiente_ja_existe", "registro_duplicado"),
    AMBIENTE_SEM_COLETOR("ambiente_sem_coletor", "ambiente_sem_coletor"),

    ERRO_CAPACIDADE_INVALIDA("erro_capacidade_invalida", "A capacidade deve ser um inteiro positivo!");

    private String chave;
    private String descricao;

    AmbienteExcecoes(String chave, String descricao) {
        this.chave = chave;
        this.descricao = descricao;
    }

    @Override
    public String getChaveExcecao() {
        return chave;
    }
    @Override
    public String getDescricaoExcecao() {
        return descricao;
    }
}
