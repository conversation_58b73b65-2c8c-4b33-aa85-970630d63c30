package br.com.pacto.service.impl.usuarioEmail;

import br.com.pacto.bean.usuario.TipoUsuarioEnum;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.bean.usuarioEmail.UsuarioEmail;
import br.com.pacto.controller.json.base.SuperControle;
import br.com.pacto.dao.intf.usuarioEmail.UsuarioEmailDao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.conexao.ConexaoZWServiceImpl;
import br.com.pacto.service.intf.Validacao;
import br.com.pacto.service.intf.usuarioEmail.UsuarioEmailService;
import br.com.pacto.util.ViewUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON> on 27/12/2016.
 */

@Service
@Qualifier(value = "usuarioEmailService")
public class UsuarioEmailServiceImpl implements UsuarioEmailService {

    @Autowired
    private ConexaoZWServiceImpl conexaoZWService;
    @Autowired
    private UsuarioEmailDao usuarioEmailDao;
    @Autowired
    private ViewUtils viewUtils;
    @Autowired
    private Validacao validacao;

    public ViewUtils getViewUtils() {
        return viewUtils;
    }


    public UsuarioEmailDao getUsuarioEmailDao() {
        return usuarioEmailDao;
    }

    public Validacao getValidacao() {
        return validacao;
    }

    public UsuarioEmail alterar(final String ctx, UsuarioEmail object) throws ServiceException {
        try {
            return getUsuarioEmailDao().update(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public UsuarioEmail inserir(final String ctx, UsuarioEmail object) throws ServiceException {
        try {
            return getUsuarioEmailDao().insert(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public void excluir(final String ctx, UsuarioEmail object) throws ServiceException {
        try {
            getUsuarioEmailDao().deleteComParam(ctx, new String[]{"codigo"}, new Object[]{object.getCodigo()});
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<UsuarioEmail> obterTodos(final String ctx) throws ServiceException {
        try {
            return getUsuarioEmailDao().findAll(ctx);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<UsuarioEmail> obterPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException {
        try {
            return getUsuarioEmailDao().findByParam(ctx, query, params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }
    public UsuarioEmail obterUsuarioEmailParam(final String ctx, String att, Object value)
            throws ServiceException {
        try {
            return getUsuarioEmailDao().findObjectByAttribute(ctx, att, value);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }
    public List<UsuarioEmail> obterPorParam(final String ctx, String query,
                                       Map<String, Object> params, int max, int index)
            throws ServiceException {
        try {
            return getUsuarioEmailDao().findByParam(ctx, query, params, max, index);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public UsuarioEmail obterPorId(final String ctx, Integer id, TipoUsuarioEnum tipoUsuario) throws ServiceException {
        if (TipoUsuarioEnum.ALUNO.equals(tipoUsuario) || SuperControle.independente(ctx)) {
            try {
                return getUsuarioEmailDao().findById(ctx, id);
            } catch (Exception ex) {
                throw new ServiceException(ex);
            }
        } else {
            try {
                String s = "select * from usuarioemail where codigo = " + id;
                try (Connection con = conexaoZWService.conexaoZw(ctx)) {
                    ResultSet rs = ConexaoZWServiceImpl.criarConsulta(s, con);
                    if (rs.next()) {
                        return montarDadosUsuarioEmail(rs);
                    }
                }
                return null;
            } catch (Exception ex) {
                throw new ServiceException(ex);
            }
        }
    }

    private UsuarioEmail montarDadosUsuarioEmail(ResultSet rs) throws SQLException {
        UsuarioEmail ue = new UsuarioEmail();
        ue.setCodigo(rs.getInt("codigo"));
        ue.setEmail(rs.getString("email"));
        ue.setVerificado(rs.getBoolean("verificado"));
        ue.setUsuario(new Usuario());
        ue.getUsuario().setCodigo(rs.getInt("usuario"));
        return ue;
    }


}
