package br.com.pacto.service.impl.notificacao.excecao;

import br.com.pacto.service.exception.ExcecaoSistema;

public enum AulaExcluidaExcecoes implements ExcecaoSistema {

    ERRO_LISTAR_AULA_EXCLUIDA("erro_listar_aula_excluida", "Ocorreu um erro ao listar as aulas excluídas"),
    ERRO_DESFAZER_AULA_EXCLUIDA("erro_desfazer_aula_excluida", "Ocorreu um erro ao desfazer a aula excluída"),
    ERRO_CRIAR_FILTRO("erro_criar_filtro", "Ocorreu um erro ao tentar criar filtro");

    private String chave;
    private String descricao;

    AulaExcluidaExcecoes(String chave, String descricao) {
        this.chave = chave;
        this.descricao = descricao;
    }

    @Override
    public String getChaveExcecao() {
        return chave;
    }
    @Override
    public String getDescricaoExcecao() {
        return descricao;
    }
}
