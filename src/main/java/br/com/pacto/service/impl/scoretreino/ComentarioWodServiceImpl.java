package br.com.pacto.service.impl.scoretreino;

import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.wod.ComentarioWod;
import br.com.pacto.bean.wod.ComentarioWodLike;
import br.com.pacto.bean.wod.ScoreTreino;
import br.com.pacto.bean.wod.Wod;
import br.com.pacto.dao.intf.wod.ComentarioWodDao;
import br.com.pacto.dao.intf.wod.ComentarioWodLikeDao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.scoretreino.ComentarioWodService;
import br.com.pacto.util.UteisValidacao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/*
 * Created by <PERSON><PERSON>
 */
@Service
public class ComentarioWodServiceImpl implements ComentarioWodService {

    @Autowired
    private ComentarioWodDao comentarioWodDao;
    @Autowired
    private ComentarioWodLikeDao comentarioWodLikeDao;


    public ComentarioWodDao getComentarioWodDao() {
        return comentarioWodDao;
    }

    @Override
    public ComentarioWod obterPorId(String ctx, Integer id) throws ServiceException {
        try {
            return getComentarioWodDao().findById(ctx, id);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public List<ComentarioWod> obterPorParam(String ctx, String query, Map<String, Object> params) throws ServiceException {
        try {
            return getComentarioWodDao().findByParam(ctx, query, params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public ComentarioWod inserir(final String ctx, ComentarioWod object) throws ServiceException {
        try {
            return getComentarioWodDao().insert(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public ComentarioWod alterar(final String ctx, ComentarioWod object) throws ServiceException {
        try {
            return getComentarioWodDao().update(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public void excluir(final String ctx, ComentarioWod object) throws ServiceException {
        try {
            getComentarioWodDao().delete(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public void excluirComRelacionamentos(final String ctx, ComentarioWod object) throws ServiceException {
        try {
            comentarioWodLikeDao.deleteComParam(ctx, new String[]{"comentarioWod.codigo"}, new Object[]{object.getCodigo()});
            getComentarioWodDao().delete(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public void preencherComentariosWod(final String ctx, Wod wod) throws ServiceException {
        try {
            Map<String, Object> params =  new HashMap<String, Object>();
            params.put("wod", wod.getCodigo());
            String hql = " SELECT obj FROM ComentarioWod obj " +
                    " WHERE obj.wod.codigo = :wod ORDER BY obj.posicao";
            wod.setComentarios(getComentarioWodDao().findByParam(ctx, hql, params));
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }
}
