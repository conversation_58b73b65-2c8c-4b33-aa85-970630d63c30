package br.com.pacto.service.impl.notificacao.excecao;

import br.com.pacto.service.exception.ExcecaoSistema;

public enum ConfiguracaoSistemaExcecoes implements ExcecaoSistema {

    ERRO_SALVAR_CONFIGURACOES("erro_salvar_configuracoes", "Erro ao salvar as configurações do sistema."),
    ERRO_SALVAR_CONFIGURACOES_VALOR_ZERO_POSITIVO("erro_salvar_configuracoes_valor_positivo", "Informe um valor numerico zero ou positivo: ");

    private String chave;
    private String descricao;

    ConfiguracaoSistemaExcecoes(String chave, String descricao) {
        this.chave = chave;
        this.descricao = descricao;
    }

    @Override
    public String getChaveExcecao() {
        return chave;
    }

    @Override
    public String getDescricaoExcecao() {
        return descricao;
    }
}
