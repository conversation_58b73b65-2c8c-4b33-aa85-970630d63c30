package br.com.pacto.service.impl.gogood;

import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.bean.gogood.ConfigGoGood;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.base.SuperControle;
import br.com.pacto.controller.json.gogood.ConfigGoGoodDTO;
import br.com.pacto.dao.intf.gogood.ConfigGoGoodDao;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.empresa.EmpresaService;
import br.com.pacto.service.intf.gogood.ConfigGoGoodService;
import br.com.pacto.service.intf.usuario.UsuarioService;
import br.com.pacto.util.UteisValidacao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
public class ConfigGoGoodServiceImpl implements ConfigGoGoodService {

    @Autowired
    private ConfigGoGoodDao dao;
    @Autowired
    private EmpresaService empresaService;
    @Autowired
    private SessaoService sessaoService;
    @Autowired
    private UsuarioService usuarioService;

    public ConfigGoGood alterarDTO(final String ctx, ConfigGoGoodDTO dto) throws ServiceException {
        try {
            Empresa empresa = empresaService.obterPorId(ctx, dto.getEmpresa());
            Usuario usuario = usuarioService.obterPorId(ctx, sessaoService.getUsuarioAtual().getId());
            Date agora = new Date();
            ConfigGoGood config = new ConfigGoGood(dto, empresa, usuario, agora);
            if (UteisValidacao.emptyString(config.getTokenAcademyGoGood())) {
                config.setAtivo(false);
            } else {
                config.setAtivo(true);
            }
            if (UteisValidacao.emptyNumber(config.getCodigo())) {
                dao.insert(ctx, config);
            } else {
                dao.update(ctx, config);
            }
            return config;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public ConfigGoGood obterPorEmpresaTR(final String ctx, Integer codEmpresaTR) throws ServiceException {
        try {
            Empresa empresa = null;
            if (!UteisValidacao.emptyNumber(codEmpresaTR)) {
                if(SuperControle.independente(ctx)){
                    empresa = empresaService.obterPorId(ctx, codEmpresaTR);
                }else{
                    empresa = empresaService.obterPorIdZW(ctx, codEmpresaTR);
                }
                return obterPorEmpresa(ctx, empresa);
            }
            return null;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public ConfigGoGood obterPorEmpresa(final String ctx, Empresa empresa) throws ServiceException {
        try {
            ConfigGoGood obj = dao.findObjectByAttributes(ctx, new String[]{"empresa.codigo"}, new Object[]{empresa.getCodigo()}, "codigo");
            if (obj == null) {
                obj = new ConfigGoGood();
                obj.setUsuarioLancou_codigo(sessaoService.getUsuarioAtual().getId());
                obj.setDataLancamento(new Date());
                obj.setEmpresa(empresa);
                obj.setTokenAcademyGoGood("");
                obj.setAtivo(false);
                dao.insert(ctx, obj);
            }
            dao.refresh(ctx, obj);
            return obj;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }
}
