/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.impl.atividadeEmpresa;

import br.com.pacto.bean.atividade.AtividadeEmpresa;
import br.com.pacto.dao.intf.atividadeEmpresa.AtividadeEmpresaDao;
import br.com.pacto.service.intf.Validacao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.exception.ValidacaoException;
import br.com.pacto.service.intf.atividadeEmpresa.AtividadeEmpresaService;
import br.com.pacto.util.ViewUtils;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR>
 */
@Service
public class AtividadeEmpresaServiceImpl implements AtividadeEmpresaService {

    @Autowired
    private ViewUtils viewUtils;
    @Autowired
    private Validacao validacao;
    @Autowired
    private AtividadeEmpresaDao atividadeEmpresaDao;

    public ViewUtils getViewUtils() {
        return viewUtils;
    }

    public void setViewUtils(ViewUtils viewUtils) {
        this.viewUtils = viewUtils;
    }

    public Validacao getValidacao() {
        return validacao;
    }

    public void setValidacao(Validacao validacao) {
        this.validacao = validacao;
    }

    public AtividadeEmpresaDao getAtividadeEmpresaDao() {
         return this.atividadeEmpresaDao;
    }

    public void setAtividadeEmpresa(AtividadeEmpresaDao atividadeEmpresaDao) {
        this.atividadeEmpresaDao = atividadeEmpresaDao;
    }

    public AtividadeEmpresa alterar(final String ctx, AtividadeEmpresa object) throws ServiceException, ValidacaoException {
        try {
            validarDados(ctx, object);
            return getAtividadeEmpresaDao().update(ctx, object);
        } catch (ValidacaoException vex) {
            throw vex;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public void excluir(final String ctx, AtividadeEmpresa object) throws ServiceException {
        try {
            getAtividadeEmpresaDao().delete(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    public AtividadeEmpresa inserir(final String ctx, AtividadeEmpresa object) throws ServiceException, ValidacaoException {
        try {
            validarDados(ctx, object);
            return getAtividadeEmpresaDao().insert(ctx, object);
        } catch (ValidacaoException vex) {
            throw vex;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public void validarDados(final String ctx, AtividadeEmpresa object) throws ValidacaoException {
        if (object.getEmpresa() == null || object.getIdentificador().isEmpty()) {
            throw new ValidacaoException("validacao.nome");
        }
        if (getAtividadeEmpresaDao().exists(ctx, object, "nome")) {
            throw new ValidacaoException("cadastros.existente");
        }
    }

    public AtividadeEmpresa obterObjetoPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException {
        try {
            return getAtividadeEmpresaDao().findObjectByParam(ctx, query, params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public AtividadeEmpresa obterPorId(final String ctx, Integer id) throws ServiceException {
        try {
            return getAtividadeEmpresaDao().findById(ctx, id);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<AtividadeEmpresa> obterPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException {
        try {
            return getAtividadeEmpresaDao().findByParam(ctx, query, params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<AtividadeEmpresa> obterPorParam(final String ctx, String query,
            Map<String, Object> params, int max, int index)
            throws ServiceException {
        try {
            return getAtividadeEmpresaDao().findByParam(ctx, query, params, max, index);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<AtividadeEmpresa> obterTodos(final String ctx) throws ServiceException {
        try {
            return getAtividadeEmpresaDao().findListByAttributes(ctx, null, null, "identificador", 0);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

}