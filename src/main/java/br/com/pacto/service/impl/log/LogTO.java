package br.com.pacto.service.impl.log;

import java.util.List;

public class LogTO {

    private String chave;
    private String operacao;
    private String usuario;
    private String dia;
    private String hora;
    private String identificador;
    private String descricao;
    private String origem;
    private List<AlteracoesTO> alteracoes;

    public LogTO(Integer chave, String operacao, String identificador, String usuario, String dia, String hora, List<AlteracoesTO> alteracoes) {
        this.identificador = identificador;
        this.chave = chave == null ? "-" : chave.toString();
        this.operacao = operacao;
        this.usuario = usuario;
        this.dia = dia;
        this.hora = hora;
        this.alteracoes = alteracoes;
        this.descricao = "";
        try {
            for(AlteracoesTO a : alteracoes){
                this.descricao  += ("["+a.getCampo()+":"+"'"+a.getValorAnterior()+"' para '"+a.getValorAlterado()+"']<br/>");
            }
        }catch (Exception e){
        }
    }

    public LogTO() {
    }

    public String getChave() {
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    public String getOperacao() {
        return operacao;
    }

    public void setOperacao(String operacao) {
        this.operacao = operacao;
    }

    public String getUsuario() {
        return usuario;
    }

    public void setUsuario(String usuario) {
        this.usuario = usuario;
    }

    public List<AlteracoesTO> getAlteracoes() {
        return alteracoes;
    }

    public void setAlteracoes(List<AlteracoesTO> alteracoes) {
        this.alteracoes = alteracoes;
    }

    public String getDia() {
        return dia;
    }

    public void setDia(String dia) {
        this.dia = dia;
    }

    public String getHora() {
        return hora;
    }

    public void setHora(String hora) {
        this.hora = hora;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getIdentificador() {
        return identificador;
    }

    public void setIdentificador(String identificador) {
        this.identificador = identificador;
    }

    public String getOrigem() {return origem; }

    public void setOrigem(String origem) { this.origem = origem;}
}
