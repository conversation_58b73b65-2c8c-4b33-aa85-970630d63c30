package br.com.pacto.service.impl.avaliacao;

import java.util.Date;

public class ItemAvaliacaoFisicaTO {

    private Integer codCliente;
    private String nome;
    private Date dataAvaliacao;
    private Date dataProxima;
    private String avaliador;
    private String professor;
    private String obs;
    private String diff;
    private String matricula;
    private String modalidade;

    public ItemAvaliacaoFisicaTO(Integer codCliente, String nome, Date dataAvaliacao, Date dataProxima, String avaliador, String professor, String matricula) {
        this.codCliente = codCliente;
        this.nome = nome;
        this.dataAvaliacao = dataAvaliacao;
        this.dataProxima = dataProxima;
        this.avaliador = avaliador;
        this.professor = professor;
        this.matricula = matricula;
    }

    public ItemAvaliacaoFisicaTO(Integer codCliente, String matricula, String nome, String obs, String diff) {
        this.codCliente = codCliente;
        this.matricula = matricula;
        this.nome = nome;
        this.obs = obs;
        this.diff = diff;
    }

    public ItemAvaliacaoFisicaTO(Integer codCliente, String nome, String matricula) {
        this.codCliente = codCliente;
        this.nome = nome;
        this.matricula = matricula;
    }

    public ItemAvaliacaoFisicaTO(Integer codCliente, String nome, String matricula, String modalidade) {
        this.codCliente = codCliente;
        this.nome = nome;
        this.matricula = matricula;
        this.modalidade = modalidade;
    }

    public String getDiff() {
        return diff;
    }

    public void setDiff(String diff) {
        this.diff = diff;
    }

    public ItemAvaliacaoFisicaTO() {
    }

    public String getObs() {
        return obs;
    }

    public void setObs(String obs) {
        this.obs = obs;
    }

    public Integer getCodCliente() {
        return codCliente;
    }

    public void setCodCliente(Integer codCliente) {
        this.codCliente = codCliente;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Date getDataAvaliacao() {
        return dataAvaliacao;
    }

    public void setDataAvaliacao(Date dataAvaliacao) {
        this.dataAvaliacao = dataAvaliacao;
    }

    public Date getDataProxima() {
        return dataProxima;
    }

    public void setDataProxima(Date dataProxima) {
        this.dataProxima = dataProxima;
    }

    public String getAvaliador() {
        return avaliador;
    }

    public void setAvaliador(String avaliador) {
        this.avaliador = avaliador;
    }

    public String getProfessor() {
        return professor;
    }

    public void setProfessor(String professor) {
        this.professor = professor;
    }

    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public String getModalidade() {
        return modalidade;
    }

    public void setModalidade(String modalidade) {
        this.modalidade = modalidade;
    }
}
