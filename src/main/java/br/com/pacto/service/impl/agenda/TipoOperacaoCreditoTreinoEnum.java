package br.com.pacto.service.impl.agenda;

public enum TipoOperacaoCreditoTreinoEnum {

    COMPRA(1, "COMP<PERSON>", "COMPRA"),
    UTILIZACAO(2, "UTILIZAÇÃO", "UTILIZAÇÃO"),
    NAO_COMPARECIMENTO(3, "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"),
    MARCOU_AULA(4, "MARCAÇÃO DE AULA", "MARCAÇÃO"),
    DESMARCOU_AULA(5, "DESMARCAÇÃO DE AULA", "DESMARCAÇÃO"),
    R<PERSON><PERSON><PERSON><PERSON><PERSON>(6, "REPOSIÇÃO DE AULA", "REPOSIÇÃO"),
    AJUSTE_MANUAL(7,"AJUSTE MANUAL", "AJUSTE MANUAL"),
    TRANSFERENCIA_SALDO(8,"TRANSFERENCIA SALDO", "TRANSF. SALDO"),
    MANUTENCAO_MODALIDADE(9,"MANUTENÇÃO DE MODALIDADE", "MANUT. MODALIDADE"),
    CANCELAMENTO_CONTRATO(10,"CANCELAMENTO DE CONTRATO", "CANC. CONTRATO"),
    AJUSTE_MENSAL(11, "AJUSTE MENSAL", "AJUSTE MENSAL");

    public static String AJUSTE_MANUAL_POSITIVO = "AJUSTE MANUAL POSITIVO";
    public static String AJUSTE_MANUAL_NEGATIVO  = "AJUSTE MANUAL NEGATIVO";
    public static String OPERACAO_TODOS  = "TODAS";
    public static int CODIGO_AJUSTE_MANUAL_NEGATIVO =888;
    public static int CODIGO_TODOS =999;

    TipoOperacaoCreditoTreinoEnum(Integer codigo, String descricao, String descricaoCurta){
        this.codigo = codigo;
        this.descricao = descricao;
        this.descricaoCurta = descricaoCurta;
    }

    public static TipoOperacaoCreditoTreinoEnum getTipo(Integer codigo){
        for (TipoOperacaoCreditoTreinoEnum tipo: TipoOperacaoCreditoTreinoEnum.values()){
            if (tipo.getCodigo().equals(codigo))
                return tipo;
        }
        return null;
    }

    private Integer codigo;
    private String descricao;
    private String descricaoCurta;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getDescricaoCurta() {
        return descricaoCurta;
    }

    public void setDescricaoCurta(String descricaoCurta) {
        this.descricaoCurta = descricaoCurta;
    }
}
