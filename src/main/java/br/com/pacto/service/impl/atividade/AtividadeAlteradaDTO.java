package br.com.pacto.service.impl.atividade;

import br.com.pacto.controller.json.atividade.read.AtividadeJSON;

public class AtividadeAlteradaDTO {
    private NovaAtividadeFichaDTO atividadeFicha;

    private AtividadeJSON atividadeBase;

    public NovaAtividadeFichaDTO getAtividadeFicha() {
        return atividadeFicha;
    }

    public void setAtividadeFicha(NovaAtividadeFichaDTO atividadeFicha) {
        this.atividadeFicha = atividadeFicha;
    }

    public AtividadeJSON getAtividadeBase() {
        return atividadeBase;
    }

    public void setAtividadeBase(AtividadeJSON atividadeBase) {
        this.atividadeBase = atividadeBase;
    }
}

