package br.com.pacto.service.impl.avaliacao;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.anamnese.RespostaCliente;
import br.com.pacto.bean.sincronizacao.TipoRevisaoEnum;
import br.com.pacto.controller.json.log.EntidadeLogEnum;
import br.com.pacto.dao.intf.anamnese.RespostaClienteDao;
import br.com.pacto.dao.intf.avaliacao.AvaliacaoFisicaDao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.log.AlteracoesTO;
import br.com.pacto.service.impl.log.LogTO;
import br.com.pacto.service.intf.avaliacao.LogAvaliacaoFisicaService;
import br.com.pacto.service.intf.log.LogService;
import br.com.pacto.util.UteisValidacao;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.DatabaseMetaData;
import java.sql.ResultSet;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

@Service
public class LogAvaliacaoFisicaServiceImpl implements LogAvaliacaoFisicaService {

    @Autowired
    private SessaoService sessaoService;
    @Autowired
    private AvaliacaoFisicaDao avaliacaoFisicaDao;
    @Autowired
    private RespostaClienteDao respostaClienteDao;
    @Autowired
    private LogService logService;

    @Override
    public List<LogTO> listarLogAtividade(JSONObject filtros, PaginadorDTO paginadorDTO, Integer codAluno, Integer codAvaliacao) throws ServiceException {
        List<LogTO> listarLog = new ArrayList<>();
        try {
            String quicksearchValue = filtros.optString("quicksearchValue");
            JSONArray tipos = filtros.optJSONArray("tipo");
            Long inicio = filtros.optLong("dataInicio");
            Long fim = filtros.optLong("dataFim");
            Integer codigoAv = null;
            try {
                codigoAv = quicksearchValue == null ? null : Integer.valueOf(quicksearchValue);
            }catch (Exception e){
                //ignore
            }
            String ctx = sessaoService.getUsuarioAtual().getChave();


            DatabaseMetaData meta = avaliacaoFisicaDao.getConnection(ctx).getMetaData();
            String nomeTabela = "avaliacaofisica_aud"; // nome da tabela
            ResultSet rsTabela = meta.getTables(null, null, nomeTabela, new String[] {"TABLE"});

            if (rsTabela.next()) {
                StringBuilder sqlprincipal = new StringBuilder();
                sqlprincipal.append(" _campos_ \n");
                sqlprincipal.append(" from customrevisionentity c \n");
                sqlprincipal.append(" inner join usuario u on u.username = c.username \n");
                sqlprincipal.append(" left join avaliacaofisica_aud aa on aa.rev = c.id \n");
                sqlprincipal.append(" where aa.codigo is not null \n");
                if(codAluno != null && codAvaliacao == null){
                    sqlprincipal.append(" and aa.cliente_codigo = ").append(codAluno).append("\n");
                }
                if(codAvaliacao != null){
                    sqlprincipal.append(" and aa.codigo = ").append(codAvaliacao).append("\n");
                }
                if(inicio != null && inicio > 0l){
                    Long dataInicio = Calendario.getDataComHoraZerada(new Date(inicio)).getTime();
                    sqlprincipal.append(" AND c.\"timestamp\" >= ").append(dataInicio).append(" \n");
                }
                if(fim != null && fim > 0l){
                    Long dataFim = Calendario.fimDoDia(new Date(fim)).getTime();
                    sqlprincipal.append(" AND c.\"timestamp\" <= ").append(dataFim).append(" \n");
                }
                if(tipos != null && tipos.length() > 0){
                    String tiposCod = "";
                    for(int i = 0; i < tipos.length(); i++){
                        tiposCod += "," + TipoRevisaoEnum.valueOf(tipos.getString(i)).getId();
                    }
                    sqlprincipal.append(" and aa.revtype in ( ").append(tiposCod.replaceFirst(",", "")).append(" )");
                }
                sqlprincipal.append(UteisValidacao.emptyString(quicksearchValue) ? "" : " and (");
                if (codigoAv != null){
                    sqlprincipal.append(" aa.codigo = ").append(codigoAv).append(" or \n");
                }
                if (!UteisValidacao.emptyString(quicksearchValue)) {
                    sqlprincipal.append("  upper(c.username) like '%").append(quicksearchValue.toUpperCase()).append("%' \n");
                }
                sqlprincipal.append(UteisValidacao.emptyString(quicksearchValue) ? "" : ")");
                sqlprincipal.append(" group by 1,2,3,4 \n");

                Integer maxResults = 10;
                Integer indiceInicial = 0;
                if (paginadorDTO != null) {
                    maxResults = paginadorDTO.getSize() == null ? 10 : paginadorDTO.getSize().intValue();
                    indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
                }

                try (ResultSet rs = avaliacaoFisicaDao.createStatement(ctx,
                        sqlprincipal.toString().replace("_campos_", "select c.\"timestamp\"/1000 as segundo, " +
                                        " c.username, coalesce(aa.revtype, 1) as revtype, aa.codigo as codavaliacao ")
                                .concat("order by c.\"timestamp\"/1000 desc, coalesce(aa.revtype, 1) desc limit ".concat(maxResults.toString()))
                                .concat(" offset ".concat(indiceInicial.toString())))) {
                    while (rs.next()) {
                        TipoRevisaoEnum revtype = TipoRevisaoEnum.getFromId(rs.getInt("revtype"));
                        Integer codigo = rs.getInt("codavaliacao");
                        Long timestamp = rs.getLong("segundo") * 1000;
                        String username = rs.getString("username");

                        LogTO logTO = new LogTO(codigo,
                                revtype.getDescricaoLog(),
                                revtype.equals(TipoRevisaoEnum.DELETE) ? "" : "Avaliação #" + codigo,
                                username,
                                Uteis.getDataAplicandoFormatacao(new Date(timestamp), "dd/MM/yyyy HH:mm"),
                                Uteis.getDataAplicandoFormatacao(new Date(timestamp), "HH:mm:ss"),
                                new ArrayList());
                        ResultSet rsRevs = descobrirQuemFoiAlteradoNoMomento(ctx, rs.getLong("segundo"), username, codigo, revtype.getId());

                        logTO.setAlteracoes(new ArrayList<>());
                        while (rsRevs.next()) {
                            Map<String, String> valoresAlterados = Uteis.montarMapa(rsRevs, camposArray);
                            Map<String, String> valoresAnteriores = null;
                            if (revtype.equals(TipoRevisaoEnum.UPDATE) || revtype.equals(TipoRevisaoEnum.DELETE)) {
                                try (ResultSet rsAnterior = avaliacaoFisicaDao.createStatement(ctx,
                                        "select aa.rev, aa.revtype, " + campos() +
                                                " from avaliacaofisica_aud aa where codigo = " + codigo +
                                                " and rev < " + rsRevs.getLong("rev") +
                                                " order by rev desc limit 1 ")) {
                                    if (rsAnterior.next()) {
                                        valoresAnteriores = Uteis.montarMapa(rsAnterior, camposArray);
                                    }
                                }
                            }
                            logTO.getAlteracoes().addAll(Uteis.compararMapas(valoresAnteriores, valoresAlterados));
                        }

                        logTO.getAlteracoes().addAll(alteracoesFlexibilidade(ctx, rs.getLong("segundo"), username, codigo));
                        logTO.getAlteracoes().addAll(alteracoesPostural(ctx, rs.getLong("segundo"), username, codigo));
                        logTO.getAlteracoes().addAll(alteracoesItensPostural(ctx, rs.getLong("segundo"), username, codigo));
                        logTO.getAlteracoes().addAll(alteracoesItensAvaliacao(ctx, rs.getLong("segundo"), username, codigo));
                        logTO.getAlteracoes().addAll(alteracoesVentilometria(ctx, rs.getLong("segundo"), username, codigo));
                        logTO.getAlteracoes().addAll(alteracoesSomatotipia(ctx, rs.getLong("segundo"), username, codigo));
                        logTO.getAlteracoes().addAll(alteracoesRespostaAnamnese(ctx, rs.getLong("segundo"), username, codAluno));

                        int i = 0;
                        for (AlteracoesTO a : logTO.getAlteracoes()) {
                            logTO.setDescricao(logTO.getDescricao() + ("[" + a.getCampo() + ":'"
                                    + (UteisValidacao.emptyString(a.getValorAnterior()) ? "" : (a.getValorAnterior() + "' para '"))
                                    + a.getValorAlterado() + "']<br/>"));
                            if (i > 5) {
                                logTO.setDescricao(logTO.getDescricao() + "<br/>...");
                                break;
                            }
                            i++;
                        }

                        listarLog.add(logTO);
                    }
                }
                try (ResultSet rsCount = avaliacaoFisicaDao.createStatement(ctx, sqlprincipal.toString().replace("_campos_", " select count(segundo) as cont from (" +
                                " select c.\"timestamp\"/1000 as segundo , c.username, \n" +
                                " coalesce(aa.revtype, 1) as revtype, aa.codigo as codavaliacao ")
                        .concat(") as t"))) {
                    if (rsCount.next()) {
                        paginadorDTO.setQuantidadeTotalElementos(rsCount.getLong("cont"));
                    } else {
                        paginadorDTO.setQuantidadeTotalElementos(10l);
                    }
                }
            } else {
                Logger.getLogger(LogAvaliacaoFisicaServiceImpl.class.getName()).log(Level.SEVERE, "A tabela "+nomeTabela+" NÃO existe.");
            }
        }catch (Exception e){
            throw new ServiceException(e);
        }

        return listarLog;
    }

    private ResultSet descobrirQuemFoiAlteradoNoMomento(String chave, Long momento, String username, Integer codigo, Integer revtype) throws Exception{
        return avaliacaoFisicaDao.createStatement(chave, "select aa.rev, aa.revtype, " + campos() +
                " from avaliacaofisica_aud aa inner join customrevisionentity c on c.id = aa.rev" +
                " where c.username = '" + username +
                "' and c.\"timestamp\"/1000 = " + momento + " and aa.codigo = " + codigo +
                " and aa.revtype = " + revtype);
    }

    private String campos(){
        String campos = "";
        for(String c : camposArray){
            campos += ", aa." + c;
        }
        return campos.replaceFirst(",", "");
    }

    private List<AlteracoesTO> alteracoesFlexibilidade(String chave, Long momento, String username, Integer codigoAvaliacao) throws Exception {
        ResultSet rs = avaliacaoFisicaDao.createStatement(chave, "select distinct fa.rev, fa.revtype, fa.codigo, fa.alcance, fa.classificacao, fa.observacao " +
                " from flexibilidade_aud fa " +
                " inner join customrevisionentity c on c.id = fa.rev" +
                " where c.username = '" + username +
                "' and c.\"timestamp\"/1000 = " + momento + " and fa.codigo = " + codigoAvaliacao);
        List<AlteracoesTO> alteracoes = new ArrayList<>();

        while (rs.next()) {
            TipoRevisaoEnum revtype = TipoRevisaoEnum.getFromId(rs.getInt("revtype"));
            Map<String, String> valoresAlterados = Uteis.montarMapa(rs, "alcance", "classificacao", "observacao");
            Map<String, String> valoresAnteriores = null;
            Integer codigo = rs.getInt("codigo");
            if (revtype.equals(TipoRevisaoEnum.UPDATE) || revtype.equals(TipoRevisaoEnum.DELETE)) {
                try (ResultSet rsAnterior = avaliacaoFisicaDao.createStatement(chave,
                        "select fa.rev, fa.revtype, fa.codigo, fa.alcance, fa.classificacao, fa.observacao " +
                                " from flexibilidade_aud fa where codigo = " + codigo +
                                " and rev < " + rs.getLong("rev") +
                                " order by rev desc limit 1 ")) {
                    if (rsAnterior.next()) {
                        valoresAnteriores = Uteis.montarMapa(rsAnterior, "alcance", "classificacao", "observacao");
                    }
                }
            }
            alteracoes.addAll(Uteis.compararMapas(valoresAnteriores, valoresAlterados));
        }
        return alteracoes;
    }

    private List<AlteracoesTO> alteracoesPostural(String chave, Long momento, String username, Integer codigoAvaliacao) throws Exception {
        List<AlteracoesTO> alteracoes;
        try (ResultSet rs = avaliacaoFisicaDao.createStatement(chave, "select distinct c.\"timestamp\"/1000 as segundo, aa.codigo, aa.rev, aa.revtype, " +
                " aa.keyimganterior, aa.keyimgdireita, aa.keyimgesquerda, aa.keyimgposterior, aa.observacao, aa.ombros, aa.quadril " +
                " from avaliacaopostural_aud aa " +
                " inner join customrevisionentity c on c.id = aa.rev" +
                " where c.username = '" + username +
                "' and c.\"timestamp\"/1000 = " + momento + " and aa.avaliacao_codigo = " + codigoAvaliacao)) {
            alteracoes = new ArrayList<>();

            while (rs.next()) {
                TipoRevisaoEnum revtype = TipoRevisaoEnum.getFromId(rs.getInt("revtype"));
                Map<String, String> valoresAlterados = Uteis.montarMapa(rs, "observacao", "ombros", "quadril");
                Map<String, String> valoresAnteriores = null;
                Integer codigo = rs.getInt("codigo");
                if (revtype.equals(TipoRevisaoEnum.UPDATE) || revtype.equals(TipoRevisaoEnum.DELETE)) {
                    try (ResultSet rsAnterior = avaliacaoFisicaDao.createStatement(chave,
                            "select aa.codigo, aa.rev, aa.revtype, aa.keyimganterior, aa.keyimgdireita, aa.keyimgesquerda, " +
                                    "aa.keyimgposterior, aa.observacao, aa.ombros, aa.quadril " +
                                    " from avaliacaopostural_aud aa where codigo = " + codigo +
                                    " and rev < " + rs.getLong("rev") +
                                    " order by rev desc limit 1 ")) {
                        if (rsAnterior.next()) {
                            valoresAnteriores = Uteis.montarMapa(rsAnterior, "observacao", "ombros", "quadril");
                        }
                    }
                }
                alteracoes.addAll(Uteis.compararMapas(valoresAnteriores, valoresAlterados));
            }
        }
        return alteracoes;
    }

    private List<AlteracoesTO> alteracoesItensPostural(String chave, Long momento, String username, Integer codigoAvaliacao) throws Exception{
        List<AlteracoesTO> alteracoes;
        Map<String, List<String>> itensSelecionados;
        List<String> alteradas;
        try (ResultSet rs = avaliacaoFisicaDao.createStatement(chave, "select distinct c.\"timestamp\"/1000 as segundo, " +
                " ia.codigo, ia.rev, ia.revtype, ia.item, ia.selecionado " +
                " from itemavaliacaopostural_aud ia " +
                " inner join customrevisionentity c on c.id = ia.rev" +
                " where selecionado = true and c.username = '" + username +
                "' and c.\"timestamp\"/1000 = " + momento + " and ia.avaliacaopostural_codigo = " + codigoAvaliacao +
                " order by c.\"timestamp\"/1000 desc, item asc")) {

            alteracoes = new ArrayList<>();
            itensSelecionados = new HashMap<>();
            alteradas = new ArrayList<>();

            while (rs.next()) {
                Integer aux = rs.getInt("item");
                alteradas.add(aux.toString());
            }
        }

        itensSelecionados.put("alteradas", alteradas);
        if(!UteisValidacao.emptyList(alteradas)) {
            alteracoes.add(new AlteracoesTO("alteração de itens da postura ", "",
                    Uteis.listToString(itensSelecionados.get("alteradas"))));
        }

        return alteracoes;
    }

    private List<AlteracoesTO> alteracoesItensAvaliacao(String chave, Long momento, String username, Integer codigoAvaliacao) throws Exception {
        List<AlteracoesTO> alteracoes;
        try (ResultSet rs = avaliacaoFisicaDao.createStatement(chave, "select distinct c.\"timestamp\"/1000 as segundo, " +
                " ia.codigo, ia.rev, ia.revtype, ia.item, ia.result " +
                " from itemavaliacaofisica_aud ia " +
                " inner join customrevisionentity c on c.id = ia.rev" +
                " where c.username = '" + username +
                "' and c.\"timestamp\"/1000 = " + momento + " and ia.avaliacaofisica_codigo = " + codigoAvaliacao)) {
            alteracoes = new ArrayList<>();

            while (rs.next()) {
                TipoRevisaoEnum revtype = TipoRevisaoEnum.getFromId(rs.getInt("revtype"));
                Map<String, String> valoresAlterados = Uteis.montarMapa(rs, "item", "result");
                Map<String, String> valoresAnteriores = null;
                Integer codigo = rs.getInt("codigo");
                if (revtype.equals(TipoRevisaoEnum.UPDATE) || revtype.equals(TipoRevisaoEnum.DELETE)) {
                    try (ResultSet rsAnterior = avaliacaoFisicaDao.createStatement(chave,
                            "select ia.codigo, ia.rev, ia.revtype, ia.item, ia.result " +
                                    " from itemavaliacaofisica_aud ia where codigo = " + codigo +
                                    " and rev < " + rs.getLong("rev") +
                                    " order by rev desc limit 1 ")) {
                        if (rsAnterior.next()) {
                            valoresAnteriores = Uteis.montarMapa(rsAnterior, "item", "result");
                        }
                    }
                }
                alteracoes.addAll(Uteis.compararMapas(valoresAnteriores, valoresAlterados));
            }
        }
        return alteracoes;
    }

    private List<AlteracoesTO> alteracoesVentilometria(String chave, Long momento, String username, Integer codigoAvaliacao) throws Exception {
        List<AlteracoesTO> alteracoes;
        try (ResultSet rs = avaliacaoFisicaDao.createStatement(chave, "select distinct c.\"timestamp\"/1000 as segundo, " +
                " va.codigo, va.rev, va.revtype, va.limiarventilatorio, va.limiarventilatorio2, va.vo2max " +
                " from ventilometria_aud va " +
                " inner join customrevisionentity c on c.id = va.rev" +
                " where c.username = '" + username +
                "' and c.\"timestamp\"/1000 = " + momento + " and va.codigo = " + codigoAvaliacao)) {
            alteracoes = new ArrayList<>();

            while (rs.next()) {
                TipoRevisaoEnum revtype = TipoRevisaoEnum.getFromId(rs.getInt("revtype"));
                Map<String, String> valoresAlterados = Uteis.montarMapa(rs, "limiarventilatorio", "limiarventilatorio2", "vo2max");
                Map<String, String> valoresAnteriores = null;
                Integer codigo = rs.getInt("codigo");
                if (revtype.equals(TipoRevisaoEnum.UPDATE) || revtype.equals(TipoRevisaoEnum.DELETE)) {
                    try (ResultSet rsAnterior = avaliacaoFisicaDao.createStatement(chave,
                            "select va.codigo, va.rev, va.revtype, va.limiarventilatorio, va.limiarventilatorio2, va.vo2max " +
                                    " from ventilometria_aud va where codigo = " + codigo +
                                    " and rev < " + rs.getLong("rev") +
                                    " order by rev desc limit 1 ")) {
                        if (rsAnterior.next()) {
                            valoresAnteriores = Uteis.montarMapa(rsAnterior, "limiarventilatorio", "limiarventilatorio2", "vo2max");
                        }
                    }
                }
                alteracoes.addAll(Uteis.compararMapas(valoresAnteriores, valoresAlterados));
            }
        }
        return alteracoes;
    }

    private List<AlteracoesTO> alteracoesSomatotipia(String chave, Long momento, String username, Integer codigoAvaliacao) throws Exception {
        List<AlteracoesTO> alteracoes;
        try (ResultSet rs = avaliacaoFisicaDao.createStatement(chave, "select distinct c.\"timestamp\"/1000 as segundo, " +
                " pa.codigo, pa.rev, pa.revtype, pa.altura, pa.diametrocotovelo, pa.diametrofemur, pa.diametropunho, " +
                " pa.diametrotornozelo, pa.percentualgordura, pa.peso, pa.pesogordura, pa.pesomuscular, pa.pesoosseo, pa.pesoresidual " +
                " from pesoosseo_aud pa " +
                " inner join customrevisionentity c on c.id = pa.rev" +
                " where c.username = '" + username +
                "' and c.\"timestamp\"/1000 = " + momento + " and pa.avaliacaofisica_codigo = " + codigoAvaliacao)) {
            alteracoes = new ArrayList<>();

            while (rs.next()) {
                TipoRevisaoEnum revtype = TipoRevisaoEnum.getFromId(rs.getInt("revtype"));
                Map<String, String> valoresAlterados = Uteis.montarMapa(rs, "altura", "diametrocotovelo",
                        "diametrofemur", "diametropunho", "diametrotornozelo", "percentualgordura", "peso",
                        "pesogordura", "pesomuscular", "pesoosseo", "pesoresidual");
                Map<String, String> valoresAnteriores = null;
                Integer codigo = rs.getInt("codigo");
                if (revtype.equals(TipoRevisaoEnum.UPDATE) || revtype.equals(TipoRevisaoEnum.DELETE)) {
                    try (ResultSet rsAnterior = avaliacaoFisicaDao.createStatement(chave,
                            "select pa.codigo, pa.rev, pa.revtype, pa.altura, pa.diametrocotovelo, pa.diametrofemur, " +
                                    "pa.diametropunho, pa.diametrotornozelo, pa.percentualgordura, pa.peso, pa.pesogordura, " +
                                    "pa.pesomuscular, pa.pesoosseo, pa.pesoresidual " +
                                    " from pesoosseo_aud pa where codigo = " + codigo +
                                    " and rev < " + rs.getLong("rev") +
                                    " order by rev desc limit 1 ")) {
                        if (rsAnterior.next()) {
                            valoresAnteriores = Uteis.montarMapa(rsAnterior, "altura", "diametrocotovelo",
                                    "diametrofemur", "diametropunho", "diametrotornozelo", "percentualgordura", "peso",
                                    "pesogordura", "pesomuscular", "pesoosseo", "pesoresidual");
                        }
                    }
                }
                alteracoes.addAll(Uteis.compararMapas(valoresAnteriores, valoresAlterados));
            }
        }
        return alteracoes;
    }

    private List<AlteracoesTO> alteracoesRespostaAnamnese(String chave, Long momento, String username, Integer codAluno) throws Exception {
        List<AlteracoesTO> alteracoes;
        Map<String, List<String>> respostas;
        List<String> lista;
        try (ResultSet rs = avaliacaoFisicaDao.createStatement(chave, "select distinct c.\"timestamp\"/1000 as segundo, " +
                " ra.codigo, ra.rev, ra.revtype, ra.obs, ra.resposta, ra.perguntaanamnese_codigo " +
                " from respostacliente_aud ra " +
                " inner join customrevisionentity c on c.id = ra.rev " +
                " left join itemavaliacaofisica_aud ia on ra.itemavaliacao_codigo = ia.codigo" +
                " where c.username = '" + username +
                "' and c.\"timestamp\"/1000 between (" + momento + ")-2 and " + momento + " and ra.cliente_codigo = " + codAluno +
                " and ia.result <> 'PAR-Q'")) {
            alteracoes = new ArrayList<>();
            respostas = new HashMap<>();
            lista = new ArrayList<>();

            while (rs.next()) {
                if (!UteisValidacao.emptyString(rs.getString("resposta")) && !UteisValidacao.emptyString(rs.getString("obs"))) {
                    lista.add(" [" + rs.getString("perguntaanamnese_codigo") + " - " + rs.getString("resposta") + ", obs - " + rs.getString("obs") + "]");
                }
            }
        }

        respostas.put("listaRespostas", lista);
        if(!UteisValidacao.emptyList(lista)) {
            alteracoes.add(new AlteracoesTO("alteração de respostas ", "",
                    Uteis.listToString(respostas.get("listaRespostas"))));
        }

        return alteracoes;
    }

    @Override
    public List<LogTO> listarLogRespostaAnamnese(JSONObject filtros, PaginadorDTO paginadorDTO, Integer codAvaliacao) throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        final List<RespostaCliente> respostas;
        try {
            Map<String, Object> params = Collections.singletonMap("codAvaliacao", codAvaliacao);
            respostas = respostaClienteDao.findByParam(
                    ctx,
                    "select rc from RespostaCliente rc join rc.itemAvaliacao ia " +
                            "join ia.avaliacaoFisica a " +
                            "where a.codigo = :codAvaliacao",
                    params
            );
        } catch (Exception e) {
            throw new ServiceException(e);
        }

        if (respostas.isEmpty()) {
            return Collections.emptyList();
        }

        String chavesPrimarias = respostas.stream()
                .map(rc -> "'" + rc.getCodigo() + "'")
                .collect(Collectors.joining(","));
        String chavesSecundarias = respostas.stream()
                .map(rc -> "'" + rc.getPerguntaAnamnese().getCodigo() + "'")
                .collect(Collectors.joining(","));

        return logService.listarLog(
                EntidadeLogEnum.ANAMNESE,
                filtros,
                paginadorDTO,
                chavesPrimarias,
                chavesSecundarias,
                "ALTERAÇÃO RESPOSTA ANAMNESE"
        );
    }

    @Override
    public List<LogTO> listarLogConsolidadoAvaliacaoFisica(JSONObject filtros, PaginadorDTO paginadorDTO, Integer codAluno, Integer codAvaliacao) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            Long originalSize = paginadorDTO.getSize();
            List<LogTO> todos = logService.listarLog(
                    EntidadeLogEnum.AVALIACAOFISICA,
                    filtros,
                    paginadorDTO,
                    "'" + codAvaliacao + "'",
                    "",
                    ""
            );

            paginadorDTO.setSize(paginadorDTO.getSize() - todos.size());

            long totalAvaFisica = todos.isEmpty() ? 0 : paginadorDTO.getQuantidadeTotalElementos();
            todos.addAll(
                    listarLogAtividade(filtros, paginadorDTO, codAluno, codAvaliacao)
            );

            List<LogTO> logAnamnese = listarLogRespostaAnamnese(filtros, paginadorDTO, codAvaliacao);
            todos.addAll(logAnamnese);

            paginadorDTO.setSize(originalSize);
            paginadorDTO.setQuantidadeTotalElementos(
                    totalAvaFisica + logAnamnese.size()
            );

            SimpleDateFormat fmt = new SimpleDateFormat("dd/MM/yyyy - HH:mm:ss");
            Collections.sort(todos, (l1, l2) -> {
                try {
                    Date d1 = fmt.parse(l1.getDia());
                    Date d2 = fmt.parse(l2.getDia());
                    return d2.compareTo(d1);
                } catch (ParseException e) {
                    return 0;
                }
            });

            return todos;
        } catch (Exception e) {
            if (e instanceof ServiceException) throw (ServiceException) e;
            throw new ServiceException(e);
        }
    }

}
