/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.impl.treinoindependente;

import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.bean.pessoa.Email;
import br.com.pacto.bean.pessoa.Telefone;
import br.com.pacto.bean.pessoa.TipoTelefoneEnum;
import br.com.pacto.bean.professor.ProfessorSintetico;
import br.com.pacto.bean.usuario.StatusEnum;
import br.com.pacto.bean.usuario.TipoUsuarioEnum;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.bean.usuarioEmail.UsuarioEmail;
import br.com.pacto.email.UteisEmail;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.exception.ValidacaoException;
import br.com.pacto.service.intf.cliente.ClienteSinteticoService;
import br.com.pacto.service.intf.configuracoes.ConfiguracaoSistemaService;
import br.com.pacto.service.intf.empresa.EmpresaService;
import br.com.pacto.service.intf.perfil.PerfilService;
import br.com.pacto.service.intf.pessoa.PessoaService;
import br.com.pacto.service.intf.professor.ProfessorSinteticoService;
import br.com.pacto.service.intf.treinoindependente.TreinoIndependenteService;
import br.com.pacto.service.intf.usuario.UsuarioService;
import br.com.pacto.service.intf.usuarioEmail.UsuarioEmailService;
import br.com.pacto.util.ConfigsEmail;
import br.com.pacto.util.FileUtilities;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.ViewUtils;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.math.BigInteger;
import java.security.SecureRandom;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR>
 */
@Service
public class TreinoIndependenteServiceImpl implements TreinoIndependenteService {

    private static final String CHAVE_URL_MOBILE = "Tr3in0";
    @Autowired
    private UsuarioService usuarioService;
    @Autowired
    private PessoaService pessoaService;
    @Autowired
    private ClienteSinteticoService clienteService;
    @Autowired
    private EmpresaService empresaService;
    @Autowired
    private ProfessorSinteticoService professorService;
    @Autowired
    private ConfiguracaoSistemaService configService;
    @Autowired
    private ViewUtils viewUtils;
    @Autowired
    private PerfilService perfilService;
    @Autowired
    private UsuarioEmailService usuarioEmailService;

    
    public ClienteSintetico alterarAluno(final String ctx,
            ClienteSintetico cliente,
            String userName,
            String senha,
            Integer nivel,
            Integer professor, boolean usarAplicativo) throws ServiceException {
        try {
            List<Telefone> tels = new ArrayList<Telefone>(cliente.getPessoa().getTelefones());
            List<Email> emails = new ArrayList<Email>(cliente.getPessoa().getEmails());
            pessoaService.deletarEmailTelefonePessoa(ctx,cliente.getPessoa().getCodigo());
            cliente.setTelefones("");
            cliente.getPessoa().setTelefones(new ArrayList<Telefone>());
            cliente.setEmail("");
            cliente.getPessoa().setEmails(new ArrayList<Email>());
            for(Telefone tel : tels){
                if(tel.getTelefone() != null && !tel.getTelefone().isEmpty())
                    cliente.getPessoa().getTelefones().add(new Telefone(tel.getTelefone(), cliente.getPessoa()));
            }
            for(Email em : emails){
                if(em.getEmail() != null && !em.getEmail().isEmpty())
                    cliente.getPessoa().getEmails().add(new Email(em.getEmail(), cliente.getPessoa()));
            }
            cliente.getPessoa().setarDataNascimento();
            cliente.setPessoa(pessoaService.alterarPessoa(ctx, cliente.getPessoa()));
            ProfessorSintetico prof = new ProfessorSintetico();
            if(professor != null) {
                prof = professorService.obterPorId(ctx, professor);
            }else{
                throw new ServiceException("O professor deve ser informado!");
            }
            cliente.setProfessorSintetico(prof);
            cliente.getPessoa().setNome(cliente.getPessoa().getNome().toUpperCase());
            cliente.setSexo(cliente.getPessoa().getSexo());
            cliente.setNome(cliente.getPessoa().getNome());
            cliente.setDataNascimento(cliente.getPessoa().getDataNascimento());
            if(cliente.getNivelAluno() == null || !cliente.getNivelAluno().getCodigo().equals(nivel))
                clienteService.atualizarNivelAluno(ctx, nivel, cliente);
            Usuario usuario = usuarioService.consultarPorCliente(ctx, cliente.getCodigo());
            if (usuario != null) {
                if (senha != null && !senha.isEmpty()) {
                    usuario.setSenha(Uteis.encriptar(senha));
                } else {
                    senha = "";//ticket #22178
                }
                if (usarAplicativo) {
                    usuario.setNome(userName);
                    usuario.setUserName(userName);
                    enviarUsuarioMovelAluno(ctx, cliente, senha);
                    clienteService.adicionarUsuarioServicoDescobrir(ctx, userName);
                } else {
                    usuario.setNome(cliente.getMatricula().toString());
                    usuario.setUserName(cliente.getMatricula().toString());
                }
                usuarioService.alterar(ctx, usuario);
            }
            return clienteService.alterar(ctx, cliente);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }
    
    @Override
    public ClienteSintetico criarAluno(final String ctx,
            ClienteSintetico cliente,
            String userName,
            String senha,
            Integer nivel, 
            Integer professor, boolean usarAplicativo) throws ServiceException {
        try {
            if (usarAplicativo && (userName == null || userName.isEmpty())) {
                throw new Exception("username.obrigatorio");
            }
            try {
                if (usarAplicativo){
                    Uteis.validarEmail(userName);
                }
            } catch (Exception e) {
                throw new ValidacaoException(getViewUtils().getMensagem("login.usuarioInvalido"));
            }
            for (Email em : cliente.getPessoa().getEmails()) {
                try {
                    if (em.getEmail() != null && !em.getEmail().isEmpty()) {
                        Uteis.validarEmail(em.getEmail());
                    }
                } catch (Exception e) {
                    throw new Exception("email.invalido");
                }
            }
            if (usarAplicativo) {
                Usuario usuario = usuarioService.consultarPorUserName(ctx, userName);
                if (usuario != null
                        && ((usuario.getCliente() != null && usuario.getCliente().getCodigo() != null)
                        || (usuario.getProfessor() != null && usuario.getProfessor().getCodigo() != null && usuario.getProfessor().getCodigo() != 0))) {
                    throw new ValidacaoException(getViewUtils().getMensagem("username.em.uso") + " "
                            + (usuario.getCliente() == null ? usuario.getProfessor() == null ? ""
                            : usuario.getProfessor().getNome() : usuario.getCliente().getNome()));
                }

            }

            ProfessorSintetico prof = null;
            if (professor != null) {
                prof = professorService.obterPorId(ctx, professor);
            }
            cliente.getPessoa().setarDataNascimento();
            for (Email em : cliente.getPessoa().getEmails()) {
                em.setPessoa(cliente.getPessoa());
            }
            for (Telefone tel : cliente.getPessoa().getTelefones()) {
                tel.setPessoa(cliente.getPessoa());
            }
            pessoaService.inserirPessoa(ctx, cliente.getPessoa());
            cliente.setProfessorSintetico(prof);
            Empresa empresa = empresaService.obterEmpresaTreinoIndependente(ctx);
            cliente.setEmpresa(empresa.getCodigo());
            cliente.setDataMatricula(Calendario.hoje());
            cliente.getPessoa().setNome(cliente.getPessoa().getNome().toUpperCase());
            cliente.setSexo(cliente.getPessoa().getSexo());
            cliente.setNome(cliente.getPessoa().getNome());
            cliente.setCodigoPessoa(cliente.getPessoa().getCodigo());
            cliente.setDataNascimento(cliente.getPessoa().getDataNascimento());
            cliente = clienteService.inserir(ctx, cliente);
            cliente.setMatricula(cliente.getCodigo());
            cliente.setCodigoCliente(cliente.getCodigo());
            clienteService.atualizarNivelAluno(ctx, nivel, cliente);
            Usuario usuario = new Usuario();
            usuario.setEmpresaZW(empresa.getCodigo());
            usuario.setTipo(TipoUsuarioEnum.ALUNO);
            usuario.setCliente(cliente);
            usuario.setStatus(StatusEnum.ATIVO);
            SecureRandom random = new SecureRandom();
            if(senha == null || senha.isEmpty()) {
                senha = new BigInteger(130, random).toString(32);
                senha = senha.length() > 8 ? senha.substring(0,8) : senha;
            }

            usuario.setSenha(Uteis.encriptar(senha));
            if (usarAplicativo) {
                usuario.setNome(userName);
                usuario.setUserName(userName);
                usuarioService.inserir(ctx, usuario);
                enviarUsuarioMovelAluno(ctx, cliente, senha);
                clienteService.adicionarUsuarioServicoDescobrir(ctx, userName);
            } else {
                usuario.setNome(cliente.getMatricula().toString());
                usuario.setUserName(cliente.getMatricula().toString());
                usuarioService.inserir(ctx, usuario);
            }
            return clienteService.alterar(ctx, cliente);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public String  enviarUsuarioMovelAluno(String ctx, ClienteSintetico cliente, String senha) throws ServiceException {
        try {
            Usuario usuario = usuarioService.consultarPorCliente(ctx, cliente.getCodigo());
            StringBuilder texto = gerarCorpoEmailSenhaUsuarioMovel(ctx, cliente, usuario, senha,br.com.pacto.controller.json.base.SuperControle.getAppUrlEmail(ctx));
            ConfigsEmail cfgEmail = configService.obterConfiguracoes(ctx);
            List<String> emails = new ArrayList<String>();
            try {
                Uteis.validarEmail(usuario.getUserName());
                emails.add(usuario.getUserName());
            } catch (Exception e) {
            }

            List<Email> emailsObj = pessoaService.obterEmailsPorPessoa(ctx, cliente.getCodigoPessoa());
            for (Email email : emailsObj) {
                try {
                    if (!email.getEmail().equals(usuario.getUserName())) {
                        Uteis.validarEmail(email.getEmail());
                        emails.add(email.getEmail());
                    }
                } catch (Exception ignored) {
                }
            }
            String[] emArr = new String[emails.size()];
            UteisEmail.enviarEmail("Informações de acesso ao Pacto Treino", "",
                    texto.toString(), cfgEmail, emails.toArray(emArr));
            return "Email enviado!";
        } catch (Exception ex) {
            throw new ServiceException("aluno.criado.email.nao.enviado");
        }
    }

    @Override
    public ProfessorSintetico criarProfessor(final String ctx,
            ProfessorSintetico professor,
            String userName,
            String senha,
            Integer empresa, boolean criarUsuario,
            Integer perfil,
            TipoUsuarioEnum tipo) throws ServiceException {
        try {
            if(professor.getNome() == null || professor.getNome().isEmpty()){
                throw new Exception("nome.obrigatorio");
            }
            if(professor.getPessoa().getTelefones().size() == 0 ||
                    professor.getPessoa().getTelefones().get(0).getTelefone().isEmpty()) {
                throw new Exception("telefone.obrigatorio");
            }
            if( criarUsuario && (null == senha || senha.isEmpty())){
                throw new Exception("senha.usuario");
            }
            if( criarUsuario && (null == userName || userName.isEmpty())){
                throw new Exception("nome.usuario");
            }
            for (Email em : professor.getPessoa().getEmails()) {
                if (null == em.getEmail() || em.getEmail().isEmpty()) {
                    throw new Exception("email.obrigatorio");
                }
                if (!em.getEmail().contains("@")){
                    throw new Exception("email.invalido");
                }
                professor.setEmail(professor.getEmail() + ";" + em.getEmail());
                em.setPessoa(professor.getPessoa());
            }
            for (Telefone tel : professor.getPessoa().getTelefones()) {
                tel.setPessoa(professor.getPessoa());
            }
            pessoaService.inserirPessoa(ctx, professor.getPessoa());

            Empresa emp = empresaService.obterEmpresaTreinoIndependente(ctx);
            professor.setEmpresa(emp);
            professor.getPessoa().setNome(professor.getNome().toUpperCase());
            professor.setEmail(professor.getEmail().replaceFirst("null;", ""));
            if (professor.getEmail().contains(";")){
                String[] emails = professor.getEmail().split(";");
                for (String email: emails) {
                    professor.setEmail(email);
                    break;
                }
            }
            professor.setNome(professor.getPessoa().getNome());
            professor.setCodigoPessoa(professor.getPessoa().getCodigo());
            professor.setPersonaInterno(false);
            professor.setPersonal(false);
            professor.setProfessorTW(true);
            professor.setAtivo(true);
            professor.setCodigoColaborador(professor.getCodigo());
            professor.setCodigoPessoa(professor.getPessoa().getCodigo());
            professorService.inserir(ctx, professor);
            if (criarUsuario) {
                Usuario usuario = new Usuario();
                usuario.setEmpresaZW(empresa);
                usuario.setPerfil(perfilService.obterPorId(ctx, perfil));
                usuario.setTipo(tipo);
                usuario.setProfessor(professor);
                usuario.setStatus(StatusEnum.ATIVO);
                usuario.setNome(professor.getNome());
                usuario.setUserName(userName);
                usuario.setSenha(Uteis.encriptar(senha));
                usuarioService.inserir(ctx, usuario);
                clienteService.adicionarUsuarioServicoDescobrir(ctx, userName);
            }
            return professor;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    private StringBuilder gerarCorpoEmailSenhaUsuarioMovel(
            final String key,
            final ClienteSintetico cliente, Usuario usuario,
            String senha, String urlAppEmail) throws Exception {
        Empresa empresa = empresaService.obterEmpresaTreinoIndependente(key);
        InputStream is = getClass().getResourceAsStream("/br/com/pacto/util/resources/emailPactoTreinoDefault.txt");
        StringBuilder texto = new StringBuilder(new String(FileUtilities.obterBytesInputStream(is)));
        String nome = cliente.getNomeAbreviado();
        senha = senha == null ? "" : senha;
        String urlApp = "TREINO";
        switch(urlAppEmail){

            case "TREINO" : urlApp = "https://sistemapacto.app.link/apptreino";
                break;

            case "MINHA_ACADEMIA" : urlApp = "https://sistemspacto.app.link/minha-academia";
                break;

            case "MEU_BOX" : urlApp = "https://sistempacto.app.link/meu-box";
                break;

        }
        final String aux = texto.toString()
                .replaceAll("#NOME_ACADEMIA", empresa.getNome())
                .replaceAll("#NEW_USER", nome)
                .replaceAll("#USER_NAME", usuario.getNome().toLowerCase())
                .replaceAll("#SENHA", senha)
                .replaceAll("#URL_APP", urlApp);


        return new StringBuilder(aux);
    }

    public ViewUtils getViewUtils() {
        return viewUtils;
    }

    public void setViewUtils(ViewUtils viewUtils) {
        this.viewUtils = viewUtils;
    }

    @Override
    public ProfessorSintetico alterarProfessor(final String ctx,
                                             ProfessorSintetico professor,
                                             String userName,
                                             String senha,
                                             Integer empresa,
                                             boolean criarUsuario,
                                             Integer perfil,
                                             int tipo,
                                             Usuario usuarioProfessor) throws ServiceException {
        try {
            if (null == usuarioProfessor){
                usuarioProfessor = new Usuario();
            }
            professor.getPessoa().setarDataNascimento();
            usuarioProfessor.setEmpresaZW(empresa);
            usuarioProfessor.setPerfil(perfilService.obterPorId(ctx, perfil));
            usuarioProfessor.setTipo(TipoUsuarioEnum.getFromID(tipo));
            usuarioProfessor.setProfessor(professor);
            usuarioProfessor.setStatus(StatusEnum.ATIVO);
            usuarioProfessor.setNome(professor.getNome());
            usuarioProfessor.setUserName(userName);
            if (!UteisValidacao.emptyString(senha)){
                usuarioProfessor.setSenha(Uteis.encriptar(senha));
            }
            usuarioService.alterar(ctx, usuarioProfessor);
            clienteService.adicionarUsuarioServicoDescobrir(ctx, userName);
            return professorService.alterar(ctx, professor);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }
}
