/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.impl.musculo;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.atividade.Atividade;
import br.com.pacto.bean.atividade.AtividadeGrupoMuscular;
import br.com.pacto.bean.avaliacao.PerimetriaEnum;
import br.com.pacto.bean.musculo.GrupoMuscular;
import br.com.pacto.bean.musculo.GrupoMuscularResponseTO;
import br.com.pacto.bean.musculo.GrupoMuscularResumidoResponseTO;
import br.com.pacto.bean.musculo.GrupoMuscularTO;
import br.com.pacto.bean.musculo.Musculo;
import br.com.pacto.bean.musculo.MusculoGrupoMuscular;
import br.com.pacto.controller.json.musculo.FiltroGrupoMuscularJSON;
import br.com.pacto.controller.json.musculo.GrupoMuscularSimplesResponseTO;
import br.com.pacto.dao.intf.atividade.AtividadeDao;
import br.com.pacto.dao.intf.atividade.AtividadeGrupoMuscularDao;
import br.com.pacto.dao.intf.musculo.GrupoMuscularDao;
import br.com.pacto.dao.intf.musculo.MusculoDao;
import br.com.pacto.dao.intf.musculo.MusculoGrupoMuscularDao;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.exception.ValidacaoException;
import br.com.pacto.service.impl.notificacao.excecao.GrupoMuscularExcecoes;
import br.com.pacto.service.intf.Validacao;
import br.com.pacto.service.intf.musculo.GrupoMuscularService;
import br.com.pacto.util.ViewUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.ResultSet;
import java.util.*;
import java.util.stream.Collectors;

/**
 *
 * <AUTHOR>
 */
@Service
public class GrupoMuscularServiceImpl implements GrupoMuscularService {

    @Autowired
    private ViewUtils viewUtils;
    @Autowired
    private Validacao validacao;
    @Autowired
    private GrupoMuscularDao grupoMuscularDao;
    @Autowired
    private MusculoGrupoMuscularDao musculoGrupoMuscularDao;
    @Autowired
    private AtividadeGrupoMuscularDao atividadeGrupoMuscularDao;

    @Autowired
    private MusculoDao musculoDao;
    @Autowired
    private AtividadeDao atividadeDao;
    @Autowired
    private SessaoService sessaoService;

    public ViewUtils getViewUtils() {
        return viewUtils;
    }

    public void setViewUtils(ViewUtils viewUtils) {
        this.viewUtils = viewUtils;
    }

    public Validacao getValidacao() {
        return validacao;
    }

    public void setValidacao(Validacao validacao) {
        this.validacao = validacao;
    }

    public GrupoMuscularDao getGrupoMuscularDao() {
        return this.grupoMuscularDao;
    }

    public void setGrupoMuscularDao(GrupoMuscularDao grupoMuscularDao) {
        this.grupoMuscularDao = grupoMuscularDao;
    }

    public GrupoMuscular alterar(final String ctx, GrupoMuscular object) throws ServiceException, ValidacaoException {
        try {
            validarDados(ctx, object);
            return getGrupoMuscularDao().update(ctx, object);
        } catch (ValidacaoException ex) {
            throw ex;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public void excluir(final String ctx, GrupoMuscular object) throws ServiceException {
        try {
            getGrupoMuscularDao().delete(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public GrupoMuscular inserir(final String ctx, GrupoMuscular object) throws ServiceException, ValidacaoException {
        try {
            validarDados(ctx, object);
            return getGrupoMuscularDao().insert(ctx, object);
        } catch (ValidacaoException ex) {
            throw ex;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public GrupoMuscular obterObjetoPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException {
        try {
            return getGrupoMuscularDao().findObjectByParam(ctx, query, params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public GrupoMuscular obterPorId(final String ctx, Integer id) throws ServiceException {
        try {
            return getGrupoMuscularDao().findById(ctx, id);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<GrupoMuscular> obterPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException {
        try {
            return getGrupoMuscularDao().findByParam(ctx, query, params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<GrupoMuscular> obterPorParam(final String ctx, String query,
            Map<String, Object> params, int max, int index)
            throws ServiceException {
        try {
            return getGrupoMuscularDao().findByParam(ctx, query, params, max, index);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<GrupoMuscular> obterTodos(final String ctx) throws ServiceException {
        try {
            return getGrupoMuscularDao().findListByAttributes(ctx, null, null, "nome", 0);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public void validarDados(final String ctx, GrupoMuscular object) throws ValidacaoException {
        if (object.getNome() == null || object.getNome().trim().isEmpty()) {
            throw new ValidacaoException("validacao.nome");
        }
        if (getGrupoMuscularDao().exists(ctx, object, "nome")) {
            throw new ValidacaoException("cadastros.existente");
        }
    }

    public void povoarPerimetros(String ctx) throws ServiceException{
        try {
            Map<String, PerimetriaEnum[]> mapa = new HashMap<String, PerimetriaEnum[]>();
            mapa.put("ABDOMEN", new PerimetriaEnum[]{
                    PerimetriaEnum.CIRCUNFERENCIA_ABDOMINAL
            });
            mapa.put("ABDUTORES", new PerimetriaEnum[]{
                    PerimetriaEnum.COXA_PROXIMAL_ESQ,
                    PerimetriaEnum.COXA_PROXIMAL_DIR,
                    PerimetriaEnum.COXA_MEDIAL_ESQ,
                    PerimetriaEnum.COXA_MEDIAL_DIR,
                    PerimetriaEnum.COXA_DISTAL_DIR,
                    PerimetriaEnum.COXA_DISTAL_ESQ
            });

            mapa.put("ADUTORES", new PerimetriaEnum[]{
                    PerimetriaEnum.COXA_PROXIMAL_ESQ,
                    PerimetriaEnum.COXA_PROXIMAL_DIR,
                    PerimetriaEnum.COXA_MEDIAL_ESQ,
                    PerimetriaEnum.COXA_MEDIAL_DIR,
                    PerimetriaEnum.COXA_DISTAL_DIR,
                    PerimetriaEnum.COXA_DISTAL_ESQ
            });

            mapa.put("ANTEBRAÇO", new PerimetriaEnum[]{
                    PerimetriaEnum.ANTEBRACO_DIR,
                    PerimetriaEnum.ANTEBRACO_ESQ
            });

            mapa.put("ANTERIOR DE COXA", new PerimetriaEnum[]{
                    PerimetriaEnum.COXA_PROXIMAL_ESQ,
                    PerimetriaEnum.COXA_PROXIMAL_DIR,
                    PerimetriaEnum.COXA_MEDIAL_ESQ,
                    PerimetriaEnum.COXA_MEDIAL_DIR,
                    PerimetriaEnum.COXA_DISTAL_DIR,
                    PerimetriaEnum.COXA_DISTAL_ESQ
            });

            mapa.put("POSTERIOR DE COXA", new PerimetriaEnum[]{
                    PerimetriaEnum.COXA_PROXIMAL_ESQ,
                    PerimetriaEnum.COXA_PROXIMAL_DIR,
                    PerimetriaEnum.COXA_MEDIAL_ESQ,
                    PerimetriaEnum.COXA_MEDIAL_DIR,
                    PerimetriaEnum.COXA_DISTAL_DIR,
                    PerimetriaEnum.COXA_DISTAL_ESQ
            });

            mapa.put("QUADRICEPS", new PerimetriaEnum[]{
                    PerimetriaEnum.COXA_PROXIMAL_ESQ,
                    PerimetriaEnum.COXA_PROXIMAL_DIR,
                    PerimetriaEnum.COXA_MEDIAL_ESQ,
                    PerimetriaEnum.COXA_MEDIAL_DIR,
                    PerimetriaEnum.COXA_DISTAL_DIR,
                    PerimetriaEnum.COXA_DISTAL_ESQ
            });

            mapa.put("ANTEBRAÇO", new PerimetriaEnum[]{
                    PerimetriaEnum.ANTEBRACO_DIR,
                    PerimetriaEnum.ANTEBRACO_ESQ
            });

            mapa.put("BÍCEPS", new PerimetriaEnum[]{
                    PerimetriaEnum.BRACO_CONTRAIDO_DIR,
                    PerimetriaEnum.BRACO_CONTRAIDO_ESQ,
                    PerimetriaEnum.BRACO_RELAXADO_DIR,
                    PerimetriaEnum.BRACO_RELAXADO_ESQ
            });

            mapa.put("TRICEPS", new PerimetriaEnum[]{
                    PerimetriaEnum.BRACO_CONTRAIDO_DIR,
                    PerimetriaEnum.BRACO_CONTRAIDO_ESQ,
                    PerimetriaEnum.BRACO_RELAXADO_DIR,
                    PerimetriaEnum.BRACO_RELAXADO_ESQ
            });

            mapa.put("DELTÓIDE", new PerimetriaEnum[]{
                    PerimetriaEnum.OMBRO
            });

            mapa.put("DORSAL", new PerimetriaEnum[]{
                    PerimetriaEnum.OMBRO
            });

            mapa.put("GLÚTEO", new PerimetriaEnum[]{
                    PerimetriaEnum.GLUTEO
            });

            mapa.put("LOMBAR", new PerimetriaEnum[]{
                    PerimetriaEnum.CIRCUNFERENCIA_ABDOMINAL
            });

            mapa.put("MANGUITO", new PerimetriaEnum[]{
                    PerimetriaEnum.OMBRO
            });

            mapa.put("PANTURRILHA", new PerimetriaEnum[]{
                    PerimetriaEnum.PANTURRILHA_DIR,
                    PerimetriaEnum.PANTURRILHA_ESQ
            });

            mapa.put("TIBIAL ANTERIOR", new PerimetriaEnum[]{
                    PerimetriaEnum.PANTURRILHA_DIR,
                    PerimetriaEnum.PANTURRILHA_ESQ
            });

            mapa.put("PEITORAL", new PerimetriaEnum[]{
                    PerimetriaEnum.TORAX
            });


            for(String k : mapa.keySet()){
                Map<String, Object> params = new HashMap<String, Object>();
                params.put("name", k);
                List<GrupoMuscular> grupos = obterPorParam(ctx, "select obj from GrupoMuscular obj WHERE nome = :name", params);
                for(GrupoMuscular g : grupos){
                    g.setPerimetros(new HashSet<PerimetriaEnum>());
                    Set pers = new HashSet(Arrays.asList(mapa.get(k)));
                    g.setPerimetros(pers);
                    alterar(ctx, g);
                }
            }
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<GrupoMuscularResumidoResponseTO>consultarGruposMusculares(FiltroGrupoMuscularJSON filtroGrupoMuscularJSON, PaginadorDTO paginadorDTO)throws ServiceException{
        String ctx = sessaoService.getUsuarioAtual().getChave();

        if (!StringUtils.isBlank(paginadorDTO.getSort())) {
            paginadorDTO.setSort(paginadorDTO.getSort());
        } else {
            paginadorDTO.setSort("nome,ASC");
        }
        return getGrupoMuscularDao().consultarGruposMusculares(ctx,filtroGrupoMuscularJSON,paginadorDTO);
    }

    public GrupoMuscularResponseTO consultarGrupoMuscular(Integer id)throws ServiceException{
        String ctx = sessaoService.getUsuarioAtual().getChave();
        getGrupoMuscularDao().getCurrentSession(ctx).clear();
        GrupoMuscular grupoMuscular = obterPorId(ctx, id);
        if ((grupoMuscular == null) || (grupoMuscular.getCodigo() == null) || (grupoMuscular.getCodigo() <= 0)){
            throw new ServiceException(GrupoMuscularExcecoes.GRUPO_MUSCULAR_NAO_ENCONTRADO);
        }
        return new GrupoMuscularResponseTO(grupoMuscular);
    }

    public void excluir(Integer id) throws ServiceException{
        String ctx = sessaoService.getUsuarioAtual().getChave();
        GrupoMuscular grupoMuscular = null;
        try{
            grupoMuscular = getGrupoMuscularDao().findById(ctx, id);
        }catch (Exception e){
            throw new ServiceException(GrupoMuscularExcecoes.ERRO_BUSCAR_GRUPO_MUSCULAR, e);
        }
        if ((grupoMuscular == null) || (grupoMuscular.getCodigo() == null) || (grupoMuscular.getCodigo() <= 0)){
            throw new ServiceException(GrupoMuscularExcecoes.GRUPO_MUSCULAR_NAO_ENCONTRADO);
        }
        try{
            getGrupoMuscularDao().delete(ctx, grupoMuscular);
        }catch (Exception e){
            throw new ServiceException(GrupoMuscularExcecoes.ERRO_EXCLUIR_GRUPO_MUSCULAR, e);
        }

    }

    public GrupoMuscularResponseTO inserir(GrupoMuscularTO grupoMuscularTO) throws ServiceException{
        try{
            String ctx = sessaoService.getUsuarioAtual().getChave();
            GrupoMuscular grupoMuscular = new GrupoMuscular();
            povoarItensGrupoMuscular(ctx, grupoMuscular, grupoMuscularTO);
            validarGrupoMuscular(ctx, grupoMuscular);
            grupoMuscular = getGrupoMuscularDao().insert(ctx, grupoMuscular);

            return new GrupoMuscularResponseTO(grupoMuscular);
        }catch (Exception e){
            throw new ServiceException(GrupoMuscularExcecoes.ERRO_INCLUIR_GRUPO_MUSCULAR, e);
        }
    }

    private void validarGrupoMuscular(String ctx, GrupoMuscular grupoMuscular) throws ServiceException {
        if(getGrupoMuscularDao().exists(ctx, grupoMuscular, "nome")){
            throw new ServiceException(GrupoMuscularExcecoes.ERRO_GRUPOMUSCULO_JA_EXISTE);
        }
    }

    public GrupoMuscularResponseTO alterar(Integer id, GrupoMuscularTO grupoMuscularTO) throws ServiceException{
        try{
            String ctx = sessaoService.getUsuarioAtual().getChave();

            GrupoMuscular grupoMuscular = getGrupoMuscularDao().findById(ctx, id);
            if ((grupoMuscular == null) || (grupoMuscular.getCodigo() == null) || (grupoMuscular.getCodigo() <= 0)){
                throw new ServiceException(GrupoMuscularExcecoes.GRUPO_MUSCULAR_NAO_ENCONTRADO);
            }

            grupoMuscularTO.setId(id);
            povoarItensGrupoMuscular(ctx, grupoMuscular, grupoMuscularTO);
            validarGrupoMuscular(ctx, grupoMuscular);

            grupoMuscular = getGrupoMuscularDao().update(ctx, grupoMuscular);
            return new GrupoMuscularResponseTO(grupoMuscular);
        }catch (Exception e){
            throw new ServiceException(GrupoMuscularExcecoes.ERRO_ALTERAR_GRUPO_MUSCULAR, e);
        }
    }

    @Override
    public List<GrupoMuscularSimplesResponseTO> consultarTodosGruposMusculares() throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        try{
            List<GrupoMuscular> gruposMuscular = getGrupoMuscularDao().findAll(ctx);
            List<GrupoMuscularSimplesResponseTO> listaRet = new ArrayList<>();

            for(GrupoMuscular grupoMuscular : gruposMuscular) {
                listaRet.add(new GrupoMuscularSimplesResponseTO(grupoMuscular));
            }

            return listaRet;
        }catch (Exception e){
            throw new ServiceException(GrupoMuscularExcecoes.ERRO_BUSCAR_GRUPO_MUSCULAR, e);
        }
    }

    private void povoarItensGrupoMuscular(final String ctx, GrupoMuscular grupoMuscular, GrupoMuscularTO grupoMuscularTO) throws Exception {
        grupoMuscular.setNome(grupoMuscularTO.getNome().trim());
        if (grupoMuscular.getMusculos().size() > 0) {
            for (MusculoGrupoMuscular musculoGrupo : grupoMuscular.getMusculos()) {
                musculoGrupoMuscularDao.delete(ctx, musculoGrupo);
            }
        }
        grupoMuscular.getMusculos().clear();
        // adicionar os musculos.
        for (Integer idMusculo: grupoMuscularTO.getMusculoIds()){
            Musculo musculo = null;
            try{
                musculo = musculoDao.findById(ctx,idMusculo);
            }catch (Exception e){
                throw new ServiceException(GrupoMuscularExcecoes.ERRO_BUSCAR_GRUPOMUSCULO_MUSCULO, e);
            }
            if ((musculo == null) || (musculo.getCodigo() == null) || (musculo.getCodigo() <= 0)) {
                throw new ServiceException(GrupoMuscularExcecoes.GRUPOMUSCULO_MUSCULO_NAO_ENCONTRADO);
            }
            MusculoGrupoMuscular musculoGrupoMuscular = new MusculoGrupoMuscular();
            musculoGrupoMuscular.setGrupoMuscular(grupoMuscular);
            musculoGrupoMuscular.setMusculo(musculo);
            grupoMuscular.getMusculos().add(musculoGrupoMuscular);
        }
        // Adicionar as atividades
        if (grupoMuscular.getAtividades().size() > 0) {
            for (AtividadeGrupoMuscular atividadeGrupo : grupoMuscular.getAtividades()) {
                atividadeGrupoMuscularDao.delete(ctx, atividadeGrupo);
            }
        }
        grupoMuscular.getAtividades().clear();
        for (Integer idAtividade: grupoMuscularTO.getAtividadeIds()){
            Atividade atividade = null;
            try{
                atividade = atividadeDao.findById(ctx,idAtividade);
            }catch (Exception e){
                throw new ServiceException(GrupoMuscularExcecoes.ERRO_BUSCAR_GRUPOMUSCULO_ATIVIDADE, e);
            }
            if ((atividade == null) || (atividade.getCodigo() == null) || (atividade.getCodigo() <= 0)) {
                throw new ServiceException(GrupoMuscularExcecoes.GRUPOMUSCULO_ATIVIDADE_NAO_ENCONTRADO);
            }
            AtividadeGrupoMuscular atividadeGrupoMuscular = new AtividadeGrupoMuscular();
            atividadeGrupoMuscular.setGrupoMuscular(grupoMuscular);
            atividadeGrupoMuscular.setAtividade(atividade);
            grupoMuscular.getAtividades().add(atividadeGrupoMuscular);
        }

        // Adicionar as Perimetrias
        grupoMuscular.setPerimetros(new HashSet<PerimetriaEnum>());
        for (PerimetriaEnum perimetriaEnum: grupoMuscularTO.getPerimetros()){
            grupoMuscular.getPerimetros().add(perimetriaEnum);
        }

    }

    @Override
    public Map<Integer, List<GrupoMuscularResponseTO>> consultarGruposMuscularesPorAtividades(String ctx, List<Integer> codigosAtividades) throws ServiceException {
        Map<Integer, List<GrupoMuscularResponseTO>> resultado = new HashMap<>();

        if (codigosAtividades == null || codigosAtividades.isEmpty()) {
            return resultado;
        }

        String codigosAtv = codigosAtividades.stream().map(String::valueOf).collect(Collectors.joining(","));

        try {
            String query = "select agm.atividade_codigo, gm.codigo, gm.nome \n" +
                    "from grupomuscular gm \n" +
                    "inner join atividadegrupomuscular agm on agm.grupomuscular_codigo = gm.codigo \n" +
                    "where agm.atividade_codigo IN (" + codigosAtv + ")";

            try (ResultSet rs = grupoMuscularDao.createStatement(ctx, query)) {
                while (rs.next()) {
                    Integer atividadeCodigo = rs.getInt("atividade_codigo");
                    GrupoMuscularResponseTO grupoMuscular = new GrupoMuscularResponseTO();
                    grupoMuscular.setId(rs.getInt("codigo"));
                    grupoMuscular.setNome(rs.getString("nome"));

                    resultado.computeIfAbsent(atividadeCodigo, k -> new ArrayList<>()).add(grupoMuscular);
                }
            }
        } catch (Exception e) {
            Uteis.logar(e, GrupoMuscularServiceImpl.class);
        }

        return resultado;
    }

}