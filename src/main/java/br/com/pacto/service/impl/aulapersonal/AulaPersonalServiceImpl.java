/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.impl.aulapersonal;

import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.bean.gestaopersonal.AgrupamentoGestaoPersonal;
import br.com.pacto.bean.gestaopersonal.AlunoAulaPersonal;
import br.com.pacto.bean.gestaopersonal.AulaAberto;
import br.com.pacto.bean.gestaopersonal.AulaPersonal;
import br.com.pacto.bean.gestaopersonal.ConfiguracaoPersonalEmpresa;
import br.com.pacto.bean.gestaopersonal.CreditoPersonal;
import br.com.pacto.bean.gestaopersonal.ItemGestaoPersonal;
import br.com.pacto.bean.gestaopersonal.LiberacaoCheckIn;
import br.com.pacto.bean.gestaopersonal.MotivoBloqueioEnum;
import br.com.pacto.bean.gestaopersonal.TipoAgrupamentoPersonalEnum;
import br.com.pacto.bean.gestaopersonal.TipoItemGestaoPersonal;
import br.com.pacto.bean.gestaopersonal.TipoOperacaoPersonalEnum;
import br.com.pacto.bean.professor.ProfessorSintetico;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.dao.intf.aulapersonal.AlunoAulaPersonalDao;
import br.com.pacto.dao.intf.aulapersonal.AulaPersonalDao;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.intf.Validacao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.aulapersonal.AulaPersonalService;
import br.com.pacto.service.intf.aulapersonal.ConfiguracaoPersonalEmpresaService;
import br.com.pacto.service.intf.aulapersonal.LiberacaoCheckInService;
import br.com.pacto.service.intf.cliente.ClienteSinteticoService;
import br.com.pacto.service.intf.creditopersonal.CreditoPersonalService;
import br.com.pacto.service.intf.empresa.EmpresaService;
import br.com.pacto.service.intf.professor.ProfessorSinteticoService;
import br.com.pacto.service.intf.usuario.UsuarioService;
import br.com.pacto.util.UtilContext;
import br.com.pacto.util.ViewUtils;
import br.com.pacto.util.impl.Ordenacao;

import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import servicos.integracao.zw.IntegracaoCadastrosWSConsumer;

/**
 *
 * <AUTHOR>
 */
@Service
public class AulaPersonalServiceImpl implements AulaPersonalService {

    @Autowired
    private ViewUtils viewUtils;
    @Autowired
    private Validacao validacao;
    @Autowired
    private AulaPersonalDao aulaPersonalDao;
    @Autowired
    private AlunoAulaPersonalDao alunoAulaPersonalDao;
    @Autowired
    private CreditoPersonalService creditoPersonalService;
    @Autowired
    private ProfessorSinteticoService professorService;
    @Autowired
    private LiberacaoCheckInService liberacaoService;
    @Autowired
    private EmpresaService empresaService;
    @Autowired
    private ConfiguracaoPersonalEmpresaService cfgPersonalService;
    @Autowired
    private ClienteSinteticoService clienteService;
    @Autowired
    private UsuarioService usuarioService;

    public ViewUtils getViewUtils() {
        return viewUtils;
    }

    public void setViewUtils(ViewUtils viewUtils) {
        this.viewUtils = viewUtils;
    }

    public Validacao getValidacao() {
        return validacao;
    }

    public void setValidacao(Validacao validacao) {
        this.validacao = validacao;
    }

    public AulaPersonalDao getAulaPersonalDao() {
        return this.aulaPersonalDao;
    }

    public AlunoAulaPersonalDao getAlunoAulaPersonalDao() {
        return this.alunoAulaPersonalDao;
    }

    public void setAulaPersonalDao(AulaPersonalDao aulaPersonalDao) {
        this.aulaPersonalDao = aulaPersonalDao;
    }

    public AulaPersonal alterar(final String ctx, AulaPersonal object) throws ServiceException {
        try {
            object.setarHorasDate();
            return getAulaPersonalDao().update(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public void excluir(final String ctx, AulaPersonal object) throws ServiceException {
        try {
            getAulaPersonalDao().delete(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public AulaPersonal inserir(final String ctx, AulaPersonal object) throws ServiceException {
        try {
            object.setarHorasDate();
            return getAulaPersonalDao().insert(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public AulaPersonal obterObjetoPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException {
        try {
            return getAulaPersonalDao().findObjectByParam(ctx, query, params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public AulaPersonal obterPorId(final String ctx, Integer id) throws ServiceException {
        try {
            return getAulaPersonalDao().findById(ctx, id);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<AulaPersonal> obterPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException {
        try {
            return getAulaPersonalDao().findByParam(ctx, query, params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<AulaPersonal> obterPorParam(final String ctx, String query,
            Map<String, Object> params, int max, int index)
            throws ServiceException {
        try {
            return getAulaPersonalDao().findByParam(ctx, query, params, max, index);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public List<AulaPersonal> obterTodos(final String ctx) throws ServiceException {
        try {
            return getAulaPersonalDao().findAll(ctx);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<AulaPersonal> obterUltimasCincoAulasPersonal(final String ctx, final Integer personal) throws ServiceException {
        try {
            List<AulaPersonal> lista = getAulaPersonalDao().findListByAttributes(ctx,
                    new String[]{"professor.codigo"}, new Integer[]{personal}, "checkIn desc", 5);
            Collections.reverse(lista);
            return lista;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public AulaPersonal obterUltimaAulaEmAberto(String ctx, Integer personal) throws ServiceException {
        String query = "SELECT obj FROM AulaPersonal obj "
                + "WHERE obj.professor.codigo = :personal AND obj.checkOut is null ORDER BY obj.checkIn DESC ";
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("personal", personal);
        List<AulaPersonal> lista = obterPorParam(ctx, query, params, 1, 0);
        if (lista == null || lista.isEmpty()) {
            return new AulaPersonal();
        } else {
            AulaPersonal aulaPersonal = lista.get(0);
            aulaPersonal.setAlunos(obterAlunosPorAulaPersonal(ctx, aulaPersonal.getCodigo()));
            return aulaPersonal;
        }
    }

    @Override
    public List<AulaAberto> obterAulasEmAberto(String ctx, Integer empresa, Integer duracaoCredito, boolean contarCreditoPorAluno) throws ServiceException {
        Map<String, Object> params = new HashMap<String, Object>();
        StringBuilder query = new StringBuilder("SELECT obj FROM AulaPersonal obj WHERE obj.checkOut is null ");
        if (empresa != null) {
            query.append(" AND obj.empresaZW = :empresa ");
            params.put("empresa", empresa);
        }
        query.append(" ORDER BY obj.checkIn DESC ");
        List<AulaAberto> aulasAberto = new ArrayList<AulaAberto>();
        List<AulaPersonal> lista = obterPorParam(ctx, query.toString(), params);
        for (AulaPersonal aula : lista) {
            AulaAberto aulaAberto = new AulaAberto();
            aulaAberto.setAula(aula);
            aulaAberto.setCreditosGastos(0);
            aulaAberto.setDuracaoUmCredito(duracaoCredito);
            aulaAberto.setContarCreditoPorAluno(contarCreditoPorAluno);
            aulasAberto.add(aulaAberto);
        }
        return aulasAberto;
    }

    @Override
    public List<AlunoAulaPersonal> obterAlunosRecentes(String ctx, Integer personal) throws ServiceException {
        List<AlunoAulaPersonal> alunosRecentes = new ArrayList<AlunoAulaPersonal>();
        try {
            String query = "SELECT obj FROM AlunoAulaPersonal obj "
                    + "WHERE obj.aula.professor.codigo = :personal "
                    + "ORDER BY obj.aula.checkIn DESC ";

            Map<String, Object> params = new HashMap<String, Object>();
            params.put("personal", personal);
            List<AlunoAulaPersonal> alunosAula = getAlunoAulaPersonalDao().findByParam(ctx, query, params, 6, 0);

            for (AlunoAulaPersonal aluno : alunosAula) {
                if (!alunoJaAdicionado(aluno, alunosRecentes)) {
                    alunosRecentes.add(aluno);
                }
            }
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
        return alunosRecentes;
    }

    public boolean alunoJaAdicionado(AlunoAulaPersonal aluno, List<AlunoAulaPersonal> alunosRecentes) {
        for (AlunoAulaPersonal aap : alunosRecentes) {
            if ((aap.getCodigoCliente() != null && aluno.getCodigoCliente() != null
                    && aluno.getCodigoCliente().equals(aap.getCodigoCliente()))
                    || (aap.getCodigoColaborador() != null && aluno.getCodigoColaborador() != null
                    && aluno.getCodigoColaborador().equals(aap.getCodigoColaborador()))) {
                return true;
            }
        }
        return false;
    }

    @Override
    public void checkOutAutomatico(final String key) throws ServiceException {
        try {
            List<Empresa> empresas = empresaService.obterTodos(key);
            for (Empresa empresa : empresas) {
                ConfiguracaoPersonalEmpresa cfgPersonal = cfgPersonalService.obterPorEmpresa(key, empresa.getCodZW());
                if (cfgPersonal != null && cfgPersonal.getTempoCheckOutAutomatico() > 0) {
                    List<AulaAberto> aulasAberto = obterAulasEmAberto(key, empresa.getCodZW(), cfgPersonal.getDuracaoCredito(), cfgPersonal.getConsumirCreditoPorAluno());
                    for (AulaAberto aula : aulasAberto) {
                        int minutosEntreDatas = (int) Uteis.minutosEntreDatas(aula.getAula().getCheckIn(), Calendario.hoje());
                        if (minutosEntreDatas >= cfgPersonal.getTempoCheckOutAutomatico()) {
                            AulaPersonal aulaFechar = obterPorId(key, aula.getAula().getCodigo());
                            aulaFechar.setCheckOut(Uteis.somarCampoData(aula.getAula().getCheckIn(), Calendar.MINUTE, cfgPersonal.getTempoCheckOutAutomatico()));
                            aulaFechar.setCheckOutAutomatico(true);
                            aulaFechar = alterar(key, aulaFechar);
                            realizarCheckOut(key, aulaFechar, cfgPersonal.getDuracaoCredito(), cfgPersonal.getConsumirCreditoPorAluno(), true);
                            Uteis.logar(null, "CheckOut Automatico para aula -> " + aula.getAula().getCodigo() + " " + key);
                        }
                    }
                }
            }
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public void realizarCheckOut(final String key, final AulaPersonal aula,
            final Integer duracaoCredito, boolean contarCreditoPorAluno, boolean automatico) throws ServiceException {
        try {
            aula.setCheckOutAutomatico(automatico);
            Integer creditosAtuais = creditoPersonalService.obterSaldoAtualPersonal(key, aula.getProfessor().getCodigo(), Calendario.hoje());
            int minutosEntreDatas = (int) Uteis.minutosEntreDatas(aula.getCheckIn(), aula.getCheckOut()) == 0
                    ? 1 : (int) Uteis.minutosEntreDatas(aula.getCheckIn(), aula.getCheckOut());

            Integer creditosAula = minutosEntreDatas / duracaoCredito;
            Integer resto = minutosEntreDatas % duracaoCredito;
            creditosAula = resto > 0 ? creditosAula + 1 : creditosAula;
            if (contarCreditoPorAluno) {
                Integer nrAlunos = aula.getAlunos().isEmpty() ? 1 : 0;
                for (AlunoAulaPersonal aln : aula.getAlunos()) {
                    if (aln.getCodigoCliente() != null) {
                        nrAlunos++;
                    } else if (aln.getCodigoColaborador() != null && aln.getColaborador().getCobrarCreditos()) {
                        nrAlunos++;
                    }
                }
                creditosAula = creditosAula * nrAlunos;
            }
            CreditoPersonal creditosGastos = new CreditoPersonal();
            creditosGastos.setSaldoPersonal(creditosAtuais - creditosAula);
            creditosGastos.setAulaPersonal(aula);
            creditosGastos.setProfessor(aula.getProfessor());
            creditosGastos.setEmpresaZW(aula.getEmpresaZW());
            creditosGastos.setDataLancamento(Calendario.hoje());
            creditosGastos.setTipoOperacao(TipoOperacaoPersonalEnum.GASTO_CREDITOS);
            creditosGastos.setUnidades(creditosAula);
            creditoPersonalService.inserir(key, creditosGastos);
            //atualizar saldo ZW
            final String url = Aplicacao.getProp(key, Aplicacao.urlZillyonWebInteg);
            IntegracaoCadastrosWSConsumer integracaoWS = (IntegracaoCadastrosWSConsumer) UtilContext.getBean(IntegracaoCadastrosWSConsumer.class);
            integracaoWS.atualizarEmAtendimento(url, key, aula.getProfessor().getCodigoColaborador(), false);
            integracaoWS.atualizarSaldoPersonal(url, key, aula.getProfessor().getCodigoColaborador(), creditosGastos.getSaldoPersonal());
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    public AulaPersonal editarCheckIn(final String key, AulaPersonal aula) throws ServiceException {
        try {
            return alterar(key, aula);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public AulaPersonal realizarCheckIn(final String key, Usuario usuario,
            List<AlunoAulaPersonal> alunosAdicionados, ProfessorSintetico personal,
            MotivoBloqueioEnum motivoBloqueio, Usuario usuarioforcou, Date checkIn) throws ServiceException {
        try {
            if (usuario.getEmpresaLogada() == null) {
                throw new Exception("conexaoZillyonWebCfgEmpresa");
            }
            if (usuario.getEmpresaLogada().isObrigatorioAssociarAlunoAoCheckIn()
                    && (alunosAdicionados == null || alunosAdicionados.isEmpty())) {
                throw new Exception("obrigatorio.alunoObrigatorioCheckIn");
            }
            AulaPersonal aula = new AulaPersonal();
            aula.setEmpresaZW(usuario.getEmpresaZW());
            aula.setAlunos(new ArrayList<AlunoAulaPersonal>());
            for (AlunoAulaPersonal aln : alunosAdicionados) {
                if (aln.getCodigoCliente() != null) {
                    aula.getAlunos().add(new AlunoAulaPersonal(aula, aln.getCliente()));
                } else if (aln.getCodigoColaborador() != null) {
                    aula.getAlunos().add(new AlunoAulaPersonal(aula, aln.getColaborador()));
                }
            }
            aula.setCheckIn(checkIn);
            aula.setProfessor(personal);
            aula = inserir(key, aula);

            final String url = Aplicacao.getProp(key, Aplicacao.urlZillyonWebInteg);
            IntegracaoCadastrosWSConsumer integracaoWS = (IntegracaoCadastrosWSConsumer) UtilContext.getBean(IntegracaoCadastrosWSConsumer.class);
            integracaoWS.atualizarEmAtendimento(url, key, aula.getProfessor().getCodigoColaborador(), false);

            if (motivoBloqueio != null) {
                LiberacaoCheckIn liberacao = new LiberacaoCheckIn();
                liberacao.setMotivo(motivoBloqueio);
                liberacao.setAulaLiberada(aula);
                liberacao.setUsuarioLiberou_codigo(usuarioforcou.getCodigo());
                liberacao.setDataLiberacao(Uteis.somarCampoData(aula.getCheckIn(), Calendar.SECOND, -1));
                liberacao.setPersonal(aula.getProfessor());
                LiberacaoCheckInService ls = (LiberacaoCheckInService) UtilContext.getBean(LiberacaoCheckInService.class);
                ls.inserir(key, liberacao);
            }
            return aula;
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public List<AgrupamentoGestaoPersonal> montarGestao(final String ctx, final Date inicio,
            final Date fim, final Usuario usuario, final TipoAgrupamentoPersonalEnum tipo) throws ServiceException {
        try {
            List<ItemGestaoPersonal> listaItem = new ArrayList<ItemGestaoPersonal>();
            String queryAula = "SELECT obj FROM AulaPersonal obj "
                    + "WHERE obj.empresaZW = :empresa "
                    + "AND obj.professor is not null "
                    + "AND obj.checkIn >= :inicio AND obj.checkIn <= :fim "
                    + "ORDER BY obj.checkIn DESC ";
            Map<String, Object> params = new HashMap<String, Object>();
            params.put("empresa", usuario.getEmpresaLogada().getCodigo());
            params.put("inicio", Calendario.getDataComHoraZerada(inicio));
            params.put("fim", Calendario.getDataComHora(fim, "23:59:59"));
            List<AulaPersonal> aulas = obterPorParam(ctx, queryAula, params);
            for (AulaPersonal aula : aulas) {
                ItemGestaoPersonal item = new ItemGestaoPersonal();
                item.setAula(aula);
                item.setTipo(TipoItemGestaoPersonal.CHECKIN);
                item.setData(aula.getCheckIn());
                item.setPersonal(aula.getProfessor());

                List<AlunoAulaPersonal> alunos = obterAlunosPorAulaPersonal(ctx, aula.getCodigo());
                aula.setAlunos(alunos);

                listaItem.add(item);
//                if (aula.getCheckOut() != null) {
//                    ItemGestaoPersonal itemConsumo = new ItemGestaoPersonal();
//                    itemConsumo.setAula(aula);
//                    itemConsumo.setTipo(TipoItemGestaoPersonal.CHECKOUT);
//                    itemConsumo.setData(aula.getCheckOut());
//                    itemConsumo.setPersonal(aula.getProfessor());
//                    listaItem.add(itemConsumo);
//                }
            }
            String queryCredito = "SELECT obj FROM CreditoPersonal obj "
                    + "WHERE obj.empresaZW = :empresa "
                    + "AND obj.dataLancamento >= :inicio AND obj.dataLancamento <= :fim "
                    + "ORDER BY obj.dataLancamento DESC ";
            List<CreditoPersonal> creditos = creditoPersonalService.obterPorParam(ctx, queryCredito, params);
            for (CreditoPersonal credito : creditos) {
                ItemGestaoPersonal item = new ItemGestaoPersonal();
                item.setCredito(credito);
                item.setPersonal(credito.getProfessor());
                item.setData(credito.getDataLancamento());
                item.setUnidades(credito.getUnidades());
                if (credito.getCompra()) {
                    item.setTipo(TipoItemGestaoPersonal.PAGAMENTO);
                } else if (credito.isPrePago()) {
                    item.setTipo(TipoItemGestaoPersonal.CONS_PRE_PAGO);
                } else {
                    item.setTipo(TipoItemGestaoPersonal.CONS_POS_PAGO);
                }
                listaItem.add(item);
            }
            String queryLiberacao = "SELECT obj FROM LiberacaoCheckIn obj "
                    + "WHERE obj.aulaLiberada.empresaZW = :empresa "
                    + "AND obj.dataLiberacao >= :inicio AND obj.dataLiberacao <= :fim "
                    + "ORDER BY obj.dataLiberacao DESC ";
            List<LiberacaoCheckIn> liberacoes = liberacaoService.obterPorParam(ctx, queryLiberacao, params);
            for (LiberacaoCheckIn liberacao : liberacoes) {
                ItemGestaoPersonal item = new ItemGestaoPersonal();
                item.setLiberacao(liberacao);
                item.setPersonal(liberacao.getPersonal());
                item.setData(liberacao.getDataLiberacao());
                item.setTipo(TipoItemGestaoPersonal.LIBERACAO_CHECK_IN);
                listaItem.add(item);
            }
            List<AgrupamentoGestaoPersonal> listaAgrupamentos = new ArrayList<AgrupamentoGestaoPersonal>();
            Ordenacao.ordenarLista(listaItem, "data");
            Collections.reverse(listaItem);
            switch (tipo) {
                case PERIODO:
                    Map<Date, List<ItemGestaoPersonal>> mapaData = new HashMap<Date, List<ItemGestaoPersonal>>();
                    for (ItemGestaoPersonal item : listaItem) {
                        List<ItemGestaoPersonal> lista = mapaData.get(Calendario.getDataComHoraZerada(item.getData()));
                        if (lista == null) {
                            lista = new ArrayList<ItemGestaoPersonal>();
                            mapaData.put(Calendario.getDataComHoraZerada(item.getData()), lista);
                        }
                        lista.add(item);
                    }
                    for (Date data : mapaData.keySet()) {
                        AgrupamentoGestaoPersonal agrupamento = new AgrupamentoGestaoPersonal();
                        agrupamento.setDia(data);
                        agrupamento.setItens(mapaData.get(data));
                    }
                    break;
                case PERSONAL:
                    Map<Integer, List<ItemGestaoPersonal>> mapaPersonal = new HashMap<Integer, List<ItemGestaoPersonal>>();
                    for (ItemGestaoPersonal item : listaItem) {
                        List<ItemGestaoPersonal> lista = mapaPersonal.get(item.getPersonal().getCodigo());
                        if (lista == null) {
                            lista = new ArrayList<ItemGestaoPersonal>();
                            mapaPersonal.put(item.getPersonal().getCodigo(), lista);
                        }
                        lista.add(item);
                    }
                    for (Integer cod : mapaPersonal.keySet()) {
                        AgrupamentoGestaoPersonal agrupamento = new AgrupamentoGestaoPersonal();
                        agrupamento.setPersonal(professorService.obterPorId(ctx, cod));
                        Integer creditosAtuais = creditoPersonalService.obterSaldoAtualPersonal(ctx, cod, Calendario.hoje());
                        agrupamento.setSaldo(creditosAtuais);
                        agrupamento.setItens(mapaPersonal.get(cod));
                        listaAgrupamentos.add(agrupamento);
                    }
                    break;
            }
            return listaAgrupamentos;
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public MotivoBloqueioEnum validarPrecisaAutorizacaoCheckIn(String ctx, ProfessorSintetico personal, Usuario user) throws ServiceException {
        try {
            if (!personal.getPosPago()) {
                Integer saldo = creditoPersonalService.obterSaldoAtualPersonal(ctx, personal.getCodigo(), Calendario.hoje());
                if (saldo <= 0) {
                    return MotivoBloqueioEnum.PERSONAL_SEM_CREDITO;
                }
            }
            if (user.getEmpresaLogada() != null && user.getEmpresaLogada().getDiasBloqueioParcelaEmAberto() > 0) {
                final String url = Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebInteg);
                IntegracaoCadastrosWSConsumer integracaoWS = (IntegracaoCadastrosWSConsumer) UtilContext.getBean(IntegracaoCadastrosWSConsumer.class);
                Integer diasParcelaEmAberto = integracaoWS.obterDiasParcelaEmAberto(url, ctx, personal.getCodigoPessoa());
                if (diasParcelaEmAberto > user.getEmpresaLogada().getDiasBloqueioParcelaEmAberto()) {
                    return MotivoBloqueioEnum.PARCELA_EM_ABERTO;
                }
            }
            return null;
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public Integer fecharCreditosPersonal(final String ctx, final ProfessorSintetico personal, final Usuario usuario) throws ServiceException {
        try {
            Integer creditosAtuais = creditoPersonalService.obterSaldoAtualPersonal(ctx, personal.getCodigo(), Calendario.hoje());
            if (creditosAtuais < 0) {
                final String url = Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebInteg);
                IntegracaoCadastrosWSConsumer integracaoWS = (IntegracaoCadastrosWSConsumer) UtilContext.getBean(IntegracaoCadastrosWSConsumer.class);
                String result = integracaoWS.gerarVendaCredito(url, ctx, personal.getCodigoColaborador(), usuario.getUsuarioZW(), creditosAtuais);
                if (result.startsWith("ok")) {
                    String[] split = result.split("\\|");
                    creditoPersonalService.inserirCreditos(ctx, personal.getCodigoColaborador(),
                            creditosAtuais, null, Integer.valueOf(split[1]),
                            TipoOperacaoPersonalEnum.FECHAMENTO_CREDITOS, Calendario.hoje());
                } else {
                    throw new ServiceException(result);
                }
            }
            return creditosAtuais;
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }

    }

    @Override
    public void enviarEmailPersonal(final String ctx, final ProfessorSintetico personal,
            final List<AgrupamentoGestaoPersonal> agrupamentos, Date inicio, Date fim)
            throws ServiceException {
        try {
            List<ItemGestaoPersonal> itens = new ArrayList<ItemGestaoPersonal>();
            for (AgrupamentoGestaoPersonal agp : agrupamentos) {
                if (agp.getPersonal().getCodigo().equals(personal.getCodigo())) {
                    itens.addAll(agp.getItens());
                    break;
                }
            }
            ViewUtils vu = (ViewUtils) UtilContext.getBean(ViewUtils.class);
            StringBuilder corpo = new StringBuilder();
            Integer creditosAtuais = creditoPersonalService.obterSaldoAtualPersonal(ctx, personal.getCodigo(), Calendario.hoje());

            corpo.append("<html><body style=\"font-size:11px;font-family:Arial,Verdana,sans-serif;color:#000;\">");
            corpo.append("<br/> Personal: ").append(personal.getNome());
            corpo.append("<br/> Saldo atual: ").append(creditosAtuais);
            if (inicio != null) {
                corpo.append("<br/> Início: ").append(Uteis.getDataAplicandoFormatacao(inicio, "dd/MM/yyyy"));
            }
            if (fim != null) {
                corpo.append("<br/> Fim: ").append(Uteis.getDataAplicandoFormatacao(fim, "dd/MM/yyyy"));
            }


            corpo.append("<table>");
            for (ItemGestaoPersonal item : itens) {
                corpo.append("<tr>");
                corpo.append("<td>").append(vu.getLabel(item.getTipo().name())).append("</td>");
                if (item.getCheckIn()) {
                    corpo.append("<td> - ").append(item.getAula().getNomesAlunos()).append("</td>");
                }
                if (item.getPagamento()) {
                    corpo.append("<td> - Créditos adquiridos : ").append(item.getCredito().getUnidades()).append(" - Recibo: ")
                            .append(item.getCredito().getReciboZW()).append("</td>");
                }
                if (item.getConsumo()) {
                    corpo.append("<td> - Créditos gastos : ").append(item.getCredito().getUnidades()).append("</td>");
                }
                if (item.getLiberacao() != null) {
                    Usuario usu = usuarioService.obterPorId(ctx, item.getLiberacao().getUsuarioLiberou_codigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    corpo.append("<td> - Usuário que liberou : ").append(usu.getNome()).append("</td>");
                }
                corpo.append("<td> - ").append(item.getDataApresentar()).append("</td>");
                corpo.append("</tr>");
            }
            corpo.append("</table>");
            corpo.append("</body>");
            corpo.append("</html>");

            final String url = Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebInteg);
            IntegracaoCadastrosWSConsumer integracaoWS = (IntegracaoCadastrosWSConsumer) UtilContext.getBean(IntegracaoCadastrosWSConsumer.class);
            String result = integracaoWS.enviarEmailColaborador(url, ctx, personal.getCodigoColaborador(), corpo.toString());
            if (!result.equals("ok")) {
                throw new ServiceException(result);
            }
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public AulaPersonal obterAulaAbertoAluno(final String key, final AlunoAulaPersonal aluno) throws ServiceException {
        try {
            HashMap<String, Object> p = new HashMap<String, Object>();
            StringBuilder query = new StringBuilder("select obj from AlunoAulaPersonal obj ");
            query.append("where obj.aula.checkOut is null");
            if (aluno.getCodigoCliente() == null) {
                query.append(" and obj.colaborador.codigo = :colaborador ");
                p.put("colaborador", aluno.getCodigoColaborador());
            } else {
                query.append(" and obj.cliente.codigo = :cliente ");
                p.put("cliente", aluno.getCodigoCliente());
            }
            AlunoAulaPersonal alunoAula = alunoAulaPersonalDao.findObjectByParam(key, query.toString(), p);
            return alunoAula == null ? null : alunoAula.getAula();
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public void ajustarSaldoCreditos(final String key, final AulaPersonal aula, boolean excluir, final Integer duracaoCredito,
            boolean contarCreditoPorAluno, Usuario usuario) throws ServiceException {
        try {
            CreditoPersonal creditoAtualizar = creditoPersonalService.obterCredito(key, aula);
            aula.setarHorasDate();
            if (creditoAtualizar == null && excluir) {
                LiberacaoCheckIn liberacao = liberacaoService.obterLiberacao(key, aula);
                if (liberacao != null) {
                    liberacaoService.excluir(key, liberacao);
                }
                excluir(key, aula);
            } else if (creditoAtualizar == null && !excluir) {
                editarAlunoAula(key, aula, usuario);
                if (aula.getCheckOut() != null) {
                    realizarCheckOut(key, aula, duracaoCredito, contarCreditoPorAluno, false);
                }
            } else if (aula.getCheckOut() != null) {
                if(usuario != null){
                    editarAlunoAula(key, aula, usuario);
                }

                Integer saldo = creditoPersonalService.obterSaldoAtualPersonal(key,
                        aula.getProfessor().getCodigo(), creditoAtualizar == null ? Calendario.hoje() : creditoAtualizar.getDataLancamento());
                List<CreditoPersonal> lista = creditoPersonalService.obterListaSaldo(key,
                        aula.getProfessor().getCodigo(),
                        creditoAtualizar == null ? Calendario.hoje() : creditoAtualizar.getDataLancamento());
                if (excluir) {
                    LiberacaoCheckIn liberacao = liberacaoService.obterLiberacao(key, aula);
                    if (liberacao != null) {
                        liberacaoService.excluir(key, liberacao);
                    }
                    creditoPersonalService.excluir(key, creditoAtualizar);
                    excluir(key, aula);
                } else {
                    int minutosEntreDatas = (int) Uteis.minutosEntreDatas(aula.getCheckIn(), aula.getCheckOut()) == 0
                            ? 1 : (int) Uteis.minutosEntreDatas(aula.getCheckIn(), aula.getCheckOut());
                    Integer creditosAula = minutosEntreDatas / duracaoCredito;
                    Integer resto = minutosEntreDatas % duracaoCredito;
                    creditosAula = resto > 0 ? creditosAula + 1 : creditosAula;
                    if (contarCreditoPorAluno) {
                        Integer nrAlunos = aula.getAlunos().isEmpty() ? 1 : aula.getAlunos().size();
                        creditosAula = creditosAula * nrAlunos;
                    }
                    creditoAtualizar.setUnidades(creditosAula);
                    saldo = saldo - creditoAtualizar.getUnidades();
                    creditoAtualizar.setSaldoPersonal(saldo);
                    creditoPersonalService.alterar(key, creditoAtualizar);
                }
                for (CreditoPersonal credito : lista) {
                    if (credito.getTipoOperacao().equals(TipoOperacaoPersonalEnum.GASTO_CREDITOS)) {
                        saldo = saldo - credito.getUnidades();
                    } else {
                        saldo = saldo + credito.getUnidades();
                    }
                    credito.setSaldoPersonal(saldo);
                    creditoPersonalService.alterar(key, credito);
                }
            }


        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }
    
    public void editarAlunoAula(final String key, AulaPersonal aula, Usuario usuario) throws ServiceException {
        try {
            if (usuario.getEmpresaLogada() == null) {
                throw new Exception("conexaoZillyonWebCfgEmpresa");
            }
            if (usuario.getEmpresaLogada().isObrigatorioAssociarAlunoAoCheckIn()
                    && (aula.getAlunos() == null || aula.getAlunos().isEmpty())) {
                throw new Exception("obrigatorio.alunoObrigatorioCheckIn");
            }
            List<AlunoAulaPersonal> lista = new ArrayList<AlunoAulaPersonal>();
            for (AlunoAulaPersonal aluno : aula.getAlunos()) {
                if (aluno.getCodigoCliente() != null) {
                    lista.add(new AlunoAulaPersonal(aluno.getAula(), aluno.getCliente()));
                } else if (aluno.getCodigoColaborador() != null) {
                    lista.add(new AlunoAulaPersonal(aluno.getAula(), aluno.getColaborador()));
                }
            }
            getAlunoAulaPersonalDao().deleteComParam(key, new String[]{"aula.codigo"}, new Object[]{aula.getCodigo()});
            aula.getAlunos().clear();
            for (AlunoAulaPersonal aluno : lista) {
                aluno = getAlunoAulaPersonalDao().insert(key, aluno);
            }
            aula.setAlunos(lista);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public List<AlunoAulaPersonal> completeParaCheckIn(final String key, final Integer empresaZW, final String param) throws ServiceException {
        try {
            List<AlunoAulaPersonal> results = new ArrayList<AlunoAulaPersonal>();
            List<ClienteSintetico> clientes = clienteService.consultarPorMatriculaOuNome(key, empresaZW, null, param, 10);
            int nrAdd = 1;
            for (ClienteSintetico cliente : clientes) {
                if (nrAdd > 10) {
                    break;
                }
                nrAdd++;
                results.add(new AlunoAulaPersonal(cliente));
            }
            nrAdd = 1;
            List<ProfessorSintetico> colaboradores = professorService.consultarPorCodigoOuNome(key, empresaZW, param);
            for (ProfessorSintetico colaborador : colaboradores) {
                if (nrAdd > 10) {
                    break;
                }
                nrAdd++;
                results.add(new AlunoAulaPersonal(colaborador));
            }
            return results;
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public List<AlunoAulaPersonal> obterAlunosPorAulaPersonal(final String ctx, Integer codigo) throws ServiceException {
        String s = "SELECT obj FROM AlunoAulaPersonal obj "
                + "WHERE obj.aula.codigo = :_codigo ";
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("_codigo", codigo);
        try {
            return getAlunoAulaPersonalDao().findByParam(ctx, s, params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public void normalizarCreditos(final String ctx, Integer professor, String dataLimite) throws Exception{
        ProfessorSintetico professorSintetico = professorService.obterPorId(ctx, professor);
        StringBuilder sql = new StringBuilder();
        sql.append(" select a.codigo, c.codigo as credito from aulapersonal a ");
        sql.append(" left join creditopersonal c on c.aulapersonal_codigo = a.codigo");
        sql.append(" where a.professor_codigo = ").append(professor).append(" and checkin > '").append(dataLimite).append("'");
        sql.append(" order by checkin desc");
        Integer creditosAtuais = creditoPersonalService.obterSaldoAtualPersonal(ctx, professor, Calendario.hoje());
        try (ResultSet rs = getAlunoAulaPersonalDao().createStatement(ctx, sql.toString())) {
            while (rs.next()) {
                int credito = rs.getInt("credito");
                if (credito > 0) {
                    continue;
                }
                AulaPersonal aulaFechar = obterPorId(ctx, rs.getInt("codigo"));
                creditosAtuais = creditosAtuais - 1;
                CreditoPersonal creditosGastos = new CreditoPersonal();
                creditosGastos.setSaldoPersonal(creditosAtuais);
                creditosGastos.setAulaPersonal(aulaFechar);
                creditosGastos.setProfessor(aulaFechar.getProfessor());
                creditosGastos.setEmpresaZW(aulaFechar.getEmpresaZW());
                creditosGastos.setDataLancamento(aulaFechar.getCheckOut());
                creditosGastos.setTipoOperacao(TipoOperacaoPersonalEnum.GASTO_CREDITOS);
                creditosGastos.setUnidades(1);
                CreditoPersonal inserir = creditoPersonalService.inserir(ctx, creditosGastos);
            }
        }
        //atualizar saldo ZW
        final String url = Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebInteg);
        IntegracaoCadastrosWSConsumer integracaoWS = (IntegracaoCadastrosWSConsumer) UtilContext.getBean(IntegracaoCadastrosWSConsumer.class);
        integracaoWS.atualizarSaldoPersonal(url, ctx, professorSintetico.getCodigoColaborador(), creditosAtuais);

        sql = new StringBuilder();
        sql.append(" select c.saldopersonal from creditopersonal c ");
        sql.append(" where c.professor_codigo = ").append(professor).append(" and datalancamento <= '").append(dataLimite).append("'");
        sql.append(" order by datalancamento desc limit 1");
        Integer saldoInicial;
        try (ResultSet rssaldo = getAlunoAulaPersonalDao().createStatement(ctx, sql.toString())) {
            saldoInicial = 0;
            if (rssaldo.next()) {
                saldoInicial = rssaldo.getInt("saldopersonal");
            }
        }

        sql = new StringBuilder();
        sql.append(" select c.codigo from creditopersonal c ");
        sql.append(" where c.professor_codigo = ").append(professor).append(" and datalancamento > '").append(dataLimite).append("'");
        sql.append(" order by datalancamento ");

        try (ResultSet rscreditos = getAlunoAulaPersonalDao().createStatement(ctx, sql.toString())) {
            while (rscreditos.next()) {
                CreditoPersonal c = creditoPersonalService.obterPorId(ctx, rscreditos.getInt("codigo"));
                if (c.getCompra()) {
                    c.setSaldoPersonal(saldoInicial + c.getUnidades());
                } else {
                    c.setSaldoPersonal(saldoInicial - c.getUnidades());
                }
                creditoPersonalService.alterar(ctx, c);
                saldoInicial = c.getSaldoPersonal();
            }
        }

    }

    @Override
    public List<ExportCreditoTO> exportCheckin(final String ctx,
                                               final Date inicio,
                                               final Date fim) throws ServiceException {
        List<ExportCreditoTO> listaItem = new ArrayList<ExportCreditoTO>();
        try {
            StringBuilder sql = new StringBuilder();
            sql.append(" select p.codigocolaborador, a.checkin, a.checkout, a.codigo from aulapersonal a \n");
            sql.append(" inner join professorsintetico p on a.professor_codigo = p.codigo \n");
            sql.append(" where a.checkin between '");
            sql.append(Uteis.getDataAplicandoFormatacao(inicio, "yyyy-MM-dd "));
            sql.append("00:00:00' and '");
            sql.append(Uteis.getDataAplicandoFormatacao(fim, "yyyy-MM-dd "));
            sql.append("23:59:59' order by checkin \n");

            try (ResultSet rs = aulaPersonalDao.createStatement(ctx, sql.toString())) {
                while (rs.next()) {
                    int codigo = rs.getInt("codigo");
                    int codigocolaborador = rs.getInt("codigocolaborador");
                    Date checkin = rs.getTimestamp("checkin");
                    ExportCreditoTO checkinTo = new ExportCreditoTO();
                    checkinTo.setCodigo(codigocolaborador + "1" + codigo);
                    checkinTo.setCodigoColaborador(codigocolaborador);
                    checkinTo.setIdentificacao(1);
                    checkinTo.setDtHrEvento(Uteis.getDataAplicandoFormatacao(checkin, "dd/MM/yyyy HH:mm:ss"));
                    listaItem.add(checkinTo);

                    Date checkout = rs.getTimestamp("checkout");
                    if (checkout != null) {
                        ExportCreditoTO checkoutTo = new ExportCreditoTO();
                        checkoutTo.setCodigo(codigocolaborador + "2" + codigo);
                        checkoutTo.setCodigoColaborador(codigocolaborador);
                        checkoutTo.setIdentificacao(2);
                        checkoutTo.setDtHrEvento(Uteis.getDataAplicandoFormatacao(checkout, "dd/MM/yyyy HH:mm:ss"));
                        listaItem.add(checkoutTo);
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return listaItem;
    }

    public boolean personalEmAtendimento(String ctx, Integer codigoColaborador) throws ServiceException{
        try {
            try (ResultSet st = aulaPersonalDao.createStatement(ctx, "select a.codigo from aulapersonal a \n" +
                    "inner join professorsintetico p on p.codigo = a.professor_codigo \n" +
                    "where checkout is null\n" +
                    "and p.codigocolaborador = " + codigoColaborador)) {
                return st.next();
            }
        }catch (Exception e){
            throw new ServiceException(e);
        }
    }

    public void normalizarChekins(String key) throws Exception{
        String sql = "select * from (\n" +
                "select to_timestamp(c.\"timestamp\" / 1000) as hora_checkin, a.codigo, \n" +
                "u.username usuario_checkin,  \n" +
                "p.nome as nome_personal, \n" +
                "a.checkin checkin_original, \n" +
                "(select checkin from aulapersonal_aud aa where aa.codigo = a.codigo and revtype = 1 order by rev desc limit 1) checkin_alterado,\n" +
                "(select checkout  from aulapersonal_aud aa where aa.codigo = a.codigo and revtype = 1 order by rev desc  limit 1) checkout,\n" +
                "(select checkout  from aulapersonal_aud aa where aa.codigo = a.codigo and revtype = 1 order by rev desc limit 1) - a.checkin as diferencaoriginal,\n" +
                "(select checkout  from aulapersonal_aud aa where aa.codigo = a.codigo and revtype = 1 order by rev desc  limit 1) - \n" +
                "(select checkin from aulapersonal_aud aa where aa.codigo = a.codigo and revtype = 1 order by rev desc limit 1) as diferencaalterada,\n" +
                "(select unidades from creditopersonal c2 where c2.aulapersonal_codigo = a.codigo) creditos_gastos,\n" +
                "(select count(codigo) from alunoaulapersonal aaa where aaa.aula_codigo  = a.codigo) as alunos\n" +
                "from aulapersonal_aud a \n" +
                "inner join customrevisionentity c on a.rev = c.id\n" +
                "inner join professorsintetico p on p.codigo = a.professor_codigo \n" +
                "left join usuario u on u.username = c.username  \n" +
                "where revtype = 0\n" +
                "and a.checkin > (select checkin from aulapersonal_aud aa where aa.codigo = a.codigo and revtype = 1 limit 1)\n" +
                "and a.checkin > '2022-06-01'\n" +
                "order by c.\"timestamp\" desc \n" +
                ") as f where f.creditos_gastos > 1 and alunos < 2 ";
        try (ResultSet rs = aulaPersonalDao.createStatement(key, sql)) {
            while (rs.next()) {
                Integer codigo = rs.getInt("codigo");
                Date in = rs.getTimestamp("checkin_original");

                AulaPersonal aulaPersonal = aulaPersonalDao.findById(key, codigo);
                aulaPersonal.setCheckIn(in);
                alterar(key, aulaPersonal);
                ajustarSaldoCreditos(key, aulaPersonal, false, 70,
                        true, null);
            }
        }

    }


}
