package br.com.pacto.service.impl.cliente;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.cliente.ClienteAcompanhamento;
import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.programa.ProgramaTreino;
import br.com.pacto.controller.json.aluno.FiltroAlunoJSON;
import br.com.pacto.controller.json.aluno.FiltroPessoaJSON;
import br.com.pacto.controller.json.aluno.SituacaoAlunoEnum;
import br.com.pacto.controller.json.aluno.SituacaoContratoZWEnum;
import br.com.pacto.controller.json.aluno.TipoPessoaEnum;
import br.com.pacto.controller.json.base.SuperControle;
import br.com.pacto.controller.json.empresa.EmpresaDTO;
import br.com.pacto.controller.json.gestao.ClienteColaboradoresDTO;
import br.com.pacto.controller.json.gestao.ClienteColaboradoresSituacaoDTO;
import br.com.pacto.dao.intf.cliente.ClienteAcompanhamentoDao;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.conexao.ConexaoZWServiceImpl;
import br.com.pacto.service.intf.cliente.ClienteService;
import br.com.pacto.service.intf.cliente.ClienteSinteticoService;
import br.com.pacto.service.intf.programa.ProgramaTreinoService;
import br.com.pacto.util.UteisValidacao;
import org.json.JSONObject;
import org.jsoup.Jsoup;
import org.jsoup.safety.Whitelist;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class ClienteServiceImpl implements ClienteService {

    @Autowired
    private ConexaoZWServiceImpl conexaoZWService;
    @Autowired
    private SessaoService sessaoService;
    @Autowired
    private ProgramaTreinoService programaTreinoService;
    @Autowired
    private ClienteSinteticoService clienteSinteticoService;
    @Autowired
    private ClienteAcompanhamentoDao clienteAcompanhamentoDao;

    @Override
    public void deletarClienteMensagem(Integer matricula, String tipoMensagem) throws ServiceException {
        try {
            try (Connection conZW = conexaoZWService.conexaoZw(sessaoService.getUsuarioAtual().getChave())) {
                deletarClienteMensagem(conZW, matricula, tipoMensagem);
            }
        }catch (Exception e){
            throw new ServiceException(e);
        }
    }


    public void deletarClienteMensagem(Connection conZW, Integer matricula, String tipoMensagem) throws Exception {
        try (ResultSet rs = conZW.prepareStatement("SELECT c.codigo FROM clienteMensagem c " +
                " inner join cliente cli on cli.codigo = c.cliente " +
                " WHERE cli.codigomatricula = " + matricula +
                " and c.tipomensagem = '" + tipoMensagem + "' ").executeQuery()) {
            if (rs.next()) {
                conZW.prepareStatement("delete FROM clienteMensagem where codigo = " +
                        rs.getInt("codigo")).execute();
            }
        }
    }

    @Override
    public void salvarClienteMensagem(Integer matricula, String tipoMensagem, String mensagem) throws ServiceException {
        try {
            try (Connection conZW = conexaoZWService.conexaoZw(sessaoService.getUsuarioAtual().getChave())) {
                deletarClienteMensagem(conZW, matricula, tipoMensagem);
                try (ResultSet rs = conZW.prepareStatement("SELECT c.codigo FROM cliente c " +
                        " WHERE c.codigomatricula = " + matricula).executeQuery()) {
                    if (rs.next()) {
                        String insert = "insert into clientemensagem (cliente, tipomensagem, mensagem) values (?,?,?)";
                        PreparedStatement stm = conZW.prepareStatement(insert);
                        stm.setInt(1, rs.getInt("codigo"));
                        stm.setString(2, tipoMensagem);
                        stm.setString(3, mensagem);
                        stm.execute();
                    }
                }
            }
        }catch (Exception e){
            throw new ServiceException(e);
        }
    }

    @Override
    public String obterClienteMensagem(Integer matricula, String tipoMensagem) {
        String mensagem = "";
        try {
            ResultSet rs;
            try (Connection conZW = conexaoZWService.conexaoZw(sessaoService.getUsuarioAtual().getChave())) {

                rs = conZW.prepareStatement("SELECT c.mensagem FROM clienteMensagem c " +
                        " inner join cliente cli on cli.codigo = c.cliente " +
                        " WHERE cli.codigomatricula = " + matricula +
                        " and c.tipomensagem = '" + tipoMensagem + "' ").executeQuery();
            }
            if(rs.next()){
                mensagem = Jsoup.clean(rs.getString("mensagem").replace("<title>Untitled document</title>", ""), Whitelist.none());
            }

            return mensagem;
        } catch (Exception ex) {
            return mensagem;
        }
    }

    public List<ClienteColaboradoresDTO> listaAlunosColaboradores(FiltroPessoaJSON filtros,
                                                                  PaginadorDTO paginadorDTO, Integer empresaId) throws Exception {
        StringBuilder sql = obterSQLAlunosColaboradores(filtros, empresaId);

        String orderBy = "";
        boolean orderByDesc = false;
        if(!UteisValidacao.emptyString(paginadorDTO.getSort())){
            String[] split = paginadorDTO.getSort().split(",");
            orderBy = split[0];
            orderByDesc = split[1].equals("DESC");
        }

        if (!UteisValidacao.emptyString(orderBy)) {
            sql.append(" order by  ").append(orderBy);
            sql.append(orderByDesc ? " desc" : " asc");
        }
        int limit = (paginadorDTO.getSize() == null ? 10000 : Integer.parseInt(paginadorDTO.getSize().toString()));
        sql.append(" LIMIT ").append(limit);
        sql.append(" OFFSET ").append((paginadorDTO.getPage() == null ? 0 : Integer.parseInt(paginadorDTO.getPage().toString())) * limit);

        final List<ClienteColaboradoresDTO> lista = new ArrayList<>();
        String chave = sessaoService.getUsuarioAtual().getChave();
        try (Connection conZW = conexaoZWService.conexaoZw(chave)) {
            PreparedStatement pst = conZW.prepareStatement(sql.toString());
            ResultSet rs = pst.executeQuery();
            while (rs.next()) {
                ClienteColaboradoresDTO dto = new ClienteColaboradoresDTO();
                dto.setEmails(new ArrayList<>());
                dto.setTelefones(new ArrayList<>());

                String emailString = rs.getString("emails");
                String telString = rs.getString("telefones");
                String tipoString = rs.getString("tipo");

                dto.setNome(rs.getString("nome"));
                dto.setPessoa(rs.getInt("pessoa"));
                dto.setCliente(rs.getInt("cliente"));
                dto.setColaborador(rs.getInt("colaborador"));
                dto.setMatricula(rs.getString("matricula"));

                if(programaTreinoService.temProgramaVigenteByMatricula(chave, dto.getMatricula())){
                    dto.setProgramaVigente(true);
                }

                dto.setGympass(rs.getBoolean("gympass"));
                dto.setTotalpass(rs.getBoolean("totalpass"));

                String fotokey = rs.getString("fotokey");

                dto.setUrlFoto(Aplicacao.obterUrlFotoDaNuvem(fotokey));

                if (!UteisValidacao.emptyString(emailString)) {
                    List<String> listaEmails = Arrays.asList(emailString.split("###"));
                    dto.setEmails(listaEmails);
                }

                if (!UteisValidacao.emptyString(telString)) {
                    List<String> listaTel = Arrays.asList(telString.split("###"));
                    dto.setTelefones(listaTel);
                }

                dto.setSituacao(new ArrayList<>());
                if (!UteisValidacao.emptyNumber(dto.getCliente())) {
                    ClienteColaboradoresSituacaoDTO sitDTO = new ClienteColaboradoresSituacaoDTO();
                    sitDTO.setTipo("cliente");
                    sitDTO.setSituacao(rs.getString("clienteSituacao"));
                    dto.setSituacaoCliente(rs.getString("clienteSituacao"));

                    try {
                        sitDTO.setSituacaoContrato(rs.getString("clienteSituacaoContrato"));
                        if (sitDTO.getSituacao() != null &&
                                sitDTO.getSituacao().equals(SituacaoAlunoEnum.VISITANTE.getCodigo())) {
                            if (rs.getBoolean("freepass")) {
                                sitDTO.setSituacaoContrato(SituacaoContratoZWEnum.FREE_PASS.getCodigo());
                            } else if (rs.getBoolean("aulaAvulsa")) {
                                sitDTO.setSituacaoContrato(SituacaoContratoZWEnum.AULA_AVULSA.getCodigo());
                            } else if (rs.getBoolean("diaria")) {
                                sitDTO.setSituacaoContrato(SituacaoContratoZWEnum.DIARIA.getCodigo());
                            }
                        }
                        dto.setSituacaoClienteContrato(sitDTO.getSituacaoContrato());
                    } catch (Exception ex) {
                        ex.printStackTrace();
                    }
                    dto.getSituacao().add(sitDTO);
                }

                if (!UteisValidacao.emptyNumber(dto.getColaborador())) {
                    ClienteColaboradoresSituacaoDTO sitDTO = new ClienteColaboradoresSituacaoDTO();
                    sitDTO.setTipo("colaborador");

                    String colaboradorSituacao = rs.getString("colaboradorSituacao");
                    if (colaboradorSituacao.equals("AT")) {
                        sitDTO.setSituacao(SituacaoAlunoEnum.ATIVO.getCodigo());
                    } else {
                        sitDTO.setSituacao(SituacaoAlunoEnum.INATIVO.getCodigo());
                    }
                    dto.setSituacaoColaborador(sitDTO.getSituacao());
                    dto.getSituacao().add(sitDTO);
                }

                List<String> listaTipo = Arrays.asList(tipoString.split("###"));
                dto.setTipos(listaTipo);

                List<EmpresaDTO> empresas = new ArrayList<>();
                Integer codigoEmpresa = rs.getInt("empresacliente");
                String nomeEmpresa = rs.getString("nomeempresacliente");
                Integer codigoEmpresaColaborador = rs.getInt("empresacolaborador");
                String nomeEmpresaColaborador = rs.getString("nomeEmpresaColaborador");

                if (codigoEmpresa != null && nomeEmpresa != null) {
                    EmpresaDTO empresa = new EmpresaDTO();
                    empresa.setCodigo(codigoEmpresa);
                    empresa.setNome(nomeEmpresa);
                    empresas.add(empresa);
                }

                if (codigoEmpresaColaborador != null && nomeEmpresaColaborador != null) {
                    EmpresaDTO empresa = new EmpresaDTO();
                    empresa.setCodigo(codigoEmpresaColaborador);
                    empresa.setNome(nomeEmpresaColaborador);
                    empresas.add(empresa);
                }

                dto.setDataNascimento(Uteis.getData(rs.getDate("datanasc")));
                dto.setDataUltimoAcesso(rs.getTimestamp("dataultimoacesso") != null ? rs.getTimestamp("dataultimoacesso").getTime() : 0);
                dto.setEmpresas(empresas);

                lista.add(dto);;
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        for(ClienteColaboradoresDTO dto : lista){
            try {
                if(dto.getMatricula() != null) {
                    ClienteSintetico cliente = clienteSinteticoService.consultarPorMatricula(chave, dto.getMatricula());
                    ProgramaTreino programaTreino = programaTreinoService.obterUltimoProgramaVigenteComOuSemTreinoRealizado(chave, cliente.getCodigo());
                    if(programaTreino != null){
                        dto.setDataFimProgramaTreino(programaTreino.getDataTerminoPrevisto().getTime());
                        dto.setDataInicioProgramaTreino(programaTreino.getDataInicio().getTime());
                    }
                    if(!SuperControle.independente(chave)) {
                        int matricula = Integer.parseInt(dto.getMatricula());
                        String sqlGrupoRisco = "SELECT * FROM situacaoclientesinteticodw WHERE matricula = " + matricula;
                        try(Connection conZW = conexaoZWService.conexaoZw(sessaoService.getUsuarioAtual().getChave())) {
                            try(PreparedStatement ps = conZW.prepareStatement(sqlGrupoRisco)) {
                                try(ResultSet rs = ps.executeQuery()) {
                                    if(rs.next()) {
                                        dto.setPesoRisco(rs.getInt("pesorisco"));
                                    }
                                }
                            }
                        }
                        StringBuilder where = new StringBuilder(" WHERE obj.cliente.matricula = ").append(matricula);
                        Map<String, Object> p = new HashMap<String, Object>();
                        List<ClienteAcompanhamento> byParam = clienteAcompanhamentoDao.findByParam(chave, where, p);
                        dto.setQtdAcompanhamentos(byParam != null ? byParam.size() : 0);
                    }
                }
            } catch (Exception ignored) {
                ignored.printStackTrace();
            }
        }

        return lista;
    }

    private String obterStringSelecionadaInt(List<Integer> listaInteger) {
        if (listaInteger == null) {
            return "";
        }
        String retorno = "";
        for (Integer obj : listaInteger) {
            retorno += "," + obj;
        }
        return retorno.replaceFirst(",", "");
    }


    private String obterStringSelecionadaString(List<String> listaString) {
        if (listaString == null) {
            return "";
        }
        String retorno = "";
        for (String obj : listaString) {
            retorno += ",'" + obj.trim() + "'";
        }
        return retorno.replaceFirst(",", "");
    }

    public StringBuilder obterSQLAlunosColaboradores(FiltroPessoaJSON filtros, Integer empresaId) throws Exception {
        String empresasSel = obterStringSelecionadaInt(filtros.getEmpresas());
        String consultoresSel = obterStringSelecionadaInt(filtros.getConsultores());
        String professoresSel = obterStringSelecionadaInt(filtros.getProfessores());
        String tiposSel = obterStringSelecionadaInt(filtros.getTipos());
        String categoriasSel = obterStringSelecionadaInt(filtros.getCategorias());
        String classificacoesSel = obterStringSelecionadaInt(filtros.getClassificacoes());
        String gruposSel = obterStringSelecionadaInt(filtros.getGrupos());
        String profissoesSel = obterStringSelecionadaInt(filtros.getProfissoes());
        String situacoesSel = obterStringSelecionadaString(filtros.getSituacoes());
        Date dataHoje = Calendario.hoje();

        StringBuilder sql = new StringBuilder();
        sql.append("select * from ( \n");
        sql.append("select \n");
        sql.append("p.codigo as pessoa, \n");
        sql.append("p.nome, \n");
        sql.append("p.nomeconsulta, \n");
        sql.append("p.cfp as cpf, \n");
        sql.append("p.rg, \n");
        sql.append("p.fotokey, \n");
        sql.append("regexp_replace(p.cfp, '[^0-9]', '', 'g') as cpf2, \n");
        sql.append("c.matricula, \n");
        sql.append("c.codigomatricula, \n");
        sql.append("c.codigo as cliente, \n");
        sql.append("c2.codigo as colaborador, \n");
        sql.append("c.situacao as clienteSituacao, \n");
        sql.append("sw.situacaocontrato as clienteSituacaoContrato, \n");
        sql.append("sw.dataultimoacesso, \n");
        sql.append("p.datanasc, \n");
        sql.append("c2.situacao as colaboradorSituacao, \n");
        sql.append("'' as colaboradorSituacaoContrato, \n");
        sql.append("CASE \n");
        sql.append("WHEN c.situacao IS NOT NULL AND c2.situacao IS NOT NULL THEN c.situacao || '###' || c2.situacao \n");
        sql.append("WHEN c.situacao IS NOT NULL THEN c.situacao \n");
        sql.append("WHEN c2.situacao IS NOT NULL THEN c2.situacao \n");
        sql.append("ELSE '' \n");
        sql.append("END AS situacoes, \n");
        sql.append("CASE \n");
        sql.append("WHEN c.codigo IS NOT NULL AND c2.codigo IS NOT NULL THEN 'Cliente###Colaborador' \n");
        sql.append("WHEN c.codigo IS NOT NULL THEN 'Cliente' \n");
        sql.append("WHEN c2.codigo IS NOT NULL THEN 'Colaborador' \n");
        sql.append("ELSE '' \n");
        sql.append("END AS tipo, \n");
        sql.append("CASE \n");
        sql.append("WHEN c.codigo IS NOT NULL AND c2.codigo IS NOT NULL THEN 2 \n");
        sql.append("WHEN c.codigo IS NOT NULL THEN 0 \n");
        sql.append("WHEN c2.codigo IS NOT NULL THEN 1 \n");
        sql.append("ELSE 0 \n");
        sql.append("END AS tipoQtd, \n");
        sql.append("c.empresa as empresacliente, \n");
        sql.append("e.nome as nomeempresacliente, \n");
        sql.append("c2.empresa as empresacolaborador,\n");
        sql.append("emp.nome as nomeEmpresaColaborador, \n");
        sql.append("CASE \n");
        sql.append("WHEN e.codigo IS NOT NULL AND emp.codigo IS NOT NULL THEN e.nome || '###' || emp.nome \n");
        sql.append("WHEN e.codigo IS NOT NULL THEN e.nome \n");
        sql.append("WHEN emp.codigo IS NOT NULL THEN emp.nome \n");
        sql.append("ELSE '' \n");
        sql.append("END AS empresas, \n");
        sql.append("ca.nome as categoria, \n");
        sql.append("array_to_string(array(\n");
        sql.append("select \n");
        sql.append("g.nome \n");
        sql.append("from clienteclassificacao gp \n");
        sql.append("inner join classificacao g on g.codigo = gp.classificacao \n");
        sql.append("where gp.cliente = c.codigo), '###') as classificacao, \n");
        sql.append("array_to_string(array(\n");
        sql.append("select \n");
        sql.append("g.descricao \n");
        sql.append("from clientegrupo gp \n");
        sql.append("inner join grupo g on g.codigo = gp.grupo \n");
        sql.append("where gp.cliente = c.codigo), '###') as grupo, \n");
        sql.append("array_to_string(array(select email from email where pessoa = p.codigo), '###') as emails, \n");
        sql.append("array_to_string(array(select numero from telefone where pessoa = p.codigo), '###') as telefones, \n");
        sql.append("(select count(codigo) from email where pessoa = p.codigo) as emailsQtd, \n");
        sql.append("(select count(codigo) from telefone where pessoa = p.codigo) as telefonesQtd, \n");
        sql.append(" exists(SELECT codigo FROM PeriodoAcessoCliente WHERE pessoa = p.codigo");
        sql.append(" AND tipoAcesso ='PL' AND tokengympass is not null and tokengympass <> '') as gympass, \n");
        sql.append(" exists(SELECT codigo FROM periodoacessocliente WHERE pessoa = p.codigo AND tipototalpass = true) as totalpass, \n");
        sql.append(" exists(SELECT codigo FROM PeriodoAcessoCliente WHERE pessoa = p.codigo AND tipoAcesso = 'DI' ");
        sql.append(" AND dataInicioAcesso <= '").append(Uteis.getDataJDBC(dataHoje)).append("' and dataFinalAcesso >= '").append(Uteis.getDataJDBC(dataHoje)).append("') as diaria, \n");
        sql.append(" exists(SELECT codigo FROM PeriodoAcessoCliente WHERE pessoa = p.codigo AND tipoAcesso = 'PL' ");
        sql.append(" AND dataInicioAcesso <= '").append(Uteis.getDataJDBC(dataHoje)).append("' and dataFinalAcesso >= '").append(Uteis.getDataJDBC(dataHoje)).append("') as freepass, \n");
        sql.append(" exists(SELECT codigo FROM PeriodoAcessoCliente WHERE pessoa = p.codigo AND tipoAcesso = 'AA' ");
        sql.append(" AND dataInicioAcesso <= '").append(Uteis.getDataJDBC(dataHoje)).append("' and dataFinalAcesso >= '").append(Uteis.getDataJDBC(dataHoje)).append("') as aulaAvulsa \n");
        sql.append("from pessoa p \n");
        sql.append("left join cliente c on c.pessoa = p.codigo \n");
        sql.append("left join categoria ca on ca.codigo = c.categoria \n");
        sql.append("left join situacaoclientesinteticodw sw on sw.codigopessoa = p.codigo \n");
        sql.append("left join colaborador c2 on c2.pessoa = p.codigo \n");
        sql.append("left join empresa e on e.codigo  = c.empresa \n");
        sql.append("left join empresa emp on emp.codigo  = c2.empresa \n");

        sql.append("where (c.codigo is not null or  c2.codigo is not null) \n");

        if (!UteisValidacao.emptyString(empresasSel)) {
            sql.append("AND (c.empresa in (").append(empresasSel).append(") or c2.empresa in (").append(empresasSel).append(")) \n ");
        }

        if (filtros.getMatricula() != null &&
                !filtros.getMatricula().isEmpty()) {
            sql.append("AND (c.matricula = '").append(filtros.getMatricula()).append("' or c.codigomatricula = '").append(filtros.getMatricula()).append("') \n");
        }

        if (filtros.getCodigoAcesso() != null &&
                !filtros.getCodigoAcesso().isEmpty()) {
            sql.append("AND c.codacessoalternativo ILIKE '%").append(filtros.getCodigoAcesso().toUpperCase()).append("%' \n");
        }

        if (!UteisValidacao.emptyString(categoriasSel)) {
            sql.append("AND c.categoria in ( ").append(categoriasSel).append(") \n");
        }

        if (filtros.getRg() != null &&
                !filtros.getRg().isEmpty()) {
            sql.append("AND p.rg ILIKE '%").append(filtros.getRg().toUpperCase()).append("%' \n");
        }

        if (filtros.getCpf() != null &&
                !filtros.getCpf().isEmpty()) {
            sql.append("AND regexp_replace(p.cfp, '[^0-9]', '', 'g') = '").append(filtros.getCpf().replaceAll("[^0-9]", "")).append("' \n");
        }

        if (filtros.getRne() != null &&
                !filtros.getRne().isEmpty()) {
            sql.append("AND p.rne ILIKE '%").append(filtros.getRne()).append("%' \n");
        }

        if (filtros.getPassaporte() != null &&
                !filtros.getPassaporte().isEmpty()) {
            sql.append("AND p.passaporte ILIKE '%").append(filtros.getPassaporte()).append("%' \n");
        }

        if (filtros.getPessoa() != null &&
                filtros.getPessoa() > 0) {
            sql.append("AND p.codigo = ").append(filtros.getPessoa()).append(" \n");
        }

        if (filtros.getCliente() != null &&
                filtros.getCliente() > 0) {
            sql.append("AND c.codigo = ").append(filtros.getCliente()).append(" \n");
        }

        if (filtros.getColaborador() != null &&
                filtros.getColaborador() > 0) {
            sql.append("AND c2.codigo = ").append(filtros.getColaborador()).append(" \n");
        }

        if (!UteisValidacao.emptyString(profissoesSel)) {
            sql.append("AND p.profissao in ( ").append(profissoesSel).append(") \n");
        }

        if (!UteisValidacao.emptyString(filtros.getTipoDeConsulta())) {
            Date inicio = null;
            Date fim = null;

            if (filtros.getTipoDeConsulta().contains("BV")) {
                if (filtros.getTipoDeConsulta().equalsIgnoreCase("hojeBV")) {
                    inicio = Calendario.hoje();
                    fim = Calendario.hoje();
                } else if (filtros.getTipoDeConsulta().equalsIgnoreCase("ultimos7DiasBV")) {
                    inicio = Calendario.hoje();
                    fim = Calendario.somarDias(Calendario.hoje(), 7);
                } else if (filtros.getTipoDeConsulta().equalsIgnoreCase("ultimos15DiasBV")) {
                    inicio = Calendario.hoje();
                    fim = Calendario.somarDias(Calendario.hoje(), 15);
                } else if (filtros.getTipoDeConsulta().equalsIgnoreCase("ultimos30DiasBV")) {
                    inicio = Calendario.hoje();
                    fim = Calendario.somarDias(Calendario.hoje(), 30);
                }
                if (inicio != null && fim != null) {
                    sql.append("AND exists( \n");
                    sql.append("SELECT \n");
                    sql.append("quest.codigo \n");
                    sql.append("FROM questionariocliente quest \n");
                    sql.append("WHERE quest.cliente = c.codigo \n");
                    sql.append("AND quest.data::date >= '").append(Uteis.getDataFormatoBD(inicio)).append("' \n");
                    sql.append("AND quest.data::date <= '").append(Uteis.getDataFormatoBD(inicio)).append("' \n");
                    sql.append(") \n");
                }
            } else {
                if (filtros.getTipoDeConsulta().equalsIgnoreCase("hoje")) {
                    inicio = Calendario.hoje();
                    fim = Calendario.hoje();
                } else if (filtros.getTipoDeConsulta().equalsIgnoreCase("ultimos7Dias")) {
                    inicio = Calendario.hoje();
                    fim = Calendario.somarDias(Calendario.hoje(), 7);
                } else if (filtros.getTipoDeConsulta().equalsIgnoreCase("ultimos15Dias")) {
                    inicio = Calendario.hoje();
                    fim = Calendario.somarDias(Calendario.hoje(), 15);
                } else if (filtros.getTipoDeConsulta().equalsIgnoreCase("ultimos30Dias")) {
                    inicio = Calendario.hoje();
                    fim = Calendario.somarDias(Calendario.hoje(), 30);
                }
                if (inicio != null && fim != null) {
                    sql.append("AND exists( \n");
                    sql.append("SELECT \n");
                    sql.append("cont.codigo \n");
                    sql.append("FROM contrato cont \n");
                    sql.append("WHERE cont.pessoa = p.codigo \n");
                    sql.append("AND cont.datalancamento::date >= '").append(Uteis.getDataFormatoBD(inicio)).append("' \n");
                    sql.append("AND cont.datalancamento::date <= '").append(Uteis.getDataFormatoBD(inicio)).append("' \n");
                    sql.append(") \n");
                }
            }
        }

        if (filtros.getResponsavel() != null &&
                !filtros.getResponsavel().isEmpty()) {
            sql.append("AND ( \n");
            sql.append("p.nomemae ILIKE '%").append(filtros.getResponsavel()).append("%' \n");
            sql.append("or \n");
            sql.append("p.nomepai ILIKE '%").append(filtros.getResponsavel()).append("%' \n");
            sql.append("or \n");
            sql.append("p.nometerceiro ILIKE '%").append(filtros.getResponsavel()).append("%' \n");
            sql.append("or \n");
            sql.append("EXISTS (SELECT 1 FROM pessoa pe WHERE pe.codigo = c.pessoaresponsavel AND pe.nome ilike '%").append(filtros.getResponsavel()).append("%') \n");
            sql.append(") \n");
        }

        if (filtros.getEmail() != null &&
                !filtros.getEmail().isEmpty()) {
            sql.append("AND EXISTS ( \n")
                    .append(" SELECT 1 \n")
                    .append(" FROM email em \n")
                    .append(" WHERE em.pessoa = p.codigo \n")
                    .append(" AND em.email ilike '%").append(filtros.getEmail()).append("%' \n")
                    .append(") ");
        }

        if (filtros.getContrato() != null &&
                filtros.getContrato() > 0) {
            sql.append("AND EXISTS ( \n")
                    .append(" SELECT 1 \n")
                    .append(" FROM contrato co \n")
                    .append(" WHERE co.pessoa = p.codigo \n")
                    .append(" AND co.codigo = ").append(filtros.getContrato()).append(" \n")
                    .append(") ");
        }

        if (!UteisValidacao.emptyString(classificacoesSel)) {
            sql.append("AND EXISTS ( \n")
                    .append(" SELECT 1 \n")
                    .append(" FROM clienteclassificacao cc \n")
                    .append(" WHERE cc.cliente = c.codigo \n")
                    .append(" AND cc.classificacao in (").append(classificacoesSel).append(") \n")
                    .append(") ");
        }

        if (!UteisValidacao.emptyString(gruposSel)) {
            sql.append("AND EXISTS ( \n")
                    .append(" SELECT 1 \n")
                    .append(" FROM clientegrupo cg \n")
                    .append(" WHERE cg.cliente = c.codigo \n")
                    .append(" AND cg.grupo in (").append(gruposSel).append(") \n")
                    .append(") ");
        }

        if (filtros.isVisualizarOutrasCarteiras()) {
            if (!UteisValidacao.emptyString(professoresSel)) {
                sql.append("AND EXISTS ( \n")
                        .append(" SELECT 1 \n")
                        .append(" FROM vinculo vi \n")
                        .append(" WHERE vi.cliente = c.codigo \n")
                        .append(" AND vi.tipovinculo in ('TW','PR','PT','PI','PE') \n")
                        .append(" AND vi.colaborador in (").append(professoresSel).append(") \n")
                        .append(") ");
            }

            if (!UteisValidacao.emptyString(consultoresSel)) {
                sql.append("AND EXISTS ( \n")
                        .append(" SELECT 1 \n")
                        .append(" FROM vinculo vi \n")
                        .append(" WHERE vi.cliente = c.codigo \n")
                        .append(" AND vi.tipovinculo in ('CO') \n")
                        .append(" AND vi.colaborador in (").append(consultoresSel).append(") \n")
                        .append(") ");
            }

        } else {
            String codigosColaborador = obterCodigosColaboradorUsuario();
            sql.append("AND EXISTS ( \n");
            sql.append(" SELECT 1 \n");
            sql.append(" FROM vinculo vi \n");
            sql.append(" WHERE vi.cliente = c.codigo \n");
            sql.append(" AND vi.colaborador in (").append(codigosColaborador).append(") \n");
            sql.append(") \n");
        }

        if (filtros.getTelefone() != null &&
                !filtros.getTelefone().isEmpty()) {
            sql.append("AND EXISTS ( \n")
                    .append(" SELECT 1 \n")
                    .append(" FROM telefone tel \n")
                    .append(" WHERE tel.pessoa = p.codigo \n")
                    .append(" AND regexp_replace(tel.numero, '[^a-zA-Z0-9]', '', 'g') ILIKE '%").append(filtros.getTelefone().replaceAll("[^0-9]", "")).append("%' \n")
                    .append(") ");
        }

        if (filtros.getPlaca() != null &&
                !filtros.getPlaca().isEmpty()) {
            sql.append("AND EXISTS ( \n")
                    .append(" SELECT 1 \n")
                    .append(" FROM placa pl \n")
                    .append(" WHERE pl.pessoa = p.codigo \n")
                    .append(" AND pl.placa ILIKE '%").append(filtros.getPlaca()).append("%' \n")
                    .append(") ");
        }

        if (!UteisValidacao.emptyString(situacoesSel)) {
            sql.append("AND (\n");
            String[] filterValues = situacoesSel.split(",");
            StringBuilder filtroSitu = new StringBuilder();
            for (String situ : filterValues) {
                situ = situ.replaceAll("'", "");
                filtroSitu.append("or c.situacao = '").append(situ).append("' or c2.situacao = '").append(situ).append("' or sw.situacaoContrato = '").append(situ).append("' \n");
            }
            sql.append(filtroSitu.toString().replaceFirst("or", ""));
            sql.append(") \n");
        }

        if (!UteisValidacao.emptyString(filtros.getTreino())) {
            FiltroAlunoJSON filtroAlunoJSON = new FiltroAlunoJSON(new JSONObject());
            filtroAlunoJSON.setStatusClienteEnum(new ArrayList<>());
            filtroAlunoJSON.getStatusClienteEnum().add(filtros.getTreino());
            List<Integer> matriculas = clienteSinteticoService.listaAlunosMatricula(filtroAlunoJSON, empresaId);
            String sqlMatricula = "";
            if (matriculas != null && matriculas.size() > 0) {
                sqlMatricula = matriculas.stream()
                        .map(Object::toString)
                        .collect(Collectors.joining(","));
            }
            if (UteisValidacao.emptyString(sqlMatricula)) {
                sql.append("AND false \n");
            } else {
                sqlMatricula.replaceFirst(",", "");
                sql.append("AND c.codigomatricula in (").append(sqlMatricula).append(") \n");
            }
        }

        sql.append(") as sql \n");
        sql.append("where 1 = 1 \n");

        if (!UteisValidacao.emptyString(tiposSel)) {
            if (tiposSel.contains(TipoPessoaEnum.CLIENTE.getCodigo().toString()) &&
                    tiposSel.contains(TipoPessoaEnum.COLABORADOR.getCodigo().toString())) {
                sql.append("AND (sql.tipo ilike '%cliente%' or sql.tipo ilike '%colaborador%') \n");
            } else if (tiposSel.contains(TipoPessoaEnum.CLIENTE.getCodigo().toString())
                    && !tiposSel.contains(TipoPessoaEnum.COLABORADOR.getCodigo().toString())) {
                sql.append("AND sql.tipo ilike 'cliente' \n");
            } else if (!tiposSel.contains(TipoPessoaEnum.CLIENTE.getCodigo().toString()) &&
                    tiposSel.contains(TipoPessoaEnum.COLABORADOR.getCodigo().toString())) {
                sql.append("AND sql.tipo ilike 'colaborador' \n");
            }
        }

        return sql;
    }

    private String obterCodigosColaboradorUsuario() throws ServiceException {
        StringBuilder codig = new StringBuilder();
        try {
            try (Connection conZW = conexaoZWService.conexaoZw(sessaoService.getUsuarioAtual().getChave())) {
                ResultSet rs = conZW.createStatement().executeQuery("select c.codigo as colaborador from colaborador c " +
                        "where c.pessoa in (select c.pessoa from usuario u inner join colaborador c on c.codigo = u.colaborador" +
                        " where u.codigo = " + sessaoService.getUsuarioAtual().getId() + ")");
                while (rs.next()) {
                    codig.append(",").append(rs.getInt("colaborador"));
                }
            }
        } catch (Exception e) {
            throw new ServiceException(e);
        }

        return codig.toString().replaceFirst(",", "");
    }

}
