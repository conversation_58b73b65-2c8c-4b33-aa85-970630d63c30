package br.com.pacto.service.impl.gympass.dto;

import org.json.JSONObject;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 22/03/2020
 */
public class BookingWindowDTO {

    private String opens_at;
    private String closes_at;

    public BookingWindowDTO() {
    }

    public BookingWindowDTO(JSONObject json) {
        this.opens_at = json.optString("opens_at");
        this.closes_at = json.optString("closes_at");
    }

    public String getOpens_at() {
        return opens_at;
    }

    public void setOpens_at(String opens_at) {
        this.opens_at = opens_at;
    }

    public String getCloses_at() {
        return closes_at;
    }

    public void setCloses_at(String closes_at) {
        this.closes_at = closes_at;
    }
}
