/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.impl.atividade;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.atividade.CategoriaAtividade;
import br.com.pacto.bean.atividade.CategoriaAtividadeResponseTO;
import br.com.pacto.bean.musculo.GrupoMuscularResumidoResponseTO;
import br.com.pacto.controller.json.atividade.FiltroCategoriaAtividadeJSON;
import br.com.pacto.controller.json.musculo.FiltroGrupoMuscularJSON;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.atividade.*;
import br.com.pacto.bean.avaliacao.PerimetriaEnum;
import br.com.pacto.bean.musculo.*;
import br.com.pacto.controller.json.atividade.FiltroCategoriaAtividadeJSON;
import br.com.pacto.dao.intf.atividade.CategoriaAtividadeDao;
import br.com.pacto.dao.intf.musculo.GrupoMuscularDao;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.impl.notificacao.excecao.AtividadeExcecoes;
import br.com.pacto.service.impl.notificacao.excecao.GrupoMuscularExcecoes;
import br.com.pacto.service.intf.Validacao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.exception.ValidacaoException;
import br.com.pacto.service.intf.atividade.CategoriaAtividadeService;
import br.com.pacto.util.ViewUtils;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR>
 */
@Service
public class CategoriaAtividadeServiceImpl implements CategoriaAtividadeService {

    @Autowired
    private ViewUtils viewUtils;
    @Autowired
    private Validacao validacao;
    @Autowired
    private CategoriaAtividadeDao categoriaAtividadeDao;
    @Autowired
    private SessaoService sessaoService;

    public ViewUtils getViewUtils() {
        return viewUtils;
    }

    public void setViewUtils(ViewUtils viewUtils) {
        this.viewUtils = viewUtils;
    }

    public Validacao getValidacao() {
        return validacao;
    }

    public void setValidacao(Validacao validacao) {
        this.validacao = validacao;
    }

    public CategoriaAtividadeDao getCategoriaAtividadeDao() {
        return this.categoriaAtividadeDao;
    }

    public void setCategoriaAtividadeDao(CategoriaAtividadeDao categoriaAtividadeDao) {
        this.categoriaAtividadeDao = categoriaAtividadeDao;
    }

    public CategoriaAtividade alterar(final String ctx, CategoriaAtividade object) throws ServiceException, ValidacaoException {
        try {
            validarDados(ctx, object);
            return getCategoriaAtividadeDao().update(ctx, object);
        } catch (ValidacaoException vex) {
            throw vex;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public void validarDados(final String ctx, CategoriaAtividade object) throws ValidacaoException {
        if (object.getNome() == null || object.getNome().trim().isEmpty()) {
            throw new ValidacaoException("validacao.nome");
        }
        if (getCategoriaAtividadeDao().exists(ctx, object, "nome")) {
            throw new ValidacaoException("cadastros.existente");
        }
    }

    public void excluir(final String ctx, CategoriaAtividade object) throws ServiceException {
        try {
            getCategoriaAtividadeDao().delete(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public CategoriaAtividade inserir(final String ctx, CategoriaAtividade object) throws ServiceException, ValidacaoException {
        try {
            validarDados(ctx, object);
            return getCategoriaAtividadeDao().insert(ctx, object);
        } catch (ValidacaoException vex) {
            throw vex;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public CategoriaAtividade obterObjetoPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException {
        try {
            return getCategoriaAtividadeDao().findObjectByParam(ctx, query, params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public CategoriaAtividade obterPorId(final String ctx, Integer id) throws ServiceException {
        try {
            return getCategoriaAtividadeDao().findById(ctx, id);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<CategoriaAtividade> obterPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException {
        try {
            return getCategoriaAtividadeDao().findByParam(ctx, query, params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<CategoriaAtividade> obterPorParam(final String ctx, String query,
            Map<String, Object> params, int max, int index)
            throws ServiceException {
        try {
            return getCategoriaAtividadeDao().findByParam(ctx, query, params, max, index);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<CategoriaAtividade> obterTodos(final String ctx) throws ServiceException {
        try {
            return getCategoriaAtividadeDao().findListByAttributes(ctx, null, null, "nome", 0);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

     public List<CategoriaAtividadeResponseTO> consultarTodos() throws ServiceException{
        String ctx = sessaoService.getUsuarioAtual().getChave();
        List<CategoriaAtividade> lista = obterTodos(ctx);
        List<CategoriaAtividadeResponseTO> ret = new ArrayList<>();
        for (CategoriaAtividade categoriaAtividade: lista){
            ret.add(new CategoriaAtividadeResponseTO(categoriaAtividade));
        }
        return ret;
    }

    public List<CategoriaAtividadeResponseTO>consultarCategoriaAtividades(FiltroCategoriaAtividadeJSON filtroCategoriaAtividadeJSON, PaginadorDTO paginadorDTO)throws ServiceException{
        String ctx = sessaoService.getUsuarioAtual().getChave();

        if (!StringUtils.isBlank(paginadorDTO.getSort())) {
            paginadorDTO.setSort(paginadorDTO.getSort());
        } else {
            paginadorDTO.setSort("nome,ASC");
        }
        return getCategoriaAtividadeDao().consultarCategoriaAtividades(ctx,filtroCategoriaAtividadeJSON,paginadorDTO);
    }


    @Override
    public CategoriaAtividadeResponseTO consultarCategoriaAtividade(Integer id) throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        getCategoriaAtividadeDao().getCurrentSession(ctx).clear();
        CategoriaAtividade categoriaAtividade = obterPorId(ctx, id);
        if ((categoriaAtividade == null) || (categoriaAtividade.getCodigo() == null) || (categoriaAtividade.getCodigo() <= 0)){
            throw new ServiceException(AtividadeExcecoes.CATEGORIA_NAO_ENCONTRADA);
        }
        return new CategoriaAtividadeResponseTO(categoriaAtividade);
    }

    @Override
    public void excluir(Integer id) throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        CategoriaAtividade categoriaAtividade = null;
        try{
            categoriaAtividade = getCategoriaAtividadeDao().findById(ctx, id);
        }catch (Exception e){
            throw new ServiceException(AtividadeExcecoes.ERRO_BUSCAR_CATEGORIA_ATIVIDADE, e);
        }
        if ((categoriaAtividade == null) || (categoriaAtividade.getCodigo() == null) || (categoriaAtividade.getCodigo() <= 0)){
            throw new ServiceException(AtividadeExcecoes.CATEGORIA_NAO_ENCONTRADA);
        }
        try{
            getCategoriaAtividadeDao().delete(ctx, categoriaAtividade);
        }catch (Exception e){
            throw new ServiceException(AtividadeExcecoes.ERRO_EXCLUIR_CATEGORIA_ATIVIDADE, e);
        }
    }

    @Override
    public CategoriaAtividadeResponseTO inserir(CategoriaAtividadeTO categoriaAtividadeTO) throws ServiceException {
        try{
            String ctx = sessaoService.getUsuarioAtual().getChave();
            CategoriaAtividade categoriaAtividade = new CategoriaAtividade();
            categoriaAtividade.setNome(categoriaAtividadeTO.getNome().trim());
            validarCategoriaAtividade(ctx, categoriaAtividade);
            categoriaAtividade = getCategoriaAtividadeDao().insert(ctx, categoriaAtividade);

            return new CategoriaAtividadeResponseTO(categoriaAtividade);
        }catch (Exception e){
            throw new ServiceException(AtividadeExcecoes.ERRO_INCLUIR_CATEGORIA_ATIVIDADE, e);
        }
    }


    private void validarCategoriaAtividade(String ctx, CategoriaAtividade categoriaAtividade) throws ServiceException {
        if(getCategoriaAtividadeDao().exists(ctx, categoriaAtividade, "nome")){
            throw new ServiceException(AtividadeExcecoes.VALIDACAO_ATIVIDADE_JA_EXISTE);
        }
    }

    @Override
    public CategoriaAtividadeResponseTO alterar(Integer id, CategoriaAtividadeTO categoriaAtividadeTO) throws ServiceException {
        try{
            String ctx = sessaoService.getUsuarioAtual().getChave();

            CategoriaAtividade categoriaAtividade = getCategoriaAtividadeDao().findById(ctx, id);
            if ((categoriaAtividade == null) || (categoriaAtividade.getCodigo() == null) || (categoriaAtividade.getCodigo() <= 0)){
                throw new ServiceException(AtividadeExcecoes.CATEGORIA_NAO_ENCONTRADA);
            }

            categoriaAtividade.setNome(categoriaAtividadeTO.getNome().trim());
            validarCategoriaAtividade(ctx, categoriaAtividade);

            categoriaAtividade = getCategoriaAtividadeDao().update(ctx, categoriaAtividade);
            return new CategoriaAtividadeResponseTO(categoriaAtividade);
        }catch (Exception e){
            throw new ServiceException(AtividadeExcecoes.ERRO_ATUALIZAR_CATEGORIA_ATIVIDADE, e);
        }
    }


}
