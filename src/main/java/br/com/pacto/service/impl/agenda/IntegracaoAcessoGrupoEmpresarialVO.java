package br.com.pacto.service.impl.agenda;

import br.com.pacto.service.intf.empresa.EmpresaVO;

public class IntegracaoAcessoGrupoEmpresarialVO {

    protected Integer codigo;
    private String urlZillyonWeb;
    private String chave;
    private EmpresaVO empresaRemota = new EmpresaVO();

    public EmpresaVO getEmpresaRemota() {
        return empresaRemota;
    }

    public void setEmpresaRemota(EmpresaVO empresaRemota) {
        this.empresaRemota = empresaRemota;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getUrlZillyonWeb() {
        return urlZillyonWeb;
    }

    public void setUrlZillyonWeb(String urlZillyonWeb) {
        this.urlZillyonWeb = urlZillyonWeb;
    }

    public String getChave() {
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }
}
