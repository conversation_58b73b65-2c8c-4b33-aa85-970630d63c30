package br.com.pacto.service.impl.benchmark;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.impl.EntityManagerService;
import br.com.pacto.bean.benchmark.Benchmark;
import br.com.pacto.bean.benchmark.TipoBenchmark;
import br.com.pacto.controller.json.benchmark.BenchmarkResponseTO;
import br.com.pacto.controller.json.benchmark.BenchmarkTO;
import br.com.pacto.controller.json.benchmark.FiltroBenchmarkJSON;
import br.com.pacto.dao.intf.benchmark.BenchmarkDao;
import br.com.pacto.enumerador.crossfit.MidiaBenchmarkEnum;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.notificacao.excecao.*;
import br.com.pacto.service.intf.Validacao;
import br.com.pacto.service.intf.benchmark.BenchmarkService;
import br.com.pacto.service.intf.benchmark.TipoBenchmarkService;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;
import br.com.pacto.util.UtilS3Base64Img;
import br.com.pacto.util.ViewUtils;
import br.com.pacto.util.impl.JSFUtilities;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.apache.commons.codec.binary.Base64;
import servicos.integracao.midias.MidiaService;
import servicos.integracao.midias.commons.MidiaEntidadeEnum;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * Created by Rafael on 11/07/2016.
 */
@Service
public class BenchmarkServiceImpl implements BenchmarkService {

    @Autowired
    private ViewUtils viewUtils;
    @Autowired
    private Validacao validacao;
    @Autowired
    private BenchmarkDao benchmarkDao;
    @Autowired
    private SessaoService sessaoService;
    @Autowired
    private TipoBenchmarkService tipoBenchmarkService;

    public ViewUtils getViewUtils() {
        return viewUtils;
    }

    public void setViewUtils(ViewUtils viewUtils) {
        this.viewUtils = viewUtils;
    }

    public Validacao getValidacao() {
        return validacao;
    }

    public void setValidacao(Validacao validacao) {
        this.validacao = validacao;
    }

    public BenchmarkDao getBenchmarkDao() {
        return benchmarkDao;
    }

    public void setBenchmarkDao(BenchmarkDao benchmarkDao) {
        this.benchmarkDao = benchmarkDao;
    }

    public String getKey() {
        return JSFUtilities.getKey();
    }

    public Benchmark alterar(final String ctx, Benchmark object) throws ServiceException {
        try {
            return getBenchmarkDao().update(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public void excluir(final String ctx, Benchmark object) throws ServiceException {
        try {
            getBenchmarkDao().delete(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public Benchmark inserir(final String ctx, Benchmark object) throws ServiceException {
        try {
            return getBenchmarkDao().insert(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public Benchmark obterObjetoPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException {
        try {
            return getBenchmarkDao().findObjectByParam(ctx, query, params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public Benchmark obterPorId(final String ctx, Integer id) throws ServiceException {
        try {
            return getBenchmarkDao().findById(ctx, id);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<Benchmark> obterPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException {
        try {
            return getBenchmarkDao().findByParam(ctx, query, params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<Benchmark> obterPorParam(final String ctx, String query,
                                         Map<String, Object> params, int max, int index)
            throws ServiceException {
        try {
            return getBenchmarkDao().findByParam(ctx, query, params, max, index);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<Benchmark> obterTodos(final String ctx) throws ServiceException {
        try {
            return getBenchmarkDao().findListByAttributes(ctx, null, null, "nome", 0);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public Benchmark obterPorNome(final String ctx, String nome) throws ServiceException {
        try {
            HashMap<String, Object> params = new HashMap<String, Object>();
            StringBuilder sql = new StringBuilder();
            sql.append("Select obj FROM Benchmark obj\n");
            if (!UteisValidacao.emptyString(nome)) {
                sql.append("Where upper(obj.nome) = :nome ");
                params.put("nome", nome.toUpperCase());
            }
            return getBenchmarkDao().findObjectByParam(ctx, sql.toString(), params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }

    }

    @Override
    public BenchmarkResponseTO cadastroBenchmark(BenchmarkTO benchmarkTO) throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        try {
            camposObrigatorios(benchmarkTO);
            TipoBenchmark tipoBenchmark = validarTipoBenchmark(ctx, benchmarkTO);
            Benchmark benchmark = hidratarBenchmark(benchmarkTO, new Benchmark(), tipoBenchmark);
            validarBenchmarkDuplicado(ctx, benchmark.getNome(), benchmark.getCodigo());
            inserir(ctx, benchmark);
            return new BenchmarkResponseTO(benchmark);
        } catch (Exception e) {
            throw new ServiceException(BenchmarkExcecoes.ERRO_INCLUIR_BENCHMARK, e);
        }
    }

    @Override
    public List<BenchmarkResponseTO> listarTodosBenchmarks() throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        prepare(ctx);
        try {
            List<Benchmark> listaDB = benchmarkDao.findAll(ctx);
            List<BenchmarkResponseTO> listaR = new ArrayList<>();
            for (Benchmark benchmark : listaDB) {
                listaR.add(new BenchmarkResponseTO(benchmark));
            }
            return listaR;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException(BenchmarkExcecoes.ERRO_INCLUIR_BENCHMARK, e);
        }
    }

    @Override
    public BenchmarkResponseTO detalhesBenchmark(Integer id) throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        try {
            Benchmark benchmark = validarID(ctx, id);

            return new BenchmarkResponseTO(benchmark);
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException(BenchmarkExcecoes.ERRO_INCLUIR_BENCHMARK, e);
        }
    }

    @Override
    public List<BenchmarkResponseTO> listaBenchmark(FiltroBenchmarkJSON filtroBenchmarkJSON, PaginadorDTO paginadorDTO) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            if (!StringUtils.isBlank(paginadorDTO.getSort())) {
                paginadorDTO.setSort(paginadorDTO.getSort());
            } else {
                paginadorDTO.setSort("nome,ASC");
            }
            List<Benchmark> lista = benchmarkDao.listarBenchmarks(ctx, filtroBenchmarkJSON, paginadorDTO);
            List<BenchmarkResponseTO> listaR = new ArrayList<>();
            if (lista != null) {
                for (Benchmark benchmark : lista) {
                    listaR.add(new BenchmarkResponseTO(benchmark));
                }
            }
            return listaR;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException(BenchmarkExcecoes.ERRO_BUSCAR_BENCHMARK, e);
        }
    }

    @Override
    public BenchmarkResponseTO alterarBenchmark(Integer id, BenchmarkTO benchmarkTO) throws ServiceException {

        String ctx = sessaoService.getUsuarioAtual().getChave();
        try {
            camposObrigatorios(benchmarkTO);
            Benchmark benchmark = validarID(ctx, id);
            TipoBenchmark tipoBenchmark = validarTipoBenchmark(ctx, benchmarkTO);

            hidratarBenchmark(benchmarkTO, benchmark, tipoBenchmark);
            validarBenchmarkDuplicado(ctx, benchmark.getNome(), benchmark.getCodigo());
            alterar(ctx, benchmark);

            return new BenchmarkResponseTO(benchmark);
        } catch (Exception e) {
            throw new ServiceException(BenchmarkExcecoes.ERRO_ALTERAR_BENCHMARK, e);
        }

    }

    @Override
    public void removerBenchmark(Integer id) throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        try {
            Benchmark benchmark = validarID(ctx, id);
            excluir(ctx, benchmark);
        } catch(ServiceException e){
            throw e;
        } catch (Exception e) {
            throw new ServiceException(AmbienteExcecoes.ERRO_ALTERAR_AMBIENTE, e);
        }
    }

    private Benchmark validarID(String ctx, Integer id) throws ServiceException {
        if (id == null || id < 1) {
            throw new ServiceException(BenchmarkExcecoes.ERRO_ID_NAO_INFORMADO);
        }
        Benchmark benchmark = obterPorId(ctx, id);
        if (benchmark == null) {
            throw new ServiceException(BenchmarkExcecoes.ERRO_BENCHMARK_NAO_EXISTE);
        }
        return benchmark;
    }

    private Benchmark hidratarBenchmark(BenchmarkTO benchmarkTO, Benchmark benchmark, TipoBenchmark tipoBenchmark) throws ServiceException {
        benchmark.setTipoBenchmark(tipoBenchmark);
        benchmark.setNome(benchmarkTO.getNome().trim());
        benchmark.setTipoWod(benchmarkTO.getTipoExercicio());
        benchmark.setDescricaoExercicios(benchmarkTO.getExercicios());
        benchmark.setObservacao(benchmarkTO.getObservacao());
        if(!StringUtils.isBlank(benchmarkTO.getImagemData())){
            if (benchmarkTO.getVideoUri() == null || benchmarkTO.getVideoUri().trim().isEmpty()) {
                benchmark.setTipoMidia(MidiaBenchmarkEnum.IMAGEM);
                try{
                    String ctx = sessaoService.getUsuarioAtual().getChave();
                    benchmark.setUrlMidia(UtilS3Base64Img.getUrlImage(benchmarkTO.getImagemData(),benchmarkTO.getExtensaoImagem(),ctx));
                }catch(Exception ex){
                    throw new ServiceException(BenchmarkExcecoes.ERRO_SALVAR_IMAGEM);
                }
            } else {
                benchmark.setTipoMidia(MidiaBenchmarkEnum.VIDEO);
                benchmark.setUrlMidia(benchmarkTO.getVideoUri());
            }
        }
        return benchmark;
    }


    private TipoBenchmark validarTipoBenchmark(String ctx, BenchmarkTO benchmarkTO) throws ServiceException {
        TipoBenchmark tipoBenchmark = tipoBenchmarkService.obterPorId(ctx, benchmarkTO.getTipoBenchmarkId());
        if (tipoBenchmark == null || UteisValidacao.emptyNumber(tipoBenchmark.getCodigo())) {
            throw new ServiceException(TipoBenchmarkExcecoes.TIPOBENCHMARK_NAO_ENCONTRADO);
        }
        return tipoBenchmark;
    }

    private void camposObrigatorios(BenchmarkTO benchmarkTO) throws ServiceException {
        if (benchmarkTO.getNome() == null || benchmarkTO.getNome().trim().isEmpty()) {
            throw new ServiceException(BenchmarkExcecoes.ERRO_NOME_NAO_INFORMADO);
        }
        if (benchmarkTO.getExercicios() == null || benchmarkTO.getExercicios().trim().isEmpty()) {
            throw new ServiceException(BenchmarkExcecoes.ERRO_EXERCICIOS_NAO_INFORMADO);
        }
        if (benchmarkTO.getTipoBenchmarkId() == null || benchmarkTO.getTipoBenchmarkId() < 1) {
            throw new ServiceException(BenchmarkExcecoes.ERRO_TIPO_BENCHMARK_ID_NAO_INFORMADO);
        }
    }

    private void validarBenchmarkDuplicado(String ctx, String nome, Integer codigo) throws ServiceException {
        Benchmark benchmark = benchmarkDao.consultarBenchmark(ctx, nome, codigo);

        if (benchmark != null) {
            throw new ServiceException(BenchmarkExcecoes.ERRO_BENCHMARK_JA_EXISTE);
        }
    }

    protected void prepare(final String ctx) throws ServiceException {
        EntityManagerService ems = (EntityManagerService) UtilContext.getBean(EntityManagerService.class);
        try {
            ems.closeQuietly(ctx);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

}