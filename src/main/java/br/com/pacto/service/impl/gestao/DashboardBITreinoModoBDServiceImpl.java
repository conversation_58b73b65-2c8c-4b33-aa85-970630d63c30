package br.com.pacto.service.impl.gestao;

import br.com.pacto.bean.bi.AcessosExecucoesJSON;
import br.com.pacto.bean.bi.DadosBITreinoJSON;
import br.com.pacto.bean.bi.FiltrosDashboard;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.impl.agenda.AgendaModoBDServiceImpl;
import br.com.pacto.service.impl.conexao.ConexaoZWServiceImpl;
import br.com.pacto.service.intf.empresa.EmpresaVO;
import br.com.pacto.service.intf.gestao.DashboardBITreinoModoBDService;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.enumeradores.TipoColaboradorZWEnum;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

public class DashboardBITreinoModoBDServiceImpl implements DashboardBITreinoModoBDService, AutoCloseable {

    private Connection con;
    private String key;

    public DashboardBITreinoModoBDServiceImpl(Connection con, String key) {
        this.key = key;
        this.con = con;
    }

    @Override
    public void close() throws Exception {
        if (con != null && !con.isClosed()) {
            con.close();
        }
    }

    private void fecharResultSetQuietly(ResultSet resultSet) {
        try {
            if (resultSet != null && !resultSet.isClosed()) {
                resultSet.close();
            }
        } catch (SQLException e) {
            // se nao for possivel fechar alguma stream entao apenas ignora a mesma
            Uteis.logar(e, AgendaModoBDServiceImpl.class);
        }
    }

    private EmpresaVO consultarEmpresaZW(Integer empresaZW) throws Exception{
        EmpresaVO empresaVO = new EmpresaVO();
        StringBuilder sql = new StringBuilder();
        sql.append("select codigo, nome, nrdiasavencer from empresa em where em.codigo = ").append(empresaZW);

        try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sql.toString(), con)){
            while(rs.next()){
                empresaVO.setCodigo(rs.getInt("codigo"));
                empresaVO.setNome(rs.getString("nome"));
                empresaVO.setNrDiasAvencer(rs.getInt("nrdiasavencer"));
            }
        }

        return empresaVO;
    }

    @Override
    public String consultarDadosBITreino(String key, String dataInicio, String dataFim, String dataInicioAvencer,
                                         String dataFimAvencer, Integer professor, Integer empresaZW) throws Exception {
        try {
            Date inicio = dataInicio == null || dataInicio.isEmpty() ? null : Uteis.getDate(dataInicio);
            Date fim = dataFim == null || dataFim.isEmpty() ? null : Uteis.getDate(dataFim);
            Date inicioVencer = dataInicioAvencer == null || dataInicioAvencer.isEmpty() ? null : Uteis.getDate(dataInicioAvencer);
            Date fimVencer = dataFimAvencer == null || dataFimAvencer.isEmpty() ? null : Uteis.getDate(dataFimAvencer);

            EmpresaVO empresaVO = consultarEmpresaZW(empresaZW);
            if(!UteisValidacao.emptyNumber(empresaVO.getNrDiasAvencer())){
                fimVencer = Uteis.somarDias(fim, empresaVO.getNrDiasAvencer());
            }
            DadosBITreinoJSON dados = new DadosBITreinoJSON();
            if (professor == null || professor == 0) {
                List<Integer> alunosAtivosSemUsuarioMovel = obterCodsAlunosAtivosSemUsuarioMovel(empresaZW);
                dados.setAlunosAtivosForaTreino(alunosAtivosSemUsuarioMovel.size());
                dados.setCodsAlunosAtivosForaTreino(Uteis.concatenarListaInteger(alunosAtivosSemUsuarioMovel));
            }
            if (professor != null && professor > 0) {
                preencherDadosBITreino(dados, fim, professor);
                List<Integer> novosNovos = obterNumeroNovosNaCarteira(empresaZW, inicio, fim, professor, true);
                List<Integer> novosTrocaram = obterNumeroNovosNaCarteira(empresaZW, inicio, fim, professor, false);
                dados.setNovosNaCarteira(novosNovos.size()+novosTrocaram.size());
                dados.setNovosNaCarteiraNovos(novosNovos.size());
                dados.setNovosNaCarteiraTrocaram(novosTrocaram.size());
                dados.setCodsNovosCarteiraNovos(Uteis.concatenarListaInteger(novosNovos));
                dados.setCodsNovosCarteiraTrocaram(Uteis.concatenarListaInteger(novosTrocaram));

                List<Integer> trocas = obterTrocasCarteira(empresaZW, inicio, fim, professor);
                dados.setTrocasCarteira(trocas.size());
                dados.setCodsTrocasCarteira(Uteis.concatenarListaInteger(trocas));
            }
            List<Integer> listaVencer = obterContratosAVencer(professor, inicioVencer, fimVencer, empresaZW);
            dados.setaVencer(listaVencer.size());
            dados.setCodsAVencer(Uteis.concatenarListaInteger(listaVencer));

            List<Integer> listaRenovados = clientesComContratosPrevistosPeriodo(empresaZW, inicio, fim,professor, true, null, null);
            dados.setRenovados(listaRenovados.size());
            dados.setCodsRenovados(Uteis.concatenarListaInteger(listaRenovados));

            List<Integer> listaPrevisaoRenovacao = clientesComContratosPrevistosPeriodo(empresaZW, inicio, fim, professor, false, null, null);

            List<Integer> listaNaoRenovados = Uteis.diferencaEntreListas(listaPrevisaoRenovacao,listaRenovados);
            dados.setCodsNaoRenovados(Uteis.concatenarListaInteger(listaNaoRenovados));
            dados.setNaoRenovados(listaNaoRenovados.size());

            List<Integer> listaAcessaramMes = alunosAcessoPorProfessorPorPeriodo(empresaZW, professor, inicio, fim);
            dados.setCodsAlunosAcessaram(Uteis.concatenarListaInteger(listaAcessaramMes));
            dados.setAlunosAcessaram(listaAcessaramMes.size());

            return dados.toJSON();
        } catch (Exception ex) {
            return ex.getMessage();
        }
    }


    private List<Integer> obterCodsAlunosAtivosSemUsuarioMovel(final Integer empresaZW) throws Exception {
        List<Integer> cods = new ArrayList<>();
        try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sqlAtivosNaoEstaoNoTreino(empresaZW, false, false, null, true), con)) {
            while (rs.next()) {
                cods.add(rs.getInt("codigocliente"));
            }
        }
        return cods;
    }

    private static String sqlAtivosNaoEstaoNoTreino(Integer empresaZW, boolean lista, boolean listaModalidades, String modalidadesFiltro, boolean cods) {
        StringBuilder sql = new StringBuilder("SELECT ");
        if (cods) {
            sql.append(" DISTINCT sw.codigocliente ");
        } else if (listaModalidades) {
            sql.append(" mod.nome as nomemodalidade, mod.codigo as codigomodalidade ");
        } else if (lista) {
            sql.append("sw.nomecliente as nome, sw.matricula, sw.codigopessoa, colp.nome as nomeprofessor, col.codigo as codigoprofessor, ");
            sql.append("ARRAY_TO_STRING(ARRAY(select nome from modalidade where codigo IN (select modalidade FROM contratomodalidade WHERE contrato = ");
            sql.append("sw.codigocontrato)), '|') as modalidades   ");
        } else {
            sql.append("COUNT(DISTINCT sw.codigocliente)");
        }
        sql.append(" FROM situacaoclientesinteticodw sw \n");
        sql.append(" INNER JOIN contrato con ON con.codigo = sw.codigocontrato \n");
        sql.append(" INNER JOIN contratomodalidade conmod ON conmod.contrato = con.codigo \n");
        sql.append(" INNER JOIN modalidade mod ON mod.codigo = conmod.modalidade AND mod.usatreino \n");
        if (lista) {
            sql.append(" LEFT JOIN vinculo vi ON vi.cliente = sw.codigocliente   AND vi.tipovinculo = 'TW' \n");
            sql.append(" LEFT JOIN colaborador col ON col.codigo = vi.colaborador \n");
            sql.append(" LEFT JOIN pessoa colp ON colp.codigo = col.pessoa \n");
        }
        sql.append(" LEFT JOIN usuariomovel u ON u.cliente = sw.codigocliente AND (u.origem = 'ZW' OR u.origem = 'TW') \n");
        sql.append(" WHERE sw.situacao = 'AT' AND u.codigo is null AND sw.empresacliente = ").append(empresaZW);
        if (modalidadesFiltro != null && !modalidadesFiltro.isEmpty()) {
            sql.append(" AND mod.codigo IN (").append(modalidadesFiltro).append(") ");
        }
        if (listaModalidades) {
            sql.append("\n GROUP BY mod.nome, mod.codigo ");
            sql.append(" ORDER BY mod.nome");
        }
        if (lista) {
            sql.append("\n GROUP BY sw.nomecliente, sw.matricula, sw.codigopessoa, colp.nome, col.codigo, sw.codigocontrato");
            sql.append(" ORDER BY sw.nomecliente");
        }
        return sql.toString();
    }

    private void preencherDadosBITreino(DadosBITreinoJSON dados, Date fim, Integer professor) throws Exception {
        dados.setMediaPermanenciaCarteira(obterMediaPermanencia(fim, professor));
        try (ResultSet rsMenor = ConexaoZWServiceImpl.criarConsulta("SELECT * FROM (" + obterSqlMediaPermanenciaCarteira(fim, professor, true)
                + ") AS media WHERE duracao > 0 ORDER BY duracao LIMIT 1", con)) {
            if (rsMenor.next()) {
                dados.setMenorPermanencia(rsMenor.getInt("duracao"));
                dados.setMatriculaClienteMenorPermanencia(rsMenor.getString("matricula"));
                dados.setNomeClienteMenorPermanencia(rsMenor.getString("nome"));
            }
        }
        try (ResultSet rsMaior = ConexaoZWServiceImpl.criarConsulta("SELECT * FROM (" + obterSqlMediaPermanenciaCarteira(fim, professor, true)
                + ") AS media WHERE duracao > 0 ORDER BY duracao DESC LIMIT 1", con)) {
            if (rsMaior.next()) {
                dados.setMaiorPermanencia(rsMaior.getInt("duracao"));
                dados.setMatriculaClienteMaiorPermanencia(rsMaior.getString("matricula"));
                dados.setNomeClienteMaiorPermanencia(rsMaior.getString("nome"));
            }
        }
    }

    private Integer obterMediaPermanencia(Date fim, Integer professor) throws Exception {
        List<Integer> valores;
        try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta("SELECT duracao FROM (" + obterSqlMediaPermanenciaCarteira(fim, professor, false)
                + ") as historico", con)) {
            valores = new ArrayList<Integer>();
            while (rs.next()) {
                int duracao = rs.getInt("duracao");
                if(duracao > 0){
                    valores.add(duracao);
                }
            }
        }
        if(valores.isEmpty()){
            return 0;
        }
        Collections.sort(valores);
        return Uteis.mediana(valores);
    }

    private String obterSqlMediaPermanenciaCarteira(Date fim, Integer professor, boolean cliente) {
        StringBuilder sql = new StringBuilder("SELECT ");
        if (cliente) {
            sql.append("cli.matricula, pes.nome,");
        }
        sql.append("cast(coalesce((SELECT dataregistro FROM historicovinculo \n");
        sql.append("WHERE cliente = hv.cliente AND tipocolaborador = 'TW' \n");
        sql.append("AND colaborador = hv.colaborador AND tipohistoricovinculo = 'SD' \n");
        sql.append("AND dataregistro >= hv.dataregistro ORDER BY dataregistro LIMIT 1),'");
        sql.append(Uteis.getDataAplicandoFormatacao(fim, "yyyy-MM-dd")).append(" 23:59:59') as date) - \n");
        sql.append("cast(hv.dataregistro  as date) AS duracao \n");
        sql.append("FROM historicovinculo hv \n");
        if (cliente) {
            sql.append("INNER JOIN cliente cli ON cli.codigo = hv.cliente\n");
            sql.append("INNER JOIN pessoa pes ON pes.codigo = cli.pessoa\n");
        }
        sql.append("WHERE hv.tipocolaborador = 'TW'\n");
        sql.append("AND hv.colaborador = ").append(professor).append("\n");
        sql.append("AND hv.tipohistoricovinculo = 'EN'\n");
        sql.append("ORDER BY hv.dataregistro DESC ");
        return sql.toString();
    }

    private List<Integer> obterNumeroNovosNaCarteira(Integer empresa, Date inicio, Date fim, Integer professor, boolean novos) throws Exception {
        List<Integer> lista;
        try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(getSqlNovosCarteira(empresa, inicio, fim, professor, novos).toString(), con)) {
            lista = new ArrayList<Integer>();
            while (rs.next()) {
                lista.add(rs.getInt("codigo"));
            }
        }
        return lista;
    }

    private StringBuilder getSqlNovosCarteira(Integer empresa, Date inicio, Date fim, Integer professor, boolean novos) throws Exception {
        StringBuilder sql = new StringBuilder("SELECT distinct cli.codigo ");
        sql.append(" FROM vinculo vi \n");
        sql.append(" INNER JOIN cliente cli ON cli.codigo = vi.cliente AND cli.empresa = ").append(empresa).append(" \n");
        sql.append(" INNER JOIN historicovinculo hv ON vi.tipovinculo = hv.tipocolaborador  \n");
        sql.append(" AND hv.tipohistoricovinculo = 'EN'  \n");
        sql.append(" AND vi.cliente = hv.cliente  \n");
        sql.append(" AND vi.colaborador = hv.colaborador  \n");
        sql.append(" AND vi.colaborador = ").append(professor).append("\n");
        sql.append(" AND vi.tipovinculo = 'TW' \n");
        sql.append(" AND hv.dataregistro::DATE BETWEEN '").append(Uteis.getDataJDBC(inicio));
        sql.append("' AND '").append(Uteis.getDataJDBC(fim)).append("'\n");
        if(novos){
            sql.append(" AND NOT EXISTS ");
        }else{
            sql.append(" AND EXISTS ");
        }
        sql.append("(SELECT codigo FROM historicovinculo WHERE tipohistoricovinculo = 'SD'  AND historicovinculo.tipocolaborador = 'TW' AND historicovinculo.cliente = vi.cliente)\n");
        return sql;
    }

    private List<Integer> obterTrocasCarteira(Integer empresa, Date inicio, Date fim, Integer professor) throws Exception {
        List<Integer> lista;
        try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(getSqlTrocasCarteira(empresa, inicio, fim, professor).toString(), con)) {
            lista = new ArrayList<Integer>();
            while (rs.next()) {
                lista.add(rs.getInt("codigo"));
            }
        }
        return lista;
    }

    private StringBuilder getSqlTrocasCarteira(Integer empresa, Date inicio, Date fim, Integer professor) throws Exception{
        StringBuilder sql = new StringBuilder("SELECT distinct cli.codigo ");
        sql.append(" FROM historicovinculo hv \n");
        sql.append(" INNER JOIN cliente cli ON cli.codigo = hv.cliente AND cli.empresa = ").append(empresa).append("\n");
        sql.append(" WHERE hv.colaborador = ").append(professor).append("\n");
        sql.append(" AND hv.tipocolaborador = 'TW'\n");
        sql.append(" AND hv.dataregistro::DATE BETWEEN '").append(Uteis.getDataJDBC(inicio));
        sql.append("' AND '").append(Uteis.getDataJDBC(fim)).append("' \n");
        sql.append(" AND hv.tipohistoricovinculo = 'SD'");
        return sql;
    }

    private List<Integer> obterContratosAVencer(Integer professor, Date inicio, Date fim, Integer empresaZW) throws Exception {
        StringBuilder sql = getRsContratosAVencer(professor, inicio, fim, empresaZW, false);
        List<Integer> lista = new ArrayList<Integer>();
        try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sql.toString(), con)) {
            while (rs.next()) {
                lista.add(rs.getInt("codigo"));
            }
        }
        return lista;
    }

    private StringBuilder getRsContratosAVencer(Integer professor, Date inicio, Date fim, Integer empresaZW, boolean lista) throws Exception {
        StringBuilder sql = new StringBuilder("SELECT distinct cli.codigo ");
        sql.append(" FROM cliente cli ");
        sql.append(" INNER JOIN contrato con ON con.pessoa = cli.pessoa AND con.dataprevistarenovar  ");
        sql.append(" BETWEEN '").append(Uteis.getDataJDBC(inicio)).append("' AND '");
        sql.append(Uteis.getDataJDBC(fim)).append("' AND datarenovarrealizada IS NULL \n");
        if (lista) {
            sql.append("INNER JOIN pessoa p ON p.codigo = cli.codigo \n");
        }
        sql.append(" INNER JOIN vinculo v ON v.cliente = cli.codigo AND v.tipovinculo = 'TW' \n");
        if (professor != null && professor > 0) {
            sql.append(" AND v.colaborador = ").append(professor).append("\n");
        }
        if (lista) {
            sql.append("INNER JOIN colaborador col ON col.codigo = v.colaborador \n");
            sql.append("INNER JOIN pessoa colp ON colp.codigo = col.pessoa \n");
        }

        sql.append(" WHERE cli.empresa = ").append(empresaZW);
        return sql;
    }


    private List<Integer> alunosAcessoPorProfessorPorPeriodo(int empresaZW, int professor, Date inicio, Date fim) throws Exception {
        StringBuilder sql = new StringBuilder();

        sql.append(" SELECT DISTINCT(acessocliente.cliente) as cliente from acessocliente ").append("\n");
        sql.append(" inner join cliente on cliente.codigo = acessocliente.cliente ").append("\n");
        sql.append(" inner join empresa on empresa.codigo = cliente.empresa ").append("\n");
        if (!UteisValidacao.emptyNumber(professor)) {
            sql.append(" inner join vinculo vin ON vin.colaborador = ").append(professor).append(" and vin.cliente = acessocliente.cliente").append("\n");
        }
        sql.append(" where empresa = ").append(empresaZW);
        sql.append(" and ((acessocliente.dthrentrada >= ? and acessocliente.dthrentrada <= ? )");
        sql.append(" or (acessocliente.dthrsaida >= ? ").append(" and acessocliente.dthrsaida <= ? ))");
        PreparedStatement stm = con.prepareStatement(sql.toString());
        stm.setTimestamp(1, Uteis.getDataJDBCTimestamp(inicio));
        stm.setTimestamp(2, Uteis.getDataJDBCTimestamp(fim));
        stm.setTimestamp(3, Uteis.getDataJDBCTimestamp(inicio));
        stm.setTimestamp(4, Uteis.getDataJDBCTimestamp(fim));

        List<Integer> clientes = new ArrayList<Integer>();
        try (ResultSet tabelaResultado = stm.executeQuery();) {
            while (tabelaResultado.next()) {
                clientes.add(tabelaResultado.getInt("cliente"));
            }
        }
        return clientes;
    }

    private List<Integer> clientesComContratosPrevistosPeriodo(Integer empresa,
                                                              Date dataInicio, Date dataFim,
                                                              Integer professor,
                                                              boolean apenasRenovados,
                                                              String condicaoDuracao,
                                                              String condicaoEspontaneoAgendado) throws Exception {
        List<Integer> clientes = new ArrayList<Integer>();
        List<Integer> codigoContratos = new ArrayList<Integer>();

        boolean filtrarConsultores = true;
        // filtra a consulta pelos colaboradores
        if (UteisValidacao.emptyNumber(professor)) {
            filtrarConsultores = false;
        }
        StringBuilder sqlStr = consultaPrevisaoRenovacao(dataInicio, dataFim, empresa, apenasRenovados, condicaoEspontaneoAgendado, condicaoDuracao);

        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sqlStr.toString())) {
                // caso não haja filtro de colaborador, retornar o valor do count normal
                if (filtrarConsultores) {
                    while (rs.next()) {
                        // verificar o histórico de vinculos
                        boolean possuiVinculoAtual;
                        if (UteisValidacao.emptyString(rs.getString("colaboradores"))) {
                            possuiVinculoAtual = false;
                        } else {
                            possuiVinculoAtual = validarVinculoColaboradores(professor.toString(), rs.getString("colaboradores"), TipoColaboradorZWEnum.PROFESSOR_TREINO);
                        }
                        if (!codigoContratos.contains(rs.getInt("codigocontrato")) && possuiVinculoAtual) {
                            clientes.add(rs.getInt("cliente"));
                            codigoContratos.add(rs.getInt("codigocontrato"));
                        } else if (!codigoContratos.contains(rs.getInt("codigocontrato"))) {
                            if (historicoVinculoVerificarVinculoNaData(dataFim, rs.getInt("cliente"), professor, TipoColaboradorZWEnum.PROFESSOR_TREINO.getSigla())) {
                                clientes.add(rs.getInt("cliente"));
                                codigoContratos.add(rs.getInt("codigocontrato"));
                            }
                        }
                    }
                } else {
                    while (rs.next()) {
                        clientes.add(rs.getInt("cliente"));
                    }
                }
            }
        }
        return clientes;
    }

    private static boolean validarVinculoColaboradores(String sqlCols, String vinculosColaborador, TipoColaboradorZWEnum tipoColaboradorZWEnum) {
        try {
            HashMap map = montarMapaVinculo(vinculosColaborador);
            for (String col : sqlCols.split(",")) {
                Object tipoColaborador = map.get(new Integer(col));
                if (tipoColaborador != null) {
                    if (tipoColaborador.toString().equals(tipoColaboradorZWEnum.getSigla())) {
                        return true;
                    }
                }
            }
        } catch (Exception ignored) {
            return false;
        }
        return false;
    }

    private static HashMap montarMapaVinculo(String vinculosColaborador) {
        HashMap map = new HashMap<Integer,String>();
        for (String cod : vinculosColaborador.split("/")) {
            if (UteisValidacao.emptyString(cod)) {
                continue;
            } else {
                String[] dados = cod.split("-");
                String codCol = dados[dados.length - 1];
                String tipoVin = dados[0];
                map.put(new Integer(codCol),tipoVin);
            }
        }
        return map;
    }

    private StringBuilder consultaPrevisaoRenovacao(Date dataInicio, Date dataFim, Integer empresaZW, boolean apenasRenovados, String condicaoEspontaneoAgendado, String condicaoDuracao) throws Exception {
        StringBuilder sqlStr = new StringBuilder();
        // caso não haja filtro de colaborador, fazer o count normal
        sqlStr.append("SELECT distinct(c.codigo) as codigocontrato, cli.codigocliente AS cliente,cli.situacao,cli.colaboradores,cli.matricula,cli.nomecliente as nome FROM contrato c \n");
        sqlStr.append("INNER JOIN SituacaoClienteSinteticoDW cli ON cli.codigopessoa = c.pessoa \n");
        sqlStr.append("WHERE (c.dataprevistarenovar) >= '" + Uteis.getDataJDBC(dataInicio) + "' AND (c.dataprevistarenovar) <= '" + Uteis.getDataJDBC(dataFim) + "'\n");
        if (empresaZW != 0) {
            sqlStr.append("AND c.empresa = " + empresaZW);
        }

        sqlStr.append(" AND c.situacao <> 'TR' \n");

        if (apenasRenovados) {
            sqlStr.append("AND c.datarenovarrealizada <= '" + Uteis.getDataJDBC(dataFim) + "' AND contratoresponsavelrenovacaomatricula <> 0 \n");
        }
        sqlStr.append("AND (c.situacao <> 'CA' and not exists (select codigo from contratooperacao where contrato = c.codigo and tipooperacao = 'CA'))  AND c.bolsa = false \n");
        if (condicaoEspontaneoAgendado != null) {
            sqlStr.append(condicaoEspontaneoAgendado);
        }
        if (condicaoDuracao != null) {
            sqlStr.append(condicaoDuracao);
        }
        return sqlStr;
    }

    private Boolean historicoVinculoVerificarVinculoNaData(Date data, Integer codigoCliente, Integer codigoColaborador, String tipoColaborador) throws Exception {
        String tipoColaboradorSQL = "";
        if (tipoColaborador.equals("CO")) {
            tipoColaboradorSQL = "AND tipocolaborador = 'CO'\n";
        } else if (tipoColaborador.equals("PR")) {
            tipoColaboradorSQL = "AND tipocolaborador = 'PR'\n";
        } else {
            tipoColaboradorSQL = "AND tipocolaborador NOT IN ('CO', 'PR')\n";
        }

        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT COUNT(codigo) AS cont FROM historicovinculo hv");
        sql.append(" WHERE (cliente = " + codigoCliente + " and hv.tipohistoricovinculo = 'EN' AND dataregistro <= '" + Uteis.getDataJDBC(data) + " 23:59:59' AND colaborador =" + codigoColaborador + "\n" + tipoColaboradorSQL + " ) \n");
        sql.append(" AND not exists ( SELECT cliente from historicovinculo ");
        sql.append(" WHERE colaborador =").append(codigoColaborador)
                .append("\nand cliente = ").append(codigoCliente)
                .append("\nAND hv.tipocolaborador = historicovinculo.tipocolaborador")
                .append("\nAND tipohistoricovinculo = 'SD' ").append(tipoColaboradorSQL)
                .append("\nAND dataregistro <= '" + Uteis.getDataJDBC(data) + " 23:59:59'")
                .append("\nand dataregistro >= hv.dataregistro) \n");

        try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sql.toString(), con)) {
            rs.next();
            return rs.getInt("cont") > 0;
        }
    }

    @Override
    public List<AcessosExecucoesJSON> consultarAcessosZW(String key, Integer codigoprofessor, FiltrosDashboard filtrosDashboard, Date fim) throws SQLException {
       try {
           Date inicio = Uteis.somarDias(fim, -filtrosDashboard.getDiasParaTras());

           StringBuilder sql = new StringBuilder(" select count(ac.codigo) as acessos, count(um.codigo) as acessotreino, ac.dthrentrada::date as dia from acessocliente ac\n");
           sql.append(" INNER JOIN cliente cli ON ac.cliente = cli.codigo \n");
           if (!UteisValidacao.emptyNumber(codigoprofessor)) {
               sql.append(" INNER JOIN vinculo vi ON vi.cliente = cli.codigo AND vi.tipovinculo = 'TW' AND vi.colaborador = ");
               sql.append(codigoprofessor);
           }
           sql.append(" LEFT JOIN usuariomovel um ON um.cliente = ac.cliente \n");
           sql.append(" WHERE ac.dthrentrada BETWEEN ? AND ? \n");
           sql.append(" AND cli.empresa = ? \n");
           sql.append(" GROUP BY ac.dthrentrada::date ");

           PreparedStatement stm = con.prepareStatement(sql.toString());
           stm.setTimestamp(1, Uteis.getDataJDBCTimestamp(Calendario.getDataComHoraZerada(inicio)));
           stm.setTimestamp(2, Uteis.getDataJDBCTimestamp(Calendario.getDataComHora(fim, "23:59")));
           stm.setInt(3, filtrosDashboard.getEmpresa());

           ResultSet rs = stm.executeQuery();
           List<AcessosExecucoesJSON> lista = new ArrayList<>();
           while (rs.next()) {
               AcessosExecucoesJSON acessosExecucoesJSON = new AcessosExecucoesJSON();
               acessosExecucoesJSON.setDia(rs.getString("dia"));
               acessosExecucoesJSON.setAcessos(rs.getInt("acessos"));
               acessosExecucoesJSON.setAcessotreino(rs.getInt("acessoTreino"));

               lista.add(acessosExecucoesJSON);
           }
           return lista;
       } catch (Exception e) {
           throw e;
       }
    }

    @Override
    public List<Integer> consultarAlunosCancelados(String chave, Integer empresa, Integer mes, Integer ano, Integer professor) throws Exception {
        try {
            Date inicio = Calendario.getInstance(ano, mes, 1).getTime();
            Date fim = Uteis.obterUltimoDiaMesUltimaHora(inicio);

            StringBuilder sql = new StringBuilder();

            sql.append(" SELECT c.* from contratooperacao c  \n");
            sql.append(" INNER JOIN contrato con on con.codigo = c.contrato \n");
            sql.append(" INNER JOIN cliente cli on cli.pessoa = con.pessoa\n");
            sql.append(" INNER JOIN historicovinculo h1 on h1.cliente = cli.codigo and h1.tipohistoricovinculo = 'EN' \n");
            sql.append(" and h1.tipocolaborador = 'TW' and h1.colaborador =").append(professor);
            sql.append(" and h1.dataregistro <= c.dataoperacao");
            sql.append(" left join historicovinculo h2 on h2.cliente = cli.codigo and h2.tipohistoricovinculo = 'EN' \n");
            sql.append(" and h2.tipocolaborador = 'TW' and h2.colaborador <> h1.colaborador and h2.dataregistro > h1.dataregistro");
            sql.append(" where cli.empresa =").append(empresa).append("\n");
            sql.append(" and dataoperacao between '").append(inicio).append("'\n");
            sql.append(" and '").append(fim).append("' \n");
            sql.append(" and tipooperacao = 'CA' and h2.codigo is null\n");

            List<Integer> retorno = new ArrayList<>();
            try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sql.toString(), con)) {
                while (rs.next()) {
                    retorno.add(rs.getInt("contrato"));
                }
            }
            return retorno;
        } catch (Exception e) {
            throw e;
        }
    }
}
