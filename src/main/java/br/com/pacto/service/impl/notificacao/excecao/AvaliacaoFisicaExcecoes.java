package br.com.pacto.service.impl.notificacao.excecao;

import br.com.pacto.service.exception.ExcecaoSistema;

/**
 * Created by paulo 09/11/2018
 */
public enum AvaliacaoFisicaExcecoes implements ExcecaoSistema {

    ERRO_AVALIACAO_FISICA_NAO_INFORMADA("erro_avaliacao_fisica_nao_informada", "Erro avaliação física não informada"),
    ERRO_BUSCAR_AVALIACAO_FISICA("erro_ao_buscar_avaliacao_fisica", "Erro ao buscar avaliação física"),
    PARQ_NAO_RESPONDIDO("parq_nao_respondido", "ParQ não respondido")
    ;

    private String chave;
    private String descricao;

    AvaliacaoFisicaExcecoes(String chave, String descricao) {
        this.chave = chave;
        this.descricao = descricao;
    }

    @Override
    public String getChaveExcecao() {
        return chave;
    }

    @Override
    public String getDescricaoExcecao() {
        return descricao;
    }
}
