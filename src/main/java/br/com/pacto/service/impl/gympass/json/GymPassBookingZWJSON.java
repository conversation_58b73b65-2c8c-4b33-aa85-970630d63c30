/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.impl.gympass.json;

import br.com.pacto.controller.json.base.SuperJSON;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 07/04/2020
 */
public class GymPassBookingZWJSON extends SuperJSON {

    private String operacao;
    private String chave;
    private Integer empresaZw;

    private Integer turma;
    private Integer idClasseGymPass;
    private Integer produtoGymPass;


    private String clienteUniqueToken;
    private String clienteNome;
    private String clienteEmail;
    private String clienteTelefone;

    public GymPassBookingZWJSON() {
    }

    public String getOperacao() {
        return operacao;
    }

    public void setOperacao(String operacao) {
        this.operacao = operacao;
    }

    public String getChave() {
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    public Integer getEmpresaZw() {
        return empresaZw;
    }

    public void setEmpresaZw(Integer empresaZw) {
        this.empresaZw = empresaZw;
    }

    public Integer getTurma() {
        return turma;
    }

    public void setTurma(Integer turma) {
        this.turma = turma;
    }

    public Integer getIdClasseGymPass() {
        return idClasseGymPass;
    }

    public void setIdClasseGymPass(Integer idClasseGymPass) {
        this.idClasseGymPass = idClasseGymPass;
    }

    public String getClienteUniqueToken() {
        return clienteUniqueToken;
    }

    public void setClienteUniqueToken(String clienteUniqueToken) {
        this.clienteUniqueToken = clienteUniqueToken;
    }

    public String getClienteNome() {
        return clienteNome;
    }

    public void setClienteNome(String clienteNome) {
        this.clienteNome = clienteNome;
    }

    public String getClienteEmail() {
        return clienteEmail;
    }

    public void setClienteEmail(String clienteEmail) {
        this.clienteEmail = clienteEmail;
    }

    public String getClienteTelefone() {
        return clienteTelefone;
    }

    public void setClienteTelefone(String clienteTelefone) {
        this.clienteTelefone = clienteTelefone;
    }

    public Integer getProdutoGymPass() {
        return produtoGymPass;
    }

    public void setProdutoGymPass(Integer produtoGymPass) {
        this.produtoGymPass = produtoGymPass;
    }
}
