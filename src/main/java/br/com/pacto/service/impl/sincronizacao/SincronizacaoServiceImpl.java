/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.impl.sincronizacao;

import br.com.pacto.service.intf.sincronizacao.SincronizacaoService;
import br.com.pacto.bean.sincronizacao.HistoricoRevisao;
import br.com.pacto.bean.sincronizacao.TipoClassSincronizarEnum;
import br.com.pacto.bean.sincronizacao.TipoRevisaoEnum;
import br.com.pacto.dao.intf.sincronizacao.HistoricoRevisaoDao;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.util.UtilContext;
import br.com.pacto.util.impl.UtilReflection;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR>
 */
@Service
public class SincronizacaoServiceImpl implements SincronizacaoService {

    @Autowired
    private HistoricoRevisaoDao historicoRevisaoDao;

    public HistoricoRevisaoDao getHistoricoRevisaoDao() {
        return historicoRevisaoDao;
    }

    private List<HistoricoRevisao> obterRevisoes(final String ctx,
            final TipoClassSincronizarEnum entidade, Date dataBase) throws ServiceException {
        if (dataBase != null) {
            StringBuilder where = new StringBuilder("WHERE entidade = :entidade and dataRegistro > :dataRegistro");
            Map<String, Object> p = new HashMap<String, Object>();
            p.put("entidade", entidade);
            p.put("dataRegistro", dataBase);
            try {
                return historicoRevisaoDao.findByParam(ctx, where, p);
            } catch (Exception ex) {
                Logger.getLogger(SincronizacaoServiceImpl.class.getName()).log(Level.SEVERE, null, ex);
                throw new ServiceException(ex);
            }
        }


        return null;
    }

    @Override
    public List obterLista(final String ctx, TipoClassSincronizarEnum tipoClass,
            Date dataBase) throws ServiceException {
        List<HistoricoRevisao> revisoes = obterRevisoes(ctx, tipoClass, dataBase);
        List lista = new ArrayList();
        try {
            for (HistoricoRevisao h : revisoes) {
                Object bean;
                if (h.getTipoRevisao().equals(TipoRevisaoEnum.DELETE)) {
                    //objeto não existe mais: retornar vazio, apenas com o codigo
                    bean = tipoClass.getClassEntity().newInstance();
                    UtilReflection.setValor(bean, h.getChavePrimaria(), "codigo");
                } else {
                    Object servicoDao = UtilContext.getBean(tipoClass.getClassOfService());
                    bean = UtilReflection.invoke(servicoDao, "findById",
                            new Class[]{String.class, Serializable.class}, new Object[]{
                        ctx, h.getChavePrimaria()});
                }
                if (bean != null) {
                    lista.add(bean);
                } else {
                    //objeto não existe mais: retornar vazio, apenas com o codigo
                    bean = tipoClass.getClassEntity().newInstance();
                    UtilReflection.setValor(bean, h.getChavePrimaria(), "codigo");
                    lista.add(bean);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e);
        }
        return lista;
    }
}