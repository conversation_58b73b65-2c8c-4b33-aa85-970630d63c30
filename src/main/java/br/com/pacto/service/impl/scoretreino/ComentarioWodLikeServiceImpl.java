package br.com.pacto.service.impl.scoretreino;

import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.bean.wod.ComentarioWod;
import br.com.pacto.bean.wod.ComentarioWodLike;
import br.com.pacto.dao.intf.wod.ComentarioWodLikeDao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.scoretreino.ComentarioWodLikeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/*
 * Created by <PERSON><PERSON>
 */
@Service
public class ComentarioWodLikeServiceImpl implements ComentarioWodLikeService {

    @Autowired
    private ComentarioWodLikeDao comentarioWodLikeDao;


    public ComentarioWodLikeDao getComentarioWodLikeDao() {
        return comentarioWodLikeDao;
    }

    @Override
    public ComentarioWodLike obterPorId(String ctx, Integer id) throws ServiceException {
        try {
            return getComentarioWodLikeDao().findById(ctx, id);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public List<ComentarioWodLike> obterPorParam(String ctx, String query, Map<String, Object> params) throws ServiceException {
        try {
            return getComentarioWodLikeDao().findByParam(ctx, query, params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public ComentarioWodLike inserir(final String ctx, ComentarioWodLike object) throws ServiceException {
        try {
            return getComentarioWodLikeDao().insert(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public ComentarioWodLike alterar(final String ctx, ComentarioWodLike object) throws ServiceException {
        try {
            return getComentarioWodLikeDao().update(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public void excluir(final String ctx, ComentarioWodLike object) throws ServiceException {
        try {
            getComentarioWodLikeDao().delete(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public void excluirComentarioWodLike(final String ctx, ComentarioWod comentarioWod, Usuario usuario) throws ServiceException {
        try {
            getComentarioWodLikeDao().deleteComParam(ctx, new String[]{"comentarioWod.codigo", "usuario.codigo"}, new Object[]{comentarioWod.getCodigo(), usuario.getCodigo()});
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public void adicionarComentarioWodLike(final String ctx, ComentarioWod comentarioWod, Usuario usuario) throws ServiceException {
        try {
            excluirComentarioWodLike(ctx, comentarioWod, usuario);

            ComentarioWodLike obj = new ComentarioWodLike();
            obj.setWod(comentarioWod.getWod());
            obj.setComentarioWod(comentarioWod);
            obj.setUsuario(usuario);
            inserir(ctx, obj);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public boolean existeComentarioWodLike(final String ctx, ComentarioWod comentarioWod, Usuario usuario) throws ServiceException {
        try {
            StringBuilder sql = new StringBuilder();
            sql.append(" comentarioWod_codigo = ").append(comentarioWod.getCodigo());
            sql.append(" and usuario_codigo = ").append(usuario.getCodigo());
            return getComentarioWodLikeDao().existsWithParam(ctx, sql);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public void verificarExcluirAntigos(final String ctx, ComentarioWod comentarioWod, Usuario usuario) throws ServiceException {
        try {
            getComentarioWodLikeDao().deleteComParam(ctx, new String[]{"comentarioWod.codigo", "usuario.codigo"}, new Object[]{comentarioWod.getCodigo(), usuario.getCodigo()});
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }
}
