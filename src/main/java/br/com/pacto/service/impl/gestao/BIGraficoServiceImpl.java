package br.com.pacto.service.impl.gestao;

import br.com.pacto.bean.bi.DashboardBI;
import br.com.pacto.bean.gestao.ItemGraficoTO;
import br.com.pacto.bean.gestao.ItemGrupoIndicadores;
import br.com.pacto.bean.professor.ProfessorSintetico;
import br.com.pacto.controller.json.gestao.BIGraficoResponseIndicadoresDTO;
import br.com.pacto.controller.json.gestao.BIGraficoResponseComProfessorDTO;
import br.com.pacto.controller.json.gestao.BIGraficoResponseSemProfessorDTO;
import br.com.pacto.dao.intf.dashboardbi.DashboardBIDao;
import br.com.pacto.dao.intf.grafico.ItemGrupoIndicadoresDao;
import br.com.pacto.dao.intf.professor.ProfessorSinteticoDao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.gestao.BIGraficoService;
import br.com.pacto.service.intf.gestao.DashboardBIService;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;
import org.olap4j.impl.ArrayMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.ZonedDateTime;
import java.util.*;

/**
 * <AUTHOR> Anderson
 * @since 18/10/2019
 */

@Service
public class BIGraficoServiceImpl implements BIGraficoService {

    @Autowired
    private DashboardBIDao dashboardDao;
    @Autowired
    private ItemGrupoIndicadoresDao itemGrupoDao;
    @Autowired
    private ProfessorSinteticoDao professorsinteticoDao;

    public DashboardBIService getService() {
        return UtilContext.getBean(DashboardBIService.class);
    }


    @Override
    public List<BIGraficoResponseSemProfessorDTO> obterDadosSemProfessor(List<DashboardBI> ListaTodosDash, List<ItemGraficoTO> indicadores, Integer mesesPesquisa, String nomeIndicador) {
        List<BIGraficoResponseSemProfessorDTO> listaMap = new ArrayList<>();
        try {
            List<String> adicionados = new ArrayList<>();
            Map<String, List<DashboardBI>> mapaDados = new HashMap<>();
            List<Date> meses = new ArrayList<>();

            for (DashboardBI d : ListaTodosDash) {
                String mes = (d.getMes() > 9 ? "" : "0") + d.getMes() + "/" + d.getAno();
                List<DashboardBI> dds = mapaDados.computeIfAbsent(mes, k -> new ArrayList<>());
                dds.add(d);
                if(!meses.contains(Uteis.getDate("01/"+mes))){
                    meses.add(Uteis.getDate("01/"+mes));
                }
            }
            Collections.sort(meses);
            Collections.reverse(meses);
            for (Date dMes : meses) {
                Date date = Date.from(ZonedDateTime.now().minusMonths(mesesPesquisa).toInstant());
                String mesAno = Calendario.getData(date, "MM/yyyy");
                Date dataPesquisa = Uteis.getDate("01/"+mesAno);
                if (Calendario.maior(dMes,dataPesquisa)){
                    String mes = Uteis.getDataAplicandoFormatacao(dMes, "MM/yyyy");
                    BIGraficoResponseSemProfessorDTO biGraficoResponseGeralDTO = new BIGraficoResponseSemProfessorDTO();
                    biGraficoResponseGeralDTO.setPeriodo(mes);
                    BIGraficoResponseIndicadoresDTO biGraficoResponseIndicadoresDTO = new BIGraficoResponseIndicadoresDTO();
                    for (DashboardBI d : mapaDados.get(mes)) {
                        if (adicionados.contains(d.getCodigoProfessor() + " - " + mes)) {
                            continue;
                        }
                        if (nomeIndicador.equals("Avaliação do treino dos alunos")) {
                            if (d.getNrAvaliacoesTreino() > 0) {
                                adicionados.add(d.getCodigoProfessor() + " - " + mes);
                                for (ItemGraficoTO tg : indicadores) {
                                    verificandoIndicadorEAdicionando(d, tg, biGraficoResponseIndicadoresDTO);
                                }
                            }
                        } else {
                            adicionados.add(d.getCodigoProfessor() + " - " + mes);
                            for (ItemGraficoTO tg : indicadores) {
                                verificandoIndicadorEAdicionando(d,tg,biGraficoResponseIndicadoresDTO);
                            }
                        }
                    }
                    biGraficoResponseGeralDTO.setValor(biGraficoResponseIndicadoresDTO);
                    listaMap.add(biGraficoResponseGeralDTO);
                }
            }
            Collections.reverse(listaMap);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return listaMap;
    }

    @Override
    public List<BIGraficoResponseComProfessorDTO> obterDadosPorProfessor(List<DashboardBI> ListaTodosDash,
                                                                         List<ItemGraficoTO> indicadores,
                                                                         Integer mesesPesquisa,
                                                                         List<ProfessorSintetico> ProfessorId) {
        List<BIGraficoResponseComProfessorDTO> listaMap = new ArrayList<>();
        try {
            Map<String, List<DashboardBI>> mapaDados = new HashMap<>();
            List<Date> meses = new ArrayList<>();

            for (DashboardBI d : ListaTodosDash) {
                String mes = (d.getMes() > 9 ? "" : "0") + d.getMes() + "/" + d.getAno();
                List<DashboardBI> dds = mapaDados.computeIfAbsent(mes, k -> new ArrayList<>());
                dds.add(d);
                if(!meses.contains(Uteis.getDate("01/"+mes))){
                    meses.add(Uteis.getDate("01/"+mes));
                }
            }
            Collections.sort(meses);
            Collections.reverse(meses);
                    for (Date dMes : meses) {
                        Date date = Date.from(ZonedDateTime.now().minusMonths(mesesPesquisa).toInstant());
                        String mesAno = Calendario.getData(date, "MM/yyyy");
                        Date dataPesquisa = Uteis.getDate("01/" + mesAno);
                        if (Calendario.maiorOuIgual(dMes,dataPesquisa)){
                            String mes = Uteis.getDataAplicandoFormatacao(dMes, "MM/yyyy");
                            BIGraficoResponseComProfessorDTO biGraficoResponseComProfessorDTO = new BIGraficoResponseComProfessorDTO();
                            biGraficoResponseComProfessorDTO.setPeriodo(mes);
                            Map<String, BIGraficoResponseIndicadoresDTO> valorPorProfessor = new ArrayMap<>();
                            for (ProfessorSintetico professor : ProfessorId) {
                                BIGraficoResponseIndicadoresDTO biGraficoResponseIndicadoresDTO = new BIGraficoResponseIndicadoresDTO();
                                for (DashboardBI d : mapaDados.get(mes)) {
                                    if (d.getCodigoProfessor().equals(professor.getCodigo())){
                                        for (ItemGraficoTO tg : indicadores) {
                                            verificandoIndicadorEAdicionando(d,tg,biGraficoResponseIndicadoresDTO);
                                        }
                                    }
                                    valorPorProfessor.put(professor.getCodigoColaborador().toString(), biGraficoResponseIndicadoresDTO);
                                }

                            }
                            biGraficoResponseComProfessorDTO.setValorPorProfessor(valorPorProfessor);
                            listaMap.add(biGraficoResponseComProfessorDTO);
                        }
                    }
                Collections.reverse(listaMap);
            } catch (Exception e) {
                e.printStackTrace();
            }
        return listaMap;
    }

    public List<DashboardBI> obterDash(final String ctx, final Integer professor, final Integer empresa) throws ServiceException {
        try {
            String sql = " SELECT obj FROM DashboardBI obj\n" ;
            String where = "WHERE obj.codigoProfessor = " +
                    professor + "\n" +
                    "AND obj.empresa = " +
                    empresa + "\n" +
                    "ORDER BY obj.ano DESC, obj.mes DESC, obj.dia DESC LIMIT 1";
            sql = sql + where;
            return dashboardDao.findByParam(ctx, sql, new HashMap<>());
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public List<ItemGrupoIndicadores> obterGruposIndicadores(final String ctx) throws ServiceException{
        try {
            return itemGrupoDao.findAll(ctx);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public void gravarGrupoIndicador(String ctx, String nome, List<ItemGraficoTO> itens, List<String> profs) throws ServiceException{
        try {
            if(UteisValidacao.emptyString(nome)){
                throw new Exception("nomesalvargrupografico");
            }
            StringBuilder professores = new StringBuilder();
            for(String s : profs){
                professores.append(";").append(s);
            }
            professores = new StringBuilder(professores.toString().replaceFirst(";", ""));
            for(ItemGraficoTO i : itens){
                if(i.isSelecionado()){
                    ItemGrupoIndicadores igi = new ItemGrupoIndicadores();
                    igi.setNome(nome);
                    igi.setIndicador(i.getIndicador());
                    igi.setTipo(i.getTipo());
                    igi.setProfessores(professores.toString());
                    itemGrupoDao.insert(ctx, igi);
                }
            }
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public void excluirGrupo(final String ctx, final String nome) throws ServiceException{
        try {
            itemGrupoDao.deleteComParam(ctx, new String[]{"nome"}, new Object[]{nome});
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public ProfessorSintetico obterPorCodigoPessoaZW(final String ctx, Integer codigoPessoa, Integer codigoEmpresaZw) throws ServiceException {
        try {
            return professorsinteticoDao.findObjectByAttributes(ctx, new String[]{"codigoPessoa", "empresa.codZW"}, new Object[]{codigoPessoa, codigoEmpresaZw}, "nome");
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public ProfessorSintetico obterPorId(final String ctx, Integer id) throws ServiceException {
        try {
            return professorsinteticoDao.obterPorId(ctx, id);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public ProfessorSintetico obterPorIdColaborador(final String ctx, Integer id) throws ServiceException {
        try {
            return professorsinteticoDao.obterPorIdColaborador(ctx, id);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    private void verificandoIndicadorEAdicionando(DashboardBI d, ItemGraficoTO tg, BIGraficoResponseIndicadoresDTO biGraficoResponseIndicadoresDTO) {
        if (tg.isSelecionado()) {
            switch (tg.getIndicador()) {
                case IND_TOTAL_ALUNOS:
                    if (biGraficoResponseIndicadoresDTO.getTotal() == null) {
                        biGraficoResponseIndicadoresDTO.setTotal(d.getTotalAlunos());
                    }
                    break;
                case IND_ATIVOS:
                    if (biGraficoResponseIndicadoresDTO.getAtivos() == null) {
                        biGraficoResponseIndicadoresDTO.setAtivos(d.getTotalAlunosAtivos());
                    }
                    break;
                case IND_INATIVOS:
                    if (biGraficoResponseIndicadoresDTO.getInativos() == null) {
                        biGraficoResponseIndicadoresDTO.setInativos(d.getTotalAlunosInativos());
                    }
                    break;
                case IND_ATIVOS_TREINO:
                    if (biGraficoResponseIndicadoresDTO.getAtivostreino() == null) {
                        biGraficoResponseIndicadoresDTO.setAtivostreino(d.getTotalAlunosTreino());
                    }
                    break;
                case IND_EM_DIA:
                    if (biGraficoResponseIndicadoresDTO.getEmdia() == null) {
                        biGraficoResponseIndicadoresDTO.setEmdia(d.getTotalTreinosEmdia());
                    }
                    break;
                case IND_VENCIDOS:
                    if (biGraficoResponseIndicadoresDTO.getTreinosvencidos() == null) {
                        biGraficoResponseIndicadoresDTO.setTreinosvencidos(d.getTotalTreinosVencidos());
                    }
                    break;
                case IND_RENOVAR:
                    if (biGraficoResponseIndicadoresDTO.getTreinosrenovar() == null) {
                        biGraficoResponseIndicadoresDTO.setTreinosrenovar(d.getTotalTreinosRenovar());
                    }
                    break;
                case IND_AGENDAMENTOS:
                    if (biGraficoResponseIndicadoresDTO.getAgendamentos() == null) {
                        biGraficoResponseIndicadoresDTO.setAgendamentos(d.getAgendamentos());
                    }
                    break;
                case IND_AGENDAMENTOS_EXECUTARAM:
                    if (biGraficoResponseIndicadoresDTO.getAgexecutaram() == null) {
                        biGraficoResponseIndicadoresDTO.setAgexecutaram(d.getCompareceram());
                    }
                    break;
                case IND_AGENDAMENTOS_FALTARAM:
                    if (biGraficoResponseIndicadoresDTO.getAgfaltaram() == null) {
                        biGraficoResponseIndicadoresDTO.setAgfaltaram(d.getFaltaram());
                    }
                    break;
                case IND_AGENDAMENTOS_CANCELARAM:
                    if (biGraficoResponseIndicadoresDTO.getAgcancelaram() == null) {
                        biGraficoResponseIndicadoresDTO.setAgcancelaram(d.getCancelaram());
                    }
                    break;
                case IND_RENOVADOS:
                    if (biGraficoResponseIndicadoresDTO.getRenovados() == null) {
                        biGraficoResponseIndicadoresDTO.setRenovados(d.getTotalRenovacoesCarteira());
                    }
                    break;
                case IND_NAO_RENOVADOS:
                    if (biGraficoResponseIndicadoresDTO.getNaorenovados() == null) {
                        biGraficoResponseIndicadoresDTO.setNaorenovados(d.getTotalNaoRenovaramCarteira());
                    }
                    break;
                case IND_A_VENCER:
                    if (biGraficoResponseIndicadoresDTO.getAvencer() == null) {
                        biGraficoResponseIndicadoresDTO.setAvencer(d.getTotalAlunosAvencer());
                    }
                    break;
                case IND_NOVOS_CARTEIRA:
                    if (biGraficoResponseIndicadoresDTO.getNovoscarteira() == null) {
                        biGraficoResponseIndicadoresDTO.setNovoscarteira(d.getTotalNovosCarteiraNovos());
                    }
                    break;
                case IND_TROCARAM_CARTEIRA:
                    if (biGraficoResponseIndicadoresDTO.getTrocaramcarteira() == null) {
                        biGraficoResponseIndicadoresDTO.setTrocaramcarteira(d.getTotalTrocaramCarteira());
                    }
                    break;
                case IND_PERCENTUAL_RENOVACAO:
                    if (biGraficoResponseIndicadoresDTO.getPercentualrenovacao() == null) {
                        biGraficoResponseIndicadoresDTO.setPercentualrenovacao(d.getPercentualRenovacoes());
                    }
                    break;
                case IND_TEMPO_MEDIO_CARTEIRA:
                    if (biGraficoResponseIndicadoresDTO.getMediocarteira() == null) {
                        biGraficoResponseIndicadoresDTO.setMediocarteira(d.getTempoMedioPermanenciaCarteira());
                    }
                    break;
                case IND_SEM_TREINO:
                    if (biGraficoResponseIndicadoresDTO.getSemtreino() == null) {
                        biGraficoResponseIndicadoresDTO.setSemtreino(d.getTotalAlunosSemTreino());
                    }
                    break;
                case IND_PERC_TREINO_EM_DIA:
                    if (biGraficoResponseIndicadoresDTO.getPercentualemdia() == null) {
                        biGraficoResponseIndicadoresDTO.setPercentualemdia(d.getPercentualEmDia());
                    }
                    break;
                case IND_PERC_TREINO_VENCIDOS:
                    if (biGraficoResponseIndicadoresDTO.getPercentualvencidos() == null) {
                        biGraficoResponseIndicadoresDTO.setPercentualvencidos(((d.getTotalTreinosEmdia()) + d.getTotalTreinosVencidos()) == 0
                                ? 0
                                : (d.getTotalTreinosVencidos() * 100) / (d.getTotalTreinosEmdia() + d.getTotalTreinosVencidos()));
                    }
                    break;
                case IND_TEMPO_MEDIO_PROGRAMA:
                    if (biGraficoResponseIndicadoresDTO.getMediorprograma() == null) {
                        biGraficoResponseIndicadoresDTO.setMediorprograma(d.getTempoMedioPermanenciaTreino());
                    }
                    break;
                case IND_NR_AVALIACOES:
                    if (biGraficoResponseIndicadoresDTO.getNravaliacoes() == null) {
                        biGraficoResponseIndicadoresDTO.setNravaliacoes(d.getNrAvaliacoesTreino());
                    }
                    break;
                case IND_MEDIA_AVALIACOES:
                    if (biGraficoResponseIndicadoresDTO.getAvaliacoes() == null) {
                        biGraficoResponseIndicadoresDTO.setAvaliacoes(Uteis.arredondar(d.getMediaValorAvaliacao(), 1));
                    }
                    break;
                case IND_ESTRELA_1:
                    if (biGraficoResponseIndicadoresDTO.getEstrelaum() == null) {
                        biGraficoResponseIndicadoresDTO.setEstrelaum(d.getNr1estrelas());
                    }
                    break;
                case IND_ESTRELA_2:
                    if (biGraficoResponseIndicadoresDTO.getEstreladois() == null) {
                        biGraficoResponseIndicadoresDTO.setEstreladois(d.getNr2estrelas());
                    }
                    break;
                case IND_ESTRELA_3:
                    if (biGraficoResponseIndicadoresDTO.getEstrelatres() == null) {
                        biGraficoResponseIndicadoresDTO.setEstrelatres(d.getNr3estrelas());
                    }
                    break;
                case IND_ESTRELA_4:
                    if (biGraficoResponseIndicadoresDTO.getEstrelaquatro() == null) {
                        biGraficoResponseIndicadoresDTO.setEstrelaquatro(d.getNr4estrelas());
                    }
                    break;
                case IND_ESTRELA_5:
                    if (biGraficoResponseIndicadoresDTO.getEstrelacinco() == null) {
                        biGraficoResponseIndicadoresDTO.setEstrelacinco(d.getNr5estrelas());
                    }
                    break;
                case IND_COM_AVALIACAO:
                    if (biGraficoResponseIndicadoresDTO.getComavaliacao() == null) {
                        biGraficoResponseIndicadoresDTO.setComavaliacao(d.getTotalAlunosAvaliacoes());
                    }
                    break;
                case IND_SEM_AVALIACAO:
                    if (biGraficoResponseIndicadoresDTO.getSemavaliacao() == null) {
                        biGraficoResponseIndicadoresDTO.setSemavaliacao(d.getTotalAlunosSemAvaliacoes());
                    }
                    break;
                case IND_AGENDAMENTO_PROFESSORES:
                    if (biGraficoResponseIndicadoresDTO.getAgprofessores() == null) {
                        biGraficoResponseIndicadoresDTO.setAgprofessores(d.getProfessores());
                    }
                    break;
                case IND_HORAS_DISPONIBILIDADE:
                    if (biGraficoResponseIndicadoresDTO.getHorasdisponibilidade() == null) {
                        biGraficoResponseIndicadoresDTO.setHorasdisponibilidade(d.getHorasDisponibilidade());
                    }
                    break;
                case IND_HORAS_EXECUTADAS:
                    if (biGraficoResponseIndicadoresDTO.getHorasexecutadas() == null) {
                        biGraficoResponseIndicadoresDTO.setHorasexecutadas(d.getHorasAtendimento());
                    }
                    break;
                case IND_PERC_OCUPACAO:
                    if (biGraficoResponseIndicadoresDTO.getPercocupacao() == null) {
                        biGraficoResponseIndicadoresDTO.setPercocupacao(d.getOcupacao());
                    }
                    break;
                case IND_AGENDAMENTO_NOVOS_TREINOS:
                    if (biGraficoResponseIndicadoresDTO.getAgnovostreinos() == null) {
                        biGraficoResponseIndicadoresDTO.setAgnovostreinos(d.getNovosTreinos());
                    }
                    break;
                case IND_AGENDAMENTO_TREINO_RENOVADOS:
                    if (biGraficoResponseIndicadoresDTO.getAgtreinosrenovados() == null) {
                        biGraficoResponseIndicadoresDTO.setAgtreinosrenovados(d.getTreinosRenovados());
                    }
                    break;
                case IND_AGENDAMENTO_TREINO_REVISADOS:
                    if (biGraficoResponseIndicadoresDTO.getAgtreinosrevisados() == null) {
                        biGraficoResponseIndicadoresDTO.setAgtreinosrevisados(d.getTreinosRevisados());
                    }
                    break;
                case IND_AGENDAMENTO_AVALIACOES_FISICAS:
                    if (biGraficoResponseIndicadoresDTO.getAgavaliacaofisica() == null) {
                        biGraficoResponseIndicadoresDTO.setAgavaliacaofisica(d.getAvaliacoesFisicas());
                    }
                    break;
            }
        }
    }

}
