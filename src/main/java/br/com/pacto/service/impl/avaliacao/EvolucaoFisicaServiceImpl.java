package br.com.pacto.service.impl.avaliacao;

import br.com.pacto.bean.avaliacao.AvaliacaoFisica;
import br.com.pacto.bean.avaliacao.EvolucaoFisica;
import br.com.pacto.bean.avaliacao.PerimetriaEnum;
import br.com.pacto.bean.avaliacao.evolucao.*;
import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.configuracoes.ConfiguracaoSistema;
import br.com.pacto.bean.configuracoes.ConfiguracoesEnum;
import br.com.pacto.bean.programa.ProgramaTreino;
import br.com.pacto.dao.intf.serie.TreinoRealizadoDao;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.avaliacao.AvaliacaoFisicaService;
import br.com.pacto.service.intf.avaliacao.EvolucaoFisicaService;
import br.com.pacto.service.intf.cliente.ClienteSinteticoService;
import br.com.pacto.service.intf.configuracoes.ConfiguracaoSistemaService;
import br.com.pacto.service.intf.programa.ProgramaTreinoService;
import br.com.pacto.service.intf.usuario.UsuarioService;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;
import br.com.pacto.util.ViewUtils;
import br.com.pacto.util.bean.GenericoTO;
import br.com.pacto.util.impl.Ordenacao;
import br.com.pacto.util.impl.UtilReflection;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * Created by alcides on 27/09/2017.
 */
@Service
class EvolucaoFisicaServiceImpl implements EvolucaoFisicaService {
    @Autowired
    private TreinoRealizadoDao treinoDao;
    @Autowired
    private ProgramaTreinoService ps;
    @Autowired
    private ViewUtils viewUtils;
    @Autowired
    private SessaoService sessaoService;
    @Autowired
    private UsuarioService usuarioService;
    @Autowired
    private AvaliacaoFisicaService avaliacaoFisicaService;
    @Autowired
    private ClienteSinteticoService clienteSinteticoService;


    @Override
    public EvolucaoFisica dadosEvolucaoFisica(String ctx, List<AvaliacaoFisica> avaliacoes, ClienteSintetico cliente) throws Exception {
        List<AvaliacaoFisica> navs = Ordenacao.ordenarLista(new ArrayList<AvaliacaoFisica>(avaliacoes), "dataAvaliacao");
        EvolucaoFisica dados = new EvolucaoFisica();
        if (navs.isEmpty()) {
            return dados;
        }
        AvaliacaoFisica avaliacaoAtual = navs.get(navs.size() - 1);
        dados.setMeta(avaliacaoAtual.getMetaPercentualGorduraAnterior());
        dados.setNrAvaliacoes(navs.size());
        dados.setPercentualGordura(Uteis.arredondarForcando2CasasDecimais(avaliacaoAtual.getPercentualGordura()));
        dados.setPercentualMassaMagra(porcentualMassaMagra(avaliacaoAtual));
        dados.setMassaGorda(Uteis.arredondarForcando2CasasDecimais(avaliacaoAtual.getMassaGorda()));
        dados.setMassaMagra(Uteis.arredondarForcando2CasasDecimais(avaliacaoAtual.getMassaMagra()));

        for (AvaliacaoFisica avaliacaoFisica : navs) {
            Map<String, Object> avaliacao = new HashMap<String, Object>();
            avaliacao.put("dia", Uteis.getDataAplicandoFormatacao(avaliacaoFisica.getDataAvaliacao(), "dd/MM"));
            avaliacao.put("peso", avaliacaoFisica.getPeso());
            avaliacao.put("gordura", avaliacaoFisica.getMassaGorda());
            avaliacao.put("percgordura", avaliacaoFisica.getPercentualGordura());
            dados.getPesoXgordura().add(avaliacao);

            dados.getDobras().add(getDobras(avaliacaoFisica, "dd/MM"));
        }

        if (navs.size() == 1) {
            dados.setMsgNrAvaliacoes(viewUtils.getMensagem("apenas.uma.avaliacao"));
            dados.setSitPercentual("igual");
            dados.setSitPercentualMagra("igual");
            dados.setMsgPercentual("");
            dados.setMsgPercentualMagra("");
            dados.setMassaGordaInicial(Uteis.arredondarForcando2CasasDecimais(avaliacaoAtual.getMassaGorda()));
            dados.setMassaMagraInicial(Uteis.arredondarForcando2CasasDecimais(avaliacaoAtual.getMassaMagra()));

        } else {
            AvaliacaoFisica primeiraAvaliacao = navs.get(0);
            Date primeira = primeiraAvaliacao.getDataAvaliacao();
            Date ultima = avaliacaoAtual.getDataAvaliacao();

            Long l = new Long(Uteis.nrDiasEntreDatas(primeira, ultima));
            dados.setMsgNrAvaliacoes(String.format(viewUtils.getMensagem("avaliacoes.dias"), l.intValue()));
            dados.setSitPercentual(primeiraAvaliacao.getPercentualGordura() > avaliacaoAtual.getPercentualGordura() ?
                    "queda" : primeiraAvaliacao.getPercentualGordura().equals(avaliacaoAtual.getPercentualGordura()) ? "igual" : "aumento");

            dados.setSitPercentualMagra(dados.getSitPercentual().equalsIgnoreCase("igual") ?
                    "igual" : dados.getSitPercentual().equalsIgnoreCase("aumento") ? "queda" : "aumento");

            dados.setMsgPercentual(dados.getSitPercentual().equals("queda") ?
                    viewUtils.getMensagem("queda.percentual.gordura") : dados.getSitPercentual().equals("aumento") ?
                    viewUtils.getMensagem("aumento.percentual.gordura") : "");

            dados.setMsgPercentualMagra(dados.getSitPercentualMagra().equals("queda") ?
                    viewUtils.getMensagem("queda.percentual.magra") : dados.getSitPercentualMagra().equals("aumento") ?
                    viewUtils.getMensagem("aumento.percentual.magra") : "");

            dados.setMassaGordaInicial(Uteis.arredondarForcando2CasasDecimais(primeiraAvaliacao.getMassaGorda()));
            dados.setMassaMagraInicial(Uteis.arredondarForcando2CasasDecimais(primeiraAvaliacao.getMassaMagra()));

            //treinos
            Map<String, Object> treinosMap = treinoDao.getTreinos(ctx, primeira, ultima, cliente.getCodigo());
            dados.setTreinosPeriodo((Integer) treinosMap.get("treinos"));
            int mediaSemanal = (int) treinosMap.get("mediaTreinosSemana");
            dados.setMediaSemanal(mediaSemanal == 0 ? "" : String.format(viewUtils.getMensagem("media.treinos.semana"),
                    mediaSemanal));

            List<Map<String, Object>> grupos = treinoDao.getGruposMusculares(ctx, primeira, ultima, cliente.getCodigo());

            ProgramaTreino programaTreino = ps.obterUltimoProgramaVigente(ctx, cliente);
            List<Map<String, Object>> gruposPrograma = new ArrayList<Map<String, Object>>();
            if (programaTreino != null) {
                gruposPrograma = treinoDao.getGruposPrograma(ctx, programaTreino.getCodigo());
            }
            Map<PerimetriaEnum, List<GenericoTO>> atividades = treinoDao.getAtividades(ctx, primeira, ultima, cliente.getCodigo());

            dados.setAtividades(atividades);
            dados.setGrupos(grupos);
            dados.setGruposPrograma(gruposPrograma);

        }
        return dados;
    }

    @Override
    public JSONObject montarGraficoPerimetro(List<AvaliacaoFisica> avaliacoes, PerimetriaEnum perimetro) throws Exception {
        JSONObject dados = new JSONObject();
        JSONArray resultado = new JSONArray();
        List<AvaliacaoFisica> navs = Ordenacao.ordenarLista(new ArrayList<AvaliacaoFisica>(avaliacoes), "dataAvaliacao");
        for (AvaliacaoFisica avaliacaoFisica : navs) {
            JSONObject avaliacao = new JSONObject();
            avaliacao.put("dia", Uteis.getDataAplicandoFormatacao(avaliacaoFisica.getDataAvaliacao(), "dd/MM"));
            avaliacao.put("perimetro", UtilReflection.getValor(avaliacaoFisica, perimetro.getField()));
            resultado.put(avaliacao);
        }
        dados.put("array", resultado);
        return dados;
    }

    @Override
    public EvolucaoFisicaDTO obterEvolucaoFisica(Integer id) throws ServiceException {
        EvolucaoFisicaDTO dto = new EvolucaoFisicaDTO();
        try {
            final String chave = sessaoService.getUsuarioAtual().getChave();
            ClienteSintetico cliente = clienteSinteticoService.consultarPorMatricula(chave, id.toString());
            Collection<AvaliacaoFisica> avaliacoes = avaliacaoFisicaService.obterAvaliacaoCliente(chave, cliente.getCodigo());
            ConfiguracaoSistema config = UtilContext.getBean(ConfiguracaoSistemaService.class).consultarPorTipo(chave, ConfiguracoesEnum.MODELO_EVOLUCAO_FISICA);
            avaliacoes = Ordenacao.ordenarLista(new ArrayList<AvaliacaoFisica>(avaliacoes), "dataAvaliacao");
            if (avaliacoes.isEmpty() || avaliacoes.size() == 1 ){
                throw new ServiceException("Não contém avaliacao evolucão");
            }
            AvaliacaoFisica avaliacaoAtual = ((List<AvaliacaoFisica>) avaliacoes).get(avaliacoes.size() - 1);

            AvaliacaoFisica avaliacaoFisicaAnterior = ((List<AvaliacaoFisica>) avaliacoes).get(avaliacoes.size() - 2);

            AvaliacaoFisica primeiraAvaliacao = ((List<AvaliacaoFisica>) avaliacoes).get(0);

            dto.setNumeroAvaliacoes(avaliacoes.size());
            if (config.getValor().equals("PRIMEIRA_PENULTIMA_ULTIMA")) {
                List<AvaliacaoFisica> avaliacoesOrdenadas = new ArrayList<>();
                avaliacoesOrdenadas.add(avaliacaoAtual);
                avaliacoesOrdenadas.add(primeiraAvaliacao);
                if (avaliacoes.size() > 2) {
                    avaliacoesOrdenadas.add(avaliacaoFisicaAnterior);
                    dto.setMassaMagraPenultima(Uteis.arredondarForcando2CasasDecimais(avaliacaoFisicaAnterior.getMassaMagra()));
                    dto.setMassaGordaPenultima(Uteis.arredondarForcando2CasasDecimais(avaliacaoFisicaAnterior.getMassaGorda()));
                }

                avaliacoes.clear();
                avaliacoes.addAll(Ordenacao.ordenarLista(avaliacoesOrdenadas, "dataAvaliacao"));
            }

            dto.setPercentualGorduraAumentado( Uteis.arredondarForcando2CasasDecimais(avaliacaoAtual.getPercentualGordura()) >
                    Uteis.arredondarForcando2CasasDecimais(primeiraAvaliacao.getPercentualGordura()));

            dto.setPercentualMassaMagraAumentando( porcentualMassaMagra(avaliacaoAtual) > porcentualMassaMagra(primeiraAvaliacao));

            dto.setPercentualGordura(Uteis.arredondarForcando2CasasDecimais(avaliacaoAtual.getPercentualGordura()));
            dto.setPercentualMassaMagra(porcentualMassaMagra(avaliacaoAtual));
            dto.setMassaGordaFinal(Uteis.arredondarForcando2CasasDecimais(avaliacaoAtual.getMassaGorda()));
            dto.setMassaMagraFinal(Uteis.arredondarForcando2CasasDecimais(avaliacaoAtual.getMassaMagra()));


            Object[] objects = motarDobraPesoXgorduraDTO(avaliacoes);
            // get dobas index 0 do array
            dto.setDobras((Collection<EvolucaoDobraItemDTO>) objects[0]);
            // get peso x gordura index 1 do array
            dto.setProporcaoPesoGordura((Collection<EvolucaoProporcaoPesoGorduraPontoDTO>) objects[1]);

            Collection<HistoricoImcDTO> historicoImc = new ArrayList<HistoricoImcDTO>();
            for (AvaliacaoFisica obj : avaliacoes) {
                historicoImc.add(new HistoricoImcDTO(obj.getDataAvaliacao().getTime(), obj.getImc()));
            }
            dto.setHistoricoImc(historicoImc);

            Date primeira = primeiraAvaliacao.getDataAvaliacao();
            Date ultima = avaliacaoAtual.getDataAvaliacao();
            Long l = new Long(Uteis.nrDiasEntreDatas(primeira, ultima));
            dto.setNumeroDiasConsiderados(l);
            dto.setMassaGordaInicial(Uteis.arredondarForcando2CasasDecimais(primeiraAvaliacao.getMassaGorda()));
            dto.setMassaMagraInicial(Uteis.arredondarForcando2CasasDecimais(primeiraAvaliacao.getMassaMagra()));


            Map<PerimetriaEnum, List<GenericoTO>> atividades = treinoDao.getAtividades(chave, primeira, ultima, cliente.getCodigo());
            EvolucaoPerimetriaDTO perimetria = new EvolucaoPerimetriaDTO();
            PerimetriaEnum value = PerimetriaEnum.PESCOCO;
            perimetria.setPescoco(new EvolucaoPerimetriaItemDTO(value.name(), preencherPerimetriaGrafico(avaliacoes, value),
                    preencherAtividadePeriodo(atividades.get(value))));

            value = PerimetriaEnum.ANTEBRACO_DIR;
            perimetria.setAntebraco_direito(new EvolucaoPerimetriaItemDTO(value.name(), preencherPerimetriaGrafico(avaliacoes, value),
                    preencherAtividadePeriodo(atividades.get(value))));

            value = PerimetriaEnum.ANTEBRACO_ESQ;
            perimetria.setAntebraco_esquerdo(new EvolucaoPerimetriaItemDTO(value.name(), preencherPerimetriaGrafico(avaliacoes, value),
                    preencherAtividadePeriodo(atividades.get(value))));

            value = PerimetriaEnum.BRACO_RELAXADO_DIR;
            perimetria.setBraco_direito(new EvolucaoPerimetriaItemDTO(value.name(), preencherPerimetriaGrafico(avaliacoes, value),
                    preencherAtividadePeriodo(atividades.get(value))));

            value = PerimetriaEnum.BRACO_RELAXADO_ESQ;
            perimetria.setBraco_esquerdo(new EvolucaoPerimetriaItemDTO(value.name(), preencherPerimetriaGrafico(avaliacoes, value),
                    preencherAtividadePeriodo(atividades.get(value))));

            value = PerimetriaEnum.CINTURA;
            perimetria.setCintura(new EvolucaoPerimetriaItemDTO(value.name(), preencherPerimetriaGrafico(avaliacoes, value),
                    preencherAtividadePeriodo(atividades.get(value))));

            value = PerimetriaEnum.CIRCUNFERENCIA_ABDOMINAL;
            perimetria.setCircunferenciaAbdominal(new EvolucaoPerimetriaItemDTO(value.name(), preencherPerimetriaGrafico(avaliacoes, value),
                    preencherAtividadePeriodo(atividades.get(value))));

            value = PerimetriaEnum.COXA_DISTAL_DIR;
            perimetria.setCoxa_distal_direita(new EvolucaoPerimetriaItemDTO(value.name(), preencherPerimetriaGrafico(avaliacoes, value),
                    preencherAtividadePeriodo(atividades.get(value))));

            value = PerimetriaEnum.COXA_DISTAL_ESQ;
            perimetria.setCoxa_distal_esquerda(new EvolucaoPerimetriaItemDTO(value.name(), preencherPerimetriaGrafico(avaliacoes, value),
                    preencherAtividadePeriodo(atividades.get(value))));

            value = PerimetriaEnum.COXA_MEDIAL_DIR;
            perimetria.setCoxa_medial_direita(new EvolucaoPerimetriaItemDTO(value.name(), preencherPerimetriaGrafico(avaliacoes, value),
                    preencherAtividadePeriodo(atividades.get(value))));

            value = PerimetriaEnum.COXA_MEDIAL_ESQ;
            perimetria.setCoxa_medial_esquerda(new EvolucaoPerimetriaItemDTO(value.name(), preencherPerimetriaGrafico(avaliacoes, value),
                    preencherAtividadePeriodo(atividades.get(value))));

            value = PerimetriaEnum.COXA_PROXIMAL_DIR;
            perimetria.setCoxa_proximal_direita(new EvolucaoPerimetriaItemDTO(value.name(), preencherPerimetriaGrafico(avaliacoes, value),
                    preencherAtividadePeriodo(atividades.get(value))));

            value = PerimetriaEnum.COXA_PROXIMAL_ESQ;
            perimetria.setCoxa_proximal_esquerda(new EvolucaoPerimetriaItemDTO(value.name(), preencherPerimetriaGrafico(avaliacoes, value),
                    preencherAtividadePeriodo(atividades.get(value))));

            value = PerimetriaEnum.GLUTEO;
            perimetria.setGluteo(new EvolucaoPerimetriaItemDTO(value.name(), preencherPerimetriaGrafico(avaliacoes, value),
                    preencherAtividadePeriodo(atividades.get(value))));

            value = PerimetriaEnum.OMBRO;
            perimetria.setOmbro(new EvolucaoPerimetriaItemDTO(value.name(), preencherPerimetriaGrafico(avaliacoes, value),
                    preencherAtividadePeriodo(atividades.get(value))));

            value = PerimetriaEnum.PANTURRILHA_DIR;
            perimetria.setPanturrilha_direita(new EvolucaoPerimetriaItemDTO(value.name(), preencherPerimetriaGrafico(avaliacoes, value),
                    preencherAtividadePeriodo(atividades.get(value))));

            value = PerimetriaEnum.PANTURRILHA_ESQ;
            perimetria.setPanturrilha_esquerda(new EvolucaoPerimetriaItemDTO(value.name(), preencherPerimetriaGrafico(avaliacoes, value),
                    preencherAtividadePeriodo(atividades.get(value))));

            value = PerimetriaEnum.QUADRIL;
            perimetria.setQuadril(new EvolucaoPerimetriaItemDTO(value.name(), preencherPerimetriaGrafico(avaliacoes, value),
                    preencherAtividadePeriodo(atividades.get(value))));

            value = PerimetriaEnum.TORAX;
            perimetria.setToraxBusto(new EvolucaoPerimetriaItemDTO(value.name(), preencherPerimetriaGrafico(avaliacoes, value),
                    preencherAtividadePeriodo(atividades.get(value))));

            dto.setPerimetria(perimetria);

            Map<String, Object> treinosMap = treinoDao.getTreinos(chave, primeira, ultima, cliente.getCodigo());
            dto.setTreinosPeriodos((Long.parseLong(treinosMap.get("treinos").toString())));
            dto.setMediaTreinoSemana(Double.parseDouble(treinosMap.get("mediaTreinosSemana").toString()));

            EvolucaoGrupoTrabalhadoDTO evolucaoGrupoTrabalhadoDTO = new EvolucaoGrupoTrabalhadoDTO();
            List<Map<String, Object>> grupos = treinoDao.getGruposMusculares(chave, primeira, ultima, cliente.getCodigo());
            evolucaoGrupoTrabalhadoDTO.setDurantePeriodo(preencherGruposMusculares(grupos));

            ProgramaTreino programaTreino = ps.obterUltimoProgramaVigente(chave, cliente);
            if (programaTreino != null) {
                List<Map<String, Object>> gruposPrograma = treinoDao.getGruposPrograma(chave, programaTreino.getCodigo());
                evolucaoGrupoTrabalhadoDTO.setProgramaAtual(preencherGruposMusculares(gruposPrograma));
            } else {
                evolucaoGrupoTrabalhadoDTO.setProgramaAtual(new ArrayList<GrupoTrabalhadoItemDTO>());
            }
            dto.setGruposTrabalhados(evolucaoGrupoTrabalhadoDTO);



        } catch (Exception e) {
            throw new ServiceException(e);
        }

        return dto;
    }


    private Collection<GrupoTrabalhadoItemDTO> preencherGruposMusculares(List<Map<String, Object>> grupos) throws Exception {
        Collection<GrupoTrabalhadoItemDTO> collection = new ArrayList<GrupoTrabalhadoItemDTO>();
        int total = 0;
        for (Map<String, Object> map1 : grupos){
           total = total + Integer.parseInt(map1.get("series").toString());
        }
        for(Map<String, Object> map : grupos) {

            collection.add(new GrupoTrabalhadoItemDTO(map.get("grupo").toString(), Double.parseDouble(map.get("series").toString()), total));
        }
        return collection;
    }

    private List<EvolucaoPerimetriaItemPontoDTO> preencherPerimetriaGrafico(Collection<AvaliacaoFisica> avaliacoes, PerimetriaEnum perimetriaEnum) {
        List<EvolucaoPerimetriaItemPontoDTO> itens = new ArrayList<EvolucaoPerimetriaItemPontoDTO>();
        for(AvaliacaoFisica obj : avaliacoes) {
            itens.add(new EvolucaoPerimetriaItemPontoDTO(obj.getDataAvaliacao().getTime(),
                    Double.parseDouble((String) UtilReflection.getValor(obj, perimetriaEnum.getField()))));

        }
        return itens;
    }

    private Collection<EvolucaoPerimetriaItemAtividade> preencherAtividadePeriodo(List<GenericoTO> genericoTOList) {
        Collection<EvolucaoPerimetriaItemAtividade> atividadesList = new ArrayList<EvolucaoPerimetriaItemAtividade>();
        if(genericoTOList == null) return new ArrayList<EvolucaoPerimetriaItemAtividade>();
        for(GenericoTO obj : genericoTOList) {
            atividadesList.add(new EvolucaoPerimetriaItemAtividade(obj.getLabel(), obj.getOrdem().doubleValue()));
        }
        return atividadesList;
    }

    private Double porcentualMassaMagra(AvaliacaoFisica avaliacaoAtual) {
        return Uteis.arredondarForcando2CasasDecimais(
                UteisValidacao.emptyNumber(avaliacaoAtual.getPercentualMassaMagra()) ?
                        (UteisValidacao.emptyNumber(avaliacaoAtual.getPercentualGordura()) ? 0.0 : (100.0 - avaliacaoAtual.getPercentualGordura())):
                        avaliacaoAtual.getPercentualMassaMagra()
        );
    }

    private Map<String, Object> getDobras(AvaliacaoFisica avaliacaoFisica, String marcaraData) {
        Map<String, Object> dobras = new HashMap<String, Object>();
        dobras.put("dia", Uteis.getDataAplicandoFormatacao(avaliacaoFisica.getDataAvaliacao(), marcaraData));
        dobras.put(viewUtils.getLabel("cadastros.aluno.coxa_medial"), avaliacaoFisica.getCoxaMedial() == null ? 0.0 : avaliacaoFisica.getCoxaMedial().intValue());
        dobras.put(viewUtils.getLabel("cadastros.aluno.biceps"), avaliacaoFisica.getBiceps() == null ? 0.0 :  avaliacaoFisica.getBiceps().intValue());
        dobras.put(viewUtils.getLabel("cadastros.aluno.triceps"), avaliacaoFisica.getTriceps() == null ? 0.0 :  avaliacaoFisica.getTriceps().intValue());
        dobras.put(viewUtils.getLabel("cadastros.aluno.axilarMedia"), avaliacaoFisica.getAxilarMedia() == null ? 0.0 :  avaliacaoFisica.getAxilarMedia().intValue());
        dobras.put(viewUtils.getLabel("cadastros.aluno.peitoral"), avaliacaoFisica.getPeitoral() == null ? 0.0 :  avaliacaoFisica.getPeitoral().intValue());
        dobras.put(viewUtils.getLabel("cadastros.aluno.abdominal"), avaliacaoFisica.getAbdominal() == null ? 0.0 :  avaliacaoFisica.getAbdominal().intValue());
        dobras.put(viewUtils.getLabel("cadastros.aluno.suprailiaca"), avaliacaoFisica.getSupraIliaca() == null ? 0.0 :  avaliacaoFisica.getSupraIliaca().intValue());
        dobras.put(viewUtils.getLabel("cadastros.aluno.subescapular"), avaliacaoFisica.getSubescapular() == null ? 0.0 :  avaliacaoFisica.getSubescapular().intValue());
        return dobras;
    }

    private Object[] motarDobraPesoXgorduraDTO(Collection<AvaliacaoFisica> avaliacoes) throws Exception {
        Collection<EvolucaoDobraItemDTO> dobras = new ArrayList<EvolucaoDobraItemDTO>();
        Collection<EvolucaoProporcaoPesoGorduraPontoDTO> pesoGorduraList = new ArrayList<EvolucaoProporcaoPesoGorduraPontoDTO>();
        Iterator<AvaliacaoFisica> itrAvaliacao = avaliacoes.iterator();
        while(itrAvaliacao.hasNext()) {
            AvaliacaoFisica avaliacaoItr = itrAvaliacao.next();
            Long data = avaliacaoItr.getDataAvaliacao().getTime();
            if (avaliacaoItr.getMassaGorda() == null || UteisValidacao.emptyNumber(avaliacaoItr.getMassaGorda())) {
                if (avaliacaoItr.getProtocolo().ordinal() == 4) {
                    double percentualGordura = avaliacaoItr.getPercentualGordura();
                    double peso = avaliacaoItr.getPeso();
                    double massaGordaCalculada = Math.round((percentualGordura / 100) * peso * 100.0) / 100.0;

                    pesoGorduraList.add(new EvolucaoProporcaoPesoGorduraPontoDTO(data, peso, massaGordaCalculada));
                } else {
                    pesoGorduraList.add(new EvolucaoProporcaoPesoGorduraPontoDTO(data, avaliacaoItr.getPeso(), avaliacaoItr.getMassaGorda()));
                }
            } else {
                pesoGorduraList.add(new EvolucaoProporcaoPesoGorduraPontoDTO(data, avaliacaoItr.getPeso(), avaliacaoItr.getMassaGorda()));
            }
            Iterator<Map.Entry<String, Object>> itr = getDobras(avaliacaoItr, "dd/MM/yyyy").entrySet().iterator();
            while(itr.hasNext()) {
                Map.Entry<String, Object> pair = itr.next();
                if (pair.getKey().equalsIgnoreCase("dia")) {
                    data = Uteis.getDate((String) pair.getValue(), "dd/MM/yyyy").getTime();
                } else {
                    if (UteisValidacao.emptyList(dobras)) {
                        dobras.add(new EvolucaoDobraItemDTO(pair.getKey(), new EvolucaoDobraPontoDTO(data, (Double) pair.getValue())));
                    }else {
                        boolean encontrado = false;
                        for (EvolucaoDobraItemDTO dobra : dobras){
                            if(dobra.getNome().equals(pair.getKey())){
                                dobra.getPontos().add(new EvolucaoDobraPontoDTO(data, (Double) pair.getValue()));
                                encontrado = true;
                            }
                        }
                        if(!encontrado){
                            dobras.add(new EvolucaoDobraItemDTO(pair.getKey(), new EvolucaoDobraPontoDTO(data, (Double) pair.getValue())));
                        }
                    }
                }
            }
        }
        return new Object[]{ dobras, pesoGorduraList };
    }

}
