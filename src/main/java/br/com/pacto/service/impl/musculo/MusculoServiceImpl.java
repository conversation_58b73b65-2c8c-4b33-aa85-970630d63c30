/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.impl.musculo;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.atividade.Atividade;
import br.com.pacto.bean.atividade.AtividadeMusculo;
import br.com.pacto.bean.musculo.GrupoMuscular;
import br.com.pacto.bean.musculo.Musculo;
import br.com.pacto.bean.musculo.MusculoGrupoMuscular;
import br.com.pacto.bean.musculo.MusculoResponseTO;
import br.com.pacto.bean.musculo.MusculoTO;
import br.com.pacto.controller.json.nivel.FiltroNivelJSON;
import br.com.pacto.dao.intf.atividade.AtividadeDao;
import br.com.pacto.dao.intf.atividade.AtividadeMusculoDao;
import br.com.pacto.dao.intf.musculo.GrupoMuscularDao;
import br.com.pacto.dao.intf.musculo.MusculoDao;
import br.com.pacto.dao.intf.musculo.MusculoGrupoMuscularDao;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.impl.notificacao.excecao.GrupoMuscularExcecoes;
import br.com.pacto.service.impl.notificacao.excecao.MusculoExcecoes;
import br.com.pacto.service.intf.Validacao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.exception.ValidacaoException;
import br.com.pacto.service.intf.musculo.MusculoService;
import br.com.pacto.util.ViewUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR>
 */
@Service
public class MusculoServiceImpl implements MusculoService {

    @Autowired
    private ViewUtils viewUtils;
    @Autowired
    private Validacao validacao;
    @Autowired
    private MusculoDao musculoDao;
    @Autowired
    private MusculoGrupoMuscularDao musculoGrupoMuscularDao;
    @Autowired
    private SessaoService sessaoService;
    @Autowired
    private GrupoMuscularDao grupoMuscularDao;
    @Autowired
    private AtividadeMusculoDao atividadeMusculoDao;
    @Autowired
    private AtividadeDao atividadeDao;

    public ViewUtils getViewUtils() {
        return viewUtils;
    }

    public void setViewUtils(ViewUtils viewUtils) {
        this.viewUtils = viewUtils;
    }

    public Validacao getValidacao() {
        return validacao;
    }

    public void setValidacao(Validacao validacao) {
        this.validacao = validacao;
    }

    public MusculoDao getMusculoDao() {
        return this.musculoDao;
    }

    public void setMusculoDao(MusculoDao musculoDao) {
        this.musculoDao = musculoDao;
    }

    public Musculo alterar(final String ctx, Musculo object) throws ServiceException, ValidacaoException {
        try {
            validarDados(ctx, object);
            return getMusculoDao().update(ctx, object);
        } catch (ValidacaoException vex) {
            throw vex;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public void excluir(final String ctx, Musculo object) throws ServiceException {
        try {
            getMusculoDao().delete(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public Musculo inserir(final String ctx, Musculo object) throws ServiceException, ValidacaoException {
        try {
            validarDados(ctx, object);
            return getMusculoDao().insert(ctx, object);
        } catch (ValidacaoException vex) {
            throw vex;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public Musculo obterObjetoPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException {
        try {
            return getMusculoDao().findObjectByParam(ctx, query, params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public Musculo obterPorId(final String ctx, Integer id) throws ServiceException {
        try {
            return getMusculoDao().findById(ctx, id);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<Musculo> obterPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException {
        try {
            return getMusculoDao().findByParam(ctx, query, params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<Musculo> obterPorParam(final String ctx, String query,
            Map<String, Object> params, int max, int index)
            throws ServiceException {
        try {
            return getMusculoDao().findByParam(ctx, query, params, max, index);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<Musculo> obterTodos(final String ctx) throws ServiceException {
        try {
            return getMusculoDao().findListByAttributes(ctx, null, null, "nome", 0);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public void validarDados(final String ctx, Musculo object) throws ValidacaoException {
        if (object.getNome() == null || object.getNome().trim().isEmpty()) {
            throw new ValidacaoException("validacao.nome");
        }
        if (getMusculoDao().exists(ctx, object, "nome")) {
            throw new ValidacaoException("cadastros.existente");
        }
    }

    public List<Musculo> obterPorGrupo(final String ctx, final Integer grupoMuscularCodigo)
            throws ServiceException {
        try {
            Map<String, Object> params = new HashMap<String, Object>();
            params.put("grupo", grupoMuscularCodigo);
            return getMusculoDao().findByParam(ctx, " SELECT obj.musculo FROM MusculoGrupoMuscular obj WHERE obj.grupoMuscular.codigo = :grupo", params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<MusculoResponseTO> consultarTodos()throws ServiceException{
        String ctx = sessaoService.getUsuarioAtual().getChave();
        List<Musculo> lista = obterTodos(ctx);
        List<MusculoResponseTO> ret = new ArrayList<>();
        for (Musculo musculo: lista){
            ret.add(new MusculoResponseTO(musculo));
        }
        return ret;
    }

    @Override
    public List<MusculoResponseTO> consultarMusculos(FiltroNivelJSON filtros, PaginadorDTO paginadorDTO) throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        if (!StringUtils.isBlank(paginadorDTO.getSort())) {
            paginadorDTO.setSort(paginadorDTO.getSort());
        } else {
            paginadorDTO.setSort("nome,ASC");        }

        List<Musculo> lista = getMusculoDao().consultarMusculos(ctx, filtros, paginadorDTO);
        List<MusculoResponseTO> listaRet = new ArrayList<>();
        if (lista != null) {
            for (Musculo musculo : lista) {
                listaRet.add(new MusculoResponseTO(musculo));
            }
        }
        return  listaRet;
    }

    public MusculoResponseTO consultarMusculo(Integer id)throws ServiceException{
        String ctx = sessaoService.getUsuarioAtual().getChave();
        getMusculoDao().getCurrentSession(ctx).clear();
        Musculo musculo = obterPorId(ctx, id);

        if ((musculo == null) || (musculo.getCodigo() == null) || (musculo.getCodigo() <= 0)){
            throw new ServiceException(MusculoExcecoes.MUSCULO_NAO_ENCONTRADO);
        }
        return new MusculoResponseTO(musculo);
    }

    public MusculoResponseTO inserir(MusculoTO musculoTO) throws ServiceException{
        try{
            String ctx = sessaoService.getUsuarioAtual().getChave();
            Musculo musculo = new Musculo();
            povoarItensMusculo(ctx, musculo, musculoTO);
            validarMusculo(ctx, musculo);
            musculo = getMusculoDao().insert(ctx, musculo);

            return new MusculoResponseTO(musculo);
        }catch (Exception e){
            throw new ServiceException(MusculoExcecoes.ERRO_INCLUIR_MUSCULO, e);
        }
    }

    public MusculoResponseTO alterar(Integer id, MusculoTO musculoTO) throws ServiceException{
        try{
            String ctx = sessaoService.getUsuarioAtual().getChave();

            Musculo musculo = getMusculoDao().findById(ctx, id);
            if ((musculo == null) || (musculo.getCodigo() == null) || (musculo.getCodigo() <= 0)){
                throw new ServiceException(MusculoExcecoes.MUSCULO_NAO_ENCONTRADO);
            }

            musculoTO.setId(id);
            povoarItensMusculo(ctx, musculo, musculoTO);
            validarMusculo(ctx, musculo);

            musculo = getMusculoDao().update(ctx, musculo);
            return new MusculoResponseTO(musculo);
        }catch (Exception e){
            throw new ServiceException(MusculoExcecoes.ERRO_ALTERAR_MUSCULO, e);
        }
    }

    public void excluir(Integer id) throws ServiceException{
        String ctx = sessaoService.getUsuarioAtual().getChave();
        Musculo musculo = null;
        try{
            musculo = getMusculoDao().findById(ctx, id);
        }catch (Exception e){
            throw new ServiceException(MusculoExcecoes.ERRO_BUSCAR_MUSCULO, e);
        }
        if ((musculo == null) || (musculo.getCodigo() == null) || (musculo.getCodigo() <= 0)){
            throw new ServiceException(MusculoExcecoes.MUSCULO_NAO_ENCONTRADO);
        }
        try{
            getMusculoDao().delete(ctx, musculo);
        }catch (Exception e){
            throw new ServiceException(MusculoExcecoes.ERRO_EXCLUIR_MUSCULO, e);
        }

    }

    private void povoarItensMusculo(final String ctx, Musculo musculo, MusculoTO musculoTO) throws Exception {
        musculo.setNome(musculoTO.getNome().trim());
        if (musculo.getGrupos().size() > 0) {
            for (MusculoGrupoMuscular musculoGrupo : musculo.getGrupos()) {
                musculoGrupoMuscularDao.delete(ctx, musculoGrupo);
            }
        }
        musculo.getGrupos().clear();
        // adicionar os musculos.
        for (Integer idGrupoMuscular: musculoTO.getGrupoMuscularIds()){
            GrupoMuscular grupoMuscular = null;
            try{
                grupoMuscular = grupoMuscularDao.findById(ctx,idGrupoMuscular);
            }catch (Exception e){
                throw new ServiceException(GrupoMuscularExcecoes.ERRO_BUSCAR_GRUPO_MUSCULAR, e);
            }
            if ((grupoMuscular == null) || (grupoMuscular.getCodigo() == null) || (grupoMuscular.getCodigo() <= 0)) {
                throw new ServiceException(GrupoMuscularExcecoes.GRUPO_MUSCULAR_NAO_ENCONTRADO);
            }
            MusculoGrupoMuscular musculoGrupoMuscular = new MusculoGrupoMuscular();
            musculoGrupoMuscular.setGrupoMuscular(grupoMuscular);
            musculoGrupoMuscular.setMusculo(musculo);
            musculo.getGrupos().add(musculoGrupoMuscular);
        }
        // Adicionar as atividades
        if (musculo.getAtividades().size() > 0) {
            for (AtividadeMusculo atividadeMusculo : musculo.getAtividades()) {
                atividadeMusculoDao.delete(ctx, atividadeMusculo);
            }
        }
        musculo.getAtividades().clear();
        for (Integer idAtividade: musculoTO.getAtividadeIds()){
            Atividade atividade = null;
            try{
                atividade = atividadeDao.findById(ctx,idAtividade);
            }catch (Exception e){
                throw new ServiceException(MusculoExcecoes.ERRO_BUSCAR_MUSCULO_ATIVIDADE, e);
            }
            if ((atividade == null) || (atividade.getCodigo() == null) || (atividade.getCodigo() <= 0)) {
                throw new ServiceException(MusculoExcecoes.MUSCULO_ATIVIDADE_NAO_ENCONTRADO);
            }
            AtividadeMusculo atividadeMusculo = new AtividadeMusculo();
            atividadeMusculo.setMusculo(musculo);
            atividadeMusculo.setAtividade(atividade);
            musculo.getAtividades().add(atividadeMusculo);
        }
    }

    private void validarMusculo(String ctx, Musculo musculo) throws ServiceException {
        if(getMusculoDao().exists(ctx, musculo, "nome")){
            throw new ServiceException(MusculoExcecoes.ERRO_MUSCULO_JA_EXISTE);
        }
    }

}