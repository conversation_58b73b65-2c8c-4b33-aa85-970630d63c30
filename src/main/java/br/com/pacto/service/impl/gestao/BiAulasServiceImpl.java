package br.com.pacto.service.impl.gestao;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ExecuteRequestHttpService;
import br.com.pacto.bean.aula.ProfessorSubstituido;
import br.com.pacto.bean.bi.DashboardBI;
import br.com.pacto.bean.bi.TipoEventoDisponibilidadeBI;
import br.com.pacto.controller.json.agendamento.*;
import br.com.pacto.controller.json.base.SuperControle;
import br.com.pacto.controller.json.gestao.BITreinoAgendaDTO;
import br.com.pacto.dao.intf.dashboardbi.DashboardBIDao;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.security.dto.UsuarioSimplesDTO;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.agendatotal.AgendaTotalService;
import br.com.pacto.service.intf.aula.AulaService;
import br.com.pacto.service.intf.gestao.BIAgendaService;
import br.com.pacto.service.intf.gestao.DashboardBIService;
import br.com.pacto.service.intf.professor.ProfessorSinteticoService;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.impl.Ordenacao;
import org.json.JSONArray;
import org.json.JSONObject;
import org.apache.http.NameValuePair;
import org.apache.http.client.ResponseHandler;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.BasicResponseHandler;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.net.URLEncoder;
import java.sql.ResultSet;
import java.util.*;

@Service
public class BiAulasServiceImpl implements BIAgendaService {

    @Autowired
    private SessaoService sessaoService;
    @Autowired
    private DashboardBIService dashboardBIService;
    @Autowired
    private DashboardBIDao dao;
    @Autowired
    private ProfessorSinteticoService professorService;
    @Autowired
    private AulaService aulaService;
    @Autowired
    private AgendaTotalService agendaService;
    @Override
    public List<ItemListagemAulasDTO> listagemAulas(JSONObject filtros, PaginadorDTO paginadorDTO, Integer empresaId) throws Exception {
        UsuarioSimplesDTO usuarioAtual = sessaoService.getUsuarioAtual();
        String chave = usuarioAtual.getChave();

        if(SuperControle.independente(chave)){
            return new ArrayList<>();
        }

        Map<Integer, String> modalidades = modalidades(chave);
        JSONObject content = chamadaZW(chave,
                "/prest/aulacheia/listagem-aulas",
                paginadorDTO,
                filtros,
                empresaId,
                0);
        List<ItemListagemAulasDTO> listagemAulas = new ArrayList(){{
            JSONArray jsonArray = content.getJSONArray("content");
            for(int i = 0; i < jsonArray.length(); i++){
                add(new ItemListagemAulasDTO(jsonArray.getJSONObject(i), modalidades));
            }
        }};
        if(paginadorDTO.getSort() == null){
            paginadorDTO.setSort("aula,ASC");
        }
        if (paginadorDTO.getSort() != null) {
            String array[] = paginadorDTO.getSort().split(",");
            Comparator<ItemListagemAulasDTO> coluna;
            if (array[1].equals("ASC")) {
                if (array[0].equals("aula")) {
                    coluna = (ItemListagemAulasDTO o1, ItemListagemAulasDTO o2) -> o1.getAula().compareTo( o2.getAula() );
                } else if (array[0].equals("modalidade")) {
                    coluna = (ItemListagemAulasDTO o1, ItemListagemAulasDTO o2) -> o1.getModalidade().compareTo( o2.getModalidade() );
                } else {
                    coluna = (ItemListagemAulasDTO o1, ItemListagemAulasDTO o2) -> o1.getOcupacao().compareTo( o2.getOcupacao() );
                }
                Collections.sort(listagemAulas, coluna);
            } else {
                if (array[0].equals("aula")) {
                    coluna = (ItemListagemAulasDTO o1, ItemListagemAulasDTO o2) -> o1.getAula().compareTo( o2.getAula() );
                } else if (array[0].equals("modalidade")) {
                    coluna = (ItemListagemAulasDTO o1, ItemListagemAulasDTO o2) -> o1.getModalidade().compareTo( o2.getModalidade() );
                } else {
                    coluna = (ItemListagemAulasDTO o1, ItemListagemAulasDTO o2) -> o1.getOcupacao().compareTo( o2.getOcupacao() );
                }
                Collections.sort(listagemAulas, coluna.reversed());
            }
        }
        return paginarFake(listagemAulas, paginadorDTO);
    }

    @Override
    public List<ItemListagemModalidadesDTO> listagemModalidades (JSONObject filtros, PaginadorDTO paginadorDTO, Integer empresaId) throws Exception {
        UsuarioSimplesDTO usuarioAtual = sessaoService.getUsuarioAtual();
        String chave = usuarioAtual.getChave();

        if(SuperControle.independente(chave)){
            return new ArrayList<>();
        }

        JSONObject content = chamadaZW(chave,
                "/prest/aulacheia/listagem-modalidades",
                paginadorDTO,
                filtros,
                empresaId,0);
        List<ItemListagemModalidadesDTO> listagemModalidades = new ArrayList(){{
            JSONArray jsonArray = content.getJSONArray("content");
            for (int i = 0; i < jsonArray.length(); i++) {
                add(new ItemListagemModalidadesDTO(jsonArray.getJSONObject(i)));
            }
        }};
        if(paginadorDTO.getSort() == null){
            paginadorDTO.setSort("modalidade,ASC");
        }
        if (paginadorDTO.getSort() != null) {
            String array[] = paginadorDTO.getSort().split(",");
            Comparator<ItemListagemModalidadesDTO> coluna;
            if (array[1].equals("ASC")) {
                if (array[0].equals("modalidade")) {
                    coluna = (ItemListagemModalidadesDTO o1, ItemListagemModalidadesDTO o2) -> o1.getModalidade().compareTo( o2.getModalidade() );
                } else if (array[0].equals("ocupacao")) {
                    coluna = (ItemListagemModalidadesDTO o1, ItemListagemModalidadesDTO o2) -> o1.getOcupacaoPercentual().compareTo( o2.getOcupacaoPercentual() );
                } else {
                    coluna = (ItemListagemModalidadesDTO o1, ItemListagemModalidadesDTO o2) -> o1.getCapacidade().compareTo( o2.getCapacidade() );
                }
                Collections.sort(listagemModalidades, coluna);
            } else {
                if (array[0].equals("nome")) {
                    coluna = (ItemListagemModalidadesDTO o1, ItemListagemModalidadesDTO o2) -> o1.getModalidade().compareTo( o2.getModalidade() );
                } else if (array[0].equals("ocupacao")) {
                    coluna = (ItemListagemModalidadesDTO o1, ItemListagemModalidadesDTO o2) -> o1.getOcupacao().compareTo( o2.getOcupacao() );
                } else {
                    coluna = (ItemListagemModalidadesDTO o1, ItemListagemModalidadesDTO o2) -> o1.getCapacidade().compareTo( o2.getCapacidade() );
                }
                Collections.sort(listagemModalidades, coluna.reversed());
            }
        }
        paginadorDTO.setQuantidadeTotalElementos(new Long(listagemModalidades.size()));
        if(paginadorDTO.getSize() > paginadorDTO.getQuantidadeTotalElementos()){
            return listagemModalidades;
        }
        Long index = paginadorDTO.getPage() * paginadorDTO.getSize();
        Long last = (index + paginadorDTO.getSize() > (listagemModalidades.size() - 1) ?
                (listagemModalidades.size()) : index + paginadorDTO.getSize());
        return listagemModalidades.subList(index.intValue(), last.intValue());
    }

    @Override
    public List<ItemListagemProfessoresDTO> listagemProfessores(JSONObject filtros, PaginadorDTO paginadorDTO, Integer empresaID) throws Exception {
        UsuarioSimplesDTO usuarioAtual = sessaoService.getUsuarioAtual();
        String chave = usuarioAtual.getChave();
        Long inicio = filtros.get("inicio") == null ? null : filtros.getLong("inicio");
        Date dtInicio = new Date(inicio);

        Long fim = filtros.get("fim") == null ? null : filtros.getLong("fim");
        Date dtFim = new Date(fim);

        Map<Integer, List<Date>> mapaAulasExcluidas = agendaService.obterAulasExcluidas(chave, dtInicio, dtFim);
        Map<Integer, Map<Date, ProfessorSubstituido>> mapaProfessoresSubstituidos = agendaService.obterProfessoresSubstituidos(chave, null, dtInicio, dtFim);
        Map<Integer, List<Date>> mapaProfessorSubstituido = new Hashtable<>();

        for (Map.Entry<Integer, Map<Date, ProfessorSubstituido>> entry : mapaProfessoresSubstituidos.entrySet()) {
            Integer chavePrincipal = entry.getKey();
            Map<Date, ProfessorSubstituido> subMap = entry.getValue();
            List<Date> data = new ArrayList<>();
            for (Map.Entry<Date, ProfessorSubstituido> subEntry : subMap.entrySet()) {
                data.add(subEntry.getKey());
            }
            mapaProfessorSubstituido.put(chavePrincipal, data);
        }

        if(SuperControle.independente(chave)){
            return new ArrayList<>();
        }
        filtros.put("mapaAulasExcluidas",mapaAulasExcluidas);
        filtros.put("mapaProfessoresSubstituidos",mapaProfessoresSubstituidos);

        JSONObject content = chamadaZW(chave,
                "/prest/aulacheia/listagem-professores",
                paginadorDTO,
                filtros,
                empresaID, 0);
        List<ItemListagemProfessoresDTO> listagemProfessores = new ArrayList(){{
            JSONArray jsonArray = content.getJSONArray("content");
            for (int i = 0; i < jsonArray.length(); i++) {
                add(new ItemListagemProfessoresDTO(jsonArray.getJSONObject(i)));
            }
        }};
        if(paginadorDTO.getSort() == null){
            paginadorDTO.setSort("professor,ASC");
        }
        if (paginadorDTO.getSort() != null) {
            String array[] = paginadorDTO.getSort().split(",");
            Comparator<ItemListagemProfessoresDTO> coluna;
            if (array[1].equals("ASC")) {
                if (array[0].equals("professor")) {
                    coluna = (ItemListagemProfessoresDTO o1, ItemListagemProfessoresDTO o2) -> o1.getProfessor().compareTo( o2.getProfessor() );
                } else if (array[0].equals("ocupacao")) {
                    coluna = (ItemListagemProfessoresDTO o1, ItemListagemProfessoresDTO o2) -> o1.getOcupacaoAcumulada().compareTo( o2.getOcupacaoAcumulada() );
                } else {
                    coluna = (ItemListagemProfessoresDTO o1, ItemListagemProfessoresDTO o2) -> o1.getFrequenciaPercentual().compareTo( o2.getFrequenciaPercentual() );
                }
                Collections.sort(listagemProfessores, coluna);
            } else {
                if (array[0].equals("nome")) {
                    coluna = (ItemListagemProfessoresDTO o1, ItemListagemProfessoresDTO o2) -> o1.getProfessor().compareTo( o2.getProfessor() );
                } else if (array[0].equals("ocupacao")) {
                    coluna = (ItemListagemProfessoresDTO o1, ItemListagemProfessoresDTO o2) -> o1.getOcupacaoAcumulada().compareTo( o2.getOcupacaoAcumulada()  );
                } else {
                    coluna = (ItemListagemProfessoresDTO o1, ItemListagemProfessoresDTO o2) -> o1.getFrequenciaPercentual().compareTo( o2.getFrequenciaPercentual()  );
                }
                Collections.sort(listagemProfessores, coluna.reversed());
            }
        }
        paginadorDTO.setQuantidadeTotalElementos(new Long(listagemProfessores.size()));
        if(paginadorDTO.getSize() > paginadorDTO.getQuantidadeTotalElementos()){
            return listagemProfessores;
        }
        Long index = paginadorDTO.getPage() * paginadorDTO.getSize();
        Long last = (index + paginadorDTO.getSize() > (listagemProfessores.size() - 1) ?
                (listagemProfessores.size()) : index + paginadorDTO.getSize());
        return listagemProfessores.subList(index.intValue(), last.intValue());
    }

    @Override
    public List<ItemListagemFrequenciaAlunosDTO> listagemFrequenciaAlunos(JSONObject filtros, PaginadorDTO paginadorDTO, Integer empresaID) throws Exception {
        UsuarioSimplesDTO usuarioAtual = sessaoService.getUsuarioAtual();
        String chave = usuarioAtual.getChave();

        if(SuperControle.independente(chave)){
            return new ArrayList<>();
        }

        JSONObject content = chamadaZW(chave,
                "/prest/aulacheia/listagem-frequencia-alunos",
                paginadorDTO,
                filtros,
                empresaID, 0);
        List<ItemListagemFrequenciaAlunosDTO> listagemAlunosFrequencia = new ArrayList(){{
            JSONArray jsonArray = content.getJSONArray("content");
            for (int i = 0; i < jsonArray.length(); i++) {
                add(new ItemListagemFrequenciaAlunosDTO(jsonArray.getJSONObject(i)));
            }
        }};
        if(paginadorDTO.getSort() == null){
            paginadorDTO.setSort("nome,ASC");
        }
        if (paginadorDTO.getSort() != null) {
            String array[] = paginadorDTO.getSort().split(",");
            Comparator<ItemListagemFrequenciaAlunosDTO> coluna;
            if (array[1].equals("ASC")) {
                if (array[0].equals("nome")) {
                    coluna = (ItemListagemFrequenciaAlunosDTO o1, ItemListagemFrequenciaAlunosDTO o2) -> o1.getNome().compareTo( o2.getNome() );
                } else {
                    coluna = (ItemListagemFrequenciaAlunosDTO o1, ItemListagemFrequenciaAlunosDTO o2) -> o1.getFrequenciaPercentual().compareTo( o2.getFrequenciaPercentual() );
                }
                Collections.sort(listagemAlunosFrequencia, coluna);
            } else {
                if (array[0].equals("nome")) {
                    coluna = (ItemListagemFrequenciaAlunosDTO o1, ItemListagemFrequenciaAlunosDTO o2) -> o1.getNome().compareTo( o2.getNome() );
                } else {
                    coluna = (ItemListagemFrequenciaAlunosDTO o1, ItemListagemFrequenciaAlunosDTO o2) -> o1.getFrequenciaPercentual().compareTo( o2.getFrequenciaPercentual() );
                }
                Collections.sort(listagemAlunosFrequencia, coluna.reversed());
            }
        }
        paginadorDTO.setQuantidadeTotalElementos(new Long(listagemAlunosFrequencia.size()));
        if(paginadorDTO.getSize() > paginadorDTO.getQuantidadeTotalElementos()){
            return listagemAlunosFrequencia;
        }
        Long index = paginadorDTO.getPage() * paginadorDTO.getSize();
        Long last = (index + paginadorDTO.getSize() > (listagemAlunosFrequencia.size() - 1) ?
                (listagemAlunosFrequencia.size()) : index + paginadorDTO.getSize());
        return listagemAlunosFrequencia.subList(index.intValue(), last.intValue());
    }

    private List<ItemListagemAulasDTO> paginarFake(List<ItemListagemAulasDTO> listagemAulas, PaginadorDTO paginadorDTO){
        paginadorDTO.setQuantidadeTotalElementos(new Long(listagemAulas.size()));
        if(paginadorDTO.getSize() > paginadorDTO.getQuantidadeTotalElementos()){
            return listagemAulas;
        }
        Long index = paginadorDTO.getPage() * paginadorDTO.getSize();
        Long last = (index + paginadorDTO.getSize() > (listagemAulas.size() - 1) ?
                (listagemAulas.size()) : index + paginadorDTO.getSize());
        return listagemAulas.subList(index.intValue(), last.intValue());
    }

    @Override
    public BiAgendaDTO obterBI(JSONObject filtros, Integer empresaId) throws Exception {

        int numeroDiasEntreDatas = Uteis.getNumeroDiasEntreDatas(new Date(filtros.getLong("inicio")), new Date(filtros.getLong("fim")));
        if(numeroDiasEntreDatas > 31){
            throw new ServiceException("Intervalo de dias não pode ser maior que um mês!");
        }

        UsuarioSimplesDTO usuarioAtual = sessaoService.getUsuarioAtual();
        String chave = usuarioAtual.getChave();

        if(SuperControle.independente(chave)){
            DashboardBI dashboardBI = new DashboardBI();
            dashboardBI.setEmpresa(empresaId);
            dashboardBIService.processarAgenda(chave, 0, dashboardBI, new Date(filtros.getLong("inicio")), new Date(filtros.getLong("fim")), empresaId, new Date(filtros.getLong("fim")));
            dashboardBI.setBiAgenda(agenda(0,empresaId,dashboardBI));
            return new BiAgendaDTO(new BiAulaDTO(), dashboardBI.getBiAgenda(),
                    Uteis.getDataAplicandoFormatacao(Calendario.hoje(), "dd/MM/yyyy HH:mm"));
        }

        JSONObject ocupacao = chamadaZW(chave,
                "/prest/aulacheia/bi-aulas",
                null,
                filtros,
                empresaId,0);
        BiAulaDTO bi = new BiAulaDTO();
        bi.setAulas(itensOcupacao(ocupacao, "aulas"));
        bi.setModalidades(itensOcupacao(ocupacao, "modalidades"));
        bi.setProfessores(itensOcupacao(ocupacao, "professores"));
        bi.setTotalAulas(ocupacao.getInt("totalAulas"));
        bi.setTotalBonificado(Uteis.arrendondarForcando2CadasDecimaisComVirgula(ocupacao.getDouble("totalBonificacoes")));
        bi.setFrequencia(new ArrayList(){{
            add(new ItemBiAulaDTO(ocupacao.getInt("presencas"), "Presença"));
            add(new ItemBiAulaDTO(ocupacao.getInt("faltas"), "Faltas"));
        }});
        bi.setAlunos(ocupacao.getInt("alunos"));
        DashboardBI dashboardBI = new DashboardBI();
        dashboardBI.setEmpresa(empresaId);
        dashboardBIService.processarAgenda(chave, 0, dashboardBI, new Date(filtros.getLong("inicio")), new Date(filtros.getLong("fim")), empresaId, new Date(filtros.getLong("fim")));
        dashboardBI.setBiAgenda(agenda(0,empresaId,dashboardBI));
        return new BiAgendaDTO(bi, dashboardBI.getBiAgenda(),
                Uteis.getDataAplicandoFormatacao(Calendario.hoje(), "dd/MM/yyyy HH:mm"));
    }

    private List<ItemBiAulaDTO> itensOcupacao(JSONObject ocupacao, String chave) throws Exception{
        return new ArrayList(){{
            JSONObject aulas = new JSONObject(ocupacao.getString(chave));
            add(new ItemBiAulaDTO(aulas.getInt("menor25"), "menor que 25%"));
            add(new ItemBiAulaDTO(aulas.getInt("menor50"), "25% a 50%"));
            add(new ItemBiAulaDTO(aulas.getInt("menor75"), "50% a 75%"));
            add(new ItemBiAulaDTO(aulas.getInt("maior75"), "maior que 75%"));
        }};
    }


    public JSONObject chamadaZW(String ctx,
                               String endpoint,
                               PaginadorDTO paginadorDTO,
                               JSONObject filtros, Integer empresa, Integer professor) throws Exception{
        int numeroDiasEntreDatas = Uteis.getNumeroDiasEntreDatas(new Date(filtros.getLong("inicio")), new Date(filtros.getLong("fim")));
        if(numeroDiasEntreDatas > 31){
            throw new ServiceException("Intervalo de dias não pode ser maior que um mês!");
        }

        final String url = Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebInteg);
        CloseableHttpClient client = ExecuteRequestHttpService.createConnector();
        HttpPost httpPost = new HttpPost(url + endpoint);
        Long inicio = filtros.getLong("inicio");
        Long fim = filtros.getLong("fim");

        List<NameValuePair> params = new ArrayList<>();
        params.add(new BasicNameValuePair("chave", ctx));
        params.add(new BasicNameValuePair("inicio", inicio.toString()));
        params.add(new BasicNameValuePair("fim", fim.toString()));
        params.add(new BasicNameValuePair("empresa", empresa.toString()));
        if (filtros.has("codigoRelatorio")) {
            params.add(new BasicNameValuePair("codigoRelatorio", String.valueOf(filtros.getInt("codigoRelatorio"))));
        }
        if(filtros.has("mapaAulasExcluidas")){
            params.add(new BasicNameValuePair("mapaAulasExcluidas",filtros.get("mapaAulasExcluidas").toString()));
        }
        if(filtros.has("mapaProfessoresSubstituidos")){
            params.add(new BasicNameValuePair("mapaProfessoresSubstituidos",filtros.get("mapaProfessoresSubstituidos").toString()));
        }
        if (filtros.has("quicksearchValue")) {
            params.add(new BasicNameValuePair("quicksearchValue", new String(filtros.getString("quicksearchValue").getBytes("ISO-8859-1"), "UTF-8")));
        }
        if(paginadorDTO != null){
            params.add(new BasicNameValuePair("page", paginadorDTO.getPage().toString()));
            params.add(new BasicNameValuePair("size", paginadorDTO.getSize().toString()));
            params.add(new BasicNameValuePair("sort", paginadorDTO.getSort()));
        }
        if(!UteisValidacao.emptyNumber(professor)){
            params.add(new BasicNameValuePair("professor", professor.toString()));
        }
        httpPost.setEntity(new UrlEncodedFormEntity(params));
        CloseableHttpResponse response = client.execute(httpPost);
        ResponseHandler<String> handler = new BasicResponseHandler();
        String body = handler.handleResponse(response);
        client.close();
        return new JSONObject(body);
    }


    public BITreinoAgendaDTO agenda(Integer idProfessor, Integer empresaId, DashboardBI dash) throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        List<TipoEventoDisponibilidadeBI> listaTiposBi = null;
        try {
            listaTiposBi = dashboardBIService.obterTipos(ctx, Uteis.getAnoData(Calendario.hoje()),
                    Uteis.getMesData(Calendario.hoje()),
                    idProfessor,
                    empresaId);
        } catch (Exception e) {
            throw new ServiceException(e);
        }
        return new BITreinoAgendaDTO(dash, listaTiposBi);
    }

    public Map<Integer, String> professores(String ctx) throws Exception{
        try (ResultSet rs = dao.createStatement(ctx, "select codigocolaborador, nome from professorsintetico")) {
            return new HashMap() {{
                while (rs.next()) {
                    put(rs.getInt("codigocolaborador"), rs.getString("nome"));
                }
            }};
        }
    }

    public Map<Integer, String> modalidades(String ctx) throws Exception{
        try (ResultSet rs = dao.createStatement(ctx, "select codigoZW, nome from modalidade")) {
            return new HashMap() {{
                while (rs.next()) {
                    put(rs.getInt("codigoZW"), rs.getString("nome"));
                }
            }};
        }
    }


    @Override
    public List<ItemListagemPresencasAlunosDTO> listagemPresenca(JSONObject filtros, PaginadorDTO paginadorDTO, Integer empresaID) throws Exception {
        UsuarioSimplesDTO usuarioAtual = sessaoService.getUsuarioAtual();
        String chave = usuarioAtual.getChave();


        if(SuperControle.independente(chave)){
            return new ArrayList<>();
        }

        JSONObject content = chamadaZW(chave,
                "/prest/aulacheia/listagem-presenca",
                paginadorDTO,
                filtros,
                empresaID, 0);
        List<ItemListagemPresencasAlunosDTO> listagemAlunosFrequencia = new ArrayList(){{
            JSONArray jsonArray = content.getJSONArray("content");
            for (int i = 0; i < jsonArray.length(); i++) {
                add(new ItemListagemPresencasAlunosDTO(jsonArray.getJSONObject(i)));
            }
        }};
        if(paginadorDTO.getSort() == null){
            paginadorDTO.setSort("nomeAluno,ASC");
        }
        paginadorDTO.setQuantidadeTotalElementos(new Long(listagemAlunosFrequencia.size()));

        if (paginadorDTO.getSort() != null) {
            String array[] = paginadorDTO.getSort().split(",");
            Comparator<ItemListagemPresencasAlunosDTO> coluna;
            if (array[1].equals("ASC")) {
                coluna = (ItemListagemPresencasAlunosDTO o1, ItemListagemPresencasAlunosDTO o2) -> o1.getNomeAluno().compareTo( o2.getNomeAluno() );
                Collections.sort(listagemAlunosFrequencia, coluna);
            } else {
                coluna = (ItemListagemPresencasAlunosDTO o1, ItemListagemPresencasAlunosDTO o2) -> o1.getNomeAluno().compareTo( o2.getNomeAluno() );
                Collections.sort(listagemAlunosFrequencia, coluna.reversed());
            }
        }

        if(paginadorDTO.getSize() > paginadorDTO.getQuantidadeTotalElementos()){
            return listagemAlunosFrequencia;
        }
        Long index = paginadorDTO.getPage() * paginadorDTO.getSize();
        Long last = (index + paginadorDTO.getSize() > (listagemAlunosFrequencia.size() - 1) ?
                (listagemAlunosFrequencia.size()) : index + paginadorDTO.getSize());
        return listagemAlunosFrequencia.subList(index.intValue(), last.intValue());
    }

    @Override
    public List<ItemListagemBonusDTO> listagemBonus(JSONObject filtros, PaginadorDTO paginadorDTO, Integer empresaId) throws Exception {
        UsuarioSimplesDTO usuarioAtual = sessaoService.getUsuarioAtual();
        String chave = usuarioAtual.getChave();

        if(SuperControle.independente(chave)){
            return new ArrayList<>();
        }

        JSONObject content = chamadaZW(chave,
                "/prest/aulacheia/listagem-aulas-bonus",
                paginadorDTO,
                filtros,
                empresaId,
                0);
        List<ItemListagemBonusDTO> listagemAulas = new ArrayList(){{
            JSONArray jsonArray = content.getJSONArray("content");
            for(int i = 0; i < jsonArray.length(); i++){
                add(new ItemListagemBonusDTO(jsonArray.getJSONObject(i)));
            }
        }};
        if(paginadorDTO.getSort() == null){
            paginadorDTO.setSort("aula,ASC");
        }
        if (paginadorDTO.getSort() != null) {
            String array[] = paginadorDTO.getSort().split(",");
            Comparator<ItemListagemBonusDTO> coluna;
            if (array[1].equals("ASC")) {
                if (array[0].equals("aula")) {
                    coluna = (ItemListagemBonusDTO o1, ItemListagemBonusDTO o2) -> o1.getAula().compareTo( o2.getAula() );
                } else if (array[0].equals("professor")) {
                    coluna = (ItemListagemBonusDTO o1, ItemListagemBonusDTO o2) -> o1.getProfessor().compareTo( o2.getProfessor() );
                } else {
                    coluna = (ItemListagemBonusDTO o1, ItemListagemBonusDTO o2) -> o1.getOcupacao().compareTo( o2.getOcupacao() );
                }
                Collections.sort(listagemAulas, coluna);
            } else {
                if (array[0].equals("aula")) {
                    coluna = (ItemListagemBonusDTO o1, ItemListagemBonusDTO o2) -> o1.getAula().compareTo( o2.getAula() );
                } else if (array[0].equals("professor")) {
                    coluna = (ItemListagemBonusDTO o1, ItemListagemBonusDTO o2) -> o1.getProfessor().compareTo( o2.getProfessor() );
                } else {
                    coluna = (ItemListagemBonusDTO o1, ItemListagemBonusDTO o2) -> o1.getOcupacao().compareTo( o2.getOcupacao() );
                }
                Collections.sort(listagemAulas, coluna.reversed());
            }
        }
        paginadorDTO.setQuantidadeTotalElementos(new Long(listagemAulas.size()));
        if(paginadorDTO.getSize() > paginadorDTO.getQuantidadeTotalElementos()){
            return listagemAulas;
        }
        Long index = paginadorDTO.getPage() * paginadorDTO.getSize();
        Long last = (index + paginadorDTO.getSize() > (listagemAulas.size() - 1) ?
                (listagemAulas.size()) : index + paginadorDTO.getSize());
        return listagemAulas.subList(index.intValue(), last.intValue());
    }

    @Override
    public List<ItemListagemBonusDTO> listagemBonusProfessor(JSONObject filtros, PaginadorDTO paginadorDTO, Integer empresaId) throws Exception {
        UsuarioSimplesDTO usuarioAtual = sessaoService.getUsuarioAtual();
        String chave = usuarioAtual.getChave();

        if(SuperControle.independente(chave)){
            return new ArrayList<>();
        }

        JSONObject content = chamadaZW(chave,
                "/prest/aulacheia/listagem-aulas-bonus",
                paginadorDTO,
                filtros,
                empresaId,
                0);
        List<ItemListagemBonusDTO> lista = new ArrayList(){{
            JSONArray jsonArray = content.getJSONArray("content");
            for(int i = 0; i < jsonArray.length(); i++){
                add(new ItemListagemBonusDTO(jsonArray.getJSONObject(i)));
            }
        }};
        Map<Integer, ItemListagemBonusDTO> mapa = new HashMap();
        for(ItemListagemBonusDTO i : lista){
            if(UteisValidacao.emptyNumber(i.getBonusDb())){
                continue;
            }
            ItemListagemBonusDTO o = mapa.get(i.getCodProf());
            if(o == null){
                mapa.put(i.getCodProf(), i);
            }else {
                o.setBonusDb(o.getBonusDb() + i.getBonusDb());
                o.setBonus("R$ " + Uteis.arrendondarForcando2CadasDecimaisComVirgula(o.getBonusDb()));
            }
        }
        List<ItemListagemBonusDTO> listagemAulas = new ArrayList(mapa.values());
        listagemAulas = Ordenacao.ordenarLista(listagemAulas, "professor");
        if(paginadorDTO.getSort() == null){
            paginadorDTO.setSort("aula,ASC");
        }
        if (paginadorDTO.getSort() != null) {
            String array[] = paginadorDTO.getSort().split(",");
            Comparator<ItemListagemBonusDTO> coluna;
            if (array[1].equals("ASC")) {
                if (array[0].equals("aula")) {
                    coluna = (ItemListagemBonusDTO o1, ItemListagemBonusDTO o2) -> o1.getAula().compareTo( o2.getAula() );
                } else if (array[0].equals("professor")) {
                    coluna = (ItemListagemBonusDTO o1, ItemListagemBonusDTO o2) -> o1.getProfessor().compareTo( o2.getProfessor() );
                } else {
                    coluna = (ItemListagemBonusDTO o1, ItemListagemBonusDTO o2) -> o1.getOcupacao().compareTo( o2.getOcupacao() );
                }
                Collections.sort(listagemAulas, coluna);
            } else {
                if (array[0].equals("aula")) {
                    coluna = (ItemListagemBonusDTO o1, ItemListagemBonusDTO o2) -> o1.getAula().compareTo( o2.getAula() );
                } else if (array[0].equals("professor")) {
                    coluna = (ItemListagemBonusDTO o1, ItemListagemBonusDTO o2) -> o1.getProfessor().compareTo( o2.getProfessor() );
                } else {
                    coluna = (ItemListagemBonusDTO o1, ItemListagemBonusDTO o2) -> o1.getOcupacao().compareTo( o2.getOcupacao() );
                }
                Collections.sort(listagemAulas, coluna.reversed());
            }
        }
        paginadorDTO.setQuantidadeTotalElementos(new Long(listagemAulas.size()));
        if(paginadorDTO.getSize() > paginadorDTO.getQuantidadeTotalElementos()){
            return listagemAulas;
        }
        Long index = paginadorDTO.getPage() * paginadorDTO.getSize();
        Long last = (index + paginadorDTO.getSize() > (listagemAulas.size() - 1) ?
                (listagemAulas.size()) : index + paginadorDTO.getSize());
        return listagemAulas.subList(index.intValue(), last.intValue());
    }
}
