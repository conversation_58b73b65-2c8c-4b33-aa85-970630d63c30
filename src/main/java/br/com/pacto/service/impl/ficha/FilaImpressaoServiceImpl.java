/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.impl.ficha;

import br.com.pacto.bean.ficha.FilaImpressao;
import br.com.pacto.bean.programa.ProgramaTreino;
import br.com.pacto.bean.sincronizacao.ObjetoSincronizarTO;
import br.com.pacto.bean.sincronizacao.TipoObjetoSincronizarEnum;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.dao.intf.cliente.ClienteSinteticoDao;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.ColecaoUtils;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.impl.memcached.MemCachedManager;
import br.com.pacto.service.intf.Validacao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.ficha.FilaImpressaoService;
import br.com.pacto.service.intf.memcached.CachedManagerInterfaceFacade;
import br.com.pacto.service.intf.usuario.UsuarioService;
import br.com.pacto.util.ViewUtils;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import org.apache.commons.collections.Predicate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR>
 */
@Service
public class FilaImpressaoServiceImpl implements FilaImpressaoService {

    @Autowired
    private UsuarioService usuarioService;
    @Autowired
    private CachedManagerInterfaceFacade memcached;
    @Autowired
    private ClienteSinteticoDao clienteDao;
    @Autowired
    private ViewUtils viewUtils;
    @Autowired
    private Validacao validacao;
    private Map<String, Map<Integer, FilaImpressao>> spoolerImpressao = new HashMap<String, Map<Integer, FilaImpressao>>();
    private ConcurrentHashMap<String, List<ObjetoSincronizarTO>> spoolerObjetosSincronizar = new ConcurrentHashMap<String, List<ObjetoSincronizarTO>>();

    public ViewUtils getViewUtils() {
        return viewUtils;
    }

    public void setViewUtils(ViewUtils viewUtils) {
        this.viewUtils = viewUtils;
    }

    public Validacao getValidacao() {
        return validacao;
    }

    public void setValidacao(Validacao validacao) {
        this.validacao = validacao;
    }

    @Override
    public void excluirTodosFilaImpressao(final String ctx) throws ServiceException {
        try {
            if (spoolerImpressao.get(ctx) != null) {
                spoolerImpressao.get(ctx).clear();
            }
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    @Override
    public void excluirTodosSincronizar(final String ctx) throws ServiceException {
        try {
            if (spoolerObjetosSincronizar.get(ctx) != null) {
                List<ObjetoSincronizarTO> tmp = new ArrayList(spoolerObjetosSincronizar.get(ctx));                
                for (ObjetoSincronizarTO to : tmp) {
                    long inicio = to.getData().getTime();
                    long fim = Calendario.hoje().getTime();
                    long segundos = (fim - inicio) / 1000;
                    if (segundos > 30) {
                        spoolerObjetosSincronizar.get(ctx).remove(to);
                    }
                }
            }
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    @Override
    public void excluirTodosSincronizar(final String ctx, final Integer empresa) throws ServiceException {
        try {
            if (spoolerObjetosSincronizar.get(ctx) != null) {
                List<ObjetoSincronizarTO> tmp = new ArrayList(spoolerObjetosSincronizar.get(ctx));
                for (ObjetoSincronizarTO to : tmp) {
                    if(empresa != null && (to.getEmpresa() != null && !to.getEmpresa().equals(empresa))){
                        continue;
                    }
                    long inicio = to.getData().getTime();
                    long fim = Calendario.hoje().getTime();
                    long segundos = (fim - inicio) / 1000;
                    if (segundos > 30) {
                        spoolerObjetosSincronizar.get(ctx).remove(to);
                    }
                }
            }
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    @Override
    public FilaImpressao enfileirar(final String ctx, FilaImpressao object) throws ServiceException {
        if (!Aplicacao.isInstanciaAgendamento()) {
            try {
                if (object != null && object.getFicha() != null && !object.getFicha().isUsarComoPredefinida()) {
                    try {
                        if (spoolerImpressao.get(ctx) == null) {
                            Map<Integer, FilaImpressao> map = new HashMap<Integer, FilaImpressao>();
                            map.put(object.getFicha().getCodigo(), object);
                            spoolerImpressao.put(ctx, map);
                        } else {
                            spoolerImpressao.get(ctx).put(object.getFicha().getCodigo(), object);
                        }
                        enfileirar(ctx, TipoObjetoSincronizarEnum.IMPRESSAO_FICHA,
                                object.getFicha().getCodigo().toString());
                        return object;
                    } catch (Exception e) {
                        Uteis.logar(e, FilaImpressaoServiceImpl.class);
                    }
                }
            } catch (Exception ex) {
                throw new ServiceException(ex.getMessage());
            }
        }
        return null;
    }

    private Integer empresa(String ctx, String username){
        try {
            String identificador = "usuario-username-".concat(username.replaceAll(" ", "0_"));
            Usuario usuario = memcached.ler(ctx, identificador);
            if(usuario == null){
                usuario = usuarioService.consultarPorUserName(ctx, username);
                if(usuario == null){
                    return null;
                }
                if(usuario.getCliente() != null){
                    usuario.setCliente(clienteDao.findById(ctx, usuario.getCliente().getCodigo()));
                }
                memcached.gravar(ctx, identificador, usuario);
            }
            return usuario.getEmpresaZW();
        }catch (Exception e){
            e.printStackTrace();
            return null;
        }
    }

    @Override
    public ObjetoSincronizarTO enfileirar(final String ctx, final TipoObjetoSincronizarEnum tipo,
            final String chaveBusca) throws ServiceException {
        if (!Aplicacao.isInstanciaAgendamento()) {
            try {
                if (chaveBusca != null && ctx != null && tipo != null) {
                    try {
                        Integer empresaZW = null;
                        if(TipoObjetoSincronizarEnum.PROGRAMA.equals(tipo) || TipoObjetoSincronizarEnum.CLIENTE.equals(tipo)){
                            empresaZW = empresa(ctx, chaveBusca);
                        }
                        ObjetoSincronizarTO objeto = new ObjetoSincronizarTO(tipo, chaveBusca, empresaZW);
                        if (spoolerObjetosSincronizar.get(ctx) == null) {
                            List<ObjetoSincronizarTO> lista = new ArrayList<ObjetoSincronizarTO>();
                            lista.add(objeto);
                            spoolerObjetosSincronizar.put(ctx, lista);
                        } else {
                            List<ObjetoSincronizarTO> lista = spoolerObjetosSincronizar.get(ctx);
                            if (!ColecaoUtils.exists(lista, new Predicate() {
                                @Override
                                public boolean evaluate(Object o) {
                                    return ((ObjetoSincronizarTO) o).getChave().equals(chaveBusca)
                                            && ((ObjetoSincronizarTO) o).getTipo().equals(tipo);
                                }
                            })) {
                                spoolerObjetosSincronizar.get(ctx).add(objeto);
                            }
                        }
                        return objeto;
                    } catch (Exception e) {
                        Uteis.logar(e, FilaImpressaoServiceImpl.class);
                    }
                }
            } catch (Exception ex) {
                throw new ServiceException(ex.getMessage());
            }
        }
        return null;
    }

    @Override
    public List<FilaImpressao> obterTodosFilaImpressao(final String ctx) throws ServiceException {
        try {
            if (spoolerImpressao.get(ctx) != null && spoolerImpressao.get(ctx).values() != null) {
                return new ArrayList(spoolerImpressao.get(ctx).values());
            }
        } catch (Exception ex) {
            Uteis.logar(ex, FilaImpressaoServiceImpl.class);
            throw new ServiceException(ex.getMessage());
        }
        return null;
    }

    @Override
    public List<ObjetoSincronizarTO> obterTodosSincronizar(final String ctx, final Integer empresaZW) throws ServiceException {
        try {
            if (spoolerObjetosSincronizar != null && spoolerObjetosSincronizar.get(ctx) != null && !spoolerObjetosSincronizar.get(ctx).isEmpty()) {
                return new ArrayList(spoolerObjetosSincronizar.get(ctx));
            }
        } catch (Exception ex) {
            System.out.println("erro em obterTodosSincronizar - " + ctx + " - " + empresaZW);
            ex.printStackTrace();
            throw new ServiceException(ex.getMessage());
        }
        return null;
    }

    @Override
    public Number count(final String ctx) throws ServiceException {
        try {
            return spoolerImpressao.get(ctx) != null ? spoolerImpressao.get(ctx).size() : 0;
        } catch (Exception ex) {
            Uteis.logar(ex, FilaImpressaoServiceImpl.class);
            throw new ServiceException(ex.getMessage());
        }
    }

    @Override
    public Number countSync(final String ctx) throws ServiceException {
        try {
            return spoolerObjetosSincronizar.get(ctx) != null ? spoolerObjetosSincronizar.get(ctx).size() : 0;
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new ServiceException(ex.getMessage());
        }
    }
}
