package br.com.pacto.service.impl.locacao;

import br.com.pacto.dao.intf.locacao.AlunoLocacaoHorarioPlayCheckinCheckoutDao;
import br.com.pacto.service.intf.locacao.AlunoLocacaoHorarioPlayCheckinCheckoutService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.test.context.ContextConfiguration;

@Service
@ContextConfiguration(locations = {"/applicationContext.xml"})
public class AlunoLocacaoHorarioPlayCheckinCheckoutServiceImpl implements AlunoLocacaoHorarioPlayCheckinCheckoutService {

    private final AlunoLocacaoHorarioPlayCheckinCheckoutDao alunoLocacaoHorarioPlayCheckinCheckoutDao;

    @Autowired
    public AlunoLocacaoHorarioPlayCheckinCheckoutServiceImpl(AlunoLocacaoHorarioPlayCheckinCheckoutDao alunoLocacaoHorarioPlayCheckinCheckoutDao) {
        this.alunoLocacaoHorarioPlayCheckinCheckoutDao = alunoLocacaoHorarioPlayCheckinCheckoutDao;
    }

}
