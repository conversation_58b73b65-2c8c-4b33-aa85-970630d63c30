/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.impl.pessoa;

import br.com.pacto.bean.pessoa.Email;
import br.com.pacto.bean.pessoa.Pessoa;
import br.com.pacto.bean.pessoa.Telefone;
import br.com.pacto.dao.intf.pessoa.EmailDao;
import br.com.pacto.dao.intf.pessoa.PessoaDao;
import br.com.pacto.dao.intf.pessoa.TelefoneDao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.pessoa.PessoaService;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR>
 */
@Service
public class PessoaServiceImpl implements PessoaService{

    @Autowired
    private PessoaDao pessoaDao;
    @Autowired
    private TelefoneDao telefoneDao;
    @Autowired
    private EmailDao emailDao;

    @Override
    public Pessoa inserirPessoa(final String ctx, Pessoa pessoa) throws ServiceException {
        try {
            return pessoaDao.insert(ctx, pessoa);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }
    
    @Override
    public Pessoa alterarPessoa(final String ctx, Pessoa pessoa) throws ServiceException {
        try {
            return pessoaDao.update(ctx, pessoa);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public Telefone inserirTelefone(String ctx, Telefone telefone) throws ServiceException {
        try {
            return telefoneDao.insert(ctx, telefone);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public Email inserirEmail(String ctx, Email email) throws ServiceException {
        try {
            return emailDao.insert(ctx, email);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }
    
    @Override
    public List<Email> obterEmailsPorPessoa(String ctx,Integer pessoa) throws Exception {
        String s = "SELECT obj FROM Email obj where obj.pessoa.codigo = :pessoa";
        Map<String, Object> params = new HashMap();
        params.put("pessoa", pessoa);
        return emailDao.findByParam(ctx, s, params);
    }

    @Override
    public List<Telefone> obterTelefonesPorPessoa(String ctx,Integer pessoa) throws Exception {
        String s = "SELECT obj FROM Telefone obj where obj.pessoa.codigo = :pessoa";
        Map<String, Object> params = new HashMap();
        params.put("pessoa", pessoa);
        return telefoneDao.findByParam(ctx, s, params);
    }
    
    @Override
    public void deletarEmailTelefonePessoa(String ctx, Integer pessoa) throws Exception {
        emailDao.deleteComParam(ctx,new String[]{"pessoa.codigo"}, new Object[]{pessoa});
        telefoneDao.deleteComParam(ctx,new String[]{"pessoa.codigo"}, new Object[]{pessoa});
        
    }

    public Pessoa obterPorId(final String ctx, Integer id) throws ServiceException {
        try {
            return pessoaDao.findById(ctx, id);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public List<Pessoa> obterTodas(String ctx) throws ServiceException {
        try {
            return pessoaDao.findAll(ctx);
        }catch (Exception e){
            throw new ServiceException(e);
        }
    }

    @Override
    public Pessoa obterPessoaPorNome(final String ctx, String nome) throws ServiceException {
        try {
            String query = "SELECT obj FROM Pessoa obj WHERE obj.nome = :nome";
            Map<String, Object> params = new HashMap<>();
            params.put("nome", nome);

            List<Pessoa> pessoas = pessoaDao.findByParam(ctx, query, params);
            if (!pessoas.isEmpty()) {
                return pessoas.get(0); // Retorna a primeira pessoa encontrada
            }
            return null; // Retorna null se não houver correspondência
        } catch (Exception ex) {
            throw new ServiceException("Erro ao buscar pessoa por nome: " + nome, ex);
        }
    }
}
