package br.com.pacto.service.impl.cliente.perfil;

public class DiasQueTreinouProgramaAtualDTO {

    private Double domingo = 0.0;
    private Double segunda = 0.0;
    private Double terca = 0.0;
    private Double quarta = 0.0;
    private Double quinta = 0.0;
    private Double sexta = 0.0;
    private Double sabado = 0.0;
    private Integer treinosExecutadosPeriodo;

    public DiasQueTreinouProgramaAtualDTO() { }

    public DiasQueTreinouProgramaAtualDTO(Double domingo, Double segunda, Double terca, Double quarta, Double quinta, Double sexta, Double sabado, Integer treinosExecutadosPeriodo) {
        this.domingo = domingo;
        this.segunda = segunda;
        this.terca = terca;
        this.quarta = quarta;
        this.quinta = quinta;
        this.sexta = sexta;
        this.sabado = sabado;
        this.treinosExecutadosPeriodo = treinosExecutadosPeriodo;
    }

    public Double getDomingo() {
        return domingo;
    }

    public void setDomingo(Double domingo) {
        this.domingo = domingo;
    }

    public Double getSegunda() {
        return segunda;
    }

    public void setSegunda(Double segunda) {
        this.segunda = segunda;
    }

    public Double getTerca() {
        return terca;
    }

    public void setTerca(Double terca) {
        this.terca = terca;
    }

    public Double getQuarta() {
        return quarta;
    }

    public void setQuarta(Double quarta) {
        this.quarta = quarta;
    }

    public Double getQuinta() {
        return quinta;
    }

    public void setQuinta(Double quinta) {
        this.quinta = quinta;
    }

    public Double getSexta() {
        return sexta;
    }

    public void setSexta(Double sexta) {
        this.sexta = sexta;
    }

    public Double getSabado() {
        return sabado;
    }

    public void setSabado(Double sabado) {
        this.sabado = sabado;
    }

    public Integer getTreinosExecutadosPeriodo() {
        return treinosExecutadosPeriodo;
    }

    public void setTreinosExecutadosPeriodo(Integer treinosExecutadosPeriodo) {
        this.treinosExecutadosPeriodo = treinosExecutadosPeriodo;
    }

}
