package br.com.pacto.service.impl.agenda;

import br.com.pacto.objeto.Calendario;
import org.json.JSONObject;

import java.text.ParseException;
import java.util.Date;

public class AmbienteAgendadoTO {
    private Integer ambiente;
    private Integer horarioTurma;
    private Date inicio;
    private Date fim;
    private Integer codigoLocacao;

    public AmbienteAgendadoTO(JSONObject json) {
        this.ambiente = json.getInt("ambiente");
        this.horarioTurma = json.getInt("horarioturma");
        Date dia = new Date(json.getLong("dia"));
        this.inicio = Calendario.getDataComHora(dia, json.getString("horainicial"));
        this.fim = Calendario.getDataComHora(dia, json.getString("horafinal"));
    }

    public AmbienteAgendadoTO(JSONObject json, boolean locacao) throws ParseException {
        this.horarioTurma = 0;
        this.codigoLocacao = json.has("codigoLocacao") ? json.getInt("codigoLocacao") : 0;
        this.ambiente = json.getInt("ambiente");
        this.inicio = Calendario.getDate("yyyy-MM-dd hh:mm:ss", json.getString("inicio"));
        this.fim = Calendario.getDate("yyyy-MM-dd hh:mm:ss", json.getString("fim"));
    }

    public Integer getAmbiente() {
        return ambiente;
    }

    public void setAmbiente(Integer ambiente) {
        this.ambiente = ambiente;
    }

    public Integer getHorarioTurma() {
        return horarioTurma;
    }

    public void setHorarioTurma(Integer horarioTurma) {
        this.horarioTurma = horarioTurma;
    }

    public Date getInicio() {
        return inicio;
    }

    public void setInicio(Date inicio) {
        this.inicio = inicio;
    }

    public Date getFim() {
        return fim;
    }

    public void setFim(Date fim) {
        this.fim = fim;
    }

    public Integer getCodigoLocacao() {
        return codigoLocacao;
    }

    public void setCodigoLocacao(Integer codigoLocacao) {
        this.codigoLocacao = codigoLocacao;
    }

}
