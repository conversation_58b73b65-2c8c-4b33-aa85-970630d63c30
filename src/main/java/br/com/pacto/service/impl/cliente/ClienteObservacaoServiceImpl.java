package br.com.pacto.service.impl.cliente;

import br.com.pacto.bean.cliente.ClienteObservacao;
import br.com.pacto.controller.json.aluno.HistoricoContatoAlunoVO;
import br.com.pacto.dao.intf.cliente.ClienteObservacaoDao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.conexao.ConexaoZWServiceImpl;
import br.com.pacto.service.intf.cliente.ClienteObservacaoService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @since 12/09/2018
 */
@Service
public class ClienteObservacaoServiceImpl implements ClienteObservacaoService {

    private final ClienteObservacaoDao clienteObservacaoDao;
    @Autowired
    private ConexaoZWServiceImpl conexaoZWService;

    @Autowired
    public ClienteObservacaoServiceImpl(ClienteObservacaoDao clienteObservacaoDao) {
        this.clienteObservacaoDao = clienteObservacaoDao;
    }

    @Override
    public List<ClienteObservacao> consultarObservacoesPorMatriculaCliente(String contexto, String matriculaCliente) throws Exception {
        if (StringUtils.isEmpty(contexto) || StringUtils.isEmpty(matriculaCliente)) {
            throw new ServiceException("O contexto e a matrícula do cliente devem ser informados.");
        }

        return clienteObservacaoDao.consultarPorMatriculaCliente(contexto, matriculaCliente);
    }

    @Override
    public void excluir(String contexto, Integer observacaoId) throws Exception {
        if (observacaoId == null || observacaoId < 1) {
            throw new ServiceException("O ID da observação deve ser um número válido maior que 0");
        }

        clienteObservacaoDao.delete(contexto, observacaoId);
    }

    @Override
    public List<ClienteObservacao> consultarObservacoesPorMatriculaClienteApp(String contexto, String matriculaCliente, int maxResult, int index) throws Exception {
        if (StringUtils.isEmpty(contexto) || StringUtils.isEmpty(matriculaCliente)) {
            throw new ServiceException("O contexto e a matrícula do cliente devem ser informados.");
        }
        return clienteObservacaoDao.consultarPorMatriculaClienteApp(contexto, matriculaCliente, maxResult, index);
    }

    //
    //

    @Override
    public List<HistoricoContatoAlunoVO> consultarHistoricoDeContatos(String contexto, String matriculaCliente, Boolean contatoCRM, Boolean observacoes) throws Exception {
        List<HistoricoContatoAlunoVO> historicoContatoAlunoVOList = new ArrayList<>();

        if(contatoCRM) {
            trazHistoricoContato(contexto, matriculaCliente, historicoContatoAlunoVOList);
        }

        if(observacoes) {
            trazObservacoesTreino(contexto, matriculaCliente, historicoContatoAlunoVOList);


            trazObservacoesADM(contexto, matriculaCliente, historicoContatoAlunoVOList);
        }

        return historicoContatoAlunoVOList;
    }

    private void trazHistoricoContato(String contexto, String matriculaCliente, List<HistoricoContatoAlunoVO> historicoContatoAlunoVOList) throws Exception {
        String tituloPattern = "Título: (.*?) </p>";
        String mensagemPattern = "Mensagem:(.*?)</p>";

        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * ");
        sql.append("FROM historicoContato as historico ");
        sql.append("LEFT JOIN cliente ON cliente.codigo = historico.cliente ");
        sql.append("WHERE cliente.codigomatricula = ").append(matriculaCliente);
        sql.append("ORDER BY historico.dia desc;");
        try (Connection conZW = conexaoZWService.conexaoZw(contexto)) {
            try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sql.toString(), conZW)) {
                while (rs.next()) {
                    HistoricoContatoAlunoVO historicoContatoAlunoVO = new HistoricoContatoAlunoVO();
                    historicoContatoAlunoVO.setDataContrato(rs.getDate("dia"));
                    historicoContatoAlunoVO.setOrigem("Contato CRM");

                    Integer malaDireta = rs.getInt("maladireta");
                    if(malaDireta != null && malaDireta > 0) {
                        String observacao = rs.getString("observacao");

                        // Extract and print the values using the method
                        String titulo = extractField(observacao, tituloPattern);
                        String mensagem = extractField(observacao, mensagemPattern);

                        historicoContatoAlunoVO.setTexto("Título: " + titulo + " \n " +
                                "Mensagem: " + mensagem);
                    } else {
                        historicoContatoAlunoVO.setTexto(rs.getString("observacao"));
                    }
                    historicoContatoAlunoVOList.add(historicoContatoAlunoVO);
                }
            }
        }
    }

    private void trazObservacoesTreino(String contexto, String matriculaCliente, List<HistoricoContatoAlunoVO> historicoContatoAlunoVOList) throws Exception {
        List<ClienteObservacao> clienteObservacaoList = consultarObservacoesPorMatriculaClienteApp(contexto, matriculaCliente, 0, 0);

        for (ClienteObservacao clienteObservacao : clienteObservacaoList) {
            HistoricoContatoAlunoVO historicoContatoAlunoVO = new HistoricoContatoAlunoVO();
            historicoContatoAlunoVO.setDataContrato(clienteObservacao.getDataObservacao());
            historicoContatoAlunoVO.setOrigem("Observação Treino");
            historicoContatoAlunoVO.setTexto(clienteObservacao.getObservacao());
            historicoContatoAlunoVOList.add(historicoContatoAlunoVO);
        }
    }

    private void trazObservacoesADM(String contexto, String matriculaCliente, List<HistoricoContatoAlunoVO> historicoContatoAlunoVOList) throws Exception {
        StringBuilder sqlObservacoes = new StringBuilder();
        sqlObservacoes.append("SELECT * ");
        sqlObservacoes.append("FROM clienteobservacao co ");
        sqlObservacoes.append("LEFT JOIN cliente ON cliente.codigo = co.cliente ");
        sqlObservacoes.append("WHERE cliente.codigomatricula = ").append(matriculaCliente);
        sqlObservacoes.append("ORDER BY co.datacadastro desc;");
        try (Connection conZW = conexaoZWService.conexaoZw(contexto)) {
            try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sqlObservacoes.toString(), conZW)) {
                while (rs.next()) {
                    HistoricoContatoAlunoVO historicoContatoAlunoVO = new HistoricoContatoAlunoVO();
                    historicoContatoAlunoVO.setDataContrato(rs.getDate("datacadastro"));
                    historicoContatoAlunoVO.setOrigem("Observação ADM");
                    historicoContatoAlunoVO.setTexto(rs.getString("observacao"));
                    historicoContatoAlunoVOList.add(historicoContatoAlunoVO);
                }
            }
        }
    }

    private static String extractField(String content, String pattern) {
        Pattern compiledPattern = Pattern.compile(pattern);
        Matcher matcher = compiledPattern.matcher(content);
        if (matcher.find()) {
            return matcher.group(1).trim();
        }
        return null;
    }

}
