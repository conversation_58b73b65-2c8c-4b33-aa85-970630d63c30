package br.com.pacto.service.impl.tvGestor;

import br.com.pacto.bean.colaborador.ColaboradorResponseTO;
import br.com.pacto.bean.colaborador.ColaboradorSimplesTO;
import br.com.pacto.bean.configuracoes.ConfiguracaoSistemaUsuario;
import br.com.pacto.bean.configuracoes.ConfiguracoesUsuarioEnum;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.ambiente.AmbienteResponseTO;
import br.com.pacto.controller.json.modalidade.ModalidadeResponseTO;
import br.com.pacto.controller.json.tvGestor.dto.BiTvGestorDTO;
import br.com.pacto.controller.json.tvGestor.dto.FiltroTvGestorJSON;
import br.com.pacto.controller.json.tvGestor.dto.TvGestorFiltroResponse;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.ambiente.AmbienteService;
import br.com.pacto.service.intf.configuracoes.ConfiguracaoSistemaService;
import br.com.pacto.service.intf.modalidade.ModalidadeService;
import br.com.pacto.service.intf.professor.ProfessorSinteticoService;
import br.com.pacto.service.intf.tvGestor.TvGestorService;
import br.com.pacto.service.intf.usuario.FotoService;
import br.com.pacto.service.intf.usuario.UsuarioService;
import br.com.pacto.util.UtilContext;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

public abstract class TvGestorServiceImpl implements TvGestorService {
    @Autowired
    protected FotoService fotoService;
    @Autowired
    protected UsuarioService usuarioService;
    @Autowired
    protected ProfessorSinteticoService professorSinteticoService;
    @Autowired
    protected ModalidadeService modalidadeService;
    @Autowired
    protected AmbienteService ambienteService;

    @Override
    public TvGestorFiltroResponse atualizarFiltros(String chave, Integer empresaId, Integer idUsuario, FiltroTvGestorJSON filtro, HttpServletRequest request) throws ServiceException {
        ConfiguracaoSistemaService cs = UtilContext.getBean(ConfiguracaoSistemaService.class);
        Usuario usuario = usuarioService.obterPorId(chave, idUsuario);

        ConfiguracaoSistemaUsuario modalidade = cs.obterConfiguracaoUsuario(chave, usuario, ConfiguracoesUsuarioEnum.TVGESTOR_FILTRO_MODALIDADE);
        modalidade.setUsuario_codigo(usuario.getCodigo());
        modalidade.setValor(Uteis.listToString(filtro.getModalidadeIdsString()));
        modalidade.setConfiguracao(ConfiguracoesUsuarioEnum.TVGESTOR_FILTRO_MODALIDADE);
        cs.gravarConfiguracaoUsuario(chave, modalidade);

        ConfiguracaoSistemaUsuario professor = cs.obterConfiguracaoUsuario(chave, usuario, ConfiguracoesUsuarioEnum.TVGESTOR_FILTRO_PROFESSOR);
        professor.setUsuario_codigo(usuario.getCodigo());
        professor.setValor(Uteis.listToString(filtro.getProfessorIdsString()));
        professor.setConfiguracao(ConfiguracoesUsuarioEnum.TVGESTOR_FILTRO_PROFESSOR);
        cs.gravarConfiguracaoUsuario(chave, professor);

        ConfiguracaoSistemaUsuario ambiente = cs.obterConfiguracaoUsuario(chave, usuario, ConfiguracoesUsuarioEnum.TVGESTOR_FILTRO_AMBIENTE);
        ambiente.setUsuario_codigo(usuario.getCodigo());
        ambiente.setValor(Uteis.listToString(filtro.getAmbienteIdsString()));
        ambiente.setConfiguracao(ConfiguracoesUsuarioEnum.TVGESTOR_FILTRO_AMBIENTE);
        cs.gravarConfiguracaoUsuario(chave, ambiente);

        return getFiltro(chave, empresaId, idUsuario, request);
    }


    @Override
    public TvGestorFiltroResponse getFiltro(String chave, Integer empresaId, Integer idUsuario, HttpServletRequest request) throws ServiceException {
        ConfiguracaoSistemaService cs = UtilContext.getBean(ConfiguracaoSistemaService.class);
        Usuario usuario = usuarioService.obterPorId(chave, idUsuario);

        ConfiguracaoSistemaUsuario cfgAmbientes = cs.obterConfiguracaoUsuario(chave, usuario, ConfiguracoesUsuarioEnum.TVGESTOR_FILTRO_AMBIENTE);
        ConfiguracaoSistemaUsuario cfgModalidades = cs.obterConfiguracaoUsuario(chave, usuario, ConfiguracoesUsuarioEnum.TVGESTOR_FILTRO_MODALIDADE);
        ConfiguracaoSistemaUsuario cfgProfessores = cs.obterConfiguracaoUsuario(chave, usuario, ConfiguracoesUsuarioEnum.TVGESTOR_FILTRO_PROFESSOR);

        List<ColaboradorSimplesTO> colaboradores = new ArrayList<>();
        for(Integer id : getListInteger(cfgProfessores.getValor(), ";")){
            ColaboradorResponseTO colaborador = professorSinteticoService.buscarColaborador(id, empresaId, request, false);
            if(colaborador != null){
                colaboradores.add(new ColaboradorSimplesTO(colaborador.getId(), colaborador.getId(), colaborador.getNome(), colaborador.getUriImagem()));
            }
        }

        List<ModalidadeResponseTO> modalidades = new ArrayList<>();
        for(Integer id : getListInteger(cfgModalidades.getValor(), ";")){
            ModalidadeResponseTO modalidade = modalidadeService.detalhesModalidade(id);
            if(modalidade != null){
                modalidades.add(modalidade);
            }
        }

        List<AmbienteResponseTO> ambientes = getListaAmbientes(empresaId, getListInteger(cfgAmbientes.getValor(), ";"));

        return new TvGestorFiltroResponse(modalidades, ambientes, colaboradores);
    }

    private List<AmbienteResponseTO> getListaAmbientes(Integer empresaId, List<Integer> ambienteIds) throws ServiceException{
        List<AmbienteResponseTO> ambientesResponse = ambienteService.obterTodos(empresaId, false);
        List<AmbienteResponseTO> ambientesRetorno = new ArrayList<>();
        if(ambientesResponse != null && !ambientesResponse.isEmpty()){
            for(AmbienteResponseTO ambienteResponseTO : ambientesResponse){
                if(ambienteIds.contains(ambienteResponseTO.getId())){
                    ambientesRetorno.add(ambienteResponseTO);
                }
            }
        }
        return ambientesRetorno;
    }

    private List<Integer> getListInteger(String entrada, String separador){
        List<Integer> retorno = new ArrayList<>();
        if(StringUtils.isNotBlank(entrada) && StringUtils.isNotBlank(separador)){
            retorno = new ArrayList<>();
            String[] valores = entrada.split(separador);
            for(String valor : valores){
                retorno.add(Integer.parseInt(valor));
            }
        }
        return retorno;
    }

    protected Map<String, BiTvGestorDTO> getBiPorDia(Date inicio, Date fim){
        Map<String, BiTvGestorDTO> dados = new TreeMap<>();
        Calendar dataInicio = Calendar.getInstance();
        dataInicio.setTime(inicio);
        Calendar dataFim = Calendar.getInstance();
        dataFim.setTime(fim);

        while (dataInicio.compareTo(dataFim) < 1) {
            Uteis.iniciarMapa(dados, Calendario.getData(dataInicio.getTime(),"yyyyMMdd"), new BiTvGestorDTO());
            dataInicio.add(Calendar.DAY_OF_YEAR,1);
        }
        return dados;
    }

    protected Map<String, Map<String, BiTvGestorDTO>> getBiPorDiaHora(Date inicio, Date fim){
        Map<String, Map<String, BiTvGestorDTO>> dados = new TreeMap<>();
        Calendar dataInicio = Calendar.getInstance();
        dataInicio.setTime(inicio);
        Calendar dataFim = Calendar.getInstance();
        dataFim.setTime(fim);


        while (dataInicio.compareTo(dataFim) < 1) {
            Map<String, BiTvGestorDTO> dadosHora = new TreeMap<>();
            for (int hora=0; hora<24; hora++){
                Uteis.iniciarMapa(dadosHora,""+hora,new BiTvGestorDTO());
            }
            Uteis.iniciarMapa(dadosHora,"total",new BiTvGestorDTO());
            Uteis.iniciarMapa(dados, Calendario.getData(dataInicio.getTime(),"yyyyMMdd"), dadosHora);
            dataInicio.add(Calendar.DAY_OF_YEAR, 1);
        }
        return dados;
    }
}
