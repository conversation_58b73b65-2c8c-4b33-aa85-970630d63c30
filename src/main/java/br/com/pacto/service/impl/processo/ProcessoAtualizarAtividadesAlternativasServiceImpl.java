package br.com.pacto.service.impl.processo;

import br.com.pacto.bean.atividade.Atividade;
import br.com.pacto.bean.atividade.AtividadeAlternativa;
import br.com.pacto.dao.intf.atividade.AtividadeAlternativaDao;
import br.com.pacto.service.intf.atividade.AtividadeService;
import br.com.pacto.service.intf.processo.ProcessoAtualizarAtividadesAlternativasService;
import br.com.pacto.util.UteisValidacao;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.hibernate.exception.ConstraintViolationException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.StringReader;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
public class ProcessoAtualizarAtividadesAlternativasServiceImpl implements ProcessoAtualizarAtividadesAlternativasService {

    @Autowired
    AtividadeService atividadeService;
    @Autowired
    AtividadeAlternativaDao atividadeAlternativaDao;

    @Override
    public void start(String ctx, String path) throws Exception {
        Integer linhaAtual = 1;
        try (BufferedReader br = new BufferedReader(new StringReader(path))) {
            String line;
            br.readLine();

            while ((line = br.readLine()) != null) {
                String[] values = line.split(",");
                Integer colunaAtividade = 6;

                if (linhaAtual >= 8 && values.length >= colunaAtividade + 1) {
                    List<String> nomesAlternativos = new ArrayList<>();
                    System.out.println("Codigo: " + Integer.parseInt(values[0]));

                    String nome = values[colunaAtividade].trim().replace("\"", "");

                    Boolean buscaNomes = !UteisValidacao.emptyString(nome);

                    if(!buscaNomes)
                    System.out.println("Nenhum nome encontrado!");

                    while (buscaNomes) {
                        if(nome.contains("\"")) {
                            nome = nome.replace("\"", "");
                        }

                        nomesAlternativos.add(nome);
                        colunaAtividade++;
                        nome = values[colunaAtividade].trim();
                        buscaNomes = !UteisValidacao.emptyString(nome);
                    }

                    for (String nomeAlternativo : nomesAlternativos) {
                        Integer codigo = Integer.parseInt(values[0]);
                        Atividade atividade = atividadeService.obterPorId(ctx, codigo);

                        if (atividade != null) {
                            StringBuilder sql = new StringBuilder();
                            sql.append("SELECT obj FROM Atividade obj ");
                            sql.append(" WHERE upper(unaccent(nome)) = upper(unaccent('").append(nomeAlternativo.toUpperCase()).append("'))");

                            System.out.println("Atividade principal: " + atividade.getNome());
                            System.out.println("Nome da atividade alternativa buscado: " + nomeAlternativo);

                            HashMap<String, Object> param = new HashMap<>();

                            List<Atividade> atividadesAlternativas = atividadeService.obterPorParam(ctx, sql.toString(), param);

                            if(atividadesAlternativas != null && atividadesAlternativas.size() > 0) {
                                Atividade atividadeAlternativa = atividadesAlternativas.get(0);

                                System.out.println("Atividade alternativa encontrada: " + atividadeAlternativa.getNome());
//
                                AtividadeAlternativa atividadeAlternativa1 = new AtividadeAlternativa();
                                atividadeAlternativa1.setAtividade(atividade);
                                atividadeAlternativa1.setAtividadeAlternativa(atividadeAlternativa.getCodigo());

                                atividadeAlternativaDao.insert(ctx, atividadeAlternativa1);
                            } else {
                                System.out.println("Atividade alternativa não encontrada: " + nomeAlternativo);
                            }
                        } else {
                            System.out.println("Atividade principal não encontrada (código): " + codigo);
                        }
                    }

                }
                linhaAtual++;
            }
        }
        System.out.println("Concluido!");
    }
}
