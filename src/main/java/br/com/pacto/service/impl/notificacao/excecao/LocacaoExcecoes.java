package br.com.pacto.service.impl.notificacao.excecao;

import br.com.pacto.service.exception.ExcecaoSistema;

public enum LocacaoExcecoes implements ExcecaoSistema {

    ERRO_CHAVE_NULL("erro_chave_null", "A chave não foi localizada."),
    ERRO_JUSTIFICATIVA_VAZIA("erro_justificativa_vazia", "Para o cancelamento, é obrigatório o preenchimento da justificativa com pelo menos 3 palavras."),
    ERRO_CANCELAR_AGENDAMENTO_LOCACAO("erro_cancelar_agendamento_locacao", "Ocorreu um erro ao tentar cancelar o agendamento da locação."),
    ERRO_FINALIZAR_AGENDAMENTO_LOCACAO("erro_finalizar_agendamento_locacao", "Ocorreu um erro ao tentar finalizar o agendamento da locação."),
    ERRO_REAGENDAR_LOCACAO("erro_reagendar_locacao", "Ocorreu um erro ao tentar reagendar a locação."),
    ERRO_REAGENDAR_LOCACAO_SEM_HORARIO("erro_reagendar_locacao_sem_horario", "Para reagendar uma locação é necessário que o horário seja informado."),
    ERRO_REAGENDAR_LOCACAO_SEM_DISPONIBILIDADE("erro_reagendar_locacao_sem_disponibilidade", "O horário selecionado não está disponível para o dia informado."),
    ERRO_USUARIO_NAO_INFORMADO("erro_usuario_nao_informado", "Erro, o usuário que fez o cancelamento não foi informado."),
    VALIDACAO_LOCACAO_SEM_VIGENCIA("validadao_locacao_sem_vigencia", "A locação não está vigente para a data informada."),
    VALIDACAO_LOCACAO_SEM_DISPONIBILIDADE("validadao_locacao_sem_disponibilidade", "A locação não possui horários disponíveis na data informada."),
    AGENDAMENTO_JA_FINALIZADO("erro_agendamento_finalizado_locacao", "Ocorreu um erro o agendamento da locação já está finalizado.");

    private String chave;
    private String descricao;

    LocacaoExcecoes(String chave, String descricao) {
        this.chave = chave;
        this.descricao = descricao;
    }

    @Override
    public String getChaveExcecao() {
        return chave;
    }

    @Override
    public String getDescricaoExcecao() {
        return descricao;
    }

}
