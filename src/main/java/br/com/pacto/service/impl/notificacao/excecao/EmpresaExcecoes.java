package br.com.pacto.service.impl.notificacao.excecao;

import br.com.pacto.service.exception.ExcecaoSistema;

/**
 * Created by ulisses on 29/08/2018.
 */
public enum EmpresaExcecoes implements ExcecaoSistema {

    ERRO_BUSCAR_EMPRESAS("erro_buscar_empresas", "Ocorreu um erro ao buscar as Empresas"),
    VALIDACAO_NOME_EMPRESAS("validacao_nome_empresas", "Nome da empresa não informado"),
    VALIDACAO_TIME_ZONE_EMPRESAS("validacao_time_zone_empresas", "Fuso horário da empresa não informado"),
    VALIDACAO_EMAIL_EMPRESAS("validacao_email_empresas", "E-mail de contato da empresa não informado"),
    VALIDACAO_EMPERSA_JA_EXISTE("validacao_empresa_ja_existe", "Já existe uma empresa cadastrada com este nome"),
    ERRO_ATIVAR_EMPRESA("erro_ativar_empresa", "erro_ativar_empresa")
    ;

    private String chave;
    private String descricao;

    EmpresaExcecoes(String chave, String descricao) {
        this.chave = chave;
        this.descricao = descricao;
    }

    @Override
    public String getChaveExcecao() {
        return chave;
    }

    @Override
    public String getDescricaoExcecao() {
        return descricao;
    }

}
