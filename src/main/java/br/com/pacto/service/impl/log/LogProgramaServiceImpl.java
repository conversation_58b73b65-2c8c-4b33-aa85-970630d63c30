package br.com.pacto.service.impl.log;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.atividade.MetodoExecucaoEnum;
import br.com.pacto.bean.log.Log;
import br.com.pacto.bean.programa.ProgramaSituacaoEnum;
import br.com.pacto.bean.programa.ProgramaTreino;
import br.com.pacto.bean.programa.ProgramaTreinoFicha;
import br.com.pacto.bean.sincronizacao.TipoRevisaoEnum;
import br.com.pacto.dao.intf.log.LogDao;
import br.com.pacto.dao.intf.programa.ProgramaTreinoDao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.log.LogProgramaService;
import br.com.pacto.util.UteisValidacao;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static java.util.Objects.isNull;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

@Service
public class LogProgramaServiceImpl implements LogProgramaService {

    @Autowired
    private SessaoService sessaoService;
    @Autowired
    private ProgramaTreinoDao programaTreinoDao;
    @Autowired
    private LogDao logDao;

    private void tratarNomes(String ctx, List<AlteracoesTO> alteracoes) {
        tratarNomes(ctx, alteracoes, "professormontou_codigo",
                "select codigo, nome from professorsintetico where codigo in (_codigos#entidades_)",
                "Professor Montou");

        tratarNomes(ctx, alteracoes, "nivel_codigo",
                "select codigo, nome from nivel where codigo in (_codigos#entidades_)",
                "Nivel");

        tratarNomes(ctx, alteracoes, "categoria_codigo",
                "select codigo, nome from categoriaficha where codigo in (_codigos#entidades_)",
                "Categoria");

    }
    public void tratarNomes(String ctx, List<AlteracoesTO> alteracoes,
                             String campo,
                             String sqlConsulta,
                             String label
                             ) {
        try {
            List<String> cods = new ArrayList<>();
            for(AlteracoesTO alteracao : alteracoes){
                if(campo.equals(alteracao.getCampo())){
                    if(!cods.contains(alteracao.getValorAlterado()) && !Uteis.removerTudoMenosNumero(alteracao.getValorAlterado()).isEmpty()){
                        cods.add(alteracao.getValorAlterado());
                    }
                    if(!cods.contains(alteracao.getValorAnterior()) && !Uteis.removerTudoMenosNumero(alteracao.getValorAnterior()).isEmpty()){
                        cods.add(alteracao.getValorAnterior());
                    }
                }
            }
            if(cods.isEmpty()){
                for(AlteracoesTO alteracao : alteracoes){
                    if(campo.equals(alteracao.getCampo())){
                        alteracao.setCampo(label);
                    }
                }
                return;
            }
            Map<String, String> nomes = Uteis.resultSetToMap(programaTreinoDao.createStatement(ctx,
                    sqlConsulta.replace("_codigos#entidades_", Uteis.arrayToString(cods.toArray(new String[cods.size()]), ","))), "codigo", "nome");
            for(AlteracoesTO alteracao : alteracoes){
                if(campo.equals(alteracao.getCampo())){
                    alteracao.setCampo(label);
                    alteracao.setValorAlterado(nomes.get(alteracao.getValorAlterado()) == null ? alteracao.getValorAlterado() : nomes.get(alteracao.getValorAlterado()));
                    alteracao.setValorAnterior(nomes.get(alteracao.getValorAnterior()) == null ? alteracao.getValorAnterior() : nomes.get(alteracao.getValorAnterior()));
                }
            }
        }catch (Exception e){
            Uteis.logar(e, LogProgramaServiceImpl.class);
        }
    }

    private String diasSemanaLog(String ctx, Long momento, String username, TipoRevisaoEnum tipo) throws Exception{
        String ficha;
        String ds;
        try (ResultSet rs = programaTreinoDao.createStatement(ctx, " select diasemana, f.nome from programatreinoficha_diasemana_aud pda  " +
                " inner join customrevisionentity c on pda.rev = c.id " +
                " left join programatreinoficha ptf on ptf.codigo = pda.programatreinoficha_codigo \n" +
                " left join ficha f on f.codigo = ptf.ficha_codigo " +
                " where c.\"timestamp\"/5000 = " + momento +
                " and c.username = '" + username +
                "' and revtype = " + tipo.getId() +
                " ORDER BY rev desc")) {
            ficha = "";
            ds = "";
            while (rs.next()) {
                ds += "," + rs.getString("diasemana");
                ficha = rs.getString("nome");
            }
        }
        return ds.replaceFirst(",", "") + (ficha.isEmpty() ? "" : (" na ficha " + ficha));
    }

    private List<AlteracoesTO> logProgramaTreinoFichaRelacionado(String ctx, Long momento, String username, Integer ficha) throws Exception {
        List<AlteracoesTO> alteracoes = new ArrayList();
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT c.id, pta.codigo, pta.tipoexecucao, pta.revtype \n");
        sql.append(" FROM customrevisionentity c \n");
        sql.append(" INNER JOIN programatreinoficha_aud pta ON pta.rev = c.id \n");
        sql.append(" WHERE c.\"timestamp\"/5000 = ").append(momento);
        sql.append(" AND c.username = '").append(username);
        sql.append("' AND pta.ficha_codigo = ").append(ficha);
        sql.append(" ORDER BY rev ");
        try (ResultSet rs = programaTreinoDao.createStatement(ctx, sql.toString())) {
            while (rs.next()) {
                TipoRevisaoEnum revtype = TipoRevisaoEnum.getFromId(rs.getInt("revtype"));
                Integer codigo = rs.getInt("codigo");
                Long rev = rs.getLong("id");
                Map<String, String> valoresAlterados = Uteis.montarMapa(rs, "tipoexecucao");
                Map<String, String> valoresAnteriores = null;
                if (revtype.equals(TipoRevisaoEnum.UPDATE)) {
                    try (ResultSet rsAnterior = programaTreinoDao.createStatement(ctx,
                            "SELECT rev, pta.tipoexecucao \n" +
                                    "FROM programatreinoficha_aud pta \n" +
                                    "WHERE codigo = " + codigo + " \n" +
                                    "AND rev < " + rev + " \n" +
                                    "ORDER BY rev DESC LIMIT 1 ")) {
                        if (rsAnterior.next()) {
                            valoresAnteriores = Uteis.montarMapa(rsAnterior, "tipoexecucao");
                        }
                    }
                }
                String tipoExecucaoAlterado = valoresAlterados.get("tipoexecucao").equals("1") ? "Dias da semana" : "Alternado";
                valoresAlterados.put("tipoexecucao", tipoExecucaoAlterado);
                if (valoresAnteriores != null) {
                    String tipoExecucaoAnterior = valoresAnteriores.get("tipoexecucao").equals("1") ? "Dias da semana" : "Alternado";
                    valoresAnteriores.put("tipoexecucao", tipoExecucaoAnterior);
                }
                alteracoes = Uteis.compararMapas(valoresAnteriores, valoresAlterados);
            }
        }
        return alteracoes;
    }

    private List<AlteracoesTO> logFichaRelacionada(String ctx, Long timestamp) throws Exception {
        List<AlteracoesTO> alteracoes = new ArrayList();
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT c.id, fa.codigo, fa.nome, fa.categoria_codigo, fa.mensagemaluno, fa.revtype \n");
        sql.append("FROM customrevisionentity c \n");
        sql.append("INNER JOIN ficha_aud fa ON fa.rev = c.id \n");
        sql.append("WHERE c.\"timestamp\"/1000 BETWEEN (").append(timestamp).append("/1000)-2 AND ").append(timestamp).append("/1000 \n");
        try (ResultSet rs = programaTreinoDao.createStatement(ctx, sql.toString())) {
            while (rs.next()) {
                TipoRevisaoEnum revtype = TipoRevisaoEnum.getFromId(rs.getInt("revtype"));
                Integer codigo = rs.getInt("codigo");
                Long rev = rs.getLong("id");
                Map<String, String> valoresAlterados = Uteis.montarMapa(rs, "nome", "categoria_codigo", "mensagemaluno");
                Map<String, String> valoresAnteriores = null;
                if (revtype.equals(TipoRevisaoEnum.UPDATE) || revtype.equals(TipoRevisaoEnum.DELETE)) {
                    try (ResultSet rsAnterior = programaTreinoDao.createStatement(ctx,
                            "SELECT fa.nome, fa.categoria_codigo, fa.mensagemaluno \n" +
                                    "FROM ficha_aud fa \n" +
                                    "WHERE codigo = " + codigo + " \n" +
                                    "AND rev < " + rev + " \n" +
                                    "ORDER BY rev DESC LIMIT 1 ")) {
                        if (rsAnterior.next()) {
                            valoresAnteriores = Uteis.montarMapa(rsAnterior, "nome", "categoria_codigo", "mensagemaluno");
                            List<AlteracoesTO> alts = Uteis.compararMapas(valoresAnteriores, valoresAlterados);
                            if (alts.size() > 0) {
                                AlteracoesTO alteracoesTO = new AlteracoesTO();
                                alteracoesTO.setCampo(revtype.equals(TipoRevisaoEnum.UPDATE) ? "FICHA ALTERADA" : "FICHA REMOVIDA");
                                alteracoesTO.setValorAlterado(revtype.equals(TipoRevisaoEnum.UPDATE) ? rs.getString("nome") : rsAnterior.getString("nome"));
                                alteracoes.add(alteracoesTO);
                                alteracoes.addAll(alts);
                            }
                        }
                    }
                } else if (revtype.equals(TipoRevisaoEnum.INSERT)) {
                    AlteracoesTO alteracoesTO = new AlteracoesTO();
                    alteracoesTO.setCampo("FICHA ADICIONADA");
                    alteracoesTO.setValorAlterado(rs.getString("nome"));
                    alteracoes.add(alteracoesTO);
                    alteracoes.addAll(Uteis.compararMapas(valoresAnteriores, valoresAlterados));
                }
            }
        }
        return alteracoes;
    }

    private List<AlteracoesTO> logAtividadeFichaRelacionada(String ctx, Long momento, String username) throws Exception {
        List<AlteracoesTO> alteracoes = new ArrayList();
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT c.id, af.codigo, af.complementonomeatividade, af.intensidade, af.metodoexecucao, af.nome, af.ordem, af.revtype, af.ficha_codigo \n");
        sql.append(" FROM customrevisionentity c \n");
        sql.append(" INNER JOIN atividadeficha_aud af ON af.rev = c.id \n");
        sql.append(" WHERE c.\"timestamp\" /5000 = ").append(momento);
        sql.append(" and c.username = '").append(username).append("'");
        try (ResultSet rs = programaTreinoDao.createStatement(ctx, sql.toString())) {
            while (rs.next()) {
                TipoRevisaoEnum revtype = TipoRevisaoEnum.getFromId(rs.getInt("revtype"));
                Integer codigo = rs.getInt("codigo");
                Long rev = rs.getLong("id");
                Map<String, String> valoresAlterados = Uteis.montarMapa(rs, "nome", "complementonomeatividade", "intensidade", "metodoexecucao", "ordem");
                valoresAlterados.put("metodoexecucao", MetodoExecucaoEnum.getFromOrdinal(rs.getInt("metodoexecucao")).toString());
                Map<String, String> valoresAnteriores = null;
                if (revtype.equals(TipoRevisaoEnum.UPDATE) || revtype.equals(TipoRevisaoEnum.DELETE)) {
                    try (ResultSet rsAnterior = programaTreinoDao.createStatement(ctx,
                            "SELECT af.complementonomeatividade, af.intensidade, af.metodoexecucao, af.nome, af.ordem \n" +
                                    "FROM atividadeficha_aud af \n" +
                                    "WHERE codigo = " + codigo + " \n" +
                                    "AND rev < " + rev + " \n" +
                                    "ORDER BY rev DESC LIMIT 1 ")) {
                        if (rsAnterior.next()) {
                            valoresAnteriores = Uteis.montarMapa(rsAnterior, "nome", "complementonomeatividade", "intensidade", "metodoexecucao", "ordem");
                            valoresAnteriores.put("metodoexecucao", MetodoExecucaoEnum.getFromOrdinal(rsAnterior.getInt("metodoexecucao")).toString());
                            List<AlteracoesTO> alts = Uteis.compararMapas(valoresAnteriores, valoresAlterados);
                            if (alts.size() > 0) {
                                alteracoes.add(montarIdentificadorFichaAlterada(ctx, rs.getInt("ficha_codigo")));
                                AlteracoesTO alteracoesTO = new AlteracoesTO();
                                alteracoesTO.setCampo(revtype.equals(TipoRevisaoEnum.UPDATE) ? "ATIVIDADE ALTERADA" : "ATIVIDADE REMOVIDA");
                                alteracoesTO.setValorAlterado(revtype.equals(TipoRevisaoEnum.UPDATE) ? rs.getString("nome") : rsAnterior.getString("nome"));
                                alteracoes.add(alteracoesTO);
                                if (revtype.equals(TipoRevisaoEnum.UPDATE)) {
                                    alteracoes.addAll(alts);
                                }
                            }
                        } else {
                            alteracoes.add(montarIdentificadorFichaAlterada(ctx, rs.getInt("ficha_codigo")));
                            AlteracoesTO alteracoesTO = new AlteracoesTO();
                            alteracoesTO.setCampo("ATIVIDADE ADICIONADA");
                            alteracoesTO.setValorAlterado(rs.getString("nome"));
                            alteracoes.add(alteracoesTO);
                            alteracoes.addAll(Uteis.compararMapas(valoresAnteriores, valoresAlterados));
                        }
                    }
                }
            }
        }
        return alteracoes;
    }

    private AlteracoesTO montarIdentificadorFichaAlterada(String ctx, Integer codigoFicha) throws Exception {
        AlteracoesTO fichaAlterada = new AlteracoesTO();
        fichaAlterada.setCampo("FICHA ALTERADA");
        fichaAlterada.setValorAlterado(obterNomeFicha(ctx, codigoFicha));
        return fichaAlterada;
    }

    private String obterNomeFicha(String ctx, Integer codigoFicha) throws Exception {
        ResultSet rs = programaTreinoDao.createStatement(ctx, "SELECT nome FROM ficha WHERE codigo = " + codigoFicha);
        if (rs.next()) {
            return rs.getString("nome");
        }
        return "";
    }

    private String obterNomeAtividadeFicha(String ctx, Integer codigoAtividade) throws Exception {
        try (ResultSet rs = programaTreinoDao.createStatement(ctx, "SELECT nome FROM atividadeficha WHERE codigo = " + codigoAtividade)) {
            if (rs.next()) {
                return rs.getString("nome");
            }
        }
        return "";
    }

    private List<AlteracoesTO> logSeriesAtividadeRelacionada(String ctx, Long momento, String username) throws Exception {
        List<AlteracoesTO> alteracoes = new ArrayList();
        StringBuilder sqlPrincipal = new StringBuilder();
        sqlPrincipal.append("SELECT count(c.id) as qtdSeries, sa.atividadeficha_codigo, c.id  \n");
        sqlPrincipal.append("FROM customrevisionentity c \n");
        sqlPrincipal.append("INNER JOIN serie_aud sa ON sa.rev = c.id \n");
        sqlPrincipal.append("WHERE c.\"timestamp\"/5000 = ").append(momento);
        sqlPrincipal.append(" and c.username = '").append(username).append("' ");
        sqlPrincipal.append("GROUP BY 2,3");

        try (ResultSet rsPrincipal = programaTreinoDao.createStatement(ctx, sqlPrincipal.toString())) {
            while (rsPrincipal.next()) {
                Integer id = rsPrincipal.getInt("id");
                Integer atividadeFichaCodigo = rsPrincipal.getInt("atividadeficha_codigo");
                Integer qtdSeries = rsPrincipal.getInt("qtdSeries");

                StringBuilder sql = new StringBuilder();
                sql.append("SELECT c.id, sa.codigo, sa.cadencia, sa.carga, sa.complemento, sa.descanso, sa.distancia, sa.duracao, sa.ordem, sa.repeticao, sa.velocidade, sa.revtype, sa.atividadeficha_codigo \n");
                sql.append("FROM customrevisionentity c \n");
                sql.append("INNER JOIN serie_aud sa ON sa.rev = c.id \n");
                sql.append("WHERE c.id = ").append(id).append(" \n");
                sql.append("LIMIT 1");

                try (ResultSet rs = programaTreinoDao.createStatement(ctx, sql.toString())) {
                    while (rs.next()) {
                        TipoRevisaoEnum revtype = TipoRevisaoEnum.getFromId(rs.getInt("revtype"));
                        Integer codigo = rs.getInt("codigo");
                        Long rev = rs.getLong("id");
                        Map<String, String> valoresAlterados = Uteis.montarMapa(rs, "cadencia", "carga", "complemento", "descanso", "distancia", "duracao", "ordem", "repeticao", "velocidade");
                        Map<String, String> valoresAnteriores = null;
                        Integer codigoAtividadeFichaAlterada = null;
                        if (revtype.equals(TipoRevisaoEnum.UPDATE) || revtype.equals(TipoRevisaoEnum.DELETE)) {
                            try (ResultSet rsAnterior = programaTreinoDao.createStatement(ctx,
                                    "SELECT sa.codigo, sa.cadencia, sa.carga, sa.complemento, sa.descanso, sa.distancia, sa.duracao, sa.ordem, sa.repeticao, sa.velocidade, sa.revtype, sa.atividadeficha_codigo \n" +
                                            "FROM serie_aud sa \n" +
                                            "WHERE codigo = " + codigo + " \n" +
                                            "AND rev < " + rev + " \n" +
                                            "ORDER BY rev DESC LIMIT 1 ")) {
                                if (rsAnterior.next()) {
                                    valoresAnteriores = Uteis.montarMapa(rsAnterior, "cadencia", "carga", "complemento", "descanso", "distancia", "duracao", "ordem", "repeticao", "velocidade");
                                    codigoAtividadeFichaAlterada = rsAnterior.getInt("atividadeficha_codigo");
                                }
                            }
                        }
                        if (revtype.equals(TipoRevisaoEnum.DELETE)) {
                            AlteracoesTO acaoSerie = new AlteracoesTO();
                            acaoSerie.setCampo("SERIE ALTERADA NA ATIVIDADE ");
                            acaoSerie.setValorAlterado(obterNomeAtividadeFicha(ctx, codigoAtividadeFichaAlterada));
                            alteracoes.add(acaoSerie);

                            AlteracoesTO qtdSeriesAlt = new AlteracoesTO();
                            qtdSeriesAlt.setCampo("séries removidas");
                            qtdSeriesAlt.setValorAlterado(qtdSeries.toString());
                            alteracoes.add(qtdSeriesAlt);
                        } else {
                            List<AlteracoesTO> alts = Uteis.compararMapas(valoresAnteriores, valoresAlterados);
                            if (alts.size() > 0) {
                                AlteracoesTO acaoSerie = new AlteracoesTO();
                                if (valoresAlterados != null && valoresAnteriores == null) {
                                    acaoSerie.setCampo("SERIE ADICIONADA NA ATIVIDADE ");
                                } else {
                                    acaoSerie.setCampo("SERIE ALTERADA NA ATIVIDADE ");
                                }
                                acaoSerie.setValorAlterado(obterNomeAtividadeFicha(ctx, rs.getInt("atividadeficha_codigo")));
                                alteracoes.add(acaoSerie);
                                AlteracoesTO qtdSeriesAlt = new AlteracoesTO();
                                if (revtype.equals(TipoRevisaoEnum.INSERT)) {
                                    qtdSeriesAlt.setCampo("séries adicionadas");
                                    qtdSeriesAlt.setValorAlterado(qtdSeries.toString());
                                    alteracoes.add(qtdSeriesAlt);
                                }
                                alteracoes.addAll(alts);
                            }
                        }
                    }
                }
            }
        }
        return alteracoes;
    }

    // ----------------- INÍCIO LOG FICHAS PREDEFINIDAS
    public List<LogTO> listarLogFichas(Integer codigoFicha, JSONObject filtros, PaginadorDTO paginadorDTO) throws ServiceException {
        List<LogTO> listarLog = new ArrayList<>();
        try {
            String quicksearchValue = filtros.optString("quicksearchValue");
            JSONArray tipos = filtros.optJSONArray("tipo");
            Long inicio = filtros.optLong("dataInicio");
            Long fim = filtros.optLong("dataFim");
            Integer codigoAtv = null;
            try {
                codigoAtv = quicksearchValue == null ? null : Integer.valueOf(quicksearchValue);
            } catch (Exception e) {
                //ignore
            }
            String ctx = sessaoService.getUsuarioAtual().getChave();
            StringBuilder sqlprincipal = new StringBuilder();
            sqlprincipal.append(" SELECT _campos_ \n");
            sqlprincipal.append(" FROM customrevisionentity c \n");
            sqlprincipal.append(" inner join usuario u on u.username = c.username \n");
            sqlprincipal.append(" INNER JOIN ficha_aud fa ON fa.rev = c.id  \n");
            sqlprincipal.append(" LEFT JOIN ficha_aud fa2 ON fa2.codigo = fa.codigo AND fa2.revtype = 0  \n");
            if (codigoFicha == null) {
                sqlprincipal.append(" WHERE fa2.usarcomopredefinida IS TRUE \n");
            } else {
                sqlprincipal.append(" WHERE fa.codigo = " + codigoFicha + " \n");
            }
            if (inicio != null && inicio > 0l) {
                Long dataInicio = Calendario.getDataComHoraZerada(new Date(inicio)).getTime();
                sqlprincipal.append(" AND c.\"timestamp\" >= ").append(dataInicio).append(" \n");
            }
            if (fim != null && fim > 0l) {
                Long dataFim = Calendario.fimDoDia(new Date(fim)).getTime();
                sqlprincipal.append(" AND c.\"timestamp\" <= ").append(dataFim).append(" \n");
            }
            if (tipos != null && tipos.length() > 0) {
                String tiposCod = "";
                for (int i = 0; i < tipos.length(); i++) {
                    tiposCod += "," + TipoRevisaoEnum.valueOf(tipos.getString(i)).getId();
                }
                sqlprincipal.append(" AND fa.revtype in ( ").append(tiposCod.replaceFirst(",", "")).append(" )").append(" \n");
            }
            sqlprincipal.append(UteisValidacao.emptyString(quicksearchValue) ? "" : " AND (");
            if (codigoAtv != null) {
                sqlprincipal.append(" fa.codigo = ").append(codigoAtv).append(" OR \n");
            }
            if (!UteisValidacao.emptyString(quicksearchValue)) {
                sqlprincipal.append(" UPPER(fa.nome) LIKE '%").append(quicksearchValue.toUpperCase()).append("%' OR \n");
                sqlprincipal.append(" UPPER(c.username) LIKE '%").append(quicksearchValue.toUpperCase()).append("%' \n");
            }
            sqlprincipal.append(UteisValidacao.emptyString(quicksearchValue) ? "" : ")");
            Integer maxResults = 10;
            Integer indiceInicial = 0;
            if (paginadorDTO != null) {
                maxResults = paginadorDTO.getSize() == null ? 10 : paginadorDTO.getSize().intValue();
                indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
            }
            try (ResultSet rs = programaTreinoDao.createStatement(ctx,
                    sqlprincipal.toString().replace("_campos_", "c.id, c.ip, c.screenname, c.\"timestamp\", c.username, c.processo, " +
                                    " fa.codigo, fa.ativo, fa.mensagemaluno, fa.nome, fa.categoria_codigo, fa.revtype")
                            .concat("ORDER BY \"timestamp\" DESC, id DESC LIMIT ".concat(maxResults.toString()))
                            .concat(" OFFSET ".concat(indiceInicial.toString())))) {
                while (rs.next()) {
                    Long timestamp = rs.getLong("timestamp");
                    TipoRevisaoEnum revtype = TipoRevisaoEnum.getFromId(rs.getInt("revtype"));
                    Integer codigo = rs.getInt("codigo");
                    Long rev = rs.getLong("id");
                    String username = rs.getString("username");
                    Map<String, String> valoresAlterados = Uteis.montarMapa(rs, "ativo", "mensagemaluno", "nome", "categoria_codigo");
                    Map<String, String> valoresAnteriores = null;
                    LogTO logTO = new LogTO(codigo, revtype.getDescricaoLog(),
                            rs.getString("nome"),
                            username,
                            Uteis.getDataAplicandoFormatacao(new Date(timestamp), "dd/MM/yyyy HH:mm"),
                            Uteis.getDataAplicandoFormatacao(new Date(timestamp), "HH:mm:ss"),
                            new ArrayList());
                    if (revtype.equals(TipoRevisaoEnum.UPDATE) || revtype.equals(TipoRevisaoEnum.DELETE)) {
                        try (ResultSet rsAnterior = programaTreinoDao.createStatement(ctx,
                                "SELECT fa.codigo, fa.ativo, fa.mensagemaluno, fa.nome, fa.categoria_codigo, fa.revtype \n" +
                                        " FROM ficha_aud fa \n" +
                                        " WHERE fa.codigo = " + codigo + " \n" +
                                        " AND fa.rev < " + rev + " \n" +
                                        " ORDER BY fa.rev DESC LIMIT 1 ")) {
                            if (rsAnterior.next()) {
                                valoresAnteriores = Uteis.montarMapa(rsAnterior, "ativo", "mensagemaluno", "nome", "categoria_codigo");
                                if (revtype.equals(TipoRevisaoEnum.DELETE)) {
                                    logTO.setIdentificador(rsAnterior.getString("nome"));
                                }
                            }
                        }
                    }
                    logTO.setAlteracoes(Uteis.compararMapas(valoresAnteriores, valoresAlterados));
                    if (revtype.equals(TipoRevisaoEnum.UPDATE)) {
                        logTO.getAlteracoes().addAll(logAtividadeFichaRelacionada(ctx, timestamp / 5000, username));
                        logTO.getAlteracoes().addAll(logSeriesAtividadeRelacionada(ctx, timestamp / 5000, username));
                    }

                    for (AlteracoesTO a : logTO.getAlteracoes()) {
                        logTO.setDescricao(logTO.getDescricao() + ("[" + a.getCampo() + ":'"
                                + (UteisValidacao.emptyString(a.getValorAnterior()) ? "" : (a.getValorAnterior() + "' para '"))
                                + a.getValorAlterado() + "']<br/>"));
                    }
                    listarLog.add(logTO);
                }
            }
            try (ResultSet rsCount = programaTreinoDao.createStatement(ctx, sqlprincipal.toString().replace("_campos_", " count(c.id) as cont "))) {
                if (rsCount.next()) {
                    paginadorDTO.setQuantidadeTotalElementos(rsCount.getLong("cont"));
                } else {
                    paginadorDTO.setQuantidadeTotalElementos(10l);
                }
            }
        }catch (Exception e){
            throw new ServiceException(e);
        }

        return listarLog;
    }

    public ResultSet descobrirQuemFoiAlteradoNoMomento(String chave, Long momento, String username, Integer codigo, Integer revtype) throws Exception{
        return programaTreinoDao.createStatement(chave, "select codigo, rev, " +
                Uteis.arrayToString(colunas, ",") +
                " from programatreino_aud pa " +
                " inner join customrevisionentity c on c.id = pa.rev" +
                " where c.username = '" + username +
                "' and c.\"timestamp\"/5000 = " + momento + (UteisValidacao.emptyNumber(codigo) ? "" : (" and pa.codigo = " + codigo)) +
                " and pa.revtype = " + revtype);
    }

    private ResultSet descobrirFichaAlteradaNoMomento(String chave, Long momento, String username, Integer codigo) throws Exception{
        String codigosFicha = fichasDoPrograma(chave, "programatreinoficha_aud", codigo);
        codigosFicha += fichasDoPrograma(chave, "programatreinoficha", codigo);
        if(codigosFicha.isEmpty()){
            return null;
        }
        return programaTreinoDao.createStatement(chave, "select rev, revtype, codigo, " +
                Uteis.arrayToString(colunasFicha, ",") +
                        " from ficha_aud pa\n" +
                        " inner join customrevisionentity c on c.id = pa.rev\n" +
                        " where c.username = '"+username+"' " +
                        " and c.\"timestamp\"/5000 = " + momento +
                        " and pa.codigo in (" + codigosFicha.replaceFirst(",", "") + ") ");
    }

    private String fichasDoPrograma(String chave, String tabela, Integer programa) throws Exception{
        String codigosFicha = "";
        try (ResultSet rsCodigos = programaTreinoDao.createStatement(chave, "select ficha_codigo from " + tabela + " pa where programa_codigo = " + programa)) {
            while (rsCodigos.next()) {
                codigosFicha += "," + rsCodigos.getInt("ficha_codigo");
            }
        }
        return codigosFicha;
    }

    public List<LogTO> listarLogProgramas(Integer codigoAluno, Integer codigoPrograma, JSONObject filtros, PaginadorDTO paginadorDTO) throws ServiceException {
        List<LogTO> listarLog = new ArrayList<>();
        try {
            String quicksearchValue = filtros.optString("quicksearchValue");
            JSONArray tipos = filtros.optJSONArray("tipo");
            Long inicio = filtros.optLong("dataInicio");
            Long fim = filtros.optLong("dataFim");
            Integer codigoAtv = null;
            try {
                codigoAtv = quicksearchValue == null ? null : Integer.valueOf(quicksearchValue);
            } catch (Exception e) {
                //ignore
            }
            String ctx = sessaoService.getUsuarioAtual().getChave();
            StringBuilder sqlprincipal = new StringBuilder();
            sqlprincipal.append(" select \"timestamp\"/5000 as segundo,\n");
            sqlprincipal.append("         processo,\n");
            sqlprincipal.append("         u.username \n");
            sqlprincipal.append(" from customrevisionentity c\n");
            sqlprincipal.append(" inner join usuario u on c.username = u.username \n");
            if(codigoAluno == null && codigoPrograma == null){
                sqlprincipal.append(" where c.processo like 'prpr_%'\n");
            } else if (codigoAluno != null && codigoPrograma == null) {
                sqlprincipal.append(" where c.processo like '%\\_c").append(codigoAluno).append("_%'");
            } else {
                sqlprincipal.append(" where c.processo like '%\\_").append(codigoPrograma).append("'");
            }
            if (inicio != null && inicio > 0l) {
                Long dataInicio = Calendario.getDataComHoraZerada(new Date(inicio)).getTime();
                sqlprincipal.append(" AND c.\"timestamp\" >= ").append(dataInicio).append(" \n");
            }
            if (fim != null && fim > 0l) {
                Long dataFim = Calendario.fimDoDia(new Date(fim)).getTime();
                sqlprincipal.append(" AND c.\"timestamp\" <= ").append(dataFim).append(" \n");
            }

            if(tipos != null && tipos.length() > 0){
                sqlprincipal.append(" and ( ");
                for(int i = 0; i < tipos.length(); i++){
                    TipoRevisaoEnum tipoRevisaoEnum = TipoRevisaoEnum.valueOf(tipos.getString(i));
                    if(i > 0){
                        sqlprincipal.append(" or ");
                    }
                    sqlprincipal.append(" c.processo like '%\\_t").append(tipoRevisaoEnum.getId()).append("_%'\n");
                }
                sqlprincipal.append(" ) \n ");
            }

            if (!UteisValidacao.emptyString(quicksearchValue) && (codigoPrograma == null || !codigoPrograma.toString().equals(quicksearchValue))) {
                sqlprincipal.append(" and (upper(c.processo) like '%").append(quicksearchValue.toUpperCase()).append("%' \n");
                sqlprincipal.append(" or upper(c.username) like '%").append(quicksearchValue.toUpperCase()).append("%') \n");
            }

            sqlprincipal.append(" group by 1,2,3  \n");

            Integer maxResults = 10;
            Integer indiceInicial = 0;
            if (paginadorDTO != null) {
                maxResults = paginadorDTO.getSize() == null ? 10 : paginadorDTO.getSize().intValue();
                indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
            }
            try (ResultSet rs = programaTreinoDao.createStatement(ctx,
                    sqlprincipal.toString()
                            .concat("order by 1 desc limit ".concat(maxResults.toString()))
                            .concat(" offset ".concat(indiceInicial.toString())))) {

            while(rs.next()){
                String[] processo = rs.getString("processo").split("_");
                TipoRevisaoEnum revtype = TipoRevisaoEnum.getFromId(Integer.valueOf(processo[3].replace("t", "")));
                Integer codigo = Integer.valueOf(processo[5]);
                Long timestamp = rs.getLong("segundo") * 5000;
                String username = rs.getString("username");
                LogTO logTO = new LogTO(codigo,
                        revtype.getDescricaoLog(),
                        processo[1],
                        username,
                        Uteis.getDataAplicandoFormatacao(new Date(timestamp), "dd/MM/yyyy HH:mm"),
                        Uteis.getDataAplicandoFormatacao(new Date(timestamp), "HH:mm:ss"),
                        new ArrayList());
                ResultSet rsRevs = descobrirQuemFoiAlteradoNoMomento(ctx, rs.getLong("segundo"), username, codigo, revtype.getId());
                logTO.setAlteracoes(new ArrayList<>());
                while(rsRevs.next()){
                    logTO.setChave(UteisValidacao.emptyNumber(codigo) ? rsRevs.getString("codigo") : String.valueOf(codigo));
                    Map<String, String> valoresAlterados = Uteis.montarMapa(rsRevs, colunas);
                    String dsAdicionados = diasSemanaLog(ctx, rs.getLong("segundo"), username, TipoRevisaoEnum.INSERT);
                    String dsRemovidos = diasSemanaLog(ctx, rs.getLong("segundo"), username, TipoRevisaoEnum.DELETE);
                    if(!dsAdicionados.isEmpty()){
                        valoresAlterados.put("Dias semana adicionados", dsAdicionados);
                    }
                    if(!dsRemovidos.isEmpty()){
                        valoresAlterados.put("Dias semana removidos", dsRemovidos);
                    }
                    Map<String, String> valoresAnteriores = null;
                    if(revtype.equals(TipoRevisaoEnum.UPDATE) || revtype.equals(TipoRevisaoEnum.DELETE)){
                        try (ResultSet rsAnterior = programaTreinoDao.createStatement(ctx,
                                "select " +
                                        Uteis.arrayToString(colunas, ",") +
                                        " from programatreino_aud pa " +
                                        " where pa.codigo = " + codigo +
                                        " and pa.rev < " + rsRevs.getLong("rev") +
                                        " order by rev desc limit 1 ")) {
                            if (rsAnterior.next()) {
                                valoresAnteriores = Uteis.montarMapa(rsAnterior, colunas);
                            }
                        }
                    }
                    logTO.getAlteracoes().addAll(Uteis.compararMapas(valoresAnteriores, valoresAlterados));
                }

                    ResultSet rsRevsFichas = descobrirFichaAlteradaNoMomento(ctx, rs.getLong("segundo"), username, codigo);
                    while (rsRevsFichas != null && rsRevsFichas.next()) {
                        logTO.setIdentificador(logTO.getIdentificador());
                        TipoRevisaoEnum revtypeFicha = TipoRevisaoEnum.getFromId(rsRevsFichas.getInt("revtype"));
                        Map<String, String> valoresAlterados = Uteis.montarMapa(rsRevsFichas, colunasFicha);
                        Map<String, String> valoresAnteriores = null;
                        if (revtypeFicha.equals(TipoRevisaoEnum.INSERT)) {
                            logTO.getAlteracoes().add(new AlteracoesTO("Ficha adicionada: ", "", rsRevsFichas.getString("nome")));
                        } else {
                            logTO.getAlteracoes().add(new AlteracoesTO("Ficha alterada: ", "", rsRevsFichas.getString("nome")));
                            try (ResultSet rsAnterior = programaTreinoDao.createStatement(ctx,
                                    "select rev, codigo, " +
                                            Uteis.arrayToString(colunasFicha, ",") +
                                            " from ficha_aud pa " +
                                            " where pa.codigo = " + rsRevsFichas.getInt("codigo") +
                                            " and pa.rev < " + rsRevsFichas.getLong("rev") +
                                            " order by rev desc limit 1 ")) {
                                if (rsAnterior.next()) {
                                    valoresAnteriores = Uteis.montarMapa(rsAnterior, colunasFicha);
                                }
                            }
                        }
                        List<AlteracoesTO> alteracoesProgramaTreinoFicha = logProgramaTreinoFichaRelacionado(ctx,
                                rs.getLong("segundo"), username, rsRevsFichas.getInt("codigo"));
                        logTO.getAlteracoes().addAll(alteracoesProgramaTreinoFicha);
                        logTO.getAlteracoes().addAll(Uteis.compararMapas(valoresAnteriores, valoresAlterados));
                    }
                    List<AlteracoesTO> alteracoesAtividades = logAtividadeFichaRelacionada(ctx, rs.getLong("segundo"), username);
                    logTO.getAlteracoes().addAll(alteracoesAtividades);
                    List<AlteracoesTO> alteracoesSeries = logSeriesAtividadeRelacionada(ctx, rs.getLong("segundo"), username);
                    logTO.getAlteracoes().addAll(alteracoesSeries);

                    int limit = 0;
                    tratarNomes(ctx, logTO.getAlteracoes());
                    for (AlteracoesTO a : logTO.getAlteracoes()) {
                        logTO.setDescricao(logTO.getDescricao() + ("[" + a.getCampo() + ":'"
                                + (UteisValidacao.emptyString(a.getValorAnterior()) ? "" : (a.getValorAnterior() + "' para '"))
                                + a.getValorAlterado() + "']<br/>"));
                        if (limit > 5) {
                            logTO.setDescricao(logTO.getDescricao() + "<br/>...");
                            break;
                        }
                        limit++;
                    }
                    listarLog.add(logTO);
                }
            }

            try (ResultSet rsCount = programaTreinoDao.createStatement(ctx,
                    " select count(segundo) as cont from (" +
                            sqlprincipal +
                            ") as t")) {
                if (rsCount.next()) {
                    paginadorDTO.setQuantidadeTotalElementos(rsCount.getLong("cont"));
                } else {
                    paginadorDTO.setQuantidadeTotalElementos(10l);
                }
            }

        }catch (Exception e){
            throw new ServiceException(e);
        }

        return listarLog;
    }
}
