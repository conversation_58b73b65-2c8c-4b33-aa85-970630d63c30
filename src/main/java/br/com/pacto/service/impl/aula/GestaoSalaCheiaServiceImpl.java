/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.impl.aula;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.aula.*;
import br.com.pacto.bean.configuracoes.ConfiguracaoSistema;
import br.com.pacto.bean.configuracoes.ConfiguracoesEnum;
import br.com.pacto.bean.professor.ProfessorSintetico;

import br.com.pacto.controller.json.colaborador.ProfessorSubstituidoDTO;
import br.com.pacto.controller.json.professor.FiltroGestaoJSON;
import br.com.pacto.dao.intf.aula.*;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.notificacao.excecao.ProfessorSubstituidoExcecoes;
import br.com.pacto.service.intf.agendatotal.AgendaTotalService;
import br.com.pacto.service.intf.aula.AulaService;
import br.com.pacto.service.intf.aula.GestaoSalaCheiaService;
import br.com.pacto.service.intf.configuracoes.ConfiguracaoSistemaService;
import br.com.pacto.service.intf.empresa.EmpresaService;
import br.com.pacto.service.intf.professor.ProfessorSinteticoService;
import br.com.pacto.service.intf.usuario.UsuarioService;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;
import br.com.pacto.util.ViewUtils;
import br.com.pacto.util.bean.AgendaTotalTO;
import br.com.pacto.util.enumeradores.DiasSemana;
import br.com.pacto.util.impl.Ordenacao;
import br.com.pacto.util.json.AgendaTotalJSON;
import org.json.JSONArray;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.test.context.ContextConfiguration;
import servicos.integracao.zw.IntegracaoTurmasWSConsumer;

import javax.faces.model.SelectItem;
import java.util.*;

import static br.com.pacto.controller.json.base.SuperControle.independente;

/**
 *
 * <AUTHOR>
 */
@Service
@ContextConfiguration(locations = {"/applicationContext.xml"})
public class GestaoSalaCheiaServiceImpl implements GestaoSalaCheiaService {

    @Autowired
    private AulaDiaExcecaoDao aulaDiaExcecaoDao;
    @Autowired
    private AulaAlunoDao aulaAlunoDao;
    @Autowired
    private AulaService aulaService;
    @Autowired
    private ConfiguracaoSistemaService configuracaoSistemaService;
    @Autowired
    private ModalidadeDao modalidadeDao;
    @Autowired
    private SessaoService sessaoService;
    @Autowired
    private AgendaTotalService agendaTotalService;
    @Autowired
    private UsuarioService usuarioService;
    @Autowired
    private EmpresaService empresaService;
    @Autowired
    private ProfessorSinteticoService professorSinteticoService;
    @Autowired
    private ProfessorSubstituidoDao professorSubstituidoDao;
    @Autowired
    private AulaHorarioDao aulaHorarioDao;

    public ConfiguracaoSistemaService getConfiguracaoSistemaService() {
        return configuracaoSistemaService;
    }

    public AulaService getAulaService() {
        return aulaService;
    }

    public AulaDiaExcecaoDao getAulaDiaExcecaoDao() {
        return aulaDiaExcecaoDao;
    }

    public AulaAlunoDao getAulaAlunoDao() {
        return aulaAlunoDao;
    }

    public List<AulaAluno> obterAlunosPorPeriodo(String ctx, Date inicio, Date fim, Boolean experimentais) throws Exception {
        StringBuilder query = new StringBuilder();
        query.append("SELECT obj FROM AulaAluno obj where obj.dia between :inicio AND :fim ");
        if (experimentais) {
            query.append("\n AND obj.aulaExperimental IS TRUE");
        }
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("inicio", inicio);
        params.put("fim", fim);
        return getAulaAlunoDao().findByParam(ctx, query.toString(), params);
    }

    @Override
    public List<GestaoSalaCheiaTO> montarBIDesempenho(String ctx, List<AulaDia> aulas) throws ServiceException {
        ConfiguracaoSistema cfgVermelhoBaixa = configuracaoSistemaService.consultarPorTipo(ctx, ConfiguracoesEnum.VALOR_VERMELHO_BAIXA_FREQUENCIA);
        ConfiguracaoSistema cfgVermelhoAlta = configuracaoSistemaService.consultarPorTipo(ctx, ConfiguracoesEnum.VALOR_VERMELHO_ALTA_FREQUENCIA);

        GestaoSalaCheiaTO verdes = new GestaoSalaCheiaTO(0, IndicadorGestaoSalaCheiaEnum.AULAS_VERDES);
        GestaoSalaCheiaTO vermelhas = new GestaoSalaCheiaTO(0, IndicadorGestaoSalaCheiaEnum.AULAS_VERMELHAS);
        GestaoSalaCheiaTO amarelas = new GestaoSalaCheiaTO(0, IndicadorGestaoSalaCheiaEnum.AULAS_AMARELAS);

        for (AulaDia aula : aulas) {
            Integer ocupacao = aula.getNrVagasOcupadas();
            Double percentualOcupacao = ocupacao * 100 / aula.getAula().getCapacidade().doubleValue();
            //verificar se se enquadra em aula verde
            if (percentualOcupacao >= aula.getAula().getMeta()) {
                verdes.setValor(verdes.getValor() + 1);
                verdes.getAulas().add(aula);
            } else
            //verificar se se enquadra em aula vermelha
            if ((aula.getAula().getFrequencia().equals(FrequenciaEnum.ALTA_FREQUENCIA) && percentualOcupacao < cfgVermelhoAlta.getValorAsInteger())
                    || (aula.getAula().getFrequencia().equals(FrequenciaEnum.BAIXA_FREQUENCIA) && percentualOcupacao < cfgVermelhoBaixa.getValorAsInteger())) {

                vermelhas.setValor(vermelhas.getValor() + 1);
                vermelhas.getAulas().add(aula);
            } else
            //verificar se se enquadra em aula vermelha
            if (((aula.getAula().getFrequencia().equals(FrequenciaEnum.ALTA_FREQUENCIA) && percentualOcupacao >= cfgVermelhoAlta.getValorAsInteger())
                    || (aula.getAula().getFrequencia().equals(FrequenciaEnum.BAIXA_FREQUENCIA) && percentualOcupacao >= cfgVermelhoBaixa.getValorAsInteger()))
                    && percentualOcupacao < aula.getAula().getMeta()) {

                amarelas.setValor(amarelas.getValor() + 1);
                amarelas.getAulas().add(aula);
            }
        }
        List<GestaoSalaCheiaTO> lista = new ArrayList<GestaoSalaCheiaTO>();
        lista.add(verdes);
        lista.add(amarelas);
        lista.add(vermelhas);
        return lista;
    }

    @Override
    public List<GestaoSalaCheiaTO> montarBIRanking(String ctx, List<AulaDia> verdes, List<AulaDia> vermelhas) throws ServiceException {
        List<GestaoSalaCheiaTO> lista = new ArrayList<GestaoSalaCheiaTO>();
        GestaoSalaCheiaTO rankingVerdes = new GestaoSalaCheiaTO(0, IndicadorGestaoSalaCheiaEnum.RANKING_PROFESSORES_VERDE);
        for (AulaDia verde : verdes) {
            Integer somar = rankingVerdes.getMapaProfessores().get(verde.getProfessor());
            somar = somar == null ? 0 : somar;
            rankingVerdes.getMapaProfessores().put(verde.getProfessor(), somar + 1);
        }
        lista.add(rankingVerdes);
        GestaoSalaCheiaTO rankingVermelhos = new GestaoSalaCheiaTO(0, IndicadorGestaoSalaCheiaEnum.RANKING_PROFESSORES_VERMELHO);
        for (AulaDia vermelho : vermelhas) {
            Integer somar = rankingVermelhos.getMapaProfessores().get(vermelho.getProfessor());
            somar = somar == null ? 0 : somar;
            rankingVermelhos.getMapaProfessores().put(vermelho.getProfessor(), somar + 1);
        }
        lista.add(rankingVermelhos);
        return lista;
    }

    @Override
    public GestaoSalaCheiaTO montarBIOcupacao(String ctx, List<AgendaTotalTO> aulas, TipoAulaCheiaOrigemEnum tipo, FiltrosGestaoSalaCheia filtro) throws ServiceException, Exception {
        GestaoSalaCheiaTO ocupacao = new GestaoSalaCheiaTO(0, IndicadorGestaoSalaCheiaEnum.OCUPACAO);
        
        for (AgendaTotalTO aula : aulas) {
            if(!filtrarAula(ctx, aula, filtro)){
              continue;  
            }
            if((tipo.equals(TipoAulaCheiaOrigemEnum.ZW) && aula.getAulaColetiva())
                    ||(tipo.equals(TipoAulaCheiaOrigemEnum.AC) && !aula.getAulaColetiva())){
                continue;
            }
            Integer horaAula = Integer.valueOf(Uteis.getDataAplicandoFormatacao(aula.getStartDate(), "HH"));
            Map<String, BubbleChartTO> mapaAula = ocupacao.getMapaBubbleChart().get(horaAula);
            if (mapaAula == null) {
                mapaAula = new HashMap<String, BubbleChartTO>();
                ocupacao.getMapaBubbleChart().put(horaAula, mapaAula);
            }
            BubbleChartTO bubble = mapaAula.get(aula.getTitulo());
            if (bubble == null) {
                bubble = new BubbleChartTO();
                bubble.setModalidade(aula.getCodigotipo());
                bubble.setCapacidade(aula.getNrVagas());
                mapaAula.put(aula.getTitulo(), bubble);
            }
            bubble.setCapacidadeTotal(bubble.getCapacidadeTotal() + aula.getNrVagas());
            bubble.setOcupacao(bubble.getOcupacao() + aula.getNrVagasPreenchidas());
            if( bubble.getCapacidadeTotal() <= 0){
                bubble.setPercentualOcupacao(0);
            } else {
                bubble.setPercentualOcupacao((bubble.getOcupacao() * 100) / bubble.getCapacidadeTotal());
            }
        }
        return ocupacao;
    }

    @Override
    public List<AulaDia> filtrarAulas(final String key, List<AulaDia> aulas,
            FiltrosGestaoSalaCheia filtro, boolean substituidos) throws Exception {
        List<AulaDia> listaFiltrada = new ArrayList<AulaDia>();
        for (AulaDia aula : aulas) {
            if (!Calendario.entre(aula.getInicio(), filtro.getInicio(), Uteis.getDateTime(filtro.getFim(), 23, 59, 59))) {
                continue;
            }
            Integer inicioHora = Integer.valueOf(Uteis.getDataAplicandoFormatacao(aula.getInicio(), "HH:mm").replaceAll(":", ""));
            Integer fimHora = Integer.valueOf(Uteis.getDataAplicandoFormatacao(aula.getFim(), "HH:mm").replaceAll(":", ""));
            boolean estaEntreUmPeriodo = filtro.getHorariosSelecionadas().isEmpty();
            for (String periodo : filtro.getHorariosSelecionadas()) {
                Integer inicioperiodo = Integer.valueOf(periodo.substring(0, 4).replaceAll(":", ""));
                Integer fimperiodo = Integer.valueOf(periodo.substring(8, 12).replaceAll(":", ""));
                if (inicioHora >= inicioperiodo && fimHora <= fimperiodo) {
                    estaEntreUmPeriodo = true;
                }
            }
            if (!estaEntreUmPeriodo) {
                continue;
            }
            if ((!filtro.getProfessoresSelecionados().isEmpty()
                    && !filtro.getProfessoresSelecionados().contains(aula.getProfessor().getCodigo().toString()))
                    && (substituidos && !filtro.getProfessoresSelecionados().isEmpty()
                    && !filtro.getProfessoresSelecionados().contains(aula.getProfessorSubstituido().getCodigo().toString())
                    || !substituidos)) {
                continue;

            }
            if (!filtro.getAmbientesSelecionados().isEmpty()
                    && !filtro.getAmbientesSelecionados().contains(aula.getAula().getAmbiente().getCodigoZW().toString())) {
                continue;
            }
            if (!filtro.getModalidadesSelecionadas().isEmpty()
                    && !filtro.getModalidadesSelecionadas().contains(aula.getAula().getModalidade().getCodigoZW().toString())) {
                continue;
            }
            Integer diaDaSemanaNumero = Uteis.getDiaDaSemanaNumero(aula.getInicio());
            if (!filtro.getDiasSemanaSelecionados().isEmpty()
                    && !filtro.getDiasSemanaSelecionados().contains(diaDaSemanaNumero.toString())) {
                continue;
            }
            listaFiltrada.add(aula);
        }
        return listaFiltrada;
    }
    
    
    public boolean filtrarAula(final String key, AgendaTotalTO aula,
            FiltrosGestaoSalaCheia filtro) throws Exception {
        
            if (!Calendario.entre(aula.getStartDate(), filtro.getInicio(), Uteis.getDateTime(filtro.getFim(), 23, 59, 59))) {
                return false;
            }
            Integer inicioHora = Integer.valueOf(Uteis.getDataAplicandoFormatacao(aula.getStartDate(), "HH:mm").replaceAll(":", ""));
            Integer fimHora = Integer.valueOf(Uteis.getDataAplicandoFormatacao(aula.getEndDate(), "HH:mm").replaceAll(":", ""));
            boolean estaEntreUmPeriodo = filtro.getHorariosSelecionadas().isEmpty();
            for (String periodo : filtro.getHorariosSelecionadas()) {
                Integer inicioperiodo = Integer.valueOf(periodo.substring(0, 4).replaceAll(":", ""));
                Integer fimperiodo = Integer.valueOf(periodo.substring(8, 12).replaceAll(":", ""));
                if (inicioHora >= inicioperiodo && fimHora <= fimperiodo) {
                    estaEntreUmPeriodo = true;
                }
            }
            if (!estaEntreUmPeriodo) {
                return false;
            }
            if ((!filtro.getProfessoresSelecionados().isEmpty()
                    && !filtro.getProfessoresSelecionados().contains(aula.getCodigoResponsavel().toString()))) {
                return false;

            }
            if (!filtro.getAmbientesSelecionados().isEmpty()
                    && !filtro.getAmbientesSelecionados().contains(aula.getCodigoLocal().toString())) {
                return false;
            }
            if (!filtro.getModalidadesSelecionadas().isEmpty()
                    && !filtro.getModalidadesSelecionadas().contains(aula.getCodigotipo().toString())) {
                return false;
            }
            Integer diaDaSemanaNumero = Uteis.getDiaDaSemanaNumero(aula.getStartDate());
            if (!filtro.getDiasSemanaSelecionados().isEmpty()
                    && !filtro.getDiasSemanaSelecionados().contains(diaDaSemanaNumero.toString())) {
                return false;
            }
        
            return true;
    }

    @Override
    public List<GestaoSalaCheiaAnaliticoTO> montarExcecaoAnalitico(String key,
            GestaoSalaCheiaTO pedidosNegados, GestaoSalaCheiaTO experimental) throws ServiceException {
        List<Modalidade> todasModalidades = getAulaService().todasModalidades(key);
        Map<Integer, Modalidade> mapaMod = new HashMap<Integer, Modalidade>();
        for (Modalidade mod : todasModalidades) {
            mapaMod.put(mod.getCodigo(), mod);
        }
        List<GestaoSalaCheiaAnaliticoTO> lista = new ArrayList<GestaoSalaCheiaAnaliticoTO>();
        for (AulaAluno aluno : experimental.getAlunos()) {
            lista.add(new GestaoSalaCheiaAnaliticoTO(aluno.getAula(), aluno, null, aluno.getCliente(), mapaMod.get(aluno.getAula().getAula().getModalidade().getCodigo()), null, null, null));
        }

        for (AulaDiaExcecao excecao : pedidosNegados.getExcecoes()) {
            lista.add(new GestaoSalaCheiaAnaliticoTO(excecao.getAula(), null, excecao, excecao.getCliente(), mapaMod.get(excecao.getAula().getAula().getModalidade().getCodigo()), null, null, null));
        }
        Ordenacao.ordenarLista(lista, "dataAula");
        return lista;
    }

    @Override
    public List<GestaoSalaCheiaAnaliticoTO> montarDesempenhoAnalitico(String key, List<AulaDia> verdes, List<AulaDia> vermelhas, List<AulaDia> amarelas) throws ServiceException {
        List<GestaoSalaCheiaAnaliticoTO> lista = new ArrayList<GestaoSalaCheiaAnaliticoTO>();
        for (AulaDia aula : verdes) {
            lista.add(new GestaoSalaCheiaAnaliticoTO(aula, null, null, null, aula.getAula().getModalidade(), true, false, aula.getAula().getBonificacao().doubleValue()));
        }
        for (AulaDia aula : vermelhas) {
            lista.add(new GestaoSalaCheiaAnaliticoTO(aula, null, null, null, aula.getAula().getModalidade(), false, true, aula.getAula().getBonificacao().doubleValue()));
        }
        for (AulaDia aula : amarelas) {
            lista.add(new GestaoSalaCheiaAnaliticoTO(aula, null, null, null, aula.getAula().getModalidade(), false, false, aula.getAula().getBonificacao().doubleValue()));
        }
        Ordenacao.ordenarLista(lista, "dataAula");
        return lista;
    }

    @Override
    public List<GestaoSalaCheiaAnaliticoTO> montarOcupacaoAnalitico(String key, GestaoSalaCheiaTO ocupacao, Boolean mostrarZerados) throws Exception {
        List<GestaoSalaCheiaAnaliticoTO> lista = new ArrayList<GestaoSalaCheiaAnaliticoTO>();
        for (Integer hora : ocupacao.getMapaBubbleChart().keySet()) {
            for (String aula : ocupacao.getMapaBubbleChart().get(hora).keySet()) {
                BubbleChartTO bb = ocupacao.getMapaBubbleChart().get(hora).get(aula);
                if (mostrarZerados || bb.getPercentualOcupacao() > 0) {
                    lista.add(new GestaoSalaCheiaAnaliticoTO(aula,
                            modalidadeDao.findObjectByAttribute(key, "codigoZW",bb.getModalidade()), 
                            bb.getCapacidade(), bb.getPercentualOcupacao(), hora));
                }
            }
        }
        Ordenacao.ordenarLista(lista, "hora");
        return lista;
    }

    @Override
    public List<GestaoSalaCheiaAnaliticoTO> montarBiSubstituidos(String key, FiltrosGestaoSalaCheia filtro, List<AgendaTotalTO> agendamentos) throws Exception {
        List<GestaoSalaCheiaAnaliticoTO> lista = new ArrayList<GestaoSalaCheiaAnaliticoTO>();
        List<AulaDia> aulas = new ArrayList<>();
        Integer professorSelecionado = 0;
        if(!UteisValidacao.emptyList(filtro.getProfessoresSelecionados())){
            professorSelecionado = Integer.parseInt(filtro.getProfessoresSelecionados().get(0));
        }
        montarLista(key, aulas, filtro, agendamentos, lista, professorSelecionado);

        return lista;
    }

    @Override
    public List<ProfessorSubstituidoDTO> montarBiSubstituidosSpa(FiltroGestaoJSON filtroGestao, PaginadorDTO paginadorDTO) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            List<ProfessorSubstituidoDTO> professoresSubstituidoDTO = new ArrayList<>();

            List<ProfessorSubstituido> professorSubstituidos = professorSubstituidoDao.filtrarProfessoresSubstituidos(ctx, filtroGestao, paginadorDTO);
            for (ProfessorSubstituido professorSubstituido : professorSubstituidos) {
                ProfessorSubstituidoDTO professorSubstituidoDTO = new ProfessorSubstituidoDTO();

                List<Integer> aulaHorarioSelecionado = new ArrayList<>();
                aulaHorarioSelecionado.add(professorSubstituido.getCodigoHorarioTurma());

                professorSubstituidoDTO.setData(professorSubstituido.getDataSubstituicao());
                professorSubstituidoDTO.setJustificativa(professorSubstituido.getJustificativa());
                professorSubstituidoDTO.setHorario(Calendario.getHora(professorSubstituido.getDataSubstituicao(), "HH:mm"));
                if (independente(ctx)) {
                    AulaHorario aulaHorario = aulaHorarioDao.findById(ctx, professorSubstituido.getCodigoHorarioTurma());
                    if (aulaHorario != null) {
                        professorSubstituidoDTO.setAula(aulaHorario.getAula().getNome());
                    } else {
                        professorSubstituidoDao.removerPorAulaHorario(ctx, aulaHorarioSelecionado);
                        continue;
                    }
                    ProfessorSintetico professorOriginal = professorSinteticoService.obterPorId(ctx, professorSubstituido.getCodigoProfessorOriginal());
                    if (professorOriginal != null) {
                        professorSubstituidoDTO.setSubstituido(professorOriginal.getNome());
                    }
                    ProfessorSintetico professorSubstituto = professorSinteticoService.obterPorId(ctx, professorSubstituido.getCodigoProfessorSubstituto());
                    if (professorSubstituto != null) {
                        professorSubstituidoDTO.setSubstituto(professorSubstituto.getNome());
                    }
                } else {
                    IntegracaoTurmasWSConsumer integracaoWS = UtilContext.getBean(IntegracaoTurmasWSConsumer.class);
                    AgendaTotalJSON aulaCheia =
                            integracaoWS.consultarUmaAula(ctx, Calendario.getData(professorSubstituido.getDiaAula(), "dd/MM/yyyy"), professorSubstituido.getCodigoHorarioTurma());
                    if (aulaCheia != null && StringUtils.isNotBlank(aulaCheia.getId())) {
                        professorSubstituidoDTO.setAula(aulaCheia.getTitulo());
                    } else {
                        professorSubstituidoDao.removerPorAulaHorario(ctx, aulaHorarioSelecionado);
                        continue;
                    }
                    ProfessorSintetico professorOriginal = professorSinteticoService.consultarPorCodigoColaborador(ctx, professorSubstituido.getCodigoProfessorOriginal());
                    if (professorOriginal != null) {
                        professorSubstituidoDTO.setSubstituido(professorOriginal.getNome());
                    }
                    ProfessorSintetico professorSubstituto = professorSinteticoService.consultarPorCodigoColaborador(ctx, professorSubstituido.getCodigoProfessorSubstituto());
                    if (professorSubstituto != null) {
                        professorSubstituidoDTO.setSubstituto(professorSubstituto.getNome());
                    }
                }
                professoresSubstituidoDTO.add(professorSubstituidoDTO);
            }
            return professoresSubstituidoDTO;
        } catch (Exception ex) {
            throw new ServiceException(ProfessorSubstituidoExcecoes.ERRO_CONSULTAR_PROFESSORES_SUBSTITUIDOS, ex);
        }
    }

    public void montarLista(String ctx, List<AulaDia> aulas, FiltrosGestaoSalaCheia filtroGeral, List<AgendaTotalTO> listaAgendaTotal, List<GestaoSalaCheiaAnaliticoTO> lista, Integer professorSelecionado) throws Exception{

        aulas = filtrarAulas(ctx, getAulaService().obterAulasDia(ctx, filtroGeral.getInicio(), filtroGeral.getFim(), true, true), filtroGeral, true);
        for (AulaDia aula : aulas) {
            lista.add(new GestaoSalaCheiaAnaliticoTO(aula));
        }
        for(AgendaTotalTO ag : listaAgendaTotal){
                if (ag.getSubstituido() != null ) {
                    if (professorSelecionado != 0 && professorSelecionado.equals(ag.getSubstituido().getCodigoProfessorOriginal())) {
                        lista.add(new GestaoSalaCheiaAnaliticoTO(new AulaDia(ag)));
                    }else if(professorSelecionado == 0){
                        lista.add(new GestaoSalaCheiaAnaliticoTO(new AulaDia(ag)));
                    }
            }
        }
        Ordenacao.ordenarLista(lista, "data");

    }


    public void montarFiltros(String ctx, List<Modalidade> todasModalidades, List<Ambiente> todosAmbientes, FiltrosGestaoSalaCheia filtroGeral)throws Exception{

        for (Modalidade mod : todasModalidades) {
            filtroGeral.getItensModalidades().add(new SelectItem(mod.getCodigoZW(), mod.getNome()));
        }
        for (Ambiente amb : todosAmbientes) {
            filtroGeral.getItensAmbientes().add(new SelectItem(amb.getCodigoZW(), amb.getNome()));
        }
        for (DiasSemana dia : DiasSemana.values()) {
            ViewUtils bean = UtilContext.getBean(ViewUtils.class);
            filtroGeral.getItensDiasSemana().add(new SelectItem(dia.getNumeral(), bean.getLabel(dia.getChave())));
        }
        if(!independente(ctx)) {
            IntegracaoTurmasWSConsumer integracaoWS = UtilContext.getBean(IntegracaoTurmasWSConsumer.class);
            JSONArray todosHorarios = new JSONArray(integracaoWS.consultarTodasHoraInicial(ctx));
            for (int i = 0; i < todosHorarios.length(); i++) {
                String horaInicio = todosHorarios.getString(i);
                filtroGeral.getItensHorarios().add(new SelectItem(horaInicio, horaInicio));
            }
        }
    }

    //############################# DISPONIBILIDADE OCUPACAO E EXCECAO######################################
    @Override
    public List<GestaoSalaCheiaTO> montarBIExcecao(String ctx, Date inicio, Date fim, FiltrosGestaoSalaCheia filtro, Integer codEmpresaZW) throws ServiceException {
        try {
            List<AulaDiaExcecao> excecoes = obterExcecoesPorPeriodo(ctx, inicio, fim, codEmpresaZW);
            List<AulaAluno> alunosExperimentais = obterAlunosPorPeriodo(ctx, inicio, fim, Boolean.TRUE);
            //pedidos negados
            GestaoSalaCheiaTO pedidosNegados = new GestaoSalaCheiaTO(0, IndicadorGestaoSalaCheiaEnum.PEDIDOS_NEGADOS);
            pedidosNegados.setExcecoes(new ArrayList<AulaDiaExcecao>());
            for (AulaDiaExcecao exc : excecoes) {
                if (processarGestaoBIExcecao(exc.getAula(), filtro)) {
                    pedidosNegados.getExcecoes().add(exc);
                }
            }
            //experimentais
            GestaoSalaCheiaTO experimentais = new GestaoSalaCheiaTO(0, IndicadorGestaoSalaCheiaEnum.AULA_EXPERIMENTAL);
            experimentais.setAlunos(new ArrayList<AulaAluno>());
            for (AulaAluno exp : alunosExperimentais) {
                if (processarGestaoBIExcecao(exp.getAula(), filtro)) {
                    experimentais.getAlunos().add(exp);
                }
            }
            return Arrays.asList(new GestaoSalaCheiaTO[]{experimentais, pedidosNegados});
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    private boolean processarGestaoBIExcecao(AulaDia aula, FiltrosGestaoSalaCheia filtro) {
        if (!filtro.getAmbientesSelecionados().isEmpty()
                && !filtro.getAmbientesSelecionados().contains(aula.getAula().getAmbiente().getCodigoZW().toString())) {
            return false;
        }
        if (!filtro.getModalidadesSelecionadas().isEmpty()
                && !filtro.getModalidadesSelecionadas().contains(aula.getAula().getModalidade().getCodigoZW().toString())) {
            return false;
        }
        if ((!filtro.getProfessoresSelecionados().isEmpty()
                && !filtro.getProfessoresSelecionados().contains(aula.getProfessor().getCodigo().toString()))) {
            return false;
        }
        return true;
    }

    public List<AulaDiaExcecao> obterExcecoesPorPeriodo(String ctx, Date inicio, Date fim, Integer codEmpresaZW) throws Exception {
        List<AulaDiaExcecao> list = new ArrayList<>();
        StringBuilder query = new StringBuilder();
        if (independente(ctx)) {
            query.append("SELECT obj FROM AulaDiaExcecao obj WHERE obj.aula.inicio >= :inicio AND obj.aula.inicio <= :fim ");
            Map<String, Object> params = new HashMap<>();
            params.put("inicio", inicio);
            params.put("fim", fim);
            list = getAulaDiaExcecaoDao().findByParam(ctx, query.toString(), params);
        } else {
            IntegracaoTurmasWSConsumer integracaoWS = UtilContext.getBean(IntegracaoTurmasWSConsumer.class);
            List<AgendaTotalTO> agendamentosZW = integracaoWS.consultarAgendamentos(ctx, inicio, fim, codEmpresaZW, null);
            List<AulaDiaExcecao> excecoes = getAulaDiaExcecaoDao().findAll(ctx);
            for (AulaDiaExcecao exe : excecoes) {
                if (!UteisValidacao.emptyString(exe.getIdHorarioTurmaReference())) {
                    for (AgendaTotalTO agendamento : agendamentosZW) {
                        if (exe.getIdHorarioTurmaReference().equals(agendamento.getIdentificador())) {
                            exe.setAula(new AulaDia(agendamento));
                            list.add(exe);
                        }
                    }
                }
            }
        }
        return list;
    }

    @Override
    public List<GestaoDisponibilidadeJSON> montarBIExcecaoPorPeriodo(
            GestaoSalaCheiaTO pedidosNegados,
            GestaoSalaCheiaTO experimental,
            List<AulaDia> aulas,
            FiltrosGestaoSalaCheia filtro) throws ServiceException {
        GestaoDisponibilidadeJSON cedo = new GestaoDisponibilidadeJSON("Cedo");
        GestaoDisponibilidadeJSON tarde = new GestaoDisponibilidadeJSON("Tarde");
        GestaoDisponibilidadeJSON noite = new GestaoDisponibilidadeJSON("Noite");
        // VAGAS E SOBRAS
        for (AulaDia aula : aulas) {
            if (!processarGestaoBIExcecao(aula, filtro)) {
                continue;
            }
            try {
                Integer horarioInicio = Integer.valueOf(Uteis.gethoraHHMMAjustado(aula.getInicio()).replaceAll(":", ""));
                if (horarioInicio <= 1259) {
                    cedo.setConfirmados(cedo.getConfirmados() + aula.getConfirmados());
                    cedo.setOcupadas(cedo.getOcupadas() + aula.getNrVagasOcupadas());
                    cedo.setSobraram(cedo.getSobraram() + (aula.getAula().getCapacidade() - aula.getNrVagasOcupadas()));
                } else if (horarioInicio > 1259 && horarioInicio <= 1859) {
                    tarde.setConfirmados(tarde.getConfirmados() + aula.getConfirmados());
                    tarde.setOcupadas(tarde.getOcupadas() + aula.getNrVagasOcupadas());
                    tarde.setSobraram(tarde.getSobraram() + (aula.getAula().getCapacidade() - aula.getNrVagasOcupadas()));
                } else if (horarioInicio <= 2359) {
                    noite.setConfirmados(noite.getConfirmados() + aula.getConfirmados());
                    noite.setOcupadas(noite.getOcupadas() + aula.getNrVagasOcupadas());
                    noite.setSobraram(noite.getSobraram() + (aula.getAula().getCapacidade() - aula.getNrVagasOcupadas()));
                }
            } catch (Exception e) {
                Uteis.logar(e, GestaoSalaCheiaServiceImpl.class);
            }
        }
        //EXPERIMENTAIS
        for (AulaAluno aluno : experimental.getAlunos()) {
            if (!processarGestaoBIExcecao(aluno.getAula(), filtro)) {
                continue;
            }
            try {
                Integer horarioInicio = Integer.valueOf(Uteis.gethoraHHMMAjustado(aluno.getAula().getInicio()).replaceAll(":", ""));
                if (horarioInicio <= 1259) {
                    cedo.setExperimentais(cedo.getExperimentais() + 1);
                } else if (horarioInicio > 1259 && horarioInicio <= 1859) {
                    tarde.setExperimentais(tarde.getExperimentais() + 1);
                } else if (horarioInicio <= 2359) {
                    noite.setExperimentais(noite.getExperimentais() + 1);
                }
            } catch (Exception e) {
                Uteis.logar(e, GestaoSalaCheiaServiceImpl.class);
            }
        }
        //pedidos negados
        for (AulaDiaExcecao excecao : pedidosNegados.getExcecoes()) {
            if (!processarGestaoBIExcecao(excecao.getAula(), filtro)) {
                continue;
            }
            try {
                Integer horarioInicio = Integer.valueOf(Uteis.gethoraHHMMAjustado(excecao.getAula().getInicio()).replaceAll(":", ""));
                if (horarioInicio <= 1259) {
                    cedo.setPedidos(cedo.getPedidos()+ 1);
                } else if (horarioInicio > 1259 && horarioInicio <= 1859) {
                    tarde.setPedidos(tarde.getPedidos() + 1);
                } else if (horarioInicio <= 2359) {
                    noite.setPedidos(noite.getPedidos() + 1);
                }
            } catch (Exception e) {
                Uteis.logar(e, GestaoSalaCheiaServiceImpl.class);
            }
        }
        return Arrays.asList(new GestaoDisponibilidadeJSON[]{cedo, tarde, noite});
    }
}
