package br.com.pacto.service.impl;

import br.com.pacto.bean.parceiro.Parceiro;
import br.com.pacto.controller.json.parceiros.ParceiroResponseTO;
import br.com.pacto.controller.json.parceiros.ParceiroTO;
import br.com.pacto.dao.intf.parceiro.ParceiroDao;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.notificacao.excecao.ParceiroExcecoes;
import br.com.pacto.service.intf.parceiro.ParceiroService;
import org.apache.commons.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import servicos.integracao.midias.MidiaService;
import servicos.integracao.midias.commons.MidiaEntidadeEnum;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
@Service
public class ParceiroServiceImpl implements ParceiroService {

    @Autowired
    private SessaoService sessaoService;
    @Autowired
    private ParceiroDao parceiroDao;

    public ParceiroDao getParceiroDao() {
        return parceiroDao;
    }

    @Override
    public Parceiro inserir(String ctx, Parceiro object) throws ServiceException {
        try {
            return getParceiroDao().insert(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public Parceiro consultarPorParceiro(String ctx, Integer id) throws ServiceException {
        try {
            return getParceiroDao().findById(ctx, id);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public Parceiro alterar(String ctx, Parceiro object) throws ServiceException {
        try {
            return getParceiroDao().update(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public void removerNotIn(String ctx,List<Parceiro> parceiros) throws ServiceException {
        try {
            if (parceiros.isEmpty()) {
                getParceiroDao().deleteAll(ctx);
            } else {
                getParceiroDao().removerNotIn(ctx, parceiros);
            }
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public List<ParceiroResponseTO> cadastroListaParceiro(List<ParceiroTO> lista) throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        try {
            camposObrigatorios(lista);
//            validarCampos(lista);
            List<Parceiro> parceiros = hidratarParceirosC(lista, new ArrayList<Parceiro>(lista.size()));
            for (Parceiro parceiro :
                    parceiros) {
                inserir(ctx, parceiro);
            }

            List<ParceiroResponseTO> listaR = new ArrayList<>();
            for (Parceiro parceiro: parceiros) {
                listaR.add(new ParceiroResponseTO(parceiro));
            }
            return listaR;
        } catch(ServiceException e){
            throw e;
        } catch (Exception e) {
            throw new ServiceException(ParceiroExcecoes.ERRO_INCLUIR_PARCEIRO, e);
        }
    }

    private String getUrlImage (String dataImage, String tipoImage)throws Exception{
        String key = "";

        String identificador =  Calendario.getData(Calendario.hoje(),"ddMMyyyyhhMMss")+"-Bench-";
        MidiaEntidadeEnum tmidia = MidiaEntidadeEnum.obterPorExtensao(tipoImage);
        String ctx = sessaoService.getUsuarioAtual().getChave();

        key =  MidiaService.getInstanceParceiro().uploadObjectFromByteArray(ctx, tmidia, identificador, Base64.decodeBase64(dataImage.getBytes("UTF-8")));
        return Aplicacao.obterUrlFotoDaNuvem(key);
    }

    @Override
    public List<ParceiroResponseTO> alterarListaParceiro(List<ParceiroTO> lista) throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        try {
            camposObrigatorios(lista);
            List<Parceiro> parceiros = validarIDs(ctx, lista);
//            validarCampos(ambienteTO);
            hidratarParceirosA(lista, parceiros);
            for (Parceiro parceiro :
                    parceiros) {
                alterar(ctx, parceiro);
            }

            List<ParceiroResponseTO> listaR = new ArrayList<>();
            for (Parceiro parceiro: parceiros) {
                listaR.add(new ParceiroResponseTO(parceiro));
            }
            return listaR;
        } catch(ServiceException e){
            throw e;
        } catch (Exception e) {
            throw new ServiceException(ParceiroExcecoes.ERRO_ALTERAR_PARCEIRO, e);
        }
    }

    public void removerParceiros(List<Integer> parceirosIds) throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();

        for (Integer parceiroId : parceirosIds) {
            try {
                Parceiro parceiro = parceiroDao.findById(ctx, parceiroId);
                MidiaService.getInstanceParceiro().deleteObject(parceiro.getUrlImagem());
                parceiroDao.delete(ctx, parceiroId);
            } catch (Exception e) {
                throw new ServiceException(ParceiroExcecoes.ERRO_PARCEIRO_NAO_EXISTE, e);
            }
        }
    }

    @Override
    public List<ParceiroResponseTO> listaParceiro() throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        try {
            List<Parceiro> parceiros = parceiroDao.findAll(ctx);
            List<ParceiroResponseTO> parceiroResponseTOList = new ArrayList<>();
            for (Parceiro parceiro: parceiros) {
                parceiroResponseTOList.add(new ParceiroResponseTO(parceiro));
            }
            return parceiroResponseTOList;
        } catch(ServiceException e){
            throw e;
        } catch (Exception e) {
            throw new ServiceException(ParceiroExcecoes.ERRO_LISTAR_PARCEIROS, e);
        }
    }

    @Override
    public void removerParceirosNotIn(List<ParceiroTO> lista) throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        try {
            camposObrigatorios(lista);
            List<Parceiro> parceiros = validarIDs(ctx, lista);
            removerNotIn(ctx, parceiros);
        } catch(ServiceException e){
            throw e;
        } catch (Exception e) {
            throw new ServiceException(ParceiroExcecoes.ERRO_LISTAR_PARCEIROS, e);
        }
    }

    private List<Parceiro> validarIDs(String ctx, List<ParceiroTO> lista) throws ServiceException {
        List<Parceiro> parceiros = new ArrayList<>();
        for (ParceiroTO parceiroTO: lista) {
            if(parceiroTO.getId() == null || parceiroTO.getId() < 1){
                throw new ServiceException(ParceiroExcecoes.ERRO_ID_NAO_INFORMADA);
            }
            Parceiro parceiro = consultarPorParceiro(ctx, parceiroTO.getId());
            if(parceiro == null) {
                throw new ServiceException(ParceiroExcecoes.ERRO_PARCEIRO_NAO_EXISTE);
            }
            parceiros.add(parceiro);
        }
        return parceiros;
    }

    private List<Parceiro> hidratarParceirosC(List<ParceiroTO> lista, List<Parceiro> parceiros) throws Exception {
        for (int i = 0; i < lista.size(); i++) {
            Parceiro parceiro = new Parceiro();
            ParceiroTO parceiroTO = lista.get(i);
            parceiro.setNome(parceiroTO.getNome());
            parceiro.setSituacao(parceiroTO.getSituacao());
            parceiro.setUrlImagem(getUrlImage(parceiroTO.getDadosImagem(), parceiroTO.getTipoImagem()));
            parceiros.add(parceiro);
        }
        return parceiros;
    }

    private List<Parceiro> hidratarParceirosA(List<ParceiroTO> lista, List<Parceiro> parceiros) {
        for (int i = 0; i < lista.size(); i++) {
            Parceiro parceiro = parceiros.get(i);
            ParceiroTO parceiroTO = lista.get(i);
            parceiro.setNome(parceiroTO.getNome());
            parceiro.setSituacao(parceiroTO.getSituacao());
        }
        return parceiros;
    }

    private void camposObrigatorios(List<ParceiroTO> lista) throws ServiceException {
        for (ParceiroTO parceiroTO: lista) {
            if(parceiroTO.getSituacao() == null) {
                throw new ServiceException(ParceiroExcecoes.ERRO_SITUACAO_NAO_INFORMADA);
            }
        }
    }
}
