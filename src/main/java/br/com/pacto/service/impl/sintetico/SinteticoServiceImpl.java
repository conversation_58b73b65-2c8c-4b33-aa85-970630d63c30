package br.com.pacto.service.impl.sintetico;

import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.perfil.Perfil;
import br.com.pacto.bean.professor.ProfessorSintetico;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.dao.intf.perfil.PerfilDao;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.service.intf.cliente.ClienteSinteticoService;
import br.com.pacto.service.intf.configuracoes.ConfiguracaoSistemaService;
import br.com.pacto.service.intf.professor.ProfessorSinteticoService;
import br.com.pacto.service.intf.sintetico.SinteticoService;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;
import org.springframework.stereotype.Service;
import servicos.integracao.zw.beans.ClienteZW;
import servicos.integracao.zw.beans.UsuarioZW;

import javax.servlet.ServletContext;
import java.util.HashMap;
import java.util.Map;

@Service
public class SinteticoServiceImpl implements SinteticoService {

    @Override
    public String sincronizarAluno(String key, UsuarioZW usuarioZW) throws Exception {
        if (!UteisValidacao.emptyNumber(usuarioZW.getCliente().getCodigoCliente())) {
            ProfessorSintetico professorAtual = null;
            if (usuarioZW.getCliente().getCodigoColaboradorProfessor() != null) {
                ProfessorSinteticoService professorService = (ProfessorSinteticoService) UtilContext.getBean(
                        ProfessorSinteticoService.class);
                professorAtual = professorService.consultarPorCodigoColaborador(key, usuarioZW.getCliente().getCodigoColaboradorProfessor());
            }

            ClienteSinteticoService clienteService = (ClienteSinteticoService) UtilContext.getBean(
                    ClienteSinteticoService.class);
            String s = "select obj from ClienteSintetico obj where codigoCliente = :id";
            Map<String, Object> p = new HashMap<String, Object>();
            p.put("id", usuarioZW.getCliente().getCodigoCliente());
            ClienteSintetico clienteSinteticoAtual = clienteService.obterObjetoPorParam(key, s, p);

            if (clienteSinteticoAtual == null) {
                usuarioZW.getCliente().setCodigo(null);
                clienteService.inserir(key, ClienteZW.toClienteSintetico(usuarioZW.getCliente()), usuarioZW.getFotoKey());
            } else {
                clienteService.refresh(key, clienteSinteticoAtual);
                ClienteSintetico novoClienteSintetico = ClienteZW.toClienteSintetico(usuarioZW.getCliente());
                novoClienteSintetico.setCodigo(clienteSinteticoAtual.getCodigo());
                clienteSinteticoAtual.setProfessorSintetico(professorAtual);
                clienteSinteticoAtual.getPessoa().setFotoKey(Aplicacao.saveImageLocal(UtilContext.getBean(ServletContext.class), key, usuarioZW.getFotoKey(), true, null, false));
                clienteService.alterar(key, novoClienteSintetico, clienteSinteticoAtual);
            }
            ConfiguracaoSistemaService configuracaoService = (ConfiguracaoSistemaService) UtilContext.getBean(
                    ConfiguracaoSistemaService.class);
            configuracaoService.atualizarDataUltimaAtualizacao(key);
            return "ok";
        } else {
            System.out.println("Aluno inválido!");
            return "fail";
        }

    }

    @Override
    public void tratarPerfilUsuario(final String key, UsuarioZW usuarioZW, Usuario usuarioTreino) throws Exception {
        PerfilDao perfilDao = (PerfilDao) UtilContext.getBean(PerfilDao.class);

        if (!UteisValidacao.emptyNumber(usuarioZW.getPerfilTw())) {
            usuarioTreino.setPerfil(perfilDao.findById(key, usuarioZW.getPerfilTw()));
        } else {
            String nomePerfil;
            switch (usuarioZW.getTipo()) {
                case CONSULTOR:
                    nomePerfil = Perfil.NOME_PERFIL_CONSULTOR;
                    break;
                case COORDENADOR:
                    nomePerfil = Perfil.NOME_PERFIL_COORDENADOR;
                    break;
                default:
                    nomePerfil = Perfil.NOME_PERFIL_PROFESSOR;
            }
            usuarioTreino.setPerfil(perfilDao.insertOrGetObjectForName(key, nomePerfil));
        }
    }
}
