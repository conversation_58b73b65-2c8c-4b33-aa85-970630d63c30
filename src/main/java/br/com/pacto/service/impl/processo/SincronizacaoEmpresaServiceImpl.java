package br.com.pacto.service.impl.processo;

import br.com.pacto.base.util.ExecuteRequestHttpService;
import br.com.pacto.bean.processo.Sincronizacao;
import br.com.pacto.dao.intf.processo.SincronizacaoDao;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.service.intf.cliente.ClienteSinteticoService;
import br.com.pacto.service.intf.processo.SincronizacaoEmpresaService;
import org.apache.http.NameValuePair;
import org.apache.http.client.ResponseHandler;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.BasicResponseHandler;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.ResultSet;
import java.util.*;

@Service
public class SincronizacaoEmpresaServiceImpl implements SincronizacaoEmpresaService {

    @Autowired
    private SincronizacaoDao dao;
    @Autowired
    private ClienteSinteticoService clienteSinteticoService;


    @Override
    public String sincronizarAtivosEmpresas(String ctx) {
        try {
            try (ResultSet rs = dao.createStatement(ctx, "select e.nome as empresa, \n" +
                    "e.codzw, \n" +
                    "count(c.codigo) as total\n" +
                    "from clientesintetico c\n" +
                    "inner join empresa e on e.codzw  = c.empresa\n" +
                    "WHERE c.situacao = 'AT' " +
                    "group by e.nome, e.codzw")) {

                while (rs.next()) {
                    JSONObject log = new JSONObject();
                    Sincronizacao sincronizacao = new Sincronizacao();
                    sincronizacao.setInicio(Calendario.hoje());
                    sincronizacao.setEmpresa(rs.getString("empresa"));
                    sincronizacao.setAtivosTR(rs.getInt("total"));
                    JSONObject object = chamadaZW(ctx,
                            "/prest/treino/ativos",
                            rs.getInt("codzw"), null);
                    sincronizacao.setAtivosZW(object.getInt("ativos"));
                    sincronizacao = dao.insert(ctx, sincronizacao);
                    try {
                        if (sincronizacao.getAtivosTR().equals(sincronizacao.getAtivosZW())) {
                            log.put("sucesso", "numero de ativos corresponde");
                        } else {
                            List<Integer> ativosTR = new ArrayList<>();
                            try (ResultSet rsAtivos = dao.createStatement(ctx, "select matricula from clientesintetico c " +
                                    "where situacao = 'AT' and empresa = " + rs.getInt("codzw"))) {
                                while (rsAtivos.next()) {
                                    ativosTR.add(rsAtivos.getInt("matricula"));
                                }
                            }

                            JSONObject objectMatriculas = chamadaZW(ctx,
                                    "/prest/treino/matriculas-ativos",
                                    rs.getInt("codzw"), null);

                            List<Integer> ativosZW = new ArrayList<>();
                            JSONArray matriculasZW = objectMatriculas.optJSONArray("matriculas");
                            for (int i = 0; i < matriculasZW.length(); i++) {
                                ativosZW.add(matriculasZW.getInt(i));
                            }

                            for (Integer matriculaZW : ativosZW) {
                                verificarMatriculaZW(ctx, matriculaZW, ativosTR, rs.getInt("codzw"));
                            }

                            for (Integer matriculaTR : ativosTR) {
                                verificarMatriculaTR(ctx, matriculaTR, ativosZW, rs.getInt("codzw"));
                            }
                        }

                    } catch (Exception e) {
                        log.put("erro-empresa", e.getMessage());
                    }
                    sincronizarAtivosProfessoresEmpresa(ctx, rs.getInt("codzw"), log);
                    sincronizacao.setFim(Calendario.hoje());
                    sincronizacao.setLog(log.toString());
                    sincronizacao = dao.update(ctx, sincronizacao);
                }
            }
            return "ok";
        } catch (Exception e) {
            e.printStackTrace();
            return e.getMessage();
        }
    }

    private void verificarMatriculaZW(String ctx, Integer matriculaZW, List<Integer> ativosTR, Integer empresa) throws Exception {
        if (!ativosTR.contains(matriculaZW)) {
            clienteSinteticoService.addAluno(ctx, matriculaZW, null, false, null, empresa, false);
        }
    }

    private void verificarMatriculaTR(String ctx, Integer matriculaTR, List<Integer> ativosZW, Integer empresa) throws Exception {
        if (!ativosZW.contains(matriculaTR)) {
            dao.executeNativeSQL(ctx, "update clientesintetico set situacao = 'IN', situacaocontrato = 'VE' where matricula = " + matriculaTR);
        }
    }

    private JSONObject chamadaZW(String ctx,
                                 String endpoint,
                                 Integer empresa,
                                 Integer professor) throws Exception {
        final String url = Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebInteg);
        CloseableHttpClient client = ExecuteRequestHttpService.createConnector();
        HttpPost httpPost = new HttpPost(url + endpoint);
        List<NameValuePair> params = new ArrayList<>();
        params.add(new BasicNameValuePair("chave", ctx));
        params.add(new BasicNameValuePair("empresa", empresa.toString()));
        if(professor != null){
            params.add(new BasicNameValuePair("professor", professor.toString()));
        }
        httpPost.setEntity(new UrlEncodedFormEntity(params));
        CloseableHttpResponse response = client.execute(httpPost);
        ResponseHandler<String> handler = new BasicResponseHandler();
        String body = handler.handleResponse(response);
        client.close();
        return new JSONObject(body);
    }

    public List<Sincronizacao> ultimas(String ctx) throws Exception {
        return dao.findListByAttributes(ctx, new String[]{}, new Object[]{}, "codigo desc", 50, 0);
    }


    public void sincronizarAtivosProfessoresEmpresa(String ctx, Integer empresa, JSONObject log) {
        try {

            try (ResultSet rs = dao.createStatement(ctx, "select codigocolaborador, count(c.codigo) as clientes from professorsintetico p\n" +
                    " inner join clientesintetico c on c.professorsintetico_codigo = p.codigo \n" +
                    " where c.empresa = " + empresa +
                    " and situacao = 'AT'\n" +
                    " group by codigocolaborador \n" +
                    " order by codigocolaborador ")) {

                JSONObject object = chamadaZW(ctx,
                        "/prest/treino/ativos-professor",
                        empresa, null);
                try {
                    Map<Integer, Integer> mapaProfessorAtivos = new HashMap<>();
                    while (rs.next()) {
                        mapaProfessorAtivos.put(rs.getInt("codigocolaborador"), rs.getInt("clientes"));
                    }
                    Set<String> codigosColaboradores = object.keySet();
                    for (String codigoColaborador : codigosColaboradores) {
                        try {
                            Integer colaborador = Integer.valueOf(codigoColaborador);
                            int ativosZW = object.getInt(codigoColaborador);
                            int ativos = mapaProfessorAtivos.get(colaborador);

                            if (ativos != ativosZW) {
                                log.put("professor".concat(codigoColaborador), "não bate");

                                JSONObject objectMatriculas = chamadaZW(ctx,
                                        "/prest/treino/matriculas-ativos-professor",
                                        empresa, colaborador);

                                String ativosZWStr = "";
                                JSONArray matriculasZW = objectMatriculas.optJSONArray("matriculas");
                                for (int i = 0; i < matriculasZW.length(); i++) {
                                    ativosZWStr += "," + matriculasZW.getInt(i);
                                }

                                try (ResultSet rsProfessor = dao.createStatement(ctx, "select codigo from professorsintetico where codigocolaborador = " + colaborador)) {
                                    if (rsProfessor.next()) {
                                        String update = "update clientesintetico set professorSintetico_codigo = " + rsProfessor.getInt("codigo") + " where matricula in ("
                                                + ativosZWStr.replaceFirst(",", "") + ")";
                                        dao.executeNative(ctx, update);
                                    }
                                }
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                } catch (Exception e) {
                    log.put("erro-professor", e.getMessage());
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    @Override
    public String sincronizarPorProblemaZW(String ctx) {
        try {
            try (ResultSet rs = dao.createStatement(ctx, "select e.codzw \n" +
                    "from empresa e")) {

                while (rs.next()) {
                    Integer empresa = rs.getInt("codzw");
                    JSONObject objectMatriculas = chamadaZW(ctx,
                            "/prest/treino/matriculas-nao-importadas",
                            empresa, null);
                    List<Integer> alunozw = new ArrayList<>();
                    JSONArray matriculasZW = objectMatriculas.optJSONArray("matriculas");
                    for (int i = 0; i < matriculasZW.length(); i++) {
                        alunozw.add(matriculasZW.getInt(i));
                    }
                    for (Integer matriculaZW : alunozw) {
                        clienteSinteticoService.addAluno(ctx, matriculaZW, null, false, null, empresa, false);
                    }
                }
            }
            return "ok";
        } catch (Exception e) {
            e.printStackTrace();
            return e.getMessage();
        }
    }

    public String sincronizarProfessoresEmpresa(String ctx, Integer empresa) {
        StringBuilder log = new StringBuilder();
        try {
            try (ResultSet rs = dao.createStatement(ctx, "select codigocolaborador, count(c.codigo) as clientes from professorsintetico p\n" +
                    " inner join clientesintetico c on c.professorsintetico_codigo = p.codigo \n" +
                    " where c.empresa = " + empresa +
                    " group by codigocolaborador \n" +
                    " order by codigocolaborador ")) {

                JSONObject object = chamadaZW(ctx,
                        "/prest/treino/nr-professor",
                        empresa, null);
                try {
                    Map<Integer, Integer> mapaProfessorAtivos = new HashMap<>();
                    while (rs.next()) {
                        mapaProfessorAtivos.put(rs.getInt("codigocolaborador"), rs.getInt("clientes"));
                    }
                    Set<String> codigosColaboradores = object.keySet();
                    for (String codigoColaborador : codigosColaboradores) {
                        try {
                            Integer colaborador = Integer.valueOf(codigoColaborador);
                            int ativosZW = object.getInt(codigoColaborador);
                            int ativos = mapaProfessorAtivos.get(colaborador);

                            if (ativos != ativosZW) {
                                log.append(colaborador).append(";");
                                JSONObject objectMatriculas = chamadaZW(ctx,
                                        "/prest/treino/matriculas-professor",
                                        empresa, colaborador);

                                String ativosZWStr = "";
                                JSONArray matriculasZW = objectMatriculas.optJSONArray("matriculas");
                                for (int i = 0; i < matriculasZW.length(); i++) {
                                    ativosZWStr += "," + matriculasZW.getInt(i);
                                }

                                try (ResultSet rsProfessor = dao.createStatement(ctx, "select codigo from professorsintetico where codigocolaborador = " + colaborador)) {
                                    if (rsProfessor.next()) {
                                        String update = "update clientesintetico set professorSintetico_codigo = " + rsProfessor.getInt("codigo") + " where matricula in ("
                                                + ativosZWStr.replaceFirst(",", "") + ")";
                                        dao.executeNative(ctx, update);
                                    }
                                }
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return log.toString();
    }
}
