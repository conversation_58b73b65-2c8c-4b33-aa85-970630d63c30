package br.com.pacto.service.impl.lesao;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.lesao.AlunoLesaoDTO;
import br.com.pacto.bean.lesao.FiltroLesaoJSON;
import br.com.pacto.bean.lesao.GravidadeLesaoEnum;
import br.com.pacto.bean.lesao.IndiceLesaoDTO;
import br.com.pacto.bean.lesao.Lesao;
import br.com.pacto.bean.lesao.LesaoAppDTO;
import br.com.pacto.bean.lesao.LesaoAppVO;
import br.com.pacto.bean.lesao.LesaoDTO;
import br.com.pacto.bean.log.Log;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.log.EntidadeLogEnum;
import br.com.pacto.dao.intf.lesao.LesaoDao;
import br.com.pacto.dao.intf.log.LogDao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.notificacao.excecao.TipoWodExcecoes;
import br.com.pacto.service.intf.cliente.ClienteSinteticoService;
import br.com.pacto.service.intf.empresa.EmpresaService;
import br.com.pacto.service.intf.lesao.LesaoService;
import br.com.pacto.service.intf.usuario.UsuarioService;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.impl.UtilReflection;
import br.com.pacto.util.json.ClienteSintenticoJson;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class LesaoServiceImpl implements LesaoService {
    @Autowired
    private LesaoDao lesaoDao;
    @Autowired
    private UsuarioService usuarioService;
    @Autowired
    private ClienteSinteticoService clienteSinteticoService;
    @Autowired
    private LogDao logDao;
    @Autowired
    private EmpresaService empresaService;

    @Override
    public LesaoDTO gravarLesao(LesaoDTO lesaoDTO, String ctx, Integer codigoUsuario, boolean aluno) throws ServiceException {
        try {
            Usuario us = usuarioService.obterPorId(ctx, codigoUsuario);
            if (us == null) {
                us = usuarioService.obterPorId(ctx, codigoUsuario, aluno);
                if (us != null && us.getCliente() == null) {
                    us = null;
                }
            }
            ClienteSintetico cliente = clienteSinteticoService.consultarPorMatricula(ctx, lesaoDTO.getMatricula().toString());
            Lesao lesao = new Lesao(lesaoDTO,cliente);

            if (lesao.getCodigo() == null || lesao.getCodigo() == 0) {
                lesao.setDataRegistro(new Date());
                lesao.setUsuarioLancou_codigo(codigoUsuario);
                lesao = lesaoDao.insert(ctx, lesao);
            } else {
                Lesao lesaoAntesAlteracao = lesaoDao.findById(ctx, lesao.getCodigo());
                lesao.setDataRegistro(lesaoAntesAlteracao.getDataRegistro());
                lesao.setUsuarioLancou_codigo(lesaoAntesAlteracao.getUsuarioLancou_codigo());
                lesao = lesaoDao.update(ctx, lesao);
            }
            ClienteSintenticoJson clienteSintenticoJson = ClienteSintenticoJson.fromClienteSinteticoToClienteSinteticoJson(cliente);
            return  new LesaoDTO(lesaoDao.findById(ctx, lesao.getCodigo()), clienteSintenticoJson, (us == null ? "" : us.getUserName()));
        } catch (Exception e) {
            throw new ServiceException("Erro ao registrar a lesão", e);
        }
    }

    @Override
    public List<LesaoDTO> listarLesao(String ctx, FiltroLesaoJSON filtroLesaoJSON, PaginadorDTO paginadorDTO) throws ServiceException {
        try {
            List<Lesao> listaLesao = lesaoDao.listarLesao(ctx, filtroLesaoJSON, paginadorDTO );
            List<LesaoDTO> listaLesaoDTO = new ArrayList<>();

            for (Lesao l : listaLesao) {
                ClienteSintenticoJson clienteSintenticoJson = ClienteSintenticoJson.fromClienteSinteticoToClienteSinteticoJson(l.getCliente());
                Usuario us = usuarioService.obterPorId(ctx, l.getUsuarioLancou_codigo());
                if (us == null) {
                    us = usuarioService.obterPorId(ctx, l.getUsuarioLancou_codigo(), true);
                    if (us != null && us.getCliente() == null) {
                        us = null;
                    }
                }
                listaLesaoDTO.add(new LesaoDTO(l, clienteSintenticoJson, (us == null ? "" : us.getUserName())));
            }

            return listaLesaoDTO;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException(TipoWodExcecoes.ERRO_BUSCAR_TIPO_WOD, e);
        }
    }

    @Override
    public LesaoDTO buscarLesao(Integer id, String ctx) throws ServiceException {
        try {
            Lesao lesao = lesaoDao.findById(ctx, id);
            ClienteSintenticoJson clienteSintenticoJson = ClienteSintenticoJson.fromClienteSinteticoToClienteSinteticoJson(lesao.getCliente());
            Usuario us = usuarioService.obterPorId(ctx, lesao.getUsuarioLancou_codigo());
            if (us == null) {
                us = usuarioService.obterPorId(ctx, lesao.getUsuarioLancou_codigo(), true);
                if (us != null && us.getCliente() == null) {
                    us = null;
                }
            }
            return new LesaoDTO(lesao, clienteSintenticoJson, (us == null ? "" : us.getUserName()));
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException(TipoWodExcecoes.ERRO_BUSCAR_TIPO_WOD, e);
        }
    }

    @Override
    public IndiceLesaoDTO montarIndiceLesao(String ctx, Integer empresaIdZw, Integer ano) throws ServiceException {
        IndiceLesaoDTO indiceLesaoDTO = new IndiceLesaoDTO();

        for (GravidadeLesaoEnum gravidade : GravidadeLesaoEnum.values()) {
            switch (gravidade) {
                case LEVE:
                    indiceLesaoDTO.setTotalLesoesLeves(consultarTotalLesoesPorEnum(ctx, empresaIdZw, ano, gravidade, false));
                    break;
                case MODERADA:
                    indiceLesaoDTO.setTotalLesoesModeradas(consultarTotalLesoesPorEnum(ctx, empresaIdZw, ano, gravidade, false));
                    break;
                case GRAVE:
                    indiceLesaoDTO.setTotalLesoesGraves(consultarTotalLesoesPorEnum(ctx, empresaIdZw, ano, gravidade, false));
                    break;
            }
        }
        indiceLesaoDTO.setTotalLesoesAoAno(consultarTotalLesoesPorEnum(ctx, empresaIdZw, ano, null, true));

        return indiceLesaoDTO;
    }

    private Integer consultarTotalLesoesPorEnum(String ctx, Integer empresaIdZw, Integer ano, GravidadeLesaoEnum gravidadeLesaoEnum, boolean todas) {
        try {
            StringBuilder sql = new StringBuilder();
            sql.append(" SELECT COUNT(l.codigo) AS qt FROM lesao l ");
            sql.append(" INNER JOIN clientesintetico c ON c.codigo = l.cliente_codigo ");
            sql.append(" WHERE c.empresa = ").append(empresaIdZw);
            sql.append(" AND EXTRACT(YEAR FROM l.datalesao) = ").append(ano);

            if (!todas) {
                sql.append(" AND l.gravidade = '").append(gravidadeLesaoEnum.name()).append("'");
            }

            try (ResultSet rs = lesaoDao.createStatement(ctx, sql.toString())) {
                if (rs.next()) {
                    return rs.getInt("qt");
                }
            }
        } catch (Exception e) {
            Uteis.logar(e, LesaoService.class);
        }

        return 0;
    }

    @Override
    public List<AlunoLesaoDTO> listarLesoesBiCross(String ctx, Integer empresaIdZw, JSONObject filtros) throws ServiceException {
        switch (filtros.getString("gravidadeEnun")) {
            case "LEVE":
                return consultarListaLesoesPorEnum(ctx, empresaIdZw, filtros, GravidadeLesaoEnum.LEVE, false);
            case "MODERADA":
                return consultarListaLesoesPorEnum(ctx, empresaIdZw, filtros, GravidadeLesaoEnum.MODERADA, false);
            case "GRAVE":
                return consultarListaLesoesPorEnum(ctx, empresaIdZw, filtros, GravidadeLesaoEnum.GRAVE, false);
            default:
                return consultarListaLesoesPorEnum(ctx, empresaIdZw, filtros, null, true);
        }
    }

    @Override
    public List<LesaoAppVO> consultarLesaoPorCliente(Integer codigoUsuarioTreino, String ctx) throws ServiceException {
        try {
            Usuario us = usuarioService.obterPorId(ctx, codigoUsuarioTreino, true);
            if (us == null || us.getCliente() == null) {
                return new ArrayList<>();
            }

            List<LesaoAppVO> listaLesaoDTO = new ArrayList<>();
            try {
                List<Lesao> listaLesao = lesaoDao.consultarLesaoPorCliente(us.getCliente().getCodigo(), ctx);
                for (Lesao l : listaLesao) {
                    listaLesaoDTO.add(new LesaoAppVO(l));
                }
            } catch (Exception e) {
                Uteis.logar(e, LesaoService.class);
            }

            return listaLesaoDTO;
        }
        catch (Exception e) {
            throw new ServiceException("Erro ao consultar lesões por cliente: " + e.getMessage(), e);
        }
    }

    @Override
    public void excluirLesao(Lesao lesao, String ctx, Integer codigoUsuarioTreino) throws ServiceException {
        try {
            incluirLog(ctx, "EXCLUSAO", "EXCLUSAO DE LESAO", "EXCLUSAO DE LESAO",
                    EntidadeLogEnum.LESAO, lesao.getCodigo().toString(),
                    lesao.toString(), "", codigoUsuarioTreino.toString(),false);

            lesaoDao.delete(ctx, lesao);
        } catch (Exception e) {
            throw new ServiceException("Erro ao excluir a lesão: " + e.getMessage(), e);
        }
    }

    @Override
    public LesaoAppVO cadastraLesao(LesaoAppDTO lesaoAppDTO, String ctx, Integer codigoUsuarioTreino) throws ServiceException {
        try {
            Usuario us = usuarioService.obterPorId(ctx, codigoUsuarioTreino, true);
            if (us == null || us.getCliente() == null) {
                throw new ServiceException("Usuário não encontrado");
            }
            Lesao lesao = new Lesao();
            lesao.setCliente(us.getCliente());
            lesao.setObservacao(lesaoAppDTO.getObservacao());
            lesao.setRegiaoLesao(lesaoAppDTO.getRegiaoLesao());
            lesao.setGravidade(lesaoAppDTO.getGravidade());
            lesao.setStatus(lesaoAppDTO.getStatusAtual());
            lesao.setDataLesao(Calendario.getDate("yyyy-MM-dd", lesaoAppDTO.getDataLesao()));
            Date dataRecuperacao = UteisValidacao.emptyString(lesaoAppDTO.getDataRecuperacao()) ? null : Calendario.getDate("yyyy-MM-dd", lesaoAppDTO.getDataRecuperacao());
            lesao.setDataRecuperacao(dataRecuperacao);
            lesao.setDataRegistro(Calendario.hoje(empresaService.obterFusoHorarioEmpresa(ctx, us.getCliente().getEmpresa())));
            lesao.setUsuarioLancou_codigo(codigoUsuarioTreino);
            lesao.setApp(true);
            lesao = lesaoDao.insert(ctx, lesao);

            return new LesaoAppVO(lesao);
        } catch (Exception e) {
            throw new ServiceException("Erro ao registrar a lesão: " + e.getMessage(), e);
        }
    }

    @Override
    public LesaoAppVO editarLesao(LesaoAppDTO lesaoAppDTO, String ctx, Integer codigoUsuarioTreino, Integer id) throws ServiceException {
        try {
            Usuario us = usuarioService.obterPorId(ctx, codigoUsuarioTreino, true);
            if (us == null || us.getCliente() == null) {
                throw new ServiceException("Usuário não encontrado");
            }
            Lesao lesao = lesaoDao.findById(ctx, id);
            if (lesao == null) {
                throw new ServiceException("Lesão não encontrada");
            }
            Lesao lesaoAntesAlteracao = UtilReflection.copy(lesao);

            lesao.setObservacao(lesaoAppDTO.getObservacao());
            lesao.setRegiaoLesao(lesaoAppDTO.getRegiaoLesao());
            lesao.setGravidade(lesaoAppDTO.getGravidade());
            lesao.setStatus(lesaoAppDTO.getStatusAtual());
            lesao.setDataLesao(Calendario.getDate("yyyy-MM-dd", lesaoAppDTO.getDataLesao()));
            Date dataRecuperacao = UteisValidacao.emptyString(lesaoAppDTO.getDataRecuperacao()) ? null : Calendario.getDate("yyyy-MM-dd", lesaoAppDTO.getDataRecuperacao());
            lesao.setDataRecuperacao(dataRecuperacao);
            lesao.setApp(true);
            lesao = lesaoDao.update(ctx, lesao);

            incluirLog(ctx, "ALTERACAO", "ALTERACAO DE LESAO", "ALTERACAO DE LESAO",
                    EntidadeLogEnum.LESAO, lesao.getCodigo().toString(),
                    lesaoAntesAlteracao.toString(), lesao.toString(), codigoUsuarioTreino.toString(),false);

            return new LesaoAppVO(lesao);
        } catch (Exception e) {
            throw new ServiceException("Erro ao registrar a lesão: " + e.getMessage(), e);
        }
    }

    @Override
    public Lesao consultaLesaoPorId(Integer id, String ctx) {
        try {
            Lesao lesao = lesaoDao.findById(ctx, id);
            return lesao;
        } catch (Exception e) {
            Uteis.logar(e, LesaoService.class);
        }
        return null;
    }

    public void incluirLog(String ctx, String operacao, String descricaoOperacao, String chavePrimariaEntidade,
                                  EntidadeLogEnum entidade, String chavePrimariaSubordinado,
                                  String descAnterior, String descAtual, String username, boolean ia) throws Exception {
        Log log = new Log();
        log.setOperacao(operacao);
        log.setDescricao(descricaoOperacao);
        log.setChavePrimaria(chavePrimariaEntidade);
        if(isNotBlank(chavePrimariaSubordinado)) {
            log.setChavePrimariaEntidadeSubordinada(chavePrimariaSubordinado);
        }
        log.setNomeEntidade(entidade.name());
        log.setNomeEntidadeDescricao("Lesão");
        log.setResponsavelAlteracao(username);
        log.setUserOAMD("");
        log.setDataAlteracao(Calendario.hoje());
        log.setValorCampoAnterior(descAnterior);
        log.setValorCampoAlterado(descAtual);
        log.setPessoa(0);
        log.setIa(ia);
        logDao.insert(ctx, log);
    }

    private List<AlunoLesaoDTO> consultarListaLesoesPorEnum(String ctx, Integer empresaIdZw, JSONObject filtros, GravidadeLesaoEnum gravidadeLesaoEnum, boolean todas) throws ServiceException {
        List<AlunoLesaoDTO> listaLesaoDTO = new ArrayList<>();

        try {
            StringBuilder sql = new StringBuilder();
            sql.append(" SELECT c.matricula, c.nome, l.dataLesao, ps.nome as professor, l.regiaoLesao, l.gravidade, l.observacao");
            sql.append(" FROM lesao l ");
            sql.append(" INNER JOIN clientesintetico c ON c.codigo = l.cliente_codigo ");
            sql.append(" LEFT JOIN professorsintetico ps ON ps.codigo = c.professorsintetico_codigo ");
            sql.append(" WHERE c.empresa = ").append(empresaIdZw);

            if (!todas) {
                sql.append(" AND l.gravidade = '").append(gravidadeLesaoEnum.name()).append("'");
            }

            if (filtros != null) {
                Integer ano = filtros.optInt("ano");
                if (!UteisValidacao.emptyNumber(ano)) {
                    sql.append(" AND EXTRACT(YEAR FROM l.datalesao) = ").append(ano);
                }

                String quicksearchValue = filtros.optString("quicksearchValue");
                if (!UteisValidacao.emptyString(quicksearchValue)) {
                    sql.append(" AND c.nome ILIKE '%").append(quicksearchValue).append("%' ");
                }
            }

            try (ResultSet rs = lesaoDao.createStatement(ctx, sql.toString())) {
                while (rs.next()) {
                    AlunoLesaoDTO lesaoDTO = new AlunoLesaoDTO();
                    lesaoDTO.setMatricula(rs.getInt("matricula"));
                    lesaoDTO.setNome(rs.getString("nome"));
                    lesaoDTO.setDataLesao(Calendario.getData(rs.getDate("dataLesao"), "dd/MM/yyyy"));
                    lesaoDTO.setProfessor(rs.getString("professor"));
                    lesaoDTO.setRegiaoLesao(rs.getString("regiaoLesao"));
                    lesaoDTO.setGravidade(GravidadeLesaoEnum.obterPorNome(rs.getString("gravidade")).getNome());
                    lesaoDTO.setObservacao(rs.getString("observacao"));
                    listaLesaoDTO.add(lesaoDTO);
                }
            }
        } catch (Exception e) {
            Uteis.logar(e, LesaoService.class);
        }

        return listaLesaoDTO;
    }

}
