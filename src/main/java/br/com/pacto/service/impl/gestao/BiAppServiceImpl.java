package br.com.pacto.service.impl.gestao;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ExecuteRequestHttpService;
import br.com.pacto.bean.bi.ConfiguracaoRankingProfessores;
import br.com.pacto.bean.bi.DashboardBI;
import br.com.pacto.bean.bi.IndicadorDashboardEnum;
import br.com.pacto.bean.bi.ProfessorRankingIndicador;
import br.com.pacto.bean.professor.ProfessorSintetico;
import br.com.pacto.controller.json.biapp.BiAppDTO;
import br.com.pacto.dao.intf.usuario.UsuarioDao;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.intf.gestao.BiAppService;
import br.com.pacto.service.intf.gestao.CacheUsamAppDTO;
import br.com.pacto.service.intf.gestao.RankingProfessoresService;
import br.com.pacto.util.UteisValidacao;
import org.json.JSONArray;
import org.json.JSONObject;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.util.EntityUtils;
import org.olap4j.impl.ArrayMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Service
public class BiAppServiceImpl implements BiAppService {

    @Autowired
    private RankingProfessoresService rankingProfessoresService;
    @Autowired
    private UsuarioDao usuarioDao;

    public String getDocumentKey(String chave) throws Exception{
        HttpGet httpGet = new HttpGet(Aplicacao.getProp(Aplicacao.urlServicoDadosApp)
                + "/getDocumentKey?key=" + chave);
        HttpClient client = ExecuteRequestHttpService.createConnectorCloseable();
        HttpResponse response = client.execute(httpGet);
        String retorno = EntityUtils.toString(response.getEntity());
        JSONObject json = new JSONObject(retorno);
        if(json.has("erro")){
            throw new Exception(json.getString("erro"));
        }
        return json.has("sucesso") ?
                json.getJSONObject("sucesso").optString("documentID") : "";
    }

    public List<String> emailsUsersApp(String key, Integer codEmpresa, Boolean obrigatorioAlunoTerVinculoProfessor) throws Exception {
        List<String> emails = new ArrayList<>();
        String sql = "SELECT distinct u.username FROM usuario u \n" +
                " INNER JOIN clientesintetico c ON c.codigo = u.cliente_codigo \n";
                if(obrigatorioAlunoTerVinculoProfessor) {
                    sql = sql + " INNER JOIN professorsintetico p ON p.codigo = c.professorsintetico_codigo  \n";
                }
                sql = sql + " WHERE c.empresa = " + codEmpresa + " \n" +
                " AND (u.idclienteapp IS NOT NULL OR u.dataregistrousoapp IS NOT NULL)";
        try (ResultSet rs = usuarioDao.createStatement(key, sql.toString())) {
            while (rs.next()) {
                emails.add(rs.getString("username"));
            }
        }
        return emails;
    }

    public List<String> emails(String key, boolean forcar) throws Exception{

        CacheUsamAppDTO cacheUsamAppDTO = chaveEmails.get(key);
        if(!forcar && cacheUsamAppDTO != null &&
            Calendario.igual(Calendario.getDataComHoraZerada(cacheUsamAppDTO.getAtualizadoEm()), Calendario.getDataComHoraZerada(Calendario.hoje()))){
            return cacheUsamAppDTO.getEmails();
        }

        String documentKey = getDocumentKey(key);
        if(UteisValidacao.emptyString(documentKey)) {
           return new ArrayList<>();
        }
        HttpGet httpGet = new HttpGet(Aplicacao.getProp(Aplicacao.urlServicoDadosApp)
                + "/manter/obterListaUsuariosApp?documentid=" + documentKey);
        HttpClient client = ExecuteRequestHttpService.createConnectorCloseable();
        HttpResponse response = client.execute(httpGet);
        String retorno = EntityUtils.toString(response.getEntity());
        JSONObject json = new JSONObject(retorno);
        JSONArray array = new JSONArray(json.getString("sucesso"));
        List<String> emails = new ArrayList<>();
        for (int i = 0; i < array.length(); i++) {
            emails.add(array.getJSONObject(i).getString("userName"));
        }
        cacheUsamAppDTO = new CacheUsamAppDTO();
        cacheUsamAppDTO.setAtualizadoEm(Calendario.hoje());
        cacheUsamAppDTO.setEmails(emails);
        chaveEmails.put(key, cacheUsamAppDTO);
        return emails;
    }

    public void processarDashAlunosComApp(final String key, final Integer empresa, DashboardBI dash, int idProfessor, boolean forcarAtualizacao, Boolean vinculoProfessor) {
        try {
            List<String> emails = Aplicacao.isTrue(Aplicacao.consultaUsuariosAppPeloFireBase) ? emails(key, forcarAtualizacao) : emailsUsersApp(key, empresa, vinculoProfessor);
            if(emails.isEmpty()){
                return;
            }
            StringBuilder whereEmails = new StringBuilder();
            for (String e : emails) {
                whereEmails.append(", '").append(e).append("' ");
            }
            int ativosInstalados = 0;
            int inativosInstalados = 0;
            int naoInstalados = 0;

            if(vinculoProfessor) {
                ativosInstalados = usuariosApp(key, null, empresa, idProfessor, whereEmails, true, false, false, false, true);
                inativosInstalados = usuariosApp(key, null, empresa, idProfessor, whereEmails, false, true, false, false, true);
                naoInstalados = usuariosApp(key, null, empresa, idProfessor, whereEmails, false, false, true, false, true);
            } else {
                ativosInstalados = usuariosApp(key, null, empresa, idProfessor, whereEmails, true, false, false, false, false);
                inativosInstalados = usuariosApp(key, null, empresa, idProfessor, whereEmails, false, true, false, false, false);
                naoInstalados = usuariosApp(key, null, empresa, idProfessor, whereEmails, false, false, true, false, false);
            }
            dash.setUtilizamApp(ativosInstalados);
            dash.setNaoUtilizamApp(naoInstalados);
            dash.setInativosUtilizamApp(inativosInstalados);
            int total = ativosInstalados + naoInstalados;
            Double porcentagem = Uteis.arredondarForcando2CasasDecimais(total == 0 ? 0.0 : (((ativosInstalados * 1.0) / total) * 100.0));
            dash.setPercUtilizamApp(porcentagem);

        } catch (Exception e) {
            Uteis.logar(e, DashboardServiceImpl.class);
        }
    }

    public void processarRankingAlunosComApp(final String key, ProfessorSintetico professorSintetico,
                                             final List<ProfessorRankingIndicador> listaItems,
                                             final List<ConfiguracaoRankingProfessores> configs,
                                             Date inicio,
                                             Date fim, Integer empresazw) {
        try {
            if(!rankingProfessoresService.contemIndicador(configs, IndicadorDashboardEnum.ALUNOS_APP_INSTALADO,
                    IndicadorDashboardEnum.ALUNOS_APP_INSTALADO,
                    IndicadorDashboardEnum.ALUNOS_APP_INSTALADO_ATIVOS,
                    IndicadorDashboardEnum.ALUNOS_APP_NAO_INSTALADO
                    )){
                return;
            }
            List<String> emails = Aplicacao.isTrue(Aplicacao.consultaUsuariosAppPeloFireBase) ? emails(key, false) : emailsUsersApp(key, empresazw, true);
            if(emails.isEmpty()){
                return;
            }
            StringBuilder whereEmails = new StringBuilder();
            for (String e : emails) {
                whereEmails.append(", '").append(e).append("' ");
            }
            int ativosInstalados = usuariosApp(key, null, empresazw, professorSintetico.getCodigo(), whereEmails, true, false, false, false, true);
            rankingProfessoresService.addIndicador(listaItems, IndicadorDashboardEnum.ALUNOS_APP_INSTALADO,
                    rankingProfessoresService.configIndicador(configs, IndicadorDashboardEnum.ALUNOS_APP_INSTALADO),
                    new Double(ativosInstalados));
            rankingProfessoresService.addIndicador(listaItems, IndicadorDashboardEnum.ALUNOS_APP_INSTALADO_ATIVOS,
                    rankingProfessoresService.configIndicador(configs, IndicadorDashboardEnum.ALUNOS_APP_INSTALADO_ATIVOS),
                    new Double(ativosInstalados));
            int inativosInstalados = usuariosApp(key, null, empresazw,  professorSintetico.getCodigo(), whereEmails, false, true, false, false, true);
            int naoInstalados = usuariosApp(key, null, empresazw,  professorSintetico.getCodigo(), whereEmails, false, false, true, false, true);
            rankingProfessoresService.addIndicador(listaItems, IndicadorDashboardEnum.ALUNOS_APP_NAO_INSTALADO,
                    rankingProfessoresService.configIndicador(configs, IndicadorDashboardEnum.ALUNOS_APP_NAO_INSTALADO),
                    new Double(naoInstalados));
        } catch (Exception e) {
            Uteis.logar(e, DashboardServiceImpl.class);
        }
    }


    public BiAppDTO biApp(final String key, final Integer empresa, boolean forcarAtualizacao) throws Exception{
        BiAppDTO biapp = new BiAppDTO();
        DashboardBI dash = new DashboardBI();
        try (ResultSet rs = usuarioDao.createStatement(key, "select count(codigo) as total from clientesintetico " +
                " where situacao = 'AT' " +
                (UteisValidacao.emptyNumber(empresa) ? "" : (" and empresa = " + empresa)))) {
            if (rs.next()) {
                biapp.setAtivos(rs.getInt("total"));
            }
        }
        processarDashAlunosComApp(key, empresa, dash, 0, forcarAtualizacao, false);
        biapp.setAtivosComApp(dash.getUtilizamApp());
        biapp.setInativosComApp(dash.getInativosUtilizamApp());
        biapp.setAtivosSemApp(biapp.getAtivos() - dash.getUtilizamApp());
        if(UteisValidacao.emptyNumber(biapp.getAtivos()) || UteisValidacao.emptyNumber(biapp.getAtivosComApp())){
            biapp.setPercentualAlunosUsamApp(0);
        } else {
            Double percentual = (biapp.getAtivosComApp() * 100.0)/new Double(biapp.getAtivos().intValue());
            biapp.setPercentualAlunosUsamApp(percentual.intValue());
        }
        CacheUsamAppDTO cacheUsamAppDTO = chaveEmails.get(key);
        biapp.setUltimaAtualizacao(Uteis.getDataAplicandoFormatacao(
                cacheUsamAppDTO == null || cacheUsamAppDTO.getAtualizadoEm() == null ? Calendario.hoje() :  cacheUsamAppDTO.getAtualizadoEm(), "dd/MM/yyyy HH:mm"));
        return biapp;
    }

    public Integer usuariosApp(String key,
                               String filter,
                               Integer empresa,
                               Integer idProfessor,
                               StringBuilder whereEmails,
                               boolean ativosInstalados,
                               boolean inativosInstalados,
                               boolean naoInstalados, boolean somenteAtivos, Boolean obrigatorioVinculoProfessor) throws Exception{
        String campos = " COUNT(c.codigo) as cont ";
        StringBuilder sql = sqlConsulta(campos,
                whereEmails.toString(),
                filter,
                empresa,
                idProfessor,
                ativosInstalados,
                naoInstalados,
                inativosInstalados,
                obrigatorioVinculoProfessor,
                somenteAtivos, null);

        try (ResultSet rs = usuarioDao.createStatement(key, sql.toString())) {
            return rs.next() ? rs.getInt("cont") : 0;
        }
    }

    public List<Map<String, String>> listaUsuariosApp(String key,
                                                      String filter,
                                                      Integer empresa,
                                                      Integer idProfessor,
                                                      boolean ativosInstalados,
                                                      boolean inativosInstalados,
                                                      boolean naoInstalados,
                                                      boolean somenteAtivos, PaginadorDTO paginadorDTO) throws Exception{

        List<Map<String, String>> lista = new ArrayList<>();
        List<String> emails = Aplicacao.isTrue(Aplicacao.consultaUsuariosAppPeloFireBase) ? emails(key, false) : emailsUsersApp(key, empresa, false);
        StringBuilder whereEmails = new StringBuilder();

        if(!somenteAtivos) {
            if(emails.isEmpty()){
                return lista;
            }
            for (String e : emails) {
                whereEmails.append(", '").append(e).append("' ");
            }
            if(emails.isEmpty()){
                return lista;
            }
        }

        String campos = "c.matricula, c.nome as nomeAbreviado, p.nome as nomeProfessor";
        StringBuilder sql = sqlConsulta(campos,
                whereEmails.toString(),
                filter,
                empresa,
                idProfessor,
                ativosInstalados,
                naoInstalados,
                inativosInstalados,
                false,
                somenteAtivos, paginadorDTO);
        int total = usuariosApp(key, filter, empresa, idProfessor, whereEmails, ativosInstalados, inativosInstalados,naoInstalados, somenteAtivos, false);
        paginadorDTO.setQuantidadeTotalElementos(new Long(total));

        try (ResultSet rs = usuarioDao.createStatement(key, sql.toString())) {
            while (rs.next()) {
                Map<String, String> aluno = new ArrayMap<>();
                aluno.put("matricula", rs.getString("matricula"));
                aluno.put("nomeAbreviado", Uteis.getNomeAbreviandoSomentePrimeiroSobrenome(rs.getString("nomeAbreviado")));
                aluno.put("nomeProfessor", Uteis.getNomeAbreviandoSomentePrimeiroSobrenome(rs.getString("nomeProfessor")));
                lista.add(aluno);
            }
        }
        return lista;
    }

    private StringBuilder sqlConsulta(String campos,
                                      String whereEmails,
                                      String filter,
                                      Integer empresa,
                                      Integer idProfessor,
                                      boolean ativosInstalados,
                                      boolean naoInstalados,
                                      boolean inativosInstalados,
                                      boolean obrigatorioAlunoTerVinculoProfessor,
                                      Boolean somenteAtivos,
                                      PaginadorDTO paginadorDTO) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select ");
        sql.append(campos);
        sql.append(" from usuario u \n");
        sql.append(" inner join clientesintetico c on u.cliente_codigo = c.codigo \n");
        if(obrigatorioAlunoTerVinculoProfessor) {
            sql.append(" inner join professorsintetico p on p.codigo = c.professorsintetico_codigo  \n");
        } else {
            sql.append(" left join professorsintetico p on p.codigo = c.professorsintetico_codigo  \n");
        }
        sql.append(" where c.empresa = ").append(empresa);

        boolean buscaRapida = filter != null && filter.contains("quicksearchValue");
        if (buscaRapida){
            JSONObject objJson = new JSONObject();
            if (filter != null){
                filter = Uteis.retirarAcentuacaoRegex(filter);
                objJson = new JSONObject(filter);
            }
            sql.append(" AND (");
            String pesquisa = objJson.getString("quicksearchValue");
            try {
                Integer matricula = Integer.valueOf(pesquisa);
                sql.append(" matricula = ").append(matricula);
                sql.append(" OR");
            } catch (NumberFormatException ignore) {
            }
            sql.append(" UPPER(c.nome) LIKE UPPER(CONCAT('").append(pesquisa.replaceAll(" ", "%")).append("','%')))");
        }

        if(!somenteAtivos) {
            sql.append(" and u.username ");
            if(naoInstalados){
                sql.append(" not ");
            }
            sql.append(" in (").append(whereEmails.replaceFirst(",", "")).append(") ");
        }

        if(ativosInstalados || naoInstalados || somenteAtivos){
            sql.append(" AND c.situacao in ('AT','NO','AV')");
        }

        if(inativosInstalados){
            sql.append("and c.situacao in ('IN', 'VI') \n");
        }

        if (idProfessor != null && idProfessor > 0){
            sql.append(" and p.codigo = ").append(idProfessor);
        }
        if(paginadorDTO != null){
            if(UteisValidacao.emptyString(paginadorDTO.getSort())){
                sql.append(" order by nomeAbreviado ASC");
            } else {
                String[] dadosOrdenacao = paginadorDTO.getSort().split(",");
                sql.append(" order by ").append(dadosOrdenacao[0]).append(" ").append(dadosOrdenacao[1]);
            }

            if(!UteisValidacao.emptyNumber(paginadorDTO.getSize())){
                sql.append(" limit ").append(paginadorDTO.getSize()).append(" offset ").append(paginadorDTO.getPage() * paginadorDTO.getSize());
            }
        }
        return sql;
    }

}
