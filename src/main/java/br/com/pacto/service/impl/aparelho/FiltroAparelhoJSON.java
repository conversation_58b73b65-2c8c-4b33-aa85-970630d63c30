package br.com.pacto.service.impl.aparelho;

import br.com.pacto.controller.json.base.SuperJSON;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;


public class FiltroAparelhoJSON extends SuperJSON {

    private Boolean nome = false;
    private String paramentro;
    private Boolean crossfit = false;

    public FiltroAparelhoJSON(JSONObject filters) throws JSONException {

        this.nome = nome;
        if (filters != null) {
            this.paramentro = filters.optString("quicksearchValue");
            JSONArray colunasVisiveis = filters.optJSONArray("quicksearchFields");
            this.crossfit = filters.optBoolean("crossfit");
            if (colunasVisiveis != null) {
                for (int i = 0; i < colunasVisiveis.length(); i++) {
                    if (colunasVisiveis.get(i).equals("nome")) {
                        this.nome = true;
                    }

                }

            }

        }
    }

    public Boolean getNome() {
        return nome;
    }

    public void setNome(Boolean nome) {
        this.nome = nome;
    }

    public String getParamentro() {
        return paramentro;
    }

    public void setParamentro(String paramentro) {
        this.paramentro = paramentro;
    }

    public Boolean getCrossfit() {
        return crossfit;
    }

    public void setCrossfit(Boolean crossfit) {
        this.crossfit = crossfit;
    }
}
