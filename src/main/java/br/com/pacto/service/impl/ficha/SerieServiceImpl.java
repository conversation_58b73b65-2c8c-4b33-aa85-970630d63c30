/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.impl.ficha;

import br.com.pacto.base.impl.EntityManagerService;
import br.com.pacto.bean.atividade.Atividade;
import br.com.pacto.bean.atividade.AtividadeFicha;
import br.com.pacto.bean.ficha.Ficha;
import br.com.pacto.bean.ficha.SerieResponseTO;
import br.com.pacto.bean.notificacao.TipoNotificacaoEnum;
import br.com.pacto.bean.serie.Serie;
import br.com.pacto.bean.serie.SerieEndpointTO;
import br.com.pacto.bean.serie.SerieRealizada;
import br.com.pacto.bean.sincronizacao.TipoObjetoSincronizarEnum;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.atividade.read.SerieJSON;
import br.com.pacto.dao.intf.atividade.AtividadeFichaDao;
import br.com.pacto.dao.intf.serie.SerieDao;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.exception.ValidacaoException;
import br.com.pacto.service.impl.notificacao.excecao.SerieExcecoes;
import br.com.pacto.service.intf.Validacao;
import br.com.pacto.service.intf.ficha.FichaService;
import br.com.pacto.service.intf.ficha.FilaImpressaoService;
import br.com.pacto.service.intf.ficha.SerieService;
import br.com.pacto.service.intf.usuario.UsuarioService;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;
import br.com.pacto.util.ViewUtils;
import br.com.pacto.util.impl.Ordenacao;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
@Service
public class SerieServiceImpl implements SerieService {

    @Autowired
    private ViewUtils viewUtils;
    @Autowired
    private Validacao validacao;
    @Autowired
    private SerieDao serieDao;
    @Autowired
    private FichaService fichaService;
    @Autowired
    private AtividadeFichaDao atividadeFichaDao;
    @Autowired
    private SessaoService sessaoService;
    @Autowired
    private UsuarioService usuarioService;
    @Autowired
    private FilaImpressaoService filaImpressaoService;

    public FichaService getFichaService() {
        return fichaService;
    }

    public ViewUtils getViewUtils() {
        return viewUtils;
    }

    public Validacao getValidacao() {
        return validacao;
    }

    public SerieDao getSerieDao() {
        return this.serieDao;
    }


    public void alterarSerie(final String ctx, SerieJSON serie) throws Exception{
        Serie serieEditar = obterPorId(ctx, serie.getCodSerie());

        double cargaApp = 0;
        int repetcaoApp = 0;

        if (serie.getCargaApp() != null){
            try{
                cargaApp = Double.parseDouble(serie.getCargaApp());
            } catch (Exception ignored){
            }
        }

        if (serie.getRepeticaoApp() != null){
            try{
                repetcaoApp = Integer.parseInt(serie.getRepeticaoApp());
            } catch (Exception ignored){
            }
        }

        if(serie.getCarga() != null){
            serieEditar.setCarga(serie.getCargaApp() != null && !UteisValidacao.emptyNumber(cargaApp)  ? cargaApp : serie.getCarga());
        }

        if(serie.getRepeticao() != null){
            serieEditar.setRepeticao(serie.getRepeticaoApp() != null && !UteisValidacao.emptyNumber(repetcaoApp) ? repetcaoApp : serie.getRepeticao());
        }

        if(serie.getDistancia() != null){
            serieEditar.setDistancia(serie.getDistancia());
        }

        if(serie.getVelocidade() != null){
            serieEditar.setVelocidade(serie.getVelocidade());
        }

        if(serie.getDuracao() != null){
            serieEditar.setDuracao(serie.getDuracao());
        }

        if (serie.getCargaComp() == null){
            serie.setCargaComp("");
        }

        if (serie.getRepeticaoComp() == null){
            serie.setRepeticaoComp("");
        }

        serieEditar.setCargaComp(serie.getCargaApp() != null ? serie.getCargaApp() : serie.getCargaComp());

        serieEditar.setRepeticaoComp(serie.getRepeticaoApp() != null ? serie.getRepeticaoApp() : serie.getRepeticaoComp());

        if(serie.getCargaApp() != null){
            serieEditar.setCargaApp(serie.getCargaApp());
        }

        if(serie.getRepeticaoApp() != null){
            serieEditar.setRepeticaoApp(serie.getRepeticaoApp());
        }

        serieEditar.setAtualizadoApp(true);

        alterar(ctx, serieEditar);

        fichaService.atualizarVersaoFicha(ctx, serieEditar.getAtividadeFicha().getFicha().getCodigo());
    }
    
    public Serie alterar(final String ctx, Serie object) throws ServiceException {
        try {
            return getSerieDao().update(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public void excluir(final String ctx, Serie object) throws ServiceException {
        try {
            getSerieDao().delete(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public Serie inserir(final String ctx, Serie object) throws ServiceException {
        try {
            return getSerieDao().insert(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public Serie obterObjetoPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException {
        try {
            return getSerieDao().findObjectByParam(ctx, query, params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public Serie obterPorId(final String ctx, Integer id) throws ServiceException {
        try {
            return getSerieDao().findById(ctx, id);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<Serie> obterPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException {
        try {
            return getSerieDao().findByParam(ctx, query, params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<Serie> obterPorParam(final String ctx, String query,
            Map<String, Object> params, int max, int index)
            throws ServiceException {
        try {
            return getSerieDao().findByParam(ctx, query, params, max, index);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<Serie> obterTodos(final String ctx) throws ServiceException {
        try {
            return getSerieDao().findAll(ctx);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public List<Serie> obterSeriePorAtividadeFicha(final String ctx, final Integer atividadeFicha) throws ServiceException {
        Map<String, Object> params = new HashMap<String, Object>();
        String s = "SELECT obj FROM Serie obj where obj.atividadeFicha.codigo = :atividadeFicha  order by ordem";
        params.put("atividadeFicha", atividadeFicha);
        try {
            return getSerieDao().findByParam(ctx, s, params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<Number> obterCodigosSeriePorAtividadeFicha(final String ctx, final Integer atividadeFicha) throws ServiceException {
        try {
            StringBuilder where = new StringBuilder(" WHERE atividadeficha_codigo ").append(" = ").append(atividadeFicha);
            return getSerieDao().listNumberWithParam(ctx, "SELECT codigo FROM Serie ", where, null);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public Serie adicionarSerie(final String key, final AtividadeFicha atividadeFicha,
                               final Ficha ficha, boolean salvar) throws ValidacaoException, ServiceException {
        Serie ultimaSerie = new Serie();
        System.out.println("<========AdicionarSerie START========>");
        atividadeFicha.printSeries();
        ultimaSerie.setDescansoStr(ficha.getDescansoRepetir());
        if (UteisValidacao.emptyList(atividadeFicha.getSeries())) {
            ultimaSerie.setOrdem(1);
        }else{
            ultimaSerie = atividadeFicha.getSeries().get(atividadeFicha.getSeries().size() - 1);
        }
        ultimaSerie.setarSegundos(false);
        Serie serie = new Serie(atividadeFicha,
                new Integer(Uteis.converterMinutosEmSegundos(ultimaSerie.getDuracaoSpin().getValorHora())),
                new Integer(ultimaSerie.getDistanciaSpin().getValorInteiro()),
                new Double(ultimaSerie.getVelocidadeSpin().getValorDouble()),
                new Integer(ultimaSerie.getDescanso()), new Integer(UteisValidacao.emptyNumber(
                ultimaSerie.getOrdem()) ? atividadeFicha.getSeries().size() + 1 : ultimaSerie.getOrdem() + 1),
                new Integer(ultimaSerie.getRepeticaoSpin().getValorInteiro()),
                new Double(ultimaSerie.getCargaSpin().getValorDouble()),
                ultimaSerie.getComplemento(), ultimaSerie.getCargaComp(), ultimaSerie.getRepeticaoComp(), ultimaSerie.getCadencia());

        if(salvar){
            atividadeFicha.getSeries().add(serie);
            arrumarOrdem(atividadeFicha.getSeries());
            if (atividadeFicha.getCodigo() != null && atividadeFicha.getCodigo().intValue() > 0) {
                salvarSeries(key, atividadeFicha, ficha);
            }
        }
        atividadeFicha.printSeries();
        System.out.println("<========AdicionarSerie END========>");

        return serie;

    }

    @Override
    public void adicionarSerie(final String key, final AtividadeFicha atividadeFicha,
            final Ficha ficha) throws ValidacaoException, ServiceException {
        adicionarSerie(key, atividadeFicha, ficha, true);
    }

    public void salvarSeries(final String ctx, final AtividadeFicha atvFicha, final Ficha ficha) throws ServiceException, ValidacaoException {
        for (Serie serie : atvFicha.getSeries()) {
            serie.setAtividadeFicha(atvFicha);
            serie.inicializarNulos();
            serie.setarSegundos(false);
            if ((serie.getCodigo() == null || serie.getCodigo().equals(0))
                    && serie.getAtividadeFicha() != null
                    && serie.getAtividadeFicha().getCodigo() != null) {
                serie = inserir(ctx, serie);
            } else {
                if (serie.getAtividadeFicha() != null && serie.getAtividadeFicha().getCodigo() != null) {
                    serie = alterar(ctx, serie);
                }
            }
        }
        fichaService.atualizarVersaoFicha(ctx, ficha.getCodigo());
    }

    public void arrumarOrdem(final List<Serie> series) {
        Ordenacao.ordenarLista(series, "ordem");
        int i = 1;
        for (Serie serie : series) {
            serie.setOrdem(i);
            i++;
        }
        Ordenacao.ordenarLista(series, "ordem");
    }

    public void removerSerie(final String ctx, final Serie serie, final AtividadeFicha atividade, final Ficha ficha) throws ValidacaoException, ServiceException {
        if (serie.getCodigo() != null && serie.getCodigo().intValue() > 0) {
            excluir(ctx, serie);
        }
        atividade.getSeries().remove(serie);
        arrumarOrdem(atividade.getSeries());
        salvarSeries(ctx, atividade, ficha);
    }
    
    @Override
    public void aplicarValorSeries(final String ctx, final Serie serie, final List<Serie> series) throws ServiceException {
        for(Serie sr : series){
            sr.setRepeticao(new Integer(serie.getRepeticao()));
            sr.setCarga(new Double(serie.getCarga()));
            sr.setDuracao(new Integer(serie.getDuracao()));
            sr.setDuracaoStr(serie.getDuracaoStr());
            sr.setComplemento(serie.getComplemento());
            sr.setCargaComp(serie.getCargaComp());
            sr.setRepeticaoComp(serie.getRepeticaoComp());
            sr.setVelocidade(new Double(serie.getVelocidade()));
            sr.setDistancia(new Integer(serie.getDistancia()));
            sr.setCadencia(serie.getCadencia());
            sr.setDescanso(new Integer(serie.getDescanso()));
            sr.setDescansoStr(serie.getDescansoStr());
        }
        
    }

    public void corrigirSerieZerada(final String ctx) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" select af.codigo as atividadeficha, emp.nome as empresa, cli.matricula, cli.nome,pt.nome as programa, ");
        sql.append(" a.nome as atividade, f.nome as ficha, serie.codigo as serie from serie \n");
        sql.append("inner join atividadeficha af on af.codigo = serie.atividadeficha_codigo\n");
        sql.append("inner join atividade a on a.codigo = af.atividade_codigo\n");
        sql.append("inner join ficha f on f.codigo = af.ficha_codigo\n");
        sql.append("inner join programatreinoficha ptf on ptf.ficha_codigo = af.ficha_codigo\n");
        sql.append("inner join programatreino pt on pt.codigo = ptf.programa_codigo\n");
        sql.append("inner join clientesintetico cli on cli.codigo = pt.cliente_codigo\n");
        sql.append("inner join empresa emp on cli.empresa = emp.codzw\n");
        sql.append("where carga  = 0\n");
        sql.append("and serie.descanso = 0\n");
        sql.append("and serie.ordem = 1\n");
        sql.append("and serie.repeticao = 0\n");
        sql.append("and serie.duracao = 0\n");
        sql.append("and serie.velocidade = 0\n");
        sql.append("and serie.atividadeficha_codigo in (\n");
        sql.append("select atividadeficha_codigo from serie where ordem > 1\n");
        sql.append("and (repeticao > 0\n");
        sql.append("or duracao > 0\n");
        sql.append("or carga > 0\n");
        sql.append("or velocidade > 0)) \n");
        sql.append("and pt.datainicio > '2017-07-01'\n");
        sql.append("order by atividadeficha_codigo desc ");
        try (ResultSet rs = getSerieDao().createStatement(ctx, sql.toString())) {
            boolean first = true;
            while (rs.next()) {
                try (ResultSet rsSerie = getSerieDao().createStatement(ctx, "select * from serie where atividadeficha_codigo = " + rs.getInt("atividadeficha") +
                        " and ordem = 2")) {
                    if (rsSerie.next()) {
                        Connection conn = getSerieDao().getConnection(ctx);
                        try {
                            PreparedStatement stmUpdateSerie = conn.prepareStatement("UPDATE serie SET carga = ?, "
                                    + "repeticao = ?, "
                                    + "cadencia = ?, "
                                    + "cargacomp = ?, "
                                    + "complemento = ?, "
                                    + "descanso = ?, "
                                    + "distancia = ?, "
                                    + "duracao = ?, "
                                    + "repeticaocomp = ?, "
                                    + "velocidade = ? where codigo = ? ");
                            stmUpdateSerie.setInt(1, rsSerie.getInt("carga"));
                            stmUpdateSerie.setInt(2, rsSerie.getInt("repeticao"));
                            stmUpdateSerie.setString(3, rsSerie.getString("cadencia"));
                            stmUpdateSerie.setString(4, rsSerie.getString("cargacomp"));
                            stmUpdateSerie.setString(5, rsSerie.getString("complemento"));
                            stmUpdateSerie.setInt(6, rsSerie.getInt("descanso"));
                            stmUpdateSerie.setInt(7, rsSerie.getInt("distancia"));
                            stmUpdateSerie.setInt(8, rsSerie.getInt("duracao"));
                            stmUpdateSerie.setString(9, rsSerie.getString("repeticaocomp"));
                            stmUpdateSerie.setInt(10, rsSerie.getInt("velocidade"));
                            stmUpdateSerie.setInt(11, rs.getInt("serie"));
                            stmUpdateSerie.execute();
                        } finally {
                            conn.close();
                        }

                    }
                }
            }
        }
    }

    public List<SerieRealizada> obterSeriesDoTreinoAud(final String ctx, final Integer idTreinoRealizado) throws ServiceException {
        try {
            StringBuilder sql = new StringBuilder();
            sql.append(" select s.atividadeficha_codigo, s.serie_codigo, tiponotificacao,(tiponotificacao is null) as semnotf, \n");
            sql.append(" s.datainicio, s.carga, s.distancia, s.duracao, s.repeticao, s.velocidade, \n");
            sql.append(" (select ordem from atividadeficha_aud \n");
            sql.append(" where revtype <> 2 and codigo = s.atividadeficha_codigo order by rev desc limit 1) as ordem , \n");
            sql.append(" (select nome from atividade_aud where revtype <> 2 and codigo in (select atividade_codigo \n");
            sql.append(" from atividadeficha_aud where revtype <> 2 and codigo = s.atividadeficha_codigo limit 1) order by rev desc limit 1) as nomeatividade, \n");
            sql.append(" (select nome from atividadeficha_aud \n");
            sql.append(" where revtype <> 2 and codigo = s.atividadeficha_codigo order by rev desc limit 1) as nomeatividadeficha \n");
            sql.append(" from serierealizada_aud s \n");
            sql.append(" where s.treinorealizado_codigo = ").append(idTreinoRealizado);
            List<SerieRealizada> series;
            try (ResultSet rs = serieDao.createStatement(ctx, sql.toString())) {
                series = new ArrayList<SerieRealizada>();
                while (rs.next()) {
                    SerieRealizada sr = new SerieRealizada();
                    sr.setSerie(new Serie());
                    sr.setDataInicio(rs.getTimestamp("datainicio"));
                    sr.setVelocidade(rs.getDouble("velocidade"));
                    sr.setDuracao(rs.getInt("duracao"));
                    sr.setRepeticao(rs.getInt("repeticao"));
                    sr.setDistancia(rs.getInt("distancia"));
                    sr.setCarga(rs.getDouble("carga"));
                    sr.getSerie().setAtividadeFicha(new AtividadeFicha());
                    sr.getSerie().getAtividadeFicha().setNome(rs.getString("nomeatividadeficha"));
                    sr.getSerie().getAtividadeFicha().setAtividade(new Atividade());
                    sr.getSerie().getAtividadeFicha().getAtividade().setNome(rs.getString("nomeatividade"));
                    sr.getSerie().getAtividadeFicha().setOrdem(rs.getInt("ordem"));
                    if (!rs.getBoolean("semnotf")) {
                        sr.setTipoNotificacao(TipoNotificacaoEnum.obterPorID(rs.getInt("tiponotificacao")));
                    }
                    sr.getSerie().getAtividadeFicha().setCodigo(rs.getInt("atividadeficha_codigo"));
                    series.add(sr);
                }
            }
            return series;
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    public void refresh(final String ctx, Serie object) throws Exception {
        getSerieDao().refresh(ctx, object);
    }

    @Override
    public SerieResponseTO cadastrarSerie(SerieEndpointTO serieTO) throws ServiceException {
        Integer fichaId = null;
        String chave = null;
        try {
            chave = sessaoService.getUsuarioAtual().getChave();
            AtividadeFicha af = atividadeFichaDao.findById(chave, serieTO.getAtividadeFichaId());
            if (af == null) {
                throw new ServiceException(SerieExcecoes.ATIVIDADE_NAO_ENCONTRADA);
            }
            fichaId = af.getFicha().getCodigo();
            Serie serie = new Serie();
            serie.setAtividadeFicha(af);
            serie.setOrdem(serieTO.getSequencia() == 0 || serieTO.getSequencia() == null ? af.getSeries().size() : serieTO.getSequencia());
            serie.setRepeticaoComp(serieTO.getRepeticoes());
            serie.setCargaComp(serieTO.getCarga());
            serie.setarCompValores();
            serie.setCadencia(serieTO.getCadencia());
            serie.setDescanso(serieTO.getDescanso());
            serie.setComplemento(serieTO.getComplemento());
            serie.setVelocidade(serieTO.getVelocidade());
            serie.setDuracao(serieTO.getDuracao());
            serie.setDistancia(serieTO.getDistancia());
            serie = inserir(chave, serie);
            return new SerieResponseTO(serie);
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException(SerieExcecoes.ERRO_INCLUIR_SERIE);
        } finally {
            if(!UteisValidacao.emptyNumber(fichaId) && StringUtils.isNotBlank(chave)){
                String username = usuarioService.obterUsernameAlunoPorFicha(chave, fichaId);
                if(StringUtils.isNotBlank(username)){
                    filaImpressaoService.enfileirar(chave, TipoObjetoSincronizarEnum.PROGRAMA, username);
                }
            }
        }
    }

    @Override
    public SerieResponseTO editarSerie(SerieEndpointTO serieTO) throws ServiceException {
        String chave = null;
        Integer fichaId = null;
        try {
            chave = sessaoService.getUsuarioAtual().getChave();
            Usuario usuario = usuarioService.obterPorId(chave, sessaoService.getUsuarioAtual().getId());
            Serie serie = obterPorId(chave, serieTO.getId());
            if(serieTO.getRepeticoes() != null) {
                serie.setRepeticaoComp(serieTO.getRepeticoes());
            }
            if(serieTO.getCarga() != null) {
                serie.setCargaComp(serieTO.getCarga());
            }
            if(serieTO.getCadencia() != null) {
                serie.setCadencia(serieTO.getCadencia());
            }
            if(serieTO.getDescanso().equals(0)) {
                serie.setDescanso(serieTO.getDescanso());
            }
            serie.setarCompValores();
            serie.setComplemento(serieTO.getComplemento());
            serie.setVelocidade(serieTO.getVelocidade());
            serie.setDuracao(serieTO.getDuracao());
            serie.setDistancia(serieTO.getDistancia());
            serie.setSerieRealizada(serieTO.getSerieRealizada());
            fichaId = serie.getAtividadeFicha().getFicha().getCodigo();
            serie = alterar(chave, serie);
            serie.getAtividadeFicha().setCodigo_professor_acompanhamento(usuario.getProfessor().getCodigo());
            atividadeFichaDao.update(chave, serie.getAtividadeFicha());
            return new SerieResponseTO(serie);
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException(SerieExcecoes.ERRO_ALTERAR_SERIE);
        }finally {
        if(!UteisValidacao.emptyNumber(fichaId) && StringUtils.isNotBlank(chave)){
            String username = usuarioService.obterUsernameAlunoPorFicha(chave, fichaId);
            if(StringUtils.isNotBlank(username)){
                filaImpressaoService.enfileirar(chave, TipoObjetoSincronizarEnum.PROGRAMA, username);
            }
        }
    }
    }

    @Override
    public void removerSerie(Integer id) throws ServiceException {
        Integer fichaId = null;
        String chave = null;
        try {
            chave = sessaoService.getUsuarioAtual().getChave();
            Serie serie = obterPorId(chave, id);
            if (serie == null) {
                throw new ServiceException(SerieExcecoes.SERIE_NAO_ENCONTRADA);
            }
            fichaId = serie.getAtividadeFicha().getFicha().getCodigo();
            excluir(chave, serie);
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException(SerieExcecoes.ERRO_EXCLUIR_SERIE);
        } finally {
            if(!UteisValidacao.emptyNumber(fichaId) && StringUtils.isNotBlank(chave)){
                String username = usuarioService.obterUsernameAlunoPorFicha(chave, fichaId);
                if(StringUtils.isNotBlank(username)){
                    filaImpressaoService.enfileirar(chave, TipoObjetoSincronizarEnum.PROGRAMA, username);
                }
            }
        }
    }

    public void espelharSerie(Integer id) throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        try {
            prepare(ctx);
            Serie serie = obterPorId(ctx, id);
            if (serie != null && serie.getAtividadeFicha() != null) {
                for (Serie serieEdit : serie.getAtividadeFicha().getSeries()) {
                    serieEdit.setDistancia(serie.getDistancia());
                    serieEdit.setDuracao(serie.getDuracao());
                    serieEdit.setVelocidade(serie.getVelocidade());
                    serieEdit.setComplemento(serie.getComplemento());
                    serieEdit.setDescanso(serie.getDescanso());
                    serieEdit.setCadencia(serie.getCadencia());
                    serieEdit.setCarga(serie.getCarga());
                    serieEdit.setRepeticao(serie.getRepeticao());

                    alterar(ctx, serieEdit);
                }
            }
        } catch (ServiceException e) {
           throw new ServiceException(SerieExcecoes.ERRO_ESPELHAR_SERIE);
        }
    }


    @Override
    public Serie adicionarSerie(final List<Serie> series, final AtividadeFicha atividadeFicha,
                                final Ficha ficha){
        Serie ultimaSerie = new Serie();
        System.out.println("<========AdicionarSerie START========>");
        atividadeFicha.printSeries();
        ultimaSerie.setDescansoStr(ficha.getDescansoRepetir());
        if (UteisValidacao.emptyList(series)) {
            ultimaSerie.setOrdem(1);
        }else{
            ultimaSerie = series.get(series.size() - 1);
        }
        ultimaSerie.setarSegundos(false);
        Serie serie = new Serie(atividadeFicha,
                new Integer(Uteis.converterMinutosEmSegundos(ultimaSerie.getDuracaoSpin().getValorHora())),
                new Integer(ultimaSerie.getDistanciaSpin().getValorInteiro()),
                new Double(ultimaSerie.getVelocidadeSpin().getValorDouble()),
                new Integer(ultimaSerie.getDescanso()), new Integer(UteisValidacao.emptyNumber(
                ultimaSerie.getOrdem()) ? series.size() + 1 : ultimaSerie.getOrdem() + 1),
                new Integer(ultimaSerie.getRepeticaoSpin().getValorInteiro()),
                new Double(ultimaSerie.getCargaSpin().getValorDouble()),
                ultimaSerie.getComplemento(), ultimaSerie.getCargaComp(), ultimaSerie.getRepeticaoComp(), ultimaSerie.getCadencia());
        atividadeFicha.printSeries();
        System.out.println("<========AdicionarSerie END========>");

        return serie;

    }

    protected void prepare(final String ctx) throws ServiceException {
        EntityManagerService ems = (EntityManagerService) UtilContext.getBean(EntityManagerService.class);
        try {
            ems.closeQuietly(ctx);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

}
