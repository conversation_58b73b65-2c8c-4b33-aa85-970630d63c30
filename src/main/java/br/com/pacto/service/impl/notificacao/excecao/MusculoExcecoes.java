package br.com.pacto.service.impl.notificacao.excecao;

import br.com.pacto.service.exception.ExcecaoSistema;

public enum MusculoExcecoes implements ExcecaoSistema {

    MUSCULO_NAO_ENCONTRADO("musculo_nao_encontrado", "Músculo informado não foi encontrado"),
    ERRO_INCLUIR_MUSCULO("erro_incluir_musculo", "Ocorreu um erro ao incluir o músculo informado"),
    ERRO_ALTERAR_MUSCULO("erro_alterar_musculo", "Ocorreu um erro ao alterar o músculo"),
    ERRO_BUSCAR_MUSCULO("erro_buscar_musculo", "Ocorreu um erro ao pesquisar o músculo informado"),
    ERRO_EXCLUIR_MUSCULO("erro_excluir_musculo", "Ocorreu um erro ao excluir o músculo informado"),
    ERRO_BUSCAR_MUSCULO_ATIVIDADE("erro_buscar_musculo_atividade", "Ocorreu um erro ao pesquisar a atividade do músculo"),
    MUSCULO_ATIVIDADE_NAO_ENCONTRADO("musculo_atividade_nao_encontrado", "Atividade do músculo informada não foi encontrada"),
    ERRO_MUSCULO_JA_EXISTE("erro_musculo_ja_existe", "registro_duplicado");

    private String chave;
    private String descricao;

    MusculoExcecoes(String chave, String descricao) {
        this.chave = chave;
        this.descricao = descricao;
    }

    @Override
    public String getChaveExcecao() {
        return chave;
    }

    @Override
    public String getDescricaoExcecao() {
        return descricao;
    }
}
