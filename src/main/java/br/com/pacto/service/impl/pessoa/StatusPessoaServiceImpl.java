/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.impl.pessoa;

import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.bean.notificacao.TipoNotificacaoEnum;
import br.com.pacto.bean.pessoa.StatusPessoa;
import br.com.pacto.bean.pessoa.TipoStatusEnum;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.dao.intf.pessoa.StatusPessoaDao;
import br.com.pacto.dao.intf.usuario.UsuarioDao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.intf.Validacao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.agenda.AgendamentoService;
import br.com.pacto.service.intf.notificacao.NotificacaoService;
import br.com.pacto.service.intf.pessoa.StatusPessoaService;
import br.com.pacto.service.intf.programa.ProgramaTreinoService;
import br.com.pacto.util.ViewUtils;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR>
 */
@Service
public class StatusPessoaServiceImpl implements StatusPessoaService {

    @Autowired
    private ViewUtils viewUtils;
    @Autowired
    private Validacao validacao;
    @Autowired
    private StatusPessoaDao statusPessoaDao;
    @Autowired
    private UsuarioDao usuarioDao;
    @Autowired
    private NotificacaoService notfService;
    @Autowired
    private ProgramaTreinoService programaService;
    @Autowired
    private AgendamentoService agendamentoService;

    public ViewUtils getViewUtils() {
        return viewUtils;
    }

    public void setViewUtils(ViewUtils viewUtils) {
        this.viewUtils = viewUtils;
    }

    public Validacao getValidacao() {
        return validacao;
    }

    public void setValidacao(Validacao validacao) {
        this.validacao = validacao;
    }

    public StatusPessoaDao getStatusPessoaDao() {
        return this.statusPessoaDao;
    }

    public void setStatusPessoaDao(StatusPessoaDao statusPessoaDao) {
        this.statusPessoaDao = statusPessoaDao;
    }

    public StatusPessoa alterar(final String ctx, StatusPessoa object) throws ServiceException {
        try {
            return getStatusPessoaDao().update(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    public void excluir(final String ctx, StatusPessoa object) throws ServiceException {
        try {
            getStatusPessoaDao().delete(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    public StatusPessoa inserir(final String ctx, StatusPessoa object) throws ServiceException {
        try {
            return getStatusPessoaDao().insert(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    public StatusPessoa obterObjetoPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException {
        try {
            return getStatusPessoaDao().findObjectByParam(ctx, query, params);
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    public StatusPessoa obterPorId(final String ctx, Integer id) throws ServiceException {
        try {
            return getStatusPessoaDao().findById(ctx, id);
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    public List<StatusPessoa> obterPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException {
        try {
            return getStatusPessoaDao().findByParam(ctx, query, params);
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    public List<StatusPessoa> obterPorParam(final String ctx, String query,
            Map<String, Object> params, int max, int index)
            throws ServiceException {
        try {
            return getStatusPessoaDao().findByParam(ctx, query, params, max, index);
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    public List<StatusPessoa> obterTodos(final String ctx) throws ServiceException {
        try {
            return getStatusPessoaDao().findAll(ctx);
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    public void atualizarStatusAluno(final String ctx, final ClienteSintetico cliente, boolean saiu, Date dataInicio, Empresa empresa) throws ServiceException {
        if (cliente != null && dataInicio != null) {
            try {
                if (saiu) {
                    StringBuilder jpql = new StringBuilder("select o from ").append(StatusPessoa.class.getSimpleName());
                    jpql.append(" o where usuario.cliente.codigo = :cliente and cast(dataInicioEvento as date) = :dataInicioEvento ");
                    jpql.append("and tipo = :tipo ");
                    jpql.append("order by dataInicioEvento");
                    Map<String, Object> p = new HashMap<String, Object>();
                    p.put("cliente", cliente.getCodigo());
                    p.put("dataInicioEvento", Calendario.getDataComHoraZerada(cliente.getDataUltimoacesso()));
                    p.put("tipo", TipoStatusEnum.ALUNO_NA_ACADEMIA);
                    List<StatusPessoa> lista = getStatusPessoaDao().findByParam(ctx, jpql.toString(), p);
                    if (lista != null && !lista.isEmpty()) {
                        for (StatusPessoa status : lista) {
                            if (status.getDataFimEvento() == null && saiu) {
                                status.setDataFimEvento(Calendario.hoje());
                                getStatusPessoaDao().update(ctx, status);
                            }
                        }
                    }
                } else {
                    StatusPessoa s = new StatusPessoa();
                    s.setDataInicioEvento(dataInicio);
                    s.setDataLancamento(Calendario.hoje());
                    s.setTipo(TipoStatusEnum.ALUNO_NA_ACADEMIA);
                    s.setUsuario(usuarioDao.findObjectByAttribute(ctx, "cliente.codigo", cliente.getCodigo()));
                    s.setEmpresa(empresa);
                    if (s.getUsuario() != null) {
                        inserir(ctx, s);
                        if (!saiu) {
                            notfService.notificarStatus(ctx, s, TipoNotificacaoEnum.ALUNO_CHEGOU);
                        }
                    }
                }
            } catch (Exception ex) {
                throw new ServiceException(ex.getMessage());
            }
        }
    }

    @Override
    public void removerPorUsuario(String ctx, Usuario usuario) throws ServiceException {
        try{
            getStatusPessoaDao().removerPorUsuario(ctx, usuario);
        }catch (Exception e){
            throw  new ServiceException(e);
        }
    }
}