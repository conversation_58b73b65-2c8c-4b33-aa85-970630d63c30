/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.impl.badge;

import br.com.pacto.bean.badge.Badge;
import br.com.pacto.bean.badge.TipoBadgeEnum;
import br.com.pacto.dao.intf.badge.BadgeDao;
import br.com.pacto.service.intf.Validacao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.badge.BadgeService;
import br.com.pacto.util.ViewUtils;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR>
 */
@Service
public class BadgeServiceImpl implements BadgeService {

    @Autowired
    private ViewUtils viewUtils;
    @Autowired
    private Validacao validacao;
    @Autowired
    private BadgeDao badgeDao;

    public ViewUtils getViewUtils() {
        return viewUtils;
    }

    public void setViewUtils(ViewUtils viewUtils) {
        this.viewUtils = viewUtils;
    }

    public Validacao getValidacao() {
        return validacao;
    }

    public void setValidacao(Validacao validacao) {
        this.validacao = validacao;
    }

    public BadgeDao getBadgeDao() {
        return this.badgeDao;
    }

    public void setBadgeDao(BadgeDao badgeDao) {
        this.badgeDao = badgeDao;
    }

    public Badge alterar(final String ctx, Badge object) throws ServiceException {
        try {
            return getBadgeDao().update(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public void excluir(final String ctx, Badge object) throws ServiceException {
        try {
            getBadgeDao().delete(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public Badge inserir(final String ctx, Badge object) throws ServiceException {
        try {
            return getBadgeDao().insert(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public Badge obterObjetoPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException {
        try {
            return getBadgeDao().findObjectByParam(ctx, query, params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public Badge obterPorId(final String ctx, Integer id) throws ServiceException {
        try {
            return getBadgeDao().findById(ctx, id);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<Badge> obterPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException {
        try {
            return getBadgeDao().findByParam(ctx, query, params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<Badge> obterPorParam(final String ctx, String query,
            Map<String, Object> params, int max, int index)
            throws ServiceException {
        try {
            return getBadgeDao().findByParam(ctx, query, params, max, index);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<Badge> obterTodos(final String ctx) throws ServiceException {
        try {
            return getBadgeDao().findListByAttributes(ctx, null, null, "nome", 0);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public void povoarDefault(String ctx) throws ServiceException {
        try {
            Badge carga = new Badge(TipoBadgeEnum.CARGA, 1000, 0, TipoBadgeEnum.CARGA.getNome(), TipoBadgeEnum.CARGA.getDataCriacao());
            getBadgeDao().insertOrGetObjectForName(ctx, carga, "nome");

            Badge distancia = new Badge(TipoBadgeEnum.DISTANCIA, 5000, 0, TipoBadgeEnum.DISTANCIA.getNome(), TipoBadgeEnum.DISTANCIA.getDataCriacao());
            getBadgeDao().insertOrGetObjectForName(ctx, distancia, "nome");

            Badge frequencia = new Badge(TipoBadgeEnum.FREQUENCIA, 80, 80, TipoBadgeEnum.FREQUENCIA.getNome(), TipoBadgeEnum.FREQUENCIA.getDataCriacao());
            getBadgeDao().insertOrGetObjectForName(ctx, frequencia, "nome");
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<Badge> consultarPorAluno(final String ctx, Integer aluno) throws ServiceException {
        try {
            StringBuilder query = new StringBuilder();
            query.append(" SELECT obj.badge FROM ClienteBadge obj WHERE obj.cliente.codigo = :aluno");
            Map<String, Object> p = new HashMap<String, Object>();
            p.put("aluno", aluno);
            return obterPorParam(ctx, query.toString(), p);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }
}