/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.impl.linhadotempo;

import br.com.pacto.base.util.ExecuteRequestHttpService;
import br.com.pacto.bean.Agendamento;
import br.com.pacto.bean.avaliacao.AvaliacaoFisica;
import br.com.pacto.bean.cliente.ClienteBadge;
import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.cliente.HistoricoNivelCliente;
import br.com.pacto.bean.notificacao.GravidadeNotificacaoEnum;
import br.com.pacto.bean.notificacao.Notificacao;
import br.com.pacto.bean.programa.HistoricoRevisaoProgramaTreino;
import br.com.pacto.bean.programa.ProgramaTreino;
import br.com.pacto.bean.serie.SerieRealizada;
import br.com.pacto.bean.serie.TreinoRealizado;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.bean.wod.ScoreTreino;
import br.com.pacto.controller.json.base.SuperControle;
import br.com.pacto.dao.intf.agenda.AgendamentoDao;
import br.com.pacto.dao.intf.avaliacao.AvaliacaoFisicaDao;
import br.com.pacto.dao.intf.cliente.ClienteBadgeDao;
import br.com.pacto.dao.intf.cliente.HistoricoNivelClienteDao;
import br.com.pacto.dao.intf.notificacao.NotificacaoDao;
import br.com.pacto.dao.intf.programa.HistoricoRevisaoProgramaTreinoDao;
import br.com.pacto.dao.intf.programa.ProgramaTreinoDao;
import br.com.pacto.dao.intf.serie.TreinoRealizadoDao;
import br.com.pacto.dao.intf.wod.ScoreTreinoDao;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.log.LogTO;
import br.com.pacto.service.intf.ficha.SerieService;
import br.com.pacto.service.intf.linhadotempo.LinhaDoTempoService;
import br.com.pacto.service.intf.log.LogService;
import br.com.pacto.service.intf.programa.ProgramaTreinoService;
import br.com.pacto.service.intf.usuario.UsuarioService;
import br.com.pacto.service.intf.empresa.EmpresaService;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;
import br.com.pacto.util.bean.GenericoTO;
import br.com.pacto.util.bean.ItemHistoricoExecucoesTO;
import br.com.pacto.util.enumeradores.PaletaCoresEnum;
import br.com.pacto.util.impl.Ordenacao;
import br.com.pacto.util.json.AgendaTotalJSON;
import br.com.pacto.util.json.FiltrosLinhaTempo;
import br.com.pacto.util.json.LinhaDoTempoTO;
import br.com.pacto.util.json.TipoLinhaEnum;
import org.apache.http.NameValuePair;
import org.apache.http.client.ResponseHandler;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.BasicResponseHandler;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import servicos.integracao.zw.IntegracaoTurmasWSConsumer;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 *
 * <AUTHOR>
 */
@Service
public class LinhaDoTempoServiceImpl implements LinhaDoTempoService {

    @Autowired
    private TreinoRealizadoDao treinoRealizadoDao;
    @Autowired
    private AgendamentoDao agendamentoDao;
    @Autowired
    private NotificacaoDao notificacaoDao;
    @Autowired
    private HistoricoNivelClienteDao historicoNivelDao;
    @Autowired
    private ClienteBadgeDao clienteBadgeDao;
    @Autowired
    private HistoricoRevisaoProgramaTreinoDao historicoRevisaoDao;
    @Autowired
    private ProgramaTreinoDao programaDao;
    @Autowired
    private SerieService serieService;
    @Autowired
    private ProgramaTreinoService programaService;
    @Autowired
    private ScoreTreinoDao scoreTreinoDao;
    @Autowired
    private LogService logService;
    @Autowired
    private UsuarioService usuarioService;

    @Autowired
    private AvaliacaoFisicaDao avaliacaoFisicaDao;

    @Autowired
    private EmpresaService empresaService;

    public HistoricoRevisaoProgramaTreinoDao getHistoricoRevisaoDao() {
        return historicoRevisaoDao;
    }

    public ProgramaTreinoDao getProgramaDao() {
        return programaDao;
    }

    public ScoreTreinoDao getScoreTreinoDao() {
        return scoreTreinoDao;
    }

    public AvaliacaoFisicaDao getAvaliacaoFisicaDao() {
        return avaliacaoFisicaDao;
    }

    public ClienteBadgeDao getClienteBadgeDao() {
        return clienteBadgeDao;
    }

    public HistoricoNivelClienteDao getHistoricoNivelDao() {
        return historicoNivelDao;
    }

    public NotificacaoDao getNotificacaoDao() {
        return notificacaoDao;
    }

    public AgendamentoDao getAgendamentoDao() {
        return agendamentoDao;
    }

    public TreinoRealizadoDao getTreinoRealizadoDao() {
        return treinoRealizadoDao;
    }

    private void addMes(Map<String, LinhaDoTempoTO> mapa, Date data, String timeZone) {
        Date dataComTimeZone = aplicarTimeZone(data, timeZone);
        LinhaDoTempoTO linha = mapa.get(Uteis.getMesNomeReferencia(dataComTimeZone) + "/" + Uteis.getAnoData(dataComTimeZone));
        if (linha == null) {
            LinhaDoTempoTO itemMes = new LinhaDoTempoTO();
            itemMes.setDivisaoMes(Boolean.TRUE);
            itemMes.setTipo(Uteis.getDataAplicandoFormatacao(dataComTimeZone, "yyyyMM") + "999999");
            itemMes.setTitulo(Uteis.getDataAplicandoFormatacao(dataComTimeZone, "MMMM/yyyy"));
            mapa.put(Uteis.getMesNomeReferencia(dataComTimeZone) + "/" + Uteis.getAnoData(dataComTimeZone), itemMes);
        }
    }

    private Date aplicarTimeZone(Date data, String timeZone) {
        if (data == null || timeZone == null) {
            return data;
        }
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");
            sdf.setTimeZone(java.util.TimeZone.getTimeZone(timeZone));
            String dataFormatada = sdf.format(data);
            return sdf.parse(dataFormatada);
        } catch (Exception e) {
            return data;
        }
    }

    @Override
    public List<LinhaDoTempoTO> montarLinha(String ctx,
            ClienteSintetico cliente,
            FiltrosLinhaTempo filtros,
            Date inicio, Date fim) throws ServiceException {
        try {
            List<LinhaDoTempoTO> linha = new ArrayList<LinhaDoTempoTO>();
            Map<String, LinhaDoTempoTO> mapaMeses = new HashMap<String, LinhaDoTempoTO>();
            List<ProgramaTreino> programasPorCliente = obterProgramasPorCliente(ctx, cliente.getCodigo(), inicio, fim, true);
            Map<String, List<LinhaDoTempoTO>> mapaOutrasUnidades = marcacoesOutrasUnidades(ctx, inicio, fim, cliente);

            String timeZone = null;
            try {
                timeZone = empresaService.obterFusoHorarioEmpresa(ctx, cliente.getEmpresa());
            } catch (Exception e) {
                Uteis.logar(e, this.getClass());
                timeZone = "America/Sao_Paulo";
            }
            for (GenericoTO filtroTipo : filtros.getTipos()) {
                if (filtroTipo.getEscolhido()) {
                    switch (filtroTipo.getTipoLinha()) {
                        case AGENDAMENTO:
                            try{
                            List<Agendamento> agendamentos = obterAgendamentos(ctx, inicio, fim, cliente.getCodigo());
                            for (Agendamento agendamento : agendamentos) {
                                LinhaDoTempoTO item = new LinhaDoTempoTO();
                                item.setTipo(TipoLinhaEnum.AGENDAMENTO.name());
                                item.setData(agendamento.getInicio());
                                item.setSubtitulo(TipoLinhaEnum.AGENDAMENTO.getSubtitulo());
                                item.setHorario(Uteis.getDataAplicandoFormatacao(agendamento.getInicio(), "HH:mm")
                                        + " - " + Uteis.getDataAplicandoFormatacao(agendamento.getFim(), "HH:mm"));
                                item.setCodigoPessoaProfessor(agendamento.getProfessor().getCodigoPessoa());
                                item.setNomeProfessor(agendamento.getProfessor().getNome());
                                item.setTitulo(Uteis.firstLetterUpper(agendamento.getTipoEvento().getNome().toLowerCase()));
                                item.setClasseCss(TipoLinhaEnum.AGENDAMENTO.getPaleta());
                                item.setStatus("Agendamento: "+agendamento.getStatus().getDescricao().toLowerCase());
                                linha.add(item);
                                addMes(mapaMeses, item.getData(), timeZone);
                            }
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                            break;
                        case TREINOU:
                            try{
                            List<TreinoRealizado> treinosRealizado = programaService.obterTreinosRealizados(ctx, null, inicio, fim, cliente.getCodigo());
                            for (TreinoRealizado treino : treinosRealizado) {
                                LinhaDoTempoTO item = new LinhaDoTempoTO();
                                item.setSubtitulo(treino.monteSubtitulo());
                                item.setHorario(Uteis.getDataAplicandoFormatacao(treino.getDataInicio(), "HH:mm"));
                                item.setData(treino.getDataInicio());
                                item.setTipo(TipoLinhaEnum.TREINOU.name());
                                item.setTitulo(TipoLinhaEnum.TREINOU.name());
                                item.setClasseCss(TipoLinhaEnum.TREINOU.getPaleta());
                                item.setItensExecucoes(montarSeries(ctx, treino));
                                item.setNomeFichaExecucao(treino.getProgramaTreinoFicha().getFicha().getNome());
                                item.setOrigem(treino.getOrigem().getNome());
                                if(treino.getUnidadeExecucao() != null && !treino.getUnidadeExecucao().isEmpty()){
                                    item.setOrigem(treino.getOrigem().getNome() + " (Unidade: "+ treino.getUnidadeExecucao() +")");
                                }
                                linha.add(item);
                                addMes(mapaMeses, item.getData(), timeZone);
                            }
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                            break;
                        case NOTIFICACAO:
                            try{
                            List<Notificacao> notificacoes = obterNotificacoes(ctx, cliente.getCodigo(), inicio, fim);
                            for (Notificacao notificacao : notificacoes) {
                                LinhaDoTempoTO item = new LinhaDoTempoTO();
                                item.setSubtitulo(notificacao.getTipo().getDescricao());
                                item.setHorario(Uteis.getDataAplicandoFormatacao(notificacao.getDataRegistro(), "HH:mm"));
                                item.setTipo(TipoLinhaEnum.NOTIFICACAO.name());
                                item.setData(notificacao.getDataRegistro());
                                if (notificacao.getProfessor() != null) {
                                    item.setCodigoPessoaProfessor(notificacao.getProfessor().getCodigoPessoa());
                                    item.setNomeProfessor(notificacao.getProfessor().getNome());
                                }
                                item.setTitulo(TipoLinhaEnum.NOTIFICACAO.name());
                                item.setClasseCss(notificacao.getTipo().getGravidade() == null
                                        || notificacao.getTipo().getGravidade().equals(GravidadeNotificacaoEnum.LEVE) ? PaletaCoresEnum.VERDE_D
                                        : notificacao.getTipo().getGravidade().equals(GravidadeNotificacaoEnum.MEDIA) ? PaletaCoresEnum.AMARELO_B
                                        : PaletaCoresEnum.VERMELHO_C);
                                linha.add(item);
                                addMes(mapaMeses, item.getData(), timeZone);
                            }
                            } catch (Exception e) {
                                    e.printStackTrace();
                                }
                                break;

                        case MUDOU_DE_NIVEL:
                            try{
                            List<HistoricoNivelCliente> historicoNivel = obterHistoricoNivel(ctx, cliente.getCodigo(), inicio, fim);
                            historicoNivel = Ordenacao.ordenarLista(historicoNivel, "inicio");
                            String ultimoNivel = null;
                            for (HistoricoNivelCliente historico : historicoNivel) {
                                LinhaDoTempoTO item = new LinhaDoTempoTO();
                                item.setNivelNovo(historico.getNivel() == null ? "" : historico.getNivel().getNome());
                                item.setNivelAnterior(ultimoNivel);
                                ultimoNivel = historico.getNivel() == null ? "" : historico.getNivel().getNome();
                                item.setTipo(TipoLinhaEnum.MUDOU_DE_NIVEL.name());
                                item.setData(historico.getInicio());
                                item.setTitulo(TipoLinhaEnum.MUDOU_DE_NIVEL.name());
                                item.setClasseCss(TipoLinhaEnum.MUDOU_DE_NIVEL.getPaleta());
                                linha.add(item);
                                addMes(mapaMeses, item.getData(), timeZone);
                            }
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                            break;
                        case REVISOU_TREINO:
                            try{
                            List<HistoricoRevisaoProgramaTreino> revisoesTreino = obterRevisoesTreino(ctx, cliente.getCodigo(),
                                    inicio, fim);
                            for (HistoricoRevisaoProgramaTreino revisao : revisoesTreino) {
                                LinhaDoTempoTO item = new LinhaDoTempoTO();
                                item.setTipo(TipoLinhaEnum.REVISOU_TREINO.name());
                                item.setData(revisao.getDataRegistro());
                                item.setSubtitulo("Programa de treino: " + revisao.getPrograma().getNome());
                                item.setTitulo(TipoLinhaEnum.REVISOU_TREINO.name());
                                item.setClasseCss(TipoLinhaEnum.REVISOU_TREINO.getPaleta());
                                item.setCodigoPessoaProfessor(revisao.getProfessorRevisou().getCodigoPessoa());
                                item.setNomeProfessor(revisao.getProfessorRevisou().getNome());
                                linha.add(item);
                                addMes(mapaMeses, item.getData(), timeZone);
                            }
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                            break;
                        case MONTOU_TREINO:
                            try{
                            for (ProgramaTreino programa : programasPorCliente) {
                                if (programa.getProgramaTreinoRenovacao() == null) {
                                    LinhaDoTempoTO item = new LinhaDoTempoTO();
                                    item.setSubtitulo("Programa de treino: " + programa.getNome());
                                    item.setData(programa.getDataLancamento());
                                    TipoLinhaEnum tipo = TipoLinhaEnum.MONTOU_TREINO;
                                    item.setTipo(tipo.name());
                                    item.setTitulo(tipo.name());
                                    item.setCodigoPessoaProfessor(programa.getProfessorMontou() != null ? programa.getProfessorMontou().getCodigoPessoa() : null);
                                    item.setNomeProfessor(programa.getProfessorMontou() != null ? programa.getProfessorMontou().getNome() : null);
                                    item.setClasseCss(tipo.getPaleta());
                                    linha.add(item);
                                    addMes(mapaMeses, item.getData(), timeZone);
                                }
                            }
                            } catch (Exception e) {
                                    e.printStackTrace();
                                }
                                break;
                        case RENOVOU_TREINO:
                            try{
                            for (ProgramaTreino programa : programasPorCliente) {
                                if (programa.getProgramaTreinoRenovacao() != null) {
                                    LinhaDoTempoTO item = new LinhaDoTempoTO();
                                    item.setSubtitulo("Programa de treino: " + programa.getNome());
                                    item.setData(programa.getDataLancamento());
                                    TipoLinhaEnum tipo = TipoLinhaEnum.RENOVOU_TREINO;
                                    item.setTipo(tipo.name());
                                    item.setTitulo(tipo.name());
                                    item.setCodigoPessoaProfessor(programa.getProfessorMontou() != null ? programa.getProfessorMontou().getCodigoPessoa() : null);
                                    item.setNomeProfessor(programa.getProfessorMontou() != null ?  programa.getProfessorMontou().getNome() : null);
                                    item.setClasseCss(tipo.getPaleta());
                                    linha.add(item);
                                    addMes(mapaMeses, item.getData(), timeZone);
                                }
                            }
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                            break;
                        case ACABOU_TREINO:
                            try{
                            List<ProgramaTreino> programasAcabando = obterProgramasPorCliente(ctx, cliente.getCodigo(), inicio, fim, false);
                            for (ProgramaTreino programa : programasAcabando) {
                                    LinhaDoTempoTO item = new LinhaDoTempoTO();
                                    item.setSubtitulo("Programa de treino: " + programa.getNome());
                                    item.setData(programa.getDataTerminoPrevisto());
                                    TipoLinhaEnum tipo = TipoLinhaEnum.ACABOU_TREINO;
                                    item.setTipo(tipo.name());
                                    item.setTitulo(tipo.name());
                                    item.setCodigoPessoaProfessor(programa.getProfessorMontou() != null ? programa.getProfessorMontou().getCodigoPessoa() : null);
                                    item.setNomeProfessor(programa.getProfessorMontou() != null ? programa.getProfessorMontou().getNome() : null);
                                    item.setClasseCss(tipo.getPaleta());
                                    linha.add(item);
                                    addMes(mapaMeses, item.getData(), timeZone);
                            }
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                            break;
                        case GANHOU_BADGE:
                            try{
                            List<ClienteBadge> badges = obterBadges(ctx, cliente.getCodigo(), inicio, fim);
                            for (ClienteBadge badge : badges) {
                                LinhaDoTempoTO item = new LinhaDoTempoTO();
                                item.setSubtitulo(badge.getBadge().getDescricao());
                                item.setData(badge.getDataConquista());
                                TipoLinhaEnum tipo = TipoLinhaEnum.GANHOU_BADGE;
                                item.setTipo(tipo.name());
                                item.setNomeBadge(badge.getBadge().getNome());
                                item.setTitulo(tipo.name());
                                item.setClasseCss(tipo.getPaleta());
                                item.setUrlBadge(badge.getBadge().getTipo().getImg());
                                linha.add(item);
                                addMes(mapaMeses, item.getData(), timeZone);
                            }
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                            break;
                        case AULA_DESMARCADA:
                            try {
                                if (!SuperControle.independente(ctx)) {
                                    JSONObject content = chamadaZW(ctx,
                                            "/prest/aulacheia/desmarcacoes-aluno",
                                            inicio.getTime(),
                                            fim.getTime(),
                                            cliente.getCodigoCliente());
                                    JSONArray jsonArray = content.getJSONArray("content");
                                    for (int i = 0; i < jsonArray.length(); i++) {
                                        JSONObject jsonObject = jsonArray.getJSONObject(i);
                                        long dia = jsonObject.getLong("dia");
                                        LinhaDoTempoTO item = new LinhaDoTempoTO();
                                        item.setTipo(TipoLinhaEnum.AULA_DESMARCADA.name());
                                        item.setData(Calendario.getDataComHora(new Date(dia), jsonObject.getString("horario")));
                                        item.setSubtitulo(jsonObject.getString("turma"));
                                        item.setHorario(jsonObject.getString("horario"));
                                        item.setNomeProfessor(jsonObject.optString("usuario"));
                                        item.setTitulo(TipoLinhaEnum.AULA_DESMARCADA.name());
                                        item.setClasseCss(TipoLinhaEnum.AULA_DESMARCADA.getPaleta());

                                        //inserir uma marcação antes
                                        LinhaDoTempoTO itemMarcacao = new LinhaDoTempoTO();
                                        itemMarcacao.setTipo(TipoLinhaEnum.FEZ_AULA.name());
                                        itemMarcacao.setData(item.getData());
                                        itemMarcacao.setSubtitulo(item.getSubtitulo());
                                        itemMarcacao.setHorario(jsonObject.getString("horario"));
                                        itemMarcacao.setTitulo(TipoLinhaEnum.FEZ_AULA.name());
                                        itemMarcacao.setClasseCss(TipoLinhaEnum.FEZ_AULA.getPaleta());
                                        linha.add(itemMarcacao);
                                        linha.add(item);
                                        addMes(mapaMeses, item.getData(), timeZone);
                                    }

                                    if(mapaOutrasUnidades.get("desmarcou") != null){
                                        for(LinhaDoTempoTO item : mapaOutrasUnidades.get("desmarcou")){
                                            item.setTipo(TipoLinhaEnum.AULA_DESMARCADA.name());
                                            item.setTitulo(TipoLinhaEnum.AULA_DESMARCADA.name());
                                            item.setClasseCss(TipoLinhaEnum.AULA_DESMARCADA.getPaleta());
                                            linha.add(item);
                                            addMes(mapaMeses, item.getData(), timeZone);
                                        }
                                    }
                                }
                            }catch (Exception e){
                                e.printStackTrace();
                            }
                            break;
                        case FEZ_AULA:
                            try {
                                if(!SuperControle.independente(ctx)) {
                                    IntegracaoTurmasWSConsumer integracaoWS = UtilContext.getBean(IntegracaoTurmasWSConsumer.class);
                                    List<AgendaTotalJSON> agendaTotalJSONS = integracaoWS.obterAulasAlunoPeriodo(ctx, cliente.getMatricula(), inicio, fim);
                                    for (AgendaTotalJSON aula : agendaTotalJSONS) {
                                        LinhaDoTempoTO item = new LinhaDoTempoTO();
                                        item.setTipo(TipoLinhaEnum.FEZ_AULA.name());
                                        item.setData(Uteis.getDate(aula.getInicio(), "dd/MM/yyyy HH:mm"));
                                        item.setSubtitulo(aula.getTitulo());
                                        try {
                                            item.setHorario(aula.getInicio().split(" ")[1]);
                                        } catch (Exception e) {
                                            item.setHorario(aula.getInicio());
                                        }

                                        item.setNomeProfessor(aula.getResponsavel());
                                        item.setTitulo(TipoLinhaEnum.FEZ_AULA.name());
                                        item.setClasseCss(TipoLinhaEnum.FEZ_AULA.getPaleta());
//                                item.setStatus();
                                        linha.add(item);
                                        addMes(mapaMeses, item.getData(), timeZone);
                                    }

                                    if(mapaOutrasUnidades.get("marcou") != null){
                                        for(LinhaDoTempoTO item : mapaOutrasUnidades.get("marcou")){
                                            item.setTipo(TipoLinhaEnum.FEZ_AULA.name());
                                            item.setTitulo(TipoLinhaEnum.FEZ_AULA.name());
                                            item.setClasseCss(TipoLinhaEnum.FEZ_AULA.getPaleta());
                                            linha.add(item);
                                            addMes(mapaMeses, item.getData(), timeZone);
                                        }
                                    }
                                }

                            } catch (Exception e) {
                                Uteis.logar(e, this.getClass());
                            }
                            break;
                        case REALIZOU_AVALIACAO:
                            try{
                            List<AvaliacaoFisica> avaliacaoFisicas = obterAvalicoesFisicas(ctx, cliente.getCodigo(), inicio, fim);
                            for (AvaliacaoFisica af : avaliacaoFisicas) {
                                LinhaDoTempoTO item = new LinhaDoTempoTO();
                                Usuario usu = usuarioService.obterPorId(ctx, af.getResponsavelLancamento_codigo());
                                item.setSubtitulo("Avalicao fisíca lançada por " + usu.getNomeProfessor());
                                item.setData(af.getDataAvaliacao());
                                item.setTipo(filtroTipo.getTipoLinha().name());
                                item.setTitulo(filtroTipo.getTipoLinha().name());
                                item.setClasseCss(filtroTipo.getTipoLinha().getPaleta());

                                linha.add(item);
                                addMes(mapaMeses, item.getData(), timeZone);
                            }
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                            break;
                        case REGISTROU_WOD:
                            try{
                            List<ScoreTreino> scoreTreinos = obterScoresTreinoPorCliente(ctx, cliente.getCodigo(), inicio, fim);
                            for (ScoreTreino st : scoreTreinos) {
                                LinhaDoTempoTO item = new LinhaDoTempoTO();
                                item.setSubtitulo("Registrou resultado de wod: " + st.getWod().getNome());
                                item.setData(st.getLancamento());
                                item.setTipo(filtroTipo.getTipoLinha().name());
                                item.setTitulo(filtroTipo.getTipoLinha().name());
                                item.setClasseCss(filtroTipo.getTipoLinha().getPaleta());
                                linha.add(item);
                                addMes(mapaMeses, item.getData(), timeZone);
                            }
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                            break;
//                          refatorar posteriormente
//                        case ALTERACAO_AGENDAMENTO_SERVICOS:
//                            JSONArray jsonArray = new JSONArray();
//                            JSONObject filtroAltAgendamentoServ = new JSONObject();
//                            filtroAltAgendamentoServ.put("tipo", jsonArray.put("UPDATE"));
//                            filtroAltAgendamentoServ.put("cliente", cliente.getCodigo());
//                            filtroAltAgendamentoServ.put("dataInicio", inicio.getTime());
//                            filtroAltAgendamentoServ.put("dataFim", fim.getTime());
//                            try{
//                            List<LogTO> logsTo = logService.listarLogDisponibilidades(filtroAltAgendamentoServ, null, true, null);
//                            for (LogTO logTO : logsTo) {
//                                String[] descricoes = logTO.getDescricao().split("<br/>");
//                                for (String descricao : descricoes) {
//                                    if (descricao.contains("status")) {
//                                        LinhaDoTempoTO item = new LinhaDoTempoTO();
//                                        String str = descricao.replace("[status:", "").replace("]", "");
//                                        item.setSubtitulo("Status do agendamento " + logTO.getIdentificador().split(" - ")[1] + " alterado de " + str);
//                                        item.setData(new SimpleDateFormat("dd/MM/yyyy HH:mm").parse(logTO.getIdentificador().split(" - ")[2]));
//                                        item.setTipo(filtroTipo.getTipoLinha().name());
//                                        item.setTitulo(filtroTipo.getTipoLinha().name());
//                                        item.setClasseCss(filtroTipo.getTipoLinha().getPaleta());
//                                        linha.add(item);
//                                        addMes(mapaMeses, item.getData());
//                                    }
//                                }
//                            }
//                            } catch (Exception e) {
//                                e.printStackTrace();
//                            }
//                            break;
                        case AGENDOU_BOOKING_GYMPASS:
                            if (!SuperControle.independente(ctx)) {
                                JSONObject contentAgendamentos = chamadaZW(ctx,
                                        "/prest/aulacheia/linha-tempo-agendado-booking",
                                        inicio.getTime(),
                                        fim.getTime(),
                                        cliente.getCodigoCliente());
                                JSONArray jsonArrayAgendamentos = contentAgendamentos.getJSONArray("content");
                                for (int i = 0; i < jsonArrayAgendamentos.length(); i++) {
                                    JSONObject jsonObject = jsonArrayAgendamentos.getJSONObject(i);
                                    LinhaDoTempoTO item = new LinhaDoTempoTO();
                                    if (jsonObject.has("origemsistema") && jsonObject.getString("origemsistema").equals("Site Vendas")) {
                                        item.setSubtitulo("Link de visitantes");
                                    } else {
                                        item.setSubtitulo("Agendou aula via Booking Gympass: " + jsonObject.getString("identificadorturma"));
                                    }
                                    item.setData(new Date(jsonObject.getLong("data")));
                                    item.setTipo(filtroTipo.getTipoLinha().name());
                                    item.setTitulo(filtroTipo.getTipoLinha().name());
                                    item.setClasseCss(filtroTipo.getTipoLinha().getPaleta());
                                    linha.add(item);
                                    addMes(mapaMeses, item.getData(), timeZone);
                                }
                            }
                            break;
                    }
                }
            }
            for (String data : mapaMeses.keySet()) {
                linha.add(mapaMeses.get(data));
            }
            linha = Ordenacao.ordenarLista(linha, "valorOrdem");
            Collections.reverse(linha);
            
            return linha;
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e.getMessage());
        }
    }

    private Map<String,List<LinhaDoTempoTO>> marcacoesOutrasUnidades(String chave, Date inicio, Date fim, ClienteSintetico cliente){
        Map<String,List<LinhaDoTempoTO>> mapa = new HashMap(){{
            put("marcou", new ArrayList());
            put("desmarcou", new ArrayList());
        }};
        try {
            JSONObject content = chamadaZW(chave,
                    "/prest/aulacheia/log-outra-unidade",
                    inicio.getTime(),
                    fim.getTime(),
                    cliente.getCodigoPessoa());
            JSONArray logs = content.getJSONArray("content");
            for (int i = 0; i < logs.length(); i++) {
                JSONObject jsonObject = logs.getJSONObject(i);
                LinhaDoTempoTO item = new LinhaDoTempoTO();

                item.setSubtitulo(jsonObject.getString("descricao"));
                item.setData(new Date(jsonObject.getLong("data")));
                if(jsonObject.getString("descricao").toLowerCase().startsWith("excl")){
                    mapa.get("desmarcou").add(item);
                } else {
                    mapa.get("marcou").add(item);
                }
            }
        }catch (Exception e){
            Uteis.logar(e, LinhaDoTempoServiceImpl.class);
        }
        return mapa;
    }

    private List<HistoricoNivelCliente> obterHistoricoNivel(final String ctx, final Integer cliente,
            final Date inicio, final Date fim) throws ServiceException {
        try {
            Map<String, Object> p = new HashMap<String, Object>();
            String sql = "SELECT obj FROM HistoricoNivelCliente obj WHERE obj.cliente.codigo = :cliente";
            if (inicio != null && fim != null) {
                sql += " and obj.inicio between :inicio and :fim ";
                p.put("inicio", Calendario.getDataComHora(inicio, "00:00:00"));
                p.put("fim", Calendario.getDataComHora(fim, "23:59:59"));
            }

            p.put("cliente", cliente);
            return getHistoricoNivelDao().findByParam(ctx, sql, p);
        } catch (Exception ex) {
            throw new ServiceException(ex);

        }
    }

    private List<HistoricoRevisaoProgramaTreino> obterRevisoesTreino(final String ctx, final Integer codigoCliente,
            Date inicio, Date fim) throws ServiceException {
        try {
            Map<String, Object> p = new HashMap<String, Object>();
            String sql = "SELECT obj FROM HistoricoRevisaoProgramaTreino obj WHERE obj.cliente.codigo = :cliente";
            if (inicio != null && fim != null) {
                sql += " and obj.dataRegistro between :inicio and :fim ";
                p.put("inicio", Calendario.getDataComHora(inicio, "00:00:00"));
                p.put("fim", Calendario.getDataComHora(fim, "23:59:59"));
            }
            p.put("cliente", codigoCliente);
            return historicoRevisaoDao.findByParam(ctx, sql, p);
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    private List<TreinoRealizado> obterTreinosRealizados(final String ctx,
            Date inicio, Date fim,
            final Integer codigoCliente)
            throws ServiceException {
        Map<String, Object> params = new HashMap<String, Object>();
        StringBuilder s = new StringBuilder(" where obj.cliente.codigo = :codigoCliente  ");
        if (inicio != null && fim != null) {
            s.append(" and obj.dataInicio between :inicio and :fim ");
            params.put("inicio", Calendario.getDataComHora(inicio, "00:00:00"));
            params.put("fim", Calendario.getDataComHora(fim, "23:59:59"));
        }
        params.put("codigoCliente", codigoCliente);
        s.append(" ORDER BY obj.dataInicio DESC");
        try {
            return getTreinoRealizadoDao().findByParam(ctx, s, params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    private List<Agendamento> obterAgendamentos(final String ctx, final Date inicio, final Date fim, final Integer idCliente) throws ServiceException {
        try {
            Map<String, Object> params = new HashMap<String, Object>();
            StringBuilder sql = new StringBuilder("SELECT obj FROM Agendamento obj ");
            sql.append(" WHERE cliente.codigo = :cliente ");
            if (inicio != null && fim != null) {
                sql.append(" AND obj.inicio >= :inicio AND obj.fim <= :fim");
                params.put("inicio", inicio);
                params.put("fim", fim);
            }
            params.put("cliente", idCliente);

            sql.append(" ORDER BY inicio DESC");
            return getAgendamentoDao().findByParam(ctx, sql.toString(), params);
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    private List<ClienteBadge> obterBadges(final String ctx, final Integer idCliente,
            final Date inicio, final Date fim) throws ServiceException {
        try {
            Map<String, Object> params = new HashMap<String, Object>();
            StringBuilder sql = new StringBuilder("SELECT obj FROM ClienteBadge obj ");
            sql.append(" WHERE cliente.codigo = :cliente ");
            params.put("cliente", idCliente);
            if (inicio != null && fim != null) {
                sql.append(" AND obj.dataConquista >= :inicio AND obj.dataConquista <= :fim");
                params.put("inicio", inicio);
                params.put("fim", fim);
            }
            sql.append(" ORDER BY dataConquista DESC");
            return getClienteBadgeDao().findByParam(ctx, sql.toString(), params);

        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    public List<Notificacao> obterNotificacoes(final String ctx, final Integer idCliente,
            final Date inicio, final Date fim) throws ServiceException {
        try {
            Map<String, Object> params = new HashMap<String, Object>();
            StringBuilder sql = new StringBuilder("SELECT obj FROM Notificacao obj ");
            sql.append(" WHERE cliente.codigo = :cliente ");
            params.put("cliente", idCliente);
            if (inicio != null && fim != null) {
                sql.append(" AND obj.dataRegistro >= :inicio AND obj.dataRegistro <= :fim");
                params.put("inicio", inicio);
                params.put("fim", fim);
            }
            sql.append(" ORDER BY dataRegistro DESC");
            return getNotificacaoDao().findByParam(ctx, sql.toString(), params);
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    public List<ItemHistoricoExecucoesTO> montarSeries(final String ctx, final TreinoRealizado treino) throws ServiceException {
        try {
            List<ItemHistoricoExecucoesTO> lista = new ArrayList<ItemHistoricoExecucoesTO>();
            List<SerieRealizada> seriesTreino = programaService.obterSeriesDoTreino(ctx, treino.getCodigo());
            Map<Integer, ItemHistoricoExecucoesTO> mapa = new HashMap<Integer, ItemHistoricoExecucoesTO>();
            for (SerieRealizada serieRealizada : seriesTreino) {
                ItemHistoricoExecucoesTO item = mapa.get(serieRealizada.getSerie().getAtividadeFicha().getCodigo());
                if (item == null) {
                    item = new ItemHistoricoExecucoesTO();
                    item.setNome(serieRealizada.getSerie().getAtividadeFicha().getNomeTransient());
                    item.setAtividade(serieRealizada.getSerie().getAtividadeFicha().getAtividade());
                    item.setOrdem(serieRealizada.getSerie().getAtividadeFicha().getOrdem());
                    mapa.put(serieRealizada.getSerie().getAtividadeFicha().getCodigo(), item);
                }
                item.setSerieMaisGrave(item.getSerieMaisGrave() == null
                        || item.getSerieMaisGrave().getTipoNotificacao() == null
                        || (serieRealizada.getTipoNotificacao() != null
                        && serieRealizada.getTipoNotificacao().getGravidade().getOrdem() > item.getSerieMaisGrave().getTipoNotificacao().getGravidade().getOrdem())
                        ? serieRealizada : item.getSerieMaisGrave());
                item.getSeriesRealizadas().add(serieRealizada);
            }
            lista.addAll(mapa.values());
            Ordenacao.ordenarLista(lista, "ordem");
            return lista;
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    public List<ProgramaTreino> obterProgramasPorCliente(final String ctx, final Integer codigoCliente, final Date inicio, final Date fim, boolean comecando)
            throws ServiceException {
        try {
            HashMap<String, Object> p = new HashMap<String, Object>();
            StringBuilder query = new StringBuilder();
            query.append(" select obj from ProgramaTreino obj where cliente.codigo = :cliente ");
            if (inicio != null && fim != null && comecando) {
                query.append(" and obj.dataLancamento >= :inicio and obj.dataLancamento <= :fim ");
                p.put("inicio", inicio);
                p.put("fim", fim);
            }
            if (inicio != null && fim != null && !comecando) {
                query.append(" and obj.dataTerminoPrevisto >= :inicio and obj.dataTerminoPrevisto <= :fim ");
                p.put("inicio", inicio);
                p.put("fim", fim);
            }
            query.append(" order by obj.dataTerminoPrevisto desc");
            p.put("cliente", codigoCliente);
            return getProgramaDao().findByParam(ctx, query.toString(), p);

        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<AvaliacaoFisica> obterAvalicoesFisicas(final String ctx, final Integer codigoCliente, final Date inicio, final Date fim)
            throws ServiceException {
        try {
            HashMap<String, Object> p = new HashMap<String, Object>();
            StringBuilder query = new StringBuilder();
            query.append(" select obj from AvaliacaoFisica obj where cliente.codigo = :cliente ");
            if (inicio != null && fim != null) {
                query.append(" and obj.dataAvaliacao >= :inicio and obj.dataAvaliacao <= :fim ");
                p.put("inicio", inicio);
                p.put("fim", fim);
            }

            query.append(" order by obj.dataAvaliacao desc");
            p.put("cliente", codigoCliente);
            return getAvaliacaoFisicaDao().findByParam(ctx, query.toString(), p);

        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public JSONObject chamadaZW(String ctx,
                                String endpoint,
                                Long inicio,
                                Long fim,
                                Integer aluno) throws Exception{
        final String url = Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebInteg);
        CloseableHttpClient client = ExecuteRequestHttpService.createConnector();
        HttpPost httpPost = new HttpPost(url + endpoint);
        List<NameValuePair> params = new ArrayList<>();
        params.add(new BasicNameValuePair("chave", ctx));
        params.add(new BasicNameValuePair("inicio", inicio.toString()));
        params.add(new BasicNameValuePair("fim", fim.toString()));
        params.add(new BasicNameValuePair("cliente", aluno.toString()));
        httpPost.setEntity(new UrlEncodedFormEntity(params));
        CloseableHttpResponse response = client.execute(httpPost);
        ResponseHandler<String> handler = new BasicResponseHandler();
        String body = handler.handleResponse(response);
        client.close();
        return new JSONObject(body);
    }

    public List<ScoreTreino> obterScoresTreinoPorCliente(final String ctx,
                                                         final Integer codigoCliente,
                                                         final Date inicio,
                                                         final Date fim) throws ServiceException {
        try {
            HashMap<String, Object> p = new HashMap<String, Object>();
            StringBuilder query = new StringBuilder();
            query.append("SELECT obj FROM ScoreTreino obj \n");
            query.append("WHERE usuario.cliente.codigo = :cliente \n");
            if (inicio != null && fim != null) {
                query.append("AND obj.lancamento >= :inicio AND obj.lancamento <= :fim \n");
                p.put("inicio", inicio);
                p.put("fim", fim);
            }
            query.append("ORDER BY obj.lancamento DESC");
            p.put("cliente", codigoCliente);
            return getScoreTreinoDao().findByParam(ctx, query.toString(), p);

        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

}
