package br.com.pacto.service.impl.notificacao.excecao;

import br.com.pacto.service.exception.ExcecaoSistema;

/**
 * Created by joa<PERSON> moita on 27/09/2018.
 */
public enum BenchmarkExcecoes implements ExcecaoSistema {

    ERRO_INCLUIR_BENCHMARK("erro_incluir_benchmark", "Ocorreu um erro ao incluir o benchmark"),
    ERRO_BUSCAR_BENCHMARK("erro_buscar_benchmark", "Ocorreu um erro ao buscar o benchmark"),
    ERRO_ALTERAR_BENCHMARK("erro_alterar_benchmark", "Ocorreu um erro ao alterar o benchmark"),

    ERRO_NOME_NAO_INFORMADO("erro_nome_nao_informado", "O nome do benchmark não foi informado!"),
    ERRO_EXERCICIOS_NAO_INFORMADO("erro_exercicios_nao_informado", "O exercicios do benchmark não foi informado!"),
    ERRO_TIPO_BENCHMARK_ID_NAO_INFORMADO("erro_tipo_benchmark_id_nao_informado", "O tipo de benchmark não foi informado!"),
    ERRO_ID_NAO_INFORMADO("erro_id_nao_informado", "O id de benchmark não foi informado!"),

    ERRO_BENCHMARK_NAO_EXISTE("erro_benchmark_nao_existe", "A benchmark informada não existe"),
    ERRO_BENCHMARK_JA_EXISTE("erro_benchmark_ja_existe", "registro_duplicado"),
    ERRO_SALVAR_IMAGEM("erro_salvar_image", "Falha ao salvar imagem, por favor tente novamente mais tarde");

    private String chave;
    private String descricao;

    BenchmarkExcecoes(String chave, String descricao) {
        this.chave = chave;
        this.descricao = descricao;
    }

    @Override
    public String getChaveExcecao() {
        return chave;
    }
    @Override
    public String getDescricaoExcecao() {
        return descricao;
    }
}
