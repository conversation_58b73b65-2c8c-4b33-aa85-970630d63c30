package br.com.pacto.service.impl.gympass.dto;

import org.json.JSONObject;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 22/03/2020
 */
public class InstructorsDTO {

    private String name;
    private boolean substitute;

    public InstructorsDTO() {
    }

    public InstructorsDTO(JSONObject json) {
        this.name = json.optString("name");
        this.substitute = json.optBoolean("substitute");
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public boolean isSubstitute() {
        return substitute;
    }

    public void setSubstitute(boolean substitute) {
        this.substitute = substitute;
    }
}
