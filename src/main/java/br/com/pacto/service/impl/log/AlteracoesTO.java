package br.com.pacto.service.impl.log;

public class AlteracoesTO {

    private String campo;
    private String valorAnterior;
    private String valorAlterado;

    public AlteracoesTO() {
    }

    public AlteracoesTO(String campo, String valorAnterior, String valorAlterado) {
        this.campo = campo;
        this.valorAnterior = valorAnterior == null || valorAnterior.equals("null") ? "vazio" : valorAnterior;
        this.valorAlterado = valorAlterado == null || valorAlterado.equals("null") ? "vazio" : valorAlterado;
    }

    public String getCampo() {
        return campo;
    }

    public void setCampo(String campo) {
        this.campo = campo;
    }

    public String getValorAnterior() {
        return valorAnterior;
    }

    public void setValorAnterior(String valorAnterior) {
        this.valorAnterior = valorAnterior;
    }

    public String getValorAlterado() {
        return valorAlterado;
    }

    public void setValorAlterado(String valorAlterado) {
        this.valorAlterado = valorAlterado;
    }
}
