package br.com.pacto.service.impl.cliente.perfil;

import java.util.List;

public class PerfilAlunoAvaliacaoDTO {

    private Integer totalAvaliacoes;
    private Integer periodoDias;
    private Integer diasProximaAvaliacao;
    private Long dataProxima;
    private Double percentualMassaGorda;
    private Double percentualMassaMagra;
    private Double massaGordaInicial;
    private Double massaGordaAtual;
    private Double evolucaoGeral;
    private Double massaMagraInicial;
    private Double massaMagraAtual;
    private Double nivelGorduraCorporal;
    private Double nivelGorduraCorporalInicial;
    private Double nivelGorduraCorporalFaltando;
    private List<TreinoGruposMuscularesDTO> grupos;
    private List<PesoMassaGordaHistoricoDTO> pesos;
    private List<DobrasAlunoHistoricoDTO> dobras;

    public PerfilAlunoAvaliacaoDTO() {
    }

    public PerfilAlunoAvaliacaoDTO(Integer totalAvaliacoes, Integer periodoDias,
                                   Integer diasProximaAvaliacao, Long dataProxima, Double percentualMassaGorda, Double percentualMassaMagra, Double massaGordaInicial, Double massaGordaAtual, Double evolucaoGeral, Double massaMagraInicial, Double massaMagraAtual, Double nivelGorduraCorporal, Double nivelGorduraCorporalInicial,
                                   Double nivelGorduraCorporalFaltando, List<TreinoGruposMuscularesDTO> grupos,
            List<PesoMassaGordaHistoricoDTO> pesos,
            List<DobrasAlunoHistoricoDTO> dobras) {
        this.totalAvaliacoes = totalAvaliacoes;
        this.periodoDias = periodoDias;
        this.diasProximaAvaliacao = diasProximaAvaliacao;
        this.dataProxima = dataProxima;
        this.percentualMassaGorda = percentualMassaGorda;
        this.percentualMassaMagra = percentualMassaMagra;
        this.massaGordaInicial = massaGordaInicial;
        this.massaGordaAtual = massaGordaAtual;
        this.evolucaoGeral = evolucaoGeral;
        this.massaMagraInicial = massaMagraInicial;
        this.massaMagraAtual = massaMagraAtual;
        this.nivelGorduraCorporal = nivelGorduraCorporal;
        this.nivelGorduraCorporalInicial = nivelGorduraCorporalInicial;
        this.nivelGorduraCorporalFaltando = nivelGorduraCorporalFaltando;
        this.grupos = grupos;
        this.pesos = pesos;
        this.dobras = dobras;
    }

    public Integer getTotalAvaliacoes() {
        return totalAvaliacoes;
    }

    public void setTotalAvaliacoes(Integer totalAvaliacoes) {
        this.totalAvaliacoes = totalAvaliacoes;
    }

    public Integer getPeriodoDias() {
        return periodoDias;
    }

    public void setPeriodoDias(Integer periodoDias) {
        this.periodoDias = periodoDias;
    }

    public Integer getDiasProximaAvaliacao() {
        return diasProximaAvaliacao;
    }

    public void setDiasProximaAvaliacao(Integer diasProximaAvaliacao) {
        this.diasProximaAvaliacao = diasProximaAvaliacao;
    }

    public Long getDataProxima() {
        return dataProxima;
    }

    public void setDataProxima(Long dataProxima) {
        this.dataProxima = dataProxima;
    }

    public Double getPercentualMassaGorda() {
        return percentualMassaGorda;
    }

    public void setPercentualMassaGorda(Double percentualMassaGorda) {
        this.percentualMassaGorda = percentualMassaGorda;
    }

    public Double getPercentualMassaMagra() {
        return percentualMassaMagra;
    }

    public void setPercentualMassaMagra(Double percentualMassaMagra) {
        this.percentualMassaMagra = percentualMassaMagra;
    }

    public Double getMassaGordaInicial() {
        return massaGordaInicial;
    }

    public void setMassaGordaInicial(Double massaGordaInicial) {
        this.massaGordaInicial = massaGordaInicial;
    }

    public Double getMassaGordaAtual() {
        return massaGordaAtual;
    }

    public void setMassaGordaAtual(Double massaGordaAtual) {
        this.massaGordaAtual = massaGordaAtual;
    }

    public Double getEvolucaoGeral() {
        return evolucaoGeral;
    }

    public void setEvolucaoGeral(Double evolucaoGeral) {
        this.evolucaoGeral = evolucaoGeral;
    }

    public Double getMassaMagraInicial() {
        return massaMagraInicial;
    }

    public void setMassaMagraInicial(Double massaMagraInicial) {
        this.massaMagraInicial = massaMagraInicial;
    }

    public Double getMassaMagraAtual() {
        return massaMagraAtual;
    }

    public void setMassaMagraAtual(Double massaMagraAtual) {
        this.massaMagraAtual = massaMagraAtual;
    }

    public Double getNivelGorduraCorporal() {
        return nivelGorduraCorporal;
    }

    public void setNivelGorduraCorporal(Double nivelGorduraCorporal) {
        this.nivelGorduraCorporal = nivelGorduraCorporal;
    }

    public Double getNivelGorduraCorporalInicial() {
        return nivelGorduraCorporalInicial;
    }

    public void setNivelGorduraCorporalInicial(Double nivelGorduraCorporalInicial) {
        this.nivelGorduraCorporalInicial = nivelGorduraCorporalInicial;
    }

    public Double getNivelGorduraCorporalFaltando() {
        return nivelGorduraCorporalFaltando;
    }

    public void setNivelGorduraCorporalFaltando(Double nivelGorduraCorporalFaltando) {
        this.nivelGorduraCorporalFaltando = nivelGorduraCorporalFaltando;
    }

    public List<TreinoGruposMuscularesDTO> getGrupos() {
        return grupos;
    }

    public void setGrupos(List<TreinoGruposMuscularesDTO> grupos) {
        this.grupos = grupos;
    }

    public List<PesoMassaGordaHistoricoDTO> getPesos() {
        return pesos;
    }

    public void setPesos(List<PesoMassaGordaHistoricoDTO> pesos) {
        this.pesos = pesos;
    }

    public List<DobrasAlunoHistoricoDTO> getDobras() {
        return dobras;
    }

    public void setDobras(List<DobrasAlunoHistoricoDTO> dobras) {
        this.dobras = dobras;
    }
}
