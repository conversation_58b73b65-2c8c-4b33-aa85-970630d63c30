/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.pacto.service.impl.programa;

import br.com.pacto.bean.programa.Compromisso;
import br.com.pacto.dao.intf.programa.CompromissoDao;
import br.com.pacto.service.intf.Validacao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.programa.CompromissoService;
import br.com.pacto.util.ViewUtils;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



/**
 *
 * <AUTHOR>
 */
@Service
public class CompromissoServiceImpl implements CompromissoService {

    @Autowired
    private ViewUtils viewUtils;
    @Autowired
    private Validacao validacao;
    @Autowired
    private CompromissoDao compromissoDao;

    public ViewUtils getViewUtils() {
        return viewUtils;
    }

    public void setViewUtils(ViewUtils viewUtils) {
        this.viewUtils = viewUtils;
    }

    public Validacao getValidacao() {
        return validacao;
    }

    public void setValidacao(Validacao validacao) {
        this.validacao = validacao;
    }

    public CompromissoDao getCompromissoDao(){
        return this.compromissoDao;
    }

    public void setCompromissoDao(CompromissoDao compromissoDao){
       this.compromissoDao = compromissoDao; 
    }


    public Compromisso alterar(final String ctx, Compromisso object) throws ServiceException {
        try {
            return getCompromissoDao().update(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public void excluir(final String ctx, Compromisso object) throws ServiceException {
        try {
            getCompromissoDao().delete(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public Compromisso inserir(final String ctx, Compromisso object) throws ServiceException {
        try {
            return getCompromissoDao().insert(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public Compromisso obterObjetoPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException {
        try {
            return getCompromissoDao().findObjectByParam(ctx, query, params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public Compromisso obterPorId(final String ctx, Integer id) throws ServiceException {
        try {
            return getCompromissoDao().findById(ctx, id);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<Compromisso> obterPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException {
        try {
            return getCompromissoDao().findByParam(ctx, query, params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<Compromisso> obterPorParam(final String ctx, String query,
            Map<String, Object> params, int max, int index)
            throws ServiceException {
        try {
            return getCompromissoDao().findByParam(ctx, query, params, max, index);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<Compromisso> obterTodos(final String ctx) throws ServiceException {
        try {
            return getCompromissoDao().findAll(ctx);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    }