/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.impl.aulapersonal;

import br.com.pacto.bean.gestaopersonal.ConfiguracaoPersonalEmpresa;
import br.com.pacto.dao.intf.aulapersonal.ConfiguracaoPersonalEmpresaDao;
import br.com.pacto.service.intf.Validacao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.aulapersonal.ConfiguracaoPersonalEmpresaService;
import br.com.pacto.util.ViewUtils;
import java.util.List;
import java.util.Map;

import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import servicos.integracao.adm.client.EmpresaWS;

/**
 *
 * <AUTHOR>
 */
@Service
public class ConfiguracaoPersonalEmpresaServiceImpl implements ConfiguracaoPersonalEmpresaService {

    @Autowired
    private ViewUtils viewUtils;
    @Autowired
    private Validacao validacao;
    @Autowired
    private ConfiguracaoPersonalEmpresaDao configuracaoPersonalEmpresaDao;

    public ViewUtils getViewUtils() {
        return viewUtils;
    }

    public void setViewUtils(ViewUtils viewUtils) {
        this.viewUtils = viewUtils;
    }

    public Validacao getValidacao() {
        return validacao;
    }

    public void setValidacao(Validacao validacao) {
        this.validacao = validacao;
    }

    public ConfiguracaoPersonalEmpresaDao getConfiguracaoPersonalEmpresaDao() {
        return this.configuracaoPersonalEmpresaDao;
    }

    public void setConfiguracaoPersonalEmpresaDao(ConfiguracaoPersonalEmpresaDao configuracaoPersonalEmpresaDao) {
        this.configuracaoPersonalEmpresaDao = configuracaoPersonalEmpresaDao;
    }

    public ConfiguracaoPersonalEmpresa alterar(final String ctx, ConfiguracaoPersonalEmpresa object) throws ServiceException {
        try {
            return getConfiguracaoPersonalEmpresaDao().update(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public void excluir(final String ctx, ConfiguracaoPersonalEmpresa object) throws ServiceException {
        try {
            getConfiguracaoPersonalEmpresaDao().delete(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public ConfiguracaoPersonalEmpresa inserir(final String ctx, ConfiguracaoPersonalEmpresa object) throws ServiceException {
        try {
            return getConfiguracaoPersonalEmpresaDao().insert(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public ConfiguracaoPersonalEmpresa obterObjetoPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException {
        try {
            return getConfiguracaoPersonalEmpresaDao().findObjectByParam(ctx, query, params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public ConfiguracaoPersonalEmpresa obterPorId(final String ctx, Integer id) throws ServiceException {
        try {
            return getConfiguracaoPersonalEmpresaDao().findById(ctx, id);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<ConfiguracaoPersonalEmpresa> obterPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException {
        try {
            return getConfiguracaoPersonalEmpresaDao().findByParam(ctx, query, params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<ConfiguracaoPersonalEmpresa> obterPorParam(final String ctx, String query,
            Map<String, Object> params, int max, int index)
            throws ServiceException {
        try {
            return getConfiguracaoPersonalEmpresaDao().findByParam(ctx, query, params, max, index);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<ConfiguracaoPersonalEmpresa> obterTodos(final String ctx) throws ServiceException {
        try {
            return getConfiguracaoPersonalEmpresaDao().findAll(ctx);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public ConfiguracaoPersonalEmpresa obterPorEmpresa(String ctx, Integer empresaZW) throws ServiceException {
        try {
            return getConfiguracaoPersonalEmpresaDao().findObjectByAttribute(ctx, "codigoEmpresaZW", empresaZW);
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    @Override
    public ConfiguracaoPersonalEmpresa atualizarCfgEmpresa(final String ctx, final EmpresaWS empresaWS) throws ServiceException{
        try {
            ConfiguracaoPersonalEmpresa cfgPersonalEmpresa = obterPorEmpresa(ctx, empresaWS.getCodigo());
            if (cfgPersonalEmpresa == null || cfgPersonalEmpresa.getCodigo() == null || cfgPersonalEmpresa.getCodigo() == 0) {
                cfgPersonalEmpresa = new ConfiguracaoPersonalEmpresa();
                cfgPersonalEmpresa.setCodigoEmpresaZW(empresaWS.getCodigo());
                cfgPersonalEmpresa = inserir(ctx, cfgPersonalEmpresa);
            }
            cfgPersonalEmpresa.setConsumirCreditoPorAluno(empresaWS.isConsumirCreditoPorAlunoVinculado());
            cfgPersonalEmpresa.setDuracaoCredito(empresaWS.getDuracaoCredito());
            cfgPersonalEmpresa.setFotoParaPersonal(empresaWS.isUsarFotoPersonal());
            cfgPersonalEmpresa.setMostrarFotoMonitor(empresaWS.isMostrarFotosAlunosMonitor());
            cfgPersonalEmpresa.setObrigatorioAssociarAluno(empresaWS.isObrigatorioAssociarAlunoAoCheckIn());
            cfgPersonalEmpresa.setTempoCheckOutAutomatico(empresaWS.getTempoCheckOutAutomatica());
            return alterar(ctx, cfgPersonalEmpresa);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public ConfiguracaoPersonalEmpresa atualizarCfgEmpresaV2(final String ctx, String json) throws ServiceException {
        try {
            JSONObject dados = new JSONObject(json);
            ConfiguracaoPersonalEmpresa cfgPersonalEmpresa = obterPorEmpresa(ctx, dados.getInt("codigoEmpresaZW"));
            if (cfgPersonalEmpresa == null || cfgPersonalEmpresa.getCodigo() == null || cfgPersonalEmpresa.getCodigo() == 0) {
                cfgPersonalEmpresa = new ConfiguracaoPersonalEmpresa();
                cfgPersonalEmpresa.setCodigoEmpresaZW(dados.getInt("codigoEmpresaZW"));
                cfgPersonalEmpresa = inserir(ctx, cfgPersonalEmpresa);
            }
            cfgPersonalEmpresa.setConsumirCreditoPorAluno(dados.getBoolean("isConsumirCreditoPorAlunoVinculado"));
            cfgPersonalEmpresa.setDuracaoCredito(dados.getInt("getDuracaoCredito"));
            cfgPersonalEmpresa.setFotoParaPersonal(dados.getBoolean("isUsarFotoPersonal"));
            cfgPersonalEmpresa.setMostrarFotoMonitor(dados.getBoolean("isMostrarFotosAlunosMonitor"));
            cfgPersonalEmpresa.setObrigatorioAssociarAluno(dados.getBoolean("isObrigatorioAssociarAlunoAoCheckIn"));
            cfgPersonalEmpresa.setTempoCheckOutAutomatico(dados.getInt("tempoCheckOutAutomatico"));
            return alterar(ctx, cfgPersonalEmpresa);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

}