package br.com.pacto.service.impl.agenda;

import br.com.pacto.base.impl.EntityManagerFactoryService;
import br.com.pacto.base.util.ExecuteRequestHttpService;
import br.com.pacto.bean.aula.Modalidade;
import br.com.pacto.bean.aula.ProfessorSubstituido;
import br.com.pacto.bean.configuracoes.ConfiguracaoSistema;
import br.com.pacto.bean.configuracoes.ConfiguracoesEnum;
import br.com.pacto.bean.configuracoes.TimeZoneEnum;
import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.agendamento.AlunoTurmaDTO;
import br.com.pacto.controller.json.aulaDia.AulaDiaJSON;
import br.com.pacto.controller.json.aulaDia.AulaDiaJSONControle;
import br.com.pacto.controller.json.turma.TurmaVideoDTO;
import br.com.pacto.dao.intf.cliente.ClienteSinteticoDao;
import br.com.pacto.dao.intf.empresa.EmpresaDao;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.conexao.ConexaoZWServiceImpl;
import br.com.pacto.service.intf.agenda.AgendaService;
import br.com.pacto.service.intf.agendatotal.AgendaTotalService;
import br.com.pacto.service.intf.aula.AulaService;
import br.com.pacto.service.intf.cliente.ClienteSinteticoService;
import br.com.pacto.service.intf.configuracoes.ConfiguracaoSistemaService;
import br.com.pacto.service.intf.empresa.EmpresaService;
import br.com.pacto.service.intf.empresa.EmpresaVO;
import br.com.pacto.service.intf.selfloops.SelfloopsConfiguracoesService;
import br.com.pacto.service.intf.usuario.UsuarioService;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.bean.AgendaTotalTO;
import br.com.pacto.util.impl.Ordenacao;
import br.com.pacto.util.json.AgendaTotalJSON;
import br.com.pacto.util.json.TipoToleranciaAulaEnum;
import br.com.pacto.util.json.*;
import org.apache.http.NameValuePair;
import org.apache.http.client.ResponseHandler;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.BasicResponseHandler;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.message.BasicNameValuePair;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.ui.ModelMap;
import br.com.pacto.util.AgendadoJSON;
import java.sql.*;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

import static org.apache.commons.lang3.StringUtils.isNotBlank;

@Service
public class AulasColetivasModoBDServiceImpl {

    @Autowired
    private AgendaTotalService agendaTotalService;
    @Autowired
    private ConexaoZWServiceImpl conexaoZWService;
    @Autowired
    private EmpresaService empService;
    @Autowired
    private EmpresaDao empresaDao;
    @Autowired
    private ClienteSinteticoService clienteService;
    @Autowired
    private UsuarioService usuarioService;
    @Autowired
    private ClienteSinteticoDao clienteSinteticoDao;
    @Autowired
    private ConfiguracaoSistemaService configService;
    @Autowired
    private AgendaService agendaService;
    @Autowired
    private AulaService aulaService;
    @Autowired
    private SelfloopsConfiguracoesService selfloopsConfiguracoesService;


    public AulasColetivasModoBDServiceImpl() {
    }

    public void modoBD(String ctx, Integer empresa, String dia, String contrato, String matricula, String chaveOrigem, ModelMap mm) {
        try {
            AgendaModoBDServiceImpl agendaAulasService;
            AutorizacaoAcessoGrupoEmpresarialVO autorizacaoAcessoGrupoEmpresarialVO = new AutorizacaoAcessoGrupoEmpresarialVO();
            boolean autorizadoGestaoRede = false;
            try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
                agendaAulasService = new AgendaModoBDServiceImpl(conZW, ctx);

                Integer contratoInt;
                try {
                    contratoInt = Integer.valueOf(contrato);
                } catch (Exception e) {
                    contratoInt = null;
                }
                boolean outraUnidade = !UteisValidacao.emptyString(chaveOrigem) && !ctx.equals(chaveOrigem);
                try {
                    if (!UteisValidacao.emptyString(matricula) && outraUnidade) {
                        autorizacaoAcessoGrupoEmpresarialVO = consultarAutorizacaoPorMatriculaChave(ctx, matricula, chaveOrigem);
                        if (!UteisValidacao.emptyNumber(autorizacaoAcessoGrupoEmpresarialVO.getCodigo())) {
                            autorizadoGestaoRede = true;
                        }
                    }
                } catch (Exception ignored) {}
                List<String> identificadoresAlunoNaAula = new ArrayList<String>();
                //caso o aluno seja informado, buscar quais as proximas aulas marcadas dele
                if (!UteisValidacao.emptyString(matricula) && !outraUnidade) {
                    List<AgendaTotalJSON> aulasMarcadas = agendaAulasService.consultarProximasAulasAulaCheia(Integer.valueOf(matricula));
                    //guardar na lista de proximas aulas
                    for (AgendaTotalJSON a : aulasMarcadas) {
                        identificadoresAlunoNaAula.add(a.getCodDia());
                    }
                } else if (!UteisValidacao.emptyString(matricula) && outraUnidade) {
                    identificadoresAlunoNaAula.addAll(agendaAulasService.aulasAutorizado(Integer.valueOf(matricula), chaveOrigem, Calendario.getDate(Calendario.MASC_DATA, dia)));
                    if (autorizadoGestaoRede) {
                        if (autorizacaoAcessoGrupoEmpresarialVO != null && !UteisValidacao.emptyNumber(autorizacaoAcessoGrupoEmpresarialVO.getCodigo())) {
                            identificadoresAlunoNaAula.addAll(agendaAulasService.aulasAutorizadoGestaoRede(autorizacaoAcessoGrupoEmpresarialVO.getCodigoMatricula(), autorizacaoAcessoGrupoEmpresarialVO.getCodAcesso(), Calendario.getDate(Calendario.MASC_DATA, dia)));
                        }
                    }

                }
                String timeZone = obterFusoHorarioEmpresa(ctx, empresa);

                ConfiguracaoSistema cfg = configService.consultarPorTipo(ctx, ConfiguracoesEnum.DIAS_MOSTRAR_TOTEM);
                cfg.setValor(cfg.getValor().isEmpty() || cfg.getValor().equals("") ? "43200" : cfg.getValor());
                Date diaInicio = Calendario.getDate(Calendario.MASC_DATA, dia);
                Date diaLimite = Uteis.somarCampoData(Calendario.hoje(timeZone), Calendar.MINUTE, cfg.getValorAsInteger());

                Map<Integer, List<Date>> mapaAulasExcluidas = agendaTotalService.obterAulasExcluidas(ctx, diaInicio, diaLimite);

                Map<Integer, String> mapaProfessores = agendaTotalService.obterMapaProfessores(ctx, empresa, Boolean.FALSE, false);
                Map<Integer, Map<Date, ProfessorSubstituido>> mapaProfessoresSubstituidos = agendaTotalService.obterProfessoresSubstituidos(ctx,
                        null, diaInicio, Calendario.getDataComHora(diaInicio, "23:59:59"));

            List<AgendaTotalJSON> aulas = agendaAulasService.consultarParaAgenda(
                    Calendario.getDataComHoraZerada(diaInicio),
                    Calendario.getDataComHora(diaInicio, "23:59:59"),
                    null, null, empresa, true, null, null);


                List<Modalidade> listaModalidades = agendaTotalService.todasModalidades(ctx);
                List<AulaDiaJSON> jsonArray = new ArrayList<AulaDiaJSON>();

                ConfiguracaoSistema cfgACNM = configService.consultarPorTipo(ctx, ConfiguracoesEnum.BLOQUEAR_AULA_COLETIVA_NAO_PERTENCE_MODALIDADE);
                if (cfgACNM.getValorAsBoolean() && !outraUnidade) {
                    String modalidades = clienteService.consultaModalidades(ctx, matricula);
                    String modalidadesDiaria = clienteService.consultaModalidadeDiaria(ctx, matricula, diaInicio);
                    modalidades += UteisValidacao.emptyString(modalidadesDiaria) ? "" : "|" + modalidadesDiaria;
                    String[] modas = modalidades.split("\\|");
                    try {
                        String modalidadesTitular = agendaTotalService.modalidadesTitular(ctx, Integer.valueOf(matricula));
                        if (modalidadesTitular != null && !modalidadesTitular.isEmpty()) {
                            Set<String> conjuntoModalidades = new HashSet<>(Arrays.asList(modas));
                            conjuntoModalidades.addAll(Arrays.asList(modalidadesTitular.split("\\|")));

                            modas = conjuntoModalidades.toArray(new String[0]);
                        }
                    } catch (Exception e) {
                        Uteis.logar(e, AulaDiaJSONControle.class);
                    }
                    Boolean totalPass = clienteService.verificaTotalPass(ctx, Integer.valueOf(matricula));
                    Boolean gymPass = clienteService.verificaGymPass(ctx, Integer.valueOf(matricula));
                    List<AgendaTotalJSON> aulas2 = new ArrayList<>(aulas);
                    for (AgendaTotalJSON aula : aulas2) {
                        AgendaTotalTO agenda = new AgendaTotalTO(aula);
                        if (agenda.getAulaColetiva() && (!aula.isNaoValidarModalidadeContrato() &&
                                Arrays.stream(modas).noneMatch(moda -> (isNotBlank(moda) && Integer.valueOf(moda).equals(agenda.getCodigotipo()))))
                                && !(aula.getVisualizarProdutosTotalPass() != null &&  aula.getVisualizarProdutosTotalPass() && totalPass) &&
                                !(aula.getVisualizarProdutosGympass() != null && aula.getVisualizarProdutosGympass() && gymPass)) {
                            aulas.remove(aula);
                        }
                    }
                } else if (!UteisValidacao.emptyString(matricula) && outraUnidade) {
                    if(cfgACNM.getValorAsBoolean()) {
                        List<AgendaTotalJSON> aulas2 = new ArrayList<>(aulas);
                        for (AgendaTotalJSON aula : aulas2) {
                            AgendaTotalTO agenda = new AgendaTotalTO(aula);
                            if(agenda.getAulaColetiva() && !aula.isNaoValidarModalidadeContrato() &&
                                    !agendaAulasService.buscaAutorizadoPorMatricula(Integer.valueOf(matricula), chaveOrigem, aula.getTipo())) {
                                aulas.remove(aula);
                            }
                        }
                    } else {
                        if(!agendaAulasService.buscaAutorizadoPorMatriculaSemValidarModalidade(ctx, Integer.valueOf(matricula), chaveOrigem, autorizacaoAcessoGrupoEmpresarialVO)) {
                            aulas.clear();
                        }
                    }
                }

                verificarBloqueados(ctx,
                        diaInicio.getTime(),
                        diaInicio.getTime(),
                        empresa,
                        aulas, agendaAulasService);
                List<Integer> modalidadeFiltrar = new ArrayList<>();
                if (!UteisValidacao.emptyNumber(contratoInt)) {
                    modalidadeFiltrar = agendaAulasService.modalidadesContrato(ctx, contratoInt, Integer.valueOf(matricula));
                }
                for (AgendaTotalJSON aula : aulas) {
                    AgendaTotalTO agenda = new AgendaTotalTO(aula);
                    if (!UteisValidacao.emptyNumber(contratoInt) && !modalidadeFiltrar.contains(aula.getCodigoTipo())) {
                        continue;
                    }
                    for (Modalidade modalidade : listaModalidades) {
                        if (modalidade.getCodigoZW() != null && modalidade.getCodigoZW().equals(agenda.getCodigotipo())) {
                            agenda.setCor(modalidade.getCor().getCor());
                            aula.setCor(modalidade.getCor().getCor());
                            agenda.setTextoStyle(modalidade.getCor().getClasseCor());
                            aula.setTextoCor(modalidade.getCor().getClasseCor());
                            break;
                        }
                    }
                    List<Date> datas = mapaAulasExcluidas.get(Integer.valueOf(agenda.getId()));
                    if (datas != null && datas.contains(Calendario.getDataComHoraZerada(agenda.getStartDate()))) {
                        continue;
                    }

                    Map<Date, ProfessorSubstituido> mapaSubstitutos = mapaProfessoresSubstituidos.get(Integer.valueOf(agenda.getId()));
                    if (mapaSubstitutos != null) {
                        ProfessorSubstituido subs = mapaSubstitutos.get(Calendario.getDataComHoraZerada(agenda.getStartDate()));
                        if (subs != null) {
                            Integer codigoProfessorSubstituto = subs.getCodigoProfessorSubstituto();
                            Usuario user = usuarioService.consultarProColaborador(ctx, Integer.valueOf(codigoProfessorSubstituto));
                            aula.setResponsavel(mapaProfessores.get(codigoProfessorSubstituto));
                            aula.setCodigoResponsavel(subs.getCodigoProfessorSubstituto());
                            aula.setFotoProfessor(mapaProfessores.get(-codigoProfessorSubstituto));
                            if (user != null && user.getFotoKeyApp() != null && !user.getFotoKeyApp().equals(""))
                                aula.setFotoProfessor(user.getFotoKeyApp());
                        }
                    }

                    Date inicio = Uteis.getDate(aula.getInicio(), "dd/MM/yyyy HH:mm");
                    Date inicioTolerancia = inicio;
                    if (!UteisValidacao.emptyNumber(aula.getTipoTolerancia())) {
                        inicioTolerancia = aula.getTipoTolerancia().equals(TipoToleranciaAulaEnum.APOS_INICIO.getCodigo()) ?
                                Uteis.somarCampoData(inicio, Calendar.MINUTE, aula.getTolerancia()) :
                                Uteis.somarCampoData(inicio, Calendar.MINUTE, -aula.getTolerancia());
                    }
                    if (((inicioTolerancia.after(Calendario.hoje(timeZone)) && inicio.before(Calendario.hoje(timeZone)))
                            || inicio.before(diaLimite)) && inicioTolerancia.after(Calendario.hoje(timeZone))) {
                        StringBuilder sql = new StringBuilder();
                        sql.append("select codigo, turma_codigo , linkvideo , professor ");
                        sql.append("from turmavideo ");
                        sql.append("where turma_codigo = ").append(aula.getCodigoTurma());

                        ResultSet resultSet = ConexaoZWServiceImpl.criarConsulta(sql.toString(), conZW);
                        List<TurmaVideoDTO> linkVideos = new ArrayList<>();
                        while (resultSet.next()) {
                            TurmaVideoDTO turmaVideoDTO = new TurmaVideoDTO();
                            turmaVideoDTO.setId(resultSet.getInt("codigo"));
                            turmaVideoDTO.setLinkVideo(resultSet.getString("linkvideo"));
                            turmaVideoDTO.setProfessor(resultSet.getBoolean("professor"));
                            linkVideos.add(turmaVideoDTO);
                        }
                        aula.setLinkVideos(linkVideos);

                        AulaDiaJSON aulaDiaJSON = new AulaDiaJSON(aula);
                        aulaDiaJSON.setAlunoEstaNaAula(identificadoresAlunoNaAula.contains(aulaDiaJSON.getCodDia()));
                        Empresa empresaLogada = empresaDao.findObjectByAttribute(ctx, "codzw", aula.getEmpresa() == null ? empresa : aula.getEmpresa());
                        aulaDiaJSON.setNomeEmpresa(empresaLogada == null ? "" : empresaLogada.getNome());
                        aulaDiaJSON.setEquipamentosOcupados(agendaService.horarioEquipamentoOcupado(ctx, aula.getDia(), aulaDiaJSON.getCodigo()));
                        if(!UteisValidacao.emptyString(aulaDiaJSON.getMapaEquipamentos())) {
                            aulaDiaJSON.setListaMapaEquipamentoAparelho(aulaService.montarListaEquipamentoAparelho(ctx, aula.getCodigoTurma(), aulaDiaJSON.getMapaEquipamentos()));
                        }
                        ConfiguracaoSistema cfgUNSIE = configService.consultarPorTipo(ctx, ConfiguracoesEnum.UTILIZAR_NUMERACAO_SEQUENCIAL_IDENTIFICADOR_EQUIPAMENTO);
                        aulaDiaJSON.setApresentarEquipamentoPorNumeracao(cfgUNSIE.getValorAsBoolean());
                        jsonArray.add(aulaDiaJSON);
                    }
                }

                jsonArray = Ordenacao.ordenarLista(jsonArray, "inicio");
                mm.addAttribute("aulas", jsonArray);
            }
        } catch (Exception ex) {
            mm.addAttribute("erro", ex.getMessage());
            Logger.getLogger(AulaDiaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
    }


    private AutorizacaoAcessoGrupoEmpresarialVO consultarAutorizacaoPorMatriculaChave(String ctx, String matricula, String chaveAluno){
        try {
            final String url = Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebInteg);
            CloseableHttpClient client = ExecuteRequestHttpService.createConnector();
            HttpPost httpPost = new HttpPost(url + "/prest/aulacheia/consultar-autorizado-gestao-rede");
            List<NameValuePair> params = new ArrayList<>();
            params.add(new BasicNameValuePair("chave", ctx));
            params.add(new BasicNameValuePair("chaveOrigem", chaveAluno));
            params.add(new BasicNameValuePair("matriculaAutorizado", matricula));
            httpPost.setEntity(new UrlEncodedFormEntity(params));
            CloseableHttpResponse response = client.execute(httpPost);
            ResponseHandler<String> handler = new BasicResponseHandler();
            String body = handler.handleResponse(response);
            client.close();
            JSONObject autorizacaoJson;
            try {
                autorizacaoJson = new JSONObject(body).getJSONObject("content");
                return new AutorizacaoAcessoGrupoEmpresarialVO(autorizacaoJson);
            } catch (Exception ignored) {
                return new AutorizacaoAcessoGrupoEmpresarialVO();
            }
        } catch (Exception ignored){
            return new AutorizacaoAcessoGrupoEmpresarialVO();
        }
    }

    private AutorizacaoAcessoGrupoEmpresarialVO consultarAutorizacaoPorMatriculaCodAcesso(String ctx, String matricula, String codAcesso){
        try {
            final String url = Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebInteg);
            CloseableHttpClient client = ExecuteRequestHttpService.createConnector();
            HttpPost httpPost = new HttpPost(url + "/prest/aulacheia/consultar-autorizado-gestao-rede");
            List<NameValuePair> params = new ArrayList<>();
            params.add(new BasicNameValuePair("chave", ctx));
            params.add(new BasicNameValuePair("codAcessoAutorizado", codAcesso));
            params.add(new BasicNameValuePair("matriculaAutorizado", matricula));
            httpPost.setEntity(new UrlEncodedFormEntity(params));
            CloseableHttpResponse response = client.execute(httpPost);
            ResponseHandler<String> handler = new BasicResponseHandler();
            String body = handler.handleResponse(response);
            client.close();
            JSONObject autorizacaoJson;
            try {
                autorizacaoJson = new JSONObject(body).getJSONObject("content");
                return new AutorizacaoAcessoGrupoEmpresarialVO(autorizacaoJson);
            } catch (Exception ignored) {
                return new AutorizacaoAcessoGrupoEmpresarialVO();
            }
        } catch (Exception ignored){
            return new AutorizacaoAcessoGrupoEmpresarialVO();
        }
    }

    private String obterFusoHorarioEmpresa(String ctx, Integer empresa) throws ServiceException {
        String timeZone = null;
        if(UteisValidacao.emptyNumber(empresa)){ // pega o menor hora para verificação. Isso porque o retira ficha não avalia empresa
            List<Empresa> listaEmpresas = empService.obterTodos(ctx);
            for(Empresa empresaVO: listaEmpresas){
                if(timeZone == null){
                    timeZone = empresaVO.getTimeZoneDefault();
                } else{
                    if(empresaVO.getTimeZoneDefault() != null){
                        if(Calendario.menorComHora(Calendario.hoje(empresaVO.getTimeZoneDefault()), Calendario.hoje(timeZone))){
                            timeZone = empresaVO.getTimeZoneDefault();
                        }
                    }
                }
            }

        } else {
            timeZone = EntityManagerFactoryService.getTimeZoneEmpresa(ctx,empresa);
            if(timeZone == null) {
                Empresa empresaZW = empService.obterPorIdZW(ctx, empresa);
                if(empresaZW.getTimeZoneDefault() != null && !empresaZW.getTimeZoneDefault().isEmpty()){
                    timeZone = empresaZW.getTimeZoneDefault();
                    EntityManagerFactoryService.setTimeZoneEmpresa(ctx,empresa, timeZone);
                }

            }
        }
        return timeZone == null ? TimeZoneEnum.Brazil_East.getId() : timeZone;
    }

    public void verificarBloqueados(String ctx,
                                    Long inicio,
                                    Long fim,
                                    Integer empresaId,
                                    List<AgendaTotalJSON> agendamentos,
                                    AgendaModoBDServiceImpl agendaAulasService){
        try {
            ConfiguracaoSistema cfg = configService.consultarPorTipo(ctx, ConfiguracoesEnum.BLOQUEAR_MESMO_AMBIENTE);
            if(!cfg.getValorAsBoolean()){
                return;
            }
            Map<Integer, List<AmbienteAgendadoTO>> ambientesAgendados = agendaAulasService.ambientesAgendados(ctx, new Date(inicio), new Date(fim), empresaId);
            if(ambientesAgendados.isEmpty()){
                return;
            }
            for (AgendaTotalJSON agenda : new ArrayList<>(agendamentos)) {
                List<AmbienteAgendadoTO> ambienteAgendadoTOS = ambientesAgendados.get(agenda.getCodigoLocal());
                if (UteisValidacao.emptyList(ambienteAgendadoTOS)) {
                    continue;
                }
                Date horarioInicial = new SimpleDateFormat("dd/MM/yyyy HH:mm").parse(agenda.getInicio());
                Date horarioFim = new SimpleDateFormat("dd/MM/yyyy HH:mm").parse(agenda.getFim());
                for(AmbienteAgendadoTO aa : ambienteAgendadoTOS){
                    if(!Integer.valueOf(agenda.getId()).equals(aa.getHorarioTurma())
                            && ((horarioInicial.getTime() == aa.getInicio().getTime() || horarioFim.getTime() == aa.getFim().getTime())
                            || Calendario.entre(horarioInicial, aa.getInicio(), aa.getFim())
                            || Calendario.entre(horarioFim, aa.getInicio(), aa.getFim())
                            || Calendario.entre( aa.getInicio(), horarioInicial, horarioFim))){
                        agendamentos.remove(agenda);
                    }
                }
            }
        }catch (Exception e){
            Uteis.logar(e, AgendaService.class);
        }
    }

    public List<AgendadoJSON> consultarAlunosDeUmaAula(String ctx, Connection con, Integer codigoHorario, Date dia) throws Exception {

            Boolean ordenarPorAveragePower = false;
            StringBuilder sql = new StringBuilder();

            sql.append(" SELECT (CASE \n" +
                    "        WHEN pes.fotokey IS NOT NULL THEN pes.fotokey \n" +
                    "        ELSE aage.fotokey END) as fotokey, \n");
            sql.append(" 	   (CASE \n");
            sql.append(" 	    WHEN pes.codigo IS NOT NULL THEN pes.codigo \n");
            sql.append(" 	    ELSE aage.codigopessoa END) AS codigopessoa, \n");
            sql.append(" 	    cli.codigo AS codigocliente, \n");
            sql.append(" 	   aht.codigo AS codigoagendamento, ht.horainicial, ht.horafinal, \n");
            sql.append(" 	   aht.matriculaAutorizado, aht.codacessoautorizado, aht.autorizadogestaorede, \n");
            sql.append(" 	   (CASE  \n");
            sql.append(" 	    WHEN pes.nome IS NOT NULL AND TRIM(pes.nome) <> '' THEN pes.nome \n");
            sql.append(" 	    WHEN aage.nomepessoa IS NOT NULL AND TRIM(aage.nomepessoa) <> '' THEN aage.nomepessoa \n");
            sql.append("        ELSE pas.nome END) AS nome, \n");
            sql.append(" 	    pes.datanasc, \n");
            sql.append(" 	   (CASE  \n");
            sql.append(" 	    WHEN cli.matricula IS NOT NULL THEN cli.matricula \n");
            sql.append(" 	    ELSE aage.codigomatricula::VARCHAR(255) END) AS matricula, \n");
            sql.append(" 	    ARRAY_TO_STRING(ARRAY(select numero from telefone where pessoa = cli.pessoa), ';') as telefones, \n");
            sql.append("        aulaexperimental, aht.bookingid, cli.situacao, aht.dia, cli.idselfloops, cli.empresa as empCliente,  \n");
            sql.append("        aht.desafio ");
            sql.append(" FROM alunohorarioturma aht \n");
            sql.append(" LEFT JOIN cliente cli ON cli.codigo = aht.cliente \n");
            sql.append(" LEFT JOIN pessoa pes ON cli.pessoa = pes.codigo \n");
            sql.append(" LEFT JOIN horarioturma ht ON ht.codigo = aht.horarioturma \n");
            sql.append(" LEFT JOIN autorizacaoacessogrupoempresarial aage ON aage.codigo = aht.autorizado \n");
            sql.append(" LEFT JOIN passivo pas ON pas.codigo = aht.passivo \n");
            sql.append(" WHERE aht.dia = ? AND aht.horarioturma = ?");
            sql.append(" ORDER BY pes.nome");
            List<AgendadoJSON> alunos;
            try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
                stm.setDate(1, Uteis.getDataJDBC(dia));
                stm.setInt(2, codigoHorario);
                try (ResultSet rs = stm.executeQuery()) {

                    HashMap<String, AgendamentoConfirmadoJSON> confirmados = obterMapaConfirmados(con, dia, dia);
                    alunos = new ArrayList<AgendadoJSON>();
                    while (rs.next()) {
                        AgendadoJSON aluno = new AgendadoJSON();
                        aluno.setUrlFoto(Uteis.getPaintFotoDaNuvem(rs.getString("fotokey")));
                        aluno.setNome(rs.getString("nome"));
                        aluno.setDataNascimento(rs.getString("datanasc"));
                        aluno.setMatricula(rs.getString("matricula"));
                        aluno.setTelefones(rs.getString("telefones"));
                        aluno.setFim(rs.getString("horafinal"));
                        aluno.setInicio(rs.getString("horainicial"));
                        aluno.setId_agendamento(rs.getString("codigoagendamento"));
                        aluno.setCodigoCliente(rs.getInt("codigocliente"));
                        aluno.setCodigoPessoa(rs.getInt("codigopessoa"));
                        aluno.setExperimental(rs.getBoolean("aulaexperimental"));
                        aluno.setDesafio(rs.getBoolean("desafio"));
                        aluno.setUserIdSelfloops(rs.getString("idselfloops"));
                        String identificador = codigoHorario + "_" + Uteis.getData(dia, "ddMMyy") + "_" + aluno.getCodigoCliente();
                        aluno.setConfirmado(confirmados.get(identificador) != null);

                        aluno.setSituacao(rs.getString("situacao"));
                        String bookingid = rs.getString("bookingid");

                        try {
                            boolean autorizadoGestaoRede = rs.getBoolean("autorizadogestaorede");
                            if (autorizadoGestaoRede){
                                String matriculaAutorizado = String.valueOf(rs.getInt("matriculaAutorizado"));
                                String codAcessoAutorizado = rs.getString("codacessoautorizado");

                                AutorizacaoAcessoGrupoEmpresarialVO autorizacaoAcessoGrupoEmpresarialVO = consultarAutorizacaoPorMatriculaCodAcesso(ctx, matriculaAutorizado, codAcessoAutorizado);

                                if (!UteisValidacao.emptyNumber(autorizacaoAcessoGrupoEmpresarialVO.getCodigo())){
                                    aluno.setMatricula(String.valueOf(autorizacaoAcessoGrupoEmpresarialVO.getCodigoMatricula()));
                                    aluno.setNome(autorizacaoAcessoGrupoEmpresarialVO.getNomePessoa());
                                    aluno.setUrlFoto(Uteis.getPaintFotoDaNuvem(autorizacaoAcessoGrupoEmpresarialVO.getFotoKey()));
                                    aluno.setCodigoPessoa(autorizacaoAcessoGrupoEmpresarialVO.getCodigoPessoa());

                                    aluno.setAutorizadoGestaoRede(rs.getBoolean("autorizadogestaorede"));
                                    aluno.setMatriculaAutorizado(rs.getInt("matriculaAutorizado"));
                                    aluno.setCodAcessoAutorizado(rs.getString("codacessoautorizado"));
                                }
                            }

                        } catch (Exception e) {
                            Uteis.logar(e, AgendaModoBDServiceImpl.class);
                        }

                        try {
                            if (rs.getString("situacao") != null
                                    && !rs.getString("situacao").equals("AT") && UteisValidacao.emptyString(bookingid)) {
                                try (ResultSet rsCheckinGympass = ConexaoZWServiceImpl.criarConsulta("SELECT EXISTS ( SELECT ic.token \n" +
                                        "FROM infocheckin ic \n" +
                                        "INNER JOIN periodoacessocliente pac ON pac.codigo = ic.periodoacesso \n" +
                                        "WHERE ic.cliente = " + rs.getInt("codigocliente") + " \n" +
                                        "AND '" + rs.getDate("dia") +
                                        "' BETWEEN pac.datainicioacesso AND pac.datafinalacesso) as temCheckin", con)) {
                                    if (rsCheckinGympass.next()) {
                                        aluno.setGymPass(rsCheckinGympass.getBoolean("temCheckin"));
                                    }
                                } catch (Exception e){
                                    Uteis.logar(e, AulasColetivasModoBDServiceImpl.class);
                                    aluno.setGymPass(false);
                                }

                                // valida caso quando checkin não é feito automaticamente via chamada gympass webhook
                                if (!aluno.isGymPass()) {
                                    try (ResultSet rsCheckinManualGympass = ConexaoZWServiceImpl.criarConsulta("SELECT EXISTS (" +
                                            "SELECT * FROM periodoacessocliente pac  \n" +
                                            "WHERE pac.pessoa = " + rs.getInt("codigopessoa") + " \n" +
                                            "AND pac.tokengympass IS NOT NULL AND pac.tokengympass <> ''" +
                                            "AND '" + rs.getDate("dia") + "' BETWEEN pac.datainicioacesso AND pac.datafinalacesso" +
                                            ") as temCheckin", con)) {
                                        if (rsCheckinManualGympass.next()) {
                                            aluno.setGymPass(rsCheckinManualGympass.getBoolean("temCheckin"));
                                        }
                                    } catch (Exception e){
                                        Uteis.logar(e, AulasColetivasModoBDServiceImpl.class);
                                    }
                                }
                            }
                        } catch (Exception e) {
                            Uteis.logar(e, AgendaModoBDServiceImpl.class);
                        }

                        try {
                            if (!UteisValidacao.emptyString(aluno.getUserIdSelfloops())) {
                                AlunoTurmaDTO aa = new AlunoTurmaDTO();
                                aa.setUserIdSelfloops(aluno.getUserIdSelfloops());
                                selfloopsConfiguracoesService.obterRankingCourseDoDiaIntegracaoSelfloops(ctx, rs.getInt("empCliente"), codigoHorario, Calendario.getData(dia, "yyyyMMdd"), aa);
                                aluno.setAveragePower(aa.getAveragePower());
                                aluno.setCalories(aa.getCalories());
                                aluno.setTempoDeAula(aa.getTempoDeAula());
                                ordenarPorAveragePower = true;
                            }
                        } catch (Exception e) {
                            Uteis.logar(e, AgendaModoBDServiceImpl.class);
                        }

                        if (!UteisValidacao.emptyString(bookingid)) {
                            aluno.setGymPass(true);
                        }

                        alunos.add(aluno);
                    }
                }
            }
            if(ordenarPorAveragePower) {
                ordenarPorAveragePower(alunos);
            }
            return alunos;
    }

    public static void ordenarPorAveragePower(List<AgendadoJSON> listaAlunos) {
        Collections.sort(listaAlunos, new Comparator<AgendadoJSON>() {
            @Override
            public int compare(AgendadoJSON a1, AgendadoJSON a2) {
                Integer power1 = a1.getAveragePower() != null ? a1.getAveragePower() : 0;
                Integer power2 = a2.getAveragePower() != null ? a2.getAveragePower() : 0;
                return power2.compareTo(power1); // ordem decrescente
            }
        });

        // Atualiza a posição no ranking
        for (int i = 0; i < listaAlunos.size(); i++) {
            listaAlunos.get(i).setPosicaoRankingAluno(i + 1); // posição começa em 1
        }
    }

    public HashMap<String, AgendamentoConfirmadoJSON> obterMapaConfirmados(Connection con, Date diaIn, Date diaFim) throws Exception {
        List<AgendamentoConfirmadoJSON> confirmados = consultarAgendamentosConfirmados(con, diaIn, diaFim);
        HashMap<String, AgendamentoConfirmadoJSON> agendamentoConfirmado = new HashMap<String, AgendamentoConfirmadoJSON>();
        for (AgendamentoConfirmadoJSON agendamento : confirmados) {
            agendamentoConfirmado.put(agendamento.getIdAgendamento() + "_" + agendamento.getCodigoCliente(), agendamento);
        }
        return agendamentoConfirmado;
    }

    public List<AgendamentoConfirmadoJSON> consultarAgendamentosConfirmados(Connection con, Date inicio, Date fim)throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select cliente, diaAula, horario from aulaconfirmada \n");
        sql.append("where diaAula BETWEEN ? and ? ");
        PreparedStatement pst = con.prepareStatement(sql.toString());
        pst.setTimestamp(1, Uteis.getDataJDBCTimestamp(Calendario.getDataComHoraZerada(inicio)));
        pst.setTimestamp(2, Uteis.getDataJDBCTimestamp(Calendario.getDataComHora(fim, "23:59:59")));
        List<AgendamentoConfirmadoJSON> lista;
        try (ResultSet rs = pst.executeQuery()) {
            lista = new ArrayList<AgendamentoConfirmadoJSON>();
            while (rs.next()) {
                AgendamentoConfirmadoJSON json = new AgendamentoConfirmadoJSON();
                json.setCodigoCliente(rs.getInt("cliente"));
                json.setIdAgendamento(rs.getInt("horario") + "_" + Uteis.getData(rs.getDate("diaAula"), "ddMMyy"));
                lista.add(json);
            }
        }
        return lista;
    }

    public ResultAlunoClienteSinteticoJSON obterAlunosDeUmaAula(final String key, final Integer codigoHorario, Date dia) throws Exception{
        ResultAlunoClienteSinteticoJSON resultado = new ResultAlunoClienteSinteticoJSON();
        try (Connection con = conexaoZWService.conexaoZw(key)) {
            resultado.setResultAgendado(consultarAlunosDeUmaAula(key, con, codigoHorario, dia));
            resultado.setResultCliSintentico(clienteSinteticoDao.findClienteSintetico(key, resultado.getResultAgendado()));
        }
        return resultado;
    }

}
