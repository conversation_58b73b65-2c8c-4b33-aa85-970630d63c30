package br.com.pacto.service.impl.cliente.perfil;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Ficha do dia")
public class FichaDoDiaDTO {

    @ApiModelProperty(value = "ID da ficha do dia", example = "1")
    private Integer id;
    @ApiModelProperty(value = "Nome da ficha do dia", example = "Ficha 1")
    private String nome;
    @ApiModelProperty(value = "Vezes", example = "1")
    private Integer vezes;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Integer getVezes() {
        return vezes;
    }

    public void setVezes(Integer vezes) {
        this.vezes = vezes;
    }
}
