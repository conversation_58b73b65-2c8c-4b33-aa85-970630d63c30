package br.com.pacto.service.impl.notificacao.excecao;

import br.com.pacto.service.exception.ExcecaoSistema;

/**
 * Created by paulo on 23/07/2019.
 */
public enum PerfilExcecoes implements ExcecaoSistema {

    ERRO_CRIAR_PERFIL("erro_criar_perfil", "Erro ao criar perfil"),
    ERRO_EDITAR_PERFIL("erro_editar_perfil", "Erro ao editar perfil"),
    ERRO_NOME_NAO_INFORMADO("erro_nome_nao_informado", "Erro nome do perfil não informado"),
    ERRO_TIPO_PERFIL_NAO_INFORMADO("erro_tipo_perfil_nao_informado", "Erro tipo do perfil não informado"),
    ERRO_AO_BUSCAR_PERFIS("erro_ao_buscar_perfis", "Erro ao buscar os perfis"),
    ERRO_PERFIL_DUPLICADO("erro_perfil_duplicado", "registro_duplicado"),
    ERRO_AO_EXCLUIR_PERFIL("erro_ao_excluir_perfil", "Erro ao excluir perfil"),
    ERRO_REGISTRO_ESTA_SENDO_UTILIZADO("erro_registro_esta_sendo_utilizado", "registro_esta_sendo_utilizado");

    private String chave;
    private String descricao;

    PerfilExcecoes(String chave, String descricao) {
        this.chave = chave;
        this.descricao = descricao;
    }

    @Override
    public String getChaveExcecao() {
        return chave;
    }
    @Override
    public String getDescricaoExcecao() {
        return descricao;
    }
}
