/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.impl.notificacao;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ExecuteRequestHttpService;
import br.com.pacto.bean.Agendamento;
import br.com.pacto.bean.agenda.LembreteAgendamento;
import br.com.pacto.bean.agenda.TipoLembreteEnum;
import br.com.pacto.bean.aula.AgendamentoLocacao;
import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.configuracoes.ConfiguracaoSistema;
import br.com.pacto.bean.configuracoes.ConfiguracoesEnum;
import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.bean.notificacao.Notificacao;
import br.com.pacto.bean.notificacao.TipoNotificacaoEnum;
import br.com.pacto.bean.notificacao.VisibilidadeNotificacaoEnum;
import br.com.pacto.bean.pessoa.StatusPessoa;
import br.com.pacto.bean.programa.ProgramaTreino;
import br.com.pacto.bean.serie.SerieRealizada;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.notificacao.PushMobileRunnable;
import br.com.pacto.dao.intf.agenda.LembreteAgendamentoDao;
import br.com.pacto.dao.intf.notificacao.NotificacaoDao;
import br.com.pacto.dao.intf.programa.ProgramaTreinoDao;
import br.com.pacto.dto.builders.notificacao.NotificacaoDTOBuilder;
import br.com.pacto.dto.notificacao.NotificacaoDTO;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.notificacao.excecao.NotificacaoExcecoes;
import br.com.pacto.service.intf.Validacao;
import br.com.pacto.service.intf.cliente.ClienteSinteticoService;
import br.com.pacto.service.intf.configuracoes.ConfiguracaoSistemaService;
import br.com.pacto.service.intf.empresa.EmpresaService;
import br.com.pacto.service.intf.notificacao.NotificacaoService;
import br.com.pacto.service.intf.professor.ProfessorSinteticoService;
import br.com.pacto.service.intf.tipoEvento.TipoEventoService;
import br.com.pacto.service.intf.usuario.UsuarioService;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;
import br.com.pacto.util.ViewUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.util.EntityUtils;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import servicos.integracao.zw.IntegracaoCadastrosWSConsumer;

import java.io.IOException;
import java.net.URLEncoder;
import java.sql.ResultSet;
import java.text.Normalizer;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
@Service
public class NotificacaoServiceImpl implements NotificacaoService {

    private static final int MAXIMO_NOTIFICACOES_CONSULTADAS = 40;
    private static final String NULL_STRING = "null";

    public static final String CHANNEL = "/notifications";
    public static final String URL_SMS = "https://s.smsup.com.br/smsservice/api/sender/do";
    public static final String COUNTRY = "55";
    public static final Integer TIMEOUT_PUSH = 10000;

    @Autowired
    private ViewUtils viewUtils;
    @Autowired
    private Validacao validacao;
    @Autowired
    private NotificacaoDao notificacaoDao;
    @Autowired
    private ProgramaTreinoDao programaTreinoDao;
    @Autowired
    private LembreteAgendamentoDao lembreteAgendamentoDao;
    @Autowired
    private UsuarioService usuarioService;
    @Autowired
    private ClienteSinteticoService clienteService;
    @Autowired
    private ProfessorSinteticoService professorService;
    @Autowired
    private EmpresaService empresaService;
    @Autowired
    private TipoEventoService eventoService;
    @Autowired
    private ConfiguracaoSistemaService configuracaoSistemaService;
    @Autowired
    private SessaoService sessaoService;

    public ViewUtils getViewUtils() {
        return viewUtils;
    }

    public void setViewUtils(ViewUtils viewUtils) {
        this.viewUtils = viewUtils;
    }

    public Validacao getValidacao() {
        return validacao;
    }

    public void setValidacao(Validacao validacao) {
        this.validacao = validacao;
    }

    public NotificacaoDao getNotificacaoDao() {
        return this.notificacaoDao;
    }

    public void setNotificacaoDao(NotificacaoDao notificacaoDao) {
        this.notificacaoDao = notificacaoDao;
    }

    public Notificacao alterar(final String ctx, Notificacao object) throws ServiceException {
        try {
            return getNotificacaoDao().update(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    public void excluir(final String ctx, Notificacao object) throws ServiceException {
        try {
            getNotificacaoDao().delete(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    public Notificacao inserir(final String ctx, Notificacao object) throws ServiceException {
        try {
            return getNotificacaoDao().insert(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    public Notificacao obterObjetoPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException {
        try {
            return getNotificacaoDao().findObjectByParam(ctx, query, params);
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    public Notificacao obterPorId(final String ctx, Integer id) throws ServiceException {
        try {
            return getNotificacaoDao().findById(ctx, id);
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    public List<Notificacao> obterPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException {
        try {
            return getNotificacaoDao().findByParam(ctx, query, params);
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    public List<Notificacao> obterPorParam(final String ctx, String query,
                                           Map<String, Object> params, int max, int index)
            throws ServiceException {
        try {
            return getNotificacaoDao().findByParam(ctx, query, params, max, index);
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    public List<Notificacao> obterTodos(final String ctx, Integer idProfessor) throws ServiceException {
        try {
            return getNotificacaoDao().findListByAttributes(ctx, new String[]{"professor.codigo"}, new Object[]{idProfessor},
                    "dataRegistro DESC", 0);
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    private void pushWebApp(final String ctx, final String dados) {
        /*try {
         PushContext pushContext = PushContextFactory.getDefault().getPushContext();
         pushContext.push(CHANNEL + ctx, dados);
         } catch (Exception e) {
         e.printStackTrace();
         }*/
    }

    private boolean pushSMS(final String key, final Integer codEmpresa,
                            final String mensagem, final String telefones) throws ServiceException, JSONException {
        try {
            ConfiguracaoSistema css = configuracaoSistemaService.consultarPorTipo(key, ConfiguracoesEnum.SMS_NOTIFICACAO);
            if (telefones != null && !telefones.isEmpty() && mensagem != null && !mensagem.isEmpty() && css.getValorAsBoolean()) {
                Empresa emp = empresaService.obterPorIdZW(key, codEmpresa);
                if (emp != null && emp.getTokenSMS() != null && !emp.getTokenSMS().isEmpty()) {
                    String[] fones = telefones.split(";");
                    if (fones != null && fones.length > 0) {
                        for (int i = 0; i < fones.length; i++) {
                            String numero = fones[i];
                            numero = numero.replaceAll("[^0-9]", "");
                            String texto = Normalizer.normalize(mensagem, Normalizer.Form.NFD);
                            texto = texto.replaceAll("[\\p{InCombiningDiacriticalMarks}]", "");
                            JSONObject json = enviarParaApiSms(key, emp.getTokenSMS(), texto, numero);
                            if (!json.isNull("return") && json.getString("return").toUpperCase().startsWith("OK")) {
                                return true;
                            } else if (emp != null && emp.getTokenSMSShortcode() != null && !emp.getTokenSMSShortcode().isEmpty()) {
                                JSONObject jsonTentativa2 = enviarParaApiSms(key, emp.getTokenSMSShortcode(), texto, numero);
                                if (!jsonTentativa2.isNull("return") && jsonTentativa2.getString("return").toUpperCase().startsWith("OK")) {
                                    return true;
                                }
                            }
                        }
                    }
                }
            }
        } catch (ServiceException ex) {
            Logger.getLogger(NotificacaoServiceImpl.class.getName()).log(Level.SEVERE, null, ex);
            throw new ServiceException(ex.getMessage());
        }
        return false;
    }

    private JSONObject enviarParaApiSms(String key, String token, String texto, String numero) throws ServiceException {
        try {
            String paramKey = "key=" + key;
            String paramToken = "token=" + token;
            String paramMsgs = "msgs=" + URLEncoder.encode(new StringBuilder(COUNTRY).append(numero).append("|").append(texto).toString(), "iso-8859-1");
            String fullUrl = URL_SMS + "?" + paramKey + "&" + paramToken + "&" + paramMsgs;
            HttpPost post = new HttpPost(fullUrl);
            HttpClient httpClient = ExecuteRequestHttpService.createConnector();
            HttpResponse response = httpClient.execute(post);
            String responseBody = EntityUtils.toString(response.getEntity());
            Uteis.logarDebug("## CTX: " + key + " RETORNO DA API SMS:" + responseBody);
            return new JSONObject(responseBody);
        } catch (IOException ex) {
            Logger.getLogger(NotificacaoServiceImpl.class.getName()).log(Level.SEVERE, null, ex);
            throw new ServiceException(ex.getMessage());
        }
    }

    public void notificarSerieRealizada(final String ctx, final SerieRealizada serieRealizada, TipoNotificacaoEnum tipoNotificacao) {
        try {
            if (tipoNotificacao.isConfigurado() && !configuracaoSistemaService.notificacaoConfigurada(ctx, tipoNotificacao)) {
                return;
            }
            Notificacao notf = new Notificacao();
            notf.setCliente(serieRealizada.getTreinoRealizado().getCliente());
            notf.setTipo(tipoNotificacao);
            notf.setProfessor(serieRealizada.getTreinoRealizado().getProfessor());
            notf.setDataRegistro(Calendario.hoje());
            notf.setSerieRealizada(serieRealizada);
            notf.setNome(String.format("%s", new Object[]{
                    notf.getTexto()}));
            getNotificacaoDao().insertAlgunsCampos(ctx, new String[]{
                            "cliente_codigo",
                            "tipo",
                            "professor_codigo",
                            "dataregistro",
                            "serierealizada_codigo",
                            "nome",
                            "lida"},
                    new Object[]{
                            notf.getCliente().getCodigo(),
                            notf.getTipo().getId(),
                            notf.getProfessor().getCodigo(),
                            notf.getDataRegistro(),
                            notf.getSerieRealizada().getCodigo(),
                            notf.getNome(),
                            false
                    });
//            inserir(ctx, notf);
            pushWebApp(ctx, notf.toJSON().toString());
        } catch (Exception e) {
            Uteis.logarDebug(String.format("notificarSerieRealizada chave: %s => cliente: %s - exception: %s", ctx, serieRealizada.getTreinoRealizado().getCliente().getCodigo(), e.getMessage()));
        }
    }

    public void notificarStatus(final String ctx, final StatusPessoa statusPessoa, TipoNotificacaoEnum tipoNotificacao) {
        try {
            boolean ntfAlunoEmRisco = false;
            boolean ntfAlunoChegou = false;
            if (tipoNotificacao.equals(TipoNotificacaoEnum.ALUNO_CHEGOU)) {
                ntfAlunoEmRisco = TipoNotificacaoEnum.ALUNO_EM_RISCO.isConfigurado() && configuracaoSistemaService.notificacaoConfigurada(ctx, TipoNotificacaoEnum.ALUNO_EM_RISCO);
                ntfAlunoChegou = TipoNotificacaoEnum.ALUNO_CHEGOU.isConfigurado() && configuracaoSistemaService.notificacaoConfigurada(ctx, TipoNotificacaoEnum.ALUNO_CHEGOU);
            }
            if (tipoNotificacao.isConfigurado() && !configuracaoSistemaService.notificacaoConfigurada(ctx, tipoNotificacao) && !ntfAlunoEmRisco) {
                return;
            }
            final ClienteSintetico aluno = statusPessoa.getUsuario().getCliente();
            if (aluno != null && aluno.getCodigo() != null) {
                Notificacao notfRisco = new Notificacao();
                notfRisco.setCliente(statusPessoa.getUsuario().getCliente());
                notfRisco.setProfessor(statusPessoa.getUsuario().getCliente().getProfessorSintetico());
                notfRisco.setDataRegistro(Calendario.hoje(statusPessoa.getEmpresa().getTimeZoneDefault()));
                if (aluno.getPesoRisco() != null && aluno.getPesoRisco() >= 6) {
                    notfRisco.setTipo(TipoNotificacaoEnum.ALUNO_EM_RISCO);
                    notfRisco.setNome(String.format("Atenção: %s %s", new Object[]{
                            notfRisco.getTexto(),
                            aluno.getPesoRisco()
                    }));
                } else if (ntfAlunoChegou) {
                    notfRisco.setTipo(TipoNotificacaoEnum.ALUNO_CHEGOU);
                    notfRisco.setNome(String.format("%s.", new Object[]{
                            notfRisco.getTexto()
                    }));
                } else {
                    return;
                }
                inserir(ctx, notfRisco);
                pushWebApp(ctx, notfRisco.toJSON().toString());
            } else {
                Notificacao notf = new Notificacao();
                notf.setCliente(statusPessoa.getUsuario().getCliente());
                notf.setTipo(tipoNotificacao);
                notf.setProfessor(statusPessoa.getUsuario().getCliente().getProfessorSintetico());
                notf.setDataRegistro(Calendario.hoje(statusPessoa.getEmpresa().getTimeZoneDefault()));
                notf.setNome(String.format("%s", new Object[]{
                        notf.getTexto()}));
                inserir(ctx, notf);
                pushWebApp(ctx, notf.toJSON().toString());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public Agendamento loadLazyAttributes(final String ctx, Agendamento agendamento) throws Exception {
        List<Integer> codCliente = notificacaoDao.listOfObjects(ctx, "select cliente_codigo from agendamento where codigo = " + agendamento.getCodigo());
        List<Integer> codProfessor = notificacaoDao.listOfObjects(ctx, "select professor_codigo from agendamento where codigo = " + agendamento.getCodigo());
        List<Integer> codTipoEvento = notificacaoDao.listOfObjects(ctx, "select tipoevento_codigo from agendamento where codigo = " + agendamento.getCodigo());
        agendamento.setCliente(clienteService.obterPorId(ctx, codCliente.get(0)));
        agendamento.setProfessor(professorService.obterPorId(ctx, codProfessor.get(0)));
        if (codTipoEvento.get(0) != null) {
            agendamento.setTipoEvento(eventoService.obterPorId(ctx, codTipoEvento.get(0)));
        }
        return agendamento;
    }

    public void notificarSolicitacaoReagendamento(final String ctx, Agendamento agendamentoAnterior,
                                                  final String novaData, final String novoHorario,
                                                  TipoNotificacaoEnum tipoNotificacao) {
        try {
            if (tipoNotificacao.isConfigurado() && !configuracaoSistemaService.notificacaoConfigurada(ctx, tipoNotificacao)) {
                return;
            }
            agendamentoAnterior = loadLazyAttributes(ctx, agendamentoAnterior);
            Notificacao notf = new Notificacao();
            notf.setCliente(agendamentoAnterior.getCliente());
            notf.setTipo(tipoNotificacao);
            notf.setProfessor(agendamentoAnterior.getProfessor());
            notf.setDataRegistro(Calendario.hoje());
            String nome = agendamentoAnterior.getTipoEvento() != null
                    ? agendamentoAnterior.getTipoEvento().getNome()
                    : agendamentoAnterior.getHorarioDisponibilidade().getDisponibilidade().getNome();
            notf.setNome(String.format("%s %s de %s para %s entre %s", new Object[]{
                    notf.getTexto(),
                    nome,
                    Calendario.getData(agendamentoAnterior.getInicio(), "dd/MM") + " às "
                            + Calendario.getData(agendamentoAnterior.getInicio(), Calendario.MASC_HORA),
                    novaData,
                    novoHorario
            }));
            inserir(ctx, notf);
            pushWebApp(ctx, notf.toJSON().toString());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void notificarLembreteAgendamento(final String ctx, Agendamento agendamento,
                                             final TipoLembreteEnum tipoLembrete) {
        try {
            //
            if (!configuracaoSistemaService.notificacaoConfigurada(ctx, TipoNotificacaoEnum.AGENDA)) {
                return;
            }
            agendamento = loadLazyAttributes(ctx, agendamento);
            Usuario u = usuarioService.obterPorAtributo(ctx, "cliente.codigo", agendamento.getCliente().getCodigo());
            Notificacao notf = new Notificacao();
            notf.setCliente(agendamento.getCliente());
            notf.setTipo(TipoNotificacaoEnum.AGENDA);
            notf.setProfessor(agendamento.getProfessor());
            notf.setDataRegistro(Calendario.hoje());
            String nome = agendamento.getTipoEvento() != null
                    ? agendamento.getTipoEvento().getNome()
                    : agendamento.getHorarioDisponibilidade().getDisponibilidade().getNome();
            notf.setNome(String.format(viewUtils.getMensagem("lembrete.agendamento"),
                    new Object[]{nome,
                            Calendario.getData(agendamento.getInicio(), Calendario.MASC_DATA) + " - "
                                    + Calendario.getData(agendamento.getInicio(), Calendario.MASC_HORA)}));
            if (u != null) {
                inserir(ctx, notf);
                LembreteAgendamento lembrete = new LembreteAgendamento();
                lembrete.setAgendamento(agendamento);
                lembrete.setTipo(tipoLembrete);
                lembrete.setTipoNotificacao(notf.getTipo());
                lembreteAgendamentoDao.insert(ctx, lembrete);
                final String msg = String.format(viewUtils.getMensagem("lembrete.agendamento"),
                        new Object[]{nome,
                                Calendario.getData(agendamento.getInicio(), Calendario.MASC_DATA) + " - "
                                        + Calendario.getData(agendamento.getInicio(), Calendario.MASC_HORA)});

                sendPush(ctx, u, notf, msg);

                if (pushSMS(ctx, u.getEmpresaZW(), msg, agendamento.getCliente().getTelefones())) {
                    notf.setSmsEnviado(true);
                }
                alterar(ctx, notf);
            }
        } catch (Exception e) {
            Logger.getLogger(NotificacaoServiceImpl.class.getName()).log(Level.SEVERE, null, e);
            e.printStackTrace();
        }
    }

    public void notificarAgendamentoNovoOuAlterado(final String ctx, Agendamento agendamento,
                                                   final TipoLembreteEnum tipoLembrete, final TipoNotificacaoEnum tipoNotificacao,
                                                   final String idMensagem) {
        try {
            //
            if (tipoNotificacao.isConfigurado() && !configuracaoSistemaService.notificacaoConfigurada(ctx, tipoNotificacao)) {
                return;
            }
            agendamento = loadLazyAttributes(ctx, agendamento);
            Usuario u = usuarioService.obterPorAtributo(ctx, "cliente.codigo",
                    agendamento.getCliente().getCodigo());
            Notificacao notf = new Notificacao();
            notf.setCliente(agendamento.getCliente());
            notf.setTipo(tipoNotificacao);
            notf.setProfessor(agendamento.getProfessor());
            notf.setDataRegistro(Calendario.hoje());
            String nome = agendamento.getTipoEvento() != null
                    ? agendamento.getTipoEvento().getNome()
                    : agendamento.getHorarioDisponibilidade().getDisponibilidade().getNome();
            notf.setNome(String.format(viewUtils.getMensagem(idMensagem),
                    new Object[]{nome,
                            Calendario.getData(agendamento.getInicio(), Calendario.MASC_DATA) + " - "
                                    + Calendario.getData(agendamento.getInicio(), Calendario.MASC_HORA)}));
            if (u != null) {
                final String msg = String.format(viewUtils.getMensagem(idMensagem),
                        new Object[]{nome,
                                Calendario.getData(agendamento.getInicio(), Calendario.MASC_DATA) + " - "
                                        + Calendario.getData(agendamento.getInicio(), Calendario.MASC_HORA)});

                inserir(ctx, notf);
                LembreteAgendamento lembrete = new LembreteAgendamento();
                lembrete.setAgendamento(agendamento);
                lembrete.setTipo(tipoLembrete);
                lembrete.setTipoNotificacao(tipoNotificacao);
                lembreteAgendamentoDao.insert(ctx, lembrete);

                sendPush(ctx, u, notf, msg);
                if (pushSMS(ctx, u.getEmpresaZW(), msg, agendamento.getCliente().getTelefones())) {
                    notf.setSmsEnviado(true);
                }
                alterar(ctx, notf);
            }
        } catch (Exception e) {
            Logger.getLogger(NotificacaoServiceImpl.class.getName()).log(Level.SEVERE, null, e);
            e.printStackTrace();
        }
    }

    public void notificarCancelamentoAgendamentoLocacao(final String ctx, AgendamentoLocacao agendamentoLocacao) {
        try {
            Usuario u = usuarioService.obterPorAtributo(ctx, "cliente.codigo", agendamentoLocacao.getCliente().getCodigo());
            Notificacao notf = new Notificacao();
            notf.setCliente(agendamentoLocacao.getCliente());
            notf.setTipo(TipoNotificacaoEnum.AGENDAMENTO_LOCACAO_CANCELADO);
            notf.setProfessor(agendamentoLocacao.getCliente().getProfessorSintetico());
            notf.setDataRegistro(Calendario.hoje());

            String dataAgendamento = Uteis.getDataAplicandoFormatacao(agendamentoLocacao.getInicio(), "dd/MM/yyyy HH:mm");
            final String msg = "O agendamento do dia " + dataAgendamento + " foi cancelado com a justificativa: " + agendamentoLocacao.getJustificativa();
            notf.setNome(msg);
            if (u != null) {
                inserir(ctx, notf);
                sendPush(ctx, u, notf, msg);
                alterar(ctx, notf);
            }
        } catch (Exception e) {
            Logger.getLogger(NotificacaoServiceImpl.class.getName()).log(Level.SEVERE, null, e);
            e.printStackTrace();
        }
    }

    public void notificarPrograma(final String ctx,
                                  final ProgramaTreino programa, final Date dataBase, final TipoLembreteEnum tipoLembrete,
                                  TipoNotificacaoEnum tipoNotf, final String msg) {
        try {
            if (tipoNotf.isConfigurado() && !configuracaoSistemaService.notificacaoConfigurada(ctx, tipoNotf)) {
                return;
            }
            Notificacao notf = new Notificacao();
            notf.setCliente(programa.getCliente());
            notf.setTipo(tipoNotf);
            notf.setProfessor(programa.getProfessorCarteira());
            notf.setDataRegistro(dataBase);
            notf.setPrograma(programa);
            notf.setNome(msg);
            //
            Usuario u = usuarioService.obterPorAtributo(ctx, "cliente.codigo", programa.getCliente().getCodigo());
            inserir(ctx, notf);
            LembreteAgendamento lembrete = new LembreteAgendamento();
            lembrete.setDataCriacao(dataBase);
            lembrete.setPrograma(programa);
            lembrete.setTipo(tipoLembrete);
            lembrete.setTipoNotificacao(tipoNotf);
            lembreteAgendamentoDao.insert(ctx, lembrete);
            sendPush(ctx, u, notf, msg);
            if (u != null) {
                if (pushSMS(ctx, u.getEmpresaZW(), msg, programa.getCliente().getTelefones())) {
                    notf.setSmsEnviado(true);
                }
            }
            alterar(ctx, notf);

        } catch (Exception e) {
            Logger.getLogger(NotificacaoServiceImpl.class.getName()).log(Level.SEVERE, null, e);
            e.printStackTrace();
        }
    }

    public void notificarStatusAgendamento(final String ctx, final Agendamento agendamento,
                                           TipoNotificacaoEnum tipoNotificacao, final String msgID) {
        try {
            if (tipoNotificacao.isConfigurado() && !configuracaoSistemaService.notificacaoConfigurada(ctx, tipoNotificacao)) {
                return;
            }
            Notificacao notf = new Notificacao();
            notf.setCliente(agendamento.getCliente());
            notf.setTipo(tipoNotificacao);
            notf.setProfessor(agendamento.getProfessor());
            notf.setDataRegistro(Calendario.hoje());
            String nome = agendamento.getTipoEvento() != null
                    ? agendamento.getTipoEvento().getNome()
                    : agendamento.getHorarioDisponibilidade().getDisponibilidade().getNome();
            notf.setNome(String.format(viewUtils.getMensagem(msgID),
                    new Object[]{nome,
                            Calendario.getData(agendamento.getInicio(), Calendario.MASC_DATA) + " - "
                                    + Calendario.getData(agendamento.getInicio(), Calendario.MASC_HORA)}));
            inserir(ctx, notf);
            pushWebApp(ctx, notf.toJSON().toString());
            //
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public Number obterTotalNaoLidas(final String ctx, Integer idProfessor) {
        try {
            return getNotificacaoDao().count(ctx, "codigo", new String[]{"lida", "professor.codigo"},
                    new Object[]{false, idProfessor});
        } catch (Exception ex) {
            Logger.getLogger(NotificacaoServiceImpl.class.getName()).log(Level.SEVERE, null, ex);
        }
        return 0;
    }

    public List<Notificacao> obterNotificacoesSeriesRealizadas(final String ctx, final Integer idSerieRealizada) throws ServiceException {
        try {
            final String sql = "select obj from Notificacao obj where obj.serieRealizada.codigo = :serie ";
            Map<String, Object> params = new HashMap<String, Object>();
            params.put("serie", idSerieRealizada);
            return getNotificacaoDao().findByParam(ctx, sql, params);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    public List<Notificacao> obterUltimasCliente(final String ctx, final Integer idCliente, final Integer maxResults) throws ServiceException {
        try {
            return getNotificacaoDao().findListByAttributes(ctx, new String[]{"cliente.codigo"}, new Object[]{idCliente}, "dataRegistro DESC", maxResults);
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    public List<Notificacao> obterUltimasProfessor(final String ctx, final Integer idProfessor, boolean apenasNaoLidas, final Integer maxResults) throws ServiceException {
        try {
            if (apenasNaoLidas) {
                return getNotificacaoDao().findListByAttributes(ctx, new String[]{"professor.codigo", "lida"}, new Object[]{idProfessor, false}, "dataRegistro DESC", maxResults);
            } else {
                return getNotificacaoDao().findListByAttributes(ctx, new String[]{"professor.codigo"}, new Object[]{idProfessor}, "dataRegistro DESC", maxResults);
            }
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    public List<Notificacao> obterMaisNotificacoes(final String ctx, final Integer idProfessor, final Integer idCliente,
                                                   boolean apenasNaoLidas,
                                                   final Integer maxResults, final Integer index, VisibilidadeNotificacaoEnum visibilidadeNotificacao) throws ServiceException {
        return obterMaisNotificacoes(ctx, idProfessor, idCliente, apenasNaoLidas, maxResults, index,
                visibilidadeNotificacao, false, null);
    }

    /**
     * Consulta as notificações de acordo com os parâmetros informados
     *
     * @param ctx                     Contexto onde as notificações serão pesquisadas
     * @param idProfessor             ID do professor ao qual as notificações pertencem
     * @param idCliente               ID do cliente ao qual as notificações pertencem
     * @param apenasNaoLidas          Devem ser consultas apenas as notificações que não foram lidas ou todas?
     * @param maxResults              Máximo de resultados a ser retornado na busca
     * @param index                   Índice do primeiro elemento da busca, assim paginamos o resultado
     * @param visibilidadeNotificacao {@link VisibilidadeNotificacaoEnum}
     * @param contarTotal             A quantidade total de notificações que atende aos parâmetros deve ser contadabilizada?
     * @param paginadorDTO            Caso se deva contar a quantidade total, guarda o resultado da mesma nesse objeto
     * @return Uma lista com as notificações encontradas no sistema
     * @throws ServiceException Caso ocorra alguma problema na busca
     */
    public List<Notificacao> obterMaisNotificacoes(final String ctx, final Integer idProfessor, final Integer idCliente,
                                                   boolean apenasNaoLidas, final Integer maxResults, final Integer index,
                                                   VisibilidadeNotificacaoEnum visibilidadeNotificacao, Boolean contarTotal,
                                                   PaginadorDTO paginadorDTO) throws ServiceException {
        try {
            getNotificacaoDao().getCurrentSession(ctx).clear();
            Map<String, Object> params = new HashMap<String, Object>();
            StringBuilder selectString = new StringBuilder("SELECT obj FROM Notificacao obj ");
            StringBuilder whereString = new StringBuilder(" WHERE ativa = true");

            if (idProfessor != null) {
                whereString.append(" AND obj.professor.codigo = :professor ");
                params.put("professor", idProfessor);
            }

            if (idCliente != null) {
                whereString.append(" AND cliente.codigo = :cliente ");
                params.put("cliente", idCliente);
            }

            if (apenasNaoLidas) {
                whereString.append(" AND lida IS NOT TRUE ");
            }

            if (visibilidadeNotificacao != null && visibilidadeNotificacao != VisibilidadeNotificacaoEnum.TODOS) {
                whereString.append(" AND tipo in (:tiposVisiveis) ");
                params.put("tiposVisiveis", TipoNotificacaoEnum.getTiposVisiveis(visibilidadeNotificacao));
            } else {
                //LUIZ FELIPE
                //ADICIONADO PARA NÃO TRAZER AS NOTIFICAÇÕES REFERENTES A CURTIR E COMENTAR NO FEED
                List<TipoNotificacaoEnum> tiposNaoBuscar = new ArrayList<TipoNotificacaoEnum>();
                tiposNaoBuscar.add(TipoNotificacaoEnum.FEED_APP_LIKE);
                tiposNaoBuscar.add(TipoNotificacaoEnum.FEED_APP_COMENTARIO);
                tiposNaoBuscar.add(TipoNotificacaoEnum.DICAS_NUTRI_RESPOSTA);
                whereString.append(" AND tipo not in (:tiposNaoBuscar) ");
                params.put("tiposNaoBuscar", tiposNaoBuscar);
            }

            if (contarTotal && paginadorDTO != null) {
                paginadorDTO.setQuantidadeTotalElementos(
                        getNotificacaoDao().countWithParam(ctx, "codigo", whereString, params).longValue());
            }

            whereString.append(" ORDER BY dataRegistro DESC");

            return getNotificacaoDao().findByParam(ctx, selectString.append(whereString).toString(), params, maxResults, index);
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    public List<Notificacao> marcarComoLidaOuNaoLida(final String ctx, final List<Notificacao> notificacoes, boolean lida) throws ServiceException {
        try {
            if (notificacoes != null && !notificacoes.isEmpty()) {
                getNotificacaoDao().executeNativeSQL(ctx, "update " + Notificacao.class.getSimpleName() + " set lida = " + lida + " where codigo in (" + Uteis.splitFromList("codigo", notificacoes, false) + ")");
                for (Notificacao notificacao : notificacoes) {
                    notificacao.setLida(lida);
                }
            }
            return notificacoes;
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    public Integer marcarLidas(final String ctx,
                            final String username,
                            final String matricula,
                            final Integer idNotificacao,
                            final Boolean todas) throws ServiceException {
        try {
            Integer total = 0;
            StringBuilder where = new StringBuilder(" WHERE not lida and ") ;
            if(todas && !UteisValidacao.emptyString(matricula)) {
                where.append(" cliente_codigo in (select codigo from clientesintetico c where matricula = ");
                where.append(matricula);
                where.append(")");
            } else if(todas && !UteisValidacao.emptyString(username)) {
                where.append(" cliente_codigo in ( select cliente_codigo  from usuario u where username = '");
                where.append(username);
                where.append("')");
            } else {
                where.append(" codigo = ").append(idNotificacao);
            }
            try (ResultSet rs = getNotificacaoDao().createStatement(ctx, "select count(codigo) as total from notificacao " + where.toString())) {
                if (rs.next()) {
                    total = rs.getInt("total");
                }
            }
            getNotificacaoDao().executeNativeSQL(ctx, "update notificacao set lida = true " + where.toString());
            return total;
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    @Override
    public boolean isTreinoVigente(final String ctx, final Integer idCliente) throws ServiceException{
        Date dataAtual = new Date();
        ProgramaTreino programaTreino = programaTreino(ctx,idCliente);
        if(dataAtual.before(programaTreino.getDataProximaRevisao())){
            return true;
        }else{
            return false;
        }

    }

    @Override
    public void geraNotificacaoAppAluno(String ctx, Notificacao notf, String mensagem, Usuario u) throws ServiceException {
        try {
            if(u != null && u.getCodigo() != null) {
                PushMobileRunnable.executarNoticacaoAppDoAluno(ctx, TipoNotificacaoEnum.ALUNO_DA_FILA_ENTROU_NA_AULA.getDescricao(), mensagem, u.getUserName(), "");
            } else {
                notf.setPushEnviado(false);
            }

        } catch (Exception e) {
            notf.setPushEnviado(false);
        }
    }

    private ProgramaTreino programaTreino(final String ctx, final Integer idCliente) throws ServiceException{

        try {
            Map<String, Object> params = new HashMap<String, Object>();
            StringBuilder selectString = new StringBuilder("SELECT obj FROM ProgramaTreino obj ");
            StringBuilder whereString = new StringBuilder(" WHERE cliente.codigo = :cliente");


            if (idCliente != null) {
                params.put("cliente", idCliente);
            }

            whereString.append(" order by dataterminoprevisto desc limit 1 ");

            ProgramaTreino programa = programaTreinoDao.findFirstObjectByParam(ctx, selectString.append(whereString).toString(), params);

            return programa;

        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    @Override
    public Notificacao gerarNotificacao(final String key, Integer idClienteZw, Date data,
                                        String nome, String textoCRM,
                                        TipoNotificacaoEnum tipo, String opcoes, Boolean enviaPushAppAluno) throws ServiceException {
        try {
            Notificacao notf = new Notificacao();
            notf.setCliente(clienteService.obterPorCodigoCliente(key, idClienteZw));
            if (notf.getCliente() == null) {
                throw new Exception("cliente.naoencontrado.treino");
            }
            notf.setNome(nome);
            String mensagem = textoCRM.replaceAll("TAG_NOME", notf.getCliente().getNome()).replaceAll("TAG_PNOME", Uteis.getPrimeiroNome(notf.getCliente().getNome()));
            notf.setTextoCRM(mensagem);
            notf.setDataRegistro(data);
            notf.setTipo(tipo);
            notf.setOpcoes(opcoes);
            Usuario u = usuarioService.obterPorAtributo(key, "cliente.codigo", notf.getCliente().getCodigo());
            if(enviaPushAppAluno) {
                geraNotificacaoAppAluno(key, notf, textoCRM, u);
            } else {
                sendPush(key, u, notf, mensagem);
            }

            return inserir(key, notf);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public Notificacao gravarResposta(final String key, Integer idNotf, String resposta) throws ServiceException {
        try {
            Notificacao notf = obterPorId(key, idNotf);
            if (notf.getOpcoes().contains(";TEXTO")) {
                resposta = resposta.replaceFirst("Confirma;", "");
            }
            notf.setResposta(resposta);
            notf = alterar(key, notf);
            final String url = Aplicacao.getProp(key, Aplicacao.urlZillyonWebInteg);
            IntegracaoCadastrosWSConsumer integracaoWS = (IntegracaoCadastrosWSConsumer) UtilContext.getBean(IntegracaoCadastrosWSConsumer.class);
            integracaoWS.gravarResposta(url, key, idNotf, resposta);
            return notf;
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public Notificacao gerarNotificacaoPorEmail(final String key, String email, String titulo, String texto, String opcoes, Date data, TipoNotificacaoEnum tipo) throws ServiceException {
        try {
            Usuario usuario = usuarioService.consultarPorUserName(key, email);
            if (usuario == null) {
                throw new Exception("Usuário não encontrado.");
            }

            Notificacao notf = new Notificacao();
            notf.setCliente(clienteService.obterPorId(key, usuario.getCliente().getCodigo()));
            if (notf.getCliente() == null) {
                throw new Exception("cliente.naoencontrado.treino");
            }
            notf.setNome(titulo);
            notf.setTextoCRM(texto);
            notf.setDataRegistro(data);
            notf.setTipo(tipo);
            notf.setOpcoes(opcoes);
            sendPush(key, usuario, notf, texto);
            return inserir(key, notf);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public List<NotificacaoDTO> getNotificacoes(PaginadorDTO paginadorDTO) throws ServiceException {
        String chaveUsuario = sessaoService.getUsuarioAtual().getChave();
        String username = sessaoService.getUsuarioAtual().getUsername();

        validarContexto(chaveUsuario);
        validarUsername(username);

        Usuario usuario = usuarioService.obterPorAtributo(chaveUsuario, "username", username);
        if (usuario == null) {
            Logger
                    .getLogger(NotificacaoServiceImpl.class.getName())
                    .log(Level.SEVERE, NotificacaoExcecoes.USUARIO_NAO_ENCONTRADO.getDescricaoExcecao());
            throw new ServiceException(NotificacaoExcecoes.USUARIO_NAO_ENCONTRADO);
        }

        Integer codigoCliente = usuario.getCliente() == null ? null : usuario.getCliente().getCodigo();
        Integer codigoProfessor = usuario.getProfessor() == null ? null : usuario.getProfessor().getCodigo();

        Integer maximoNotficacoes = MAXIMO_NOTIFICACOES_CONSULTADAS;
        Integer indiceInicial = 0;

        if (paginadorDTO != null) {
            maximoNotficacoes = paginadorDTO.getSize() == null ? maximoNotficacoes : paginadorDTO.getSize().intValue();
            indiceInicial = paginadorDTO.getPage() == null || paginadorDTO.getSize() == null ? indiceInicial : paginadorDTO.getPage().intValue() * paginadorDTO.getSize().intValue();
        }

        List<Notificacao> lista;
        try {
            lista = obterMaisNotificacoes(chaveUsuario, codigoProfessor, codigoCliente,false, maximoNotficacoes,
                    indiceInicial, codigoCliente != null ? VisibilidadeNotificacaoEnum.ALUNO : VisibilidadeNotificacaoEnum.PROFESSOR,
                    true, paginadorDTO);
        } catch (Exception e) {
            Logger
                    .getLogger(NotificacaoServiceImpl.class.getName())
                    .log(Level.SEVERE, NotificacaoExcecoes.ERRO_BUSCAR_NOTIFICACOES.getDescricaoExcecao(), e);
            throw new ServiceException(NotificacaoExcecoes.ERRO_BUSCAR_NOTIFICACOES, e);
        }

        List<NotificacaoDTO> notificacoes = new ArrayList<NotificacaoDTO>();
        for (Notificacao notificacao : lista) {
            inserirNotificacao(notificacoes, notificacao);
        }

        return notificacoes;
    }

    public List<NotificacaoDTO> getNotificacoesAlunos(Integer aluno, PaginadorDTO paginadorDTO) throws ServiceException {
        String chaveUsuario = sessaoService.getUsuarioAtual().getChave();
        validarContexto(chaveUsuario);

        Integer maximoNotficacoes = MAXIMO_NOTIFICACOES_CONSULTADAS;
        Integer indiceInicial = 0;

        if (paginadorDTO != null) {
            maximoNotficacoes = paginadorDTO.getSize() == null ? maximoNotficacoes : paginadorDTO.getSize().intValue();
            indiceInicial = paginadorDTO.getPage() == null || paginadorDTO.getSize() == null ? indiceInicial : paginadorDTO.getPage().intValue() * paginadorDTO.getSize().intValue();
        }

        List<Notificacao> lista;
        try {
            lista = obterMaisNotificacoes(chaveUsuario, null, aluno,false, maximoNotficacoes,
                    indiceInicial, VisibilidadeNotificacaoEnum.TODOS,
                    true, paginadorDTO);
        } catch (Exception e) {
            Logger
                    .getLogger(NotificacaoServiceImpl.class.getName())
                    .log(Level.SEVERE, NotificacaoExcecoes.ERRO_BUSCAR_NOTIFICACOES.getDescricaoExcecao(), e);
            throw new ServiceException(NotificacaoExcecoes.ERRO_BUSCAR_NOTIFICACOES, e);
        }

        List<NotificacaoDTO> notificacoes = new ArrayList<NotificacaoDTO>();
        for (Notificacao notificacao : lista) {
            inserirNotificacao(notificacoes, notificacao);
        }

        return notificacoes;
    }

    @Override
    public void marcarComoLida(Integer id) throws ServiceException {
        String chaveUsuario = sessaoService.getUsuarioAtual().getChave();

        validarContexto(chaveUsuario);
        validarID(id);

        Notificacao notificacao;
        try {
            notificacao = obterPorId(chaveUsuario, id);
        } catch (Exception e) {
            throw new ServiceException(NotificacaoExcecoes.NOTIFICACAO_NAO_ENCONTRADA, e);
        }

        try {
            notificacao.setLida(true);
            alterar(chaveUsuario, notificacao);
        } catch (Exception e) {
            throw new ServiceException(NotificacaoExcecoes.ERRO_MARCAR_LIDA, e);
        }
    }

    @Override
    public Long getQuantidadeNaoLidas() throws ServiceException {
        String chaveUsuario = sessaoService.getUsuarioAtual().getChave();
        String username = sessaoService.getUsuarioAtual().getUsername();

        validarContexto(chaveUsuario);
        validarUsername(username);

        Usuario usuario = usuarioService.obterPorAtributo(chaveUsuario, "username", username);

        return (Long) obterTotalNaoLidas(chaveUsuario, usuario.getProfessor().getCodigo());
    }

    private void inserirNotificacao(List<NotificacaoDTO> notificacoes, Notificacao notificacao) {
        String imagemUri = null;
        if (notificacao.getCliente() != null
                && notificacao.getCliente().getPessoa() != null
                && StringUtils.isNotBlank(notificacao.getCliente().getPessoa().getFotoKey())
                && !NULL_STRING.equalsIgnoreCase(notificacao.getCliente().getPessoa().getFotoKey())) {
            // TODO: delegar a busca do objeto de imagem para o ZillyonWeb
            imagemUri = notificacao.getCliente().getPessoa().getFotoKey();
        }

        String tipo = notificacao.getTipo() == null ? null : notificacao.getTipo().getDescricao();
        notificacoes.add(new NotificacaoDTOBuilder()
                .comID(String.valueOf(notificacao.getCodigo()))
                .comGravidade(notificacao.getTipo().getGravidade().getStyle())
                .comdataregistro(notificacao.getDataRegistro().getTime())
                .comConteudo(notificacao.getTexto())
                .comImagem(imagemUri)
                .comTipo(tipo)
                .visualizada(notificacao.isLida())
                .build());
    }

    private void validarUsername(String username) throws ServiceException {
        if (StringUtils.isBlank(username)) {
            throw new ServiceException(NotificacaoExcecoes.USUARIO_NAO_INFORMADO);
        }
    }

    private void validarID(Integer id) throws ServiceException {
        if (id == null) {
            throw new ServiceException(NotificacaoExcecoes.ID_NAO_INFORMADO);
        }
    }

    private void validarContexto(String contexto) throws ServiceException {
        if (StringUtils.isBlank(contexto)) {
            throw new ServiceException(NotificacaoExcecoes.CONTEXTO_NAO_INFORMADO);
        }
    }

    private void sendPush(final String ctx, final Usuario usuario, final Notificacao notf, final String message){
        if (!usuario.getUserName().contains("@"))
            notf.setPushEnviado(false);
        else if (usuario != null){
            PushMobileRunnable.executarNoticacao(ctx, notf.getTipo().getDescricao(), message, usuario.getUserName());
            notf.setPushEnviado(true);
        }
    }
}
