package br.com.pacto.service.impl.gympass.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 22/03/2020
 */
public class BookingsDTO {

    private int class_id;
    private int status;
    private String reason;
    private String virtual_class_url;

    public BookingsDTO() {
    }

    public int getClass_id() {
        return class_id;
    }

    public void setClass_id(int class_id) {
        this.class_id = class_id;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getVirtual_class_url() {
        return virtual_class_url;
    }

    public void setVirtual_class_url(String virtual_class_url) {
        this.virtual_class_url = virtual_class_url;
    }
}
