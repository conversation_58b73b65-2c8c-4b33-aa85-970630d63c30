package br.com.pacto.service.impl.notificacao.excecao;

import br.com.pacto.service.exception.ExcecaoSistema;

/**
 * <AUTHOR> 20/02/2019
 */
public enum EventoUsuarioExcecoes implements ExcecaoSistema {

    ERRO_ATUALIZAR_EVENTO_USUARIO("erro_atualizar_evento_usuario", "Erro ao tentar atualizar o evento do usuario"),
    ERRO_CAMPOS_OBRIGATORIOS_NAO_PREENCHIDO("erro_campos_obrigatorios_nao_preenchidos", "Erro campos obrigatórios não preenchido"),
    ERRO_BUSCAR_EVENTO_USUARIO("erro_buscar_evento_usuario", "Erro ao buscar o evento usuário")
    ;

    private String chave;
    private String descricao;

    EventoUsuarioExcecoes(String chave, String descricao) {
        this.chave = chave;
        this.descricao = descricao;
    }

    @Override
    public String getChaveExcecao() {
        return this.chave;
    }

    @Override
    public String getDescricaoExcecao() {
        return this.descricao;
    }
}
