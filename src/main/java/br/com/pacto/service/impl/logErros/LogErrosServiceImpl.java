package br.com.pacto.service.impl.logErros;

import br.com.pacto.bean.logErros.LogErros;
import br.com.pacto.dao.intf.logErros.LogErrosDao;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.logErros.LogErrosService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
public class LogErrosServiceImpl implements LogErrosService {

    @Autowired
    private LogErrosDao logErrosDao;

    @Override
    public void incluir(String ctx, String descricao, String logJSON) throws ServiceException {
        try {
            LogErros logErros = new LogErros();
            logErros.setDataInclusao(new Date());
            logErros.setDescricao(descricao);
            logErros.setLogJSON(logJSON);
            logErrosDao.insert(ctx, logErros);
        } catch (Exception ex) {
            Uteis.logar(ex, LogErrosServiceImpl.class);
        }
    }
}
