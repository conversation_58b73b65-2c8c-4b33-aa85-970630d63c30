package br.com.pacto.service.impl.selfloops;

import br.com.pacto.base.util.ExecuteRequestHttpService;
import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.bean.selfloops.SelfLoopsConfiguracoes;
import br.com.pacto.controller.json.agendamento.AlunoTurmaDTO;
import br.com.pacto.controller.json.selfloops.*;
import br.com.pacto.dao.intf.selfloops.SelfloopsConfiguracoesDao;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.aula.AulaServiceImpl;
import br.com.pacto.service.intf.conexao.ConexaoZWService;
import br.com.pacto.service.intf.empresa.EmpresaService;
import br.com.pacto.service.intf.selfloops.SelfloopsConfiguracoesService;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.impl.Ordenacao;
import org.apache.http.client.ResponseHandler;
import org.apache.http.client.methods.*;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.BasicResponseHandler;
import org.apache.http.impl.client.CloseableHttpClient;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

@Service
@Qualifier(value = "selfloopsConfiguracoesService")
public class SelfloopsConfiguracoesServiceImpl implements SelfloopsConfiguracoesService {

    @Autowired
    private SelfloopsConfiguracoesDao selfloopsConfiguracoesDao;
    @Autowired
    private EmpresaService empresaService;
    @Autowired
    private ConexaoZWService conexaoZWService;

    public SelfLoopsConfiguracoes obterPorEmpresa(final String ctx, final Integer empresa) throws ServiceException {
        try {
            return selfloopsConfiguracoesDao.findObjectByAttribute(ctx, "empresa.codigo", empresa);
        }catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public void salvarIntegracoesSelfloops(final String ctx, List<SelfloopsDTO> selfloopsDTOList) throws ServiceException {
        try {
            for (SelfloopsDTO selfloopsDTO : selfloopsDTOList) {
                SelfLoopsConfiguracoes configuracoes = obterPorEmpresa(ctx, selfloopsDTO.getEmpresa());
                if (configuracoes == null) {
                    configuracoes = new SelfLoopsConfiguracoes();
                    configuracoes.setEmpresa(new Empresa());
                    configuracoes.getEmpresa().setCodigo(selfloopsDTO.getEmpresa());
                }
                configuracoes.setCodeSelfloops(selfloopsDTO.getCode());
                configuracoes.setEmpresaSelfloops(selfloopsDTO.getEmpresaSelfloops());
                if (UteisValidacao.emptyNumber(configuracoes.getCodigo())) {
                    if (!UteisValidacao.emptyString(configuracoes.getCodeSelfloops())) {
                        String refreshToken = obterTokenSelfloops(configuracoes.getCodeSelfloops(), configuracoes.getRefreshToken(), null, null).getString("refresh_token");
                        configuracoes.setRefreshToken(refreshToken);
                    }
                    selfloopsConfiguracoesDao.insert(ctx, configuracoes);
                } else {
                    selfloopsConfiguracoesDao.update(ctx, configuracoes);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e.getMessage());
        }
    }

    public List<SelfloopsDTO> obterIntegracoesSelfloops(final String ctx) throws ServiceException {
        try {
            List<Empresa> empresas = empresaService.obterTodos(ctx);
            List<SelfloopsDTO> selfloopsDTOS = new ArrayList<>();

            empresas.forEach(n -> {
                SelfloopsDTO selfloopsDTO = new SelfloopsDTO();
                selfloopsDTO.setPactoClientId(Aplicacao.getProp(Aplicacao.pacto_client_id_selfloops));
                selfloopsDTO.setEmpresa(n.getCodigo());
                selfloopsDTO.setNome(n.getNome());
                try {
                    SelfLoopsConfiguracoes configsSelf = obterPorEmpresa(ctx, n.getCodigo());
                    if (configsSelf != null) {
                        selfloopsDTO.setEmpresaSelfloops(configsSelf.getEmpresaSelfloops());
                        selfloopsDTO.setCode(configsSelf.getCodeSelfloops() != null ? configsSelf.getCodeSelfloops() : "");
                        selfloopsDTO.setTeams(obterTeamsSelfloops(configsSelf, ctx, configsSelf.getEmpresa().getCodigo()));
                    }
                } catch (ServiceException e) {
                    throw new RuntimeException(e);
                }
                selfloopsDTOS.add(selfloopsDTO);
            });
            return selfloopsDTOS;
        }catch (Exception ex){
            ex.printStackTrace();
            throw new ServiceException(ex.getMessage());
        }
    }

    public List<TeamsSelfloopsDTO> obterTeamsSelfloops(SelfLoopsConfiguracoes config, String chave, Integer empresaTreino) {
        List<TeamsSelfloopsDTO> teams = new ArrayList<>();
        try {
            if (!UteisValidacao.emptyString(config.getCodeSelfloops())) {
                CloseableHttpClient client = ExecuteRequestHttpService.createConnectorCloseable();
                HttpGet httpGet = new HttpGet(Aplicacao.getProp(Aplicacao.urlApiSelfloops) + "/teams");
                httpGet.setHeader("Authorization", "Bearer " + obterTokenSelfloops(config.getCodeSelfloops(), config.getRefreshToken(), chave, empresaTreino).getString("access_token"));
                CloseableHttpResponse response = client.execute(httpGet);
                ResponseHandler<String> handler = new BasicResponseHandler();
                String body = handler.handleResponse(response);
                client.close();
                JSONObject retorno = new JSONObject(body);
                JSONArray array = retorno.getJSONArray("teams");
                for (int i = 0; i < array.length(); i++) {
                    JSONObject obj = array.getJSONObject(i);
                    TeamsSelfloopsDTO team = new TeamsSelfloopsDTO();
                    team.setId(obj.getString("id"));
                    team.setName(obj.getString("name"));
                    team.setFullname(obj.getString("fullname"));
                    team.setFull_descrition(obj.getString("full_descrition"));
                    teams.add(team);
                }
            }
            return teams;
        } catch (Exception ex) {
            ex.printStackTrace();
            return teams;
        }
    }

    public List<SensorsSelfloopsDTO> obterSensoresSelfloops(SelfLoopsConfiguracoes config, String chave, Integer empresaTreino) {
        List<SensorsSelfloopsDTO> sensors = new ArrayList<>();
        try {
            if (!UteisValidacao.emptyString(config.getCodeSelfloops())) {
                CloseableHttpClient client = ExecuteRequestHttpService.createConnectorCloseable();
                HttpGet httpGet = new HttpGet(Aplicacao.getProp(Aplicacao.urlApiSelfloops) + "/sensors?team="+config.getEmpresaSelfloops());
                httpGet.setHeader("Authorization", "Bearer " + obterTokenSelfloops(config.getCodeSelfloops(), config.getRefreshToken(), chave, empresaTreino).getString("access_token"));
                CloseableHttpResponse response = client.execute(httpGet);
                ResponseHandler<String> handler = new BasicResponseHandler();
                String body = handler.handleResponse(response);
                client.close();
                JSONObject retorno = new JSONObject(body);
                JSONArray array = retorno.getJSONArray("sensors");
                for (int i = 0; i < array.length(); i++) {
                    JSONObject obj = array.getJSONObject(i);
                    SensorsSelfloopsDTO sensor = new SensorsSelfloopsDTO();
                    sensor.setId(obj.getString("id"));
                    sensor.setDevice_id(obj.getString("device_id"));
                    sensor.setDevice_label(obj.getString("device_label"));
                    sensor.setDevice_type(obj.getString("device_type"));
                    sensor.setDevice_category(obj.getString("device_category"));
                    sensor.setCombined_power_id(obj.getString("combined_power_id"));
                    sensor.setCombined_cadence_id(obj.getString("combined_cadence_id"));
                    sensor.setCombined_speedcadence_id(obj.getString("combined_speedcadence_id"));
                    sensor.setCombined_hr_id(obj.getString("combined_hr_id"));
                    sensor.setTeam_id(obj.getString("team_id"));
                    sensors.add(sensor);
                }
            }
            return Ordenacao.ordenarLista(sensors, "device_label");
        } catch (Exception ex) {
            ex.printStackTrace();
            return sensors;
        }
    }

    public JSONObject obterTokenSelfloops(String code, String refresh_token, String ctx, Integer empresaTreino) throws ServiceException {
        try {
            CloseableHttpClient client = ExecuteRequestHttpService.createConnectorCloseable();
            String url = Aplicacao.getProp(Aplicacao.urlOauthTokenSelfloops);
            if (UteisValidacao.emptyString(refresh_token)) {
                url += "?code=" + code + "&grant_type=authorization_code";
            } else {
                url += "?code=" + code + "&refresh_token=" + refresh_token + "&grant_type=refresh_token";
            }
            HttpPost httpPost = new HttpPost(url);
            httpPost.setHeader("Authorization", "Basic " +
                    Base64.getEncoder().encodeToString((Aplicacao.getProp(Aplicacao.pacto_client_id_selfloops) + ":" +
                            Aplicacao.getProp(Aplicacao.pacto_secret_id_selfloops)).getBytes()));
            CloseableHttpResponse response = client.execute(httpPost);
            ResponseHandler<String> handler = new BasicResponseHandler();
            String body = handler.handleResponse(response);
            client.close();
            JSONObject token = new JSONObject(body);

            if (!UteisValidacao.emptyString(ctx) && !UteisValidacao.emptyNumber(empresaTreino)) {
                SelfLoopsConfiguracoes configuracoes = obterPorEmpresa(ctx, empresaTreino);
                if (configuracoes != null && !UteisValidacao.emptyNumber(configuracoes.getCodigo())) {
                    configuracoes.setRefreshToken(token.getString("refresh_token"));
                    selfloopsConfiguracoesDao.update(ctx, configuracoes);
                }
            }

            return token;
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logar(null, ex.getMessage());
            throw new ServiceException("Não foi possivel gerar o token da integração Selfloops");
        }
    }

    public JSONObject criarAulaIntegracaoSelfloops(String code, String refresh_token, CourseScheduleDTO courseScheduleDTO, String chave, Integer empresaTreino) {
        try {
            Logger.getLogger(AulaServiceImpl.class.getName()).log(Level.INFO, "CRIANDO AULA SELFLOOPS JSON -> "+ new JSONObject(courseScheduleDTO));
            CloseableHttpClient client = ExecuteRequestHttpService.createConnectorCloseable();
            String url = Aplicacao.getProp(Aplicacao.urlApiSelfloops) + "/course-schedules";
            HttpPost httpPost = new HttpPost(url);
            httpPost.setHeader("Authorization", "Bearer " + obterTokenSelfloops(code, refresh_token, chave, empresaTreino).getString("access_token"));
            httpPost.setEntity(new StringEntity(new JSONObject(courseScheduleDTO).toString(), ContentType.APPLICATION_JSON));
            CloseableHttpResponse response = client.execute(httpPost);
            ResponseHandler<String> handler = new BasicResponseHandler();
            String body = handler.handleResponse(response);
            client.close();
            return new JSONObject(body);
        } catch (Exception ex) {
            JSONObject json = new JSONObject();
            json.put("error", "criarAulaIntegracaoSelfloops: " + ex.getMessage());
            ex.printStackTrace();
            Uteis.logar(null, json.toString());
            return json;
        }
    }

    public JSONObject alterarAulaIntegracaoSelfloops(String code, String refresh_token, CourseScheduleDTO courseScheduleDTO, String courseScheduleId, String chave, Integer empresaTreino) {
        try {
            Logger.getLogger(AulaServiceImpl.class.getName()).log(Level.INFO, "EDITANDO AULA SELFLOOPS JSON -> "+ new JSONObject(courseScheduleDTO));
            CloseableHttpClient client = ExecuteRequestHttpService.createConnectorCloseable();
            String url = Aplicacao.getProp(Aplicacao.urlApiSelfloops) + "/course-schedules/" + courseScheduleId;
            HttpPut httpPut = new HttpPut(url);
            httpPut.setHeader("Authorization", "Bearer " + obterTokenSelfloops(code, refresh_token, chave, empresaTreino).getString("access_token"));
            httpPut.setEntity(new StringEntity(new JSONObject(courseScheduleDTO).toString(), ContentType.APPLICATION_JSON));
            CloseableHttpResponse response = client.execute(httpPut);
            ResponseHandler<String> handler = new BasicResponseHandler();
            String body = handler.handleResponse(response);
            client.close();
            return new JSONObject(body);
        } catch (Exception ex) {
            JSONObject json = new JSONObject();
            json.put("error", "alterarAulaIntegracaoSelfloops:" + ex.getMessage());
            ex.printStackTrace();
            Uteis.logar(null, json.toString());
            return json;
        }
    }

    public JSONObject deleteAulaIntegracaoSelfloops(String code, String refresh_token, String courseScheduleId, String chave, Integer empresaTreino) throws ServiceException {
        try {
            Logger.getLogger(AulaServiceImpl.class.getName()).log(Level.INFO, "DELETANDO AULA SELFLOOPS ID -> "+ courseScheduleId);
            CloseableHttpClient client = ExecuteRequestHttpService.createConnectorCloseable();
            String url = Aplicacao.getProp(Aplicacao.urlApiSelfloops) + "/course-schedules/" + courseScheduleId;
            HttpDelete httpDelete = new HttpDelete(url);
            httpDelete.setHeader("Authorization", "Bearer " + obterTokenSelfloops(code, refresh_token, chave, empresaTreino).getString("access_token"));
            CloseableHttpResponse response = client.execute(httpDelete);
            JSONObject retorno = new JSONObject();
            retorno.put("status", response.getStatusLine().getStatusCode());
            client.close();
            return retorno;
        } catch (Exception ex) {
            JSONObject json = new JSONObject();
            json.put("error", "deleteAulaIntegracaoSelfloops:" + ex.getMessage());
            ex.printStackTrace();
            Uteis.logar(null, json.toString());
            return json;
        }
    }

    public JSONObject criarAlunoIntegracaoSelfloops(String code, String refresh_token, UserDTO aluno, String chave, Integer empresaTreino) {
        try {
            Logger.getLogger(AulaServiceImpl.class.getName()).log(Level.INFO, "CRIANDO ALUNO SELFLOOPS JSON -> "+ new JSONObject(aluno));
            CloseableHttpClient client = ExecuteRequestHttpService.createConnectorCloseable();
            String url = Aplicacao.getProp(Aplicacao.urlApiSelfloops) + "/users";
            HttpPost httpPost = new HttpPost(url);
            httpPost.setHeader("Authorization", "Bearer " + obterTokenSelfloops(code, refresh_token, chave, empresaTreino).getString("access_token"));
            httpPost.setEntity(new StringEntity(new JSONObject(aluno).toString(), ContentType.APPLICATION_JSON));
            CloseableHttpResponse response = client.execute(httpPost);
            ResponseHandler<String> handler = new BasicResponseHandler();
            String body = handler.handleResponse(response);
            client.close();
            return new JSONObject(body);
        } catch (Exception ex) {
            JSONObject json = new JSONObject();
            json.put("error", "criarAlunoIntegracaoSelfloops: " + ex.getMessage());
            ex.printStackTrace();
            Uteis.logar(null, json.toString());
            return json;
        }
    }

    public JSONObject inserirAlunoAulaSensorIntegracaoSelfloops(String code, String refresh_token, String userSelfloopsId, String courseSelfloopsId, CourseDTO course, String chave, Integer empresaTreino) {
        try {
            Logger.getLogger(AulaServiceImpl.class.getName()).log(Level.INFO, "INSERINDO ALUNO AULA SELFLOOPS JSON -> "+ new JSONObject(course));
            CloseableHttpClient client = ExecuteRequestHttpService.createConnectorCloseable();
            String url = Aplicacao.getProp(Aplicacao.urlApiSelfloops) + "/users/" + userSelfloopsId + "/courses/" + courseSelfloopsId + "/enroll";
            HttpPost httpPost = new HttpPost(url);
            httpPost.setHeader("Authorization", "Bearer " + obterTokenSelfloops(code, refresh_token, chave, empresaTreino).getString("access_token"));
            httpPost.setEntity(new StringEntity(new JSONObject(course).toString(), ContentType.APPLICATION_JSON));
            CloseableHttpResponse response = client.execute(httpPost);
            ResponseHandler<String> handler = new BasicResponseHandler();
            String body = handler.handleResponse(response);
            client.close();
            return new JSONObject(body);
        } catch (Exception ex) {
            JSONObject json = new JSONObject();
            json.put("error", "inserirAlunoAulaSensorIntegracaoSelfloops: " + ex.getMessage());
            ex.printStackTrace();
            Uteis.logar(null, json.toString());
            return json;
        }
    }

    public JSONObject deleteAlunoAulaSensorIntegracaoSelfloops(String code, String refresh_token, String userSelfloopsId, String courseSelfloopsId, String chave, Integer empresaTreino) {
        try {
            Logger.getLogger(AulaServiceImpl.class.getName()).log(Level.INFO, "DELETANDO ALUNO AULA SELFLOOPS USER AND COUSE -> " + userSelfloopsId + " - " + courseSelfloopsId);
            CloseableHttpClient client = ExecuteRequestHttpService.createConnectorCloseable();
            String url = Aplicacao.getProp(Aplicacao.urlApiSelfloops) + "/users/" + userSelfloopsId + "/courses/" + courseSelfloopsId + "/enroll";
            HttpDelete httpDelete = new HttpDelete(url);
            httpDelete.setHeader("Authorization", "Bearer " + obterTokenSelfloops(code, refresh_token, chave, empresaTreino).getString("access_token"));
            CloseableHttpResponse response = client.execute(httpDelete);
            JSONObject retorno = new JSONObject();
            retorno.put("status", response.getStatusLine().getStatusCode());
            client.close();
            return retorno;
        } catch (Exception ex) {
            JSONObject json = new JSONObject();
            json.put("error", "deleteAulaIntegracaoSelfloops:" + ex.getMessage());
            ex.printStackTrace();
            Uteis.logar(null, json.toString());
            return json;
        }
    }

    public JSONObject obterCourseDoDiaIntegracaoSelfloops(String code, String refresh_token, String empresaSelfloops, String courseScheduleSelfloopsId, String dia, String chave, Integer empresaTreino) {
        try {
            Logger.getLogger(AulaServiceImpl.class.getName()).log(Level.INFO, "OBTENDO AULA SELFLOOPS DIA courseScheduleID -> "+ courseScheduleSelfloopsId);
            CloseableHttpClient client = ExecuteRequestHttpService.createConnectorCloseable();
            HttpGet httpGet = new HttpGet(Aplicacao.getProp(Aplicacao.urlApiSelfloops) + "/courses" +
                    "?team=" + empresaSelfloops + "&course_schedule=" + courseScheduleSelfloopsId + "&start_date=" + (dia.contains("-") ? dia : Calendario.getData(Uteis.getDate(dia, "yyyyMMdd"), "yyyy-MM-dd"))+ "&end_date=" + (dia.contains("-") ? dia : Calendario.getData(Uteis.getDate(dia, "yyyyMMdd"), "yyyy-MM-dd")));
            httpGet.setHeader("Authorization", "Bearer " + obterTokenSelfloops(code, refresh_token, chave, empresaTreino).getString("access_token"));
            CloseableHttpResponse response = client.execute(httpGet);
            ResponseHandler<String> handler = new BasicResponseHandler();
            String body = handler.handleResponse(response);
            client.close();
            return new JSONObject(body);
        } catch (Exception ex) {
            JSONObject json = new JSONObject();
            json.put("error", "obterCourseDoDiaIntegracaoSelfloops:" + ex.getMessage());
            ex.printStackTrace();
            Uteis.logar(null, json.toString());
            return json;
        }
    }

    public JSONObject obterAtividadesCourseDoDiaIntegracaoSelfloops(String code, String refresh_token, String courseSelfloopsId, String chave, Integer empresaTreino) {
        try {
            Logger.getLogger(AulaServiceImpl.class.getName()).log(Level.INFO, "OBTENDO ATIVIDADES SELFLOOPS DIA courseScheduleID -> "+ courseSelfloopsId);
            CloseableHttpClient client = ExecuteRequestHttpService.createConnectorCloseable();
            HttpGet httpGet = new HttpGet(Aplicacao.getProp(Aplicacao.urlApiSelfloops) + "/courses/" + courseSelfloopsId + "/activities");
            httpGet.setHeader("Authorization", "Bearer " + obterTokenSelfloops(code, refresh_token, chave, empresaTreino).getString("access_token"));
            CloseableHttpResponse response = client.execute(httpGet);
            ResponseHandler<String> handler = new BasicResponseHandler();
            String body = handler.handleResponse(response);
            client.close();
            return new JSONObject(body);
        } catch (Exception ex) {
            JSONObject json = new JSONObject();
            json.put("error", "obterCourseDoDiaIntegracaoSelfloops:" + ex.getMessage());
            ex.printStackTrace();
            Uteis.logar(null, json.toString());
            return json;
        }
    }

    public void obterRankingCourseDoDiaIntegracaoSelfloops(String ctx, Integer empresaId, Integer horarioId, String dia, AlunoTurmaDTO aluno) {
        try {
            Empresa empTreino = empresaService.obterPorIdZW(ctx, empresaId);
            if (empTreino != null && !UteisValidacao.emptyNumber(empTreino.getCodigo())) {
                SelfLoopsConfiguracoes configSelf = obterPorEmpresa(ctx, empTreino.getCodigo());
                if (configSelf != null && configSelf.isIntegracaoRelizadaSucesso()) {
                    try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
                        try (PreparedStatement select = conZW.prepareStatement("select * from turmahorarioatividadesintegracaoselfloops where horarioturma = ? and diaAula::date = ? and userIdSelfloops = ?;")) {
                            select.setInt(1, horarioId);
                            select.setDate(2, Uteis.getDataJDBC(Calendario.getDate("yyyyMMdd", dia)));
                            select.setString(3, aluno.getUserIdSelfloops());
                            try (ResultSet dados = select.executeQuery()) {
                                while (dados.next()) {
                                    JSONObject activities = new JSONObject(dados.getString("jsonRetorno"));
                                    aluno.setAveragePower(activities.getInt("average_power"));
                                    aluno.setCalories(activities.getInt("calories"));
                                    Integer totalTimeInMinutes = UteisValidacao.notEmptyNumber(activities.getInt("total_time")) ? activities.getInt("total_time") / 60 : 0;
                                    aluno.setTempoDeAula(totalTimeInMinutes);
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logar(null, "Erro obterRankingCourseDoDiaIntegracaoSelfloops: " + ex.getMessage());
        }
    }
}
