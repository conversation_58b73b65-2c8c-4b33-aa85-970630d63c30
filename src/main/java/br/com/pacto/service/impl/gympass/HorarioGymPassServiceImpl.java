/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.impl.gympass;

import br.com.pacto.bean.gympass.HorarioGymPass;
import br.com.pacto.dao.intf.gympass.HorarioGymPassDao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.gympass.HorarioGymPassService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 06/04/2020
 */
@Service
public class HorarioGymPassServiceImpl implements HorarioGymPassService {

    @Autowired
    private HorarioGymPassDao dao;

    public HorarioGymPass obterPorId(final String ctx, Integer id) throws ServiceException {
        try {
            return dao.findById(ctx, id);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public HorarioGymPass incluir(final String ctx, HorarioGymPass object) throws ServiceException {
        try {
            return dao.insert(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public HorarioGymPass alterar(final String ctx, HorarioGymPass object) throws ServiceException {
        try {
            return dao.update(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public HorarioGymPass obterPorIdReferencia(final String ctx, Integer idTurma, String idReferencia) throws ServiceException {
        try {
            return dao.findObjectByAttributes(ctx, new String[]{"idTurma", "idReferencia"}, new Object[]{idTurma, idReferencia}, "codigo");
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<HorarioGymPass> obterPorIdReferencia(final String ctx, String idReferencia) throws ServiceException {
        try {
            StringBuilder sql = new StringBuilder();
            Map<String, Object> params = new HashMap<>();

            sql.append("SELECT obj FROM HorarioGymPass obj WHERE obj.idReferencia = :idReferencia  ");
            params.put("idReferencia", idReferencia);
            return dao.findByParam(ctx, sql.toString(), params, 0);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public HorarioGymPass obterPorIdSlotGymPass(final String ctx, Integer idSlotGymPass) throws ServiceException {
        try {
            return dao.findObjectByAttributes(ctx, new String[]{"idSlotGymPass"}, new Object[]{idSlotGymPass}, "codigo desc");
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<HorarioGymPass> obterPorIdTurma(final String ctx, Integer idTurma, Boolean ativo) throws ServiceException {
        try {
            StringBuilder sql = new StringBuilder();
            Map<String, Object> params = new HashMap<>();

            sql.append("SELECT obj FROM HorarioGymPass obj WHERE obj.idTurma = :idTurma AND obj.inicioHorario >= now() ");
            params.put("idTurma", idTurma);

            if (ativo != null) {
                sql.append(" AND obj.ativo = :ativo ");
                params.put("ativo", ativo);
            }

            return dao.findByParam(ctx, sql.toString(), params, 0);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public Map<String, HorarioGymPass> obterMapaIdReferenciaPorIdTurma(final String ctx, Integer idTurma, Boolean ativo) throws ServiceException {
        Map<String, HorarioGymPass> mapa = new HashMap<>();
        List<HorarioGymPass> lista = obterPorIdTurma(ctx, idTurma, ativo);
        for (HorarioGymPass obj : lista) {
            mapa.put(obj.getIdReferencia(), obj);
        }
        return mapa;
    }
}
