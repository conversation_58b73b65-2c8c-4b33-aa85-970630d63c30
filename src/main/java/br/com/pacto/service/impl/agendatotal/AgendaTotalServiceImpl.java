/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.impl.agendatotal;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ExecuteRequestHttpService;
import br.com.pacto.bean.Agendamento;
import br.com.pacto.bean.aula.AulaDiaExcecao;
import br.com.pacto.bean.aula.AulaDiaExclusao;
import br.com.pacto.bean.aula.AulaHorario;
import br.com.pacto.bean.aula.EdicaoAulaTemporaria;
import br.com.pacto.bean.aula.Modalidade;
import br.com.pacto.bean.aula.ProfessorSubstituido;
import br.com.pacto.bean.aula.TipoAulaCheiaOrigemEnum;
import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.configuracoes.ConfiguracaoSistema;
import br.com.pacto.bean.configuracoes.ConfiguracoesEnum;
import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.bean.gympass.ConfigGymPass;
import br.com.pacto.bean.gympass.TipoThreadGympassEnum;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.bean.perfil.permissao.TipoPermissaoEnum;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.agendamento.AlunoTurmaDTO;
import br.com.pacto.controller.json.agendamento.AlunoVinculoAulaEnum;
import br.com.pacto.controller.json.agendamento.EventoAulaDTO;
import br.com.pacto.controller.json.agendamento.TurmaResponseDTO;
import br.com.pacto.controller.json.aluno.AlunoResponseTO;
import br.com.pacto.controller.json.aulaDia.AulaConfirmadaVO;
import br.com.pacto.controller.json.aulaDia.AulaDiaJSON;
import br.com.pacto.controller.json.aulaDia.FilaDeEsperaDTO;
import br.com.pacto.controller.json.aulaDia.PassivoDTO;
import br.com.pacto.controller.json.aulaDia.SaldoAulaColetivaVO;
import br.com.pacto.controller.json.base.SuperControle;
import br.com.pacto.controller.json.turma.TurmaVideoDTO;
import br.com.pacto.controller.to.ScheduleModel;
import br.com.pacto.dao.intf.aula.AulaDiaExcecaoDao;
import br.com.pacto.dao.intf.aula.AulaDiaExclusaoDao;
import br.com.pacto.dao.intf.aula.AulaHorarioDao;
import br.com.pacto.dao.intf.aula.ModalidadeDao;
import br.com.pacto.dao.intf.aula.ProfessorSubstituidoDao;
import br.com.pacto.dao.intf.empresa.EmpresaDao;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.JSONMapper;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.agenda.AgendaModoBDServiceImpl;
import br.com.pacto.service.impl.agenda.AutorizacaoAcessoGrupoEmpresarialVO;
import br.com.pacto.service.impl.agenda.IntegracaoAcessoGrupoEmpresarialVO;
import br.com.pacto.service.impl.conexao.ConexaoZWServiceImpl;
import br.com.pacto.service.impl.notificacao.excecao.AgendaExcecoes;
import br.com.pacto.service.intf.agenda.AgendaService;
import br.com.pacto.service.intf.agenda.AgendamentoService;
import br.com.pacto.service.intf.agendatotal.AgendaTotalService;
import br.com.pacto.service.intf.aula.AulaService;
import br.com.pacto.service.intf.cliente.ClienteSinteticoService;
import br.com.pacto.service.intf.configuracoes.ConfiguracaoSistemaService;
import br.com.pacto.service.intf.empresa.EmpresaService;
import br.com.pacto.service.intf.gympass.ConfigGymPassService;
import br.com.pacto.service.intf.usuario.UsuarioService;
import br.com.pacto.threads.ThreadGympass;
import br.com.pacto.util.AgendadoJSON;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;
import br.com.pacto.util.bean.AgendaTotalTO;
import br.com.pacto.util.bean.AgendadoTO;
import br.com.pacto.util.bean.AgendamentoConfirmadoTO;
import br.com.pacto.util.bean.AgendamentoDesmarcadoTO;
import br.com.pacto.util.bean.DiaSemanaAgendaTO;
import br.com.pacto.util.bean.GenericoTO;
import br.com.pacto.util.bean.HoraAgendaTO;
import br.com.pacto.util.bean.LinhaAgendaTO;
import br.com.pacto.util.bean.LinhaSemanaTO;
import br.com.pacto.util.enumeradores.OrigemSistemaEnum;
import br.com.pacto.util.enumeradores.TipoAgrupamentoAgendaEnum;
import br.com.pacto.util.impl.Ordenacao;
import br.com.pacto.util.json.AgendaTotalJSON;
import br.com.pacto.util.json.ControleCreditoTreinoJSON;
import br.com.pacto.util.json.ParamTurmasJSON;
import br.com.pacto.util.json.ResultAlunoClienteSinteticoJSON;
import org.apache.http.NameValuePair;
import org.apache.http.client.ResponseHandler;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.BasicResponseHandler;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.message.BasicNameValuePair;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import servicos.integracao.zw.IntegracaoTurmasWSConsumer;

import javax.faces.model.SelectItem;
import javax.servlet.http.HttpServletRequest;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static org.apache.commons.lang3.StringUtils.isBlank;

/**
 *
 * <AUTHOR>
 */
@Service
public class AgendaTotalServiceImpl implements AgendaTotalService {

    private static final int MAXIMO_AULAS_CONFIRMADAS_CONSULTAR = 10;
    @Autowired
    private ModalidadeDao modalidadeDao;
    @Autowired
    private EmpresaDao empresaDao;
    @Autowired
    private AulaDiaExclusaoDao aulaDiaExclusaoDao;
    @Autowired
    private ProfessorSubstituidoDao profSubDao;
    @Autowired
    private AgendamentoService agendamentoService;
    @Autowired
    private AgendaService agendaService;
    @Autowired
    private ConfiguracaoSistemaService configuracaoSistemaService;
    @Autowired
    private AulaDiaExcecaoDao aulaDiaExcecaoDao;
    @Autowired
    private UsuarioService usuarioService;
    @Autowired
    private SessaoService sessaoService;
    @Autowired
    private ConexaoZWServiceImpl conexaoZWService;
    @Autowired
    private AulaService aulaService;

    @Autowired
    private EmpresaService empresaService;



    @Override
    public void montarAgenda(String ctx, ScheduleModel eventModel, Date inicio,
                             Date fim, Integer empresa, Integer modalidade, String search) throws Exception {
        eventModel.clear();
        Map<Integer, Modalidade> mapaCorModalidade = mapaCorModalidade(ctx);
        final String url = Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebInteg);
        IntegracaoTurmasWSConsumer integracaoWS = UtilContext.getBean(IntegracaoTurmasWSConsumer.class);
        List<AgendaTotalTO> agendamentos = integracaoWS.consultarAgendamentos( ctx, inicio, fim, empresa, null);
        for (AgendaTotalTO agenda : agendamentos) {
            agenda.setStyleClass(mapaCorModalidade.get(agenda.getCodigotipo()).getNomeCss());
            agenda.setCorLinha(url);
            eventModel.addEvent(agenda);
        }
    }

    @Override
    public Map<String, AgendaTotalTO> montarAgenda(String ctx, Date inicio, Date fim, Integer empresa,
                                                   Integer modalidade, TipoAulaCheiaOrigemEnum tipo, String search, String situacaoHorario) throws Exception {
        Map<String, AgendaTotalTO> mapa = new HashMap<String, AgendaTotalTO>();
        List<AgendaTotalTO> agendamentos = new ArrayList<>();
        Map<Integer, Modalidade> mapaCorModalidade = mapaCorModalidade(ctx);
        Map<Integer, List<Date>> mapaAulasExcluidas = obterAulasExcluidas(ctx, inicio, fim);
        Map<Integer, Map<Date, ProfessorSubstituido>> mapaProfessoresSubstituidos = new HashMap<>();

        try(Connection conZW = conexaoZWService.conexaoZw(ctx)){
            AgendaModoBDServiceImpl agendaAulasService = new AgendaModoBDServiceImpl(conZW, ctx);
            agendamentos = agendaAulasService.consultarAgendamentos(ctx, inicio, fim, empresa, null, search, situacaoHorario);
            mapaProfessoresSubstituidos = obterProfessoresSubstituidos(ctx, conZW, inicio, fim);
        }
        Map<Integer, EdicaoAulaTemporaria> mapaEdicoes = obterEdicoesTemporarias(ctx, Calendario.getDataComHoraZerada(inicio),
                Calendario.getDataComHora(fim, "23:59"));

        for (AgendaTotalTO agenda : agendamentos) {
            if (!UteisValidacao.emptyString(agenda.getSituacao())
                    && agenda.getSituacao().equals("IN")
                    && agenda.getDataEntrouTurma() == null
                    && agenda.getDataSaiuTurma() == null) {
                continue;
            }
            Date hoje      = agenda.getStartDate();
            Date inicioVig = agenda.getDataInicioVigencia();
            Date fimVig    = agenda.getVigencia();

            if (inicioVig != null && Calendario.menor(hoje, inicioVig)) {
                continue;
            }
            if (fimVig != null && Calendario.maior(hoje, fimVig)) {
                continue;
            }
            if (!UteisValidacao.emptyString(agenda.getSituacao())
                    && agenda.getSituacao().equals("IN")
                    && agenda.getDataSaiuTurma() != null) {
                Date fimAula = agenda.getEndDate();
                Date saiuTurma = agenda.getDataSaiuTurma();
                if (Calendario.maior(fimAula, saiuTurma)) {
                    continue;
                }
            }
            if(agenda.getAulaColetiva()){
                if(tipo.equals(TipoAulaCheiaOrigemEnum.ZW)){
                    continue;
                }

                EdicaoAulaTemporaria edicaoAulaTemporaria = mapaEdicoes.get(Integer.valueOf(agenda.getId()));
                if(edicaoAulaTemporaria != null){
                    agenda.setResponsavel(edicaoAulaTemporaria.getProfessorNome());
                    if(edicaoAulaTemporaria.getNome() != null) {
                        agenda.setNome(edicaoAulaTemporaria.getNome());
                    }
                    agenda.setCodigoResponsavel(edicaoAulaTemporaria.getProfessor());
                    agenda.setNrVagas(edicaoAulaTemporaria.getCapacidade());
                    agenda.setLocal(edicaoAulaTemporaria.getAmbienteNome());
                    agenda.setCodigoLocal(edicaoAulaTemporaria.getAmbiente());
                    String horarioInicial = formatTime(edicaoAulaTemporaria.getHorarioInicial());
                    String horarioFinal = formatTime(edicaoAulaTemporaria.getHorarioFinal());
                    agenda.setStartDate(Calendario.getDataComHora(agenda.getStartDate(), horarioInicial));
                    agenda.setEndDate(Calendario.getDataComHora(agenda.getEndDate(), horarioFinal));

                }

                List<Date> datas = mapaAulasExcluidas.get(Integer.valueOf(agenda.getId()));
                if(datas != null && datas.contains(Calendario.getDataComHoraZerada(agenda.getStartDate()))){
                    continue;
                }
                Map<Date, ProfessorSubstituido> mapaSubstitutos = mapaProfessoresSubstituidos.get(Integer.valueOf(agenda.getId()));
                if(mapaSubstitutos != null){
                    ProfessorSubstituido subs = mapaSubstitutos.get(Calendario.getDataComHoraZerada(agenda.getStartDate()));
                    if(subs != null){
                        subs.setNomeProfessorOrigem(agenda.getResponsavel());
                        agenda.setResponsavel(subs.getNomeProfessorSubstituto());
                        agenda.setCodigoResponsavel(subs.getCodigoProfessorSubstituto());
                        agenda.setSubstituiuProfessor(true);
                        agenda.setSubstituido(subs);
                    }
                }

            }else if(tipo.equals(TipoAulaCheiaOrigemEnum.AC)){
                continue;
            }

            //Processo de consulta sytleclass ja faz o prodesso de devolver a classe "aulacheia"
            Modalidade mod = mapaCorModalidade.get(agenda.getCodigotipo());
            if (mod == null) {
                AulaService aulaService = (AulaService) UtilContext.getBean(AulaService.class);
                aulaService.obterModalidadesZW(ctx, empresa, null);
                mapaCorModalidade = mapaCorModalidade(ctx);
                mod = mapaCorModalidade.get(agenda.getCodigotipo());
            }
            agenda.setStyleClass(mod == null ? "" : mod.getNomeCss());
            agenda.setTextoStyle(mod == null ? "" : mod.getCor().getClasseCor());
            agenda.setIcone((mod == null || mod.getIcone() == null) ? null : mod.getIcone().getNome());
            agenda.setCorLinha(isBlank(agenda.getCor()) ? (mod == null ? "" : mod.getCor().getBorderColor()) : agenda.getCor());
            mapa.put(agenda.getIdentificador(), agenda);
        }
        return mapa;
    }

    public String formatTime(String input) {
        StringBuilder formattedTime = new StringBuilder(input);
        formattedTime.insert(2, ':');
        return formattedTime.toString();
    }

    @Override
    public Map<String, List<AgendadoTO>> montarMapaAgendados(String ctx, Date inicio,
                                                             Date fim, Integer empresa, List<AgendaTotalTO> agendamentos,
                                                             boolean armazenarTurma) throws Exception {

        IntegracaoTurmasWSConsumer integracaoWS = UtilContext.getBean(IntegracaoTurmasWSConsumer.class);
        List<AgendadoTO> agendados = integracaoWS.consultarAgendados( ctx, inicio, fim, empresa);
        List<AgendadoTO> reposicoes = integracaoWS.consultarReposicoes( ctx, inicio, fim, empresa);
        Map<Integer, Map<String, AgendamentoDesmarcadoTO>> desmarcados = integracaoWS.consultarDesmarcados( ctx, inicio, fim, empresa);
        Map<Integer, Map<String, AgendamentoConfirmadoTO>> confirmados = integracaoWS.consultarConfirmados( ctx, inicio, fim);

        Map<String, List<AgendadoTO>> mapa = new HashMap<String, List<AgendadoTO>>();
        for (AgendaTotalTO agenda : agendamentos) {
            List<AgendadoTO> lista = new ArrayList<AgendadoTO>();
            int vagasPreenchidas = 0;
            int nrdesmarcados = 0;
            int nrreposicoes = 0;
            int nrConfirmados = 0;
            int marcacoes = 0;
            int experimentais = 0;
            int diaristas = 0;
            int desafios = 0;

            for (AgendadoTO ag : agendados) {

                AgendadoTO agendado = ag.cloneTO();
                agendado.setAgendamento(agenda);
                if (agenda.getId().equals(agendado.getIdAgendamento())
                        && Calendario.menorOuIgual(agendado.getInicio(), agenda.getStartDate())
                        && Calendario.maiorOuIgual(agendado.getFim(), agenda.getStartDate())) {
                    Map<String, AgendamentoDesmarcadoTO> mapaDesmarcados = desmarcados.get(agendado.getCodigoCliente());
                    Map<String, AgendamentoConfirmadoTO> mapaConfirmados = confirmados.get(agendado.getCodigoCliente());
                    if (mapaDesmarcados != null && mapaDesmarcados.get(agenda.getIdentificador()) != null &&
                            mapaDesmarcados.get(agenda.getIdentificador()).getCodigoContrato().equals(ag.getCodigoContrato())) {
                        if(mapaDesmarcados.get(agenda.getIdentificador()).getJustificativa() != null) {
                            agendado.setJustificativa(mapaDesmarcados.get(agenda.getIdentificador()).getJustificativa());
                        }
                        nrdesmarcados++;
                        agendado.setDesmarcado(true);
                    } else  {
                        if(agenda.getAulaColetiva() && mapaConfirmados != null && mapaConfirmados.get(agenda.getIdentificador()) != null){
                            nrConfirmados ++;
                            agendado.setConfirmado(true);
                            agenda.getAlunosConfirmados().put(agendado.getNomeAbreviado(), agendado.getMatricula());
                        }
                        if(agendado.isDiaria()) {
                            diaristas++;
                        }
                        if (agendado.getDesafio()) {
                            desafios++;
                        }
                        vagasPreenchidas++;
                        agenda.getAlunos().put(agendado.getNomeAbreviado(), agendado.getMatricula());
                    }

                    if (armazenarTurma) {
                        agendado.setAgendamento(agenda);
                    }
                    lista.add(agendado);
                }
            }
            for (AgendadoTO agendado : reposicoes) {
                if (agenda.getId().equals(agendado.getIdAgendamento())
                        && Calendario.menorOuIgual(agendado.getInicio(), agenda.getStartDate())
                        && Calendario.maiorOuIgual(agendado.getFim(), agenda.getStartDate())) {
                    vagasPreenchidas++;
                    if(agendado.isDiaria()){
                        diaristas++;
                    }else {
                        agendado.setReposicao(true);
                        if (agendado.getDesafio()) {
                            desafios++;
                        } else if (agendado.isExperimental()) {
                            experimentais++;
                        } else if (agendado.getUsaSaldo()) {
                            marcacoes++;
                        } else {
                            nrreposicoes++;
                        }
                    }
                    Map<String, AgendamentoConfirmadoTO> mapaConfirmados = confirmados.get(agendado.getCodigoCliente());
                    if(agenda.getAulaColetiva() && mapaConfirmados != null && mapaConfirmados.get(agenda.getIdentificador()) != null){
                        nrConfirmados++;
                        agendado.setConfirmado(true);
                        agenda.getAlunosConfirmados().put(agendado.getNomeAbreviado(), agendado.getMatricula());
                    }
                    //criado validação para verificar se já tem algum cliente que está ativo nesta aula e não adicionando dados duplicados. #21863
                    boolean adicionaReposicao = true;
                    for (AgendadoTO agendadosAdicionados : lista) {
                        if (agendado.getMatricula().equals(agendadosAdicionados.getMatricula()) && !(agendadosAdicionados.isDesmarcado())) {
                            vagasPreenchidas--;
                            if(agendado.isDiaria()){
                                diaristas--;
                            }else {
                                agendado.setReposicao(true);
                                if (agendado.getDesafio()) {
                                    desafios--;
                                } else if (agendado.isExperimental()) {
                                    experimentais--;
                                } else if (agendado.getUsaSaldo()) {
                                    marcacoes--;
                                } else {
                                    nrreposicoes--;
                                }
                            }
                            adicionaReposicao = false;
                            break;
                        }
                    }
                    if (adicionaReposicao){
                        agendado.setAgendamento(agenda);
                        lista.add(agendado);
                    }
                    agenda.getAlunos().put(agendado.getNomeAbreviado(), agendado.getMatricula());
                }

            }

            agenda.setExperimentais(experimentais);
            agenda.setMarcacoes(marcacoes);
            agenda.setConfirmacoes(nrConfirmados);
            agenda.setReposicoes(nrreposicoes);
            agenda.setNrVagasPreenchidas(vagasPreenchidas);
            agenda.setDesmarcados(nrdesmarcados);
            agenda.setDiaristas(diaristas);
            agenda.setDesafios(desafios);
            mapa.put(agenda.getIdentificador(), lista);

        }
        return mapa;
    }

    public Map<Integer, Modalidade> mapaCorModalidade(String ctx) throws Exception {
        Map<Integer, Modalidade> mapa = new HashMap<Integer, Modalidade>();
        List<Modalidade> all = modalidadeDao.findAll(ctx);
        for (Modalidade m : all) {
            mapa.put(m.getCodigoZW(), m);
        }
        return mapa;
    }

    @Override
    public List<Modalidade> todasModalidades(String ctx) throws ServiceException {
        try {
            return modalidadeDao.findAll(ctx);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    private ClienteSinteticoService getClienteSinteticoService() {
        return UtilContext.getBean(ClienteSinteticoService.class);
    }

    private UsuarioService getUsuarioService() {
        return UtilContext.getBean(UsuarioService.class);
    }

    public String inserirAutorizadoAulaCheiaMatricula(String ctx,
                                                      String matricula,
                                                      String chaveAluno,
                                                      Integer idHorarioTurma, Date data,
                                                      OrigemSistemaEnum origem, Usuario usuario, Integer empresa) throws ServiceException{
        try {
            AutorizacaoAcessoGrupoEmpresarialVO autorizacaoAcessoGrupoEmpresarialVO = new AutorizacaoAcessoGrupoEmpresarialVO();
            final String url = Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebInteg);
            CloseableHttpClient client = ExecuteRequestHttpService.createConnector();
            HttpPost httpPost = new HttpPost(url + "/prest/aulacheia/consultar-autorizado-gestao-rede");
            List<NameValuePair> params = new ArrayList<>();
            params.add(new BasicNameValuePair("chave", ctx));
            params.add(new BasicNameValuePair("chaveOrigem", chaveAluno));
            params.add(new BasicNameValuePair("matriculaAutorizado", matricula));
            httpPost.setEntity(new UrlEncodedFormEntity(params));
            CloseableHttpResponse response = client.execute(httpPost);
            ResponseHandler<String> handler = new BasicResponseHandler();
            String body = handler.handleResponse(response);
            client.close();
            JSONObject autorizacaoJson;
            try {
                autorizacaoJson = new JSONObject(body).getJSONObject("content");
                autorizacaoAcessoGrupoEmpresarialVO.setCodigo(autorizacaoJson.getInt("codigo"));
            } catch (Exception ignored) {}
            return inserirAutorizadoAulaCheia(ctx, autorizacaoAcessoGrupoEmpresarialVO.getCodigo(), idHorarioTurma, data, origem, usuario, empresa);
        } catch (Exception ignored){
        }

        try {
            final String url = Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebInteg);
            CloseableHttpClient client = ExecuteRequestHttpService.createConnector();
            HttpPost httpPost = new HttpPost(url + "/prest/aulacheia/consultar-autorizado-criando");
            List<NameValuePair> params = new ArrayList<>();
            params.add(new BasicNameValuePair("chave", ctx));
            params.add(new BasicNameValuePair("chaveAluno", chaveAluno));
            params.add(new BasicNameValuePair("matricula", matricula));
            httpPost.setEntity(new UrlEncodedFormEntity(params));
            CloseableHttpResponse response = client.execute(httpPost);
            ResponseHandler<String> handler = new BasicResponseHandler();
            String body = handler.handleResponse(response);
            client.close();
            JSONObject consulta;
            try{
                consulta = new JSONObject(body);
            }catch (Exception e){
                throw new Exception(body);
            }
            return inserirAutorizadoAulaCheia(ctx, consulta.getInt("content"), idHorarioTurma, data, origem, usuario, empresa);
        }catch (Exception e){
            throw new ServiceException(e);
        }
    }

    public String inserirAutorizadoAulaCheia(String ctx, Integer codigoAutorizado, Integer idHorarioTurma, Date data,
                                             OrigemSistemaEnum origem, Usuario usuario, Integer empresa) throws ServiceException{
        try {
            ConfiguracaoSistema cfTipo = configuracaoSistemaService.consultarPorTipo(ctx, ConfiguracoesEnum.VALIDAR_AGENDA_AULACHEIA);
            JSONObject jSONObject;
            IntegracaoTurmasWSConsumer integracaoWS = UtilContext.getBean(IntegracaoTurmasWSConsumer.class);
            String retornoPesquisaAula = integracaoWS.pesquisarHorarioTurma( ctx, idHorarioTurma);
            try {
                jSONObject = new JSONObject(retornoPesquisaAula);
            }catch (Exception e){
                jSONObject = null;
                Uteis.logar(e, AgendaTotalServiceImpl.class);
            }
            if (cfTipo.getValorAsBoolean() ) {
                if (retornoPesquisaAula.isEmpty() || retornoPesquisaAula.startsWith("ERRO") || jSONObject == null) {
                    throw new ServiceException("horario.turma.invalido");
                }
            }

            if (null != jSONObject && jSONObject.toString().contains("horainicial") && null != usuario){
                Date dataComHora = Calendario.getDataComHora(data,jSONObject.getString("horainicial"));
                if (null != codigoAutorizado){
                    if(dataComHora.before(Calendario.hoje())
                            && !usuario.isItemHabilitado(RecursoEnum.INSERIR_ALUNO_AULA_INICIADA.name(), TipoPermissaoEnum.CONSULTAR.name())){
                        throw new Exception("naoTemPermissaoParaAdicionarAlunoPassado");
                    }
                }
            }

            ConfiguracaoSistemaService css = UtilContext.getBean(ConfiguracaoSistemaService.class);
            ConfiguracaoSistema cfgAulaValidarMd = css.consultarPorTipo(ctx, ConfiguracoesEnum.VALIDAR_MODALIDADE);
            ConfiguracaoSistema cfgAulaValidarHorario = css.consultarPorTipo(ctx, ConfiguracoesEnum.VALIDAR_HORARIO_CONTRATO);
            ConfiguracaoSistema cfgControlarFP = css.consultarPorTipo(ctx, ConfiguracoesEnum.CONTROLAR_POR_FREEPASS);
            ConfiguracaoSistema cfInacimplente = css.consultarPorTipo(ctx,
                    ConfiguracoesEnum.PROIBIR_MARCAR_AULA_PARCELA_VENCIDA);
            ConfiguracaoSistema cfgProibirMarcarAulaAntesPagamentoPrimeiraParc = configuracaoSistemaService.consultarPorTipo(ctx, ConfiguracoesEnum.PROIBIR_MARCAR_AULA_ANTES_PAGAMENTO_PRIMEIRA_PARCELA);
            ConfiguracaoSistema cfgPermAlunoMarcarAulaOutraUnidade = css.consultarPorTipo(ctx,ConfiguracoesEnum.PERMITIR_ALUNO_MARCAR_AULA_POR_TIPO_MODALIDADE);
            int minutosAgendarAntecendencia = obterMinutosMarcarDesmarcarAula(ctx, usuario, true, null);
            ParamTurmasJSON param = new ParamTurmasJSON();
            param.setIgnorarTolerancia(usuario != null &&
                    (usuario.isFuncionalidadeHabilitado(RecursoEnum.USUARIO_MARCAR_ANTECEDENCIA.name()) ||
                            usuario.isFuncionalidadeHabilitado(RecursoEnum.INSERIR_ALUNO_AULA_INICIADA.name())));
            param.setUsuario(null != usuario && null != usuario.getUsuarioZW() ? usuario.getUsuarioZW() : 0);
            param.setEmpresa(empresa);
            param.setAutorizado(codigoAutorizado);
            param.setExperimental(false);
            param.setValidarModalidade(cfgAulaValidarMd.getValorAsBoolean());
            param.setValidarHorario(cfgAulaValidarHorario.getValorAsBoolean());
            param.setControlarFreepass(cfgControlarFP.getValorAsBoolean());
            param.setProibirComParcelaVencida(cfInacimplente.getValorAsBoolean());
            param.setProibirMarcarAulaAntesPagamentoPrimeiraParcela(cfgProibirMarcarAulaAntesPagamentoPrimeiraParc.getValorAsBoolean());
            param.setNrAulasExperimentais(0);
            param.setIdHorarioTurmaOrigem(idHorarioTurma);
            param.setDataOrigem(Uteis.getDataAplicandoFormatacao(data, "dd/MM/yyyy"));
            param.setMinutosAgendarAntecedencia(minutosAgendarAntecendencia);
            param.setOrigemSistema(origem != null ? origem.getCodigo() : null);
            param.setPermAlunoMarcarAulaOutraEmpresa(cfgPermAlunoMarcarAulaOutraUnidade.getValorAsBoolean());
            String retorno = integracaoWS.inserirAlunoAulaCheia( ctx, param);
            try {
                if(!retorno.toLowerCase().startsWith("erro")){
                    ConfigGymPassService configGymPassService = UtilContext.getBean(ConfigGymPassService.class);
                    ConfigGymPass configGymPass = configGymPassService.obterPorEmpresaZW(ctx, param.getEmpresa());
                    if(!UteisValidacao.emptyString(configGymPass.getCodigoGymPass())){
                        iniciaThreadGympass(ctx, idHorarioTurma, param.getEmpresa(), data, configGymPass);
                    }
                }
            }catch (Exception e){
                Uteis.logar(e, AgendaTotalServiceImpl.class);
            }

            return retorno;
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public String inserirAlunoAulaCheiaConversasIA(String ctx, Integer codigoCliente, Integer idHorarioTurma,
                                                   Date data, OrigemSistemaEnum origem, Usuario usuario, Integer empresaZw,
                                                   boolean aulaExperimental, Boolean somenteValidar) throws ServiceException {
        try {
            ConfiguracaoSistema cfTipo = configuracaoSistemaService.consultarPorTipo(ctx, ConfiguracoesEnum.VALIDAR_AGENDA_AULACHEIA);
            ClienteSintetico clienteSintetico = getClienteSinteticoService().obterPorCodigoCliente(ctx, codigoCliente);
            JSONObject jSONObject;
            IntegracaoTurmasWSConsumer integracaoWS = UtilContext.getBean(IntegracaoTurmasWSConsumer.class);
            String retornoPesquisaAula = integracaoWS.pesquisarHorarioTurma( ctx, idHorarioTurma);
            try {
                jSONObject = new JSONObject(retornoPesquisaAula);
            } catch (Exception e) {
                jSONObject = null;
                Uteis.logar(e, AgendaTotalServiceImpl.class);
            }

            if (cfTipo.getValorAsBoolean() ) {
                if (retornoPesquisaAula.isEmpty() || retornoPesquisaAula.startsWith("ERRO") || jSONObject == null) {
                    throw new ServiceException("horario.turma.invalido");
                }

                if (clienteSintetico != null && !UteisValidacao.emptyNumber(clienteSintetico.getCodigo())) {
                    String dados = integracaoWS.consultarAulaPeloClienteHorario(
                            ctx, clienteSintetico.getMatriculaString(), Calendario.getDataComHoraZerada(data),
                            jSONObject.getString("horainicial"), jSONObject.getString("horafinal")
                    );

                    if (!dados.contains("ERRO") && origem != null) {
                        JSONArray jsonArray = new JSONArray(dados);
                        if (jsonArray.length() > 0) {
                            JSONObject jSONObject1 = jsonArray.optJSONObject(0);
                            String identicadorTurma = jSONObject1.getString("identificadorturma");
                            Pattern pattern = Pattern.compile("\\((\\d{2}:\\d{2}) - (\\d{2}:\\d{2})\\)");
                            Matcher matcher = pattern.matcher(identicadorTurma);

                            if (matcher.find()) {
                                String horarioInicialConflitante = matcher.group(1);
                                String horarioFinalConflitante = matcher.group(2);
                                StringBuilder ret = new StringBuilder("");
                                for (int i = 0; i < jsonArray.length(); i++) {
                                    JSONObject jObj = jsonArray.optJSONObject(i);
                                    if (ret.toString().isEmpty()) {
                                        ret.append(jObj.getString("identificadorturma"));
                                    } else {
                                        ret.append(",").append(jObj.getString("identificadorturma"));
                                    }
                                }
                                if (!(horarioFinalConflitante.equals(jSONObject.getString("horainicial")) || horarioInicialConflitante.equals(jSONObject.getString("horafinal")))) {
                                    if (origem == OrigemSistemaEnum.CONVERSAS_IA) {
                                        throw new Exception("Você já possui compromissos no horário: " + ret);
                                    } else {
                                        throw new Exception("O aluno já possui compromissos no horário: " + ret);
                                    }
                                }
                            }
                        }
                    } else {
                        throw new Exception(dados);
                    }
                }
            }

            if (null != jSONObject && jSONObject.toString().contains("horainicial") && null != usuario) {
                Date dataComHora = Calendario.getDataComHora(data,jSONObject.getString("horainicial"));
                if (null != codigoCliente) {
                    if (dataComHora.before(Calendario.hoje()) && !usuario.isItemHabilitado(RecursoEnum.INSERIR_ALUNO_AULA_INICIADA.name(), TipoPermissaoEnum.CONSULTAR.name())){
                        throw new Exception("naoTemPermissaoParaAdicionarAlunoPassado");
                    }
                }
            }

            ConfiguracaoSistemaService css = UtilContext.getBean(ConfiguracaoSistemaService.class);
            ConfiguracaoSistema cfgAulaExperimental = css.consultarPorTipo(ctx, ConfiguracoesEnum.NR_AULA_EXPERIMENTAL_ALUNO);
            ConfiguracaoSistema cfgAulaValidarMd = css.consultarPorTipo(ctx, ConfiguracoesEnum.VALIDAR_MODALIDADE);
            ConfiguracaoSistema cfgAulaValidarVezesMd = css.consultarPorTipo(ctx, ConfiguracoesEnum.NR_VALIDAR_VEZES_MODALIDADE);
            ConfiguracaoSistema cfgQtdFaltasBloqueioAluno = css.consultarPorTipo(ctx, ConfiguracoesEnum.QTD_FALTAS_BLOQUEIO_ALUNO);
            ConfiguracaoSistema cfgQtdTempoBloqueioAluno = css.consultarPorTipo(ctx, ConfiguracoesEnum.QTD_TEMPO_BLOQUEIO_ALUNO);
            ConfiguracaoSistema cfgAulaValidarHorario = css.consultarPorTipo(ctx, ConfiguracoesEnum.VALIDAR_HORARIO_CONTRATO);
            ConfiguracaoSistema cfgControlarFP = css.consultarPorTipo(ctx, ConfiguracoesEnum.CONTROLAR_POR_FREEPASS);
            ConfiguracaoSistema cfInacimplente = css.consultarPorTipo(ctx, ConfiguracoesEnum.PROIBIR_MARCAR_AULA_PARCELA_VENCIDA);
            ConfiguracaoSistema cfgProibirMarcarAulaAntesPagamentoPrimeiraParc = configuracaoSistemaService.consultarPorTipo(ctx, ConfiguracoesEnum.PROIBIR_MARCAR_AULA_ANTES_PAGAMENTO_PRIMEIRA_PARCELA);
            ConfiguracaoSistema cfgPermAlunoMarcarAulaOutraUnidade = css.consultarPorTipo(ctx,ConfiguracoesEnum.PERMITIR_ALUNO_MARCAR_AULA_POR_TIPO_MODALIDADE);
            int minutosAgendarAntecendencia = obterMinutosMarcarDesmarcarAula(ctx, usuario, true, null);
            ConfiguracaoSistema cfgFilaEspera = configuracaoSistemaService.consultarPorTipo(ctx, ConfiguracoesEnum.HABILITAR_FILA_ESPERA);
            ConfiguracaoSistema cfgDescontarCredito = configuracaoSistemaService.consultarPorTipo(ctx, ConfiguracoesEnum.DESCONTAR_CREDITO_AO_MARCAR_AULA_SEM_CONFIRMAR_PRESENCA);
            ConfiguracaoSistema cfgBloquearReposicao = css.consultarPorTipo(ctx, ConfiguracoesEnum.BLOQUEAR_GERAR_REPOSICAO_AULA_JA_REPOSTA);

            ParamTurmasJSON param = new ParamTurmasJSON();
            param.setBloquearGerarReposicaoAulaJaReposta(cfgBloquearReposicao.getValorAsBoolean());
            param.setIgnorarTolerancia(
                    usuario != null &&
                    (usuario.isFuncionalidadeHabilitado(RecursoEnum.USUARIO_MARCAR_ANTECEDENCIA.name()) || usuario.isFuncionalidadeHabilitado(RecursoEnum.INSERIR_ALUNO_AULA_INICIADA.name()))
            );
            param.setCodigoCliente(codigoCliente);
            param.setUsuario(null != usuario && null != usuario.getUsuarioZW() ? usuario.getUsuarioZW() : 0);
            param.setEmpresa(empresaZw);
            param.setExperimental(aulaExperimental);

            param.setValidarModalidade(cfgAulaValidarMd.getValorAsBoolean());
            if (cfgAulaValidarVezesMd.getValorAsInteger() != null && cfgAulaValidarVezesMd.getValorAsInteger() > 0) {
                param.setNrValidarVezesModalidade(cfgAulaValidarVezesMd.getValorAsInteger());
            } else {
                param.setNrValidarVezesModalidade(0);
            }
            param.setValidarHorario(cfgAulaValidarHorario.getValorAsBoolean());
            param.setControlarFreepass(cfgControlarFP.getValorAsBoolean());
            param.setProibirComParcelaVencida(cfInacimplente.getValorAsBoolean());
            param.setProibirMarcarAulaAntesPagamentoPrimeiraParcela(cfgProibirMarcarAulaAntesPagamentoPrimeiraParc.getValorAsBoolean());

            param.setNrAulasExperimentais(
                    clienteSintetico != null && clienteSintetico.getNrAulasExperimentais() > 0
                    ? clienteSintetico.getNrAulasExperimentais()
                    : cfgAulaExperimental.getValorAsInteger()
            );

            param.setIdHorarioTurmaOrigem(idHorarioTurma);
            param.setDataOrigem(Uteis.getDataAplicandoFormatacao(data, "dd/MM/yyyy"));
            param.setMinutosAgendarAntecedencia(minutosAgendarAntecendencia);
            param.setOrigemSistema(origem != null ? origem.getCodigo() : null);
            param.setPermAlunoMarcarAulaOutraEmpresa(cfgPermAlunoMarcarAulaOutraUnidade.getValorAsBoolean());
            param.setSomenteValidar(somenteValidar);
            param.setFilaEspera(false);
            param.setDescontarCreditoContratoAulaMarcada(cfgDescontarCredito != null && cfgDescontarCredito.getValorAsBoolean());

            if (clienteSintetico !=null && clienteSintetico.getNivelAluno() != null) {
                param.setNivel(clienteSintetico.getNivelAluno().getCodigo());
            }

            String retorno = integracaoWS.inserirAlunoAulaCheia( ctx, param);
            if (retorno.toLowerCase().startsWith("erro") && !retorno.toLowerCase().contains("aulaexperimental") && origem != null && !origem.equals(OrigemSistemaEnum.AULA_CHEIA)) {
                try {
                    this.aulaDiaExcecaoDao.insert(ctx, new AulaDiaExcecao(Calendario.hoje(), idHorarioTurma + "_" + Uteis.getData(data, "ddMMyy"), clienteSintetico));
                } catch (Exception e) {
                    e.printStackTrace();
                    Uteis.logarDebug("Erro ao tentar inserir registro aulaDiaExcecaoDao: " + e.getMessage());
                }
            }

            try {
                if (!retorno.toLowerCase().startsWith("erro")) {
                    ConfigGymPassService configGymPassService = UtilContext.getBean(ConfigGymPassService.class);
                    ConfigGymPass configGymPass = configGymPassService.obterPorEmpresaZW(ctx, param.getEmpresa());
                    if (!UteisValidacao.emptyString(configGymPass.getCodigoGymPass())) {
                        iniciaThreadGympass(ctx, idHorarioTurma, param.getEmpresa(), data, configGymPass);
                    }
                }
            } catch (Exception e) {
                Uteis.logar(e, AgendaTotalServiceImpl.class);
            }

            return retorno;
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public String inserirAlunoAulaCheia(String ctx, Integer codigoCliente, Integer idHorarioTurma,
                                        Date data, OrigemSistemaEnum origem, Usuario usuario, Integer empresa,
                                        boolean aulaExperimental, Boolean somenteValidar,
                                        String bookingId) throws ServiceException {
        try {
            ConfiguracaoSistema cfTipo = configuracaoSistemaService.consultarPorTipo(ctx, ConfiguracoesEnum.VALIDAR_AGENDA_AULACHEIA);
            ClienteSintetico clienteSintetico = getClienteSinteticoService().obterPorCodigoCliente(ctx, codigoCliente);
            JSONObject jSONObject;
            IntegracaoTurmasWSConsumer integracaoWS = UtilContext.getBean(IntegracaoTurmasWSConsumer.class);
            String retornoPesquisaAula = integracaoWS.pesquisarHorarioTurma( ctx, idHorarioTurma);
            try {
                jSONObject = new JSONObject(retornoPesquisaAula);
            }catch (Exception e){
                jSONObject = null;
                Uteis.logar(e, AgendaTotalServiceImpl.class);
            }
            if (cfTipo.getValorAsBoolean() ) {
                if (retornoPesquisaAula.isEmpty() || retornoPesquisaAula.startsWith("ERRO") || jSONObject == null) {
                    throw new ServiceException("horario.turma.invalido");
                }

                if (clienteSintetico != null && !UteisValidacao.emptyNumber(clienteSintetico.getCodigo()) && SuperControle.independente(ctx)) {
                    List<Agendamento> listaAgendamento = agendamentoService.consultarAgendamentoDataHora(ctx, data, jSONObject.getString("horainicial"), jSONObject.getString("horafinal"), clienteSintetico.getCodigoCliente(), clienteSintetico.getCodigo());
                    if (!listaAgendamento.isEmpty()) {
                        if (origem != null) {
                            StringBuilder compromissos = new StringBuilder("");
                            for (Agendamento agendamento : listaAgendamento) {
                                if (compromissos.toString().isEmpty()) {
                                    compromissos.append(agendamento.getTipoEvento().getComportamento().getDescricao());
                                } else {
                                    compromissos.append(",").append(agendamento.getTipoEvento().getComportamento().getDescricao());
                                }
                            }

                            if (origem == OrigemSistemaEnum.AUTO_ATENDIMENTO || origem == OrigemSistemaEnum.APP_TREINO) {
                                throw new Exception("Você já possui compromissos nesse horário: " + compromissos);
                            } else {
                                throw new Exception("O aluno já possui compromissos nesse horário: " + compromissos);
                            }
                        }
                    }
                } else if (clienteSintetico != null && !UteisValidacao.emptyNumber(clienteSintetico.getCodigo())) {
                    String dados = integracaoWS.consultarAulaPeloClienteHorario( ctx, clienteSintetico.getMatriculaString(), Calendario.getDataComHoraZerada(data), jSONObject.getString("horainicial"), jSONObject.getString("horafinal"));
                    if (!dados.contains("ERRO") && origem != null) {
                        JSONArray jsonArray = new JSONArray(dados);
                        if (jsonArray.length() > 0) {
                            JSONObject jSONObject1 = jsonArray.optJSONObject(0);
                            String identicadorTurma = jSONObject1.getString("identificadorturma");
                            Pattern pattern = Pattern.compile("\\((\\d{2}:\\d{2}) - (\\d{2}:\\d{2})\\)");
                            Matcher matcher = pattern.matcher(identicadorTurma);

                            if(matcher.find()){
                                String horarioInicialConflitante = matcher.group(1);
                                String horarioFinalConflitante = matcher.group(2);
                                StringBuilder ret = new StringBuilder("");
                                for (int i = 0; i < jsonArray.length(); i++) {
                                    JSONObject jObj = jsonArray.optJSONObject(i);
                                    if (ret.toString().isEmpty()) {
                                        ret.append(jObj.getString("identificadorturma"));
                                    } else {
                                        ret.append(",").append(jObj.getString("identificadorturma"));
                                    }
                                }
                                if(!(horarioFinalConflitante.equals(jSONObject.getString("horainicial"))
                                        || horarioInicialConflitante.equals(jSONObject.getString("horafinal")))
                                ) {
                                    if (origem == OrigemSistemaEnum.AUTO_ATENDIMENTO || origem == OrigemSistemaEnum.APP_TREINO) {
                                        throw new Exception("Você já possui compromissos no horário: " + ret);
                                    } else {
                                        throw new Exception("O aluno já possui compromissos no horário: " + ret);
                                    }
                                }
                            }
                        }
                    } else {
                        throw new Exception(dados);
                    }
                }
            }

            if (null != jSONObject && jSONObject.toString().contains("horainicial") && null != usuario){
                Date dataComHora = Calendario.getDataComHora(data,jSONObject.getString("horainicial"));
                if (null != codigoCliente){
                    if(dataComHora.before(Calendario.hoje())
                            && !usuario.isItemHabilitado(RecursoEnum.INSERIR_ALUNO_AULA_INICIADA.name(), TipoPermissaoEnum.CONSULTAR.name())){
                        throw new Exception("naoTemPermissaoParaAdicionarAlunoPassado");
                    }
                }
            }

            ConfiguracaoSistemaService css = UtilContext.getBean(ConfiguracaoSistemaService.class);
            ConfiguracaoSistema cfgAulaExperimental = css.consultarPorTipo(ctx, ConfiguracoesEnum.NR_AULA_EXPERIMENTAL_ALUNO);
            ConfiguracaoSistema cfgAulaValidarMd = css.consultarPorTipo(ctx, ConfiguracoesEnum.VALIDAR_MODALIDADE);
            ConfiguracaoSistema cfgAulaValidarVezesMd = css.consultarPorTipo(ctx, ConfiguracoesEnum.NR_VALIDAR_VEZES_MODALIDADE);
            ConfiguracaoSistema cfgQtdFaltasBloqueioAluno = css.consultarPorTipo(ctx, ConfiguracoesEnum.QTD_FALTAS_BLOQUEIO_ALUNO);
            ConfiguracaoSistema cfgQtdTempoBloqueioAluno = css.consultarPorTipo(ctx, ConfiguracoesEnum.QTD_TEMPO_BLOQUEIO_ALUNO);
            ConfiguracaoSistema cfgAulaValidarHorario = css.consultarPorTipo(ctx, ConfiguracoesEnum.VALIDAR_HORARIO_CONTRATO);
            ConfiguracaoSistema cfgControlarFP = css.consultarPorTipo(ctx, ConfiguracoesEnum.CONTROLAR_POR_FREEPASS);
            ConfiguracaoSistema cfInacimplente = css.consultarPorTipo(ctx,
                    ConfiguracoesEnum.PROIBIR_MARCAR_AULA_PARCELA_VENCIDA);
            ConfiguracaoSistema cfgProibirMarcarAulaAntesPagamentoPrimeiraParc = configuracaoSistemaService.consultarPorTipo(ctx, ConfiguracoesEnum.PROIBIR_MARCAR_AULA_ANTES_PAGAMENTO_PRIMEIRA_PARCELA);
            ConfiguracaoSistema cfgPermAlunoMarcarAulaOutraUnidade = css.consultarPorTipo(ctx,ConfiguracoesEnum.PERMITIR_ALUNO_MARCAR_AULA_POR_TIPO_MODALIDADE);
            int minutosAgendarAntecendencia = obterMinutosMarcarDesmarcarAula(ctx, usuario, true, null);
            ConfiguracaoSistema cfgFilaEspera = configuracaoSistemaService.consultarPorTipo(ctx, ConfiguracoesEnum.HABILITAR_FILA_ESPERA);
            ConfiguracaoSistema cfgDescontarCredito = configuracaoSistemaService.consultarPorTipo(ctx, ConfiguracoesEnum.DESCONTAR_CREDITO_AO_MARCAR_AULA_SEM_CONFIRMAR_PRESENCA);
            ConfiguracaoSistema cfgBloquearReposicao = css.consultarPorTipo(ctx, ConfiguracoesEnum.BLOQUEAR_GERAR_REPOSICAO_AULA_JA_REPOSTA);
            ParamTurmasJSON param = new ParamTurmasJSON();
            param.setBloquearGerarReposicaoAulaJaReposta(cfgBloquearReposicao.getValorAsBoolean());
            param.setIgnorarTolerancia(usuario != null &&
                    (usuario.isFuncionalidadeHabilitado(RecursoEnum.USUARIO_MARCAR_ANTECEDENCIA.name()) ||
                            usuario.isFuncionalidadeHabilitado(RecursoEnum.INSERIR_ALUNO_AULA_INICIADA.name())));
            param.setCodigoCliente(codigoCliente);
            param.setUsuario(null != usuario && null != usuario.getUsuarioZW() ? usuario.getUsuarioZW() : 0);
            param.setEmpresa(empresa);
            param.setExperimental(aulaExperimental);

            param.setValidarModalidade(cfgAulaValidarMd.getValorAsBoolean());
            if (cfgAulaValidarVezesMd.getValorAsInteger() != null && cfgAulaValidarVezesMd.getValorAsInteger() > 0) {
                param.setNrValidarVezesModalidade(cfgAulaValidarVezesMd.getValorAsInteger());
            } else {
                param.setNrValidarVezesModalidade(0);
            }
            param.setValidarHorario(cfgAulaValidarHorario.getValorAsBoolean());
            param.setControlarFreepass(cfgControlarFP.getValorAsBoolean());
            param.setProibirComParcelaVencida(cfInacimplente.getValorAsBoolean());
            param.setProibirMarcarAulaAntesPagamentoPrimeiraParcela(cfgProibirMarcarAulaAntesPagamentoPrimeiraParc.getValorAsBoolean());

            param.setNrAulasExperimentais(clienteSintetico != null && clienteSintetico.getNrAulasExperimentais() > 0 ?
                    clienteSintetico.getNrAulasExperimentais() :
                    cfgAulaExperimental.getValorAsInteger());

            param.setIdHorarioTurmaOrigem(idHorarioTurma);
            param.setDataOrigem(Uteis.getDataAplicandoFormatacao(data, "dd/MM/yyyy"));
            param.setMinutosAgendarAntecedencia(minutosAgendarAntecendencia);
            param.setOrigemSistema(origem != null ? origem.getCodigo() : null);
            param.setPermAlunoMarcarAulaOutraEmpresa(cfgPermAlunoMarcarAulaOutraUnidade.getValorAsBoolean());
            param.setSomenteValidar(somenteValidar);
            param.setBookingId(bookingId);
            if(origem != OrigemSistemaEnum.APP_TREINO) {
                param.setFilaEspera(cfgFilaEspera != null && cfgFilaEspera.getValorAsBoolean());
            } else {
                param.setFilaEspera(false);
            }
            param.setModalidade(modalidadeDao.consultarModalidadeHorarioTurma(ctx,idHorarioTurma));

            if (cfgQtdFaltasBloqueioAluno.getValorAsInteger() != null && cfgQtdFaltasBloqueioAluno.getValorAsInteger() > 0) {
                param.setQtdfaltasbloqueioaluno(cfgQtdFaltasBloqueioAluno.getValorAsInteger());
            } else {
                param.setQtdfaltasbloqueioaluno(0);
            }
            if (cfgQtdTempoBloqueioAluno.getValorAsInteger() != null && cfgQtdTempoBloqueioAluno.getValorAsInteger() > 0) {
                param.setQtdtempobloqueioaluno(cfgQtdTempoBloqueioAluno.getValorAsInteger());
            } else {
                param.setQtdtempobloqueioaluno(0);
            }
            param.setDescontarCreditoContratoAulaMarcada(cfgDescontarCredito != null && cfgDescontarCredito.getValorAsBoolean());
            if(clienteSintetico !=null && clienteSintetico.getNivelAluno() != null){
                param.setNivel(clienteSintetico.getNivelAluno().getCodigo());
            }

            String retorno = integracaoWS.inserirAlunoAulaCheia( ctx, param);
            if(retorno.toLowerCase().startsWith("erro") && !retorno.toLowerCase().contains("aulaexperimental") &&
                    origem != null && !origem.equals(OrigemSistemaEnum.AULA_CHEIA)){
                this.aulaDiaExcecaoDao.insert(ctx, new AulaDiaExcecao(Calendario.hoje(), idHorarioTurma+"_"+Uteis.getData(data, "ddMMyy"), clienteSintetico));
            }

            try {
                if(!retorno.toLowerCase().startsWith("erro")){
                    ConfigGymPassService configGymPassService = UtilContext.getBean(ConfigGymPassService.class);
                    ConfigGymPass configGymPass = configGymPassService.obterPorEmpresaZW(ctx, param.getEmpresa());
                    if(!UteisValidacao.emptyString(configGymPass.getCodigoGymPass())){
                        iniciaThreadGympass(ctx, idHorarioTurma, param.getEmpresa(), data, configGymPass);
                    }
                }
            }catch (Exception e){
                Uteis.logar(e, AgendaTotalServiceImpl.class);
            }

            return retorno;
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    public String inserirNaFilaDeEspera(String ctx, Integer codigoHorarioTurma, String dia, Integer codigoAluno) throws Exception {
        FilaDeEsperaDTO param = new FilaDeEsperaDTO();
        param.setCodigoAluno(codigoAluno);
        param.setCodigoHorarioTurma(codigoHorarioTurma);
        param.setDia(dia);
        JSONObject jsonObject = postFilaEspera(ctx,
                "/prest/aulacheia/inserir-fila-espera",
                param);
        return jsonObject.getString("content");
    }

    public String removerDaFilaDeEspera(String ctx, Integer codigoHorarioTurma, String dia, Integer codigoAluno) throws Exception {
        FilaDeEsperaDTO param = new FilaDeEsperaDTO();
        param.setCodigoAluno(codigoAluno);
        param.setCodigoHorarioTurma(codigoHorarioTurma);
        param.setDia(dia);
        JSONObject jsonObject = postFilaEspera(ctx,
                "/prest/aulacheia/remover-fila-espera",
                param);
        return jsonObject.getString("content");
    }

    public String removerDaFilaDeEsperaV2(String ctx, Integer codigoHorarioTurma, String dia, Integer codigoAluno, Integer codigoUsuario) throws Exception {
        FilaDeEsperaDTO param = new FilaDeEsperaDTO();
        param.setCodigoAluno(codigoAluno);
        param.setCodigoHorarioTurma(codigoHorarioTurma);
        param.setDia(dia);
        param.setCodigoUsuario(codigoUsuario);
        param.setOrigemSistema(OrigemSistemaEnum.AULA_CHEIA.getCodigo());
        JSONObject jsonObject = postFilaEspera(ctx,
                "/prest/aulacheia/remover-fila-espera",
                param);
        return jsonObject.getString("content");
    }

    public String inserirNaFilaDeEsperaTurmaCrm(String ctx, Integer codigoHorarioTurma, Integer codigoAluno, PassivoDTO passivo, Integer codigoPassivo) throws ServiceException {
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("INSERT INTO filaesperaturmacrm (");
            sql.append("cliente, passivo, horarioturma, dataregistro, dataentrada, ordem)");
            sql.append(" VALUES ( ?, ?, ?, ?, ?, ?)");
            sql.append(" RETURNING codigo");

            int i = 1;
            try (Connection connection = conexaoZWService.conexaoZw(ctx)) {
                Integer codigoReceptivo = inserirPassivo(passivo, connection);
                Integer ordem = obterUltimaOrdemFilaTurmaCRM(connection, codigoHorarioTurma);

                PreparedStatement stm = connection.prepareStatement(sql.toString());
                if(!UteisValidacao.emptyNumber(codigoAluno)) {
                    stm.setInt(i++, codigoAluno);
                } else {
                    stm.setNull(i++, 0);
                }
                if (!UteisValidacao.emptyNumber(codigoReceptivo)) {
                    stm.setInt(i++, codigoReceptivo);
                } else {
                    if (!UteisValidacao.emptyNumber(codigoPassivo)) {
                        stm.setInt(i++, codigoPassivo);
                    } else {
                        stm.setNull(i++, 0);
                    }
                }
                stm.setInt(i++, codigoHorarioTurma);
                stm.setDate(i++, Uteis.getDataJDBC(Calendario.hoje()));
                stm.setDate(i++, Uteis.getDataJDBC(Calendario.hoje()));
                stm.setInt(i++, ordem);

                try (ResultSet resultSet = stm.executeQuery()) {
                    if (resultSet.next()) {
                        return "Inserido na fila com sucesso, codigo: " + resultSet.getInt("codigo");
                    }
                    return null;
                }
            }
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    public String removerDaFilaDeEsperaTurmaCrm(String ctx, Integer codigoHorarioTurma, Integer codigoAluno, Integer passivo) throws ServiceException {
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("DELETE FROM filaesperaturmacrm WHERE horarioturma = ?");
            if(!UteisValidacao.emptyNumber(codigoAluno)) {
                sql.append(" AND cliente = ?");
            }
            if(!UteisValidacao.emptyNumber(passivo)) {
                sql.append(" AND passivo = ?");
            }

            try (Connection connection = conexaoZWService.conexaoZw(ctx)) {
                PreparedStatement stm = connection.prepareStatement(sql.toString());
                stm.setInt(1, codigoHorarioTurma);
                stm.setInt(2, !UteisValidacao.emptyNumber(codigoAluno) ? codigoAluno : passivo);
                stm.execute();
                return "Removido da fila com sucesso";
            }
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    private Integer inserirPassivo(PassivoDTO passivo, Connection connection) throws Exception {
        if (passivo != null) {
            StringBuilder sqlRecep = new StringBuilder();
            sqlRecep.append("INSERT INTO passivo (");
            sqlRecep.append("nome, telefonecelular, dia, email, origemsistema, responsavelcadastro, empresa)");
            sqlRecep.append(" VALUES ( ?, ?, ?, ?, ?, ?, ?)");
            sqlRecep.append(" RETURNING codigo");

            int i = 1;
            PreparedStatement stm1 = connection.prepareStatement(sqlRecep.toString());
            stm1.setString(i++, passivo.getNomeCompleto());
            stm1.setString(i++, passivo.getCelular());
            stm1.setDate(i++, Uteis.getDataJDBC(Calendario.hoje()));
            stm1.setString(i++, passivo.getEmail());
            stm1.setInt(i++, OrigemSistemaEnum.FILA_ESPERA.getCodigo());
            stm1.setInt(i++, passivo.getResponsavelCadastro());
            stm1.setInt(i++, passivo.getEmpresa());

            try (ResultSet resultSet = stm1.executeQuery()) {
                if (resultSet.next()) {
                    return resultSet.getInt("codigo");
                }
                return null;
            }
        }
        return null;
    }

    private Integer obterUltimaOrdemFilaTurmaCRM(Connection connection, Integer horarioTurmaId) throws Exception {
        String sql = "SELECT COALESCE(MAX(ordem), 0) + 1 as proxima_ordem " +
                "FROM filaesperaturmacrm " +
                "WHERE horarioturma = ?";

        try (PreparedStatement ps = connection.prepareStatement(sql)) {
            ps.setInt(1, horarioTurmaId);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt("proxima_ordem");
                }
                return 1;
            }
        }
    }

    public String consultarFila(String ctx, Integer codigoHorarioTurma, String dia, Integer matricula) throws Exception {
        FilaDeEsperaDTO param = new FilaDeEsperaDTO();
        param.setMatricula(matricula);
        param.setCodigoHorarioTurma(codigoHorarioTurma);
        param.setDia(dia);
        JSONObject jsonObject = postFilaEspera(ctx,
                "/prest/aulacheia/consultar-fila-espera",
                param);
        return jsonObject.getString("content");
    }


    public void iniciaThreadGympass(String chave, Integer idHorarioTurma, Integer empresaZW, Date dia, ConfigGymPass configGymPass) {
        try {
            ThreadGympass thread = new ThreadGympass(chave, null, empresaZW, null, TipoThreadGympassEnum.SINCRONIZAR_BOOKED);
            thread.setHorarioTurma(idHorarioTurma);
            thread.setConfigGymPass(configGymPass);
            thread.setDia(dia);
            thread.setDaemon(true);
            thread.start();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private int obterMinutosMarcarDesmarcarAula(final String ctx, Usuario usuarioAtual, boolean marcarAula, Integer codUsuarioTreino) throws ServiceException {
        ConfiguracaoSistemaService css = UtilContext.getBean(ConfiguracaoSistemaService.class);
        ConfiguracaoSistema cfgLimiteMarcar = css.consultarPorTipo(ctx, ConfiguracoesEnum.MINUTOS_AGENDAR_COM_ANTECEDENCIA);
        ConfiguracaoSistema cfgLimiteDesmarcar = css.consultarPorTipo(ctx, ConfiguracoesEnum.MINUTOS_DESMARCAR_COM_ANTECEDENCIA);
        if (usuarioAtual == null && codUsuarioTreino != null) {
            usuarioAtual = usuarioService.obterPorId(ctx, sessaoService.getUsuarioAtual().getId());
        }
        int minutosMarcarDesmarcar = 0;
        if(marcarAula){
            minutosMarcarDesmarcar = cfgLimiteMarcar.getValorAsInteger();
        }else{
            minutosMarcarDesmarcar = cfgLimiteDesmarcar.getValorAsInteger();
        }
        if (usuarioAtual != null) {
            if (!SuperControle.independente(ctx)) {
                if (usuarioAtual.isFuncionalidadeHabilitado(RecursoEnum.USUARIO_MARCAR_ANTECEDENCIA.name())) {
                    minutosMarcarDesmarcar = 0;
                }
            }
        }
        return minutosMarcarDesmarcar;
    }

    @Override
    public String excluirAlunoAulaCheia(String ctx, Integer codigoCliente, Integer codigoPassivo, Integer idHorarioTurma,
                                        Date data, OrigemSistemaEnum origem, Integer usuario, Integer empresa, Integer codUsuarioTreino) throws ServiceException {
        try {
            Usuario usuarioAtual = null;
            if(!UteisValidacao.emptyNumber(usuario)){
                usuarioAtual = getUsuarioService().obterPorId(ctx, usuario);
            }

            if(Aplicacao.independente(ctx)){
                if (validarSePodeDesmarcarAulaAluno(ctx, data, idHorarioTurma, usuario)) {
                    agendaService.removerAluno(ctx, codigoCliente, data, idHorarioTurma);
                    return "OK";
                } else {
                    if(!UteisValidacao.emptyNumber(usuario)){
                        usuarioAtual = getUsuarioService().obterPorId(ctx, usuario);
                    }
                    AgendaExcecoes.ERRO_ENCERROU_PRAZO_DESMARCAR.setDescricaoExcecao(
                            String.format("Você só pode desmarcar aula com %s minutos de antecedência",
                                    obterMinutosMarcarDesmarcarAula(ctx, usuarioAtual, false, null)
                            ));
                    throw new ServiceException(AgendaExcecoes.ERRO_ENCERROU_PRAZO_DESMARCAR);
                }
            }else{
                ParamTurmasJSON param = new ParamTurmasJSON();
                param.setUsuario(usuario);
                param.setEmpresa(empresa);
                param.setCodigoCliente(codigoCliente);
                param.setCodigoPassivo(codigoPassivo);
                param.setOrigemSistema(origem.getCodigo());
                param.setIdHorarioTurmaOrigem(idHorarioTurma);
                param.setDataOrigem(Uteis.getDataAplicandoFormatacao(data, "dd/MM/yyyy"));
                param.setMinutosDesmarcarAntecedencia(obterMinutosMarcarDesmarcarAula(ctx, usuarioAtual, false, codUsuarioTreino));

                try {
                    ConfiguracaoSistemaService css = UtilContext.getBean(ConfiguracaoSistemaService.class);
                    ConfiguracaoSistema cfgManterReposicao = css.consultarPorTipo(ctx, ConfiguracoesEnum.MANTER_REPOSICAO_AULA_COLETIVA);
                    ConfiguracaoSistema cfgLimiteReposicao = css.consultarPorTipo(ctx, ConfiguracoesEnum.LIMITE_DIAS_REPOSICAO_AULA_COLETIVA);
                    ConfiguracaoSistema cfgBloquearReposicao = css.consultarPorTipo(ctx, ConfiguracoesEnum.BLOQUEAR_GERAR_REPOSICAO_AULA_JA_REPOSTA);
                    param.setLimiteReposicoes(cfgLimiteReposicao.getValorAsInteger());
                    param.setManterRenovacao(cfgManterReposicao.getValorAsBoolean());
                    param.setBloquearGerarReposicaoAulaJaReposta(cfgBloquearReposicao.getValorAsBoolean());
                    ConfiguracaoSistema cfgFilaEspera= css.consultarPorTipo(ctx, ConfiguracoesEnum.HABILITAR_FILA_ESPERA);
                    param.setFilaEspera(cfgFilaEspera.getValorAsBoolean());
                }catch (Exception e){
                    e.printStackTrace();
                    param.setLimiteReposicoes(0);
                    param.setManterRenovacao(false);
                }


                IntegracaoTurmasWSConsumer integracaoWS = UtilContext.getBean(IntegracaoTurmasWSConsumer.class);
                return integracaoWS.excluirAlunoAulaCheia( ctx, param);
            }
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    private boolean validarSePodeDesmarcarAulaAluno(String ctx, Date data, Integer idHorarioTurma, Integer usuario) throws Exception {
        Usuario usuarioAtual = null;
        if(!UteisValidacao.emptyNumber(usuario)){
            usuarioAtual = getUsuarioService().obterPorId(ctx, usuario);
        }
        int minutosAgendarAntecendencia = obterMinutosMarcarDesmarcarAula(ctx, usuarioAtual, false, null);
        AulaHorario aulaHorario = UtilContext.getBean(AulaHorarioDao.class).findById(ctx, idHorarioTurma);
        String[] horaMin = aulaHorario.getInicio().split(":");
        data.setHours(Integer.parseInt(horaMin[0], 10));
        data.setMinutes(Integer.parseInt(horaMin[1], 10));

        data.setMinutes(data.getMinutes() - minutosAgendarAntecendencia);

        Date dataAcao = new Date();
        return Calendario.menorComHora(dataAcao, data);
    }

    @Override
    public String desmarcarAluno(String ctx, Integer codigoCliente, Integer idHorarioTurma, Date data, Integer codigoContrato,
                                 OrigemSistemaEnum origem, Integer usuario, Integer empresa, boolean isExperimental, String justificativa) throws ServiceException {
        try {
            ParamTurmasJSON param = new ParamTurmasJSON();
            param.setUsuario(usuario);
            param.setEmpresa(empresa);
            param.setCodigoCliente(codigoCliente);
            param.setIdHorarioTurmaOrigem(idHorarioTurma);
            param.setCodigoContrato(codigoContrato);
            param.setOrigemSistema(origem.getCodigo());
            param.setDataOrigem(Uteis.getDataAplicandoFormatacao(data, "dd/MM/yyyy"));
            param.setExperimental(isExperimental);
            param.setJustificativa(justificativa);
            final String url = Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebInteg);
            IntegracaoTurmasWSConsumer integracaoWS = UtilContext.getBean(IntegracaoTurmasWSConsumer.class);
            return integracaoWS.desmarcarAula( ctx, param);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public List<LinhaSemanaTO> montarAgendaSemana(Date inicio, Date fim,
                                                  Map<String, AgendaTotalTO> mapaAgenda, List<Integer> diasSemana,
                                                  List<Integer> horas,
                                                  Map<Integer, GenericoTO> mapaLocais,
                                                  Map<Integer, GenericoTO> mapaTipos,
                                                  Integer responsavelSelecionado) {

        Map<Integer, LinhaSemanaTO> mapa = new HashMap<Integer, LinhaSemanaTO>();
        Map<Integer, Map<Integer, DiaSemanaAgendaTO>> mapaPorDia = new HashMap<Integer, Map<Integer, DiaSemanaAgendaTO>>();
        for (Integer hora : horas) {
            mapa.put(hora, new LinhaSemanaTO(hora));
            Map<Integer, DiaSemanaAgendaTO> mapadias = new HashMap<Integer, DiaSemanaAgendaTO>();
            for (Integer dia : diasSemana) {
                mapadias.put(dia, new DiaSemanaAgendaTO(dia, hora));
            }
            mapaPorDia.put(hora, mapadias);
        }

        for (AgendaTotalTO agendamento : mapaAgenda.values()) {
            if ((!Calendario.entre(agendamento.getStartDate(), inicio, fim)
                    && !Calendario.igual(agendamento.getStartDate(), inicio)
                    && !Calendario.igual(agendamento.getStartDate(), fim))
                    || !filtrar(agendamento, responsavelSelecionado, mapaLocais, mapaTipos)) {
                continue;
            }
            Map<Integer, DiaSemanaAgendaTO> linha = mapaPorDia.get(Uteis.gethoraHH(agendamento.getStartDate()));
            if(linha != null){
                Integer diaSemana = Uteis.getDiaDaSemanaNumero(agendamento.getStartDate());
                DiaSemanaAgendaTO diaSemanaTO = linha.get(diaSemana);
                diaSemanaTO.getAgendamentos().add(agendamento);
            }

        }
        for (Integer key : mapaPorDia.keySet()) {
            LinhaSemanaTO linha = mapa.get(key);
            Map<Integer, DiaSemanaAgendaTO> mapahora = mapaPorDia.get(key);
            List<DiaSemanaAgendaTO> values = new ArrayList<DiaSemanaAgendaTO> (mapahora.values());
            for (DiaSemanaAgendaTO dsa : values) {
                dsa.agendamentoOrderBy();
            }
            linha.getDias().addAll(Ordenacao.ordenarLista(values, "diaSemana"));
        }
        List<LinhaSemanaTO> lista = new ArrayList<LinhaSemanaTO>(mapa.values());
        return Ordenacao.ordenarLista(lista, "hora");
    }

    public boolean filtrar(AgendaTotalTO a, Integer responsavelSelecionado,
                           Map<Integer, GenericoTO> mapaLocais, Map<Integer, GenericoTO> mapaTipos) {
        if (responsavelSelecionado != null && responsavelSelecionado > 0
                && responsavelSelecionado != a.getCodigoResponsavel()) {
            return false;
        }
        if (mapaLocais.get(a.getCodigoLocal()) != null
                && !mapaLocais.get(a.getCodigoLocal()).getEscolhido()) {
            return false;
        }
        if (mapaTipos.get(a.getCodigotipo()) != null
                && !mapaTipos.get(a.getCodigotipo()).getEscolhido()) {
            return false;
        }
        return true;
    }

    @Override
    public List<LinhaAgendaTO> montarAgendaHoras(Date data, Map<String, AgendaTotalTO> mapaAgenda,
                                                 List<GenericoTO> tipos,
                                                 List<Integer> horas,
                                                 TipoAgrupamentoAgendaEnum agrupamento,
                                                 Map<Integer, GenericoTO> mapaLocais,
                                                 Map<Integer, GenericoTO> mapaTipos,
                                                 Integer responsavelSelecionado) {
        Map<Integer, LinhaAgendaTO> mapa = new HashMap<Integer, LinhaAgendaTO>();
        Map<Integer, Map<Integer, HoraAgendaTO>> mapaPorHora = new HashMap<Integer, Map<Integer, HoraAgendaTO>>();

        for (GenericoTO generico : tipos) {
            mapa.put(generico.getCodigo(), new LinhaAgendaTO(generico.getCodigo(),
                    generico.getLabel(), generico.getCss()));
            Map<Integer, HoraAgendaTO> mapahoras = new HashMap<Integer, HoraAgendaTO>();
            for (Integer hr : horas) {
                mapahoras.put(hr, new HoraAgendaTO(hr, generico.getCodigo()));
            }
            mapaPorHora.put(generico.getCodigo(), mapahoras);
        }

        for (AgendaTotalTO agendamento : mapaAgenda.values()) {
            if (!Calendario.igual(agendamento.getStartDate(), data)
                    || !filtrar(agendamento, responsavelSelecionado, mapaLocais, mapaTipos)) {
                continue;
            }
            Integer chave = 0;
            switch (agrupamento) {
                case LOCAL:
                    chave = agendamento.getCodigoLocal();
                    break;
                case RESPONSAVEL:
                    chave = agendamento.getCodigoResponsavel();
                    break;
                case TIPO:
                    chave = agendamento.getCodigotipo();
                    break;
            }
            Map<Integer, HoraAgendaTO> linha = mapaPorHora.get(chave);
            Integer hora = Uteis.gethoraHH(agendamento.getStartDate());
            HoraAgendaTO horaTO = ((linha != null && linha.size() > 0) ? linha.get(hora) : null);
            if(horaTO != null){
                horaTO.getAgendamentos().add(agendamento);
                horaTO.setNrVagasDisponiveis(horaTO.getNrVagasDisponiveis() + (agendamento.getNrVagas() - agendamento.getNrVagasPreenchidas()));
                horaTO.setNrVagasPreenchidas(horaTO.getNrVagasPreenchidas() + agendamento.getNrVagasPreenchidas());
                horaTO.setNrVagas(horaTO.getNrVagas() + agendamento.getNrVagas());
            }

        }
        for (Integer key : mapaPorHora.keySet()) {
            LinhaAgendaTO linha = mapa.get(key);
            Map<Integer, HoraAgendaTO> mapahora = mapaPorHora.get(key);
            List<HoraAgendaTO> values = new ArrayList<HoraAgendaTO> (mapahora.values());
            for (HoraAgendaTO hato : values) {
                hato.agendamentoOrderBy();
            }
            linha.getLista().addAll(Ordenacao.ordenarLista(values, "hora"));
        }
        List<LinhaAgendaTO> lista = new ArrayList<LinhaAgendaTO>(mapa.values());
        return Ordenacao.ordenarLista(lista, "nome");
    }

    @Override
    public String reporAula(String ctx,
                            Integer codigoCliente,
                            Integer idHorarioTurmaOrigem,
                            Integer idHorarioTurmaReposicao,
                            Integer codigoContrato,
                            OrigemSistemaEnum origem,
                            Integer usuario,
                            Integer empresa,
                            Date dataOrigem,
                            Date dataReposicao,
                            boolean forcarMarcar) throws ServiceException {
        try {
            ParamTurmasJSON param = new ParamTurmasJSON();
            param.setUsuario(usuario);
            param.setEmpresa(empresa);
            param.setCodigoCliente(codigoCliente);
            param.setIdHorarioTurmaReposicao(idHorarioTurmaReposicao);
            param.setIdHorarioTurmaOrigem(idHorarioTurmaOrigem);
            param.setCodigoContrato(codigoContrato);
            param.setForcarMarcar(forcarMarcar);
            param.setOrigemSistema(origem.getCodigo());
            param.setDataOrigem(Uteis.getDataAplicandoFormatacao(dataOrigem, "dd/MM/yyyy"));
            param.setDataReposicao(Uteis.getDataAplicandoFormatacao(dataReposicao, "dd/MM/yyyy"));

            IntegracaoTurmasWSConsumer integracaoWS = UtilContext.getBean(IntegracaoTurmasWSConsumer.class);
            return integracaoWS.reporAula( ctx, param);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public AgendaTotalTO obterAulaOrigem(String ctx, AgendaTotalTO agendamentoReposicao,
                                         Map<String, AgendaTotalTO> agenda,
                                         Map<String, List<AgendadoTO>> mapaAgendados, AgendadoTO agendado) throws Exception {

        IntegracaoTurmasWSConsumer integracaoWS = UtilContext.getBean(IntegracaoTurmasWSConsumer.class);
        String aulaARepor = integracaoWS.aulaARepor( ctx, agendado.getCodigoCliente(), agendado.getCodigoContrato(),
                agendamentoReposicao.getCodigotipo());
        AgendaTotalTO agendamento = agenda.get(aulaARepor);
        if (agendamento == null) {
            return new AgendaTotalTO();
        }
        List<AgendadoTO> agendados = mapaAgendados.get(agendamento.getIdentificador());
        for (AgendadoTO ag : agendados) {
            if (ag.isDesmarcado()
                    && ag.isReposto()
                    && ag.getMatricula().equals(agendado.getMatricula())) {
                throw new ServiceException("auladessediajafoiremarcada");
            }
        }
        return agendamento;
    }

    @Override
    public void baixarFotosAlunos(String ctx, HttpServletRequest request) throws Exception {
        List<Empresa> all = empresaDao.findAll(ctx);
        List<Integer> pessoas = new ArrayList<Integer>();
        for (Empresa e : all) {
            IntegracaoTurmasWSConsumer integracaoWS = UtilContext.getBean(IntegracaoTurmasWSConsumer.class);
            List<AgendadoTO> agendados = integracaoWS.consultarAgendados( ctx, Calendario.hoje(),
                    Calendario.hoje(), e.getCodZW());
            for (AgendadoTO agendado : agendados) {
                if (!pessoas.contains(agendado.getCodigoPessoa())) {
                    pessoas.add(agendado.getCodigoPessoa());
                }
            }

        }
        for (Integer codigoPessoa : pessoas) {
            Aplicacao.preencherFoto(ctx, codigoPessoa, true, false, false, request.getSession().getServletContext());
        }
    }

    @Override
    public String obterProximasAulas(String ctx, Integer matricula, Boolean proximos30dias) throws Exception {
        IntegracaoTurmasWSConsumer integracaoWS = UtilContext.getBean(IntegracaoTurmasWSConsumer.class);
        return integracaoWS.consultarProximasAulas( ctx, matricula, proximos30dias);
    }

    @Override
    public String obterProximasAulasVerificandoModoConsulta(String ctx, Integer matricula, Boolean proximos30dias) throws Exception {
        if(Aplicacao.getProp(Aplicacao.modoConsultaAgenda).equals("webservice")) {
            return obterProximasAulas(ctx, matricula, proximos30dias);
        } else {
            AgendaModoBDServiceImpl agendaAulasService;
            try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
                agendaAulasService = new AgendaModoBDServiceImpl(conZW, ctx);
                return agendaAulasService.consultarProximasAulas(matricula, proximos30dias);
            }

        }
    }

    @Override
    public List<AgendaTotalJSON> consultarAulasDesmarcadas(String ctx, Integer matricula, Integer modalidade) throws Exception {
        final String url = Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebInteg);
        IntegracaoTurmasWSConsumer integracaoWS = UtilContext.getBean(IntegracaoTurmasWSConsumer.class);
        return integracaoWS.obterAulasDesmarcadas(url, ctx, matricula, modalidade);
    }

    @Override
    public List<AgendaTotalJSON> consultarAulasDiaAluno(String ctx, Integer matricula, Date data, Integer modalidade) throws Exception {
        final String url = Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebInteg);
        IntegracaoTurmasWSConsumer integracaoWS = UtilContext.getBean(IntegracaoTurmasWSConsumer.class);
        return integracaoWS.obterAulasDiaAluno(url, ctx, matricula, data, modalidade);
    }

    @Override
    public String marcarDesmarcar(String ctx, Integer matricula, Date data,
                                  Integer codigoHorarioTurma, boolean marcar, boolean validarParcelaVencida,
                                  boolean ProibirMarcarAulaAntesPagamentoPrimeiraParcela, Integer contrato) throws Exception {
        JSONObject dados = new JSONObject();
        dados.put("matriculaAluno", matricula);
        dados.put("horarioturma", codigoHorarioTurma);
        dados.put("data", Uteis.getDataAplicandoFormatacao(data, "dd/MM/yyyy"));
        dados.put("marcar", marcar + "");
        dados.put("bloquearParcelaVencida", validarParcelaVencida + "");
        dados.put("proibirMarcarAulaAntesPagamentoPrimeiraParcela", ProibirMarcarAulaAntesPagamentoPrimeiraParcela + "");
        dados.put("contrato", contrato);

        JSONObject content = chamadaZW(ctx,
                "/prest/aulacheia/marcar-desmarcar-aulas-app",
                null,
                null,
                null,
                dados);
        return content.getString("content");
    }

    @Override
    public List<Integer> modalidadesContrato(String ctx, Integer contrato, Integer matricula){
        List<Integer> modalidadesContrato = new ArrayList<>();
        try {
            JSONObject dados = new JSONObject();
            dados.put("contrato", contrato);
            dados.put("matricula", matricula);

            JSONObject content = chamadaZW(ctx,
                    "/prest/aulacheia/modalidades-contrato",
                    null,
                    null,
                    null,
                    dados);
            JSONArray jsonArray = content.getJSONArray("content");
            for (int i = 0; i < jsonArray.length(); i++){
                modalidadesContrato.add(jsonArray.getInt(i));
            }
        } catch (Exception e){
            Uteis.logar(e, AgendaTotalServiceImpl.class);
        }
        return modalidadesContrato;
    }

    @Override
    public String modalidadesTitular(String ctx, Integer matricula) {
        String modalidadesTitular = "";
        try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
            try {
                String sql = "SELECT STRING_AGG(m.codigo :: text, '|') AS codigos from cliente cli " +
                        "                                inner join situacaoclientesinteticodw tit on tit.codigocliente = cli.titularplanocompartilhado\n" +
                        "                                inner join pessoa ptitular on ptitular.codigo = tit.codigopessoa\n" +
                        "                                inner join contrato con on ptitular.codigo = con.pessoa OR ptitular.codigo = con.pessoaoriginal \n" +
                        "                                inner join contratomodalidade cm on cm.contrato = con.codigo\n" +
                        "                                INNER JOIN usuario u ON u.codigo = con.responsavelcontrato\n" +
                        "                                INNER JOIN modalidade m ON m.codigo = cm.modalidade\n" +
                        "where cli.titularplanocompartilhado is not null and cli.codigomatricula = " + matricula + " and con.situacao = 'AT';";
                try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sql, conZW)) {
                    if (rs.next()) {
                        modalidadesTitular = rs.getString("codigos") != null ? rs.getString("codigos") : "";
                    }
                }
            } catch (Exception e) {
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return modalidadesTitular;
    }

    private JSONObject chamadaZW(String ctx,
                                 String endpoint,
                                 Integer empresa,
                                 Long inicio,
                                 Long fim,
                                 JSONObject dados) throws Exception {
        final String url = Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebIntegProp);
        CloseableHttpClient client = ExecuteRequestHttpService.createConnector();
        HttpPost httpPost = new HttpPost(url + endpoint);
        List<NameValuePair> params = new ArrayList<>();
        params.add(new BasicNameValuePair("chave", ctx));
        if (empresa != null) {
            params.add(new BasicNameValuePair("empresa", empresa.toString()));
        }
        if(dados != null){
            params.add(new BasicNameValuePair("dados", dados.toString()));
        }
        if(inicio != null){
            params.add(new BasicNameValuePair("inicio", inicio.toString()));
        }
        if(fim != null){
            params.add(new BasicNameValuePair("fim", fim.toString()));
        }
        httpPost.setEntity(new UrlEncodedFormEntity(params));
        CloseableHttpResponse response = client.execute(httpPost);
        ResponseHandler<String> handler = new BasicResponseHandler();
        String body = handler.handleResponse(response);
        client.close();
        return new JSONObject(body);
    }

    @Override
    public String incluirAulaExperimental(String ctx, Integer matricula, Date data, Integer codigoHorarioTurma, Integer usuario, Integer produtoFreePass) throws Exception {
        IntegracaoTurmasWSConsumer integracaoWS = UtilContext.getBean(IntegracaoTurmasWSConsumer.class);
        String experimental = integracaoWS.inserirAlunoExperimental(ctx, matricula, codigoHorarioTurma, data, usuario, produtoFreePass);
        try {
            iniciaThreadGympass(ctx, codigoHorarioTurma, null, data, null);
        } catch (Exception e) {
            e.printStackTrace();
            Uteis.logarDebug("Erro ao iniciar thread gympass para aula experimental: " + e.getMessage());
        }
        return experimental;
    }

    @Override
    public String gravarPresenca(String ctx, Integer cliente, Date data, Integer codigoHorarioTurma, boolean reposicao, boolean desmarcar, Integer codColaborador, OrigemSistemaEnum origem) throws Exception {
        if (codColaborador == null && origem != null  && !origem.equals(OrigemSistemaEnum.APP_TREINO)) {
            try {
                codColaborador = usuarioService.obterPorId(ctx, sessaoService.getUsuarioAtual().getId()).getProfessor().getCodigoColaborador();
            }catch (Exception e){
                codColaborador = 0;
            }
        }

        JSONObject dados = new JSONObject();
        dados.put("codCliente", cliente);
        dados.put("codHorarioTurma", codigoHorarioTurma);
        dados.put("diaAula", Calendario.getData(data, "dd/MM/yyyy HH:mm"));
        dados.put("codColaborador", codColaborador == null ? 0 : codColaborador);
        dados.put("origem", origem == null ? OrigemSistemaEnum.AULA_CHEIA.getCodigo() : origem.getCodigo());
        dados.put("desmarcar", desmarcar);
        dados.put("isAulaCheia", false);
        dados.put("reposicao", reposicao);

        JSONObject content = chamadaZW(ctx,
                "/prest/confirmar-presenca/presenca",
                null,
                null,
                null,
                dados);

        return content.getString("content");
    }

    @Override
    public JSONArray produtosFreePass(String ctx, Integer empresa, Boolean somenteAtivos) throws Exception{
        IntegracaoTurmasWSConsumer integracaoWS = UtilContext.getBean(IntegracaoTurmasWSConsumer.class);
        String freePass = integracaoWS.produtosFreePass( ctx, empresa, somenteAtivos);
        return new JSONArray(freePass);
    }

    @Override
    public List<ControleCreditoTreinoJSON> consultarExtratoCreditos(String ctx, Integer matricula, Date data) throws Exception {
        IntegracaoTurmasWSConsumer integracaoWS = UtilContext.getBean(IntegracaoTurmasWSConsumer.class);
        return integracaoWS.obterExtratoCreditos( ctx, matricula, data);
    }

    @Override
    public List<AulaDiaJSON> consultarAgendamentosModalidadesAluno(final String key, final Date inicio,
                                                                   final Date fim, final Integer matricula, final Integer empresa) throws Exception {

        IntegracaoTurmasWSConsumer integracaoWS = UtilContext.getBean(IntegracaoTurmasWSConsumer.class);
        String retornoListaJson = integracaoWS.consultarAgendamentosModalidadeAluno( key, inicio, fim, matricula);
        if(retornoListaJson.startsWith("ERRO:")){
            throw new Exception(retornoListaJson);
        }

        List<AgendaTotalJSON> listaJson = JSONMapper.getList(new JSONArray(retornoListaJson), AgendaTotalJSON.class);

        Map<Integer, List<Date>> mapaAulasExcluidas = obterAulasExcluidas(key, inicio, fim);
        List<AgendaTotalTO> listaTO = new ArrayList<AgendaTotalTO>();
        for(AgendaTotalJSON a : listaJson){
            if(!UteisValidacao.emptyString(a.getSituacao()) && a.getSituacao().equals("IN")){
                continue;
            }
            AgendaTotalTO agenda = new AgendaTotalTO(a);
            List<Date> datas = mapaAulasExcluidas.get(Integer.valueOf(agenda.getId()));
            if(datas != null && datas.contains(Calendario.getDataComHoraZerada(agenda.getStartDate()))){
                continue;
            }
            listaTO.add(agenda);
        }
        montarMapaAgendados(key, inicio, fim, empresa, listaTO, false);
        List<AulaDiaJSON> adjsons = new ArrayList<AulaDiaJSON>();
        for(AgendaTotalTO ato : listaTO){
            adjsons.add(new AulaDiaJSON(ato));
        }
        return adjsons;
    }

    public List<AgendaTotalJSON> consultarTurmasAluno(final String key, final Integer matricula) throws Exception {
        IntegracaoTurmasWSConsumer integracaoWS = UtilContext.getBean(IntegracaoTurmasWSConsumer.class);
        return integracaoWS.consultarTurmasAluno( key, matricula);
    }

    @Override
    public String consultarSaldoAluno(String key, Integer matricula, Integer contrato) throws Exception {
        JSONObject dados = new JSONObject();
        dados.put("matriculaAluno", matricula);
        dados.put("contrato", contrato);

        JSONObject content = chamadaZW(key,
                "/prest/aulacheia/consultar-saldo-aluno-app",
                null,
                null,
                null,
                dados);
        return content.getString("content");
    }

    @Override
    public String consultarSaldoAlunoVerificandoModoConsulta(String key, Integer matricula, Integer contrato) throws Exception {
        if(Aplicacao.getProp(Aplicacao.modoConsultaAgenda).equals("webservice")) {
            return consultarSaldoAluno(key, matricula, contrato);
        } else {
            AgendaModoBDServiceImpl agendaAulasService;
            try (Connection conZW = conexaoZWService.conexaoZw(key)) {
                agendaAulasService = new AgendaModoBDServiceImpl(conZW, key);
                return agendaAulasService.consultarSaldoAluno(matricula, false, contrato);
            }
        }
    }

    @Override
    public List<AgendaTotalJSON> obterAulasColetivas(String ctx, Date inicio, Date fim, Integer empresa) throws Exception {
        return obterAulasColetivas(ctx, inicio, fim, empresa, false);
    }

    public List<AgendaTotalJSON> obterAulasColetivas(String ctx, Date inicio, Date fim, Integer empresa, boolean independente) throws Exception {
        if(independente){
            List<EventoAulaDTO> eventoAulaDTOS = agendaService.agendaDia(inicio, ctx);
            List<AgendaTotalJSON> aulas  = new ArrayList<AgendaTotalJSON>();
            for(EventoAulaDTO e : eventoAulaDTOS){
                aulas.add(new AgendaTotalJSON(e, agendaService.nrAlunosHorarioDia(ctx, e.getId(), Uteis.dataHoraZeradaUTC(e.getDia()))));
            }
            return aulas;
        }else{
            IntegracaoTurmasWSConsumer integracaoWS = UtilContext.getBean(IntegracaoTurmasWSConsumer.class);
            return integracaoWS.consultarAgendamentosJSON( ctx, inicio, fim, empresa, "COLETIVAS", null);
        }
    }

    @Override
    public List<AgendaTotalJSON> obterAulas(String ctx, Date inicio, Date fim, Integer empresa) throws Exception {
        IntegracaoTurmasWSConsumer integracaoWS = UtilContext.getBean(IntegracaoTurmasWSConsumer.class);
        String timeZone = "";
        if(empresa != null && empresa > 0){
            Empresa timeZone1 = empresaService.obterPorIdZW(ctx, empresa);
            timeZone = timeZone1 != null && timeZone1.getTimeZoneDefault() != null ? timeZone1.getTimeZoneDefault() : "";
        }
        return integracaoWS.consultarAgendamentosJSON( ctx, inicio, fim, empresa, null, timeZone);
    }

    @Override
    public List<AgendaTotalJSON> obterAulasColetivasDoProfessor(String ctx, Date inicio, Date fim, Integer empresa, Integer codigoProfessor) throws Exception {
        IntegracaoTurmasWSConsumer integracaoWS = UtilContext.getBean(IntegracaoTurmasWSConsumer.class);
        return integracaoWS.consultarAgendamentosJSONDoProfessor( ctx, inicio, fim, empresa, "COLETIVAS", codigoProfessor);
    }

    @Override
    public List<AgendaTotalJSON> obterAulasDoProfessor(String ctx, Date inicio, Date fim, Integer empresa, Integer codigoProfessor) throws Exception {
        IntegracaoTurmasWSConsumer integracaoWS = UtilContext.getBean(IntegracaoTurmasWSConsumer.class);
        return integracaoWS.consultarAgendamentosJSONDoProfessor( ctx, inicio, fim, empresa, null, codigoProfessor);
    }


    public Map<Integer, List<Date>> obterAulasExcluidas(String ctx, Date inicio, Date fim) throws Exception {
        Map<Integer, List<Date>> mapaExcluidas = new HashMap<Integer, List<Date>>();
        List<AulaDiaExclusao> lista = aulaDiaExclusaoDao.obterAulasExcluidas(ctx, inicio, fim);
        for(AulaDiaExclusao exc : lista){
            if(exc.getCodigoHorarioTurma() == null){
                continue;
            }
            List<Date> datas = mapaExcluidas.get(exc.getCodigoHorarioTurma());
            if(datas == null){
                datas = new ArrayList<Date>();
                mapaExcluidas.put(exc.getCodigoHorarioTurma(), datas);
            }
            datas.add(Calendario.getDataComHoraZerada(exc.getDataAulaDia()));
        }
        return mapaExcluidas;
    }

    public Map<Integer, Map<Date, ProfessorSubstituido>> obterProfessoresSubstituidos(String ctx,
                                                                                      Connection con,
                                                                                      Date inicio, Date fim) throws Exception {
        Map<Integer, Map<Date, ProfessorSubstituido>> mapaSubs = new HashMap<Integer, Map<Date, ProfessorSubstituido>>();
        List<ProfessorSubstituido> lista = profSubDao.obterProfessoresSubstituidos(ctx, inicio, fim);
        for(ProfessorSubstituido sub : lista){
            if (mapaSubs.containsKey(sub.getCodigoHorarioTurma()) && mapaSubs.get(sub.getCodigoHorarioTurma()).containsKey(Calendario.getDataComHoraZerada(sub.getDiaAula())) ) {
                continue;
            }
            if(con != null){
                try (ResultSet resultSet = ConexaoZWServiceImpl.criarConsulta("select p.nome from colaborador c " +
                        " inner join pessoa p on p.codigo = c.pessoa " +
                        " where c.codigo = " + sub.getCodigoProfessorSubstituto(), con)) {
                    if (resultSet.next()) {
                        sub.setNomeProfessorSubstituto(resultSet.getString("nome"));
                    }
                }
            }
            Map<Date, ProfessorSubstituido> mapaDatas = mapaSubs.get(sub.getCodigoHorarioTurma());
            if(mapaDatas == null){
                mapaDatas = new HashMap<Date, ProfessorSubstituido>();
                mapaSubs.put(sub.getCodigoHorarioTurma(), mapaDatas);
            }
            mapaDatas.put(Calendario.getDataComHoraZerada(sub.getDiaAula()), sub);
        }
        return mapaSubs;
    }

    public ProfessorSubstituido obterProfessorSubstituido(String ctx, Date dataHoje, int codigoHorarioTurma) throws Exception {
        ProfessorSubstituido professorSubstituido = profSubDao.obterProfessorSubstituido(ctx, dataHoje, codigoHorarioTurma);
        return professorSubstituido;
    }

    @Override
    public List<SelectItem> obterListaProfessores(String ctx, Integer empresa, Boolean somenteAtivos) throws Exception{
        IntegracaoTurmasWSConsumer integracaoWS = UtilContext.getBean(IntegracaoTurmasWSConsumer.class);
        JSONArray professores = integracaoWS.consultarProfessores( ctx, empresa, somenteAtivos);
        List<SelectItem> itens = new ArrayList<SelectItem>();
        for (int i = 0; i < professores.length(); i++) {
            itens.add(new SelectItem(professores.getJSONObject(i).getInt("codigo"), professores.getJSONObject(i).getString("nome")));
        }
        return itens;
    }

    @Override
    public Map<Integer, String> obterMapaProfessores(String ctx, Integer empresa,
                                                     Boolean somenteAtivos, Boolean independente) throws Exception{
        Map<Integer, String> mapa = new HashMap<Integer,String>();

        if(Aplicacao.independente(ctx)){
            List<Usuario> usuarios = getUsuarioService().obterUsuariosProfessores(ctx, empresa);
            for (Usuario u : usuarios) {
                if(u.getProfessor().getNome().isEmpty()){
                    continue;
                }
                Integer codigo = u.getProfessor().getCodigo();
                mapa.put(codigo, u.getProfessor().getNome());
                if(!u.getFotoKeyApp().isEmpty()){
                    mapa.put(-codigo, u.getFotoKeyApp());
                } else if (!u.getProfessor().getUriImagem().isEmpty()) {
                    mapa.put(-codigo, u.getProfessor().getUriImagem());
                }

            }
        }else{
            IntegracaoTurmasWSConsumer integracaoWS = UtilContext.getBean(IntegracaoTurmasWSConsumer.class);
            JSONArray professores = integracaoWS.consultarProfessores( ctx, empresa, somenteAtivos);

            for (int i = 0; i < professores.length(); i++) {
                if(professores.getJSONObject(i).getString("nome").isEmpty()){
                    continue;
                }
                Integer codigo = professores.getJSONObject(i).getInt("codigo");
                mapa.put(codigo, professores.getJSONObject(i).getString("nome"));
                if(professores.getJSONObject(i).getString("fotokey").isEmpty()){
                    continue;
                }
                mapa.put(-codigo, professores.getJSONObject(i).getString("fotokey"));
            }
        }

        return mapa;
    }

    @Override
    public Boolean consultarTemAulaExtra(String ctx, Integer modalidade, Integer contrato, Integer saldo) throws Exception {
        IntegracaoTurmasWSConsumer integracaoWS = UtilContext.getBean(IntegracaoTurmasWSConsumer.class);
        String result = integracaoWS.consultarTemAulaExtra( ctx, contrato, modalidade, saldo);
        if (result.startsWith("ERRO:")) {
            throw new Exception(result.replaceFirst("ERRO:", ""));
        } else {
            return Boolean.parseBoolean(result);
        }
    }

    @Override
    public String confirmarAlunoAula(String ctx, Integer cliente, Integer horarioTurma, String dia, Integer usuario) throws Exception {
        IntegracaoTurmasWSConsumer integracaoWS = UtilContext.getBean(IntegracaoTurmasWSConsumer.class);
        return integracaoWS.confirmarAlunoAula( ctx, cliente, horarioTurma, dia, usuario);
    }

    @Override
    public String presencaAlunoAula(String ctx, Integer cliente, Integer horarioTurma, String dia, Integer codColaborador,
                                    OrigemSistemaEnum origem, Boolean confirmar) throws Exception {
        try {
            if (codColaborador == null) {
                if (sessaoService.getUsuarioAtual() != null) {
                    codColaborador = usuarioService.obterPorId(ctx, sessaoService.getUsuarioAtual().getId()).getProfessor().getCodigoColaborador();
                } else {
                    codColaborador = 0;
                }
            }
        }catch (Exception e){
            codColaborador = 0;
        }

        JSONObject dados = new JSONObject();
        dados.put("codCliente", cliente);
        dados.put("codHorarioTurma", horarioTurma);
        dados.put("diaAula", dia);
        dados.put("codColaborador", codColaborador);
        dados.put("origem", origem.getCodigo());
        dados.put("confirmar", confirmar);
        dados.put("isAulaCheia", true);

        JSONObject content = chamadaZW(ctx,
                "/prest/confirmar-presenca/presenca",
                null,
                null,
                null,
                dados);

        return content.getString("content");
    }

    private List preencherDadosAluno(final String ctx, List<AgendadoJSON> lista) {
        List<Map<String, String>> lm = new ArrayList<Map<String, String>>();
        for (AgendadoJSON a : lista) {
            if(!a.isDesmarcado() || a.isPresencaReposicao()) {
                String foto = a.getUrlFoto();
                try {
                    Usuario usuario = getUsuarioService().consultarPorMatricula(ctx, Integer.parseInt(a.getMatricula()));
                    if (usuario != null && !UteisValidacao.emptyString(usuario.getFotoKeyApp())) {
                        foto = usuario.getFotoKeyApp();
                    } else {
                        foto = Aplicacao.getUrlFoto(ctx, a.getCodigoPessoa(), false);
                    }
                } catch (Exception ignored) {
                    Uteis.logar(ignored, this.getClass());
                }
                if (UteisValidacao.emptyString(foto)) {
                    foto = Aplicacao.getUrlFoto(ctx, a.getCodigoPessoa(), false);
                }
                Map<String, String> m = new HashMap<String, String>();
                m.put("foto", foto);
                m.put("nome", a.getNome());
                m.put("matricula", a.getMatricula());
                m.put("confirmado", a.getConfirmado().toString());
                lm.add(m);
            }
        }
        return lm;
    }

    private List preencherDadosAlunoResponse(final String ctx, List<AlunoResponseTO> lista) {
        List<Map<String, String>> lm = new ArrayList<Map<String, String>>();
        for (AlunoResponseTO a : lista) {
            String foto = a.getImageUri();
            try {
                Usuario usuario = getUsuarioService().consultarPorMatricula(ctx, a.getMatriculaZW());
                if (usuario != null && !UteisValidacao.emptyString(usuario.getFotoKeyApp())) {
                    foto = usuario.getFotoKeyApp();
                }
            } catch (Exception ignored) {
                Uteis.logar(ignored, this.getClass());
            }
            Map<String, String> m = new HashMap<String, String>();
            m.put("foto", foto);
            m.put("nome", a.getNome());
            m.put("matricula", a.getMatriculaZW().toString());
            lm.add(m);
        }
        return lm;
    }

    @Override
    public List<Map<String, String>> fotosAlunosAula(String ctx, Integer empresa, Integer horarioTurma, String dia) throws Exception{
        if(Aplicacao.independente(ctx)){
            List<AlunoResponseTO> alunosHorarioDia = agendaService.alunosHorarioDia(ctx, new AulaHorario(horarioTurma), Uteis.getDate(dia, "dd/MM/yyyy"));
            return preencherDadosAlunoResponse(ctx, alunosHorarioDia);
        }else{
            IntegracaoTurmasWSConsumer integracaoWS = UtilContext.getBean(IntegracaoTurmasWSConsumer.class);
            final ResultAlunoClienteSinteticoJSON result = integracaoWS.obterAlunosDeUmaAula(
                    ctx, horarioTurma, Uteis.getDate(dia, "dd/MM/yyyy"));
            return preencherDadosAluno(ctx, result.getResultAgendado());
        }
    }

    @Override
    public List<Map<String, String>> fotosAlunosTurma(String ctx, Integer empresa, Integer horarioTurma, String dia) throws Exception {
        return fotosAlunosTurma(ctx, empresa, horarioTurma, dia, null);
    }

    public List<Map<String, String>> fotosAlunosTurma(String ctx, Integer empresa, Integer horarioTurma, String dia, HttpServletRequest request) throws Exception {
        TurmaResponseDTO turmaResponseDTO = agendaService.aulaDetalhada(ctx, empresa, horarioTurma, Calendario.getDate("dd/MM/yyyy", dia.toString()), true, request);
        List<Map<String, String>> lm = new ArrayList<Map<String, String>>();
        for (AlunoTurmaDTO a : turmaResponseDTO.getAlunos()) {
            if(a.getVinculoComAula() != null && a.getVinculoComAula().equals(AlunoVinculoAulaEnum.DESMARCADO)){
                continue;
            }
            String foto = a.getImageUri();
            String dataNascimento = "";
            String telefone = "";
            try {
                Usuario usuario = getUsuarioService().consultarPorMatricula(ctx, a.getMatriculaZW());
                if (usuario != null && usuario.getCliente() != null && usuario.getCliente().getPessoa() != null && !UteisValidacao.emptyString(usuario.getCliente().getPessoa().getFotoKey())) {
                    foto = Aplicacao.obterUrlFotoDaNuvem(usuario.getCliente().getPessoa().getFotoKey());
                } else {
                    foto = Aplicacao.obterUrlFotoDaNuvem(null);
                }
                dataNascimento = usuario.getCliente().getDataNascimento() == null ? "" : Uteis.getData(usuario.getCliente().getDataNascimento());
                telefone = usuario.getCliente().getTelefones();
            } catch (Exception ignored) {
                Uteis.logar(ignored, this.getClass());
            }

            Map<String, String> m = new HashMap<String, String>();
            m.put("foto", foto);
            m.put("nome", a.getNome());
            m.put("matricula", a.getMatriculaZW().toString());
            m.put("confirmado", a.getConfirmado().toString());
            m.put("dataNascimento", dataNascimento);
            m.put("telefone", Uteis.celulares(telefone));
            m.put("vinculoComAula",a.getVinculoComAula() != null &&
                    a.getVinculoComAula().equals(AlunoVinculoAulaEnum.AULA_EXPERIMENTAL) ? AlunoVinculoAulaEnum.AULA_EXPERIMENTAL.toString() : "");
            lm.add(m);
        }
        return lm;
    }

    @Override
    public List<AulaDiaJSON> consultarAgendamentosModalidadesAlunoApp(final String key, final Date inicio,
                                                                      final Date fim, final Integer matricula, final Integer empresa,
                                                                      final Integer contrato) throws Exception {
        try (Connection conZW = conexaoZWService.conexaoZw(key)){
            AgendaModoBDServiceImpl agendaAulasService = new AgendaModoBDServiceImpl(conZW, key);
            agendaAulasService.setMapaAulasExcluidas(obterAulasExcluidas(key, inicio, fim));
            return agendaAulasService.consultarAgendamentosModalidadesAlunoApp(key, inicio, fim, matricula, empresa, contrato);
        }
    }

    public void atualizarSpiviClientID(final String key, final Integer codigoPessoa, final Integer spiviClientID) throws Exception{
        final String url = Aplicacao.getProp(key, Aplicacao.urlZillyonWebInteg);
        IntegracaoTurmasWSConsumer integracaoWS = UtilContext.getBean(IntegracaoTurmasWSConsumer.class);
        String retornoAlteracao = integracaoWS.atualizarSpiviClientID(url, key, codigoPessoa, spiviClientID);
        if(retornoAlteracao.toUpperCase().contains("ERRO")){
            String msgApresentar = retornoAlteracao;
            try {
                JSONObject obj = new JSONObject(retornoAlteracao);
                msgApresentar = obj.getString(SuperControle.STATUS_ERRO);
            } catch (Exception ignored) {

            }
            throw new Exception(msgApresentar);
        }
    }

    @Override
    public JSONObject obterContratoVigentePorPessoaSintetico(final String key, Integer codigoPessoa, Date dataAtual) throws Exception {
        IntegracaoTurmasWSConsumer integracaoWS = UtilContext.getBean(IntegracaoTurmasWSConsumer.class);
        final String url = Aplicacao.getProp(key, Aplicacao.urlZillyonWebInteg);
        JSONObject obj = integracaoWS.obterContratoVigentePorPessoaSintetico(url, key, codigoPessoa, dataAtual);
        if(obj == null) {
            String mensagem = "ERRO";
        }
        return obj;
    }

    private JSONObject postFilaEspera(String ctx,
                                      String endpoint,
                                      FilaDeEsperaDTO payload) throws Exception {
        final String url = Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebInteg);
        CloseableHttpClient client = ExecuteRequestHttpService.createConnector();
        HttpPost httpPost = new HttpPost(url + endpoint);
        List<NameValuePair> params = new ArrayList<>();
        params.add(new BasicNameValuePair("chave", ctx));
        params.add(new BasicNameValuePair("codigoAluno", (payload.getCodigoAluno() != null ? payload.getCodigoAluno().toString():"")));
        params.add(new BasicNameValuePair("matricula", (payload.getMatricula() != null ? payload.getMatricula().toString():"")));
        params.add(new BasicNameValuePair("codigoHorarioTurma", payload.getCodigoHorarioTurma().toString()));
        params.add(new BasicNameValuePair("dia", payload.getDia()));
        if(payload.getCodigoUsuario()!=null){
            params.add(new BasicNameValuePair("codigoUsuario", payload.getCodigoUsuario().toString()));
        }
        if(payload.getOrigemSistema()!=null){
            params.add(new BasicNameValuePair("origemSistema", payload.getOrigemSistema().toString()));
        }
        httpPost.setEntity(new UrlEncodedFormEntity(params));
        CloseableHttpResponse response = client.execute(httpPost);
        ResponseHandler<String> handler = new BasicResponseHandler();
        String body = handler.handleResponse(response);
        client.close();
        return new JSONObject(body);
    }


    @Override
    public void excluirAlunoAulaCheiaOutraUnidade(String ctx, Integer matricula, Integer idHorarioTurma,
                                                  Date data, String chaveOrigem) throws ServiceException {
        try {
            JSONObject dados = new JSONObject();
            dados.put("chaveAluno", chaveOrigem);
            dados.put("matricula", matricula);
            dados.put("dia", data.getTime());
            dados.put("codigoHorarioTurma", idHorarioTurma);

            JSONObject content = chamadaZW(ctx,
                    "/prest/aulacheia/remover-autorizado-aula",
                    null,
                    null,
                    null,
                    dados);
            if(!content.getString("content").equals("sucesso")){
                throw new Exception("Erro ao remover aluno da aula");
            }
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public List<String> aulasAutorizado(String ctx, Integer matricula, Date data, String chaveOrigem) throws ServiceException {
        try {
            JSONObject dados = new JSONObject();
            dados.put("chaveAluno", chaveOrigem);
            dados.put("matricula", matricula);
            dados.put("dia", data.getTime());

            JSONObject content = chamadaZW(ctx,
                    "/prest/aulacheia/autorizado-aulas",
                    null,
                    null,
                    null,
                    dados);
            List<String> aulasAutorizado = new ArrayList<>();
            JSONArray jsonArray = content.getJSONArray("content");
            for(int i = 0; i < jsonArray.length(); i++){
                aulasAutorizado.add(jsonArray.getString(i));
            }
            return aulasAutorizado;
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public String consultarSaldoAlunoReporEMarcar(String ctx, Integer matricula, Integer contrato) throws Exception {
        try (Connection conZW = conexaoZWService.conexaoZw(ctx)){
            AgendaModoBDServiceImpl agendaAulasService = new AgendaModoBDServiceImpl(conZW, ctx);
            agendaAulasService.validaSituacaoContrato(matricula, contrato);
        }

        JSONObject dados = new JSONObject();
        dados.put("matriculaAluno", matricula);
        dados.put("contrato", contrato);

        JSONObject content = chamadaZW(ctx,
                "/prest/aulacheia/consultar-saldo-aluno-repor-e-marcar-app",
                null,
                null,
                null,
                dados);
        return content.getString("content");
    }

    @Override
    public List<AulaConfirmadaVO> consultarAulasConfirmadas(String ctx, Integer matricula, String dataInicial, String dataFinal, PaginadorDTO paginadorDTO) throws Exception {
        List<AulaConfirmadaVO> aulaConfirmadaVOS = new ArrayList<>();

        int maxResults = MAXIMO_AULAS_CONFIRMADAS_CONSULTAR;
        int indiceInicial = 0;
        if (paginadorDTO != null) {
            maxResults = paginadorDTO.getSize() == null ? MAXIMO_AULAS_CONFIRMADAS_CONSULTAR : paginadorDTO.getSize().intValue();
            indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
        }

        if (paginadorDTO != null && paginadorDTO.getSort() == null) {
            paginadorDTO.setSort("diaaula DESC");
        }

        StringBuilder sql = new StringBuilder();
        sql.append("SELECT diaaula, t.descricao, pessoaColaborador.nome FROM aulaconfirmada ac ");
        sql.append(" JOIN cliente c ON ac.cliente = c.codigo ");
        sql.append(" JOIN horarioturma h on ac.horario = h.codigo ");
        sql.append(" JOIN colaborador c2 on ac.colaborador = c2.codigo ");
        sql.append(" JOIN pessoa pessoaColaborador on c2.pessoa = pessoaColaborador.codigo ");
        sql.append(" JOIN turma t on h.turma = t.codigo ");

        sql.append(" where c.codigomatricula = " + matricula);

        Date dataInicio = UteisValidacao.emptyString(dataInicial) ? null : Uteis.getDate(dataInicial, "dd/MM/yyyy");
        Date dataFim = UteisValidacao.emptyString(dataFinal) ? null : Uteis.getDate(dataFinal, "dd/MM/yyyy");

        sql.append(dataInicio != null ? " and diaaula >= '" + Uteis.getDataJDBC(dataInicio) + "' " : "");
        sql.append(dataFim != null ? " and diaaula <= '" + Uteis.getDataJDBC(dataFim) + "' " : "");

        sql.append(" UNION ALL ");
        sql.append(" SELECT datareposicao AS diaaula, t.descricao, pessoaColaborador.nome ");
        sql.append(" FROM reposicao r ");
        sql.append(" JOIN cliente c ON r.cliente = c.codigo ");
        sql.append(" JOIN horarioturma h on r.horarioturma = h.codigo ");
        sql.append(" JOIN colaborador c2 on h.professor = c2.codigo ");
        sql.append(" JOIN turma t on h.turma = t.codigo ");
        sql.append(" JOIN pessoa pessoaColaborador on c2.pessoa = pessoaColaborador.codigo ");
        sql.append(" WHERE c.codigomatricula = " + matricula);
        sql.append(dataInicio != null ? " AND datareposicao >= '" + Uteis.getDataJDBC(dataInicio) + "' " : "");
        sql.append(dataFim != null ? " AND datareposicao <= '" + Uteis.getDataJDBC(dataFim) + "' " : "");
        sql.append(" AND datapresenca is not null");

        try (Connection conZW = conexaoZWService.conexaoZw(ctx)){

            if (paginadorDTO != null) {
                sql.append(" order by " + paginadorDTO.getSort());
                sql.append(" limit " + maxResults);
                sql.append(" offset " + indiceInicial);
                StringBuilder sqlCount = new StringBuilder();
                sqlCount.append("SELECT count(*) FROM (" + sql + ") AS subquery");
                ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sqlCount.toString(), conZW);
                paginadorDTO.setQuantidadeTotalElementos(rs.next() ? rs.getLong("count") : 0L);
            }

            ResultSet resultSet = ConexaoZWServiceImpl.criarConsulta(sql.toString(), conZW);
            while(resultSet.next()){
                AulaConfirmadaVO aulaConfirmadaVO = new AulaConfirmadaVO();
                aulaConfirmadaVO.setDataAula(Uteis.getDataAplicandoFormatacao(resultSet.getDate("diaaula"), "dd/MM/yyyy"));
                aulaConfirmadaVO.setNomeAula(resultSet.getString("descricao"));
                aulaConfirmadaVO.setNomeProfessor(resultSet.getString("nome"));
                aulaConfirmadaVOS.add(aulaConfirmadaVO);
            }

            return aulaConfirmadaVOS;
        } catch (Exception e) {
            throw e;
        }
    }
    @Override
    public List<TurmaVideoDTO> obterListaTurmaVideo(String ctx, Integer codigo) throws Exception {

        StringBuilder sql = new StringBuilder();
        sql.append("select codigo, turma_codigo , linkvideo , professor ");
        sql.append("from turmavideo ");
        sql.append("where turma_codigo = ").append(codigo);
        try (Connection conZW = conexaoZWService.conexaoZw(ctx)){

            ResultSet resultSet = ConexaoZWServiceImpl.criarConsulta(sql.toString(), conZW);
            List<TurmaVideoDTO> linkVideos = new ArrayList<>();
            while (resultSet.next()) {
                TurmaVideoDTO turmaVideoDTO = new TurmaVideoDTO();
                turmaVideoDTO.setId(resultSet.getInt("codigo"));
                turmaVideoDTO.setLinkVideo(resultSet.getString("linkvideo"));
                turmaVideoDTO.setProfessor(resultSet.getBoolean("professor"));
                linkVideos.add(turmaVideoDTO);
            }
            return linkVideos;
        }
    }

    public Map<Integer, EdicaoAulaTemporaria> obterEdicoesTemporarias(String ctx, Date inicio, Date fim) throws Exception {
        Map<Integer, EdicaoAulaTemporaria> mapaEds = new HashMap<>();
        List<EdicaoAulaTemporaria> edicoes = aulaService.consultarEdicoesAulaTemporariaPeriodo(ctx, inicio, fim);
        for (EdicaoAulaTemporaria ed : edicoes) {
            mapaEds.put(ed.getHorarioTurma(), ed);
        }
        return mapaEds;
    }
    @Override
    public String removerDaFilaDeEsperaV2(String ctx, Integer codigoHorarioTurma, String dia, Integer codigoAluno) {
        String sql = "DELETE FROM filaesperaturma WHERE cliente = ? AND horarioturma = ? AND DATE(dataregistro) = ?";
        String sqlCliente = "SELECT * FROM cliente WHERE codigo = ?";
        try (Connection conZw = conexaoZWService.conexaoZw(ctx);
             PreparedStatement stmtCliente = conZw.prepareStatement(sqlCliente)) {
            stmtCliente.setInt(1, codigoAluno);
            ResultSet rs = stmtCliente.executeQuery();
            if (!rs.next()) {
                return "Aluno não encontrado!";
            }
        } catch (Exception e) {
            return "Erro ao buscar aluno!";
        }

        try (Connection conZW = conexaoZWService.conexaoZw(ctx);
             PreparedStatement stmt = conZW.prepareStatement(sql)) {
            SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy");
            Date data = sdf.parse(dia);

            stmt.setInt(1, codigoAluno);
            stmt.setInt(2, codigoHorarioTurma);
            stmt.setDate(3, new java.sql.Date(data.getTime()));

            int linhasRemovidas = stmt.executeUpdate();

            if (linhasRemovidas > 0) {
                return "Aluno removido da fila de espera com sucesso!";
            } else {
                return "Nenhum aluno encontrado na fila de espera para o horário e data especificados.";
            }
        } catch (Exception e) {
            return "Erro ao remover aluno da fila de espera!";
        }
    }

    @Override
    public void deletarHorarioEquipamentoAluno(String ctx, Integer empresa, Integer codigoHorarioTurma, Integer codigoCliente, Date date) throws ServiceException {
        agendaService.deletarHorarioEquipamentoAluno(ctx, empresa, codigoHorarioTurma, codigoCliente, date);
    }

    @Override
    public SaldoAulaColetivaVO consultarSaldoAlunoAulasColetivas(String ctx, Integer matricula) {
        SaldoAulaColetivaVO dados = new SaldoAulaColetivaVO();
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT COUNT(*) AS total FROM alunohorarioturmadesmarcado a ");
        sql.append("INNER JOIN cliente c ON a.cliente = c.codigo ");
        sql.append("INNER JOIN contrato con ON con.codigo = a.contrato ");
        sql.append("INNER JOIN plano p ON p.codigo = con.plano ");
        sql.append("WHERE a.reposicao AND c.codigomatricula = ").append(matricula);
        sql.append(" AND aulareposta IS NULL ");
        try (Connection conZW = conexaoZWService.conexaoZw(ctx);
             ResultSet contDisponiveis = ConexaoZWServiceImpl.criarConsulta(sql.toString() +
                     " AND datalimitereposicao >= current_date " +
                     " AND (con.vigenciaateajustada >= current_date OR a.manternarenovacao)", conZW)) {
            if (contDisponiveis.next()) {
                dados.setDisponiveis(contDisponiveis.getInt("total"));
            }
            ResultSet contExpiradas = ConexaoZWServiceImpl.criarConsulta(sql.toString() +
                    " AND (datalimitereposicao  < current_date " +
                    " OR (con.vigenciaateajustada < current_date AND NOT a.manternarenovacao))", conZW);
            if (contExpiradas.next()) {
                dados.setExpiradas(contExpiradas.getInt("total"));
            }
            ResultSet contUtilizadas = ConexaoZWServiceImpl.criarConsulta("SELECT COUNT(*) AS total FROM alunohorarioturmadesmarcado a " +
                    "INNER JOIN cliente c ON a.cliente = c.codigo " +
                    "INNER JOIN contrato con ON a.contrato = con.codigo " +
                    "WHERE a.reposicao AND c.codigomatricula = "+ matricula + " " +
                    "AND aulareposta IS NOT NULL", conZW);
            if (contUtilizadas.next()) {
                dados.setUtilizadas(contUtilizadas.getInt("total"));
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return dados;
    }
}

