package br.com.pacto.service.impl.notificacao.excecao;

import br.com.pacto.service.exception.ExcecaoSistema;

/**
 * Created by ulisses on 04/08/2018.
 */
public enum FichaExcecoes implements ExcecaoSistema {

    ERRO_ALTERAR_FICHA("erro_alterar_ficha", "Ocorreu um erro ao alterar a ficha"),
    FICHA_PREDEFINIDA_NAO_ENCONTRADA("ficha_predefinida_nao_encontrada", "Ficha pré-definida informada não foi encontrada"),
    FICHA_PREDEFINIDA_INVALIDA("ficha_predefinida_invalida", "Ficha pré-definida informada é inválida"),
    FICHA_NAO_ENCONTRADA("ficha_nao_encontrada", "Ficha informada não foi encontrada"),
    PROGRAMA_NAO_ENCONTRADO("programa_nao_encontrado", "Programa informado não foi encontrado"),
    CATEGORIA_FICHA_NAO_ENCONTRADA("categoria_ficha_nao_encontrada", "Categoria Ficha informada não foi encontrada"),
    ATIVIDADE_FICHA_NAO_ENCONTRADA("atividade_ficha_nao_encontrada", "Atividade Ficha informada não foi encontrada"),
    ERRO_BUSCAR_FICHA("erro_buscar_ficha", "Ocorreu um erro ao pesquisar a Ficha informada"),
    ERRO_MONTAR_FICHA("erro_montar_ficha", "Ocorreu um erro ao montar a Ficha informada"),
    ERRO_INCLUIR_FICHA("erro_incluir_ficha", "Ocorreu um erro ao incluir a ficha informada"),
    ERRO_BUSCAR_FICHAS("erro_buscar_fichas", "Ocorreu um erro ao pesquisar as fichas"),
    ERRO_BUSCAR_PERFIL("erro_buscar_perfil", "Ocorreu um erro ao pesquisar o perfil."),
    ERRO_BUSCAR_MSG_RECOMENDADAS("erro_buscar_msg_recomendadas", "Ocorreu um erro ao pesquisar as mensagens recomendadas"),
    ERRO_EXCLUIR_FICHA("erro_excluir_ficha", "Ocorreu um erro ao excluir a ficha informada"),
    ERRO_REORDENAR_ATIVIDADES("erro_reordenar_atividades", "Ocorreu um erro ao reordenar as atividades da ficha"),
    ERRO_VALIDACAO_DADOS_FICHA("erro_validacao_dados_ficha", "Erro de validação de dados."),
    VALIDACAO_NOME_FICHA("validacao_nome_ficha", "Nome da ficha não informado"),
    ERRO_BUSCAR_CATEGORIA_FICHA("erro_buscar_categoria_ficha", "Erro ao buscar categoria de fichas"),
    ERRO_TORNAR_FICHA_PRE_DEFINIDA("erro_tornar_ficha_pre_definida", "Erro ao tornar ficha pré-definida"),
    ERRO_REGISTRO_DUPLICADO("erro_registro_duplicado", "ficha_duplicada"),
    ERRO_ATUALIZAR_SET_ATIVIDADES("erro_atualizar_set_atividades", "Erro ao atualizar set das atividades da ficha"),
    VALIDACAO_FICHA_PREDEFINIDA_JA_EXISTE("validacao_ficha_predefinida_ja_existe", "Ficha predefinida já existente com este nome."),
    ;

    private String chave;
    private String descricao;

    FichaExcecoes(String chave, String descricao) {
        this.chave = chave;
        this.descricao = descricao;
    }

    @Override
    public String getChaveExcecao() {
        return chave;
    }
    @Override
    public String getDescricaoExcecao() {
        return descricao;
    }

}
