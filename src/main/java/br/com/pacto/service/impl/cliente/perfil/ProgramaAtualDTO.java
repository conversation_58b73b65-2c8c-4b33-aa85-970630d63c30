package br.com.pacto.service.impl.cliente.perfil;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Programa Atual")
public class ProgramaAtualDTO {

    @ApiModelProperty(value = "ID do Programa Atual", example = "1")
    private Integer id;
    @ApiModelProperty(value = "Nome do Programa Atual", example = "Programa 1")
    private String nome;
    @ApiModelProperty(value = "Fichas", example = "1")
    private Integer fichas;
    @ApiModelProperty(value = "Previstas", example = "1")
    private Integer previstas;
    @ApiModelProperty(value = "Realizadas", example = "1")
    private Integer realizadas;
    @ApiModelProperty(value = "Percentual", example = "100.0")
    private Double percentual;

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Integer getFichas() {
        return fichas;
    }

    public void setFichas(Integer fichas) {
        this.fichas = fichas;
    }

    public Integer getPrevistas() {
        return previstas;
    }

    public void setPrevistas(Integer previstas) {
        this.previstas = previstas;
    }

    public Integer getRealizadas() {
        return realizadas;
    }

    public void setRealizadas(Integer realizadas) {
        this.realizadas = realizadas;
    }

    public Double getPercentual() {
        return percentual;
    }

    public void setPercentual(Double percentual) {
        this.percentual = percentual;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
}
