package br.com.pacto.service.impl.notificacao.excecao;

import br.com.pacto.service.exception.ExcecaoSistema;

public enum UsuarioExcecoes implements ExcecaoSistema {

    ERRO_BUSCAR_USUARIOS("erro_buscar_usuarios", "Erro ao buscar os usuários!"),
    ERRO_BUSCAR_USUARIO("erro_buscar_usuario", "Erro ao buscar o usuário"),
    ERRO_BUSCAR_USUARIO_POR_PROFESSOR("erro_buscar_usuario_por_professor", "Erro ao buscar o usuário por professor"),
    ERRO_USUARIO_NAO_ENCONTRADO("erro_usuario_nao_encontrado", "Erro usuário informado não encontrado"),
    ERRO_VERIFICAR_USUARIO_DUPLICADO("erro_verificar_usuario_duplicado", "Erro ao verificar usuário duplicado"),
    USUARIO_DUPLICADO("usuario_duplicado", "usuario_duplicado"),
    ERRO_SALVAR_USUARIO("erro_salvar_usuario", "Erro ao salvar usuário"),
    ERRO_ACAO_PROIBIDA("erro_acao_proibida", "Ação não pode ser requirida quando se tem integração com o ZW"),

    ERRO_USERNAME_NULL("erro_campo_obrigatorio", "Erro nome de usuário é obrigatório"),
    ERRO_TIPO_USUARIO_NULL("erro_campo_obrigatorio", "Erro tipo de usuário é obrigatório"),
    ERRO_PERFIL_USUARIO_NULL("erro_campo_obrigatorio", "Erro perfil do usuário é obrigatório"),
    ERRO_OBTER_EMAIL("erro_obter_email", "E-mail não encontrado"),
    ERRO_EMAIL_INVALIDO("erro_email_invalido", "O e-mail do aluno informado é invalido"),
    ERRO_EMAIL_NULL("erro_campo_obrigatorio", "E-mail não informado"),
    ERRO_EMAIL_JA_EXISTENTE("erro_email_ja_existente", "E-mail já pertence a outro usuário!"),
    ERRO_SOLICITAR_ATENDIMENTO("ERRO_SOLICITAR_ATENDIMENTO", "Não foi possivel solicitar atendimento");

    private String chave;
    private String descricao;

    UsuarioExcecoes(String chave, String descricao) {
        this.chave = chave;
        this.descricao = descricao;
    }

    @Override
    public String getChaveExcecao() {
        return chave;
    }
    @Override
    public String getDescricaoExcecao() {
        return descricao;
    }
}
