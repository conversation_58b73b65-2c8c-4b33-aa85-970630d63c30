package br.com.pacto.service.impl.cliente.perfil;

public class HorariosQueTreinouProgramaAtual {

    private Integer manha = 0;
    private Integer tarde = 0;
    private Integer noite = 0;
    private Integer qtdDiasQueOProgTaVigente;

    public HorariosQueTreinouProgramaAtual() { }

    public HorariosQueTreinouProgramaAtual(Integer manha, Integer tarde, Integer noite, Integer qtdDiasQueOProgTaVigente) {
        this.manha = manha;
        this.tarde = tarde;
        this.noite = noite;
        this.qtdDiasQueOProgTaVigente = qtdDiasQueOProgTaVigente;
    }

    public Integer getManha() {
        return manha;
    }

    public void setManha(Integer manha) {
        this.manha = manha;
    }

    public Integer getTarde() {
        return tarde;
    }

    public void setTarde(Integer tarde) {
        this.tarde = tarde;
    }

    public Integer getNoite() {
        return noite;
    }

    public void setNoite(Integer noite) {
        this.noite = noite;
    }

    public Integer getQtdDiasQueOProgTaVigente() {
        return qtdDiasQueOProgTaVigente;
    }

    public void setQtdDiasQueOProgTaVigente(Integer qtdDiasQueOProgTaVigente) {
        this.qtdDiasQueOProgTaVigente = qtdDiasQueOProgTaVigente;
    }

}
