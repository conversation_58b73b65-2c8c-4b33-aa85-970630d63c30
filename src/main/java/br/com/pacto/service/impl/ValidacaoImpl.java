package br.com.pacto.service.impl;

import br.com.pacto.service.intf.Validacao;
import java.util.List;
import org.springframework.stereotype.Service;

@Service
public class ValidacaoImpl implements Validacao {

    @Override
    public boolean isValidTime(String txtHora) {

        String hora = txtHora.substring(0, 2);
        String minuto = txtHora.substring(3, 5);

        // Verifica se é número
        String strValidos = "0123456789:";
        for (int i = 0; i == txtHora.length(); i++) {
            if (strValidos.indexOf(txtHora.charAt(i)) == -1) {
                return false;
            }
        }

        // Verifica se é uma hora válida
        if ((Integer.parseInt(hora) > 23) || (Integer.parseInt(hora) < 0)) {
            return false;
        }
        if ((Integer.parseInt(minuto) > 59) || (Integer.parseInt(minuto) < 0)) {
            return false;
        }
        return true;
    }

    @Override
    public boolean isNull(Object object) {
        if (object == null) {
            return true;
        } else {
            return false;
        }
    }

    @Override
    public boolean emptyString(String str) {
        return str == null || str.isEmpty();
    }

    @Override
    public  boolean emptyNumber(Number n){
        if (n == null) {
            return true;
        }
        if (n.equals(0)) {
            return true;
        }
        if (n.equals(0.0)) {
            return true;
        }
        return false;
    }

    @Override
    public boolean isNotNull(Object object) {
        if (isNull(object)) {
            return false;
        } else {
            return true;
        }
    }

    @Override
    public boolean isEqualValue(Object object1, Object object2) {
        if (object1.toString().equals(object2.toString())) {
            return true;
        } else {
            return false;
        }
    }

    @Override
    public boolean isNotEqualValue(Object object1, Object object2) {
        if (isEqualValue(object1, object2)) {
            return false;
        } else {
            return true;
        }
    }

    @Override
    public boolean isEmpty(Object object) {
        if (object.toString().trim().equals("")) {
            return true;
        } else {
            return false;
        }
    }

    @Override
    public boolean isEmpty(List list) {
        if (list == null || list.isEmpty()) {
            return true;
        } else {
            return false;
        }
    }

    @Override
    public boolean isValorIgual(Object obj1, Object obj2) {
        boolean resultado = false;

        if ((obj1 != null) && (obj2 != null)) {
            resultado = (obj1.equals(obj2));
        } else {
            resultado = ((obj1 == null) && (obj2 == null));
        }
        return resultado;
    }

    @Override
    public boolean isNotEmpty(Object object) {
        if (isEmpty(object)) {
            return false;
        } else {
            return true;
        }
    }
}
