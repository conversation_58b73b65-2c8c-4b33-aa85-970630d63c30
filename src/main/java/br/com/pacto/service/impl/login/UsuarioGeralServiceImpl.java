package br.com.pacto.service.impl.login;

import br.com.pacto.base.util.ExecuteRequestHttpService;
import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.bean.usuarioEmail.UsuarioEmail;
import br.com.pacto.controller.json.base.SuperControle;
import br.com.pacto.dao.intf.usuarioEmail.UsuarioEmailDao;
import br.com.pacto.objeto.JSONMapper;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.security.dto.AlterarEmailDTO;
import br.com.pacto.security.dto.AlterarSenhaDTO;
import br.com.pacto.security.dto.AlterarTelefoneDTO;
import br.com.pacto.security.dto.AlterarUsuarioGeralDTO;
import br.com.pacto.security.dto.DesvincularUsuarioDTO;
import br.com.pacto.security.dto.UsuarioSimplesDTO;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.discovery.ClientDiscoveryDataDTO;
import br.com.pacto.service.discovery.DiscoveryMsService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.usuario.UsuarioServiceImpl;
import br.com.pacto.service.intf.empresa.EmpresaService;
import br.com.pacto.service.intf.login.UsuarioGeralService;
import br.com.pacto.service.intf.usuario.UsuarioService;
import br.com.pacto.service.intf.usuarioEmail.UsuarioEmailService;
import br.com.pacto.service.login.*;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.enumeradores.LocaleEnum;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.*;

@Service
@Qualifier(value = "usuarioGeralService")
public class UsuarioGeralServiceImpl implements UsuarioGeralService {

    private static String ENDPOINT_LOGIN_USUARIO_NOVO = "/prest/login/v2/usuario/novo";
    private static String ENDPOINT_LOGIN_USUARIO_ATUALIZAR = "/prest/login/v2/usuario/atualizar";

    private static String ENDPOINT_LOGIN_NOVA_SENHA = "/prest/login/v2/senha/nova";
    private static String ENDPOINT_LOGIN_DESVINCULAR_USUARIO = "/prest/login/v2/usuario/desvincular";

    private static String ENDPOINT_LOGIN_NOVO_EMAIL = "/prest/login/v2/email/novo";
    private static String ENDPOINT_LOGIN_PROCESSAR_TOKEN = "/prest/login/v2/token";

    @Autowired
    private UsuarioService usuarioService;
    @Autowired
    private UsuarioEmailService usuarioEmailService;
    @Autowired
    private SessaoService sessaoService;
    @Autowired
    private EmpresaService empresaService;
    public void alterarSenha(String ctx, AlterarSenhaDTO dto) throws Exception {
        Usuario usuario = obterUsuario(ctx, dto.getUsuario_tw(), dto.getUsuario_zw(), dto.getUsername());
        String senhaCripto = dto.getSenha_cripto();
        if (UteisValidacao.emptyString(senhaCripto)) {
            if (UteisValidacao.emptyString(dto.getSenha())) {
                throw new ServiceException("Senha não informada");
            }
            senhaCripto = Uteis.encriptar(dto.getSenha().toUpperCase());
        }
        usuario.setSenha(senhaCripto);
        usuarioService.alterar(ctx, usuario);
    }

    public void alterarEmail(String ctx, AlterarEmailDTO dto) throws Exception {
        Usuario usuario = obterUsuario(ctx, dto.getUsuario_tw(), dto.getUsuario_zw(), dto.getUsername());

        usuario.setEmail(dto.getEmail());
        usuarioService.alterar(ctx, usuario);

        if (usuario.getUsuarioEmail() != null &&
                !UteisValidacao.emptyNumber(usuario.getUsuarioEmail().getCodigo())) {
            UsuarioEmail usuarioEmail = usuarioEmailService.obterPorId(ctx, usuario.getUsuarioEmail().getCodigo(), usuario.getTipo());
            usuarioEmail.setEmail(dto.getEmail());
            usuarioEmail.setVerificado(true);
            usuarioEmailService.alterar(ctx, usuarioEmail);
        }
    }

    public void alterarTelefone(String ctx, AlterarTelefoneDTO dto) throws Exception {
        Usuario usuario = obterUsuario(ctx, dto.getUsuario_tw(), dto.getUsuario_zw(), dto.getUsername());
        //todo terminar
    }

    public void alterarUsuarioGeral(String ctx, AlterarUsuarioGeralDTO dto) throws Exception {
        Usuario usuario = obterUsuario(ctx, dto.getUsuario_tw(), dto.getUsuario_zw(), dto.getUsername());
        usuario.setUsuarioGeral(dto.getUsuarioGeral());
        usuarioService.alterar(ctx, usuario);
    }

    private void adicionarUsuarioNovoLogin(final String ctx, final Integer codigo, boolean exception, HttpServletRequest request) throws Exception {
        try {
            if (SuperControle.independente(ctx)) {
                Usuario usuario = usuarioService.obterPorId(ctx, codigo);
                if (usuario == null) {
                    throw new ServiceException("Usuario não encontrado com o codigo " + codigo);
                }
                if (usuario.getProfessor() != null) {
                    ClientDiscoveryDataDTO clientDiscoveryDataDTO = DiscoveryMsService.urlsChave(ctx);
                    Empresa empresa = empresaService.obterEmpresaTreinoIndependente(ctx);
                    UsuarioGeralDTO usuarioGeralDTO = obterUsuarioGeralDTO(ctx, usuario, empresa, sessaoService.getUsuarioAtual(), Uteis.getIp(request));
                    usuarioGeralDTO = LoginMsService.saveUsuario(clientDiscoveryDataDTO, usuarioGeralDTO);
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logar(ex, UsuarioGeralServiceImpl.class);
            if (exception) {
                throw ex;
            }
        }
    }

    private UsuarioGeralPendenteDTO toUsuarioGeralPendenteDTO(String chave, Usuario usuario, String email, Empresa empresa, boolean definirSenha,
                                                              UsuarioSimplesDTO usuarioSimplesDTO, String ipCliente) throws Exception {
        UsuarioGeralPendenteDTO usuarioGeralPendenteDTO = new UsuarioGeralPendenteDTO();
        usuarioGeralPendenteDTO.setChave(chave);
        //caso marcado será enviado um email que ao clicar ele irá validar o email e após ira definir a senha
        usuarioGeralPendenteDTO.setDefinirSenha(definirSenha);
        usuarioGeralPendenteDTO.setEmail(email);
        usuarioGeralPendenteDTO.setUsuarioGeral(obterUsuarioGeralDTO(chave, usuario, empresa, usuarioSimplesDTO, ipCliente));
        return usuarioGeralPendenteDTO;
    }

    private UsuarioGeralDTO obterUsuarioGeralDTO(String chave, Usuario usuario, Empresa empresa,
                                                 UsuarioSimplesDTO usuarioSimplesDTO, String ipCliente) throws Exception {
        UsuarioGeralDTO usuarioGeralDTO = new UsuarioGeralDTO();
        usuarioGeralDTO.setId(UteisValidacao.emptyString(usuario.getUsuarioGeral()) ? null : usuario.getUsuarioGeral());
        usuarioGeralDTO.setNome(usuario.getNomeApresentar());
        usuarioGeralDTO.setEmail(usuario.getEmail());
        try {
            usuarioGeralDTO.setFotokey(usuario.getFotoKeyApp());
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        try {
            LocaleEnum localeEnum = LocaleEnum.obterLocale(usuario.getLocale());
            usuarioGeralDTO.setIdioma(localeEnum.getLocalize());
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        usuarioGeralDTO.setSenha(usuario.getSenha());

        TokenSolicitacaoDTO tokenSolicitacaoDTO = new TokenSolicitacaoDTO(usuario, chave, empresa.getCodigo(), usuarioSimplesDTO, ipCliente);
        tokenSolicitacaoDTO.setIp(ipCliente);
        usuarioGeralDTO.setSolicitacao(tokenSolicitacaoDTO);

        DadosAcessoDTO dadosAcessoDTO = new DadosAcessoDTO();
        dadosAcessoDTO.setChave(chave);
        dadosAcessoDTO.setCodigoUsuarioTW(usuario.getCodigo());
        dadosAcessoDTO.setCodigoEmpresa(empresa.getCodigo());
        if (usuario.getProfessor() != null) {
            dadosAcessoDTO.setTipoUsuario("CO"); //colaborador
        } else if (usuario.getCliente() != null) {
            dadosAcessoDTO.setTipoUsuario("CL"); //cliente
        } else {
            throw new Exception("Tipo usuário inválido");
        }

        //idioma
        if (!UteisValidacao.emptyString(usuario.getLocale())) {
            try {
                LocaleEnum localeEnum = LocaleEnum.obterLocale(usuario.getLocale());
                dadosAcessoDTO.setIdioma(localeEnum.getLocalize());
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        } else {
            try {
                dadosAcessoDTO.setIdioma(LocaleEnum.PORTUGUES.getLocalize());
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }

        usuarioGeralDTO.setDadosAcesso(new ArrayList<>());
        usuarioGeralDTO.getDadosAcesso().add(dadosAcessoDTO);
        return usuarioGeralDTO;
    }

    public Usuario obterUsuario(String ctx, Integer codigoUsuarioTW,
                                 Integer codigoUsuarioZW, String username) throws Exception {
        Usuario obj = null;
        if (!UteisValidacao.emptyNumber(codigoUsuarioZW)) {
            obj = usuarioService.obterPorId(ctx, codigoUsuarioZW);
        } else  if (!UteisValidacao.emptyNumber(codigoUsuarioTW)) {
            obj = usuarioService.obterPorId(ctx, codigoUsuarioTW);
        }
        if (obj == null && !UteisValidacao.emptyString(username)) {
            obj = usuarioService.consultarPorUserName(ctx, username);
            if (obj == null) {
                obj = usuarioService.consultarPorUserNameSimples(ctx, username);
            }
        }

        if (obj == null) {
            throw new Exception("Usuário não encontrado");
        }
        return obj;
    }

    @Override
    public TokenDTO solicitarTrocaEmail(String chave, Integer idUsuario, boolean viaCodigoEmail, boolean enviarLinkEmail,
                                        UsuarioEmail usuarioEmailNovo, Integer empresaSolicitante,
                                        String ipCliente) throws Exception {
        try {
            if (SuperControle.independente(chave)) {
                if (!UteisValidacao.validaEmail(usuarioEmailNovo.getEmail())) {
                    throw new Exception("E-mail inválido");
                }

                final ClientDiscoveryDataDTO discoveryDataDTO = DiscoveryMsService.urlsChave(chave);

                Usuario usuario = usuarioService.obterPorId(chave, idUsuario);

                TokenSolicitacaoDTO dto = new TokenSolicitacaoDTO(usuario, chave, empresaSolicitante, sessaoService.getUsuarioAtual(), ipCliente);
                dto.setEmail(usuarioEmailNovo.getEmail());
                dto.setCodigoViaEmail(viaCodigoEmail);

                String urlAtivar = obterURLLoginBack(discoveryDataDTO) + ENDPOINT_LOGIN_NOVO_EMAIL;

                Map<String, String> headers = new HashMap<>();
                headers.put("Content-Type", "application/json");
                String response = ExecuteRequestHttpService.post(urlAtivar, new JSONObject(dto).toString(), headers);
                if (!new JSONObject(response).has("content")) {
                    throw new Exception(response);
                }
                TokenDTO tokenDTO = new TokenDTO();
                tokenDTO.setToken(new JSONObject(response).getString("content"));
                return tokenDTO;
            }
            throw new ServiceException("Ação não permitida");
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    @Override
    public void validarToken(String chave, Integer idUsuario, Integer empresaSolicitante, TokenDTO tokenDTO,
                             UsuarioSimplesDTO usuarioResponsavelVO, String ipCliente, String operacao) throws Exception {
        try {
            if (SuperControle.independente(chave)) {
                final ClientDiscoveryDataDTO discoveryDataDTO = DiscoveryMsService.urlsChave(chave);
                Usuario usuario = usuarioService.obterPorId(chave, idUsuario);
                tokenDTO.setIp(ipCliente);
                tokenDTO.setUsuarioGeral(usuario.getUsuarioGeral());
                TokenSolicitacaoDTO dto = new TokenSolicitacaoDTO(usuario, chave, empresaSolicitante, usuarioResponsavelVO, ipCliente);
                tokenDTO.setOrigem(new JSONObject(dto).toString());

                String urlAtivar = obterURLLoginBack(discoveryDataDTO) + ENDPOINT_LOGIN_PROCESSAR_TOKEN;

                Map<String, String> headers = new HashMap<>();
                headers.put("Content-Type", "application/json");
                try {
                    String response = ExecuteRequestHttpService.post(urlAtivar, new JSONObject(tokenDTO).toString(), headers);
                    if (!new JSONObject(response).has("content")) {
                        throw new Exception(response);
                    }
                } catch (IOException ioe) {
                    throw new ServiceException(new JSONObject(ioe.getMessage()).getJSONObject("meta").getString("message"));
                }
//            usuarioDAO.incluirHistoricoSincronizacao(usuario, true, respostaHttpDTO.getResponse(), operacao);
            } else {
                throw new ServiceException("Ação não permitida");
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new ServiceException(ex);
        }
    }

    @Override
    public String enviarEmailNovoUsuario(String ctx, Integer codigoUsuario, boolean definirSenha, boolean enviarCodigo,
                                         Empresa empresa, UsuarioSimplesDTO usuarioSimplesDTO, String ipCliente) throws Exception {
        try {
            Usuario usuario = obterUsuario(ctx, codigoUsuario, null, null);
            if (!UteisValidacao.emptyString(usuario.getUsuarioGeral())) {
                throw new Exception("Usuário já tem um id geral");
            }

            UsuarioGeralPendenteDTO usuarioGeralPendenteDTO = new UsuarioGeralPendenteDTO();
            usuarioGeralPendenteDTO.setChave(ctx);
            //caso marcado será enviado um email que ao clicar ele irá validar o email e após ira definir a senha
            usuarioGeralPendenteDTO.setDefinirSenha(definirSenha);
            UsuarioEmail usuarioEmail = obterEmailUsuario(ctx, usuario);
            if (usuarioEmail == null) {
                usuarioEmail = new UsuarioEmail();
            }
            if (usuarioEmail.getCodigo() == null) {
                usuarioEmail.setEmail(usuario.getEmail());
                usuarioEmail.setUsuario(usuario);
                usuarioEmail.setVerificado(false);
                usuarioEmailService.inserir(ctx, usuarioEmail);
            }
            usuarioGeralPendenteDTO.setEmail(usuarioEmail.getEmail());
            usuarioGeralPendenteDTO.setUsuarioGeral(obterUsuarioGeralDTO(ctx, usuario, empresa, usuarioSimplesDTO, ipCliente));
            usuarioGeralPendenteDTO.getUsuarioGeral().getSolicitacao().setCodigoViaEmail(enviarCodigo);

            String tokenUsuarioPendente = novoUsuarioGeral(usuarioGeralPendenteDTO);
            if (UteisValidacao.emptyString(tokenUsuarioPendente)) {
                throw new Exception("Usuário não sincronizado");
            }
//            usuarioDAO.incluirHistoricoSincronizacao(usuarioVO.getCodigo(), true, tokenUsuarioPendente, "NOVO_USUARIO_PENDENTE");
            return tokenUsuarioPendente;
        } catch (Exception ex) {
            ex.printStackTrace();
//            try {
//                if (usuarioDAO == null) {
//                    usuarioDAO = new Usuario(con);
//                }
//                usuarioDAO.incluirHistoricoSincronizacao(usuario, false, ex.getMessage(), "NOVO_USUARIO_PENDENTE");
//            } catch (Exception ex1) {
//                ex1.printStackTrace();
//            }
            throw ex;
        }
    }

    public void atualizarUsuarioGeral(String ctx, Integer codigoUsuario, Empresa empresaSolicitante,
                                      UsuarioSimplesDTO usuarioSimplesDTO, String ipCliente) throws Exception {
        try {
            Usuario usuario = obterUsuario(ctx, codigoUsuario, null, null);

            UsuarioGeralDTO usuarioGeralDTO = obterUsuarioGeralDTO(ctx, usuario, empresaSolicitante, usuarioSimplesDTO, ipCliente);
            usuarioGeralDTO = atualizarUsuarioGeral(usuarioGeralDTO);

            if (UteisValidacao.emptyString(usuarioGeralDTO.getId())) {
                throw new Exception("Usuário não sincronizado");
            }

            if (UteisValidacao.emptyString(usuario.getUsuarioGeral())) {
                usuario.setUsuarioGeral(usuarioGeralDTO.getId());
                usuarioService.alterar(ctx, usuario);
            }

//            usuarioDAO.incluirHistoricoSincronizacao(usuarioVO.getCodigo(), true, new JSONObject(usuarioGeralDTO).toString(), "SAVE_USUARIO");
        } catch (Exception ex) {
            ex.printStackTrace();
//            try {
//                if (usuarioDAO == null) {
//                    usuarioDAO = new Usuario(con);
//                }
//                usuarioDAO.incluirHistoricoSincronizacao(usuarioVO.getCodigo(), false, ex.getMessage(), "SAVE_USUARIO");
//            } catch (Exception ex1) {
//                ex1.printStackTrace();
//            }
            throw ex;
        }
    }

    private UsuarioEmail obterEmailUsuario(String ctx, Usuario usuario) throws Exception {
        if (usuario.getUsuarioEmail() == null) return null;
        UsuarioEmail usuarioEmail = usuarioEmailService.obterPorId(ctx, usuario.getUsuarioEmail().getCodigo(), usuario.getTipo());
        if (usuarioEmail == null ||
                UteisValidacao.emptyString(usuarioEmail.getEmail())) {
            throw new Exception("E-mail do usuário não encontrado");
        }
        if (!UteisValidacao.validaEmail(usuarioEmail.getEmail())) {
            throw new Exception("E-mail do usuário inválido");
        }
        return usuarioEmail;
    }

    private String obterURLLoginBack(ClientDiscoveryDataDTO clientDiscoveryDataDTO) {
        return clientDiscoveryDataDTO.getServiceUrls().getLoginAppUrl();
    }

    private String novoUsuarioGeral(UsuarioGeralPendenteDTO usuarioGeralPendenteDTO) throws Exception {
        ClientDiscoveryDataDTO clientDiscoveryDataDTO = DiscoveryMsService.urls();
        String url = (obterURLLoginBack(clientDiscoveryDataDTO) + ENDPOINT_LOGIN_USUARIO_NOVO);
        String response = ExecuteRequestHttpService.post(url, new JSONObject(usuarioGeralPendenteDTO).toString(), new HashMap<>());
        if (!new JSONObject(response).has("content")) {
            throw new Exception(response);
        }
        return new JSONObject(response).getString("content");
    }

    private UsuarioGeralDTO atualizarUsuarioGeral(UsuarioGeralDTO usuarioGeralDTO) throws Exception {
        ClientDiscoveryDataDTO clientDiscoveryDataDTO = DiscoveryMsService.urls();
        String url = (obterURLLoginBack(clientDiscoveryDataDTO) + ENDPOINT_LOGIN_USUARIO_ATUALIZAR);
        String response = ExecuteRequestHttpService.post(url, new JSONObject(usuarioGeralDTO).toString(), new HashMap<>());
        if (!new JSONObject(response).has("content")) {
            throw new Exception(response);
        }
        return JSONMapper.getObject(new JSONObject(response).getJSONObject("content"), UsuarioGeralDTO.class);
    }

    public void processarUsuarioGeral(final String ctx, final Integer codigo, boolean novoUsuario, boolean definirSenha,
                                      boolean exception, HttpServletRequest request) throws Exception {
        try {
            if (SuperControle.independente(ctx)) {
                Usuario usuario = usuarioService.obterPorId(ctx, codigo);
                if (usuario == null) {
                    throw new ServiceException("Usuario não encontrado com o codigo " + codigo);
                }
                if (usuario.getProfessor() != null) {
                    Empresa empresa = empresaService.obterEmpresaTreinoIndependente(ctx);
                    if (novoUsuario) {
                        enviarEmailNovoUsuario(ctx, usuario.getCodigo(), definirSenha, false, empresa, sessaoService.getUsuarioAtual(), Uteis.getIp(request));
                    } else {
                        atualizarUsuarioGeral(ctx, usuario.getCodigo(), empresa, sessaoService.getUsuarioAtual(), Uteis.getIp(request));
                    }
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logar(ex, UsuarioServiceImpl.class);
            if (exception) {
                throw ex;
            }
        }
    }

    @Override
    public void solicitarTrocaSenha(String chave, Integer idUsuario, Integer empresaSolicitante, UsuarioSimplesDTO usuarioResponsavel, String ip) throws Exception {
        try {
            if (SuperControle.independente(chave)) {

                Usuario usuario = usuarioService.obterPorId(chave, idUsuario);
                if (StringUtils.isEmpty(usuario.getUsuarioGeral())) {
                    throw new ServiceException("E-mail do usuário não foi verificado!");
                }

                final ClientDiscoveryDataDTO discoveryDataDTO = DiscoveryMsService.urlsChave(chave);

                TokenSolicitacaoDTO dto = new TokenSolicitacaoDTO(usuario, chave, empresaSolicitante, sessaoService.getUsuarioAtual(), ip);

                String urlAtivar = obterURLLoginBack(discoveryDataDTO) + ENDPOINT_LOGIN_NOVA_SENHA;

                Map<String, String> headers = new HashMap<>();
                headers.put("Content-Type", "application/json");
                String response = ExecuteRequestHttpService.post(urlAtivar, new JSONObject(dto).toString(), headers);
                if (!new JSONObject(response).has("content")) {
                    throw new Exception(response);
                }
            } else {
                throw new ServiceException("Ação não permitida");
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    @Override
    public void desvincularUsuario(DesvincularUsuarioDTO dto) throws ServiceException {
        try {
            String chave = dto.getChaveDesvincular();
            Usuario usuario = usuarioService.obterPorId(chave, dto.getCodigoTw());
            usuario.setUsuarioGeral(null);
            usuario.setEmail(null);
            usuario.setSenha(Uteis.encriptar(UUID.randomUUID().toString()));
            usuarioService.alterar(chave, usuario);
            UsuarioEmail usuarioEmail = usuarioEmailService.obterUsuarioEmailParam(chave, "usuario", usuario);
            if (usuarioEmail != null) {
                usuarioEmailService.excluir(chave, usuarioEmail);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new ServiceException("Erro ao desvincular usuário da chave " + dto.getChaveDesvincular(), ex);
        }
    }

    @Override
    public void desvincularUsuarioNovoLogin(String chave, Integer idUsuario, Integer empresaSolicitante, UsuarioSimplesDTO usuarioAtual, String ip) throws ServiceException {
        try {
            if (SuperControle.independente(chave)) {
                Usuario usuario = usuarioService.obterPorId(chave, idUsuario);
                if (StringUtils.isEmpty(usuario.getUsuarioGeral())) {
                    throw new ServiceException("email-nao-verificado", "E-mail do usuário não foi verificado!");
                }

                final ClientDiscoveryDataDTO discoveryDataDTO = DiscoveryMsService.urlsChave(chave);

                TokenSolicitacaoDTO tokenSolicitacaoDTO = new TokenSolicitacaoDTO(usuario, chave, empresaSolicitante, sessaoService.getUsuarioAtual(), ip);
                DesvincularUsuarioDTO desvincularUsuarioDTO = new DesvincularUsuarioDTO();
                desvincularUsuarioDTO.setIdUsuarioResponsavel(usuarioAtual.getId());
                desvincularUsuarioDTO.setTokenSolicitacaoDTO(tokenSolicitacaoDTO);
                desvincularUsuarioDTO.setChaveDesvincular(chave);
                desvincularUsuarioDTO.setIdUsarioGeral(usuario.getUsuarioGeral());
                desvincularUsuarioDTO.setCodigoTw(usuario.getCodigo());

                String urlDesvincular = obterURLLoginBack(discoveryDataDTO) + ENDPOINT_LOGIN_DESVINCULAR_USUARIO;
                Map<String, String> headers = new HashMap<>();
                headers.put("Content-Type", "application/json");
                ExecuteRequestHttpService.post(urlDesvincular, new JSONObject(desvincularUsuarioDTO).toString(), headers);
            } else {
                throw new ServiceException("Ação não permitida");
            }
        } catch(ServiceException se) {
            throw se;
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new ServiceException("Erro ao desvincular usuário da chave " + chave);
        }
    }
}
