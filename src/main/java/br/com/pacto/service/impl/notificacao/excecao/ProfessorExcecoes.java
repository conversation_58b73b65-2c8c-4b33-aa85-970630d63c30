package br.com.pacto.service.impl.notificacao.excecao;

import br.com.pacto.service.exception.ExcecaoSistema;

/**
 * Created by ulisses on 29/08/2018.
 */
public enum ProfessorEx<PERSON><PERSON><PERSON> implements ExcecaoSistema {

    ERRO_BUSCAR_PROFESSORES("erro_buscar_professores", "Ocorreu um erro ao buscar os Professores"),
    ERRO_BUSCAR_PROFESSOR("erro_buscar_professor", "Ocorreu um erro ao buscar o Professor"),
    ERRO_BUSCAR_INDICADORES_CARTEIRA_PROFESSOR("erro_buscar_indicadores_carteiria_professor", "Ocorreu um erro ao tentar carregar indicadores de carteira dos professores"),
    ERRO_CRIAR_FILTRO("erro_criar_filtro", "Ocorreu um erro ao tentar criar filtro"),
    ERRO_CARREGAR_ALUNOS_POR_INDICADOR("erro_carregar_alunos_atividade_professor", "Erro ao carregar alunos por indicador"),

    CAMPOS_OBRIGATORIOS("campos_obrigatorio", "Campos obrigatório não foram preenchidos"),
    ERRO_INCLUIR_CONFIGURACAO_RANKING("erro_incluir_configuracao_ranking", "Erro ao incluir configuração do ranking dos professores"),
    ERRO_CARREGAR_CONFIGURACOES_RANKING("erro_carregar_configuracoes_ranking", "Erro ao carregar configurações do ranking dos professores"),
    ERRO_REMOVER_CONFIGURACAO("erro_remover_configuracao", "Erro ao tentar remover configuração"),
    CONFIGURACAO_NAO_INFORMADO("configuracao_nao_informado", "Configuração não informado"),
    ERRO_AO_SALVAR_CONFIG_RELATORIO("erro_ao_salvar_config_relatorio", "Erro ao salvar config relatorio"),
    ERRO_AO_OBTER_CONFIG_RELATORIO("erro_ao_obter_config_relatorio", "Erro ao obter config relatorio"),
    ERRO_BUSCAR_COLABORADORES("erro_buscar_colaboradores", "Ocorreu um erro ao buscar os colaboradores")

    ;

    private String chave;
    private String descricao;

    ProfessorExcecoes(String chave, String descricao) {
        this.chave = chave;
        this.descricao = descricao;
    }

    @Override
    public String getChaveExcecao() {
        return chave;
    }

    @Override
    public String getDescricaoExcecao() {
        return descricao;
    }

}
