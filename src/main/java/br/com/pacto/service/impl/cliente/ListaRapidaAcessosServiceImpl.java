package br.com.pacto.service.impl.cliente;

import br.com.pacto.bean.aula.SpiviSeat;
import br.com.pacto.bean.configuracoes.ConfiguracaoSistema;
import br.com.pacto.bean.configuracoes.ConfiguracoesEnum;
import br.com.pacto.bean.configuracoes.NotificacoesAcessoUsuario;
import br.com.pacto.bean.programa.ProgramaTreino;
import br.com.pacto.controller.json.aluno.*;
import br.com.pacto.dao.intf.configuracoes.NotificacoesAcessoUsuarioDao;
import br.com.pacto.dao.intf.programa.ProgramaTreinoDao;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.conexao.ConexaoZWServiceImpl;
import br.com.pacto.service.impl.memcached.MemCachedManager;
import br.com.pacto.service.intf.cliente.ListaRapidaAcessosService;
import br.com.pacto.service.intf.configuracoes.ConfiguracaoSistemaService;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.*;

@Service
public class ListaRapidaAcessosServiceImpl implements ListaRapidaAcessosService{

    @Autowired
    private ConexaoZWServiceImpl conexaoZWService;
    @Autowired
    private SessaoService sessaoService;
    @Autowired
    private ProgramaTreinoDao programaTreinoDao;
    @Autowired
    private ConfiguracaoSistemaService configSistemaService;
    @Autowired
    private NotificacoesAcessoUsuarioDao notificacoesAcessoUsuarioDao;
    @Autowired
    private MemCachedManager memCachedManager;

    public ConfigListaRapidaAcessoDTO listaUltimosAcessos(Integer empresa, TipoListaAcessoEnum tipo, Integer limit) throws ServiceException {
        return listaUltimosAcessos(empresa, tipo, limit, false);
    }
    public ConfigListaRapidaAcessoDTO listaUltimosAcessos(Integer empresa, TipoListaAcessoEnum tipo, Integer limit, boolean cache) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            Integer idUsuario = sessaoService.getUsuarioAtual().getId();
            String username = sessaoService.getUsuarioAtual().getUsername();
            int limiteConsulta = limit == null ? 50 : limit;
            // Verifica permissões e configurações
            ConfigListaRapidaAcessoDTO cfg = carregarConfiguracoesRapidas(ctx, empresa, tipo, username);
            if (tipo == TipoListaAcessoEnum.TODOS_ALUNOS && !cfg.getVerTodos()) {
                tipo = TipoListaAcessoEnum.ALUNOS_VINCULADOS;
            }
            // Tentativa de cache
            if (cache) {
                ConfigListaRapidaAcessoDTO cfgComCache = getConfigListaRapidaAcessoDTOComCache(empresa, tipo, ctx, limiteConsulta, cfg);
                if (cfgComCache != null) return cfgComCache;
            }
            StringBuilder sqlStr = gerarConsulta(tipo, null);
            List<ListaRapidaAcessoDTO> lista = new ArrayList<>();
            StringBuilder codigosClientes = new StringBuilder();
            Integer horaAnterior = null;
            Date hojeDate = Calendario.hoje();
            String hojeStr = Uteis.getDataAplicandoFormatacao(hojeDate, "dd/MM");
            try (Connection con = conexaoZWService.conexaoZw(ctx);
                 PreparedStatement stm = con.prepareStatement(sqlStr.toString())) {
                int paramIndex = 1;
                if (tipo == TipoListaAcessoEnum.ALUNOS_VINCULADOS) {
                    stm.setInt(paramIndex++, idUsuario);
                }
                stm.setInt(paramIndex++, empresa);
                stm.setInt(paramIndex, limiteConsulta);
                try (ResultSet rs = stm.executeQuery()) {
                    while (rs.next()) {
                        Date dthrentrada = rs.getTimestamp("dataultimoacesso");
                        int horaAtual = Uteis.gethoraHH(dthrentrada);
                        lista.add(montarAcesso(con, rs,
                                horaAnterior,
                                horaAtual, hojeDate, hojeStr, codigosClientes));
                        horaAnterior = horaAtual;
                    }
                }
            }

            List<ListaRapidaAcessoDTO> finalLista = lista.isEmpty()
                    ? lista
                    : processarPendencias(ctx, codigosClientes.toString().replaceFirst(",", ""), lista);

            cfg.setLista(finalLista);

            if (cache) {
                memCachedManager.gravar(ctx, "listaAcesso:" + ctx + ":" + empresa + ":" + tipo + ":" + limiteConsulta, finalLista);
            }

            return cfg;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    private StringBuilder gerarConsulta(TipoListaAcessoEnum tipo,  String matricula) {
        StringBuilder sqlStr = new StringBuilder();
        sqlStr.append(" select s.dataultimoacesso, s.codigopessoa, s.codigocliente, s.nomecliente, s.matricula, s.situacao, ")
                .append(" s.situacaocontrato, s.nomeplano, p.fotokey, cli.parqpositivo, ")
                .append(" p.datanasc, s.pesorisco ")
                .append(" FROM situacaoclientesinteticodw s ")
                .append(" INNER JOIN pessoa p ON p.codigo = s.codigopessoa ")
                .append(" INNER JOIN cliente cli ON cli.codigo = s.codigocliente ");
        if (matricula != null) {
            sqlStr.append(" WHERE s.matricula = ?");
        } else if (tipo == TipoListaAcessoEnum.ALUNOS_VINCULADOS) {
            sqlStr.append(" INNER JOIN vinculo v ON v.cliente = cli.codigo ")
                    .append(" INNER JOIN usuario u ON u.colaborador = v.colaborador ")
                    .append(" WHERE u.codigo = ? AND s.empresacliente = ? ");
        } else {
            sqlStr.append(" WHERE s.empresacliente = ? ");
        }
        sqlStr.append(" and s.dataultimoacesso is not null ");
        sqlStr.append(" ORDER BY s.dataultimoacesso DESC LIMIT ? ");
        return sqlStr;
    }

    private ListaRapidaAcessoDTO montarAcesso(String ctx, String matricula,
                                              Integer empresa) throws Exception{
        StringBuilder sqlStr = gerarConsulta(TipoListaAcessoEnum.TODOS_ALUNOS, matricula);
        StringBuilder codigosClientes = new StringBuilder();
        Integer horaAnterior = null;
        Date hojeDate = Calendario.hoje();
        String hojeStr = Uteis.getDataAplicandoFormatacao(hojeDate, "dd/MM");
        try (Connection con = conexaoZWService.conexaoZw(ctx);
             PreparedStatement stm = con.prepareStatement(sqlStr.toString())) {
            int paramIndex = 1;
            // Set matricula parameter if it's being used
            if (matricula != null) {
                stm.setString(paramIndex++, matricula);
            }
            stm.setInt(paramIndex++, empresa);
            stm.setInt(paramIndex, 1);
            try (ResultSet rs = stm.executeQuery()) {
                while (rs.next()) {
                    Date dthrentrada = rs.getTimestamp("dataultimoacesso");
                    int horaAtual = Uteis.gethoraHH(dthrentrada);
                    horaAnterior = horaAtual;
                    return montarAcesso(con, rs,
                            horaAnterior,
                            horaAtual, hojeDate, hojeStr, codigosClientes);
                }
            }
        }
        return null;
    }

    private ListaRapidaAcessoDTO montarAcesso(Connection con, ResultSet rs,
                                              Integer horaAnterior,
                                              Integer horaAtual, Date hojeDate, String hojeStr,
                                              StringBuilder codigosClientes) throws Exception {
        ListaRapidaAcessoDTO obj = new ListaRapidaAcessoDTO();
        int codigoCliente = rs.getInt("codigocliente");
        codigosClientes.append(",").append(codigoCliente);
        obj.setCodigoCliente(codigoCliente);
        Date dthrentrada = rs.getTimestamp("dataultimoacesso");
        obj.setDivisor(horaAnterior != null && !horaAnterior.equals(horaAtual));
        obj.setIdMemcached(codigoCliente + ";" + Uteis.getDataAplicandoFormatacao(dthrentrada, "ddMMHHmm"));
        obj.setMatricula(rs.getInt("matricula"));
        obj.setNome(minuscula(rs.getString("nomecliente")));
        obj.setSituacao(minuscula(rs.getString("situacao")));
        obj.setSituacaoContrato(minuscula(rs.getString("situacaocontrato")));
        obj.setPlano(minuscula(rs.getString("nomeplano")));
        obj.setFoto(Aplicacao.obterUrlFotoDaNuvem(rs.getString("fotokey")));
        obj.setHora(Uteis.getDataAplicandoFormatacao(dthrentrada, Calendario.igual(hojeDate, dthrentrada) ? "HH:mm" : "dd/MM HH:mm"));
        obj.setParq(rs.getBoolean("parqpositivo"));
        obj.setAniversariante(hojeStr.equals(Uteis.getDataAplicandoFormatacao(rs.getDate("datanasc"), "dd/MM")));
        int risco = rs.getInt("pesorisco");
        obj.setCor(risco > 7 ? "vermelho-acesso" : risco == 6 ? "amarelo-acesso" : "azul-acesso");
        if(!"AT".equals(rs.getString("situacao"))){
            tipoAcesso(con, rs.getInt("codigopessoa"), obj);
        }
        return obj;
    }

    private void tipoAcesso(Connection con, Integer pessoa, ListaRapidaAcessoDTO obj) throws Exception{
        try (PreparedStatement stm = con.prepareStatement(" select aulaavulsadiaria, tokengympass," +
                " tipototalpass, tipoacesso from periodoacessocliente p" +
                " where pessoa = " + pessoa +
                " order by p.codigo desc limit 1")) {
            try (ResultSet rs = stm.executeQuery()) {
                if (rs.next()) {
                    String tokengympass = rs.getString("tokengympass");
                    if(!UteisValidacao.emptyString(tokengympass)) {
                        obj.setGympass(true);
                        return;
                    }
                    boolean tipototalpass = rs.getBoolean("tipototalpass");
                    if(tipototalpass) {
                        obj.setTotalpass(true);
                        return;
                    }
                    String tipoacesso = rs.getString("tipoacesso");
                    obj.setDiaria("DI".equals(tipoacesso));
                    obj.setAulaAvulsa("AA".equals(tipoacesso));
                    obj.setFreepass("PL".equals(tipoacesso));
                }
            }
        } catch (Exception e) {
            Uteis.logar(e, ListaRapidaAcessoDTO.class);
        }
    }

    private ConfigListaRapidaAcessoDTO getConfigListaRapidaAcessoDTOComCache(Integer empresa, TipoListaAcessoEnum tipo,
                                                                             String ctx, int limiteConsulta, ConfigListaRapidaAcessoDTO cfg) {
        try {
            List<ListaRapidaAcessoDTO> cached = (List<ListaRapidaAcessoDTO>) memCachedManager.ler(ctx, "listaAcesso:" +
                    ctx + ":" + empresa + ":" + tipo + ":" + limiteConsulta);
            if(cached != null){
                List<String> accessos = (List<String>) MemCachedManager.getInstance().getMemcachedClient().get("accessos_recentes:" + ctx);
                if(accessos != null){
                    for(String acesso : accessos){
                        //procura na lista cached se tem algum com o mesmo codigo cached
                        if(cached.stream().noneMatch(c -> c.getIdMemcached().equals(acesso))){
                            ListaRapidaAcessoDTO acessoDTO = montarAcesso(ctx, acesso, empresa);
                            cached.add(0, acessoDTO);
                        }
                    }
                }
                cfg.setLista(cached);
                return cfg;
            }
        }catch (Exception e){
            Uteis.logar(e, ListaRapidaAcessosServiceImpl.class);
        }
        return null;
    }

    private ConfigListaRapidaAcessoDTO carregarConfiguracoesRapidas(String ctx, Integer empresa, TipoListaAcessoEnum tipo, String username) throws Exception {
        ConfigListaRapidaAcessoDTO cfg = new ConfigListaRapidaAcessoDTO();
        ConfiguracaoSistemaService css = (ConfiguracaoSistemaService) UtilContext.getBean(ConfiguracaoSistemaService.class);
        ConfiguracaoSistema config = css.consultarPorTipo(ctx, ConfiguracoesEnum.ESCOLHER_TIPO_LISTA_RAPIDA_ACESSOS);
        cfg.setVerTodos(config.getValorAsBoolean());
        cfg.setVerPendencias(permissaoPendencias(ctx, empresa, username));

        NotificacoesAcessoUsuario notifs = notificacoesAcessoUsuario(ctx, sessaoService.getUsuarioAtual().getId());
        if (!UteisValidacao.emptyNumber(notifs.getCodigo())) {
            notificacoesAcessoUsuarioDao.refresh(ctx, notifs);
        }

        for (PendenciasAcessoEnum p : PendenciasAcessoEnum.values()) {
            Boolean val = p.getValor(notifs, p);
            cfg.getMapConfigs().put(p.name(), val != null && val);
        }

        return cfg;
    }


    public void gravarCfgNotificacoesAcessosUsuarios(PendenciasAcessoEnum cfgPendencia, Boolean valor) throws ServiceException{
        try{
            String ctx = sessaoService.getUsuarioAtual().getChave();
            Integer id = sessaoService.getUsuarioAtual().getId();
            NotificacoesAcessoUsuario notificacoesAcessoUsuario = notificacoesAcessoUsuario(ctx, id);
            if(!UteisValidacao.emptyNumber(notificacoesAcessoUsuario.getCodigo())){
                notificacoesAcessoUsuarioDao.refresh(ctx, notificacoesAcessoUsuario);
            }
            notificacoesAcessoUsuario.setUsuario(id);
            switch (cfgPendencia){
                case SEM_PROGRAMA_DE_TREINO:
                    notificacoesAcessoUsuario.setSemProgramaTreino(valor);
                    break;
                case FALTOSOS:
                    notificacoesAcessoUsuario.setFaltosos(valor);
                    break;
                case QUATRO_DIAS_OU_MAIS_SEM_EXECUTAR_O_TREINO:
                    notificacoesAcessoUsuario.setQuatrosDiasSemAcesso(valor);
                    break;
                case PROGRAMA_DE_TREINO_VENCIDO:
                    notificacoesAcessoUsuario.setTreinoVencido(valor);
                    break;
                case PROGRAMA_DE_TREINO_A_VENCER:
                    notificacoesAcessoUsuario.setTreinoVencer(valor);
                    break;
                case ALUNO_SEM_VINCULO_DE_PROFESSOR_TREINOWEB:
                    notificacoesAcessoUsuario.setAlunoSemVinculoProfessor(valor);
                    break;
                case PARCELAS_EM_ATRASO:
                    notificacoesAcessoUsuario.setParcelasAtrasadas(valor);
                    break;
                case AVALIACAO_FISICA_ATRASADA:
                    notificacoesAcessoUsuario.setAvaliacaoFisicaAtrasada(valor);
                    break;
                case SEM_ASSINATURA_DE_CONTRATO:
                    notificacoesAcessoUsuario.setSemAssinaturaContrato(valor);
                    break;
                case CADASTRO_INCOMPLETO:
                    notificacoesAcessoUsuario.setCadastroIncompleto(valor);
                    break;
            }

            if(UteisValidacao.emptyNumber(notificacoesAcessoUsuario.getCodigo())){
                notificacoesAcessoUsuarioDao.insert(ctx, notificacoesAcessoUsuario);
            } else {
                notificacoesAcessoUsuarioDao.update(ctx, notificacoesAcessoUsuario);
            }

        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    private NotificacoesAcessoUsuario notificacoesAcessoUsuario(String ctx, Integer usuario) throws Exception{
        Map<String, Object> params = new HashMap<>();
        params.put("usuario", usuario);

        List<NotificacoesAcessoUsuario> notificacoes = notificacoesAcessoUsuarioDao.findByParam(ctx,
                "select obj from NotificacoesAcessoUsuario obj where usuario = :usuario",
                params);

        return notificacoes == null || notificacoes.isEmpty() ? new NotificacoesAcessoUsuario() : notificacoes.get(0);
    }

    private Boolean permissaoPendencias(String ctx, Integer empresa, String username) throws Exception{
        try (Connection con = conexaoZWService.conexaoZw(ctx);
             PreparedStatement stm = con.prepareStatement(
                     "select p.permissoes  from permissao p\n" +
                             "        inner join perfilacesso pa on pa.codigo = p.codperfilacesso\n" +
                             "        inner join usuarioperfilacesso up on up.perfilacesso = pa.codigo\n" +
                             "        inner join usuario u on u.codigo = up.usuario\n" +
                             "        where p.tituloapresentacao like '13.16%'\n" +
                             "        and upper(u.username) = '"+username.toUpperCase()+"'\n" +
                             "        and up.empresa = " + empresa);
             ResultSet rs = stm.executeQuery()){
             return rs.next() ? rs.getString("permissoes").contains("(0)") : false;
        }
    }

    private List<ListaRapidaAcessoDTO> processarPendencias(String chave, String codigosClientes, List<ListaRapidaAcessoDTO> lista) throws Exception{
        ConfiguracaoSistema diasAntesVencimento = configSistemaService.consultarPorTipo(chave, ConfiguracoesEnum.DIAS_ANTES_VENCIMENTO);
        Date dataLimiteTreinoAVencer = Uteis.somarDias(Calendario.hoje(), diasAntesVencimento.getValorAsInteger());
        Date dataLimiteExecucaoTreino = Uteis.somarDias(Calendario.hoje(), -4);
        List<Integer> alunosSemProfessor = verificarClientesSemProfessor(chave, codigosClientes);
        List<Integer> faltosos = clientesFaltosos(chave, codigosClientes);
        List<Integer> parqNaoRespondido = verificarClientesSemParq(chave, codigosClientes);
        List<Integer> clientesSemAssinaturaContrato = clientesSemAssinaturaContrato(chave, codigosClientes);
        Map<Integer, Date> datasProgramas = verificarProgramaTreino(chave, codigosClientes);
        Map<Integer, Date> datasAvaliacoes = verificarAvaliacaoFisica(chave, codigosClientes);
        Map<Integer, Date> excucoesTreino = verificarExcucoesTreino(chave, codigosClientes);
        Map<Integer, List<String>> mensagens = verificarClienteMensagem(chave, codigosClientes);

        for (ListaRapidaAcessoDTO obj : lista) {
            try {
                if(parqNaoRespondido.contains(obj.getMatricula())){
                    obj.addPendencia(PendenciasAcessoEnum.PARQ_NAO_RESPONDIDO);
                } else {
                    obj.addPendencia(obj.isParq() ? PendenciasAcessoEnum.PARQ_POSITIVO : PendenciasAcessoEnum.PARQ_NEGATIVO);
                }
//                Sem programa de trein
                Date dataTerminoPrograma = datasProgramas.get(obj.getMatricula());
                if(dataTerminoPrograma == null){
                    obj.addPendencia(PendenciasAcessoEnum.SEM_PROGRAMA_DE_TREINO);
                }
//                Faltosos
                if(faltosos.contains(obj.getCodigoCliente())){
                    obj.addPendencia(PendenciasAcessoEnum.FALTOSOS);
                }
//                4 dias ou mais sem executar o treino
                Date dataUltimaExecucao = excucoesTreino.get(obj.getMatricula());
                if(dataUltimaExecucao != null && Calendario.menor(dataUltimaExecucao, dataLimiteExecucaoTreino)){
                    obj.addPendencia(PendenciasAcessoEnum.QUATRO_DIAS_OU_MAIS_SEM_EXECUTAR_O_TREINO);
                }
//                Programa de treino vencido
                if(dataTerminoPrograma != null && Calendario.menor(dataTerminoPrograma, Calendario.hoje())){
                    obj.addPendencia(PendenciasAcessoEnum.PROGRAMA_DE_TREINO_VENCIDO);
                }
//                Programa de treino a Vencer
                if(dataTerminoPrograma != null
                        && Calendario.maiorOuIgual(dataTerminoPrograma, Calendario.hoje())
                        && Calendario.menor(dataTerminoPrograma, dataLimiteTreinoAVencer)){
                    obj.addPendencia(PendenciasAcessoEnum.PROGRAMA_DE_TREINO_A_VENCER);
                }
//                Aluno sem vínculo de professor treinoweb
                if (alunosSemProfessor.contains(obj.getCodigoCliente())) {
                    obj.addPendencia(PendenciasAcessoEnum.ALUNO_SEM_VINCULO_DE_PROFESSOR_TREINOWEB);
                }
                List<String> mensagensCliente = mensagens.get(obj.getCodigoCliente());
//                parcelas em atraso
                if(mensagensCliente != null && mensagensCliente.contains("PA")){
                    obj.addPendencia(PendenciasAcessoEnum.PARCELAS_EM_ATRASO);
                }
//                Avaliação física atrasad
                Date dataUltimaAvaliacao = datasAvaliacoes.get(obj.getMatricula());
                if(dataUltimaAvaliacao != null && Calendario.menor(dataUltimaAvaliacao, Calendario.hoje())){
                    obj.addPendencia(PendenciasAcessoEnum.AVALIACAO_FISICA_ATRASADA);
                }
//                Sem assinatura de contrato
                if(clientesSemAssinaturaContrato.contains(obj.getCodigoCliente())){
                    obj.addPendencia(PendenciasAcessoEnum.SEM_ASSINATURA_DE_CONTRATO);
                }
//                Cadastro incompleto
                if(mensagensCliente != null && mensagensCliente.contains("DI")){
                    obj.addPendencia(PendenciasAcessoEnum.CADASTRO_INCOMPLETO);
                }
            }catch (Exception e){
                Uteis.logar(e, ListaRapidaAcessosServiceImpl.class);
            }
        }
        return lista;
    }

    private List<Integer> verificarClientesSemProfessor(String chave,
                                                        String codigoClientes) throws Exception{
        List<Integer> alunosSemProfessor = new ArrayList<>();
        try (Connection con = programaTreinoDao.getConnection(chave)) {
            try (PreparedStatement pstm = con.prepareStatement("select codigocliente from clientesintetico c where professorsintetico_codigo is null and codigocliente in (" + codigoClientes + ")");
                 ResultSet rs = pstm.executeQuery()) {
                while (rs.next()) {
                    alunosSemProfessor.add(rs.getInt("codigocliente"));
                }
            }
        }
        return alunosSemProfessor;
    }

    private List<Integer> verificarClientesSemParq(String chave,
                                                        String codigoClientes) throws Exception{
        List<Integer> clientesSemParq = new ArrayList<>();
        try (Connection con = programaTreinoDao.getConnection(chave)) {
            try (PreparedStatement pstm = con.prepareStatement(
                    "select matricula from clientesintetico c \n" +
                            "left join respostacliente r on r.cliente_codigo = c.codigo \n" +
                            "where codigocliente in (" + codigoClientes + ") and r.respostaclienteparq_codigo is null and r.codigo is null");
                 ResultSet rs = pstm.executeQuery()) {
                while (rs.next()) {
                    clientesSemParq.add(rs.getInt("matricula"));
                }
            }
        }
        return clientesSemParq;
    }

    private List<Integer> clientesSemAssinaturaContrato(String chave,
                                                        String codigosClientes) throws Exception{
        List<Integer> alunosSemAssinatura = new ArrayList<>();
        try (Connection con = conexaoZWService.conexaoZw(chave);
             PreparedStatement stm = con.prepareStatement(
                "select codigocliente from situacaoclientesinteticodw s \n" +
                        "left join contratoassinaturadigital c on c.contrato = s.codigocontrato \n" +
                        "where codigocliente in ("  + codigosClientes + ") and assinatura is null\n" +
                        "and codigoContrato > 0 ");
            ResultSet rs = stm.executeQuery()){
            while (rs.next()){
                alunosSemAssinatura.add(rs.getInt("codigocliente"));
            }
        }
        return alunosSemAssinatura;
    }

    private List<Integer> clientesFaltosos(String chave, String codigosClientes) throws Exception{
        List<Integer> faltosos = new ArrayList<>();
        try (Connection con = conexaoZWService.conexaoZw(chave);
             PreparedStatement stm = con.prepareStatement(
                "select f.cliente from fecharmetadetalhado f \n" +
                        "inner join fecharmeta fm on f.fecharmeta = fm.codigo \n" +
                        "inner join aberturameta a on a.codigo = fm.aberturameta \n" +
                        "where fm.identificadormeta = 'FA' and f.cliente in ("  + codigosClientes +
                        ")  and a.dia = '"+Uteis.getDataAplicandoFormatacao(Calendario.hoje(), "yyyy-MM-dd")+"'");
            ResultSet rs = stm.executeQuery()){
            while (rs.next()){
                faltosos.add(rs.getInt("cliente"));
            }
        }
        return faltosos;
    }

    private Map<Integer, Date> verificarProgramaTreino(String chave,
                                                       String codigoClientes) throws Exception{
        Map<Integer, Date> map = new HashMap<>();
        try (Connection con = programaTreinoDao.getConnection(chave)) {
            try (PreparedStatement pstm = con.prepareStatement(
                    "select matricula, max(p.dataterminoprevisto) from clientesintetico c \n" +
                            "left join programatreino p on p.cliente_codigo = c.codigo \n" +
                            "where codigocliente in (" + codigoClientes + ") group by matricula ");
                 ResultSet rs = pstm.executeQuery()) {
                while (rs.next()) {
                    map.put(rs.getInt("matricula"), rs.getDate(2));
                }
            }
        }
        return map;
    }

    private Map<Integer, Date> verificarExcucoesTreino(String chave,
                                                       String codigoClientes) throws Exception{
        Map<Integer, Date> map = new HashMap<>();
        try (Connection con = programaTreinoDao.getConnection(chave)) {
            try (PreparedStatement pstm = con.prepareStatement(
                    "select matricula, max(t.datainicio) from clientesintetico c \n" +
                            "left join treinorealizado t on t.cliente_codigo = c.codigo \n" +
                            "where codigocliente in (" + codigoClientes + ") group by matricula ");
                 ResultSet rs = pstm.executeQuery()) {
                while (rs.next()) {
                    map.put(rs.getInt("matricula"), rs.getDate(2));
                }
            }
        }
        return map;
    }

    private Map<Integer, List<String>> verificarClienteMensagem(String chave,
                                                       String codigosClientes) throws Exception{
        Map<Integer, List<String>> map = new HashMap<>();
        try (Connection con = conexaoZWService.conexaoZw(chave);
             PreparedStatement stm = con.prepareStatement(
                "select tipomensagem, cliente  from clientemensagem c \n" +
                        "where cliente in ("  + codigosClientes + ") and tipomensagem in ('PA', 'DI') ");
             ResultSet rs = stm.executeQuery()){
            while (rs.next()){
                List<String> strings = map.get(rs.getInt("cliente"));
                if(strings == null){
                    strings = new ArrayList<>();
                    map.put(rs.getInt("cliente"), strings);
                }
                strings.add(rs.getString("tipomensagem"));
            }
        }
        return map;
    }


    private Map<Integer, Date> verificarAvaliacaoFisica(String chave,
                                                       String codigoClientes) throws Exception{
        Map<Integer, Date> map = new HashMap<>();
        try (Connection con = programaTreinoDao.getConnection(chave)) {
            try (PreparedStatement pstm = con.prepareStatement(
                    "select matricula, max(a.dataproxima) from clientesintetico c \n" +
                            "left join avaliacaofisica a on a.cliente_codigo = c.codigo \n" +
                            "where codigocliente in (" + codigoClientes + ") group by matricula ");
                 ResultSet rs = pstm.executeQuery()) {
                while (rs.next()) {
                    map.put(rs.getInt("matricula"), rs.getDate(2));
                }
            }
        }
        return map;
    }

    private String minuscula(String w){
        return w == null ? null : w.toLowerCase();
    }

}
