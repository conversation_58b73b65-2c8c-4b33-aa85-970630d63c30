/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.impl.tipowod;

import br.com.pacto.base.impl.EntityManagerService;
import br.com.pacto.bean.tipowod.TipoWod;
import br.com.pacto.bean.wod.FiltroTipoWodJSON;
import br.com.pacto.bean.wod.TipoWodResponseTO;
import br.com.pacto.bean.wod.TipoWodTO;
import br.com.pacto.dao.intf.tipowod.TipoWodDao;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.exception.ValidacaoException;
import br.com.pacto.service.impl.notificacao.excecao.TipoWodExcecoes;
import br.com.pacto.service.intf.tipowod.TipoWodService;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR>
 */
@Service
public class TipoWodServiceImpl implements TipoWodService {

    @Autowired
    private TipoWodDao tipoWodDao;
    @Autowired
    private SessaoService sessaoService;
    private String tipoOrdenado = "";
    private String campoResultadoMontar = "";
    public TipoWodDao getTipoWodDao() {
        return tipoWodDao;
    }

    public void setTipoWodDao(TipoWodDao tipoWodDao) {
        this.tipoWodDao = tipoWodDao;
    }
    
    @Override
    public TipoWod gravarTipoWod(String key, TipoWod tipoWod) throws ServiceException {
        try {
            validarDados(key, tipoWod);
            if (tipoWod.getCodigo() == null || tipoWod.getCodigo().equals(0)) {
                getTipoWodDao().insert(key, tipoWod);
                return tipoWod;
            }else{
                return getTipoWodDao().update(key, tipoWod);
            }
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    @Override
    public List<TipoWod> obterTodas(String key) throws ServiceException {
        try {
            return getTipoWodDao().findByParam(key, "SELECT obj FROM TipoWod obj", new HashMap<String, Object>());
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    @Override
    public void excluirTipoWod(String key, TipoWod tipoWod) throws ServiceException {
        try {
            if (tipoWod.getCodigo().equals(0)) {
                throw new Exception("tipowod.nao.pode.excluir");
            }
            tipoWodDao.delete(key, tipoWod);
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }
    
    public void validarDados(final String ctx, TipoWod tipoWod) throws ValidacaoException{
        if (getTipoWodDao().exists(ctx, tipoWod, "nome")) {
            throw new ValidacaoException("cadastros.existente");
        }
    }

    @Override
    public TipoWod obterPorNome(String ctx, String nome) throws ServiceException {
        String s = "select obj from TipoWod obj where nome = :nome";
        Map<String, Object> p = new HashMap();
        p.put("nome", nome);
        return getTipoWodDao().obterObjetoPorParam(ctx, s, p);
    }

    @Override
    public TipoWod obterPorCodigo(String ctx, Integer codigo) throws ServiceException {
        String s = "select obj from TipoWod obj where codigo = :codigo";
        Map<String, Object> p = new HashMap();
        p.put("codigo", codigo);
        return getTipoWodDao().obterObjetoPorParam(ctx, s, p);
    }

    @Override
    public List<TipoWodResponseTO> listarTiposWod(FiltroTipoWodJSON filtros) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            List<TipoWod> tipos = tipoWodDao.listarTiposWod(ctx, filtros);
            List<TipoWodResponseTO> ret = new ArrayList<>();
            for (TipoWod tipo : tipos) {
                ret.add(new TipoWodResponseTO(tipo));
            }
            return ret;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException(TipoWodExcecoes.ERRO_BUSCAR_TIPOS_WOD, e);
        }
    }

    @Override
    public TipoWodResponseTO buscarTiposWod(Integer id) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            TipoWod tipo = obterPorCodigo(ctx, id);
            if (tipo == null) {
                throw new ServiceException(TipoWodExcecoes.ERRO_BUSCAR_TIPO_WOD);
            }
            return new TipoWodResponseTO(tipo);
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException(TipoWodExcecoes.ERRO_BUSCAR_TIPO_WOD, e);
        }
    }

    @Override
    public TipoWodResponseTO cadastrarTipoWod(TipoWodTO tipoWodTO) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            TipoWod tipo = new TipoWod();
            preencherTipoWod(tipoWodTO, tipo);
            validarCampos(ctx, tipoWodTO, tipo);

            tipo = tipoWodDao.insert(ctx, tipo);
            return new TipoWodResponseTO(tipo);
        } catch (Exception e) {
            throw new ServiceException(TipoWodExcecoes.ERRO_CADASTRAR_TIPO_WOD, e);
        }
    }

    @Override
    public TipoWodResponseTO atualizarTipoWod(TipoWodTO tipoWodTO) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            TipoWod tipo = obterPorCodigo(ctx, tipoWodTO.getId());
            if (tipo == null) {
                throw new ServiceException(TipoWodExcecoes.TIPO_WOD_NAO_ENCONTRADO);
            }
            preencherTipoWod(tipoWodTO, tipo);
            validarCampos(ctx, tipoWodTO, tipo);
            tipo = tipoWodDao.update(ctx, tipo);
            return new TipoWodResponseTO(tipo);
        } catch (Exception e) {
            throw new ServiceException(TipoWodExcecoes.ERRO_ALTERAR_TIPO_WOD, e);
        }
    }

    @Override
    public void removerTipoWod(Integer id) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            TipoWod tipo = obterPorCodigo(ctx, id);
            if (tipo == null) {
                throw new ServiceException(TipoWodExcecoes.TIPO_WOD_NAO_ENCONTRADO);
            }
            tipoWodDao.delete(ctx, tipo);
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException(TipoWodExcecoes.ERRO_EXCLUIR_TIPO_WOD, e);
        }
    }



    private void preencherTipoWod(TipoWodTO tipoWodTO, TipoWod tipoWod){

        this.tipoOrdenado = "";
        this.campoResultadoMontar = "";
        if (!UteisValidacao.emptyNumber(tipoWod.getCodigo())) {
            tipoWod.setOrderBy(null);
            tipoWod.setCamposResultado(null);
        }

        String[] listaOrdenamentosSelecioandos = tipoWodTO.getOrdenamentosSelecionados();

        tipoWod.setNome(tipoWodTO.getNome().trim());
        if(tipoWodTO.getUsaRanking() != null && tipoWodTO.getUsaRanking()){
            for (String ordenamento : listaOrdenamentosSelecioandos) {
                detStringOrdenar(ordenamento);
            }
        }
        tipoWod.setOrderBy(this.tipoOrdenado);
        tipoWod.setCamposResultado(this.campoResultadoMontar);

    }

    private void detStringOrdenar (String ordenamentoSelecionado){

        String ordenamento = ordenamentoSelecionado.equals("PROFISSIONAIS") ? "nivelCrossfit" : ordenamentoSelecionado ;

        if(!ordenamento.equals("nivelCrossfit")){
            ordenamento =  ordenamento.toLowerCase();
        }

        this.campoResultadoMontar += this.campoResultadoMontar.isEmpty() ? ordenamento:","+ordenamento;

        if (tipoOrdenado.isEmpty()) {
            this.tipoOrdenado += "ORDER BY "+ordenamento+" ";
        }else{
            this.tipoOrdenado += " DESC, "+ordenamento+" ";
        }

    }
    private void validarCampos(String ctx, TipoWodTO tipoWodTO, TipoWod tipoWod) throws ServiceException {
        if(tipoWodTO.getNome() == null || tipoWod.getNome().trim().isEmpty()) {
            throw new ServiceException(TipoWodExcecoes.ERRO_NOME_NAO_ENCONTRADO);
        }
        validarNomeExiste(ctx, tipoWod);
    }

    private void validarNomeExiste(String ctx, TipoWod tipoWod) throws ServiceException {
        if (getTipoWodDao().exists(ctx, tipoWod, "nome")) {
            throw new ServiceException(TipoWodExcecoes.ERRO_NOME_EXISTE);
        }
    }
    protected void prepare(final String ctx) throws ServiceException {
        EntityManagerService ems = (EntityManagerService) UtilContext.getBean(EntityManagerService.class);
        try {
            ems.closeQuietly(ctx);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

}
