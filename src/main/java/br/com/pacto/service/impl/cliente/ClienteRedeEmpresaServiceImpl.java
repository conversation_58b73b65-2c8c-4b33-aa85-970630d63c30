package br.com.pacto.service.impl.cliente;

import br.com.pacto.base.oamd.RedeEmpresaVO;
import br.com.pacto.base.util.ExecuteRequestHttpService;
import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.controller.json.cliente.ClienteJSON;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.cliente.ClienteRedeEmpresaService;
import br.com.pacto.service.intf.empresa.EmpresaService;
import br.com.pacto.util.UteisValidacao;
import org.apache.http.NameValuePair;
import org.apache.http.client.ResponseHandler;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.BasicResponseHandler;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

@Service
public class ClienteRedeEmpresaServiceImpl implements ClienteRedeEmpresaService {

    @Autowired
    private EmpresaService empresaService;

    @Override
    public List<ClienteJSON> consultarPorMatriculaOuNome(String chaveOrigem,
                                                         RedeEmpresaVO rede,
                                                         String filtro) throws ServiceException {
        List<ClienteJSON> clientesRede = new ArrayList<>();
        try {
            if(!UteisValidacao.emptyString(rede.getChaveFranqueadora())){
                clientesRede = consultarAutorizados(chaveOrigem, rede.getChaveFranqueadora(), filtro);
            }
        }catch (Exception e){
            Uteis.logar(e, ClienteRedeEmpresaService.class);
        }
        return clientesRede;
    }

    @Override
    public JSONObject programaEmRede(String chaveOrigem, String token, HttpServletRequest request) throws ServiceException {
        try {
            String[] split = token.split("\\_");
            String chave = split[1];
            String matricula = split[2];
            String urlAplicacao = Uteis.getURLValidaProtocolo(request);
            CloseableHttpClient client = ExecuteRequestHttpService.createConnector();
            HttpPost httpPost = new HttpPost(urlAplicacao.replace(chaveOrigem, chave) + "/atual");
            List<NameValuePair> params = new ArrayList<>();
            params.add(new BasicNameValuePair("matricula", matricula));
            httpPost.setEntity(new UrlEncodedFormEntity(params));
            CloseableHttpResponse response = client.execute(httpPost);
            ResponseHandler<String> handler = new BasicResponseHandler();
            return new JSONObject(handler.handleResponse(response));
        }catch (Exception e){
            Uteis.logar(e, ClienteRedeEmpresaService.class);
            return null;
        }
    }

    @Override
    public JSONObject executarFichaEmRede(String chaveOrigem,
                                          String dataAtual, String idPrograma, String idFicha, boolean fichaConcluida,
                                          String token, HttpServletRequest request) throws ServiceException {
        try {
            String[] split = token.split("\\_");
            String chave = split[1];
            String urlAplicacao = Uteis.getURLValidaProtocolo(request);
            CloseableHttpClient client = ExecuteRequestHttpService.createConnector();
            HttpPost httpPost = new HttpPost(urlAplicacao.replace(chaveOrigem, chave) + "/executarFicha");
            List<NameValuePair> params = new ArrayList<>();

            List<Empresa> empresas = empresaService.obterTodos(chaveOrigem);
            String unidadeExecucao = empresas == null || empresas.isEmpty() ? "" : empresas.get(0).getNome();

            params.add(new BasicNameValuePair("dataAtual", dataAtual));
            params.add(new BasicNameValuePair("idPrograma", idPrograma));
            params.add(new BasicNameValuePair("idFicha", idFicha));
            params.add(new BasicNameValuePair("chaveExecucao", chaveOrigem));
            params.add(new BasicNameValuePair("unidadeExecucao", unidadeExecucao));
            params.add(new BasicNameValuePair("fichaConcluida", String.valueOf(fichaConcluida)));

            httpPost.setEntity(new UrlEncodedFormEntity(params));
            CloseableHttpResponse response = client.execute(httpPost);
            ResponseHandler<String> handler = new BasicResponseHandler();
            return new JSONObject(handler.handleResponse(response));
        }catch (Exception e){
            Uteis.logar(e, ClienteRedeEmpresaService.class);
            return null;
        }
    }

    private List<ClienteJSON> consultarAutorizados(String chaveOrigem, String chaveIntegracao, String param){
        List<ClienteJSON> clientes = new ArrayList<>();
        try {
            List<NameValuePair> params = new ArrayList<>();
            params.add(new BasicNameValuePair("chave", chaveIntegracao));
            params.add(new BasicNameValuePair("param", param));
            params.add(new BasicNameValuePair("empresa", "0"));
            String retorno = Uteis.chamadaZW(chaveIntegracao, "/prest/treino/autorizados", params);
            JSONArray content = new JSONArray(retorno);
            for(int i = 0; i < content.length(); i++){
                JSONObject json = content.getJSONObject(i);
                String chaveAluno = json.getString("chave");
                if(!chaveAluno.equals(chaveOrigem)){
                    ClienteJSON cliente = new ClienteJSON();
                    cliente.setNome(json.getString("nomepessoa") + "("+ json.getString("nomeempresa")+")");
                    cliente.setNomeEmpresa(json.getString("nomeempresa"));
                    cliente.setMatricula(json.getInt("codigomatricula"));
                    cliente.setUserName("VIP_"+chaveAluno+"_"+json.getInt("codigomatricula"));
                    clientes.add(cliente);
                }
            }
        }catch (Exception e){
            Uteis.logar(e, ClienteSinteticoServiceImpl.class);
        }
        return clientes;
    }


}
