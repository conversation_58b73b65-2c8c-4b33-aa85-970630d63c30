package br.com.pacto.service.impl.cliente;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.cliente.ClientePesquisa;
import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.controller.json.aluno.AlunoPesquisaResponseTO;
import br.com.pacto.dao.intf.cliente.ClientePesquisaDao;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.notificacao.excecao.AlunoExcecoes;
import br.com.pacto.service.intf.cliente.ClientePesquisaService;
import br.com.pacto.service.intf.usuario.FotoService;
import br.com.pacto.util.UteisValidacao;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

@Service
public class ClientePesquisaServiceImpl implements ClientePesquisaService {

    @Autowired
    private ClientePesquisaDao clientePesquisaDao;

    @Autowired
    private FotoService fotoService;

    @Autowired
    private SessaoService sessaoService;

    @Override
    public ClientePesquisa inserir(String ctx, ClientePesquisa object) throws ServiceException {
        try{
            return getClientePesquisaDao().updateNoFlush(ctx,object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public ClientePesquisa inserir(String ctx, ClienteSintetico object) throws ServiceException {
        try{
            ClientePesquisa clientePesquisa = new ClientePesquisa(object);
            return inserir(ctx,clientePesquisa);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public ClientePesquisa inserirDandoFlush(String ctx, ClienteSintetico object) throws ServiceException {
        try{
            ClientePesquisa clientePesquisa = new ClientePesquisa(object);
            return getClientePesquisaDao().update(ctx, clientePesquisa);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public ClientePesquisa inserir(String ctx, JSONObject object) throws ServiceException {
        try{
            ClientePesquisa clientePesquisa = new ClientePesquisa(object);
            return inserir(ctx,clientePesquisa);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public ClientePesquisa inserirDandoFlush(String ctx, JSONObject object) throws ServiceException {
        try{
            ClientePesquisa clientePesquisa = new ClientePesquisa(object);
            return getClientePesquisaDao().update(ctx, clientePesquisa);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public void excluir(String ctx, ClientePesquisa object) throws ServiceException {
        try {
            getClientePesquisaDao().delete(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public void excluirPorMatricula(String ctx, Integer matricula) throws ServiceException {
        try {
            getClientePesquisaDao().deleteComParam(ctx, new String[]{"matricula"}, new Object[]{matricula});
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public ClientePesquisa alterar(String ctx, ClientePesquisa object) throws ServiceException {
        return null;
    }

    @Override
    public List<ClientePesquisa> consultar(String ctx, String filtro, PaginadorDTO paginadorDTO, Integer empresaId) {
        return null;
    }

    @Override
    public ClientePesquisa consultarPorMatricula(String ctx, Integer matricula) throws ServiceException {
        try {
            return getClientePesquisaDao().findObjectByAttribute(ctx, "matricula", matricula);
        } catch (Exception ex) {
            Uteis.logar(ex, ClienteSinteticoServiceImpl.class);
            throw new ServiceException(ex.getMessage());
        }
    }

    public ClientePesquisaDao getClientePesquisaDao() {
        return clientePesquisaDao;
    }

    public void setClientePesquisaDao(ClientePesquisaDao clientePesquisaDao) {
        this.clientePesquisaDao = clientePesquisaDao;
    }

    @Override
    public List<AlunoPesquisaResponseTO> listaAlunos(final String parametro, PaginadorDTO paginadorDTO, HttpServletRequest request, Integer empresaId) throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        try {
            paginadorDTO.setSort("nome,ASC");

            List<ClientePesquisa> alunos = getClientePesquisaDao().consultarClientesPesquisa(ctx, parametro, paginadorDTO, empresaId);
            List<AlunoPesquisaResponseTO> alunoResponseTOS = new ArrayList<>();

            for (ClientePesquisa cp : alunos) {
                cp.setUrlFoto(Aplicacao.obterUrlFotoDaNuvem(cp.getFotokey()));

                alunoResponseTOS.add(new AlunoPesquisaResponseTO(cp));
            }
            return alunoResponseTOS;
        } catch (Exception ex) {
            throw new ServiceException(AlunoExcecoes.ERRO_BUSCAR_ALUNOS, ex);
        }
    }
}
