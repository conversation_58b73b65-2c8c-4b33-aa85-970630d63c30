package br.com.pacto.service.impl.modalidade;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.aula.Modalidade;
import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.aluno.AlunoResponseTO;
import br.com.pacto.controller.json.base.SuperControle;
import br.com.pacto.controller.json.modalidade.CoresResponseTO;
import br.com.pacto.controller.json.modalidade.FiltroModalidadeJSON;
import br.com.pacto.controller.json.modalidade.ModalidadeResponseTO;
import br.com.pacto.controller.json.modalidade.ModalidadeTO;
import br.com.pacto.dao.intf.aula.ModalidadeDao;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.notificacao.excecao.AlunoExcecoes;
import br.com.pacto.service.impl.notificacao.excecao.ModalidadeExcecoes;
import br.com.pacto.service.intf.aula.AulaService;
import br.com.pacto.service.intf.empresa.EmpresaService;
import br.com.pacto.service.intf.modalidade.ModalidadeService;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.enumeradores.PaletaCoresEnum;
import br.com.pacto.util.impl.Ordenacao;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class ModalidadeServiceImpl implements ModalidadeService {

    @Autowired
    private ModalidadeDao modalidadeDao;

    @Autowired
    private SessaoService sessaoService;

    @Autowired
    private AulaService aulaService;

    @Autowired
    private EmpresaService empresaService;

    @Override
    public Modalidade inserir(String ctx, Modalidade object) throws ServiceException {
        try {
            return getModalidadeDao().insert(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public Modalidade alterar(String ctx, Modalidade object) throws ServiceException {
        try {
            return getModalidadeDao().update(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public void remover(String ctx, Modalidade object) throws ServiceException {
        try {
            getModalidadeDao().delete(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public Modalidade consultarPorModalidade(String ctx, Integer id) throws ServiceException{
        try {
            Modalidade modalidade;
            if (SuperControle.independente(ctx)) {
                modalidade = getModalidadeDao().findById(ctx, id);
            } else {
                modalidade = getModalidadeDao().findObjectByAttribute(ctx, "codigoZW", id);
            }
            return modalidade;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }
    @Override
    public List<CoresResponseTO> obterTodasCores() throws ServiceException {
        try {
            PaletaCoresEnum[] paletaCores = PaletaCoresEnum.values();
            List<CoresResponseTO> coresResponse = new ArrayList<>(paletaCores.length);
            for (PaletaCoresEnum cor : paletaCores) {
                if (!cor.name().startsWith("VERMELHO")) {
                    coresResponse.add(new CoresResponseTO(cor.getId(), cor.getDescricao(), cor.getCor()));
                }
            }
            return coresResponse;
        } catch (Exception e) {
            throw new ServiceException(ModalidadeExcecoes.ERRO_OBTER_CORES, e);
        }
    }

    @Override
    public ModalidadeResponseTO cadastroModalidade(ModalidadeTO modalidadeTO) throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        try {
            camposObrigatorios(modalidadeTO);
            validarCampos(modalidadeTO);
            Modalidade modalidade = hidratarModalidade(modalidadeTO, new Modalidade());
            if (modalidadeDao.exists(ctx, modalidade, "nome")) {
                throw new ServiceException(ModalidadeExcecoes.ERRO_MODALIDADE_DUPLICADO);
            }
            if (SuperControle.independente(ctx)) {
                inserir(ctx, modalidade);
            } else {
                throw new ServiceException(ModalidadeExcecoes.ERRO_ACAO_PROIBIDA);
            }
            return new ModalidadeResponseTO(modalidade, SuperControle.independente(ctx));
        } catch (Exception e) {
            throw new ServiceException(ModalidadeExcecoes.ERRO_INCLUIR_MODALIDADE, e);
        }
    }

    @Override
    public ModalidadeResponseTO detalhesModalidade(Integer id) throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        try {
            if(id == null || id < 1) {
                throw new ServiceException(ModalidadeExcecoes.ERRO_MODALIDADE_NAO_EXISTE);
            }
            Modalidade modalidade = consultarPorModalidade(ctx, id);
            return new ModalidadeResponseTO(modalidade, SuperControle.independente(ctx));
        } catch(ServiceException e){
            throw e;
        } catch (Exception e) {
            throw new ServiceException(ModalidadeExcecoes.ERRO_BUSCAR_MODALIDADE, e);
        }
    }

    @Override
    public List<ModalidadeResponseTO> listaModalidades(FiltroModalidadeJSON filtros, PaginadorDTO paginadorDTO, Integer empresaId) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            if (!StringUtils.isBlank(paginadorDTO.getSort())) {
                paginadorDTO.setSort(paginadorDTO.getSort());
            } else {
                paginadorDTO.setSort("nome,ASC");        }

            List<Modalidade> lista;
            if (SuperControle.independente(ctx)) {
                lista = getModalidadeDao().listarModadidades(ctx, filtros, paginadorDTO);
            } else {
                lista = listaModalidadesZW(ctx, filtros, paginadorDTO, empresaId);
            }
            List<ModalidadeResponseTO> listaRet = new ArrayList<>();
            if (lista != null) {
                for (Modalidade md : lista) {
                    listaRet.add(new ModalidadeResponseTO(md, SuperControle.independente(ctx)));
                }
            }
            return listaRet;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException(ModalidadeExcecoes.ERRO_lISTAR_MODALIDADES, e);
        }
    }

    private List<Modalidade> listaModalidadesZW(String ctx, FiltroModalidadeJSON filtros, PaginadorDTO paginadorDTO, Integer empresaZWId) throws ServiceException {
        try {
            List<Modalidade> modalidades = aulaService.obterModalidadesZW(ctx, empresaZWId, null);
            List<Modalidade> result = new ArrayList<>();
            if (!StringUtils.isBlank(filtros.getParametro()) && filtros.getNome()) {
                modalidades = Uteis.filtrarListaValores(modalidades, filtros.getParametro(), new String[]{"nome"});
            }

            if(paginadorDTO != null) {
                paginadorDTO.setQuantidadeTotalElementos(new Long(modalidades.size()));

                if (paginadorDTO.getSortMap() != null) {
                    for (Map.Entry<String, String> entry : paginadorDTO.getSortMap().entrySet()) {
                        if (entry.getValue().equals("ASC")) {
                            modalidades = Ordenacao.ordenarLista(modalidades, entry.getKey());
                        } else {
                            modalidades = Ordenacao.ordenarListaReverse(modalidades, entry.getKey());
                        }
                    }
                }
                if (paginadorDTO.getSize() < modalidades.size()) {
                    Long pointIndex = (paginadorDTO.getPage() > 0 ? (paginadorDTO.getPage() * paginadorDTO.getSize()) : paginadorDTO.getPage());
                    int i = 0;
                    for (Modalidade modalidade : modalidades) {
                        if (i >= pointIndex && result.size() < paginadorDTO.getSize()) {
                            result.add(modalidade);
                        }
                        i++;
                    }
                } else {
                    result.addAll(modalidades);
                }
            }

            return result;
        } catch (Exception e) {
            throw new ServiceException(ModalidadeExcecoes.ERRO_lISTAR_MODALIDADES);
        }
    }
    @Override
    public ModalidadeResponseTO alterarModalidade(Integer id, ModalidadeTO modalidadeTO) throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        try {
            camposObrigatorios(modalidadeTO);
            Modalidade modalidade = validarID(ctx, id);
            validarCampos(modalidadeTO);
            /**
             * Se a caso estiver integrado com o ZW não pode ser alterado o nome da modalidade, somente a cor
             */
            if (!SuperControle.independente(ctx)) {
                modalidadeTO.setNome(modalidade.getNome());
            }
            hidratarModalidade(modalidadeTO, modalidade);
            if (modalidadeDao.exists(ctx, modalidade, "nome") && SuperControle.independente(ctx)) {
                throw new ServiceException(ModalidadeExcecoes.ERRO_MODALIDADE_DUPLICADO);
            }
            alterar(ctx, modalidade);

            return new ModalidadeResponseTO(modalidade, SuperControle.independente(ctx));
        } catch (Exception e) {
            throw new ServiceException(ModalidadeExcecoes.ERRO_ALTERAR_MODALIDADE, e);
        }
    }

    @Override
    public void removerModalidade(Integer id) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            if (SuperControle.independente(ctx)) {
                Modalidade mod = validarID(ctx, id);
                remover(ctx, mod);
            } else {
                throw new ServiceException(ModalidadeExcecoes.ERRO_ACAO_PROIBIDA);
            }
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException(ModalidadeExcecoes.ERRO_EXCLUIR_MODALIDADE, e);
        }
    }
    private Modalidade validarID(String ctx, Integer id) throws Exception {
        if(id == null || id < 1){
            throw new ServiceException(ModalidadeExcecoes.ERRO_ID_NAO_INFORMADA);
        }
        Modalidade modalidade;
        if (SuperControle.independente(ctx)) {
            modalidade = consultarPorModalidade(ctx, id);
        } else {
            modalidade = modalidadeDao.findObjectByAttribute(ctx, "codigoZW", id);
        }
        if(modalidade == null) {
            throw new ServiceException(ModalidadeExcecoes.ERRO_MODALIDADE_NAO_EXISTE);
        }
        return modalidade;
    }

    private Modalidade hidratarModalidade(ModalidadeTO modalidadeTO, Modalidade modalidade) {
        modalidade.setNome(modalidadeTO.getNome());
        modalidade.setCor(PaletaCoresEnum.getFromId(modalidadeTO.getCorId()));
        return modalidade;
    }

    private void validarCampos(ModalidadeTO modalidadeTO) throws ServiceException{
        if(PaletaCoresEnum.getFromId(modalidadeTO.getCorId()) == null) {
            throw new ServiceException(ModalidadeExcecoes.ERRO_COR_NAO_EXISTE);
        }

    }

    private void camposObrigatorios(ModalidadeTO modalidadeTO) throws ServiceException {
        if(modalidadeTO.getNome() == null || modalidadeTO.getNome().trim().isEmpty()){
            throw new ServiceException(ModalidadeExcecoes.ERRO_NOME_NAO_INFORMADO);
        }
        if(modalidadeTO.getCorId() == null || modalidadeTO.getCorId() < 0){
            throw new ServiceException(ModalidadeExcecoes.ERRO_COR_NAO_INFORMADA);
        }
    }

    private ModalidadeDao getModalidadeDao() {
        return modalidadeDao;
    }

    public List<ModalidadeResponseTO> obterTodasModalidades(Integer empresaId, Boolean turma) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            List<Modalidade> modalidades;
            Empresa empresaZW;
            if (SuperControle.independente(ctx)) {
                empresaZW = empresaService.obterPorId(ctx, empresaId);

                StringBuilder where = new StringBuilder();
                Map<String, Object> params = new HashMap<>();
                where.append("WHERE obj.ativo is :ativo ");
                params.put("ativo", true);
                modalidades = modalidadeDao.findByParam(ctx, where, params);
            } else {
                empresaZW = empresaService.obterPorIdZW(ctx, empresaId);
                modalidades = aulaService.obterModalidadesZW(ctx, empresaZW.getCodZW(), turma);
            }
            List<ModalidadeResponseTO> listaRet = new ArrayList<>();
            if (!UteisValidacao.emptyList(modalidades)) {
                for (Modalidade md : modalidades) {
                    listaRet.add(new ModalidadeResponseTO(md, SuperControle.independente(ctx)));
                }
            }
            return listaRet;
        } catch (Exception e) {
            throw new ServiceException(ModalidadeExcecoes.ERRO_lISTAR_MODALIDADES);
        }
    }


}
