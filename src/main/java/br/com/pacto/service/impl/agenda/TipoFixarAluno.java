package br.com.pacto.service.impl.agenda;

public enum TipoFixarAluno {
    FIM_CONTRATO(1),
    DATA_DETERMINADA(2);

    private Integer codigo;

    TipoFixarAluno(Integer codigo){
        this.codigo = codigo;
    }

    public static TipoFixarAluno getTipo(Integer codigo){
        for (TipoFixarAluno tipo: TipoFixarAluno.values()){
            if (tipo.getCodigo().equals(codigo))
                return tipo;
        }
        return null;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

}
