package br.com.pacto.service.impl.notificacao.excecao;

import br.com.pacto.service.exception.ExcecaoSistema;

/**
 * Exceções para a camada de serviço de notificação
 *
 * <AUTHOR>
 * @since 16/07/2018
 */
public enum NotificacaoExcecoes implements ExcecaoSistema {

    USUARIO_NAO_ENCONTRADO("usuario_nao_encontrado", "O usuário informado não foi encontrado"),
    ERRO_BUSCAR_NOTIFICACOES("erro_buscar_notificacoes", "Ocorreu um erro ao procurar as notificações no sistema"),
    CONTEXTO_NAO_INFORMADO("contexto_nao_informado", "O contexto não foi informado"),
    USUARIO_NAO_INFORMADO("usuario_nao_informado", "O usuário não foi informado"),
    ID_NAO_INFORMADO("id_nao_informado", "O ID da notificação não foi informado"),
    NOTIFICACAO_NAO_ENCONTRADA("notificacao_nao_encontrada", "A notificação não foi encontrada no sistema"),
    ERRO_MARCAR_LIDA("erro_marcar_lida", "Ocorreu um erro ao tentar marcar a notificação como lida"),
    CLIENTE_NULO("cliente_sintetico_nulo", "O cliente sintético está nulo.");

    private String chave;
    private String descricao;

    NotificacaoExcecoes(String chave, String descricao) {
        this.chave = chave;
        this.descricao = descricao;
    }

    @Override
    public String getChaveExcecao() {
        return chave;
    }

    @Override
    public String getDescricaoExcecao() {
        return descricao;
    }
}
