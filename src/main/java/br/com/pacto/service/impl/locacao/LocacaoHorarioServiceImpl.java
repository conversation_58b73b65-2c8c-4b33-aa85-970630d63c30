package br.com.pacto.service.impl.locacao;

import br.com.pacto.bean.locacao.LocacaoHorario;
import br.com.pacto.bean.locacao.LocacaoPlayCanceladaFinalizada;
import br.com.pacto.dao.intf.locacao.LocacaoHorarioDao;
import br.com.pacto.dao.intf.locacao.LocacaoPlayCanceladaFinalizadaDao;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.locacao.LocacaoHorarioService;
import br.com.pacto.service.intf.usuario.UsuarioService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.test.context.ContextConfiguration;

import java.util.Date;
import java.util.HashMap;

@Service
@ContextConfiguration(locations = {"/applicationContext.xml"})
public class LocacaoHorarioServiceImpl implements LocacaoHorarioService {

    private final LocacaoHorarioDao locacaoHorarioDao;
    private final SessaoService sessaoService;
    private final UsuarioService usuarioService;
    private final LocacaoPlayCanceladaFinalizadaDao locacaoPlayCanceladaFinalizadaDao;

    @Autowired
    public LocacaoHorarioServiceImpl(LocacaoHorarioDao locacaoHorarioDao, SessaoService sessaoService, UsuarioService usuarioService, LocacaoPlayCanceladaFinalizadaDao locacaoPlayCanceladaFinalizadaDao) {
        this.locacaoHorarioDao = locacaoHorarioDao;
        this.sessaoService = sessaoService;
        this.usuarioService = usuarioService;
        this.locacaoPlayCanceladaFinalizadaDao = locacaoPlayCanceladaFinalizadaDao;
    }

    public LocacaoHorarioDTO findById(Integer id, String dia, Integer ambiente) throws ServiceException {
        try {
            Date diaLocacao = Uteis.getDate(dia, "yyyyMMdd");
            String ctx = sessaoService.getUsuarioAtual().getChave();
            LocacaoHorarioDTO dto = new LocacaoHorarioDTO();
            LocacaoHorario lh = locacaoHorarioDao.findById(ctx, id);
            dto.setNomeLocacao(lh.getLocacao().getNome());
            dto.setDiaSemana(lh.getDiaSemana());
            dto.setHoraInicio(lh.getHoraInicio());
            dto.setHoraFim(lh.getHoraFim());
            dto.setResponsavel(usuarioService.obterPorId(ctx, lh.getResponsavel()).getNome());

            LocacaoPlayCanceladaFinalizada isFinalizada = locacaoPlayCanceladaFinalizadaDao.findObjectByParam(ctx,
                    "SELECT obj FROM LocacaoPlayCanceladaFinalizada obj\n" +
                            "where obj.locacaoHorario.codigo = " + lh.getCodigo() + "\n" +
                            "AND obj.dia = '" + Uteis.getDataAplicandoFormatacao(diaLocacao, "yyyy-MM-dd") + "'\n" +
                            "AND obj.ambiente = " + ambiente + "\n" +
                            "AND obj.finalizada = true", new HashMap<>());

            LocacaoPlayCanceladaFinalizada isCancelada = locacaoPlayCanceladaFinalizadaDao.findObjectByParam(ctx,
                    "SELECT obj FROM LocacaoPlayCanceladaFinalizada obj\n" +
                            "WHERE obj.locacaoHorario.codigo = " + lh.getCodigo() + "\n" +
                            "AND obj.cancelada = true", new HashMap<>());

            dto.setFinalizada(isFinalizada != null);
            dto.setCancelada(isCancelada != null);

            return dto;
        } catch (Exception exception) {
            throw new ServiceException(exception.getMessage());
        }
    }

}
