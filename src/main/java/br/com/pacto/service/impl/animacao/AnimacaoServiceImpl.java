/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.impl.animacao;

import br.com.pacto.bean.animacao.Animacao;
import br.com.pacto.bean.animacao.NomeAnimacao;
import br.com.pacto.bean.animacao.TipoAnimacaoEnum;
import br.com.pacto.bean.atividade.AtividadeAnimacao;
import br.com.pacto.bean.atividade.AtividadeImagemUploadTO;
import br.com.pacto.bean.imagem.ImagemResponseTO;
import br.com.pacto.dao.intf.animacao.AnimacaoDao;
import br.com.pacto.dao.intf.atividade.AtividadeAnimacaoDao;
import br.com.pacto.dao.intf.atividade.NomeAnimacaoDao;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.impl.notificacao.excecao.CatalogoImagensExcecoes;
import br.com.pacto.service.intf.Validacao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.animacao.AnimacaoService;
import br.com.pacto.util.ViewUtils;
import br.com.pacto.util.impl.Ordenacao;

import java.awt.*;
import java.awt.image.BufferedImage;
import java.awt.image.ColorModel;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import br.com.pacto.controller.to.UploadedFile;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import servicos.integracao.admapp.AdmAppWSConsumer;
import servicos.integracao.admapp.client.Midia;
import servicos.integracao.admapp.client.TipoMidiaEnum;

import javax.imageio.ImageIO;

/**
 *
 * <AUTHOR>
 */
@Service
public class AnimacaoServiceImpl implements AnimacaoService {

    @Autowired
    private ViewUtils viewUtils;
    @Autowired
    private Validacao validacao;
    @Autowired
    private AnimacaoDao animacaoDao;
    @Autowired
    private AtividadeAnimacaoDao atividadeAnimacaoDao;
    @Autowired
    private NomeAnimacaoDao nomeAnimacaoDao;
    @Autowired
    private SessaoService sessaoService;

    public AtividadeAnimacaoDao getAtividadeAnimacaoDao() {
        return atividadeAnimacaoDao;
    }

    public void setAtividadeAnimacaoDao(AtividadeAnimacaoDao atividadeAnimacaoDao) {
        this.atividadeAnimacaoDao = atividadeAnimacaoDao;
    }

    public ViewUtils getViewUtils() {
        return viewUtils;
    }

    public void setViewUtils(ViewUtils viewUtils) {
        this.viewUtils = viewUtils;
    }

    public Validacao getValidacao() {
        return validacao;
    }

    public void setValidacao(Validacao validacao) {
        this.validacao = validacao;
    }

    public AnimacaoDao getAnimacaoDao() {
        return this.animacaoDao;
    }

    public void setAnimacaoDao(AnimacaoDao animacaoDao) {
        this.animacaoDao = animacaoDao;
    }

    @Override
    public Animacao alterar(final String ctx, Animacao object) throws ServiceException {
        try {
            return getAnimacaoDao().update(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public void excluir(final String ctx, Animacao object) throws ServiceException {
        try {
            getAnimacaoDao().delete(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public Animacao inserir(final String ctx, Animacao object) throws ServiceException {
        try {
            return getAnimacaoDao().insert(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public Animacao obterObjetoPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException {
        try {
            return getAnimacaoDao().findObjectByParam(ctx, query, params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public Animacao obterPorId(final String ctx, Integer id) throws ServiceException {
        try {
            return getAnimacaoDao().findById(ctx, id);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public List<Animacao> obterPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException {
        try {
            return getAnimacaoDao().findByParam(ctx, query, params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public List<Animacao> obterTodos(final String ctx) throws ServiceException {
        try {
            List<Animacao> animacoes = new ArrayList<Animacao>();
            List<Midia> l = AdmAppWSConsumer.obterMidias(ctx);
            if (l != null) {
                Map<String, String> nomesAnimacoes = consultarNomesAnimacoes(ctx);
                for (Midia midia : l) {
//                Uteis.logar(null, midia.getTitulo() + " -> " + EntityManagerFactoryService.getPropertyOAMD(ctx, Aplicacao.urlBaseMidias) + "/Large/Wide/" + midia.getNome());
                    Animacao animacao = new Animacao();
                    animacao.setTipo(midia.getTipo().equals(TipoMidiaEnum.IMAGEM) ? TipoAnimacaoEnum.IMAGEM : TipoAnimacaoEnum.VIDEO);
                    String nome = nomesAnimacoes.get(midia.getNome());
                    animacao.setTitulo(nome == null || nome.isEmpty() ? midia.getTitulo() : nome);
                    animacao.setUrl(midia.getNome());
                    animacoes.add(animacao);
                }
                Ordenacao.ordenarLista(animacoes, "titulo");
            }
            return animacoes;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public List<Animacao> obterPorParam(final String ctx, String query,
            Map<String, Object> params, int max, int index)
            throws ServiceException {
        try {
            return getAnimacaoDao().findByParam(ctx, query, params, max, index);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public Animacao obterAnimacaoPorNomeArquivo(String ctx, String nomeArquivo) throws ServiceException {
        try {
            String query = "select obj from Animacao obj where obj.url = :nomeArquivo";
            Map<String, Object> p = new HashMap<String, Object>();
            p.put("nomeArquivo", nomeArquivo);
            return getAnimacaoDao().findObjectByParam(ctx, query.toString(), p);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public void deleteAtividadeAnimacao(String ctx, Integer codigoAtividade, String urlsAnimacoes) throws ServiceException {
        try {
            String query = "select obj from AtividadeAnimacao obj where obj.fotoKey is null and obj.atividade.codigo = :codigoAtividade ";
            if (!urlsAnimacoes.isEmpty()) {
                query += "and obj.animacao.url not in (" + urlsAnimacoes + ")";
            }
            Map<String, Object> p = new HashMap<String, Object>();
            p.put("codigoAtividade", codigoAtividade);
            List<AtividadeAnimacao> animacoesExcluir = getAtividadeAnimacaoDao().findByParam(ctx, query, p);
            for (AtividadeAnimacao atvAn : animacoesExcluir) {
                getAtividadeAnimacaoDao().delete(ctx, atvAn);
            }
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public NomeAnimacao inserirNome(final String ctx, NomeAnimacao object) throws ServiceException {
        try {
            return nomeAnimacaoDao.insert(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }
    
    @Override
    public NomeAnimacao alterarNome(final String ctx, NomeAnimacao object) throws ServiceException {
        try {
            return nomeAnimacaoDao.update(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }
    
    @Override
    public NomeAnimacao consultarNomeAnimacao(final String ctx, final String url) throws ServiceException {
        try {
            Map<String, Object> params = new HashMap<String, Object>();
            params.put("url", url);
            return nomeAnimacaoDao.findObjectByParam(ctx, "SELECT obj FROM NomeAnimacao obj WHERE obj.url = :url", params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }
    
    @Override
    public Map<String, String> consultarNomesAnimacoes(final String ctx) throws ServiceException {
        try {
            List<NomeAnimacao> lista = nomeAnimacaoDao.findAll(ctx);
            Map<String, String> mapa = new HashMap<String, String>();
            for (NomeAnimacao na : lista) {
                mapa.put(na.getUrl(), na.getNome());
            }
            return mapa;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }
    
    @Override
    public void excluirNome(final String ctx, NomeAnimacao object) throws ServiceException {
        try {
            nomeAnimacaoDao.delete(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public byte[] redimensionarImagemGrande(UploadedFile uploadedFile, Integer limite) throws Exception {
        String formato = uploadedFile.getFileName().endsWith("jpeg") ? "jpeg" :
                        uploadedFile.getFileName().endsWith("png") ? "png" :
                        uploadedFile.getFileName().endsWith("gif") ? "gif"
                        : "jpg";
        InputStream in = new ByteArrayInputStream(uploadedFile.getContents());
        BufferedImage buf = ImageIO.read(in);

        boolean paisagem = buf.getWidth() > buf.getHeight();

        if ((paisagem && buf.getWidth() <= limite) || (!paisagem && buf.getHeight() <= limite)) {
            return uploadedFile.getContents();
        }

        int largura = paisagem ? limite : ((buf.getWidth() * limite)/buf.getHeight());
        int altura = paisagem ? ((buf.getHeight() * limite)/buf.getWidth()) : limite;

        Image image = ImageIO.read(new ByteArrayInputStream(uploadedFile.getContents()));
        return getBytes(limite, formato, buf, paisagem, largura, altura, image);
    }

    @Override
    public List<ImagemResponseTO> listarImagens() throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            List<Animacao> animacoes = obterTodos(ctx);
            List<ImagemResponseTO> ret = new ArrayList<>();
            for (Animacao animacao : animacoes) {
                ret.add(new ImagemResponseTO(animacao));
            }
            return ret;
        } catch (Exception e) {
            throw new ServiceException(CatalogoImagensExcecoes.ERRO_BUSCAR_IMAGENS, e);
        }
    }

    private BufferedImage createResizedCopy(Image originalImage,
                                    int scaledWidth, int scaledHeight,
                                    boolean preserveAlpha, int limite,
                                    int tamanhoOriginal)
    {
        int imageType = preserveAlpha ? BufferedImage.TYPE_INT_RGB : BufferedImage.TYPE_INT_ARGB;
        BufferedImage scaledBI = new BufferedImage(scaledWidth, scaledHeight, imageType);
        Graphics2D g = scaledBI.createGraphics();
        if (preserveAlpha) {
            g.setComposite(AlphaComposite.Src);
        }
        g.drawImage(originalImage, 0, 0, scaledWidth, scaledHeight, null);
        g.dispose();
        return scaledBI;
    }

    public Integer checarLarguraImagem(UploadedFile uploadedFile) throws Exception{
        InputStream in = new ByteArrayInputStream(uploadedFile.getContents());
        BufferedImage buf = ImageIO.read(in);
        return buf.getWidth();
    }

    public byte[] redimensionarImagemGrandeEndpoint(AtividadeImagemUploadTO atividadeImagemUploadTO, Integer limite) throws Exception {
        String formato = atividadeImagemUploadTO.getNome().endsWith("jpeg") ? "jpeg" :
                atividadeImagemUploadTO.getNome().endsWith("png") ? "png" :
                        atividadeImagemUploadTO.getNome().endsWith("gif") ? "gif" : "jpg";

        InputStream in = new ByteArrayInputStream(atividadeImagemUploadTO.getData());
        BufferedImage buf = ImageIO.read(in);

        boolean paisagem = buf.getWidth() > buf.getHeight();

        if ((paisagem && buf.getWidth() <= limite) || (!paisagem && buf.getHeight() <= limite)) {
            return atividadeImagemUploadTO.getData();
        }

        int largura = paisagem ? limite : ((buf.getWidth() * limite)/buf.getHeight());
        int altura = paisagem ? ((buf.getHeight() * limite)/buf.getWidth()) : limite;

        Image image = ImageIO.read(new ByteArrayInputStream(atividadeImagemUploadTO.getData()));
        return getBytes(limite, formato, buf, paisagem, largura, altura, image);
    }

    private byte[] getBytes(Integer limite, String formato, BufferedImage buf, boolean paisagem, int largura, int altura, Image image) throws IOException {
        BufferedImage resizedCopy = createResizedCopy(image, largura, altura, true, limite, paisagem ? buf.getWidth() : buf.getHeight());
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ImageIO.write(resizedCopy, formato, baos);
        baos.flush();
        byte[] imageInByte = baos.toByteArray();
        baos.close();
        return imageInByte;
    }


}
