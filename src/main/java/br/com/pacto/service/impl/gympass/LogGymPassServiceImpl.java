/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.impl.gympass;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.gympass.LogGymPass;
import br.com.pacto.bean.gympass.LogGymPassTO;
import br.com.pacto.dao.intf.gympass.LogGymPassDao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.notificacao.excecao.AtividadeExcecoes;
import br.com.pacto.service.intf.gympass.LogGymPassService;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.impl.Ordenacao;
import edu.emory.mathcs.backport.java.util.Collections;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 * User: Luiz Felipe
 * Date: 12/05/2020
 */
@Service
public class LogGymPassServiceImpl implements LogGymPassService {

    @Autowired
    private LogGymPassDao dao;
    @Autowired
    private SessaoService sessaoService;

    public LogGymPass incluir(final String ctx, String idTurma, String log, String json) throws ServiceException {
        try {
            LogGymPass obj = new LogGymPass();
            obj.setDataRegistro(Calendario.hoje());
            obj.setIdTurma(idTurma);
            obj.setLog(log + " - " + json);
            return dao.insert(ctx, obj);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<LogGymPass> ultimos(final String ctx) throws Exception{
        List<LogGymPass> all = dao.findAll(ctx);
        all = Ordenacao.ordenarLista(all, "dataRegistro");
        Collections.reverse(all);
        return all;
    }

    @Override
    public List<LogGymPassTO> listarLog(PaginadorDTO paginadorDTO) throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        int maxResults = 25;
        int indiceInicial = 0;
        if (paginadorDTO != null) {
            maxResults = paginadorDTO.getSize() == null ? 25 : paginadorDTO.getSize().intValue();
            indiceInicial = paginadorDTO.getPage() == null || paginadorDTO.getSize() == null ? indiceInicial : paginadorDTO.getPage().intValue() * paginadorDTO.getSize().intValue();

        }
        StringBuilder hql = new StringBuilder();
        StringBuilder where = new StringBuilder();
        Map<String, Object> param = new HashMap<String, Object>();

        hql.append("select obj from LogGymPass obj where obj.idTurma not like 'BOOKING%' and log not like '%Aula inativa Pacto e indisponivel Gympass%' ");
        if(UteisValidacao.emptyString(paginadorDTO.getSQLOrderByUse())){
            hql.append(" ORDER BY codigo DESC");
        }else{
            hql.append(paginadorDTO.getSQLOrderByUse());
        }


        List<LogGymPassTO> lista = null;
        try {
            if (paginadorDTO != null) {
                paginadorDTO.setQuantidadeTotalElementos(dao.countWithParam(ctx, "codigo", where, param).longValue());
            }
            List<LogGymPass> byParam = dao.findByParam(ctx, hql.toString(), param, maxResults, indiceInicial);
            lista = new ArrayList(){{
                for(LogGymPass b : byParam){
                    add(new LogGymPassTO(b));
                }
            }};
        } catch (Exception e) {
            throw new ServiceException(AtividadeExcecoes.ERRO_BUSCAR_ATIVIDADES, e);
        }
        paginadorDTO.setSize((long) maxResults);
        paginadorDTO.setPage((long) indiceInicial);
        return lista;

    }

    @Override
    public List<LogGymPass> listarLog(String bookingId) throws Exception {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        StringBuilder hql = new StringBuilder();
        Map<String, Object> param = new HashMap<String, Object>();
        hql.append(" select obj from LogGymPass obj ");
        hql.append(" WHERE obj.idTurma = 'BOOKING-").append(bookingId).append("'");
        hql.append(" ORDER BY codigo DESC");
        return dao.findByParam(ctx, hql.toString(), param);
    }
}
