package br.com.pacto.service.impl.notificacao.excecao;

import br.com.pacto.service.exception.ExcecaoSistema;

/**
 * Created by ulisses on 14/08/2018.
 */
public enum ProgramaTreinoExcecoes implements ExcecaoSistema {

    PROGRAMA_TREINO_NAO_ENCONTRADO("programa_treino_nao_encontrado", "Programa Treino informado não foi encontrado"),
    ERRO_BUSCAR_PROGRAMA_TREINO("erro_buscar_programa_treino", "Ocorreu um erro ao pesquisar o Programa Treino informado"),
    ERRO_PROGRAMA_TREINO_NAO_ENCONTRADO("erro_buscar_programa_treino", "erro_buscar_programa_treino"),
    ERRO_BUSCAR_PROGRAMAS_TREINO_PREDEFINIDOS("erro_buscar_programas_treino_predefinidos", "Ocorreu um erro ao pesquisar os Programas Treino Pré-definidos"),
    ERRO_MONTAR_PROGRAMA_TREINO("erro_montar_programa_treino", "Ocorreu um erro ao montar o Programa Treino informado"),
    ERRO_BUSCAR_ALUNO("erro_buscar_aluno", "Ocorreu um erro ao pesquisar o Aluno informado"),
    ERRO_BUSCAR_PROFESSOR("erro_buscar_professor", "Ocorreu um erro ao pesquisar o Professor informado"),
    ERRO_BUSCAR_PROFESSOR_SESSAO("erro_buscar_professor_SESSAO", "Ocorreu um erro ao pesquisar o Professor Sessão"),
    ERRO_INCLUIR_PROGRAMA_TREINO("erro_incluir_programa_treino", "Ocorreu um erro ao incluir o Programa Treino informado"),
    ERRO_ALTERAR_PROGRAMA_TREINO("erro_alterar_programa_treino", "Ocorreu um erro ao alterar o Programa Treino"),
    ERRO_COLOCAR_PROGRAMA_TREINO_COMO_PREDEFINIDO("erro_colocar_programa_treino_como_predefinido", "Ocorreu um erro ao colocar o Programa Treino como Pré-definido"),
    ERRO_EXCLUIR_PROGRAMA_TREINO("erro_excluir_programa_treino", "registro_esta_sendo_usado"),
    ERRO_PERIODO_INVALIDO("erro_periodo_invalido", "aluno_possui_treino_periodo_informado"),
    ERRO_CALCULAR_AULAS_PREVISTAS("erro_calcular_aulas_previstas", "Erro ao calcular aulas previstas"),
    ERRO_BUSCAR_ALUNO_ACOMPANHAMENTO("erro_buscar_aluno_acompanhamento", "Ocorreu um erro ao pesquisar o Acompanhamento do Aluno informado"),

    ERRO_PROGRAMA_TREINO_PREDEFINIDO_DUPLICADO("erro_programa_treino_predefinido_duplicado", "Já existe um programa treino pré-definido com o mesmo nome"),
    ERRO_TORNAR_PROGRAMA_TREINO_PREDEFINIDO("erro_tornar_programa_treino_predefinido", "Erro ao tentar tornar programa treino como  pré-definido"),
    ERRO_VALIDAR_PROGRAMA_CONFLITANTE("erro_validar_programa_conflitante", "Erro ao validar programa conflitante"),
    ERRO_FORMATO_DATA_INICIO_PROGRAMA_TREINO("erro_formato_data_inicio_programa_treino", "A data de início do programa está com formato incorreto"),
    ERRO_EXCLUIR_PROGRAMA_TREINO_REALIZADO("erro_excluir_programa_treino_realizado", "registro_esta_sendo_usado_treino_realizado"),
    VALIDACAO_PROGRAMA_PREDEFINIDO_JA_EXISTE("validacao_programa_predefinido_ja_existe", "Programa predefinido já existente com este nome."),
    FICHA_NAO_ENCONTRADA("erro_buscar_ficha", "Ficha informada não encontrada"),;

    private String chave;
    private String descricao;

    ProgramaTreinoExcecoes(String chave, String descricao) {
        this.chave = chave;
        this.descricao = descricao;
    }

    @Override
    public String getChaveExcecao() {
        return chave;
    }
    @Override
    public String getDescricaoExcecao() {
        return descricao;
    }
}
