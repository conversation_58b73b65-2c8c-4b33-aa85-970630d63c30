/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.impl.creditopersonal;

import br.com.pacto.bean.gestaopersonal.AulaPersonal;
import br.com.pacto.bean.gestaopersonal.CreditoPersonal;
import br.com.pacto.bean.gestaopersonal.TipoOperacaoPersonalEnum;
import br.com.pacto.bean.professor.ProfessorSintetico;
import br.com.pacto.dao.intf.creditopersonal.CreditoPersonalDao;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.intf.Validacao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.creditopersonal.CreditoPersonalService;
import br.com.pacto.service.intf.professor.ProfessorSinteticoService;
import br.com.pacto.util.UtilContext;
import br.com.pacto.util.ViewUtils;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import servicos.integracao.zw.IntegracaoCadastrosWSConsumer;

/**
 *
 * <AUTHOR>
 */
@Service
public class CreditoPersonalServiceImpl implements CreditoPersonalService {

    @Autowired
    private ViewUtils viewUtils;
    @Autowired
    private Validacao validacao;
    @Autowired
    private CreditoPersonalDao creditoPersonalDao;
    @Autowired
    private ProfessorSinteticoService professorService;

    public ViewUtils getViewUtils() {
        return viewUtils;
    }

    public void setViewUtils(ViewUtils viewUtils) {
        this.viewUtils = viewUtils;
    }

    public Validacao getValidacao() {
        return validacao;
    }

    public void setValidacao(Validacao validacao) {
        this.validacao = validacao;
    }

    public CreditoPersonalDao getCreditoPersonalDao() {
        return this.creditoPersonalDao;
    }

    public void setCreditoPersonalDao(CreditoPersonalDao creditoPersonalDao) {
        this.creditoPersonalDao = creditoPersonalDao;
    }

    public CreditoPersonal alterar(final String ctx, CreditoPersonal object) throws ServiceException {
        try {
            return getCreditoPersonalDao().update(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public void excluir(final String ctx, CreditoPersonal object) throws ServiceException {
        try {
            getCreditoPersonalDao().delete(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public CreditoPersonal inserir(final String ctx, CreditoPersonal object) throws ServiceException {
        try {
            return getCreditoPersonalDao().insert(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public CreditoPersonal obterObjetoPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException {
        try {
            return getCreditoPersonalDao().findObjectByParam(ctx, query, params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public CreditoPersonal obterPorId(final String ctx, Integer id) throws ServiceException {
        try {
            return getCreditoPersonalDao().findById(ctx, id);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<CreditoPersonal> obterPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException {
        try {
            return getCreditoPersonalDao().findByParam(ctx, query, params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<CreditoPersonal> obterPorParam(final String ctx, String query,
            Map<String, Object> params, int max, int index)
            throws ServiceException {
        try {
            return getCreditoPersonalDao().findByParam(ctx, query, params, max, index);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public List<CreditoPersonal> obterTodos(final String ctx) throws ServiceException {
        try {
            return getCreditoPersonalDao().findAll(ctx);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public Integer obterSaldoAtualPersonal(String ctx, Integer codigoPersonal, Date horaLimite ) throws ServiceException {
        try {
            final String query = "select obj from CreditoPersonal obj "
                    + "where obj.professor.codigo = :professor "
                    + " and dataLancamento < :hora"
                    + " order by dataLancamento DESC";
            HashMap<String, Object> p = new HashMap<String, Object>();
            p.put("professor", codigoPersonal);
            p.put("hora", horaLimite);
            List<CreditoPersonal> list = getCreditoPersonalDao().findByParam(ctx, query, p, 1,0);
            CreditoPersonal credito = list == null || list.isEmpty() ? null : list.get(0);
            return credito == null ? 0 : credito.getSaldoPersonal();
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }
    
    @Override
    public List<CreditoPersonal> obterListaSaldo(String ctx, Integer codigoProfessor, Date data) throws ServiceException {
        try {
            HashMap<String, Object> p = new HashMap<String, Object>();
            String query = "select obj from CreditoPersonal obj where obj.professor.codigo = :professor ";
            if(data == null){
                query += " order by dataLancamento DESC LIMIT 10";
            }else{
                query += " and dataLancamento > :data order by dataLancamento ASC";
                p.put("data", data);
            }
            p.put("professor", codigoProfessor);
            return getCreditoPersonalDao().findByParam(ctx, query, p);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }
    
    @Override
    public CreditoPersonal obterCredito(String ctx, AulaPersonal aula) throws ServiceException {
        try {
            final String query = "select obj from CreditoPersonal obj "
                    + "where obj.aulaPersonal.codigo = :aula ";
            HashMap<String, Object> p = new HashMap<String, Object>();
            p.put("aula", aula.getCodigo());
            return getCreditoPersonalDao().findObjectByParam(ctx, query, p);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }
    
    @Override
    public void estornarCreditos(final String key, final Integer reciboZW, final Integer vendaZW) throws ServiceException {
        try {
            CreditoPersonal creditoPersonal;
            if(vendaZW == null || vendaZW.equals(0)){
                creditoPersonal = obterCreditoRecibo(key, reciboZW);
            }else{
                creditoPersonal = obterCreditoVendaZW(key, vendaZW);
            }
            if(creditoPersonal == null || creditoPersonal.getProfessor() == null 
                    || creditoPersonal.getProfessor().getCodigo() == null || creditoPersonal.getProfessor().getCodigo() == 0){
                return;
            }
            ProfessorSintetico professor = professorService.obterPorId(key,creditoPersonal.getProfessor().getCodigo());
            
            Integer saldo = obterSaldoAtualPersonal(key,
                    professor.getCodigo(), creditoPersonal.getDataLancamento());
            List<CreditoPersonal> lista = obterListaSaldo(key, professor.getCodigo(), creditoPersonal.getDataLancamento());
            excluir(key, creditoPersonal);
            for (CreditoPersonal credito : lista) {
                if (credito.getTipoOperacao().equals(TipoOperacaoPersonalEnum.GASTO_CREDITOS)) {
                    saldo = saldo - credito.getUnidades();
                } else {
                    saldo = saldo + credito.getUnidades();
                }
                credito.setSaldoPersonal(saldo);
                alterar(key, credito);
            }
        } catch (ServiceException ex) {
            throw new ServiceException(ex.getMessage());
        }
    }
    
    @Override
    public CreditoPersonal obterCreditoRecibo(String ctx, Integer recibo) throws ServiceException {
        try {
            final String query = "select obj from CreditoPersonal obj "
                    + "where obj.reciboZW = :recibo ";
            HashMap<String, Object> p = new HashMap<String, Object>();
            p.put("recibo", recibo);
            return getCreditoPersonalDao().findObjectByParam(ctx, query, p);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }
    @Override
    public CreditoPersonal obterCreditoVendaZW(String ctx, Integer vendaZW) throws ServiceException {
        try {
            final String query = "select obj from CreditoPersonal obj "
                    + "where obj.vendaAvulsa = :vendaZW ";
            HashMap<String, Object> p = new HashMap<String, Object>();
            p.put("vendaZW", vendaZW);
            return getCreditoPersonalDao().findObjectByParam(ctx, query, p);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }
    
    
    @Override
    public CreditoPersonal inserirCreditos(final String key,final Integer colaborador,
             Integer unidadesCreditos,final Integer recibozw,final Integer vendaZW, 
             TipoOperacaoPersonalEnum tipo, Date dataExpiracao ) throws ServiceException{
        try {
            unidadesCreditos = unidadesCreditos < 0 ? unidadesCreditos*-1 : unidadesCreditos;
            ProfessorSintetico professor = professorService.consultarPorCodigoColaborador(key, colaborador);
            CreditoPersonal credito = new CreditoPersonal();
            credito.setProfessor(professor);
            credito.setUnidades(unidadesCreditos);
            credito.setDataLancamento(Calendario.hoje());
            credito.setReciboZW(recibozw);
            credito.setVendaAvulsa(vendaZW);
            credito.setEmpresaZW(professor.getEmpresa().getCodZW());
            credito.setDataExpiracao(dataExpiracao);
            credito.setExpirado(Boolean.FALSE);
            Integer saldoAtual = obterSaldoAtualPersonal(key, professor.getCodigo(), Calendario.hoje());
            credito.setSaldoPersonal(saldoAtual+unidadesCreditos);
            credito.setTipoOperacao(tipo);
            CreditoPersonal inserido = inserir(key, credito);
            return inserido;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public void verificarExpirados(String ctx, Integer codigoProfessor, Date data) throws ServiceException {
        try {
            HashMap<String, Object> p = new HashMap<String, Object>();
            String query = "select obj from CreditoPersonal obj where obj.professor.codigo = :professor ";
            if(data == null){
                query += " order by dataLancamento DESC LIMIT 10";
            }else{
                query += " and dataLancamento > :data order by dataLancamento ASC";
                p.put("data", data);
            }
            p.put("professor", codigoProfessor);
            List<CreditoPersonal> expirados = getCreditoPersonalDao().findByParam(ctx, query, p);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }
    @Override
    public void expirarCreditos(final String ctx, final Date data) throws ServiceException {
        try {
            Uteis.logar(null, "CREDITOS_PERSONAL - Vou consultar os creditos a serem expirados  da chave -> " 
                    + ctx);
            
            List<Integer> professores = getCreditoPersonalDao().listOfObjects(ctx, "SELECT professor_codigo FROM (\n" +
                            "SELECT MAX(dataexpiracao) as maxdata, professor_codigo FROM creditopersonal cp\n" +
                            "WHERE dataexpiracao IS NOT NULL \n" +
                            " and not exists(select codigo from creditopersonal where datalancamento > cp.datalancamento and professor_codigo = cp.professor_codigo and recibozw is not null) " +
                            " GROUP BY professor_codigo) AS t WHERE maxdata < '"+
                            Uteis.getDataAplicandoFormatacao(Calendario.hoje(), "yyyy-MM-dd")+
                            "'");
            for(Integer professor : professores){
                Integer saldo = obterSaldoAtualPersonal(ctx, professor, data);
                if(saldo > 0){
                    Uteis.logar(null, "Expirando créditos do personal "+professor);
                    ProfessorSintetico professorSintetico = professorService.obterPorId(ctx, professor);
                    CreditoPersonal credito = new CreditoPersonal();
                    credito.setProfessor(professorSintetico);
                    credito.setUnidades(saldo);
                    credito.setDataLancamento(Calendario.hoje());
                    credito.setEmpresaZW(professorSintetico.getEmpresa().getCodZW());
                    credito.setExpirado(Boolean.FALSE);
                    credito.setSaldoPersonal(0);
                    credito.setTipoOperacao(TipoOperacaoPersonalEnum.CREDITOS_EXPIRADOS);
                    inserir(ctx, credito);
                    final String url = Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebInteg);
                    IntegracaoCadastrosWSConsumer integracaoWS = (IntegracaoCadastrosWSConsumer) UtilContext.getBean(IntegracaoCadastrosWSConsumer.class);
                    integracaoWS.atualizarSaldoPersonal(url, ctx, professorSintetico.getCodigoColaborador(), credito.getSaldoPersonal());
                    
                }
            }
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }
}