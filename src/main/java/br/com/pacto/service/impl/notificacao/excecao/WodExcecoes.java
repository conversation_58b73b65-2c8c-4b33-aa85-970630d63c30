package br.com.pacto.service.impl.notificacao.excecao;

import br.com.pacto.service.exception.ExcecaoSistema;

/**
 * Created by ulisses on 28/08/2018.
 */
public enum WodExcecoes implements ExcecaoSistema {

    WOD_NAO_ENCONTRADO("wod_nao_encontrado", "Wod informado não encontrado"),
    ATIVIDADE_NAO_ENCONTRADA("atividade_nao_encontrada", "Atividade informada não encontrada"),
    APARELHO_NAO_ENCONTRADO("aparelho_nao_encontrado", "Aparelho informado não encontrado"),
    TIPO_WOD_NAO_ENCONTRADO("tipo_wod_nao_encontrado", "Tipo Wod informado não encontrado"),
    IMAGEM_WOD_INVALIDA("imagem_wod_invalida", "Imagem Wod informada é inválida"),
    ERRO_BUSCAR_WODS("erro_buscar_wods", "Ocorreu um erro ao buscar os Wods"),
    ERRO_BUSCAR_WOD("erro_buscar_wod", "Ocorreu um erro ao buscar o Wod informado"),
    ERRO_INCLUIR_WOD("erro_incluir_wod", "Ocorreu um erro ao incluir o Wod informado"),
    ERRO_ALTERAR_WOD("erro_alterar_wod", "Ocorreu um erro ao alterar o Wod informado"),
    ERRO_EXCLUIR_WOD("erro_excluir_wod", "Ocorreu um erro ao excluir o Wod informado"),
    ERRO_DATA_SUPERIOR_ATUAL("ERRO_DATA_SUPERIOR_ATUAL", "Data informada, não pode ser superior a data atual"),

    VALIDACAO_NOME_EXISTE("validacao_nome_existe", "registro_duplicado"),
    ERRO_IMPORTAR_WOD_CROSSFIT("erro_importar_wod_crossfit", "Ocorreu um erro ao importar o Wod do Crossfit"),
    ERRO_NAO_ENCONTRADO_WOD_DO_DIA("erro_nao_encontrado_wod_do_dia", "Não foi encontrado Wod do dia especificado no site oficial da CrossFit."),
    ERRO_NAO_ENCONTRADO_WOD_FRANQUIA("erro_nao_encontrado_wod_franquia", "Não foram encontrados nenhum WOD na chave franqueadora para ser importados.");

    private String chave;
    private String descricao;

    WodExcecoes(String chave, String descricao) {
        this.chave = chave;
        this.descricao = descricao;
    }

    @Override
    public String getChaveExcecao() {
        return chave;
    }
    @Override
    public String getDescricaoExcecao() {
        return descricao;
    }

}
