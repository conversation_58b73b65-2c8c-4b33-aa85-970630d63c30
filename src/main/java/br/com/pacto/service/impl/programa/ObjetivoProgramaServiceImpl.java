/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.pacto.service.impl.programa;

import br.com.pacto.bean.programa.ObjetivoPrograma;
import br.com.pacto.dao.intf.programa.ObjetivoProgramaDao;
import br.com.pacto.service.intf.Validacao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.programa.ObjetivoProgramaService;
import br.com.pacto.util.ViewUtils;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



/**
 *
 * <AUTHOR>
 */
@Service
public class ObjetivoProgramaServiceImpl implements ObjetivoProgramaService {

    @Autowired
    private ViewUtils viewUtils;
    @Autowired
    private Validacao validacao;
    @Autowired
    private ObjetivoProgramaDao objetivoProgramaDao;

    public ViewUtils getViewUtils() {
        return viewUtils;
    }

    public void setViewUtils(ViewUtils viewUtils) {
        this.viewUtils = viewUtils;
    }

    public Validacao getValidacao() {
        return validacao;
    }

    public void setValidacao(Validacao validacao) {
        this.validacao = validacao;
    }

    public ObjetivoProgramaDao getObjetivoProgramaDao(){
        return this.objetivoProgramaDao;
    }

    public void setObjetivoProgramaDao(ObjetivoProgramaDao objetivoProgramaDao){
       this.objetivoProgramaDao = objetivoProgramaDao; 
    }


    public ObjetivoPrograma alterar(final String ctx, ObjetivoPrograma object) throws ServiceException {
        try {
            return getObjetivoProgramaDao().update(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public void excluir(final String ctx, ObjetivoPrograma object) throws ServiceException {
        try {
            getObjetivoProgramaDao().delete(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public ObjetivoPrograma inserir(final String ctx, ObjetivoPrograma object) throws ServiceException {
        try {
            return getObjetivoProgramaDao().insert(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public ObjetivoPrograma obterObjetoPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException {
        try {
            return getObjetivoProgramaDao().findObjectByParam(ctx, query, params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public ObjetivoPrograma obterPorId(final String ctx, Integer id) throws ServiceException {
        try {
            return getObjetivoProgramaDao().findById(ctx, id);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<ObjetivoPrograma> obterPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException {
        try {
            return getObjetivoProgramaDao().findByParam(ctx, query, params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<ObjetivoPrograma> obterPorParam(final String ctx, String query,
            Map<String, Object> params, int max, int index)
            throws ServiceException {
        try {
            return getObjetivoProgramaDao().findByParam(ctx, query, params, max, index);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<ObjetivoPrograma> obterTodos(final String ctx) throws ServiceException {
        try {
            return getObjetivoProgramaDao().findAll(ctx);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    }