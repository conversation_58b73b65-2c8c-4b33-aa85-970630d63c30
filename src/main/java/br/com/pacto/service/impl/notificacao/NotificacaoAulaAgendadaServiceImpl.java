package br.com.pacto.service.impl.notificacao;

import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.notificacao.NotificacaoAulaAgendada;
import br.com.pacto.controller.json.notificacao.PushMobileRunnable;
import br.com.pacto.dao.intf.cliente.ClienteSinteticoDao;
import br.com.pacto.dao.intf.notificacao.NotificacaoAulaAgendadaDao;
import br.com.pacto.service.intf.notificacao.NotificacaoAulaAgendadaService;
import br.com.pacto.util.UteisValidacao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;


@Service
public class NotificacaoAulaAgendadaServiceImpl implements NotificacaoAulaAgendadaService {
    @Autowired
    NotificacaoAulaAgendadaDao notificacaoAulaAgendadaDao;
    @Autowired
    ClienteSinteticoDao clienteSinteticoDao;


    @Override
    public void salvaNotificacaoAulaAgendada(String ctx, ClienteSintetico cliente, Integer codigoHorarioTurma, String refPush, Date data) throws Exception {
        try {
            if (!UteisValidacao.emptyString(refPush)) {
                NotificacaoAulaAgendada notificacao = new NotificacaoAulaAgendada();
                notificacao.setCancelada(false);
                notificacao.setCliente(cliente);
                notificacao.setHorario(codigoHorarioTurma);
                notificacao.setDia(data);
                notificacao.setRefPush(refPush);

                notificacaoAulaAgendadaDao.insert(ctx, notificacao);
                System.out.println("Notificação agendada registrada no banco de dados.");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void cancelaNotificacaoAulaAgendada(String ctx, ClienteSintetico cliente, Integer codigoHorarioTurma, Date data) throws Exception {
        try {
            List<NotificacaoAulaAgendada> notificacao = notificacaoAulaAgendadaDao.findByClienteHorarioDia(ctx, cliente, codigoHorarioTurma, data, false);

            for (NotificacaoAulaAgendada notificacaoAulaAgendada : notificacao) {
                PushMobileRunnable.cancelarNotificacaoAulaAgendadaAluno(notificacaoAulaAgendada.getRefPush());

                notificacaoAulaAgendada.setCancelada(true);
                notificacaoAulaAgendadaDao.update(ctx, notificacaoAulaAgendada);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
