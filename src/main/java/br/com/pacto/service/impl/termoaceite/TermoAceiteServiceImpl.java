package br.com.pacto.service.impl.termoaceite;

import br.com.pacto.controller.json.termoaceite.TermoAceiteAssinaturaDTO;
import br.com.pacto.controller.json.termoaceite.TermoAceiteDTO;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.conexao.ConexaoZWServiceImpl;
import br.com.pacto.service.intf.cliente.ClienteSinteticoService;
import br.com.pacto.service.intf.conexao.ConexaoZWService;
import br.com.pacto.service.intf.termoaceite.TermoAceiteService;
import br.com.pacto.util.UteisValidacao;
import org.postgresql.util.PSQLException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.sql.Date;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

@Service
public class TermoAceiteServiceImpl implements TermoAceiteService {

    @Autowired
    private SessaoService sessaoService;
    @Autowired
    private ConexaoZWService conexaoZWService;
    @Autowired
    private ClienteSinteticoService clienteService;


    @Override
    public void save(String termo) throws Exception {
        String chave = sessaoService.getUsuarioAtual().getChave();

        StringBuilder sql = new StringBuilder();
        sql.append("INSERT INTO termoaceite (descricao) VALUES (?)");

        try (Connection connection = conexaoZWService.conexaoZw(chave);
             PreparedStatement stm = connection.prepareStatement(sql.toString());) {
            Integer i = 1;
            stm.setString(i, termo);
            stm.execute();
        }
    }

    public TermoAceiteDTO encontraTermoPorCodigo(Integer codTermo) {
        String chave = sessaoService.getUsuarioAtual().getChave();
        TermoAceiteDTO termoAceiteDTO = new TermoAceiteDTO();
        try (Connection conZW = conexaoZWService.conexaoZw(chave)) {

            try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta("SELECT * FROM termoaceite WHERE codigo = " + codTermo, conZW)) {
                if (rs.next()) {
                    termoAceiteDTO.setCodigo(rs.getInt("codigo"));
                    termoAceiteDTO.setDescricao(rs.getString("descricao"));
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return termoAceiteDTO;
    }

    @Override
    public void saveAssinatura(String ip, String nome, String cpf, String data, Integer termo, String email, Integer codigoMatricula) throws Exception {
        String chave = sessaoService.getUsuarioAtual().getChave();

        if (encontraTermoPorCodigo(termo).getCodigo() == null) {
            throw new ServiceException("Não foi possível encontrar o termo de aceite informado.");
        }

        if (!matriculaExiste(sessaoService.getUsuarioAtual().getChave(), codigoMatricula)) {
            throw new ServiceException("Não foi possível encontrar o cliente informado.");
        }

//        SOLICITAÇÃO JOÃO PAULO PARA COMENTAR AS VALIDAÇÕES A SEGUIR
//        if (!UteisValidacao.emptyString(cpf) && !UteisValidacao.validaCPF(cpf)) {
//            throw new ServiceException("O CPF informado não é válido.");
//        }
//        if (!UteisValidacao.emptyString(email) && !UteisValidacao.validaEmail(email)) {
//            throw new ServiceException("O email informado não é válido.");
//        }

        long timestampMillis = Long.parseLong(data);
        Timestamp timestamp = new Timestamp(timestampMillis);

        StringBuilder sql = new StringBuilder();
        sql.append("INSERT INTO termoaceiteassinatura (data, nome, cpf, termoaceite, ip, email, codigomatricula) VALUES (?, ?,?,?,?,?,?)");

        try (Connection connection = conexaoZWService.conexaoZw(chave);
             PreparedStatement stm = connection.prepareStatement(sql.toString());) {
            Integer i = 1;
            stm.setTimestamp(i++, timestamp);
            stm.setString(i++, nome);
            stm.setString(i++, cpf);
            stm.setInt(i++, termo);
            stm.setString(i++, ip);
            stm.setString(i++, email);
            stm.setInt(i++, codigoMatricula);
            stm.execute();
        } catch (PSQLException e) {
            if (e.getMessage().contains("uk_codigomatricula_termoaceite")) {
                throw new ServiceException("O termo já foi assinado por esse aluno.");
            }
            throw e;
        }
    }

    private boolean matriculaExiste(String chave, Integer codigoMatricula) {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * FROM cliente WHERE codigomatricula = ?");

        try (Connection connection = conexaoZWService.conexaoZw(chave);
             PreparedStatement stm = connection.prepareStatement(sql.toString());) {
            Integer i = 1;
            stm.setInt(i++, codigoMatricula);
            ResultSet rs = stm.executeQuery();
            if(rs.next()){
                return true;
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return false;
    }

    @Override
    public List<TermoAceiteDTO> findAll() throws Exception {
        String chave = sessaoService.getUsuarioAtual().getChave();
        List<TermoAceiteDTO> termoAceiteDTOS = new ArrayList<>();
        try (Connection conZW = conexaoZWService.conexaoZw(chave)) {
            try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta("SELECT * FROM termoaceite", conZW)) {
                while (rs.next()) {
                    TermoAceiteDTO termoAceiteDTO = new TermoAceiteDTO();
                    termoAceiteDTO.setCodigo(rs.getInt("codigo"));
                    termoAceiteDTO.setDescricao(rs.getString("descricao"));
                    termoAceiteDTOS.add(termoAceiteDTO);
                }
            }
        }
        return termoAceiteDTOS;
    }

    @Override
    public List<TermoAceiteAssinaturaDTO> findAllAssinados() throws Exception {
        String chave = sessaoService.getUsuarioAtual().getChave();
        List<TermoAceiteAssinaturaDTO> termoAceiteAssinaturaDTOS = new ArrayList<>();
        SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");
        try (Connection conZW = conexaoZWService.conexaoZw(chave)) {
            try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta("SELECT * FROM termoaceiteassinatura", conZW)) {
                while (rs.next()) {
                    TermoAceiteAssinaturaDTO termoAceiteAssinaturaDTO = new TermoAceiteAssinaturaDTO();
                    termoAceiteAssinaturaDTO.setCodigo(rs.getInt("codigo"));
                    termoAceiteAssinaturaDTO.setCpf(rs.getString("cpf"));
                    termoAceiteAssinaturaDTO.setIp(rs.getString("ip"));
                    termoAceiteAssinaturaDTO.setNome(rs.getString("nome"));
                    termoAceiteAssinaturaDTO.setData(sdf.format(rs.getTimestamp("data")));
                    termoAceiteAssinaturaDTO.setEmail(rs.getString("email"));
                    termoAceiteAssinaturaDTO.setCodigoMatricula(rs.getInt("codigomatricula"));
                termoAceiteAssinaturaDTOS.add(termoAceiteAssinaturaDTO);}
            }
        }
        return termoAceiteAssinaturaDTOS;
    }

    @Override
    public List<TermoAceiteAssinaturaDTO> findByCodigoMatricula(Integer codigoMatricula) throws Exception {
        String chave = sessaoService.getUsuarioAtual().getChave();
        if (UteisValidacao.emptyNumber(codigoMatricula)) {
            throw new ServiceException("O código de matrícula informado não é válido.");
        }
        List<TermoAceiteAssinaturaDTO> termoAceiteAssinaturaDTOS = new ArrayList<>();
        SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");
        try (Connection conZW = conexaoZWService.conexaoZw(chave)) {
            try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta("SELECT * FROM termoaceiteassinatura where codigomatricula = " + codigoMatricula, conZW)) {
                while (rs.next()) {
                    TermoAceiteAssinaturaDTO termoAceiteAssinaturaDTO = new TermoAceiteAssinaturaDTO();
                    termoAceiteAssinaturaDTO.setCodigo(rs.getInt("codigo"));
                    termoAceiteAssinaturaDTO.setCpf(rs.getString("cpf"));
                    termoAceiteAssinaturaDTO.setIp(rs.getString("ip"));
                    termoAceiteAssinaturaDTO.setNome(rs.getString("nome"));
                    termoAceiteAssinaturaDTO.setData(sdf.format(rs.getTimestamp("data")));
                    termoAceiteAssinaturaDTO.setEmail(rs.getString("email"));
                    termoAceiteAssinaturaDTO.setTermo(encontraTermoPorCodigo(rs.getInt("termoaceite")));
                termoAceiteAssinaturaDTO.setCodigoMatricula(rs.getInt("codigomatricula"));
                    termoAceiteAssinaturaDTOS.add(termoAceiteAssinaturaDTO);
                }
            }
        }
        return termoAceiteAssinaturaDTOS;
    }
}

