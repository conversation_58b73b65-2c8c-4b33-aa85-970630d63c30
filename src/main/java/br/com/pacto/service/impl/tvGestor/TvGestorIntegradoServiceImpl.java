package br.com.pacto.service.impl.tvGestor;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.aula.TipoAulaCheiaOrigemEnum;
import br.com.pacto.controller.json.tvGestor.dto.AlunoDTO;
import br.com.pacto.controller.json.tvGestor.dto.BiTvGestorDTO;
import br.com.pacto.controller.json.tvGestor.dto.FiltroTvGestorJSON;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.notificacao.excecao.AgendaExcecoes;
import br.com.pacto.service.intf.agendatotal.AgendaTotalService;
import br.com.pacto.service.intf.agendatotal.TVGestorService;
import br.com.pacto.util.bean.AgendaTotalTO;
import br.com.pacto.util.bean.AgendadoTO;
import br.com.pacto.util.bean.FiltrosAgendaTO;
import br.com.pacto.util.impl.Ordenacao;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.util.*;

@Service
public class TvGestorIntegradoServiceImpl extends TvGestorServiceImpl {
    @Autowired
    private TVGestorService tvGestorService;
    @Autowired
    private AgendaTotalService agendaTotalService;

    @Override
    public Map<String, Map<String, BiTvGestorDTO>> biSemana(String chave, Integer empresa, FiltroTvGestorJSON filtro) throws ServiceException {
        try {
            Date dataInicio = Uteis.somarDias(Calendario.getDataComHoraZerada(Calendario.hoje()), -0);
            Date dataFim = Uteis.somarDias(Calendario.fimDoDia(Calendario.hoje()), +6);

            Map<String, Map<String, BiTvGestorDTO>> dados = getBiPorDiaHora(dataInicio, dataFim);

            Map<String,AgendaTotalTO> agendamentosMap = montarAgendamentos(chave, empresa, dataInicio, dataFim);
            Map<String, List<AgendadoTO>> agendadosMap = montarAgendados(chave, empresa, agendamentosMap, dataInicio, dataFim);

            preencherEsperadosDiaHora(agendamentosMap, dados, filtro);

            List<AgendadoTO> todosAlunos = tvGestorService.obterAcessaram(chave, empresa, agendamentosMap, agendadosMap, new ArrayList<>(), getFiltrosAgendaTO(filtro), null);
            Map<Date, Map<Integer, Map<Integer, AgendadoTO>>> mapaAcessaram = montarCompareceuDiaHora(todosAlunos);

            preencherComparecidosDiaHora(mapaAcessaram, dados);

            return dados;
        }catch (Exception e){
            throw new ServiceException(AgendaExcecoes.ERRO_CONSULTAR_AGENDAMENTO, e);
        }
    }

    @Override
    public Map<String, BiTvGestorDTO> biPeriodo(String chave, Integer empresa, FiltroTvGestorJSON filtro) throws ServiceException {
        try {
            Date dataInicio = Uteis.somarDias(Calendario.hoje(), -29);
            Date dataFim = Uteis.somarDias(Calendario.hoje(), +0);

            Map<String, BiTvGestorDTO> dados = getBiPorDia(dataInicio, dataFim);

            Map<String,AgendaTotalTO> agendamentosMap = montarAgendamentos(chave, empresa, dataInicio, dataFim);
            Map<String, List<AgendadoTO>> agendadosMap = montarAgendados(chave, empresa, agendamentosMap, dataInicio, dataFim);

            preencherEsperadosDia(agendamentosMap, dados, filtro);

            List<AgendadoTO> todosAlunos = tvGestorService.obterAcessaram(chave, empresa, agendamentosMap, agendadosMap, new ArrayList<>(), getFiltrosAgendaTO(filtro), null);
            Map<Date, Map<Integer, Map<Integer, AgendadoTO>>> mapaAcessaram = montarCompareceuDiaHora(todosAlunos);

            preencherComparecidosDia(mapaAcessaram, dados);

            return dados;
        }catch (Exception e){
            throw new ServiceException(AgendaExcecoes.ERRO_CONSULTAR_AGENDAMENTO, e);
        }
    }

    private Map<String, List<AgendadoTO>> montarAgendados(String chave, Integer empresa, Map<String, AgendaTotalTO> agendamentosMap, Date dataInicio, Date dataFim) throws Exception {
        return agendaTotalService.montarMapaAgendados(chave, dataInicio,dataFim, empresa,
                new ArrayList<>(agendamentosMap.values()),true);
    }

    private FiltrosAgendaTO getFiltrosAgendaTO(FiltroTvGestorJSON filtro) {
        FiltrosAgendaTO filtrosAgendaTO = new FiltrosAgendaTO();
        filtrosAgendaTO.setAmbientesSelecionadosInteger(filtro.getAmbienteIds());
        filtrosAgendaTO.setProfessoresSelecionadosCodsInteger(filtro.getProfessorIds());
        filtrosAgendaTO.setTiposSelecionadosCodsInteger(filtro.getModalidadeIds());
        return filtrosAgendaTO;
    }

    private boolean filtrar(FiltroTvGestorJSON filtros, AgendaTotalTO agendamento){
        return filtros == null
                || ((filtros.getAmbienteIds().isEmpty() || filtros.getAmbienteIds().contains(agendamento.getCodigoLocal()))
                && (filtros.getModalidadeIds().isEmpty() || filtros.getModalidadeIds().contains(agendamento.getCodigotipo()))
                && (filtros.getProfessorIds().isEmpty() || filtros.getProfessorIds().contains(agendamento.getCodigoResponsavel())));
    }

    private Map<Date, Map<Integer, Map<Integer, AgendadoTO>>> montarCompareceuDiaHora(List<AgendadoTO> todosalunos){
        Map<Date, Map<Integer, Map<Integer, AgendadoTO>>> mapaAcessaram = new HashMap<>();
        for (AgendadoTO ag : todosalunos) {
            Map<Integer, Map<Integer, AgendadoTO>> horaAgendados = (Map<Integer, Map<Integer, AgendadoTO>>) Uteis.iniciarMapa(mapaAcessaram, Calendario.getDataComHoraZerada(ag.getAgendamento().getStartDate()), new HashMap<Integer, Map<Integer, AgendadoTO>>());
            Map<Integer, AgendadoTO> agendados = (Map<Integer, AgendadoTO>) Uteis.iniciarMapa(horaAgendados, Uteis.gethoraHH(ag.getAgendamento().getStartDate()), new HashMap<Integer, AgendadoTO>());
            agendados.put(ag.getCodigoCliente(), ag);
        }
        return mapaAcessaram;
    }

    private Map<String,AgendaTotalTO> montarAgendamentos(String chave, Integer empresa, Date dataInicio, Date dataFim) throws Exception{
        Map<String,AgendaTotalTO> agendamentosMap = agendaTotalService.montarAgenda(chave,
                dataInicio,dataFim,empresa,0,TipoAulaCheiaOrigemEnum.TODAS, null, null);

        agendaTotalService.montarMapaAgendados(chave,dataInicio,dataFim,empresa,
                new ArrayList<>(agendamentosMap.values()),true);

        return agendamentosMap;
    }

    private void preencherEsperadosDia(Map<String, AgendaTotalTO> agendamentosMap, Map<String, BiTvGestorDTO> dados, FiltroTvGestorJSON filtro) {
        for(String key : agendamentosMap.keySet()){
            AgendaTotalTO agenda = agendamentosMap.get(key);
            if(filtrar(filtro, agenda)) {
                if (agenda.getNrVagasPreenchidas() > 0) {
                    String data = new SimpleDateFormat("yyyyMMdd").format(agenda.getStartDate());
                    BiTvGestorDTO tvGestorDTO = dados.get(data);
                    if (tvGestorDTO == null) {
                        tvGestorDTO = new BiTvGestorDTO();
                    }
                    tvGestorDTO.setEsperado(tvGestorDTO.getEsperado() + agenda.getNrVagasPreenchidas());
                    dados.put(data, tvGestorDTO);
                }
            }
        }
    }

    private void preencherEsperadosDiaHora(Map<String, AgendaTotalTO> agendamentosMap, Map<String, Map<String, BiTvGestorDTO>> dados, FiltroTvGestorJSON filtro) {
        for(String key : agendamentosMap.keySet()){
            AgendaTotalTO agenda = agendamentosMap.get(key);
            if(filtrar(filtro, agenda)) {
                if (agenda.getNrVagasPreenchidas() > 0) {
                    Map<String, BiTvGestorDTO> dadosHora = dados.get(Calendario.getData(agenda.getStartDate(), "yyyyMMdd"));
                    if(dadosHora != null){
                        String hora = String.valueOf(Integer.parseInt(Calendario.getData(agenda.getStartDate(), "HH")));
                        BiTvGestorDTO tvGestorDTO = dadosHora.get(hora);
                        if (tvGestorDTO == null) {
                            tvGestorDTO = new BiTvGestorDTO();
                        }
                        tvGestorDTO.setEsperado(tvGestorDTO.getEsperado() + agenda.getNrVagasPreenchidas());
                        dadosHora.put(hora, tvGestorDTO);
                        BiTvGestorDTO tvGestorTotal = dadosHora.get("total");
                        tvGestorTotal.setEsperado(tvGestorTotal.getEsperado() + agenda.getNrVagasPreenchidas());
                        dadosHora.put("total", tvGestorTotal);
                    }
                }
            }
        }
    }

    private void preencherComparecidosDia(Map<Date, Map<Integer, Map<Integer, AgendadoTO>>> mapaDiaAcessaram, Map<String, BiTvGestorDTO> dados) {
        for(Date dataKey : mapaDiaAcessaram.keySet()){
            String data = Calendario.getData(dataKey, "yyyyMMdd");
            Map<Integer, Map<Integer, AgendadoTO>> mapaHoraAcessaram = mapaDiaAcessaram.get(dataKey);
            for(Integer hora : mapaHoraAcessaram.keySet()){
                BiTvGestorDTO tvGestorDTO = dados.get(data);
                if (tvGestorDTO == null) {
                    tvGestorDTO = new BiTvGestorDTO();
                }
                tvGestorDTO.setCompareceu(tvGestorDTO.getCompareceu() + mapaHoraAcessaram.get(hora).size());
                dados.put(data, tvGestorDTO);
            }
        }
    }

    private void preencherComparecidosDiaHora(Map<Date, Map<Integer, Map<Integer, AgendadoTO>>> mapaAcessaram, Map<String, Map<String, BiTvGestorDTO>> dados) {
        for(Date dataKey : mapaAcessaram.keySet()){
            String data = new SimpleDateFormat("yyyyMMdd").format(dataKey);
            Map<Integer, Map<Integer, AgendadoTO>> mapaHoraAcessaram = mapaAcessaram.get(dataKey);
            Map<String, BiTvGestorDTO> mapaHora = dados.get(data);
            for(Integer hora : mapaHoraAcessaram.keySet()){
                BiTvGestorDTO tvGestorDTO = mapaHora.get(""+hora);
                if (tvGestorDTO == null) {
                    tvGestorDTO = new BiTvGestorDTO();
                }
                tvGestorDTO.setCompareceu(mapaHoraAcessaram.get(hora).size()+tvGestorDTO.getCompareceu());
                mapaHora.put(""+hora, tvGestorDTO);
                BiTvGestorDTO tvGestorTotal = mapaHora.get("total");
                tvGestorTotal.setCompareceu(mapaHoraAcessaram.get(hora).size()+tvGestorDTO.getCompareceu());
                mapaHora.put("total", tvGestorTotal);
            }
        }
    }

    @Override
    public List<AlunoDTO> getAlunosTvGestor(String chave, Integer empresa, FiltroTvGestorJSON filtro, PaginadorDTO paginator, HttpServletRequest request) throws ServiceException {
        try{
            long start = 0;
            long end = 10;

            Map<String,AgendaTotalTO> agendamentosMap = montarAgendamentos(chave, empresa, filtro.getData(), filtro.getData());
            Map<String, List<AgendadoTO>> agendadosMap = montarAgendados(chave, empresa, agendamentosMap, filtro.getData(), filtro.getData());
            List<AgendadoTO> agendadosAcessaram = tvGestorService.obterAcessaram(chave, empresa, agendamentosMap, agendadosMap, new ArrayList<>(), getFiltrosAgendaTO(filtro), filtro.getData());
            List<AlunoDTO> todosAlunos = montarListaAlunos(agendadosMap, filtro, filtro.getHora(), agendadosAcessaram, chave, request);

            if (StringUtils.isNotBlank(filtro.getParametro())) {
                if (filtro.getFiltrarAluno() && filtro.getFiltrarTurma()) {
                    todosAlunos = Uteis.filtrarListaValores(todosAlunos, filtro.getParametro(), new String[]{"nomeAluno", "nomeTurma"});
                } else if (filtro.getFiltrarAluno()) {
                    todosAlunos = Uteis.filtrarListaValores(todosAlunos, filtro.getParametro(), new String[]{"nomeAluno"});
                } else if (filtro.getFiltrarTurma()) {
                    todosAlunos = Uteis.filtrarListaValores(todosAlunos, filtro.getParametro(), new String[]{"nomeTurma"});
                }
            }
            List<AlunoDTO> alunos = new ArrayList<>();
            if(paginator != null){
                paginator.setQuantidadeTotalElementos((long)todosAlunos.size());
                if(paginator.getSize() != null && paginator.getPage() != null){
                    start = paginator.getPage()*paginator.getSize();
                    end = todosAlunos.size() < paginator.getSize() ? todosAlunos.size(): paginator.getSize() > 0 ? (paginator.getSize()*paginator.getPage())+paginator.getSize():end;
                }

                if (paginator.getSort() != null){
                    String coluna = paginator.getSort().split(",")[0];
                    if(paginator.getSort().contains("DESC")) {
                        Ordenacao.ordenarListaReverse(todosAlunos, coluna);
                    } else {
                        Ordenacao.ordenarLista(todosAlunos, coluna);
                    }
                }
                if (todosAlunos.size() < start) {
                    start = 0;
                    end = 10;
                }
                if(todosAlunos.size() < end){
                    end = todosAlunos.size();
                }
            }else{
                Ordenacao.ordenarLista(todosAlunos, "nomeAluno");
            }
            while (start < end){
                alunos.add(todosAlunos.get((int)start));
                start++;
            }
            return alunos;
        }catch (Exception e){
            throw new ServiceException(e);
        }
    }

    private List<AlunoDTO> montarListaAlunos(Map<String, List<AgendadoTO>> agendadosMap, FiltroTvGestorJSON filtro, Integer hora, List<AgendadoTO> agendadosAcessaram, String chave, HttpServletRequest request){
        List<AlunoDTO> alunos = new ArrayList<>();
        for(String key : agendadosMap.keySet()){
            List<AgendadoTO> agendados = agendadosMap.get(key);
            for(AgendadoTO agendado : agendados){
                if(agendado.getAgendamento() != null
                        && (hora == null || hora == Integer.parseInt(Calendario.getData(agendado.getAgendamento().getStartDate(),"HH")))
                        && filtrar(filtro, agendado.getAgendamento())
                        && !agendado.isDesmarcado()){
                    for (AgendadoTO agendadoAcessou : agendadosAcessaram) {
                        if (agendado.getCodigoCliente().equals(agendadoAcessou.getCodigoCliente())
                                && agendado.getAgendamento() != null && agendadoAcessou.getAgendamento() != null
                                && agendado.getAgendamento().getIdentificador().equals(agendadoAcessou.getAgendamento().getIdentificador())) {
                            agendado.setDataAcesso(agendadoAcessou.getDataAcesso());
                        }
                    }
                    AlunoDTO aluno = getAlunoDTO(chave, agendado, request);
                    if(!alunos.contains(aluno)){
                        alunos.add(aluno);
                    }
                }
            }
        }
        return alunos;
    }

    @Override
    public List<AlunoDTO> biAcessos(String chave, Integer empresa, FiltroTvGestorJSON filtro, HttpServletRequest request) throws ServiceException {
        try{
            List<AlunoDTO> alunos = new ArrayList<>();
            Date dataInicio = Uteis.somarDias(Calendario.getDataComHoraZerada(Calendario.hoje()), 0);
            Date dataFim = Uteis.somarDias(Calendario.fimDoDia(Calendario.hoje()), 0);

            Map<String,AgendaTotalTO> agendamentosMap = montarAgendamentos(chave, empresa, dataInicio, dataFim);
            Map<String, List<AgendadoTO>> agendadosMap = montarAgendados(chave, empresa, agendamentosMap, dataInicio, dataFim);

            List<AgendadoTO> todosAlunos = tvGestorService.obterAcessaram(chave, empresa, agendamentosMap, agendadosMap, new ArrayList<>(), getFiltrosAgendaTO(filtro), null);
            int qtde = 0;
            for(AgendadoTO agendadoTO : todosAlunos){
                alunos.add(getAlunoDTO(chave, agendadoTO, request));
                qtde++;
            }

            return alunos;
        }catch (Exception e){
            throw new ServiceException(e);
        }
    }

    private AlunoDTO getAlunoDTO(String chave, AgendadoTO agendadoTO, HttpServletRequest request) {
        AlunoDTO alunoDTO = new AlunoDTO();
        alunoDTO.setCodigoAluno(agendadoTO.getCodigoCliente().longValue());
        alunoDTO.setMatriculaAluno(agendadoTO.getMatricula());
        alunoDTO.setNomeAluno(agendadoTO.getNome());
        alunoDTO.setNomeTurma(agendadoTO.getAgendamento().getTipo());
        alunoDTO.setDataHoraInicioTurma(Calendario.getData(agendadoTO.getAgendamento().getStartDate(), "yyyyMMddHHmm"));
        alunoDTO.setDataHoraFimTurma(Calendario.getData(agendadoTO.getAgendamento().getEndDate(), "yyyyMMddHHmm"));
        if(agendadoTO.getDataAcesso() != null){
            alunoDTO.setDataHoraAcesso(Calendario.getData(agendadoTO.getDataAcesso(), "yyyyMMddHHmm"));
        }
        alunoDTO.setImageUri(Aplicacao.obterUrlFotoDaNuvem(agendadoTO.getFotokey()));
        return alunoDTO;
    }

}
