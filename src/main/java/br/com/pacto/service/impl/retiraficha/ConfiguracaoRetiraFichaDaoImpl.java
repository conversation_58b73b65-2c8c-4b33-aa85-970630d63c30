package br.com.pacto.service.impl.retiraficha;

import br.com.pacto.bean.retiraficha.ConfiguracaoRetiraFicha;
import br.com.pacto.dao.impl.base.DaoGenericoImpl;
import br.com.pacto.dao.intf.retiraficha.ConfiguracaoRetiraFichaDao;
import br.com.pacto.service.exception.ServiceException;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;

@Repository
public class ConfiguracaoRetiraFichaDaoImpl extends DaoGenericoImpl<ConfiguracaoRetiraFicha, Integer> implements ConfiguracaoRetiraFichaDao {

    @Override
    public List<ConfiguracaoRetiraFicha> listarLogsAlteracoes(String contexto, ConfiguracaoRetiraFicha configuracaoRetiraFicha) throws Exception {
        final String hql = new StringBuilder("SELECT obj FROM ")
                .append(ConfiguracaoRetiraFicha.class.getSimpleName()).append(" conf ")
                .append("where conf.codigo = :coletor")
                .toString();

        HashMap<String, Object> param = new HashMap<String, Object>();

        param.put("coletor", configuracaoRetiraFicha.getCodigo());
        return findByParam(contexto, hql, param);

    }

    @Override
    public List<ConfiguracaoRetiraFicha> obterTodos(String contexto) throws Exception {
        final String hql = new StringBuilder("SELECT obj FROM ")
                .append(ConfiguracaoRetiraFicha.class.getSimpleName()).append(" conf ")
//                .append("where conf.codigo = :coletor")
                .toString();

        HashMap<String, Object> param = new HashMap<String, Object>();

//        param.put("coletor", configuracaoRetiraFicha.getCodigo());
        return findByParam(contexto, hql, param);
    }



}
