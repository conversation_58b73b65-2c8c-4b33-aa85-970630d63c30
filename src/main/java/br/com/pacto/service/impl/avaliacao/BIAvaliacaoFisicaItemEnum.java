package br.com.pacto.service.impl.avaliacao;

import br.com.pacto.bean.gestao.CategoriaIndicadorEnum;

/**
 * Created by alcides on 21/10/2017.
 */
public enum BIAvaliacaoFisicaItemEnum {
    AVALIACOES(CategoriaIndicadorEnum.COLUNAS_AVALIACAO_FISICA),
    NOVAS(CategoriaIndicadorEnum.COLUNAS_AVALIACAO_FISICA),
    REAVALIACOES(CategoriaIndicadorEnum.COLUNAS_AVALIACAO_FISICA),
    PREVISTAS(CategoriaIndicadorEnum.COLUNAS_AVALIACAO_FISICA),
    REALIZADAS(CategoriaIndicadorEnum.COLUNAS_AVALIACAO_FISICA),
    ATRASADAS(CategoriaIndicadorEnum.COLUNAS_AVALIACAO_FISICA),
    FUTURAS(CategoriaIndicadorEnum.COLUNAS_AVALIACAO_FISICA),
    SEM_AVALIACAO(CategoriaIndicadorEnum.COLUNAS_ALUNOS_DASHBOARD),
    ATIVOS_AVALIACAO_ATRASADA(CategoriaIndicadorEnum.COLUNAS_AVALIACAO_FISICA),
    ATIVOS_AVALIACAO_EM_DIA(CategoriaIndicadorEnum.COLUNAS_AVALIACAO_FISICA),
    PERDERAM_PERCENTUAL(CategoriaIndicadorEnum.COLUNAS_AVALIACAO_FISICA),
    PERDERAM_PESO(CategoriaIndicadorEnum.COLUNAS_AVALIACAO_FISICA),
    GANHARAM_MASSA(CategoriaIndicadorEnum.COLUNAS_AVALIACAO_FISICA),
    PARQ(CategoriaIndicadorEnum.COLUNAS_AVALIACAO_FISICA),
    OBJETIVOS(CategoriaIndicadorEnum.COLUNAS_AVALIACAO_FISICA);

    private String colunas;

    BIAvaliacaoFisicaItemEnum(String colunas) {
        this.colunas = colunas;
    }

    public String getColunas() {
        return colunas;
    }

    public void setColunas(String colunas) {
        this.colunas = colunas;
    }
}
