package br.com.pacto.service.impl.gympass;

import br.com.pacto.base.util.ExecuteRequestHttpService;
import br.com.pacto.bean.gympass.ConfigGymPass;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.gympass.dto.BookingsDTO;
import br.com.pacto.service.impl.gympass.dto.ClassesDTO;
import br.com.pacto.service.impl.gympass.dto.ProductDTO;
import br.com.pacto.service.impl.gympass.dto.SlotsDTO;
import br.com.pacto.service.telegram.TelegramService;
import br.com.pacto.util.UteisValidacao;
import org.json.JSONArray;
import org.json.JSONObject;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpDelete;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPatch;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpPut;
import org.apache.http.entity.StringEntity;
import org.apache.http.util.EntityUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 * User: Luiz Felipe
 * Date: 22/03/2020
 * <p>
 * Reference: https://developers.gympass.com/apis/booking/
 */
public class IntegracaoGymPassBooking {

    private static final String HEADER_AUTHORIZATION = "Authorization";
    private static final String HEADER_CONTENT_TYPE = "Content-Type";
    private static final String HEADER_ACCEPT = "Accept";
    private static final String APPLICATION_JSON = "application/json";
    private static final String APPLICATION_FORM = "application/x-www-form-urlencoded";
    private static final String CHARSET_UTF8 = "UTF-8";
    private static final String GRANT_TYPE = "grant_type";
    private static final String CLIENT_CREDENTIALS = "client_credentials";
    private static final String CLIENT_ID = "client_id";
    private static final String CLIENT_SECRET = "client_secret";

    private static final String CLIENT_ID_PACO = "pacto";
    private static final String CLIENT_SECRET_KEY = "f3677289-46ca-4c0f-a270-3aeb262db4af";

    private String URL_BASE;

    private static final String ENDPOINT_HEALTH_CHECK = "/booking/v1/health";

    private static final String ENDPOINT_CLASSES_GET = "/booking/v1/gyms/%s/classes/%s";
    private static final String ENDPOINT_CLASSES_GET_LIST = "/booking/v1/gyms/%s/classes";
    private static final String ENDPOINT_CLASSES_CREATE = "/booking/v1/gyms/%s/classes";
    private static final String ENDPOINT_CLASSES_UPDATE = "/booking/v1/gyms/%s/classes/%s";

    private static final String ENDPOINT_SLOTS_GET = "/booking/v1/gyms/%s/classes/%s/slots/%s";
    private static final String ENDPOINT_SLOTS_GET_LIST = "/booking/v1/gyms/%s/classes/%s/slots";
    private static final String ENDPOINT_SLOTS_CREATE = "/booking/v1/gyms/%s/classes/%s/slots";
    private static final String ENDPOINT_SLOTS_UPDATE = "/booking/v1/gyms/%s/classes/%s/slots/%s";
    private static final String ENDPOINT_SLOTS_DELETE = "/booking/v1/gyms/%s/classes/%s/slots/%s";
    private static final String ENDPOINT_SLOTS_PATCH = "/booking/v1/gyms/%s/classes/%s/slots/%s";

    private static final String ENDPOINT_BOOKINGS_UPDATE = "/booking/v1/gyms/%s/bookings/%s";

    private static final String ENDPOINT_PRODUCTS_GET_LIST = "/setup/v1/gyms/%s/products";

    private String gymPass_id;
    private String client_id;
    private String client_secret;
    private String token;
    private Date dataToken;

    public IntegracaoGymPassBooking(ConfigGymPass configGymPass) {
        this.gymPass_id = configGymPass.getCodigoGymPass() == null ? null : configGymPass.getCodigoGymPass().trim();
        this.URL_BASE = Aplicacao.getProp(Aplicacao.urlAPIGymPassBookingProduction);
    }

    private String getCaminho(String endpoint) {
        return URL_BASE + endpoint;
    }

    private String getToken() throws Exception {
//        if (UteisValidacao.emptyString(this.token)) {
        obterToken();
//        }
        if (UteisValidacao.emptyString(this.token)) {
            throw new Exception("Não foi possível obter o token de integração com a GymPass Booking");
        }
        return "Bearer " + this.token;
    }

    public void obterToken() throws Exception {
        try {

            String data = "grant_type=client_credentials&client_id=pacto&client_secret=f3677289-46ca-4c0f-a270-3aeb262db4af";

            StringEntity entity = new StringEntity(data, CHARSET_UTF8);

            String urlTokenGymPass = Aplicacao.getProp(Aplicacao.urlAPIGymPassBookingToken);

            HttpPost httpPost = new HttpPost(urlTokenGymPass);
            httpPost.setEntity(entity);
            httpPost.setHeader(HEADER_CONTENT_TYPE, APPLICATION_FORM);
            HttpClient client = ExecuteRequestHttpService.createConnectorCloseable();
            HttpResponse response = client.execute(httpPost);

            int statusCode = response.getStatusLine().getStatusCode();
            String responseBody = EntityUtils.toString(response.getEntity());
            if (statusCode != 200) {
                throw new Exception(responseBody);
            }

            JSONObject json = new JSONObject(responseBody);
            if (json.has("access_token")) {
                this.token = json.optString("access_token");
            } else {
                throw new Exception(responseBody);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    public ClassesDTO createClasse(ClassesDTO dto) throws Exception {
        try {

            List<ClassesDTO> lista = new ArrayList<>();
            lista.add(dto);

            JSONObject json = new JSONObject();
            json.put("classes", new JSONArray(lista));

            StringEntity entity = new StringEntity(json.toString(), CHARSET_UTF8);
            HttpPost httpPost = new HttpPost(getCaminho(String.format(ENDPOINT_CLASSES_CREATE, this.gymPass_id)));
            httpPost.setEntity(entity);
            httpPost.setHeader(HEADER_CONTENT_TYPE, APPLICATION_JSON);
            httpPost.setHeader(HEADER_AUTHORIZATION, getToken());
            HttpClient client = ExecuteRequestHttpService.createConnectorCloseable();
            HttpResponse response = client.execute(httpPost);

            int statusCode = response.getStatusLine().getStatusCode();
            String responseBody = EntityUtils.toString(response.getEntity());

            if (statusCode == 201) {
                JSONObject jsonResponse = new JSONObject(responseBody);
                JSONArray jsonArray = jsonResponse.getJSONArray("classes");
                if (jsonArray.length() > 0) {
                    dto.setId(jsonArray.getJSONObject(0).optInt("id"));
                }
            } else {
                throw new Exception(responseBody);
            }
            return dto;
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    public ClassesDTO updateClasse(ClassesDTO dto) throws Exception {
        try {
            StringEntity entity = new StringEntity(new JSONObject(dto).toString(), CHARSET_UTF8);
            HttpPut httpPut = new HttpPut(getCaminho(String.format(ENDPOINT_CLASSES_UPDATE, this.gymPass_id, dto.getId())));
            httpPut.setEntity(entity);
            httpPut.setHeader(HEADER_CONTENT_TYPE, APPLICATION_JSON);
            httpPut.setHeader(HEADER_AUTHORIZATION, getToken());
            HttpClient client = ExecuteRequestHttpService.createConnectorCloseable();
            HttpResponse response = client.execute(httpPut);

            int statusCode = response.getStatusLine().getStatusCode();
            String responseBody = EntityUtils.toString(response.getEntity());

            if (statusCode != 200) {
                throw new Exception(responseBody);
            }
            return dto;
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    public ClassesDTO obterClasse(int class_id) throws Exception {
        try {
            HttpGet httpGet = new HttpGet(getCaminho(String.format(ENDPOINT_CLASSES_GET, this.gymPass_id, class_id)));
            httpGet.setHeader(HEADER_ACCEPT, APPLICATION_JSON);
            httpGet.setHeader(HEADER_AUTHORIZATION, getToken());
            HttpClient client = ExecuteRequestHttpService.createConnectorCloseable();
            HttpResponse response = client.execute(httpGet);

            int statusCode = response.getStatusLine().getStatusCode();
            String responseBody = EntityUtils.toString(response.getEntity());
            return new ClassesDTO(new JSONObject(responseBody));
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    public List<ClassesDTO> obterClasse() throws Exception {
        try {
            HttpGet httpGet = new HttpGet(getCaminho(String.format(ENDPOINT_CLASSES_GET_LIST, this.gymPass_id)));
            httpGet.setHeader(HEADER_ACCEPT, APPLICATION_JSON);
            httpGet.setHeader(HEADER_AUTHORIZATION, getToken());
            HttpClient client = ExecuteRequestHttpService.createConnectorCloseable();
            HttpResponse response = client.execute(httpGet);

            int statusCode = response.getStatusLine().getStatusCode();
            String responseBody = EntityUtils.toString(response.getEntity());

            List<ClassesDTO> lista = new ArrayList<>();
            JSONObject json = new JSONObject(responseBody);
            JSONArray classes = json.getJSONArray("classes");
            for (int e = 0; e < classes.length(); e++) {
                JSONObject obj = classes.getJSONObject(e);
                try {
                    lista.add(new ClassesDTO(obj));
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }
            return lista;
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    public SlotsDTO createSlots(Integer class_id, SlotsDTO dto) throws Exception {
        try {
            StringEntity entity = new StringEntity(new JSONObject(dto).toString(), CHARSET_UTF8);
            HttpPost httpPost = new HttpPost(getCaminho(String.format(ENDPOINT_SLOTS_CREATE, this.gymPass_id, class_id)));
            httpPost.setEntity(entity);
            httpPost.setHeader(HEADER_CONTENT_TYPE, APPLICATION_JSON);
            httpPost.setHeader(HEADER_AUTHORIZATION, getToken());
            HttpClient client = ExecuteRequestHttpService.createConnectorCloseable();
            HttpResponse response = client.execute(httpPost);

            int statusCode = response.getStatusLine().getStatusCode();
            String responseBody = EntityUtils.toString(response.getEntity());

            if (statusCode == 201) {
                JSONObject json = new JSONObject(responseBody);
                if (json.has("results")) {
                    JSONArray array = json.optJSONArray("results");
                    for (int e = 0; e < array.length(); e++) {
                        JSONObject obj = array.getJSONObject(e);
                        return new SlotsDTO(obj);
                    }
                } else {
                    throw new Exception(responseBody);
                }
            } else if (responseBody.contains("Slot not created. Already exists")) {
                List<SlotsDTO> existentes = obterSlots(class_id, dto.getOccur_date(), dto.getOccur_date());
                if (existentes.size() == 1) {
                    SlotsDTO existente = existentes.get(0);
                    existente.setOccur_date(dto.getOccur_date());
                    existente.setRoom(dto.getRoom());
                    existente.setStatus(dto.getStatus());
                    existente.setLength_in_minutes(dto.getLength_in_minutes());
                    existente.setTotal_capacity(dto.getTotal_capacity());
                    existente.setTotal_booked(dto.getTotal_booked());
                    existente.setProduct_id(dto.getProduct_id());
                    existente.setCancellable_until(dto.getCancellable_until());
                    existente.setRating(dto.getRating());
                    existente.setBooking_window(dto.getBooking_window());
                    existente.setInstructors(dto.getInstructors());
                    existente.setVirtual(dto.isVirtual());
                    return updateSlotsPatch(class_id, existente);
                } else {
                    throw new Exception(responseBody);
                }
            } else {
                throw new Exception(responseBody);
            }
            return null;
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    public SlotsDTO obterSlots(int class_id, int slot_id) throws Exception {
        try {
            HttpGet httpGet = new HttpGet(getCaminho(String.format(ENDPOINT_SLOTS_GET, this.gymPass_id, class_id, slot_id)));
            httpGet.setHeader(HEADER_ACCEPT, APPLICATION_JSON);
            httpGet.setHeader(HEADER_AUTHORIZATION, getToken());
            HttpClient client = ExecuteRequestHttpService.createConnectorCloseable();
            HttpResponse response = client.execute(httpGet);

            int statusCode = response.getStatusLine().getStatusCode();
            String responseBody = EntityUtils.toString(response.getEntity());
            return new SlotsDTO(new JSONObject(responseBody));
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    public List<SlotsDTO> obterSlots(int class_id, String inicio, String fim) throws Exception {
        try {

            Map<String, String> params = new HashMap<>();
            params.put("from", inicio);
            params.put("to", fim);

            String url = String.format(ENDPOINT_SLOTS_GET_LIST, this.gymPass_id, class_id);

            url = ExecuteRequestHttpService.obterUrlComParams(url, params, CHARSET_UTF8);

            HttpGet httpGet = new HttpGet(getCaminho(url));
            httpGet.setHeader(HEADER_ACCEPT, APPLICATION_JSON);
            httpGet.setHeader(HEADER_AUTHORIZATION, getToken());
            HttpClient client = ExecuteRequestHttpService.createConnectorCloseable();
            HttpResponse response = client.execute(httpGet);

            int statusCode = response.getStatusLine().getStatusCode();
            String responseBody = EntityUtils.toString(response.getEntity());

            List<SlotsDTO> lista = new ArrayList<>();

            JSONObject json = new JSONObject(responseBody);
            if (json.has("results")) {
                JSONArray array = json.optJSONArray("results");
                for (int e = 0; e < array.length(); e++) {
                    JSONObject obj = array.getJSONObject(e);
                    lista.add(new SlotsDTO(obj));
                }
                return lista;
            } else {
                throw new Exception(responseBody);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    public SlotsDTO updateSlotsPatch(Integer class_id, SlotsDTO dto) throws Exception {
        try {

            StringEntity entity = new StringEntity(new JSONObject(dto).toString(), CHARSET_UTF8);
            HttpPut httpPut = new HttpPut(getCaminho(String.format(ENDPOINT_SLOTS_UPDATE, this.gymPass_id, class_id, dto.getId())));
            httpPut.setEntity(entity);
            httpPut.setHeader(HEADER_CONTENT_TYPE, APPLICATION_JSON);
            httpPut.setHeader(HEADER_AUTHORIZATION, getToken());
            HttpClient client = ExecuteRequestHttpService.createConnectorCloseable();
            HttpResponse response = client.execute(httpPut);

            int statusCode = response.getStatusLine().getStatusCode();
            String responseBody = EntityUtils.toString(response.getEntity());

            JSONObject json = new JSONObject(responseBody);
            if (json.has("results")) {
                JSONArray array = json.optJSONArray("results");
                for (int e = 0; e < array.length(); e++) {
                    JSONObject obj = array.getJSONObject(e);
                    return new SlotsDTO(obj);
                }
            } else if (json.optString("message").toLowerCase().contains("slot not found")) {
                return createSlots(class_id, dto);
            } else {
                throw new Exception(responseBody);
            }
            return null;
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    public void deleteSlots(Integer class_id, SlotsDTO dto) throws Exception {
        try {
            HttpDelete httpDelete = new HttpDelete(getCaminho(String.format(ENDPOINT_SLOTS_DELETE, this.gymPass_id, class_id, dto.getId())));
            httpDelete.setHeader(HEADER_CONTENT_TYPE, APPLICATION_JSON);
            httpDelete.setHeader(HEADER_AUTHORIZATION, getToken());
            HttpClient client = ExecuteRequestHttpService.createConnectorCloseable();
            HttpResponse response = client.execute(httpDelete);

            int statusCode = response.getStatusLine().getStatusCode();
            if (statusCode != 204) {
                String responseBody = EntityUtils.toString(response.getEntity());
                throw new Exception(responseBody);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    public void deleteSlotID(Integer class_id, Integer slot_id) throws Exception {
        try {
            HttpDelete httpDelete = new HttpDelete(getCaminho(String.format(ENDPOINT_SLOTS_DELETE, this.gymPass_id, class_id, slot_id)));
            httpDelete.setHeader(HEADER_CONTENT_TYPE, APPLICATION_JSON);
            httpDelete.setHeader(HEADER_AUTHORIZATION, getToken());
            HttpClient client = ExecuteRequestHttpService.createConnectorCloseable();
            HttpResponse response = client.execute(httpDelete);

            int statusCode = response.getStatusLine().getStatusCode();
            if (statusCode != 204) {
                String responseBody = EntityUtils.toString(response.getEntity());
                throw new Exception(responseBody);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    public BookingsDTO booking(BookingsDTO dto, String booking_number) throws Exception {
        try {
            StringEntity entity = new StringEntity(new JSONObject(dto).toString(), CHARSET_UTF8);
            HttpPatch httpPatch = new HttpPatch(getCaminho(String.format(ENDPOINT_BOOKINGS_UPDATE, this.gymPass_id, booking_number)));
            httpPatch.setEntity(entity);
            httpPatch.setHeader(HEADER_CONTENT_TYPE, APPLICATION_JSON);
            httpPatch.setHeader(HEADER_AUTHORIZATION, getToken());
            HttpClient client = ExecuteRequestHttpService.createConnectorCloseable();
            HttpResponse response = client.execute(httpPatch);

            int statusCode = response.getStatusLine().getStatusCode();
            if (statusCode != 200 && statusCode != 204) {
                String responseBody = EntityUtils.toString(response.getEntity());
                throw new Exception(responseBody);
            }
            return dto;
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    public String healthCheck() throws Exception {
        try {
            HttpGet httpGet = new HttpGet(getCaminho(ENDPOINT_HEALTH_CHECK));
            httpGet.setHeader(HEADER_AUTHORIZATION, getToken());
            HttpClient client = ExecuteRequestHttpService.createConnectorCloseable();
            HttpResponse response = client.execute(httpGet);

            int statusCode = response.getStatusLine().getStatusCode();
            String responseBody = EntityUtils.toString(response.getEntity());
            if (statusCode != 200) {
                throw new Exception(responseBody);
            }
            return responseBody;
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    public void patchSlots(String ctx, Integer class_id, SlotsDTO slot, String virtual_class_url) throws Exception {
        try {
            HttpPatch httpPatch = new HttpPatch(getCaminho(String.format(ENDPOINT_SLOTS_PATCH, this.gymPass_id, class_id, slot.getId())));
            JSONObject dto = new JSONObject();
            dto.put("total_capacity", slot.getTotal_capacity());
            dto.put("total_booked", slot.getTotal_booked());
            dto.put("virtual_class_url", virtual_class_url == null ? "" : virtual_class_url);
            StringEntity entity = new StringEntity(dto.toString(), CHARSET_UTF8);
            httpPatch.setEntity(entity);
            httpPatch.setHeader(HEADER_CONTENT_TYPE, APPLICATION_JSON);
            httpPatch.setHeader(HEADER_AUTHORIZATION, getToken());
            HttpClient client = ExecuteRequestHttpService.createConnectorCloseable();
            HttpResponse response = client.execute(httpPatch);
            int statusCode = response.getStatusLine().getStatusCode();
            if (statusCode != 204) {
                String responseBody = EntityUtils.toString(response.getEntity());
                TelegramService.enviarMsg(ctx, " retorno: " +  EntityUtils.toString(response.getEntity()) );
                throw new Exception(responseBody);
            } else {
                TelegramService.enviarMsg(ctx, " 204: " );
            }

        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    public void deleteUmSlot(Integer class_id, Integer idSlot) throws Exception {
        try {
            HttpDelete httpDelete = new HttpDelete(getCaminho(String.format(ENDPOINT_SLOTS_DELETE, this.gymPass_id, class_id, idSlot)));
            httpDelete.setHeader(HEADER_CONTENT_TYPE, APPLICATION_JSON);
            httpDelete.setHeader(HEADER_AUTHORIZATION, getToken());
            HttpClient client = ExecuteRequestHttpService.createConnectorCloseable();
            HttpResponse response = client.execute(httpDelete);

            int statusCode = response.getStatusLine().getStatusCode();
            if (statusCode != 204) {
                String responseBody = EntityUtils.toString(response.getEntity());
                throw new Exception(responseBody);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    public List<ProductDTO> listarProdutos() throws Exception {
        try {
            HttpGet httpGet = new HttpGet(getCaminho(String.format(ENDPOINT_PRODUCTS_GET_LIST, this.gymPass_id)));
            httpGet.setHeader(HEADER_ACCEPT, APPLICATION_JSON);
            httpGet.setHeader(HEADER_AUTHORIZATION, getTokenV3());
            HttpClient client = ExecuteRequestHttpService.createConnectorCloseable();
            HttpResponse response = client.execute(httpGet);

            String responseBody = EntityUtils.toString(response.getEntity());

            List<ProductDTO> lista = new ArrayList<>();
            JSONObject json = new JSONObject(responseBody);
            if (json.has("products")) {
                JSONArray products = json.getJSONArray("products");
                for (int e = 0; e < products.length(); e++) {
                    JSONObject obj = products.getJSONObject(e);
                    try {
                        lista.add(new ProductDTO(obj));
                    } catch (Exception ex) {
                        ex.printStackTrace();
                    }
                }
            } else if (json.has("Message")) {
                throw new ServiceException(json.getString("Message"));
            }

            return lista;
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    private String getTokenV3() {
        String tokenV3 = Aplicacao.getProp(Aplicacao.tokenGymPassV3);
        return "Bearer " + tokenV3;
    }

}
