package br.com.pacto.service.impl.aulaExcluida;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ExecuteRequestHttpService;
import br.com.pacto.bean.aula.AulaDiaExclusao;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.agendamento.TurmaResponseDTO;
import br.com.pacto.controller.json.aulaExcluida.AulaExcluidaDTO;
import br.com.pacto.controller.json.aulaExcluida.FiltroAulaExcluidaJSON;
import br.com.pacto.dao.intf.aula.AulaDiaExclusaoDao;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.notificacao.excecao.AulaExcluidaExcecoes;
import br.com.pacto.service.intf.agenda.AgendaService;
import br.com.pacto.service.intf.aulaExcluida.AulaExcluidaService;
import br.com.pacto.service.intf.usuario.UsuarioService;
import br.com.pacto.util.UteisValidacao;
import org.apache.http.NameValuePair;
import org.apache.http.client.ResponseHandler;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.BasicResponseHandler;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

@Service
public class AulaExcluidaServiceImpl implements AulaExcluidaService {

    @Autowired
    private AulaDiaExclusaoDao aulaDiaExclusaoDao;

    @Autowired
    private SessaoService sessaoService;

    @Autowired
    private AgendaService agendaService;

    @Autowired
    private UsuarioService usuarioService;

    public AulaDiaExclusaoDao getAulaDiaExclusaoDao() {
        return aulaDiaExclusaoDao;
    }

    @Override
    public AulaDiaExclusao inserir(String ctx, AulaDiaExclusao object) throws ServiceException {
        try {
            return getAulaDiaExclusaoDao().insert(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public AulaDiaExclusao consultarPorAulaExcluida(String ctx, Integer id) throws ServiceException {
        try {
            return getAulaDiaExclusaoDao().findById(ctx, id);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public AulaDiaExclusao alterar(String ctx, AulaDiaExclusao aulaDiaExclusao) throws ServiceException {
        try {
            return getAulaDiaExclusaoDao().update(ctx, aulaDiaExclusao);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public void remover(String ctx, AulaDiaExclusao aulaDiaExclusao) throws ServiceException {
        try {
            getAulaDiaExclusaoDao().delete(ctx, aulaDiaExclusao);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<AulaDiaExclusao> consultarOrdenada(String ctx, FiltroAulaExcluidaJSON filtroAulaExcluidaJSON, String idsHorarioTurma, PaginadorDTO paginadorDTO) throws ServiceException {
        try {
            return getAulaDiaExclusaoDao().consultarOrdenada(ctx, filtroAulaExcluidaJSON, idsHorarioTurma, paginadorDTO);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public List<AulaExcluidaDTO> listaAulaExcluida(FiltroAulaExcluidaJSON filtroAulaExcluidaJSON, PaginadorDTO paginadorDTO, Integer empresaId, HttpServletRequest request) throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        try {
            JSONObject idsHorarioTurma = chamadaZW(ctx,"/prest/aulacheia/listar-ids-horario-turma",null, null, null, null);
            List<AulaDiaExclusao> lista = consultarOrdenada(ctx, filtroAulaExcluidaJSON, idsHorarioTurma.getString("content"), paginadorDTO);

            List<AulaExcluidaDTO> listaRet = new ArrayList<>();
            TurmaResponseDTO aula;
            for (AulaDiaExclusao aulaDiaEx: lista) {
                Usuario us = usuarioService.obterPorId(ctx, aulaDiaEx.getUsuario_codigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                aula = agendaService.aulaDetalhada(null, empresaId, aulaDiaEx.getCodigoHorarioTurma(),
                        aulaDiaEx.getDataAulaDia(), true,
                        request);
                if (aula.getHorarioTurmaId() != null) {
                    if(filtroAulaExcluidaJSON.getProfessoresIds() != null && !UteisValidacao.emptyList(filtroAulaExcluidaJSON.getProfessoresIds())) {
                        for (Integer professor : filtroAulaExcluidaJSON.getProfessoresIds()) {
                            if (aula.getProfessor().getId().equals(professor)) {
                                AulaExcluidaDTO dto = new AulaExcluidaDTO(aulaDiaEx, aula);
                                dto.setNomeUsuario(us != null ? us.getNome() : "-");
                                listaRet.add(dto);
                            }
                        }
                    } else {
                        AulaExcluidaDTO dto = new AulaExcluidaDTO(aulaDiaEx, aula);
                        dto.setNomeUsuario(us != null ? us.getNome() : "-");
                        listaRet.add(dto);
                    }

                }
            }

            return listaRet;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException(AulaExcluidaExcecoes.ERRO_LISTAR_AULA_EXCLUIDA, e);
        }
    }

    @Override
    public void removerAulaExcluida(Integer id) throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        try {
            aulaDiaExclusaoDao.delete(ctx, id);
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException(AulaExcluidaExcecoes.ERRO_DESFAZER_AULA_EXCLUIDA, e);
        }
    }

    private JSONObject chamadaZW(String ctx,
                                 String endpoint,
                                 Integer empresa,
                                 Long inicio,
                                 Long fim,
                                 JSONObject dados) throws Exception {
        final String url = Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebInteg);
        CloseableHttpClient client = ExecuteRequestHttpService.createConnector();
        HttpPost httpPost = new HttpPost(url + endpoint);
        List<NameValuePair> params = new ArrayList<>();
        params.add(new BasicNameValuePair("chave", ctx));
        if (empresa != null) {
            params.add(new BasicNameValuePair("empresa", empresa.toString()));
        }
        if(dados != null){
            params.add(new BasicNameValuePair("dados", dados.toString()));
        }
        if(inicio != null){
            params.add(new BasicNameValuePair("inicio", inicio.toString()));
        }
        if(fim != null){
            params.add(new BasicNameValuePair("fim", fim.toString()));
        }
        httpPost.setEntity(new UrlEncodedFormEntity(params));
        CloseableHttpResponse response = client.execute(httpPost);
        ResponseHandler<String> handler = new BasicResponseHandler();
        String body = handler.handleResponse(response);
        client.close();
        return new JSONObject(body);
    }
}
