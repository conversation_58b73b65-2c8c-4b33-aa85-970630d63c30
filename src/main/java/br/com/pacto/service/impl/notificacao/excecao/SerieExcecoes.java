package br.com.pacto.service.impl.notificacao.excecao;

import br.com.pacto.service.exception.ExcecaoSistema;

/**
 * Created by ulisses on 17/08/2018.
 */
public enum SerieExcecoes implements ExcecaoSistema {

    SERIE_NAO_ENCONTRADA("serie_nao_encontrada", "Série informada não encontrada"),
    ATIVIDADE_NAO_ENCONTRADA("atividade_nao_encontrada", "Atividade informada não encontrada"),
    ERRO_INCLUIR_SERIE("erro_incluir_serie", "Ocorreu um erro ao incluir a Série informada"),
    ERRO_ALTERAR_SERIE("erro_alterar_serie", "Ocorreu um erro ao alterar a Série informada"),
    ERRO_EXCLUIR_SERIE("erro_excluir_serie", "Ocorreu um erro ao excluir a Série informada"),
    ERRO_ESPELHAR_SERIE("erro_espelhar_serie", "Erro ao tentar espelhar serie"),
    ERRO_BUSCAR_SERIES("erro_buscar_series", "Erro ao buscar as series da atividade ficha")
    ;

    private String chave;
    private String descricao;

    SerieExcecoes(String chave, String descricao) {
        this.chave = chave;
        this.descricao = descricao;
    }

    @Override
    public String getChaveExcecao() {
        return chave;
    }
    @Override
    public String getDescricaoExcecao() {
        return descricao;
    }
}
