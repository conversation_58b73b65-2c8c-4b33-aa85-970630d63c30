package br.com.pacto.service.impl.avaliacao;

import br.com.pacto.bean.anamnese.AgrupamentoAvaliacaoIntegradaEnum;
import br.com.pacto.bean.anamnese.Anamnese;
import br.com.pacto.bean.anamnese.AnamneseTO;
import br.com.pacto.bean.anamnese.Movimento3D;
import br.com.pacto.bean.anamnese.Movimento3DEnum;
import br.com.pacto.bean.anamnese.OpcaoPergunta;
import br.com.pacto.bean.anamnese.PerguntaAnamnese;
import br.com.pacto.bean.anamnese.RespostaCliente;
import br.com.pacto.bean.anamnese.ResultadoMovimentoEnum;
import br.com.pacto.bean.anamnese.ResultadoVidaEnum;
import br.com.pacto.bean.anamnese.TipoMovimento3DEnum;
import br.com.pacto.bean.avaliacao.AnamnesePerguntaRespostaDTO;
import br.com.pacto.bean.avaliacao.AnamnesePerguntaRespostaDTOUpdate;
import br.com.pacto.bean.avaliacao.AvaliacaoIntegradaDTO;
import br.com.pacto.bean.avaliacao.AvaliacaoIntegradaDTOUptade;
import br.com.pacto.bean.avaliacao.ItemAvaliacaoFisica;
import br.com.pacto.bean.avaliacao.ItemAvaliacaoFisicaEnum;
import br.com.pacto.bean.avaliacao.Movimento3DDTO;
import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.avaliacao.AvaliadorDTO;
import br.com.pacto.dao.intf.avaliacao.ItemAvaliacaoFisicaDao;
import br.com.pacto.enumerador.anamnese.TiposPerguntaEnum;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.anamnese.AnamneseService;
import br.com.pacto.service.intf.avaliacao.AvaliacaoFisicaService;
import br.com.pacto.service.intf.avaliacao.AvaliacaoIntegradaService;
import br.com.pacto.service.intf.cliente.ClienteSinteticoService;
import br.com.pacto.service.intf.empresa.EmpresaService;
import br.com.pacto.service.intf.usuario.UsuarioService;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.impl.Ordenacao;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import java.net.URL;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

@Service
public class AvaliacaoIntegradaImpl implements AvaliacaoIntegradaService {

    private static String CHAVE_MORMAII = "a46fc753befe2c9cfdd7b3b0908bdfed";

    @Autowired
    private ItemAvaliacaoFisicaDao itemDao;
    @Autowired
    private AvaliacaoFisicaService avaliacaoFisicaService;
    @Autowired
    private AnamneseService anamneseService;
    @Autowired
    private UsuarioService usuarioService;
    @Autowired
    private ClienteSinteticoService csService;
    @Autowired
    private EmpresaService empresaService;

    @Override
    public List<AvaliacaoIntegradaDTO> allByAluno(final String ctx, final int cliente) throws ServiceException {
        List<ItemAvaliacaoFisica> avaliacoes = consultarAvIntegradasAluno(ctx, cliente);
        List<AvaliacaoIntegradaDTO> avIntegradasDTO = toListAvIntegradaDTO(ctx, avaliacoes);
        return avIntegradasDTO;
    }

    public List<ItemAvaliacaoFisica> consultarAvIntegradasAluno(final String ctx, final int cliente) throws ServiceException {
        try {
            StringBuilder sql = new StringBuilder("SELECT obj FROM ItemAvaliacaoFisica obj ");
            sql.append(" WHERE obj.cliente.codigo = :cliente AND obj.item = :i ");
            sql.append(" ORDER by obj.dataResposta DESC, obj.codigo DESC");
            HashMap params = new HashMap<String, Object>();
            params.put("cliente", cliente);
            params.put("i", ItemAvaliacaoFisicaEnum.AVALIACAO_INTEGRADA);

            return itemDao.findByParam(ctx, sql.toString(), params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<AvaliacaoIntegradaDTO> toListAvIntegradaDTO(String ctx, List<ItemAvaliacaoFisica> avaliacoes) throws ServiceException {
        List<AvaliacaoIntegradaDTO> avaliacoesDTO = new ArrayList<>();
        for (ItemAvaliacaoFisica item : avaliacoes) {
            AvaliacaoIntegradaDTO integradaDTO = new AvaliacaoIntegradaDTO();
            integradaDTO.setId(item.getCodigo());
            Usuario usu = usuarioService.obterPorId(ctx, item.getResponsavelLancamento_codigo());
            integradaDTO.setAvaliador(
                new AvaliadorDTO(
                    item.getResponsavelLancamento_codigo(),
                        usu.getNomeProfessor()
                )
            );
            integradaDTO.setDataLancamento(item.getDataLancamento().getTime());
            item.getAnamnese().setPerguntas(anamneseService.obterPerguntasAnamnese(ctx, item.getAnamnese().getCodigo()));
            verAavaliacao(ctx, item);
            HashMap<AgrupamentoAvaliacaoIntegradaEnum, List<PerguntaAnamnese>> agrupamento = montarAgrupamentoPerguntas(item.getAnamnese());

            item = calcularResultadoMovimento(agrupamento.get(AgrupamentoAvaliacaoIntegradaEnum.QUALIDADE_MOVIMENTO), item, item.getMovimentos3D());
            integradaDTO.setSomaQualidadeMovimento(item.getSomaQualidadeMovimento());
            integradaDTO.setQualidadeMovimento(ResultadoMovimentoEnum.getResult(item.getSomaQualidadeMovimento()));

            item = calcularResultadoVida(agrupamento.get(AgrupamentoAvaliacaoIntegradaEnum.QUALIDADE_VIDA), item);
            integradaDTO.setSomaQualidadeVida(item.getSomaQualidadeVida());
            integradaDTO.setQualidadeVida(ResultadoVidaEnum.getResult(item.getSomaQualidadeVida()));

            avaliacoesDTO.add(integradaDTO);
        }

        return avaliacoesDTO;
    }

    public void verAavaliacao(String ctx, ItemAvaliacaoFisica item) throws ServiceException {
        try {
            selecionarAnamnese(ctx, item.getAnamnese());
            avaliacaoFisicaService.obterRespostas(ctx, item.getAnamnese(), item);
            List<Movimento3D> movimento3DS = avaliacaoFisicaService.obterMovimento3D(ctx, item);
            List<Movimento3D> mobilidade = new ArrayList<Movimento3D>();
            List<Movimento3D> estabilidade = new ArrayList<Movimento3D>();
            for (Movimento3D m : movimento3DS) {
                if (m.getMovimento().getTipo().equals(TipoMovimento3DEnum.mobilidade)) {
                    mobilidade.add(m);
                } else {
                    estabilidade.add(m);
                }
            }
        } catch (ServiceException e) {
            throw new ServiceException(e.getMessage(), e);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void selecionarAnamnese(String ctx, Anamnese a) throws ServiceException {
        for (PerguntaAnamnese pa : a.getPerguntas()) {
            if (pa.getPergunta().getTipoPergunta().equals(TiposPerguntaEnum.SIMPLES_ESCOLHA)
                    || pa.getPergunta().getTipoPergunta().equals(TiposPerguntaEnum.MULTIPLA_ESCOLHA)) {
                pa.getPergunta().setOpcoes(anamneseService.obterOpcoes(ctx, pa.getPergunta().getCodigo()));
                if (pa.getPergunta().getAgrupamento() != null &&
                        !pa.getPergunta().getAgrupamento().equals(AgrupamentoAvaliacaoIntegradaEnum.GERAL)) {
                    pa.getPergunta().setOpcoes(Ordenacao.ordenarLista(pa.getPergunta().getOpcoes(), "peso"));
                }
            }
        }
    }

    public HashMap<AgrupamentoAvaliacaoIntegradaEnum, List<PerguntaAnamnese>> montarAgrupamentoPerguntas(Anamnese a) throws ServiceException {
        HashMap<AgrupamentoAvaliacaoIntegradaEnum, List<PerguntaAnamnese>> agrupamento = new HashMap<>();
        for (PerguntaAnamnese pa : a.getPerguntas()) {
            if (pa.getPergunta().getAgrupamento() == null) {
                continue;
            }
            List<PerguntaAnamnese> perguntas = agrupamento.get(pa.getPergunta().getAgrupamento());
            if (perguntas == null) {
                perguntas = new ArrayList<PerguntaAnamnese>();
                agrupamento.put(pa.getPergunta().getAgrupamento(), perguntas);
            }
            perguntas.add(pa);
        }

        return agrupamento;
    }

    @Override
    public Integer saveOrUpdate(final String ctx, Integer idAluno, Integer idUsuario, AvaliacaoIntegradaDTOUptade avaliacaoIntegrada) throws ServiceException {
        Integer codItemAvIntegrada = null;

        try {
            Anamnese anamnese = anamneseService.getAnamneseForId(ctx, avaliacaoIntegrada.getAnamneseSelecionadaId());
            anamnese.setPerguntas(anamneseService.obterPerguntasAnamnese(ctx, anamnese.getCodigo()));
            selecionarAnamnese(ctx, anamnese);
            anamnese = montarRespostasAnamnese(ctx, anamnese, avaliacaoIntegrada);

            if (UteisValidacao.emptyNumber(avaliacaoIntegrada.getId())) {
                ItemAvaliacaoFisica item = montarItemAvIntegrada(avaliacaoIntegrada, anamnese, new ItemAvaliacaoFisica());
                ClienteSintetico cs = csService.obterPorId(ctx, idAluno);
                Usuario usuario = usuarioService.obterPorId(ctx, idUsuario);

                ItemAvaliacaoFisica itemAvaliacaoFisica = avaliacaoFisicaService.adicionarItemAvaliacaoIntegrada(
                        ctx,
                        item,
                        anamnese.getDescricao(),
                        cs,
                        usuario,
                        anamnese,
                        new Date(avaliacaoIntegrada.getDataAvaliacao())
                );

                codItemAvIntegrada = item.getCodigo();
                anamneseService.gravarAnamneseCliente(ctx, anamnese, cs, item);
                avaliacaoFisicaService.gravarMovimento3D(ctx, item, avaliacaoIntegrada.getEstabilidade());
                avaliacaoFisicaService.gravarMovimento3D(ctx, item, avaliacaoIntegrada.getMobilidade());
            } else {
                ItemAvaliacaoFisica item = avaliacaoFisicaService.consultarItemCodigo(ctx, avaliacaoIntegrada.getId());
                item = montarItemAvIntegrada(avaliacaoIntegrada, anamnese, item);
                item.setAnamnese(anamnese);
                prepararMovimento3DParaSalvar(ctx, avaliacaoIntegrada, item);
                anamneseService.alterarAvaliacaoIntegrada(ctx, item, anamnese, avaliacaoIntegrada.getMobilidade(), avaliacaoIntegrada.getEstabilidade());
                avaliacaoFisicaService.alterarItemAvaliacaoIntegrada(ctx, item, anamnese.getDescricao(), anamnese, new Date(avaliacaoIntegrada.getDataAvaliacao()));
                codItemAvIntegrada = avaliacaoIntegrada.getId();
            }
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage(), ex);
        }

        return codItemAvIntegrada;
    }

    private void prepararMovimento3DParaSalvar(String ctx, AvaliacaoIntegradaDTOUptade avaliacaoIntegrada, ItemAvaliacaoFisica item) throws Exception {
        List<Movimento3D> movimento3DS = avaliacaoFisicaService.obterMovimento3D(ctx, item);
        movimento3DS.forEach(mov-> {
            avaliacaoIntegrada.getMobilidade().forEach(mob -> {
                if (mov.getMovimento().equals(mob.getMovimento())) {
                    mob.setCodigo(mov.getCodigo());
                    mob.setItem(mov.getItem());
                }
            });
            avaliacaoIntegrada.getEstabilidade().forEach(est -> {
                if (mov.getMovimento().equals(est.getMovimento())) {
                    est.setCodigo(mov.getCodigo());
                    est.setItem(mov.getItem());
                }
            });
        });
    }

    private Anamnese montarRespostasAnamnese(String ctx, Anamnese anamnese, AvaliacaoIntegradaDTOUptade avaliacaoIntegrada) throws ServiceException {
        Collection<PerguntaAnamnese> perguntaAnamneseList = new ArrayList<PerguntaAnamnese>();
        for (AnamnesePerguntaRespostaDTOUpdate objAnamnese : avaliacaoIntegrada.getAnamneseRespostas()) {
            ((ArrayList<PerguntaAnamnese>) perguntaAnamneseList).add(avaliacaoFisicaService.preencherListaPerguntaResposta(ctx, objAnamnese, anamnese));
        }
        anamnese.setPerguntas((List<PerguntaAnamnese>) perguntaAnamneseList);

        return anamnese;
    }

    private ItemAvaliacaoFisica montarItemAvIntegrada(AvaliacaoIntegradaDTOUptade avIn, Anamnese anamnese, ItemAvaliacaoFisica item) throws ServiceException {
        item.setDataResposta(new Date(avIn.getDataAvaliacao()));

        item.setSomaMobilidadeDir(avIn.getSomaMobilidadeDir());
        item.setSomaEstabilidadeDir(avIn.getSomaEstabilidadeDir());
        item.setMediaMobilidadeDir(avIn.getMediaMobilidadeDir());
        item.setMediaEstabilidadeDir(avIn.getMediaEstabilidadeDir());

        item.setSomaMobilidadeEsq(avIn.getSomaMobilidadeEsq());
        item.setSomaEstabilidadeEsq(avIn.getSomaEstabilidadeEsq());
        item.setMediaMobilidadeEsq(avIn.getMediaMobilidadeEsq());
        item.setMediaEstabilidadeEsq(avIn.getMediaEstabilidadeEsq());

        List<Movimento3D> movimento3DS = new ArrayList<>();
        movimento3DS.addAll(avIn.getMobilidade());
        movimento3DS.addAll(avIn.getEstabilidade());
        HashMap<AgrupamentoAvaliacaoIntegradaEnum, List<PerguntaAnamnese>> agrupamento = montarAgrupamentoPerguntas(anamnese);
        item = calcularResultadoMovimento(agrupamento.get(AgrupamentoAvaliacaoIntegradaEnum.QUALIDADE_MOVIMENTO), item, movimento3DS);
        item = calcularResultadoVida(agrupamento.get(AgrupamentoAvaliacaoIntegradaEnum.QUALIDADE_VIDA), item);

        return item;
    }

    public ItemAvaliacaoFisica calcularResultadoMovimento(List<PerguntaAnamnese> perguntas, ItemAvaliacaoFisica item, List<Movimento3D> movimento3DS) {
        Double resultado = perguntas == null ? 0.0 : somarPeso(perguntas);
        item.setSomaMobilidadeDir(0);
        item.setSomaMobilidadeEsq(0);
        item.setSomaEstabilidadeDir(0);
        item.setSomaEstabilidadeEsq(0);
        for (Movimento3D m3d : movimento3DS) {
            if (m3d.getMovimento().getTipo().equals(TipoMovimento3DEnum.mobilidade)) {
                item.setSomaMobilidadeDir(m3d.getDireita() + item.getSomaMobilidadeDir());
                item.setSomaMobilidadeEsq(m3d.getEsquerda() + item.getSomaMobilidadeEsq());
            } else {
                item.setSomaEstabilidadeDir(m3d.getDireita() + item.getSomaEstabilidadeDir());
                item.setSomaEstabilidadeEsq(m3d.getEsquerda() + item.getSomaEstabilidadeEsq());
            }
        }
        item.setMediaEstabilidadeDir(Uteis.arredondar(item.getSomaEstabilidadeDir() / 3.0, 1));
        item.setMediaEstabilidadeEsq(Uteis.arredondar(item.getSomaEstabilidadeEsq() / 3.0, 1));

        item.setMediaMobilidadeDir(Uteis.arredondar(item.getSomaMobilidadeDir() / 4.0, 1));
        item.setMediaMobilidadeEsq(Uteis.arredondar(item.getSomaMobilidadeEsq() / 4.0, 1));

        resultado = item.getMediaEstabilidadeDir() + item.getMediaEstabilidadeEsq()
                + item.getMediaMobilidadeDir() + item.getMediaMobilidadeEsq()
                + resultado;
        item.setSomaQualidadeMovimento(resultado);
        item.setQualidadeMovimento(ResultadoMovimentoEnum.getResult(resultado));

        return item;
    }

    public ItemAvaliacaoFisica calcularResultadoVida(List<PerguntaAnamnese> perguntas, ItemAvaliacaoFisica item) {
        Double resultado = perguntas == null ? 0.0 : somarPeso(perguntas);
        item.setSomaQualidadeVida(resultado.intValue());
        item.setQualidadeVida(ResultadoVidaEnum.getResult(resultado.intValue()));

        return item;
    }

    public Double somarPeso(List<PerguntaAnamnese> perguntas) {
        Double resultado = 0.0;
        for (PerguntaAnamnese pa : perguntas) {
            for (OpcaoPergunta op : pa.getPergunta().getOpcoes()) {
                if (pa.getPergunta().getTipoPergunta().equals(TiposPerguntaEnum.MULTIPLA_ESCOLHA)) {
                    if (pa.getRespostas() == null) {
                        continue;
                    }
                    for (String r : pa.getRespostas()) {
                        if (r.equals(op.getCodigo().toString())) {
                            resultado += op.getPeso();
                        }
                    }
                } else {
                    if (pa.getResposta() != null && pa.getResposta().equals(op.getCodigo().toString())) {
                        resultado += op.getPeso();
                    }
                }
            }
        }

        return resultado;
    }

    @Override
    public AvaliacaoIntegradaDTO findAvIntegradaById(final String ctx, final Integer idAvIntegrada) throws ServiceException {
        AvaliacaoIntegradaDTO avaliacaoDTO = new AvaliacaoIntegradaDTO();

        try {
            ItemAvaliacaoFisica item = itemDao.findById(ctx, idAvIntegrada);
            if (item != null) {
                avaliacaoDTO = toAvIntegradaDTO(ctx, item);
            }
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage(), ex);
        }

        return avaliacaoDTO;
    }

    public AvaliacaoIntegradaDTO toAvIntegradaDTO(String ctx, ItemAvaliacaoFisica item) throws Exception {
        AvaliacaoIntegradaDTO dto = new AvaliacaoIntegradaDTO();
        AnamnesePerguntaRespostaDTO anamnesePerguntaRespostaDTO = new AnamnesePerguntaRespostaDTO();

        dto.setId(item.getCodigo());
        dto.setDataLancamento(item.getDataLancamento().getTime());
        Usuario usu = usuarioService.obterPorId(ctx, item.getResponsavelLancamento_codigo());
        dto.setAvaliador(
            new AvaliadorDTO(
                    item.getResponsavelLancamento_codigo(),
                    usu.getNomeProfessor()
            )
        );
        dto.setAnamneseRespostas(new ArrayList<AnamnesePerguntaRespostaDTO>());
        if (item != null) {
            Anamnese anamnese = item.getAnamnese();
            anamnese.setPerguntas(anamneseService.obterPerguntasAnamnese(ctx, anamnese.getCodigo()));
            for (PerguntaAnamnese pa : anamnese.getPerguntas()) {
                if (pa.getPergunta().getTipoPergunta().equals(TiposPerguntaEnum.SIMPLES_ESCOLHA)
                        || pa.getPergunta().getTipoPergunta().equals(TiposPerguntaEnum.MULTIPLA_ESCOLHA)) {
                    pa.getPergunta().setOpcoes(anamneseService.obterOpcoes(ctx, pa.getPergunta().getCodigo()));
                }
                RespostaCliente respostaCliente = anamneseService.obterRespostaPerguntasAnamnese(
                        ctx, pa.getCodigo(), item.getCliente().getCodigo(), item.getCodigo());
                if (respostaCliente != null) {
                    if (pa.getPergunta().getTipoPergunta().equals(TiposPerguntaEnum.SIMPLES_ESCOLHA)
                            || pa.getPergunta().getTipoPergunta().equals(TiposPerguntaEnum.MULTIPLA_ESCOLHA)) {

                        List<Integer> respostas = new ArrayList<Integer>();
                        if(!UteisValidacao.emptyString(respostaCliente.getResposta())){
                            String[] split = respostaCliente.getResposta().split("\\_");
                            for(String r : split){
                                if (anamnesePerguntaRespostaDTO.isNumber(r)){
                                    respostas.add(Integer.valueOf(r));
                                }
                            }
                        }
                        Integer verifica = 0;
                        for( Integer r : respostas){
                            for(int j = 0; j< pa.getPergunta().getOpcoes().size(); j++) {
                                if(pa.getPergunta().getOpcoes().get(j).getCodigo().equals(r)) {
                                    verifica++;
                                }
                            }
                        }
                        if(verifica < respostas.size()) {
                            respostaCliente.setResposta("");
                        }
                    }
                    dto.getAnamneseRespostas().add(new AnamnesePerguntaRespostaDTO(respostaCliente));
                }
            }
            dto.setAnamneseSelecionada(new AnamneseTO(anamnese));
        }

        List<Movimento3D> movimento3DS = avaliacaoFisicaService.obterMovimento3D(ctx, item);
        List<Movimento3DDTO> mobilidade = new ArrayList<>();
        List<Movimento3DDTO> estabilidade = new ArrayList<>();
        movimento3DS.forEach(x -> {
            Movimento3DDTO mov = toMovimento3DDTO(x);
            if (isMobilidade(x)) {
                mobilidade.add(mov);
            } else {
                estabilidade.add(mov);
            }
        });
        dto.setMobilidade(mobilidade);
        dto.setEstabilidade(estabilidade);

        dto.setSomaMobilidadeDir(item.getSomaMobilidadeDir());
        dto.setSomaEstabilidadeDir(item.getSomaEstabilidadeDir());
        dto.setMediaMobilidadeDir(item.getMediaMobilidadeDir());
        dto.setMediaEstabilidadeDir(item.getMediaEstabilidadeDir());
        dto.setSomaMobilidadeEsq(item.getSomaMobilidadeEsq());
        dto.setSomaEstabilidadeEsq(item.getSomaEstabilidadeEsq());
        dto.setMediaMobilidadeEsq(item.getMediaMobilidadeEsq());
        dto.setMediaEstabilidadeEsq(item.getMediaEstabilidadeEsq());

        return dto;
    }

    private boolean isMobilidade(Movimento3D movimento3D) {
        if (movimento3D.getMovimento().equals(Movimento3DEnum.cadeia_anterior) ||
            movimento3D.getMovimento().equals(Movimento3DEnum.cadeia_posterior) ||
            movimento3D.getMovimento().equals(Movimento3DEnum.cadeia_lateral) ||
            movimento3D.getMovimento().equals(Movimento3DEnum.cadeia_rotacional)) {
            return true;
        }

        return false;
    }

    private Movimento3DDTO toMovimento3DDTO(Movimento3D movimento) {
        return new Movimento3DDTO(
                movimento.getCodigo(),
                movimento.getItem().getCodigo(),
                movimento.getMovimento().ordinal(),
                movimento.getEsquerda(),
                movimento.getDireita()
        );
    }

    @Override
    public void delete(final String ctx, Integer idAvIntegrada) throws ServiceException {
        try {
            ItemAvaliacaoFisica item = avaliacaoFisicaService.consultarItemCodigo(ctx, idAvIntegrada);
            anamneseService.excluirAvaliacaoIntegrada(ctx, item);
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage(), ex);
        }
    }

    @Override
    public String printIndividualAvIntegrada(final String ctx, final Integer idAvIntegrada, Integer idUsuario, HttpServletRequest request) throws Exception {
        ItemAvaliacaoFisica item = avaliacaoFisicaService.consultarItemCodigo(ctx, idAvIntegrada);
        if (item == null || item.getCodigo() == null) {
            throw new ServiceException("Não foi possível localizar a avaliação para impressão.");
        }
        Usuario usuario = usuarioService.obterPorId(ctx, idUsuario);
        URL url = new URL(request.getRequestURL().toString());
        String linkPrint = getLinkPrint(ctx, idAvIntegrada, usuario);

        String urlAvaliacao = "";
        if (url.getPort() == -1) {
            urlAvaliacao = String.format("%s://%s%s/%s", new Object[]{
                    url.getProtocol(),
                    url.getHost(),
                    request.getContextPath(),
                    linkPrint.replaceFirst("../", "")
            });
        } else {
            String validHttps = String.valueOf(url.getPort());
            urlAvaliacao = String.format("%s://%s:%s%s/%s", new Object[]{
                    validHttps.startsWith("90") ? url.getProtocol() + "s" : url.getProtocol(),
                    url.getHost(),
                    url.getPort(),
                    request.getContextPath(),
                    linkPrint.replaceFirst("../", "")
            });
        }

        return urlAvaliacao;
    }

    private String getLinkPrint(String ctx, Integer codigoItemAvaliacao, Usuario usuario) throws Exception {
        JSONObject j = new JSONObject();
        j.put("k", ctx);
        j.put("u", usuario.getCodigo());
        j.put("i", codigoItemAvaliacao);
        if (getApresentarImpressaoMormaii(ctx)) {
            j.put("e", usuario.getEmpresaDefault() != null ? usuario.getEmpresaDefault().getCodigo() : 0);
            return "../avaliacaofisicaintegradaimp?parametros=".concat(Uteis.encriptar(j.toString(), "cript0p4r4msint"));
        } else {
            return "../avaliacaointegrada?isComparativo=false&j=".concat(Uteis.encriptar(j.toString(), "cript0p4r4msint"));
        }
    }

    /**
     * Esse novo layout de impressão da avaliação integrada, vai está atendendo somente a Mormaii.
     * Pois a forma que foi construindo o questionário, não tem como está atendendo outras academias
     * @return
     */
    public Boolean getApresentarImpressaoMormaii(String ctx) {
        return ctx.equals(CHAVE_MORMAII);
    }

    @Override
    public String printGroupAvIntegrada(final String ctx, Integer idUsuario, String codigos, HttpServletRequest request) throws Exception {
        String linkPrint = getLinkPrintComp(ctx, codigos, idUsuario);
        URL url = new URL(request.getRequestURL().toString());

        String urlAvaliacao = "";
        if (url.getPort() == -1) {
            urlAvaliacao = String.format("%s://%s%s/%s", new Object[]{
                    url.getProtocol(),
                    url.getHost(),
                    request.getContextPath(),
                    linkPrint.replaceFirst("../", "")
            });
        } else {
            String validHttps = String.valueOf(url.getPort());
            urlAvaliacao = String.format("%s://%s:%s%s/%s", new Object[]{
                    validHttps.startsWith("90") ? url.getProtocol() + "s" : url.getProtocol(),
                    url.getHost(),
                    url.getPort(),
                    request.getContextPath(),
                    linkPrint.replaceFirst("../", "")
            });
        }

        return urlAvaliacao;
    }

    private String getLinkPrintComp(String ctx, String codigos, Integer idUsuario) throws Exception {
        JSONObject j = new JSONObject();
        j.put("k", ctx);
        j.put("u", idUsuario);
        j.put("i", codigos);
        return("../avaliacaointegrada?isComparativo=true&j=".concat(Uteis.encriptar(j.toString(), "cript0p4r4msint")));
    }

    @Override
    public void sendIndividualAvIntegrada(final String ctx,
                                          Integer idAluno,
                                          Integer idAvaliacao,
                                          Integer idUsuario,
                                          HttpServletRequest request,
                                          ServletContext servletContext) throws ServiceException {
        try {
            ClienteSintetico cs = csService.obterPorId(ctx, idAluno);
            List<String> listaEmails = cs.getListaEmails();
            String[] emails = new String[listaEmails.size()];
            emails = listaEmails.toArray(emails);
            ItemAvaliacaoFisica item = avaliacaoFisicaService.consultarItemCodigo(ctx, idAvaliacao);
            Usuario usuario = usuarioService.obterPorId(ctx, idUsuario);
            String nomeEmpresa = usuario.getEmpresaLogada() != null ? usuario.getEmpresaLogada().getNome() : empresaService.obterPorIdZW(ctx, cs.getEmpresa()).getNome();

            avaliacaoFisicaService.enviarAvaliacaoIntegrada(
                    ctx,
                    item,
                    nomeEmpresa,
                    usuario,
                    emails,
                    servletContext,
                    request,
                    getLinkPrint(ctx, item.getCodigo(), usuario)
            );
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage(), ex);
        }
    }

    @Override
    public void sendGrupoAvIntegrada(final String ctx,
                                     Integer idAluno,
                                     String idAvaliacoes,
                                     Integer idUsuario,
                                     HttpServletRequest request,
                                     ServletContext servletContext) throws ServiceException {
        try {
            ClienteSintetico cs = csService.obterPorId(ctx, idAluno);
            List<String> listaEmails = cs.getListaEmails();
            String[] emails = new String[listaEmails.size()];
            emails = listaEmails.toArray(emails);
            Usuario usuario = usuarioService.obterPorId(ctx, idUsuario);
            String nomeEmpresa = usuario.getEmpresaLogada() != null ? usuario.getEmpresaLogada().getNome() : empresaService.obterPorIdZW(ctx, cs.getEmpresa()).getNome();
            List<ItemAvaliacaoFisica> itens = new ArrayList<>();
            String[] ids = idAvaliacoes.split("_");
            for (String idItem : ids) {
                ItemAvaliacaoFisica item = avaliacaoFisicaService.consultarItemCodigo(ctx, Integer.valueOf(idItem));
                if (item != null) {
                    itens.add(item);
                }
            }

            avaliacaoFisicaService.enviarComparativoAvaliacaoIntegrada(
                    ctx,
                    itens,
                    nomeEmpresa,
                    usuario,
                    emails,
                    servletContext
            );
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage(), ex);
        }
    }

}
