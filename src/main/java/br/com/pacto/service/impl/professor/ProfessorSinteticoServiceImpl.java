/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.impl.professor;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.impl.EntityManagerService;
import br.com.pacto.base.util.ExecuteRequestHttpService;
import br.com.pacto.bean.bi.AgrupamentoIndicadorDashboardEnum;
import br.com.pacto.bean.bi.ConfiguracaoRankingProfessores;
import br.com.pacto.bean.bi.DashboardBI;
import br.com.pacto.bean.bi.IndicadorDashboardEnum;
import br.com.pacto.bean.cliente.ClienteAcompanhamento;
import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.colaborador.ColaboradorResponseTO;
import br.com.pacto.bean.colaborador.ColaboradorSimplesTO;
import br.com.pacto.bean.colaborador.ColaboradorTO;
import br.com.pacto.bean.colaborador.EmailTO;
import br.com.pacto.bean.colaborador.FiltroColaboradorJSON;
import br.com.pacto.bean.colaborador.SituacaoColaboradorEnum;
import br.com.pacto.bean.colaborador.TelefoneTO;
import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.bean.gestao.AgrupadorIndicadores;
import br.com.pacto.bean.gestao.CategoriaIndicadorEnum;
import br.com.pacto.bean.gestao.IndicadorEnum;
import br.com.pacto.bean.gestao.SituacaoContratoEnum;
import br.com.pacto.bean.gestaopersonal.ConfiguracaoPersonalEmpresa;
import br.com.pacto.bean.pessoa.Email;
import br.com.pacto.bean.pessoa.Pessoa;
import br.com.pacto.bean.pessoa.Telefone;
import br.com.pacto.bean.professor.ConfigRelatorio;
import br.com.pacto.bean.professor.IndicadorAtividadeProfessorAcumuladoVO;
import br.com.pacto.bean.professor.ProfessorDisponivel;
import br.com.pacto.bean.professor.ProfessorSintetico;
import br.com.pacto.bean.programa.HistoricoRevisaoProgramaTreino;
import br.com.pacto.bean.programa.ProfessorBIResponseTO;
import br.com.pacto.bean.programa.ProfessorResponseTO;
import br.com.pacto.bean.programa.ProgramaTreino;
import br.com.pacto.bean.tipoEvento.TipoEvento;
import br.com.pacto.bean.usuario.StatusEnum;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.bean.usuario.UsuarioColaboradorTO;
import br.com.pacto.controller.json.aluno.AlunoSimplesDTO;
import br.com.pacto.controller.json.aluno.SituacaoAlunoEnum;
import br.com.pacto.controller.json.base.SuperControle;
import br.com.pacto.controller.json.gestao.BITreinoTreinamentoDTO;
import br.com.pacto.controller.json.professor.ConfiguracaoRankingBuilder;
import br.com.pacto.controller.json.professor.ConfiguracaoRankingDTO;
import br.com.pacto.controller.json.professor.FiltroGestaoJSON;
import br.com.pacto.controller.json.professor.ProfessorIndicadorResponseDTO;
import br.com.pacto.controller.json.professor.ProfessoresAlunosAvisoMedicoResponseDTO;
import br.com.pacto.controller.json.professor.SituacaoProgramaEnum;
import br.com.pacto.controller.to.FiltrosGestaoTO;
import br.com.pacto.dao.intf.cliente.ClienteAcompanhamentoDao;
import br.com.pacto.dao.intf.dashboardbi.ConfiguracaoRankingProfessoresDao;
import br.com.pacto.dao.intf.pessoa.EmailDao;
import br.com.pacto.dao.intf.pessoa.PessoaDao;
import br.com.pacto.dao.intf.pessoa.TelefoneDao;
import br.com.pacto.dao.intf.professor.ConfigRelatorioDao;
import br.com.pacto.dao.intf.professor.ProfessorDisponivelDao;
import br.com.pacto.dao.intf.professor.ProfessorSinteticoDao;
import br.com.pacto.dao.intf.programa.HistoricoRevisaoProgramaTreinoDao;
import br.com.pacto.dao.intf.programa.ProgramaTreinoDao;
import br.com.pacto.dao.intf.usuario.UsuarioDao;
import br.com.pacto.dto.ColaboradorDTO;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.ColecaoUtils;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.exception.ValidacaoException;
import br.com.pacto.service.impl.cliente.ClienteSinteticoServiceImpl;
import br.com.pacto.service.impl.conexao.ConexaoZWServiceImpl;
import br.com.pacto.service.impl.configuracoes.ConfiguracaoSistemaServiceImpl;
import br.com.pacto.service.impl.notificacao.excecao.ColaboradoresExcecoes;
import br.com.pacto.service.impl.notificacao.excecao.ProfessorExcecoes;
import br.com.pacto.service.impl.usuario.UsuarioServiceImpl;
import br.com.pacto.service.intf.Validacao;
import br.com.pacto.service.intf.aulapersonal.ConfiguracaoPersonalEmpresaService;
import br.com.pacto.service.intf.cliente.ClienteSinteticoService;
import br.com.pacto.service.intf.empresa.EmpresaService;
import br.com.pacto.service.intf.gestao.BITreinoService;
import br.com.pacto.service.intf.gestao.DashboardBIService;
import br.com.pacto.service.intf.gestao.GestaoService;
import br.com.pacto.service.intf.memcached.CachedManagerInterfaceFacade;
import br.com.pacto.service.intf.perfil.PerfilService;
import br.com.pacto.service.intf.pessoa.PessoaService;
import br.com.pacto.service.intf.professor.ProfessorSinteticoService;
import br.com.pacto.service.intf.programa.ProgramaTreinoService;
import br.com.pacto.service.intf.tipoEvento.TipoEventoService;
import br.com.pacto.service.intf.treinoindependente.TreinoIndependenteService;
import br.com.pacto.service.intf.usuario.FotoService;
import br.com.pacto.service.intf.usuario.UsuarioService;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;
import br.com.pacto.util.UtilS3Base64Img;
import br.com.pacto.util.ViewUtils;
import br.com.pacto.util.bean.GenericoTO;
import br.com.pacto.util.impl.Ordenacao;
import br.com.pacto.webservice.TreinoWS;
import org.apache.commons.collections.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.NameValuePair;
import org.apache.http.client.ResponseHandler;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.BasicResponseHandler;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.message.BasicNameValuePair;
import org.hibernate.Criteria;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.context.ServletContextAware;
import servicos.integracao.midias.MidiaService;
import servicos.integracao.zw.IntegracaoCadastrosWSConsumer;
import servicos.integracao.zw.IntegracaoTurmasWSConsumer;
import servicos.integracao.zw.json.AddColaboradorJSON;
import servicos.integracao.zw.json.VinculoJSON;

import javax.faces.model.SelectItem;
import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class ProfessorSinteticoServiceImpl implements ProfessorSinteticoService, ServletContextAware {

    @Autowired
    private ViewUtils viewUtils;
    @Autowired
    private Validacao validacao;
    @Autowired
    private ProfessorSinteticoDao professorsinteticoDao;
    @Autowired
    private ProfessorDisponivelDao professorDisponivelDao;
    @Autowired
    private UsuarioDao usuarioDao;
    @Autowired
    private ProgramaTreinoDao programaDao;
    @Autowired
    private ConfiguracaoPersonalEmpresaService cfgPersonalEmpresaService;
    @Autowired
    private EmpresaService empresaService;
    @Autowired
    private SessaoService sessaoService;
    @Autowired
    private GestaoService gestaoService;
    @Autowired
    private ProgramaTreinoService programaService;
    @Autowired
    private UsuarioService usuarioService;
    @Autowired
    private PerfilService perfilService;
    @Autowired
    private PessoaService pessoaService;
    @Autowired
    private TreinoIndependenteService treinoIndependenteService;
    @Autowired
    private EmailDao emailDao;
    @Autowired
    private TelefoneDao telefoneDao;
    @Autowired
    private PessoaDao pessoaDao;
    @Autowired
    private HistoricoRevisaoProgramaTreinoDao historicoRevisaoDao;
    @Autowired
    private ClienteAcompanhamentoDao clienteAcompanhamentoDao;
    @Autowired
    private ConfigRelatorioDao configRelatorioDao;
    @Autowired
    private FotoService fotoService;
    private ServletContext context;
    @Autowired
    private TipoEventoService tipoEventoService;
    @Autowired
    private ClienteSinteticoService cs;
    @Autowired
    private  DashboardBIService dashboardBIService;
    @Autowired
    private BITreinoService biService;
    @Autowired
    private ConexaoZWServiceImpl conexaoZWService;
    @Autowired
    private CachedManagerInterfaceFacade memcached;

    private static final String SEPARADOR_ORDENACOES = ";";
    private static final String SEPARADOR_ORDENACAO = ",";
    private static final int INDEX_CAMPO_ORDENAR = 0;
    private static final int INDEX_DIRECAO_ORDENACAO = 1;

    public ViewUtils getViewUtils() {
        return viewUtils;
    }

    public void setViewUtils(ViewUtils viewUtils) {
        this.viewUtils = viewUtils;
    }

    public Validacao getValidacao() {
        return validacao;
    }

    public void setValidacao(Validacao validacao) {
        this.validacao = validacao;
    }

    public ProfessorSinteticoDao getProfessorSinteticoDao() {
        return this.professorsinteticoDao;
    }

    public void setProfessorSinteticoDao(ProfessorSinteticoDao professorsinteticoDao) {
        this.professorsinteticoDao = professorsinteticoDao;
    }

    @Override
    public ProfessorSintetico alterar(final String ctx, ProfessorSintetico object) throws ServiceException {
        try {
            object.setVersao(object.getVersao() == null ? 1 : (object.getVersao() + 1));
            object.setUriImagem(Uteis.getPaintFotoDaNuvem(object.getPessoa().getFotoKey()));
            return getProfessorSinteticoDao().update(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public void editSituacao(final String ctx, ProfessorSintetico object) throws ServiceException {
        try {
            StatusEnum statusEnum;
            if (object.isAtivo()) {
                object.setAtivo(false);
                statusEnum = StatusEnum.INATIVO;
            } else {
                object.setAtivo(true);
                statusEnum = StatusEnum.ATIVO;
            }
            object.setVersao(object.getVersao() == null ? 1 : (object.getVersao() + 1));
            getProfessorSinteticoDao().update(ctx, object);
            Usuario usuario = usuarioService.obterUsuarioPorColaborador(ctx,object.getCodigo());
            if (usuario != null && !UteisValidacao.emptyNumber(usuario.getCodigo())) {
                usuario.setStatus(statusEnum);
                usuarioService.alterar(ctx, usuario);
            }
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public void excluirAluno(final String ctx, ProfessorSintetico object) throws ServiceException {
        try {
            object.setVersao(object.getVersao() == null ? 1 : (object.getVersao() + 1));
            Usuario usuario = usuarioService.obterUsuarioPorColaborador(ctx,object.getCodigo());
            if (usuario != null && !UteisValidacao.emptyNumber(usuario.getCodigo())) {
                usuarioService.excluir(ctx, usuario);
            }
            getProfessorSinteticoDao().delete(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public ProfessorSintetico inserir(final String ctx, ProfessorSintetico object) throws ServiceException {
        try {
            return getProfessorSinteticoDao().insert(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public ProfessorSintetico insertOrMerge(final String ctx, ProfessorSintetico object) throws ServiceException {
        try {
            return getProfessorSinteticoDao().insertOrMerge(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public ProfessorSintetico obterObjetoPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException {
        try {
            return getProfessorSinteticoDao().findObjectByParam(ctx, query, params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public ProfessorSintetico obterPorId(final String ctx, Integer id) throws ServiceException {
        try {
            return getProfessorSinteticoDao().obterPorId(ctx, id);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public ProfessorSintetico obterPorCodigoPessoaZW(final String ctx, Integer codigoPessoa, Integer codigoEmpresaZw) throws ServiceException {
        try {
            return getProfessorSinteticoDao().findObjectByAttributes(ctx, new String[]{"codigoPessoa", "empresa.codZW"}, new Object[]{codigoPessoa, codigoEmpresaZw}, "nome");
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public List<ProfessorSintetico> obterProfessoresPorCodigoPessoaZw(final String ctx, Integer codigoPessoa) throws ServiceException {
        try {
            return getProfessorSinteticoDao().findListByAttributes(ctx, new String[]{"codigoPessoa"}, new Object[]{codigoPessoa}, "nome", 0, 0);
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    @Override
    public List<ProfessorSintetico> obterPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException {
        try {
            return getProfessorSinteticoDao().findByParam(ctx, query, params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public List<ProfessorSintetico> obterPorParam(final String ctx, String query,
                                                  Map<String, Object> params, int max, int index)
            throws ServiceException {
        try {
            return getProfessorSinteticoDao().findByParam(ctx, query, params, max, index);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public List<ProfessorSintetico> obterTodos(final String ctx, final Integer empresaZW, boolean somenteAtivos) throws ServiceException {
        try {
            if (!somenteAtivos && UteisValidacao.emptyNumber(empresaZW)) {
                return getProfessorSinteticoDao().findAll(ctx);
            } else if (somenteAtivos) {
                return getProfessorSinteticoDao().findListByAttributes(ctx,
                        new String[]{"empresa.codZW", "ativo"}, new Object[]{empresaZW, somenteAtivos}, "nome", 0);
            } else {
                return getProfessorSinteticoDao().findListByAttributes(ctx,
                        new String[]{"empresa.codZW"}, new Integer[]{empresaZW}, "nome", 0);
            }
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public List<ProfessorSintetico> obterTodos(final String ctx, boolean somenteAtivos) throws ServiceException {
        try {
            if (somenteAtivos) {
                return getProfessorSinteticoDao().findListByAttributes(ctx,
                        new String[]{"ativo"}, new Object[]{somenteAtivos}, "nome", 0);
            } else {
                return getProfessorSinteticoDao().findListByAttributes(ctx,
                        new String[]{}, new Integer[]{}, "nome", 0);
            }
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public ProfessorSintetico consultarPorCodigoColaborador(final String ctx, final Integer codigo) throws ServiceException {
        try {
            if (codigo == null) {
                return null;
            }
            String query = "select obj from ProfessorSintetico obj where obj.codigoColaborador = :codigo";
            HashMap<String, Object> p = new HashMap<String, Object>();
            p.put("codigo", codigo);
            return obterObjetoPorParam(ctx, query, p);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public ProfessorSintetico consultarPorCodigoPessoa(String ctx, String codigopessoa) throws ServiceException {
        try {
            return getProfessorSinteticoDao().findObjectByAttribute(ctx, "codigopessoa", Integer.valueOf(codigopessoa));
        } catch (Exception ex) {
            Uteis.logar(ex, ClienteSinteticoServiceImpl.class);
            throw new ServiceException(ex.getMessage());
        }
    }

    @Override
    public ProfessorSintetico obterProfessorTreino(String ctx, ClienteSintetico clienteEscolhido,
                                                   List<VinculoJSON> vinculos) throws ValidacaoException, ServiceException {
        return obterProfessorTreino(ctx, clienteEscolhido, vinculos, true);
    }

    public ProfessorSintetico obterProfessorTreino(String ctx, ClienteSintetico clienteEscolhido,
                                                   List<VinculoJSON> vinculos, boolean obrigarProfessor) throws ValidacaoException, ServiceException {
        if (clienteEscolhido.getProfessorSintetico() == null
                || clienteEscolhido.getProfessorSintetico().getCodigoColaborador() == null
                || clienteEscolhido.getProfessorSintetico().getCodigoColaborador().equals(0)) {
            if(obrigarProfessor){
                throw new ValidacaoException("validacao.professor");
            }
            return null;
        }
        ProfessorSintetico professor = consultarPorCodigoColaborador(ctx,
                clienteEscolhido.getProfessorSintetico().getCodigoColaborador());
        if (professor == null || professor.getCodigo() == null || professor.getCodigo().equals(0)) {
            VinculoJSON vinculoJSON = new VinculoJSON();
            for (VinculoJSON sinProfessor : vinculos) {
                if (sinProfessor.getCodColaborador().equals(clienteEscolhido.getProfessorSintetico().getCodigoColaborador())) {
                    vinculoJSON = sinProfessor;
                    break;
                }
            }
            professor = new ProfessorSintetico();
            professor.setCodigoColaborador(vinculoJSON.getCodColaborador());
            professor.setCodigoPessoa(vinculoJSON.getCodPessoaColaborador());
            professor.setNome(vinculoJSON.getNomeColaborador());
            professor.setEmpresa(empresaService.obterPorIdZW(ctx, clienteEscolhido.getEmpresa()));
            return inserir(ctx, professor);
        } else {
            return professor;
        }
    }

    @Override
    public List<SelectItem> montarComboProfessor(final String ctx,
                                                 final Integer empresaZW, final boolean somenteAtivos) throws ServiceException {
        List<SelectItem> lista = new ArrayList<SelectItem>();
        lista.add(new SelectItem(0, ""));
        for (ProfessorSintetico ps : obterTodos(ctx, empresaZW, somenteAtivos)) {
            lista.add(new SelectItem(ps.getCodigo(), ps.getNome()));
        }
        return lista;
    }

    public List<ProfessorSintetico> obterListaPersonais(String ctx, String param, Integer empresaZW) throws ServiceException {
        try {
            try {
                ProfessorSintetico porId = obterPorId(ctx, Integer.valueOf(param));
                List<ProfessorSintetico> lista = new ArrayList<ProfessorSintetico>();
                lista.add(porId);
                return lista;
            } catch (NumberFormatException numberExc) {
                String query = "select obj from ProfessorSintetico obj "
                        + "where obj.empresa.codZW = :empresa and nome like :nome";
                HashMap<String, Object> p = new HashMap<String, Object>();
                p.put("empresa", empresaZW);
                p.put("nome", param);
                return obterPorParam(ctx, "", p);
            }
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }

    }

    @Override
    public List<ProfessorSintetico> consultarPersonais(String ctx) throws ServiceException {
        try {
            Map<String, Object> params = new HashMap<String, Object>();
            return obterPorParam(ctx, "select obj from ProfessorSintetico obj where obj.personal is true and ativo is true", params);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public List<ProfessorSintetico> consultarPorCodigoOuNome(final String ctx,
                                                             final Integer empresaZW, final String param) throws ServiceException {
        List<String> attrs = new ArrayList<String>();
        List<Object> values = new ArrayList<Object>();
        if (empresaZW != null) {
            attrs.add("empresa.codZW");
            values.add(empresaZW);
        }

        try {
            Integer codigoColaborador = Integer.valueOf(param);
            attrs.add("codigoColaborador");
            values.add(codigoColaborador);
            String[] atributos = new String[attrs.size()];
            atributos = attrs.toArray(atributos);
            return getProfessorSinteticoDao().findListByAttributes(ctx,
                    atributos, values.toArray(), "nome", 0);

        } catch (NumberFormatException ne) {
            try {
                attrs.add("nome.like");
                values.add(param.toLowerCase());
                String[] atributos = new String[attrs.size()];
                atributos = attrs.toArray(atributos);
                return getProfessorSinteticoDao().findListByAttributes(ctx,
                        atributos, values.toArray(), "nome", 0);
            } catch (Exception ex) {
                throw new ServiceException(ex.getMessage());
            }
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public void atualizarFotoProfessor(final String ctx, final Integer codigo,
                                       final Integer codigoEmpresaZW, HttpServletRequest request, ServletContext context) throws ServiceException {
        try {
            request.getContextPath();
            ConfiguracaoPersonalEmpresa cfgPersonalService = cfgPersonalEmpresaService.obterPorEmpresa(ctx, codigoEmpresaZW);
            ProfessorSintetico professor = null;
            if (cfgPersonalService.getFotoParaPersonal()) {
                Aplicacao.saveImageLocal(context, ctx, "", true, codigo, true);
            } else {
                professor = obterPorCodigoPessoaZW(ctx, codigo, codigoEmpresaZW);
                professor.getPessoa().setFotoKey(Aplicacao.saveImageLocal(context, ctx, professor.getPessoa().getFotoKey(), true, codigo, false));
                alterar(ctx, professor);
            }
            alterar(ctx, professor);
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    @Override
    public void sincronizarTodosColaboradores(String key, final Integer empresaZW) throws Exception {
        sincronizarTodosColaboradores(key, empresaZW, null);
    }

    public void sincronizarLancamentoPrograma(final String key, final Integer empresaZW, Integer codigoColaborador) throws Exception {
        try (ResultSet rs = professorsinteticoDao.createStatement(key,
                "select codigo from ProfessorSintetico obj where codigoColaborador = " + codigoColaborador)) {
            if (!rs.next()) {
                sincronizarTodosColaboradores(key, empresaZW, codigoColaborador);
            }
        }
    }

    @Override
    public List<ProfessorBIResponseTO> consultarBiProfessoresComVinculos(HttpServletRequest request, Integer empresaId) throws ServiceException {
        List<ProfessorBIResponseTO> professorBIResponseTOS = new ArrayList<ProfessorBIResponseTO>();
        List<ProfessorResponseTO> professorResponseTOList = consultarProfessoresComVinculos(request, empresaId);
        String ctx = sessaoService.getUsuarioAtual().getChave();
        if(professorResponseTOList != null && professorResponseTOList.size() > 0) {
            for(ProfessorResponseTO professorResponseTO : professorResponseTOList) {
                ProfessorBIResponseTO professorBIResponseTO = new ProfessorBIResponseTO();
                professorBIResponseTO.setProfessor(professorResponseTO);
                DashboardBI dashboardBI = biService.obterBiTreinamento(biService.codigoProfessor(professorBIResponseTO.getProfessor().getId(), empresaId, 0), empresaId, ctx);
                professorBIResponseTO.setBiTreinoTreinamentoDTO(dashboardBI != null ? dashboardBI.getBiTreinamento() : new BITreinoTreinamentoDTO());
                professorBIResponseTOS.add(professorBIResponseTO);
            }
        }

        return professorBIResponseTOS;
    }

    public ProfessorSintetico sincronizarTodosColaboradores(String key, final Integer empresaZW, Integer codigoColaborador) throws Exception {
        EmpresaService empresaSer = UtilContext.getBean(EmpresaService.class);
        Empresa empresa = empresaSer.obterPorIdZW(key, empresaZW);
        IntegracaoCadastrosWSConsumer integracaoWS = (IntegracaoCadastrosWSConsumer) UtilContext.getBean(IntegracaoCadastrosWSConsumer.class);
        final String url = Aplicacao.getProp(key, Aplicacao.urlZillyonWebInteg);
        List<AddColaboradorJSON> colaboradoresAdd = integracaoWS.consultarColaboradores(url, key, empresaZW, "%", codigoColaborador != null, true);
        for(AddColaboradorJSON colaboradorJSON : colaboradoresAdd){
            if(codigoColaborador != null && !codigoColaborador.equals(colaboradorJSON.getCodigoColaborador())){
                continue;
            }
            ProfessorSintetico colaborador = new ProfessorSintetico();
            colaborador.setNome(colaboradorJSON.getNome());
            colaborador.setCodigoColaborador(colaboradorJSON.getCodigoColaborador());
            colaborador.setCodigoPessoa(colaboradorJSON.getCodigoPessoa());
            colaborador.setEmpresa(empresa);
            colaborador.setPersonal(colaboradorJSON.getTipo().equals("PT"));
            colaborador.setColaboradorSimples(!colaboradorJSON.getTipo().equals("PT") && !colaboradorJSON.getTipo().equals("TW"));
            colaborador.setProfessorTW(true);
            String s = "select obj from ProfessorSintetico obj where codigoColaborador = :id";
            Map<String, Object> p = new HashMap();
            p.put("id", colaboradorJSON.getCodigoColaborador());
            ProfessorSintetico profLocal = obterObjetoPorParam(key, s, p);
            if (profLocal == null) {
                System.out.println("sincronizar colaborador " + colaborador.getNome());
                colaborador = inserir(key, colaborador);
                if(codigoColaborador != null){
                    return colaborador;
                }
            }
        }
        return null;
    }
    @Override
    public String sincronizarProfessor(String key, ProfessorSintetico professor,
                                       final Integer empresaZW) throws ServiceException {
        return sincronizarProfessor(key, professor, empresaZW, null);
    }

    @Override
    public String sincronizarProfessor(String key, ProfessorSintetico professor,
                                       final Integer empresaZW, String fotoKey) throws ServiceException {
        if (professor.getCodigoColaborador() > 0) {
            String s = "select obj from ProfessorSintetico obj where codigoColaborador = :id";
            Map<String, Object> p = new HashMap();
            p.put("id", professor.getCodigoColaborador());
            ProfessorSintetico profLocal = obterObjetoPorParam(key, s, p);
            if (profLocal == null) {
                professor.setCodigo(null);
                professor.setEmpresa(empresaService.obterPorIdZW(key, empresaZW));
                if (professor.getPessoa() != null) {
                    professor.getPessoa().setFotoKey(Aplicacao.saveImageLocal(context, key, fotoKey, false, null, false));
                }
                inserir(key, professor);
            } else {
                profLocal.setEmpresa(empresaService.obterPorIdZW(key, empresaZW));
                profLocal.setAtivo(professor.isAtivo());
                profLocal.setProfessorTW(professor.getProfessorTW());
                profLocal.setPersonaInterno(professor.getPersonaInterno());
                profLocal.setPersonal(professor.getPersonal());
                profLocal.setCodigoPessoa(professor.getCodigoPessoa());
                profLocal.setPosPago(professor.getPosPago());
                profLocal.setEmail(professor.getEmail());
                profLocal.setNome(professor.getNome());
                profLocal.setCref(professor.getCref());
                profLocal.setCodigoAcesso(professor.getCodigoAcesso());

                if(!UteisValidacao.emptyString(professor.getFotoKey())){
                    profLocal.setFotoKey(professor.getFotoKey());
                    profLocal.setUriImagem(professor.getUriImagem());
                    profLocal.getPessoa().setFotoKey(professor.getFotoKey());
                }else{
                    try {
                        if (profLocal.getPessoa() != null && !UteisValidacao.emptyString(fotoKey)
                                && !fotoKey.equals(professor.getPessoa().getFotoKey()) ) {
                            profLocal.getPessoa().setFotoKey(Aplicacao.saveImageLocal(context, key, fotoKey, true, null, false));
                        }
                    } catch (Exception e) {
                        Logger.getLogger(TreinoWS.class.getName()).log(Level.SEVERE, null, e);
                    }
                }

//                profLocal.setFotoKey(professor.getFotoKey());
//                profLocal.setAvatar(professor.getFotoKey());
                alterar(key, profLocal);
            }
            return "ok";
        } else {
            throw new ServiceException("Professor inválido! ");
        }
    }

    @Override
    public void baixarFotoAluno(String ctx, HttpServletRequest request, String codigopessoa, String fotoKey) throws Exception {
        ProfessorSintetico professor = consultarPorCodigoPessoa(ctx, codigopessoa);
        fotoService.migracaoFotoPessoa(context, ctx, professor.getCodigoPessoa(), fotoKey);
        getProfessorSinteticoDao().update(ctx, professor);
    }

    @Override
    public String alterarProfessor(String key, ProfessorSintetico professor,
                                       final Integer empresaZW) throws ServiceException {
        if (professor.getCodigoColaborador() > 0) {
            String s = "select obj from ProfessorSintetico obj where codigoColaborador = :id";
            Map<String, Object> p = new HashMap();
            p.put("id", professor.getCodigoColaborador());
            ProfessorSintetico profLocal = obterObjetoPorParam(key, s, p);
            if (profLocal == null) {
               return "ok";
            } else {
                profLocal.setEmpresa(empresaService.obterPorIdZW(key, empresaZW));
                profLocal.setAtivo(false);
                profLocal.setProfessorTW(professor.getProfessorTW());
                profLocal.setPersonaInterno(professor.getPersonaInterno());
                profLocal.setPersonal(professor.getPersonal());
                profLocal.setPosPago(professor.getPosPago());
                profLocal.setEmail(professor.getEmail());
                profLocal.setNome(professor.getNome());
                profLocal.setCref(professor.getCref());
                alterar(key, profLocal);
            }
            return "ok";
        } else {
            throw new ServiceException("Professor inválido!");
        }
    }

    @Override
    public void marcarProfessorDisponivel(final String ctx, ProfessorSintetico professor,
                                          boolean disponivel) throws ServiceException {
        try {
            ProfessorDisponivel profDisponivel = professorDisponivelDao.findObjectByAttributes(
                    ctx, new String[]{"professor.codigo"},
                    new Object[]{professor.getCodigo()}, "professor");
            if (profDisponivel != null && !disponivel) {
                professorDisponivelDao.delete(ctx, profDisponivel);
            } else if (profDisponivel != null && disponivel) {
                profDisponivel.setDataRegistro(Calendario.hoje());
                professorDisponivelDao.update(ctx, profDisponivel);
            } else if (disponivel) {
                ProfessorDisponivel novo = new ProfessorDisponivel();
                novo.setProfessor(professor);
                novo.setDataRegistro(Calendario.hoje());
                professorDisponivelDao.insert(ctx, novo);
            }
        } catch (Exception ex) {
            Logger.getLogger(ProfessorSinteticoServiceImpl.class.getName()).log(Level.SEVERE, null, ex);
            throw new ServiceException(ex);
        }
    }

    @Override
    public boolean verificarProfessorDisponivel(final String ctx, ProfessorSintetico professor) throws ServiceException {
        try {
            ProfessorDisponivel profDisponivel = professorDisponivelDao.findObjectByAttributes(
                    ctx, new String[]{"professor.codigo"},
                    new Object[]{professor.getCodigo()}, "professor");
            return profDisponivel != null;
        } catch (Exception ex) {
            Logger.getLogger(ProfessorSinteticoServiceImpl.class.getName()).log(Level.SEVERE, null, ex);
            throw new ServiceException(ex);
        }
    }

    @Override
    public List<ProfessorSintetico> consultarProfessores(String ctx, Integer empresa) throws ServiceException {
        return consultarProfessores(ctx, empresa, true);
    }

    @Override
    public List<ProfessorSintetico> consultarProfessores(String ctx, Integer empresa, Boolean ativos) throws ServiceException {
        try {
            Map<String, Object> params = new HashMap<String, Object>();
            StringBuffer sql = new StringBuffer("select obj from ProfessorSintetico obj where obj.professorTW is true ");
            if (null != ativos) {
                sql.append("and ativo is ").append(ativos);
            }

            if (empresa != null && empresa > 0) {
                sql.append(" and obj.empresa.codZW = :empresa");
                params.put("empresa", empresa);
            }

            if (!SuperControle.independente(ctx)){
                sql.append(" and obj.professorTW = true");
            }

            sql.append(" ORDER BY obj.nome ");
            return obterPorParam(ctx, sql.toString(), params);

        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    private List<ProfessorSintetico> consultarTodosProfessores(String ctx, Integer empresa, Boolean incluirInativo, Boolean todosTipos, String nome, Long limit) throws ServiceException {
        List<ProfessorSintetico> professores = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select obj.nome, obj.codigo, p.fotoKey, obj.codigocolaborador, obj.codigopessoa, p.codigo as pessoa from ProfessorSintetico obj ");
        sql.append(" inner join empresa emp on emp.codigo = obj.empresa_codigo ");
        sql.append(" left join pessoa p on p.codigo = obj.pessoa_codigo where 0 = 0 ");
        if (empresa != null && empresa > 0) {
            if (SuperControle.independente(ctx)) {
                sql.append(" and emp.codigo = ").append(empresa);
            } else {
                sql.append(" and emp.codZW = ").append(empresa);
            }
        }
        if(!UteisValidacao.emptyString(nome)){
            sql.append(" and upper(obj.nome) like CONCAT('%").append(nome.toUpperCase()).append("%')");
        }
        if (incluirInativo != null && !incluirInativo) {
            sql.append(" and obj.ativo is true");
        }
        if(!todosTipos) {
            sql.append(" and obj.professortw is true ");
        }
        sql.append(" ORDER BY obj.nome ");
        if(limit != null){
            sql.append(" LIMIT ").append(limit);
        }

        try (ResultSet resultSet = professorsinteticoDao.createStatement(ctx, sql.toString())) {
            while (resultSet.next()) {
                ProfessorSintetico ps = new ProfessorSintetico();
                String professorNome = resultSet.getString("nome");
                if (UteisValidacao.emptyString(professorNome)) {
                    int codigoColaborador = resultSet.getInt("codigocolaborador");
                    if (!UteisValidacao.emptyNumber(codigoColaborador)) {
                        try {
                            ps.setNome(obterNomeDoColaborador(ctx, codigoColaborador));
                        } catch (Exception e) {
                            Uteis.logar(e, ConfiguracaoSistemaServiceImpl.class);
                        }
                    }
                } else {
                    ps.setNome(professorNome);
                }
                ps.setCodigo(resultSet.getInt("codigo"));
                ps.setCodigoColaborador(resultSet.getInt("codigocolaborador"));
                ps.setCodigoPessoa(resultSet.getInt("codigopessoa"));
                ps.setPessoa(new Pessoa());
                ps.getPessoa().setCodigo(resultSet.getInt("pessoa"));
                ps.getPessoa().setFotoKey(resultSet.getString("fotoKey"));
                professores.add(ps);
            }
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }

        return professores;
    }


    private String obterNomeDoColaborador(String ctx, int codigoColaborador) throws SQLException {
        String sql = "select distinct c.codigo, p.nome \n" +
                "from colaborador c \n" +
                "inner join pessoa p on c.pessoa = p.codigo \n" +
                "where c.codigo = ?";
        try (Connection conZW = conexaoZWService.conexaoZw(ctx);
             PreparedStatement statement = conZW.prepareStatement(sql)) {
            statement.setInt(1, codigoColaborador);
            try (ResultSet resultSet = statement.executeQuery()) {
                if (resultSet.next()) {
                    return resultSet.getString("nome");
                }
            }
        }  catch (Exception e) {
            Uteis.logar(e, ConfiguracaoSistemaServiceImpl.class);
        }
        return null;
    }


    @Override
    public List<ColaboradorDTO> consultarTodosProfessoresZW(Integer empresa, Boolean incluirInativo, Boolean todosTipos) throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        List<ColaboradorDTO> professoresZW = new ArrayList<>();
        try {
            StringBuilder sql = new StringBuilder("SELECT c.codigo, p.nome FROM colaborador c");
            sql.append(" INNER JOIN pessoa p on p.codigo = c.pessoa");
            sql.append(" INNER JOIN tipoColaborador tC on tC.colaborador = c.codigo");
            sql.append(" WHERE 1=1");
            if(!incluirInativo) {
                sql.append(" AND c.situacao = 'AT'");
            }
            if(empresa != null && empresa > 0){
                sql.append(" AND c.empresa = ").append(empresa);
            }
            if(!todosTipos){
                sql.append(" AND tC.descricao = 'PR'");
            }

            ResultSet rsProfessorZw;
            try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
                rsProfessorZw = ConexaoZWServiceImpl.criarConsulta(sql.toString(), conZW);
            }
            while (rsProfessorZw.next()) {
                ColaboradorDTO colaboradorDTO = new ColaboradorDTO();
                colaboradorDTO.setId(rsProfessorZw.getInt("codigo"));
                colaboradorDTO.setNome(rsProfessorZw.getString("nome"));
                professoresZW.add(colaboradorDTO);
            }

            Ordenacao.ordenarLista(professoresZW, "nome");
            return professoresZW;
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public ProfessorSintetico consultarProfessorCarteiraPorCliente(String ctx, Integer codigoCliente) throws ServiceException {
        try {
            Map<String, Object> params = new HashMap<String, Object>();
            StringBuffer sql = new StringBuffer("SELECT cs.professorSintetico FROM ClienteSintetico cs ");
            sql.append("        WHERE cs.codigo = :codigoCliente");

            params.put("codigoCliente", codigoCliente);
            return professorsinteticoDao.findObjectByParam(ctx, sql.toString(), params);

        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public ProfessorSintetico consultarProfessorPorCodigoPessoa(String ctx, Integer codigoPessoa) throws ServiceException {
        try {
            Map<String, Object> params = new HashMap<String, Object>();
            StringBuffer sql = new StringBuffer("select obj from ProfessorSintetico obj ");
            sql.append("        where obj.codigoPessoa = :codigoPessoa");

            params.put("codigoPessoa", codigoPessoa);
            return professorsinteticoDao.findObjectByParam(ctx, sql.toString(), params);

        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }


    @Override
    public List<ColaboradorResponseTO> listarColaboradores(HttpServletRequest request, FiltroColaboradorJSON filtros,
                                                           PaginadorDTO paginadorDTO, Integer empresaId, Boolean todosTipos) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            if (!StringUtils.isBlank(paginadorDTO.getSort())) {
                paginadorDTO.setSort(paginadorDTO.getSort());
            } else {
                paginadorDTO.setSort("nome,ASC");
            }
            List<ProfessorSintetico> lista = professorsinteticoDao.listarColaboradores(ctx, filtros, paginadorDTO, empresaId, todosTipos);
            List<ColaboradorResponseTO> listaRet = new ArrayList<>();
            if (lista != null) {
                for (ProfessorSintetico ps : lista) {
                    Usuario usuario = usuarioService.consultarPorProfessor(ctx, ps.getCodigo());
                    if (ps.getPessoa() != null) {
                        ps.setUriImagem(fotoService.defineURLFotoPessoa(request, ps.getPessoa().getFotoKey(), ps.getCodigoPessoa(), false, ctx, !SuperControle.independente(ctx)));
                    }
                    listaRet.add(new ColaboradorResponseTO(ps, usuario, SuperControle.independente(ctx)));
                }
            }
            return listaRet;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException(ColaboradoresExcecoes.ERRO_BUSCAR_COLABORADORES, e);
        }
    }

    private List<ColaboradorResponseTO> filtraTipoColaboradorZW(String ctx, List<ColaboradorResponseTO> lista, FiltroColaboradorJSON filtros) throws Exception {
        List<ColaboradorResponseTO> colaboradoresFiltrados = new ArrayList<>();
        for (ColaboradorResponseTO colaboradorResponseTO : lista) {
            String sql = "SELECT * FROM tipocolaborador WHERE colaborador = " + colaboradorResponseTO.getCodigoColaborador() + " AND descricao = '" + filtros.getTipoColaborador().getSigla() + "'";
            try (Connection conZW = conexaoZWService.conexaoZw(ctx);
                 ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sql.toString(), conZW)) {
                if(rs.next()){
                    colaboradoresFiltrados.add(colaboradorResponseTO);
                }
            }
        }
        return colaboradoresFiltrados;
    }


    @Override
    public List<ColaboradorResponseTO> listaColaboradorDadosBasicos(Integer idProfessorMontou, FiltroColaboradorJSON filtros, Integer empresaZwId) throws ServiceException {
        // retornar somente nome e codigo professor;
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            List<ColaboradorResponseTO> listaRet = new ArrayList<>();

            StringBuilder sql = new StringBuilder("select distinct p.codigo, p.codigocolaborador, p.nome, COALESCE(p2.fotokey, u.fotokeyapp) AS foto, \n");
            sql.append("case when p.codigocolaborador = ").append(idProfessorMontou).append(" then 0 else 1 end as ordem \n");
            sql.append("from professorsintetico p \n");
            sql.append("inner join empresa e on e.codigo = p.empresa_codigo \n");
            sql.append("left join usuario u on u.professor_codigo = p.codigo \n");
            sql.append("inner join pessoa p2 on p.pessoa_codigo = p2.codigo \n");

            StringBuilder where = new StringBuilder();
            where.append("where (e.codzw = ").append(empresaZwId).append(" or p.codigocolaborador = ").append(idProfessorMontou).append(") ");
            where.append("and (p.professortw is true or p.codigocolaborador = ").append(idProfessorMontou).append(") ");
            where.append("and (p.codigocolaborador = ").append(idProfessorMontou).append(" or p.ativo is true) \n");
            if (filtros.getNome() && !StringUtils.isBlank(filtros.getParametros())) {
                where.append("and unaccent(upper(p.nome)) like unaccent(CONCAT('%").append(filtros.getParametros().toUpperCase().trim()).append("%')) \n");
            }
            where.append("order by 5, p.nome asc limit 50");

            if (SuperControle.independente(ctx)) {
                where = new StringBuilder(where.toString().replaceAll("codigocolaborador", "codigo"));
            }

            try (ResultSet resultSet = professorsinteticoDao.createStatement(ctx, sql.toString().concat(where.toString()))) {
                while (resultSet.next()) {
                    listaRet.add(new ColaboradorResponseTO(
                            resultSet.getInt("codigo"),
                            resultSet.getInt("codigocolaborador"),
                            resultSet.getString("nome"),
                            SuperControle.independente(ctx),
                            Uteis.getPaintFotoDaNuvem(resultSet.getString("foto")
                        )));
                }
            }

            if(filtros.getTipoColaborador() != null) {
                return filtraTipoColaboradorZW(ctx, listaRet, filtros);
            }
            return listaRet;
        } catch (Exception e) {
            throw new ServiceException(ColaboradoresExcecoes.ERRO_BUSCAR_COLABORADORES, e);
        }
    }

    @Override
    public ColaboradorResponseTO buscarColaborador(Integer id, Integer empresaId, HttpServletRequest request, boolean origemIsTelaColaboradorZWUI) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            ProfessorSintetico ps = null;
            if (SuperControle.independente(ctx)) {
                ps = obterPorId(ctx, id);
            } else {
                ps = professorsinteticoDao.findObjectByAttribute(ctx, "codigoColaborador", id);
                atualizarDadosPessoaColaborador(ctx, ps, origemIsTelaColaboradorZWUI);
            }
            if (ps == null) {
                IntegracaoTurmasWSConsumer integracaoWS = UtilContext.getBean(IntegracaoTurmasWSConsumer.class);
                JSONArray professoresZW = integracaoWS.consultarProfessores( ctx, empresaId, true);
                for (int i = 0; i < professoresZW.length(); i++) {
                    if(professoresZW.getJSONObject(i).getInt("codigo") == id){
                        ProfessorSintetico professorZW = new ProfessorSintetico();
                        professorZW.setCodigoColaborador(professoresZW.getJSONObject(i).getInt("codigo"));
                        professorZW.setNome(professoresZW.getJSONObject(i).getString("nome"));
                        return new ColaboradorResponseTO(professorZW, null, SuperControle.independente(ctx));
                    }
                }
                return new ColaboradorResponseTO();
            }
            if (ps.getPessoa() != null) {
                ps.setUriImagem(fotoService.defineURLFotoPessoa(request, ps.getPessoa().getFotoKey(), ps.getCodigoPessoa(), false, ctx, !SuperControle.independente(ctx)));
            }
            Usuario usuario = usuarioService.consultarPorProfessor(ctx, ps.getCodigo());
            return new ColaboradorResponseTO(ps, usuario, SuperControle.independente(ctx));
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException(ColaboradoresExcecoes.ERRO_BUSCAR_COLABORADOR, e);
        }
    }

    private void atualizarDadosPessoaColaborador(String ctx, ProfessorSintetico ps, boolean origemIsTelaColaboradorZWUI) throws Exception {
        if (!SuperControle.independente(ctx) && origemIsTelaColaboradorZWUI && ps.getPessoa() != null) {
            try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
                Pessoa pessoa = pessoaService.obterPorId(ctx, ps.getPessoa().getCodigo());
                Integer codPessoaZW = 0;
                boolean teveAlteracao = false;
                try {
                    String sqlPessoa =
                            "select p.nome, p.datanasc, p.sexo, p.codigo as codPessoa from colaborador c \n" +
                                    "inner join pessoa p on p.codigo = c.pessoa \n" +
                                    "where c.codigo = " + ps.getCodigoColaborador();
                    try (ResultSet rsPessoa = ConexaoZWServiceImpl.criarConsulta(sqlPessoa, conZW)) {
                        if (rsPessoa.next()) {
                            codPessoaZW = rsPessoa.getInt("codPessoa");
                            if (pessoa.getNome() == null || pessoa.getDataNascimento() == null || pessoa.getSexo() == null
                                    || !pessoa.getSexo().equals(rsPessoa.getString("sexo"))
                                    || pessoa.getDataNascimento().compareTo(rsPessoa.getDate("datanasc")) != 0
                                    || !pessoa.getNome().equals(rsPessoa.getString("nome"))) {
                                pessoa.setDataNascimento(rsPessoa.getDate("datanasc"));
                                pessoa.setSexo(rsPessoa.getString("sexo"));
                                pessoa.setNome(rsPessoa.getString("nome"));
                                teveAlteracao = true;
                            }
                        }
                    }

                    if (codPessoaZW > 0) {
                        // sincronizar email
                        String sqlEmail = "select email from email e where e.pessoa = " + codPessoaZW;
                        List<String> emailsZWColaborador;
                        try (ResultSet rsEmail = ConexaoZWServiceImpl.criarConsulta(sqlEmail, conZW)) {
                            List<Email> emailsPessoa = pessoaService.obterEmailsPorPessoa(ctx, pessoa.getCodigo());
                            emailsZWColaborador = new ArrayList<>();
                            while (rsEmail.next()) {
                                emailsZWColaborador.add(rsEmail.getString("email"));
                                boolean emailJaCadastrado = false;
                                for (Email email : emailsPessoa) {
                                    if (email.getEmail().equals(rsEmail.getString("email"))) {
                                        emailJaCadastrado = true;
                                    }
                                }
                                if (!emailJaCadastrado) {
                                    Email email = new Email(rsEmail.getString("email"), pessoa);
                                    emailDao.insert(ctx, email);
                                    teveAlteracao = true;
                                }
                            }
                        }

                        // remover do treino email que não existe no zw
                        List<Email> emailsAtualizadosPessoa = pessoaService.obterEmailsPorPessoa(ctx, pessoa.getCodigo());
                        for (Email emailTr : emailsAtualizadosPessoa) {
                            boolean emailTrExisteZw = false;
                            for (String emailZw : emailsZWColaborador) {
                                if (emailTr.getEmail().equals(emailZw)) {
                                    emailTrExisteZw = true;
                                }
                            }
                            if (!emailTrExisteZw) {
                                emailDao.executeNativeSQL(ctx, "delete from email where codigo = " + emailTr.getCodigo());
                                teveAlteracao = true;
                            }
                        }

                        // validar email duplicado
                        emailsAtualizadosPessoa = pessoaService.obterEmailsPorPessoa(ctx, pessoa.getCodigo());
                        for (Email emailTr : emailsAtualizadosPessoa) {
                            long countTr = pessoaService.obterEmailsPorPessoa(ctx, pessoa.getCodigo()).stream().filter(item -> item.getEmail().equals(emailTr.getEmail())).count();
                            long countZw = emailsZWColaborador.stream().filter(item -> item.equals(emailTr.getEmail())).count();

                            if (countTr > countZw) {
                                emailDao.executeNativeSQL(ctx, "delete from email where codigo = " + emailTr.getCodigo());
                                teveAlteracao = true;
                            }
                        }

                        // sincronizar telefone
                        String sqlTelefone = "select numero, tipotelefone from telefone t where t.pessoa = " + codPessoaZW;
                        List<String> telefonesZWColaborador;
                        try (ResultSet rsTelefone = ConexaoZWServiceImpl.criarConsulta(sqlTelefone, conZW)) {
                            List<Telefone> telefonesPessoa = pessoaService.obterTelefonesPorPessoa(ctx, pessoa.getCodigo());
                            telefonesZWColaborador = new ArrayList<>();
                            while (rsTelefone.next()) {
                                telefonesZWColaborador.add(rsTelefone.getString("numero"));
                                boolean telefoneJaCadastrado = false;
                                for (Telefone telefone : telefonesPessoa) {
                                    if (telefone.getTelefone().equals(rsTelefone.getString("numero"))) {
                                        telefoneJaCadastrado = true;
                                    }
                                }
                                if (!telefoneJaCadastrado) {
                                    Telefone telefone = new Telefone(rsTelefone.getString("numero"), pessoa);
                                    telefoneDao.insert(ctx, telefone);
                                    teveAlteracao = true;
                                }
                            }
                        }

                        // remover do treino telefone que não existe no zw
                        List<Telefone> telefonesAtualizadosPessoa = pessoaService.obterTelefonesPorPessoa(ctx, pessoa.getCodigo());
                        for (Telefone telefoneTr : telefonesAtualizadosPessoa) {
                            boolean telefoneTrExisteZw = false;
                            for (String telefoneZw : telefonesZWColaborador) {
                                if (telefoneTr.getTelefone().equals(telefoneZw)) {
                                    telefoneTrExisteZw = true;
                                }
                            }
                            if (!telefoneTrExisteZw) {
                                telefoneDao.executeNativeSQL(ctx, "delete from telefone where codigo = " + telefoneTr.getCodigo());
                                teveAlteracao = true;
                            }
                        }

                        // validar telefone duplicado
                        telefonesAtualizadosPessoa = pessoaService.obterTelefonesPorPessoa(ctx, pessoa.getCodigo());
                        for (Telefone telefoneTr : telefonesAtualizadosPessoa) {
                            long countTr = pessoaService.obterTelefonesPorPessoa(ctx, pessoa.getCodigo()).stream().filter(item -> item.getTelefone().equals(telefoneTr.getTelefone())).count();
                            long countZw = telefonesZWColaborador.stream().filter(item -> item.equals(telefoneTr.getTelefone())).count();

                            if (countTr > countZw) {
                                telefoneDao.executeNativeSQL(ctx, "delete from telefone where codigo = " + telefoneTr.getCodigo());
                                teveAlteracao = true;
                            }
                        }
                    }

                    if (teveAlteracao) {
                        pessoaService.alterarPessoa(ctx, pessoa);
                        ps.setPessoa(pessoaService.obterPorId(ctx, ps.getPessoa().getCodigo()));
                        ps.getPessoa().setEmails(pessoaService.obterEmailsPorPessoa(ctx, pessoa.getCodigo()));
                        ps.getPessoa().setTelefones(pessoaService.obterTelefonesPorPessoa(ctx, pessoa.getCodigo()));
                    }
                } catch (Exception e) {
                    Uteis.logar(e, ConfiguracaoSistemaServiceImpl.class);
                }
            }
        }
    }

    @Override
    public ColaboradorResponseTO cadastrarColaborador(ColaboradorTO colaboradorTO, Integer empresaId, HttpServletRequest request) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();

            return cadastrarColaborador(ctx, colaboradorTO, empresaId, request);
        } catch (Exception e) {
            throw new ServiceException(ColaboradoresExcecoes.ERRO_INSERIR_COLABORADOR, e);
        }
    }

    @Override
    public List<ColaboradorSimplesTO> colaboradorIsNullUsuario(HttpServletRequest request) throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();

        List<ProfessorSintetico> listaProfessor = professorsinteticoDao.colaboradoresIsNullUsuario(ctx);
        List<ColaboradorSimplesTO> listaReturn = new ArrayList<>();

        for (ProfessorSintetico colaborador : listaProfessor) {
            if (colaborador.getPessoa() != null) {
                colaborador.setUriImagem(fotoService.defineURLFotoPessoa(request, colaborador.getPessoa().getFotoKey(), colaborador.getCodigoPessoa(), false, ctx, !SuperControle.independente(ctx)));
            }
            listaReturn.add(new ColaboradorSimplesTO(colaborador, SuperControle.independente(ctx)));
        }

        return listaReturn;
    }

    public List<ColaboradorSimplesTO> listarTodosColaboradorAptoAAula(HttpServletRequest request, Integer empresaId, Boolean incluirInativos, String nome) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            List<ProfessorSintetico> professores = new ArrayList<>();
            if (SuperControle.independente(ctx)) {
                professores.addAll(consultarTodosProfessores(ctx, empresaId, incluirInativos, false, null, null));
            } else {
                IntegracaoTurmasWSConsumer integracaoWS = UtilContext.getBean(IntegracaoTurmasWSConsumer.class);
                JSONArray professoresZW = integracaoWS.consultarProfessores( ctx, empresaId, true);
                for (int i = 0; i < professoresZW.length(); i++) {
                    String nomeColaborador = professoresZW.getJSONObject(i).getString("nome");
                    if(nome == null || (nomeColaborador != null && nomeColaborador.toLowerCase().startsWith(nome.toLowerCase()))){
                        ProfessorSintetico professorZW = professorsinteticoDao.findObjectByAttribute(ctx, "codigoColaborador", professoresZW.getJSONObject(i).getInt("codigo"));
                        if (professorZW == null) {
                            professorZW = new ProfessorSintetico();
                            professorZW.setCodigoColaborador(professoresZW.getJSONObject(i).getInt("codigo"));
                            professorZW.setNome(nomeColaborador);
                        }
                        professorZW.setUriImagem(fotoService.defineURLFotoPessoa(request, professorZW.getPessoa().getFotoKey(), professorZW.getCodigoPessoa(), false, ctx, false));
                        professores.add(professorZW);
                    }
                }
            }
            List<ColaboradorSimplesTO> ret = new ArrayList<>();
            for (ProfessorSintetico professor : professores) {
                ret.add(new ColaboradorSimplesTO(professor, SuperControle.independente(ctx)));
            }
            return ret;
        } catch (Exception ex) {
            throw new ServiceException(ProfessorExcecoes.ERRO_BUSCAR_PROFESSORES, ex);
        }
    }

    @Override
    public List<ProfessorResponseTO> consultarProfessoresComVinculos(HttpServletRequest request, Integer empresaId) throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        try {
            List <ProfessorSintetico> professores = new ArrayList<>();
            professores.addAll(consultarProfessoresComVinculos(ctx, empresaId));
            List<ProfessorResponseTO> listaRet = new ArrayList<>();
            if (!UteisValidacao.emptyList(professores)) {
                for (ProfessorSintetico ps : professores) {
                    if(ps.isAtivo()){
                        listaRet.add(new ProfessorResponseTO(ps, SuperControle.independente(ctx), false));
                    }
                }
            }
            return Ordenacao.ordenarLista(listaRet, "nome");
        } catch (Exception e) {
            throw new ServiceException(ProfessorExcecoes.ERRO_BUSCAR_PROFESSORES);
        }
    }

    @Override
    public List<ProfessorSintetico> consultarProfessoresComVinculos(String ctx, Integer empresa) throws ServiceException {
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT obj.professorSintetico FROM ClienteSintetico obj WHERE 1 = 1\n");
            sql.append("AND obj.professorSintetico.professorTW is true \n");
            if (empresa != null && empresa > 0) {
                sql.append(" and obj.professorSintetico.empresa.codZW = ").append(empresa);
            }
            sql.append("ORDER BY obj.professorSintetico\n");

            return getProfessorSinteticoDao().getCurrentSession(ctx).createQuery(sql.toString())
                    .setResultTransformer(Criteria.DISTINCT_ROOT_ENTITY)
                    .list();
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    public List<ProfessorResponseTO> listarTodosColaborador(HttpServletRequest request, Integer empresaId, Boolean incluirInativos, Boolean todosTipos, Boolean validarNoTreino) throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        try {
            List <ProfessorSintetico> professores = new ArrayList<>();
            professores.addAll(consultarTodosProfessores(ctx,empresaId, incluirInativos, todosTipos, null, null));
            List<ProfessorResponseTO> listaRet = new ArrayList<>();
            if (!UteisValidacao.emptyList(professores)) {
                for (ProfessorSintetico ps : professores) {
                    listaRet.add(new ProfessorResponseTO(ps, SuperControle.independente(ctx), validarNoTreino));
                }
            }
            return listaRet;
        } catch (Exception e) {
            throw new ServiceException(ProfessorExcecoes.ERRO_BUSCAR_PROFESSORES);
        }
    }

    @Override
    public ProfessorResponseTO consultarColaboradorPorUsuario(Integer usuarioId) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            Usuario usuario = usuarioService.obterPorId(ctx, usuarioId);
            ProfessorResponseTO profRetorno = null;
            if (usuario != null && usuario.getProfessor() != null) {
                profRetorno = new ProfessorResponseTO(usuario, SuperControle.independente(ctx));
            } else {
                throw new ServiceException(ProfessorExcecoes.ERRO_BUSCAR_PROFESSOR);
            }

            return profRetorno;
        } catch (ServiceException e) {
            throw new ServiceException(ProfessorExcecoes.ERRO_BUSCAR_PROFESSOR, e);
        }
    }

    public ColaboradorResponseTO cadastrarColaborador(String ctx, ColaboradorTO colaboradorTO, Integer empresaId,
                                                      HttpServletRequest request) throws Exception {

            if (colaboradorTO.getNome() == null || colaboradorTO.getNome().isEmpty()) {
                throw new ServiceException(ColaboradoresExcecoes.COLABORADOR_NOME_NAO_INFORMADO);
            }
            if(colaboradorTO.getFones().size() == 0) {
                throw new ServiceException(ColaboradoresExcecoes.COLABORADOR_FONE_NAO_ENCONTRADO);
            }
            if(empresaId == null || empresaId < 0) {
                throw new ServiceException(ColaboradoresExcecoes.COLABORADOR_EMPRESA_NAO_ENCONTRADO);
            }
            Empresa empresa = empresaService.obterPorId(ctx, empresaId);
            if(empresa == null) {
                throw new ServiceException(ColaboradoresExcecoes.COLABORADOR_EMPRESA_NAO_EXISTE);
            }
            UsuarioColaboradorTO usuarioColaboradorTO = new UsuarioColaboradorTO();
            if (colaboradorTO.getUsarApp()) {
                usuarioColaboradorTO.setAppPassword(colaboradorTO.getAppPassword());
                usuarioColaboradorTO.setAppUserName(colaboradorTO.getAppUserName());
                usuarioColaboradorTO.setImagemData(colaboradorTO.getImagemData());
                usuarioColaboradorTO.setExtensaoImagem(colaboradorTO.getExtensaoImagem());
                usuarioColaboradorTO.setPerfilUsuarioPermissoes(colaboradorTO.getPerfilUsuarioId());
                usuarioColaboradorTO.setTipoUsuario(colaboradorTO.getTipoUsuario());
                if (UteisValidacao.validaEmail(usuarioColaboradorTO.getAppUserName())) {
                    usuarioColaboradorTO.setEmail(usuarioColaboradorTO.getAppUserName());
                } else if(colaboradorTO.getEmails() != null && !colaboradorTO.getEmails().isEmpty()){
                    usuarioColaboradorTO.setEmail(colaboradorTO.getEmails().get(0).getEmail());
                }

                usuarioService.validarUsuarioColaborador(ctx, usuarioColaboradorTO, empresaId);
            }

            Pessoa pessoa = new Pessoa();
            ProfessorSintetico ps = new ProfessorSintetico();
            ps.setNome(colaboradorTO.getNome().trim());

            if (colaboradorTO.getEmails() != null && colaboradorTO.getEmails().size() > 0) {
                ps.setEmail(colaboradorTO.getEmails().get(0).getEmail());
            }
            pessoa.setNome(colaboradorTO.getNome().trim());
            pessoa.setDataNascimento(colaboradorTO.getDataNascimento());
            pessoa.setSexo(colaboradorTO.getSexo() == null ? null : colaboradorTO.getSexo().getSexo());
            pessoa.getEmails().clear();
            ps.setAtivo(colaboradorTO.getSituacao() == SituacaoColaboradorEnum.ATIVO);
            ps.setEmpresa(empresa);

            if (colaboradorTO.getEmails() != null) {
                for (EmailTO email : colaboradorTO.getEmails()) {
                    Uteis.validarEmailJson(email.getEmail());
                    pessoa.getEmails().add(new Email(email.getEmail(), pessoa));
                }
            }
            pessoa.getTelefones().clear();
            if (colaboradorTO.getFones() != null) {
                for (TelefoneTO telefone : colaboradorTO.getFones()) {
                    pessoa.getTelefones().add(new Telefone(telefone.getPhone(), pessoa));
                }
            }

            ps.setProfessorTW(true);
            pessoa = pessoaService.inserirPessoa(ctx, pessoa);
            ps.setPessoa(pessoa);
            ps = insertOrMerge(ctx, ps);
            if(!StringUtils.isBlank(colaboradorTO.getImagemData())){
                salvarMidiaNuvem(ctx, colaboradorTO, ps);
            }
            Usuario usuario = null;
            if(colaboradorTO.getUsarApp()) {

                /**
                 * 27/12/2018 - Ainda não foi criado o crud de perfil do usuario, quando estiver criado o crud, é para descomentando essa parte
                 */
//                if (colaboradorTO.getPerfilUsuarioId() == null
//                        || colaboradorTO.getPerfilUsuarioId() < 0) {
//                    throw new ServiceException(ColaboradoresExcecoes.COLABORADOR_PERFIL_NAO_ENCONTRADO);
//                }
//                Perfil perfil = perfilService.obterPorId(ctx, colaboradorTO.getPerfilUsuarioId());
//                if (perfil == null) {
//                    throw new ServiceException(ColaboradoresExcecoes.COLABORADOR_PERFIL_NAO_EXISTE);
//                }

                usuario = new Usuario();
                if (colaboradorTO.getAppUserName() == null || colaboradorTO.getAppUserName().isEmpty()) {
                    throw new ServiceException(ColaboradoresExcecoes.COLABORADOR_USER_NAME_NAO_ENCONTRADO);
                }


                usuarioColaboradorTO.setColaboradorId(ps.getCodigo());
                usuarioColaboradorTO.setSituacao(colaboradorTO.getSituacao());
                int usuarioId = usuarioService.cadastrarUsuarioColaborador(ctx, usuarioColaboradorTO, empresaId, request).getId();

                usuario = usuarioService.obterPorId(ctx, usuarioId);
            }

            return new ColaboradorResponseTO(ps, usuario, SuperControle.independente(ctx));

    }

    @Override
    public ColaboradorResponseTO atualizarColaborador(Integer id, Integer empresaId, ColaboradorTO colaboradorTO) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            ProfessorSintetico ps = obterPorId(ctx, id);
            if (ps == null) {
                throw new ServiceException(ColaboradoresExcecoes.COLABORADOR_NAO_ENCONTRADO);
            }

            validarCampos(ps, colaboradorTO, empresaId, ctx);

            Empresa empresa = empresaService.obterPorId(ctx, empresaId);
            if(empresa == null) {
                throw new ServiceException(ColaboradoresExcecoes.COLABORADOR_EMPRESA_NAO_EXISTE);
            }

            pessoaService.deletarEmailTelefonePessoa(ctx, ps.getPessoa().getCodigo());

            Pessoa pessoa = ps.getPessoa();
            pessoa.setEmails(pessoaService.obterEmailsPorPessoa(ctx, pessoa.getCodigo()));
            pessoa.setTelefones(pessoaService.obterTelefonesPorPessoa(ctx, pessoa.getCodigo()));
            pessoa.setNome(colaboradorTO.getNome().trim());
            pessoa.setDataNascimento(colaboradorTO.getDataNascimento());
            pessoa.setSexo(colaboradorTO.getSexo().getSexo());
            ps.setAtivo(colaboradorTO.getSituacao() == SituacaoColaboradorEnum.ATIVO);
            ps.setEmpresa(empresa);
            ps.setNome(pessoa.getNome().trim());
            if (colaboradorTO.getEmails() != null) {
                for (EmailTO email : colaboradorTO.getEmails()) {
                    Email em = new Email(email.getEmail(), pessoa);
                    pessoa.getEmails().add(em);
                }
            }
            if (colaboradorTO.getFones() != null) {
                for (TelefoneTO telefone : colaboradorTO.getFones()) {
                    Telefone tel = new Telefone(telefone.getPhone(), pessoa);
                    pessoa.getTelefones().add(tel);
                }
            }
            Usuario usuario = usuarioService.obterUsuarioPorColaborador(ps.getCodigo());
            if (usuario != null && !UteisValidacao.emptyNumber(usuario.getCodigo())) {
                usuario.setStatus(StatusEnum.valueOf(colaboradorTO.getSituacao().name()));
            }
            if(!StringUtils.isBlank(colaboradorTO.getImagemData()) && !StringUtils.isBlank(colaboradorTO.getExtensaoImagem())) {
                salvarMidiaNuvem(ctx, colaboradorTO, ps);
            } else if (StringUtils.isNotEmpty(ps.getUriImagem()) && StringUtils.isEmpty(colaboradorTO.getImagemData())){
                if (!ps.getFotoKey().contains("http")) {
                    deleteFotoNuvem(ps.getPessoa().getFotoKey());
                    ps.getPessoa().setFotoKey("fotoPadrao.jpg");
                }

                if (usuario != null && !UteisValidacao.emptyNumber(usuario.getCodigo())) {
                    usuario.setFotoKeyApp(null);
                    usuarioService.alterar(ctx, usuario);
                }
                ps.setUriImagem(null);
            }

            ps.setProfessorTW(true);
            pessoa = pessoaService.alterarPessoa(ctx, pessoa);
            ps.setPessoa(pessoa);
            ps = alterar(ctx, ps);

            return new ColaboradorResponseTO(ps, usuario, SuperControle.independente(ctx));
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException(ColaboradoresExcecoes.ERRO_ALTERAR_COLABORADOR, e);
        }
    }

    public void validarCampos(ProfessorSintetico ps, ColaboradorTO colaboradorTO, Integer empresaId, String ctx) throws ServiceException {
        if (colaboradorTO.getNome() == null || colaboradorTO.getNome().isEmpty()) {
            throw new ServiceException(ColaboradoresExcecoes.COLABORADOR_NOME_NAO_INFORMADO);
        }
        if(colaboradorTO.getDataNascimento() == null) {
            throw new ServiceException(ColaboradoresExcecoes.COLABORADOR_DATA_NASCIMENTO_NAO_ENCONTRADO);
        }
        if(colaboradorTO.getFones().size() == 0) {
            throw new ServiceException(ColaboradoresExcecoes.COLABORADOR_FONE_NAO_ENCONTRADO);
        }
        if(empresaId == null || empresaId < 0) {
            throw new ServiceException(ColaboradoresExcecoes.COLABORADOR_EMPRESA_NAO_ENCONTRADO);
        }

    }
    @Override
    public void editSituacaoColaborador(Integer id) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            ProfessorSintetico ps = obterPorId(ctx, id);
            editSituacao(ctx, ps);
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException(ColaboradoresExcecoes.ERRO_EXCLUIR_COLABORADOR, e);
        }
    }

    @Override
    public List<ProfessorResponseTO> listarProfessores(Integer empresaId, Boolean incluirInativos) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            Boolean treinoIndependente = SuperControle.independente(ctx);
            StringBuilder hql = new StringBuilder();
            hql.append("select obj from Usuario obj where professor.codigo is not null ");
            if (incluirInativos == null || !incluirInativos) {
                hql.append("and status = :situacao");
            }
            HashMap params = new HashMap<String, Object>();
            if (incluirInativos == null || !incluirInativos) {
                params.put("situacao", StatusEnum.ATIVO);
            }
            List<Usuario> usuarios = usuarioService.obterPorParam(ctx, hql.toString(),
                    params);
            List<ProfessorResponseTO> ret = new ArrayList<>();
            for (Usuario usuario : usuarios) {
                if (usuario.getProfessor().getProfessorTW()) {
                    ProfessorResponseTO professor = new ProfessorResponseTO(usuario, treinoIndependente);
                    ret.add(professor);
                }
            }
            return ret;
        } catch (Exception e) {
            throw new ServiceException(ProfessorExcecoes.ERRO_BUSCAR_PROFESSORES, e);
        }
    }

    @Override
    public void atualizarRankingProfessores(Integer empresaId, Date dataInicio){
        String ctx = sessaoService.getUsuarioAtual().getChave();
        try {
            dashboardBIService.processarRankingBIProfessoresEmpresa(ctx,  dataInicio, empresaId);
        } catch (ServiceException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void removerFotoAluno(String ctx, HttpServletRequest request, String codigopessoa) throws Exception {
        ProfessorSintetico professor = consultarPorCodigoPessoa(ctx, codigopessoa);
        Aplicacao.deletarFoto(ctx, professor.getPessoa().getFotoKey(), false, request.getSession().getServletContext());
        professor.getPessoa().setFotoKey("fotoPadrao.jpg");
        getProfessorSinteticoDao().update(ctx, professor);
    }

    @Override
    public List<ProfessorIndicadorResponseDTO> carregarIndicadores(HttpServletRequest request, PaginadorDTO paginadorDTO, Integer empresaId, FiltroGestaoJSON filtroGestaoJSON, String sort) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();

            FiltrosGestaoTO filtros = popularFiltroGestao(request, empresaId, filtroGestaoJSON);

            List<AgrupadorIndicadores> agrupadores = gestaoService.calcularIndicadores(
                    request, ctx, paginadorDTO, empresaId, filtros, true,
                    filtroGestaoJSON.getCategoria(), false, true
            );

            Map<Integer, ProfessorSintetico> cacheProfessores = new ConcurrentHashMap<>();

            List<ProfessorIndicadorResponseDTO> listaReturn = agrupadores
                    .parallelStream()
                    .map(agrupador -> {
                        int codProfessor = agrupador.getCodProfessor();

                        ProfessorSintetico professorSintetico = cacheProfessores.computeIfAbsent(codProfessor, codigo -> {
                            try {
                                return ((ProfessorSinteticoService) UtilContext.getBean(ProfessorSinteticoService.class))
                                        .obterPorId(ctx, codigo);
                            } catch (ServiceException e) {
                                throw new RuntimeException("Erro ao obter professor com código: " + codigo, e);
                            }
                        });

                        Object ref = agrupador.getIndicadores().stream()
                                .filter(i -> i != null && i.getTipo() == IndicadorEnum.N_PROF_TREINO_ACOMPANHADO)
                                .flatMap(i -> i.getObjetos().stream())
                                .filter(obj -> obj instanceof ClienteAcompanhamento)
                                .findFirst()
                                .orElse(null);

                        Date dataAcompanhamento = null;
                        if (ref instanceof ClienteAcompanhamento) {
                            dataAcompanhamento = ((ClienteAcompanhamento) ref).getInicio();
                        }

                        ProfessorIndicadorResponseDTO dto = new ProfessorIndicadorResponseDTO(agrupador, professorSintetico);
                        dto.setDataAcompanhamento(dataAcompanhamento);

                        return dto;
                    })
                    .collect(Collectors.toList());

            HashMap<String, String> sortMap = carregarSortMap(sort);

            if (sortMap != null && !sortMap.isEmpty()) {
                Map.Entry<String, String> entry = sortMap.entrySet().iterator().next();
                String field = entry.getKey();
                boolean asc = "ASC".equalsIgnoreCase(entry.getValue());

                boolean temCampo = Arrays.stream(ProfessorIndicadorResponseDTO.class.getDeclaredMethods())
                        .anyMatch(m -> m.getName().equalsIgnoreCase("get" + field));

                if (temCampo) {
                    if (asc) {
                        listaReturn = Ordenacao.ordenarLista(listaReturn, field);
                    } else {
                        listaReturn = Ordenacao.ordenarListaReverse(listaReturn, field);
                    }
                }
            }

            /**
             * De acordo com a doc add o total no final da lista
             */
            listaReturn.add(totalCadaIndicador(listaReturn));

            return listaReturn;
        } catch (ServiceException e) {
            throw new ServiceException(ProfessorExcecoes.ERRO_BUSCAR_INDICADORES_CARTEIRA_PROFESSOR);
        }

    }

    @Override
    public List<ProfessoresAlunosAvisoMedicoResponseDTO> alunosAvisoMedico(HttpServletRequest request, PaginadorDTO paginadorDTO, Integer empresaId, FiltroGestaoJSON filtroGestaoJSON, String sort, boolean contar) throws ServiceException {
        try {
            List<ProfessoresAlunosAvisoMedicoResponseDTO> listPAAM = new ArrayList<>();
            String ctx = sessaoService.getUsuarioAtual().getChave();
            FiltrosGestaoTO filtros = popularFiltroGestao(request, empresaId, filtroGestaoJSON);

            Map<String, String> params = new HashMap<>();
            params.put("chave", ctx);
            params.put("empresa", empresaId.toString());
            params.put("contar", String.valueOf(contar));
            StringBuilder colaborador = new StringBuilder();
            if (filtros.getProfessoresSelecionados() != null && filtros.getProfessoresSelecionados().size() > 0) {
                filtros.getProfessoresSelecionados().forEach(cod -> {
                    try {
                        if(!contar) {
                            ProfessorSintetico professorSintetico =
                                    ((ProfessorSinteticoService) UtilContext.getBean(ProfessorSinteticoService.class))
                                            .obterPorId(ctx, Integer.valueOf(cod));
                            if (professorSintetico != null && professorSintetico.getCodigoColaborador() != 0) {
                                if (colaborador.toString().isEmpty()) {
                                    colaborador.append(professorSintetico.getCodigoColaborador());
                                } else {
                                    colaborador.append(";").append(professorSintetico.getCodigoColaborador());
                                }
                            }
                        }else {
                            if (colaborador.toString().isEmpty()) {
                                colaborador.append(cod);
                            } else {
                                colaborador.append(";").append(cod);
                            }
                        }
                    } catch (ServiceException e) {
                        throw new RuntimeException(e);
                    }
                });
            }
            params.put("colaborador", colaborador.toString());

            JSONArray body = chamadaZW(ctx, "/prest/treino/alunos-aviso-medico", empresaId, params);

            for (int i = 0; i < body.length(); i++) {
                JSONObject obj = body.getJSONObject(i);
                if (contar) {
                    ProfessorSintetico professorSintetico = gestaoService.obterPorIdColaborador(ctx, obj.getInt("colaborador"));
                    ProfessoresAlunosAvisoMedicoResponseDTO pAAM = new ProfessoresAlunosAvisoMedicoResponseDTO(professorSintetico);
                    pAAM.setQtdAlunosMsg(obj.getInt("qtdAlunosMsg"));
                    listPAAM.add(pAAM);
                } else {
                    ProfessoresAlunosAvisoMedicoResponseDTO pAAM = new ProfessoresAlunosAvisoMedicoResponseDTO();
                    pAAM.setNome(obj.getString("nome"));
                    pAAM.setMatricula(obj.getString("matricula"));
                    pAAM.setSituacao(obj.getString("situacao"));
                    try {
                        pAAM.setTerminoContrato(obj.getString("terminoContrato"));
                    }catch (Exception ignore){}
                    ClienteSintetico clienteSintetico = cs.consultarPorMatricula(ctx, pAAM.getMatricula());
                    ProgramaTreino prog = programaService.obterUltimoProgramaVigente(ctx, clienteSintetico);
                    pAAM.setTerminoProgramaVigente(prog != null ? prog.getDataTerminoPrevisto().toString() : "");
                    pAAM.setAvisoMedico(obj.getString("avisoMedico"));
                    listPAAM.add(pAAM);
                }
            }
            HashMap<String, String> sortMap = carregarSortMap(sort);
            if (sortMap != null) {
                for (Map.Entry<String, String> entry : sortMap.entrySet()) {
                    if (entry.getValue().equals("ASC")) {
                        listPAAM = Ordenacao.ordenarListaReverse(listPAAM, entry.getKey());
                    } else {
                        listPAAM = Ordenacao.ordenarLista(listPAAM, entry.getKey());
                    }
                }
            }

            return Ordenacao.ordenarLista(listPAAM, "nome");
        } catch (ServiceException e) {
            throw new ServiceException(ProfessorExcecoes.ERRO_BUSCAR_INDICADORES_CARTEIRA_PROFESSOR);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

    }

    private JSONArray chamadaZW(String ctx, String endpoint, Integer empresa, Map<String, String> requestParams) throws Exception{
        final String url = Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebInteg);
        CloseableHttpClient client = ExecuteRequestHttpService.createConnector();
        HttpPost httpPost = new HttpPost(url + endpoint);

        List<NameValuePair> params = new ArrayList<>();
        params.add(new BasicNameValuePair("chave", ctx));
        params.add(new BasicNameValuePair("empresa", empresa.toString()));
        if(requestParams != null){
            for(String k : requestParams.keySet()){
                params.add(new BasicNameValuePair(k, requestParams.get(k)));
            }
        }

        httpPost.setEntity(new UrlEncodedFormEntity(params));
        CloseableHttpResponse response = client.execute(httpPost);
        ResponseHandler<String> handler = new BasicResponseHandler();
        String body = handler.handleResponse(response);
        client.close();
        return new JSONArray(body);
    }

    private ProfessorIndicadorResponseDTO totalCadaIndicador(List<ProfessorIndicadorResponseDTO> professores) {
        ProfessorIndicadorResponseDTO professor = new ProfessorIndicadorResponseDTO();

        for (ProfessorIndicadorResponseDTO prof : professores) {
            //######################### Indicadores da carteira ####################
            professor.setComTreino(resultadoSoma(professor.getComTreino() != null ? professor.getComTreino().doubleValue() : 0, prof.getComTreino() != null ? prof.getComTreino().doubleValue() : 0));
            professor.setSemTreino(resultadoSoma(professor.getSemTreino() != null ? professor.getSemTreino().doubleValue() : 0, prof.getSemTreino() != null ? prof.getSemTreino().doubleValue() : 0));
            professor.setVencidos(resultadoSoma(professor.getVencidos() != null ? professor.getVencidos().doubleValue() : 0, prof.getVencidos() != null ? prof.getVencidos().doubleValue() : 0));
            professor.setProxVencimento(resultadoSoma(professor.getProxVencimento() != null ? professor.getProxVencimento().doubleValue() : 0,
                    prof.getProxVencimento() != null ? prof.getProxVencimento().doubleValue() : 0));
            professor.setAvaliacao(resultadoSoma(professor.getAvaliacao() != null ? professor.getAvaliacao().doubleValue() : 0, prof.getAvaliacao() != null ? prof.getAvaliacao().doubleValue() : 0));
            professor.setEstrelas2(resultadoSoma(professor.getEstrelas2() != null ? professor.getEstrelas2().doubleValue() : 0, prof.getEstrelas2() != null ? prof.getEstrelas2().doubleValue() : 0));

            //############### Indicadores de atividades #######################
            professor.setNovos(resultadoSoma(professor.getNovos() != null ? professor.getNovos().doubleValue() : 0, prof.getNovos() != null ? prof.getNovos().doubleValue() : 0));
            professor.setRenovados(resultadoSoma(professor.getRenovados() != null ? professor.getRenovados().doubleValue() : 0, prof.getRenovados() != null ? prof.getRenovados().doubleValue() : 0));
            professor.setRevisados(resultadoSoma(professor.getRevisados() != null ? professor.getRevisados().doubleValue() : 0, prof.getRevisados() != null ? prof.getRevisados().doubleValue() : 0));
            professor.setAcompanhamentos(resultadoSoma(professor.getAcompanhamentos() != null ? professor.getAcompanhamentos().doubleValue() : 0,
                    prof.getAcompanhamentos() != null ? prof.getAcompanhamentos().doubleValue() : 0));
            professor.setRevisar(resultadoSoma(professor.getRevisar() != null ? professor.getRevisar().doubleValue() : 0, prof.getRevisar() != null ? prof.getRevisar().doubleValue() : 0));
            professor.setAtividadesAcompanhamentos(resultadoSoma(professor.getAtividadesAcompanhamentos() != null ? professor.getAtividadesAcompanhamentos().doubleValue() : 0, prof.getAtividadesAcompanhamentos() != null ? prof.getAtividadesAcompanhamentos().doubleValue() : 0));
            //############### Indicadores de atividades #######################
            professor.setAgendados(resultadoSoma(professor.getAgendados() != null ? professor.getAgendados().doubleValue() : 0, prof.getAgendados() != null ? prof.getAgendados().doubleValue() : 0));
            professor.setExecutados(resultadoSoma(professor.getExecutados() != null ? professor.getExecutados().doubleValue() : 0, prof.getExecutados() != null ? prof.getExecutados().doubleValue() : 0));
            professor.setCancelados(resultadoSoma(professor.getCancelados() != null ? professor.getCancelados().doubleValue() : 0, prof.getCancelados() != null ? prof.getCancelados().doubleValue() : 0));
            professor.setFaltas(resultadoSoma(professor.getFaltas() != null ? professor.getFaltas().doubleValue() : 0, prof.getFaltas() != null ? prof.getFaltas().doubleValue() : 0));

        }

        return professor;
    }

    private Double resultadoSoma(Double variavelPrincipal, Double soma) {
        return variavelPrincipal + soma;
    }

    public HashMap<String, String> carregarSortMap(String sort) {
        if (StringUtils.isBlank(sort)) {
            return null;
        }

        final String[] ordenacoes = sort.split(SEPARADOR_ORDENACOES);
        if (ordenacoes.length < 1) {
            return null;
        }

        HashMap<String, String> sortMap= new HashMap<>();
        for (String ordenacao : ordenacoes) {
            final String[] ordem = ordenacao.split(SEPARADOR_ORDENACAO);
            if (ordem.length < 2) {
                continue;
            }

            sortMap.put(ordem[INDEX_CAMPO_ORDENAR], ordem[INDEX_DIRECAO_ORDENACAO]);
        }
        return sortMap;
    }

    @Override
    public List<AlunoSimplesDTO> carregarAlunosPorIndicador(HttpServletRequest request, PaginadorDTO paginadorDTO, Integer empresaId, FiltroGestaoJSON filtroGestaoJSON, String sort) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            List<ClienteSintetico> clientes = new ArrayList<>();

            FiltrosGestaoTO filtros = popularFiltroGestao(request, empresaId, filtroGestaoJSON);
            String professoresSelecionados = "";
            for (String cod : filtros.getProfessoresSelecionados()) {
                if (!UteisValidacao.emptyString(cod)) {
                    final Integer codPesq;
                    if (filtros.isVazio()) {
                        ProfessorSintetico professorSintetico = obterPorId(ctx, Integer.valueOf(cod));
                        codPesq = professorSintetico.getCodigo();
                    } else {
                        ProfessorSintetico professorSintetico = consultarPorCodigoColaborador(ctx, Integer.valueOf(cod));
                        codPesq = professorSintetico.getCodigo();
                    }
                    SelectItem selecionado = (SelectItem) ColecaoUtils.find(filtros.getProfessores(), new Predicate() {
                        @Override
                        public boolean evaluate(Object o) {
                            Integer codProfessor = (Integer) ((SelectItem) o).getValue();
                            return codProfessor.intValue() == codPesq;
                        }
                    });
                    if (selecionado != null) {
                        professoresSelecionados += "," + ((Integer) selecionado.getValue()).toString();
                    }
                }
            }

            if (filtroGestaoJSON.getCategoria().equals(CategoriaIndicadorEnum.CARTEIRAS_PROF)) {
                clientes.addAll(gestaoService.carregarAlunosPorIndicadoresCarteiraProfessor(request, ctx, paginadorDTO, empresaId, filtroGestaoJSON.getIndicador(),
                        professoresSelecionados, filtros, false));
            } else if (filtroGestaoJSON.getCategoria().equals(CategoriaIndicadorEnum.ATIVIDADES_PROF)) {
                clientes.addAll(gestaoService.carregarAlunosPorAtividadeProfessor(ctx, empresaId, filtros, filtroGestaoJSON.getIndicador(), professoresSelecionados));
            }

            List<AlunoSimplesDTO> listReturn = new ArrayList<>();
            for (ClienteSintetico cs : clientes) {
                ProgramaTreino pt = programaService.consultarSomenteDataTreinoAtual(ctx, cs);
                cs.setProgramaVigente(pt);
                AlunoSimplesDTO aluno = new AlunoSimplesDTO(cs);

                listReturn.add(aluno);
            }

            HashMap<String, String> sortMap = carregarSortMap(sort);

            if (sortMap != null) {
                for (Map.Entry<String, String> entry : sortMap.entrySet()) {
                    if (entry.getValue().equals("ASC")) {
                        listReturn = Ordenacao.ordenarLista(listReturn, entry.getKey());
                    } else {
                        listReturn = Ordenacao.ordenarListaReverse(listReturn, entry.getKey());
                    }
                }
            }

            return listReturn;
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(ProfessorExcecoes.ERRO_CARREGAR_ALUNOS_POR_INDICADOR);
        }
    }

    public FiltrosGestaoTO popularFiltroGestao(HttpServletRequest request, Integer empresaId, FiltroGestaoJSON filtroGestaoJSON) throws ServiceException {
        FiltrosGestaoTO filtros = new FiltrosGestaoTO();
        String ctx = sessaoService.getUsuarioAtual().getChave();

        if (filtroGestaoJSON.getIncluirProfessorInativo()) {
            filtros.getOpcoesProfessores().add(new GenericoTO("IA", "Incluir Professores Inativos", true));
        }
        if (filtroGestaoJSON.getDataReferencia() != null) {
            filtros.setInicio(filtroGestaoJSON.getDataReferencia());
            filtros.setFim(filtroGestaoJSON.getDataReferencia());
        } else if (filtroGestaoJSON.getInicio() != null && filtroGestaoJSON.getFim() != null){
            filtros.setInicio(filtroGestaoJSON.getInicio());
            filtros.setFim(filtroGestaoJSON.getFim());

        } else {
            filtros.setInicio(new Date());
            filtros.setFim(new Date());
        }
        if(filtroGestaoJSON.getMesAno().size()!= 0){
            for(FiltroGestaoJSON.MesAno mesAno : filtroGestaoJSON.getMesAno()){
                filtros.setMes(mesAno.getMes());
                filtros.setAno(mesAno.getAno());
            }

        }

        if (filtroGestaoJSON.getProfessoresIds() != null && filtroGestaoJSON.getProfessoresIds().size() > 0) {
            if (filtroGestaoJSON.getIndicador() != null) {
                for (Integer professor : filtroGestaoJSON.getProfessoresIds()) {
                    ProfessorSintetico professorSintetico = consultarPorCodigoColaborador(ctx, professor);
                    filtros.getProfessoresSelecionados().add(professorSintetico.getCodigo().toString());
                }
            } else {
                for (Integer professor : filtroGestaoJSON.getProfessoresIds()) {
                    filtros.getProfessoresSelecionados().add(professor.toString());
                }
            }
        }
        if (filtroGestaoJSON.getTiposEventosIds() != null && filtroGestaoJSON.getTiposEventosIds().size() > 0) {
            for (Integer tipoEvento : filtroGestaoJSON.getTiposEventosIds()) {
                TipoEvento tipoEvent = tipoEventoService.obterPorId(ctx, tipoEvento);
                filtros.getTiposEvento().add(tipoEvent);
            }
        }

        if (!UteisValidacao.emptyNumber(filtroGestaoJSON.getProfessorId())) {
            filtros.getProfessoresSelecionados().add(filtroGestaoJSON.getProfessorId().toString());
        }

        if (filtroGestaoJSON.getStatus() != null && filtroGestaoJSON.getStatus().size() > 0) {
            for (SituacaoAlunoEnum sit : filtroGestaoJSON.getStatus()) {
                GenericoTO genericoTO = new GenericoTO();
                genericoTO.setLabel(sit.name());
                genericoTO.setEscolhido(true);

                filtros.getSituacoesAluno().add(genericoTO);
            }
        }

        if (filtroGestaoJSON.getContrato() != null && filtroGestaoJSON.getContrato().size() > 0) {
            for (SituacaoContratoEnum situacaoContrato : filtroGestaoJSON.getContrato()) {
                GenericoTO genericoTO = new GenericoTO();
                genericoTO.setLabel(situacaoContrato.name());
                genericoTO.setEscolhido(true);

                filtros.getSituacoesContrato().add(genericoTO);
            }
        }

        if (filtroGestaoJSON.getSituacaoPrograma() != null && filtroGestaoJSON.getSituacaoPrograma().size() > 0) {
            for(SituacaoProgramaEnum situacaoProgramaEnum : filtroGestaoJSON.getSituacaoPrograma()) {
                if (situacaoProgramaEnum == SituacaoProgramaEnum.NOVOS) {
                    filtros.setNovo(true);
                }

                if (situacaoProgramaEnum == SituacaoProgramaEnum.RENOVADOS) {
                    filtros.setRenovado(true);
                }

                if (situacaoProgramaEnum == SituacaoProgramaEnum.REVISADOS) {
                    filtros.setRevisado(true);
                }

                if (situacaoProgramaEnum == SituacaoProgramaEnum.NAO_REVISADOS) {
                    filtros.setNaoRevisado(true);
                }
            }
        }

        List<ProfessorResponseTO> listaProfessores = listarTodosColaborador(request, empresaId, filtroGestaoJSON.getIncluirProfessorInativo(), false, null);
        List<SelectItem> professoresListados = new ArrayList<>();
        if (!UteisValidacao.emptyList(filtros.getProfessoresSelecionados())) { filtros.setVazio(true);}
        for (ProfessorResponseTO professor : listaProfessores) {

            ProfessorSintetico professorSintetico = consultarPorCodigoColaborador(ctx, professor.getId());
            SelectItem selectItem = new SelectItem();
            selectItem.setDescription(null);
            selectItem.setDisabled(false);
            selectItem.setEscape(true);
            selectItem.setLabel(professor.getNome());
            selectItem.setNoSelectionOption(false);
            if(!SuperControle.independente(ctx) && professorSintetico != null) {
                selectItem.setValue(professorSintetico.getCodigo());
            } else {
                selectItem.setValue(professor.getId());
            }

            if (filtros.isVazio()) {
                if (filtros.getProfessoresSelecionados().contains(professor.getId().toString())) {
                    filtros.getProfessoresSelecionados().add(professor.getId().toString());
                }
            } else {
                filtros.getProfessoresSelecionados().add(professor.getId().toString());
            }
            professoresListados.add(selectItem);
        }

        filtros.setProfessores(professoresListados);

        return filtros;
    }

    @Override
    public ConfiguracaoRankingDTO incluirConfiguracao(Integer empresaId, ConfiguracaoRankingDTO configuracaoRankingDTO) throws ServiceException {

        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();

            ConfiguracaoRankingProfessores config = ConfiguracaoRankingBuilder.convertDTOToEntitie(configuracaoRankingDTO, empresaId);

            if (validarConfiguracoes(config)) {
                config = ((DashboardBIService) UtilContext.getBean(DashboardBIService.class)).gravarCfgRanking(ctx, config);
            } else {
                throw new ServiceException(ProfessorExcecoes.CAMPOS_OBRIGATORIOS);
            }

            return ConfiguracaoRankingBuilder.convertEntitieToDTO(config);
        } catch (Exception e) {
            throw new ServiceException(ProfessorExcecoes.ERRO_INCLUIR_CONFIGURACAO_RANKING);
        }
    }

    private Boolean validarConfiguracoes(ConfiguracaoRankingProfessores configuracao) {
        if (configuracao.getIndicador() == null) {
            return false;
        }
        if (UteisValidacao.emptyNumber(configuracao.getPeso())) {
            return false;
        }
        return true;
    }

    public boolean toggleConfiguracoesRanking(Integer empresaId, IndicadorDashboardEnum indicador) throws ServiceException{
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            Map<IndicadorDashboardEnum, ConfiguracaoRankingProfessores> indicadores = new HashMap<>();
            List<ConfiguracaoRankingProfessores> listConfig = dashboardBIService.obterCfgRanking(ctx, null, empresaId, null, false);
            for (ConfiguracaoRankingProfessores configuracao : listConfig) {
                indicadores.put(configuracao.getIndicador(), configuracao);
            }
            ConfiguracaoRankingProfessores cfg = indicadores.get(indicador);
            if(cfg == null){
                cfg = new ConfiguracaoRankingProfessores();
                cfg.setIndicador(indicador);
                cfg.setEmpresa(empresaId);
                cfg.setAtivo(false);
                cfg.setPositivo(true);
                cfg.setPeso(0.0);
            }
            cfg.setAtivo(!cfg.getAtivo());
            cfg = dashboardBIService.gravarCfgRanking(ctx, cfg);
            return cfg.getAtivo();
        } catch (Exception e) {
            throw new ServiceException(ProfessorExcecoes.ERRO_CARREGAR_CONFIGURACOES_RANKING);
        }
    }

    public Map<AgrupamentoIndicadorDashboardEnum, List<ConfiguracaoRankingDTO>> gravarConfiguracoesRanking(Integer empresaId, Map<String, String> valores) throws ServiceException{
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            Map<IndicadorDashboardEnum, ConfiguracaoRankingProfessores> indicadores = new HashMap<>();
            List<ConfiguracaoRankingProfessores> listConfig = dashboardBIService.obterCfgRanking(ctx, null, empresaId, null, false);
            for (ConfiguracaoRankingProfessores configuracao : listConfig) {
                indicadores.put(configuracao.getIndicador(), configuracao);
            }
            for(String indStr : valores.keySet()){
                IndicadorDashboardEnum ind = IndicadorDashboardEnum.valueOf(indStr);
                if(ind.getAgrupamento() == null){
                    continue;
                }
                ConfiguracaoRankingProfessores cfg = indicadores.get(ind);
                Integer peso = Integer.valueOf(valores.get(indStr));
                if(cfg == null && peso == 0){
                    continue;
                }

                if(cfg == null){
                    cfg = new ConfiguracaoRankingProfessores();
                    cfg.setIndicador(ind);
                    cfg.setEmpresa(empresaId);
                    cfg.setAtivo(false);
                }
                cfg.setPositivo(peso >= 0);
                cfg.setPeso(new Double(peso < 0 ? peso * -1 : peso));
                dashboardBIService.gravarCfgRanking(ctx, cfg);
            }
            return listarConfiguracoesRanking(empresaId, null, null);
        } catch (Exception e) {
            throw new ServiceException(ProfessorExcecoes.ERRO_CARREGAR_CONFIGURACOES_RANKING);
        }
    }

    @Override
    public Map<AgrupamentoIndicadorDashboardEnum, List<ConfiguracaoRankingDTO>> listarConfiguracoesRanking(Integer empresaId, PaginadorDTO paginadorDTO, JSONObject filters) throws ServiceException {

        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            Map<AgrupamentoIndicadorDashboardEnum, List<ConfiguracaoRankingDTO>> mapa = new HashMap<>();
            Map<IndicadorDashboardEnum, ConfiguracaoRankingDTO> indicadores = new HashMap<>();
            mapa.put(AgrupamentoIndicadorDashboardEnum.AGENDA, new ArrayList<>());
            mapa.put(AgrupamentoIndicadorDashboardEnum.ALUNOS, new ArrayList<>());
            mapa.put(AgrupamentoIndicadorDashboardEnum.TREINO, new ArrayList<>());
            List<ConfiguracaoRankingProfessores> listConfig = (UtilContext.getBean(DashboardBIService.class)).obterCfgRanking(ctx, null, empresaId, filters, false);
            for (ConfiguracaoRankingProfessores configuracao : listConfig) {
                indicadores.put(configuracao.getIndicador(), ConfiguracaoRankingBuilder.convertEntitieToDTO(configuracao));
            }
            for(IndicadorDashboardEnum ind : IndicadorDashboardEnum.values()){
                if(ind.getAgrupamento() == null){
                    continue;
                }
                ConfiguracaoRankingDTO configuracaoRankingDTO = indicadores.get(ind);
                if(configuracaoRankingDTO == null){
                    configuracaoRankingDTO = new ConfiguracaoRankingDTO(ind);
                } else {
                    DecimalFormat myDF = new DecimalFormat("0.#");
                    configuracaoRankingDTO.setPontuacao((configuracaoRankingDTO.getOperacao() ? "" : "-") + myDF.format(configuracaoRankingDTO.getPeso()));
                }
                mapa.get(ind.getAgrupamento()).add(configuracaoRankingDTO);
            }

            return mapa;
        } catch (Exception e) {
            throw new ServiceException(ProfessorExcecoes.ERRO_CARREGAR_CONFIGURACOES_RANKING);
        }
    }

    @Override
    public void removerConfiguracaoRanking(Integer configuracaoId) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            if (!UteisValidacao.emptyNumber(configuracaoId)) {
                ConfiguracaoRankingProfessores config = ((ConfiguracaoRankingProfessoresDao) UtilContext.getBean(ConfiguracaoRankingProfessoresDao.class)).findById(ctx, configuracaoId);

                ((DashboardBIService) UtilContext.getBean(DashboardBIService.class)).excluirCfgRanking(ctx, config);
            } else {
                throw new ServiceException(ProfessorExcecoes.CONFIGURACAO_NAO_INFORMADO);
            }
        } catch (Exception e) {
            throw new ServiceException(ProfessorExcecoes.ERRO_REMOVER_CONFIGURACAO);
        }
    }

    protected void prepare(final String ctx) throws ServiceException {
        EntityManagerService ems = (EntityManagerService) UtilContext.getBean(EntityManagerService.class);
        try {
            ems.closeQuietly(ctx);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    public void salvarMidiaNuvem(String chave, ColaboradorTO colaboradorTO, ProfessorSintetico professorSintetico) throws Exception {
        if (UteisValidacao.emptyNumber(professorSintetico.getCodigo())) {
            professorSintetico = professorsinteticoDao.insert(chave, professorSintetico);
        }
        String key = UtilS3Base64Img.getUrlImage(colaboradorTO.getImagemData(), colaboradorTO.getExtensaoImagem(), chave, professorSintetico.getPessoa());
        Aplicacao.saveImageLocal(context, chave, key, true, null, false);
        professorSintetico.getPessoa().setFotoKey(key+ "?time=" + Calendario.hoje().getTime());
        professorSintetico.setUriImagem(Uteis.getPaintFotoDaNuvem(key));
        professorsinteticoDao.update(chave,professorSintetico);
        alteraFotoZW(chave,professorSintetico.getCodigoColaborador(),professorSintetico.getPessoa().getFotoKey());
    }

    public String incluirAtualizarConfigRelatorio(String configId, String config) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            Integer idUsuario = sessaoService.getUsuarioAtual().getId();
            Usuario usuario = usuarioService.obterPorId(ctx, idUsuario);

            ConfigRelatorio cr = new ConfigRelatorio();
            cr.setUsuario(usuario);
            cr.setTableId(configId);
            cr.setConfigTable(config);

            if (!configRelatorioDao.exists(ctx, usuario, "nome")) {
                configRelatorioDao.insert(ctx, cr);
            } else {
                configRelatorioDao.update(ctx, cr);
            }
            return config;
        } catch (Exception e) {
            throw new ServiceException(ProfessorExcecoes.ERRO_AO_SALVAR_CONFIG_RELATORIO);
        }
    }

    public String obterConfigRelatorio(String configId) throws Exception {

        String ctx = sessaoService.getUsuarioAtual().getChave();
        Integer idUsuario = sessaoService.getUsuarioAtual().getId();

        ConfigRelatorio cr = new ConfigRelatorio();
        cr.setUsuario(usuarioService.obterPorId(ctx, idUsuario));

        String sql = "SELECT * FROM ConfigRelatorio  WHERE usuario_codigo = " + cr.getUsuario().getCodigo() + " and  tableid like '" + configId + "%'";
        try (ResultSet rs = configRelatorioDao.createStatement(ctx, sql)) {
            if (rs.next()) {
                return rs.getString("configtable");
            } else {
                throw new ServiceException(ProfessorExcecoes.ERRO_AO_OBTER_CONFIG_RELATORIO);
            }
        }
    }

    public void deleteFotoNuvem(final String key) throws ServiceException{
        try {
            MidiaService.getInstanceWood().deleteObject(key);
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    @Override
    public void setServletContext(ServletContext servletContext) {
        this.context = servletContext;
    }


    @Override
    public List<ColaboradorResponseTO> listarColaboradoresSimples(HttpServletRequest request, FiltroColaboradorJSON filtros,
                                                           PaginadorDTO paginadorDTO, Integer empresaId) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            List<ProfessorSintetico> lista = consultarTodosProfessores(ctx, empresaId, false, false, filtros.getParametros(), paginadorDTO.getSize());
            List<ColaboradorResponseTO> listaRet = new ArrayList<>();
            if (lista != null) {
                for (ProfessorSintetico ps : lista) {
                    if (ps.getPessoa() != null) {
                        ps.setUriImagem(fotoService.defineURLFotoPessoa(request, ps.getPessoa().getFotoKey(), ps.getCodigoPessoa(), false, ctx, !SuperControle.independente(ctx)));
                    }
                    listaRet.add(new ColaboradorResponseTO(ps, null, SuperControle.independente(ctx)));
                }
            }
            return listaRet;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException(ColaboradoresExcecoes.ERRO_BUSCAR_COLABORADORES, e);
        }
    }

    @Override
    public Integer obterCodigoColaboradorPorUsuario(String ctx, Integer codUsuario) {
        try {
            String where = " where u.usuariozw = "+ codUsuario;
            if(SuperControle.independente(ctx)){
                where = " where u.codigo = "+ codUsuario;
            }

            String sql =
                    "select p.codigocolaborador " +
                    "from professorsintetico p " +
                    "inner join usuario u on u.professor_codigo = p.codigo " + where;
            try (ResultSet rs = configRelatorioDao.createStatement(ctx, sql)) {
                if (rs.next()) {
                    return rs.getInt("codigocolaborador");
                }
            }
        } catch (Exception ex) {
            Uteis.logar(ex, ProfessorSinteticoServiceImpl.class);
        }
        return null;
    }

    @Override
    public ProfessorSintetico obterPorAtributo(String ctx, String atributo, Object valor) throws ServiceException {
        try {
            String identificador = "professorsintetico-".concat(atributo).concat("-").concat(valor.toString().replaceAll(" ", "0_"));
            ProfessorSintetico professorSintetico = memcached.ler(ctx, identificador);
            if (professorSintetico != null) {
                return professorSintetico;
            }
            professorSintetico = getProfessorSinteticoDao().findObjectByAttribute(ctx, atributo, valor);

            if (professorSintetico != null) {
                cachearEmpresa(ctx, identificador, professorSintetico);
            }
            return professorSintetico;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public Integer obterCodigoProfessorPorColaborador(String ctx, Integer codColaborador) {
        try {
            String sql = "SELECT p.codigo FROM professorsintetico p WHERE p.codigocolaborador = " + codColaborador;
            try (ResultSet rs = professorsinteticoDao.createStatement(ctx, sql)) {
                if (rs.next()) {
                    return rs.getInt("codigo");
                }
            }
        } catch (Exception ex) {
            Uteis.logar(ex, ProfessorSinteticoServiceImpl.class);
        }
        return null;
    }

    private void cachearEmpresa(String ctx, String identificador, ProfessorSintetico professorSintetico) {
        try {
            if (memcached.getMemcachedClient() == null) {
                return;
            }
            memcached.gravar(ctx, identificador, professorSintetico);
        } catch (Exception e) {
            Uteis.logar(e, UsuarioServiceImpl.class);
        }
    }

    @Override
    public Integer consultarCodProfessorPorNomeECodigoEmpresa(final String chave, String nome, Integer codigoEmpresa) throws ServiceException {
        try {
            StringBuilder sql = new StringBuilder("SELECT obj.codigo FROM professorsintetico obj WHERE obj.professortw IS TRUE AND obj.empresa_codigo = ").append(codigoEmpresa);
            if(!UteisValidacao.emptyString(nome)){
                sql.append(" AND upper(obj.nome) ilike CONCAT('%").append(nome.toUpperCase()).append("%')");
            }
            try (ResultSet rs = getProfessorSinteticoDao().createStatement(chave, sql.toString())) {
                if (rs.next()) {
                    return rs.getInt("codigo");
                }
            }
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
        return null;
    }

    public void alteraFotoZW(String ctx, Integer codigoColaborador, String fotokey){
        Integer codigoPessoa=0;
        String sql = "select pessoa from colaborador c where c.codigo = ?";
        try (Connection conZW = conexaoZWService.conexaoZw(ctx);
            PreparedStatement statement = conZW.prepareStatement(sql)) {
            statement.setInt(1, codigoColaborador);
            try (ResultSet resultSet = statement.executeQuery()) {
                if (resultSet.next()) {
                    codigoPessoa = resultSet.getInt("pessoa");
                }
            }
            String insert = "update pessoa set fotokey =? where codigo = ?";
            PreparedStatement stm = conZW.prepareStatement(insert);
            stm.setString(1,fotokey);
            stm.setInt(2, codigoPessoa);
            stm.execute();

        }  catch (Exception e) {
            Uteis.logar(e, ConfiguracaoSistemaServiceImpl.class);
        }
    }

    public String sincronizarTodosProfessores(String key) throws ServiceException {
        try {
            final String url = Aplicacao.getProp(key, Aplicacao.urlZillyonWebInteg);
            CloseableHttpClient client = ExecuteRequestHttpService.createConnector();
            HttpPost httpPost = new HttpPost(url + "/prest/processos");

            List<NameValuePair> params = new ArrayList<>();
            params.add(new BasicNameValuePair("key", key));
            params.add(new BasicNameValuePair("op", "sincronizar-professor-colaborador-treino"));

            httpPost.setEntity(new UrlEncodedFormEntity(params));
            CloseableHttpResponse response = client.execute(httpPost);
            ResponseHandler<String> handler = new BasicResponseHandler();
            String body = handler.handleResponse(response);
            client.close();
            return body;
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new ServiceException(ex);
        }
    }

    @Override
    public List<ColaboradorDTO> listarTodosColaboradoresPorEmpresa(String ids, Integer empresaZw) throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("select c.codigo, p.nome, p.fotokey \n");
            sql.append("from colaborador c \n");
            sql.append("inner join pessoa p on p.codigo = c.pessoa \n");
            sql.append(
                    (UteisValidacao.emptyString(ids)
                            ? "where c.situacao = 'AT' \n"
                            : ("where c.codigo in (" + ids + ") \n"))
            );

            if (empresaZw != null && empresaZw > 0) {
                sql.append("and c.empresa = " + empresaZw + " \n");
            }

            sql.append("order by p.nome");

            List<ColaboradorDTO> colaboradores = new ArrayList<>();
            try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
                try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sql.toString(), conZW)) {
                    while (rs.next()) {
                        ColaboradorDTO colaboradorDTO = new ColaboradorDTO();
                        colaboradorDTO.setId(rs.getInt("codigo"));
                        colaboradorDTO.setNome(rs.getString("nome"));
                        colaboradorDTO.setImageUri(Uteis.getPaintFotoDaNuvem(rs.getString("fotokey")));
                        colaboradores.add(colaboradorDTO);
                    }
                }
            }
            return colaboradores;
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(ProfessorExcecoes.ERRO_BUSCAR_COLABORADORES);
        }
    }

    @Override
    public IndicadorAtividadeProfessorAcumuladoVO carregarIndicadoresAcumulados(HttpServletRequest request, Integer empresaId) throws ServiceException {

        try {
            IndicadorAtividadeProfessorAcumuladoVO indicadorAtividadeProfessorAcumuladoVO = new IndicadorAtividadeProfessorAcumuladoVO();

            LinkedHashMap<String, Integer> indicadores = new LinkedHashMap <>();
            String ctx = sessaoService.getUsuarioAtual().getChave();
            ProfessorSintetico professor = usuarioService.consultarPorUsuarioZWAndEmpresaZW(ctx, sessaoService.getUsuarioAtual().getId(), null).getProfessor();
            boolean existeProfessor = professor != null && !UteisValidacao.emptyNumber(professor.getCodigo());
            if(!existeProfessor) {
                throw new ServiceException("O token informado não possui um colaborador associado.");
            }
            Date dataAtual = Calendario.hoje();
            String nome = existeProfessor ? professor.getNome() : "";
            Integer professorId = professor.getCodigo();

            LinkedHashMap<String, Integer> periodos = new LinkedHashMap <>();
            periodos.put("30_dias", 30);
            periodos.put("60_dias", 60);
            periodos.put("90_dias", 90);

            for (Map.Entry<String, Integer> periodo : periodos.entrySet()) {
                String chave = periodo.getKey();
                Integer dias = periodo.getValue();

                // Prepara parâmetros e consulta para o intervalo
                Date dataInicio = Uteis.somarDias(dataAtual, -dias);
                Map<String, Object> parametros = new HashMap<>();
                parametros.put("inicio", dataInicio);
                parametros.put("fim", dataAtual);
                parametros.put("professorId", professorId);

                // Consulta para novos programas de treino
                StringBuilder whereNovos = new StringBuilder(" WHERE obj.cliente.empresa = ").append(empresaId);
                whereNovos.append(" AND obj.dataLancamento BETWEEN :inicio AND :fim");
                whereNovos.append(" AND obj.programaTreinoRenovacao IS NULL");
                whereNovos.append(" AND obj.professorMontou.codigo = :professorId");
                List<ProgramaTreino> novos = programaDao.findByParam(ctx, whereNovos, parametros);

                // Consulta para programas renovados
                StringBuilder whereRenovados = new StringBuilder(" WHERE obj.cliente.empresa = ").append(empresaId);
                whereRenovados.append(" AND obj.dataLancamento BETWEEN :inicio AND :fim");
                whereRenovados.append(" AND obj.programaTreinoRenovacao IS NOT NULL");
                whereRenovados.append(" AND obj.professorMontou.codigo = :professorId");
                List<ProgramaTreino> renovados = programaDao.findByParam(ctx, whereRenovados, parametros);

                // Consulta para revisões
                StringBuilder whereRevisoes = new StringBuilder(" WHERE obj.cliente.empresa = ").append(empresaId);
                whereRevisoes.append(" AND obj.dataRegistro BETWEEN :inicio AND :fim");
                whereRevisoes.append(" AND obj.professorRevisou.codigo = :professorId");
                List<HistoricoRevisaoProgramaTreino> revisoes = historicoRevisaoDao.findByParam(ctx, whereRevisoes, parametros);

                // Consulta para acompanhamentos
                StringBuilder whereAcompanhamentos = new StringBuilder(" WHERE obj.cliente.empresa = ").append(empresaId);
                whereAcompanhamentos.append(" AND obj.inicio BETWEEN :inicio AND :fim");
                whereAcompanhamentos.append(" AND obj.professor.codigo = :professorId");
                List<ClienteAcompanhamento> acompanhamentos = clienteAcompanhamentoDao.findByParam(ctx, whereAcompanhamentos, parametros);

                Integer totalAtendimentos = novos.size() + renovados.size() + revisoes.size() + acompanhamentos.size();
                indicadores.put(chave, totalAtendimentos);
            }

            indicadorAtividadeProfessorAcumuladoVO.setIndicadores(indicadores);
            indicadorAtividadeProfessorAcumuladoVO.setProfessorId(professorId);
            indicadorAtividadeProfessorAcumuladoVO.setNome(nome);
            return indicadorAtividadeProfessorAcumuladoVO;
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public ProfessorSintetico consultarPorCodigo(String ctx, Integer codigo, Integer empresa, Boolean ativos) throws ServiceException {
        try {
            Map<String, Object> params = new HashMap<String, Object>();
            StringBuffer sql = new StringBuffer("select obj from ProfessorSintetico obj where obj.professorTW is true ");
            if (null != ativos) {
                sql.append("and ativo is ").append(ativos);
            }

            if (empresa != null && empresa > 0) {
                sql.append(" and obj.empresa.codZW = :empresa");
                params.put("empresa", empresa);
            }

            if (!SuperControle.independente(ctx)){
                sql.append(" and obj.professorTW = true");
            }

            sql.append(" and obj.codigo = :codigo");
            params.put("codigo", codigo);

            sql.append(" ORDER BY obj.nome ");
            return obterObjetoPorParam(ctx, sql.toString(), params);

        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

}
