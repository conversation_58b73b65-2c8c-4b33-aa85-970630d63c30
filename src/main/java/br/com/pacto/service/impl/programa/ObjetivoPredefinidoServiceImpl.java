/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */

package br.com.pacto.service.impl.programa;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.impl.EntityManagerService;
import br.com.pacto.bean.anamnese.Anamnese;
import br.com.pacto.bean.anamnese.AnamneseResponseTO;
import br.com.pacto.bean.programa.ObjetivoPredefinido;
import br.com.pacto.bean.programa.ObjetivoPredefinidoDTO;
import br.com.pacto.dao.intf.programa.ObjetivoPredefinidoDao;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.impl.notificacao.excecao.AnamneseExcecoes;
import br.com.pacto.service.impl.notificacao.excecao.AtividadeExcecoes;
import br.com.pacto.service.intf.Validacao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.exception.ValidacaoException;
import br.com.pacto.service.intf.programa.ObjetivoPredefinidoService;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;
import br.com.pacto.util.ViewUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.persistence.EntityExistsException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



/**
 *
 * <AUTHOR>
 */
@Service
public class ObjetivoPredefinidoServiceImpl implements ObjetivoPredefinidoService {

    @Autowired
    private ViewUtils viewUtils;
    @Autowired
    private SessaoService sessaoService;
    @Autowired
    private Validacao validacao;
    @Autowired
    private ObjetivoPredefinidoDao objetivoPredefinidoDao;

    public ViewUtils getViewUtils() {
        return viewUtils;
    }

    public void setViewUtils(ViewUtils viewUtils) {
        this.viewUtils = viewUtils;
    }

    public Validacao getValidacao() {
        return validacao;
    }

    public void setValidacao(Validacao validacao) {
        this.validacao = validacao;
    }

    public ObjetivoPredefinidoDao getObjetivoPredefinidoDao(){
        return this.objetivoPredefinidoDao;
    }

    public void setObjetivoPredefinidoDao(ObjetivoPredefinidoDao objetivoPredefinidoDao){
       this.objetivoPredefinidoDao = objetivoPredefinidoDao; 
    }


    public ObjetivoPredefinido alterar(final String ctx, ObjetivoPredefinido object) throws ServiceException, ValidacaoException {
        try {
            validarDados(object);
            if(getObjetivoPredefinidoDao().exists(ctx,object,"nome")){
                throw new ValidacaoException("registro_duplicado");
            }
            return getObjetivoPredefinidoDao().update(ctx, object);
        } catch (ValidacaoException ex) {
            throw ex;
        }catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public void excluir(final String ctx, ObjetivoPredefinido object) throws ServiceException {
        try {
            getObjetivoPredefinidoDao().delete(ctx, object);
        }catch (EntityExistsException eex) {
            throw new ServiceException("entidadePossuiRelacionamento");
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }
    public void validarDados(ObjetivoPredefinido object) throws ValidacaoException{
        if(object.getNome() == null || object.getNome().trim().isEmpty())
            throw new ValidacaoException("validacao.nome");
    }
    public ObjetivoPredefinido inserir(final String ctx, ObjetivoPredefinido object) throws ServiceException, ValidacaoException {
        try {
            validarDados(object);
            if(getObjetivoPredefinidoDao().exists(ctx,object,"nome")){
                throw new ValidacaoException("registro_duplicado");
            }
            return getObjetivoPredefinidoDao().insert(ctx, object);
        } catch (ValidacaoException vex) {
            throw vex;
        }catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public ObjetivoPredefinido obterObjetoPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException {
        try {
            return getObjetivoPredefinidoDao().findObjectByParam(ctx, query, params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public ObjetivoPredefinido obterPorId(final String ctx, Integer id) throws ServiceException {
        try {
            return getObjetivoPredefinidoDao().findById(ctx, id);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<ObjetivoPredefinido> obterPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException {
        try {
            return getObjetivoPredefinidoDao().findByParam(ctx, query, params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<ObjetivoPredefinido> obterPorParam(final String ctx, String query,
            Map<String, Object> params, int max, int index)
            throws ServiceException {
        try {
            return getObjetivoPredefinidoDao().findByParam(ctx, query, params, max, index);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<ObjetivoPredefinido> obterTodos(final String ctx) throws ServiceException {
        try {
            return getObjetivoPredefinidoDao().findListByAttributes(ctx, null, null, "nome", 0);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<ObjetivoPredefinidoDTO> consultar(String nome, PaginadorDTO paginadorDTO) throws ServiceException{
        String ctx = sessaoService.getUsuarioAtual().getChave();
        prepare(ctx);
        int maxResults = 50;
        int indiceInicial=0;
        if (paginadorDTO != null) {
            maxResults = paginadorDTO.getSize() == null ? 50 : paginadorDTO.getSize().intValue();
            indiceInicial = paginadorDTO.getPage() == null || paginadorDTO.getSize() == null ? indiceInicial : paginadorDTO.getPage().intValue() * paginadorDTO.getSize().intValue();
        }
        StringBuilder hql = new StringBuilder();
        StringBuilder where = new StringBuilder();
        List<ObjetivoPredefinidoDTO> listaRet = new ArrayList<ObjetivoPredefinidoDTO>();
        hql.append("SELECT obj FROM ObjetivoPredefinido obj ");

        if (!UteisValidacao.emptyString(nome)) {
            where.append("where upper(obj.nome) like '").append(nome.trim().toUpperCase()).append("%'");
        }

        StringBuilder orderBy = new StringBuilder();
        if ((paginadorDTO != null) && (paginadorDTO.getSortMap() != null) && (paginadorDTO.getSortMap().size() > 0)) {
            for (Map.Entry<String, String> entry : paginadorDTO.getSortMap().entrySet()){
                if (orderBy.length() >=1){
                    orderBy.append(",");
                }
                orderBy.append(entry.getKey().equals("nome") ? "nome" : entry.getKey()).append(" ").append(entry.getValue());
            }
        }
        if (where.length() > 0){
            hql.append(where.toString());
        }
        if (orderBy.length() > 0){
            hql.append(" order by ").append(orderBy.toString());
        }
        List<ObjetivoPredefinido> lista = null;
        try {
            if (paginadorDTO != null) {
                paginadorDTO.setQuantidadeTotalElementos(objetivoPredefinidoDao.countWithParam(ctx, "codigo", where, null).longValue());
            }
            lista = objetivoPredefinidoDao.findByParam(ctx, hql.toString(), new HashMap<String, Object>(),maxResults,indiceInicial);
        } catch (Exception e) {
            throw new ServiceException(AnamneseExcecoes.ERRO_BUSCAR_ANAMNESES, e);
        }
        if (lista != null) {
            for (ObjetivoPredefinido o : lista) {
                listaRet.add(new ObjetivoPredefinidoDTO(o));
            }
        }

        paginadorDTO.setSize((long) maxResults);
        paginadorDTO.setPage((long) indiceInicial);

        return listaRet;
    }

    public List<ObjetivoPredefinidoDTO> obterTodos() throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();

            List<ObjetivoPredefinido> lista =  getObjetivoPredefinidoDao().findAll(ctx);
            List<ObjetivoPredefinidoDTO> listaRet = new ArrayList<>();
            for(ObjetivoPredefinido obj  : lista){
                listaRet.add(new ObjetivoPredefinidoDTO(obj));
            }
            return listaRet;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    protected void prepare(final String ctx) throws ServiceException {
        EntityManagerService ems = (EntityManagerService) UtilContext.getBean(EntityManagerService.class);
        try {
            ems.closeQuietly(ctx);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    }