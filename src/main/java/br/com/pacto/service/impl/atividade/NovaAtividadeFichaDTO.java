package br.com.pacto.service.impl.atividade;

import java.util.List;

public class NovaAtividadeFichaDTO {
    private Integer codFicha;
    private Integer codMetodoExecucao;
    private String nomeMetodoExecucao;
    private Integer atividade;
    private Integer codigoAtividade;
    private Integer ordem;
    private String intensidade;
    private List<SerieDTO> series;

    public Integer getCodFicha() {
        return codFicha;
    }

    public void setCodFicha(Integer codFicha) {
        this.codFicha = codFicha;
    }

    public Integer getCodMetodoExecucao() {
        return codMetodoExecucao;
    }

    public void setCodMetodoExecucao(Integer codMetodoExecucao) {
        this.codMetodoExecucao = codMetodoExecucao;
    }

    public String getNomeMetodoExecucao() {
        return nomeMetodoExecucao;
    }

    public void setNomeMetodoExecucao(String nomeMetodoExecucao) {
        this.nomeMetodoExecucao = nomeMetodoExecucao;
    }

    public Integer getAtividade() {
        return atividade;
    }

    public void setAtividade(Integer atividade) {
        this.atividade = atividade;
    }

    public Integer getCodigoAtividade() {
        return codigoAtividade;
    }

    public void setCodigoAtividade(Integer codigoAtividade) {
        this.codigoAtividade = codigoAtividade;
    }

    public Integer getOrdem() {
        return ordem;
    }

    public void setOrdem(Integer ordem) {
        this.ordem = ordem;
    }

    public String getIntensidade() {
        return intensidade;
    }

    public void setIntensidade(String intensidade) {
        this.intensidade = intensidade;
    }

    public List<SerieDTO> getSeries() {
        return series;
    }

    public void setSeries(List<SerieDTO> series) {
        this.series = series;
    }
}
