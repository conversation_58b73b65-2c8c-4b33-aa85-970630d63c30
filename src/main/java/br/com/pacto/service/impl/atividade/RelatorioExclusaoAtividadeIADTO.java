package br.com.pacto.service.impl.atividade;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class RelatorioExclusaoAtividadeIADTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private int totalAtividadesAnalisadas;
    private int totalAtividadesExcluidas;
    private int totalAtividadesNaoExcluidas;
    private String mensagem;
    private List<AtividadeNaoExcluidaInfoDTO> atividadesNaoExcluidas;

    public RelatorioExclusaoAtividadeIADTO() {
        this.atividadesNaoExcluidas = new ArrayList<>();
    }

    public int getTotalAtividadesAnalisadas() {
        return totalAtividadesAnalisadas;
    }

    public void setTotalAtividadesAnalisadas(int totalAtividadesAnalisadas) {
        this.totalAtividadesAnalisadas = totalAtividadesAnalisadas;
    }

    public int getTotalAtividadesExcluidas() {
        return totalAtividadesExcluidas;
    }

    public void setTotalAtividadesExcluidas(int totalAtividadesExcluidas) {
        this.totalAtividadesExcluidas = totalAtividadesExcluidas;
    }

    public int getTotalAtividadesNaoExcluidas() {
        return totalAtividadesNaoExcluidas;
    }

    public void setTotalAtividadesNaoExcluidas(int totalAtividadesNaoExcluidas) {
        this.totalAtividadesNaoExcluidas = totalAtividadesNaoExcluidas;
    }

    public String getMensagem() {
        return mensagem;
    }

    public void setMensagem(String mensagem) {
        this.mensagem = mensagem;
    }

    public List<AtividadeNaoExcluidaInfoDTO> getAtividadesNaoExcluidas() {
        return atividadesNaoExcluidas;
    }

    public void setAtividadesNaoExcluidas(List<AtividadeNaoExcluidaInfoDTO> atividadesNaoExcluidas) {
        this.atividadesNaoExcluidas = atividadesNaoExcluidas;
    }
}