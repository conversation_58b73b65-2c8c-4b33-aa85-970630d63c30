package br.com.pacto.service.impl.processo;

import br.com.pacto.controller.json.aulaDia.AulaDiaJSONControle;
import br.com.pacto.dao.intf.cliente.ClienteSinteticoDao;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.impl.conexao.ConexaoZWServiceImpl;
import br.com.pacto.service.intf.processo.ProcessoSincronizarVinculosAlunoProfessorTW;
import br.com.pacto.service.intf.programa.ProgramaTreinoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.ui.ModelMap;

import java.sql.Connection;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

// chaves de busca: sincronizarvinculos atualizarvinculos verificarvinculos
@Service
public class ProcessoSincronizarVinculosAlunoProfessorTWImpl implements ProcessoSincronizarVinculosAlunoProfessorTW {

    @Autowired
    private ConexaoZWServiceImpl conexaoZWService;
    @Autowired
    private ClienteSinteticoDao clienteSinteticoDao;

    private static List<String> logs = new ArrayList<>();

    public void start(String ctx, int empresaZW, ModelMap mm, boolean vaiSincronizar) {
        try {
            logs = new ArrayList<>();

            try (Connection conBDTW = clienteSinteticoDao.getConnection(ctx)) {
                try (Connection conBDZW = conexaoZWService.conexaoZw(ctx)) {
                    ResultSet rsColaboradoresZW = ConexaoZWServiceImpl.criarConsulta(
                            "select c.codigo, p.nome " +
                                    "from colaborador c " +
                                    "inner join pessoa p on p.codigo = c.pessoa " +
                                    "and empresa = " + empresaZW,
                            conBDZW
                    );

                    while (rsColaboradoresZW.next()) {
                        if (isQtVinculosDiferentes(conBDZW, conBDTW, rsColaboradoresZW.getInt("codigo"), rsColaboradoresZW.getString("nome"), empresaZW)) {
                            atualizarVinculosProfessorBDTW(ctx, conBDZW, conBDTW, rsColaboradoresZW.getInt("codigo"), rsColaboradoresZW.getString("nome"), empresaZW, vaiSincronizar);
                        } else {
                            logs.add("Os vínculos do(a) professor(a) " + rsColaboradoresZW.getString("nome").toUpperCase() + " estão atualizados");
                        }
                    }
                }
            }
            mm.addAttribute("SUCESSO", "vinculos sincronizados com sucesso!");
            mm.addAttribute("logs", logs);
        } catch (Exception ex) {
            mm.addAttribute("ERRO", ex.getMessage());
            mm.addAttribute("logs", logs);
            Logger.getLogger(AulaDiaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    private static boolean isQtVinculosDiferentes(Connection conBDZW, Connection conBDTW, int colaboradorId, String nomeProfessor, int empresaZW) throws Exception {
        ResultSet rsVinculosProfessorZW = ConexaoZWServiceImpl.criarConsulta(
                "select count(*) as qtvinculosZW " +
                    "from vinculo v " +
                    "where v.tipovinculo = 'TW' and v.colaborador = " + colaboradorId,
                conBDZW
        );

        if (rsVinculosProfessorZW.next()) {
            int countZW = rsVinculosProfessorZW.getInt("qtvinculosZW");
            if (countZW > 0) {
                ResultSet rsVinculosProfessorTW = conBDTW.prepareStatement(
                        "select count(*) qtvinculosTW " +
                           "from clientesintetico c " +
                           "inner join professorsintetico p on p.codigo = c.professorsintetico_codigo " +
                           "where p.codigocolaborador = " + colaboradorId + " and c.empresa = " + empresaZW
                ).executeQuery();

                if (rsVinculosProfessorTW.next()) {
                    int countTw = rsVinculosProfessorTW.getInt("qtvinculosTW");
                    if (countZW != countTw) {
                        logs.add(".............................................................................................................");
                        logs.add("ATENÇÃO!!! Existem " + (countZW - countTw) + " alunos com vínculo desatualizado para o(a) professor(a): " + nomeProfessor.toUpperCase());
                    }
                    return countZW != countTw;
                }
            }
        }

        return false;
    }

    public void atualizarVinculosProfessorBDTW(String ctx, Connection conBDZW, Connection conBDTW, int colaboradorId, String nomeProfessor, int empresaZW, boolean vaiSincronizar) throws Exception {
        int codProfessorTW = obterCodProfessorBDTW(conBDTW, colaboradorId, empresaZW);
        if (codProfessorTW > 0) {
            ResultSet rsVinculosProfessorZW = ConexaoZWServiceImpl.criarConsulta(
                    "select c.codigomatricula, c.codigo, p.nome " +
                        "from vinculo v " +
                        "inner join cliente c on c.codigo = v.cliente " +
                        "inner join pessoa p on p.codigo = c.pessoa " +
                        "where v.tipovinculo = 'TW' and v.colaborador = " + colaboradorId,
                    conBDZW
            );

            int count = 0;
            while (rsVinculosProfessorZW.next()) {
                ResultSet aluno = conBDTW.prepareStatement(
                        "select codigo, matricula, nome, professorsintetico_codigo " +
                           "from clientesintetico " +
                           "where matricula = " + rsVinculosProfessorZW.getInt("codigomatricula")
                ).executeQuery();

                if (aluno.next()) {
                    if (vaiSincronizar && aluno.getInt("professorsintetico_codigo") != codProfessorTW) {
                        try {
                            clienteSinteticoDao.executeNativeSQL(
                                    ctx,
                                    "update clientesintetico " +
                                         "set professorsintetico_codigo = " + codProfessorTW + " " +
                                         "where matricula = " + rsVinculosProfessorZW.getInt("codigomatricula")
                            );
                            count++;
                        } catch (Exception e){
                            Uteis.logar(e, ProgramaTreinoService.class);
                        }
                    }
                } else {
                    logs.add("..... Este aluno não foi localizado no treino: " + rsVinculosProfessorZW.getString("nome").toUpperCase() + " MAT: " + rsVinculosProfessorZW.getInt("codigomatricula"));
                }
            }
            logs.add("..... SUCESSO: Foram atualizados " + count + " vinculos do(a) professor(a) " + nomeProfessor.toUpperCase() + " no banco do treino!");
        } else {
            logs.add("--> TEMOS UM PROBLEMINHA HEHE: Não foi localizado o código do(a) professor(a)" + nomeProfessor.toUpperCase() + " no banco do treino, a atualização não pode prosseguir!");
        }
        logs.add(".............................................................................................................");
    }

    public static int obterCodProfessorBDTW(Connection conBDTW, int colaboradorId, int empresaZW) throws Exception {
        ResultSet rsProfessorTW = conBDTW.prepareStatement(
                "select p.codigo " +
                   "from professorsintetico p " +
                   "inner join empresa e on e.codigo = p.empresa_codigo " +
                   "where p.codigocolaborador = " + colaboradorId + " and e.codzw = " + empresaZW
        ).executeQuery();

        if (rsProfessorTW.next()) {
            return rsProfessorTW.getInt("codigo");
        }

        return 0;
    }

}
