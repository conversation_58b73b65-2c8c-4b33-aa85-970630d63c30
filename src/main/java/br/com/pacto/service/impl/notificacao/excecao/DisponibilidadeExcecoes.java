package br.com.pacto.service.impl.notificacao.excecao;

import br.com.pacto.service.exception.ExcecaoSistema;

/**
 * Created by paulo 30/08/2019
 */
public enum DisponibilidadeExcecoes implements ExcecaoSistema {
    ERRO_LISTAR_CONFIG_DISPONIBILIDADE("erro_listar_config_disponibilidade", "Erro ao carregar as disponibilidades geradora"),
    ERRO_SALVAR_CONFIG_DISPONIBILIDADE("erro_salvar_config_disponibilidade", "Erro ao salvar a disponibilidade geradora"),
    ERRO_SALVAR_CONFIG_DISPONIBILIDADE_HORARIO("erro_salvar_config_disponibilidade", "Erro ao salvar a disponibilidade horário menor que o possível"),
    ERRO_REMOVER_DISPONIBILIDADE_CONFIG("erro_remover_disponibilidade_config", "Erro ao remover a disponibilidade config"),
    ERRO_GERAR_CONFIG_DISPONIBILIDADE("", "Erro ao gerar config disponibilidade");

    private String chave;
    private String descricao;

    DisponibilidadeExcecoes(String chave, String descricao) {
        this.chave = chave;
        this.descricao = descricao;
    }

    @Override
    public String getChaveExcecao() {
        return chave;
    }

    @Override
    public String getDescricaoExcecao() {
        return descricao;
    }
}
