package br.com.pacto.service.impl.avaliacao;

import br.com.pacto.objeto.Uteis;
import com.drew.imaging.ImageMetadataReader;
import com.drew.imaging.ImageProcessingException;
import com.drew.metadata.Directory;
import com.drew.metadata.Metadata;
import com.drew.metadata.MetadataException;
import com.drew.metadata.exif.ExifIFD0Directory;
import com.drew.metadata.jpeg.JpegDirectory;
import org.apache.commons.io.FileUtils;

import javax.imageio.IIOImage;
import javax.imageio.ImageIO;
import javax.imageio.ImageWriteParam;
import javax.imageio.ImageWriter;
import javax.imageio.stream.ImageInputStream;
import javax.servlet.ServletContext;
import java.awt.*;
import java.awt.geom.AffineTransform;
import java.awt.image.AffineTransformOp;
import java.awt.image.BufferedImage;
import java.io.*;
import java.util.Iterator;

public class ResizeImage {

    // Inner class containing image information
    public static class ImageInformation {
        public final int orientation;
        public final int width;
        public final int height;

        public ImageInformation(int orientation, int width, int height) {
            this.orientation = orientation;
            this.width = width;
            this.height = height;
        }

        public String toString() {
            return String.format("%dx%d,%d", this.width, this.height, this.orientation);
        }
    }

    public static ImageInformation readImageInformation(File file)  throws IOException, MetadataException, ImageProcessingException {
        try {
            Metadata metadata = ImageMetadataReader.readMetadata(file);
            Directory directory = metadata.getFirstDirectoryOfType(ExifIFD0Directory.class);
            JpegDirectory jpegDirectory = metadata.getFirstDirectoryOfType(JpegDirectory.class);

            int orientation = 1;
            try {
                orientation = directory.getInt(ExifIFD0Directory.TAG_ORIENTATION);
            } catch (MetadataException me) {
            }
            int width = jpegDirectory.getImageWidth();
            int height = jpegDirectory.getImageHeight();

            return new ImageInformation(orientation, width, height);
        } catch (Exception ex) {
            Uteis.logar(ex, ResizeImage.class);
            return null;
        }
    }

    public static ImageInformation readImageInformationOfByte(ByteArrayInputStream entrada, int width, int height)  throws IOException, MetadataException, ImageProcessingException {
        try {
            Metadata metadata = ImageMetadataReader.readMetadata(entrada);
            Directory directory = metadata.getFirstDirectoryOfType(ExifIFD0Directory.class);

            int orientation = 1;
            try {
                orientation = directory.getInt(ExifIFD0Directory.TAG_ORIENTATION);
            } catch (MetadataException me) { }

            return new ImageInformation(orientation, width, height);
        } catch (Exception ex) {
            Uteis.logar(ex, ResizeImage.class);
            return null;
        }
    }

    public byte[] scale(String nome, ServletContext servletContext, byte[] fileData, int width, int height) {
        try {
            try(InputStream in = new ByteArrayInputStream(fileData)) {
                File file = salvarArquivo(nome, fileData, servletContext);
                ImageInformation imageInformation = readImageInformation(file);
                BufferedImage img = ImageIO.read(in);
                if(height == 0) {
                    height = (width * img.getHeight())/ img.getWidth();
                }
                if(width == 0) {
                    width = (height * img.getWidth())/ img.getHeight();
                }
                Image scaledImage = img.getScaledInstance(width, height, Image.SCALE_SMOOTH);
                BufferedImage imageBuff = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
                imageBuff.getGraphics().drawImage(scaledImage, 0, 0, new Color(0,0,0), null);
                if (imageInformation != null) {
                    imageBuff = transformImage(imageBuff, getExifTransformation(imageInformation, width, height));
                }
                try(ByteArrayOutputStream buffer = new ByteArrayOutputStream()) {
                    ImageIO.write(imageBuff, "jpg", buffer);
                    file.delete();
                    return buffer.toByteArray();
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return fileData;
    }

    public static AffineTransform getExifTransformation(ImageInformation info, int w, int h) {

        AffineTransform t = new AffineTransform();

        switch (info.orientation) {
            case 1:
                break;
            case 2: // Flip X
                t.scale(-1.0, 1.0);
                t.translate(-w, 0);
                break;
            case 3: // PI rotation
                t.translate(w, h);
                t.rotate(Math.PI);
                break;
            case 4: // Flip Y
                t.scale(1.0, -1.0);
                t.translate(0, -h);
                break;
            case 5: // - PI/2 and Flip X
                t.rotate(-Math.PI / 2);
                t.scale(-1.0, 1.0);
                break;
            case 6: // -PI/2 and -width
                t.translate(h, 0);
                t.rotate(Math.PI / 2);
                break;
            case 7: // PI/2 and Flip
                t.scale(-1.0, 1.0);
                t.translate(-h, 0);
                t.translate(0, w);
                t.rotate(  3 * Math.PI / 2);
                break;
            case 8: // PI / 2
                t.translate(0, w);
                t.rotate(  3 * Math.PI / 2);
                break;
        }

        return t;
    }

    public File salvarArquivo(final String nome,
                              final byte[] data,
                              ServletContext servletContext) throws Exception {
        String nomeRelPDF = "temp" + File.separator + "postural";
        File pdfFolder = new File(obterCaminhoWebAplicacao(servletContext) + File.separator + nomeRelPDF);
        pdfFolder.mkdirs();
        nomeRelPDF = nomeRelPDF + File.separator + nome;
        File imgFile = new File(obterCaminhoWebAplicacao(servletContext) + File.separator + nomeRelPDF);
        if(imgFile.exists()){
            imgFile.delete();
        }
        FileUtils.writeByteArrayToFile(imgFile, data);
        return imgFile;
    }

    public String salvarArquivoURL(final String nome,
                                   final byte[] data,
                                   ServletContext servletContext) throws Exception {
        salvarArquivo(nome, data, servletContext);
        return obterCaminhoWebAplicacao(servletContext)  + File.separator +
                "temp" + File.separator + "postural" + File.separator + nome;
    }

    public String obterCaminhoWebAplicacao(ServletContext servletContext) throws Exception {
        return new File(servletContext.getRealPath("")).getAbsolutePath();
    }

    public static BufferedImage transformImage(BufferedImage image, AffineTransform transform) throws Exception {
        AffineTransformOp op = new AffineTransformOp(transform, AffineTransformOp.TYPE_BICUBIC);
        BufferedImage destinationImage = op.createCompatibleDestImage(image, image.getColorModel());
        Graphics2D g = destinationImage.createGraphics();
        g.setBackground(Color.WHITE);
        g.clearRect(0, 0, destinationImage.getWidth(), destinationImage.getHeight());
        destinationImage = op.filter(image, destinationImage);
        return destinationImage;
    }

    public static boolean isReduzirImagem(byte[] imagem, Integer limite) {
        try {
            ByteArrayInputStream entrada = new ByteArrayInputStream(imagem);
            BufferedImage image = ImageIO.read(entrada);

            int width = image.getWidth();
            int height = image.getHeight();

            if (width < limite || height < limite) {
                return false;
            }
            return true;
        } catch (Exception ex) {
            Uteis.logar(ex, ResizeImage.class);
            return false;
        }
    }

    public static byte[] reduzirImagem(byte[] imagem, int reduzaoPorcentagem) throws Exception {
        try {
            ByteArrayInputStream entrada = new ByteArrayInputStream(imagem);
            BufferedImage image = ImageIO.read(entrada);

            int width = image.getWidth();
            int height = image.getHeight();

            if (width < 1000 || height < 1000) {
                // retornar a imagem original sem redimensionar
                return imagem;
            }

            int newWidth = width * reduzaoPorcentagem / 100;
            int newHeight = height * reduzaoPorcentagem / 100;

            BufferedImage novaImagem = new BufferedImage(newWidth, newHeight, BufferedImage.TYPE_INT_RGB);

            novaImagem.createGraphics().drawImage(image.getScaledInstance(newWidth, newHeight, java.awt.Image.SCALE_SMOOTH), 0, 0, null);
            ImageInformation imageInformation = readImageInformationOfByte(new ByteArrayInputStream(imagem), width, height);
            if (imageInformation != null) {
                novaImagem = transformImage(novaImagem, getExifTransformation(imageInformation, newWidth, newHeight));
            }

            ByteArrayOutputStream buffer = new ByteArrayOutputStream();
            ImageIO.write(novaImagem, "jpg", buffer);
            return buffer.toByteArray();
        } catch (Exception ex) {
            Uteis.logar(ex, ResizeImage.class);
            return imagem;
        }

    }

    public static byte[] comprimirImagem(byte[] imagem, float qualidade) throws Exception {
        try {
            ByteArrayInputStream entrada = new ByteArrayInputStream(imagem);
            ImageInputStream entradaImagem = ImageIO.createImageInputStream(entrada);
            String format = ImageIO.getImageReaders(entradaImagem).next().getFormatName();

            BufferedImage image = ImageIO.read(entradaImagem);

            Iterator<ImageWriter> writers = ImageIO.getImageWritersByFormatName(format);
            ImageWriter writer = writers.next();

            ImageWriteParam param = writer.getDefaultWriteParam();
            param.setCompressionMode(ImageWriteParam.MODE_EXPLICIT);
            param.setCompressionQuality(qualidade);

            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            writer.setOutput(ImageIO.createImageOutputStream(byteArrayOutputStream));
            IIOImage novaImagem = new IIOImage(image, null, null);
            writer.write(null, novaImagem, param);
            writer.dispose();

            return byteArrayOutputStream.toByteArray();
        } catch (Exception ex) {
            Uteis.logar(ex, ResizeImage.class);
            return imagem;
        }
    }

}
