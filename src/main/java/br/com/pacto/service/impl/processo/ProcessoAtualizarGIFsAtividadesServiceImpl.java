package br.com.pacto.service.impl.processo;

import br.com.pacto.base.util.ExecuteRequestHttpService;
import br.com.pacto.bean.atividade.Atividade;
import br.com.pacto.bean.atividade.AtividadeImagemUploadTO;
import br.com.pacto.service.intf.atividade.AtividadeService;
import br.com.pacto.service.intf.processo.ProcessoAtualizarGIFsAtividadesService;
import br.com.pacto.util.UteisValidacao;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.io.StringReader;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
public class ProcessoAtualizarGIFsAtividadesServiceImpl implements ProcessoAtualizarGIFsAtividadesService {

    @Autowired
    AtividadeService atividadeService;

    private static String extractFileId(String driveLink) {
        String fileId = null;
        Pattern pattern = Pattern.compile("/file/d/(.*?)/");
        Matcher matcher = pattern.matcher(driveLink);

        if (matcher.find()) {
            fileId = matcher.group(1);
        }

        return fileId;
    }

    private static byte[] downloadFileFromDrive(String fileId) {
        try {
            HttpClient httpClient = ExecuteRequestHttpService.createConnector();
            HttpGet httpGet = new HttpGet("https://drive.google.com/uc?export=download&id=" + fileId);

            HttpResponse response = httpClient.execute(httpGet);

            return EntityUtils.toByteArray(response.getEntity());
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }

    public static boolean isInteger(String s) {
        try {
            Integer.parseInt(s);
        } catch (Exception e) {
            return false;
        }
        return true;
    }

    @Override
    public void start(String ctx, String path) {
        Map<Integer, String> codigoGifMap = new HashMap<>();
        Map<String, String> nomeGifMap = new HashMap<>();

        Integer linhaAtual = 1;
        try (BufferedReader br = new BufferedReader(new StringReader(path))) {
            String line;
            br.readLine();

            while ((line = br.readLine()) != null) {
                String[] values = line.split(",");

                if (linhaAtual >= 531 && (!(values.length == 0) && !values[0].isEmpty() && isInteger(values[0].trim()) && values.length >= 4)) {
                    Integer codigo = Integer.parseInt(values[0].trim());
                    String linkGif = values[4].trim();
                    codigoGifMap.put(codigo, linkGif);
                } else {
                    if (!(values.length == 0) && values.length >= 9 && !values[8].isEmpty() && isInteger(values[8].trim())) {
                        String linkGif = values[4].trim();
                        Integer codigo = Integer.parseInt(values[8].trim());
                        codigoGifMap.put(codigo, linkGif);
                    } else if (!(values.length == 0) && values.length >= 4 && !values[1].isEmpty()) {
                        String linkGif = values[4].trim();
                        String descricao = values[1];
                        nomeGifMap.put(descricao, linkGif);
                    }
                }
                linhaAtual++;
            }
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }

        baixaGIFsPorCodigo(ctx, codigoGifMap);

        baixaGIFsPorDescricao(ctx, nomeGifMap);
        System.out.println("Concluido!");
    }

    private void baixaGIFsPorCodigo(String ctx, Map<Integer, String> codigoGifMap) {
        for (Map.Entry<Integer, String> entry : codigoGifMap.entrySet()) {
            int codigo = entry.getKey();
            String linkGif = entry.getValue();

            if (!UteisValidacao.emptyString(linkGif) && linkGif.contains("drive")) {
                try {
                    Atividade atividade = atividadeService.obterPorId(ctx, codigo);

                    if (atividade != null) {
                        String fileId = extractFileId(linkGif);
                        byte[] fileContent = downloadFileFromDrive(fileId);

                        String contentString = new String(fileContent, StandardCharsets.UTF_8);
                        if (contentString.contains("Sign in")) {
                            System.out.println("N�o � poss�vel ter acesso ao arquivo: " + codigo);
                        }


                        List<AtividadeImagemUploadTO> atividadeImagemUploadTOList = new ArrayList<>();
                        AtividadeImagemUploadTO atividadeImagemUploadTO = new AtividadeImagemUploadTO();
                        atividadeImagemUploadTO.setData(fileContent);
                        atividadeImagemUploadTO.setNome("imagem-drive-" + codigo);
                        atividadeImagemUploadTOList.add(atividadeImagemUploadTO);

                        atividadeService.salvarMidiaNuvemEndpoint(ctx, atividadeImagemUploadTOList, atividade);
                    }
                    System.out.println(codigo + " - " + linkGif);

                } catch (Exception e) {
                    System.out.println(e.getMessage());
                }
            }
        }
    }

    private void baixaGIFsPorDescricao(String ctx, Map<String, String> nomeGifMap) {
        for (Map.Entry<String, String> entry : nomeGifMap.entrySet()) {
            String descricao = entry.getKey();
            String linkGif = entry.getValue();

            if (!UteisValidacao.emptyString(linkGif) && linkGif.contains("drive")) {
                try {
                    StringBuilder sql = new StringBuilder();
                    sql.append("SELECT obj FROM Atividade obj ");
                    sql.append(" WHERE nome = '").append(descricao).append("'");

                    HashMap<String, Object> param = new HashMap<>();


                    List<Atividade> atividades = atividadeService.obterPorParam(ctx, sql.toString(), param);

                    if (atividades != null && atividades.size() == 1) {
                        Atividade atividade = atividades.get(0);
                        String fileId = extractFileId(linkGif);
                        byte[] fileContent = downloadFileFromDrive(fileId);

                        String contentString = new String(fileContent, StandardCharsets.UTF_8);
                        if (contentString.contains("Sign in")) {
                            System.out.println("N�o � poss�vel ter acesso ao arquivo: " + descricao);
                        }

                        List<AtividadeImagemUploadTO> atividadeImagemUploadTOList = new ArrayList<>();
                        AtividadeImagemUploadTO atividadeImagemUploadTO = new AtividadeImagemUploadTO();
                        atividadeImagemUploadTO.setData(fileContent);
                        atividadeImagemUploadTO.setNome("imagem-drive-" + descricao);
                        atividadeImagemUploadTOList.add(atividadeImagemUploadTO);

                        atividadeService.salvarMidiaNuvemEndpoint(ctx, atividadeImagemUploadTOList, atividade);
                    }
                    System.out.println(descricao + " - " + linkGif);
                } catch (Exception e) {
                    System.out.println(e.getMessage());
                }
            }
        }
    }
}
