package br.com.pacto.service.impl.notificacao.excecao;

import br.com.pacto.service.exception.ExcecaoSistema;

public enum ProfessorSubstituidoExcecoes implements ExcecaoSistema {

    ERRO_CONSULTAR_PROFESSORES_SUBSTITUIDOS("erro_consultar_professores_substituidos", "Erro ao consultar os professores substituidos");

    private String chave;
    private String descricao;

    ProfessorSubstituidoExcecoes(String chave, String descricao) {
        this.chave = chave;
        this.descricao = descricao;
    }

    @Override
    public String getChaveExcecao() {
        return chave;
    }
    @Override
    public String getDescricaoExcecao() {
        return descricao;
    }
}
