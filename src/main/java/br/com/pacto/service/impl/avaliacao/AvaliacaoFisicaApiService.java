package br.com.pacto.service.impl.avaliacao;

import br.com.pacto.bean.avaliacao.AvaliacaoFisica;
import br.com.pacto.dao.intf.avaliacao.AvaliacaoFisicaDao;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.util.UteisValidacao;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Service
public class AvaliacaoFisicaApiService {
    @Autowired
    private AvaliacaoFisicaDao avaliacaoFisicaDao;
    private final ExecutorService executorService = Executors.newFixedThreadPool(10);

    public void salvaAvaliacaoFisicaApiExternaAsync(String ctx, Integer codEmpresa, Integer codUsuario, AvaliacaoFisica avaliacaoFisica) {
        executorService.submit(() -> salvaAvaliacaoFisicaApiExterna(ctx, codEmpresa, codUsuario, avaliacaoFisica));
    }

    public void deletaAvaliacaoFisicaApiExternaAsync(AvaliacaoFisica avaliacaoFisica) {
        executorService.submit(() -> deletaAvaliacaoFisicaApiExterna(avaliacaoFisica));
    }

    public void salvaAvaliacaoFisicaApiExterna(String ctx, Integer codEmpresa, Integer codUsuario, AvaliacaoFisica avaliacaoFisica) {
        try {
            ObjectMapper mapper = new ObjectMapper();
            String dadoX = mapper.writeValueAsString(avaliacaoFisica);

            JSONObject json = new JSONObject();
            json.put("chaveZw", ctx);
            json.put("codEmpresa", codEmpresa);
            json.put("codUsuario", codUsuario);
            json.put("dadoX", dadoX);
            boolean avaliacaoJaExisteNaAPIExterna = !UteisValidacao.emptyString(avaliacaoFisica.getIdAvaliacaoFisicaApi());
            if (avaliacaoJaExisteNaAPIExterna) {
                json.put("idFirebase", avaliacaoFisica.getIdAvaliacaoFisicaApi());
            }

            HttpURLConnection httpUrlConnection = (HttpURLConnection) new URL(
                    Aplicacao.getProp(Aplicacao.urlAvaliacaoFisicaApi) + "/inserirAvaliacaoFisica").openConnection();
            httpUrlConnection.setRequestMethod("POST");
            httpUrlConnection.setDoOutput(true);
            httpUrlConnection.setRequestProperty("Content-Type", "application/json");
            httpUrlConnection.setRequestProperty("Accept", "application/json");

            OutputStreamWriter wr = new OutputStreamWriter(httpUrlConnection.getOutputStream(), StandardCharsets.UTF_8);
            wr.write(json.toString());
            wr.flush();

            int responseCode = httpUrlConnection.getResponseCode();
            System.out.println("Response code: " + responseCode);

            InputStreamReader inputStreamReader = new InputStreamReader(httpUrlConnection.getInputStream());
            String retorno = new BufferedReader(inputStreamReader).readLine();
            System.out.println(retorno);
            JSONObject respostaJson = new JSONObject(retorno);
            JSONObject sucessoObject = respostaJson.getJSONObject("sucesso");

            String id = sucessoObject.optString("ref");
            avaliacaoFisica.setIdAvaliacaoFisicaApi(id);
            avaliacaoFisicaDao.update(ctx, avaliacaoFisica);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void deletaAvaliacaoFisicaApiExterna(AvaliacaoFisica avaliacaoFisica) {

        try {
            if (UteisValidacao.emptyString(avaliacaoFisica.getIdAvaliacaoFisicaApi())) {
                return;
            }

            HttpURLConnection httpUrlConnection = (HttpURLConnection) new URL(
                    Aplicacao.getProp(Aplicacao.urlAvaliacaoFisicaApi) + "/deletarAvaliacaoFisica?idFirebase=" + avaliacaoFisica.getIdAvaliacaoFisicaApi()).openConnection();
            httpUrlConnection.setRequestMethod("DELETE");

            int responseCode = httpUrlConnection.getResponseCode();
            System.out.println("Response code: " + responseCode);

            InputStreamReader inputStreamReader = new InputStreamReader(httpUrlConnection.getInputStream());
            String retorno = new BufferedReader(inputStreamReader).readLine();
            System.out.println(retorno);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
