package br.com.pacto.service.impl.locacao;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.aula.AgendamentoLocacao;
import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.locacao.*;
import br.com.pacto.controller.json.agendamento.AlunoLocacaoPlayDTO;
import br.com.pacto.controller.json.aluno.SituacaoAlunoEnum;
import br.com.pacto.controller.json.locacao.AgendamentoLocacaoDTO;
import br.com.pacto.dao.intf.locacao.AgendamentoLocacaoDao;
import br.com.pacto.dao.intf.locacao.AlunoLocacaoHorarioPlayCheckinCheckoutDao;
import br.com.pacto.dao.intf.locacao.AlunoLocacaoHorarioPlayDao;
import br.com.pacto.dao.intf.locacao.LocacaoHorarioDao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.conexao.ConexaoZWServiceImpl;
import br.com.pacto.service.intf.cliente.ClienteSinteticoService;
import br.com.pacto.service.intf.conexao.ConexaoZWService;
import br.com.pacto.service.intf.locacao.AlunoLocacaoHorarioPlayService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.test.context.ContextConfiguration;

import javax.servlet.http.HttpServletRequest;
import java.sql.Connection;
import java.sql.ResultSet;
import java.util.*;

@Service
@ContextConfiguration(locations = {"/applicationContext.xml"})
public class AlunoLocacaoHorarioPlayServiceImpl implements AlunoLocacaoHorarioPlayService {

    private static final Integer LIVRE = 0;
    private static final Integer PLANO = 1;
    private static final Integer PRODUTO = 2;

    private final AlunoLocacaoHorarioPlayDao alunoLocacaoHorarioPlayDao;
    private final SessaoService sessaoService;
    private final LocacaoHorarioDao locacaoHorarioDao;
    private final ConexaoZWService conexaoZWService;
    private final ClienteSinteticoService clienteSinteticoService;
    private final AlunoLocacaoHorarioPlayCheckinCheckoutDao alunoLocacaoHorarioPlayCheckinCheckoutDao;
    private final AgendamentoLocacaoDao agendamentoLocacaoDao;

    @Autowired
    public AlunoLocacaoHorarioPlayServiceImpl(AlunoLocacaoHorarioPlayDao alunoLocacaoHorarioPlayDao, SessaoService sessaoService, LocacaoHorarioDao locacaoHorarioDao, ConexaoZWService conexaoZWService, ClienteSinteticoService clienteSinteticoService, AlunoLocacaoHorarioPlayCheckinCheckoutDao alunoLocacaoHorarioPlayCheckinCheckoutDao, AgendamentoLocacaoDao agendamentoLocacaoDao) {
        this.alunoLocacaoHorarioPlayDao = alunoLocacaoHorarioPlayDao;
        this.sessaoService = sessaoService;
        this.locacaoHorarioDao = locacaoHorarioDao;
        this.conexaoZWService = conexaoZWService;
        this.clienteSinteticoService = clienteSinteticoService;
        this.alunoLocacaoHorarioPlayCheckinCheckoutDao = alunoLocacaoHorarioPlayCheckinCheckoutDao;
        this.agendamentoLocacaoDao = agendamentoLocacaoDao;
    }

    @Override
    public Map<String, Object> addAlunoHorarioPlay(AlunoLocacaoHorarioPlayDTO alunoLocacaoHorarioPlayDTO, String ctx) throws ServiceException {
        try {
            AlunoLocacaoHorarioPlay alunoLocacaoHorarioPlay = new AlunoLocacaoHorarioPlay(alunoLocacaoHorarioPlayDTO);
            validarItensIngressarAula(ctx, alunoLocacaoHorarioPlay);
            alunoLocacaoHorarioPlayDao.insert(ctx, alunoLocacaoHorarioPlay);
            alunoLocacaoHorarioPlayDTO.setCodigo(alunoLocacaoHorarioPlay.getCodigo());

            LocacaoHorario locacaoHorario = locacaoHorarioDao.findById(ctx, alunoLocacaoHorarioPlayDTO.getLocacaoHorario());

            String query = "SELECT al FROM AgendamentoLocacao al\n" +
                    "WHERE al.locacaoHorario = :locacaohorario\n" +
                    "ORDER BY al.codigo DESC LIMIT 1";
            Map<String, Object> params = new HashMap<>();
            params.put("locacaohorario", locacaoHorario);

            AgendamentoLocacao agendamentoLocacao = agendamentoLocacaoDao.findObjectByParam(ctx, query, params);

            if (agendamentoLocacao == null) {
                ClienteSintetico cliente = clienteSinteticoService.consultarSimplesPorCodigoCliente(ctx, alunoLocacaoHorarioPlayDTO.getCliente());

                AgendamentoLocacao agendamento = new AgendamentoLocacao(new AgendamentoLocacaoDTO(), cliente, locacaoHorario);
                agendamento.setUsuarioZW(sessaoService.getUsuarioAtual().getId());
                agendamento.setUsuarioResponsavelZW(alunoLocacaoHorarioPlayDTO.getResponsavelLancamento());
                agendamento.setCancelado(false);
                agendamento.setLocacaoHorario(locacaoHorario);
                agendamento.setAmbiente(alunoLocacaoHorarioPlayDTO.getAmbiente());
                agendamento.setInicio(Calendario.getDataComHora(alunoLocacaoHorarioPlay.getDia(), locacaoHorario.getHoraInicio()));
                agendamento.setFim(Calendario.getDataComHora(alunoLocacaoHorarioPlay.getDia(), locacaoHorario.getHoraFim()));

                agendamentoLocacao = agendamentoLocacaoDao.insert(ctx, agendamento);
            }

            Map<String, Object> response = new HashMap<>();
            response.put("alunoLocacaoHorarioPlay", alunoLocacaoHorarioPlayDTO);
            response.put("agendamentoLocacaoCodigo", agendamentoLocacao.getCodigo());
            return response;
        } catch (Exception exception) {
            throw new ServiceException(exception.getMessage());
        }
    }

    private void validarItensIngressarAula(String chave, AlunoLocacaoHorarioPlay alunoLocacaoHorarioPlay) throws Exception {
        LocacaoHorario lh = locacaoHorarioDao.findById(chave, alunoLocacaoHorarioPlay.getLocacaoHorario().getCodigo());
        ClienteSintetico cli = clienteSinteticoService.obterPorId(chave, alunoLocacaoHorarioPlay.getCliente().getCodigo());
        try (Connection conZW = conexaoZWService.conexaoZw(chave)) {
            if (Objects.equals(lh.getLocacao().getTipoValidacao(), PLANO)) {
                boolean possuiPlano = lh.getLocacao().getItensValidacao().stream().anyMatch(iv -> {
                    String query = String.format(
                            "SELECT EXISTS (SELECT 1 FROM contrato c " +
                                    "WHERE current_date BETWEEN c.vigenciade AND c.vigenciaateajustada " +
                                    "AND c.situacao = 'AT' AND c.pessoa = %d AND c.plano = %d " +
                                    "AND c.empresa = %d)",
                            cli.getCodigoPessoa(), iv.getPlano(), alunoLocacaoHorarioPlay.getEmpresa().getCodigo()
                    );

                    try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(query, conZW)) {
                        return rs.next() && rs.getBoolean(1);
                    } catch (Exception e) {
                        e.printStackTrace();
                        return false;
                    }
                });

                if(!possuiPlano) {
                    throw new ServiceException("errorValidacaoPlano");
                }
            } else if (Objects.equals(lh.getLocacao().getTipoValidacao(), PRODUTO)) {
                boolean possuiProduto = lh.getLocacao().getItensValidacao().stream().anyMatch(iv -> {
                    String query = String.format(
                            "SELECT EXISTS (SELECT 1 FROM movproduto mp WHERE mp.pessoa = %d AND mp.produto = %d AND mp.empresa = %d)",
                            cli.getCodigoPessoa(), iv.getProduto(), alunoLocacaoHorarioPlay.getEmpresa().getCodigo()
                    );

                    try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(query, conZW)) {
                        return rs.next() && rs.getBoolean(1);
                    } catch (Exception e) {
                        e.printStackTrace();
                        return false;
                    }
                });

                if (!possuiProduto) {
                    throw new ServiceException("errorValidacaoProduto");
                }
            }
        }
    }

    @Override
    public List<AlunoLocacaoPlayDTO> alunosLocacaoPlay(Integer empresaId, Date dia, Integer locacaoHorario, Integer ambiente, PaginadorDTO paginadorDTO, HttpServletRequest request) throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        alunoLocacaoHorarioPlayDao.getCurrentSession(ctx).clear();
        try {
            List<AlunoLocacaoPlayDTO> alunosDTO = new ArrayList<>();
            String query = "SELECT obj FROM AlunoLocacaoHorarioPlay obj "
                    + "WHERE obj.empresa.codigo = :empresa AND obj.dia = :dia AND obj.locacaoHorario.codigo = :locacaoHorario" +
                    " AND obj.ambiente = :ambiente ORDER BY obj.cliente.nome ASC ";
            Map<String, Object> params = new HashMap<>();
            params.put("empresa", empresaId);
            params.put("dia", dia);
            params.put("locacaoHorario", locacaoHorario);
            params.put("ambiente", ambiente);
            int max = Math.toIntExact(paginadorDTO.getSize());
            int page = Math.toIntExact(paginadorDTO.getPage());
            List<AlunoLocacaoHorarioPlay> alunos = alunoLocacaoHorarioPlayDao.findByParam(ctx, query, params, max, page);

            if (alunos != null && !alunos.isEmpty()) {
                alunos.forEach(a -> {
                    AlunoLocacaoPlayDTO aluno = new AlunoLocacaoPlayDTO();
                    aluno.setId(a.getCodigo());
                    aluno.setNome(a.getCliente().getNome());
                    aluno.setMatriculaZW(a.getCliente().getMatricula());
                    aluno.setUnidade(a.getEmpresa().getNome());
                    aluno.setImageUri(Uteis.getPaintFotoDaNuvem(a.getCliente().getPessoa().getFotoKey()));
                    aluno.setSituacaoAluno(SituacaoAlunoEnum.getInstance(a.getCliente().getSituacao()));
                    aluno.setCheckin(a.getCheckin());
                    alunosDTO.add(aluno);
                });
            }

            paginadorDTO.setQuantidadeTotalElementos(new Long(alunosDTO.size()));
            return alunosDTO;
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    public String deleteAlunoLocacaoHorarioPlay(Integer alunoLocacaoHorarioPlayId, String ctx) throws ServiceException {
        try {
            alunoLocacaoHorarioPlayCheckinCheckoutDao.deleteComParam(ctx, new String[]{"alunoLocacaoHorarioPlay.codigo"}, new Object[]{alunoLocacaoHorarioPlayId});
            alunoLocacaoHorarioPlayDao.delete(ctx, alunoLocacaoHorarioPlayId);
            return "removidoId";
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    @Override
    public String addAlunoHorarioPlayCheckinCheckout(AlunoLocacaoHorarioPlayCheckinCheckoutDTO dto, String ctx) throws ServiceException {
        alunoLocacaoHorarioPlayDao.getCurrentSession(ctx).clear();
        alunoLocacaoHorarioPlayCheckinCheckoutDao.getCurrentSession(ctx).clear();
        try {
            String query = "SELECT obj FROM AlunoLocacaoHorarioPlayCheckinCheckout obj\n" +
                    "WHERE obj.alunoLocacaoHorarioPlay.codigo = :codigoAlunoHorarioPlay\n" +
                    "and obj.horaCheckin is not null and obj.horaCheckout is null\n" +
                    "ORDER BY obj.codigo DESC LIMIT 1";
            Map<String, Object> params = new HashMap<>();
            params.put("codigoAlunoHorarioPlay", dto.getAlunoLocacaoHorarioPlay());
            AlunoLocacaoHorarioPlayCheckinCheckout alunoCheck = alunoLocacaoHorarioPlayCheckinCheckoutDao.findObjectByParam(ctx, query, params);

            if (alunoCheck == null) {
                alunoCheck = new AlunoLocacaoHorarioPlayCheckinCheckout(dto);
                alunoLocacaoHorarioPlayCheckinCheckoutDao.insert(ctx, alunoCheck);
                AlunoLocacaoHorarioPlay alunoLocacaoHorarioPlay = alunoLocacaoHorarioPlayDao.findById(ctx, dto.getAlunoLocacaoHorarioPlay());
                alunoLocacaoHorarioPlay.setCheckin(true);
                alunoLocacaoHorarioPlayDao.update(ctx, alunoLocacaoHorarioPlay);
                return "checkinId";
            } else {
                alunoCheck.setHoraCheckout(Uteis.getDate(dto.getDataHora(), "yyyy/MM/dd HH:mm:ss"));
                alunoLocacaoHorarioPlayCheckinCheckoutDao.update(ctx, alunoCheck);
                AlunoLocacaoHorarioPlay alunoLocacaoHorarioPlay = alunoLocacaoHorarioPlayDao.findById(ctx, dto.getAlunoLocacaoHorarioPlay());
                alunoLocacaoHorarioPlay.setCheckin(false);
                alunoLocacaoHorarioPlayDao.update(ctx, alunoLocacaoHorarioPlay);
                return "checkoutId";
            }
        }catch (Exception ex){
            throw new ServiceException(ex.getMessage());
        }
    }
}
