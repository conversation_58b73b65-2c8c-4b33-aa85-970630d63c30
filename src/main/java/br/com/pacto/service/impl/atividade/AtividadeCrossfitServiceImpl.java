package br.com.pacto.service.impl.atividade;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.atividade.Atividade;
import br.com.pacto.bean.atividade.AtividadeEmpresa;
import br.com.pacto.bean.atividade.AtividadeEmpresaTO;
import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.controller.json.atividade.AtividadeCrossfitDTO;
import br.com.pacto.controller.json.atividade.AtividadeCrossfitResponseDTO;
import br.com.pacto.controller.json.atividade.FiltroAtividadeCrossfitJSON;
import br.com.pacto.dao.intf.atividade.AtividadeDao;
import br.com.pacto.dao.intf.atividadeEmpresa.AtividadeEmpresaDao;
import br.com.pacto.dao.intf.empresa.EmpresaDao;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.notificacao.excecao.AtividadeExcecoes;
import br.com.pacto.service.intf.atividade.AtividadeCrossfitService;
import br.com.pacto.util.UteisValidacao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> 31/01/2019
 */
@Service
public class AtividadeCrossfitServiceImpl implements AtividadeCrossfitService {

    @Autowired
    private AtividadeDao atividadeDao;
    @Autowired
    private SessaoService sessaoService;
    @Autowired
    private AtividadeEmpresaDao atividadeEmpresaDao;
    @Autowired
    private EmpresaDao empresaDao;

    @Override
    public AtividadeCrossfitResponseDTO insert(AtividadeCrossfitDTO atividadeCrossfitDTO) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();

            validarCampos(atividadeCrossfitDTO);
            Atividade atividade = montarEntidade(ctx, atividadeCrossfitDTO);

            if (validarDuplicidade(ctx, atividade)) {
                throw new ServiceException(AtividadeExcecoes.VALIDACAO_ATIVIDADE_JA_EXISTE);
            }

            atividade = atividadeDao.insert(ctx, atividade);

            return new AtividadeCrossfitResponseDTO(atividade);
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    @Override
    public AtividadeCrossfitResponseDTO update(AtividadeCrossfitDTO atividadeCrossfitDTO) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();

            validarCampos(atividadeCrossfitDTO);
            Atividade atividade = montarEntidade(ctx, atividadeCrossfitDTO);

            if (validarDuplicidade(ctx, atividade)) {
                throw new ServiceException(AtividadeExcecoes.VALIDACAO_ATIVIDADE_JA_EXISTE);
            }

            atividade = atividadeDao.update(ctx, atividade);
            return new AtividadeCrossfitResponseDTO(atividade);
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    @Override
    public AtividadeCrossfitResponseDTO obterPorId(Integer atividadeCrossfitId) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            Atividade atividade = atividadeDao.obterPorId(ctx, atividadeCrossfitId);

            return new AtividadeCrossfitResponseDTO(atividade);
        } catch (Exception e) {
            throw new ServiceException(AtividadeExcecoes.ERRO_BUSCAR_ATIVIDADE);
        }
    }

    @Override
    public List<AtividadeCrossfitResponseDTO> listarAtividadesCrossfit(FiltroAtividadeCrossfitJSON filtros, PaginadorDTO paginadorDTO) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();

            List<Atividade> atividades = atividadeDao.listarAtividadeCrossfit(ctx, filtros, paginadorDTO);
            List<AtividadeCrossfitResponseDTO> listReturn = new ArrayList<>();

            for (Atividade atividade : atividades) {
                listReturn.add(new AtividadeCrossfitResponseDTO(atividade));
            }

            return listReturn;
        } catch (Exception e) {
            throw new ServiceException(AtividadeExcecoes.ERRO_BUSCAR_ATIVIDADES, e);
        }
    }

    @Override
    public void excluir(Integer atividadeCrossfitId) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            Atividade atividade = atividadeDao.findById(ctx, atividadeCrossfitId);
            atividade.setAtivo(!atividade.isAtivo());

            atividadeDao.update(ctx, atividade);
        } catch (Exception e) {
            throw new ServiceException(AtividadeExcecoes.ERRO_EXCLUIR_ATIVIDADE, e);
        }
    }

    private boolean validarDuplicidade(String ctx, Atividade atividade) throws Exception {
        StringBuilder where = new StringBuilder();
        if (!UteisValidacao.emptyNumber(atividade.getCodigo())) {
            where.append("codigo <> ").append(atividade.getCodigo());
            where.append(" and upper(nome) = '").append(atividade.getNome().toUpperCase() + "'");
        } else {
            where.append(" upper(nome) = '").append(atividade.getNome().toUpperCase() + "'");
        }
        where.append(" and (ativo = true or ativo is null)");
        where.append(" and crossfit = true");

        return atividadeDao.existsWithParam(ctx, where);
    }

    private void validarCampos(AtividadeCrossfitDTO atividadeCrossfitDTO) throws ServiceException {
        if (UteisValidacao.emptyString(atividadeCrossfitDTO.getNome())) {
            throw new ServiceException(AtividadeExcecoes.VALIDACAO_NOME_OBRIGATORIO);
        }
        if (atividadeCrossfitDTO.getCategoria() == null) {
            throw new ServiceException(AtividadeExcecoes.VALIDACAO_CATEGORIA_OBRIGATORIO);
        }
        if (atividadeCrossfitDTO.getUnidadeMedida() == null) {
            throw new ServiceException(AtividadeExcecoes.VALIDACAO_UNIDADE_MEDIDA_OBRIGATORIO);
        }
    }

    private Atividade montarEntidade(String ctx, AtividadeCrossfitDTO atividadeCrossfitDTO) throws Exception {
        Atividade atividade = new Atividade();
        List<AtividadeEmpresa> empresas = new ArrayList<>();
        if (atividadeCrossfitDTO != null && !UteisValidacao.emptyNumber(atividadeCrossfitDTO.getId())) {
            atividade = atividadeDao.findById(ctx, atividadeCrossfitDTO.getId());
            if (atividade == null || UteisValidacao.emptyNumber(atividade.getCodigo())) {
                throw new ServiceException(AtividadeExcecoes.ATIVIDADE_NAO_ENCONTRADA);
            }
            if (!UteisValidacao.emptyList(atividade.getEmpresasHabilitadas())) {
                removerRelacoes(ctx, atividade);
            }
        }
        atividade.setCrossfit(true);
        atividade.setNome(atividadeCrossfitDTO.getNome().trim());
        atividade.setAtivo(atividadeCrossfitDTO.isAtivo());
        atividade.setUnidadeMedida(atividadeCrossfitDTO.getUnidadeMedida());
        atividade.setCategoriaAtividadeWod(atividadeCrossfitDTO.getCategoria());
        atividade.setDescricao(atividadeCrossfitDTO.getDescricao() == null ? null : atividadeCrossfitDTO.getDescricao().trim());
        atividade.setLinkVideo(atividadeCrossfitDTO.getVideoUri());

        if (atividadeCrossfitDTO.getEmpresas() == null || UteisValidacao.emptyList(atividadeCrossfitDTO.getEmpresas())) {
            atividade.setTodasEmpresas(true);
        } else {
            atividade.setTodasEmpresas(false);
            for (AtividadeEmpresaTO aeTO : atividadeCrossfitDTO.getEmpresas()) {
                if (aeTO.getEmpresa() != null && aeTO.getEmpresa().getId() != null) {
                    Empresa empresa = empresaDao.findById(ctx, aeTO.getEmpresa().getId());
                    if (empresa == null) {
                        throw new ServiceException(AtividadeExcecoes.EMPRESA_NAO_ENCONTRADA);
                    }
                    AtividadeEmpresa ae = new AtividadeEmpresa(atividade, empresa);
                    ae.setIdentificador(aeTO.getIdentificador());
                    empresas.add(ae);
                }
            }
            atividade.getEmpresasHabilitadas().addAll(empresas);
        }

        return atividade;
    }

    private void removerRelacoes(String ctx, Atividade atividade) throws Exception {
        String[] strIdAtividade = new String[]{"atividade.codigo"};
        Object[] objIdAtividade = new Object[]{atividade.getCodigo()};
        atividadeEmpresaDao.deleteComParam(ctx, strIdAtividade, objIdAtividade);
    }
}
