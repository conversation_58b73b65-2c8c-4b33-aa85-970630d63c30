package br.com.pacto.service.impl.replicarEmpresa;

import br.com.pacto.base.util.ExecuteRequestHttpService;
import br.com.pacto.bean.replicarEmpresa.ConfiguracaoRedeEmpresa;
import br.com.pacto.controller.json.base.*;
import br.com.pacto.dao.intf.replicarEmpresa.ConfiguracaoReplicarRedeEmpresaDao;
import br.com.pacto.objeto.JSONMapper;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.authenticacao.AuthenticacaoMsService;
import br.com.pacto.service.discovery.ClientDiscoveryDataDTO;
import br.com.pacto.service.discovery.DiscoveryMsService;
import br.com.pacto.service.discovery.EmpresaDTO;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.configuracoes.ConfiguracaoSistemaService;
import br.com.pacto.service.intf.replicarEmpresa.ConfiguracaoReplicarRedeEmpresaService;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static java.util.Objects.nonNull;

@Service
public class ConfiguracaoReplicarRedeEmpresaServiceImpl implements ConfiguracaoReplicarRedeEmpresaService {

    @Autowired
    ConfiguracaoReplicarRedeEmpresaDao configuracaoReplicarRedeEmpresaDao;

    @Autowired
    private ConfiguracaoSistemaService configuracaoSistemaService;

    @Override
    public List<ConfiguracaoRedeEmpresa> findByChaveOrigem(String chaveOrigem) throws ServiceException {
        return configuracaoReplicarRedeEmpresaDao.findByChaveOrigem(chaveOrigem);
    }

    @Override
    public String replicar(String chaveOrigem, String chaveDestino, String tokenAtual) throws ServiceException {
        ConfiguracaoRedeEmpresa configuracaoRedeEmpresa = null;
        try {
            ClientDiscoveryDataDTO clientDiscoveryDataDTO = DiscoveryMsService.urlsChave(chaveDestino);
            String nomeUnidaes = clientDiscoveryDataDTO.getEmpresas().stream().map(EmpresaDTO::getNome).collect(Collectors.joining(", "));
            String urlTreino = clientDiscoveryDataDTO.getServiceUrls().getTreinoUrl();

            configuracaoRedeEmpresa = configuracaoReplicarRedeEmpresaDao.findByChaveDestino(chaveOrigem, chaveDestino);
            if (configuracaoRedeEmpresa == null) {
                configuracaoRedeEmpresa = new ConfiguracaoRedeEmpresa();
                configuracaoRedeEmpresa.setChaveOrigem(chaveOrigem);
                configuracaoRedeEmpresa.setChaveDestino(chaveDestino);
                configuracaoRedeEmpresa.setDataCadastro(new Date());
                configuracaoRedeEmpresa.setNomeUnidade(nomeUnidaes);
            }

            String[] configs = {"gerais", "gestao", "notificacoes", "aulas", "apps", "treino", "avaliacao"};

            String authorization = "Bearer " + AuthenticacaoMsService.getAutenticacaoTokenDTO(chaveDestino, tokenAtual).getToken();
            String retorno = "";
            for (String config : configs) {
                String urlSaveReplica = urlTreino + "/prest/replicar-empresa/configuracoes/saveReplica?chaveDestino=" + chaveDestino + "&configName=" + config;
                CloseableHttpResponse response = saveReplica(authorization, urlSaveReplica, getConfigDto(config, chaveOrigem));
                retorno = EntityUtils.toString(response.getEntity());
            }

            if (new JSONObject(retorno).getString("content").equals("sucesso")) {
                configuracaoRedeEmpresa.setDataAtualizacao(new Date());
                configuracaoRedeEmpresa.setMensagemSituacao("REPLICADO EM " + Uteis.getDataComHHMM(configuracaoRedeEmpresa.getDataAtualizacao()) + ". As configurações foram replicadas para a chave " + chaveDestino + " com sucesso!");
            }

            if (nonNull(configuracaoRedeEmpresa.getCodigo()) && configuracaoRedeEmpresa.getCodigo() != 0) {
                configuracaoReplicarRedeEmpresaDao.update(chaveOrigem, configuracaoRedeEmpresa);
            } else {
                configuracaoReplicarRedeEmpresaDao.insert(chaveOrigem, configuracaoRedeEmpresa);
            }
        } catch (Exception e) {
            e.printStackTrace();
            String msg = e.getCause() != null ? " Erro: " + e.getCause().toString() : (e.getMessage() != null ? e.getMessage() : "");
            registerError(msg, configuracaoRedeEmpresa, chaveOrigem, chaveDestino);
            throw new ServiceException("Não foi possivel replicar as configurações para chave " + chaveDestino + " Motivo: " + e.getMessage());
        }

        return "sucesso";
    }

    private CloseableHttpResponse saveReplica(String authorization, String urlTreino, String configDto) throws Exception {
        CloseableHttpClient client = ExecuteRequestHttpService.createConnectorCloseable();
        HttpPost httpPost = new HttpPost(urlTreino);
        httpPost.addHeader("Content-type", "application/json");
        httpPost.addHeader("Authorization", authorization);
        StringEntity entity = new StringEntity(configDto, "UTF-8");
        httpPost.setEntity(entity);
        return client.execute(httpPost);
    }

    private void registerError(String msg, ConfiguracaoRedeEmpresa configuracaoRedeEmpresa, String chaveOrigem, String chaveDestino) throws ServiceException {
        if (configuracaoRedeEmpresa == null) {
            configuracaoRedeEmpresa = new ConfiguracaoRedeEmpresa();
            configuracaoRedeEmpresa.setChaveOrigem(chaveOrigem);
            configuracaoRedeEmpresa.setChaveDestino(chaveDestino);
            configuracaoRedeEmpresa.setDataCadastro(new Date());
        }

        configuracaoRedeEmpresa.setMensagemSituacao("Erro: " + msg);
        try {
            if (nonNull(configuracaoRedeEmpresa.getCodigo()) && configuracaoRedeEmpresa.getCodigo() != 0) {
                configuracaoReplicarRedeEmpresaDao.update(chaveOrigem, configuracaoRedeEmpresa);
            } else {
                configuracaoReplicarRedeEmpresaDao.insert(chaveOrigem, configuracaoRedeEmpresa);
            }
        } catch (Exception exception) {
            exception.printStackTrace();
            throw new ServiceException("Houve uma falha ao replicar o plano e registro do erro não pode ser registrado");
        }
    }

    private String getConfigDto(String configName, String chaveOrigem) throws Exception {
        Class classs = null;
        Object obj = null;

        if (configName.equals("gerais")) {
            classs = ConfiguracoesGeraisDTO.class;
            obj = configuracaoSistemaService.configsDTO(chaveOrigem, classs, new ConfiguracoesGeraisDTO());
        } else if (configName.equals("gestao")) {
            classs = ConfiguracoesGestaoDTO.class;
            obj = configuracaoSistemaService.configsDTO(chaveOrigem, classs, new ConfiguracoesGestaoDTO());
        } else if (configName.equals("notificacoes")) {
            classs = ConfiguracoesNotificacaoDTO.class;
            obj = configuracaoSistemaService.configsDTO(chaveOrigem, classs, new ConfiguracoesNotificacaoDTO());
        } else if (configName.equals("aulas")) {
            classs = ConfiguracoesAulasDTO.class;
            ConfiguracoesAulasDTO aula = JSONMapper.getObject(new JSONObject(configuracaoSistemaService.configsDTO(chaveOrigem, classs, new ConfiguracoesAulasDTO())), ConfiguracoesAulasDTO.class);
            aula.setMinutos_agendar_com_antecedencia(Uteis.removerTudoMenosNumero(aula.getMinutos_agendar_com_antecedencia()));
            aula.setMinutos_desmarcar_com_antecedencia(Uteis.removerTudoMenosNumero(aula.getMinutos_desmarcar_com_antecedencia()));
            obj = JSONMapper.getObject(new JSONObject(aula), ConfiguracoesAulasDTO.class);
        } else if (configName.equals("apps")) {
            classs = ConfiguracoesAplicativosDTO.class;
            obj = configuracaoSistemaService.configsDTO(chaveOrigem, classs, new ConfiguracoesAplicativosDTO());
        } else if (configName.equals("treino")) {
            classs = ConfiguracoesTreinoDTO.class;
            obj = configuracaoSistemaService.configsDTO(chaveOrigem, classs, new ConfiguracoesTreinoDTO());
        } else if (configName.equals("avaliacao")) {
            classs = ConfiguracoesAvaliacaoDTO.class;
            obj = configuracaoSistemaService.configsDTO(chaveOrigem, classs, new ConfiguracoesAvaliacaoDTO());
        } else if (configName.equals("ia")) {
            classs = ConfiguracoesIaDTO.class;
            obj = configuracaoSistemaService.configsDTO(chaveOrigem, classs, new ConfiguracoesIaDTO());
        }else {
            throw new Exception("Nenhuma configuração encontrada.");
        }
        return new JSONObject(obj).toString();
    }
}
