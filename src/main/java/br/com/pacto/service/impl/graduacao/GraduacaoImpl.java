package br.com.pacto.service.impl.graduacao;

import br.com.pacto.base.util.ExecuteRequestHttpService;
import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.controller.json.graduacao.ImpressaoAtividadeDTO;
import br.com.pacto.controller.json.graduacao.ImpressaoAvaliacaoProgressoAlunoDTO;
import br.com.pacto.controller.json.graduacao.ImpressaoSubAtividadeDTO;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.discovery.ClientDiscoveryDataDTO;
import br.com.pacto.service.discovery.DiscoveryMsService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.cliente.ClienteSinteticoService;
import br.com.pacto.service.intf.empresa.EmpresaService;
import br.com.pacto.service.intf.graduacao.GraduacaoService;
import br.com.pacto.util.PDFFromXmlFile;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.bean.ItemRelatorioAvaliacaoProgressoTO;
import com.fasterxml.jackson.databind.ObjectMapper;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.util.EntityUtils;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class GraduacaoImpl implements GraduacaoService {

    @Autowired
    private ClienteSinteticoService clienteSinteticoService;
    @Autowired
    private EmpresaService empresaService;
    @Autowired
    private SessaoService sessaoService;

    @Override
    public String gerarPdfAvaliacaoProgressoV2(Integer matricula,
                                               Integer alunoId,
                                               Integer fichaId,
                                               Integer avaliacaoAlunoRespondidaId,
                                               HttpServletRequest request) throws Exception {

        String ctx = sessaoService.getUsuarioAtual().getChave();
        JSONObject result = Uteis.getJSON(Aplicacao.getProp(Aplicacao.discoveryUrls) + "/find/" + ctx);
        String graduacaoMsUrl = result.getJSONObject("content").getJSONObject("serviceUrls").getString("graduacaoMsUrl");
        String fullUrl = graduacaoMsUrl + "/aluno/consultar-dados-pdf-avaliacao-progresso/v2/" + ctx + "/" + matricula + "/" + alunoId + "/" + fichaId + "/" + avaliacaoAlunoRespondidaId;

        HttpGet httpGet = new HttpGet(fullUrl);
        httpGet.setHeader("Content-Type", "application/json");
        httpGet.addHeader("Authorization", "Bearer " + sessaoService.getUsuarioAtual().getToken());
        HttpClient client = ExecuteRequestHttpService.createConnectorCloseable();
        HttpResponse response = client.execute(httpGet);

        String responseBody = EntityUtils.toString(response.getEntity());
        JSONObject json = new JSONObject(responseBody);
        if (json.has("content")) {
            try {
                String contentJson = json.getJSONObject("content").toString();
                String conteudoLimpoDeCaracteresEspeciais = contentJson.replaceAll("%(?![0-9A-Fa-f]{2})", "%20");
                String dadosImprimirDecoded = URLDecoder.decode(conteudoLimpoDeCaracteresEspeciais, "UTF-8");
                ObjectMapper objectMapper = new ObjectMapper();
                ImpressaoAvaliacaoProgressoAlunoDTO dadosImprimirDTO = objectMapper.readValue(dadosImprimirDecoded, ImpressaoAvaliacaoProgressoAlunoDTO.class);
                return gerarPdfAvaliacaoProgresso(ctx, request, true, dadosImprimirDTO, null);
            } catch (Exception e) {
                Uteis.logar(e, GraduacaoImpl.class);
                throw new ServiceException("erro_gerar_pdf", e);
            }
        } else {
            Uteis.logarDebug("#### NÃO FOI POSSÍVEL CONSULTAR O ImpressaoAvaliacaoProgressoAlunoDTO PARA GERAR O PDF, FULLURL: " + fullUrl + " - RETORNO DO GRADUACAO-MS: " + json.toString());
            throw new ServiceException("erro_gerar_pdf");
        }
    }

    public String gerarPdfAvaliacaoProgresso(final String ctx,
                                             HttpServletRequest request,
                                             boolean externo,
                                             ImpressaoAvaliacaoProgressoAlunoDTO dadosImprimirDTO,
                                             String urlAplicacao) throws Exception {

        PDFFromXmlFile geradorPDF = new PDFFromXmlFile();
        Map<String, Object> parameters = new HashMap<String, Object>();

        ClienteSintetico cliente = clienteSinteticoService.consultarPorMatricula(ctx, dadosImprimirDTO.getMatricula().toString());
        Empresa empresa = empresaService.obterPorIdZW(ctx, cliente.getEmpresa());

        String urlFoto = Aplicacao.obterUrlFotoDaNuvem(cliente.getPessoa().getFotoKey());

        dadosImprimirDTO.setNomeAluno(cliente.getNome());
        dadosImprimirDTO.setUrlFotoAluno(urlFoto);
        dadosImprimirDTO.setNomeEmpresa(primeiraLetraMaiuscula(empresa.getNome()));

        parameters.put("fotoAluno", urlFoto);
        parameters.put("nomeEmpresa", primeiraLetraMaiuscula(empresa.getNome()));
        parameters.put("nomeAluno", primeiraLetraMaiuscula(cliente.getNome()));
        parameters.put("dataAvaliacao", dadosImprimirDTO.getDataAvaliacao());
        parameters.put("horariosAulas", dadosImprimirDTO.getHorarios());
        parameters.put("nomeProfessor", primeiraLetraMaiuscula(dadosImprimirDTO.getProfessor()));
        parameters.put("nomeNivelAtual", primeiraLetraMaiuscula(dadosImprimirDTO.getNivel()));
        parameters.put("diasAulas", dadosImprimirDTO.getDias());
        parameters.put("resultado", dadosImprimirDTO.getResultado());
        parameters.put("SUBREPORT_DIR", getDesignIReportAvProgressoSubReport());

        List<ItemRelatorioAvaliacaoProgressoTO> item = new ArrayList<>();
        int countAtv = 1;
        for (ImpressaoAtividadeDTO atividade : dadosImprimirDTO.getAtividades()) {
            String urlFotoAtv =
                    UteisValidacao.emptyString(atividade.getImageUri())
                            ? getNoImage()
                            : Aplicacao.obterUrlFotoDaNuvem(atividade.getImageUri());
            atividade.setImageUri(urlFotoAtv);

            String obs = atividade.getPossuiSubAtividade() ? null : atividade.getObservacao();
            ItemRelatorioAvaliacaoProgressoTO atividadeTO = new ItemRelatorioAvaliacaoProgressoTO(
                    countAtv + ". " + atividade.getNome(),
                    atividade.getDescricao(),
                    obs,
                    urlFotoAtv,
                    getDesignIReportAvProgressoSubReport(),
                    new JRBeanCollectionDataSource(montarSubAtividades(atividade)));
            atividadeTO.setPossuiSubAtividade(atividade.getPossuiSubAtividade());
            item.add(atividadeTO);

            countAtv++;
        }

        parameters.put("atividadeJR", new JRBeanCollectionDataSource(item));

        parameters.put("jan", dadosImprimirDTO.getFrequenciaAluno().get(0).getPresenca().toString());
        parameters.put("fev", dadosImprimirDTO.getFrequenciaAluno().get(1).getPresenca().toString());
        parameters.put("mar", dadosImprimirDTO.getFrequenciaAluno().get(2).getPresenca().toString());
        parameters.put("abr", dadosImprimirDTO.getFrequenciaAluno().get(3).getPresenca().toString());
        parameters.put("mai", dadosImprimirDTO.getFrequenciaAluno().get(4).getPresenca().toString());
        parameters.put("jun", dadosImprimirDTO.getFrequenciaAluno().get(5).getPresenca().toString());
        parameters.put("jul", dadosImprimirDTO.getFrequenciaAluno().get(6).getPresenca().toString());
        parameters.put("ago", dadosImprimirDTO.getFrequenciaAluno().get(7).getPresenca().toString());
        parameters.put("set", dadosImprimirDTO.getFrequenciaAluno().get(8).getPresenca().toString());
        parameters.put("out", dadosImprimirDTO.getFrequenciaAluno().get(9).getPresenca().toString());
        parameters.put("nov", dadosImprimirDTO.getFrequenciaAluno().get(10).getPresenca().toString());
        parameters.put("dez", dadosImprimirDTO.getFrequenciaAluno().get(11).getPresenca().toString());

        if (dadosImprimirDTO.getAvaliacaoAnterior() != null) {
            parameters.put("mesAno", dadosImprimirDTO.getAvaliacaoAnterior().getMesAno());
            String professoresHistorico = "";
            for (String professor : dadosImprimirDTO.getAvaliacaoAnterior().getProfessores()) {
                professoresHistorico += "; " + primeiraLetraMaiuscula(professor);
            }
            if (professoresHistorico.length() > 0) {
                professoresHistorico = new StringBuilder(professoresHistorico).deleteCharAt(0).toString();
            }
            parameters.put("professorHistorico", professoresHistorico);
            parameters.put("nivelHistorico", dadosImprimirDTO.getAvaliacaoAnterior().getNivel());
            String horariosHistorico = "";
            for (String horario : dadosImprimirDTO.getAvaliacaoAnterior().getHorarios()) {
                horariosHistorico += "; " + horario;
            }
            if (horariosHistorico.length() > 0) {
                horariosHistorico = new StringBuilder(horariosHistorico).deleteCharAt(0).toString();
            }
            parameters.put("horarioHistorico", horariosHistorico);
            parameters.put("resultadoHistorico", dadosImprimirDTO.getAvaliacaoAnterior().getResultado());
            parameters.put("diasHistorico", dadosImprimirDTO.getAvaliacaoAnterior().getDias());
        } else {
            parameters.put("mesAno", "-");
            parameters.put("professorHistorico", "-");
            parameters.put("nivelHistorico", "-");
            parameters.put("horarioHistorico", "-");
            parameters.put("resultadoHistorico", "-");
            parameters.put("diasHistorico", "-");
        }

        parameters.put("observacaoGeral", dadosImprimirDTO.getObservacaoGeral() == null ? "" : dadosImprimirDTO.getObservacaoGeral());

        String pdf = "";
        try {
            ClientDiscoveryDataDTO clientDiscoveryDataDTO = DiscoveryMsService.urlsChave(ctx);
            String url = clientDiscoveryDataDTO.getServiceUrls().getRelatorioMsUrl();
            String endpoint = "/graduacao/avaliacao-progresso-aluno";
            pdf = geradorPDF.sendToMsAvaliacaoProgressoGraduacao(url, endpoint, dadosImprimirDTO, sessaoService.getUsuarioAtual().getToken());
        } catch (Exception e) {
            Uteis.logar(e, GraduacaoImpl.class);
            pdf = imprimirAplicacaoLocal(
                    request,
                    geradorPDF.gerarPDF(ctx, request, getDesignIReportAvProgresso(), parameters,"resultadoAvaliacaoProgresso", false, externo, "PT")
            );
        }
        return pdf;
    }

    public String imprimirAplicacaoLocal(HttpServletRequest request, String path) {
        Uteis.logarDebug("#### NÃO FOI POSSÍVEL GERAR O PDF DA AVALIAÇÃO DE PROGRESSO NO RELATORIO-MS, GERANDO LOCALMENTE...");
        String urlAplicacao = Uteis.getURLValidaProtocolo(request);
        String urlRemove = request.getPathInfo();
        if (urlRemove == null) {
            return urlAplicacao + "/" + path;
        }
        urlRemove = urlRemove.replaceAll("/gerar-pdf-avaliacao-progresso-aluno", "");
        urlAplicacao = urlAplicacao.replaceAll("prest" + urlRemove, "");
        return urlAplicacao = urlAplicacao + path;
    }

    private List<ItemRelatorioAvaliacaoProgressoTO> montarSubAtividades(ImpressaoAtividadeDTO atividade) {
        List<ItemRelatorioAvaliacaoProgressoTO> subAtividades = new ArrayList<>();
        if (atividade.getPossuiSubAtividade()) {
            char countSub = 'a';
            for (ImpressaoSubAtividadeDTO subAtividadeDTO : atividade.getSubAtividades()) {
                subAtividades.add(new ItemRelatorioAvaliacaoProgressoTO(
                        countSub + ". " + subAtividadeDTO.getNome(),
                        subAtividadeDTO.getResposta(),
                        subAtividadeDTO.getObservacao()
                ));
                countSub++;
            }
        } else {
            subAtividades.add(new ItemRelatorioAvaliacaoProgressoTO(
                    atividade.getDescricao(),
                    atividade.getResposta(),
                    null
            ));
        }

        return subAtividades;
    }

    private String primeiraLetraMaiuscula(String str) {
        StringBuilder sb = new StringBuilder();
        if (!str.equals("") && str != null) {
            String[] words = str.split("\\s");

            for(int i = 0; i < words.length; i++){
                sb.append(words[i].substring(0, 1).toUpperCase() + words[i].substring(1).toLowerCase());
                sb.append(" ");
            }
        }

        return sb.toString();
    }

    public String getDesignIReportAvProgresso() {
        return ("br" + File.separator +
                "com" + File.separator +
                "pacto" + File.separator +
                "relatorio" + File.separator +
                "avaliacaoProgressoGraduacao" + File.separator +
                "avaliacao_progresso_aluno.jrxml");
    }

    public String getDesignIReportAvProgressoSubReport() {
        return ("br" + File.separator +
                "com" + File.separator +
                "pacto" + File.separator +
                "relatorio" + File.separator +
                "avaliacaoProgressoGraduacao" + File.separator);
    }

    public String getNoImage() {
        return ("br" + File.separator +
                "com" + File.separator +
                "pacto" + File.separator +
                "relatorio" + File.separator +
                "avaliacaoProgressoGraduacao" + File.separator +
                "noImage.png");
    }

}
