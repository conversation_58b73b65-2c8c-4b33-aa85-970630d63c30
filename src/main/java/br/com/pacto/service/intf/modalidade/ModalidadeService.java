package br.com.pacto.service.intf.modalidade;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.aula.Modalidade;
import br.com.pacto.controller.json.modalidade.CoresResponseTO;
import br.com.pacto.controller.json.modalidade.FiltroModalidadeJSON;
import br.com.pacto.controller.json.modalidade.ModalidadeResponseTO;
import br.com.pacto.controller.json.modalidade.ModalidadeTO;
import br.com.pacto.service.exception.ServiceException;

import java.util.List;

public interface ModalidadeService {

    public static final String SERVICE_NAME = "ModalidadeService";

    Modalidade inserir(final String ctx, Modalidade object) throws ServiceException;

    Modalidade alterar(final String ctx, Modalidade object) throws ServiceException;

    void remover(final String ctx, Modalidade object) throws ServiceException;

    Modalidade consultarPorModalidade(final String ctx, Integer id) throws ServiceException;

    List<CoresResponseTO> obterTodasCores() throws ServiceException;

    ModalidadeResponseTO cadastroModalidade(ModalidadeTO modalidadeTO) throws ServiceException;

    ModalidadeResponseTO detalhesModalidade(Integer id) throws ServiceException;

    List<ModalidadeResponseTO> listaModalidades(FiltroModalidadeJSON filtros, PaginadorDTO paginadorDTO, Integer empresaId) throws ServiceException;

    ModalidadeResponseTO alterarModalidade(Integer id, ModalidadeTO modalidadeTO) throws ServiceException;

    void removerModalidade(Integer id) throws ServiceException;

    List<ModalidadeResponseTO> obterTodasModalidades(Integer empresaId, Boolean turma) throws ServiceException;
}

