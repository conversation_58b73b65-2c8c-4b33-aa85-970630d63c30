/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.pacto.service.intf.configuracoes;

import br.com.pacto.bean.configuracoes.ConfiguracaoDobras;
import br.com.pacto.bean.configuracoes.ConfiguracaoPerimetro;
import br.com.pacto.bean.configuracoes.ConfiguracaoSistema;
import br.com.pacto.bean.configuracoes.ConfiguracaoSistemaUsuario;
import br.com.pacto.bean.configuracoes.ConfiguracoesEnum;
import br.com.pacto.bean.configuracoes.ConfiguracoesUsuarioEnum;
import br.com.pacto.bean.notificacao.TipoNotificacaoEnum;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.base.ConfiguracaoIntegracoesDTO;
import br.com.pacto.controller.json.configuracao.ConfiguracaoSistemaJSON;
import br.com.pacto.controller.json.gogood.ConfigGoGoodDTO;
import br.com.pacto.controller.json.mgb.ConfigMgb;
import br.com.pacto.controller.json.mqv.MqvDTO;
import br.com.pacto.controller.json.totalpass.TotalPassDTO;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.util.ConfigsEmail;
import com.fasterxml.jackson.core.JsonProcessingException;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;
import java.util.Map;



/**
 *
 * <AUTHOR>
 */
public interface ConfiguracaoSistemaService {

    public static final String SERVICE_NAME = "ConfiguracaoSistemaService";

    public ConfiguracaoSistema inserir(final String ctx, ConfiguracaoSistema object) throws ServiceException;

    public ConfiguracaoSistema obterPorId(final String ctx, Integer id) throws ServiceException;

    public ConfiguracaoSistema alterar(final String ctx, ConfiguracaoSistema object) throws ServiceException;
    
    public void refresh(final String ctx, ConfiguracaoSistema object) throws ServiceException;

    public void excluir(final String ctx, ConfiguracaoSistema object) throws ServiceException;

    public List<ConfiguracaoSistema> obterTodos(final String ctx) throws ServiceException;

    public List<ConfiguracaoSistema> obterPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException;

    public List<ConfiguracaoSistema> obterPorParam(final String ctx, String query,
            Map<String, Object> params, int max, int index)
            throws ServiceException;

    public ConfiguracaoSistema obterObjetoPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException;
    
    public List<ConfiguracaoSistema> consultarPovoando(String ctx) throws ServiceException;
    
    public ConfiguracaoSistema consultarPorTipo(String ctx, ConfiguracoesEnum cfg) throws ServiceException;
    
    public List<ConfiguracaoSistema> consultarPorTipos(String ctx, ConfiguracoesEnum... cfgs) throws ServiceException;
    
    public ConfigsEmail obterConfiguracoes(List<ConfiguracaoSistema> configuracoes) throws ServiceException;

    String consultarVersaoBd(String ctx) throws ServiceException;

    public ConfigsEmail obterConfiguracoes(String ctx) throws ServiceException;
    
    public ConfiguracaoSistema consultarPorTipoNotifacao(final String ctx, final TipoNotificacaoEnum tipoNotificacao) throws ServiceException;
    
    public boolean notificacaoConfigurada(final String ctx, final TipoNotificacaoEnum tipoNotificacao) throws ServiceException;
    
    public ConfiguracaoSistemaUsuario obterConfiguracaoUsuario(final String ctx, final Usuario usuario,
           ConfiguracoesUsuarioEnum config) throws ServiceException;

    public ConfiguracaoSistemaUsuario gravarConfiguracaoUsuario(final String ctx, final ConfiguracaoSistemaUsuario config) throws ServiceException;

    public Date obterDataUltimaAtualizacao(final String ctx) throws Exception;

    public void atualizarDataUltimaAtualizacao(final String ctx) throws Exception;

    public ConfigsEmail obterConfiguracoesPadrao() throws ServiceException;

    public List<ConfiguracaoPerimetro> obterCfgsPerimetro(final String ctx) throws Exception;

    public void gravarNovaOrdemPerimetros(final String ctx, List<ConfiguracaoPerimetro> cfgs) throws  Exception;

    String obterTokenIntegracaoOlympia(final String ctx);

    void atualizarQuantidadeAulasExperimentais(final String ctx, final Integer aulasExperimentaisAntesAlteracao, final Integer valorNovo) throws Exception;

    void atualizarQuantidadeAulasExperimentaisTodos(final String ctx, final Integer valorNovo) throws Exception;

    void incrementarVersaoProgramasSets(String key) throws Exception;

    void gravarCfgsDTO(String chave, Class classe, Object obj) throws ServiceException;

    void atualizarManutencao(final Integer valorNovo) throws ServiceException;

    Object configsDTO(Class classe, Object obj) throws ServiceException;

    Object configsDTO(String chave, Class classe, Object obj) throws ServiceException;

    List<ConfiguracaoDobras> obterCfgsDobras(final String ctx) throws Exception;

    void gravarCfgsDobras(final String ctx, List<ConfiguracaoDobras> cfgs) throws Exception;

    void executarExclusaoCliNaoExisteZw(final String ctx, final Integer empresa) throws ServiceException;

    List<ConfigMgb> obterConfigsMGB(final String ctx) throws ServiceException;

    void inserirConfigsMGB(final String ctx, List<ConfigMgb> configMGB) throws ServiceException;

    void sincronizarBooking(final String ctx, final Integer empresa) throws Exception;

    void preencherPerimetriasAutomatico(boolean esquerdaDireita, List<ConfiguracaoPerimetro> lista);

    List<ConfiguracaoSistema> obterTodosNativo(final String ctx) throws ServiceException;

    ConfiguracaoIntegracoesDTO obterConfigsBookingGympass(final String ctx, Integer empresaId, ConfiguracaoIntegracoesDTO configs) throws  ServiceException;

    ConfigGoGoodDTO obterConfigsGoGood(final String ctx, Integer empresaId, ConfiguracaoIntegracoesDTO configs) throws ServiceException;

    void gravarCfgsNotificacaoDTO(String chave, Class classe, Object obj) throws ServiceException;

    void normalizarImportacao(final String ctx, Integer empresa) throws ServiceException;

    List<MqvDTO> obterIntegracoesMQV(String ctx) throws ServiceException;

    void salvarIntegracoesMQV(String ctx, List<MqvDTO> mqvDTOList) throws ServiceException;

    List<TotalPassDTO> obterIntegracoesTotalPass(String ctx) throws ServiceException;

    void salvarIntegracoesTotalPass(String ctx, List<TotalPassDTO> totalPassDTOList) throws ServiceException;

    void notificarOuvintes(String ctx, HttpServletRequest request);

    boolean isOnCache(final String key);

    void purgeCache(final String key);

    ConfiguracaoSistemaJSON getCache(final String key);

    void putsOnCache(final String key, ConfiguracaoSistemaJSON json);

    void persistirIntegracoesLog(String ctx);

    String sincronizarAlunosMgb(final String ctx, final Integer empresa) throws ServiceException;

    String setAlunoParqFalse(final String ctx, final Integer matricula) throws Exception;

    String sincronizarProfessoresTW(final String ctx, final Integer empresaZW) throws ServiceException;

    String getDescricaoParaLog(Object obj, Object obj2);

    Boolean verificaPermiteDependente(String ctx) throws Exception;

    Boolean verificaPermiteRedirecionarAulaExperimentalCRMAGENDA(String ctx) throws Exception;

    String sincronizarAtividadesProfessoresTW(String ctx, Integer empresaZW) throws ServiceException;

    ConfiguracaoSistemaJSON alterarConfiguracoesParaApp(ConfiguracaoSistemaJSON json) throws JsonProcessingException;
}
