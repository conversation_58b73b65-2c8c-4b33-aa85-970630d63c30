package br.com.pacto.service.intf.usuarioEmail;

import br.com.pacto.bean.usuario.TipoUsuarioEnum;
import br.com.pacto.bean.usuarioEmail.UsuarioEmail;
import br.com.pacto.service.exception.ServiceException;

import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON> on 27/12/2016.
 */
public interface UsuarioEmailService {

    public static final String SERVICE_NAME = "usuarioEmailService";

    public UsuarioEmail inserir(final String ctx, UsuarioEmail object) throws ServiceException;

    public UsuarioEmail obterPorId(final String ctx, Integer id, TipoUsuarioEnum tipoUsuario) throws ServiceException;

    public UsuarioEmail alterar(final String ctx, UsuarioEmail object) throws ServiceException;

    public void excluir(final String ctx, UsuarioEmail object) throws ServiceException;

    public List<UsuarioEmail> obterTodos(final String ctx) throws ServiceException;

    public List<UsuarioEmail> obterPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException;

    public UsuarioEmail obterUsuarioEmailParam(final String ctx, String att, Object value)
            throws ServiceException;

    public List<UsuarioEmail> obterPorParam(final String ctx, String query,
                                            Map<String, Object> params, int max, int index)
            throws ServiceException;

}
