/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.pacto.service.intf.musculo;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.musculo.Musculo;
import br.com.pacto.bean.musculo.MusculoResponseTO;
import br.com.pacto.bean.musculo.MusculoTO;
import br.com.pacto.controller.json.nivel.FiltroNivelJSON;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.exception.ValidacaoException;

import java.util.List;
import java.util.Map;



/**
 *
 * <AUTHOR>
 */
public interface MusculoService {

    public static final String SERVICE_NAME = "MusculoService";

    public Musculo inserir(final String ctx, Musculo object) throws ServiceException, ValidacaoException;

    public Musculo obterPorId(final String ctx, Integer id) throws ServiceException;

    public Musculo alterar(final String ctx, Musculo object) throws ServiceException, ValidacaoException;

    public void excluir(final String ctx, Musculo object) throws ServiceException;

    public List<Musculo> obterTodos(final String ctx) throws ServiceException;

    public List<Musculo> obterPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException;

    public List<Musculo> obterPorParam(final String ctx, String query,
            Map<String, Object> params, int max, int index)
            throws ServiceException;

    public Musculo obterObjetoPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException;
    
    public List<Musculo> obterPorGrupo(final String ctx, final Integer grupoMuscularCodigo)
            throws ServiceException;

    List<MusculoResponseTO> consultarTodos()throws ServiceException;

    List<MusculoResponseTO>consultarMusculos(FiltroNivelJSON filtros, PaginadorDTO paginadorDTO)throws ServiceException;

    MusculoResponseTO consultarMusculo(Integer id)throws ServiceException;

    MusculoResponseTO inserir(MusculoTO musculoTO) throws ServiceException;

    MusculoResponseTO alterar(Integer id, MusculoTO musculoTO) throws ServiceException;

    void excluir(Integer id) throws ServiceException;
}
