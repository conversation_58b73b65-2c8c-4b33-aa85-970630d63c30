package br.com.pacto.service.intf.locacao;


import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.controller.json.agendamento.AgendaDisponibilidadeDTO;
import br.com.pacto.controller.json.agendamento.FiltroTurmaDTO;
import br.com.pacto.controller.json.locacao.*;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.agendatotal.HorarioItemAgendaDTO;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface LocacaoService {

    List<LocacaoTO> consultar(FiltrosLocacaoJSON filtrosL<PERSON>acaoJson, Integer empresaId, PaginadorDTO paginadorDTO) throws ServiceException;

    LocacaoTO consultarPorCodigo(Integer codigo) throws ServiceException;

    LocacaoTO cadastrar(LocacaoTO locacaoTO, Integer empresaId) throws ServiceException;

    List<LocacaoHorarioTO> saveOrUpdateLocacaoHorarios(Integer codigoLocacao, List<LocacaoHorarioTO> horarioTOS) throws ServiceException;

    Integer agendar(Date dia, AgendamentoLocacaoDTO agendamento, Integer empresaId, String ctx, Integer usuarioZWId) throws ServiceException;

    void reagendar(Integer codigoAgendamentoLocacao, AgendamentoLocacaoDTO agendamento, Date dia, String ctx) throws ServiceException;

    LocacaoTO alterar(LocacaoTO locacaoTO, Integer empresaId) throws ServiceException;

    void deletar(Integer codigo) throws ServiceException;

    void deletarLocacaoHorario(Integer codigo) throws ServiceException;

    void cardsLocacoesDisponiveis(String chave, String chaveAgrupadora,
                                  Date diaMes, List<String> listaHorarios,
                                  Integer empresa,
                                  Map<String, Map<String, List<HorarioItemAgendaDTO>>> agenda, FiltroTurmaDTO filtro) throws Exception;

    List<AgendaDisponibilidadeDTO> disponibilidades(Integer empresaId,
                                                           String ctx,
                                                           Date dia,
                                                           String horaSelecionada,
                                                           Integer locacaoCodigo,
                                                           PaginadorDTO paginadorDTO) throws ServiceException;

    ConfigAgendamentoLocacaoDTO configAgendamento(Integer horarioLocacao, String data, String ctx) throws ServiceException;

    void cardsLocacoesAgendadas(String chave, String chaveAgrupadora,
                                Date diaMes, List<String> listaHorarios,
                                Integer empresa,
                                Map<String, Map<String, List<HorarioItemAgendaDTO>>> agenda, FiltroTurmaDTO filtro) throws Exception;

    ConfigAgendamentoLocacaoDTO editAgendamento(Integer agendamentoId, String data, String ctx) throws ServiceException;

    ConfigAgendamentoLocacaoDTO cancelarAgendamentoLocacao(String ctx, Integer codigoUsuarioZW, Boolean canceladoPeloAluno, Integer codigoAgendamentoLocacao, String justificativa) throws ServiceException;
    boolean finalizarAgendamentoLocacao(String ctx, Integer codigoUsuarioZW, Integer codigoAgendamentoLocacao, AgendamentoLocacaoDTO agendamento, Date dia, Integer empresaId) throws ServiceException;

    List<LocacaoHorarioTO> validarDataReagendamento(String ctx, Integer codigoAgendamentoLocacao, Date novaData) throws ServiceException;

    void sincronizarVendaAvulsa(Integer agendamentoLocacaoCodigo, Integer vendaAvulsaCodigo) throws ServiceException;

    List<LocacaoTO> getLocacoes(Integer empresaId, String ctx, PaginadorDTO paginadorDTO) throws ServiceException;

    List<LocacaoVO> getLocacaoPorUsuario(Integer empresaId, String ctx, Date data, Integer codUsuario);
}
