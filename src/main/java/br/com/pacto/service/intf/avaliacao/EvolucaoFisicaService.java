package br.com.pacto.service.intf.avaliacao;

import br.com.pacto.bean.avaliacao.AvaliacaoFisica;
import br.com.pacto.bean.avaliacao.EvolucaoFisica;
import br.com.pacto.bean.avaliacao.PerimetriaEnum;
import br.com.pacto.bean.avaliacao.evolucao.EvolucaoFisicaDTO;
import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.service.exception.ServiceException;
import org.json.JSONObject;

import java.util.List;

/**
 * Created by alcides on 27/09/2017.
 */
public interface EvolucaoFisicaService {

    public static final String SERVICE_NAME = "EvolucaoFisicaService";

    public JSONObject montarGraficoPerimetro(List<AvaliacaoFisica> avaliacoes, PerimetriaEnum perimetro) throws Exception;

    public EvolucaoFisica dadosEvolucaoFisica(String key, List<AvaliacaoFisica> avaliacoes, ClienteSintetico cliente) throws Exception;

    EvolucaoFisicaDTO obterEvolucaoFisica(Integer id) throws ServiceException;
}
