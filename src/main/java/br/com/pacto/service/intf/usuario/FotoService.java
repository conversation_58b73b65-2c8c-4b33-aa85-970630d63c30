package br.com.pacto.service.intf.usuario;

import br.com.pacto.bean.usuario.Usuario;

import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;

/**
 * Serviços de foto
 *
 * <AUTHOR>
 * @since 24/08/2018
 */
public interface FotoService {

    /**
     * @param contexto       Chave do usuário loggado
     * @param usuario        O usuário para o qual se deseja a foto
     * @param forcarDownload Força o download da foto caso ela não exista
     * @param request        O request que veio do front
     * @return A string com a URL da foto encontrada
     * @throws Exception Caso ocorra algum problema durante a procura da foto
     * @see FotoService#carregarFoto(String, String, Integer, boolean, HttpServletRequest)
     */
    String carregarFoto(String contexto, Usuario usuario, boolean forcarDownload, HttpServletRequest request) throws Exception;

    /**
     * @param contexto       Chave do usuário loggado
     * @param fotoKeyApp     A foto do usuário para o aplicativo (pode ser nula)
     * @param codigoPessoa   Código da pessoa a qual se deseja a foto (para o caso do usuário ser nulo)
     * @param forcarDownload Força o download da foto caso ela não exista
     * @param request        O request que veio do front
     * @return A string com a URL da foto encontrada
     * @throws Exception Caso ocorra algum problema durante a procura da foto
     */
    String carregarFoto(String contexto, String fotoKeyApp, Integer codigoPessoa, boolean forcarDownload, HttpServletRequest request) throws Exception;

    String defineURLFotoPessoa(HttpServletRequest request, String identificador, Integer codigoPessoa, Boolean fotoPersonal, String key, Boolean verificarFotoKeyZW);

    void migracaoFotoPessoa(ServletContext context, String key, Integer codigoPessoa, String fotokey);

    String carregarFotoClienteNuvem(String contexto, Usuario usuario) throws Exception;

}
