/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.intf.configuracoes;

import br.com.pacto.bean.configuracoes.LinkPreDefinido;
import br.com.pacto.service.exception.ServiceException;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public interface LinkPredefinidoService {
    
    public static final String SERVICE_NAME = "LinkPredefinidoService";

    public LinkPreDefinido inserir(final String ctx, LinkPreDefinido object) throws ServiceException;
    
    public void deleteInativos(final String ctx, Integer empresa) throws ServiceException;

    public LinkPreDefinido obterPorId(final String ctx, Integer id) throws ServiceException;

    public LinkPreDefinido alterar(final String ctx, LinkPreDefinido object) throws ServiceException;

    public void excluir(final String ctx, LinkPreDefinido object) throws ServiceException;

    public List<LinkPreDefinido> obterTod<PERSON>(final String ctx) throws ServiceException;

    public List<LinkPreDefinido> obterPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException;

    public List<LinkPreDefinido> obterAtivos(final String ctx, Integer empresa)
            throws ServiceException;

    public List<LinkPreDefinido> obterPorParam(final String ctx, String query,
            Map<String, Object> params, int max, int index)
            throws ServiceException;

    public LinkPreDefinido obterObjetoPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException;
}
