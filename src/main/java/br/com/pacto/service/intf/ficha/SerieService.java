/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.intf.ficha;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.bean.atividade.AtividadeFicha;
import br.com.pacto.bean.ficha.Ficha;
import br.com.pacto.bean.ficha.SerieResponseTO;
import br.com.pacto.bean.serie.Serie;
import br.com.pacto.bean.serie.SerieEndpointTO;
import br.com.pacto.bean.serie.SerieRealizada;
import br.com.pacto.controller.json.atividade.read.SerieJSON;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.exception.ValidacaoException;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public interface SerieService {

    public static final String SERVICE_NAME = "SerieService";

    public Serie inserir(final String ctx, Serie object) throws ServiceException;

    public Serie obterPorId(final String ctx, Integer id) throws ServiceException;

    public void alterarSerie(final String ctx, SerieJSON serie) throws Exception;

    public Serie alterar(final String ctx, Serie object) throws ServiceException;

    public void excluir(final String ctx, Serie object) throws ServiceException;

    public List<Serie> obterTodos(final String ctx) throws ServiceException;

    public List<Serie> obterPorParam(final String ctx, String query,
            Map<String, Object> params)
            throws ServiceException;

    public List<Serie> obterPorParam(final String ctx, String query,
            Map<String, Object> params, int max, int index)
            throws ServiceException;

    public Serie obterObjetoPorParam(final String ctx, String query,
            Map<String, Object> params)
            throws ServiceException;

    public List<Serie> obterSeriePorAtividadeFicha(final String ctx,
            Integer atividadeFicha) throws ServiceException;

    public List<Number> obterCodigosSeriePorAtividadeFicha(final String ctx, final Integer atividadeFicha) throws ServiceException;

    public List<SerieRealizada> obterSeriesDoTreinoAud(final String ctx, final Integer idTreinoRealizado) throws ServiceException;

    public void adicionarSerie(String key, AtividadeFicha atividade, Ficha ficha)
            throws ValidacaoException, ServiceException;

    public Serie adicionarSerie(String key, AtividadeFicha atividade, Ficha ficha, boolean salvar)
            throws ValidacaoException, ServiceException;

    public void salvarSeries(String ctx, AtividadeFicha atvFicha, Ficha ficha)
            throws ServiceException, ValidacaoException;

    public void arrumarOrdem(List<Serie> series);

    public void removerSerie(String ctx, Serie serie, AtividadeFicha atividade,
            Ficha ficha) throws ValidacaoException, ServiceException;
    
    public void aplicarValorSeries(final String ctx, final Serie serie, final List<Serie> series) throws ServiceException;

    public void corrigirSerieZerada(final String ctx) throws Exception;

    void refresh(final String ctx, Serie object) throws Exception;

    SerieResponseTO cadastrarSerie(SerieEndpointTO serieTO) throws  ServiceException;

    SerieResponseTO editarSerie(SerieEndpointTO serieTO) throws ServiceException;

    void removerSerie(Integer id) throws ServiceException;

    void espelharSerie(Integer id) throws ServiceException;

    Serie adicionarSerie(final List<Serie> series, final AtividadeFicha atividadeFicha,
                                final Ficha ficha);
}
