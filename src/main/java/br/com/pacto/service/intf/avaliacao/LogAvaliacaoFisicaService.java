package br.com.pacto.service.intf.avaliacao;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.log.LogTO;
import org.json.JSONObject;

import java.util.List;

public interface LogAvaliacaoFisicaService {

    String SERVICE_NAME = "LogAvaliacaoFisicaService";
    String[] camposArray = new String[]{"codigo",
            "abdominal",
            "altura",
            "antebracodir",
            "antebracoesq",
            "aumentopercentualgordura",
            "axilarmedia",
            "biceps",
            "bracocontraidodir",
            "bracocontraidoesq",
            "bracorelaxadodir",
            "bracorelaxadoesq",
            "cargaastrand",
            "categoriaavaliacaoimc",
            "categoriapercentualgordura",
            "cintura",
            "circunferenciaabdominal",
            "coxadistaldir",
            "coxadistalesq",
            "coxamediadir",
            "coxamediaesq",
            "coxamedial",
            "coxaproximaldir",
            "coxaproximalesq",
            "dataavaliacao",
            "dataproxima",
            "distancia12",
            "ectomorfia",
            "endomorfia",
            "fcastrand",
            "fcmaxima",
            "fcqueens",
            "flexibilidade",
            "gluteo",
            "gorduraideal",
            "gorduravisceral",
            "idademetabolica",
            "imc",
            "massagorda",
            "massamagra",
            "mesomorfia",
            "metapercentualgordura",
            "metapercentualgorduraanterior",
            "movproduto",
            "necessidadecalorica",
            "necessidadefisica",
            "ombro",
            "panturrilha",
            "panturrilhadir",
            "panturrilhaesq",
            "peitoral",
            "percentualagua",
            "percentualgordura",
            "percentualmassamagra",
            "pescoco",
            "peso",
            "pesomuscular",
            "pesoosseo",
            "protocolo",
            "protocolovo",
            "punho",
            "quadril",
            "reatancia",
            "recomendacoes",
            "residual",
            "resistencia",
            "subescapular",
            "supraespinhal",
            "suprailiaca",
            "tempo2400",
            "tmb",
            "toraxbusto",
            "totaldobras",
            "totalperimetria",
            "triceps",
            "urlassinatura",
            "venda",
            "vo2astrand",
            "vo2max12",
            "vo2max2400",
            "vo2maxastrand",
            "vo2maxqueens",
            "vomaxaerobico",
            "agendamentoreavaliacao_codigo",
            "cliente_codigo"
    };

    List<LogTO> listarLogAtividade(JSONObject filtros, PaginadorDTO paginadorDTO, Integer codAluno, Integer codAvaliacao) throws ServiceException;

    List<LogTO> listarLogRespostaAnamnese(JSONObject filtros, PaginadorDTO paginadorDTO, Integer codAvaliacao) throws ServiceException;

    List<LogTO> listarLogConsolidadoAvaliacaoFisica(JSONObject filtros, PaginadorDTO paginadorDTO, Integer codAluno, Integer codAvaliacao) throws ServiceException;

}
