package br.com.pacto.service.intf.cliente;

import br.com.pacto.base.oamd.RedeEmpresaVO;
import br.com.pacto.controller.json.cliente.ClienteJSON;
import br.com.pacto.service.exception.ServiceException;
import org.json.JSONObject;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

public interface ClienteRedeEmpresaService {

    List<ClienteJSON> consultarPorMatriculaOuNome(final String chaveOrigem,
                                                  final RedeEmpresaVO rede,
                                                  final String filtro) throws ServiceException;

    JSONObject programaEmRede(String chaveOrigem, String token, HttpServletRequest request) throws ServiceException;

    JSONObject executarFichaEmRede(String chaveOrigem,
                                          String dataAtual, String idPrograma, String idFicha, boolean fichaConcluida,
                                          String token, HttpServletRequest request) throws ServiceException;

}
