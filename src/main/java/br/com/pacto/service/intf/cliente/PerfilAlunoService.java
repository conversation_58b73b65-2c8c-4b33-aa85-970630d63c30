package br.com.pacto.service.intf.cliente;

import br.com.pacto.bean.avaliacao.PerimetriaEnum;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.cliente.perfil.*;

import java.util.List;

public interface PerfilAlunoService {

    PerfilAlunoAvaliacaoDTO avaliacaoFisica(Integer matricula) throws ServiceException;

    FichaDoDiaDTO fichaDoDia(Integer matricula, Boolean buscarNaoVigente) throws ServiceException;

    FichasRelacionadasDTO fichasRelacionadas(Integer matricula) throws ServiceException;

    DiasQueTreinouProgramaAtualDTO diasQueTreinouProgramaAtual(Integer matricula) throws ServiceException;

    HorariosQueTreinouProgramaAtual horariosQueTreinouProgramaAtual(Integer matricula) throws ServiceException;

    ProgramaAtualDTO programaAtual(Integer matricula) throws ServiceException;

    List<PerimetriaHistoricoAlunoDTO> historicoPerimetria(Integer matricula, PerimetriaEnum perimetria) throws ServiceException;

    FichaDoDiaDTO fichaDoDia(Integer matricula, Boolean buscarNaoVigente, String ctx) throws ServiceException;
}
