/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.intf.aula;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.aula.*;
import br.com.pacto.controller.json.colaborador.ProfessorSubstituidoDTO;
import br.com.pacto.controller.json.professor.FiltroGestaoJSON;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.util.bean.AgendaTotalTO;

import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public interface GestaoSalaCheiaService {
    public static final String SERVICE_NAME = "GestaoSalaCheiaService";
    
    public List<GestaoSalaCheiaTO> montarBIExcecao(String ctx, Date inicio, Date fim,FiltrosGestaoSalaCheia filtro, Integer codEmpresaZW) throws ServiceException;
    
    public List<GestaoSalaCheiaTO> montarBIDesempenho(String ctx, List<AulaDia> obterAulasDia) throws ServiceException;
    
    public List<GestaoSalaCheiaTO> montarBIRanking(String ctx, List<AulaDia> verdes, List<AulaDia> vermelhas) throws ServiceException;
    
    public List<GestaoDisponibilidadeJSON> montarBIExcecaoPorPeriodo(
            GestaoSalaCheiaTO pedidosNegados,
            GestaoSalaCheiaTO experimental,
            List<AulaDia> aulas,
            FiltrosGestaoSalaCheia filtro) throws ServiceException;
    
    public GestaoSalaCheiaTO montarBIOcupacao(String ctx, List<AgendaTotalTO> aulas, TipoAulaCheiaOrigemEnum tipo, FiltrosGestaoSalaCheia filtro) throws Exception;
    
    public List<AulaDia> filtrarAulas(final String key, List<AulaDia> aulas, FiltrosGestaoSalaCheia filtro, boolean substituidos) throws Exception;
    
    public List<GestaoSalaCheiaAnaliticoTO> montarExcecaoAnalitico(String key, GestaoSalaCheiaTO pedidosNegados, GestaoSalaCheiaTO experimental) throws ServiceException;
    
    public List<GestaoSalaCheiaAnaliticoTO> montarDesempenhoAnalitico(String key, List<AulaDia> verdes, List<AulaDia> vermelhas, List<AulaDia> amarelas) throws ServiceException;
    
    public List<GestaoSalaCheiaAnaliticoTO> montarOcupacaoAnalitico(String key, GestaoSalaCheiaTO ocupacao, Boolean mostrarZerados) throws Exception;
    
    public List<GestaoSalaCheiaAnaliticoTO> montarBiSubstituidos(String key, FiltrosGestaoSalaCheia filtro, List<AgendaTotalTO> agendamentos) throws Exception;

    List<ProfessorSubstituidoDTO> montarBiSubstituidosSpa(FiltroGestaoJSON filtroGestao, PaginadorDTO paginadorDTO) throws ServiceException;

}
