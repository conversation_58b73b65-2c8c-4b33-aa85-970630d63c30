package br.com.pacto.service.intf.cliente;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.cliente.ClientePesquisa;
import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.controller.json.aluno.AlunoPesquisaResponseTO;
import br.com.pacto.service.exception.ServiceException;
import org.json.JSONObject;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

public interface ClientePesquisaService {

    String SERVICE_NAME = "ClientePesquisaService";
    ClientePesquisa inserir(final String ctx, ClientePesquisa object) throws ServiceException;
    ClientePesquisa inserir(final String ctx, ClienteSintetico object) throws ServiceException;
    ClientePesquisa inserir(String ctx, JSONObject object) throws ServiceException;
    ClientePesquisa inserirDandoFlush(String ctx, ClienteSintetico object) throws ServiceException;
    ClientePesquisa inserirDandoFlush(String ctx, JSONObject object) throws ServiceException;
    public void excluir(String ctx, ClientePesquisa object) throws ServiceException;
    public void excluirPorMatricula(String ctx, Integer matricula) throws ServiceException;
    ClientePesquisa alterar(final String ctx, ClientePesquisa object) throws ServiceException;
    List<ClientePesquisa> consultar(final String ctx, String filtro, PaginadorDTO paginadorDTO, Integer empresaId);
    public ClientePesquisa consultarPorMatricula(String ctx, Integer matricula) throws ServiceException;
    public List<AlunoPesquisaResponseTO> listaAlunos(final String parametro, PaginadorDTO paginadorDTO, HttpServletRequest request, Integer empresaId) throws ServiceException;
}
