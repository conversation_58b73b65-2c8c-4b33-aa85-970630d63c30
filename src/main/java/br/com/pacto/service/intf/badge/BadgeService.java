/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.pacto.service.intf.badge;

import br.com.pacto.bean.badge.Badge;
import br.com.pacto.service.exception.ServiceException;
import java.util.List;
import java.util.Map;



/**
 *
 * <AUTHOR>
 */
public interface BadgeService {

    public static final String SERVICE_NAME = "BadgeService";

    public Badge inserir(final String ctx, Badge object) throws ServiceException;

    public Badge obterPorId(final String ctx, Integer id) throws ServiceException;

    public Badge alterar(final String ctx, Badge object) throws ServiceException;

    public void excluir(final String ctx, Badge object) throws ServiceException;

    public List<Badge> obterTodos(final String ctx) throws ServiceException;

    public List<Badge> obterPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException;

    public List<Badge> obterPorParam(final String ctx, String query,
            Map<String, Object> params, int max, int index)
            throws ServiceException;

    public Badge obterObjetoPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException;

    public void povoarDefault(String ctx)throws ServiceException;
    
    public List<Badge> consultarPorAluno(final String ctx, Integer aluno) throws ServiceException;
}
