/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.intf.pessoa;

import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.bean.pessoa.StatusPessoa;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.service.exception.ServiceException;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public interface StatusPessoaService {

    public static final String SERVICE_NAME = "StatusPessoaService";

    public StatusPessoa inserir(final String ctx, StatusPessoa object) throws ServiceException;

    public StatusPessoa obterPorId(final String ctx, Integer id) throws ServiceException;

    public StatusPessoa alterar(final String ctx, StatusPessoa object) throws ServiceException;

    public void excluir(final String ctx, StatusPessoa object) throws ServiceException;

    public List<StatusPessoa> obterTod<PERSON>(final String ctx) throws ServiceException;

    public List<StatusPessoa> obterPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException;

    public List<StatusPessoa> obterPorParam(final String ctx, String query,
            Map<String, Object> params, int max, int index)
            throws ServiceException;

    public StatusPessoa obterObjetoPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException;

    public void atualizarStatusAluno(final String ctx, final ClienteSintetico cliente, boolean saiu, Date dataInicio, final Empresa empresaAcesso) throws ServiceException;

    /**
     * Realiza a remoção das {@link StatusPessoa} vinculadas ao {@link Usuario}
     * @param ctx chave do banco
     * @param usuario {@link Usuario}
     * @throws ServiceException
     */
    void removerPorUsuario(String ctx, Usuario usuario) throws  ServiceException;
}
