package br.com.pacto.service.intf.selfloops;

import br.com.pacto.bean.selfloops.SelfLoopsConfiguracoes;
import br.com.pacto.controller.json.agendamento.AlunoTurmaDTO;
import br.com.pacto.controller.json.selfloops.*;
import br.com.pacto.service.exception.ServiceException;
import org.json.JSONObject;

import java.util.List;

public interface SelfloopsConfiguracoesService {

    public static final String SERVICE_NAME = "SelfloopsConfiguracoesService";

    SelfLoopsConfiguracoes obterPorEmpresa(final String ctx, final Integer empresa) throws ServiceException;

    void salvarIntegracoesSelfloops(final String ctx, List<SelfloopsDTO> selfloopsDTOList) throws ServiceException;

    List<SelfloopsDTO> obterIntegracoesSelfloops(final String ctx) throws ServiceException;

    List<SensorsSelfloopsDTO> obterSensoresSelfloops(SelfLoopsConfiguracoes config, String chave, Integer empresaTreino);

    JSONObject criarAulaIntegracaoSelfloops(String code, String refresh_token, CourseScheduleDTO courseScheduleDTO, String chave, Integer empresaTreino) throws ServiceException;

    JSONObject deleteAulaIntegracaoSelfloops(String code, String refresh_token, String courseScheduleId, String chave, Integer empresaTreino) throws ServiceException;

    JSONObject alterarAulaIntegracaoSelfloops(String code, String refresh_token, CourseScheduleDTO courseScheduleDTO, String courseScheduleId, String chave, Integer empresaTreino) throws ServiceException;

    JSONObject criarAlunoIntegracaoSelfloops(String code, String refresh_token, UserDTO aluno, String chave, Integer empresaTreino);

    JSONObject inserirAlunoAulaSensorIntegracaoSelfloops(String code, String refresh_token, String userSelfloopsId, String courseSelfloopsId, CourseDTO course, String chave, Integer empresaTreino);

    JSONObject deleteAlunoAulaSensorIntegracaoSelfloops(String code, String refresh_token, String userSelfloopsId, String courseSelfloopsId, String chave, Integer empresaTreino);

    JSONObject obterCourseDoDiaIntegracaoSelfloops(String code, String refresh_token, String empresaSelfloops, String courseScheduleSelfloopsId, String dia, String chave, Integer empresaTreino) throws ServiceException;

    JSONObject obterAtividadesCourseDoDiaIntegracaoSelfloops(String code, String refresh_token, String courseSelfloopsId, String chave, Integer empresaTreino);

    void obterRankingCourseDoDiaIntegracaoSelfloops(String ctx, Integer empresaId, Integer horarioId, String dia, AlunoTurmaDTO aluno);
}
