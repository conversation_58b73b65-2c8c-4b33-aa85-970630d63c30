package br.com.pacto.service.intf.replicarEmpresa;

import br.com.pacto.bean.replicarEmpresa.ConfiguracaoRedeEmpresa;
import br.com.pacto.service.exception.ServiceException;

import java.util.List;

/**
 * Created by <PERSON><PERSON> Alcides on 04/05/2017.
 */
public interface ConfiguracaoReplicarRedeEmpresaService {

    public static final String SERVICE_NAME = "ReplicarEmpresaService";

    List<ConfiguracaoRedeEmpresa> findByChaveOrigem(String chaveOrigem) throws ServiceException;

    String replicar(String chaveOrigem, String chaveDestino, String tokenAtual) throws ServiceException;
}
