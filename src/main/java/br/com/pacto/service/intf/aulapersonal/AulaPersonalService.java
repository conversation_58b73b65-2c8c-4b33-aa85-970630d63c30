/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.pacto.service.intf.aulapersonal;

import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.gestaopersonal.AgrupamentoGestaoPersonal;
import br.com.pacto.bean.gestaopersonal.AlunoAulaPersonal;
import br.com.pacto.bean.gestaopersonal.AulaAberto;
import br.com.pacto.bean.gestaopersonal.AulaPersonal;
import br.com.pacto.bean.gestaopersonal.MotivoBloqueioEnum;
import br.com.pacto.bean.gestaopersonal.TipoAgrupamentoPersonalEnum;
import br.com.pacto.bean.professor.ProfessorSintetico;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.aulapersonal.ExportCreditoTO;

import java.util.Date;
import java.util.List;
import java.util.Map;



/**
 *
 * <AUTHOR>
 */
public interface AulaPersonalService {

    public static final String SERVICE_NAME = "AulaPersonalService";

    public AulaPersonal inserir(final String ctx, AulaPersonal object) throws ServiceException;

    public AulaPersonal obterPorId(final String ctx, Integer id) throws ServiceException;

    public AulaPersonal alterar(final String ctx, AulaPersonal object) throws ServiceException;

    public void excluir(final String ctx, AulaPersonal object) throws ServiceException;

    public List<AulaPersonal> obterTodos(final String ctx) throws ServiceException;

    public List<AulaPersonal> obterPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException;

    public List<AulaPersonal> obterPorParam(final String ctx, String query,
            Map<String, Object> params, int max, int index)
            throws ServiceException;

    public AulaPersonal obterObjetoPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException;

    public List<AulaPersonal> obterUltimasCincoAulasPersonal(final String ctx, final Integer personal) throws ServiceException;
    
    public AulaPersonal obterUltimaAulaEmAberto(final String ctx, final Integer personal) throws ServiceException;
    
    public List<AulaAberto> obterAulasEmAberto(String ctx, Integer empresa, Integer duracaoCredito, boolean contarCreditoPorAlunos) throws ServiceException;
    
    public List<AlunoAulaPersonal> obterAlunosRecentes(String ctx, Integer personal) throws ServiceException;
    
    public void realizarCheckOut(final String key, final AulaPersonal aula, final Integer duracaoCredito, boolean contarCreditoPorAluno, boolean automatico) throws ServiceException;
    
    public List<AgrupamentoGestaoPersonal> montarGestao(final String ctx, final Date inicio,
            final Date fim, final Usuario usuario, final TipoAgrupamentoPersonalEnum tipo) throws ServiceException;
    
    public MotivoBloqueioEnum validarPrecisaAutorizacaoCheckIn(String ctx, ProfessorSintetico personal, Usuario user) throws ServiceException;
    
    public AulaPersonal realizarCheckIn(final String key, Usuario usuario,
            List<AlunoAulaPersonal> alunosAdicionados, ProfessorSintetico personal,
            MotivoBloqueioEnum motivoBloqueio, Usuario usuarioforcou, Date checkIn) throws ServiceException;
    
    public void enviarEmailPersonal(final String ctx, final ProfessorSintetico personal, 
            final List<AgrupamentoGestaoPersonal> agrupamentos, Date inicio, Date fim)
            throws ServiceException;
    
    public Integer fecharCreditosPersonal(final String ctx, final ProfessorSintetico personal, final Usuario usuario) throws ServiceException;
    
    public AulaPersonal editarCheckIn(final String key, AulaPersonal aula) throws ServiceException;
    
    public AulaPersonal obterAulaAbertoAluno(final String key, final AlunoAulaPersonal aluno) throws ServiceException;
    
    public void ajustarSaldoCreditos(final String key, final AulaPersonal aula,boolean excluir, final Integer duracaoCredito,
            boolean contarCreditoPorAluno, Usuario usuario) throws ServiceException;
    
    public void checkOutAutomatico(final String key) throws ServiceException;
    
    public List<AlunoAulaPersonal> completeParaCheckIn(final String key, final Integer empresaZW, final String param) throws ServiceException;

    List<AlunoAulaPersonal> obterAlunosPorAulaPersonal(final String ctx, Integer codigo) throws ServiceException;

    void normalizarCreditos(final String ctx, Integer professor, String dataLimite) throws Exception;

    List<ExportCreditoTO> exportCheckin(final String ctx,
                                        final Date inicio,
                                        final Date fim) throws ServiceException;

    boolean personalEmAtendimento(String ctx, Integer codigoColaborador) throws ServiceException;

    void normalizarChekins(String key) throws Exception;
}
