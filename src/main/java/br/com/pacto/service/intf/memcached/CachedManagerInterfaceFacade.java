package br.com.pacto.service.intf.memcached;

import br.com.pacto.service.impl.memcached.EntidadeCacheEnum;
import net.spy.memcached.MemcachedClient;

import java.util.List;
import java.util.Map;

public interface CachedManagerInterfaceFacade {

   void gravar(String key, String identificador, Object obj);

   void gravar(String key, String identificador, Object obj, EntidadeCacheEnum entidade);

   void gravar(String key, String identificador, Object obj, EntidadeCacheEnum entidade, String matricula);

   void gravar(String key, Object obj, int tempoExpirar, String identificador, EntidadeCacheEnum entidade, String matricula);

   <T> T ler(String key, String identificador);

   void remover(String key, String identificador);

   void removerEntidade(String key, EntidadeCacheEnum entidadeCacheEnum);

   void removerMatricula(String key, String matricula);

   void remover(String key, String identificador, EntidadeCacheEnum entidade, String matricula);

   Map<String, Map<EntidadeCacheEnum, List<String>>> mapaCache();

   void invalidarCacheAgenda(String key);

   void invalidarCacheAgendados(String key);

   void invalidarCache(String key, EntidadeCacheEnum entidadeCacheEnum, Integer matricula);

   void clear();

   void enable();

   void disable();

   MemcachedClient getMemcachedClient();

   boolean getMemcachedOn();

}
