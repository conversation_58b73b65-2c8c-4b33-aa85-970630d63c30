package br.com.pacto.service.intf.crossfit;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.controller.json.gestao.BICrossfitDTO;
import br.com.pacto.controller.json.gestao.FiltrosBiCrossfitJSON;
import br.com.pacto.controller.json.gestao.RankingGeralDTO;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.crossfit.AgendamentosAlunosDTO;
import br.com.pacto.service.impl.crossfit.DetalhesIndicadorBICrossfitDTO;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;

public interface BICrossfitService {

    public static final String SERVICE_NAME = "BICrossfitService";

    BICrossfitDTO gerarBI(Integer mes, Integer ano, Integer idProfessor, Integer empresa) throws ServiceException;

    List<RankingGeralDTO> rankingGeral(Date inicio, Date fim, HttpServletRequest request, Integer empresaId) throws Exception;

    List<DetalhesIndicadorBICrossfitDTO> alunosComResultadosLancados(FiltrosBiCrossfitJSON filtrosBiCrossfitJSON, PaginadorDTO paginadorDTO) throws ServiceException;

    List<AgendamentosAlunosDTO> alunosPorAgendamentos(FiltrosBiCrossfitJSON filtrosBiCrossfitJSON, Integer empresaId, PaginadorDTO paginadorDTO) throws ServiceException;

    List<DetalhesIndicadorBICrossfitDTO> alunosFrequentaAulaProfessor(Integer empresa, FiltrosBiCrossfitJSON filtrosBiCrossfitJSON, PaginadorDTO paginadorDTO) throws ServiceException;
}
