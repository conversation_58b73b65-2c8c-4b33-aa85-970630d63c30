/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.pacto.service.intf.programa;

import br.com.pacto.bean.programa.ObjetivoPrograma;
import br.com.pacto.service.exception.ServiceException;
import java.util.List;
import java.util.Map;



/**
 *
 * <AUTHOR>
 */
public interface ObjetivoProgramaService {

    public static final String SERVICE_NAME = "ObjetivoProgramaService";

    public ObjetivoPrograma inserir(final String ctx, ObjetivoPrograma object) throws ServiceException;

    public ObjetivoPrograma obterPorId(final String ctx, Integer id) throws ServiceException;

    public ObjetivoPrograma alterar(final String ctx, ObjetivoPrograma object) throws ServiceException;

    public void excluir(final String ctx, ObjetivoPrograma object) throws ServiceException;

    public List<ObjetivoPrograma> obterTodos(final String ctx) throws ServiceException;

    public List<ObjetivoPrograma> obterPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException;

    public List<ObjetivoPrograma> obterPorParam(final String ctx, String query,
            Map<String, Object> params, int max, int index)
            throws ServiceException;

    public ObjetivoPrograma obterObjetoPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException;

}
