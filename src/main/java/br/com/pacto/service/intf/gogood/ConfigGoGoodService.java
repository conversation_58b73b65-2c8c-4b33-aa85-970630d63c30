package br.com.pacto.service.intf.gogood;

import br.com.pacto.bean.gogood.ConfigGoGood;
import br.com.pacto.controller.json.gogood.ConfigGoGoodDTO;
import br.com.pacto.service.exception.ServiceException;


public interface ConfigGoGoodService {

    public static final String SERVICE_NAME = "ConfigGoGoodService";

    ConfigGoGood alterarDTO(final String ctx, ConfigGoGoodDTO dto) throws ServiceException;

    ConfigGoGood obterPorEmpresaTR(final String ctx, Integer codEmpresaTR) throws ServiceException;
}
