package br.com.pacto.service.intf.scoretreino;

import br.com.pacto.bean.wod.ComentarioWod;
import br.com.pacto.bean.wod.Wod;
import br.com.pacto.service.exception.ServiceException;

import java.util.List;
import java.util.Map;

/*
 * Created by <PERSON><PERSON>
 */
public interface ComentarioWodService {

    ComentarioWod obterPorId(final String ctx, Integer id) throws ServiceException;

    List<ComentarioWod> obterPorParam(final String ctx, String query, Map<String, Object> param) throws ServiceException;

    ComentarioWod inserir(final String ctx, ComentarioWod object) throws ServiceException;

    ComentarioWod alterar(final String ctx, ComentarioWod object) throws ServiceException;

    void excluir(final String ctx, ComentarioWod object) throws ServiceException;

    void excluirComRelacionamentos(final String ctx, ComentarioWod object) throws ServiceException;

    void preencherComentariosWod(final String ctx, Wod wod) throws ServiceException;

}
