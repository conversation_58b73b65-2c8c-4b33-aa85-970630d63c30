/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */

package br.com.pacto.service.intf.perfil;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.perfil.FiltroPerfilJSON;
import br.com.pacto.bean.perfil.Perfil;
import br.com.pacto.bean.perfil.PerfilDTO;
import br.com.pacto.bean.perfil.PerfilResponseTO;
import br.com.pacto.bean.perfil.permissao.Permissao;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.bean.programa.ProfessorResponseTO;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.util.bean.CategoriaRecursosTO;
import br.com.pacto.util.bean.GenericoTO;
import br.com.pacto.util.bean.RecursoTO;

import java.util.HashMap;
import java.util.List;
import java.util.Map;



/**
 *
 * <AUTHOR>
 */
public interface PerfilService {

    public static final String SERVICE_NAME = "PerfilService";

    public Perfil inserir(final String ctx, Perfil object) throws ServiceException;

    public Perfil obterPorId(final String ctx, Integer id) throws ServiceException;

    public Perfil alterar(final String ctx, Perfil object) throws ServiceException;

    public void excluir(final String ctx, Perfil object) throws ServiceException;

    public List<Perfil> obterTodos(final String ctx) throws ServiceException;

    public List<Perfil> obterPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException;

    public List<Perfil> obterPorParam(final String ctx, String query,
            Map<String, Object> params, int max, int index)
            throws ServiceException;

    public Perfil obterObjetoPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException;
    
    public Permissao obterPermissaoPorPerfilRecurso(final String ctx, Integer perfil, RecursoEnum recurso) throws ServiceException;
    
    public void gerenciarPermissao(String ctx, Perfil perfil, RecursoTO recurso, GenericoTO tipo) throws ServiceException;
    
    public void inserirNovo(String ctx, Perfil perfil) throws ServiceException;
    
    public void marcarTodos(String ctx, Perfil perfil, GenericoTO tipo, CategoriaRecursosTO categoria, boolean marcarRecurso) throws ServiceException;
    public boolean verificarPerfilExcluir(String ctx, Perfil perfil) throws ServiceException;
    public void alterarNome(String ctx, Perfil perfil) throws ServiceException;
    List<PerfilResponseTO> consultarTodos()throws ServiceException;

    PerfilResponseTO cadastrarPerfil(HashMap<String, Object> perfil) throws ServiceException;

    List<PerfilResponseTO> consultarPerfil(FiltroPerfilJSON filtros, PaginadorDTO paginadorDTO) throws ServiceException;

    PerfilDTO obterPerfil(Integer perfilId) throws ServiceException;

    void removerPerfil(Integer perfilId) throws ServiceException;

    PerfilDTO editarPerfil(HashMap<String, Object> perfilDTO, Integer perfilId, String perfilNome) throws ServiceException;

    Permissao obterPermissaoPorPerfilRecursoNativo(final String ctx,  final Integer perfil,  final RecursoEnum recurso) throws ServiceException;

    void restaurarPerfilCoordenador(String ctx) throws ServiceException, Exception;

    String corrigirNomeUsuarioPacto(String ctx) throws ServiceException, Exception;

    Perfil obterPorUsuario(final String ctx, final Integer idUsuario) throws ServiceException;

    List<ProfessorResponseTO> consultarProfessoresPorPerfil(Integer perfilId, PaginadorDTO paginadorDTO, String filter, Integer empresaIdZw) throws ServiceException;

}
