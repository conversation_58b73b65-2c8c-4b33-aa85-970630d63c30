/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.pacto.service.intf.gympass;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.gympass.BookingGymPass;
import br.com.pacto.bean.gympass.DetalheLogGympassTO;
import br.com.pacto.bean.gympass.LogGymPassTO;
import br.com.pacto.service.exception.ServiceException;

import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 06/04/2020
 */
public interface BookingGymPassService {

    public static final String SERVICE_NAME = "BookingGymPassService";

    BookingGymPass obterPorId(final String ctx, Integer id) throws ServiceException;

    BookingGymPass obterPorBookingId(final String ctx, String bid) throws ServiceException;

    BookingGymPass incluir(final String ctx, BookingGymPass object) throws ServiceException;

    BookingGymPass alterar(final String ctx, BookingGymPass object) throws ServiceException;

    List<LogGymPassTO> listarLog(PaginadorDTO paginadorDTO) throws Exception;

    DetalheLogGympassTO listarLogDetalhe(Integer codigo) throws Exception;

}
