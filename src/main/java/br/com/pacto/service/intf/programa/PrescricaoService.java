package br.com.pacto.service.intf.programa;


import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.oamd.RedeEmpresaVO;
import br.com.pacto.bean.cliente.PessoaPrescricaoDTO;
import br.com.pacto.bean.colaborador.FiltroColaboradorJSON;
import br.com.pacto.bean.configuracoes.ConfiguracaoSistema;
import br.com.pacto.controller.json.cliente.ClienteJSON;
import br.com.pacto.controller.json.programa.read.OrigemEnum;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.util.ViewUtils;
import br.com.pacto.util.bean.ItemTO;
import org.json.JSONObject;
import org.springframework.ui.ModelMap;

import java.util.List;
import java.util.Map;

public interface PrescricaoService {

     String SERVICE_NAME = "PrescricaoService";

    List<Integer> matriculasPrescricao(Integer empresa, JSONObject filtros) throws ServiceException;

    List<PessoaPrescricaoDTO> listaPrescricao(Integer empresa, JSONObject filtros, PaginadorDTO paginadorDTO) throws ServiceException;

    List<PessoaPrescricaoDTO> listaPrescricaoV2(Integer empresa, JSONObject filtros, PaginadorDTO paginadorDTO) throws ServiceException;

    List<ItemTO> filtroPrincipal(Integer empresa, FiltroColaboradorJSON filtros, PaginadorDTO paginadorDTO) throws ServiceException;

    List<ClienteJSON> consultarProfessorTreinoPorNome(final String ctx,
                                                  final String filtro) throws ServiceException;

    void setAtualColaborador(Integer codigoColaborador, String ctx,
                                    ModelMap mm,
                                    ConfiguracaoSistema configSeriesSet,
                                    String ordenarFicha,
                                    ViewUtils viewUtils,
                                    OrigemEnum origemEnum) throws Exception;
}
