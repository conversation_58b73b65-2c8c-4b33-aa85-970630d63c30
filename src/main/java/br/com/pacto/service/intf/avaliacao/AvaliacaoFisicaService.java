package br.com.pacto.service.intf.avaliacao;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.anamnese.*;
import br.com.pacto.bean.avaliacao.*;
import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.empresa.IdiomaBancoEnum;
import br.com.pacto.bean.pessoa.SexoEnum;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.avaliacao.*;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.exception.ValidacaoException;
import br.com.pacto.service.impl.avaliacao.RMLConfigDTO;
import br.com.pacto.util.ViewUtils;
import br.com.pacto.util.enumeradores.ProtocolosAvaliacaoFisicaEnum;
import org.json.JSONObject;

import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.Date;

import java.util.List;
import java.util.Map;

/**
 * Created by Rafael on 08/10/2016.
 */
public interface AvaliacaoFisicaService {

    public static final String SERVICE_NAME = "AvaliacaoFisicaService";

    public AvaliacaoFisica inserir(final String ctx, AvaliacaoFisica object) throws ServiceException, ValidacaoException;

    public ItemAvaliacaoFisica alterarItem(final String ctx, ItemAvaliacaoFisica objectAntesAlteracao, ItemAvaliacaoFisica object, AvaliacaoFisica avaliacaoFisica) throws ServiceException, ValidacaoException;

    public ItemAvaliacaoFisica alterarItem(final String ctx, ItemAvaliacaoFisica object) throws ServiceException, ValidacaoException;

    public AvaliacaoFisica obterPorId(final String ctx, Integer id) throws ServiceException;

    public AvaliacaoFisica alterar(final String ctx, AvaliacaoFisica object) throws ServiceException, ValidacaoException;

    public void excluir(final String ctx, AvaliacaoFisica object, String origem) throws ServiceException;

    public List<AvaliacaoFisica> obterAvaliacaoMovProduto(final String ctx,final int movProduto) throws ServiceException;

    public void calcularTabelFrequenciaCardiaca(final AvaliacaoFisica a, final ClienteSintetico cliente, final Integer fcRepouso) throws ServiceException;

    public List<AvaliacaoFisica> obterTodos(final String ctx) throws ServiceException;

    public List<AvaliacaoFisica> obterPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException;

    public List<AvaliacaoFisica> obterPorParam(final String ctx, String query,
                                               Map<String, Object> params, int max, int index)
            throws ServiceException;

    public List<ItemAvaliacaoFisica> obterItensAvaliacaoFisica(final String ctx,final int cliente,
                                                               final ItemAvaliacaoFisicaEnum i, Integer limit, boolean parq,
                                                               final AvaliacaoFisica avaliacao) throws ServiceException;

    public List<ItemAvaliacaoFisica> obterItensAvaliacaoIntegrada(final String ctx,final int cliente) throws ServiceException;

    public List<PesoOsseo> obterItensPesoOsseo(final String ctx,final int cliente) throws ServiceException;

    public AvaliacaoFisica obterObjetoPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException;

    public AvaliacaoFisica obterAvaliacaoVigente(final String ctx,final int cliente) throws ServiceException ;

    public ItemImpressao montarListaImpressao(final String ctx,final String codigosAvalicacao) throws Exception;

    public List<AvaliacaoFisica> obterAvaliacaoCliente(final String ctx,final int cliente) throws ServiceException;

    public void classificarPercentualGordura(AvaliacaoFisica obj,Integer idade,String sexo) throws ServiceException;

    public void calcularAvalicaoFisica(final AvaliacaoFisica avaliacaoFisica,
                                       final PesoOsseo pesoOsseo,
                                       ProtocolosAvaliacaoFisicaEnum protocolo,
                                       boolean temImportacaoBiosanny) throws ServiceException;

    public ItemAvaliacaoFisica adicionarItem(final String key, final String pressao,
                                             final ClienteSintetico cliente, final Usuario responsavel, final ItemAvaliacaoFisicaEnum i,
                                             final Anamnese anamnese, final Ventilometria ventilometria, final AvaliacaoFisica avaliacaoFisica) throws ServiceException;

    public RespostaClienteParQ adicionarRespostaParQ(final String ctx, final JSONObject json, final Integer usuarioZw, final ClienteSintetico cliente) throws ServiceException;

    public ItemAvaliacaoFisica gravarItem(final String key, ItemAvaliacaoFisica prs) throws ServiceException;

    public void excluirItem(final String key, final ItemAvaliacaoFisica prs) throws Exception;

    public void excluirPesoOsseo(final String key, final PesoOsseo po) throws ServiceException;

    public PesoOsseo inserirPesoOsseo(final String key, final PesoOsseo po) throws ServiceException;

    public PesoOsseo calcularPesoOsseo(final PesoOsseo po, boolean masculino) throws ServiceException;

    public PesoOsseo calcularPesoOsseo(final PesoOsseo po, boolean masculino, AvaliacaoFisica avaliacaoFisica) throws ServiceException;

    public PesoOsseo obterPesoOsseo(final String ctx,final int cliente, final AvaliacaoFisica avaliacao) throws ServiceException;

    AvaliacaoBioimpedanciaDTO extrairInformacoesAvaliacaoBioimpedancia(ImportarAvaliacaoBioimpedanciaDTO avaliacaoBioimpedanciaDTO) throws IOException, ServiceException;

    public ItemAvaliacaoFisica obterItemAvaliacaoFisica(final String ctx,final int cliente,
                                                        final ItemAvaliacaoFisicaEnum i,
                                                        final Date dia,
                                                        final AvaliacaoFisica avaliacao) throws ServiceException;
    ItemAvaliacaoFisica obterItemAvaliacaoPorAnamnese(final String ctx, final int anamneseId) throws ServiceException;

    public ResultadoResistenciaEnum calcularResistencia(ItemAvaliacaoFisicaEnum tipo, Integer idade, Integer valor) throws Exception;

    public List<ResultadoVO2> calcularVo212Minutos(AvaliacaoFisica avaliacao, boolean homem, Integer idade) throws Exception;

    public List<ResultadoVO2> calcularVoAerobicoDeBanco(AvaliacaoFisica avaliacaoFisica, boolean homemm, Integer idade) throws  Exception;

    public void calcularVo2Astrand(AvaliacaoFisica avaliacao, boolean homem) throws Exception;

    public void calcularVo2Queens(AvaliacaoFisica avaliacao, boolean homem) throws Exception;

    public List<ResultadoVO2> calcularVo2Teste2400(AvaliacaoFisica avaliacao, boolean homem, Integer idade) throws Exception;

    public ResultadoResistenciaEnum obterResultadoResistencia(final String chave, ClienteSintetico cliente, ItemAvaliacaoFisicaEnum i, AvaliacaoFisica avaliacao) throws Exception;

    public AvaliacaoFisicaJSON obterAvaliacaoAtual(final String chave, final String matricula) throws Exception;

    public List<AvaliadorFisicoJSON> avaliadoresFisicosDisponiveis(final String chave, final Integer empresaZw, final Date data, final HttpServletRequest request) throws Exception;

    public List<AvaliacaoFisicaJSON> historico(final String chave, final String matricula) throws Exception;

    public List<AvaliacaoFisica> historico(final String chave, final Integer codigoCliente) throws Exception;

    public void salvarFlexibilidade(final String ctx, final Flexibilidade flexibilidade) throws Exception;

    public Flexibilidade obterFlexibilidade(final String ctx, final Integer avaliacao) throws Exception;

    public void alterarVentilometria(final String key, final Ventilometria ventilometria) throws Exception;

    public List<SugestaoHorarioJSON> sugerirHorarios(final String chave, final Date data, final Integer professor, final Integer empresa)
            throws Exception;

    public void calcularResultadoMovimento(List<PerguntaAnamnese> perguntas, ItemAvaliacaoFisica item, List<Movimento3D> movimento3DS);

    public void calcularResultadoVida(List<PerguntaAnamnese> perguntas, ItemAvaliacaoFisica item);

    public void gravarMovimento3D(final String key, final ItemAvaliacaoFisica item, final List<Movimento3D> movimento3DS) throws Exception;

    public List<Movimento3D> obterMovimento3D(final String key, final ItemAvaliacaoFisica item) throws Exception;

    public ItemAvaliacaoFisica consultarItemCodigo(final String key, final Integer codigo) throws Exception;

    public ItemAvaliacaoFisica montarVisualizacao(final String key, final Integer item) throws Exception;

    public void obterRespostas(String ctx, Anamnese a, ItemAvaliacaoFisica i) throws Exception;

    public ItemAvaliacaoFisica adicionarItemAvaliacaoIntegrada(final String key,
                                                               final ItemAvaliacaoFisica item,
                                                               final String result,
                                                               final ClienteSintetico cliente,
                                                               final Usuario responsavel,
                                                               final  Anamnese anamnese,
                                                               final Date dataResposta) throws ServiceException;

    public ItemAvaliacaoFisica alterarItemAvaliacaoIntegrada(final String key,
                                                             final ItemAvaliacaoFisica item,
                                                             final String result,
                                                             final Anamnese anamnese,
                                                             final Date dataResposta) throws ServiceException;

    public void enviarAvaliacaoIntegrada(final String key, ItemAvaliacaoFisica i, String nomeEmpresa, Usuario usuario,
                                         String[] destinatarios,
                                         ServletContext context, HttpServletRequest request, String linkAvaliacao) throws Exception;

    public String gerarPDFComparativo(final String key, final List<AvaliacaoFisica> avaliacoes,
                                      final Usuario usuario, ViewUtils viewUtils,
                                      HttpServletRequest request, ServletContext servletContext,final Boolean enviarPorEmail) throws Exception;

    public String gerarPDFAvaliacaoFisica(final String key, final AvaliacaoFisica avaliacao,
                                          Flexibilidade flexibilidade,
                                          PesoOsseo pesoOsseo,
                                          Ventilometria ventilometria,
                                          final Anamnese anamnese,
                                          final Anamnese questionarioParq,
                                          final Usuario usuario, ViewUtils viewUtils,
                                          HttpServletRequest request, ServletContext servletContext,
                                          boolean enviarEmail, boolean externo, String language) throws Exception;

    public PesoOsseo gerarPesoOsseo(AvaliacaoFisica avaliacaoFisica, ClienteSintetico cliente) throws Exception;

    public AvaliacaoPostural gravarAvaliacaoPostural(final String key, AvaliacaoPostural avaliacaoPostural,
                                                     List<ItemAvaliacaoPostural> itens) throws Exception;

    public List<ItemAvaliacaoPostural> consultarItens(final String key, final AvaliacaoPostural postural) throws Exception;

    public AvaliacaoPostural consultarPostural(final String key, final AvaliacaoFisica avaliacao) throws Exception;

    public void enviarEmailAvaliacao(final String key,
                                     final ClienteSintetico cliente,
                                     final String nomeEmpresa,
                                     final AvaliacaoFisica avaliacao,
                                     Flexibilidade flexibilidade,
                                     PesoOsseo pesoOsseo,
                                     Ventilometria ventilometria,
                                     final Anamnese anamnese,
                                     final Anamnese questionarioParq,
                                     final Usuario usuario, ViewUtils viewUtils,
                                     HttpServletRequest request, ServletContext servletContext, final String language) throws Exception;

    public String montarLinksWpp(Integer avaliacaoId, String telefone, HttpServletRequest request, String idiomaBanco)throws Exception;

    public String montarLinksComparativoWpp(Integer alunoId, List<Integer> avaliacoes, HttpServletRequest request, ViewUtils viewUtils) throws Exception;

    public void salvarAssinatura(String key, AvaliacaoFisica avaliacao) throws Exception;

    public RespostaClienteParQ salvarAssinaturaParQ(String ctx, String assinatura64, RespostaClienteParQ respostaClienteParQ) throws Exception;

    public void limparAssinatura(String key, AvaliacaoFisica avaliacao) throws Exception;

    public String gerarPDFParQ(final String key, final Anamnese questionarioParq, final AvaliacaoFisica avaliacaoFisica,
                               final Usuario usuario, ViewUtils viewUtils,
                               HttpServletRequest request, ServletContext servletContext, boolean externo, String assinaturaDigital) throws Exception;

    public String gerarPDFParQAssinaturaDigital(final String ctx,
                                                final Anamnese questionarioParq,
                                                final List<RespostaCliente> respostaClienteList,
                                                RespostaClienteParQ rcp,
                                                ViewUtils viewUtils,
                                                HttpServletRequest request,
                                                ServletContext servletContext,
                                                boolean externo,
                                                Integer cliente) throws Exception;

    Double obterUltimaMetaGordura(String key, Integer cliente, Date diaAvaliacao, Integer avaliacao) throws Exception;

    List<AvaliacaoIntegradaJSON> obterJSONAvaliacoesIntegradas(final String ctx, final String matricula) throws Exception;

    void gravarBioimpedancia(final String ctx, ClienteSintetico cliente, final String dados) throws Exception;

    boolean temNovaAvaliacao(String ctx, Integer cliente) throws Exception;

    AvaliacaoFisicaDTO avaliacaoAtual(String chave, Integer codigoCliente, Usuario usuario) throws Exception;

    List<AvaliacaoFisicaDTO> todasCliente(String chave, Integer codigoCliente, Usuario usuario) throws Exception;

    List<AvaliacaoFisicaDTO> todasAvaliacoesPorPeriodo(final String chave, final String dataInicio, final String dataFim) throws Exception;

    void enviarEmailAvaliacao(Integer empresaId,
                              final String key,
                                     final Integer avaliacao ,
                                     final String email,
                                     Usuario usuario,
                                     HttpServletRequest request, ServletContext servletContext, IdiomaBancoEnum language) throws Exception;

    List<PerguntaResponseTO> obterPerguntasParQ(String ctx, Integer codigoAnamnese) throws Exception;

    void enviarComparativoSpa(List<Integer> avaliacoes, HttpServletRequest request, ServletContext sc) throws Exception;

    AvaliacaoFisicaDTO inserir( AvaliacaoFisicaDTOUpdate object, Integer codigoCliente) throws ServiceException;

    AvaliacaoFisicaDTO inserir( AvaliacaoFisicaDTOUpdate object, Integer codigoCliente, Integer idProduto) throws ServiceException;

    AvaliacaoFisicaDTO alterar( AvaliacaoFisicaDTOUpdate object, Integer codigoAvaliacaoFisica) throws ServiceException;

    AvaliacaoFisicaDTO alterarVo2(Integer codigoAvaliacaoFisica, AvaliacaoVo2AstrandDTO dados) throws ServiceException;

    void enviarEmailComparativoAvaliacao(final String key,
                                         final String pdf,
                                         final ClienteSintetico cliente,
                                         final String nomeEmpresa,
                                         HttpServletRequest request,
                                         ServletContext servletContext, final Usuario usuario) throws Exception;

    String comporUrlPdf(String ctx, Integer codAvaliacao, HttpServletRequest request, Boolean isNewTreino, String language) throws Exception;

    AvaliacaoFisicaDTO obterAvaliacaoFisica(Integer id) throws ServiceException;

    void enviarComparativoAvaliacaoIntegrada(final String key, List<ItemAvaliacaoFisica> avaliacoes, String nomeEmpresa, Usuario usuario,
                                         String[] destinatarios,
                                         ServletContext context) throws Exception;

    Boolean obterResultadoQuestionarioParq(String ctx, ClienteSintetico cliente) throws Exception;

    boolean isAvaliacaoVigente(final String ctx, AvaliacaoFisica object) throws ServiceException;

    void atualizarResultadoEvolucaoAluno(String ctx, ClienteSintetico cliente);

    List<SugestaoHorarioPersonalJSON> sugerirHorariosApp(final String chave, final Date data, final Integer empresa)
            throws Exception;

    Map<String, String> restaurarRespostas(String ctx, String codigosPergunta) throws Exception;

    List<RespostaCliente> obterRespostasCliente(final String ctx, final Integer cliente, final Integer respostaClienteParQ) throws ServiceException;

    List<RespostaClienteTO> respostaClienteToTO(List<RespostaCliente> lista) throws ServiceException;

    String consultarClientesParQAssinaturaDigital(String ctx, Integer empresaZw, String nomeOuMatricula, HttpServletRequest request, Integer diasParaVencimentoParq, String todos) throws Exception;

    PerguntaAnamnese preencherListaPerguntaResposta(String ctx, AnamnesePerguntaRespostaDTOUpdate objAnamnese, Anamnese anamnese) throws ServiceException;

    boolean isParqAlunoAssinado(String ctx, Integer empresaZw, String matricula, HttpServletRequest request) throws ServiceException;

    String obterMatAlunosParq(String ctx, Integer empresaZw, String tipoConsulta, Integer diasParaVencimentoParq) throws ServiceException;

    ResultadoResistenciaEnum calcularResistenciaHomem(ItemAvaliacaoFisicaEnum tipo, Integer idade, Integer valor, SexoEnum sexo) throws Exception;

    ResultadoResistenciaEnum calcularResistenciaMulher(ItemAvaliacaoFisicaEnum tipo, Integer idade, Integer valor, SexoEnum sexo) throws Exception;

    RMLConfigDTO obterTabelaRML(Integer alunoId) throws ServiceException;

    String processoRemoverAvaliacaoPosturalDuplicada(final String ctx, boolean todas, Integer codigoAvaliacaoPostural) throws Exception;

    void processoAjustarResponsavel(final String ctx) throws Exception;

    JSONObject consultarParQsAluno(String ctx, Integer empresaZw, Integer codigoCliente, PaginadorDTO paginadorDTO) throws ServiceException;
}
