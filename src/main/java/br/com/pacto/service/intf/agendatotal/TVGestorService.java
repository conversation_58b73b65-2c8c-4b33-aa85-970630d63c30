/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.intf.agendatotal;

import br.com.pacto.bean.tvgestor.AlunoFavorito;
import br.com.pacto.bean.tvgestor.TVGestorItem;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.util.bean.AgendaTotalTO;
import br.com.pacto.util.bean.AgendadoTO;
import br.com.pacto.util.bean.FiltrosAgendaTO;
import br.com.pacto.util.bean.LinhaSemanaTO;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public interface TVGestorService {
    
    public static final String SERVICE_NAME = "TVGestorService";
    
    public Map<String, List<AgendadoTO>> montarMapaPresencas(String ctx,Integer empresa) throws Exception;
    
    public List<AgendadoTO> obterAcessaram(String ctx, Integer empresa,
                                           Map<String, AgendaTotalTO> agenda,
                                           Map<String, List<AgendadoTO>> agendados,
                                           List<AlunoFavorito> favoritos,
                                           FiltrosAgendaTO filtrosAgendaTO,
                                           Date dia) throws Exception;
    
    public String montarGrafico(List<TVGestorItem> itens, Date dt,
            Map<String, List<AlunoFavorito>> mapaFavoritos)throws Exception;
    
    public List<TVGestorItem> processarItens(String ctx, Integer empresa, Date limite, 
                               List<AgendadoTO> acessaram,
                               Map<String, AgendaTotalTO> agenda,
                               List<AlunoFavorito> favoritos, boolean reprocessar,
                               FiltrosAgendaTO filtros) throws Exception;
    
    public AlunoFavorito gravarFavorito(final String ctx,AlunoFavorito af) throws ServiceException;
    
    public List<AlunoFavorito> obterAlunosFavoritos(final String ctx,
            final Usuario usuario)throws ServiceException;
    
    public void removerFavorito(final String ctx,AlunoFavorito af) throws ServiceException;
    
    public List<LinhaSemanaTO> montarSemanaTVGestor(String ctx, Date inicio, Date fim, 
                               Integer empresa,
                               List<Date> dias,
                               List<Integer> horas,
                               Map<String, AgendaTotalTO> agenda,
                               Map<String, List<AgendadoTO>> mapaAgendados,
                               FiltrosAgendaTO filtrosAgendaTO,
                               Map<Date, Map<Integer, Map<Integer, AgendadoTO>>> mapaAcessaram) throws Exception;
    
    public List<AgendadoTO> detalharGrafico(final String chave, final boolean acessaram, Integer hora,
            Date dia, final Integer empresa, FiltrosAgendaTO filtrosAgendaTO,List<AlunoFavorito> favoritos,
            Map<String, AgendaTotalTO> agendamentosDia,
            Map<String, List<AgendadoTO>> mapaAgendados) throws ServiceException ;
    
    public Integer nrTotalEsperadosDia(List<LinhaSemanaTO> listaSemana, Date dia);
}
