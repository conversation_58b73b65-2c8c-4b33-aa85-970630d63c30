package br.com.pacto.service.intf.gestao;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.bi.ConfiguracaoRankingProfessores;
import br.com.pacto.bean.bi.DashboardBI;
import br.com.pacto.bean.bi.ProfessorRankingIndicador;
import br.com.pacto.bean.professor.ProfessorSintetico;
import br.com.pacto.controller.json.biapp.BiAppDTO;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public interface BiAppService {

    Map<String, CacheUsamAppDTO> chaveEmails = new ConcurrentHashMap<>();

    void processarDashAlunosComApp(final String key, final Integer empresa, DashboardBI dash, int idProfessor, boolean forcarAtualizacao, Boolean vinculoProfessor);

    BiAppDTO biApp(final String key, final Integer empresa, boolean forcarAtualizacao) throws Exception;

    List<Map<String, String>> listaUsuariosApp(String key,
                                               String filter,
                                               Integer empresa,
                                               Integer idProfessor,
                                               boolean ativosInstalados,
                                               boolean inativosInstalados,
                                               boolean naoInstalados,
                                               boolean somenteAtivos, PaginadorDTO paginadorDTO) throws Exception;

    void processarRankingAlunosComApp(final String key, ProfessorSintetico professorSintetico,
                                      final List<ProfessorRankingIndicador> listaItems,
                                      final List<ConfiguracaoRankingProfessores> configs,
                                      Date inicio,
                                      Date fim, Integer empresazw);

}
