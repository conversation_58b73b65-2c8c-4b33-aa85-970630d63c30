package br.com.pacto.service.intf.gestao;


import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.bi.DashboardBI;
import br.com.pacto.bean.bi.IndicadorDashboardEnum;
import br.com.pacto.controller.json.aluno.AlunoSimplesDTO;
import br.com.pacto.controller.json.gestao.BITreinoAgendaDTO;
import br.com.pacto.controller.json.gestao.BITreinoCarteiraDTO;
import br.com.pacto.controller.json.gestao.BITreinoResponseDTO;
import br.com.pacto.controller.json.gestao.BITreinoTreinamentoDTO;
import br.com.pacto.enumerador.agenda.StatusAgendamentoEnum;
import br.com.pacto.enumerador.agenda.TipoAgendamentoEnum;
import br.com.pacto.service.exception.ServiceException;
import org.json.JSONException;
import org.json.JSONObject;


import javax.servlet.http.HttpServletRequest;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;


public interface BITreinoService {

    public static final String SERVICE_NAME = "BITreinoService";

    void processar(Integer idProfessor, Integer empresaId) throws ServiceException;

    BITreinoResponseDTO gerarBI(Integer idProfessor, Integer empresaId) throws ServiceException;

    BITreinoCarteiraDTO biCarteira(Integer idProfessor, Integer empresaId) throws ServiceException;

    BITreinoTreinamentoDTO treinamento(Integer idProfessor, Integer empresaId) throws ServiceException;

    BITreinoAgendaDTO agenda(Integer idProfessor, Integer empresaId) throws ServiceException;

    List<Map<String, Object>> listaBI(Integer idProfessor, Integer empresaId, IndicadorDashboardEnum indicador, PaginadorDTO paginadorDTO, String filter) throws ServiceException;

    List<Map<String, Object>> listaBIRequest(Integer idProfessor, Integer empresaId, IndicadorDashboardEnum indicador, PaginadorDTO paginadorDTO, String filter, HttpServletRequest request) throws ServiceException;

    List<LinkedHashMap<String, Object>> listaBILinkedMap(Integer idProfessor, Integer empresaId, IndicadorDashboardEnum indicador, PaginadorDTO paginadorDTO, String filter) throws ServiceException, JSONException;

    List<AlunoSimplesDTO> listAlunosExecucaoTreinoUltimosDias(Integer idProfessor, Integer empresaId, Integer dia, String periodido, JSONObject filters, PaginadorDTO paginadorDTO) throws Exception;

    List<Map<String, Object>> listaAcessos(Integer idProfessor, Integer empresaId, Long dia, String tipo) throws ServiceException;

    DashboardBI obterDash(Integer idProfessor, Integer empresaId) throws ServiceException;

    DashboardBI obterDash(String ctx) throws ServiceException;

    DashboardBI atualizarDash(Integer idProfessor, Integer empresaId) throws ServiceException;

    List<Map<String, Object>> listaDisponibilidades(Integer empresaId,
                                                     Integer idProfessor,
                                                     IndicadorDashboardEnum indicadorSelecionado,
                                                     StatusAgendamentoEnum statusAg,
                                                     TipoAgendamentoEnum tipoAg,
                                                    String filter) throws ServiceException;

    Integer codigoProfessor(Integer idProfessor, Integer empresa, Integer pessoa) throws ServiceException;
    DashboardBI obterBiTreinamento(Integer idProfessor, Integer empresaId, String ctx) throws ServiceException;

    void importarAlunosForaTreino(Integer idProfessor, Integer empresaId) throws ServiceException;
}
