package br.com.pacto.service.intf.crossfit;

import br.com.pacto.bean.cliente.Prospect;
import br.com.pacto.bean.crossfit.EquipeEvento;
import br.com.pacto.bean.crossfit.EventoCrossfit;
import br.com.pacto.bean.crossfit.ParticipanteEquipeEvento;
import br.com.pacto.bean.crossfit.PontuacaoGameCrossfit;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.bean.wod.NivelCrossfitEnum;
import br.com.pacto.bean.wod.ScoreTreino;
import br.com.pacto.bean.wod.Wod;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.exception.ValidacaoException;

import java.util.List;
import java.util.Map;

public interface EventoCrossfitService {

    public static final String SERVICE_NAME = "EventoCrossfitService";

    public EventoCrossfit inserir(final String ctx, EventoCrossfit object) throws Exception;

    public EquipeEvento inserirEquipe(final String ctx, EquipeEvento object, List<ParticipanteEquipeEvento> participantes) throws Exception;

    public EventoCrossfit obterPorId(final String ctx, Integer id) throws Exception;

    public EventoCrossfit alterar(final String ctx, EventoCrossfit object) throws Exception;

    public EquipeEvento alterarEquipe(final String ctx, EquipeEvento object, List<ParticipanteEquipeEvento> participantes) throws Exception;

    public void excluir(final String ctx, EventoCrossfit object) throws Exception;

    public List<EventoCrossfit> obterTodos(final String ctx) throws Exception;

    public List<ScoreTreino> resultadosGamePorNivel(final String key, final EventoCrossfit game, final NivelCrossfitEnum n, final Wod wod) throws Exception;

    public List<EquipeEvento> equipes(final String ctx, EventoCrossfit game) throws Exception;

    public Map<String, List<EquipeEvento>> mapaEquipes(final String ctx, EventoCrossfit game, List<Wod> wods) throws Exception;

    public List<PontuacaoGameCrossfit> gerarPontuacao(final String ctx,
                                                      final Integer pontosPrimeiro,
                                                      final Integer passo,
                                                      final Integer colocado,
                                                      final NivelCrossfitEnum nivel) throws Exception;

    public void gravarPontuacao(final String ctx,
                                EventoCrossfit game,
                                Map<NivelCrossfitEnum, List<PontuacaoGameCrossfit>> pontuacoes) throws Exception;

    public Map<NivelCrossfitEnum, List<PontuacaoGameCrossfit>> obterMapasPontuacoes(final String ctx, final EventoCrossfit game) throws Exception;

    public void gerarParcelaCapitao(final String key, EquipeEvento equipe, EventoCrossfit game, Usuario usuario) throws Exception;

    public Prospect gravarProspect(final String key, Prospect prospect) throws Exception;

    public void gravarResultados(final String key, List<ScoreTreino> scoreTreinos) throws Exception;

}
