/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.pacto.service.intf.creditopersonal;

import br.com.pacto.bean.gestaopersonal.AulaPersonal;
import br.com.pacto.bean.gestaopersonal.CreditoPersonal;
import br.com.pacto.bean.gestaopersonal.TipoOperacaoPersonalEnum;
import br.com.pacto.service.exception.ServiceException;
import java.util.Date;
import java.util.List;
import java.util.Map;



/**
 *
 * <AUTHOR>
 */
public interface CreditoPersonalService {

    public static final String SERVICE_NAME = "CreditoPersonalService";

    public CreditoPersonal inserir(final String ctx, CreditoPersonal object) throws ServiceException;

    public CreditoPersonal obterPorId(final String ctx, Integer id) throws ServiceException;

    public CreditoPersonal alterar(final String ctx, CreditoPersonal object) throws ServiceException;

    public void excluir(final String ctx, CreditoPersonal object) throws ServiceException;

    public List<CreditoPersonal> obterTodos(final String ctx) throws ServiceException;

    public List<CreditoPersonal> obterPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException;

    public List<CreditoPersonal> obterPorParam(final String ctx, String query,
            Map<String, Object> params, int max, int index)
            throws ServiceException;

    public CreditoPersonal obterObjetoPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException;
    
    public Integer obterSaldoAtualPersonal(final String ctx, Integer codigoPersonal, Date horaLimite )
            throws ServiceException;
    
    public List<CreditoPersonal> obterListaSaldo(String ctx, Integer codigoProfessor, Date data) throws ServiceException;
    
    public void verificarExpirados(String ctx, Integer codigoProfessor, Date data) throws ServiceException;
    
    public CreditoPersonal inserirCreditos(final String key,final Integer colaborador,
            final Integer unidadesCreditos,final Integer recibozw,final Integer vendaZW, TipoOperacaoPersonalEnum tipo, Date dataExpiracao) throws ServiceException;
    
    public CreditoPersonal obterCredito(String ctx, AulaPersonal aula) throws ServiceException;
    
    public CreditoPersonal obterCreditoRecibo(String ctx, Integer recibo) throws ServiceException;
    
    public void estornarCreditos(final String key, final Integer reciboZW, final Integer vendaZW) throws ServiceException;
    
    public CreditoPersonal obterCreditoVendaZW(String ctx, Integer vendaZW) throws ServiceException;
    
    public void expirarCreditos(final String ctx, final Date data) throws ServiceException;

}
