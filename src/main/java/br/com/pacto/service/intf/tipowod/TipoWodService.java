/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.intf.tipowod;

import br.com.pacto.bean.tipowod.TipoWod;
import br.com.pacto.bean.wod.FiltroTipoWodJSON;
import br.com.pacto.bean.wod.TipoWodResponseTO;
import br.com.pacto.bean.wod.TipoWodTO;
import br.com.pacto.service.exception.ServiceException;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public interface TipoWodService {
    
    public static final String SERVICE_NAME = "TipoWodService";
    
    public TipoWod gravarTipoWod(final String key, final TipoWod tipoWod) throws ServiceException;
    
    public List<TipoWod> obterTodas(final String key) throws ServiceException;
 
    public void excluirTipoWod(final String key, final TipoWod tipoWod) throws ServiceException;

    public TipoWod obterPorNome(final  String ctx,final String nome) throws ServiceException;
    
    public TipoWod obterPorCodigo(final  String ctx, final Integer codigo) throws ServiceException;

    List<TipoWodResponseTO> listarTiposWod(FiltroTipoWodJSON filtros) throws ServiceException;

    TipoWodResponseTO buscarTiposWod(Integer id) throws ServiceException;

    TipoWodResponseTO cadastrarTipoWod(TipoWodTO tipoWodTO) throws ServiceException;

    TipoWodResponseTO atualizarTipoWod(TipoWodTO tipoWodTO) throws ServiceException;

    void removerTipoWod(Integer id) throws ServiceException;
}
