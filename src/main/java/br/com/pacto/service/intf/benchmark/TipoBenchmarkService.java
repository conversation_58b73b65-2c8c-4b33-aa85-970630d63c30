package br.com.pacto.service.intf.benchmark;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.benchmark.OrigemTipoBenchmark;
import br.com.pacto.bean.benchmark.TipoBenchmark;
import br.com.pacto.bean.benchmark.TipoBenchmarkResponseTO;
import br.com.pacto.bean.benchmark.TipoBenchmarkTO;
import br.com.pacto.controller.json.crossfit.FiltroTipoBenchmarkJSON;
import br.com.pacto.service.exception.ServiceException;

import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON> on 12/07/2016.
 */
public interface TipoBenchmarkService {

    public static final String SERVICE_NAME = "TipoBenchmarkService";

    public TipoBenchmark inserir(final String ctx, TipoBenchmark object) throws ServiceException;

    public TipoBenchmark obterPorId(final String ctx, Integer id) throws ServiceException;

    public TipoBenchmark alterar(final String ctx, TipoBenchmark object) throws ServiceException;

    public void excluir(final String ctx, TipoBenchmark object) throws ServiceException;

    public List<TipoBenchmark> obterTodos(final String ctx, OrigemTipoBenchmark origem) throws ServiceException;

    public List<TipoBenchmark> obterPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException;

    public List<TipoBenchmark> obterPorParam(final String ctx, String query,
                                         Map<String, Object> params, int max, int index)
            throws ServiceException;

    public TipoBenchmark obterObjetoPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException;

    List<TipoBenchmarkResponseTO> consultarTipoBenchmark(FiltroTipoBenchmarkJSON filtros, PaginadorDTO paginadorDTO)throws ServiceException;
    TipoBenchmarkResponseTO inserir(TipoBenchmarkTO tipoBenchmarkTO)throws ServiceException;
    TipoBenchmarkResponseTO consultar(Integer id)throws ServiceException;
    TipoBenchmarkResponseTO alterar(TipoBenchmarkTO tipoBenchmarkTO)throws ServiceException;
    void excluir(Integer id)throws ServiceException;

    List<TipoBenchmarkResponseTO> consultarTodos() throws ServiceException;

}
