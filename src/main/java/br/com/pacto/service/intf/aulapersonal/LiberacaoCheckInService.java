/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.pacto.service.intf.aulapersonal;

import br.com.pacto.bean.gestaopersonal.AulaPersonal;
import br.com.pacto.bean.gestaopersonal.LiberacaoCheckIn;
import br.com.pacto.service.exception.ServiceException;
import java.util.List;
import java.util.Map;



/**
 *
 * <AUTHOR>
 */
public interface LiberacaoCheckInService {

    public static final String SERVICE_NAME = "LiberacaoCheckInService";

    public LiberacaoCheckIn inserir(final String ctx, LiberacaoCheckIn object) throws ServiceException;

    public LiberacaoCheckIn obterPorId(final String ctx, Integer id) throws ServiceException;

    public LiberacaoCheckIn alterar(final String ctx, LiberacaoCheckIn object) throws ServiceException;

    public void excluir(final String ctx, LiberacaoCheckIn object) throws ServiceException;

    public List<LiberacaoCheckIn> obterTod<PERSON>(final String ctx) throws ServiceException;

    public List<LiberacaoCheckIn> obterPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException;

    public List<LiberacaoCheckIn> obterPorParam(final String ctx, String query,
            Map<String, Object> params, int max, int index)
            throws ServiceException;

    public LiberacaoCheckIn obterObjetoPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException;
    
    public LiberacaoCheckIn obterLiberacao(String ctx, AulaPersonal aula) throws ServiceException;

}
