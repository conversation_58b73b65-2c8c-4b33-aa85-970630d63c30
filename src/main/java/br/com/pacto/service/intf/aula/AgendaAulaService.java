/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.intf.aula;

import br.com.pacto.bean.aula.AulaDia;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.util.bean.FiltrosAgendaTO;
import java.util.Date;
import java.util.List;
import java.util.Map;
import br.com.pacto.controller.to.ScheduleModel;

/**
 *
 * <AUTHOR>
 */
public interface AgendaAulaService {
    
    public static final String SERVICE_NAME = "AgendaAulaService";
    
    public Map<Date, List<AulaDia>> montarAgenda(String ctx, ScheduleModel eventModel, Usuario usuario, FiltrosAgendaTO filtros,
            Map<Date, List<AulaDia>> aulas) throws ServiceException;
    
}
