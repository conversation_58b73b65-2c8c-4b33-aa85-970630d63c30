package br.com.pacto.service.intf.cliente;

import java.util.ArrayList;
import java.util.List;

public class RetrospectivaAnoVO {
    private Integer quantidadeTreinosNoAno = 0;
    private Integer quantidadeDeAtividadesConcluidas = 0;
    private Integer quantidadeDePesoLevantado = 0;
    private List<String> top3Atividades = new ArrayList<>();
    private Integer quantidadeDeCaloriasQueimadas = 0;
    private Integer totalDeAulasMarcadas = 0;
    private String aulaFavorita = "";
    private Integer totalDeAvaliacoesFisicas = 0;
    private Double pesoEntreAvaliacoes = 0.0;
    private Integer wodsRealizados = 0;
    private Integer tempoTotalWods = 0;
    private Integer posicaoRanking3lugar = 0;
    private Integer posicaoRanking2lugar = 0;
    private Integer posicaoRanking1lugar = 0;
    private Integer diasFrequentados = 0;
    private Integer qtdTreinosManha = 0;
    private Integer qtdTreinosTarde = 0;
    private Integer qtdTreinosNoite = 0;

    public Integer getQuantidadeTreinosNoAno() {
        return quantidadeTreinosNoAno;
    }

    public void setQuantidadeTreinosNoAno(Integer quantidadeTreinosNoAno) {
        this.quantidadeTreinosNoAno = quantidadeTreinosNoAno;
    }

    public Integer getQuantidadeDeAtividadesConcluidas() {
        return quantidadeDeAtividadesConcluidas;
    }

    public void setQuantidadeDeAtividadesConcluidas(Integer quantidadeDeAtividadesConcluidas) {
        this.quantidadeDeAtividadesConcluidas = quantidadeDeAtividadesConcluidas;
    }

    public Integer getQuantidadeDePesoLevantado() {
        return quantidadeDePesoLevantado;
    }

    public void setQuantidadeDePesoLevantado(Integer quantidadeDePesoLevantado) {
        this.quantidadeDePesoLevantado = quantidadeDePesoLevantado;
    }

    public List<String> getTop3Atividades() {
        return top3Atividades;
    }

    public void setTop3Atividades(List<String> top3Atividades) {
        this.top3Atividades = top3Atividades;
    }

    public Integer getQuantidadeDeCaloriasQueimadas() {
        return quantidadeDeCaloriasQueimadas;
    }

    public void setQuantidadeDeCaloriasQueimadas(Integer quantidadeDeCaloriasQueimadas) {
        this.quantidadeDeCaloriasQueimadas = quantidadeDeCaloriasQueimadas;
    }

    public Integer getTotalDeAulasMarcadas() {
        return totalDeAulasMarcadas;
    }

    public void setTotalDeAulasMarcadas(Integer totalDeAulasMarcadas) {
        this.totalDeAulasMarcadas = totalDeAulasMarcadas;
    }

    public String getAulaFavorita() {
        return aulaFavorita;
    }

    public void setAulaFavorita(String aulaFavorita) {
        this.aulaFavorita = aulaFavorita;
    }
    public Integer getTotalDeAvaliacoesFisicas() {
        return totalDeAvaliacoesFisicas;
    }

    public void setTotalDeAvaliacoesFisicas(Integer totalDeAvaliacoesFisicas) {
        this.totalDeAvaliacoesFisicas = totalDeAvaliacoesFisicas;
    }

    public Double getPesoEntreAvaliacoes() {
        return pesoEntreAvaliacoes;
    }

    public void setPesoEntreAvaliacoes(Double pesoEntreAvaliacoes) {
        this.pesoEntreAvaliacoes = pesoEntreAvaliacoes;
    }

    public Integer getWodsRealizados() {
        return wodsRealizados;
    }

    public void setWodsRealizados(Integer wodsRealizados) {
        this.wodsRealizados = wodsRealizados;
    }

    public Integer getTempoTotalWods() {
        return tempoTotalWods;
    }

    public void setTempoTotalWods(Integer tempoTotalWods) {
        this.tempoTotalWods = tempoTotalWods;
    }

    public Integer getPosicaoRanking3lugar() {
        return posicaoRanking3lugar;
    }

    public void setPosicaoRanking3lugar(Integer posicaoRanking3lugar) {
        this.posicaoRanking3lugar = posicaoRanking3lugar;
    }

    public Integer getPosicaoRanking2lugar() {
        return posicaoRanking2lugar;
    }

    public void setPosicaoRanking2lugar(Integer posicaoRanking2lugar) {
        this.posicaoRanking2lugar = posicaoRanking2lugar;
    }

    public Integer getPosicaoRanking1lugar() {
        return posicaoRanking1lugar;
    }

    public void setPosicaoRanking1lugar(Integer posicaoRanking1lugar) {
        this.posicaoRanking1lugar = posicaoRanking1lugar;
    }

    public Integer getDiasFrequentados() {
        return diasFrequentados;
    }

    public void setDiasFrequentados(Integer diasFrequentados) {
        this.diasFrequentados = diasFrequentados;
    }

    public Integer getQtdTreinosManha() {
        return qtdTreinosManha;
    }

    public void setQtdTreinosManha(Integer qtdTreinosManha) {
        this.qtdTreinosManha = qtdTreinosManha;
    }

    public Integer getQtdTreinosTarde() {
        return qtdTreinosTarde;
    }

    public void setQtdTreinosTarde(Integer qtdTreinosTarde) {
        this.qtdTreinosTarde = qtdTreinosTarde;
    }

    public Integer getQtdTreinosNoite() {
        return qtdTreinosNoite;
    }

    public void setQtdTreinosNoite(Integer qtdTreinosNoite) {
        this.qtdTreinosNoite = qtdTreinosNoite;
    }
}
