package br.com.pacto.service.intf.gestao;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.controller.json.agendamento.*;
import org.json.JSONObject;

import java.util.List;

public interface BIAgendaService {

    BiAgendaDTO obterBI(JSONObject filtros, Integer idEmpresa) throws Exception;

    List<ItemListagemAulasDTO> listagemAulas(JSONObject filtros, PaginadorDTO paginadorDTO, Integer empresaId) throws Exception;

    List<ItemListagemBonusDTO> listagemBonusProfessor(JSONObject filtros, PaginadorDTO paginadorDTO, Integer empresaId) throws Exception;

    List<ItemListagemBonusDTO> listagemBonus(JSONObject filtros, PaginadorDTO paginadorDTO, Integer empresaId) throws Exception;

    List<ItemListagemModalidadesDTO> listagemModalidades(JSONObject filtros, PaginadorDTO paginadorDTO, Integer empresaId) throws Exception;

    List<ItemListagemProfessoresDTO> listagemProfessores(JSONObject filtros, PaginadorDTO paginadorDTO, Integer empresaId) throws Exception;

    List<ItemListagemFrequenciaAlunosDTO> listagemFrequenciaAlunos(JSONObject filtros, PaginadorDTO paginadorDTO, Integer empresaId) throws Exception;

    List<ItemListagemPresencasAlunosDTO> listagemPresenca(JSONObject filtros, PaginadorDTO paginadorDTO, Integer empresaID) throws Exception;

    JSONObject chamadaZW(String ctx,
                                String endpoint,
                                PaginadorDTO paginadorDTO,
                                JSONObject filtros, Integer empresa, Integer professor) throws Exception;
}
