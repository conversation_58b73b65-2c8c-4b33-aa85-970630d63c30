package br.com.pacto.service.intf.agendatotal;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "DTO que representa os cards de agenda, contendo uma lista de dias e os respectivos itens de horário.")
public class AgendaCardsDTO {

    @ApiModelProperty(value = "Lista de dias, no formato YYYYMMDD", dataType = "List<String>")
    private List<String> dias;

    @ApiModelProperty(value = "Lista dos itens de horário da agenda", dataType = "List<AgendaHorarioDocumentacaoDTO>", required = true)
    private List<AgendaHorarioDTO> itens;

    public List<String> getDias() {
        return dias;
    }

    public void setDias(List<String> dias) {
        this.dias = dias;
    }

    public List<AgendaHorarioDTO> getItens() {
        return itens;
    }

    public void setItens(List<AgendaHorarioDTO> itens) {
        this.itens = itens;
    }
}
