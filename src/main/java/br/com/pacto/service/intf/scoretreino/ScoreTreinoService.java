package br.com.pacto.service.intf.scoretreino;

import br.com.pacto.bean.wod.ScoreTreino;
import br.com.pacto.service.exception.ServiceException;

import java.util.List;
import java.util.Map;

/**
 * Created by rafaelc on 09/06/2017.
 */
public interface ScoreTreinoService {


    public ScoreTreino obterPorId(final String ctx, Integer id) throws ServiceException;

    public List<ScoreTreino> obterPorParam(final String ctx, String query,
            Map<String, Object> param)
            throws ServiceException;
}
