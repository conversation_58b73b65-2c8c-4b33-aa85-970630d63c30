package br.com.pacto.service.intf.login;

import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.bean.usuarioEmail.UsuarioEmail;
import br.com.pacto.security.dto.AlterarEmailDTO;
import br.com.pacto.security.dto.AlterarSenhaDTO;
import br.com.pacto.security.dto.AlterarTelefoneDTO;
import br.com.pacto.security.dto.AlterarUsuarioGeralDTO;
import br.com.pacto.security.dto.DesvincularUsuarioDTO;
import br.com.pacto.security.dto.UsuarioSimplesDTO;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.login.TokenDTO;

import javax.servlet.http.HttpServletRequest;
import java.sql.Connection;

public interface UsuarioGeralService {

    public static final String SERVICE_NAME = "usuarioGeralService";

    void alterarSenha(String ctx, AlterarSenhaDTO dto) throws Exception;

    void alterarEmail(String ctx, AlterarEmailDTO dto) throws Exception;

    void alterarTelefone(String ctx, AlterarTelefoneDTO dto) throws Exception;

    void alterarUsuarioGeral(String ctx, AlterarUsuarioGeralDTO dto) throws Exception;

    TokenDTO solicitarTrocaEmail(String chave, Integer idUsuario, boolean viaCodigoEmail, boolean enviarLinkEmail,
                                 UsuarioEmail usuarioEmailNovo, Integer empresaSolicitante,
                                 String ipCliente) throws Exception;

    void validarToken(String chave, Integer idUsuario, Integer empresaSolicitante, TokenDTO tokenDTO,
                      UsuarioSimplesDTO usuarioResponsavelVO, String ipCliente, String operacao) throws Exception;

    String enviarEmailNovoUsuario(String ctx, Integer codigoUsuario, boolean definirSenha, boolean enviarCodigo,
                                  Empresa empresa, UsuarioSimplesDTO usuarioSimplesDTO, String ipCliente) throws Exception;

    void atualizarUsuarioGeral(String ctx, Integer codigoUsuario, Empresa empresaSolicitante,
                               UsuarioSimplesDTO usuarioSimplesDTO, String ipCliente) throws Exception;

    void processarUsuarioGeral(final String ctx, final Integer codigo, boolean novoUsuario, boolean definirSenha,
                               boolean exception, HttpServletRequest request) throws Exception;

    void solicitarTrocaSenha(String chave, Integer idUsuario, Integer empresaSolicitante, UsuarioSimplesDTO usuarioResponsavel, String ip) throws Exception;

    Usuario obterUsuario(String ctx, Integer codigoUsuarioTW,
                         Integer codigoUsuarioZW, String username) throws Exception;

    void desvincularUsuario(DesvincularUsuarioDTO dto) throws ServiceException;

    void desvincularUsuarioNovoLogin(String chave, Integer idUsuario, Integer empresaSolicitante, UsuarioSimplesDTO usuarioAtual, String ip) throws ServiceException;
}
