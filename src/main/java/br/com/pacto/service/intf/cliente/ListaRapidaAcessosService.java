package br.com.pacto.service.intf.cliente;

import br.com.pacto.controller.json.aluno.ConfigListaRapidaAcessoDTO;
import br.com.pacto.controller.json.aluno.ListaRapidaAcessoDTO;
import br.com.pacto.controller.json.aluno.PendenciasAcessoEnum;
import br.com.pacto.controller.json.aluno.TipoListaAcessoEnum;
import br.com.pacto.service.exception.ServiceException;

import java.util.List;

public interface ListaRapidaAcessosService {

    ConfigListaRapidaAcessoDTO listaUltimosAcessos(Integer empresa, TipoListaAcessoEnum tipo, Integer limit) throws ServiceException;

    ConfigListaRapidaAcessoDTO listaUltimosAcessos(Integer empresa, TipoListaAcessoEnum tipo, Integer limit, boolean cache) throws ServiceException;

    void gravarCfgNotificacoesAcessosUsuarios(PendenciasAcessoEnum cfg<PERSON><PERSON><PERSON><PERSON>, Boolean valor) throws ServiceException;
}
