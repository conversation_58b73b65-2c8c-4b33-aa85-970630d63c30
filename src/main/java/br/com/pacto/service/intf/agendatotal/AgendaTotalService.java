/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.intf.agendatotal;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.aula.Modalidade;
import br.com.pacto.bean.aula.ProfessorSubstituido;
import br.com.pacto.bean.aula.TipoAulaCheiaOrigemEnum;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.aulaDia.AulaConfirmadaVO;
import br.com.pacto.controller.json.aulaDia.AulaDiaJSON;
import br.com.pacto.controller.json.aulaDia.PassivoDTO;
import br.com.pacto.controller.json.aulaDia.SaldoAulaColetivaVO;
import br.com.pacto.controller.json.turma.TurmaVideoDTO;
import br.com.pacto.controller.to.ScheduleModel;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.util.bean.AgendaTotalTO;
import br.com.pacto.util.bean.AgendadoTO;
import br.com.pacto.util.bean.GenericoTO;
import br.com.pacto.util.bean.LinhaAgendaTO;
import br.com.pacto.util.bean.LinhaSemanaTO;
import br.com.pacto.util.enumeradores.OrigemSistemaEnum;
import br.com.pacto.util.enumeradores.TipoAgrupamentoAgendaEnum;
import br.com.pacto.util.json.AgendaTotalJSON;
import br.com.pacto.util.json.ControleCreditoTreinoJSON;
import org.json.JSONArray;
import org.json.JSONObject;

import javax.faces.model.SelectItem;
import javax.servlet.http.HttpServletRequest;
import java.sql.Connection;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public interface AgendaTotalService {

    public static final String SERVICE_NAME = "AgendaTotalService";

    void montarAgenda(String ctx, ScheduleModel eventModel, Date inicio, Date fim, Integer empresa, Integer modalidade, String search) throws Exception;

    List<Modalidade> todasModalidades(String ctx) throws ServiceException;

    Map<String,AgendaTotalTO> montarAgenda(String ctx, Date inicio, Date fim, Integer empresa,
                                           Integer modalidade, TipoAulaCheiaOrigemEnum tipo, String search, String situacaoHorario) throws Exception;

    Map<String, List<AgendadoTO>> montarMapaAgendados(String ctx, Date inicio, Date fim, Integer empresa,
                                                      List<AgendaTotalTO> agendamentos, boolean armazenarTurma) throws Exception;

    String inserirNaFilaDeEspera(String ctx, Integer codigoHorarioTurma, String dia, Integer codigoAluno) throws Exception;

    String removerDaFilaDeEspera(String ctx, Integer codigoHorarioTurma, String dia, Integer codigoAluno) throws Exception;

    String removerDaFilaDeEsperaV2(String ctx, Integer codigoHorarioTurma, String dia, Integer codigoAluno, Integer codigoUsuario) throws Exception;

    String removerDaFilaDeEsperaTurmaCrm(String ctx, Integer codigoHorarioTurma, Integer codigoAluno, Integer passivo) throws ServiceException;

    String consultarFila(String ctx, Integer codigoHorarioTurma, String dia, Integer codigoAluno) throws Exception;


    String inserirAlunoAulaCheiaConversasIA(String ctx, Integer codigoCliente, Integer idHorarioTurma,
                                            Date data, OrigemSistemaEnum origem, Usuario usuario, Integer empresaZw,
                                            boolean aulaExperimental, Boolean somenteValidar) throws ServiceException;

    String inserirAlunoAulaCheia(String ctx, Integer codigoCliente, Integer idHorarioTurma, Date data,
                                 OrigemSistemaEnum origem, Usuario usuario, Integer empresa, boolean aulaExperimental, Boolean somenteValidar,
                                 String bookingId) throws ServiceException;

    String inserirAutorizadoAulaCheiaMatricula(String ctx,
                                               String matricula,
                                               String chaveAluno,
                                               Integer idHorarioTurma, Date data,
                                               OrigemSistemaEnum origem, Usuario usuario, Integer empresa) throws ServiceException;

    String inserirAutorizadoAulaCheia(String ctx, Integer codigoAutorizado, Integer idHorarioTurma, Date data,
                                      OrigemSistemaEnum origem, Usuario usuario, Integer empresa) throws ServiceException;

    String excluirAlunoAulaCheia(String ctx, Integer codigoCliente, Integer codigoPassivo, Integer idHorarioTurma,
                                 Date data, OrigemSistemaEnum origem,Integer usuario, Integer empresa, Integer codUsuarioTreino) throws ServiceException;

    String desmarcarAluno(String ctx, Integer codigoCliente,
                          Integer idHorarioTurma, Date data, Integer codigoContrato, OrigemSistemaEnum origem,
                          Integer usuario, Integer empresa, boolean isExperimental, String justificativa) throws ServiceException;

    List<LinhaAgendaTO> montarAgendaHoras(Date data, Map<String, AgendaTotalTO> mapaAgenda,
                                          List<GenericoTO> tipos,
                                          List<Integer> horas,
                                          TipoAgrupamentoAgendaEnum agrupamento,
                                          Map<Integer, GenericoTO> mapaLocais,
                                          Map<Integer, GenericoTO> mapaTipos,
                                          Integer responsavelSelecionado);

    List<LinhaSemanaTO> montarAgendaSemana(Date inicio, Date fim,
                                           Map<String, AgendaTotalTO> mapaAgenda, List<Integer> diasSemana,
                                           List<Integer> horas,
                                           Map<Integer, GenericoTO> mapaLocais,
                                           Map<Integer, GenericoTO> mapaTipos,
                                           Integer responsavelSelecionado);

    String reporAula(String ctx,
                     Integer codigoCliente,
                     Integer idHorarioTurmaOrigem,
                     Integer idHorarioTurmaReposicao,
                     Integer codigoContrato,
                     OrigemSistemaEnum origem,
                     Integer usuario,
                     Integer empresa,
                     Date dataOrigem,
                     Date dataReposicao,
                     boolean forcarMarcar) throws ServiceException;



    AgendaTotalTO obterAulaOrigem(String ctx, AgendaTotalTO agendamentoReposicao,
                                  Map<String, AgendaTotalTO> agenda,
                                  Map<String, List<AgendadoTO>> mapaAgendados, AgendadoTO agendado) throws Exception;

    List<Integer> modalidadesContrato(String ctx, Integer contrato, Integer matricula);

    String modalidadesTitular(String ctx, Integer matricula);

    void baixarFotosAlunos(String ctx, HttpServletRequest request) throws Exception;

    String obterProximasAulas(String ctx, Integer matricula, Boolean proximos30dias) throws Exception;

    String obterProximasAulasVerificandoModoConsulta(String ctx, Integer matricula, Boolean proximos30dias) throws Exception;

    String marcarDesmarcar(String ctx, Integer matricula, Date data,
                           Integer codigoHorarioTurma, boolean marcar, boolean validarParcelaVencida,
                           boolean ProibirMarcarAulaAntesPagamentoPrimeiraParc, Integer contrato) throws Exception;

    String incluirAulaExperimental(String ctx, Integer matricula, Date data, Integer codigoHorarioTurma, Integer usuario, Integer produtoFreePass) throws Exception;

    String gravarPresenca(String ctx, Integer cliente, Date data, Integer codigoHorarioTurma, boolean reposicao, boolean desmarcar,
                          Integer codColaborador, OrigemSistemaEnum origemSistemaEnum) throws Exception;

    List<AgendaTotalJSON> consultarAulasDesmarcadas(String ctx, Integer matricula, Integer modalidade) throws Exception;

    List<AgendaTotalJSON> consultarAulasDiaAluno(String ctx, Integer matricula, Date data, Integer modalidade) throws Exception;

    List<ControleCreditoTreinoJSON> consultarExtratoCreditos(String ctx, Integer matricula, Date data) throws Exception;

    List<AulaDiaJSON> consultarAgendamentosModalidadesAluno(final String key, final Date inicio,
                                                            final Date fim, final Integer matricula, final Integer empresa) throws Exception;

    List<AgendaTotalJSON> consultarTurmasAluno(final String key, final Integer matricula) throws Exception;

    String consultarSaldoAluno(final String key, final Integer matricula, final Integer contrato) throws Exception;

    String consultarSaldoAlunoVerificandoModoConsulta(final String key, final Integer matricula, final Integer contrato) throws Exception;

    List<AgendaTotalJSON> obterAulasColetivas(String ctx, Date inicio, Date fim, Integer empresa) throws Exception;

    List<AgendaTotalJSON> obterAulasColetivas(String ctx, Date inicio, Date fim, Integer empresa, boolean independente) throws Exception;

    List<AgendaTotalJSON> obterAulas(String ctx, Date inicio, Date fim, Integer empresa) throws Exception;

    List<AgendaTotalJSON> obterAulasColetivasDoProfessor(String ctx, Date inicio, Date fim, Integer empresa, Integer codigoProfessor) throws Exception;

    List<AgendaTotalJSON> obterAulasDoProfessor(String ctx, Date inicio, Date fim, Integer empresa, Integer codigoProfessor) throws Exception;

    List<SelectItem> obterListaProfessores(String ctx, Integer empresa, Boolean somenteAtivos) throws Exception;

    Map<Integer, String> obterMapaProfessores(String ctx, Integer empresa, Boolean somenteAtivos, Boolean independente) throws Exception;

    Map<Integer, Modalidade> mapaCorModalidade(String ctx) throws Exception;

    Boolean consultarTemAulaExtra(String ctx, Integer modalidade, Integer contrato, Integer saldo) throws Exception;

    Map<Integer, List<Date>> obterAulasExcluidas(String ctx, Date inicio, Date fim) throws Exception;

    JSONArray produtosFreePass(String ctx, Integer empresa, Boolean somenteAtivos) throws Exception;

    String confirmarAlunoAula(final String ctx,final Integer cliente,
                              final Integer horarioTurma,final String dia,final Integer usuario) throws Exception;

    String presencaAlunoAula(String ctx,
                             Integer cliente,
                             Integer horarioTurma,
                             String dia,
                             Integer codColaborador,
                             OrigemSistemaEnum origem,
                             Boolean confirmar) throws Exception;

    List<Map<String, String>> fotosAlunosAula(final String ctx,
                                              final Integer empresa, final Integer horarioTurma, final String dia) throws Exception;

    List<Map<String, String>> fotosAlunosTurma(String ctx, Integer empresa,
                                               Integer horarioTurma, String dia) throws Exception;

    List<Map<String, String>> fotosAlunosTurma(String ctx, Integer empresa, Integer horarioTurma, String dia, HttpServletRequest request) throws Exception;

    Map<Integer, Map<Date, ProfessorSubstituido>> obterProfessoresSubstituidos(String ctx,
                                                                               Connection con,
                                                                               Date inicio, Date fim) throws Exception;

    ProfessorSubstituido obterProfessorSubstituido(String ctx, Date dataHoje, int codigoHorarioTurma) throws Exception;

    List<AulaDiaJSON> consultarAgendamentosModalidadesAlunoApp(final String key, final Date inicio,
                                                               final Date fim, final Integer matricula, final Integer empresa, final Integer contrato) throws Exception;

    void atualizarSpiviClientID(final String key, final Integer codigoPessoa, final Integer spiviClientID) throws Exception;

    JSONObject obterContratoVigentePorPessoaSintetico(final String key, Integer codigoPessoa, Date dataAtual) throws Exception;

    void excluirAlunoAulaCheiaOutraUnidade(String ctx, Integer matricula, Integer idHorarioTurma,
                                           Date data, String chaveOrigem) throws ServiceException;

    List<String> aulasAutorizado(String ctx, Integer matricula, Date data, String chaveOrigem) throws ServiceException;

    String consultarSaldoAlunoReporEMarcar(String ctx, Integer matricula, Integer contrato) throws Exception;

    List<AulaConfirmadaVO> consultarAulasConfirmadas(String ctx, Integer matricula, String dataInicial, String dataFinal, PaginadorDTO paginadorDTO) throws Exception;

    List<TurmaVideoDTO> obterListaTurmaVideo(String ctx, Integer codigo) throws Exception;

    String inserirNaFilaDeEsperaTurmaCrm(String ctx, Integer codigoHorarioTurma, Integer codigoAluno, PassivoDTO passivo, Integer codigoPassivo) throws ServiceException;

    String removerDaFilaDeEsperaV2(String ctx, Integer codigoHorarioTurma, String dia, Integer codigoAluno);
    void deletarHorarioEquipamentoAluno(String ctx, Integer empresa, Integer codigoHorarioTurma, Integer codigoCliente, Date date) throws ServiceException;

    SaldoAulaColetivaVO consultarSaldoAlunoAulasColetivas(String ctx, Integer matricula);
}
