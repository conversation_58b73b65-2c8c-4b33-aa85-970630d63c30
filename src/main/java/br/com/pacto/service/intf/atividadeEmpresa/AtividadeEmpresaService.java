/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.pacto.service.intf.atividadeEmpresa;

import br.com.pacto.bean.atividade.AtividadeEmpresa;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.exception.ValidacaoException;
import java.util.List;
import java.util.Map;



/**
 *
 * <AUTHOR>
 */
public interface AtividadeEmpresaService {

    public static final String SERVICE_NAME = "AtividadeEmpresa";

    public AtividadeEmpresa inserir(final String ctx, AtividadeEmpresa object) throws ServiceException, ValidacaoException;

    public AtividadeEmpresa obterPorId(final String ctx, Integer id) throws ServiceException;

    public AtividadeEmpresa alterar(final String ctx, AtividadeEmpresa object) throws ServiceException, ValidacaoException;

    public void excluir(final String ctx, AtividadeEmpresa object) throws ServiceException;

    public List<AtividadeEmpresa> obterTodos(final String ctx) throws ServiceException;

    public List<AtividadeEmpresa> obterPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException;

    public List<AtividadeEmpresa> obterPorParam(final String ctx, String query,
            Map<String, Object> params, int max, int index)
            throws ServiceException;

    public AtividadeEmpresa obterObjetoPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException;

}
