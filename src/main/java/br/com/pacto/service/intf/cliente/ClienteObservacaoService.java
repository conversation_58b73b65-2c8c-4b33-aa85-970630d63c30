/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.pacto.service.intf.cliente;

import br.com.pacto.bean.cliente.ClienteObservacao;
import br.com.pacto.controller.json.aluno.HistoricoContatoAlunoVO;

import java.util.List;

/**
 * <AUTHOR>
 * @since 12/09/2018
 */
public interface ClienteObservacaoService {

    /**
     * @param contexto         Contexto da aplicação (chave)
     * @param matriculaCliente Matrícula do cliente para o qual se deseja as observações
     * @return Retorna a lista de observações do cliente que possui a matrícula informada
     * @throws Exception Caso ocorra algum problema durante a consulta
     */
    List<ClienteObservacao> consultarObservacoesPorMatriculaCliente(String contexto, String matriculaCliente) throws Exception;

    /**
     * Exclui uma observação do banco
     *
     * @param contexto     contexto que guarnece o dado
     * @param observacaoId ID da observação
     * @throws Exception Caso haja algum problema ao remover a observação
     */
    void excluir(String contexto, Integer observacaoId) throws Exception;

    List<ClienteObservacao> consultarObservacoesPorMatriculaClienteApp(String contexto, String matriculaCliente, int maxResult, int index) throws Exception;

    List<HistoricoContatoAlunoVO> consultarHistoricoDeContatos(String contexto, String matriculaCliente, Boolean contatoCRM, Boolean observacoes) throws Exception;
}

