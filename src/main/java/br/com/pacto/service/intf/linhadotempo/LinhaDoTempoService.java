/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.intf.linhadotempo;

import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.util.json.FiltrosLinhaTempo;
import br.com.pacto.util.json.LinhaDoTempoTO;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public interface LinhaDoTempoService {
    
    public static final String SERVICE_NAME = "LinhaDoTempoService";
    
    public List<LinhaDoTempoTO> montarLinha(String ctx,
            ClienteSintetico cliente,
            FiltrosLinhaTempo filtros,
            Date inicio,
            Date fim) throws ServiceException;
    
}
