package br.com.pacto.service.intf.venda;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.List;

@JsonInclude
public class VendaAvulsaDTO {
    private String nomeComprador;
    private Boolean colaborador;
    private Integer pessoa;
    private Integer tipo;
    private Long lancamento;
    private Integer parcelas;
    private Long primeiraParcela;
    private String descricaoParcela = "";
    private List<ItemVendaAvulsaDTO> itens;

    public Integer getPessoa() {
        return pessoa;
    }

    public void setPessoa(Integer pessoa) {
        this.pessoa = pessoa;
    }

    public Integer getTipo() {
        return tipo;
    }

    public void setTipo(Integer tipo) {
        this.tipo = tipo;
    }

    public Long getLancamento() {
        return lancamento;
    }

    public void setLancamento(Long lancamento) {
        this.lancamento = lancamento;
    }

    public Integer getParcelas() {
        return parcelas;
    }

    public void setParcelas(Integer parcelas) {
        this.parcelas = parcelas;
    }

    public Long getPrimeiraParcela() {
        return primeiraParcela;
    }

    public void setPrimeiraParcela(Long primeiraParcela) {
        this.primeiraParcela = primeiraParcela;
    }

    public List<ItemVendaAvulsaDTO> getItens() {
        return itens;
    }

    public void setItens(List<ItemVendaAvulsaDTO> itens) {
        this.itens = itens;
    }

    public String getDescricaoParcela() {
        return descricaoParcela;
    }

    public void setDescricaoParcela(String descricaoParcela) {
        this.descricaoParcela = descricaoParcela;
    }

    public String getNomeComprador() {
        return nomeComprador;
    }

    public void setNomeComprador(String nomeComprador) {
        this.nomeComprador = nomeComprador;
    }

    public Boolean getColaborador() {
        return colaborador;
    }

    public void setColaborador(Boolean colaborador) {
        this.colaborador = colaborador;
    }
}
