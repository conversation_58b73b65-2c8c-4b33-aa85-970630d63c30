package br.com.pacto.service.intf.lesao;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.lesao.AlunoLesaoDTO;
import br.com.pacto.bean.lesao.FiltroLesaoJSON;
import br.com.pacto.bean.lesao.IndiceLesaoDTO;
import br.com.pacto.bean.lesao.Lesao;
import br.com.pacto.bean.lesao.LesaoAppDTO;
import br.com.pacto.bean.lesao.LesaoAppVO;
import br.com.pacto.bean.lesao.LesaoDTO;
import br.com.pacto.service.exception.ServiceException;
import org.json.JSONObject;

import java.util.List;

public interface LesaoService {
    public static final String SERVICE_NAME = "LesaoService";

    LesaoDTO gravarLesao(LesaoDTO lesaoDTO, String ctx, Integer codigoUsuario, boolean aluno) throws ServiceException;

    List<LesaoDTO> listarLesao(String ctx, FiltroLesaoJSON filtroLesaoJSON, PaginadorDTO paginadorDTO) throws ServiceException;

    LesaoDTO buscarLesao(Integer id, String ctx) throws ServiceException;

    IndiceLesaoDTO montarIndiceLesao(String ctx, Integer empresaIdZw, Integer ano) throws ServiceException;

    List<AlunoLesaoDTO> listarLesoesBiCross(String ctx, Integer empresaIdZw, JSONObject filtros) throws ServiceException;

    List<LesaoAppVO> consultarLesaoPorCliente(Integer codigoCliente, String ctx) throws ServiceException;

    void excluirLesao(Lesao lesao, String ctx, Integer codigoUsuarioTreino) throws ServiceException;

    LesaoAppVO cadastraLesao(LesaoAppDTO lesaoDTO, String ctx, Integer codigoUsuarioTreino) throws ServiceException;

    LesaoAppVO editarLesao(LesaoAppDTO lesaoAppDTO, String ctx, Integer codigoUsuarioTreino, Integer id) throws ServiceException;

    Lesao consultaLesaoPorId(Integer id, String ctx);
}
