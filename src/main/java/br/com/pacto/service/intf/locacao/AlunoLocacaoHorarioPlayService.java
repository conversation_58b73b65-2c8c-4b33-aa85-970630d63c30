package br.com.pacto.service.intf.locacao;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.locacao.AlunoLocacaoHorarioPlayCheckinCheckoutDTO;
import br.com.pacto.bean.locacao.AlunoLocacaoHorarioPlayDTO;
import br.com.pacto.controller.json.agendamento.AlunoLocacaoPlayDTO;
import br.com.pacto.service.exception.ServiceException;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;
import java.util.Map;

public interface AlunoLocacaoHorarioPlayService {

    Map<String, Object> addAlunoHorarioPlay(AlunoLocacaoHorarioPlayDTO alunoLocacaoHorarioPlayDTO, String ctx) throws ServiceException;

    List<AlunoLocacaoPlayDTO> alunosLocacaoPlay(Integer empresaId, Date dia, Integer locacaoHorario, Integer ambiente, PaginadorDTO paginadorDTO, HttpServletRequest request) throws ServiceException;

    String deleteAlunoLocacaoHorarioPlay(Integer alunoLocacaoHorarioPlayId, String ctx) throws ServiceException;

    String addAlunoHorarioPlayCheckinCheckout(AlunoLocacaoHorarioPlayCheckinCheckoutDTO dto, String ctx) throws ServiceException;
}
