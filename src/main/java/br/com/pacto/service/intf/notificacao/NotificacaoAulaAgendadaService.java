package br.com.pacto.service.intf.notificacao;

import br.com.pacto.bean.cliente.ClienteSintetico;

import java.util.Date;

public interface NotificacaoAulaAgendadaService {
    void salvaNotificacaoAulaAgendada(String ctx, ClienteSintetico cliente, Integer codigoHorarioTurma, String refPush, Date data) throws Exception;

    void cancelaNotificacaoAulaAgendada(String ctx, ClienteSintetico cliente, Integer codigoHorarioTurma, Date data) throws Exception;
}
