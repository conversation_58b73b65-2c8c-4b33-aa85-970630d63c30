package br.com.pacto.service.intf.termoaceite;

import br.com.pacto.controller.json.termoaceite.TermoAceiteAssinaturaDTO;
import br.com.pacto.controller.json.termoaceite.TermoAceiteDTO;

import java.sql.SQLException;
import java.util.List;

public interface TermoAceiteService {

    void save(String termo) throws Exception;

    void saveAssinatura(String ip, String nome, String cpf, String data, Integer termo, String email, Integer codigoMatricula) throws Exception;

    List<TermoAceiteDTO> findAll() throws Exception;

    List<TermoAceiteAssinaturaDTO> findAllAssinados() throws Exception;

    List<TermoAceiteAssinaturaDTO> findByCodigoMatricula(Integer codigoMatricula) throws Exception;

    TermoAceiteDTO encontraTermoPorCodigo(Integer codTermo);

}
