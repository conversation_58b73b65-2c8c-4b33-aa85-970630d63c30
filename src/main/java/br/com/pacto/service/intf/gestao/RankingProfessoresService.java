package br.com.pacto.service.intf.gestao;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.bi.*;
import br.com.pacto.controller.to.FiltrosGestaoTO;
import br.com.pacto.controller.to.RankingProfessoresTO;
import br.com.pacto.controller.json.professor.DetalhesProfessorRankingDTO;
import br.com.pacto.controller.json.professor.FiltroGestaoJSON;
import br.com.pacto.controller.json.professor.InfoProfessorRankingDTO;
import br.com.pacto.controller.json.professor.RankingProfessoresResponseDTO;
import br.com.pacto.service.exception.ServiceException;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;
import java.util.Map;

public interface RankingProfessoresService {
    String SERVICE_NAME = "RankingProfessoresService";

    Map<String, Object> obterDeletarDia(final String key, final Integer mes, final Integer ano, final Integer professor, final FiltrosDashboard filtros, final Date dataProcessamento) throws Exception;

    void deletarDiasGuardados  (String key, Map<String, Object> objectMap)throws Exception;

    void addIndicador(List<ProfessorRankingIndicador> itemRanking,
                      IndicadorDashboardEnum indicador,
                      ConfiguracaoRankingProfessores config,
                      Double valor);

    ConfiguracaoRankingProfessores configIndicador(List<ConfiguracaoRankingProfessores> configs, IndicadorDashboardEnum indicador);

    boolean contemIndicador(List<ConfiguracaoRankingProfessores> configs, IndicadorDashboardEnum ... indicadores);


    List<RankingProfessoresResponseDTO> carregarRankingProfessores(HttpServletRequest request,
                                                                   PaginadorDTO paginadorDTO,
                                                                   Date inicio,
                                                                   Date fim,
                                                                   Integer empresaId,
                                                                   FiltroGestaoJSON filtroGestaoJSON, String sort, Boolean treinoindependente) throws ServiceException;

    List<RankingProfessoresResponseDTO> podium(HttpServletRequest request,
                                                           Date inicio,
                                                           Date fim,
                                                           Integer empresaId,
                                                           FiltroGestaoJSON filtroGestaoJSON, Boolean treinoindependente) throws ServiceException;

    DetalhesProfessorRankingDTO detalhesProfessor(HttpServletRequest request,
                                                           Date inicio,
                                                           Date fim,
                                                           Integer empresaId,
                                                           Integer codigoColaborador,
                                                           FiltroGestaoJSON filtroGestaoJSON, Boolean treinoindependente) throws ServiceException;

    List<InfoProfessorRankingDTO>  detalhesCompartilharProfessor(HttpServletRequest request,
                                                                 Date inicio,
                                                                 Date fim,
                                                                 Integer empresaId,
                                                                 Integer codigoColaborador,
                                                                 FiltroGestaoJSON filtroGestaoJSON, Boolean treinoindependente) throws ServiceException;

    GeracaoRankingProfessores gerarRankingNoBanco(final String key,
                                                         final Date inicio,
                                                         final Date fim,
                                                         boolean treinoIndependente,
                                                         int usuario,
                                                         int codEmpresaZW) throws ServiceException;

    void conferir(Date inicio, Date fim, Integer empresaId, FiltroGestaoJSON filtroGestaoJSON, Boolean treinoindependente) throws ServiceException;

}
