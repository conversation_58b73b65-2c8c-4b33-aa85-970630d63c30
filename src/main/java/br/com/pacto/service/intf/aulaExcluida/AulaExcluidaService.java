package br.com.pacto.service.intf.aulaExcluida;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.aula.AulaDiaExclusao;
import br.com.pacto.controller.json.aulaExcluida.AulaExcluidaDTO;
import br.com.pacto.controller.json.aulaExcluida.FiltroAulaExcluidaJSON;
import br.com.pacto.service.exception.ServiceException;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

public interface AulaExcluidaService {

    String SERVICE_NAME = "AulaExcluidaService";

    AulaDiaExclusao inserir(final String ctx, AulaDiaExclusao object) throws ServiceException;

    AulaDiaExclusao consultarPorAulaExcluida(String ctx, Integer id) throws ServiceException;

    AulaDiaExclusao alterar(String ctx, AulaDiaExclusao aulaDiaExclusao) throws ServiceException;

    void remover(String ctx, AulaDiaExclusao aulaDiaExclusao) throws ServiceException;

    List<AulaExcluidaDTO> listaAulaExcluida(FiltroAulaExcluidaJSON filtros, PaginadorDTO paginadorDTO, Integer empresaId, HttpServletRequest request) throws ServiceException;

    void removerAulaExcluida(Integer id) throws ServiceException;
}
