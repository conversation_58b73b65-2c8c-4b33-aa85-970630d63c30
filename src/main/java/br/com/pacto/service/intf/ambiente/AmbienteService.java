package br.com.pacto.service.intf.ambiente;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.aula.Ambiente;
import br.com.pacto.controller.json.ambiente.*;
import br.com.pacto.service.exception.ServiceException;

import java.util.List;

public interface AmbienteService {

    String SERVICE_NAME = "AmbienteService";

    Ambiente inserir(final String ctx, Ambiente object) throws ServiceException;

    Ambiente consultarPorAmbiente(String ctx, Integer id) throws ServiceException;

    Ambiente consultarPorAmbienteZW(String ctx, Integer codigoZW) throws ServiceException;

    Ambiente alterar(String ctx, Ambiente ambiente) throws ServiceException;

    void remover(String ctx, Ambiente ambiente) throws ServiceException;

    List<Ambiente> consultarPorNome(String ctx, FiltroAmbienteJSON filtros, PaginadorDTO paginadorDTO) throws ServiceException;

    AmbienteResponseTO cadastroAmbiente(AmbienteTO ambienteTO) throws ServiceException;

    List<AmbienteResponseTO> listaAmbientes(FiltroAmbienteJSON filtros, PaginadorDTO paginadorDTO, Integer empresaId) throws ServiceException;

    AmbienteResponseTO alterarAmbiente(Integer id, AmbienteTO ambienteTO) throws ServiceException;

    void removerAmbiente(Integer id) throws ServiceException;

    List<TipoAmbienteResponseTO> obterTodosTiposAmbiente() throws Exception;

    List<AmbienteResponseTO> obterTodos(Integer empresaId, Boolean validarNoTreino) throws ServiceException;

    List<AmbienteResponseTO> obterTodosAtivos(FiltroAmbienteJSON filtros) throws Exception;

    List<ColetorResponseTO> obterColetoresAmbiente() throws Exception;

    List<NivelTurmaResponseTO> obterNiveisTurmaAmbiente() throws Exception;
}
