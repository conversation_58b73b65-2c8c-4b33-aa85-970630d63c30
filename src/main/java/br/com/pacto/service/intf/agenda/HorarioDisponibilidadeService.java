package br.com.pacto.service.intf.agenda;

import br.com.pacto.bean.disponibilidade.HorarioDisponibilidade;
import br.com.pacto.service.exception.ServiceException;

import java.util.List;

public interface HorarioDisponibilidadeService {

    HorarioDisponibilidade obterPorId(Integer id) throws ServiceException;

    HorarioDisponibilidade obterPorId(String ctx, Integer id) throws ServiceException;

    List<HorarioDisponibilidade> consultarHorarioDisponibilidadesNoMesmoNSU(String ctx, Integer nsu) throws ServiceException;
}
