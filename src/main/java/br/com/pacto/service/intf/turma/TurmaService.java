package br.com.pacto.service.intf.turma;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.controller.json.turma.AmbienteDTO;
import br.com.pacto.controller.json.turma.HorarioTurmaResponseDTO;
import br.com.pacto.controller.json.turma.NivelTurmaDTO;
import br.com.pacto.controller.json.turma.TurmaResponseDTO;
import br.com.pacto.service.exception.ServiceException;
import org.json.JSONObject;

import java.util.List;

public interface TurmaService {
    
    public static final String SERVICE_NAME = "TurmaService";

    TurmaResponseDTO save(TurmaResponseDTO turmaDTO) throws Exception;

    TurmaResponseDTO update(TurmaResponseDTO turmaDTO) throws Exception;

    void syncTurmaMgb(Integer codEmprezaZw, Integer codTurma) throws Exception;

    List<HorarioTurmaResponseDTO> saveOrUpdateHorario(List<HorarioTurmaResponseDTO> horarioDTO, Integer empresaZwId) throws Exception;

    HorarioTurmaResponseDTO saveHorario(HorarioTurmaResponseDTO horarioDTO) throws Exception;

    HorarioTurmaResponseDTO updateHorario(HorarioTurmaResponseDTO horarioDTO) throws Exception;

    AmbienteDTO saveAmbiente(AmbienteDTO ambienteDTO) throws Exception;

    NivelTurmaDTO saveNivelTurma(NivelTurmaDTO nivelTurmaDTO) throws Exception;

    List<TurmaResponseDTO> listarTurmas(JSONObject filtros, PaginadorDTO paginadorDTO, Integer empresaId) throws ServiceException;

    List<TurmaResponseDTO> obterTurmas(Integer codTurmaEdit, Integer empresa, PaginadorDTO paginadorDTO, JSONObject filtros) throws Exception;

    TurmaResponseDTO obterTurma(Integer codigo) throws Exception;

    List<HorarioTurmaResponseDTO> listarHorariosTurma(JSONObject filtros, PaginadorDTO paginadorDTO, Integer turma) throws Exception;

    String removerHorarioTurma(Integer id, Integer empresaZwId) throws Exception;

    String validarExisteAlunosHorarioTurma(Integer id) throws Exception;
}
