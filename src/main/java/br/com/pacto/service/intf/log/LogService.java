package br.com.pacto.service.intf.log;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.log.Log;
import br.com.pacto.controller.json.log.EntidadeLogEnum;
import br.com.pacto.controller.json.log.ItemExportacaoEnum;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.log.LogTO;
import org.json.JSONObject;

import java.util.List;

public interface LogService {

    public static final String SERVICE_NAME = "LogService";
    static final String camposCliente = "c.id, c.ip, c.screenname, c.\"timestamp\", c.username, c.processo, codigo,ca.codigoacesso,ca.codigocliente,ca.codigocontrato,ca.codigopessoa,ca.codigoultimocontatocrm,ca.\n" +
            "colaboradores,ca.dadosavaliacao,ca.datafimperiodoacesso,ca.datainicioperiodoacesso,ca.datalancamentocontrato,ca.\n" +
            "datamatricula,ca.datanascimento,ca.datarematriculacontrato,ca.datarenovacaocontrato,ca.dataultimarematricula,ca.\n" +
            "dataultimobv,ca.dataultimocontatocrm,ca.dataultimoacesso,ca.datavigenciaate,ca.datavigenciaateajustada,ca.datavigenciade,ca.\n" +
            "dia,ca.diasacessomes2,ca.diasacessomes3,ca.diasacessomes4,ca.diasacessosemana2,ca.diasacessosemana3,ca.diasacessosemana4,ca.\n" +
            "diasacessosemanapassada,ca.diasacessoultimomes,ca.diasassiduidadeultrematriculaatehoje,ca.diasfaltasemacesso,ca.\n" +
            "duracaocontratomeses,ca.email,ca.empresa,ca.faseatualcrm,ca.idade,ca.matricula,ca.mediadiasacesso4meses,ca.mnemonicodocontrato,ca.modalidades,ca.nome,ca.nomeplano,ca.\n" +
            "nraulasexperimentais,ca.nrtreinosprevistos,ca.nrtreinosrealizados,ca.objetivos,ca.pesorisco,ca.profissao,ca.responsavelultimocontatocrm,ca.\n" +
            "saldocontacorrentecliente,ca.sexo,ca.situacao,ca.situacaocontrato,ca.situacaomatriculacontrato,ca.\n" +
            "telefones,ca.tipoperiodoacesso,ca.valorpagocontrato,ca.valorparcabertocontrato,ca.\n" +
            "valorfaturadocontrato,ca.versao,ca.vezesporsemana,ca.grupo_codigo,ca.nivelaluno_codigo,ca.\n" +
            "pessoa_codigo,ca.professorsintetico_codigo,ca.descricaoduracao,ca.descricoesmodalidades,ca.\n" +
            "parq,ca.saldocreditotreino,ca.totalcreditotreino,ca.crossfit,ca.dataatualizacaofoto,ca.\n" +
            "frequenciasemanal,ca.fcmaxima,ca.fcrepouso,ca.situacaocontratooperacao,ca.\n" +
            "nomeconsulta,ca.codigoexterno,ca.fotokeyapp,ca.ativo,ca.datacadastro,ca.\n" +
            "empresausafreepass,ca.existeparcvencidacontrato,ca.cpf,ca.rg,ca.bairro,ca.cargo,ca.cidade,ca.endereco,ca.\n" +
            "estadocivil,ca.freepass,ca.telefone,ca.ultimavisita,ca.uf,ca.massamagraatual,ca.massamagrainicio,ca.\n" +
            "percentualgorduraatual,ca.percentualgordurainicio,ca.pesoatual,ca.pesoinicio,ca.gympassuniquetoken";


    void incluir (String chave, Log log) throws Exception;

    List<LogTO> listarLogAtividade(JSONObject filtros, PaginadorDTO paginadorDTO, boolean isAtividadeCrosfit, Integer id) throws ServiceException;

    List<LogTO> listarLogCategoriaAtividade(JSONObject filtros, PaginadorDTO paginadorDTO) throws ServiceException;

    List<LogTO> listarLogAparelhos(JSONObject filtros, PaginadorDTO paginadorDTO, boolean isAparelhoCrosfit) throws ServiceException;

    List<LogTO> listarLogWods(JSONObject filtros, PaginadorDTO paginadorDTO) throws ServiceException;

    List<LogTO> listarBanchmark(JSONObject filtros, PaginadorDTO paginadorDTO) throws ServiceException;

    List<LogTO> listarTiposbenchmarks(JSONObject filtros, PaginadorDTO paginadorDTO) throws ServiceException;

    List<LogTO> listarColaboradoresUsuario(JSONObject filtros, PaginadorDTO paginadorDTO) throws ServiceException;

    List<LogTO> listarMusculos(JSONObject filtros, PaginadorDTO paginadorDTO) throws ServiceException;

    List<LogTO> listarLogGruposMusculares(JSONObject filtros, PaginadorDTO paginadorDTO) throws ServiceException;

    List<LogTO> listarLogAvaliacaoObjetivos(JSONObject filtros, PaginadorDTO paginadorDTO) throws ServiceException;

    List<LogTO> listarTiposWod(JSONObject filtros, PaginadorDTO paginadorDTO) throws ServiceException;

    List<LogTO> listarLogCliente(String matricula, JSONObject filtros, PaginadorDTO paginadorDTO) throws ServiceException;

    List<LogTO> listarLogPerfilAcesso(JSONObject filtros, PaginadorDTO paginadorDTO) throws ServiceException;

    List<LogTO> listarLogPerfilAcessoRecurso(JSONObject filtros, Integer perfilId, String categoriaRecurso, PaginadorDTO paginadorDTO) throws ServiceException;

    List<LogTO> listarLogDisponibilidades(JSONObject filtros, PaginadorDTO paginadorDTO, Boolean agendamento, Integer codigoAgendamento) throws ServiceException;

    List<LogTO> listarLog(EntidadeLogEnum entidade, JSONObject filtros,
                          PaginadorDTO paginadorDTO, String chavesPrimarias,
                          String chavePrimariaEntidadeSubordinada, String descOperacao) throws ServiceException;

    List<LogTO> listarLog(EntidadeLogEnum entidade, JSONObject filtros,
                          PaginadorDTO paginadorDTO, String chavesPrimarias,
                          String chavePrimariaEntidadeSubordinada, String descOperacao, boolean ia) throws ServiceException;

    List<LogTO> listarLogExportacaoZW(ItemExportacaoEnum entidade, JSONObject filtros,
                                      PaginadorDTO paginadorDTO, String chavesPrimarias,
                                      String chavePrimariaEntidadeSubordinada, String descOperacao, boolean ia) throws ServiceException;
}
