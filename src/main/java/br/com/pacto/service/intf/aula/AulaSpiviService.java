package br.com.pacto.service.intf.aula;

import br.com.pacto.bean.aula.SpiviSeat;
import br.com.pacto.util.bean.AgendaTO;
import br.com.pacto.util.bean.AgendaTotalTO;
import br.com.pacto.util.bean.AgendadoTO;
import servicos.integracao.adm.client.EmpresaWS;

import java.util.Date;
import java.util.Map;

public interface AulaSpiviService {

    SpiviSeat gravarBike(final String ctx, SpiviSeat bike) throws Exception;

    Map<Integer, SpiviSeat> bikesAula(final String ctx, final String idzw, final Date dia, final Integer matricula) throws Exception;

    void removerClienteEventoSpivi(final String ctx,
                                   EmpresaWS empresaWS,
                                   Integer matricula,
                                   Integer eventID,
                                   Integer clientID,
                                   final String idzw,
                                   final Date dia) throws Exception;


    void addClienteEventoSpivi(final String ctx,
                               AgendaTotalTO agendamento,
                               AgendadoTO agendadoSelecionado,
                               EmpresaWS empresaWS,
                               Integer seatId,
                               Integer eventId,
                               Integer scheduleID) throws Exception;


    void trocarBikeEventoSpivi(final String ctx,
                                      AgendaTotalTO agendamento,
                                      AgendadoTO agendadoSelecionado,
                                      EmpresaWS empresaWS,
                                      Integer seatId,
                                      Integer eventId,
                                      Integer scheduleID) throws Exception;

}
