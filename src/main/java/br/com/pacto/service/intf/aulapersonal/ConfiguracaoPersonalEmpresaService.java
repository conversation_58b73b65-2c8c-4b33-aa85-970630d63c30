/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.pacto.service.intf.aulapersonal;

import br.com.pacto.bean.gestaopersonal.ConfiguracaoPersonalEmpresa;
import br.com.pacto.service.exception.ServiceException;
import java.util.List;
import java.util.Map;
import servicos.integracao.adm.client.EmpresaWS;



/**
 *
 * <AUTHOR>
 */
public interface ConfiguracaoPersonalEmpresaService {

    public static final String SERVICE_NAME = "ConfiguracaoPersonalEmpresaService";

    public ConfiguracaoPersonalEmpresa inserir(final String ctx, ConfiguracaoPersonalEmpresa object) throws ServiceException;

    public ConfiguracaoPersonalEmpresa obterPorId(final String ctx, Integer id) throws ServiceException;

    public ConfiguracaoPersonalEmpresa alterar(final String ctx, ConfiguracaoPersonalEmpresa object) throws ServiceException;

    public void excluir(final String ctx, ConfiguracaoPersonalEmpresa object) throws ServiceException;

    public List<ConfiguracaoPersonalEmpresa> obterTodos(final String ctx) throws ServiceException;
    
    public ConfiguracaoPersonalEmpresa obterPorEmpresa(final String ctx, final Integer empresaZW) throws ServiceException;

    public List<ConfiguracaoPersonalEmpresa> obterPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException;

    public List<ConfiguracaoPersonalEmpresa> obterPorParam(final String ctx, String query,
            Map<String, Object> params, int max, int index)
            throws ServiceException;

    public ConfiguracaoPersonalEmpresa obterObjetoPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException;
    
    public ConfiguracaoPersonalEmpresa atualizarCfgEmpresa(final String ctx, EmpresaWS empresaWS) throws ServiceException;

    public ConfiguracaoPersonalEmpresa atualizarCfgEmpresaV2(final String ctx, String json) throws ServiceException;

}
