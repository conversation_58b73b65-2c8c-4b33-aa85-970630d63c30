/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.intf.treinoindependente;

import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.pessoa.Email;
import br.com.pacto.bean.pessoa.Telefone;
import br.com.pacto.bean.professor.ProfessorSintetico;
import br.com.pacto.bean.usuario.TipoUsuarioEnum;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.service.exception.ServiceException;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public interface TreinoIndependenteService {
    
    public static final String SERVICE_NAME = "TreinoIndependenteService";
    
    public ClienteSintetico alterarAluno(final String ctx,
            ClienteSintetico cliente,
            String userName,
            String senha,
            Inte<PERSON> nivel,
            Integer professor, boolean usarAplicativo) throws ServiceException;
    
    public ClienteSintetico criar<PERSON><PERSON>no(final String ctx,
            ClienteSintetico cliente, 
            String userName,
            String senha,
            <PERSON>te<PERSON> nivel,  
            Integer professor, boolean usarAplicativo) throws ServiceException;
    
    public String enviarUsuarioMovelAluno(final String ctx, ClienteSintetico cliente, String senha) throws ServiceException;
    
    public ProfessorSintetico criarProfessor(final String ctx,
            ProfessorSintetico professor, 
            String userName,
            String senha, 
            Integer empresa,boolean criarUsuario,
            Integer perfil,
            TipoUsuarioEnum tipo) throws ServiceException;

    public ProfessorSintetico alterarProfessor(final String ctx,
            ProfessorSintetico professor,
            String userName,
            String senha,
            Integer empresa,boolean criarUsuario,
            Integer perfil,
            int tipo,
            Usuario usuarioProfessor) throws ServiceException;
    
}
