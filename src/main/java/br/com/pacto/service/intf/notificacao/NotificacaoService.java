/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.intf.notificacao;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.aula.AgendamentoLocacao;
import br.com.pacto.bean.notificacao.Notificacao;
import br.com.pacto.bean.notificacao.TipoNotificacaoEnum;
import br.com.pacto.bean.pessoa.StatusPessoa;
import br.com.pacto.bean.Agendamento;
import br.com.pacto.bean.agenda.TipoLembreteEnum;
import br.com.pacto.bean.notificacao.VisibilidadeNotificacaoEnum;
import br.com.pacto.bean.programa.ProgramaTreino;
import br.com.pacto.bean.serie.SerieRealizada;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.agendamento.AgendamentoPersonalDTO;
import br.com.pacto.dto.notificacao.NotificacaoAppDoAlunoDTO;
import br.com.pacto.dto.notificacao.NotificacaoDTO;
import br.com.pacto.service.exception.ServiceException;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public interface NotificacaoService {

    public static final String SERVICE_NAME = "NotificacaoService";

    public Notificacao inserir(final String ctx, Notificacao object) throws ServiceException;

    public Notificacao obterPorId(final String ctx, Integer id) throws ServiceException;

    public Notificacao alterar(final String ctx, Notificacao object) throws ServiceException;

    public void excluir(final String ctx, Notificacao object) throws ServiceException;

    public List<Notificacao> obterTodos(final String ctx, Integer idProfessor) throws ServiceException;

    public List<Notificacao> obterPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException;

    public List<Notificacao> obterPorParam(final String ctx, String query,
            Map<String, Object> params, int max, int index)
            throws ServiceException;

    public Notificacao obterObjetoPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException;

    public Number obterTotalNaoLidas(final String ctx, Integer idProfessor);

    public void notificarSerieRealizada(final String ctx, final SerieRealizada serieRealizada, TipoNotificacaoEnum tipoNotificacao);

    public List<Notificacao> obterNotificacoesSeriesRealizadas(final String ctx, final Integer idSerieRealizada) throws ServiceException;

    public List<Notificacao> obterUltimasCliente(final String ctx, final Integer idCliente, final Integer maxResults) throws ServiceException;

    public List<Notificacao> obterUltimasProfessor(final String ctx, final Integer idProfessor, boolean apenasNaoLidas, final Integer maxResults) throws ServiceException;

    public void notificarStatus(final String ctx, final StatusPessoa statusPessoa, TipoNotificacaoEnum tipoNotificacao);

    public void notificarSolicitacaoReagendamento(final String ctx, Agendamento agendamentoAnterior,
            final String novaData, final String novoHorario,
            TipoNotificacaoEnum tipoNotificacao);

    public void notificarLembreteAgendamento(final String ctx, Agendamento agendamento, final TipoLembreteEnum tipoLembrete);

    public void notificarAgendamentoNovoOuAlterado(final String ctx, Agendamento agendamento,
            final TipoLembreteEnum tipoLembrete, final TipoNotificacaoEnum tipoNotificacao,
            final String idMensagem);

    public void notificarCancelamentoAgendamentoLocacao(final String ctx, AgendamentoLocacao agendamentoLocacao);

    public void notificarStatusAgendamento(final String ctx, final Agendamento agendamento,
            TipoNotificacaoEnum tipoNotificacao, final String msgID);

    public void notificarPrograma(final String ctx,
            final ProgramaTreino programa, final Date dataBase, final TipoLembreteEnum tipoLembrete, TipoNotificacaoEnum tipoNotf,
            final String msg);

    public List<Notificacao> marcarComoLidaOuNaoLida(final String ctx, final List<Notificacao> notificacoes, boolean lida) throws ServiceException;

    public List<Notificacao> obterMaisNotificacoes(final String ctx, final Integer idProfessor, final Integer idCliente,
            boolean apenasNaoLidas,
            final Integer maxResults,
            final Integer index, VisibilidadeNotificacaoEnum visibilidadeNotificacao) throws ServiceException;

    Integer marcarLidas(final String ctx,
                        final String username,
                        final String matricula,
                        final Integer idNotificacao,
                        final Boolean todas) throws ServiceException;

    public Agendamento loadLazyAttributes(final String ctx, Agendamento agendamento) throws Exception;

    public Notificacao gerarNotificacao(final String key, Integer idClienteZw, Date data,
                                        String nome, String textoCRM,
                                        TipoNotificacaoEnum tipo, String opcoes, Boolean enviaPush) throws ServiceException;

    public Notificacao gravarResposta(final String key, Integer idNotf, String resposta) throws ServiceException;

    Notificacao gerarNotificacaoPorEmail(final String key, String email, String titulo, String texto, String opcoes, Date data, TipoNotificacaoEnum tipo) throws ServiceException;

    /**
     * Recupera as últimas 40 notificações do usuário atual
     *
     * @param paginadorDTO DTO de paginação para buscar páginas com determinado tamanho no sistema.
     * @return A lista de notificações deste usuário
     * @throws ServiceException Caso haja qualquer problema ao buscar as notificações do sistema
     */
    List<NotificacaoDTO> getNotificacoes(PaginadorDTO paginadorDTO) throws ServiceException;

    List<NotificacaoDTO> getNotificacoesAlunos(Integer aluno, PaginadorDTO paginadorDTO) throws ServiceException;
    /**
     * Marca uma notificação como LIDA no banco de dados
     *
     * @param id       ID da notificação que será marcada como lida
     * @throws ServiceException Caso ocorra algum problema na marcação
     */
    void marcarComoLida(Integer id) throws ServiceException;

    /**
     * Calcula a quantidade de notificações não lidas para o usuário atual
     *
     * @return A quantidade encontrada para o usuário atual
     * @throws ServiceException Caso ocorra algum problema na contagem de notificações não lidas
     */
    Long getQuantidadeNaoLidas() throws ServiceException;

    /**
     * Verifica se tem algum treino não vencido
     *
     * @return A quantidade encontrada para o usuário atual
     * @throws ServiceException Caso ocorra algum problema na contagem de notificações não lidas
     */

    public boolean isTreinoVigente(final String ctx, final Integer idCliente) throws ServiceException;


    void geraNotificacaoAppAluno(String ctx, Notificacao notf, String mensagem, Usuario u) throws ServiceException;
}
