package br.com.pacto.service.intf.eventoUsuario;

import br.com.pacto.bean.eventoUsuario.EventoUsuario;
import br.com.pacto.service.exception.ServiceException;

import java.util.List;

/**
 * <AUTHOR> 19/02/2019
 */
public interface EventoUsuarioService {

    String SERVICE_NAME = "EventoUsuarioService";

    EventoUsuario atualizarEventoUsuario(EventoUsuario eventoUsuario) throws ServiceException;

    List<EventoUsuario> obterTodosEventoUsuario() throws ServiceException;
}
