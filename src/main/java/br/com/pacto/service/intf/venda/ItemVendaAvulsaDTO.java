package br.com.pacto.service.intf.venda;

import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude
public class ItemVendaAvulsaDTO {
    private Integer codigoProduto;
    private Integer qtd;
    private Integer pontos;
    private String descricaoProduto;
    private Double precoProduto;
    private Double descontoManual;
    private Double valorParcial;

    public Integer getCodigoProduto() {
        return codigoProduto;
    }

    public void setCodigoProduto(Integer codigoProduto) {
        this.codigoProduto = codigoProduto;
    }

    public Integer getQtd() {
        return qtd;
    }

    public void setQtd(Integer qtd) {
        this.qtd = qtd;
    }

    public Integer getPontos() {
        return pontos;
    }

    public void setPontos(Integer pontos) {
        this.pontos = pontos;
    }

    public String getDescricaoProduto() {
        return descricaoProduto;
    }

    public void setDescricaoProduto(String descricaoProduto) {
        this.descricaoProduto = descricaoProduto;
    }

    public Double getPrecoProduto() {
        return precoProduto;
    }

    public void setPrecoProduto(Double precoProduto) {
        this.precoProduto = precoProduto;
    }

    public Double getDescontoManual() {
        return descontoManual;
    }

    public void setDescontoManual(Double descontoManual) {
        this.descontoManual = descontoManual;
    }

    public Double getValorParcial() {
        return valorParcial;
    }

    public void setValorParcial(Double valorParcial) {
        this.valorParcial = valorParcial;
    }
}
