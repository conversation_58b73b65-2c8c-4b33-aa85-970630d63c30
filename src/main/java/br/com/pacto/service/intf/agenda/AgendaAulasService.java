package br.com.pacto.service.intf.agenda;

import br.com.pacto.util.bean.AgendadoTO;
import br.com.pacto.util.json.AgendaTotalJSON;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface AgendaAulasService {

    List<AgendaTotalJSON> consultarAgendamentosModalidadeAluno(final String key, final Date inicio,
                                                               final Date fim, final Integer matricula, Integer empresa) throws Exception;

    List<AgendaTotalJSON> consultarAulasAluno(Date inicio, Date fim, Integer matricula) throws Exception;

    List<AgendaTotalJSON> consultarParaAgenda(Date inicio, Date fim, Integer turma,
                                              List<Integer> modalidades, Integer empresa,
                                              boolean aulasColetivas, String search, String situacaoHorario) throws Exception;

    List<AgendaTotalJSON> consultarParaAgenda(Date inicio, Date fim, Integer turma,
                                              List<Integer> modalidades, Integer empresa, boolean aulasColetivas,
                                              Date agora, Map<String, Date> mapaMatriculas,
                                              Map<String, List<Date>> mapaReposicoes, Integer matricula,
                                              boolean isApp, String search, String situacaoHorario) throws Exception;

    List<AgendaTotalJSON> consultarParaAgenda(Date inicio, Date fim, Integer turma,
                                                     List<Integer> modalidades, Integer empresa, boolean aulasColetivas,
                                                     Date agora, Map<String, Date> mapaMatriculas,
                                                     Map<String, List<Date>> mapaReposicoes, Integer matricula,
                                                     boolean isApp, boolean isAgendaOnline, String search, String situacaoHorario) throws Exception;

    List<Integer> dependentes(List<AgendadoTO> agendados);

    String consultarProximasAulas(Integer matricula, Boolean proximos30dias) throws Exception;

    public String consultarSaldoAluno(Integer matricula, Boolean forcarMarcar, Integer contrato) throws Exception;

    public void validaSituacaoContrato(Integer matricula, Integer codValidarContrato) throws Exception;
}
