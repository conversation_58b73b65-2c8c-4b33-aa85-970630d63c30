/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.pacto.service.intf.programa;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.programa.ObjetivoPredefinido;
import br.com.pacto.bean.programa.ObjetivoPredefinidoDTO;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.exception.ValidacaoException;
import java.util.List;
import java.util.Map;



/**
 *
 * <AUTHOR>
 */
public interface ObjetivoPredefinidoService {

    public static final String SERVICE_NAME = "ObjetivoPredefinidoService";

    public ObjetivoPredefinido inserir(final String ctx, ObjetivoPredefinido object) throws ServiceException, ValidacaoException;

    public ObjetivoPredefinido obterPorId(final String ctx, Integer id) throws ServiceException;

    public ObjetivoPredefinido alterar(final String ctx, ObjetivoPredefinido object) throws ServiceException, ValidacaoException;

    public void excluir(final String ctx, ObjetivoPredefinido object) throws ServiceException;

    public List<ObjetivoPredefinido> obterTodos(final String ctx) throws ServiceException;

    public List<ObjetivoPredefinido> obterPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException;

    public List<ObjetivoPredefinido> obterPorParam(final String ctx, String query,
            Map<String, Object> params, int max, int index)
            throws ServiceException;

    public ObjetivoPredefinido obterObjetoPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException;

    List<ObjetivoPredefinidoDTO> consultar(String nome, PaginadorDTO paginadorDTO) throws ServiceException;

    public List<ObjetivoPredefinidoDTO> obterTodos() throws ServiceException;
}
