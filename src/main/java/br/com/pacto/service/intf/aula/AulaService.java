/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.intf.aula;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.aula.*;
import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.aulaDia.AulaColetivaResponseDTO;
import br.com.pacto.controller.json.aulaDia.AulaDTO;
import br.com.pacto.controller.json.aulaDia.AulaResponseDTO;
import br.com.pacto.controller.json.aulaDia.FilaDeEsperaDTO;
import br.com.pacto.controller.json.aulaDia.MapaEquipamentoAparelhoDTO;
import br.com.pacto.controller.json.aulaDia.TvAulaDTO;
import br.com.pacto.controller.json.turma.HorarioTurmaResponseDTO;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.exception.ValidacaoException;
import br.com.pacto.service.impl.log.LogTO;
import br.com.pacto.util.json.AgendaTotalJSON;
import br.com.pacto.util.AgendadoJSON;
import br.com.pacto.util.json.ResultAlunoClienteSinteticoJSON;
import br.com.pacto.util.json.TurmaAulaCheiaJSON;
import org.json.JSONObject;

import javax.servlet.http.HttpServletRequest;
import java.sql.Connection;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public interface AulaService {

    public static final String SERVICE_NAME = "AulaService";

    public Aula inserir(final String ctx, Aula object) throws ServiceException;

    public Aula obterPorId(final String ctx, Integer id) throws ServiceException;

    public Aula alterar(final String ctx, Aula object) throws ServiceException;

    public void excluir(final String ctx, Aula object) throws ServiceException;

    public List<Aula> obterTodos(final String ctx) throws ServiceException;

    public List<Aula> obterPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException;

    public List<Aula> obterPorParam(final String ctx, String query,
            Map<String, Object> params, int max, int index)
            throws ServiceException;

    public Aula obterObjetoPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException;

    public List<Modalidade> obterModalidadesZW(String ctx, Integer empresa, Boolean turma) throws ServiceException;

    public List<Ambiente> obterAmbientesZW(String ctx, Integer empresa) throws ServiceException;

    public void inserirAula(final String key, final Aula aula, final List<String> diasSelecionados,
            final List<AulaHorario> horasSelecionados, final List<String> horariosExcluir) throws ValidacaoException, ServiceException;

    public List<AulaHorario> obterHorarios(final String ctx, final Aula aula) throws ServiceException;
    
    public Modalidade alteraModalidade(final String ctx, final Modalidade modalidade) throws ServiceException;
    
    public List<Modalidade> todasModalidades(String ctx) throws ServiceException;
    
    public AulaDia editarAulaDia(final String ctx, final AulaDia aulaDia) throws ServiceException;
    
    public void excluirAulaDia(final String ctx, final Integer codigoHorarioTurma, final Date dia, final Usuario usuario, final String justificativa) throws ServiceException;
    
    public List<AulaDia> obterAulasDia(final String ctx, final Date dia, final Date diaFim, 
            boolean desconsiderarCfg,
            boolean substituidos) throws ServiceException;

    public AulaAluno marcarPresenca(final String ctx, final String matricula, final Integer codigoAula, final Boolean aulaExperimental) 
            throws ServiceException,ValidacaoException;
    
    public List<AulaAluno> obterAulaAlunos(final String ctx, final Integer codigoAula, final Integer codigoAluno) throws ServiceException ;
    
    public List<AulaAluno> obterMatriculasDaAulaNoDia(final String ctx, final Integer codigoAula, final Date dia) throws ServiceException;
    
    public List<Ambiente> todosAmbientes(String ctx) throws ServiceException;
    
    public void excluirAula(final String key, final Aula aula) throws ServiceException;
   
    public void excluirAulasGeradas(final String key, final Aula aula) throws ServiceException;
    
    public AulaDia inserirAulaDia(String ctx, AulaDia aulaDia) throws Exception;
    
    public Modalidade obterModalidadePorIdZW(final String key, final Integer id) throws ServiceException;
    
    public Modalidade obterModalidadePorNome(final String key, final String nome) throws ServiceException;
    
    public List<AgendaTotalJSON> obterAulasAluno(final String key, final Integer matricula) throws ServiceException;
    
    public List<AgendaTotalJSON> obterAulasAluno(final String key, final Integer matricula, final String dia) throws ServiceException;
    
    public List<AgendaTotalJSON> obterAulasAlunoAPartirDoDia(final String key, final Integer matricula, final String dia) throws ServiceException;
    
    public Integer obterPontosAluno(final String key, final Integer matricula) throws ServiceException;
    
    public Ambiente obterAmbientePorId(final String key, final Integer id) throws ServiceException;
    
    public void removerAluno(final String ctx, final AulaAluno aluno, final Usuario usuario) throws ServiceException ;
    
    public void verificarHorarioComAlunoFuturo(final String ctx, final Aula aula, final String periodo, final Date data) throws ValidacaoException, ServiceException;
    
    public void substituirProfessor(final String key, final Integer codigoHorarioTurma, final Integer codigoProfessorOrigem, final Integer codigoProfessorSubstituto, 
            final Date diaAula, final Usuario usuario,
            final String justificativa) throws ServiceException;
    
    public List<AulaAluno> consultarAlunoEmAula(final String ctx, final AulaDia aulaDia, final Integer aluno) throws ServiceException;
    
    public String inserirZW(String ctx, TurmaAulaCheiaJSON turma, AulaDTO aulaDTO) throws ServiceException;

    public List<TurmaAulaCheiaJSON> obterAulasColetivas(String ctx, Integer codigo, Integer empresa, PaginadorDTO paginadorDTO, JSONObject filtroAulasJSON) throws Exception;
    
    public String excluirAulaCheia(final String ctx,final Integer codigo, final Integer usuario) throws Exception;

    public ClienteSintetico addAlunoAutomaticamente(final String ctx, final String matricula) throws Exception ;

    ResultAlunoClienteSinteticoJSON obterAlunosDeUmaAula(final String key, final Integer codigoHorario, Date dia) throws Exception;

    /**
     * Recupera os alunos agendados para a aula no horário e no dia informados.
     * Esses alunos podem ter suas presenças confirmadas ou não.
     *
     * @param key           Chave de conexão com o banco
     * @param codigoHorario Código do horário da turma desejada
     * @param dia           O dia em que os alunos foram agendados
     * @return Retorna a lista de alunos agendados naquele dia
     * @throws Exception Caso ocorra algum problema nessa consulta
     */
    ResultAlunoClienteSinteticoJSON obterAlunosAulaNaoColetiva(final String key, final Integer codigoHorario, Date dia) throws Exception;

    public List<AgendadoJSON> obterAlunosDeUmaTurma(final String ctx, final Integer empresa, 
        final Integer codigoHorario, Date dia) throws Exception;

    public String confirmarAlunoAula(final String key,final Integer cliente,
                                     final Integer horarioTurma,final String dia ,final Integer usuario) throws Exception;

    /**
     * Consulta no banco de dados se a aula atual seria a reposicao
     *
     * @param contexto     contexto do banco de dados
     * @param horarioTurma código do horário da turma
     * @param cliente      matrícula do cliente
     * @param dia          dia em que a presença foi confirmada
     * @return Retorna <b>true</b> se a reposição for efetuada e <b>false</b> em caso contrário
     */
    Boolean reposicaoNestaAula(String contexto, Integer horarioTurma, Integer cliente, Date dia) throws Exception;

    List<AulaResponseDTO> listarAulas(JSONObject filtroAulasJSON, PaginadorDTO paginadorDTO, Integer emmpresaId) throws ServiceException;

    AulaColetivaResponseDTO cadastroAulaV2(AulaColetivaResponseDTO aulaDTO, Integer codigoAula, Integer empresaZwId) throws ServiceException;

    List<HorarioTurmaResponseDTO> saveOrUpdateHorarios(List<HorarioTurmaResponseDTO> horarioDTO) throws Exception;

    String removerHorarioAula(Integer codigoHorario, Integer empresaZwId) throws Exception;

    Boolean existeAlunosHorarioAulaColetivaFutura(Integer codigoHorario) throws Exception;

    AulaResponseDTO cadastroAula(AulaDTO aulaDTO, Integer id, Integer empresaId) throws ServiceException;

    EdicaoAulaTemporaria editarAulaTemporariamente(AulaDTO aulaDTO, Integer id, String dataAula) throws ServiceException;

    EdicaoAulaTemporaria replicarAulaTemporariamente(AulaDTO aulaDTO, Integer id, String dataAula) throws ServiceException;

    AulaResponseDTO alterar(Integer id, AulaDTO aulaDTO) throws ServiceException;

    void removerAula(Integer id) throws ServiceException;

    void resetAula(Integer id, Integer empresa) throws ServiceException;

    AulaColetivaResponseDTO detalhesAulaColetiva(Integer id, Integer empresaId, Integer horario) throws ServiceException;

    List<HorarioTurmaResponseDTO> obterHorariosAulaColetiva(JSONObject filtros, PaginadorDTO paginadorDTO, Integer codigoAula) throws Exception;

    AulaResponseDTO detalhesAula(Integer id, Integer empresaId, Integer horario) throws ServiceException;

    List<TvAulaDTO> listaAulasHorario(Integer empresaId, HttpServletRequest request) throws ServiceException;

    TvAulaDTO detalhesAulaHorario(HttpServletRequest request, Integer empresaId, Integer horarioAulaId) throws ServiceException;

    List<LogTO> listarLogAula(String dia, String id, Integer empresa, JSONObject filtros, PaginadorDTO paginadorDTO) throws ServiceException;

    List<LogTO> listarLogAgendaAulas(String id, Integer empresa, JSONObject filtros, PaginadorDTO paginadorDTO) throws ServiceException;

    Boolean verificarAulasCrossfitAluno(String ctx, String matricula) throws Exception;

    boolean alunoEmAula(String ctx, String matricula, Integer codigoHorarioTurma, Date dia, String chaveOrigem) throws Exception;

    boolean aulaFoiExlcuida(Integer codigoAula, String ctx, String dia) throws Exception;

    public boolean horarioTurmaExiste(Integer codigoAula, String ctx) throws Exception;

    AulaResponseDTO clonarAula(Integer codigoAulaOriginal, Integer empresaIdZw) throws ServiceException;

    Integer atualizarAgendamentoRemovendoPassivo(String ctx, Integer aulaHorarioId, Date dia, String nomePassivo, Integer codigoPassivo) throws Exception;

    Integer atualizarAgendamentoRemovendoIndicado(String ctx, Integer aulaHorarioId, Date dia, String nomeIndicado, Integer codigoIndicado) throws Exception;

    List<FilaDeEsperaDTO> consultarFilaEspera(Integer codigoHorarioTurma, String dia) throws ServiceException;

    List<FilaDeEsperaDTO> consultarFilaEsperaTurmaCrm(Integer codigoHorarioTurma) throws ServiceException;

    List<FilaDeEsperaDTO> ordenarFilaEsperaTurmaCrm(Integer codigoHorarioTurma, Integer matricula, Integer passivo, String tipoOrdenacao, Integer novaOrdem) throws ServiceException;

    List<FilaDeEsperaDTO> ordenarFilaEspera(Integer codigoHorarioTurma, String dia, Integer matricula, String tipoOrdenacao, Integer novaOrdem) throws ServiceException;

    void moverParaCima(List<FilaDeEsperaDTO> alunosOrdenados, FilaDeEsperaDTO alunoMovido, Connection con) throws Exception;

    void moverParaBaixo(List<FilaDeEsperaDTO> alunosOrdenados, FilaDeEsperaDTO alunoMovido, Connection con) throws Exception;

    void moverParaOrdemEspecifica(List<FilaDeEsperaDTO> alunosOrdenados, FilaDeEsperaDTO alunoMovido, int novaOrdem, Connection con) throws Exception;

    void atualizarOrdemNoBanco(List<FilaDeEsperaDTO> alunosOrdenados, String ctx) throws ServiceException;

    AulaResponseDTO edicaoAulaTemporaria(Integer idHorarioTurma, String diaAula)  throws ServiceException;

    List<EdicaoAulaTemporaria> consultarEdicoesAulaTemporariaPeriodo(String ctx, Date inicio, Date fim) throws ServiceException;

    List<MapaEquipamentoAparelhoDTO> montarListaEquipamentoAparelho(String ctx, Integer codigoAula, String mapaEquipamentos) throws Exception;

    }
