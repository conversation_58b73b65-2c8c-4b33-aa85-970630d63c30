/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.intf.professor;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.bi.AgrupamentoIndicadorDashboardEnum;
import br.com.pacto.bean.bi.DashboardBI;
import br.com.pacto.bean.bi.IndicadorDashboardEnum;
import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.colaborador.ColaboradorResponseTO;
import br.com.pacto.bean.colaborador.ColaboradorSimplesTO;
import br.com.pacto.bean.colaborador.ColaboradorTO;
import br.com.pacto.bean.colaborador.FiltroColaboradorJSON;
import br.com.pacto.bean.professor.IndicadorAtividadeProfessorAcumuladoVO;
import br.com.pacto.bean.professor.ProfessorSintetico;
import br.com.pacto.bean.programa.ProfessorBIResponseTO;
import br.com.pacto.bean.programa.ProfessorResponseTO;
import br.com.pacto.controller.to.FiltrosGestaoTO;
import br.com.pacto.controller.json.aluno.AlunoSimplesDTO;
import br.com.pacto.controller.json.professor.ConfiguracaoRankingDTO;
import br.com.pacto.controller.json.professor.FiltroGestaoJSON;
import br.com.pacto.controller.json.professor.ProfessorIndicadorResponseDTO;
import br.com.pacto.controller.json.professor.ProfessoresAlunosAvisoMedicoResponseDTO;
import br.com.pacto.dto.ColaboradorDTO;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.exception.ValidacaoException;
import org.json.JSONObject;
import servicos.integracao.zw.json.VinculoJSON;

import javax.faces.model.SelectItem;
import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public interface ProfessorSinteticoService {

    public static final String SERVICE_NAME = "ProfessorSinteticoService";

    public ProfessorSintetico inserir(final String ctx, ProfessorSintetico object) throws ServiceException;
    public ProfessorSintetico insertOrMerge(final String ctx, ProfessorSintetico object) throws ServiceException;

    public ProfessorSintetico obterPorId(final String ctx, Integer id) throws ServiceException;

    ProfessorSintetico obterPorCodigoPessoaZW(final String ctx, Integer codigoPessoa, Integer codigoEmpresaZw) throws ServiceException;

    List<ProfessorSintetico> obterProfessoresPorCodigoPessoaZw(final String ctx, Integer codigoPessoa) throws ServiceException;

    public ProfessorSintetico alterar(final String ctx, ProfessorSintetico object) throws ServiceException;

    public void editSituacao(final String ctx, ProfessorSintetico object) throws ServiceException;

    public void excluirAluno(final String ctx, ProfessorSintetico object) throws ServiceException;

    public List<ProfessorSintetico> obterTodos(final String ctx, final Integer empresaZW, boolean somenteAtivos) throws ServiceException;

    List<ProfessorSintetico> obterTodos(final String ctx, boolean somenteAtivos) throws ServiceException;

    public List<ProfessorSintetico> obterPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException;

    public List<ProfessorSintetico> obterPorParam(final String ctx, String query,
            Map<String, Object> params, int max, int index)
            throws ServiceException;

    public ProfessorSintetico obterObjetoPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException;

    public ProfessorSintetico consultarPorCodigoColaborador(final String ctx, final Integer codigo) throws ServiceException;

    public ProfessorSintetico consultarPorCodigoPessoa(String ctx, String codigopessoa) throws ServiceException;

    public ProfessorSintetico obterProfessorTreino(String ctx, ClienteSintetico clienteEscolhido,
            List<VinculoJSON> vinculos) throws ValidacaoException, ServiceException;

    public ProfessorSintetico obterProfessorTreino(String ctx, ClienteSintetico clienteEscolhido,
                                                   List<VinculoJSON> vinculos, boolean obrigarProfessor) throws ValidacaoException, ServiceException;

    public List<SelectItem> montarComboProfessor(final String ctx, final Integer empresaZW, final boolean somenteAtivos) throws ServiceException;

    public List<ProfessorSintetico> consultarPorCodigoOuNome(final String ctx, final Integer empresaZW, final String param) throws ServiceException;

    public List<ProfessorSintetico> consultarPersonais(String ctx) throws ServiceException;

    public void atualizarFotoProfessor(final String ctx, final Integer codigo, final Integer codigoEmpresaZW, HttpServletRequest request, ServletContext context) throws ServiceException;

    public String sincronizarProfessor(String key, ProfessorSintetico professor,
            final Integer empresaZW) throws ServiceException;

    public String alterarProfessor(String key, ProfessorSintetico professor,
            final Integer empresaZW) throws ServiceException;

    String sincronizarProfessor(String key, ProfessorSintetico professor,
                                final Integer empresaZW, String fotoKey) throws ServiceException;

    void baixarFotoAluno(String ctx, HttpServletRequest request, String codigopessoa, String fotoKey) throws Exception;

    void marcarProfessorDisponivel(final String ctx, ProfessorSintetico professor,
     boolean disponivel) throws ServiceException;
    
    boolean verificarProfessorDisponivel(final String ctx, ProfessorSintetico professor) throws ServiceException;

    List<ProfessorSintetico> consultarProfessores(String ctx, final Integer codigoEmpresaZW, Boolean ativos) throws ServiceException;

    List<ProfessorSintetico> consultarProfessores(String ctx, final Integer codigoEmpresaZW) throws ServiceException;

    ProfessorSintetico consultarProfessorCarteiraPorCliente(String ctx, Integer codigoCliente) throws ServiceException;

    ProfessorSintetico consultarProfessorPorCodigoPessoa(String ctx, Integer codigoPessoa) throws ServiceException;

    List<ProfessorSintetico> consultarProfessoresComVinculos(String ctx, Integer empresa) throws ServiceException;

    List<ColaboradorResponseTO> listarColaboradores(HttpServletRequest request, FiltroColaboradorJSON filtros, PaginadorDTO paginadorDTO, Integer empresaId, Boolean todosTipos) throws ServiceException;

    List<ColaboradorResponseTO> listaColaboradorDadosBasicos(Integer idProfessorMontou, FiltroColaboradorJSON filtros, Integer empresaZwId) throws ServiceException;

    ColaboradorResponseTO buscarColaborador(Integer id, Integer empresaId, HttpServletRequest request, boolean origemIsTelaColaboradorZWUI) throws ServiceException;

    ColaboradorResponseTO cadastrarColaborador(ColaboradorTO colaboradorTO, Integer empresaId, HttpServletRequest request) throws ServiceException;

    ColaboradorResponseTO cadastrarColaborador(String ctx, ColaboradorTO colaboradorTO, Integer empresaId, HttpServletRequest request) throws Exception;

    ColaboradorResponseTO atualizarColaborador(Integer id, Integer empresaId, ColaboradorTO colaboradorTO) throws ServiceException;

    void editSituacaoColaborador(Integer id) throws ServiceException;

    List<ProfessorResponseTO> listarProfessores(Integer empresaId, Boolean incluirInativos) throws ServiceException;

    List<ColaboradorSimplesTO> colaboradorIsNullUsuario(HttpServletRequest request) throws ServiceException;

    List<ProfessorIndicadorResponseDTO> carregarIndicadores(HttpServletRequest request,PaginadorDTO paginadorDTO, Integer empresaId, FiltroGestaoJSON filtroGestaoJSON, String sort) throws ServiceException;

    List<ProfessoresAlunosAvisoMedicoResponseDTO> alunosAvisoMedico(HttpServletRequest request, PaginadorDTO paginadorDTO, Integer empresaId, FiltroGestaoJSON filtroGestaoJSON, String sort, boolean contar) throws ServiceException;

    List<AlunoSimplesDTO> carregarAlunosPorIndicador(HttpServletRequest request,PaginadorDTO paginadorDTO, Integer empresaId, FiltroGestaoJSON filtroGestaoJSON, String sort) throws ServiceException;

    ConfiguracaoRankingDTO incluirConfiguracao(Integer empresaId, ConfiguracaoRankingDTO configuracaoRankingDTO) throws ServiceException;

    Map<AgrupamentoIndicadorDashboardEnum, List<ConfiguracaoRankingDTO>> listarConfiguracoesRanking(Integer empresaId, PaginadorDTO paginadorDTO, JSONObject filters) throws ServiceException;

    Map<AgrupamentoIndicadorDashboardEnum, List<ConfiguracaoRankingDTO>> gravarConfiguracoesRanking(Integer empresaId, Map<String, String> valores) throws ServiceException;

    boolean toggleConfiguracoesRanking(Integer empresaId, IndicadorDashboardEnum indicador) throws ServiceException;

    void removerConfiguracaoRanking(Integer configuracaoId) throws ServiceException;

    String incluirAtualizarConfigRelatorio(String configId, String config) throws ServiceException;

    String obterConfigRelatorio(String configId) throws Exception;

    List<ProfessorResponseTO> listarTodosColaborador(HttpServletRequest request, Integer empresaId, Boolean incluirInativos, Boolean todosTipos, Boolean validarNoTreino) throws ServiceException;

    List<ColaboradorDTO> consultarTodosProfessoresZW(Integer empresa, Boolean incluirInativo, Boolean todosTipos) throws ServiceException;

    ProfessorResponseTO consultarColaboradorPorUsuario(Integer usuarioId) throws ServiceException;

    List<ColaboradorSimplesTO> listarTodosColaboradorAptoAAula(HttpServletRequest request, Integer empresaId, Boolean incluirInativos, String nome) throws ServiceException;

    List<ProfessorResponseTO> consultarProfessoresComVinculos(HttpServletRequest request, Integer empresaId) throws ServiceException;

    List<ColaboradorResponseTO> listarColaboradoresSimples(HttpServletRequest request, FiltroColaboradorJSON filtros,
                                                           PaginadorDTO paginadorDTO, Integer empresaId) throws ServiceException;
    void atualizarRankingProfessores (Integer empresaId, Date dataInicio);

    void removerFotoAluno(String ctx, HttpServletRequest request, String codigopessoa) throws Exception;

    FiltrosGestaoTO popularFiltroGestao(HttpServletRequest request, Integer empresaId, FiltroGestaoJSON filtroGestaoJSON) throws ServiceException;

    HashMap<String, String> carregarSortMap(String sort);

    void sincronizarTodosColaboradores(String key, final Integer empresaZW) throws Exception;

    void sincronizarLancamentoPrograma(final String key, final Integer empresaZW, Integer codigoColaborador) throws Exception;

    List<ProfessorBIResponseTO> consultarBiProfessoresComVinculos(HttpServletRequest request, Integer empresaId) throws ServiceException;

    Integer obterCodigoColaboradorPorUsuario(final String ctx, final Integer codUsuario);

    Integer obterCodigoProfessorPorColaborador(String ctx, Integer codColaborador);

    ProfessorSintetico obterPorAtributo(String ctx, String codigo, Object valor) throws ServiceException;

    Integer consultarCodProfessorPorNomeECodigoEmpresa(String ctx, String nome, Integer codigoEmpresa) throws ServiceException;

    String sincronizarTodosProfessores(String key) throws ServiceException;

    List<ColaboradorDTO> listarTodosColaboradoresPorEmpresa(String ids, Integer empresaId) throws ServiceException;

    IndicadorAtividadeProfessorAcumuladoVO carregarIndicadoresAcumulados(HttpServletRequest request, Integer empresaId) throws ServiceException;

    ProfessorSintetico consultarPorCodigo(String ctx, Integer codigo, Integer empresa, Boolean ativos) throws ServiceException;
}
