package br.com.pacto.service.intf.atividade;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.controller.json.atividade.AtividadeCrossfitDTO;
import br.com.pacto.controller.json.atividade.AtividadeCrossfitResponseDTO;
import br.com.pacto.controller.json.atividade.FiltroAtividadeCrossfitJSON;
import br.com.pacto.service.exception.ServiceException;

import java.util.List;

/**
 * <AUTHOR> 31/01/2019
 */
public interface AtividadeCrossfitService {
    public static final String SERVICE_NAME = "AtividadeCrossfitService";

    AtividadeCrossfitResponseDTO insert(AtividadeCrossfitDTO atividadeCrossfitDTO) throws ServiceException;

    AtividadeCrossfitResponseDTO update(AtividadeCrossfitDTO atividadeCrossfitDTO) throws ServiceException;

    AtividadeCrossfitResponseDTO obterPorId(Integer atividadeCrossfitId) throws ServiceException;

    List<AtividadeCrossfitResponseDTO> listarAtividadesCrossfit(FiltroAtividadeCrossfitJSON filtros, PaginadorDTO paginadorDTO) throws ServiceException;

    void excluir(Integer atividadeCrossfitId) throws ServiceException;
}
