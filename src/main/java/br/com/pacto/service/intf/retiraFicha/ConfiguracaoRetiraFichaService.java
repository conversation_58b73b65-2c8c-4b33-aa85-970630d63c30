package br.com.pacto.service.intf.retiraFicha;

import br.com.pacto.bean.retiraficha.ConfiguracaoRetiraFicha;
import br.com.pacto.controller.json.retiraFicha.ConfiguracaoRetiraFichaJson;
import br.com.pacto.service.exception.ServiceException;

import java.util.List;

public interface ConfiguracaoRetiraFichaService {

    List<ConfiguracaoRetiraFicha> obterTodos(final String ctx) throws ServiceException;

    ConfiguracaoRetiraFicha obterPorId(final String ctx, Integer id) throws ServiceException;

    ConfiguracaoRetiraFicha cadastrarConfiguracaoRetiraFicha(final String ctx, ConfiguracaoRetiraFicha<PERSON>son configuracaoRetiraFichaJson, Integer codigoUsuario) throws ServiceException;

    void alterarConfiguracaoRetiraFicha(final String ctx, ConfiguracaoRetiraFicha<PERSON>son configuracaoR<PERSON>ra<PERSON><PERSON><PERSON><PERSON><PERSON>, Integer codigoUsuario) throws ServiceException;

}
