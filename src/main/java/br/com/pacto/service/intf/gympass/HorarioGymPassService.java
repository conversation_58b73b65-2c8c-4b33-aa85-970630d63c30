/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.pacto.service.intf.gympass;

import br.com.pacto.bean.gympass.HorarioGymPass;
import br.com.pacto.service.exception.ServiceException;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 06/04/2020
 */
public interface HorarioGymPassService {

    public static final String SERVICE_NAME = "HorarioGymPassService";

    HorarioGymPass obterPorId(final String ctx, Integer id) throws ServiceException;

    HorarioGymPass incluir(final String ctx, HorarioGymPass object) throws ServiceException;

    HorarioGymPass alterar(final String ctx, HorarioGymPass object) throws ServiceException;

    HorarioGymPass obterPorIdReferencia(final String ctx, Integer idTurma, String idReferencia) throws ServiceException;

    List<HorarioGymPass> obterPorIdReferencia(final String ctx, String idReferencia) throws ServiceException;

    HorarioGymPass obterPorIdSlotGymPass(final String ctx, Integer idSlotGymPass) throws ServiceException;

    List<HorarioGymPass> obterPorIdTurma(final String ctx, Integer idTurma, Boolean ativo) throws ServiceException;

    Map<String, HorarioGymPass> obterMapaIdReferenciaPorIdTurma(final String ctx, Integer idTurma, Boolean ativo) throws ServiceException;

}
