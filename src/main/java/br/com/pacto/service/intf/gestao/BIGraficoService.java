package br.com.pacto.service.intf.gestao;


import br.com.pacto.bean.bi.DashboardBI;
import br.com.pacto.bean.gestao.ItemGraficoTO;
import br.com.pacto.bean.gestao.ItemGrupoIndicadores;
import br.com.pacto.bean.professor.ProfessorSintetico;
import br.com.pacto.controller.json.gestao.BIGraficoResponseComProfessorDTO;
import br.com.pacto.controller.json.gestao.BIGraficoResponseSemProfessorDTO;
import br.com.pacto.service.exception.ServiceException;

import java.util.List;

/**
 * <AUTHOR>
 * @since 18/10/2019
 */

public interface BIGraficoService {

    String SERVICE_NAME = "BIGraficoService";

    List<BIGraficoResponseSemProfessorDTO> obterDadosSemProfessor(
                                        final List<DashboardBI> ListaTodosDash,
                                        final List<ItemGraficoTO> indicadores,
                                        final Integer meses,
                                        String nomeIndicador) throws ServiceException;

    List<BIGraficoResponseComProfessorDTO> obterDadosPorProfessor(
                                        final List<DashboardBI> ListaTodosDash,
                                        final List<ItemGraficoTO> indicadores,
                                        final Integer meses,
                                        final List<ProfessorSintetico> professorId) throws ServiceException;

    List<DashboardBI> obterDash(final String ctx, final Integer professor, final Integer empresa) throws ServiceException;

    List<ItemGrupoIndicadores> obterGruposIndicadores(final String ctx) throws ServiceException;

    void gravarGrupoIndicador(String ctx, String nome, List<ItemGraficoTO> itens, List<String> profs) throws ServiceException;

    void excluirGrupo(final String ctx, final String nome) throws ServiceException;

    ProfessorSintetico obterPorCodigoPessoaZW(final String ctx, Integer codigoPessoa, Integer codigoEmpresaZw) throws ServiceException;

    ProfessorSintetico obterPorId(final String ctx, Integer id) throws ServiceException;

    ProfessorSintetico obterPorIdColaborador(final String ctx, Integer id) throws ServiceException;

}
