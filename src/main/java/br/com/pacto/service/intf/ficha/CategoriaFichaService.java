/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.pacto.service.intf.ficha;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.ficha.CategoriaFicha;
import br.com.pacto.bean.ficha.CategoriaFichaResponseTO;
import br.com.pacto.bean.ficha.FiltroCategoriaFichaJSON;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.exception.ValidacaoException;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;



/**
 *
 * <AUTHOR>
 */
public interface CategoriaFichaService {

    public static final String SERVICE_NAME = "CategoriaFichaService";

    public CategoriaFicha inserir(final String ctx, CategoriaFicha object) throws ServiceException, ValidacaoException;

    public CategoriaFicha obterPorId(final String ctx, Integer id) throws ServiceException;

    public CategoriaFicha alterar(final String ctx, CategoriaFicha object) throws ServiceException, ValidacaoException;

    public void excluir(final String ctx, CategoriaFicha object) throws ServiceException;

    public List<CategoriaFicha> obterTodos(final String ctx) throws ServiceException;

    public List<CategoriaFicha> obterPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException;

    public List<CategoriaFicha> obterPorParam(final String ctx, String query,
            Map<String, Object> params, int max, int index)
            throws ServiceException;

    public CategoriaFicha obterObjetoPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException;

    List<CategoriaFichaResponseTO> listarCategoriasFicha(FiltroCategoriaFichaJSON filtro, PaginadorDTO paginadorDTO) throws ServiceException;

    CategoriaFichaResponseTO criarCategoriaFicha(CategoriaFichaResponseTO categoriaFicha, HttpServletRequest request) throws Exception;

    CategoriaFichaResponseTO editarCategoriaFicha(CategoriaFichaResponseTO categoriaFichaTo, HttpServletRequest request) throws Exception;

    void excluirCategoriaFicha(Integer id, HttpServletRequest request) throws Exception;
}
