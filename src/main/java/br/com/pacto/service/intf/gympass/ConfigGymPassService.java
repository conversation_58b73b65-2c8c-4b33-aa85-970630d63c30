/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.pacto.service.intf.gympass;

import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.bean.gympass.ConfigGymPass;
import br.com.pacto.controller.json.gympass.ConfigGymPassDTO;
import br.com.pacto.service.exception.ServiceException;

import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 22/03/2020
 */
public interface ConfigGymPassService {

    public static final String SERVICE_NAME = "ConfigGymPassService";

    ConfigGymPass obterPorEmpresa(final String ctx, Empresa empresa) throws ServiceException;

    ConfigGymPass obterPorEmpresaZW(final String ctx, Integer codEmpresaZW) throws ServiceException;

    ConfigGymPass obterPorEmpresaTR(final String ctx, Integer codEmpresaTR) throws ServiceException;

    ConfigGymPass obterPorId(final String ctx, Integer id) throws ServiceException;

    ConfigGymPass alterar(final String ctx, ConfigGymPass object) throws ServiceException;

    List<ConfigGymPass> obterTodos(final String ctx) throws ServiceException;

    List<ConfigGymPassDTO> obterTodosDTO(final String ctx) throws ServiceException;

    ConfigGymPass alterarDTO(final String ctx, ConfigGymPassDTO dto) throws ServiceException;

    ConfigGymPass obterPorCodigoGymPass(final String ctx, String codigoGymPass) throws ServiceException;

    void validarConfiguracaoGympass(ConfigGymPass configGymPass) throws Exception;
}
