package br.com.pacto.service.intf.agendatotal;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@ApiModel(description = "DTO para representar o horário de uma agenda, com os itens agrupados por dia.")
public class AgendaHorarioDTO {

    @ApiModelProperty(value = "Horário (por exemplo, '00:00')", example = "00:00")
    private String horario;

    @ApiModelProperty(
            value = "Mapeamento dos dias com a lista de itens de agenda. A chave é a data (no formato YYYYMMDD) e o valor é uma lista de HorarioItemAgendaDTO.",
            dataType = "Map<String, List<HorarioItemAgendaDTO>>"
    )
    private Map<String, List<HorarioItemAgendaDTO>> dias = new HashMap<>();

    public String getHorario() {
        return horario;
    }

    public void setHorario(String horario) {
        this.horario = horario;
    }

    public Map<String, List<HorarioItemAgendaDTO>> getDias() {
        return dias;
    }

    public void setDias(Map<String, List<HorarioItemAgendaDTO>> dias) {
        this.dias = dias;
    }
}
