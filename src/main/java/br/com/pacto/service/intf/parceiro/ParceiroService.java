package br.com.pacto.service.intf.parceiro;

import br.com.pacto.bean.parceiro.Parceiro;
import br.com.pacto.controller.json.parceiros.ParceiroResponseTO;
import br.com.pacto.controller.json.parceiros.ParceiroTO;
import br.com.pacto.service.exception.ServiceException;

import java.util.List;

public interface ParceiroService {

    public static final String SERVICE_NAME = "ParceiroService";

    Parceiro inserir(String ctx, Parceiro object) throws ServiceException;

    Parceiro consultarPorParceiro(String ctx, Integer id) throws ServiceException;

    Parceiro alterar(String ctx, Parceiro object) throws ServiceException;

    void removerNotIn(String ctx,List<Parceiro> parceiros) throws ServiceException;

    List<ParceiroResponseTO> cadastroListaParceiro(List<ParceiroTO> listaC) throws ServiceException;

    List<ParceiroResponseTO> alterarListaParceiro(List<ParceiroTO> listaA) throws ServiceException;

    List<ParceiroResponseTO> listaParceiro() throws ServiceException;

    void removerParceirosNotIn(List<ParceiroTO> listaA) throws ServiceException;

    void removerParceiros(List<Integer> parceirosIds) throws ServiceException;
}
