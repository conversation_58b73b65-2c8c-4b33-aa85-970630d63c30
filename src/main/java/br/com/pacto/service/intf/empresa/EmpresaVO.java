package br.com.pacto.service.intf.empresa;

public class EmpresaVO{

    private Integer codigo;
    private int tempoAposFaltaReposicao = 60;
    private boolean permMarcarAulaFeriado;
    private String nome = "";
    private String horaAberturaFeriado = "";
    private String horaFechamentoFeriado = "";
    private boolean adicionarAulasDesmarcadasContratoAnterior = false;
    private CidadeVO cidade;
    private EstadoVO estado;
    private Integer nrDiasAvencer;

    public CidadeVO getCidade() {
        return cidade;
    }

    public void setCidade(CidadeVO cidade) {
        this.cidade = cidade;
    }

    public EstadoVO getEstado() {
        return estado;
    }

    public void setEstado(EstadoVO estado) {
        this.estado = estado;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public boolean isAdicionarAulasDesmarcadasContratoAnterior() {
        return adicionarAulasDesmarcadasContratoAnterior;
    }

    public void setAdicionarAulasDesmarcadasContratoAnterior(boolean adicionarAulasDesmarcadasContratoAnterior) {
        this.adicionarAulasDesmarcadasContratoAnterior = adicionarAulasDesmarcadasContratoAnterior;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public int getTempoAposFaltaReposicao() {
        return tempoAposFaltaReposicao;
    }

    public void setTempoAposFaltaReposicao(int tempoAposFaltaReposicao) {
        this.tempoAposFaltaReposicao = tempoAposFaltaReposicao;
    }

    public boolean isPermMarcarAulaFeriado() {
        return permMarcarAulaFeriado;
    }

    public void setPermMarcarAulaFeriado(boolean permMarcarAulaFeriado) {
        this.permMarcarAulaFeriado = permMarcarAulaFeriado;
    }

    public String getHoraAberturaFeriado() {
        return horaAberturaFeriado;
    }

    public void setHoraAberturaFeriado(String horaAberturaFeriado) {
        this.horaAberturaFeriado = horaAberturaFeriado;
    }

    public String getHoraFechamentoFeriado() {
        return horaFechamentoFeriado;
    }

    public void setHoraFechamentoFeriado(String horaFechamentoFeriado) {
        this.horaFechamentoFeriado = horaFechamentoFeriado;
    }

    public Integer getNrDiasAvencer() {
        return nrDiasAvencer;
    }

    public void setNrDiasAvencer(Integer nrDiasAvencer) {
        this.nrDiasAvencer = nrDiasAvencer;
    }

}
