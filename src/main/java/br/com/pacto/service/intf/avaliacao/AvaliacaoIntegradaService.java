package br.com.pacto.service.intf.avaliacao;

import br.com.pacto.bean.avaliacao.AvaliacaoIntegradaDTO;
import br.com.pacto.bean.avaliacao.AvaliacaoIntegradaDTOUptade;
import br.com.pacto.service.exception.ServiceException;

import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

public interface AvaliacaoIntegradaService {

    public static final String SERVICE_NAME = "AvaliacaoIntegradaService";

    List<AvaliacaoIntegradaDTO> allByAluno(final String ctx, final int cliente) throws ServiceException;

    AvaliacaoIntegradaDTO findAvIntegradaById(final String ctx, final Integer idAvIntegrada) throws ServiceException;

    Integer saveOrUpdate(final String ctx, Integer idAluno, Integer idUsuario, AvaliacaoIntegradaDTOUptade avaliacaoIntegrada) throws ServiceException;

    void delete(final String ctx, Integer idAvIntegrada) throws ServiceException;

    String printIndividualAvIntegrada(final String ctx,
                                      final Integer idAvIntegrada,
                                      Integer idUsuario,
                                      HttpServletRequest request) throws Exception;

    String printGroupAvIntegrada(final String ctx, Integer idUsuario, String codigos, HttpServletRequest request) throws Exception;

    void sendIndividualAvIntegrada(final String ctx,
                                   Integer idAluno,
                                   Integer idAvaliacao,
                                   Integer idUsuario,
                                   HttpServletRequest request,
                                   ServletContext servletContext) throws ServiceException;

    void sendGrupoAvIntegrada(final String ctx,
                              Integer idAluno,
                              String idAvaliacoes,
                              Integer idUsuario,
                              HttpServletRequest request,
                              ServletContext servletContext) throws ServiceException;

}
