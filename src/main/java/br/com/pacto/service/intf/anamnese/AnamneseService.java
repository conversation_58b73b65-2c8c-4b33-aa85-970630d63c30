package br.com.pacto.service.intf.anamnese;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.anamnese.*;
import br.com.pacto.bean.avaliacao.AvaliacaoFisica;
import br.com.pacto.bean.avaliacao.ItemAvaliacaoFisica;
import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.avaliacao.FiltrosJSON;
import br.com.pacto.controller.json.avaliacao.QuestionarioJSON;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.exception.ValidacaoException;
import br.com.pacto.service.impl.log.LogTO;
import br.com.pacto.util.ViewUtils;
import org.json.JSONObject;

import java.util.List;

/**
 * Created by Joao Alcides on 04/05/2017.
 */
public interface AnamneseService {

    public static final String SERVICE_NAME = "AnamneseService";

    public Anamnese gravarAnamnese(final String key, final Anamnese anamnese, final List<Pergunta> perguntas, final List<Pergunta> perguntasExcluir, final List<OpcaoPergunta> opcoesExcluir) throws ServiceException;

    public List<Anamnese> obterTodas(final String key) throws ServiceException;

    public List<Anamnese> consultarPovoando(final String key, final Usuario usuario, final Boolean apenasAtivos, final ViewUtils vu) throws ServiceException;

    public List<PerguntaAnamnese> obterPerguntasAnamnese(final String key, final Integer anamnese) throws ServiceException;

    public void gravarAnamneseCliente(final String key, final Anamnese anamnese, final ClienteSintetico cliente, ItemAvaliacaoFisica item) throws ServiceException;

    public List<OpcaoPergunta> obterOpcoes(final String key, final Integer pergunta) throws ServiceException;

    public RespostaCliente obterRespostaPerguntasAnamnese(final String key, final Integer perguntaanamnese,
                                                          final Integer cliente, final Integer itemAvaliacao) throws ServiceException;

    public Boolean obterRespostaParQAssinaturaDigital(final String ctx, final Integer cliente) throws ServiceException;

    public void excluirAnamnese(final String key, final Anamnese anamnese) throws ServiceException;

    public boolean anamneseUsada(final String key, final Integer codigo) throws Exception;

    public boolean perguntaUsada(final String key, final Integer codigo) throws Exception;

    public RespostaCliente obterRespostaPerguntasAnamnese(final String key, final Integer itemAvaliacao) throws ServiceException;

    public void excluirAnamneseAluno(final String key, final ItemAvaliacaoFisica prs) throws Exception;

    public void alterarAnamnese(final String key, final Usuario usuario, final ClienteSintetico cliente, Anamnese anamnese, boolean parq, AvaliacaoFisica avaliacao) throws Exception;

    public Anamnese consultarParq(final String key, final Integer usuario, final ViewUtils vu) throws ServiceException;

    public Anamnese obterAvaliacaoIntegrada(final String key, final Usuario usuario) throws ServiceException;

    public void alterarAvaliacaoIntegrada(final String key, ItemAvaliacaoFisica item, Anamnese avaliacao,
                                          List<Movimento3D> mobilidade, List<Movimento3D> estabilidade) throws Exception;

    public void excluirAvaliacaoIntegrada(final String key, ItemAvaliacaoFisica item) throws Exception;

    public Anamnese povoarAvaliacaoIntegrada(final String key, final Usuario usuario) throws Exception;

    public QuestionarioJSON consultarParQ(String key, Integer empresa, final ViewUtils vu) throws Exception;

    public void responderParQServico(String ctx, String matricula, JSONObject respostas, String assinatura) throws Exception;

    List<AnamneseResponseTO> consultarAnamneses(FiltrosJSON filtroAvaliacaoFisicaJSON, PaginadorDTO paginadorDTO)throws ServiceException;

    AnamneseResponseTO consultarAnamnese(Integer codigoAnamnese)throws ServiceException;

    AnamneseResponseTO inserir(AnamneseTO anamneseTO) throws ServiceException;

    AnamneseResponseTO alterar(AnamneseTO anamneseTO) throws ServiceException;

    void excluir(Integer codigoAnamnese) throws Exception, ValidacaoException;

    public List<AnamneseResponseTO> obterTodas(Boolean isIntegrada, Boolean ativas) throws ServiceException;

    Anamnese getAnamneseForId(String ctx, Integer id) throws ServiceException;

    String salvarRespostasParQ(String ctx, JSONObject json, Integer usuarioZw) throws Exception;

    String salvarEdicaoRespostasParQ(String ctx, JSONObject json, Integer usuarioZw, Integer codigoRespostaParq) throws Exception;

    List<LogTO> listarLogAnamneses(JSONObject filtros, PaginadorDTO paginadorDTO, Integer codigoAnamnese) throws ServiceException;

    public void gravarParqPadrao10Perguntas(final String key, final ViewUtils vu) throws ServiceException;
}
