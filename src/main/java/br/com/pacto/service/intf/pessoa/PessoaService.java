/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.intf.pessoa;

import br.com.pacto.bean.pessoa.Email;
import br.com.pacto.bean.pessoa.Pessoa;
import br.com.pacto.bean.pessoa.Telefone;
import br.com.pacto.service.exception.ServiceException;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public interface PessoaService {
    
    public static final String SERVICE_NAME = "PessoaService";
        
    public Pessoa inserirPessoa(final String ctx, Pessoa pessoa) throws ServiceException;
    
    public Telefone inserirTelefone(final String ctx, Telefone telefone) throws ServiceException;
    
    public Email inserirEmail(final String ctx, Email email) throws ServiceException;
    
    public List<Email> obterEmailsPorPessoa(String ctx,Integer pessoa) throws Exception;

    public List<Telefone> obterTelefonesPorPessoa(String ctx,Integer pessoa) throws Exception;
    
    public void deletarEmailTelefonePessoa(String ctx, Integer pessoa) throws Exception;
    
    public Pessoa alterarPessoa(final String ctx, Pessoa pessoa) throws ServiceException;

    Pessoa obterPorId(final String ctx, Integer id) throws ServiceException;

    List<Pessoa> obterTodas(final String ctx) throws ServiceException;

    Pessoa obterPessoaPorNome(final String ctx, String nome) throws ServiceException;
}
