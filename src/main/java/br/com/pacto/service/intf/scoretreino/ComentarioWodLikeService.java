package br.com.pacto.service.intf.scoretreino;

import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.bean.wod.ComentarioWod;
import br.com.pacto.bean.wod.ComentarioWodLike;
import br.com.pacto.service.exception.ServiceException;

import java.util.List;
import java.util.Map;

/*
 * Created by <PERSON><PERSON>
 */
public interface ComentarioWodLikeService {

    ComentarioWodLike obterPorId(final String ctx, Integer id) throws ServiceException;

    List<ComentarioWodLike> obterPorParam(final String ctx, String query, Map<String, Object> param) throws ServiceException;

    ComentarioWodLike inserir(final String ctx, ComentarioWodLike object) throws ServiceException;

    ComentarioWodLike alterar(final String ctx, ComentarioWodLike object) throws ServiceException;

    void excluir(final String ctx, ComentarioWodLike object) throws ServiceException;

    void excluirComentarioWodLike(final String ctx, ComentarioWod comentarioWod, Usuario usuario) throws ServiceException;

    void adicionarComentarioWodLike(final String ctx, ComentarioWod comentarioWod, Usuario usuario) throws ServiceException;

    boolean existeComentarioWodLike(final String ctx, ComentarioWod comentarioWod, Usuario usuario) throws ServiceException;

}
