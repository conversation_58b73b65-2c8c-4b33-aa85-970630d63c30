/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.pacto.service.intf.atividade;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.atividade.CategoriaAtividade;
import br.com.pacto.bean.atividade.CategoriaAtividadeResponseTO;
import br.com.pacto.bean.musculo.GrupoMuscularResumidoResponseTO;
import br.com.pacto.controller.json.atividade.FiltroCategoriaAtividadeJSON;
import br.com.pacto.controller.json.musculo.FiltroGrupoMuscularJSON;
import br.com.pacto.bean.atividade.CategoriaAtividadeTO;
import br.com.pacto.bean.musculo.GrupoMuscularResponseTO;
import br.com.pacto.bean.musculo.GrupoMuscularTO;
import br.com.pacto.controller.json.atividade.FiltroCategoriaAtividadeJSON;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.exception.ValidacaoException;

import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 */
public interface CategoriaAtividadeService {

    public static final String SERVICE_NAME = "CategoriaAtividadeService";

    public CategoriaAtividade inserir(final String ctx, CategoriaAtividade object) throws ServiceException, ValidacaoException;

    public CategoriaAtividade obterPorId(final String ctx, Integer id) throws ServiceException;

    public CategoriaAtividade alterar(final String ctx, CategoriaAtividade object) throws ServiceException, ValidacaoException;

    public void excluir(final String ctx, CategoriaAtividade object) throws ServiceException;

    public List<CategoriaAtividade> obterTodos(final String ctx) throws ServiceException;

    public List<CategoriaAtividade> obterPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException;

    public List<CategoriaAtividade> obterPorParam(final String ctx, String query,
                                                  Map<String, Object> params, int max, int index)
            throws ServiceException;

    public CategoriaAtividade obterObjetoPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException;

    public List<CategoriaAtividadeResponseTO> consultarTodos() throws ServiceException;

    List<CategoriaAtividadeResponseTO> consultarCategoriaAtividades(FiltroCategoriaAtividadeJSON filtroCategoriaAtividadeJSON, PaginadorDTO paginadorDTO) throws ServiceException;

    public CategoriaAtividadeResponseTO consultarCategoriaAtividade(Integer id) throws ServiceException;

    public void excluir(Integer id) throws ServiceException;

    public CategoriaAtividadeResponseTO inserir(CategoriaAtividadeTO categoriaAtividadeTO) throws ServiceException;

    public CategoriaAtividadeResponseTO alterar(Integer id, CategoriaAtividadeTO categoriaAtividadeTO) throws ServiceException;
}
