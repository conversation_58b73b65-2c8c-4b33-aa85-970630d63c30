/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.pacto.service.intf.cliente;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.controller.json.aluno.FiltroPessoaJSON;
import br.com.pacto.controller.json.gestao.ClienteColaboradoresDTO;
import br.com.pacto.service.exception.ServiceException;

import java.util.List;

public interface ClienteService {

    String obterClienteMensagem(Integer matricula, String tipoMensagem);

    void salvarClienteMensagem(Integer matricula, String tipoMensagem, String mensagem) throws ServiceException;

    void deletarClienteMensagem(Integer matricula, String tipoMensagem) throws ServiceException;

    List<ClienteColaboradoresDTO>  listaAlunosColaboradores(FiltroPessoaJSON filtroAlunoJSON, PaginadorDTO paginadorDTO, Integer empresaId) throws Exception;
}

