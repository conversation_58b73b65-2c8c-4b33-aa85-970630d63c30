package br.com.pacto.service.intf.tvGestor;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.controller.json.tvGestor.dto.AlunoDTO;
import br.com.pacto.controller.json.tvGestor.dto.BiTvGestorDTO;
import br.com.pacto.controller.json.tvGestor.dto.FiltroTvGestorJSON;
import br.com.pacto.controller.json.tvGestor.dto.TvGestorFiltroResponse;
import br.com.pacto.service.exception.ServiceException;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;
import java.util.Map;

public interface TvGestorService {

    Map<String, Map<String, BiTvGestorDTO>> biSemana(String chave, Integer empresa, FiltroTvGestorJSON filtro) throws ServiceException;
    Map<String, BiTvGestorDTO> biPeriodo(String chave, Integer empresa, FiltroTvGestorJSON filtro) throws ServiceException;
    List<AlunoDTO> getAlunosTvGestor(String chave, Integer empresa, FiltroTvGestorJSON filtro, PaginadorDTO paginator, HttpServletRequest request) throws ServiceException;
    List<AlunoDTO> biAcessos(String chave, Integer empresa, FiltroTvGestorJSON filtro, HttpServletRequest request) throws ServiceException;
    TvGestorFiltroResponse atualizarFiltros(String chave, Integer empresaId,Integer idUsuario, FiltroTvGestorJSON filtro, HttpServletRequest request) throws ServiceException;
    TvGestorFiltroResponse getFiltro(String chave, Integer empresaId,Integer idUsuario, HttpServletRequest request) throws ServiceException;
}
