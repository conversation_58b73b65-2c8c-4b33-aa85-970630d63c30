/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.intf.gestao;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.gestao.*;
import br.com.pacto.bean.notificacao.Notificacao;
import br.com.pacto.bean.professor.ProfessorSintetico;
import br.com.pacto.bean.programa.ExecucoesTreinoTO;
import br.com.pacto.bean.programa.ProgramaTreino;
import br.com.pacto.bean.programa.ProgramaTreinoAndamentoTO;
import br.com.pacto.controller.to.FiltrosGestaoTO;
import br.com.pacto.controller.json.gestao.FiltroGestaoProgramaDTO;
import br.com.pacto.service.exception.ServiceException;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public interface GestaoService {

    public static final String SERVICE_NAME = "GestaoService";

    public List<AgrupadorIndicadores> calcularIndicadores(HttpServletRequest request,final String ctx,PaginadorDTO paginadorDTO,
                                                          final Integer empresaZW, FiltrosGestaoTO filtro,
                                                          boolean ignorarZerados, CategoriaIndicadorEnum categoria, Boolean treinoIndependente, Boolean colaboraorId) throws ServiceException;

    ProfessorSintetico obterPorIdColaborador(final String ctx, Integer id) throws ServiceException;

    public Indicador calcularIndicador(final String ctx, final Integer empresaZW, IndicadorEnum tipo, final FiltrosGestaoTO filtro,
            final Integer codProfessor, final String nomeProfessor, ResultEnum result) throws ServiceException;

    public List<Notificacao> gestaoNotificacoes(String ctx, final Integer empresaZW,
            final FiltrosGestaoTO filtro) throws ServiceException;
    
    public List<ProgramaTreino> gestaoAndamento(String ctx, final Integer empresaZW, final FiltrosGestaoTO filtro) throws ServiceException;


    public List<ProgramaTreinoAndamentoTO> gestaoAndamento(String ctx, final Integer empresaZW, final FiltroGestaoProgramaDTO filtro, PaginadorDTO paginadorDTO) throws ServiceException;

    public List<ExecucoesTreinoTO> gestaoExecucoesTreino(String ctx, final Integer empresaZW, final FiltroGestaoProgramaDTO filtro, PaginadorDTO paginadorDTO) throws ServiceException;

    List<ClienteSintetico> carregarAlunosPorIndicadoresCarteiraProfessor(HttpServletRequest request, String ctx, PaginadorDTO paginadorDTO, Integer empresaZW, IndicadorEnum tipo, final String professoresSelecionados,
                                                                         final FiltrosGestaoTO filtro, Boolean treinoIndependente) throws Exception;

    List<ClienteSintetico> carregarAlunosPorAtividadeProfessor(final String ctx,
                                                              final Integer empresaZW, final FiltrosGestaoTO filtro, IndicadorEnum tipo,
                                                              final String professoresSelecionados) throws Exception;
}
