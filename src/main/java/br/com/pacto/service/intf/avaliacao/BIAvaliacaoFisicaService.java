package br.com.pacto.service.intf.avaliacao;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.bi.IndicadorAvaliacaoFisicaEnum;
import br.com.pacto.controller.json.avaliacao.BIAvaliacaoFisicaDTO;
import br.com.pacto.controller.json.avaliacao.bi_enum.TipoBIAvaliacaoEnum;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.avaliacao.BIAvaliacaoFisicaTO;
import br.com.pacto.service.impl.avaliacao.ItemAvaliacaoFisicaTO;

import java.util.Date;
import java.util.List;

/**
 * Created by alcides on 14/10/2017.
 */
public interface BIAvaliacaoFisicaService {

    static final String SERVICE_NAME = "BIAvaliacaoFisicaService";

    BIAvaliacaoFisicaTO montarBI(final String key, Integer codigoEmpresa, final Date inicio, final Date fim, Integer codigoProfessor, Integer codigoAvaliador) throws Exception;

    List<ItemAvaliacaoFisicaTO> listaBI(String ctx, Integer codigoEmpresa, IndicadorAvaliacaoFisicaEnum indicador, final Date inicio, final Date fim, final String obj, Integer codigoProfessor, String patametro, PaginadorDTO paginadorDTO, Integer codigoAvaliador) throws ServiceException;

    BIAvaliacaoFisicaDTO getBI(final String key, Integer codigoEmpresa, final Date inicio, final Date fim, Integer codigoProfessor, TipoBIAvaliacaoEnum tipoBIAvaliacaoEnum, Integer codigoAvaliador) throws ServiceException;

    void atualizarBase(final String key, Integer empresa);
}
