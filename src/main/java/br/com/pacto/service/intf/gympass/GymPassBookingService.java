/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.intf.gympass;

import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.bean.gympass.ConfigGymPass;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.gympass.dto.BookingsDTO;
import br.com.pacto.service.impl.gympass.dto.ClassesShowDTO;
import br.com.pacto.service.impl.gympass.dto.ProductDTO;
import br.com.pacto.service.impl.gympass.dto.SlotsDTO;
import br.com.pacto.service.impl.gympass.json.SlotDiaDTO;
import br.com.pacto.service.impl.gympass.json.TurmaGymPassJSON;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 22/03/2020
 */
public interface GymPassBookingService {

    public static final String SERVICE_NAME = "GymPassBookingService";

    TurmaGymPassJSON sincronizarTurma(String ctx, Empresa empresa, TurmaGymPassJSON turmaGymPassJSON) throws ServiceException;

    void sincronizarHorarios(String ctx, Date inicioSinc, Empresa empresa, TurmaGymPassJSON turmaGymPassJSON) throws ServiceException;

    String healthGympass(String ctx, Integer empresaTR) throws ServiceException;

    void enviarBooking(BookingsDTO bookingsDTO, String booking_number, ConfigGymPass configGymPass) throws Exception;

    String excluirHorarios(String ctx, Empresa empresa, boolean excluirTodas, TurmaGymPassJSON turmaGymPassJSON) throws ServiceException;

    String excluirHorarioEspecifico(String ctx, Empresa empresa, TurmaGymPassJSON turmaGymPassJSON, Integer codigoHorarioTurmaExcluir, Integer codigoTurma) throws ServiceException;

    List<ClassesShowDTO> aulas(String ctx, Integer empresa) throws Exception;

    Map<String, List<SlotsDTO>> horarios(String ctx, Integer empresa, String dia, Integer idClasse) throws Exception;

    List<ClassesShowDTO> inativarUmaAula(String ctx, Integer empresacod, String reference, Integer exceto) throws Exception;

    void normalizar(String ctx, Integer empresacod) throws Exception;

    void excluirSlotsDeUmDia(String ctx, Integer empresaid, Date dia, Integer turmaId);

    List<ProductDTO> listarProdutos(Integer empresa) throws Exception;

    List<SlotDiaDTO> consultarSlotsGympass(String chave, Date inicio, Date fim) throws Exception;

    void excluirSlots(String ctx, Integer empresaid, Integer slot, Integer idClass);
}
