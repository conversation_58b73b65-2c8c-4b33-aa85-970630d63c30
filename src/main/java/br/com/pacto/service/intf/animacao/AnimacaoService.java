/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.pacto.service.intf.animacao;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.bean.animacao.Animacao;
import br.com.pacto.bean.animacao.NomeAnimacao;
import br.com.pacto.bean.atividade.AtividadeImagemUploadTO;
import br.com.pacto.bean.imagem.ImagemResponseTO;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.controller.to.UploadedFile;

import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public interface AnimacaoService {

    public static final String SERVICE_NAME = "AnimacaoService";

    public Animacao inserir(final String ctx, Animacao object) throws ServiceException;

    public Animacao obterPorId(final String ctx, Integer id) throws ServiceException;

    public Animacao alterar(final String ctx, Animacao object) throws ServiceException;

    public void excluir(final String ctx, Animacao object) throws ServiceException;

    public List<Animacao> obterTodos(final String ctx) throws ServiceException;

    public List<Animacao> obterPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException;

    public List<Animacao> obterPorParam(final String ctx, String query,
            Map<String, Object> params, int max, int index)
            throws ServiceException;

    public Animacao obterObjetoPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException;
    
    public Animacao obterAnimacaoPorNomeArquivo(String ctx, String nomeArquivo) throws ServiceException;
    
    public void deleteAtividadeAnimacao(String ctx,Integer codigoAtividade, String urlsAnimacoes) throws ServiceException;
    
    public NomeAnimacao inserirNome(final String ctx, NomeAnimacao object) throws ServiceException;
    
    public NomeAnimacao alterarNome(final String ctx, NomeAnimacao object) throws ServiceException;
    
    public NomeAnimacao consultarNomeAnimacao(final String ctx, final String url) throws ServiceException;
    
    public Map<String, String> consultarNomesAnimacoes(final String ctx) throws ServiceException;
    
    public void excluirNome(final String ctx, NomeAnimacao object) throws ServiceException;

    public Integer checarLarguraImagem(UploadedFile uploadedFile) throws Exception;

    public byte[] redimensionarImagemGrande(UploadedFile uploadedFile, Integer limite) throws Exception;

    List<ImagemResponseTO> listarImagens() throws ServiceException;

    public byte[] redimensionarImagemGrandeEndpoint(AtividadeImagemUploadTO atividadeImagemUploadTO, Integer limite) throws Exception;
}
