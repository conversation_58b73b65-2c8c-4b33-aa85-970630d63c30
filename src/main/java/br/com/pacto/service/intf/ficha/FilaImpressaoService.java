/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.intf.ficha;

import br.com.pacto.bean.ficha.FilaImpressao;
import br.com.pacto.bean.sincronizacao.ObjetoSincronizarTO;
import br.com.pacto.bean.sincronizacao.TipoObjetoSincronizarEnum;
import br.com.pacto.service.exception.ServiceException;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public interface FilaImpressaoService {

    String SERVICE_NAME = "FilaImpressaoService";

    FilaImpressao enfileirar(final String ctx, FilaImpressao object) throws ServiceException;

    ObjetoSincronizarTO enfileirar(final String ctx, final TipoObjetoSincronizarEnum tipo,
     final String chaveBusca) throws ServiceException;

    void excluirTodosFilaImpressao(final String ctx) throws ServiceException;
    
    void excluirTodosSincronizar(final String ctx) throws ServiceException;

    void excluirTodosSincronizar(final String ctx, final Integer empresa) throws ServiceException;

    List<FilaImpressao> obterTodosFilaImpressao(final String ctx) throws ServiceException;
    
    List<ObjetoSincronizarTO> obterTodosSincronizar(final String ctx, final Integer empresaZW) throws ServiceException;

    Number count(final String ctx) throws ServiceException;
    
    Number countSync(final String ctx) throws ServiceException;
}
