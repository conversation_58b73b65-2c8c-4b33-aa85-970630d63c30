package br.com.pacto.service.intf.graduacao;

import br.com.pacto.controller.json.graduacao.ImpressaoAvaliacaoProgressoAlunoDTO;

import javax.servlet.http.HttpServletRequest;

public interface GraduacaoService {

    public static final String SERVICE_NAME = "GraduacaoService";

    public String gerarPdfAvaliacaoProgressoV2(Integer matricula,
                                               Integer alunoId,
                                               Integer fichaId,
                                               Integer avaliacaoAlunoRespondidaId,
                                               HttpServletRequest request) throws Exception;

    public String gerarPdfAvaliacaoProgresso(final String ctx,
                                      HttpServletRequest request,
                                      boolean externo,
                                      ImpressaoAvaliacaoProgressoAlunoDTO dadosImprimirDTO,
                                      String urlAplicacao) throws Exception;

}
