/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.pacto.service.intf.gympass;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.gympass.LogGymPass;
import br.com.pacto.bean.gympass.LogGymPassTO;
import br.com.pacto.service.exception.ServiceException;

import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 12/05/2020
 */
public interface LogGymPassService {

    public static final String SERVICE_NAME = "LogGymPassService";

    LogGymPass incluir(final String ctx, String idTurma, String log, String json) throws ServiceException;

    List<LogGymPass> ultimos(final String ctx) throws Exception;

    List<LogGymPassTO> listarLog(PaginadorDTO paginadorDTO) throws ServiceException;

    List<LogGymPass> listarLog(String bookingId) throws Exception;



}
