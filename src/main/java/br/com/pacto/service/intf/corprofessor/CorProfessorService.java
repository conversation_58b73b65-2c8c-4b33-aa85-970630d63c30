package br.com.pacto.service.intf.corprofessor;

import br.com.pacto.bean.corprofessor.CorProfessor;
import br.com.pacto.service.exception.ServiceException;
import java.util.List;
import java.util.Map;
/**
 * Created by Rafael on 18/09/2015.
 */

public interface CorProfessorService {
    public static final String SERVICE_NAME = "CorProfessorService";

    public CorProfessor inserir(final String ctx, CorProfessor object) throws ServiceException;

    public CorProfessor obterPorId(final String ctx, Integer id) throws ServiceException;

    public CorProfessor alterar(final String ctx, CorProfessor object) throws ServiceException;

    public void excluir(final String ctx, CorProfessor object) throws ServiceException;

    public List<CorProfessor> obterTodos(final String ctx) throws ServiceException;

    public List<CorProfessor> obterPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException;

    public List<CorProfessor> obterPorParam(final String ctx, String query,
                                    Map<String, Object> params, int max, int index)
            throws ServiceException;

    public CorProfessor obterObjetoPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException;
    
    public CorProfessor obterPorProfessor(String ctx, Integer codigoProfessor) throws ServiceException;


}
