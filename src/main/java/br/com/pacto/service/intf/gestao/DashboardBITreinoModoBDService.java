package br.com.pacto.service.intf.gestao;

import br.com.pacto.bean.bi.AcessosExecucoesJSON;
import br.com.pacto.bean.bi.FiltrosDashboard;

import java.sql.SQLException;
import java.util.Date;
import java.util.List;

public interface DashboardBITreinoModoBDService {

    String consultarDadosBITreino(final String key,
                                  final String dataInicio,
                                  final String dataFim,
                                  final String dataInicioAvencer,
                                  final String dataFimAvencer,
                                  final Integer professor,
                                  final Integer empresaZW) throws Exception;

    List<AcessosExecucoesJSON> consultarAcessosZW(String key, Integer professor, FiltrosDashboard filtrosDashboard, Date fim) throws SQLException;

    List<Integer> consultarAlunosCancelados(String chave, Integer empresa, Integer mes, Integer ano, Integer professor) throws Exception;
}
