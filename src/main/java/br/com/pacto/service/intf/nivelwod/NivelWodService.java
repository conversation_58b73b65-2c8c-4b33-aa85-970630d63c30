/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.intf.nivelwod;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.nivelwod.NivelWod;
import br.com.pacto.bean.wod.FiltroNivelWodJSON;
import br.com.pacto.bean.wod.NivelWodResponseTO;
import br.com.pacto.bean.wod.NivelWodTO;
import br.com.pacto.service.exception.ServiceException;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
public interface NivelWodService {
    
    public static final String SERVICE_NAME = "NivelWodService";
    List<NivelWodResponseTO> listarNiveisWod(FiltroNivelWodJSON filtroNivelWodJSON, PaginadorDTO paginadorDTO) throws ServiceException;

    NivelWodResponseTO buscarNivelWod(Integer id) throws ServiceException;

    NivelWodResponseTO cadastrarNivelWod(NivelWodTO nivelWodTO) throws ServiceException;

    NivelWodResponseTO editarNivelWod(NivelWodTO nivelWodTO) throws ServiceException;

    NivelWodResponseTO reverterNivelWod(Integer id) throws ServiceException;

    void excluirNivelWod(Integer id) throws ServiceException;

    NivelWod obterPorCodigo(String ctx, Integer codigo) throws ServiceException;

    NivelWod obterPorNome(String ctx, String nome) throws ServiceException;

    List<NivelWodResponseTO> listarNiveisWodPorChave(String ctx) throws ServiceException;
}
