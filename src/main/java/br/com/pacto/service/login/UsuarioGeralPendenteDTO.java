package br.com.pacto.service.login;

import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class UsuarioGeralPendenteDTO {

    private String chave;
    private String email;
    private boolean definirSenha = false;
    private UsuarioGeralDTO usuarioGeral;

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public UsuarioGeralDTO getUsuarioGeral() {
        return usuarioGeral;
    }

    public void setUsuarioGeral(UsuarioGeralDTO usuarioGeral) {
        this.usuarioGeral = usuarioGeral;
    }

    public String getChave() {
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    public boolean isDefinirSenha() {
        return definirSenha;
    }

    public void setDefinirSenha(boolean definirSenha) {
        this.definirSenha = definirSenha;
    }
}
