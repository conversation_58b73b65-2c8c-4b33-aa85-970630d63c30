package br.com.pacto.service.login;

import br.com.pacto.base.util.ExecuteRequestHttpService;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.JSONMapper;
import br.com.pacto.service.discovery.ClientDiscoveryDataDTO;
import br.com.pacto.service.discovery.DiscoveryMsService;
import br.com.pacto.util.UteisValidacao;
import org.json.JSONObject;

import java.util.HashMap;

public class LoginMsService {

    public static UsuarioGeralDTO saveUsuario(ClientDiscoveryDataDTO clientDiscoveryDataDTO, UsuarioGeralDTO usuarioGeralDTO) throws Exception {
        if (clientDiscoveryDataDTO == null) {
            clientDiscoveryDataDTO = DiscoveryMsService.urls();
        }
        String urlLoginBack = clientDiscoveryDataDTO.getServiceUrls().getLoginAppUrl();
        String url = (urlLoginBack + "/prest/usuario/v2/save");
        String response = ExecuteRequestHttpService.post(url, new JSONObject(usuarioGeralDTO).toString(), new HashMap<>());
        if (!new JSONObject(response).has("content")) {
            throw new Exception(response);
        }
        return JSONMapper.getObject(new JSONObject(response).getJSONObject("content"), UsuarioGeralDTO.class);
    }

}
