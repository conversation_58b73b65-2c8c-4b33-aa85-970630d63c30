package br.com.pacto.service.telegram;

import br.com.pacto.objeto.Uteis;

import java.io.BufferedInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.URL;
import java.net.URLConnection;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

public class TelegramService {

    public static void enviarErro(String ctx, Exception e, String content){
        String msg = "ERRO: " + ctx + "-" + e.getMessage() + " \nconteudo: " + content;
        sendToTelegramTreino(msg, "-1001353275435");
    }

    public static void enviarMsg(String ctx, String content){
        if(ctx.equals("67f6ab171119549036f6e4d07647d062")
            || ctx.equals("654a93bcd57f411e0ce5a456376cef53")
            || ctx.equals("acadvidaativaunidarturbernardesrj")
            || ctx.equals("c4f0ab01248752b846def0b4c8cba16c")){
            sendToTelegramTreino(ctx + ": " + content, "-1001353275435");
        }
    }

    public static void sendToTelegram(String message, String chatId) {
        System.out.println("enviar erro telegram");
        String urlString = "https://api.telegram.org/bot%s/sendMessage?chat_id=%s&text=%s";

        //Add Telegram token (given Token is fake)
        String apiToken = "1225924271:AAHGPakQgGJpW-OEO9ezvrsx8GDCqj1qAvU";

        urlString = String.format(urlString, apiToken, chatId, Uteis.encodeValue(message));

        try {
            URL url = new URL(urlString);
            URLConnection conn = url.openConnection();
            InputStream is = new BufferedInputStream(conn.getInputStream());
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public static void main(String[] args) {
        sendToTelegramTreino("teste mensagem  \"categoriaFicha\" : \"\",\n" +
                "  \"nivel\" : {\n" +
                "    \"ordem\" : 6,\n" +
                "    \"nome\" : \"AVANÇADO 1\",\n" +
                "    \"codigo\" : 5\n" +
                "  },\n" +
                "  \"categoria\" : 1,\n" +
                "  \"codigo\" : 13,", "-1001353275435");
    }



    public static void sendToTelegramTreino(String message, String chatId) {
        try {
            String urlString = "https://api.telegram.org/bot%s/sendMessage?chat_id=%s&text=%s";

            //Add Telegram token (given Token is fake)
            String apiToken = "1626329984:AAG8lCUqbLAW-cYlsgtLBgk8jwd4ZwxeZC0";

            urlString = String.format(urlString, apiToken, chatId, Uteis.encodeValue(message));

            try {
                URL url = new URL(urlString);
                URLConnection conn = url.openConnection();
                InputStream is = new BufferedInputStream(conn.getInputStream());
            } catch (IOException e) {
                e.printStackTrace();
            }
        }catch (Exception e){
            e.printStackTrace();
        }

    }

}
