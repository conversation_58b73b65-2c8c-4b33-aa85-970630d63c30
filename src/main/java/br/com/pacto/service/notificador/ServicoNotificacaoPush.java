package br.com.pacto.service.notificador;

import br.com.pacto.dto.notificacao.NotificacaoAppDoGestorDTO;
import br.com.pacto.util.UteisValidacao;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ServicoNotificacaoPush {

    private static String URL_NOTIFICACAO_APP = "https://us-central1-appgestor-3f25c.cloudfunctions.net/comunicacao/mandarNotificacao";
    private static String TITULO_AVALIACAO_PROFESSOR = "Nota menor que 3 em avaliação";

    private static void enviaNotificacaoApp(List<NotificacaoAppDoGestorDTO> notificacaoApp) {
        try {
            if (notificacaoApp.size() > 0) {
                System.out.println("Enviando notificacao ");
                RestTemplate restTemplate = new RestTemplate(new HttpComponentsClientHttpRequestFactory());
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.APPLICATION_JSON);

                HttpEntity<List<NotificacaoAppDoGestorDTO>> requestPush = new HttpEntity<>(notificacaoApp, headers);
                ResponseEntity<String> response = restTemplate.exchange(URL_NOTIFICACAO_APP, HttpMethod.PATCH, requestPush, String.class);
                System.out.println(response.getBody());
            }
        } catch (Exception ignore) {
            System.out.println(ignore.getMessage());
        }
    }

    public static void enviaNotificacaoAvaliacao(String key, Integer codEmpresa, Boolean geral, String nome, String nomeEmpresa) {
        try {
            NotificacaoAppDoGestorDTO notificacaoApp = new NotificacaoAppDoGestorDTO();
            notificacaoApp.setChave(key);
            notificacaoApp.setCodEmpresa(codEmpresa);
            notificacaoApp.setGeral(geral);
            notificacaoApp.setTitulo(TITULO_AVALIACAO_PROFESSOR);
            if(!UteisValidacao.emptyString(nomeEmpresa)) {
                notificacaoApp.setNomeEmpresa(nomeEmpresa);
            }
            notificacaoApp.setContent("O professor " + nome + " recebeu nota abaixo da média na avaliação de Treino. Entre em contato com o aluno que realizou a avaliação para entender os motivos.");
            notificacaoApp.setHoraEnvioNotificacao(getDataFormatadaNotificacao());
            List<NotificacaoAppDoGestorDTO> notificacaoAppList = new ArrayList<>();
            notificacaoAppList.add(notificacaoApp);
            enviaNotificacaoApp(notificacaoAppList);
        } catch (Exception ignore) {
            System.out.println(ignore.getMessage());
        }
    }

    private static String getDataFormatadaNotificacao() {
        SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");
        String dataFormatada = dateFormat.format(new Date(System.currentTimeMillis()));
        return dataFormatada;
    }

}
