package br.com.pacto.service.notificador;


import br.com.pacto.util.bean.RecursoEmpresaTO;

import java.util.ArrayList;
import java.util.List;

/**
 * @autor wendersonn
 */
public class NotificadorRecursoEmpresaServiceControle {

    public static List<RecursoEmpresaTO> notificacoes = new ArrayList();


    public static void adicionarNotificacao(RecursoEmpresaTO notf) {
        notificacoes.add(notf);
    }

    public static void removerItem(RecursoEmpresaTO recursoEmpresaTO) {
        notificacoes.remove(recursoEmpresaTO);

    }
}
