package br.com.pacto.service.discovery;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class EmpresaFinanceiroDTO {
    private Integer empresazw;
    private Integer codigoFinanceiro;
    private String nome;
    private String cidade;
    private String estado;
    private String pais;
    private Integer redeEmpresaId;
    private String chaveRede;

    public Integer getEmpresazw() {
        return empresazw;
    }

    public void setEmpresazw(Integer empresazw) {
        this.empresazw = empresazw;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Integer getCodigoFinanceiro() {
        return codigoFinanceiro;
    }

    public void setCodigoFinanceiro(Integer codigoFinanceiro) {
        this.codigoFinanceiro = codigoFinanceiro;
    }

    public String getCidade() {
        return cidade;
    }

    public void setCidade(String cidade) {
        this.cidade = cidade;
    }

    public String getEstado() {
        return estado;
    }

    public void setEstado(String estado) {
        this.estado = estado;
    }

    public String getPais() {
        return pais;
    }

    public void setPais(String pais) {
        this.pais = pais;
    }

    public Integer getRedeEmpresaId() {
        return redeEmpresaId;
    }

    public void setRedeEmpresaId(Integer redeEmpresaId) {
        this.redeEmpresaId = redeEmpresaId;
    }

    public String getChaveRede() {
        return chaveRede;
    }

    public void setChaveRede(String chaveRede) {
        this.chaveRede = chaveRede;
    }
}
