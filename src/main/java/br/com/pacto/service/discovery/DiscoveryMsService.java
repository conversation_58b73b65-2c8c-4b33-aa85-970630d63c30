package br.com.pacto.service.discovery;

import br.com.pacto.base.util.ExecuteRequestHttpService;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.JSONMapper;
import br.com.pacto.util.UteisValidacao;
import org.json.JSONObject;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class DiscoveryMsService {

    private static final Map<String, ClientDiscoveryDataDTO> cache = new ConcurrentHashMap<>();

    public static String baseUrl() throws DiscoveryException {
        String url = Aplicacao.getProp(Aplicacao.discoveryUrls);
        if (url.equals("@DISCOVERY_URL@")) {
            throw new DiscoveryException("A propriedade @DISCOVERY_URL@ n�o est� definida");
        }
        return url;
    }

    public static ClientDiscoveryDataDTO urls() throws Exception {
        String cacheKey = "urls";
        if (cache.containsKey(cacheKey)) {
            return cache.get(cacheKey);
        }

        String url = baseUrl() + "/find";
        System.out.println(baseUrl());

        String response = ExecuteRequestHttpService.get(url, new HashMap<>());
        ClientDiscoveryDataDTO data = JSONMapper.getObject(new JSONObject(response).getJSONObject("content"), ClientDiscoveryDataDTO.class);
        cache.put(cacheKey, data);
        return data;
    }

    public static ClientDiscoveryDataDTO urlsChave(String chave) throws Exception {
        if (cache.containsKey(chave)) {
            return cache.get(chave);
        }

        String url = baseUrl() + "/find/" + chave;
        String response = ExecuteRequestHttpService.get(url, new HashMap<>());
        ClientDiscoveryDataDTO data = JSONMapper.getObject(new JSONObject(response).getJSONObject("content"), ClientDiscoveryDataDTO.class);
        cache.put(chave, data);
        return data;
    }

    public static void clearnCache(String key) {
        if (UteisValidacao.emptyString(key)) {
            cache.clear();
        } else {
            cache.remove(key);
        }
    }

    public static ClientDiscoveryDataDTO urlsChaveRedeEmpresa(String chave) throws Exception {
        String url = baseUrl() + "/find-url-rede/" + chave;

        String response = ExecuteRequestHttpService.get(url, new HashMap<>());
        return JSONMapper.getObject(new JSONObject(response).getJSONObject("content"), ClientDiscoveryDataDTO.class);
    }

    public static List<RedeDTO> urlsRede(String chave) throws Exception {
        String url = baseUrl() + "/find/rede/" + chave;

        String response = ExecuteRequestHttpService.get(url, new HashMap<>());
        return JSONMapper.getList(new JSONObject(response).getJSONArray("content"), RedeDTO.class);
    }

    public static boolean integranteRedeEmpresa(String chave) throws Exception {
        String url = baseUrl() + "/find-integrante-rede/" + chave;

        String response = ExecuteRequestHttpService.get(url, new HashMap<>());
        Map<String, Boolean> dados = JSONMapper.getObject(new JSONObject(response).getJSONObject("content"), Map.class);
        if (dados.containsKey("integranteRedeEmpresa")) {
            return dados.get("integranteRedeEmpresa");
        } else {
            return false;
        }
    }
}
