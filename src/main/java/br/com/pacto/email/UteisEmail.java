package br.com.pacto.email;

import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.util.ConfigsEmail;
import org.json.JSONObject;
import servicos.integracao.zw.client.ConsistirException;

import javax.activation.DataHandler;
import javax.activation.DataSource;
import javax.activation.FileDataSource;
import javax.imageio.ImageIO;
import javax.mail.AuthenticationFailedException;
import javax.mail.BodyPart;
import javax.mail.Message;
import javax.mail.Part;
import javax.mail.PasswordAuthentication;
import javax.mail.Session;
import javax.mail.Transport;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeBodyPart;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeMultipart;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR> Maciel
 */
public class UteisEmail {

    private static final long serialVersionUID = 3048499584639227602L;
    private String emailRemet;
    private String nomeRemet;
    private String assunto;
    private String smtpPadrao;
    private String loginServidorSmtp;
    private String senhaServidorSmtp;
    private boolean conexaoSegura;
    private boolean iniciarTLS;
    private Map<String, File> mapaAnexo = new HashMap<String, File>();
    private List<ImageEmailHtml> listaImagem = new ArrayList<ImageEmailHtml>();
    private static final String SSL_FACTORY = "javax.net.ssl.SSLSocketFactory";
    private static final int timeout_ms = 5000;

    public UteisEmail() throws Exception {
    }

    /**
     * - Recupera as configurações de email, obtidas em configuracaoSistema. -
     * Recupera o nome do remetente padrão, o nome da instituição educacional. -
     * Se necessário formata a mensagem utilizando o template HTML padrão.
     *
     * @throws Exception
     */
    public void inicializarDados(final String remetente, final String assunto) throws Exception {
        this.emailRemet = remetente;
        this.assunto = assunto;
        mapaAnexo = new HashMap<String, File>();
        listaImagem = new ArrayList<ImageEmailHtml>();
    }

    public void preencherConfiguracaoEmailPadrao(final String login,
            final String senha, final String hostName, final String emailRemetente,
            final String nomeRemetente, boolean conexaoSegura, boolean iniciarTLS) throws Exception {
        // Setando configurações de email padrão
        this.loginServidorSmtp = login;
        this.senhaServidorSmtp = senha;
        this.smtpPadrao = hostName;
        this.emailRemet = emailRemetente;
        this.nomeRemet = nomeRemetente;
        this.conexaoSegura = conexaoSegura;
        this.iniciarTLS = iniciarTLS;

        if (smtpPadrao == null || loginServidorSmtp == null || senhaServidorSmtp == null || emailRemet == null) {
            // verifico mais uma vez se existe uma configuracao de e-mail
            throw new Exception("");
        }
    }

    public void preencherConfiguracaoEmailPadrao(JSONObject configsEmail, String nome) throws Exception {
        // Setando configurações de email padrão
        loginServidorSmtp = configsEmail.getString("loginServidorSmtp");
        senhaServidorSmtp = configsEmail.getString("senhaServidorSmtp");
        smtpPadrao = configsEmail.getString("smtpPadrao");
        emailRemet = configsEmail.getString("emailRemet");
        nomeRemet = nome;
        conexaoSegura = configsEmail.getBoolean("conexaoSegura");
        iniciarTLS = configsEmail.getBoolean("iniciarTLS");

        if (smtpPadrao == null || loginServidorSmtp == null || senhaServidorSmtp == null || emailRemet == null) {
            // verifico mais uma vez se existe uma configuracao de e-mail
            throw new Exception("");
        }
    }

    public void preencherConfiguracaoEmailPadrao(final ConfigsEmail config) throws Exception {
        // Setando configurações de email padrão
        this.loginServidorSmtp = config.getLogin();
        this.senhaServidorSmtp = config.getSenha();
        this.smtpPadrao = config.getMail_server();
        this.emailRemet = config.getEmail_padrao();
        this.nomeRemet = config.getRemetente();
        this.conexaoSegura = config.isConexao_segura();
        this.iniciarTLS = config.isIniciarTLS();

        if (smtpPadrao == null || loginServidorSmtp == null || senhaServidorSmtp == null || emailRemet == null) {
            // verifico mais uma vez se existe uma configuracao de e-mail
            throw new Exception("");
        }
    }

    /**
     * Adiciona um anexo ao email.
     *
     * @param nomeAnexo
     * @param anexo
     * @return
     */
    public UteisEmail addAnexo(String nomeAnexo, File anexo) {
        this.mapaAnexo.put(nomeAnexo, anexo);
        return this;
    }

    /**
     * Adiciona todos os arquivos contidos no diretorio informado na lista de
     * imagens.
     *
     * @param diretorioImagens
     * @return
     */
    public UteisEmail addImagensEmDiretorio(ImageEmailHtml diretorioImagens) {
        listaImagem.add(diretorioImagens);
        return this;
    }

    /**
     * Adiciona um arquivo de imagem ao email.
     *
     * @param imagem
     * @return
     */
    public UteisEmail addImagem(ImageEmailHtml imagem) {
        this.listaImagem.add(imagem);
        return this;
    }

    /**
     * Metodo que cria uma imagem a partir de um array de bytes no caminho que
     * foi passado com o nome passado.
     *
     * Obs.: - Caso o diretorio nao exista ele cria;
     *
     * Autor: Pedro Y. Saito Criado em 08/04/2011
     */
    public static void criarImagem(String path, byte[] imagem, String arquivo) throws Exception {
        if ((path == null) || ("".equals(path))) {
            return;
        }
        if ((imagem == null) || (imagem.length <= 0)) {
            return;
        }
        if ((arquivo == null) || ("".equals(arquivo))) {
            return;
        }

        //Criando um apontamento para o diretorio
        File arq = new File(path);
        //Verificando se o diretorio existe
        if (!arq.exists()) {
            //Criando o diretorio
            arq.mkdirs();
        }

        path = path.replaceAll("\\\\", "/");

        arq = new File(path + arquivo.trim());

        if (!arq.exists()) {
            InputStream in = new ByteArrayInputStream(imagem);
            BufferedImage outImage = ImageIO.read(in);

            if (outImage != null) {
                String[] extensao = arquivo.split("\\.");
                if ("png".equalsIgnoreCase(extensao[extensao.length - 1])) {
                    ImageIO.write(outImage, "PNG", arq);
                } else {
                    ImageIO.write(outImage, "JPEG", arq);
                }
            }
        }
    }

    /**
     * Método que envia um email, para um destinatário por vez.
     *
     * @throws ConsistirException
     * @throws Exception
     */
    public void enviarEmail(String emailDest, String nomeDest, String mensagem, String nomeEmpresa) throws Exception {
        if (assunto == null){
            assunto = "(SEM ASSUNTO)";
        }
        enviarEmail(emailDest, nomeDest, mensagem, nomeEmpresa, assunto);
    }
    public void enviarEmail(String emailDest, String nomeDest, String mensagem, String nomeEmpresa, String assunto) throws Exception {
        this.assunto = assunto;
        this.send(new String[]{emailDest}, mensagem, assunto, false, nomeEmpresa, null);
    }

    public void enviarEmail(String emailDest, String nomeDest, String mensagem, String nomeEmpresa, MimeBodyPart pdf, String assunto) throws Exception {
        if (assunto == null){
            assunto = "(SEM ASSUNTO)";
        }
        this.send(new String[]{emailDest}, mensagem, assunto, false, nomeEmpresa, pdf);
    }

    /**
     * Método que envia um e-mail para vários destinatários com BCC (Cópia
     * Oculta) ou não de uma só vez.
     *
     * @throws ConsistirException
     * @throws Exception
     */
    public void enviarEmailN(String[] emailDest, String mensagem,
            String oAssunto, String nomeEmpresa) throws Exception {
        this.send(emailDest, mensagem, oAssunto, true, nomeEmpresa, null);
    }

    public void enviarEmailN(String[] emailDest, String mensagem,
            String oAssunto, String nomeEmpresa, MimeBodyPart pdf) throws Exception {
        this.send(emailDest, mensagem, oAssunto, true, nomeEmpresa, pdf);
    }

    private void send(String[] emailDest, String mensagem,
            String oAssunto, boolean copiaOculta, String nomeEmpresa, MimeBodyPart pdf) throws Exception {
        if (emailDest != null && emailDest.length > 0) {
            boolean debug = false;
            Session session = null;
            try {
                String port = (conexaoSegura ? "465" : "587");
                Properties props = new Properties();
                props.put("mail.smtp.host", smtpPadrao);
                props.put("mail.smtp.auth", "true");
                props.put("mail.debug", debug);
                props.put("mail.smtp.port", port);
                props.put("mail.smtp.socketFactory.port", port);
                props.put("mail.smtp.connectiontimeout", timeout_ms);
                props.put("mail.smtp.timeout", timeout_ms);
                props.put("mail.smtp.writetimeout", timeout_ms);
                if (iniciarTLS) {
                    props.put("mail.smtp.starttls.enable", "true");
                }
                //caso usar SSL
                if (conexaoSegura) {
                    props.put("mail.smtp.socketFactory.class", SSL_FACTORY);
                }

                session = Session.getInstance(props,
                        new javax.mail.Authenticator() {
                    protected PasswordAuthentication getPasswordAuthentication() {
                        return new PasswordAuthentication(loginServidorSmtp.trim(),
                                senhaServidorSmtp.trim());
                    }
                });
                // com essa flag aqui ele imprimira todos os
                // dados da conexão e do envio,
                // setar isso para false se quiser que rode
                // no silent mode.
                session.setDebug(debug);

                MimeMultipart mpRoot = new MimeMultipart("mixed");
                MimeMultipart mpContent = new MimeMultipart("related");
                MimeBodyPart contentPartRoot = new MimeBodyPart();
                contentPartRoot.setContent(mpContent);
                mpRoot.addBodyPart(contentPartRoot);
                if(pdf != null){
                    mpRoot.addBodyPart(pdf);
                }


                //enviando html
                MimeBodyPart mbp1 = new MimeBodyPart();
                listaImagem = gerarListaImagensHtmlParaEmail(mensagem);

                // enviando anexo
                if (!mapaAnexo.isEmpty()) {
                    for (String nomeAnexo : mapaAnexo.keySet()) {
                        File anexo = mapaAnexo.get(nomeAnexo);

                        MimeBodyPart mbp2 = new MimeBodyPart();
                        DataSource fds = new FileDataSource(anexo);
                        mbp2.setDisposition(Part.ATTACHMENT);
                        mbp2.setDataHandler(new DataHandler(fds));
                        mbp2.setFileName(nomeAnexo);
                        mpRoot.addBodyPart(mbp2);
                    }
                }
                List<BodyPart> bp = new ArrayList<BodyPart>();
                // adicionando as imagens do html
                if (!listaImagem.isEmpty()) {
                    for (ImageEmailHtml imagem : listaImagem) {
                        MimeBodyPart imagePart = new MimeBodyPart();
                        imagePart.attachFile(imagem.getArquivo());
                        imagePart.setHeader("Content-ID", "<" + imagem.getArquivo().getName() + ">");
                        bp.add(imagePart);

                        mensagem = mensagem.replace("src=\"" + imagem.getCaminhoReplace(),
                                "src=\"cid:" + imagem.getArquivo().getName());
                    }
                }

                mbp1.setContent(mensagem, "text/html");
                mpContent.addBodyPart(mbp1);
                for (BodyPart imagePart : bp) {
                    mpContent.addBodyPart(imagePart);
                }
                //preparar definitivamente o envio
                //
                //definir quem são os endereços destinatários
                InternetAddress[] emails = new InternetAddress[emailDest.length];
                for (int i = 0; i < emailDest.length; i++) {
                    String email = emailDest[i];
                    InternetAddress ia = new InternetAddress(email);
                    emails[i] = ia;
                }


                MimeMessage message = new MimeMessage(session);
                message.setSentDate(Calendario.hoje());
                message.setFrom(new InternetAddress(emailRemet, ((nomeEmpresa == null || nomeEmpresa.isEmpty()) ? "" : nomeEmpresa + " - ") + nomeRemet));
                message.addRecipients(copiaOculta ? Message.RecipientType.BCC
                        : Message.RecipientType.TO, emails);
                message.setSubject(oAssunto);

                message.setContent(mpRoot);
                message.saveChanges();
                //enviar!
                Transport.send(message);
            } catch (AuthenticationFailedException e) {
                Uteis.logar(e, UteisEmail.class);
                throw new Exception("As configurações de email estão incorretas, entre em contato com o administrador.");
            }
        }
    }

    public static boolean getValidEmail(String email) {
        Pattern p = Pattern.compile("^[\\w-]+(\\.[\\w-]+)*@([\\w-]+\\.)+[a-zA-Z]{2,7}$");
        Matcher m = p.matcher(email);
        if (m.find()) {
            return true;
        } else {
            return false;
        }
    }

    protected void limparRecursosMemoria() {
        this.emailRemet = null;
        this.nomeRemet = null;
        this.assunto = null;
        this.smtpPadrao = null;
        this.loginServidorSmtp = null;
        this.senhaServidorSmtp = null;
    }

    public String getAssunto() {
        return assunto;
    }

    public String getEmailRemet() {
        return emailRemet;
    }

    public String getNomeRemet() {
        return nomeRemet;
    }

    /**
     * Operação responsável por varrer um conteúdo html procurando tags <img> e
     * gerar uma lista de imagens presentes nesse contexto. Este método utiliza
     * Regex (Regular Expressions).
     *
     * @param html Objeto da classe <code>String</code> que servirá de base para
     * a varredura.
     * regular.
     */
    public List gerarListaImagensHtmlParaEmail(String html) {

        ArrayList<ImageEmailHtml> lista = new ArrayList<ImageEmailHtml>();

        String source = html;

        //String regexTag = "<?\\s*\\w*\\s*src='?\\s*([\\w\\s%#\\/\\.;:_-])'?.*?>";
        String regexTag = "<?\\s*\\w*\\s*src\\s*=\\s*'?\\s*([\\w\\s%#\\/\\.;:_-]*)\\s*'?.*?>";

        Pattern pattern = Pattern.compile(regexTag);

        Matcher matcher = pattern.matcher(source);

        // Mostra as similaridades
        while (matcher.find()) {

            String temp = source.substring(matcher.start(), matcher.end());
            int ini = temp.indexOf("src=");
            int fim = temp.indexOf("\"", ini);
            fim = temp.indexOf("\"", fim + 1);
            String caminhoArquivo = temp.substring(ini + 5, fim);

            try {
                //
                String nomeArquivo = (getCaminhoDasImagens() + File.separator
                        + caminhoArquivo);
                nomeArquivo = nomeArquivo.replaceAll("////", File.separator);
                if (nomeArquivo != null) {
                    File imagem = new File(nomeArquivo);
                    if ((imagem != null) && (imagem.exists())) {
                        ImageEmailHtml imgEmail = new ImageEmailHtml();
                        imgEmail.setArquivo(imagem);
                        imgEmail.setCaminhoReplace(caminhoArquivo);
                        lista.add(imgEmail);
                    }
                }

            } catch (Exception ex) {
                Logger.getLogger(UteisEmail.class.getName()).log(Level.SEVERE, null, ex);
            }

        }

        return lista;

    }
    
    public static void enviarEmail(String assunto, String nomeEmpresa, String mensagem, ConfigsEmail cfg,String ... emailDest) throws Exception{
        UteisEmail u = new UteisEmail();
        u.preencherConfiguracaoEmailPadrao(cfg.getLogin(),
                cfg.getSenha(), cfg.getMail_server(), cfg.getEmail_padrao(),
                cfg.getRemetente(), cfg.isConexao_segura(), cfg.isIniciarTLS());
        u.enviarEmailN(emailDest, mensagem, assunto, nomeEmpresa);
        
    }

    public static String getCaminhoDasImagens() {
        return "/opt/img_email";
    }

    public static void main(String... args) throws Exception {
        try {
//            final String url = Aplicacao.getProp("mormai", Aplicacao.urlZillyonWebInteg);
//            JSONObject configsEmail = IntegracaoCadastrosWSConsumer.configsEmail(url);
//            UteisEmail u = new UteisEmail();
//            u.preencherConfiguracaoEmailPadrao(configsEmail, "aaa");
//
//            u.enviarEmail("<EMAIL>", "<EMAIL>", "OLA ISSO É UM TESTE", "JOAO");
        }catch (Exception e){
            System.out.println(e.getMessage());
        }

    }
}
