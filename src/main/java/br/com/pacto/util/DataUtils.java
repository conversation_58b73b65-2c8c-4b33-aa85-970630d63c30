package br.com.pacto.util;


import java.text.SimpleDateFormat;
import java.util.Date;

public class DataUtils {

    public static String dateToString(Date data, String pattern) {
        if (data == null) return "";

        SimpleDateFormat format = new SimpleDateFormat(pattern);
        try {
            return format.format(data);
        } catch (Exception e) {
        }
        return "";
    }

    public static Date stringToDate( String data,  String pattern) {
        if (data == null || data.equals("")) return null;

        SimpleDateFormat format = new SimpleDateFormat(pattern);
        try{
            return format.parse(data);
        }catch (Exception e){

        }
        return null;
    }
}
