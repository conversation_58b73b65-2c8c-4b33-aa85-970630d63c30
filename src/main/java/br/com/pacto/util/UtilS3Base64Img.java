package br.com.pacto.util;

import br.com.pacto.bean.pessoa.Pessoa;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Calendario;

import org.apache.commons.codec.binary.Base64;

import servicos.integracao.midias.MidiaService;
import servicos.integracao.midias.commons.MidiaEntidadeEnum;

import java.io.File;

/**
 * <AUTHOR>
 * @since  11/10/2018.
 */

public class UtilS3Base64Img {





    public  static String getUrlImage (String dataImage, String tipoImage, String chave)throws Exception{
        String key = "";

        String identificador =  Calendario.getData(Calendario.hoje(),"ddMMyyyyhhMMss")+"-Bench-";
        MidiaEntidadeEnum tmidia = MidiaEntidadeEnum.obterPorExtensao(tipoImage);

        key =  MidiaService.getInstanceBenchmark().uploadObjectFromByteArray(chave, tmidia, identificador, Base64.decodeBase64(dataImage.getBytes("UTF-8")));
        return key;
    }

    public  static String getUrlImage (String dataImage, String tipoImage, String chave, Pessoa pessoa)throws Exception{
        String key = "";

        String identificador =  pessoa.getCodigo().toString();
        MidiaEntidadeEnum tmidia = MidiaEntidadeEnum.FOTO_PESSOA;
        key =  MidiaService.getInstanceBenchmark().uploadObjectFromByteArray(chave, tmidia, identificador, Base64.decodeBase64(dataImage.getBytes("UTF-8")));
        return key;
    }

    public static void deleteObject(String key)throws Exception{
        MidiaService.getInstanceBenchmark().deleteObject(key);
    }

}
