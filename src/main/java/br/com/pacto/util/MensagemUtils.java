/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.util;

import javax.persistence.EntityExistsException;
import org.hibernate.exception.ConstraintViolationException;

/**
 *
 * <AUTHOR>
 */
public interface MensagemUtils {
    
    public String tratarMensagemErroExclusao(EntityExistsException e);
    
    public String tratarMensagemUniqueConstraint(EntityExistsException e);
    
    public String tratarMensagemErroExclusao(ConstraintViolationException e);
}
