package br.com.pacto.util;

import javax.imageio.IIOImage;
import javax.imageio.ImageIO;
import javax.imageio.ImageTypeSpecifier;
import javax.imageio.ImageWriter;
import javax.imageio.metadata.IIOInvalidTreeException;
import javax.imageio.metadata.IIOMetadata;
import javax.imageio.metadata.IIOMetadataNode;
import javax.imageio.stream.ImageOutputStream;
import java.awt.image.BufferedImage;
import java.io.IOException;

public class GifSequenceWriter {
    protected ImageOutputStream output;
    protected ImageWriter writer;
    protected IIOMetadata metadata;

    public GifSequenceWriter(ImageOutputStream output) throws IOException {
        this.output = output;
        this.writer = ImageIO.getImageWritersBySuffix("gif").next();
        this.writer.setOutput(output);
        this.metadata = initializeMetadata();
        this.writer.prepareWriteSequence(null);
    }

    private IIOMetadata initializeMetadata() throws IIOInvalidTreeException {
        ImageTypeSpecifier imageTypeSpecifier = ImageTypeSpecifier.createFromBufferedImageType(BufferedImage.TYPE_INT_ARGB);
        IIOMetadata metadata = writer.getDefaultImageMetadata(imageTypeSpecifier, null);
        String metadataFormatName = metadata.getNativeMetadataFormatName();
        IIOMetadataNode root = (IIOMetadataNode) metadata.getAsTree(metadataFormatName);
        IIOMetadataNode graphicsControlExtension = getNode(root, "GraphicControlExtension");
        graphicsControlExtension.setAttribute("disposalMethod", "none");
        graphicsControlExtension.setAttribute("userInputFlag", "FALSE");
        graphicsControlExtension.setAttribute("transparentColorFlag", "FALSE");
        graphicsControlExtension.setAttribute("delayTime", "1");
        graphicsControlExtension.setAttribute("transparentColorIndex", "0");
        IIOMetadataNode appExtension = getNode(root, "ApplicationExtensions");
        IIOMetadataNode child = new IIOMetadataNode("ApplicationExtension");
        child.setAttribute("applicationID", "NETSCAPE");
        child.setAttribute("authenticationCode", "2.0");
        byte[] childBytes = new byte[]{0x1, (byte) (0 & 0xFF), (byte) ((0 >> 8) & 0xFF)};
        child.setUserObject(childBytes);
        appExtension.appendChild(child);
        root.appendChild(appExtension);
        metadata.setFromTree(metadataFormatName, root);
        return metadata;
    }

    private static IIOMetadataNode getNode(IIOMetadataNode rootNode, String nodeName) {
        int nNodes = rootNode.getLength();
        for (int i = 0; i < nNodes; i++) {
            if (rootNode.item(i).getNodeName().compareToIgnoreCase(nodeName) == 0) {
                return ((IIOMetadataNode) rootNode.item(i));
            }
        }
        IIOMetadataNode node = new IIOMetadataNode(nodeName);
        rootNode.appendChild(node);
        return (node);
    }

    public void writeToSequence(BufferedImage img) throws IOException {
        writer.writeToSequence(new IIOImage(img, null, metadata), null);
    }

    public void close() throws IOException {
        writer.endWriteSequence();
        output.close();
    }
}
