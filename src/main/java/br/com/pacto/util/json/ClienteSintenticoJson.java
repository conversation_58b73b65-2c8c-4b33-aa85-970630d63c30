package br.com.pacto.util.json;

import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.controller.json.base.SuperJSON;

import java.util.Date;
import java.util.List;


/**
 * <AUTHOR> Si<PERSON>ira
 */
public class ClienteSintenticoJson extends SuperJSON {

    private Integer codigo;
    private Integer codigoPessoaZW;
    private Integer codigoClienteZW;
    private Integer codigoContrato;
    private Integer matricula;
    private String nome;
    private Date dataNascimento;
    private Integer idade;
    private String profissao;
    private String colaboradores;
    private String professor;
    private String situacao;
    private String situacaoContrato;
    private Date dataUltimoAcesso;
    private Date dataInicioPeriodoAcesso;
    private Date dataFimPeriodoAcesso;
    private Integer empresa;
    private String email = "";
    private String telefones;
    private String andamentoTreino;
    private String urlFoto;
    private String userName;
    private Date dia;
    private String nivel;
    private String nomeEmpresa;
    private Integer versao;
    private String codigoAcesso;
    private String vencPlano;
    private List<String> listaTelefones;
    private List<String> listaEmails;
    private String sexo;
    private String objetivos;
    private String dadosAvaliacao;
    private Boolean parQ;
    private Date dataVirgencia;

    public ClienteSintenticoJson() {
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getCodigoPessoaZW() {
        return codigoPessoaZW;
    }

    public void setCodigoPessoaZW(Integer codigoPessoaZW) {
        this.codigoPessoaZW = codigoPessoaZW;
    }

    public Integer getCodigoClienteZW() {
        return codigoClienteZW;
    }

    public void setCodigoClienteZW(Integer codigoClienteZW) {
        this.codigoClienteZW = codigoClienteZW;
    }

    public Integer getCodigoContrato() {
        return codigoContrato;
    }

    public void setCodigoContrato(Integer codigoContrato) {
        this.codigoContrato = codigoContrato;
    }

    public Integer getMatricula() {
        return matricula;
    }

    public void setMatricula(Integer matricula) {
        this.matricula = matricula;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Date getDataNascimento() {
        return dataNascimento;
    }

    public void setDataNascimento(Date dataNascimento) {
        this.dataNascimento = dataNascimento;
    }

    public Integer getIdade() {
        return idade;
    }

    public void setIdade(Integer idade) {
        this.idade = idade;
    }

    public String getProfissao() {
        return profissao;
    }

    public void setProfissao(String profissao) {
        this.profissao = profissao;
    }

    public String getColaboradores() {
        return colaboradores;
    }

    public void setColaboradores(String colaboradores) {
        this.colaboradores = colaboradores;
    }

    public String getProfessor() {
        return professor;
    }

    public void setProfessor(String professor) {
        this.professor = professor;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public String getSituacaoContrato() {
        return situacaoContrato;
    }

    public void setSituacaoContrato(String situacaoContrato) {
        this.situacaoContrato = situacaoContrato;
    }

    public Date getDataUltimoAcesso() {
        return dataUltimoAcesso;
    }

    public void setDataUltimoAcesso(Date dataUltimoAcesso) {
        this.dataUltimoAcesso = dataUltimoAcesso;
    }

    public Date getDataInicioPeriodoAcesso() {
        return dataInicioPeriodoAcesso;
    }

    public void setDataInicioPeriodoAcesso(Date dataInicioPeriodoAcesso) {
        this.dataInicioPeriodoAcesso = dataInicioPeriodoAcesso;
    }

    public Date getDataFimPeriodoAcesso() {
        return dataFimPeriodoAcesso;
    }

    public void setDataFimPeriodoAcesso(Date dataFimPeriodoAcesso) {
        this.dataFimPeriodoAcesso = dataFimPeriodoAcesso;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getTelefones() {
        return telefones;
    }

    public void setTelefones(String telefones) {
        this.telefones = telefones;
    }

    public String getAndamentoTreino() {
        return andamentoTreino;
    }

    public void setAndamentoTreino(String andamentoTreino) {
        this.andamentoTreino = andamentoTreino;
    }

    public String getUrlFoto() {
        return urlFoto;
    }

    public void setUrlFoto(String urlFoto) {
        this.urlFoto = urlFoto;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public Date getDia() {
        return dia;
    }

    public void setDia(Date dia) {
        this.dia = dia;
    }

    public String getNivel() {
        return nivel;
    }

    public void setNivel(String nivel) {
        this.nivel = nivel;
    }

    public String getNomeEmpresa() {
        return nomeEmpresa;
    }

    public void setNomeEmpresa(String nomeEmpresa) {
        this.nomeEmpresa = nomeEmpresa;
    }

    public Integer getVersao() {
        return versao;
    }

    public void setVersao(Integer versao) {
        this.versao = versao;
    }

    public String getCodigoAcesso() {
        return codigoAcesso;
    }

    public void setCodigoAcesso(String codigoAcesso) {
        this.codigoAcesso = codigoAcesso;
    }

    public String getVencPlano() {
        return vencPlano;
    }

    public void setVencPlano(String vencPlano) {
        this.vencPlano = vencPlano;
    }

    public String getSexo() {
        return sexo;
    }

    public void setSexo(String sexo) {
        this.sexo = sexo;
    }

    public String getObjetivos() {
        return objetivos;
    }

    public void setObjetivos(String objetivos) {
        this.objetivos = objetivos;
    }

    public List<String> getListaTelefones() {
        return listaTelefones;
    }

    public void setListaTelefones(List<String> listaTelefones) {
        this.listaTelefones = listaTelefones;
    }

    public List<String> getListaEmails() {
        return listaEmails;
    }

    public void setListaEmails(List<String> listaEmails) {
        this.listaEmails = listaEmails;
    }

    public String getDadosAvaliacao() {
        return dadosAvaliacao;
    }

    public void setDadosAvaliacao(String dadosAvaliacao) {
        this.dadosAvaliacao = dadosAvaliacao;
    }

    public Boolean getParQ() {
        return parQ;
    }

    public void setParQ(Boolean parQ) {
        this.parQ = parQ;
    }

    public Date getDataVirgencia() {
        return dataVirgencia;
    }

    public void setDataVirgencia(Date dataVirgencia) {
        this.dataVirgencia = dataVirgencia;
    }

    public static ClienteSintenticoJson fromClienteSinteticoToClienteSinteticoJson(ClienteSintetico c){
        ClienteSintenticoJson cliente = new ClienteSintenticoJson();
        cliente.setCodigo(c.getCodigo());
        cliente.setCodigoPessoaZW(c.getCodigoPessoa());
        cliente.setCodigoClienteZW(c.getCodigoCliente());
        cliente.setCodigoContrato(c.getCodigoContrato());
        cliente.setMatricula(c.getMatricula());
        cliente.setNome(c.getNome());
        cliente.setDataNascimento(c.getDataNascimento());
        cliente.setIdade(c.getIdade());
        cliente.setProfissao(c.getProfissao());
        cliente.setColaboradores(c.getColaboradores());
        cliente.setProfessor(c.getProfessor());
        cliente.setSituacao(c.getSituacao());
        cliente.setSituacaoContrato(c.getSituacaoContrato());
        cliente.setDataUltimoAcesso(c.getDataUltimoacesso());
        cliente.setDataInicioPeriodoAcesso(c.getDataInicioPeriodoAcesso());
        cliente.setDataFimPeriodoAcesso(c.getDataFimPeriodoAcesso());
        cliente.setEmpresa(c.getEmpresa());
        cliente.setEmail(c.getEmail());
        cliente.setTelefones(c.getTelefones());
        cliente.setAndamentoTreino(c.getAndamentoTreino());
        cliente.setUrlFoto(c.getUrlFoto());
        cliente.setUserName(c.getNome());
        cliente.setDia(c.getDia());
        cliente.setNivel(c.getNivel());
        if(c.getEmpresaTreino() != null){
            cliente.setNomeEmpresa(c.getEmpresaTreino().getNome());
        }
        cliente.setNomeEmpresa("");
        cliente.setVersao(c.getVersao());
        cliente.setCodigoAcesso(c.getCodigoAcesso());
        cliente.setVencPlano(c.getDataVigenciaDeApresentar());
        cliente.setListaTelefones(c.getListaTelefones());
        cliente.setListaEmails(c.getListaEmails());
        cliente.setSexo(c.getSexo());
        cliente.setObjetivos(c.getObjetivos());
        cliente.setDadosAvaliacao(c.getDadosAvaliacao());
        cliente.setParQ(c.getParq());
        cliente.setDataVirgencia(c.getDataVigenciaDe());

        return cliente;
    }
}

