/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.util.json;

import br.com.pacto.controller.json.base.SuperJSON;

/**
 *
 * <AUTHOR>
 */
public class ControleCreditoTreinoJSON extends SuperJSON{
    private String data;
    private String operacao;
    private Integer quantidade;
    private Integer saldo;
    private Integer codigo;
    private String aulaDesmarcada;
    private String aulaMarcada;

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public String getOperacao() {
        return operacao;
    }

    public void setOperacao(String operacao) {
        this.operacao = operacao;
    }

    public Integer getQuantidade() {
        return quantidade;
    }

    public void setQuantidade(Integer quantidade) {
        this.quantidade = quantidade;
    }

    public Integer getSaldo() {
        return saldo;
    }

    public void setSaldo(Integer saldo) {
        this.saldo = saldo;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getAulaDesmarcada() {
        return aulaDesmarcada;
    }

    public void setAulaDesmarcada(String aulaDesmarcada) {
        this.aulaDesmarcada = aulaDesmarcada;
    }

    public String getAulaMarcada() {
        return aulaMarcada;
    }

    public void setAulaMarcada(String aulaMarcada) {
        this.aulaMarcada = aulaMarcada;
    }
}
