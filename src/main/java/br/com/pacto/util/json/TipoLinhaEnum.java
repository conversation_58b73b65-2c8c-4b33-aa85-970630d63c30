/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.util.json;

import br.com.pacto.util.enumeradores.PaletaCoresEnum;

/**
 *
 * <AUTHOR>
 */
public enum TipoLinhaEnum {
    TREINOU(PaletaCoresEnum.AZUL_G, "Treinou", true),
    AGENDAMENTO(PaletaCoresEnum.LARANJA_C, "Agendamento", true),
    REVISOU_TREINO(PaletaCoresEnum.VERDE_LIMAO_B, "Revisou Treino", true),
    RENOVOU_TREINO(PaletaCoresEnum.VERDE_LIMAO_E,"Renovou Treino", true),
    ACABOU_TREINO(PaletaCoresEnum.VERMELHO_I, "Acabou Treino", true),
    NOTIFICACAO(PaletaCoresEnum.AZUL_ESCURO_B,"Notificação", true),
    MONTOU_TREINO(PaletaCoresEnum.VERDE_E,"Montou Treino", true),
    MUDOU_DE_NIVEL(PaletaCoresEnum.ROXO_D, "<PERSON><PERSON><PERSON> Ni<PERSON>", true),
    GANHOU_BADGE(PaletaCoresEnum.MARROM_C,"Ganhou Badge", true),
    FEZ_AULA(PaletaCoresEnum.ROXO_B,"Fez Aula", true),
    REALIZOU_AVALIACAO(PaletaCoresEnum.VERDE_J,"Realizou Avaliação", true),
    REGISTROU_WOD(PaletaCoresEnum.VERDE_J,"Registrou Resultado de Wod", true),
    AGENDOU_BOOKING_GYMPASS(PaletaCoresEnum.VERDE_J,"Agendou aula pelo Booking Gympass", true),
    ALTERACAO_AGENDAMENTO_SERVICOS(PaletaCoresEnum.VERDE_J,"Alterou o status do agendamento de serviços", true),
    AULA_DESMARCADA(PaletaCoresEnum.MARROM_D,"", true),
    ;
    
    private PaletaCoresEnum paleta;
    private String subtitulo;
    private boolean defaultFiltro;

    private TipoLinhaEnum(PaletaCoresEnum classeCss, String subtitulo,Boolean defaultFiltro) {
        this.paleta = classeCss;
        this.subtitulo = subtitulo;
        this.defaultFiltro = defaultFiltro;
        
    }

    public boolean isDefaultFiltro() {
        return defaultFiltro;
    }

    public void setDefaultFiltro(boolean defaultFiltro) {
        this.defaultFiltro = defaultFiltro;
    }

    public PaletaCoresEnum getPaleta() {
        return paleta;
    }

    public void setPaleta(PaletaCoresEnum paleta) {
        this.paleta = paleta;
    }

    

    public String getSubtitulo() {
        return subtitulo;
    }

    public void setSubtitulo(String subtitulo) {
        this.subtitulo = subtitulo;
    }
    
    
    
    
}
