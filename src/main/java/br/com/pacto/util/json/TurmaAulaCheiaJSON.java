/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.util.json;

import br.com.pacto.controller.json.base.SuperJSON;
import br.com.pacto.objeto.Uteis;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.Date;

/**
 *
 * <AUTHOR>
 */
@JsonIgnoreProperties
public class TurmaAulaCheiaJSON extends SuperJSON{
    private String nome;
    private String nomeModalidade;
    private Integer modalidade;
    private Integer codigo;
    private Integer professor;
    private Integer professorCodigoPessoa;
    private String nomeProfessor;
    private Integer ambiente;
    private String nomeAmbiente;
    private Integer tolerancia;
    private Integer tipoTolerancia;
    private Integer capacidade;
    private Integer limiteVagasAgregados;
    private String dias;
    private String horarios;
    private Integer horarioCodigo;
    private String inicio;
    private String fim;
    private String mensagem;
    private Double bonificacao;
    private Integer pontosBonus;
    private Double meta;
    private Integer ocupacao;
    private Integer codigoAulaCheia;
    private Integer minutosTolerancia;
    private Integer empresa;
    private String campoOrdenacao;
    private boolean validarRestricoesMarcacao = true;
    private boolean naoValidarModalidadeContrato = false;
    private String modalidadeFotokey;
    private String professorFotokey;
    private boolean integracaoSpivi;
    private Integer produtoGymPass;
    private Integer idClasseGymPass;
    private String urlTurmaVirtual;
    private Integer usuario;
    private String urlVideoYoutube;
    private byte[] image;
    private String fotoKey;
    private String imageUrl;
    private Boolean visualizarProdutosGympass;
    private Boolean visualizarProdutosTotalpass;
    private Boolean permiteFixar;
    private Boolean aulaIntegracaoSelfloops;
    private Boolean manterFotoAnterior;

    private Integer idadeMaximaAnos;
    private Integer idadeMaximaMeses;
    private Integer idadeMinimaAnos;
    private Integer idadeMinimaMeses;
    private String niveis;
    private String tipoReservaEquipamento;
    private String mapaEquipamentos;

    @JsonIgnore
    public String getPrimeiroNomeProfessor(){
        if(null != nomeProfessor) {
            String parts[] = nomeProfessor.split(" ");
            return parts[0];
        }
        return "";
    }

    @JsonIgnore
    public String getUltimoNomeProfessor(){
        if(null != nomeProfessor) {
            String parts[] = nomeProfessor.split(" ");
            if (parts[0] == parts[parts.length - 1]) {
                return "";
            } else {
                return parts[parts.length - 1];
            }
        }
        return "";

    }

    public void setCampoOrdenacao(String campoOrdenacao) {
        this.campoOrdenacao = campoOrdenacao;
    }
    
    public String getCampoOrdenacao(){
        if(campoOrdenacao == null){
            campoOrdenacao = nome+" - "+nomeProfessor+" - "+codigo;
        }
        return campoOrdenacao;
    }

    public Integer getHorarioCodigo() {
        return horarioCodigo;
    }

    public void setHorarioCodigo(Integer horarioCodigo) {
        this.horarioCodigo = horarioCodigo;
    }
    
    public Integer getMinutosTolerancia() {
        return minutosTolerancia;
    }

    public void setMinutosTolerancia(Integer minutosTolerancia) {
        this.minutosTolerancia = minutosTolerancia;
    }
    
    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Integer getModalidade() {
        return modalidade;
    }

    public void setModalidade(Integer modalidade) {
        this.modalidade = modalidade;
    }

    public Integer getProfessor() {
        return professor;
    }

    public void setProfessor(Integer professor) {
        this.professor = professor;
    }

    public Integer getProfessorCodigoPessoa() {
        return professorCodigoPessoa;
    }

    public void setProfessorCodigoPessoa(Integer professorCodigoPessoa) {
        this.professorCodigoPessoa = professorCodigoPessoa;
    }

    public Integer getAmbiente() {
        return ambiente;
    }

    public void setAmbiente(Integer ambiente) {
        this.ambiente = ambiente;
    }

    public Integer getTolerancia() {
        return tolerancia;
    }

    public void setTolerancia(Integer tolerancia) {
        this.tolerancia = tolerancia;
    }

    public Integer getCapacidade() {
        return capacidade;
    }

    public void setCapacidade(Integer capacidade) {
        this.capacidade = capacidade;
    }

    public Integer getLimiteVagasAgregados() {
        return limiteVagasAgregados;
    }

    public void setLimiteVagasAgregados(Integer limiteVagasAgregados) {
        this.limiteVagasAgregados = limiteVagasAgregados;
    }

    public String getDias() {
        if(dias == null){
            dias = "";
        }
        return dias;
    }

    public void setDias(String dias) {
        this.dias = dias;
    }

    public String getHorarios() {
        if(horarios == null){
            horarios = "";
        }
        return horarios;
    }

    public void setHorarios(String horarios) {
        this.horarios = horarios;
    }

    public String getInicio() {
        return inicio;
    }

    public void setInicio(String inicio) {
        this.inicio = inicio;
    }

    public String getFim() {
        return fim;
    }

    public void setFim(String fim) {
        this.fim = fim;
    }

    public String getMensagem() {
        return mensagem;
    }

    public void setMensagem(String mensagem) {
        this.mensagem = mensagem;
    }

    public Double getBonificacao() {
        return bonificacao;
    }

    public void setBonificacao(Double bonificacao) {
        this.bonificacao = bonificacao;
    }

    public Integer getPontosBonus() {
        return pontosBonus;
    }

    public void setPontosBonus(Integer pontosBonus) {
        this.pontosBonus = pontosBonus;
    }

    public Double getMeta() {
        return meta;
    }

    public void setMeta(Double meta) {
        this.meta = meta;
    }

    public Integer getOcupacao() {
        return ocupacao;
    }

    public void setOcupacao(Integer ocupacao) {
        this.ocupacao = ocupacao;
    }

    public Integer getCodigoAulaCheia() {
        return codigoAulaCheia;
    }

    public void setCodigoAulaCheia(Integer codigoAulaCheia) {
        this.codigoAulaCheia = codigoAulaCheia;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNomeModalidade() {
        return nomeModalidade;
    }

    public void setNomeModalidade(String nomeModalidade) {
        this.nomeModalidade = nomeModalidade;
    }

    public String getNomeProfessor() {
        return nomeProfessor;
    }

    public void setNomeProfessor(String nomeProfessor) {
        this.nomeProfessor = nomeProfessor;
    }

    public String getNomeAmbiente() {
        return nomeAmbiente;
    }

    public void setNomeAmbiente(String nomeAmbiente) {
        this.nomeAmbiente = nomeAmbiente;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public boolean isValidarRestricoesMarcacao() {
        return validarRestricoesMarcacao;
    }

    public void setValidarRestricoesMarcacao(boolean validarRestricoesMarcacao) {
        this.validarRestricoesMarcacao = validarRestricoesMarcacao;
    }

    public String getModalidadeFotokey() {
        return modalidadeFotokey;
    }

    public void setModalidadeFotokey(String modalidadeFotokey) {
        this.modalidadeFotokey = modalidadeFotokey;
    }

    public String getProfessorFotokey() {
        return professorFotokey;
    }

    public void setProfessorFotokey(String professorFotokey) {
        this.professorFotokey = professorFotokey;
    }

    public boolean isIntegracaoSpivi() {
        return integracaoSpivi;
    }

    public void setIntegracaoSpivi(boolean integracaoSpivi) {
        this.integracaoSpivi = integracaoSpivi;
    }

    public Date getDataInicio() {
        try {
            return Uteis.getDate(inicio,"dd/MM/yyyy");
        } catch (Exception e) {
            return null;
        }
    }

    public Date getDataFim() {
        try {
            return Uteis.getDate(fim,"dd/MM/yyyy");
        } catch (Exception e) {
            return null;
        }
    }

    public Integer getProdutoGymPass() {
        return produtoGymPass;
    }

    public void setProdutoGymPass(Integer produtoGymPass) {
        this.produtoGymPass = produtoGymPass;
    }

    public Integer getIdClasseGymPass() {
        return idClasseGymPass;
    }

    public void setIdClasseGymPass(Integer idClasseGymPass) {
        this.idClasseGymPass = idClasseGymPass;
    }

    public String getUrlTurmaVirtual() {
        return urlTurmaVirtual;
    }

    public void setUrlTurmaVirtual(String urlTurmaVirtual) {
        this.urlTurmaVirtual = urlTurmaVirtual;
    }

    public boolean isNaoValidarModalidadeContrato() {
        return naoValidarModalidadeContrato;
    }

    public void setNaoValidarModalidadeContrato(boolean naoValidarModalidadeContrato) {
        this.naoValidarModalidadeContrato = naoValidarModalidadeContrato;
    }

    public Integer getTipoTolerancia() {
        return tipoTolerancia;
    }

    public void setTipoTolerancia(Integer tipoTolerancia) {
        this.tipoTolerancia = tipoTolerancia;
    }

    public Integer getUsuario() {
        return usuario;
    }

    public void setUsuario(Integer usuario) {
        this.usuario = usuario;
    }

    public String getUrlVideoYoutube() {
        return urlVideoYoutube;
    }

    public void setUrlVideoYoutube(String urlVideoYoutube) {
        this.urlVideoYoutube = urlVideoYoutube;
    }

    public byte[] getImage() {
        return image;
    }

    public void setImage(byte[] image) {
        this.image = image;
    }

    public String getFotoKey() {
        return fotoKey;
    }

    public void setFotoKey(String fotoKey) {
        this.fotoKey = fotoKey;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public Boolean getVisualizarProdutosGympass() { return visualizarProdutosGympass; }

    public void setVisualizarProdutosGympass(Boolean visualizarProdutosGympass) {
        this.visualizarProdutosGympass = visualizarProdutosGympass;
    }

    public Boolean getVisualizarProdutosTotalpass() {return visualizarProdutosTotalpass;}

    public void setVisualizarProdutosTotalpass(Boolean visualizarProdutosTotalpass) {
        this.visualizarProdutosTotalpass = visualizarProdutosTotalpass;
    }

    public Boolean getPermiteFixar() {
        return permiteFixar;
    }

    public void setPermiteFixar(Boolean permiteFixar) {
        this.permiteFixar = permiteFixar;
    }

    public Boolean getManterFotoAnterior() {
        return manterFotoAnterior;
    }

    public void setManterFotoAnterior(Boolean manterFotoAnterior) {
        this.manterFotoAnterior = manterFotoAnterior;
    }

    public Integer getIdadeMaximaAnos() {
        return idadeMaximaAnos;
    }

    public void setIdadeMaximaAnos(Integer idadeMaximaAnos) {
        this.idadeMaximaAnos = idadeMaximaAnos;
    }

    public Integer getIdadeMaximaMeses() {
        return idadeMaximaMeses;
    }

    public void setIdadeMaximaMeses(Integer idadeMaximaMeses) {
        this.idadeMaximaMeses = idadeMaximaMeses;
    }

    public Integer getIdadeMinimaAnos() {
        return idadeMinimaAnos;
    }

    public void setIdadeMinimaAnos(Integer idadeMinimaAnos) {
        this.idadeMinimaAnos = idadeMinimaAnos;
    }

    public Integer getIdadeMinimaMeses() {
        return idadeMinimaMeses;
    }

    public void setIdadeMinimaMeses(Integer idadeMinimaMeses) {
        this.idadeMinimaMeses = idadeMinimaMeses;
    }

    public String getNiveis() {
        return niveis;
    }

    public void setNiveis(String niveis) {
        this.niveis = niveis;
    }

    public String getTipoReservaEquipamento() {
        return tipoReservaEquipamento;
    }

    public void setTipoReservaEquipamento(String tipoReservaEquipamento) {
        this.tipoReservaEquipamento = tipoReservaEquipamento;
    }

    public String getMapaEquipamentos() {
        return mapaEquipamentos;
    }

    public void setMapaEquipamentos(String mapaEquipamentos) {
        this.mapaEquipamentos = mapaEquipamentos;
    }

    public Boolean getAulaIntegracaoSelfloops() {
        return aulaIntegracaoSelfloops;
    }

    public void setAulaIntegracaoSelfloops(Boolean aulaIntegracaoSelfloops) {
        this.aulaIntegracaoSelfloops = aulaIntegracaoSelfloops;
    }
}
