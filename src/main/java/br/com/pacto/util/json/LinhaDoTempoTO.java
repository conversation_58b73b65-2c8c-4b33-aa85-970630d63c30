/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.util.json;

import br.com.pacto.objeto.Uteis;
import br.com.pacto.util.bean.ItemHistoricoExecucoesTO;
import br.com.pacto.util.enumeradores.PaletaCoresEnum;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class LinhaDoTempoTO{
    
    private Date data;
    private String tipo;
    private String titulo;
    private String status;
    private String subtitulo;
    private String horario;
    private String nivelNovo;
    private String nivelAnterior;
    private PaletaCoresEnum classeCss;
    private String classeNotificacaoCss;
    private Boolean divisaoMes = false;
    private Integer codigoPessoaProfessor;
    private String nomeProfessor;
    private String urlBadge = null;
    private String nomeBadge = null;
    private List<ItemHistoricoExecucoesTO> itensExecucoes = new ArrayList<ItemHistoricoExecucoesTO>();
    private String nomeFichaExecucao;
    private String origem;

    public String getValorOrdem(){
        try {
            return divisaoMes ? tipo 
                    : Uteis.getDataAplicandoFormatacao(data, "yyyyMMddHHmm");
        } catch (Exception e) {
            return "00000000";
        }
    }
    

    public Date getData() {
        return data;
    }

    public void setData(Date data) {
        this.data = data;
    }

    public String getTitulo() {
        return titulo;
    }

    public void setTitulo(String titulo) {
        this.titulo = titulo;
    }

    public PaletaCoresEnum getClasseCss() {
        return classeCss;
    }

    public void setClasseCss(PaletaCoresEnum classeCss) {
        this.classeCss = classeCss;
    }
    
    public Integer getDia(){
        return Uteis.getDiaMesData(data);
    }
    
    public String getDataApresentar(){
        return Uteis.getData(data);
    }

    public String getClasseNotificacaoCss() {
        if(classeNotificacaoCss == null && classeCss != null){
            classeNotificacaoCss = classeCss.getDescricao();
        }
        return classeNotificacaoCss;
    }

    public void setClasseNotificacaoCss(String classeNotificacaoCss) {
        this.classeNotificacaoCss = classeNotificacaoCss;
    }

    public String getTipo() {
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }

    public Boolean getDivisaoMes() {
        return divisaoMes;
    }

    public void setDivisaoMes(Boolean divisaoMes) {
        this.divisaoMes = divisaoMes;
    }

    public String getSubtitulo() {
        return subtitulo;
    }

    public void setSubtitulo(String subtitulo) {
        this.subtitulo = subtitulo;
    }

    public String getHorario() {
        return horario;
    }

    public void setHorario(String horario) {
        this.horario = horario;
    }

    public Integer getCodigoPessoaProfessor() {
        return codigoPessoaProfessor;
    }

    public void setCodigoPessoaProfessor(Integer codigoPessoaProfessor) {
        this.codigoPessoaProfessor = codigoPessoaProfessor;
    }

    public String getNomeProfessor() {
        return nomeProfessor;
    }
    
    public String getNomeProfessorMinusculo() {
        try {
            return nomeProfessor.toLowerCase();
        } catch (Exception e) {
            return "";
        }
        
    }

    public void setNomeProfessor(String nomeProfessor) {
        this.nomeProfessor = nomeProfessor;
    }

    public List<ItemHistoricoExecucoesTO> getItensExecucoes() {
        return itensExecucoes;
    }

    public void setItensExecucoes(List<ItemHistoricoExecucoesTO> itensExecucoes) {
        this.itensExecucoes = itensExecucoes;
    }

    public String getNivelNovo() {
        return nivelNovo;
    }

    public void setNivelNovo(String nivelNovo) {
        this.nivelNovo = nivelNovo;
    }

    public String getNivelAnterior() {
        return nivelAnterior;
    }

    public void setNivelAnterior(String nivelAnterior) {
        this.nivelAnterior = nivelAnterior;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getUrlBadge() {
        return urlBadge;
    }

    public void setUrlBadge(String urlBadge) {
        this.urlBadge = urlBadge;
    }

    public String getNomeBadge() {
        return nomeBadge;
    }

    public void setNomeBadge(String nomeBadge) {
        this.nomeBadge = nomeBadge;
    }

    public String getNomeFichaExecucao() {
        return nomeFichaExecucao;
    }

    public void setNomeFichaExecucao(String nomeFichaExecucao) {
        this.nomeFichaExecucao = nomeFichaExecucao;
    }

    public String getOrigem() {
        return origem;
    }

    public void setOrigem(String origem) {
        this.origem = origem;
    }

}
