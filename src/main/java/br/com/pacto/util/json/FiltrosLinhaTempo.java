/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.util.json;

import br.com.pacto.util.bean.GenericoTO;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class FiltrosLinhaTempo implements Serializable{
 
    private List<GenericoTO> tipos;
    private Date inicio;
    private Date fim;
    

    public FiltrosLinhaTempo() {
        tipos = new ArrayList<GenericoTO>();
        for(TipoLinhaEnum tipo : TipoLinhaEnum.values()){
            GenericoTO generico = new GenericoTO();
            generico.setLabel(tipo.name());
            generico.setEscolhido(tipo.isDefaultFiltro());
            generico.setTipoLinha(tipo);
            tipos.add(generico);
        }
        
    }

    public List<GenericoTO> getTipos() {
        return tipos;
    }

    public void setTipos(List<GenericoTO> tipos) {
        this.tipos = tipos;
    }

    public Date getInicio() {
        return inicio;
    }

    public void setInicio(Date inicio) {
        this.inicio = inicio;
    }

    public Date getFim() {
        return fim;
    }

    public void setFim(Date fim) {
        this.fim = fim;
    }
    
    
    
}
