package br.com.pacto.util.json;

import br.com.pacto.controller.json.base.SuperJSON;
import br.com.pacto.util.AgendadoJSON;

import java.util.List;

/**
 * <AUTHOR> Si<PERSON>ira
 */

public class ResultAlunoClienteSinteticoJSON extends SuperJSON {

    List<ClienteSintenticoJson> resultCliSintentico;
    List<AgendadoJSON> resultAgendado;

    public ResultAlunoClienteSinteticoJSON() {
    }

    public ResultAlunoClienteSinteticoJSON(List<ClienteSintenticoJson> resultCliSintentico, List<AgendadoJSON> resultAgendado) {
        this.resultCliSintentico = resultCliSintentico;
        this.resultAgendado = resultAgendado;
    }

    public List<ClienteSintenticoJson> getResultCliSintentico() {
        return resultCliSintentico;
    }

    public void setResultCliSintentico(List<ClienteSintenticoJson> resultCliSintentico) {
        this.resultCliSintentico = resultCliSintentico;
    }

    public List<AgendadoJSON> getResultAgendado() {
        return resultAgendado;
    }

    public void setResultAgendado(List<AgendadoJSON> resultAgendado) {
        this.resultAgendado = resultAgendado;
    }
}
