package br.com.pacto.util.json;

import br.com.pacto.controller.json.base.SuperJSON;
import br.com.pacto.objeto.Uteis;

public class AlunoAulaAcessoJSON extends SuperJSON {

    private Integer codigo;
    private Integer codigoPessoa;
    private String nome;
    private String sexo;
    private String dthrentrada;
    private String dthrsaida;
    private String fotokey;
    private Integer meioidentificacaosaida;
    private Integer meioidentificacaoentrada;
    private Integer tipoacesso;

    private boolean acessoViaMobile;
    private boolean acessoViakeyboard;
    private boolean acessoViaFingerPrint;
    private boolean acessoViaFace;

    public String getPrimeiroNome(){
        String parts[] = nome.split(" ");
        return parts[0];
    }

    public String getUltimoNome(){
        String parts[] = nome.split(" ");
        if( parts[0] == parts[parts.length -1] ){
            return "";
        }else{
            return parts[parts.length -1];
        }

    }
    public String getPrimeiroNomeComLetraSobrenome() {
        if (nome != null) {
            return Uteis.obterPrimeiroNomeConcatenadoSobreNome(nome, false);
        }
        return "";
    }

    public String getDthrsaida() {
        return dthrsaida;
    }

    public void setDthrsaida(String dthrsaida) {
        this.dthrsaida = dthrsaida;
    }

    public Integer getMeioidentificacaosaida() {
        return meioidentificacaosaida;
    }

    public void setMeioidentificacaosaida(Integer meioidentificacaosaida) {
        this.meioidentificacaosaida = meioidentificacaosaida;
    }

    public Integer getMeioidentificacaoentrada() {
        return meioidentificacaoentrada;
    }

    public void setMeioidentificacaoentrada(Integer meioidentificacaoentrada) {
        this.meioidentificacaoentrada = meioidentificacaoentrada;
    }

    public Integer getTipoacesso() {
        return tipoacesso;
    }

    public void setTipoacesso(Integer tipoacesso) {
        this.tipoacesso = tipoacesso;
    }

    public String getFotokey() {
        return fotokey;
    }

    public void setFotokey(String fotokey) {
        this.fotokey = fotokey;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public Integer getCodigoPessoa() {
        return codigoPessoa;
    }

    public void setCodigoPessoa(Integer codigoPessoa) {
        this.codigoPessoa = codigoPessoa;
    }

    public String getDthrentrada() {
        return dthrentrada;
    }

    public void setDthrentrada(String dthrentrada) {
        this.dthrentrada = dthrentrada;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getSexo() {
        return sexo;
    }

    public void setSexo(String sexo) {
        this.sexo = sexo;
    }

    public boolean isAcessoViaMobile() {
        return acessoViaMobile;
    }

    public void setAcessoViaMobile(boolean acessoViaMobile) {
        this.acessoViaMobile = acessoViaMobile;
    }

    public boolean isAcessoViakeyboard() {
        return acessoViakeyboard;
    }

    public void setAcessoViakeyboard(boolean acessoViakeyboard) {
        this.acessoViakeyboard = acessoViakeyboard;
    }

    public boolean isAcessoViaFingerPrint() {
        return acessoViaFingerPrint;
    }

    public void setAcessoViaFingerPrint(boolean acessoViaFingerPrint) {
        this.acessoViaFingerPrint = acessoViaFingerPrint;
    }

    public boolean isAcessoViaFace() {
        return acessoViaFace;
    }

    public void setAcessoViaFace(boolean acessoViaFace) {
        this.acessoViaFace = acessoViaFace;
    }
}
