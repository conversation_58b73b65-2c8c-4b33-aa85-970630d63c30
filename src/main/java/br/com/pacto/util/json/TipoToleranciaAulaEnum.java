package br.com.pacto.util.json;

public enum TipoToleranciaAulaEnum {

    APOS_INICIO(1),
    ANTES_INICIO(2);

    TipoToleranciaAulaEnum(Integer codigo) {
        this.codigo = codigo;
    }

    private Integer codigo;

    public static TipoToleranciaAulaEnum getFromCodigo(Integer codigo){
        for(TipoToleranciaAulaEnum t : values()){
            if(t.getCodigo().equals(codigo)){
                return t;
            }
        }
        return null;
    }

    public Integer getCodigo() {
        return codigo;
    }
}
