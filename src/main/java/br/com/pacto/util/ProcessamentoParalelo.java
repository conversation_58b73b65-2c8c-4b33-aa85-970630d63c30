package br.com.pacto.util;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;

/**
 * Realiza o processamento de varias {@link Runnable} em paralelo;
 * Created by johny<PERSON> on 01/09/2016.
 */
public class ProcessamentoParalelo implements Serializable{

    private List<Runnable> executaveis = new ArrayList<Runnable>();

    private ExecutorService executor = Executors.newFixedThreadPool(10);

    /**
     * Define se o processamento paralelo iniciou.
     */
    private Boolean iniciouExecucao = false;

    public ProcessamentoParalelo(){}

    public ProcessamentoParalelo(List<Runnable> executaveis){
        setExecutaveis(executaveis);
    }

    /**
     * Adiciona {@link Runnable} para a execução do processo.
     * @param executaveis
     */
    public void adicionarExecutavel(Runnable ... executaveis){
        if(this.iniciouExecucao){
            throw  new RuntimeException("Não é possivel adicionar novos processamentos apos chamar o método executar");
        }
        this.executaveis.addAll(Arrays.asList(executaveis));
    }

    /**
     * Realiza a execução dos processos adicionados em paralelo. Utilizando a quantidade padrão de threads para processar.
     * @return
     */
    public void executar() throws  InterruptedException{
        this.iniciouExecucao = true;
        realizarExecucao();
        aguardarFinalizacao();
        this.iniciouExecucao = false;
        this.executaveis.clear();
    }

    /**
     * Realiza a verificação se o processamento foi finalizado.
     * @return
     */
    private void aguardarFinalizacao() throws InterruptedException {
        this.executor.awaitTermination(30, TimeUnit.MINUTES);
    }

    /**
     * Chama a execução dos <code>executavies</code> para o ExecutorService
     */
    private void realizarExecucao() {
        for(Runnable r : this.executaveis){
            this.executor.execute(r);
        }
        this.executor.shutdown();
    }

    private List<Runnable> getExecutaveis() {
        return executaveis;
    }

    public void setExecutaveis(List<Runnable> executaveis) {
        this.executaveis = executaveis;
    }
}
