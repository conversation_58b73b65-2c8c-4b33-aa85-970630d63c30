/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.util;

import br.com.pacto.objeto.Uteis;
import java.io.UnsupportedEncodingException;
import java.util.Random;

/**
 *
 * <AUTHOR>
 */
public class MagneticCrypto {

    public static String encriptar(String textoPlano, String chave) {
        int faixa = 256;
        int tamanhoChave = chave.length();
        int posChave = -1;
        int offset = new Random().nextInt(faixa);
        int posTextoPlano = 0;
        int codigo;

        if (tamanhoChave == 0) {
            return "";
        }

        StringBuilder result = new StringBuilder();
        result.append(converterParaHexa(offset));

        for (; posTextoPlano < textoPlano.length(); posTextoPlano++) {
            codigo = ((int) textoPlano.charAt(posTextoPlano) + offset) % 255;

            if (posChave < (tamanhoChave - 1)) {
                posChave++;
            } else {
                posChave = 0;
            }

            codigo = codigo ^ ((int) chave.charAt(posChave));
            result.append(converterParaHexa(codigo));
            offset = codigo;
        }

        return result.toString();
    }

    private static boolean ehNumeroHexa(String texto) {
        if (texto.length() < 2) {
            return false;
        }
        String carecteres = "0123456789abcdefghABCDEFGH";
        return carecteres.indexOf(texto.charAt(0)) > -1 && carecteres.indexOf(texto.charAt(1)) > -1;
    }

    public static String desencriptar(String textoCifrado, String chave) {
        int tamanhoChave = chave.length();
        int posChave = -1;
        int offset;
        int posTextoCifrado;
        int codigoTemp;
        int codigoCaractereDecifrado;
        String textoHexa;
        StringBuilder result = new StringBuilder();

        if (tamanhoChave == 0) {
            return "";
        }

        textoHexa = textoCifrado.substring(0, 2);
        if (!ehNumeroHexa(textoHexa)) {
            return "";
        }

        offset = Integer.parseInt(textoHexa, 16);
        posTextoCifrado = 2;
        do {
            textoHexa = textoCifrado.substring(posTextoCifrado, posTextoCifrado + 2);
            if (!ehNumeroHexa(textoHexa)) {
                return "";
            }
            codigoTemp = Integer.parseInt(textoHexa, 16);
            if (posChave < (tamanhoChave - 1)) {
                posChave++;
            } else {
                posChave = 0;
            }
            codigoCaractereDecifrado = codigoTemp ^ (int) (chave.charAt(posChave));
            if (codigoCaractereDecifrado <= offset) {
                codigoCaractereDecifrado = 255 + codigoCaractereDecifrado - offset;
            } else {
                codigoCaractereDecifrado = codigoCaractereDecifrado - offset;
            }
            result.append((char) codigoCaractereDecifrado);
            offset = codigoTemp;
            posTextoCifrado = posTextoCifrado + 2;
        } while (posTextoCifrado < textoCifrado.length() - 1);

        return result.toString();
    }

    private static String converterParaHexa(int codigo) {
        String hexConvertido = Integer.toHexString(codigo);
        if (hexConvertido.length() == 1) {
            hexConvertido = '0' + hexConvertido;
        }
        return hexConvertido;
    }

    public static void main(String... args) throws UnsupportedEncodingException {
        final String texto = Uteis.encriptar("treino://98b9a58dc46802a8951a7cd69bcbdd84|<EMAIL>|123|20140806", "Tr3in0");
        System.out.println(texto);
        System.out.println(Uteis.desencriptar(texto, "Tr3in0"));
    }
}
