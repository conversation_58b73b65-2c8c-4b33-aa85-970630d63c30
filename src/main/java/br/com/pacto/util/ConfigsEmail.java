/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pacto.util;

/**
 *
 * <AUTHOR>
 */
public class ConfigsEmail {
    
//    LOGIN(ConfiguracaoTipoEnum.STRING, null, "", AbaConfiguracaoEnum.EMAIL),
    private String login;
//    SENHA(ConfiguracaoTipoEnum.SENHA, null, "", AbaConfiguracaoEnum.EMAIL),
    private String senha;
//    REMETENTE(ConfiguracaoTipoEnum.STRING, null, "", AbaConfiguracaoEnum.EMAIL),
    private String remetente;
//    EMAIL_PADRAO(ConfiguracaoTipoEnum.STRING, null, "", AbaConfiguracaoEnum.EMAIL),
    private String email_padrao;
//    MAIL_SERVER(ConfiguracaoTipoEnum.STRING, null, "", AbaConfiguracaoEnum.EMAIL),
    private String mail_server;
//    CONEXAO_SEGURA(ConfiguracaoTipoEnum.BOOLEAN, null, "false", AbaConfiguracaoEnum.EMAIL),
    private boolean conexao_segura = false;
//    INICIAR_TLS(ConfiguracaoTipoEnum.BOOLEAN, null, "false", AbaConfiguracaoEnum.EMAIL)
    private boolean iniciarTLS = false;

    public String getLogin() {
        return login;
    }

    public void setLogin(String login) {
        this.login = login;
    }

    public String getSenha() {
        return senha;
    }

    public void setSenha(String senha) {
        this.senha = senha;
    }

    public String getRemetente() {
        return remetente;
    }

    public void setRemetente(String remetente) {
        this.remetente = remetente;
    }

    public String getEmail_padrao() {
        return email_padrao;
    }

    public void setEmail_padrao(String email_padrao) {
        this.email_padrao = email_padrao;
    }

    public String getMail_server() {
        return mail_server;
    }

    public void setMail_server(String mail_server) {
        this.mail_server = mail_server;
    }

    public boolean isConexao_segura() {
        return conexao_segura;
    }

    public void setConexao_segura(boolean conexao_segura) {
        this.conexao_segura = conexao_segura;
    }

    public boolean isIniciarTLS() {
        return iniciarTLS;
    }

    public void setIniciarTLS(boolean iniciarTLS) {
        this.iniciarTLS = iniciarTLS;
    }
    
}
