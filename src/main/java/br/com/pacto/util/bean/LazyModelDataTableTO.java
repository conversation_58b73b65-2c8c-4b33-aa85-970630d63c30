/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.util.bean;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import br.com.pacto.controller.to.LazyDataModel;
import br.com.pacto.controller.to.SortOrder;

 public class LazyModelDataTableTO extends LazyDataModel<HistoricoExecucoesTO> {
      
    private List<HistoricoExecucoesTO> datasource;  

    public List<HistoricoExecucoesTO> getDatasource() {
        return datasource;
    }

    public void setDatasource(List<HistoricoExecucoesTO> datasource) {
        this.datasource = datasource;
    }
      
    
    public LazyModelDataTableTO(List<HistoricoExecucoesTO> datasource) {  
        this.datasource = datasource;  
    }  
      
    @Override  
    public HistoricoExecucoesTO getRowData(String rowKey) {  
        for(HistoricoExecucoesTO car : datasource) {  
            if(car.equals(rowKey))  
                return car;  
        }  
  
        return null;  
    }  
  
    @Override  
    public Object getRowKey(HistoricoExecucoesTO car) {  
        return new HistoricoExecucoesTO();  
    }  
  
    @Override  
    public List<HistoricoExecucoesTO> load(int first, int pageSize, String sortField, SortOrder sortOrder, Map<String,String> filters) {
        List<HistoricoExecucoesTO> data = new ArrayList<HistoricoExecucoesTO>();  
  
        //filter  
        for(HistoricoExecucoesTO car : datasource) {  
            boolean match = true;  
  
            for(Iterator<String> it = filters.keySet().iterator(); it.hasNext();) {  
                try {  
                    String filterProperty = it.next();  
                    String filterValue = filters.get(filterProperty);  
                    String fieldValue = String.valueOf(car.getClass().getField(filterProperty).get(car));  
  
                    if(filterValue == null || fieldValue.startsWith(filterValue)) {  
                        match = true;  
                    }  
                    else {  
                        match = false;  
                        break;  
                    }  
                } catch(Exception e) {  
                    match = false;  
                }   
            }  
  
            if(match) {  
                data.add(car);  
            }  
        }  
  
        //sort  
        if(sortField != null) {  
//            Collections.sort(data, new ArrayList<Object>());  
        }  
  
        //rowCount  
        int dataSize = data.size();  
        this.setRowCount(dataSize);  
  
        //paginate  
        if(dataSize > pageSize) {  
            try {  
                return data.subList(first, first + pageSize);  
            }  
            catch(IndexOutOfBoundsException e) {  
                return data.subList(first, first + (dataSize % pageSize));  
            }  
        }  
        else {  
            return data;  
        }  
    }  
}  
