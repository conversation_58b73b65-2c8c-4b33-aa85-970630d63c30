/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.util.bean;

import br.com.pacto.objeto.Uteis;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class HistoricoExecucoesTO implements Serializable {

    private Date dia;
    private Boolean faltou = false;
    private Boolean iniciou = false;
    private String nomeFicha;
    private String diaSemana;
    private String nomeProfessor;
    private String origem;

    public String getDiaSemana() {
        return diaSemana;
    }

    public void setDiaSemana(String diaSemana) {
        this.diaSemana = diaSemana;
    }

    public String getNomeFicha() {
        return nomeFicha;
    }

    public void setNomeFicha(String nomeFicha) {
        this.nomeFicha = nomeFicha;
    }
    private List<ItemHistoricoExecucoesTO> itensExecucoes = new ArrayList<ItemHistoricoExecucoesTO>();

    public Date getDia() {
        return dia;
    }

    public void setDia(Date dia) {
        this.dia = dia;
    }

    public Boolean getFaltou() {
        return faltou;
    }

    public void setFaltou(Boolean faltou) {
        this.faltou = faltou;
    }

    public List<ItemHistoricoExecucoesTO> getItensExecucoes() {
        return itensExecucoes;
    }

    public void setItensExecucoes(List<ItemHistoricoExecucoesTO> itensExecucoes) {
        this.itensExecucoes = itensExecucoes;
    }

    public Integer getDiaNoMes() {
        return Uteis.getDiaMesData(dia);
    }

    public String getMes() {
        return Uteis.getMesNomeReferencia(dia);
    }

    public Boolean getIniciou() {
        return iniciou;
    }

    public void setIniciou(Boolean iniciou) {
        this.iniciou = iniciou;
    }

    public String getNomeProfessor() {
        return nomeProfessor;
    }

    public void setNomeProfessor(String nomeProfessor) {
        this.nomeProfessor = nomeProfessor;
    }

    public String getOrigem() {
        return origem;
    }

    public void setOrigem(String origem) {
        this.origem = origem;
    }
}
