/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.util.bean;

import br.com.pacto.bean.Agendamento;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import java.util.Date;
import br.com.pacto.controller.to.DefaultScheduleEvent;

/**
 *
 * <AUTHOR>
 */
public class AgendaTO extends DefaultScheduleEvent{
    private Agendamento agendamento;
    private Date inicio;
    private Date fim;
    private Boolean agrupar;
    private Boolean agruparPorTipo;
    private Boolean visaoMensal;
    private Integer tamanho;

    public String getCodigoIdentificador(){
        if(visaoMensal){
            if(agrupar && agruparPorTipo){
                return agendamento.getTipoEvento().getCodigo()
                + (agendamento.getDisponibilidade() ? "-D" : "")
                + (Calendario.getData(inicio, "ddMMyyyy"));
            }else{
                return agendamento.getProfessor().getCodigo()+"-"+agendamento.getTipoEvento().getCodigo()
                + (agendamento.getDisponibilidade() ? "-D" : "")
                + (Calendario.getData(inicio, "ddMMyyyy"));
            }
        }else if(!agrupar){
            return agendamento.getProfessor().getCodigo()+"-"+agendamento.getTipoEvento().getCodigo()
                + (agendamento.getCliente() == null ? "" : "-"+agendamento.getCliente())
                + (Calendario.getData(inicio, "ddMMyyyyhhmm"))+"-"+ (Calendario.getData(fim, "ddMMyyyyhhmm"));
        }else if(agruparPorTipo){
            return agendamento.getTipoEvento().getCodigo()
                + agendamento.getProfessor().getCodigo()
                + (agendamento.getCliente() == null ? "" : "-"+agendamento.getCliente())
                + (Calendario.getData(inicio, "ddMMyyyyhhmm"));
        }else{
            return agendamento.getProfessor().getCodigo()
                + (agendamento.getCliente() == null ? "" : "-"+agendamento.getCliente())
                + (Calendario.getDataComHoraZerada(inicio));
        }
    }

    public Boolean getAgrupar() {
        return agrupar;
    }

    public void setAgrupar(Boolean agrupar) {
        this.agrupar = agrupar;
    }

    public Boolean getAgruparPorTipo() {
        return agruparPorTipo;
    }

    public void setAgruparPorTipo(Boolean agruparPorTipo) {
        this.agruparPorTipo = agruparPorTipo;
    }
    
    @Override
    public int hashCode() { 
        return this.getCodigoIdentificador() != null ? 
                this.getCodigoIdentificador().hashCode() : super.hashCode();
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null) {
            return false;
        }
        if (!(obj instanceof AgendaTO)) {
            return false;
        }
        final AgendaTO other = (AgendaTO) obj;
        if(this.getVisaoMensal()){
           if (this.getCodigoIdentificador() == null
                    || !this.getCodigoIdentificador().equals(other.getCodigoIdentificador())) {
                return false;
            } 
        }else{
            if (!(other.getInicio().after(this.getInicio()) && other.getInicio().before(this.getFim()))
                    && !(other.getFim().after(this.getInicio()) && other.getFim().before(this.getFim()))
                    && !(other.getFim().equals(this.getInicio()) || other.getFim().equals(this.getFim()))
                    && !(other.getInicio().equals(this.getInicio()) || other.getInicio().equals(this.getFim()))) {
                return false;
            }
            if (this.getCodigoIdentificador() == null
                    || !this.getCodigoIdentificador().equals(other.getCodigoIdentificador())) {
                return false;
            }
        }
        
        return true;
    }

    public Agendamento getAgendamento() {
        return agendamento;
    }

    public void setAgendamento(Agendamento idAgendamento) {
        this.agendamento = idAgendamento;
    }

    public Date getInicio() {
        return inicio;
    }

    public void setInicio(Date inicio) {
        this.inicio = inicio;
    }

    public Date getFim() {
        return fim;
    }

    public void setFim(Date fim) {
        this.fim = fim;
    }
    
    @Override
    public String getTitle(){
        try {
            return super.getTitle()+ "\n" + Uteis.getDataAplicandoFormatacao(this.getInicio(), "HH:mm")
                + " - " + Uteis.getDataAplicandoFormatacao(this.getFim(), "HH:mm");
        } catch (Exception e) {
            return "";
        }
        
        
    }
    
    public AgendaTO(String title, Date start, Date end, String styleClass, Agendamento idAgendamento, boolean diaTodo) {
		super.setTitle(title);
		super.setStartDate(start);
		super.setEndDate(end);
        super.setStyleClass(styleClass);
        super.setAllDay(diaTodo);
        this.agendamento = idAgendamento;
        this.inicio = new Date(start.getTime());
        this.fim = new Date(end.getTime());
        this.tamanho = new Long(fim.getTime() - inicio.getTime()).intValue();
	}

    public Boolean getVisaoMensal() {
        return visaoMensal;
    }

    public void setVisaoMensal(Boolean visaoMensal) {
        this.visaoMensal = visaoMensal;
    }

    public Integer getTamanho() {
        return tamanho;
    }

    public void setTamanho(Integer tamanho) {
        this.tamanho = tamanho;
    }
}
