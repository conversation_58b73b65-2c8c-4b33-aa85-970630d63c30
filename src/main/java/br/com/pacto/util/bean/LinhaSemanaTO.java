/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.util.bean;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class LinhaSemanaTO implements Serializable{
    
    private Integer hora;
    private List<DiaSemanaAgendaTO> dias;
    private String css;

    public String getCss() {
        return css;
    }

    public void setCss(String css) {
        this.css = css;
    }
    
    

    public LinhaSemanaTO(Integer hora) {
        this.hora = hora;
        dias = new ArrayList<DiaSemanaAgendaTO>();
    }
    public Integer getHora() {
        return hora;
    }

    public void setHora(Integer hora) {
        this.hora = hora;
    }

    public List<DiaSemanaAgendaTO> getDias() {
        return dias;
    }

    public void setDias(List<DiaSemanaAgendaTO> dias) {
        this.dias = dias;
    }
    
    
    
}
