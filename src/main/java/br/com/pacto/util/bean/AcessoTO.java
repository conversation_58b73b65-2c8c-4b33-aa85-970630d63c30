/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.util.bean;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class AcessoTO implements Serializable{
    
    private Integer cliente;
    private Date dataAcesso;

    public AcessoTO(Integer cliente, Date dataAcesso) {
        this.cliente = cliente;
        this.dataAcesso = dataAcesso;
    }

    public Integer getCliente() {
        return cliente;
    }

    public void setCliente(Integer cliente) {
        this.cliente = cliente;
    }

    public Date getDataAcesso() {
        return dataAcesso;
    }

    public void setDataAcesso(Date dataAcesso) {
        this.dataAcesso = dataAcesso;
    }
    
}
