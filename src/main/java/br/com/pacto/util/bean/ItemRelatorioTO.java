package br.com.pacto.util.bean;

import br.com.pacto.objeto.Uteis;

import java.io.Serializable;

public class ItemRelatorioTO implements Serializable {

    private String valor1;
    private String valor2;
    private String valor3;

    public ItemRelatorioTO(String valor1, String valor2) {
        this.valor1 = valor1;
        this.valor2 = valor2;
    }

    public ItemRelatorioTO(String valor1, String valor2, String valor3) {
        this.valor1 = valor1;
        this.valor2 = valor2;
        this.valor3 = valor3;
    }
    public ItemRelatorioTO(String valor1, Double v2, Double v3) {
        this.valor1 = valor1;
        this.valor2 = apresentar(v2);
        this.valor3 = apresentar(v3);
    }

    public String apresentar(Double valor) {
        return valor == null ? "" : Uteis.formatarValorNumerico(Uteis.arredondarForcando2CasasDecimais(valor));
    }

    public String getValor1() {
        return valor1;
    }

    public void setValor1(String valor1) {
        this.valor1 = valor1;
    }

    public String getValor2() {
        return valor2;
    }

    public void setValor2(String valor2) {
        this.valor2 = valor2;
    }

    public String getValor3() {
        return valor3;
    }

    public void setValor3(String valor3) {
        this.valor3 = valor3;
    }
}
