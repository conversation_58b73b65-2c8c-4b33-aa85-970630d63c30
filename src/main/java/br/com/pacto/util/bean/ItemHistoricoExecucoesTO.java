/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.util.bean;

import br.com.pacto.bean.atividade.Atividade;
import br.com.pacto.bean.serie.SerieRealizada;
import br.com.pacto.objeto.Uteis;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class ItemHistoricoExecucoesTO implements Serializable{
    private String nome;
    private Atividade atividade;
    private Integer nrNotificacoes;
    private Integer ordem = 0;
    private List<SerieRealizada> seriesRealizadas = new ArrayList<SerieRealizada>();
    private SerieRealizada serieMaisGrave = null;

    public Atividade getAtividade() {
        return atividade;
    }

    public void setAtividade(Atividade atividade) {
        this.atividade = atividade;
    }

    public SerieRealizada getSerieMaisGrave() {
        return serieMaisGrave;
    }

    public void setSerieMaisGrave(SerieRealizada serieMaisGrave) {
        this.serieMaisGrave = serieMaisGrave;
    }
    public String getDuracao(){
        return Uteis.converterSegundosEmMinutos(getSeries());
    }
    public Integer getSeries() {
        if(atividade.getAerobica()){
            Integer segundosDuracao = 0;
            Long diff = 0L;
            for(SerieRealizada serie : seriesRealizadas){
                segundosDuracao += serie.getDuracao();
            }
           // return  Double.valueOf(segundosDuracao/60).intValue();
            return segundosDuracao;
        }else{
            return seriesRealizadas.size();
        }
    }

    public String getStyle(){
        return getSerieMaisGrave() == null || getSerieMaisGrave().getTipoNotificacao() == null ?
                "semNotificacao" : getSerieMaisGrave().getTipoNotificacao().getStyle();
    }

    public Integer getResultado() {
        if(atividade.getAerobica()){
            Integer distancia = 0;
            for(SerieRealizada serie : seriesRealizadas){
                distancia += serie.getDistancia();
            }
           return  distancia;
        }else{
           return serieMaisGrave == null ? 0 : serieMaisGrave.getCarga().intValue();
        }
    }


    public List<SerieRealizada> getSeriesRealizadas() {
        return seriesRealizadas;
    }

    public void setSeriesRealizadas(List<SerieRealizada> seriesRealizadas) {
        this.seriesRealizadas = seriesRealizadas;
    }

    public Integer getNrNotificacoes() {
        return nrNotificacoes;
    }

    public void setNrNotificacoes(Integer nrNotificacoes) {
        this.nrNotificacoes = nrNotificacoes;
    }

    public Integer getOrdem() {
        return ordem;
    }

    public void setOrdem(Integer ordem) {
        this.ordem = ordem;
    }

    public String getNome() {
        return nome;
    }
    public String getNome_Apresentar() {
        if(getNome().length() >= 70){
            return getNome().substring(0 , 69)+"...";
        }
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }
    
    
}
