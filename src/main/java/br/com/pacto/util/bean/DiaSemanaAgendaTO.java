/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.util.bean;

import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import java.io.Serializable;
import java.util.*;

/**
 *
 * <AUTHOR>
 */
public class DiaSemanaAgendaTO implements Serializable{
    private Date dia;
    private Integer diaSemana;
    private Integer hora;
    private Integer nrAlunos = 0;
    private Integer nrAlunosAcessaram = 0;
    private List<AgendaTotalTO> agendamentos;
    private List<AgendadoTO> agendados;

    public boolean isHoje(){
        try {
            return  Calendario.igual(Calendario.hoje(), dia);
        }catch (Exception e){
            return false;
        }
    }

    public DiaSemanaAgendaTO(Integer diaSemana, Integer hora) {
        this.diaSemana = diaSemana;
        agendamentos = new ArrayList<AgendaTotalTO>();
        agendados = new ArrayList<AgendadoTO>();
    }

    public DiaSemanaAgendaTO(Date dia, Integer hora) {
        this.dia = dia;
        this.diaSemana = Uteis.getDiaMesData(dia);
        agendamentos = new ArrayList<AgendaTotalTO>();
        agendados = new ArrayList<AgendadoTO>();
    }

    public final void agendamentoOrderBy() {
        Collections.sort(agendamentos, new Comparator<AgendaTotalTO>() {
            @Override
            public int compare(AgendaTotalTO first, AgendaTotalTO second) {
                return first.getDateInicio().compareTo(second.getDateInicio());
            }
        });
    }

    public Integer getHora() {
        return hora;
    }

    public void setHora(Integer hora) {
        this.hora = hora;
    }
    
    public Integer getDiaSemana() {
        return diaSemana;
    }

    public void setDiaSemana(Integer diaSemana) {
        this.diaSemana = diaSemana;
    }

    public List<AgendaTotalTO> getAgendamentos() {
        return agendamentos;
    }

    public void setAgendamentos(List<AgendaTotalTO> agendamentos) {
        this.agendamentos = agendamentos;
    }

    public Integer getNrAlunos() {
        return nrAlunos;
    }

    public void setNrAlunos(Integer nrAlunos) {
        this.nrAlunos = nrAlunos;
    }

    public List<AgendadoTO> getAgendados() {
        return agendados;
    }

    public void setAgendados(List<AgendadoTO> agendados) {
        this.agendados = agendados;
    }

    public Date getDia() {
        return dia;
    }

    public void setDia(Date dia) {
        this.dia = dia;
    }

    public Integer getNrAlunosAcessaram() {
        return nrAlunosAcessaram;
    }

    public void setNrAlunosAcessaram(Integer nrAlunosAcessaram) {
        this.nrAlunosAcessaram = nrAlunosAcessaram;
    }
    
}
