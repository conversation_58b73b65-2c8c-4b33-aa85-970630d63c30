/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.util.bean;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class CategoriaRecursosTO implements Serializable {
    private String categoria;
    private List<RecursoTO> recursos = new ArrayList<RecursoTO>();
    private List<RecursoTO> funcionalidades = new ArrayList<RecursoTO>();

    public List<RecursoTO> getFuncionalidades() {
        return funcionalidades;
    }

    public void setFuncionalidades(List<RecursoTO> funcionalidades) {
        this.funcionalidades = funcionalidades;
    }
    
    public String getCategoria() {
        return categoria;
    }

    public void setCategoria(String categoria) {
        this.categoria = categoria;
    }

    public List<RecursoTO> getRecursos() {
        return recursos;
    }

    public void setRecursos(List<RecursoTO> recursos) {
        this.recursos = recursos;
    }
}
