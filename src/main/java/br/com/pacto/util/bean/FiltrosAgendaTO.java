/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.util.bean;

import br.com.pacto.bean.aula.Ambiente;
import br.com.pacto.bean.aula.Modalidade;
import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.professor.ProfessorSintetico;
import br.com.pacto.bean.tipoEvento.TipoEvento;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.util.impl.Ordenacao;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.faces.model.SelectItem;

/**
 *
 * <AUTHOR>
 */
public class FiltrosAgendaTO implements Serializable {

    private Integer codigoProfessorSelecionado = 0;
    private Date inicio;
    private Date fim;
    private Date mesAtual = Calendario.hoje();
    private List<String> professoresSelecionados = new ArrayList<String>();
    private List<String> tiposSelecionados = new ArrayList<String>();
    private Boolean disponibilidades = Boolean.FALSE;
    private List<GenericoTO> statusFiltros = new ArrayList<GenericoTO>();
    private List<TipoEvento> tiposEvento = new ArrayList<TipoEvento>();
    private List<GenericoTO> tiposGenerico = new ArrayList<GenericoTO>();
    private List<SelectItem> tiposSelectItem = new ArrayList<SelectItem>();
    private List<SelectItem> alunosCarteira = new ArrayList<SelectItem>();
    private List<SelectItem> professores = new ArrayList<SelectItem>();
    private List<SelectItem> professoresModal = new ArrayList<SelectItem>();
    private ClienteSintetico cliente = new ClienteSintetico();
    private Map<Integer, List<ProfessorSintetico>> tipoProfessores = new HashMap<Integer, List<ProfessorSintetico>>();
    private boolean visaoMensal = false;
    private Boolean agruparPorEventos = true, agrupar = true;
    private String panelMenuLateral = "2,3";
    private String modalidades = "";
    private String ambientes = "";
    
    private List<String> professoresSelecionadosCods = new ArrayList<String>();
    private List<String> tiposSelecionadosCods = new ArrayList<String>();
    private List<String> ambientesSelecionados = new ArrayList<String>();
    
    public void montarAmbientes(List<Ambiente> ambientesLista){
        ambientes = "";
        for(Ambiente amb : ambientesLista){
            if(amb.getEscolhido()){
                ambientes += ","+amb.getCodigo();
            }
        }
        ambientes = ambientes.replaceFirst(",", "");
    }
    
    public void montarModalidades(List<Modalidade> modalidadesLista){
        modalidades = "";
        for(Modalidade mod : modalidadesLista){
            if(mod.getEscolhido()){
                modalidades += ","+mod.getCodigo();
            }
        }
        modalidades = modalidades.replaceFirst(",", "");
    }
    

    public void addProfessorTipo(TipoEvento tipo, ProfessorSintetico professor) {
        List<ProfessorSintetico> lista = tipoProfessores.get(tipo.getCodigo());
        if (lista == null) {
            lista = new ArrayList<ProfessorSintetico>();
            tipoProfessores.put(tipo.getCodigo(), lista);
        }
        if (!lista.contains(professor)) {
            lista.add(professor);
        }

    }

    public void povoarProfessores(TipoEvento tipo) {
        List<ProfessorSintetico> lista = tipoProfessores.get(tipo.getCodigo());
        Ordenacao.ordenarLista(lista, "nome");
        professoresModal = new ArrayList<SelectItem>();
        if (lista != null) {
            for (ProfessorSintetico pfs : lista) {
                getProfessoresModal().add(new SelectItem(pfs.getCodigo(), pfs.getNomeMinusculo()));
            }
        }
    }

    public ClienteSintetico getCliente() {
        return cliente;
    }

    public void setCliente(ClienteSintetico cliente) {
        this.cliente = cliente;
    }

    public boolean mostrarNomeProfessor() {
        return !agrupar
                || !agruparPorEventos;
    }

    public boolean mostrarTipo() {
        return !agrupar
                || agruparPorEventos;
    }

    public List<SelectItem> getTiposSelectItem() {
        return tiposSelectItem;
    }

    public void setTiposSelectItem(List<SelectItem> tiposSelectItem) {
        this.tiposSelectItem = tiposSelectItem;
    }

    public List<GenericoTO> getTiposGenerico() {
        return tiposGenerico;
    }

    public void setTiposGenerico(List<GenericoTO> tiposGenerico) {
        this.tiposGenerico = tiposGenerico;
    }

    public Date getMesAtual() {
        return mesAtual;
    }

    public void setMesAtual(Date mesAtual) {
        this.mesAtual = mesAtual;
    }

    public List<TipoEvento> getTiposEvento() {
        return tiposEvento;
    }

    public void setTiposEvento(List<TipoEvento> tiposEvento) {
        this.tiposEvento = tiposEvento;
    }

    public Integer getCodigoProfessorSelecionado() {
        return codigoProfessorSelecionado;
    }

    public void setCodigoProfessorSelecionado(Integer codigoProfessorSelecionado) {
        this.codigoProfessorSelecionado = codigoProfessorSelecionado;
    }

    public Date getInicio() {
        return inicio;
    }

    public void setInicio(Date inicio) {
        this.inicio = inicio;
    }

    public Date getFim() {
        return fim;
    }

    public void setFim(Date fim) {
        this.fim = fim;
    }

    public List<String> getProfessoresSelecionados() {
        return professoresSelecionados;
    }

    public void setProfessoresSelecionados(List<String> professoresSelecionados) {
        this.professoresSelecionados = professoresSelecionados;
    }

    public List<String> getTiposSelecionados() {
        return tiposSelecionados;
    }

    public void setTiposSelecionados(List<String> tiposSelecionados) {
        this.tiposSelecionados = tiposSelecionados;
    }

    public Boolean getDisponibilidades() {
        return disponibilidades;
    }

    public void setDisponibilidades(Boolean disponibilidades) {
        this.disponibilidades = disponibilidades;
    }

    public List<GenericoTO> getStatusFiltros() {
        return statusFiltros;
    }

    public void setStatusFiltros(List<GenericoTO> statusFiltros) {
        this.statusFiltros = statusFiltros;
    }

    public List<SelectItem> getAlunosCarteira() {
        return alunosCarteira;
    }

    public void setAlunosCarteira(List<SelectItem> alunosCarteira) {
        this.alunosCarteira = alunosCarteira;
    }

    public List<SelectItem> getProfessores() {
        return professores;
    }

    public void setProfessores(List<SelectItem> professores) {
        this.professores = professores;
    }

    public List<SelectItem> getProfessoresModal() {
        return professoresModal;
    }

    public void setProfessoresModal(List<SelectItem> professoresModal) {
        this.professoresModal = professoresModal;
    }

    public Boolean getAgruparPorEventos() {
        return agruparPorEventos;
    }

    public void setAgruparPorEventos(Boolean agruparPorEventos) {
        if (agruparPorEventos) {
            setPanelMenuLateral("2,3");
        } else {
            setPanelMenuLateral("1,3");
        }
        this.agruparPorEventos = agruparPorEventos;
    }

    public String getPanelMenuLateral() {
        return panelMenuLateral;
    }

    public void setPanelMenuLateral(String panelMenuLateral) {
        this.panelMenuLateral = panelMenuLateral;
    }

    public Boolean getAgrupar() {
        return agrupar;
    }

    public void setAgrupar(Boolean agrupar) {
        this.agrupar = agrupar;
    }

    public Integer getTiposSelecionadosCount() {
        Integer count = 0;
        for (GenericoTO item : tiposGenerico) {
            if (item.getEscolhido()) {
                count++;
            }
        }
        return count;
    }

    public Map<Integer, List<ProfessorSintetico>> getTipoProfessores() {
        return tipoProfessores;
    }

    public void setTipoProfessores(Map<Integer, List<ProfessorSintetico>> tipoProfessores) {
        this.tipoProfessores = tipoProfessores;
    }

    public boolean isVisaoMensal() {
        return visaoMensal;
    }

    public void setVisaoMensal(boolean visaoMensal) {
        this.visaoMensal = visaoMensal;
    }

    public String getModalidades() {
        return modalidades;
    }

    public void setModalidades(String modalidades) {
        this.modalidades = modalidades;
    }

    public String getAmbientes() {
        return ambientes;
    }

    public void setAmbientes(String ambientes) {
        this.ambientes = ambientes;
    }
    
    public String getInicioFimApresentar() {
        
        String mesFim = Calendario.getData(fim, "MMMMM");
        return Calendario.igual(inicio, fim) ? 
                Calendario.getData(inicio, "MMMMM d, yyyy")
                : Calendario.getData(inicio, "MMMMM d") + " - "
                +(Calendario.getData(inicio, "MMMMM").equals(mesFim) ? "" : mesFim+" ")+
                Calendario.getData(fim, "d, yyyy");
    }

    public List<String> getProfessoresSelecionadosCods() {
        return professoresSelecionadosCods;
    }

    public void setProfessoresSelecionadosCods(List<String> professoresSelecionadosCods) {
        this.professoresSelecionadosCods = professoresSelecionadosCods;
    }

    public void setProfessoresSelecionadosCodsInteger(List<Integer> professoresSelecionadosCods) {
        if(professoresSelecionadosCods != null && !professoresSelecionadosCods.isEmpty()){
            this.professoresSelecionadosCods = new ArrayList<>();
            for(Integer cod : professoresSelecionadosCods){
                this.professoresSelecionadosCods.add(cod.toString());
            }
        }
    }

    public List<String> getTiposSelecionadosCods() {
        return tiposSelecionadosCods;
    }

    public void setTiposSelecionadosCods(List<String> tiposSelecionadosCods) {
        this.tiposSelecionadosCods = tiposSelecionadosCods;
    }

    public void setTiposSelecionadosCodsInteger(List<Integer> tiposSelecionadosCods) {
        if(tiposSelecionadosCods != null && !tiposSelecionadosCods.isEmpty()){
            this.tiposSelecionadosCods = new ArrayList<>();
            for(Integer cod : tiposSelecionadosCods){
                this.tiposSelecionadosCods.add(cod.toString());
            }
        }
    }

    public List<String> getAmbientesSelecionados() {
        return ambientesSelecionados;
    }

    public void setAmbientesSelecionados(List<String> ambientesSelecionados) {
        this.ambientesSelecionados = ambientesSelecionados;
    }


    public void setAmbientesSelecionadosInteger(List<Integer> ambientesSelecionados) {
        if(ambientesSelecionados != null && !ambientesSelecionados.isEmpty()){
            this.ambientesSelecionados = new ArrayList<>();
            for(Integer cod : ambientesSelecionados){
                this.ambientesSelecionados.add(cod.toString());
            }
        }
    }
    
    
}
