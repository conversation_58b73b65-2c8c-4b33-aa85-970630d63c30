/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.util.bean;

import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.dao.intf.cliente.ClienteSinteticoDao;
import br.com.pacto.util.impl.JSFUtilities;
import java.util.logging.Level;
import java.util.logging.Logger;
import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.convert.Converter;
import javax.faces.convert.FacesConverter;
import org.springframework.beans.factory.annotation.Autowired;

/**
 *
 * <AUTHOR>
 */
@FacesConverter(forClass = ClienteSintetico.class, value = "ClienteConverter")
public class ClienteConverter implements Converter {

    public ClienteConverter() {

    }

    @Override
    public Object getAsObject(FacesContext arg0, UIComponent arg1, String arg2) {
        if (arg2 == null || arg2.trim().equals("") || arg2.trim().replaceAll("[^0-9]*", "").equals("")) {
            return null;
        } else {
            try {
                ClienteSintetico obj = new ClienteSintetico();
                obj.setCodigo(Integer.parseInt(arg2.trim()));
                return obj;
            } catch (ClassCastException castEx) {
                Logger.getLogger(ClienteConverter.class.getName()).log(Level.SEVERE, null, castEx);
            } catch (NumberFormatException exception) {
            } catch (Exception ex) {
                Logger.getLogger(ClienteConverter.class.getName()).log(Level.SEVERE, null, ex);
            }
        }
        return null;
    }

    @Override
    public String getAsString(FacesContext arg0, UIComponent arg1, Object arg2) {
        try {
            ClienteSintetico cliente = (ClienteSintetico) arg2;
            if (cliente == null || cliente.getCodigo() == null) {
                return null;
            }
            return String.valueOf(cliente.getCodigo());
        } catch (ClassCastException castEx) {
            Logger.getLogger(ClienteConverter.class.getName()).log(Level.SEVERE, null, castEx);
        }
        return "";
    }

}
