/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.util.bean;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class HoraAgendaTO implements Serializable{
    
    private Integer hora;
    private Integer chave;
    private List<AgendaTotalTO> agendamentos;
    private Integer nrVagas;
    private Integer nrVagasDisponiveis;
    private Integer nrVagasPreenchidas;

    public HoraAgendaTO() {
    }

    public HoraAgendaTO(Integer hora, Integer chave) {
        this.hora = hora;
        this.chave = chave;
        this.agendamentos = new ArrayList<AgendaTotalTO>();
        this.nrVagas = 0;
        this.nrVagasDisponiveis = 0;
        this.nrVagasPreenchidas = 0;
    }

    public final void agendamentoOrderBy() {
        Collections.sort(agendamentos, new Comparator<AgendaTotalTO>() {
            @Override
            public int compare(AgendaTotalTO first, AgendaTotalTO second) {
                return first.getDateInicio().compareTo(second.getDateInicio());
            }
        });
    }

    public Integer getHora() {
        return hora;
    }

    public void setHora(Integer hora) {
        this.hora = hora;
    }

    public List<AgendaTotalTO> getAgendamentos() {
        return agendamentos;
    }

    public void setAgendamentos(List<AgendaTotalTO> agendamentos) {
        this.agendamentos = agendamentos;
    }

    public Integer getNrVagas() {
        return nrVagas;
    }

    public void setNrVagas(Integer nrVagas) {
        this.nrVagas = nrVagas;
    }

    public Integer getChave() {
        return chave;
    }

    public void setChave(Integer chave) {
        this.chave = chave;
    }

    public Integer getNrVagasDisponiveis() {
        return nrVagasDisponiveis;
    }

    public void setNrVagasDisponiveis(Integer nrVagasDisponiveis) {
        this.nrVagasDisponiveis = nrVagasDisponiveis;
    }

    public Integer getNrVagasPreenchidas() {
        return nrVagasPreenchidas;
    }

    public void setNrVagasPreenchidas(Integer nrVagasPreenchidas) {
        this.nrVagasPreenchidas = nrVagasPreenchidas;
    }
    
    
}
