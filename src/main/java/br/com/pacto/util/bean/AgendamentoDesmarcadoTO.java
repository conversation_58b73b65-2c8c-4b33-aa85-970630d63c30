/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.util.bean;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class AgendamentoDesmarcadoTO implements Serializable{
    
    private String idAgendamento;
    private Integer codigoCliente;
    private boolean reposto;
    private Integer codigoContrato;
    private String justificativa;

    public AgendamentoDesmarcadoTO(String idAgendamento, Integer codigoCliente, boolean reposto, Integer codigoContrato, String justificativa) {
        this.idAgendamento = idAgendamento;
        this.codigoCliente = codigoCliente;
        this.reposto = reposto;
        this.codigoContrato = codigoContrato;
        this.justificativa = justificativa;
    }

    public boolean isReposto() {
        return reposto;
    }

    public void setReposto(boolean reposto) {
        this.reposto = reposto;
    }
    
    public String getIdAgendamento() {
        return idAgendamento;
    }

    public void setIdAgendamento(String idAgendamento) {
        this.idAgendamento = idAgendamento;
    }

    public Integer getCodigoCliente() {
        return codigoCliente;
    }

    public void setCodigoCliente(Integer codigoCliente) {
        this.codigoCliente = codigoCliente;
    }

    public Integer getCodigoContrato() {
        return codigoContrato;
    }

    public void setCodigoContrato(Integer codigoContrato) {
        this.codigoContrato = codigoContrato;
    }

    public String getJustificativa() { return justificativa; }

    public void setJustificativa(String justificativa) { this.justificativa = justificativa; }

}
