/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.util.bean;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class LinhaAgendaTO implements Serializable{
    
    private Integer chave;
    private String nome;
    private String css;
    private List<HoraAgendaTO> lista = new ArrayList<HoraAgendaTO>();
    
    public boolean getTemAgendamento(){
        try {
            for(HoraAgendaTO h : lista){
                if(!h.getAgendamentos().isEmpty()){
                    return true;
                }
            }
            return false;
        } catch (Exception e) {
            return false;
        }
    }

    public LinhaAgendaTO(Integer chave, String nome, String css) {
        this.chave = chave;
        this.nome = nome;
        this.css = css;
    }
    
    

    public Integer getChave() {
        return chave;
    }

    public void setChave(Integer chave) {
        this.chave = chave;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getCss() {
        return css;
    }

    public void setCss(String css) {
        this.css = css;
    }

    public List<HoraAgendaTO> getLista() {
        return lista;
    }

    public void setLista(List<HoraAgendaTO> lista) {
        this.lista = lista;
    }
    
    
}
