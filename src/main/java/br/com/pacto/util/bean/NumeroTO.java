/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.util.bean;

import br.com.pacto.objeto.Uteis;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class NumeroTO implements Serializable {

    private Integer milhar = 0;
    private Integer centena = 0;
    private Integer dezena = 0;
    private Integer unidade = 0;
    private Integer decimal = 0;

    public NumeroTO() {
    }
    
    public Integer getValorInteiro() {
        return Integer.valueOf(milhar.toString() + centena.toString() + dezena.toString() + unidade.toString());
    }

    public Double getValorDouble() {
        return Double.valueOf(milhar.toString() + centena.toString() + dezena.toString() + unidade.toString() + "." + decimal.toString());
    }

    public String getValorHora() {
        return centena.toString() + dezena.toString() + ":" + unidade.toString() + decimal.toString();
    }

    public NumeroTO(Integer numero) {
        numero = numero == null ? 0 : numero;
        String numberStr = numero < 1000
                ? (numero < 100.0 ? (numero < 10.0 ? "000" : "00") : "0") + numero.toString()
                : numero.toString();
        try {
            milhar = Integer.valueOf(numberStr.substring(0, 1));
            centena = Integer.valueOf(numberStr.substring(1, 2));
            dezena = Integer.valueOf(numberStr.substring(2, 3));
            unidade = Integer.valueOf(numberStr.substring(3, 4));
        } catch (Exception e) {
            Uteis.logar(e, NumeroTO.class);
        }

    }

    public NumeroTO(String hora) {
        hora = hora == null || hora.isEmpty() ? "00:00" : hora;
        if (hora.contains("_")) {
            hora = hora.replaceAll("_", "0");
        }
        try {
            centena = Integer.valueOf(hora.substring(0, 1));
            dezena = Integer.valueOf(hora.substring(1, 2));
            unidade = Integer.valueOf(hora.substring(3, 4));
            decimal = Integer.valueOf(hora.substring(4, 5));
        } catch (Exception e) {
            Uteis.logar(e, NumeroTO.class);
        }

    }

    public NumeroTO(Double numero) {
        numero = numero == null ? 0.0 : numero;
        String numberStr = numero < 1000
                ? (numero < 100.0 ? (numero < 10.0 ? "000" : "00") : "0") + numero.toString()
                : numero.toString();
        try {
            milhar = Integer.valueOf(numberStr.substring(0, 1));
            centena = Integer.valueOf(numberStr.substring(1, 2));
            dezena = Integer.valueOf(numberStr.substring(2, 3));
            unidade = Integer.valueOf(numberStr.substring(3, 4));
            decimal = Integer.valueOf(numberStr.substring(5, 6));
        } catch (Exception e) {
            Uteis.logar(e, NumeroTO.class);
        }
    }

    public Integer getCentena() {
        return centena;
    }

    public void setCentena(Integer centena) {
        this.centena = centena;
    }

    public Integer getDezena() {
        return dezena;
    }

    public void setDezena(Integer dezena) {
        this.dezena = dezena;
    }

    public Integer getUnidade() {
        return unidade;
    }

    public void setUnidade(Integer unidade) {
        this.unidade = unidade;
    }

    public Integer getDecimal() {
        return decimal;
    }

    public void setDecimal(Integer decimal) {
        this.decimal = decimal;
    }

    public Integer getMilhar() {
        return milhar;
    }

    public void setMilhar(Integer milhar) {
        this.milhar = milhar;
    }
    
    @Override
    public NumeroTO clone(){
        NumeroTO novo = new NumeroTO();
        novo.setCentena(centena);
        novo.setDecimal(decimal);
        novo.setDezena(dezena);
        novo.setMilhar(milhar);
        novo.setUnidade(unidade);
        return novo;
    }

}
