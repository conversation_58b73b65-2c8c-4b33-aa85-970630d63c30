/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.util.bean;

import br.com.pacto.bean.perfil.permissao.TipoPermissaoEnum;
import br.com.pacto.bean.perfil.permissao.TipoRecurso;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class RecursoTO extends GenericoTO {

    private List<GenericoTO> tiposPermissoes;
    private TipoRecurso tipo;
    private List<TipoPermissaoEnum> tiposConjuntos;

    public RecursoTO(Integer codigo, String nome, String detalhe,
            List<GenericoTO> tiposPermissoes, TipoRecurso tipo,
            TipoPermissaoEnum[] tiposConjuntos) {
        super(codigo, nome);
        this.setDetalhe(detalhe);
        this.tipo = tipo;
        this.tiposPermissoes = new ArrayList<GenericoTO>(tiposPermissoes);
        this.tiposConjuntos = tiposConjuntos == null ? new ArrayList<TipoPermissaoEnum>() : Arrays.asList(tiposConjuntos);
    }

    public RecursoTO() {
    }

    public List<TipoPermissaoEnum> getTiposConjuntos() {
        return tiposConjuntos;
    }

    public void setTiposConjuntos(List<TipoPermissaoEnum> tiposConjuntos) {
        this.tiposConjuntos = tiposConjuntos;
    }

    public TipoRecurso getTipo() {
        return tipo;
    }

    public void setTipo(TipoRecurso tipo) {
        this.tipo = tipo;
    }

    @Override
    public String getLabel() {
        return super.getLabel();
    }

    public List<GenericoTO> getTiposPermissoes() {
        return tiposPermissoes;
    }

    public void setTiposPermissoes(List<GenericoTO> tiposPermissoes) {
        this.tiposPermissoes = tiposPermissoes;
    }

    public boolean isTodos() {
        return getCodigo() == null;
    }
}
