/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.util.bean;

import br.com.pacto.bean.bi.DashboardBI;
import static br.com.pacto.bean.gestao.IndicadorGraficoEnum.IND_AGENDAMENTOS;
import static br.com.pacto.bean.gestao.IndicadorGraficoEnum.IND_AGENDAMENTOS_CANCELARAM;
import static br.com.pacto.bean.gestao.IndicadorGraficoEnum.IND_AGENDAMENTOS_EXECUTARAM;
import static br.com.pacto.bean.gestao.IndicadorGraficoEnum.IND_AGENDAMENTOS_FALTARAM;
import static br.com.pacto.bean.gestao.IndicadorGraficoEnum.IND_AGENDAMENTO_AVALIACOES_FISICAS;
import static br.com.pacto.bean.gestao.IndicadorGraficoEnum.IND_AGENDAMENTO_NOVOS_TREINOS;
import static br.com.pacto.bean.gestao.IndicadorGraficoEnum.IND_AGENDAMENTO_PROFESSORES;
import static br.com.pacto.bean.gestao.IndicadorGraficoEnum.IND_AGENDAMENTO_TREINO_RENOVADOS;
import static br.com.pacto.bean.gestao.IndicadorGraficoEnum.IND_AGENDAMENTO_TREINO_REVISADOS;
import static br.com.pacto.bean.gestao.IndicadorGraficoEnum.IND_ATIVOS;
import static br.com.pacto.bean.gestao.IndicadorGraficoEnum.IND_ATIVOS_TREINO;
import static br.com.pacto.bean.gestao.IndicadorGraficoEnum.IND_A_VENCER;
import static br.com.pacto.bean.gestao.IndicadorGraficoEnum.IND_COM_AVALIACAO;
import static br.com.pacto.bean.gestao.IndicadorGraficoEnum.IND_EM_DIA;
import static br.com.pacto.bean.gestao.IndicadorGraficoEnum.IND_ESTRELA_1;
import static br.com.pacto.bean.gestao.IndicadorGraficoEnum.IND_ESTRELA_2;
import static br.com.pacto.bean.gestao.IndicadorGraficoEnum.IND_ESTRELA_3;
import static br.com.pacto.bean.gestao.IndicadorGraficoEnum.IND_ESTRELA_4;
import static br.com.pacto.bean.gestao.IndicadorGraficoEnum.IND_ESTRELA_5;
import static br.com.pacto.bean.gestao.IndicadorGraficoEnum.IND_HORAS_DISPONIBILIDADE;
import static br.com.pacto.bean.gestao.IndicadorGraficoEnum.IND_HORAS_EXECUTADAS;
import static br.com.pacto.bean.gestao.IndicadorGraficoEnum.IND_INATIVOS;
import static br.com.pacto.bean.gestao.IndicadorGraficoEnum.IND_MEDIA_AVALIACOES;
import static br.com.pacto.bean.gestao.IndicadorGraficoEnum.IND_NAO_RENOVADOS;
import static br.com.pacto.bean.gestao.IndicadorGraficoEnum.IND_NOVOS_CARTEIRA;
import static br.com.pacto.bean.gestao.IndicadorGraficoEnum.IND_NR_AVALIACOES;
import static br.com.pacto.bean.gestao.IndicadorGraficoEnum.IND_PERC_OCUPACAO;
import static br.com.pacto.bean.gestao.IndicadorGraficoEnum.IND_PERC_TREINO_EM_DIA;
import static br.com.pacto.bean.gestao.IndicadorGraficoEnum.IND_PERC_TREINO_VENCIDOS;
import static br.com.pacto.bean.gestao.IndicadorGraficoEnum.IND_RENOVADOS;
import static br.com.pacto.bean.gestao.IndicadorGraficoEnum.IND_RENOVAR;
import static br.com.pacto.bean.gestao.IndicadorGraficoEnum.IND_SEM_AVALIACAO;
import static br.com.pacto.bean.gestao.IndicadorGraficoEnum.IND_SEM_TREINO;
import static br.com.pacto.bean.gestao.IndicadorGraficoEnum.IND_TEMPO_MEDIO_CARTEIRA;
import static br.com.pacto.bean.gestao.IndicadorGraficoEnum.IND_TEMPO_MEDIO_PROGRAMA;
import static br.com.pacto.bean.gestao.IndicadorGraficoEnum.IND_TOTAL_ALUNOS;
import static br.com.pacto.bean.gestao.IndicadorGraficoEnum.IND_TROCARAM_CARTEIRA;
import static br.com.pacto.bean.gestao.IndicadorGraficoEnum.IND_VENCIDOS;
import br.com.pacto.bean.gestao.ItemGraficoTO;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.util.UteisValidacao;
import org.json.JSONArray;
import org.json.JSONObject;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class GraficoIndicadores {

    public static String montarItens(List<ItemGraficoTO> selecionados, List<String> prselecionados, 
            Map<String, String> professores, boolean mostrarNome, Map<Integer, String> mapaCores) {
        String itens = "";
        for (ItemGraficoTO tg : selecionados) {
            if (tg.isSelecionado()) {
                for (String c : prselecionados) {
                    itens += montarItem(tg, c, professores.get(c), true, mostrarNome, mapaCores.get(Integer.valueOf(c)));
                }
            }
        }
        return itens;
    }

    public static String montarJSON(List<ItemGraficoTO> selecionados, List<DashboardBI> dados) throws Exception {
        List<String> adicionados = new ArrayList<String>();
        JSONArray array = new JSONArray();
        List<JSONObject> lista = new ArrayList<JSONObject>();
        Map<String, List<DashboardBI>> mapaDados = new HashMap<String, List<DashboardBI>>();
        List<Date> meses = new ArrayList<Date>();
        for (DashboardBI d : dados) {
            String mes = (d.getMes() > 9 ? "" : "0") + d.getMes() + "/" + d.getAno();
            List<DashboardBI> dds = mapaDados.get(mes);
            if (dds == null) {
                dds = new ArrayList<DashboardBI>();
                mapaDados.put(mes, dds);
            }
            dds.add(d);
            if(!meses.contains(Uteis.getDate("01/"+mes))){
                meses.add(Uteis.getDate("01/"+mes));
            }
        }
        Collections.sort(meses);
        Collections.reverse(meses);
        for (Date dMes : meses) {
            String mes = Uteis.getDataAplicandoFormatacao(dMes, "MM/yyyy");
            JSONObject obj = new JSONObject();
            obj.put("mes", mes);
            for (DashboardBI d : mapaDados.get(mes)) {
                if (adicionados.contains(d.getCodigoProfessor() + " - " + mes)) {
                    continue;
                }
                adicionados.add(d.getCodigoProfessor() + " - " + mes);
                for (ItemGraficoTO tg : selecionados) {
                    if (tg.isSelecionado()) {
                        switch (tg.getIndicador()) {
                            case IND_TOTAL_ALUNOS:
                                obj.put(tg.getIndicador().getValueField() + d.getCodigoProfessor(), d.getTotalAlunos());
                                break;
                            case IND_ATIVOS:
                                obj.put(tg.getIndicador().getValueField() + d.getCodigoProfessor(), d.getTotalAlunosAtivos());
                                break;
                            case IND_INATIVOS:
                                obj.put(tg.getIndicador().getValueField() + d.getCodigoProfessor(), d.getTotalAlunosInativos());
                                break;
                            case IND_ATIVOS_TREINO:
                                obj.put(tg.getIndicador().getValueField() + d.getCodigoProfessor(), d.getTotalAlunosTreino());
                                break;
                            case IND_EM_DIA:
                                obj.put(tg.getIndicador().getValueField() + d.getCodigoProfessor(), d.getTotalTreinosEmdia());
                                break;
                            case IND_VENCIDOS:
                                obj.put(tg.getIndicador().getValueField() + d.getCodigoProfessor(), d.getTotalTreinosVencidos());
                                break;
                            case IND_RENOVAR:
                                obj.put(tg.getIndicador().getValueField() + d.getCodigoProfessor(), d.getTotalTreinosRenovar());
                                break;
                            case IND_AGENDAMENTOS:
                                obj.put(tg.getIndicador().getValueField() + d.getCodigoProfessor(), d.getAgendamentos());
                                break;
                            case IND_AGENDAMENTOS_EXECUTARAM:
                                obj.put(tg.getIndicador().getValueField() + d.getCodigoProfessor(), d.getCompareceram());
                                break;
                            case IND_AGENDAMENTOS_FALTARAM:
                                obj.put(tg.getIndicador().getValueField() + d.getCodigoProfessor(), d.getFaltaram());
                                break;
                            case IND_AGENDAMENTOS_CANCELARAM:
                                obj.put(tg.getIndicador().getValueField() + d.getCodigoProfessor(), d.getCancelaram());
                                break;
                            case IND_RENOVADOS:
                                obj.put(tg.getIndicador().getValueField() + d.getCodigoProfessor(), d.getTotalRenovacoesCarteira());
                                break;
                            case IND_NAO_RENOVADOS:
                                obj.put(tg.getIndicador().getValueField() + d.getCodigoProfessor(), d.getTotalNaoRenovaramCarteira());
                                break;
                            case IND_A_VENCER:
                                obj.put(tg.getIndicador().getValueField() + d.getCodigoProfessor(), d.getTotalAlunosAvencer());
                                break;
                            case IND_NOVOS_CARTEIRA:
                                obj.put(tg.getIndicador().getValueField() + d.getCodigoProfessor(), d.getTotalNovosCarteiraNovos());
                                break;
                            case IND_TROCARAM_CARTEIRA:
                                obj.put(tg.getIndicador().getValueField() + d.getCodigoProfessor(), d.getTotalTrocaramCarteira());
                                break;
                            case IND_PERCENTUAL_RENOVACAO:
                                obj.put(tg.getIndicador().getValueField() + d.getCodigoProfessor(), d.getPercentualRenovacoes());
                                break;
                            case IND_TEMPO_MEDIO_CARTEIRA:
                                obj.put(tg.getIndicador().getValueField() + d.getCodigoProfessor(), d.getTempoMedioPermanenciaCarteira());
                                break;
                            case IND_SEM_TREINO:
                                obj.put(tg.getIndicador().getValueField() + d.getCodigoProfessor(), d.getTotalAlunosSemTreino());
                                break;
                            case IND_PERC_TREINO_EM_DIA:
                                obj.put(tg.getIndicador().getValueField() + d.getCodigoProfessor(), d.getPercentualEmDia());
                                break;
                            case IND_PERC_TREINO_VENCIDOS:
                                obj.put(tg.getIndicador().getValueField() + d.getCodigoProfessor(), ((d.getTotalTreinosEmdia()) + d.getTotalTreinosVencidos()) == 0
                                        ? 0
                                        : (d.getTotalTreinosVencidos() * 100) / (d.getTotalTreinosEmdia() + d.getTotalTreinosVencidos()));
                                break;
                            case IND_TEMPO_MEDIO_PROGRAMA:
                                obj.put(tg.getIndicador().getValueField() + d.getCodigoProfessor(), d.getTempoMedioPermanenciaTreino());
                                break;
                            case IND_NR_AVALIACOES:
                                obj.put(tg.getIndicador().getValueField() + d.getCodigoProfessor(), d.getNrAvaliacoesTreino());
                                break;
                            case IND_MEDIA_AVALIACOES:
                                obj.put(tg.getIndicador().getValueField() + d.getCodigoProfessor(), d.getMediaValorAvaliacao());
                                break;
                            case IND_ESTRELA_1:
                                obj.put(tg.getIndicador().getValueField() + d.getCodigoProfessor(), d.getNr1estrelas());
                                break;
                            case IND_ESTRELA_2:
                                obj.put(tg.getIndicador().getValueField() + d.getCodigoProfessor(), d.getNr2estrelas());
                                break;
                            case IND_ESTRELA_3:
                                obj.put(tg.getIndicador().getValueField() + d.getCodigoProfessor(), d.getNr3estrelas());
                                break;
                            case IND_ESTRELA_4:
                                obj.put(tg.getIndicador().getValueField() + d.getCodigoProfessor(), d.getNr4estrelas());
                                break;
                            case IND_ESTRELA_5:
                                obj.put(tg.getIndicador().getValueField() + d.getCodigoProfessor(), d.getNr5estrelas());
                                break;
                            case IND_COM_AVALIACAO:
                                obj.put(tg.getIndicador().getValueField() + d.getCodigoProfessor(), d.getTotalAlunosAvaliacoes());
                                break;
                            case IND_SEM_AVALIACAO:
                                obj.put(tg.getIndicador().getValueField() + d.getCodigoProfessor(), d.getTotalAlunosSemAvaliacoes());
                                break;
                            case IND_AGENDAMENTO_PROFESSORES:
                                obj.put(tg.getIndicador().getValueField() + d.getCodigoProfessor(), d.getProfessores());
                                break;
                            case IND_HORAS_DISPONIBILIDADE:
                                obj.put(tg.getIndicador().getValueField() + d.getCodigoProfessor(), d.getHorasDisponibilidade());
                                break;
                            case IND_HORAS_EXECUTADAS:
                                obj.put(tg.getIndicador().getValueField() + d.getCodigoProfessor(), d.getHorasAtendimento());
                                break;
                            case IND_PERC_OCUPACAO:
                                obj.put(tg.getIndicador().getValueField() + d.getCodigoProfessor(), d.getOcupacao());
                                break;
                            case IND_AGENDAMENTO_NOVOS_TREINOS:
                                obj.put(tg.getIndicador().getValueField() + d.getCodigoProfessor(), d.getNovosTreinos());
                                break;
                            case IND_AGENDAMENTO_TREINO_RENOVADOS:
                                obj.put(tg.getIndicador().getValueField() + d.getCodigoProfessor(), d.getTreinosRenovados());
                                break;
                            case IND_AGENDAMENTO_TREINO_REVISADOS:
                                obj.put(tg.getIndicador().getValueField() + d.getCodigoProfessor(), d.getTreinosRevisados());
                                break;
                            case IND_AGENDAMENTO_AVALIACOES_FISICAS:
                                obj.put(tg.getIndicador().getValueField() + d.getCodigoProfessor(), d.getAvaliacoesFisicas());
                                break;
                        }

                    }
                }
            }
            lista.add(obj);
        }
        Collections.reverse(lista);
        for (JSONObject o : lista) {
            array.put(o);
        }
        return array.toString();
    }

    public static String montarItem(ItemGraficoTO itemG, String codProfessor, String nomeProfessor, boolean clustered, boolean putTheNameOnIt, String cor) {
        StringBuilder item = new StringBuilder();
        item.append(",{");
        item.append("\"id\":\"").append(itemG.getIndicador().getValueField()).append(codProfessor).append("\",\n");
        item.append("\"valueAxis\":\"").append(itemG.getIndicador().getValueAxis()).append("\",\n");
        item.append("\"lineColor\":\"").append(itemG.getIndicador().getCor()).append("\",\n");
        item.append("\"type\":\"").append(itemG.getTipo().getTipo()).append("\",\n");
        item.append("\"title\":\"").append(itemG.getIndicador().getTitle()).append("\",\n");
        if (putTheNameOnIt && !UteisValidacao.emptyString(nomeProfessor)) {
            item.append("\"labelText\":\"").append(nomeProfessor).append("\",\n");
        }
        item.append("\"valueField\":\"").append(itemG.getIndicador().getValueField()).append(codProfessor).append("\",\n");
        if (itemG.getTipo().getTipo().equals("column")) {
            if(cor != null && !cor.isEmpty()){
                item.append("\"fillColor\":\"").append(cor).append("\",\n");
            }else{
                item.append("\"fillColor\":\"").append(itemG.getIndicador().getCor()).append("\",\n");
            }
            item.append("\"columnWidth\":").append(itemG.getIndicador().getLargura()).append(",\n");
            item.append("\"fillAlphas\":1,\n");
            item.append("\"clustered\":").append(clustered ? "true," : "false,");
        } else {
            item.append("\"bullet\":\"").append(itemG.getIndicador().getRound()).append("\",\n");
            if(cor != null && !cor.isEmpty()){
                item.append("\"bulletColor\":\"").append(cor).append("\",\n");
            }
            item.append("\"lineThickness\":1,\n");
            item.append("\"showBalloon\":true,\n");
            item.append("\"animationPlayed\":true,\n");
        }
        item.append("\"legendValueText\":\"[[value]]\",");
        if(nomeProfessor == null || nomeProfessor.isEmpty()){
            item.append("\"balloonText\":\"[[title]]<br/><b>[[value]]</b>\"");
        }else{
            item.append("\"balloonText\":\"[[title]]<br/>").append(nomeProfessor).append("<br/><b>[[value]]</b>\"");
        }
        
        item.append("}\n");
        return item.toString();
    }
}
