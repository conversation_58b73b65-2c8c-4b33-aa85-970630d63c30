/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.util.bean;

import br.com.pacto.bean.cliente.ClienteObservacao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.ColecaoUtils;
import br.com.pacto.util.impl.Ordenacao;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import org.apache.commons.collections.Predicate;

/**
 *
 * <AUTHOR>
 */
public class ObservacoesFlagsTO implements Serializable{
    private ClienteObservacao obsAluno = new ClienteObservacao();
    private List<ClienteObservacao> todosObsAluno = new ArrayList<ClienteObservacao>();
    private List<ClienteObservacao> todosObsAvaliacao = new ArrayList<ClienteObservacao>();
    private ClienteObservacao obsAvaliacao = new ClienteObservacao();
    private String obsAlunoString = "";
    private String obsAvaliacaoString = "";
    private Boolean editarObsAluno = false;
    private Boolean editarObsAvaliacao = false;
    private Boolean listaObsAluno = false;
    private Boolean listaObsAvaliacao = false;
    private Boolean importanteObs = false;
    private Boolean importanteAvaliacao = false;
    public ClienteObservacao getObsAluno() {
        return obsAluno;
    }

    public void setObsAluno(ClienteObservacao obsAluno) {
        this.obsAluno = obsAluno;
    }

    public ClienteObservacao getObsAvaliacao() {
        return obsAvaliacao;
    }

    public void setObsAvaliacao(ClienteObservacao obsAvaliacao) {
        this.obsAvaliacao = obsAvaliacao;
    }

    public String getObsAlunoString() {
        if(obsAlunoString == null){
            obsAlunoString = "";
        }
        return obsAlunoString;
    }

    public void setObsAlunoString(String obsAlunoString) {
        this.obsAlunoString = obsAlunoString;
    }

    public String getObsAvaliacaoString() {
        if(obsAvaliacaoString == null){
            obsAvaliacaoString = "";
        }
        return obsAvaliacaoString;
    }

    public void setObsAvaliacaoString(String obsAvaliacaoString) {
        this.obsAvaliacaoString = obsAvaliacaoString;
    }

    public Boolean getEditarObsAluno() {
        return editarObsAluno;
    }

    public void setEditarObsAluno(Boolean editarObsAluno) {
        this.editarObsAluno = editarObsAluno;
    }

    public Boolean getEditarObsAvaliacao() {
        return editarObsAvaliacao;
    }

    public void setEditarObsAvaliacao(Boolean editarObsAvaliacao) {
        this.editarObsAvaliacao = editarObsAvaliacao;
    }

    public Boolean getListaObsAluno() {
        return listaObsAluno;
    }

    public void setListaObsAluno(Boolean listaObsAluno) {
        this.listaObsAluno = listaObsAluno;
    }

    public Boolean getListaObsAvaliacao() {
        return listaObsAvaliacao;
    }

    public void setListaObsAvaliacao(Boolean listaObsAvaliacao) {
        this.listaObsAvaliacao = listaObsAvaliacao;
    }
    
    public boolean getAlterouObsAluno(){
        return (obsAluno == null && obsAlunoString != null && !obsAlunoString.isEmpty()
                ||(obsAlunoString == null && obsAluno.getObservacao() != null)
                ||(obsAlunoString != null && obsAluno.getObservacao() == null)
                ||(!obsAlunoString.equals(obsAluno.getObservacao())));
    }
    

    public List<ClienteObservacao> getTodosObsAluno() {
        return todosObsAluno;
    }

    public void setTodosObsAluno(List<ClienteObservacao> todosObsAluno) {
        this.todosObsAluno = todosObsAluno;
    }

    public List<ClienteObservacao> getTodosObsAvaliacao() {
        return todosObsAvaliacao;
    }

    public void setTodosObsAvaliacao(List<ClienteObservacao> todosObsAvaliacao) {
        this.todosObsAvaliacao = todosObsAvaliacao;
    }
    public String obterUltimaObservacao(){
        Date data = null;
        String retorno = "";
        for(ClienteObservacao observacao : getTodosObsAluno()){
            if(data == null || Calendario.maior(observacao.getDataObservacao(),data)){
                data = observacao.getDataObservacao();
                retorno = observacao.getObservacao();
                importanteObs = observacao.getImportante();
                obsAluno = observacao;
            }
        }
        return retorno;
    }
    public String obterUltimaObsAvaliacao(){
        Date data = null;
        String retorno = "";
        for(ClienteObservacao observacao : getTodosObsAvaliacao()){
            if(data == null || Calendario.maior(observacao.getDataObservacao(),data)){
                data = observacao.getDataObservacao();
                retorno = observacao.getObservacao();
                importanteAvaliacao = observacao.getImportante();
                obsAvaliacao = observacao;
            }
        }
        return retorno;
    }

    public void retirarObservacaoAlunoDaLista(final ClienteObservacao obj){
        ClienteObservacao cli = (ClienteObservacao) ColecaoUtils.find(getTodosObsAluno(), new Predicate() {
            @Override
            public boolean evaluate(Object o) {
                return ((ClienteObservacao)o).getCodigo().equals(obj.getCodigo());
            }
        });

        getTodosObsAluno().remove(cli);
    }

    public void retirarObservacaoDaLista(final ClienteObservacao obj){
        ClienteObservacao cli = (ClienteObservacao) ColecaoUtils.find(getTodosObsAvaliacao(), new Predicate() {
            @Override
            public boolean evaluate(Object o) {
                return ((ClienteObservacao)o).getCodigo().equals(obj.getCodigo());
            }
        });

        getTodosObsAvaliacao().remove(cli);
    }
    
    public Boolean getImportanteObs() {
        if(importanteObs == null){
            importanteObs = false;
        }
        return importanteObs;
    }

    public void setImportanteObs(Boolean importanteObs) {
        this.importanteObs = importanteObs;
    }

    public Boolean getImportanteAvaliacao() {
        if(importanteAvaliacao == null){
            importanteAvaliacao = false;
        }
        return importanteAvaliacao;
    }

    public void setImportanteAvaliacao(Boolean importanteAvaliacao) {
        this.importanteAvaliacao = importanteAvaliacao;
    }
}
