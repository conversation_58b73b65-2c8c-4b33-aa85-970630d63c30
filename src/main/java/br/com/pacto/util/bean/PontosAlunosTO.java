/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.util.bean;

import br.com.pacto.controller.json.base.SuperJSON;
import java.io.Serializable;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class PontosAlunosTO extends SuperJSON implements Serializable{
    
    private Integer codigoCliente = 0;
    private String nomeAluno = "";
    private Integer totalPontos = 0;
    private Integer pontos = 0;
    private String descricao = "";
    private Date dataConfirmacao;
    private Integer codigoHistorico = 0;
    private String nomeBrinde = "";
    private Date dataAula;
    private String operacao = "";
    private String dataConfirmacaoString = "";
    private String dataAulaString = "";

    public PontosAlunosTO(Integer codigoCliente, String nomeAluno, Integer totalPontos) {
        this.codigoCliente = codigoCliente;
        this.nomeAluno = nomeAluno;
        this.totalPontos = totalPontos;
    }

    public PontosAlunosTO() {
    }

    public Integer getCodigoCliente() {
        return codigoCliente;
    }

    public void setCodigoCliente(Integer codigoCliente) {
        this.codigoCliente = codigoCliente;
    }

    public String getNomeAluno() {
        return nomeAluno;
    }

    public void setNomeAluno(String nomeAluno) {
        this.nomeAluno = nomeAluno;
    }

    public Integer getTotalPontos() {
        if (totalPontos == null) {
            totalPontos = 0;
        }
        return totalPontos;
    }

    public void setTotalPontos(Integer totalPontos) {
        this.totalPontos = totalPontos;
    }

    public Integer getPontos() {
        if (pontos == null) {
            pontos = 0;
        }
        return pontos;
    }

    public void setPontos(Integer pontos) {
        this.pontos = pontos;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Date getDataConfirmacao() {
        return dataConfirmacao;
    }

    public void setDataConfirmacao(Date dataConfirmacao) {
        this.dataConfirmacao = dataConfirmacao;
    }

    public Integer getCodigoHistorico() {
        return codigoHistorico;
    }

    public void setCodigoHistorico(Integer codigoHistorico) {
        this.codigoHistorico = codigoHistorico;
    }

    public String getNomeBrinde() {
        return nomeBrinde;
    }

    public void setNomeBrinde(String nomeBrinde) {
        this.nomeBrinde = nomeBrinde;
    }

    public Date getDataAula() {
        return dataAula;
    }

    public void setDataAula(Date dataAula) {
        this.dataAula = dataAula;
    }

    public String getOperacao() {
        return operacao;
    }

    public void setOperacao(String operacao) {
        this.operacao = operacao;
    }
    
    public String getDescricao_apresentar(){
        if (!nomeBrinde.equals("-")) {
            return nomeBrinde;
        } else {
            return descricao;
        }
    }

    public String getDataConfirmacaoString() {
        if (dataConfirmacaoString == null) {
            dataConfirmacaoString = "";
        }
        return dataConfirmacaoString;
    }

    public void setDataConfirmacaoString(String dataConfirmacaoString) {
        this.dataConfirmacaoString = dataConfirmacaoString;
    }

    public String getDataAulaString() {
        if (dataAulaString == null) {
            dataAulaString = "";
        }
        return dataAulaString;
    }

    public void setDataAulaString(String dataAulaString) {
        this.dataAulaString = dataAulaString;
    }
    
}
