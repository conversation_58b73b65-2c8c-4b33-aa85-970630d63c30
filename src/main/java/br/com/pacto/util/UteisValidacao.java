package br.com.pacto.util;

import br.com.pacto.objeto.Calendario;
import java.lang.reflect.InvocationTargetException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Collection;
import java.util.Date;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.net.InetAddress;
import java.util.Random;
import org.apache.commons.beanutils.BeanUtils;

/**
 * Esta classe visa centralizar as l�gicas de valida��o do sistema.
 * 
 * <AUTHOR>
 * 
 */
public class UteisValidacao {

    /**
     * Valida E-mail
     *
     * @param email
     * @return verdadeiro ou falso
     * <AUTHOR>
     */
    public static boolean validaEmail(String email) {
        Pattern p = Pattern.compile("^[\\w-]+(\\.[\\w-]+)*@([\\w-]+\\.)+[a-zA-Z]{2,7}$");
        Matcher m = p.matcher(email.trim());
        if (m.find()) {
            return true;
        } else {
            return false;
        }
    }

    public static boolean notEmptyNumber(Number number) {
        return !emptyNumber(number);
    }

    /**
     * Criado para validar se um horario � maior ou igual a outro
     *
     * @param hrInicial
     * @param hrFinal
     * @return<i>true</i> caso os dados sejam v�lidos, <i>false</i> caso n�o
     *                    sejam v�lidos
     * <AUTHOR>
     */
    public static boolean comparaHorario(Date hrInicial, Date hrFinal) {
        if (hrInicial.before(hrFinal)) {
            return true;
        }
        if (hrInicial.equals(hrFinal)) {
            return true;
        }
        return false;
    }

    /**
     * Criado para validar um telefone no formato (XX)XXXX-XXXX, onde os
     * par�nteses e tra�os podem ser substituidos por espa�os
     *
     * @param telefone
     *            entrada a ser validada
     * @return <i>true</i> caso os dados sejam v�lidos, <i>false</i> caso n�o
     *         sejam v�lidos
     * <AUTHOR>
     */
    public static boolean validaTelefone(String telefone) {
        String expression = "^\\(?(\\d{2})\\)?[- ]?(\\d{4})[- ]?(\\d{4})$";
        CharSequence inputStr = telefone;
        Pattern pattern = Pattern.compile(expression);
        Matcher matcher = pattern.matcher(inputStr);
        if (matcher.matches()) {
            return true;
        }
        return false;
    }

    /**
     * Valida o CPF de acordo com as regras da RF
     *
     * @param cpf
     *            - String
     * @return <i>true</i> se o dado for v�lido, <i>false</i> se n�o
     */
    public static boolean validaCPF(String cpf) {
        int[] pesoCPF = {11, 10, 9, 8, 7, 6, 5, 4, 3, 2};
        if ((cpf == null) || (cpf.length() != 11)) {
            return false;
        }
        Integer digito1 = calcularDigito(cpf.substring(0, 9), pesoCPF);
        Integer digito2 = calcularDigito(cpf.substring(0, 9) + digito1, pesoCPF);
        return cpf.equals(cpf.substring(0, 9) + digito1.toString()
                + digito2.toString());
    }

    /**
     * M�todo inteno utilizado para verifica��o de CPF e CNPJ
     *
     * @param str
     *            - String
     * @param peso
     *            - int[]
     * @return digito verificador
     */
    private static int calcularDigito(String str, int[] peso) {
        int soma = 0;
        for (int indice = str.length() - 1, digito; indice >= 0; indice--) {
            digito = Integer.parseInt(str.substring(indice, indice + 1));
            soma += digito * peso[peso.length - str.length() + indice];
        }
        soma = 11 - soma % 11;
        return soma > 9 ? 0 : soma;
    }

    /**
     * Valida um CNPJ de acordo com as regras da RF
     *
     * @param cnpj
     *            - String
     * @return <i>true</i> se o dado for v�lido, <i>false</i> se n�o
     */
    public static boolean validaCNPJ(String cnpj) {
        int[] pesoCNPJ = {6, 5, 4, 3, 2, 9, 8, 7, 6, 5, 4, 3, 2};
        // remove pontos e barras
        cnpj = cnpj.replaceAll("\\.", "");
        cnpj = cnpj.replaceAll("-", "");
        cnpj = cnpj.replaceAll("/", "");
        // prossegue normalmente a valida��o
        if ((cnpj == null) || (cnpj.length() != 14)) {
            return false;
        }
        Integer digito1 = calcularDigito(cnpj.substring(0, 12), pesoCNPJ);
        Integer digito2 = calcularDigito(cnpj.substring(0, 12) + digito1,
                pesoCNPJ);
        return cnpj.equals(cnpj.substring(0, 12) + digito1.toString()
                + digito2.toString());
    }

    /**
     * Testa se um n�mero � nulo ou igual a zero
     *
     * @param n
     *            java.lang.Number
     * @return <i>true</i>se for nulo ou igual a zero, <i>false</i> se n�o
     */
    public static boolean emptyNumber(Number n) {
        if (n == null) {
            return true;
        }
        if (n.equals(0)) {
            return true;
        }
        if (n.equals(0.0)) {
            return true;
        }
        return false;
    }

    /**
     * Testa se uma string � nula ou vazia
     *
     * @param s
     * @return <i>true</i>se for nulo ou vazio, <i>false</i> se n�o
     */
    public static boolean emptyString(String s) {
        if (s == null) {
            return true;
        }
        if (s.equals("")) {
            return true;
        }
        return false;
    }

    public static String caseEmptyString(String s, String defaultValue) {
        if (s == null) {
            return defaultValue;
        }
        if (s.equals("")) {
            return defaultValue;
        }
        return s;
    }

    /**
     * Verifica se dentre um conjunto de objetos todos est�o v�lidos (neste caso
     * n�o nulos)
     *
     * @param objs
     * @return <i>true</i> se todos os objetos forem v�lidos, <i>false</i> se
     *         n�o
     * <AUTHOR>
     */
    public static boolean nenhumNulo(Object... objs) {
        boolean validade = true;
        for (Object obj : objs) {
            if (obj == null) {
                validade = false;
            }
        }
        return validade;
    }

    /**
     * Compara datas
     *
     * @param data
     *            Date
     * @return <i>true</i> se a data for menor que a atual, <i>false</i> se n�o
     */
    public static boolean dataMenorDataAtual(Date data) {
        if (data == null) {
            return false;
        }

        if (data.before(Calendario.hoje())) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * Compara datas ignorando a hora
     *
     * @param data
     *            Date
     * @return <i>true</i> se a data for menor que a atual, <i>false</i> se n�o
     */
    public static boolean dataMenorDataAtualSemHora(Date data) {
        try {
            if (data == null) {
                return false;
            }

            SimpleDateFormat format = new SimpleDateFormat("dd/MM/yyyy");
            Date dataAtualFormatada = format.parse(format.format(Calendario.hoje()));

            if (data.before(dataAtualFormatada)) {
                return true;
            } else {
                return false;
            }
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * Verifica se algum dos objetos passados como par�metro � igual ao primeiro
     * Impede o ocorrimento de NPE
     *
     * @param valorInicial
     *            - objeto a ser comparado com os demais
     * @param objects
     *            - os demais
     * @return <i>true</i> caso o primeiro objeto seja igual a um dos outros,
     *         <i>false</i> se n�o
     */
    public static boolean valorIn(Object valorInicial, Object... objects) {
        if (valorInicial == null || objects == null) {
            return false;
        }
        for (Object object : objects) {
            if (valorInicial.equals(object)) {
                return true;
            }
        }
        return false;
    }

    /*
     * author: Ulisses Data: 01/06/11 Retorno: True: Se o conte�do da string
     * tiver somente n�meros. False: Se o conte�do da string tiver algum
     * caracter diferente de n�mero.
     */
    public static boolean somenteNumeros(String str) {
        String numeros = str.replaceAll("[^0-9]", "");
        return (str.length() == numeros.length());
    }

    public static boolean somenteNumeroZeroPositivo(String valor) {
        if (!valor.matches("[0-9]+")) {
            return false;
        }
        return true;
    }

    public static boolean somenteNumerosEPontos(String str) {
        str = str.replaceAll("\\.", "");
        String numeros = str.replaceAll("[^0-9]", "");
        return (str.length() == numeros.length());
    }

    /**
     * Verifica se a propriedade possui valor nulo
     *
     * @param objeto
     *            - Object
     * @param propriedade
     *            - EL do caminho da propriedade
     * @return <i>true</i> caso n�o consiga obter a propriedade, ela esteja
     *         vazia ou nula, <i>false</i> se a propriedade possuir valor
     */
    public static boolean valorNulo(Object objeto, String propriedade) {
        try {
            String property = BeanUtils.getProperty(objeto, propriedade);
            return property == null || property.equals("");
        } catch (IllegalAccessException e) {
            return true;
        } catch (IllegalArgumentException e) {
            return true;
        } catch (InvocationTargetException e) {
            return true;
        } catch (NoSuchMethodException e) {
            return true;
        }
    }

    /**
     * M�todo criado para validar dia e m�s informados, sendo que o dia e m�s
     * informados s�o sempre separadamente v�lidos vindos de um comboBox com
     * n�meros sempre v�lidos.
     *
     * @param dia
     * @param mes
     * @return
     * @throws Exception
     */
    public static boolean validaDiaMesCalendar(int dia, int mes)
            throws Exception {
        Calendar cal = Calendario.getInstance();
        cal.setTime(Calendario.hoje());
        cal.set(Calendar.DAY_OF_MONTH, dia);
        cal.set(Calendar.MONTH, mes);

        Date data = cal.getTime();
        cal.setTime(data);
        // verifica se o Calendar n�o acrescentou ou retirou dias
        // se o usu�rio informar valores errados o dia ser� diferente
        if (cal.get(Calendar.DAY_OF_MONTH) != dia) {
            return false;
        }
        return true;
    }

    public static String removerZeros(String string) {
        String r = string.replaceAll("0", "");
        int i = r.indexOf(".");
        if (i > 0) {
            String s1 = r.substring(0, i);
            String s2 = r.substring(i + 1, r.length());
            String[] s = s2.split("\\.");
            String s3 = ".";
            for (String cdg : s) {
                Integer integer = new Integer(cdg);
                int d2 = integer;
                String d3 = String.valueOf(d2);
                s3 += d3 + ".";
            }
            String string2 = s1 + s3;
            String substring = string2.substring(0, string2.length() - 1);
            return substring;
        } else {
            return r;
        }
    }

    public static String rearanjar(int[] codigos, String codigoPlano) {
        int niveis = codigoPlano.split("\\.").length;
        String retorno = null;
        if (niveis == 1) {
            Integer codigo = codigos[0];
            retorno = String.valueOf(codigo++);
            codigos[0] = codigo;
        } else {
            int codigo = codigos[niveis - 1];
            String codigoNovo = "";
            for (int k = 0; k < niveis - 1; k++) {
                codigoNovo += codigos[k] - 1 + ".";
            }
            retorno = codigoNovo + codigo;
            codigos[niveis - 1] = ++codigo;
        }
        codigos[niveis] = 0;
        return retorno;
    }

    public static int gerarNumeroRandomico(int nrInicial, int nrFinal) {
        Random random = new Random();
        int sorteio = random.nextInt(nrFinal - nrInicial); //Tamanho do intervalo
        return sorteio + nrInicial;
    }

    public static void validarHoraMinutos(String hora, String mensagem) throws Exception {
        int minutos = Integer.parseInt(hora.substring(3, 5));
        int horas = Integer.parseInt(hora.substring(0, 2));
        if (minutos > 60) {
            throw new Exception(mensagem);
        }
        if (horas > 23) {
            throw new Exception(mensagem);
        }
    }

    /**
     * Respons�vel por validar o formato dos c�digos agrupadores de centro de custos e plano de custos  
     * <AUTHOR>
     * 25/11/2011
     */
    public static void validaCodigoFinanceiro(String codigo) throws Exception {
        if (!somenteNumerosEPontos(codigo)) {
            throw new Exception("" + codigo + " está incorreto. Informe apenas números e pontos.");
        }
        if (codigo.startsWith(".") || codigo.endsWith(".")) {
            throw new Exception("" + codigo + " está incorreto. Informe pontos apenas entre os números.");
        }
        if (codigo.contains(".")) {
            String[] codigos = codigo.split("[.]");
            for (String cod : codigos) {
                if (cod.length() != 3) {
                    throw new Exception("" + codigo + " está incorreto. Cada agrupador do código deve ter 3 dígitos.");
                }
            }
        } else {
            if (codigo.length() != 3) {
                throw new Exception("" + codigo + " está incorreto. Cada agrupador do código deve ter 3 dígitos.");
            }
        }
    }

    /**
     * Testa se uma collection � nula ou vazia
     *
     * @param l
     *            java.lang.Number
     * @return <i>true</i>se for nulo ou igual a zero, <i>false</i> se n�o
     */
    public static boolean emptyList(Collection l) {
        if (l == null) {
            return true;
        }
        if (l.isEmpty()) {
            return true;
        }
        return false;
    }
    
    public static boolean emptyArray(Object[] arr) {
        if (arr == null) {
            return true;
        }
        if (arr.length == 0) {
            return true;
        }
        return false;
    }

    public static boolean emptyStringOrZero(String s) {
        if (s == null) {
            return true;
        }else if (s.equals("00:00")){
            return true;
        }
        return s.equals("");
    }
    
}
