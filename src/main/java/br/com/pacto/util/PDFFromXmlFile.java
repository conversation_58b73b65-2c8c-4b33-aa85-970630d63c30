package br.com.pacto.util;

import br.com.pacto.base.util.ExecuteRequestHttpService;
import br.com.pacto.controller.json.graduacao.ImpressaoAvaliacaoProgressoAlunoDTO;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.impl.avaliacao.ImpressaoAvaliacaoDTO;
import br.com.pacto.util.bean.ItemRelatorioTO;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import net.sf.jasperreports.engine.util.JRLoader;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;
import org.json.JSONObject;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.IOException;
import java.text.DateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class PDFFromXmlFile {

    public String gerarPDF(String key, HttpServletRequest request,
                           String nomeDesignIReport,
                           Map<String, Object> params, String nomeRelatotio, boolean enviarPorEmail, boolean externo, String language) throws Exception{
        JasperPrint print = gerarRelatorioJasperPrintObjeto(request, nomeDesignIReport, params, language);
        return visualizarRelatorioPDF(key, request, print, nomeRelatotio, enviarPorEmail, externo);
    }

    private static CloseableHttpClient buildHttpClient() {
        return ExecuteRequestHttpService.createConnectorCloseable();
    }

    public String sendToMs(String url, String endpoint, ImpressaoAvaliacaoDTO impressaoAvaliacaoDTO, String token) throws Exception{
        CloseableHttpClient httpClient = buildHttpClient();
        HttpPost httpPost = new HttpPost(url + endpoint);

        Uteis.logarDebug("[PDFFromXmlFile.sendToMs] URL: " + url + endpoint);
        Uteis.logarDebug("[PDFFromXmlFile.sendToMs] Token enviado: " + (token != null ? token.substring(0, Math.min(20, token.length())) + "..." : "null"));

        httpPost.addHeader("Authorization", token);
        httpPost.addHeader("Content-Type", "application/json");
        StringEntity requestEntity = new StringEntity(new JSONObject(impressaoAvaliacaoDTO).toString(), ContentType.APPLICATION_JSON);
        httpPost.setEntity(requestEntity);

        CloseableHttpResponse response = httpClient.execute(httpPost);
        int statusCode = response.getStatusLine().getStatusCode();
        String string = EntityUtils.toString(response.getEntity());

        Uteis.logarDebug("[PDFFromXmlFile.sendToMs] Status Code: " + statusCode);
        Uteis.logarDebug("[PDFFromXmlFile.sendToMs] Response: " + string);

        JSONObject retorno = new JSONObject(string);
        httpClient.close();
        return url + "/temp/" + retorno.getString("content");
    }

    public String sendToMsAvaliacaoProgressoGraduacao(String url, String endpoint, ImpressaoAvaliacaoProgressoAlunoDTO dadosImprimirDTO, String token) throws Exception{
        CloseableHttpClient httpClient = buildHttpClient();
        HttpPost httpPost = new HttpPost(url + endpoint);
        httpPost.addHeader("Authorization", token);
        StringEntity requestEntity = new StringEntity(new JSONObject(dadosImprimirDTO).toString(), ContentType.APPLICATION_JSON);
        httpPost.setEntity(requestEntity);
        CloseableHttpResponse response = httpClient.execute(httpPost);
        String string = EntityUtils.toString(response.getEntity());
        JSONObject retorno = new JSONObject(string);
        httpClient.close();
        return url + "/temp/" + retorno.getString("content");
    }


    public JasperPrint gerarRelatorioJasperPrintObjeto(HttpServletRequest request,
                                                       String nomeDesignIReport,
                                                       Map<String, Object> params, String language) throws Exception {

        JRDataSource jr = new JREmptyDataSource();
        String nomeJasperReportDesignIReport = nomeDesignIReport.substring(0, nomeDesignIReport.lastIndexOf(".")) +
                (!language.equalsIgnoreCase("pt") ? "_"+language.toLowerCase()+".jasper" :".jasper");
        File arquivoIReport = new File(obterCaminhoBaseAplicacao(request) + File.separator + nomeJasperReportDesignIReport);

        JasperReport jasperReport = (JasperReport) JRLoader.loadObject(arquivoIReport);

        JasperPrint print = JasperFillManager.fillReport(jasperReport, params, jr);

        return print;
    }

    protected String visualizarRelatorioPDF(final String key,
                                            HttpServletRequest request,
                                            JasperPrint print,
                                            String nomeRelatorio,
                                            boolean enviarPorEmail, boolean externo) throws ServletException, IOException, Exception {
        String nomePDF = nomeRelatorio
                + "-" + key
                + "-" + String.valueOf(Calendario.hoje().getTime())
                + ".pdf";
        String nomeRelPDF = "resources" + File.separator + "img" + File.separator + "documents";
        File pdfFolder = new File(obterCaminhoWebAplicacao(request) + File.separator + nomeRelPDF);
        pdfFolder.mkdirs();
        nomeRelPDF = nomeRelPDF + File.separator + nomePDF;
        File pdfFile = new File(obterCaminhoWebAplicacao(request) + File.separator + nomeRelPDF);
        if (pdfFile.exists()) {
            try {
                pdfFile.delete();
            } catch (Exception e) {
                DateFormat formatador = DateFormat.getDateInstance(DateFormat.MEDIUM);
                String dataStr = formatador.format(Calendario.hoje());
                nomePDF = nomePDF + dataStr + ".pdf";
                nomeRelPDF = "relatorios" + File.separator + nomePDF;
                pdfFile = new File(obterCaminhoWebAplicacao(request) + File.separator + nomeRelPDF);
            }

        }

        JasperExportManager.exportReportToPdfFile(print,
                obterCaminhoWebAplicacao(request) + File.separator + nomeRelPDF);
        Uteis.logarDebug("[PDFFromXmlFile.visualizarRelatorioPDF] PDF exportado: " + pdfFile.getAbsolutePath());
        String urlAplicacao = request.getRequestURI();
        urlAplicacao = urlAplicacao.substring(0, urlAplicacao.lastIndexOf("/"));
        String retorno;
        if(enviarPorEmail){
            retorno = obterCaminhoWebAplicacao(request) + File.separator + nomeRelPDF;
        }else if(externo){
            retorno = nomeRelPDF.replaceAll("\\\\", "/");
        } else{
            retorno = urlAplicacao +"/" + nomeRelPDF.replaceAll("\\\\", "/");
        }
        return retorno;
    }


    public static void main(String[] args) throws JRException, IOException {
        // Compile jrxml file.
        JasperReport jasperReport = JasperCompileManager
                .compileReport("C:/PactoJ/Sistemas/treino-tronco/src/main/resources/br/com/pacto/relatorio/avaliacao/avaliacao_fisica.jrxml");

        // Parameters for report
        Map<String, Object> parameters = new HashMap<String, Object>();
        parameters.put("logoPadraoRelatorio", "http://www.guiafeurosa.com.br/files/guia/121/academia-corpory-center-em-feu-rosa-serra-es.png");
        parameters.put("empresaNome", "Academia Teste");
        parameters.put("empresaEndereco", "Rua C-200, nº 246 - Jd. América - Goiânia - GO");
        parameters.put("empresaSite", "www.academia.com.br");

        parameters.put("fotoAluno", "https://dt39m1atv5spm.cloudfront.net/sete/97jxGKuba0*PGeIPqAkibQ==/QUMpkDketRAdKVGtu*T7pA==.jpg");
        parameters.put("nomeAluno", "João Alcides Resende Guimarães");
        parameters.put("idade", "28 anos");
        parameters.put("avaliador", "Maria José Cleide");
        parameters.put("dataAvaliacao", "25/01/2018");
        parameters.put("sexo", "Masculino");
        parameters.put("contato", "(62) 999781234");
        parameters.put("proximaAvaliacao", "25/02/2018");

        parameters.put("circunferencia", "108cm");
        parameters.put("circunferenciaResultado", "Risco aumentado substancialmente");
        parameters.put("imc", "30,31");
        parameters.put("altura", "1,87m");
        parameters.put("peso", "106kg");
        parameters.put("imcResultado", "Obesidade Grau I");
        parameters.put("composicaoResultado", "Ruim");
        parameters.put("percResiduos", "15%");
        parameters.put("percGordura", "26%");
        parameters.put("percMusculos", "28%");
        parameters.put("percOssos", "31%");
        parameters.put("objetivosAluno", "Condicionamento Físico, Definição e Diminuir % Gordura.");
        parameters.put("recomendacoes", "Exercícios aeróbicos, concentrados no abdomên. Dieta.");
        parameters.put("peso1", "30/10 - 106,0kg");
        parameters.put("peso2", "30/09 - 110,7kg");
        parameters.put("peso3", "30/08 - 135,2kg");
        parameters.put("peso4", " ");
        parameters.put("usuario", "Pactobr");
        parameters.put("horaEmissao", "16/01/2018 09:52");
        parameters.put("alturaAtual", "1,86");
        parameters.put("pesoAtual", "106");
        parameters.put("freqCardiaca", "120");
        parameters.put("pressaoArterial", "12/8");


        List<ItemRelatorioTO> anamnese = new ArrayList<ItemRelatorioTO>();
        anamnese.add(new ItemRelatorioTO("1 - Alguma vez seu médico disse que você possui algum problema cardíaco e recomendou\n" +
                "que você só praticasse atividade física sob prescrição médica?", "Sim\n" +
                "Observação: Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diam nonummy nibh\n" +
                "euismod tincidunt ut laoreet dolore magna aliquam erat volutpat. Ut wisi enim ad minim\n" +
                "veniam, quis."));
        anamnese.add(new ItemRelatorioTO("2 - Você sente dor no tórax quando pratica uma atividade física?", "Não"));
        anamnese.add(new ItemRelatorioTO("3 - No último mês você sentiu dor torácica quando não estava praticando atividade física?", "Não"));
        anamnese.add(new ItemRelatorioTO("4 - Você perdeu o equilíbrio em virtude de tonturas ou perdeu a consciência quando\n" +
                "estava praticando atividade física?", "Sim"));
        anamnese.add(new ItemRelatorioTO("5 - Você tem algum problema ósseo ou articular que poderia ser agravado com a prática\n" +
                "de atividades físicas?", "Sim"));
        anamnese.add(new ItemRelatorioTO("6 - Seu médico já recomendou o uso de medicamentos para controle da sua pressão arterial\n" +
                "ou condição cardiovascular?", "Não"));
        anamnese.add(new ItemRelatorioTO("7 - Você tem conhecimento de alguma outra razão física que o impeça de participar de\n" +
                "atividades físicas?", "Sim"));

        parameters.put("anamneseJR", new JRBeanCollectionDataSource(anamnese));


        List<ItemRelatorioTO> parq = new ArrayList<ItemRelatorioTO>();
        parq.add(new ItemRelatorioTO("1 - ParqAlguma vez seu médico disse que você possui algum problema cardíaco e recomendou\n" +
                "que você só praticasse atividade física sob prescrição médica?", "Sim"));
        parq.add(new ItemRelatorioTO("2 - ParqVocê sente dor no tórax quando pratica uma atividade física?", "Não"));
        parq.add(new ItemRelatorioTO("3 - ParqNo último mês você sentiu dor torácica quando não estava praticando atividade física?", "Não"));
        parq.add(new ItemRelatorioTO("4 - ParqVocê perdeu o equilíbrio em virtude de tonturas ou perdeu a consciência quando\n" +
                "estava praticando atividade física?", "Sim"));
        parq.add(new ItemRelatorioTO("5 - ParqVocê tem algum problema ósseo ou articular que poderia ser agravado com a prática\n" +
                "de atividades físicas?", "Sim"));
        parq.add(new ItemRelatorioTO("6 - ParqSeu médico já recomendou o uso de medicamentos para controle da sua pressão arterial\n" +
                "ou condição cardiovascular?", "Não"));
        parq.add(new ItemRelatorioTO("7 - ParqVocê tem conhecimento de alguma outra razão física que o impeça de participar de\n" +
                "atividades físicas?", "Sim"));

        parameters.put("parqJR", new JRBeanCollectionDataSource(parq));
        parameters.put("parq", "Negativo");
        parameters.put("protocolo", "Polloc 3 dobras");
        parameters.put("totalDobras", "80mm");
        List<ItemRelatorioTO> dobras = new ArrayList<ItemRelatorioTO>();
        dobras.add(new ItemRelatorioTO("Abdominal", "20mm"));
        dobras.add(new ItemRelatorioTO("Tórax", "45mm"));
        dobras.add(new ItemRelatorioTO("Coxa", "15mm"));
        dobras.add(new ItemRelatorioTO("Total", "80mm"));

        parameters.put("dobrasJR", new JRBeanCollectionDataSource(dobras));

        List<ItemRelatorioTO> perimetria = new ArrayList<ItemRelatorioTO>();

        perimetria.add(new ItemRelatorioTO("Antebraço", "20.0", "20.0"));
        perimetria.add(new ItemRelatorioTO("Braço relaxado", "25.0", "25.0"));
        perimetria.add(new ItemRelatorioTO("Braço contraído", "29.0", "29.0"));
        perimetria.add(new ItemRelatorioTO("Coxa distal", "45.0", "45.0"));
        perimetria.add(new ItemRelatorioTO("Coxa média", "49.0", "49.0"));
        perimetria.add(new ItemRelatorioTO("Coxa proximal", "46.0", "46.0"));
        perimetria.add(new ItemRelatorioTO("Panturrilha", "39.0", "39.0"));
        perimetria.add(new ItemRelatorioTO("Pescoço", "30.0", ""));
        perimetria.add(new ItemRelatorioTO("Ombro, ", "70.0", ""));
        perimetria.add(new ItemRelatorioTO("Tórax / Busto relaxado", "90.0", ""));
        perimetria.add(new ItemRelatorioTO("Quadril", "90.0", ""));
        perimetria.add(new ItemRelatorioTO("Cintura", "102.0", ""));
        perimetria.add(new ItemRelatorioTO("Circunferência abdominal", "108.0", ""));
        perimetria.add(new ItemRelatorioTO("Glúteo", "90.0", ""));

        parameters.put("perimetriaJR", new JRBeanCollectionDataSource(perimetria));
        parameters.put("diametroJoelho", "20.0");
        parameters.put("diametroPunho", "12.0");

        parameters.put("pesoGordura", "25,97");
        parameters.put("pesoResidual", "33,09");
        parameters.put("pesoOsseo", "24,50");
        parameters.put("pesoMuscular", "22,50");

        parameters.put("classificacaoFlexibilidade", "Boa");
        parameters.put("alcanceMaximo", "35cm");
        parameters.put("obsFlexibilidade", "Aluno com boa fexibilidade já fez aulas de Yoga.");

        parameters.put("obsPostural", "Aluno possui boa postura, no entanto foi encontrado algumas limitações.");
        parameters.put("assimetriaQuadril", "nenhuma elevação");
        parameters.put("ombrosAssimetricos", "nenhuma elevação");
        parameters.put("anterior", "Nada a declarar.");
        parameters.put("posterior", "Depressão escapular e Escoliose torácica.");
        parameters.put("visaoLateral", "Anterversão de quadril, Joelho flexo e Pé cavo.");


        parameters.put("rmlBraco", "10 - Fraco (Média recomendada: 15)");
        parameters.put("rmlAbdomen", "25 - Acima da Média (Média recomendada: 23)");
        parameters.put("vo2", "27,00");
        parameters.put("limiar1", "15,00");
        parameters.put("limiar2", "15,00");
        parameters.put("testeCampo", "\bTeste:\b Caminhada ou corrida de 12 minutos");
        parameters.put("valor1TesteCampo", "\bDistância percorrida:\b 1960m - Muito Fraca");
        parameters.put("valor2TesteCampo", "\bVO2 Máx (ml/kg/min):\b 1.56902");
        parameters.put("valor3TesteCampo", "");


        JRDataSource dataSource = new JREmptyDataSource();
        JasperPrint jasperPrint = JasperFillManager.fillReport(jasperReport,
                parameters, dataSource);

        // Make sure the output directory exists.
        File outDir = new File("C:/jasperoutput");
        outDir.mkdirs();

        // Export to PDF.
        JasperExportManager.exportReportToPdfFile(jasperPrint,
                "C:/jasperoutput/StyledTextReport.pdf");

        System.out.println("Done!");
    }

    public String obterCaminhoBaseAplicacao(HttpServletRequest request) throws Exception {
        return new File(request.getSession().getServletContext().getRealPath("WEB-INF" + File.separator + "classes")).getAbsolutePath();
    }

    public String obterCaminhoWebAplicacao(HttpServletRequest request) throws Exception {
        return new File(request.getSession().getServletContext().getRealPath("")).getAbsolutePath();
    }
}
