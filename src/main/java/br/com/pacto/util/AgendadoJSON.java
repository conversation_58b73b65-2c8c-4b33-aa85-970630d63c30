/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.util;

import br.com.pacto.controller.json.base.SuperJSON;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AgendadoJSON extends SuperJSON {

    private Integer codigoPessoa;
    private Integer codigoCliente;
    private Integer codigoContrato;
    private String id_agendamento;
    private String urlFoto;
    private String inicio;
    private String fim;
    private String nome;
    private String matricula;
    private String telefones;
    private Integer saldoCreditoTreino = 0;
    private Boolean usaSaldo = false;
    private Boolean usaTurma = false;
    private Boolean confirmado = false;
    private String situacaoContrato;
    private String situacao;
    private Integer qtdReposicoesFuturas = 0;
    private Boolean contratoFuturo;
    private List<Integer> modalidadesContratoFuturo = new ArrayList<Integer>();
    private Integer codContratoFuturo = 0;
    private Integer qtdCreditoFuturo = 0;
    private boolean gymPass = false;
    private boolean totalPass = false;
    private boolean diaria = false;
    private boolean fixo = false;
    private boolean experimental = false;
    private boolean presencaReposicao = false;
    private boolean desmarcado = false;
    private String fotokey;
    private boolean desafio = false;

    private int spiviSeat;
    private int spiviEventID;
    private int spiviClientID;
    private String dataNascimento;
    private String email;
    private String sexo;
    private String cidade;

    private Long horarioMarcacao;
    private Integer codigoPassivo;
    private Integer codigoIndicado;
    private String equipamentoReservado;
    private String userIdSelfloops;
    private Integer averagePower;
    private Integer calories;
    private Integer tempoDeAula;
    private Integer posicaoRankingAluno;

    private boolean espera = false;

    private Boolean autorizadoGestaoRede = false;
    private String codAcessoAutorizado = "";
    private Integer matriculaAutorizado = 0;


    public AgendadoJSON() {
    }

    public AgendadoJSON(Integer codigoPessoa, Integer codigoCliente, String nome, String matricula,
                        Integer codigoContrato, boolean usaSaldo, Integer saldo, Integer qtdReposicoesFuturas) {
        this.codigoPessoa = codigoPessoa;
        this.codigoCliente = codigoCliente;
        this.nome = nome;
        this.matricula = matricula;
        this.codigoContrato = codigoContrato;
        this.saldoCreditoTreino = saldo;
        this.usaSaldo = usaSaldo;
        this.qtdReposicoesFuturas = qtdReposicoesFuturas;
    }

    public Long getHorarioMarcacao() {
        return horarioMarcacao;
    }

    public void setHorarioMarcacao(Long horarioMarcacao) {
        this.horarioMarcacao = horarioMarcacao;
    }

    public String getUrlFoto() {
        return urlFoto;
    }

    public void setUrlFoto(String urlFoto) {
        this.urlFoto = urlFoto;
    }

    public Integer getCodigoPessoa() {
        return codigoPessoa;
    }

    public void setCodigoPessoa(Integer codigoPessoa) {
        this.codigoPessoa = codigoPessoa;
    }

    public Integer getCodigoCliente() {
        return codigoCliente;
    }

    public void setCodigoCliente(Integer codigoCliente) {
        this.codigoCliente = codigoCliente;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public String getId_agendamento() {
        return id_agendamento;
    }

    public void setId_agendamento(String id_agendamento) {
        this.id_agendamento = id_agendamento;
    }

    public String getInicio() {
        return inicio;
    }

    public void setInicio(String inicio) {
        this.inicio = inicio;
    }

    public String getFim() {
        return fim;
    }

    public void setFim(String fim) {
        this.fim = fim;
    }

    public Integer getCodigoContrato() {
        return codigoContrato;
    }

    public void setCodigoContrato(Integer codigoContrato) {
        this.codigoContrato = codigoContrato;
    }

    public String getTelefones() {
        return telefones;
    }

    public void setTelefones(String telefones) {
        this.telefones = telefones;
    }

    public Integer getSaldoCreditoTreino() {
        return saldoCreditoTreino;
    }

    public void setSaldoCreditoTreino(Integer saldoCreditoTreino) {
        this.saldoCreditoTreino = saldoCreditoTreino;
    }

    public Boolean getUsaSaldo() {
        return usaSaldo;
    }

    public void setUsaSaldo(Boolean usaSaldo) {
        this.usaSaldo = usaSaldo;
    }

    public Boolean getUsaTurma() {
        return usaTurma;
    }

    public void setUsaTurma(Boolean usaTurma) {
        this.usaTurma = usaTurma;
    }

    public Boolean getConfirmado() {
        return confirmado;
    }

    public void setConfirmado(Boolean confirmado) {
        this.confirmado = confirmado;
    }

    public String getSituacaoContrato() {
        return situacaoContrato;
    }

    public void setSituacaoContrato(String situacaoContrato) {
        this.situacaoContrato = situacaoContrato;
    }


    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public Integer getQtdReposicoesFuturas() {
        return qtdReposicoesFuturas;
    }

    public void setQtdReposicoesFuturas(Integer qtdReposicoesFuturas) {
        this.qtdReposicoesFuturas = qtdReposicoesFuturas;
    }

    public Boolean getContratoFuturo() {
        return contratoFuturo;
    }

    public void setContratoFuturo(Boolean contratoFuturo) {
        this.contratoFuturo = contratoFuturo;
    }

    public List<Integer> getModalidadesContratoFuturo() {
        return modalidadesContratoFuturo;
    }

    public void setModalidadesContratoFuturo(List<Integer> modalidadesContratoFuturo) {
        this.modalidadesContratoFuturo = modalidadesContratoFuturo;
    }

    public Integer getCodContratoFuturo() {
        return codContratoFuturo;
    }

    public void setCodContratoFuturo(Integer codContratoFuturo) {
        this.codContratoFuturo = codContratoFuturo;
    }

    public Integer getQtdCreditoFuturo() {
        return qtdCreditoFuturo;
    }

    public void setQtdCreditoFuturo(Integer qtdCreditoFuturo) {
        this.qtdCreditoFuturo = qtdCreditoFuturo;
    }

    public boolean isGymPass() {
        return gymPass;
    }

    public void setGymPass(boolean gymPass) {
        this.gymPass = gymPass;
    }

    public boolean isTotalPass() {
        return totalPass;
    }

    public void setTotalPass(boolean totalPass) {
        this.totalPass = totalPass;
    }

    public boolean isDiaria() {
        return diaria;
    }

    public void setDiaria(boolean diaria) {
        this.diaria = diaria;
    }

    public boolean isExperimental() {
        return experimental;
    }

    public void setExperimental(boolean experimental) {
        this.experimental = experimental;
    }

    public boolean isPresencaReposicao() {
        return presencaReposicao;
    }

    public void setPresencaReposicao(boolean presencaReposicao) {
        this.presencaReposicao = presencaReposicao;
    }

    public boolean isDesmarcado() {
        return desmarcado;
    }

    public void setDesmarcado(boolean desmarcado) {
        this.desmarcado = desmarcado;
    }

    public int getSpiviSeat() {
        return spiviSeat;
    }

    public void setSpiviSeat(final int spiviSeat) {
        this.spiviSeat = spiviSeat;
    }

    public int getSpiviEventID() {
        return spiviEventID;
    }

    public void setSpiviEventID(final int spiviEventID) {
        this.spiviEventID = spiviEventID;
    }

    public int getSpiviClientID() {
        return spiviClientID;
    }

    public void setSpiviClientID(final int spiviClientID) {
        this.spiviClientID = spiviClientID;
    }

    public boolean isDesafio() {
        return desafio;
    }

    public void setDesafio(boolean desafio) {
        this.desafio = desafio;
    }

    public String getFotokey() {
        return fotokey;
    }

    public void setFotokey(String fotokey) {
        this.fotokey = fotokey;
    }

    public String getDataNascimento() {
        return dataNascimento;
    }

    public void setDataNascimento(String dataNascimento) {
        this.dataNascimento = dataNascimento;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getSexo() {
        return sexo;
    }

    public void setSexo(String sexo) {
        this.sexo = sexo;
    }

    public String getCidade() {
        return cidade;
    }

    public void setCidade(String cidade) {
        this.cidade = cidade;
    }

    public boolean isFixo() {
        return fixo;
    }

    public void setFixo(boolean fixo) {
        this.fixo = fixo;
    }

    public Integer getCodigoPassivo() {
        return codigoPassivo;
    }

    public void setCodigoPassivo(Integer codigoPassivo) {
        this.codigoPassivo = codigoPassivo;
    }

    public Integer getCodigoIndicado() {
        return codigoIndicado;
    }

    public void setCodigoIndicado(Integer codigoIndicado) {
        this.codigoIndicado = codigoIndicado;
    }

    public boolean isEspera() {
        return espera;
    }

    public void setEspera(boolean espera) {
        this.espera = espera;
    }

    public String getEquipamentoReservado() {
        return equipamentoReservado;
    }

    public void setEquipamentoReservado(String equipamentoReservado) {
        this.equipamentoReservado = equipamentoReservado;
    }

    public String getUserIdSelfloops() {
        return userIdSelfloops;
    }

    public void setUserIdSelfloops(String userIdSelfloops) {
        this.userIdSelfloops = userIdSelfloops;
    }

    public Integer getAveragePower() {
        return averagePower;
    }

    public void setAveragePower(Integer averagePower) {
        this.averagePower = averagePower;
    }

    public Integer getCalories() {
        return calories;
    }

    public void setCalories(Integer calories) {
        this.calories = calories;
    }

    public Integer getTempoDeAula() {
        return tempoDeAula;
    }

    public void setTempoDeAula(Integer tempoDeAula) {
        this.tempoDeAula = tempoDeAula;
    }

    public Integer getPosicaoRankingAluno() {
        return posicaoRankingAluno;
    }

    public void setPosicaoRankingAluno(Integer posicaoRankingAluno) {
        this.posicaoRankingAluno = posicaoRankingAluno;
    }

    public Boolean getAutorizadoGestaoRede() {
        if (autorizadoGestaoRede == null) {
            autorizadoGestaoRede = false;
        }
        return autorizadoGestaoRede;
    }

    public void setAutorizadoGestaoRede(Boolean autorizadoGestaoRede) {
        this.autorizadoGestaoRede = autorizadoGestaoRede;
    }

    public String getCodAcessoAutorizado() {
        return codAcessoAutorizado;
    }

    public void setCodAcessoAutorizado(String codAcessoAutorizado) {
        this.codAcessoAutorizado = codAcessoAutorizado;
    }

    public Integer getMatriculaAutorizado() {
        return matriculaAutorizado;
    }

    public void setMatriculaAutorizado(Integer matriculaAutorizado) {
        this.matriculaAutorizado = matriculaAutorizado;
    }
}
