package br.com.pacto.util;

import br.com.pacto.service.exception.ValidacaoException;
import java.io.IOException;
import javax.faces.application.FacesMessage.Severity;
import javax.servlet.http.Cookie;

public interface ViewUtils {

    public void mensInfo(String id, String message, Severity severity);

    public void mensInfo(String message);

    public void mensErro(String message);

    public Object recuperarParametro(String obj);

    public Object recuperarObjeto(String param);

    public Object recuperarSessao(String obj);

    public Cookie recuperarCookie(final String name);

    public void addCookie(Cookie cookie);

    public void removeCookie(final String name);

    public String getMensagem(String mensagemID);

    public String getMensagem(String key, Object... params);

    public String getMensagem(final ValidacaoException ve);

    public String getLabel(String mensagemID);

    public String getLabelInternacionalizada(String mensagemID, String language);

    public void redirect(final String page) throws IOException;

    public String getCurrentViewID();

    public String getCurrentPrettyURL();
}
