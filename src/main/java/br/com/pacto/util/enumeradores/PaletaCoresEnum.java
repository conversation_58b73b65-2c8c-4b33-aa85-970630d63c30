/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.util.enumeradores;

import io.swagger.annotations.ApiModel;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Random;

/**
 *
 * <AUTHOR>
 */
@ApiModel(description = "Enum que representa as paletas de cores disponíveis. " +
        "Valores possíveis:\n\n" +
        "Azul:\n" +
        "  0 = AZUL_A (Azul A, #60C2EC, border: #444882)\n" +
        "  1 = AZUL_B (Azul B, #A5E7E7, border: #444882)\n" +
        "  2 = AZUL_C (Azul C, #35B5EC, border: #444882)\n" +
        "  3 = AZUL_D (Azul D, #5CE7E7, border: #444882)\n" +
        "  4 = AZUL_E (Azul E, #009BDC, border: #444882)\n" +
        "  5 = AZUL_F (Azul F, #008888, border: #444882)\n" +
        "  6 = AZUL_G (Azul G, #2C7C9F, border: #444882)\n" +
        "  7 = AZUL_H (Azul H, #1E5A5A, border: #444882)\n" +
        "  8 = AZUL_I (Azul I, #005E86, border: #444882)\n" +
        "  9 = AZUL_J (Azul J, #004848, border: #444882)\n\n" +
        "Azul Escuro:\n" +
        "  10 = AZUL_ESCURO_A (Azul Escuro A, #60C2EC, border: #444882)\n" +
        "  11 = AZUL_ESCURO_B (Azul Escuro B, #A5E7E7, border: #444882)\n" +
        "  12 = AZUL_ESCURO_C (Azul Escuro C, #35B5EC, border: #444882)\n" +
        "  13 = AZUL_ESCURO_D (Azul Escuro D, #5CE7E7, border: #444882)\n" +
        "  14 = AZUL_ESCURO_E (Azul Escuro E, #009BDC, border: #444882)\n\n" +
        "Roxo:\n" +
        "  15 = ROXO_A (Roxo A, #D4A8EC, border: #532253)\n" +
        "  16 = ROXO_B (Roxo B, #BB63EC, border: #532253)\n" +
        "  17 = ROXO_C (Roxo C, #7109AA, border: #532253)\n" +
        "  18 = ROXO_D (Roxo D, #9C299C, border: #532253)\n" +
        "  19 = ROXO_E (Roxo E, #9C179C, border: #532253)\n" +
        "  20 = ROXO_F (Roxo F, #740074, border: #532253)\n" +
        "  21 = ROXO_G (Roxo G, #5A2974, border: #532253)\n" +
        "  22 = ROXO_H (Roxo H, #5C135C, border: #532253)\n" +
        "  23 = ROXO_I (Roxo I, #520052, border: #532253)\n" +
        "  24 = ROXO_J (Roxo J, #3E025F, border: #532253)\n\n" +
        "Vermelho:\n" +
        "  25 = VERMELHO_A (Vermelho A, #FD6775, border: #850E1A)\n" +
        "  26 = VERMELHO_B (Vermelho B, #FD4C5D, border: #850E1A)\n" +
        "  27 = VERMELHO_C (Vermelho C, #D0404E, border: #850E1A)\n" +
        "  28 = VERMELHO_D (Vermelho D, #DD8E99, border: #850E1A)\n" +
        "  29 = VERMELHO_E (Vermelho E, #DD6677, border: #850E1A)\n" +
        "  30 = VERMELHO_F (Vermelho F, #843741, border: #850E1A)\n" +
        "  31 = VERMELHO_G (Vermelho G, #FC293B, border: #850E1A)\n" +
        "  32 = VERMELHO_H (Vermelho H, #AC293B, border: #850E1A)\n" +
        "  33 = VERMELHO_I (Vermelho I, #BE1424, border: #850E1A)\n" +
        "  34 = VERMELHO_J (Vermelho J, #740F1D, border: #850E1A)\n\n" +
        "Laranja:\n" +
        "  35 = LARANJA_A (Laranja A, #FED788, border: #773F11)\n" +
        "  36 = LARANJA_B (Laranja B, #FEC34C, border: #773F11)\n" +
        "  37 = LARANJA_C (Laranja C, #FDA902, border: #773F11)\n" +
        "  38 = LARANJA_D (Laranja D, #C2912E, border: #773F11)\n" +
        "  39 = LARANJA_E (Laranja E, #AB7201, border: #773F11)\n" +
        "  40 = LARANJA_F (Laranja F, #EF9D5A, border: #773F11)\n" +
        "  41 = LARANJA_G (Laranja G, #EF8732, border: #773F11)\n" +
        "  42 = LARANJA_H (Laranja H, #E36700, border: #773F11)\n" +
        "  43 = LARANJA_I (Laranja I, #BA6721, border: #773F11)\n" +
        "  44 = LARANJA_J (Laranja J, #AA4D00, border: #773F11)\n\n" +
        "Marrom:\n" +
        "  45 = MARROM_A (Marrom A, #DDB77C, border: #291A0B)\n" +
        "  46 = MARROM_B (Marrom B, #DDA245, border: #291A0B)\n" +
        "  47 = MARROM_C (Marrom C, #A36400, border: #291A0B)\n" +
        "  48 = MARROM_D (Marrom D, #8B5D15, border: #291A0B)\n" +
        "  49 = MARROM_E (Marrom E, #814F00, border: #291A0B)\n" +
        "  50 = MARROM_F (Marrom F, #A87845, border: #291A0B)\n" +
        "  51 = MARROM_G (Marrom G, #A86926, border: #291A0B)\n" +
        "  52 = MARROM_H (Marrom H, #603100, border: #291A0B)\n" +
        "  53 = MARROM_I (Marrom I, #452D13, border: #291A0B)\n" +
        "  54 = MARROM_J (Marrom J, #3B1E00, border: #291A0B)\n\n" +
        "Verde:\n" +
        "  55 = VERDE_A (Verde A, #4CFF8E, border: #608021)\n" +
        "  56 = VERDE_B (Verde B, #58E28C, border: #608021)\n" +
        "  57 = VERDE_C (Verde C, #21C05A, border: #608021)\n" +
        "  58 = VERDE_D (Verde D, #339B5A, border: #608021)\n" +
        "  59 = VERDE_E (Verde E, #0F8C3D, border: #608021)\n" +
        "  60 = VERDE_F (Verde F, #82C79C, border: #608021)\n" +
        "  61 = VERDE_G (Verde G, #50A56C, border: #608021)\n" +
        "  62 = VERDE_H (Verde H, #1F7740, border: #608021)\n" +
        "  63 = VERDE_I (Verde I, #2E6843, border: #608021)\n" +
        "  64 = VERDE_J (Verde J, #0B5025, border: #608021)\n\n" +
        "Verde Limao:\n" +
        "  65 = VERDE_LIMAO_A (Verde Limao A, #D5F29B, border: #608021)\n" +
        "  66 = VERDE_LIMAO_B (Verde Limao B, #BEF256, border: #608021)\n" +
        "  67 = VERDE_LIMAO_C (Verde Limao C, #8DD400, border: #608021)\n" +
        "  68 = VERDE_LIMAO_D (Verde Limao D, #7BA723, border: #608021)\n" +
        "  69 = VERDE_LIMAO_E (Verde Limao E, #639500, border: #608021)\n\n" +
        "Amarelo:\n" +
        "  70 = AMARELO_A (Amarelo A, #FEE790, border: #937600)\n" +
        "  71 = AMARELO_B (Amarelo B, #FEDB52, border: #937600)\n" +
        "  72 = AMARELO_C (Amarelo C, #FBC904, border: #937600)\n" +
        "  73 = AMARELO_D (Amarelo D, #D5B123, border: #937600)\n" +
        "  74 = AMARELO_E (Amarelo E, #C69E02, border: #937600)"
)
public enum PaletaCoresEnum {
    // Azul

    AZUL_A(0, "Azul A", "#60C2EC", "#444882"),
    AZUL_B(1, "Azul B", "#A5E7E7", "#444882"),
    AZUL_C(2, "Azul C", "#35B5EC", "#444882"),
    AZUL_D(3, "Azul D", "#5CE7E7", "#444882"),
    AZUL_E(4, "Azul E", "#009BDC", "#444882"),//
    AZUL_F(5, "Azul F", "#008888", "#444882"),//
    AZUL_G(6, "Azul G", "#2C7C9F", "#444882"),
    AZUL_H(7, "Azul H", "#1E5A5A", "#444882"),
    AZUL_I(8, "Azul I", "#005E86", "#444882"),
    AZUL_J(9, "Azul J", "#004848", "#444882"),
    //Azul Escuro
    AZUL_ESCURO_A(10, "Azul Escuro A", "#60C2EC", "#444882"),
    AZUL_ESCURO_B(11, "Azul Escuro B", "#A5E7E7", "#444882"),
    AZUL_ESCURO_C(12, "Azul Escuro C", "#35B5EC", "#444882"),//
    AZUL_ESCURO_D(13, "Azul Escuro D", "#5CE7E7", "#444882"),
    AZUL_ESCURO_E(14, "Azul Escuro E", "#009BDC", "#444882"),
    // Roxo
    ROXO_A(15, "Roxo A", "#D4A8EC","#532253"),
    ROXO_B(16, "Roxo B", "#BB63EC","#532253"),
    ROXO_C(17, "Roxo C", "#7109AA","#532253"),//
    ROXO_D(18, "Roxo D", "#9C299C","#532253"),
    ROXO_E(19, "Roxo E", "#9C179C","#532253"),
    ROXO_F(20, "Roxo F", "#740074","#532253"),//
    ROXO_G(21, "Roxo G", "#5A2974","#532253"),
    ROXO_H(22, "Roxo H", "#5C135C","#532253"),
    ROXO_I(23, "Roxo I", "#520052","#532253"),
    ROXO_J(24, "Roxo J", "#3E025F","#532253"),
    // Vermelho
    VERMELHO_A(25, "Vermelho A", "#FD6775","#850E1A"),
    VERMELHO_B(26, "Vermelho B", "#FD4C5D","#850E1A"),
    VERMELHO_C(27, "Vermelho C", "#D0404E","#850E1A"),
    VERMELHO_D(28, "Vermelho D", "#DD8E99","#850E1A"),
    VERMELHO_E(29, "Vermelho E", "#DD6677","#850E1A"),
    VERMELHO_F(30, "Vermelho F", "#843741","#850E1A"),
    VERMELHO_G(31, "Vermelho G", "#FC293B","#850E1A"),//
    VERMELHO_H(32, "Vermelho H", "#AC293B","#850E1A"),//
    VERMELHO_I(33, "Vermelho I", "#BE1424","#850E1A"),
    VERMELHO_J(34, "Vermelho J", "#740F1D","#850E1A"),
    // Laranja
    LARANJA_A(35, "Laranja A", "#FED788","#773F11"),
    LARANJA_B(36, "Laranja B", "#FEC34C","#773F11"),
    LARANJA_C(37, "Laranja C", "#FDA902","#773F11"),//
    LARANJA_D(38, "Laranja D", "#C2912E","#773F11"),
    LARANJA_E(39, "Laranja E", "#AB7201","#773F11"),
    LARANJA_F(40, "Laranja F", "#EF9D5A","#773F11"),
    LARANJA_G(41, "Laranja G", "#EF8732","#773F11"),
    LARANJA_H(42, "Laranja H", "#E36700","#773F11"),//
    LARANJA_I(43, "Laranja I", "#BA6721","#773F11"),
    LARANJA_J(44, "Laranja J", "#AA4D00","#773F11"),
    // Marrom
    MARROM_A(45, "Marrom A", "#DDB77C", "#291A0B"),
    MARROM_B(46, "Marrom B", "#DDA245", "#291A0B"),
    MARROM_C(47, "Marrom C", "#A36400", "#291A0B"),//
    MARROM_D(48, "Marrom D", "#8B5D15", "#291A0B"),
    MARROM_E(49, "Marrom E", "#814F00", "#291A0B"),
    MARROM_F(50, "Marrom F", "#A87845", "#291A0B"),
    MARROM_G(51, "Marrom G", "#A86926", "#291A0B"),
    MARROM_H(52, "Marrom H", "#603100", "#291A0B"),//
    MARROM_I(53, "Marrom I", "#452D13", "#291A0B"),
    MARROM_J(54, "Marrom J", "#3B1E00", "#291A0B"),
    // Verde
    VERDE_A(55, "Verde A", "#4CFF8E", "#"),
    VERDE_B(56, "Verde B", "#58E28C", "#608021"),
    VERDE_C(57, "Verde C", "#21C05A", "#608021"),//
    VERDE_D(58, "Verde D", "#339B5A", "#608021"),
    VERDE_E(59, "Verde E", "#0F8C3D", "#608021"),
    VERDE_F(60, "Verde F", "#82C79C", "#608021"),
    VERDE_G(61, "Verde G", "#50A56C", "#608021"),
    VERDE_H(62, "Verde H", "#1F7740", "#608021"),//
    VERDE_I(63, "Verde I", "#2E6843", "#608021"),
    VERDE_J(64, "Verde J", "#0B5025", "#608021"),
    // Verde Limao
    VERDE_LIMAO_A(65, "Verde Limao A", "#D5F29B","#608021"),
    VERDE_LIMAO_B(66, "Verde Limao B", "#BEF256","#608021"),
    VERDE_LIMAO_C(67, "Verde Limao C", "#8DD400","#608021"),//
    VERDE_LIMAO_D(68, "Verde Limao D", "#7BA723","#608021"),
    VERDE_LIMAO_E(69, "Verde Limao E", "#639500","#608021"),
    // Amarelo
    AMARELO_A(70, "Amarelo A", "#FEE790","#937600"),
    AMARELO_B(71, "Amarelo B", "#FEDB52","#937600"),
    AMARELO_C(72, "Amarelo C", "#FBC904","#937600"),//
    AMARELO_D(73, "Amarelo D", "#D5B123","#937600"),
    AMARELO_E(74, "Amarelo E", "#C69E02","#937600");
    private Integer id;
    private String descricao;
    private String cor;
    private String borderColor;
    
    private static final List<PaletaCoresEnum> VALUES = Collections.unmodifiableList(Arrays.asList(values()));
    private static final int SIZE = VALUES.size();
    private static final Random RANDOM = new Random();

    private PaletaCoresEnum(Integer id, String descricao, String cor, String borderColor) {
        this.id = id;
        this.descricao = descricao;
        this.cor = cor;
        this.borderColor = borderColor;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getCor() {
        return cor;
    }

    public void setCor(String cor) {
        this.cor = cor;
    }

    public String getTextoCor() {
        String corConverter = cor;
        String corDoTexto = "";

        corConverter = corConverter.replace("#", "");

        Integer c_r = Integer.parseInt((corConverter.substring(0, 2)), 16);
        Integer c_g = Integer.parseInt((corConverter.substring(2, 4)), 16);
        Integer c_b = Integer.parseInt((corConverter.substring(4, 6)), 16);

        Integer brilho = ((c_r * 299) + (c_g * 587) + (c_b * 114)) / 1000;

        if (brilho > 130) {
            corDoTexto = "#000000";
        } else {
            corDoTexto = "#FFFFFF";
        }

        return corDoTexto;
    }

    public String getClasseCor() {
        String corConverter = cor;
        String corDoTexto = "";

        corConverter = corConverter.replace("#", "");

        Integer c_r = Integer.parseInt((corConverter.substring(0, 2)), 16);
        Integer c_g = Integer.parseInt((corConverter.substring(2, 4)), 16);
        Integer c_b = Integer.parseInt((corConverter.substring(4, 6)), 16);

        Integer brilho = ((c_r * 299) + (c_g * 587) + (c_b * 114)) / 1000;

        if (brilho > 130) {
            corDoTexto = "textoPreto";
        } else {
            corDoTexto = "textoBranco";
        }

        return corDoTexto;
    }

    public static PaletaCoresEnum getFromId(Integer id) {
        for (PaletaCoresEnum tipo : values()) {
            if (tipo.getId().equals(id)) {
                return tipo;
            }
        }
        return null;
    }

    public static PaletaCoresEnum getFromCss(String css) {
        for (PaletaCoresEnum tipo : values()) {
            if (tipo.getCss().equals(css)) {
                return tipo;
            }
        }
        return null;
    }

    public static PaletaCoresEnum getFromHex(String hex) {
        for (PaletaCoresEnum tipo : values()) {
            if (tipo.getCor().equals(hex)) {
                return tipo;
            }
        }
        return null;
    }


    public String getCss() {
        return getDescricao() + " " + getClasseCor();
    }

    public static PaletaCoresEnum randomCor(boolean vaiVermelho) {
        PaletaCoresEnum p = null;
        while(p == null){
            PaletaCoresEnum get = VALUES.get(RANDOM.nextInt(SIZE));
            if(vaiVermelho || !get.name().startsWith("VERMELHO")){
                p = get;
            }
        }
        return p;
    }

    public String getBorderColor() {
        return borderColor;
    }

    public void setBorderColor(String borderColor) {
        this.borderColor = borderColor;
    }
    
    
}
