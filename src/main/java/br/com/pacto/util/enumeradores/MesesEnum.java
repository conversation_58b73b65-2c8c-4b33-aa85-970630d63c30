/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.util.enumeradores;

/**
 *
 * <AUTHOR>
 */
public enum MesesEnum {
    
    JANEIRO(1,31),
    FE<PERSON><PERSON><PERSON><PERSON>(2,29),
    <PERSON><PERSON><PERSON>(3,31),
    <PERSON><PERSON><PERSON>(4,30),
    <PERSON><PERSON>(5,31),
    <PERSON><PERSON><PERSON><PERSON>(6,30),
    <PERSON><PERSON><PERSON><PERSON><PERSON>(7,31),
    AGOSTO(8,31),
    SETEMBRO(9,30),
    OUTUBRO(10,31),
    NOVEMBRO(11,30),
    DEZEMBRO(12,31);
    
    private Integer numero;
    private Integer nrDias;
    
    private MesesEnum(Integer numero, Integer nrDias) {
        this.numero = numero;
        this.nrDias = nrDias;
        
    }
    
    public Integer getNumero() {
        return numero;
    }

    public void setNumero(Integer numero) {
        this.numero = numero;
    }

    public Integer getNrDias() {
        return nrDias;
    }

    public void setNrDias(Integer nrDias) {
        this.nrDias = nrDias;
    }
    
    public static MesesEnum getFromId(int i){
        for(MesesEnum m : MesesEnum.values()){
            if(m.getNumero() == i){
                return m;
            }
        }
        return null;
    }
    
}
