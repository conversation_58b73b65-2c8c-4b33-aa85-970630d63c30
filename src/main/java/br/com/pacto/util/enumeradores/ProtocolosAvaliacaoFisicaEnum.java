/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.util.enumeradores;

import br.com.pacto.notificacao.RecursoSistema;

/**
 *
 * <AUTHOR>
 */
public enum ProtocolosAvaliacaoFisicaEnum {
    POLLOCK_3_DOBRAS(new String[]{"abdominal", "coxa", "torax"}, new String[]{"triceps", "suprailiaca", "coxa"}, RecursoSistema.NAV_AvaliacaoPollock3),
    POLLOCK_7_DOBRAS(new String[]{"abdominal", "coxa", "torax", "triceps", "axilarMedia", "suprailiaca", "subescapular"}, new String[]{}, RecursoSistema.NAV_AvaliacaoPollock7),
    GUEDES(new String[]{"triceps", "suprailiaca", "abdominal"}, new String[]{"subescapular", "suprailiaca", "coxa"}, RecursoSistema.NAV_AvaliacaoGuedes),
    FAULKNER_DOBRAS(new String[]{"triceps", "subescapular", "suprailiaca", "abdominal"}, new String[]{"triceps", "subescapular", "suprailiaca", "abdominal"}, RecursoSistema.NAV_AvaliacaoFaukner4),
    BIOIMPEDANCIA(new String[]{"abdominal", "coxa", "torax", "triceps", "axilarMedia", "suprailiaca", "subescapular"}, new String[]{}, RecursoSistema.NAV_AvaliacaoBioimpedancia),
    WELTMAN_OBESO(new String[]{"peso"}, new String[]{"peso", "altura"}, RecursoSistema.NAV_AvaliacaoWeltman),
    POLLOCK_ADOLESCENTE(new String[]{"coxa", "triceps"}, new String[]{"triceps", "coxa"}, RecursoSistema.NAV_AvaliacaoPollockAdolescente),
    SLAUGHTER(new String[]{"panturrilha", "triceps"}, new String[]{"triceps", "panturrilha"}, RecursoSistema.NAV_AvaliacaoSlaughter),
    YUHASZ(new String[]{"abdominal", "coxa", "torax", "triceps", "suprailiaca", "subescapular"},
            new String[]{"abdominal", "coxa", "torax", "triceps", "suprailiaca", "subescapular"}, RecursoSistema.NAV_AvaliacaoYuhasz6),
    TG_LOHMAN(new String[]{"coxa", "triceps"}, new String[]{"coxa", "triceps"}, RecursoSistema.NAV_AvaliacaoTGLohman2)

    ;

    private String[] camposMulher;
    private String[] camposHomem;
    private RecursoSistema recursoSistema;

    private ProtocolosAvaliacaoFisicaEnum(String[] camposH, String[] camposM, RecursoSistema recurso) {
        this.camposHomem = camposH;
        this.camposMulher = camposM;
        this.recursoSistema = recurso;
    }

    public String[] getCamposMulher() {
        return camposMulher;
    }

    public void setCamposMulher(String[] camposMulher) {
        this.camposMulher = camposMulher;
    }

    public String[] getCamposHomem() {
        return camposHomem;
    }

    public void setCamposHomem(String[] camposHomem) {
        this.camposHomem = camposHomem;
    }

    public String getClasses(Boolean masculino, Integer idade){
        String classes = "";
        if(this.equals(POLLOCK_3_DOBRAS) && idade != null && idade < 18){
            return " coxa triceps";
        }
        String[] arr = this.equals(POLLOCK_7_DOBRAS) || masculino ? camposHomem : camposMulher;
        for(String s : arr){
            classes += " "+s;
        }
        return classes;
    }

    public RecursoSistema getRecursoSistema() {
        return recursoSistema;
    }
}
