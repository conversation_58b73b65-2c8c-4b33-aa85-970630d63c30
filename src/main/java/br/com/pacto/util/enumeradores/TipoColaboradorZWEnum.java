package br.com.pacto.util.enumeradores;

public enum TipoColaboradorZWEnum {

    PROFESSOR("PR", "Professor"),
    PROFESSOR_TREINO("TW", "Professor (<PERSON><PERSON><PERSON>Web)"),
    PERSONAL_TRAINER("PT", "Personal Trainer"),
    ORIENTADOR("OR", "Orientador"),
    CONSULTOR("CO", "Consultor"),
    PERSONAL_INTERNO("PI", "Personal Interno"),
    PERSONAL_EXTERNO("PE", "Personal Externo"),
    TERCEIRIZADO("TE", "Terceirizado"),
    ESTUDIO("ES", "Estúdio"),
    FORNECEDOR("FO", "Fornecedor"),
    COORDENADOR("CR", "Coordenador"),
    MEDICO("MD", "Médico"),
    FUNCIONARIO("FC", "Funcionário"),
    ADMINISTRADOR("AD", "Administrador");

    private String sigla;
    private String descricao;


    private TipoColaboradorZWEnum(String sigla, String descricao) {
        this.sigla = sigla;
        this.descricao = descricao;
    }

    /**
     * <AUTHOR>
     * 08/11/2011
     */
    public static TipoColaboradorZWEnum getTipo(String sigla) {
        TipoColaboradorZWEnum tipo = null;
        for (TipoColaboradorZWEnum tipoColaborador : TipoColaboradorZWEnum.values()) {
            if (tipoColaborador.getSigla().equals(sigla)) {
                tipo = tipoColaborador;
                break;
            }
        }
        return tipo;
    }

    public static String getTiposSQL(boolean comAspas, TipoColaboradorZWEnum... tipoColaboradorZWEnums) {
        StringBuilder sb = new StringBuilder();
        for (TipoColaboradorZWEnum tipo : tipoColaboradorZWEnums) {
            if (comAspas) {
                sb.append("'");
            }
            sb.append(tipo.getSigla());
            if (comAspas) {
                sb.append("'");
            }
            sb.append(",");
        }
        if (sb.length() > 2) {
            sb.deleteCharAt(0);
            sb.deleteCharAt(sb.length() - 1);
            sb.deleteCharAt(sb.length() - 1);
        }
        return sb.toString();
    }


    public static TipoColaboradorZWEnum obterPorSigla(String sigla){
        for(TipoColaboradorZWEnum s : values()){
            if(s.getSigla().equals(sigla)){
                return s;
            }
        }
        return null;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getSigla() {
        return sigla;
    }

    public void setSigla(String sigla) {
        this.sigla = sigla;
    }

}
