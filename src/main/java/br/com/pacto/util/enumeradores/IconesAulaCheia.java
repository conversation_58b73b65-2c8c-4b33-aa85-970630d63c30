/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.util.enumeradores;

/**
 *
 * <AUTHOR>
 */
public enum IconesAulaCheia {
    ICONE_1("basket32.png"),
    ICONE_2("bodyparts70.png"),
    ICONE_3("bodyparts71.png"),
    ICONE_4("dumbbell.png"),
    ICONE_5("dumbbell1.png"),
    ICONE_6("dumbbell14.png"),
    ICONE_7("dumbbell16.png"),
    ICONE_8("dumbbell19.png"),
    ICONE_9("dumbbells10.png"),
    ICONE_10("dumbbells7.png"),
    ICONE_11("exercise1.png"),
    ICONE_12("exercise2.png"),
    ICONE_13("exercise29.png"),
    ICONE_14("exercise35.png"),
    ICONE_15("exercise45.png"),
    ICONE_16("exercise5.png"),
    ICONE_17("exercise54.png"),
    ICONE_18("exercise55.png"),
    ICONE_19("exercise6.png"),
    ICONE_20("exercise65.png"),
    ICONE_21("exercise7.png"),
    ICONE_22("exercise8.png"),
    ICONE_23("female107.png"),
    ICONE_24("flexions.png"),
    ICONE_25("gym11.png"),
    ICONE_26("gym20.png"),
    ICONE_27("gym31.png"),
    ICONE_28("gym39.png"),
    ICONE_29("gym5.png"),
    ICONE_30("gym6.png"),
    ICONE_31("gymnast3.png"),
    ICONE_32("gymnast44.png"),
    ICONE_33("hand227.png"),
    ICONE_34("hand95.png"),
    ICONE_35("happiness4.png"),
    ICONE_36("human92.png"),
    ICONE_37("jump11.png"),
    ICONE_38("jump12.png"),
    ICONE_39("jump2.png"),
    ICONE_40("man252.png"),
    ICONE_41("man308.png"),
    ICONE_42("man309.png"),
    ICONE_43("man311.png"),
    ICONE_44("muscles1.png"),
    ICONE_45("olympic22.png"),
    ICONE_46("person316.png"),
    ICONE_47("pulling.png"),
    ICONE_48("runner9.png"),
    ICONE_49("silhouette18.png"),
    ICONE_50("slim.png"),
    ICONE_51("sport231.png"),
    ICONE_52("sport286.png"),
    ICONE_53("sport36.png"),
    ICONE_54("sport37.png"),
    ICONE_55("stairway.png"),
    ICONE_56("stick5.png"),
    ICONE_57("stickman141.png"),
    ICONE_58("upper20.png"),
    ICONE_59("walking2.png"),
    ICONE_60("weightlifter2.png"),
    ICONE_61("working6.png"),
    ICONE_62("working7.png"),
    ICONE_63("yoga230.png");

private String nome;

    private IconesAulaCheia(String nome) {
        this.nome = nome;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }




}
