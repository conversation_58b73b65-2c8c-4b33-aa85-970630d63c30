package br.com.pacto.util.impl;

import br.com.pacto.service.exception.ValidacaoException;

import java.text.MessageFormat;
import java.util.Map;

import javax.faces.application.Application;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import javax.faces.el.ValueBinding;

import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.ViewUtils;
import com.ocpsoft.pretty.PrettyContext;
import java.io.IOException;
import java.util.List;
import java.util.Locale;
import java.util.MissingResourceException;
import java.util.ResourceBundle;
import javax.faces.application.FacesMessage.Severity;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.springframework.stereotype.Service;

@Service
public class FacesUtils implements ViewUtils {

    private static final String bundleMsg = "br.com.pacto.util.resources.international.msg";
    private static final String bundleTitle = "br.com.pacto.util.resources.international.title";
    private static final String bundleTitleEN = "br.com.pacto.util.resources.international.title_en";
    private static final String bundleTitleES = "br.com.pacto.util.resources.international.title_es";

    @Override
    public void mensInfo(String id, String message, Severity severity) {
        mensagem(id, message, severity);
    }

    @Override
    public void mensInfo(final String message) {
        mensagem(message, FacesMessage.SEVERITY_INFO);
    }

    public void mensValidacao(final String message) {
        mensagem(message, FacesMessage.SEVERITY_WARN);
    }

    @Override
    public void mensErro(final String message) {
        mensagem(message, FacesMessage.SEVERITY_ERROR);
    }

    private static ClassLoader getCurrentLoader(Object fallbackClass) {
        ClassLoader loader = Thread.currentThread().getContextClassLoader();
        if (loader == null) {
            loader = fallbackClass.getClass().getClassLoader();
        }
        return loader;
    }

    @Override
    public String getMensagem(final String mensagemID) {
        return mensagemID != null ? getInternacionalizacao(mensagemID, bundleMsg) : NullPointerException.class.getSimpleName();
    }

    @Override
    public String getMensagem(String key, Object... params) {
        String text = getMensagem(key);
        if (params != null && params.length > 0 && text != key) {
            MessageFormat mf = new MessageFormat(text, new Locale("pt", "BR"));
            text = mf.format(params, new StringBuffer(), null).toString();
        }
        return text;
    }

    @Override
    public String getMensagem(final ValidacaoException ve) {
        List<String> msgs = ve.getMensagens();
        StringBuilder result = new StringBuilder();
        if (!msgs.isEmpty()) {
            for (String msg : msgs) {
                result.append("- ").append(getInternacionalizacao(msg, bundleMsg)).append("\n");
            }
        } else {
            result.append(ve.getMessage());
        }
        return result.toString();
    }

    @Override
    public String getLabel(final String mensagemID) {
        return getInternacionalizacao(mensagemID, bundleTitle);
    }

    @Override
    public String getLabelInternacionalizada(final String mensagemID, final String language) {
        if(!UteisValidacao.emptyString(language) && language != "PT"){
            if(language.equals("ES")){
                return getInternacionalizacao(mensagemID, bundleTitleES);
            }else if(language.equals("EN")){
                return getInternacionalizacao(mensagemID, bundleTitleEN);
            }
        }
        return getInternacionalizacao(mensagemID, bundleTitle);
    }

    private void mensagem(String message, Severity severity) {
        FacesContext.getCurrentInstance().addMessage(null,
                new FacesMessage(severity, getMensagem(message), null));
    }

    private void mensagem(String id, String message, Severity severity) {
        FacesContext.getCurrentInstance().addMessage(id,
                new FacesMessage(severity, getMensagem(message), null));
    }

    @Override
    public Object recuperarParametro(final String param) {
        return FacesContext.getCurrentInstance().getExternalContext()
                .getRequestParameterMap().get(param);
    }

    @Override
    public Cookie recuperarCookie(final String name) {
        if (context() != null) {
            Cookie[] cookies = ((HttpServletRequest) context().getExternalContext()
                    .getRequest()).getCookies();
            if (cookies != null) {
                for (int i = 0; i < cookies.length; i++) {
                    Cookie c = cookies[i];
                    /*Uteis.logar(null, "COOKIE: " + c.getName() + " => " + c.getValue());
                     Uteis.logar(null, "          MaxAge: " + c.getMaxAge());*/
                    if (c.getName().equals(name)) {
                        return c;
                    }
                }
            }
        }
        return null;
    }

    @Override
    public void addCookie(Cookie cookie) {
        if (context() != null) {
            ((HttpServletResponse) FacesContext.getCurrentInstance().getExternalContext()
                    .getResponse()).addCookie(cookie);
        }
    }

    @Override
    public void removeCookie(String name) {
        Cookie c = (Cookie) recuperarCookie(name);
        if (c != null) {
            c.setValue(null);
            c.setMaxAge(0);
            addCookie(c);
        }
    }

    @Override
    public Object recuperarObjeto(final String param) {
        return context().getExternalContext().getRequestMap().get(param);
    }

    protected static FacesContext context() {
        return (FacesContext.getCurrentInstance());
    }

    @Override
    public Object recuperarSessao(final String obj) {
        return FacesContext.getCurrentInstance().getExternalContext()
                .getSessionMap().get(obj);
    }

    public static Object getManagedBeanValue(final String ref) {
        StringBuilder buff = new StringBuilder("#{");
        buff.append(ref);
        buff.append("}");
        return resolveExpression(buff.toString());

    }

    public static void setManagedBeanValue(final String ref, final Object newValue) {
        StringBuilder buff = new StringBuilder("#{");
        buff.append(ref);
        buff.append("}");
        setExpressionValue(buff.toString(), newValue);
    }

    public static Object resolveExpression(final String expression) {
        FacesContext ctx = FacesContext.getCurrentInstance();
        Application app = ctx.getApplication();
        ValueBinding bind = app.createValueBinding(expression);
        return bind.getValue(ctx);
    }

    public static void setExpressionValue(final String expression, final Object newValue) {
        FacesContext ctx = FacesContext.getCurrentInstance();
        Application app = ctx.getApplication();
        ValueBinding bind = app.createValueBinding(expression);

        Class bindClass = bind.getType(ctx);
        if (bindClass.isPrimitive() || bindClass.isInstance(newValue)) {
            bind.setValue(ctx, newValue);
        }
    }

    public static Map getContextMap() {
        return (Map) getManagedBeanValue("paramBean.parametros");
    }

    private String getInternacionalizacao(final String mensagemID, final String bundleNome) {
        Locale local;
        if (context() != null) {
            local = context().getViewRoot().getLocale();
        } else {
            local = Locale.getDefault();
        }
        ResourceBundle bundle = ResourceBundle.getBundle(bundleNome, local);
        try {
            String mensagem = bundle.getString(mensagemID);
            return mensagem;
        } catch (MissingResourceException e) {
            return mensagemID;
        }
    }

    public static Object getComponent(String id) {
        return context().getViewRoot().findComponent(id);
    }

    @Override
    public void redirect(final String page) throws IOException {
        final String ctx = context().getExternalContext().getRequestContextPath();
        context().getExternalContext().redirect(String.format("%s%s", ctx, page));
    }

    @Override
    public String getCurrentViewID() {
        return ((HttpServletRequest) context().getExternalContext().getRequest()).getRequestURI();
    }

    @Override
    public String getCurrentPrettyURL() {
        return PrettyContext.getCurrentInstance().getRequestURL().toURL();
    }
}
