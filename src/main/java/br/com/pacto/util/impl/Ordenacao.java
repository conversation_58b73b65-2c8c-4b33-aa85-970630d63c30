/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.util.impl;

import br.com.pacto.objeto.Calendario;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;

public class Ordenacao implements Comparator {

    private String campo;
    private static ThreadLocal<Ordenacao> instance;

    private static ThreadLocal<Ordenacao> getInstance(final String campo) {
        if (instance == null) {
            instance = new ThreadLocal<Ordenacao>();
            instance.set(new Ordenacao());
        }
        if (instance.get() == null) {
            instance.set(new Ordenacao());
        }
        instance.get().campo = campo;
        return instance;
    }

    /**
     * Construtor privado somente para prevenir tentativas de inst�ncia direta
     * da classe
     *
     */
    private Ordenacao() {
        super();
    }

    public int compare(Object primeiro, Object segundo) {
        Object o1 = null, o2 = null;
        Method getMethodPrimeiro = null;
        Method getMethodSegundo = null;
        try {
            if (!(primeiro == null || segundo == null)) {
                if (primeiro instanceof String) {
                    return ((String) primeiro).compareToIgnoreCase((String) segundo);
                }
                getMethodPrimeiro = primeiro.getClass().getDeclaredMethod("get".concat(campo.substring(0, 1).toUpperCase()).concat(campo.substring(1)), null);
                getMethodSegundo = segundo.getClass().getDeclaredMethod("get".concat(campo.substring(0, 1).toUpperCase()).concat(campo.substring(1)), null);
                o1 = getMethodPrimeiro.invoke(primeiro, null);
                o2 = getMethodSegundo.invoke(segundo, null);

                if (o1 != null && o2 != null && o1 instanceof Integer || o1 instanceof Float || o1 instanceof Double || o1 instanceof Byte || o1 instanceof Long) {

                    Double numero = new Double(String.valueOf(o1));
                    return numero.compareTo(new Double(String.valueOf(o2)));
                }

                if (o1 != null && o2 != null && o1 instanceof Date) {
                    Calendar cal1 = Calendario.getInstance();
                    cal1.setTime((Date) o1);
                    Calendar cal2 = Calendario.getInstance();
                    cal2.setTime((Date) o2);

                    return cal1.compareTo(cal2);
                }

                if (o1 != null && o2 != null) {
                    return String.valueOf(o1).compareToIgnoreCase(String.valueOf(o2));
                }
            }
        } catch (NoSuchMethodException metodo) {
            throw new RuntimeException(metodo);
        } catch (InvocationTargetException invoke) {
            throw new RuntimeException(invoke);
        } catch (IllegalAccessException access) {
            throw new RuntimeException(access);
        }
        return -1;
    }

    /**
     * M�todo respons�vel por retorna uma lista de Object ordenada pelo nome do
     * campo informado
     *
     * @param lista
     * @param campo
     * @return
     */
    public static <T> List<T> ordenarLista(List<T> lista, final String campo) {
        if ((lista != null)
                && (!lista.isEmpty())
                && (campo != null)
                && (!(campo.trim().length() == 0))) {
            Collections.sort(lista, getInstance(campo).get());
        }
        return lista;
    }

    public static List ordenarListaReverse(final List lista, final String campo) {
        if ((lista != null)
                && (!lista.isEmpty())
                && (campo != null)
                && (!(campo.trim().length() == 0))) {
            Collections.sort(lista, getInstance(campo).get());
            Collections.reverse(lista);
        }
        return lista;
    }
}