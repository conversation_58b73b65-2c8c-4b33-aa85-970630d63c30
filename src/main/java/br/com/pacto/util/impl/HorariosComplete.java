/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.util.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class HorariosComplete {

    public static List<String> gerarHorarios(String campo, List<String> listaIgnorar) {
        if (campo == null || campo.isEmpty()) {
            return new ArrayList<String>();
        }
        if (campo.length() < 2) {
            campo = campo + "0";
        }
        
        List<String> horarios = new ArrayList<String>();
        List<String> iniciais = gerarHorasIniciais(campo);
        List<String> finais = gerarHorasFinais(campo);
        for(String inicio : iniciais){
            int intInicio = Integer.valueOf(inicio.replaceAll(":", ""));
            for(String fim : finais){
                int intFim = Integer.valueOf(fim.replaceAll(":", ""));
                if(intInicio < intFim){
                    fim = intFim > 2359 ? "00:"+fim.substring(3) : fim;
                    String add = inicio + " - "+fim;
                    if(listaIgnorar == null || !listaIgnorar.contains(add)){
                        horarios.add(add);    
                    }
                }
            }
        }
        Collections.sort(horarios);
        return horarios;
    }

    public static List<String> gerarHorasIniciais(String campo) {
        List<String> horarios = new ArrayList<String>();
        if (campo.length() >= 5) {
            horarios.add(campo.substring(0, 5));
            return horarios;
        }
        if (campo.length() < 4) {
            horarios.add(campo.substring(0, 2) + ":00");
            horarios.add(campo.substring(0, 2) + ":30");
        }
        if (campo.length() == 4) {
            horarios.add(campo + "0");
            horarios.add(campo + "5");
        }
        return horarios;
    }

    public static List<String> gerarHorasFinais(String campo) {
        List<String> horarios = new ArrayList<String>();
        if (campo.length() >= 13) {
            horarios.add(campo.substring(8));
            return horarios;
        }
        int horaInicio = Integer.valueOf(campo.substring(0, 2));
        int minutoInicio = campo.length() > 5 ? Integer.valueOf(campo.substring(3, 5)) : 0;
        if (campo.length() < 10) {
            horarios.add(campo.substring(0, 2) + ":30");
            horarios.add(campo.substring(0, 2) + ":45");
            int maxSug = horaInicio > 21 ? 24-horaInicio : 3;
            for (int i = 1; i <= maxSug; i++) {
                String horaFim = horaInicio + i > 9 ? String.valueOf(horaInicio + i) : "0" + String.valueOf(horaInicio + i);
                horarios.add(horaFim + ":00");
                horarios.add(horaFim + ":30");
                if(minutoInicio != 0 && minutoInicio != 30){
                    String minFim = minutoInicio > 9 ? String.valueOf(minutoInicio) : "0" + String.valueOf(minutoInicio);
                    horarios.add(horaFim + ":"+minFim);
                }
            }
            return horarios;
        }
        if(campo.length() == 10){
            campo = campo + ":";
        }
        if (campo.length() < 12) {
            int horaFinal = Integer.valueOf(campo.substring(8, 10));
            String horaFim = horaFinal > 9 ? String.valueOf(horaFinal) : "0" + String.valueOf(horaFinal);
            horarios.add(horaFim + ":00");
            horarios.add(horaFim + ":30");
            return horarios;
        }
        if (campo.length() == 12) {
            horarios.add(campo.substring(8, 12) + "0");
            horarios.add(campo.substring(8, 12) + "5");
            return horarios;
        }
        return horarios;
    }

    public static void main(String... ar) {
//        System.out.println(gerarHorarios("10:"));
//        System.out.println(gerarHorarios("10:4"));
//        System.out.println(gerarHorarios("10:50"));
        System.out.println(gerarHorarios("13:15 - ", new ArrayList<String>()));
        System.out.println(gerarHorarios("13:00 - 14:0", new ArrayList<String>()));
        System.out.println(gerarHorarios("13:00 - 14:30", new ArrayList<String>()));

    }
}
