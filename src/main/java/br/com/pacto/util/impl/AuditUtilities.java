package br.com.pacto.util.impl;

import br.com.pacto.security.dto.UsuarioSimplesDTO;

import java.util.HashMap;
import java.util.Map;

public class AuditUtilities {

    public static final ThreadLocal<Map<Long, UsuarioSimplesDTO>> usuario = new ThreadLocal<>();
    public static final ThreadLocal<Map<Long, String>> acaoAluno = new ThreadLocal<>();
    public static final ThreadLocal<Map<Long, String>> acoes = new ThreadLocal<>();
    public static final ThreadLocal<Map<Long, String>> ips = new ThreadLocal<>();


    public static String getAcaoAlunoFromCurrentThread(Long id) {
        return  acaoAluno.get() == null ? null : acaoAluno.get().get(id);
    }

    public static String getIpFromCurrentThread(Long id) {
        return ips.get() == null ? null : ips.get().get(id);
    }

    public static void putAcaoAlunoFromCurrentThread(final long id, final String acao) {
        Map<Long, String> mapAcaoAluno = acaoAluno.get();
        if (mapAcaoAluno == null) {
           mapAcaoAluno = new HashMap<>();
           acaoAluno.set(mapAcaoAluno);
        }
        mapAcaoAluno.put(id, acao);
    }

    public static void putIpFromCurrentThread(final long id, final String ip) {
        Map<Long, String> mapIp = ips.get();
        if (mapIp == null) {
            mapIp = new HashMap<>();
            ips.set(mapIp);
        }
        mapIp.put(id, ip);
    }

    public static void leaveAcaoAlunoFromCurrentThread() {
        acaoAluno.remove();
    }

    public static UsuarioSimplesDTO getUsuarioFromCurrentThread(Long id) {
        return usuario.get() == null ? null : usuario.get().get(id);
    }

    public static void putUsuarioFromCurrentThread(final long id, final UsuarioSimplesDTO usuarioS) {
        Map<Long, UsuarioSimplesDTO> mapUsuario = usuario.get();
        if (mapUsuario == null) {
            mapUsuario = new HashMap<>();
            usuario.set(mapUsuario);
        }
        mapUsuario.put(id, usuarioS);
    }

    public static void leaveUsuarioFromCurrentThread() {
        usuario.remove();
    }

    public static String getAcaoFromCurrentThread(Long id) {
        return acoes.get() == null ? null : acoes.get().get(id);
    }

    public static void putAcaoFromCurrentThread(final long id, final String acao) {
        Map<Long, String> mapAcoes = acoes.get();
        if (mapAcoes == null) {
            mapAcoes = new HashMap<>();
            acoes.set(mapAcoes);
        }
        mapAcoes.put(id, acao);
    }

    public static void leaveAcaoFromCurrentThread() {
        acoes.remove();
    }

    public static void leaveIpCurrentThread() {
        ips.remove();
    }

    public static void leaveAll(){
        leaveUsuarioFromCurrentThread();
        leaveIpCurrentThread();
        leaveAcaoFromCurrentThread();
        leaveAcaoAlunoFromCurrentThread();
    }

}
