package br.com.pacto.util.impl;

import javax.faces.event.ActionEvent;

public class ModalPanel {

    private boolean modalRendered;
    private String messagePanel;
    private String captionPanel;

    public ModalPanel(String mensagem, String titulo) {
        this.messagePanel = mensagem;
        this.captionPanel = titulo;
    }

    public boolean isModalRendered() {
        return modalRendered;
    }

    public void setModalRendered(boolean modalRendered) {
        this.modalRendered = modalRendered;
    }

    public String getMessagePanel() {
        return messagePanel;
    }

    public void setMessagePanel(String messagePanel) {
        this.messagePanel = messagePanel;
    }

    public String getCaptionPanel() {
        return captionPanel;
    }

    public void setCaptionPanel(String captionPanel) {
        this.captionPanel = captionPanel;
    }

    public void toggleModal(ActionEvent event) {
        modalRendered = !modalRendered;
    }
}
