package br.com.pacto.util.impl;

import br.com.pacto.util.UtilContext;
import br.com.pacto.util.ViewUtils;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.MissingResourceException;
import java.util.ResourceBundle;

import javax.faces.application.Application;
import javax.faces.application.FacesMessage;
import javax.faces.component.UIComponent;
import javax.faces.component.UIParameter;
import javax.faces.component.UIViewRoot;
import javax.faces.component.html.HtmlDataTable;
import javax.faces.context.FacesContext;
import javax.faces.el.ValueBinding;
import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

@SuppressWarnings({"deprecation", "unchecked"})
public final class JSFUtilities {

    private static final String RSOURCE_NOT_FOUND = "Missing resource: ";
    public static final String IP_ADDR = "ip_addr";
    public static final String KEY = "key";
    public static final String TREINO_INDEPENDENTE = "treinoIndependente";
    public static final String LINK_LOGIN_SERVICE = "linkLoginSol";
    public static final String LINK_ATENDIMENTO_SOL = "linkParamSol";
    public static final String LOGGED = "logado";
    public static final String HEIGHT_SCREEN = "heightScreen";
    public static final String WIDTH_SCREEN = "widthScreen";
    public static final String URL_LOGIN = "urlLogin";
    public static final String COOKIE_CREDENTIALS = "cR33d";
    public static final String COOKIE_CREDENTIALS_FAILOVER = "faILover";
    public static final String JSESSIONID = "JSESSIONID";
    public static final String USER_OAMD = "userOamd";
    public static final String TZ = "timeZoneID";
    public static final String INTEGRACAO_OLYMPIA = "integracaoOlympia";
    public static final String USAR_NOVA_MONTAGEM = "usarNovaMontagem";
    public static final String DIAS_PARA_EXPIRAR = "diasParaExpirar";

    private JSFUtilities() {
    }

    public static Object getManagedBean(final String instanceName) {
        return FacesContext.getCurrentInstance().getApplication().createValueBinding("#{" + instanceName + "}").getValue(
                FacesContext.getCurrentInstance());
    }

    public static Object getManagedBeanValue(final String ref) {
        final StringBuffer buff = new StringBuffer("#{");
        buff.append(ref);
        buff.append("}");
        return JSFUtilities.resolveExpression(buff.toString());
    }

    public static Object getManagedBeanValue(final Class ref) {
        if (isJSFContext()) {
            final StringBuffer buff = new StringBuffer("#{");
            buff.append(ref.getSimpleName());
            buff.append("}");
            return JSFUtilities.resolveExpression(buff.toString());
        } else {
            return null;
        }
    }

    public static void setManagedBeanValue(final String ref, final Object newValue) {
        final StringBuffer buff = new StringBuffer("#{");
        buff.append(ref);
        buff.append("}");
        JSFUtilities.setExpressionValue(buff.toString(), newValue);
    }

    public static Object resolveExpression(final String expression) {
        final FacesContext ctx = FacesContext.getCurrentInstance();
        final Application app = ctx.getApplication();
        final ValueBinding bind = app.createValueBinding(expression);
        return bind.getValue(ctx);
    }

    public static void setExpressionValue(final String expression, final Object newValue) {
        final FacesContext ctx = FacesContext.getCurrentInstance();
        final Application app = ctx.getApplication();
        final ValueBinding bind = app.createValueBinding(expression);

        final Class bindClass = bind.getType(ctx);
        if (bindClass.isPrimitive() || bindClass.isInstance(newValue)) {
            bind.setValue(ctx, newValue);
        }
    }

    public static String getResourceBundleProperty(final String key, final String defaultValue) {
        final FacesContext ctx = FacesContext.getCurrentInstance();
        final UIViewRoot uiRoot = ctx.getViewRoot();
        final Locale locale = uiRoot.getLocale();
        final ClassLoader ldr = Thread.currentThread().getContextClassLoader();
        final ResourceBundle bundle = ResourceBundle.getBundle(ctx.getApplication().getMessageBundle(), locale, ldr);

        String resource = null;
        try {
            resource = bundle.getString(key);
        } catch (MissingResourceException mrex) {
            if (defaultValue != null) {
                resource = defaultValue;
            } else {
                resource = JSFUtilities.RSOURCE_NOT_FOUND + key;
            }
        }
        return resource;
    }

    public static String getAcaoDisparadora() {
        return (String) getFromSession("acaoDisparadora");
    }
    public static Object getFromSession(final String key) {
        final FacesContext ctx = FacesContext.getCurrentInstance();
        if (ctx != null) {
            final Map sessionState = ctx.getExternalContext().getSessionMap();
            return sessionState.get(key);
        } else {
            return null;
        }
    }

    public static FacesContext ctx() {
        return FacesContext.getCurrentInstance();
    }

    public static void invalidateSession() {
        ViewUtils viewUtils = (ViewUtils) UtilContext.getBean(ViewUtils.class);
        viewUtils.removeCookie(COOKIE_CREDENTIALS_FAILOVER);
        HttpSession session = (HttpSession) ctx().getExternalContext().getSession(false);
        session.invalidate();
    }

    public static Object getFromRequest(final String key) {
        if (ctx() != null) {
            final Map requestState = ctx().getExternalContext().getRequestMap();
            return requestState.get(key);
        } else {
            return null;
        }
    }

    public static Object getFromSession(Class classRef) {
        if (ctx() != null) {
            final Map sessionState = ctx().getExternalContext().getSessionMap();
            return sessionState.get(classRef.getSimpleName());
        } else {
            return null;
        }
    }

    public static void acaoDisparadora(final String a) {
        storeOnSession("acaoDisparadora", a);
    }
    
    public static void storeOnSession(final String key, final Object object) {
        if (ctx() != null) {
            final Map sessionState = ctx().getExternalContext().getSessionMap();
            sessionState.put(key, object);
        }
    }

    public static boolean isJSFContext() {
        return ctx() != null;
    }

    public static Object getRequestAttribute(final String name) {
        return ctx().getExternalContext().getRequestMap().get(name);
    }

    public static void setRequestAttribute(final String name, final Object value) {
        ctx().getExternalContext().getRequestMap().put(name, value);
    }

    public static void addGlobalErrorMessage(final String msg) {
        final FacesMessage facesm = new FacesMessage(FacesMessage.SEVERITY_ERROR, msg, "");
        ctx().addMessage(JSFUtilities.getRootViewComponentId(), facesm);
    }

    public static void addGlobalInfoMessage(final String msg) {
        final FacesMessage facesm = new FacesMessage(FacesMessage.SEVERITY_INFO, msg, "");
        ctx().addMessage(JSFUtilities.getRootViewComponentId(), facesm);
    }

    public static void addComponentMessage(final String componentId, final String msg) {
        final FacesMessage facesm = new FacesMessage(FacesMessage.SEVERITY_ERROR, componentId, msg);
        ctx().addMessage(JSFUtilities.getRootViewComponentId(), facesm);
    }

    public static String getRootViewId() {
        return ctx().getViewRoot().getViewId();
    }

    public static String getRootViewComponentId() {
        return ctx().getViewRoot().getId();
    }

    public static HttpServletRequest getRequest() {
        return (HttpServletRequest) ctx().getExternalContext().getRequest();
    }
    
    public static HttpServletResponse getResponse() {
        return (HttpServletResponse) ctx().getExternalContext().getResponse();
    }

    public static void removeFromSession(final String name) {
        JSFUtilities.getRequest().getSession().removeAttribute(name);
    }

    public static void removeFromSession(final Class classRef) {
        JSFUtilities.getRequest().getSession().removeAttribute(classRef.getSimpleName());
    }

    /**
     * Retorna um List<SelectItem> atrav�s de um Enum
     *
     * <AUTHOR> Maciel
     * @param <E>
     * @param elemType
     * @param nomeAtributo
     * @param itemNone
     * @return
     */
    public static <E extends Enum<E>> List getSelectItemListFromEnum(Class<E> elemType,
            final String nomeAtributo, final String itemNone) {
        List temp = new ArrayList();
        if (itemNone != null && !itemNone.isEmpty()) {
            temp.add(new SelectItem(null, itemNone));
        }

        for (int i = 0; i < elemType.getEnumConstants().length; i++) {
            E obj = elemType.getEnumConstants()[i];
            temp.add(new SelectItem(obj, (String) UtilReflection.getValor(obj, nomeAtributo)));
        }
        Ordenacao.ordenarLista(temp, "label");
        return temp;
    }

    /**
     * Retorna um List dos objetos de um Enum
     *
     * @param <E>
     * @param elemType
     * @return
     */
    public static <E extends Enum<E>> List getListFromEnum(Class<E> elemType) {
        List temp = new ArrayList();

        for (int i = 0; i < elemType.getEnumConstants().length; i++) {
            E obj = elemType.getEnumConstants()[i];
            temp.add(obj);
        }
        return temp;
    }

    /**
     * Retorna um List<SelectItem> atrav�s de um List gen�rico, com Value e
     * Label dos argumentos.
     *
     * @param lista
     * @param attributeLabel
     * @param attributeValue
     * @param itemNone
     * @return
     */
    public static List getSelectItemListFrom(List lista, final String attributeLabel,
            final String attributeValue, final boolean itemNone) {
        List temp = new ArrayList();
        if (itemNone) {
            temp.add(new SelectItem(0, "(Nenhum)"));
        }

        for (Object o : lista) {
            temp.add(new SelectItem(
                    UtilReflection.getValor(o, attributeValue),
                    (String) UtilReflection.getValor(o, attributeLabel)));
        }

        return temp;
    }
    /**
     * Retorna um List<SelectItem> atrav�s de um List gen�rico, com Value e
     * Label dos argumentos.
     *
     * @param lista
     * @param attributeLabel
     * @param attributeValue
     * @param itemNone
     * @return
     */
    public static List getSelectItemListFrom(List lista, final String attributeLabel,
                                             final String attributeValue, final boolean itemNone,String labelNone) {
        List temp = new ArrayList();
        if (itemNone) {
            temp.add(new SelectItem(0, labelNone));
        }

        for (Object o : lista) {
            temp.add(new SelectItem(
                    UtilReflection.getValor(o, attributeValue),
                    (String) UtilReflection.getValor(o, attributeLabel)));
        }

        return temp;
    }
    public static List getSelectItemListFrom(List lista, final boolean itemNone) {
        List temp = new ArrayList();
        if (itemNone) {
            temp.add(new SelectItem(0, "(Nenhum)"));
        }

        for (Object o : lista) {
            temp.add(new SelectItem(o));
        }

        return temp;
    }

    /**
     * Obtem atrav�s de objeto HtmlDatatable a lista de objetos conforme seu
     * estado, orden��o, filtro, etc
     *
     * @param table
     * @return
     * @throws Exception
     */
    public static List getListFromDataScroller(HtmlDataTable table) throws Exception {
        List lista = new ArrayList();
        for (int row = 0; row < table.getRowCount(); row++) {
            try {
                table.setRowIndex(row);
                lista.add(table.getRowData());
            } catch (Exception e) {
                throw e;
            }
        }
        return lista;
    }

    public static List getListFromDataScrollerSorted(HtmlDataTable table, final String atributoOrdem) throws Exception {
        List lista = getListFromDataScroller(table);
        return Ordenacao.ordenarLista(lista, atributoOrdem);
    }

    public static StringBuffer printDataTableToHtmlBasic(HtmlDataTable table) throws Exception {
        List lista = getListFromDataScroller(table);
        return printListToTableHTML(lista);
    }

    public static StringBuffer printListToTableHTML(List lista) {
        StringBuffer sb = new StringBuffer();
        sb.append("<table style=\"font-size:10px;font-family:Arial;\" border=\"1\">");
        if (lista.size() > 0) {
            Object o = lista.get(0);
            List<String> attrs = UtilReflection.getListAttributes(o.getClass());
            sb.append("<tr>");
            for (String titulo : attrs) {
                sb.append("<td>").append(titulo.toUpperCase()).append("</td>");
            }
            sb.append("</tr>");

            for (Object value : lista) {
                sb.append("<tr>");
                for (String titulo : attrs) {
                    sb.append("<td>").append(UtilReflection.getValor(value, titulo)).append("</td>");
                }
                sb.append("</tr>");
            }
        }
        sb.append("</table>");
        return sb;
    }

    public static ActionEvent createEventParameter(final String name, Object value) {
        Map<String, Object> parametros = new HashMap<String, Object>();
        parametros.put(name, value);
        return createEventParameter(parametros);
    }

    public static ActionEvent createEventParameter(Map<String, Object> parametros) {
        UIComponent c = new UIParameter();
        c.getAttributes().putAll(parametros);
        return new ActionEvent(c);
    }

    public static String getKey() {
        return (String) getFromSession(KEY);
    }

    public static String getIp() {
        return (String) getFromSession(IP_ADDR);
    }

    public static String getProp(final String name) {
        if ((name != null && !name.isEmpty())) {
            if (name.equals("rootPath")) {
                return ctx().getExternalContext().getRequestContextPath();
            }//demais recursos...
        }
        return "";
    }

    public static Object getBeanView(final String name) {
        Map<String, Object> viewMap = FacesContext.getCurrentInstance().getViewRoot().getViewMap();
        return viewMap.get(name);
    }
}
