/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.util.impl;

import br.com.pacto.objeto.Uteis;
import br.com.pacto.util.MensagemUtils;
import br.com.pacto.util.ViewUtils;
import java.sql.BatchUpdateException;
import javax.persistence.EntityExistsException;
import org.hibernate.exception.ConstraintViolationException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR>
 */
@Service
public class MensagemUtilsImpl implements MensagemUtils {

    @Autowired
    private ViewUtils viewUtils;

    @Override
    public String tratarMensagemErroExclusao(EntityExistsException e) {
        try {
            String mensagem = (((BatchUpdateException) ((ConstraintViolationException) e.getCause()).getCause()).getNextException().getMessage());
            mensagem = viewUtils.getMensagem("entidadePossuiRelacionamento") + " " + mensagem.substring(mensagem.lastIndexOf(" "));
            return mensagem;
        } catch (Exception ex) {
            Uteis.logar(null, "Erro ao MensagemUtilsImpl.tratarMensagemErroExclusao (EntityExistsException) => " + ex.getMessage());
            return e.getMessage();
        }
    }
    
    @Override
    public String tratarMensagemErroExclusao(ConstraintViolationException e) {
        try {
            String mensagem = (((BatchUpdateException) e.getCause()).getNextException().getMessage());
            mensagem = viewUtils.getMensagem("entidadePossuiRelacionamento") + " " + mensagem.substring(mensagem.lastIndexOf(" "));
            return mensagem;
        } catch (Exception ex) {
            Uteis.logar(null, "Erro ao MensagemUtilsImpl.tratarMensagemErroExclusao (ConstraintViolationException) => " + ex.getMessage());
            return e.getMessage();
        }
    }

    @Override
    public String tratarMensagemUniqueConstraint(EntityExistsException e) {
        try {
            String mensagem = ((ConstraintViolationException) e.getCause()).getMessage();
            mensagem = viewUtils.getMensagem("entidadeNomeDuplicado") + " " + mensagem.substring(mensagem.lastIndexOf(" "));
            return mensagem;
        } catch (Exception ex) {
            Uteis.logar(null, "Erro ao MensagemUtilsImpl.tratarMensagemUniqueConstraint => " + ex.getMessage());
            return e.getMessage();
        }
    }
}
