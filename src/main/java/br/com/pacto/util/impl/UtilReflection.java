/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.util.impl;

import br.com.pacto.bean.atividade.Atividade;
import br.com.pacto.bean.atividade.AtividadeAlternativa;
import br.com.pacto.bean.atividade.AtividadeAparelho;
import br.com.pacto.bean.atividade.AtividadeCategoriaAtividade;
import br.com.pacto.bean.atividade.AtividadeGrupoMuscular;
import br.com.pacto.bean.atividade.AtividadeNivel;
import br.com.pacto.controller.json.modalidade.ModalidadeTO;
import br.com.pacto.controller.json.turma.TurmaResponseDTO;
import br.com.pacto.controller.json.turma.TurmaVideoDTO;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import javax.persistence.ElementCollection;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;

import static java.util.Objects.isNull;

/**
 *
 * <AUTHOR>
 */
public class UtilReflection {

    private static final String VAZIO = "vazio";
    private static final String BR = "<br/>";

    public static <T> T copy(T entity) {
        try {
            Class<?> clazz = entity.getClass();
            T newEntity = (T) entity.getClass().newInstance();

            while (clazz != null) {
                copyFields(entity, newEntity, clazz);
                clazz = clazz.getSuperclass();
            }
            return newEntity;
        } catch (Exception ex) {
            ex.printStackTrace();
            return null;
        }
    }

    public static <T> T copyFields(T entity, T newEntity, Class<?> clazz) throws IllegalAccessException {
        List<Field> fields = new ArrayList<>();
        for (Field field : clazz.getDeclaredFields()) {
            fields.add(field);
        }
        for (Field field : fields) {
            field.setAccessible(true);
            field.set(newEntity, field.get(entity));
        }
        return newEntity;
    }

    public static void setValor(Object instance, final Object valor, final String nomeAtributo)
            throws ClassNotFoundException,
            NoSuchMethodException,
            IllegalAccessException,
            IllegalArgumentException,
            InvocationTargetException,
            NoSuchFieldException {

        if (valor != null) {

            Class clazz = instance.getClass();
            Class partypes[] = new Class[1];
            Object[] parvalues = new Object[1];

            Method metodo = null;
            Field attr = null;

            attr = clazz.getDeclaredField(nomeAtributo);

            if (attr.getType() == Double.class) {
                partypes[0] = Double.class;
                parvalues[0] = Double.valueOf(valor.toString());
            } else if (attr.getType() == Integer.class) {
                partypes[0] = Integer.class;
                parvalues[0] = Integer.valueOf(valor.toString());

            } else {
                partypes[0] = valor.getClass();
                parvalues[0] = valor;
            }

            metodo = clazz.getMethod("set" + firstLetterUpper(nomeAtributo), partypes);
            metodo.invoke(instance, parvalues);
        }
    }

    public static Object getValorScript(Object obj, String nomeAtributo) {
        return getValorScript(obj, nomeAtributo, null);
    }

    public static Object getValorScript(Object obj, String nomeAtributo,
            String classesCallStack) {
        try {
            Class clazz = obj.getClass();

            Method metodo;

            Field attr = clazz.getDeclaredField(nomeAtributo);

            if (classesCallStack != null
                    && (classesCallStack.contains(attr.getType().getCanonicalName()))) {
                return "";
            }

            metodo = clazz.getMethod("get" + firstLetterUpper(nomeAtributo), null);
            Object retorno = metodo.invoke(obj, null);

            if (retorno != null) {
                attr.setAccessible(true);
                if (attr.getType() == Double.class) {
                    return Double.valueOf(retorno == null ? "0.0" : retorno.toString());

                } else if (attr.getType() == Integer.class) {
                    return Integer.valueOf(retorno == null ? "0" : retorno.toString());
                } else if (attr.getType() == String.class) {
                    return retorno == null ? "" : retorno.toString();
                } else if (attr.getType() == List.class) {
                    return retorno == null ? new ArrayList() : retorno;
                } else {
                    return retorno == null ? "" : retorno.toString();
                }
            }
        } catch (Exception e) {
            return null;
        }
        return null;
    }

    public static Object getValor(Object obj, String nomeAtributo) {
        return getValor(obj, nomeAtributo, null);
    }

    public static Object getValor(Object obj, String nomeAtributo,
                                  String classesCallStack) {
        try {
            Class clazz = obj.getClass();

            Method metodo = null;

            Field attr = null;

            try {
                clazz.getDeclaredField(nomeAtributo);
            }catch (Exception e){

            }

            /*if (attributeComposite(attr.getType(), clazz)){
             return "";
             }*/
            if (attr != null && (classesCallStack != null)
                    && (classesCallStack.contains(attr.getType().getCanonicalName()))) {
                return "";
            }

            metodo = clazz.getMethod("get" + firstLetterUpper(nomeAtributo), null);
            Object retorno = metodo.invoke(obj, null);

            if(retorno != null && attr == null){
                return retorno.toString();
            }

            if (retorno != null) {
                attr.setAccessible(true);
                if (attr.getType() == Double.class) {
                    return Double.valueOf(retorno == null ? "0.0" : retorno.toString());

                } else if (attr.getType() == Integer.class) {
                    return Integer.valueOf(retorno == null ? "0" : retorno.toString());
                } else if (attr.getType() == String.class) {
                    return retorno == null ? "" : retorno.toString();
//                } else if ((retorno instanceof SuperVO) && (attr.isAnnotationPresent(ChaveEstrangeira.class))) {
//                    List<String> atributos = getListAttributes(retorno.getClass());
//                    Object object = retorno;
//                    classesCallStack += object.getClass().getCanonicalName();
//                    for (String atributo : atributos) {
//                        Object o = getValor(object, atributo, classesCallStack);
//                        String valor = o == null ? "" : o.toString();
//                        //Uteis.logar(null, object + "." + atributo + " -> " + valor);
//                        String aux = retorno.toString() + (!valor.toString().isEmpty() ? valor.toString() + "|" : "");
//                        retorno = aux;
//                    }
//                    classesCallStack = null;
//                    return retorno;
//                } else if ((retorno instanceof List) && (attr.isAnnotationPresent(Lista.class))) {
//                    if (!((List) retorno).isEmpty()) {
//                        if (((List) retorno).get(0) instanceof SuperVO) {
//                            List lista = (List) retorno;
//                            for (Object object : lista) {
//                                classesCallStack += object.getClass().getCanonicalName();
//                                List<String> atributos = getListAttributes(object.getClass());
//                                for (String atributo : atributos) {
//                                    Object o = getValor(object, atributo, classesCallStack);
//                                    String valor = o == null ? "" : o.toString();
//                                    //Uteis.logar(null, object + "." + atributo + " -> " + valor);
//                                    String aux = retorno.toString() + (!valor.toString().isEmpty() ? valor.toString() + "|" : "");
//                                    retorno = aux;
//                                }
//                            }
//                            classesCallStack = null;
//                            return retorno;
//                        }
//                    }
                } else if (attr.getType() == List.class) {
                    return retorno == null ? new ArrayList() : retorno;
                } else {
                    return retorno == null ? "" : retorno.toString();
                }
            }
        } catch (Exception e) {
            return null;
        }
        return null;
    }

    public static Object getValorObject(Object obj, String nomeAtributo) {
        try {
            Class clazz = Class.forName(obj.getClass().getCanonicalName());

            Method metodo = null;

            Field attr = clazz.getDeclaredField(nomeAtributo);

            metodo = clazz.getMethod("get" + firstLetterUpper(nomeAtributo), null);
            Object retorno = metodo.invoke(obj, null);
            if (retorno != null) {
                attr.setAccessible(true);
                if (attr.getType() == Double.class) {
                    return Double.valueOf(retorno == null ? "0.0" : retorno.toString());

                } else if (attr.getType() == Integer.class) {
                    return Integer.valueOf(retorno == null ? "0" : retorno.toString());
                } else if (attr.getType() == String.class) {
                    return retorno == null ? "" : retorno.toString();
                } else if (attr.getType() == List.class) {
                    return retorno == null ? new ArrayList() : retorno;
                } else {
                    return retorno;
                }
            }
        } catch (Exception e) {
            return null;
        }
        return null;
    }

    public static List<String> getListAttributes(Class classe) {
        List<String> lista = new ArrayList();
        Class clazz;
        try {
            clazz = Class.forName(classe.getCanonicalName());
            Field[] attr = null;

            attr = clazz.getDeclaredFields();

            for (int i = 0; i < attr.length; i++) {
                Field field = attr[i];
                lista.add(field.getName());
            }
        } catch (ClassNotFoundException ex) {
            Logger.getLogger(UtilReflection.class.getName()).log(Level.SEVERE, null, ex);
        }

        return lista;

    }

    public static List<Field> getListFields(final Class classe) {
        List<Field> lista = new ArrayList();
        Class clazz;
        try {
            clazz = Class.forName(classe.getCanonicalName());
            Field[] attr = clazz.getDeclaredFields();
            for (int i = 0; i < attr.length; i++) {
                Field field = attr[i];
                if (!field.isAnnotationPresent(OneToMany.class)
                        && !field.isAnnotationPresent(ManyToOne.class)
                        && !field.isAnnotationPresent(ElementCollection.class)) {
                    lista.add(field);
                }
            }
        } catch (ClassNotFoundException ex) {
            Logger.getLogger(UtilReflection.class.getName()).log(Level.SEVERE, null, ex);
        }
        return lista;
    }

    public static StringBuilder difference(final Object old, final Object n, String ... campos) {
        StringBuilder sb = new StringBuilder();
        if (old != null) {
            List<Field> fs = getListFields(old.getClass());
            for (Field f : fs) {
                try {
                    boolean fazerLog = campos == null || campos.length == 0
                                        || Arrays.stream(campos).anyMatch(element -> element.equals(f.getName()));
                    if(!fazerLog){
                        continue;
                    }
                    if (isNull(n)) {
                        String oldValue =  prepareApresentacao(old, f);
                        if (!isNull(oldValue) && !oldValue.contains("br.com.pacto")) {
                            if(campos == null || campos.length != 1){
                                sb.append(BR);
                                sb.append(f.getName().toUpperCase()).append(": ");
                            }
                            sb.append(firstLetterUpper(oldValue)).append(";");
                        }
                    } else {
                        String oldValue = prepareApresentacao(old, f);

                        String newValue = prepareApresentacao(n, f);

                        if (!isNull(oldValue) && !isNull(newValue) && !oldValue.equals(newValue)
                                && (!oldValue.contains("br.com.pacto") || !newValue.contains("br.com.pacto"))) {


                            if(campos == null || campos.length != 1){
                                sb.append(BR);
                                sb.append(f.getName().toUpperCase()).append(": ");
                            }
                            sb.append(firstLetterUpper(oldValue)).append(";");

                        }
                    }
                } catch (Exception ex) {
                    Logger.getLogger(UtilReflection.class.getName()).log(Level.SEVERE, null, ex);
                }
            }
            if(sb.length() > 0){
                sb.insert(0, BR + old.getClass().getSimpleName() + ": ");
            }
        }
        return sb;
    }

    private static String prepareApresentacao(Object obj, Field f){
        if(isNull(obj) || isNull(getValorObject(obj, f.getName()))){
            return null;
        }
        Object value = getValorObject(obj, f.getName());
        if(value == null){
            return VAZIO;
        }
        if(value instanceof Boolean){
            return (Boolean) value ? "Sim" : "Não";
        }
        if(value instanceof Date){
            return Calendario.getData((Date) value, "dd/MM/yyyy HH:mm");
        }
        try {
            if (value instanceof ModalidadeTO) {
                Integer codigo = ((ModalidadeTO) value).getId();
                String nome = ((ModalidadeTO) value).getNome();
                return codigo + " - " + nome;
            }

            if (obj instanceof TurmaResponseDTO && value instanceof ArrayList) {
                String listaLinksVideo = "";
                for (Object item : (ArrayList) value) {
                    if (item instanceof TurmaVideoDTO) {
                        String professor = ((TurmaVideoDTO) item).getProfessor() ? "Sim" : "Não";
                        listaLinksVideo += ((TurmaVideoDTO) item).getLinkVideo() + " - professor? " + professor + "; ";
                    }
                }
                return listaLinksVideo;
            }
        } catch (Exception e) {
            e.printStackTrace();
            Uteis.logarDebug("Erro ao capturar logs da turma: " + e.getMessage());
        }
        return value.toString();
    }

    public static StringBuilder differenceReference(final Object old, final Object n, String ... campos) {
        StringBuilder sb = new StringBuilder();
        if (old != null) {
            List<Field> fs = getListFieldsReferencce(old.getClass());
            for (Field f : fs) {
                try {
                    boolean fazerLog = campos == null || campos.length == 0
                            || Arrays.stream(campos).anyMatch(element -> element.equals(f.getName()));
                    if(!fazerLog){
                        continue;
                    }
                    if (isNull(n)) {
                        String oldValue =  prepareApresentacaoReference(old, f);
                        if (!isNull(oldValue) && !oldValue.contains("br.com.pacto")) {
                            if(campos == null || campos.length != 1){
                                sb.append(BR);
                                sb.append(f.getName().toUpperCase()).append(": ");
                            }
                            sb.append(firstLetterUpper(oldValue)).append(";");
                        }
                    } else {
                        String oldValue = prepareApresentacaoReference(old, f);

                        String newValue = prepareApresentacaoReference(n, f);

                        if (!isNull(oldValue) && !isNull(newValue) && !oldValue.equals(newValue)
                                && (!oldValue.contains("br.com.pacto") || !newValue.contains("br.com.pacto"))) {


                            if(campos == null || campos.length != 1){
                                sb.append(BR);
                                sb.append(f.getName().toUpperCase()).append(": ");
                            }
                            sb.append(firstLetterUpper(oldValue)).append(";");

                        }
                    }
                } catch (Exception ex) {
                    Logger.getLogger(UtilReflection.class.getName()).log(Level.SEVERE, null, ex);
                }
            }
            if(sb.length() > 0){
                sb.insert(0, BR + old.getClass().getSimpleName() + ": ");
            }
        }
        return sb;
    }

    public static List<Field> getListFieldsReferencce(final Class classe) {
        List<Field> lista = new ArrayList();
        Class clazz;
        try {
            clazz = Class.forName(classe.getCanonicalName());
            Field[] attr = clazz.getDeclaredFields();
            for (int i = 0; i < attr.length; i++) {
                Field field = attr[i];
                lista.add(field);
            }
        } catch (ClassNotFoundException ex) {
            Logger.getLogger(UtilReflection.class.getName()).log(Level.SEVERE, null, ex);
        }
        return lista;
    }

    private static String prepareApresentacaoReference(Object obj, Field f){
        if(isNull(obj) || isNull(getValorObject(obj, f.getName()))){
            return null;
        }
        Object value = getValorObject(obj, f.getName());
        if(value == null){
            return VAZIO;
        }
        if(value instanceof Boolean){
            return (Boolean) value ? "Sim" : "Não";
        }
        if(value instanceof Date){
            return Calendario.getData((Date) value, "dd/MM/yyyy HH:mm");
        }
        try {
            if (value instanceof ModalidadeTO) {
                Integer codigo = ((ModalidadeTO) value).getId();
                String nome = ((ModalidadeTO) value).getNome();
                return codigo + " - " + nome;
            }

            if (obj instanceof TurmaResponseDTO && value instanceof ArrayList) {
                String listaLinksVideo = "";
                for (Object item : (ArrayList) value) {
                    if (item instanceof TurmaVideoDTO) {
                        String professor = ((TurmaVideoDTO) item).getProfessor() ? "Sim" : "Não";
                        listaLinksVideo += ((TurmaVideoDTO) item).getLinkVideo() + " - professor? " + professor + "; ";
                    }
                }
                return listaLinksVideo;
            }
            if(obj instanceof Atividade){
                if(getValorObject(obj, f.getName()) instanceof List){
                    String valor ="";
                    try {
                        List<Object> lista = (List<Object>) getValorObject(obj, f.getName());
                        for (Object objeto : lista) {
                            if(f.getName().equals("gruposMusculares")){
                                valor +=((AtividadeGrupoMuscular) objeto).getGrupoMuscular().getNome()+BR;
                            }
                            if(f.getName().equals("atividadesAlternativas")){
                                valor += ((AtividadeAlternativa) objeto).getAtividadeAlternativa()+BR;
                            }
                            if(f.getName().equals("aparelhos")){
                                valor += ((AtividadeAparelho) objeto).getAparelho().getNome()+BR;
                            }
                            if(f.getName().equals("categorias")){
                                valor += ((AtividadeCategoriaAtividade) objeto).getCategoriaAtividade().getNome()+BR;
                            }
                            if(f.getName().equals("niveis")){
                                valor += ((AtividadeNivel) objeto).getNivel().getNome()+BR;
                            }
                        }
                    }catch (Exception e) {

                    }

                    return valor;
                }

            }
        } catch (Exception e) {
            e.printStackTrace();
            Uteis.logarDebug("Erro ao capturar logs da turma: " + e.getMessage());
        }
        return value.toString();
    }

    public static List<String> getListAttributes(Class classe, String iniciaCom, int lenNome, Class tipo) {
        List<String> lista = getListAttributes(classe);
        List<String> ret = new ArrayList();
        for (String att : lista) {
            try {
                if (att.startsWith(iniciaCom) && att.length() == lenNome
                        && classe.getDeclaredField(att).getType() == tipo) {
                    ret.add(att);
                }
            } catch (Exception e) {
            }
        }
        return ret;
    }

    public static boolean attributeComposite(Class classField, Class clazz) {
        try {
            Field[] attr = null;
            attr = clazz.getDeclaredFields();
            for (int i = 0; i < attr.length; i++) {
                Field f = attr[i];
                if (f.getType() == classField) {
                    return true;
                }
            }
        } catch (Exception ex) {
            Logger.getLogger(UtilReflection.class.getName()).log(Level.SEVERE, null, ex);
        }

        return false;

    }

    public static String firstLetterUpper(String palavra) {
        try {
            return palavra.replaceFirst(String.valueOf(palavra.charAt(0)),
                    String.valueOf(palavra.charAt(0)).toUpperCase());
        }catch (Exception e) {
            return palavra;
        }

    }

    public static Double sum(List list, final String attr) {
        Double result = 0.0;
        for (Object obj : list) {
            result += (Double) getValor(obj, attr);
        }
        return result;

    }

    public static Object invoke(Object obj, final String nomeMetodo) throws Exception {
        Class clazz = Class.forName(obj.getClass().getCanonicalName());
        Method metodo = null;
        metodo = clazz.getMethod(nomeMetodo, null);
        return metodo.invoke(obj, null);
    }

    public static Object invoke(Object instance, final String nomeMetodo,
            final Class[] parameterTypes, final Object[] parameterValues) throws Exception {
        Class clazz = instance.getClass();
        Method metodo = clazz.getMethod(nomeMetodo, parameterTypes);
        return metodo.invoke(instance, parameterValues);
    }

    public static boolean objetoMaiorQueZero(Object instancia, String objetoVerificar) throws Exception {
        if (instancia == null){
            return false;
        }
        if ((objetoVerificar == null) || (objetoVerificar.trim().equals(""))){
            return (instancia != null) && (((Number)instancia).intValue() > 0);
        }
        Object ret = getValueFromTag(instancia, objetoVerificar);
        if (ret == null)
            return false;
        return ((Number)ret).intValue() > 0;
    }

    private static Object getValueFromTag (Object object, String tag) throws Exception {
        if (object == null)
            return null;
        String fields[] = tag.split("\\.");
        Object returnValue = object;
        for (int i=0; i < fields.length; i++) {
            if (returnValue == null)
                return null;
            String atributo = fields[i].substring(3,fields[i].length()-2);
            returnValue = getValue(atributo, returnValue);
        }
        return returnValue;
    }

    private static Object getValue (String atributo, Object returnValue) throws Exception{
        String nomeMetodoGet = "get" +  atributo.substring(0, 1).toUpperCase()	+  atributo.substring(1);
        Method metodoGet = returnValue.getClass().getMethod(nomeMetodoGet, new Class[]{});
        return metodoGet.invoke(returnValue);
    }
}
