package br.com.pacto.dto.notificacao;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;

/**
 * DTO de notificações para o novo sistema de Treino.
 *
 * <AUTHOR>
 * @since 12/07/2018
 */
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class NotificacaoDTO {

    private String id;
    private String type;
    private String imageUri;
    private Boolean seen;
    private String payload;
    private String gravidade;
    private Long dataRegistro;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getImageUri() {
        return imageUri;
    }

    public void setImageUri(String imageUri) {
        this.imageUri = imageUri;
    }

    public Boolean getSeen() {
        return seen;
    }

    public void setSeen(Boolean seen) {
        this.seen = seen;
    }

    public String getPayload() {
        return payload;
    }

    public void setPayload(String payload) {
        this.payload = payload;
    }

    public String getGravidade() {
        return gravidade;
    }

    public void setGravidade(String gravidade) {
        this.gravidade = gravidade;
    }

    public Long getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(Long dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

}
