package br.com.pacto.dto.notificacao;

import java.util.List;

public class NotificacaoAppDoAlunoDTO {
    private String titulo;
    private List<String> horarios;
    private String userNameUsuario;
    private String content;
    private String chave;
    private Integer codEmpresa;
    private List<Integer> codUsuarios;
    private Boolean isGeral;
    private String nomeEmpresa;

    public NotificacaoAppDoAlunoDTO() {
    }

    public String getTitulo() {
        return titulo;
    }

    public void setTitulo(String titulo) {
        this.titulo = titulo;
    }

    public String getNomeEmpresa() {
        return nomeEmpresa;
    }

    public void setNomeEmpresa(String nomeEmpresa) {
        this.nomeEmpresa = nomeEmpresa;
    }

    public String getContent() {
        return content;
    }

    public List<String> getHorarios() {
        return horarios;
    }

    public void setHorarios(List<String> horarios) {
        this.horarios = horarios;
    }

    public String getUserNameUsuario() {
        return userNameUsuario;
    }

    public void setUserNameUsuario(String userNameUsuario) {
        this.userNameUsuario = userNameUsuario;
    }

    public void setContent(String content) {
        this.content = content;
    }


    public String getChave() {
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    public Integer getCodEmpresa() {
        return codEmpresa;
    }

    public void setCodEmpresa(Integer codEmpresa) {
        this.codEmpresa = codEmpresa;
    }

    public List<Integer> getCodUsuarios() {
        return codUsuarios;
    }

    public void setCodUsuarios(List<Integer> codUsuarios) {
        this.codUsuarios = codUsuarios;
    }

    public Boolean getGeral() {
        return isGeral;
    }

    public void setGeral(Boolean geral) {
        isGeral = geral;
    }
}
