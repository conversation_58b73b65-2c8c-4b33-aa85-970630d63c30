package br.com.pacto.dto.notificacao;

import java.util.List;

public class NotificacaoAppDoGestorDTO {
    private String titulo;
    private String horaEnvioNotificacao;
    private String content;
    private String categoria;
    private String chave;
    private Boolean naoAgendada;
    private Integer codEmpresa;
    private List<Integer> codUsuarios;
    private Boolean isGeral;
    private String nomeEmpresa;

    public NotificacaoAppDoGestorDTO(String titulo, String horaEnvioNotificacao, String content, String categoria, String chave, Boolean naoAgendada, Integer codEmpresa, List<Integer> codUsuarios, Boolean isGeral) {
        this.titulo = titulo;
        this.horaEnvioNotificacao = horaEnvioNotificacao;
        this.content = content;
        this.categoria = categoria;
        this.chave = chave;
        this.naoAgendada = naoAgendada;
        this.codEmpresa = codEmpresa;
        this.codUsuarios = codUsuarios;
        this.isGeral = isGeral;
    }

    public NotificacaoAppDoGestorDTO() {
    }

    public String getTitulo() {
        return titulo;
    }

    public void setTitulo(String titulo) {
        this.titulo = titulo;
    }

    public String getHoraEnvioNotificacao() {
        return horaEnvioNotificacao;
    }

    public void setHoraEnvioNotificacao(String horaEnvioNotificacao) {
        this.horaEnvioNotificacao = horaEnvioNotificacao;
    }

    public String getNomeEmpresa() {
        return nomeEmpresa;
    }

    public void setNomeEmpresa(String nomeEmpresa) {
        this.nomeEmpresa = nomeEmpresa;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getCategoria() {
        return categoria;
    }

    public void setCategoria(String categoria) {
        this.categoria = categoria;
    }

    public String getChave() {
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    public Boolean getNaoAgendada() {
        return naoAgendada;
    }

    public void setNaoAgendada(Boolean naoAgendada) {
        this.naoAgendada = naoAgendada;
    }

    public Integer getCodEmpresa() {
        return codEmpresa;
    }

    public void setCodEmpresa(Integer codEmpresa) {
        this.codEmpresa = codEmpresa;
    }

    public List<Integer> getCodUsuarios() {
        return codUsuarios;
    }

    public void setCodUsuarios(List<Integer> codUsuarios) {
        this.codUsuarios = codUsuarios;
    }

    public Boolean getGeral() {
        return isGeral;
    }

    public void setGeral(Boolean geral) {
        isGeral = geral;
    }
}
