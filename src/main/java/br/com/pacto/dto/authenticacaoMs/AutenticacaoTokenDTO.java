package br.com.pacto.dto.authenticacaoMs;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class AutenticacaoTokenDTO {

        private AutenticacaoUsuarioDTO dados;
        private String token;
        private Long validade;

        public AutenticacaoUsuarioDTO getDados() {
                return dados;
        }

        public void setDados(AutenticacaoUsuarioDTO dados) {
                this.dados = dados;
        }

        public String getToken() {
                return token;
        }

        public void setToken(String token) {
                this.token = token;
        }

        public Long getValidade() {
                return validade;
        }

        public void setValidade(Long validade) {
                this.validade = validade;
        }
}

