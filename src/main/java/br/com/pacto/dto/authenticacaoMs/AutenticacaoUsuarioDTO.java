package br.com.pacto.dto.authenticacaoMs;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class AutenticacaoUsuarioDTO {

        private Integer codZW;
        private String codTreino;
        private String perfilZW;
        private String perfilTR;
        private String userName;
        private String nome;
        private Integer colaboradorId;
        private String provider;
        private Boolean administrador;

        public Integer getCodZW() {
                return codZW;
        }

        public void setCodZW(Integer codZW) {
                this.codZW = codZW;
        }

        public String getCodTreino() {
                return codTreino;
        }

        public void setCodTreino(String codTreino) {
                this.codTreino = codTreino;
        }

        public String getPerfilZW() {
                return perfilZW;
        }

        public void setPerfilZW(String perfilZW) {
                this.perfilZW = perfilZW;
        }

        public String getPerfilTR() {
                return perfilTR;
        }

        public void setPerfilTR(String perfilTR) {
                this.perfilTR = perfilTR;
        }

        public String getUserName() {
                return userName;
        }

        public void setUserName(String userName) {
                this.userName = userName;
        }

        public String getNome() {
                return nome;
        }

        public void setNome(String nome) {
                this.nome = nome;
        }

        public Integer getColaboradorId() {
                return colaboradorId;
        }

        public void setColaboradorId(Integer colaboradorId) {
                this.colaboradorId = colaboradorId;
        }

        public String getProvider() {
                return provider;
        }

        public void setProvider(String provider) {
                this.provider = provider;
        }

        public Boolean getAdministrador() {
                return administrador;
        }

        public void setAdministrador(Boolean administrador) {
                this.administrador = administrador;
        }

}

