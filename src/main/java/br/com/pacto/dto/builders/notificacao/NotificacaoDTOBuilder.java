package br.com.pacto.dto.builders.notificacao;

import br.com.pacto.dto.notificacao.NotificacaoDTO;

/**
 * Construtor auxiliar para os DTOs de notificação
 *
 * <AUTHOR>
 * @since 12/07/2018
 */
public class NotificacaoDTOBuilder {

    private NotificacaoDTO notificacaoDTO;

    public NotificacaoDTOBuilder() {
        this.notificacaoDTO = new NotificacaoDTO();
    }

    public NotificacaoDTOBuilder comID(String id) {
        this.notificacaoDTO.setId(id);
        return this;
    }

    public NotificacaoDTOBuilder comGravidade(String gravidade) {
        this.notificacaoDTO.setGravidade(gravidade);
        return this;
    }

    public NotificacaoDTOBuilder comdataregistro(Long data) {
        this.notificacaoDTO.setDataRegistro(data);
        return this;
    }
    public NotificacaoDTOBuilder comTipo(String tipo) {
        this.notificacaoDTO.setType(tipo);
        return this;
    }

    public NotificacaoDTOBuilder comImagem(String imagemURI) {
        this.notificacaoDTO.setImageUri(imagemURI);
        return this;
    }

    public NotificacaoDTOBuilder visualizada(Boolean visualizada) {
        this.notificacaoDTO.setSeen(visualizada);
        return this;
    }

    public NotificacaoDTOBuilder comConteudo(String conteudo) {
        this.notificacaoDTO.setPayload(conteudo);
        return this;
    }

    public NotificacaoDTO build() {
        return this.notificacaoDTO;
    }

}
