package br.com.pacto.threads;

import br.com.pacto.bean.gympass.ConfigGymPass;
import br.com.pacto.bean.gympass.TipoThreadGympassEnum;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.intf.agenda.AgendaService;
import br.com.pacto.service.telegram.TelegramService;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;

import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 20/04/2020
 */
public class ThreadGympass extends Thread {

    private String chave;
    private Integer turma;
    private Integer horarioTurma;
    private Date dia;
    private Integer empresaZW;
    private Integer empresaTR;
    private TipoThreadGympassEnum tipoThreadGympassEnum;
    private ConfigGymPass configGymPass;

    public ThreadGympass(String chave, Integer turma, Integer empresaZW, Integer empresaTR, TipoThreadGympassEnum tipoThreadGympassEnum) {
        this.chave = chave;
        this.turma = turma;
        this.empresaZW = empresaZW;
        this.empresaTR = empresaTR;
        this.tipoThreadGympassEnum = tipoThreadGympassEnum;
    }

    @Override
    public void run() {
        try {
            synchronized (this) {
                try {
                    if (UteisValidacao.emptyString(this.chave)) {
                        throw new Exception("Chave não informada!");
                    }

                    Uteis.logarDebug( "Iniciei ThreadGympass | Chave " + this.chave);

                    AgendaService agendaService = UtilContext.getBean(AgendaService.class);
                    String retorno = "";
                    switch (getTipoThreadGympassEnum()){
                        case SINCRONIZAR_TURMA:
                            retorno = agendaService.sincronizarTurmaGympassBooking(chave, empresaZW, empresaTR, turma);
                            break;
                        case SINCRONIZAR_BOOKED:
                            retorno = agendaService.sincronizarTurmaGympassBooked(this.chave, this.empresaZW, this.dia, this.horarioTurma, this.configGymPass);
                            break;
                        case EXCLUIR_TURMA:
                            retorno = agendaService.excluirHorariosGympassBooking(chave, empresaZW, empresaTR, turma, false);
                            break;
                        case SINCRONIZAR_HORARIOS:
                            agendaService.sincronizarTurmasGympassBooking(chave, empresaZW, empresaTR);
                            retorno = "sincronizados";
                            break;
                        case EXCLUIR_HORARIO:
                            retorno = agendaService.excluirHorarioGympassBooking(chave, empresaZW, empresaTR, horarioTurma, turma);
                            break;
                    }
                    Uteis.logarDebug( "ThreadGympass | Retorno " + retorno);
                    Uteis.logarDebug( "Terminando ThreadGympass | Chave " + this.chave);
                } catch (Exception ex) {
                    Uteis.logarDebug( "ERRO ThreadGympass | Chave " + this.chave + " | " + ex.getMessage());
                    ex.printStackTrace();
                } finally {
                    Uteis.logarDebug( "Finalizei ThreadGympass | Chave " + this.chave);
                    this.interrupt();
                }
            }
        } catch (Exception ex) {
            Uteis.logar(ex, ThreadGympass.class);
        }
    }

    public Integer getTurma() {
        return turma;
    }

    public void setTurma(Integer turma) {
        this.turma = turma;
    }

    public Integer getEmpresaZW() {
        return empresaZW;
    }

    public Integer getEmpresaTR() {
        return empresaTR;
    }

    public void setEmpresaTR(Integer empresaTR) {
        this.empresaTR = empresaTR;
    }

    public void setEmpresaZW(Integer empresaZW) {
        this.empresaZW = empresaZW;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    public TipoThreadGympassEnum getTipoThreadGympassEnum() {
        return tipoThreadGympassEnum;
    }

    public void setTipoThreadGympassEnum(TipoThreadGympassEnum tipoThreadGympassEnum) {
        this.tipoThreadGympassEnum = tipoThreadGympassEnum;
    }

    public Integer getHorarioTurma() {
        return horarioTurma;
    }

    public void setHorarioTurma(Integer horarioTurma) {
        this.horarioTurma = horarioTurma;
    }

    public Date getDia() {
        return dia;
    }

    public void setDia(Date dia) {
        this.dia = dia;
    }

    public String getChave() {
        return chave;
    }

    public ConfigGymPass getConfigGymPass() {
        return configGymPass;
    }

    public void setConfigGymPass(ConfigGymPass configGymPass) {
        this.configGymPass = configGymPass;
    }
}
