package br.com.pacto.threads;

import java.util.Map;

/**
 * Interface para chamadas ass�ncronas.
 * Quando um callback deve ser executado em outra thread ou quando a mesma finaliza com sucesso ou falha.
 *
 * <AUTHOR>
 * @since 18/08/2018
 */
public interface Callback {

    /**
     * Executa uma determinada a��o
     *
     * @throws Exception caso ocorra algum erro na execu��o do servi�o
     */
    void call() throws Exception;

    /**
     * Os par�metros transmitidos entre os callbacks
     *
     * @return Os par�metros trocados entre os callbacks de a��o, sucesso e falha
     */
    Map<String, Object> getParametros();

}
