package br.com.pacto.threads;

import br.com.pacto.base.util.ExecuteRequestHttpService;
import br.com.pacto.bean.perguntaucp.IntegracaoTagUcpTO;
import br.com.pacto.bean.perguntaucp.PerguntaUcpTO;
import br.com.pacto.bean.perguntaucp.TagUcpTO;
import br.com.pacto.bean.perguntaucp.UCPTagTO;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.JSONMapper;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.impl.Ordenacao;
import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/*
 * Created by <PERSON><PERSON> on 25/04/2018.
 */
public class ThreadUCP extends Thread {

    @Override
    public void run() {
        try {
            Uteis.logar(null, "Iniciei ThreadUCP...");
            while (true) {
                consultarConhecimentosUCP();
                consultarTodosConhecimentosUCP();
                consultarListaConhecimentosTelaUCP();
                Thread.sleep(60 * 60 * 1000);
                Uteis.logar(null, "Finalizei ThreadUCP...");
            }
        } catch (Exception ex) {
            Uteis.logar(ex, ThreadUCP.class);
        }
    }

    private void consultarConhecimentosUCP() {
        try {
            List<IntegracaoTagUcpTO> listaFinal = new ArrayList<IntegracaoTagUcpTO>();

            Map<String, String> params = new HashMap<String, String>();
            Uteis.logar(null, "Inicia consulta Integracao Tags UCP ....");

            ExecuteRequestHttpService executeRequestHttpService = new ExecuteRequestHttpService();
            String executeRequest = executeRequestHttpService.executeRequestInner(Aplicacao.getProp(Aplicacao.myUpUrlBase) + "/prest/config/listaConhecimentosUCP", params, "UTF-8");

            JSONObject jsonObject = new JSONObject(executeRequest);
            JSONArray lista = new JSONArray(jsonObject.get("return").toString());

            for (int e = 0; e < lista.length(); e++) {
                JSONObject obj = lista.getJSONObject(e);
                try {
                    IntegracaoTagUcpTO integracaoTagUcpTO = JSONMapper.getObject(obj, IntegracaoTagUcpTO.class);
                    listaFinal.add(integracaoTagUcpTO);
                } catch (Exception ignored) {
                }
            }

            if (!UteisValidacao.emptyList(listaFinal)) {
                // descontinuado
//                UCPControle.listaIntegracaoTagUcp = listaFinal;
            }

            Uteis.logar(null, "Finaliza consulta Integracao Tags UCP ....");
        } catch (Exception e) {
            Uteis.logar(e, ThreadUCP.class);
        }
    }

    private void consultarTodosConhecimentosUCP() {
        try {
            List<PerguntaUcpTO> listaFinal = new ArrayList<PerguntaUcpTO>();

            Map<String, String> params = new HashMap<String, String>();
            Uteis.logar(null, "Inicia consulta de TODOS conhecimentos da UCP ....");

            ExecuteRequestHttpService executeRequestHttpService = new ExecuteRequestHttpService();
            String executeRequest = executeRequestHttpService.executeRequestInner(Aplicacao.getProp(Aplicacao.myUpUrlBase) + "/prest/config/listaTodosConhecimentosUCP", params, "UTF-8");

            JSONObject jsonObject = new JSONObject(executeRequest);
            JSONArray lista = new JSONArray(jsonObject.get("return").toString());

            for (int e = 0; e < lista.length(); e++) {
                JSONObject obj = lista.getJSONObject(e);
                try {
                    PerguntaUcpTO perguntaUcpTO = JSONMapper.getObject(obj, PerguntaUcpTO.class);
                    perguntaUcpTO.setTags(JSONMapper.getList(obj.getJSONArray("tags"), TagUcpTO.class));
                    listaFinal.add(perguntaUcpTO);
                } catch (Exception ignored) {
                }
            }

            if (!UteisValidacao.emptyList(listaFinal)) {
                List<PerguntaUcpTO> conhecimentosFundamentais = new ArrayList<PerguntaUcpTO>();
                for (PerguntaUcpTO perg : listaFinal) {
                    if (perg.isApresentarTRConhecimentoFundamental()) {
                        conhecimentosFundamentais.add(perg);
                        if (conhecimentosFundamentais.size() == 5) {
                            break;
                        }
                    }
                }
// descontinuado
//                UCPControle.listaTodosConhecimentosUCP = listaFinal;

                Ordenacao.ordenarLista(conhecimentosFundamentais, "ordemApresentar");
                // descontinuado
//                UCPControle.listaConhecimentosFundamentais = conhecimentosFundamentais;
            }

            Uteis.logar(null, "Finaliza consulta de TODOS conhecimentos da UCP ....");
        } catch (Exception e) {
            Uteis.logar(e, ThreadUCP.class);
        }
    }

    private void consultarListaConhecimentosTelaUCP() {
        try {
            List<IntegracaoTagUcpTO> listaFinal = new ArrayList<IntegracaoTagUcpTO>();

            Map<String, String> params = new HashMap<String, String>();
            Uteis.logar(null, "Inicia consultarListaConhecimentosTelaUCP ....");

            ExecuteRequestHttpService executeRequestHttpService = new ExecuteRequestHttpService();
            String executeRequest = executeRequestHttpService.executeRequestInner(Aplicacao.getProp(Aplicacao.myUpUrlBase) + "/prest/config/listaConhecimentosTelaUCP", params, "UTF-8");

            JSONObject jsonObject = new JSONObject(executeRequest);
            JSONArray lista = new JSONArray(jsonObject.get("return").toString());

            for (int e = 0; e < lista.length(); e++) {
                try {
                    JSONObject obj = lista.getJSONObject(e);

                    JSONArray listaPerguntas = new JSONArray(obj.get("perguntas").toString());

                    IntegracaoTagUcpTO integracaoTagUcpTO = new IntegracaoTagUcpTO();
                    integracaoTagUcpTO.setNome(obj.getString("tela"));
                    List<PerguntaUcpTO> listaUcp = new ArrayList<PerguntaUcpTO>();
                    for (int i = 0; i < listaPerguntas.length(); i++) {
                        JSONObject perg = listaPerguntas.getJSONObject(i);
                        PerguntaUcpTO perguntaUcpTO = new PerguntaUcpTO();
                        perguntaUcpTO.setCodigo(perg.getInt("codigo"));
                        perguntaUcpTO.setTitulo(perg.getString("titulo"));
                        listaUcp.add(perguntaUcpTO);
                    }
                    integracaoTagUcpTO.setPerguntas(listaUcp);

                    listaFinal.add(integracaoTagUcpTO);

                } catch (Exception ignored) {
                }
            }

            if (!UteisValidacao.emptyList(listaFinal)) {
                // descontinuado
//                UCPControle.listaTelaConhecimentosUCP = listaFinal;
            }

            Uteis.logar(null, "Finaliza consultarListaConhecimentosTelaUCP ....");
        } catch (Exception e) {
            Uteis.logar(e, ThreadUCP.class);
        }
    }

//    public static void main(String[] args) {
//        try {
//            ThreadUCP threadUCP = new ThreadUCP();
//            threadUCP.setDaemon(true);
//            threadUCP.run();
//        } catch (Exception ex) {
//            Uteis.logar(ex, ThreadUCP.class);
//        }
//    }
}
