/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.enumerador.wod;

import org.apache.commons.lang3.StringUtils;

/**
 *
 * <AUTHOR>
public enum CategoriaNivelWodEnum {

    PADRAO(0, "Padrão"),
    PADRAOPERSONALIZADO(1, "Padrão (personalizado)"),
    PERSONALIZADO(2, "Personalizado");
    private Integer id;
    private String descricao;

    private CategoriaNivelWodEnum(Integer id, final String descricao) {
        this.id = id;
        this.descricao = descricao;
    }

    public static CategoriaNivelWodEnum getInstance(String codigo) {
        if (!StringUtils.isBlank(codigo)) {
            for (CategoriaNivelWodEnum categoriaNivelWodEnum : CategoriaNivelWodEnum.values()) {
                if (codigo.equals(categoriaNivelWodEnum)) {
                    return categoriaNivelWodEnum;
                }
            }
        }
        return null;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public static CategoriaNivelWodEnum getFromId(Integer id) {
        for (CategoriaNivelWodEnum tipo : values()) {
            if (tipo.getId().equals(id)) {
                return tipo;
            }
        }
        return null;
    }
}
