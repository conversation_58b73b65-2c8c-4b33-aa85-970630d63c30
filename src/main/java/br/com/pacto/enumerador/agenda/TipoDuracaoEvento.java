/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.enumerador.agenda;

/**
 *
 * <AUTHOR>
 */
public enum TipoDuracaoEvento {
    
    DURACAO_LIVRE,
    DURACAO_PREDEFINIDA,
    INTERVALO_DE_TEMPO;
    
    public static TipoDuracaoEvento getFromId(int id){
        for(TipoDuracaoEvento tt : values()){
            if(tt.ordinal() == id){
                return tt;
            }
        }
        return null;
    }
    
}
