/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pacto.enumerador.agenda;

/**
 *
 * <AUTHOR>
 */
public enum PeriodicidadeAgendamentoEnum {

    NAO_SE_REPETE(0,"Não se repete"),
    TODOS_OS_DIAS(1,"Todos os dias"),
    SEMANAL(2,"Revisão de treino"),
    MENSAL(3,"Mensal"),
    PERSONALIZADO(4,"Personalizado");

    private Integer id;
    private String descricao;


    private PeriodicidadeAgendamentoEnum(Integer id, String descricao){
        this.id = id;
        this.descricao = descricao;
    }


    
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
    
    public static PeriodicidadeAgendamentoEnum getFromId(Integer id){
        for(PeriodicidadeAgendamentoEnum periodicidade : values()){
            if(periodicidade.getId().equals(id)){
                return periodicidade;
            }
        }
        return null;
    }
    public static PeriodicidadeAgendamentoEnum getFromDescricaoSimples(String descricao){
        for(PeriodicidadeAgendamentoEnum periodicidade : values()){
            if(periodicidade.getDescricao().equals(periodicidade)){
                return periodicidade;
            }
        }
        return null;
    }
    
}
