/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pacto.enumerador.agenda;

/**
 *
 * <AUTHOR>
 */
public enum SemanaMesAgendamentoEnum {


    SEMANA(1,"Semana (s)"),
    MES(2,"Mês (s)");

    private Integer id;
    private String descricao;


    private SemanaMesAgendamentoEnum(Integer id, String descricao){
        this.id = id;
        this.descricao = descricao;
    }


    
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
    
    public static SemanaMesAgendamentoEnum getFromId(Integer id){
        for(SemanaMesAgendamentoEnum semanaMes : values()){
            if(semanaMes.getId().equals(id)){
                return semanaMes;
            }
        }
        return null;
    }
    public static SemanaMesAgendamentoEnum getFromDescricaoSimples(String descricao){
        for(SemanaMesAgendamentoEnum semanaMes : values()){
            if(semanaMes.getDescricao().equals(semanaMes)){
                return semanaMes;
            }
        }
        return null;
    }
    
}
