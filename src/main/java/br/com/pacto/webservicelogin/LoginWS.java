/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.webservicelogin;

import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.bean.professor.Professor<PERSON>int<PERSON><PERSON>;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.usuario.UsuarioJSON;
import br.com.pacto.email.UteisEmail;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.configuracoes.ConfiguracaoSistemaService;
import br.com.pacto.service.intf.usuario.UsuarioService;
import br.com.pacto.util.ConfigsEmail;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;

import java.util.logging.Level;
import java.util.logging.Logger;
import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebService;


/**
 *
 * <AUTHOR>
 */
@WebService(serviceName = "LoginWS")
public class LoginWS {

    /**
     * Operação de Web service
     */
    @WebMethod(operationName = "validarUsuario")
    public String validarUsuario(@WebParam(name = "key") String key, @WebParam(name = "usuario") String usuario, 
    @WebParam(name = "senha") String senha) {
        try {
            UsuarioService usuarioService = UtilContext.getBean(UsuarioService.class);
            Usuario u = usuarioService.validarUsuario(key, usuario, senha);
            UsuarioJSON uJSON = new UsuarioJSON(u, usuarioService.empresasApp(key, u));
            return uJSON.toJSON();
        } catch (ServiceException ex) {
            return "ERRO: " + ex.getMessage();
        }
    }

    @WebMethod(operationName = "recuperarLoginPorEmail")
    public String recuperarLoginPorEmail(@WebParam(name = "key") String key,
                                         @WebParam(name = "email") String email) throws Exception {

        try{
            Empresa empresa = new Empresa();

            UsuarioService usuarioService = (UsuarioService) UtilContext.getBean( UsuarioService.class);
            ConfiguracaoSistemaService cfgsService = (ConfiguracaoSistemaService) UtilContext.getBean(ConfiguracaoSistemaService.class);
            Usuario usuario = usuarioService.recuperarSenhaPorEmail(key, email);
            StringBuilder sql = new StringBuilder();
            StringBuilder assunto = new StringBuilder();

            assunto.append("Recuperação de Senha");

            Integer senha = UteisValidacao.gerarNumeroRandomico(1000,5000);
            sql.append("Usuario: ").append(usuario.getUserName());
            sql.append("<br> </b>");
            sql.append("Senha: ").append(senha);
            usuario.setSenha(Uteis.encriptar(senha.toString()));
            usuarioService.alterar(key,usuario);
            String emailUsuario = null;
            ProfessorSintetico pf = usuario.getProfessor();
            if(usuario.getUserName().contains("@")){
                emailUsuario = usuario.getUserName();
            }
            if(emailUsuario == null && pf != null && usuario !=null ){
                emailUsuario = pf.getEmail();
            }
            ConfigsEmail cfgEmail = cfgsService.obterConfiguracoes(key);
            UteisEmail envioEmail = new UteisEmail();
            envioEmail.preencherConfiguracaoEmailPadrao(cfgEmail);
            envioEmail.enviarEmail(emailUsuario,"",sql.toString(),"", assunto.toString());
            return "ok";
        } catch (ServiceException ex) {
            return "ERRO: " + ex.getMessage();
        }
    }


}

