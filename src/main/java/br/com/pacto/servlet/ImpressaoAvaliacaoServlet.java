package br.com.pacto.servlet;

import br.com.pacto.bean.anamnese.Anamnese;
import br.com.pacto.bean.avaliacao.*;
import br.com.pacto.bean.empresa.IdiomaBancoEnum;
import br.com.pacto.bean.usuario.Usuario;

import br.com.pacto.objeto.AvaliacaoFisicaIntegradaPDF;
import br.com.pacto.objeto.AvaliacaoFisicaPDF;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.intf.anamnese.AnamneseService;
import br.com.pacto.service.intf.avaliacao.AvaliacaoFisicaService;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;
import br.com.pacto.util.ViewUtils;
import br.com.pacto.util.impl.JSFUtilities;
import org.json.JSONObject;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.util.List;



public class ImpressaoAvaliacaoServlet extends HttpServlet {

    /**
     * @see HttpServlet#service(HttpServletRequest request, HttpServletResponse response)
     */
    protected void service(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        try {
            final String j = request.getParameter("j");
            String cript0p4r4msint = Uteis.desencriptar(j, "cript0p4r4msint");
            JSONObject params = new JSONObject(cript0p4r4msint);
            String ctx = params.getString("k");
            Integer avaliacao = params.getInt("a");
            IdiomaBancoEnum idiomaBanco;
            try {
                idiomaBanco = IdiomaBancoEnum.getFromOrdinal(params.getInt("i"));
            }catch (Exception ignore){
                idiomaBanco = IdiomaBancoEnum.getFromOrdinal(0);
            }

            AvaliacaoFisicaService avaliacaoService = (AvaliacaoFisicaService) UtilContext.getBean(AvaliacaoFisicaService.class);

            AvaliacaoFisica avaliacaoFisica = avaliacaoService.obterPorId(ctx, avaliacao);
            Flexibilidade flexibilidade = avaliacaoService.obterFlexibilidade(ctx, avaliacaoFisica.getCodigo());
            PesoOsseo pesoOsseo = avaliacaoService.obterPesoOsseo(ctx, avaliacaoFisica.getCliente().getCodigo(), avaliacaoFisica);
            ItemAvaliacaoFisica ventItem = avaliacaoService.obterItemAvaliacaoFisica(ctx, avaliacaoFisica.getCliente().getCodigo(),
                    ItemAvaliacaoFisicaEnum.VENTILOMETRIA, null, avaliacaoFisica);
            Ventilometria ventilometria = new Ventilometria();
            if (ventItem != null && !UteisValidacao.emptyNumber(ventItem.getCodigo())) {
                ventilometria = ventItem.getVentilometria();
            }
            Anamnese anamnese = null;
            Anamnese questionarioParq = null;

            AnamneseService as = (AnamneseService) UtilContext.getBean(AnamneseService.class);
            questionarioParq = as.consultarParq(ctx, 0, (ViewUtils) UtilContext.getBean(ViewUtils.class));
            questionarioParq.setPerguntas(as.obterPerguntasAnamnese(ctx, questionarioParq.getCodigo()));
            List<ItemAvaliacaoFisica> parqs = avaliacaoService.obterItensAvaliacaoFisica(ctx, avaliacaoFisica.getCliente().getCodigo(), ItemAvaliacaoFisicaEnum.ANAMNESE, 1, true,
                    avaliacaoFisica);
            if (!UteisValidacao.emptyList(parqs)) {
                avaliacaoService.obterRespostas(ctx, questionarioParq, parqs.get(0));
            }
            List<ItemAvaliacaoFisica> anamnesesAluno = avaliacaoService.obterItensAvaliacaoFisica(ctx, avaliacaoFisica.getCliente().getCodigo(),
                    ItemAvaliacaoFisicaEnum.ANAMNESE, 1, false, avaliacaoFisica);
            if (!UteisValidacao.emptyList(anamnesesAluno)) {
                anamnese = anamnesesAluno.get(0).getAnamnese();
                anamnese.setPerguntas(as.obterPerguntasAnamnese(ctx, anamnese.getCodigo()));
                avaliacaoService.obterRespostas(ctx, anamnese, anamnesesAluno.get(0));
            }

            String refPdf = avaliacaoService.gerarPDFAvaliacaoFisica(ctx, avaliacaoFisica,
                    flexibilidade,
                    pesoOsseo, ventilometria,
                    anamnese, questionarioParq,
                    null, (ViewUtils) UtilContext.getBean(ViewUtils.class),
                    request, request.getSession().getServletContext(), false, true, idiomaBanco.name());

            // setting some response headers
            response.setHeader("Expires", "0");
            response.setHeader("Cache-Control",
                    "must-revalidate, post-check=0, pre-check=0");
            response.setHeader("Pragma", "public");
            // setting the content type
            response.setContentType("application/pdf");
            response.sendRedirect(refPdf);
        }catch(Exception e) {
            throw new IOException(e.getMessage());
        }
    }
}
