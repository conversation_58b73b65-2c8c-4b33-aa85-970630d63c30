/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pacto.servlet;

import br.com.pacto.bean.configuracoes.ConfiguracoesEnum;
import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.bean.pessoa.Telefone;
import br.com.pacto.bean.professor.ProfessorSintetico;
import br.com.pacto.bean.usuario.Usuario;

import br.com.pacto.dao.intf.pessoa.TelefoneDao;
import br.com.pacto.objeto.*;
import br.com.pacto.service.intf.configuracoes.ConfiguracaoSistemaService;
import br.com.pacto.service.intf.empresa.EmpresaService;
import br.com.pacto.service.intf.pessoa.PessoaService;
import br.com.pacto.service.intf.professor.ProfessorSinteticoService;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;
import br.com.pacto.util.impl.JSFUtilities;
import org.json.JSONObject;


import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.logging.Level;
import java.util.logging.Logger;

import static br.com.pacto.util.impl.JSFUtilities.getKey;

/**
 *
 * <AUTHOR> Felipe 27/04/2018
 */
public class RedirectServlet extends HttpServlet {

    private static final String URL_WIKI = "https://wiki.pactosolucoes.com.br/content/index.php";
    public static final String Crypt_KEY = "P@cT0zIlLy0nW3b1";
    public static final AlgoritmoCriptoEnum Crypt_ALGORITM = AlgoritmoCriptoEnum.ALGORITMO_AES;

    /**
     * Processes requests for both HTTP
     * <code>GET</code> and
     * <code>POST</code> methods.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {

        response.setContentType("text/html;charset=UTF-8");
        try (PrintWriter out = response.getWriter()) {
            if (request.getParameter("up") != null) {
                Usuario user = (Usuario) request.getSession().getAttribute(JSFUtilities.LOGGED);
                if (user != null) {
                    try {
                        final String key = (String) request.getSession().getAttribute(JSFUtilities.KEY);

                        if (!UteisValidacao.emptyString(key)) {

                            JSONObject o = new JSONObject();

                            boolean treinoIndependente = false;
                            try {
                                treinoIndependente = request.getSession().getAttribute(JSFUtilities.TREINO_INDEPENDENTE) != null && (Boolean) request.getSession().getAttribute(JSFUtilities.TREINO_INDEPENDENTE);
                            } catch (Exception ignored) {
                            }
                            o.put("treinoIndependente", treinoIndependente);

                            Empresa empresa = null;
                            try {
                                if (treinoIndependente) {
                                    EmpresaService empresaService = UtilContext.getBean(EmpresaService.class);
                                    empresa = empresaService.obterEmpresaTreinoIndependente(key);
                                } else {
                                    empresa = Empresa.copyAttributes(new Empresa(), user.getEmpresaLogada());
                                }
                                o.put("nomeEmpresa", empresa.getNome());
                            } catch (Exception e) {
                                o.put("nomeEmpresa", "EMPRESA");
                            }

                            try {
                                PessoaService pessoaService = UtilContext.getBean(PessoaService.class);
                                user.getProfessor().getPessoa().setTelefones(new ArrayList<Telefone>());
                                user.getProfessor().getPessoa().getTelefones().addAll(pessoaService.obterTelefonesPorPessoa(key, user.getProfessor().getPessoa().getCodigo()));
                                o.put("telefones", user.getProfessor().getPessoa().getTelefones(true));
                            } catch (Exception ex) {
                                o.put("telefones", "");
                            }

                            o.put("key", key);
                            o.put("userName", user.getUserName());
                            o.put("codigoUsuario", user.getCodigo()); //utilizado na UCP
                            o.put("nomeCompleto", user.getProfessor().getNome());
                            o.put("origemSistema", 1); //ENUM DA UCP PARA IDENTIFICAR QUE SUA ORIGEM É TREINO WEB
                            o.put("timevld", Calendario.getInstance(Calendario.hoje().getTime() + ((60 * 30) * 1000)).getTimeInMillis());
                            if (!UteisValidacao.emptyString(user.getProfessor().getPessoa().getFotoKey())) {
                                o.put("urlFoto", Aplicacao.obterUrlFotoDaNuvem(user.getProfessor().getPessoa().getFotoKey()));
                            }


                            String paginaAtualZW = request.getParameter("paginaAtualZW");
                            if (paginaAtualZW != null && !paginaAtualZW.isEmpty()) {
                                o.put("paginaAtualZW", paginaAtualZW);
                            } else {
                                o.put("paginaAtualZW", "");
                            }

                            String codPerguntaUCP = request.getParameter("codPerguntaUCP");
                            if (codPerguntaUCP != null && !codPerguntaUCP.isEmpty()) {
                                o.put("codPerguntaUCP", codPerguntaUCP);
                            } else {
                                o.put("codPerguntaUCP", "");
                            }

                            String visualizacaoUCP = request.getParameter("visualizacaoUCP");
                            if (visualizacaoUCP != null && !visualizacaoUCP.isEmpty()) {
                                o.put("visualizacaoUCP", visualizacaoUCP);
                            } else {
                                o.put("visualizacaoUCP", "");
                            }

                            String solicitacao = request.getParameter("solicitacao");
                            if (solicitacao != null && !solicitacao.isEmpty()) {
                                o.put("solicitacao", solicitacao);
                            } else {
                                o.put("solicitacao", "false");
                            }

                            String ranking = request.getParameter("ranking");
                            if (ranking != null && !ranking.isEmpty()) {
                                o.put("ranking", ranking);
                            } else {
                                o.put("ranking", "false");
                            }

                            String duvida = request.getParameter("duvida");
                            if (duvida != null && !duvida.isEmpty()) {
                                try {
                                    duvida = Criptografia.decrypt(duvida, Crypt_KEY, Crypt_ALGORITM);
                                } catch (Exception ex) {

                                }
                                o.put("duvida", duvida);
                            } else {
                                o.put("duvida", "");
                            }

                            prepararInformacoesService(key, o, empresa);

                            final String lgn = Uteis.encriptar(o.toString(), "chave_login_unificado");
                            response.sendRedirect(String.format("%s/oid?lgn=%s",
                                    Aplicacao.getProp(Aplicacao.myUpUrlBase),
                                    lgn));
                        } else {
                            out.println("<!DOCTYPE html>");
                            out.println("<html>");
                            out.println("<head>");
                            out.println("<title>Universidade Pacto</title>");
                            out.println("</head>");
                            out.println("<body>");
                            out.println("<h2>Seu usuário não pôde logar na Universidade Corporativa Pacto porque não está alocado em nenhuma empresa do Treino Web</h2>");
                            out.println("</body>");
                            out.println("</html>");
                        }
                    } catch (Exception ex) {
                        Logger.getLogger(RedirectServlet.class.getName()).log(Level.SEVERE, null, ex);
                    }
                }
            }
        }
    }

    private void prepararInformacoesService(String key, JSONObject jsonObject, Empresa empresa) throws Exception {

        ConfiguracaoSistemaService configuracaoSistemaService = UtilContext.getBean(ConfiguracaoSistemaService.class);
        String usuarioService = configuracaoSistemaService.consultarPorTipo(key,ConfiguracoesEnum.USUARIO_SERVICE).getValor();
        String senhaService = configuracaoSistemaService.consultarPorTipo(key,ConfiguracoesEnum.SENHA_SERVICE).getValor();
        String telefoneEmpresa = configuracaoSistemaService.consultarPorTipo(key,ConfiguracoesEnum.TELEFONE_SERVICE).getValor();

        String email  = configuracaoSistemaService.consultarPorTipo(key, ConfiguracoesEnum.EMAIL_PADRAO).getValor();
        if (!UteisValidacao.emptyString(senhaService)) {
            senhaService = Criptografia.decrypt(senhaService, Crypt_KEY, Crypt_ALGORITM);
        }

        if (empresa != null && !UteisValidacao.emptyString(empresa.getEmail())) {
            email = empresa.getEmail();
        }

        jsonObject.put("email", email)
                .put("telefoneEmpresa", telefoneEmpresa)
                .put("usuarioService", usuarioService)
                .put("senhaService", senhaService)
                .put("serviceEmpresa", true)
                .put("key", key)
                .put("codigoEmpresa", empresa.getCodZW() == null ? empresa.getCodigo() : empresa.getCodZW())
                .put("nomeFantasia", empresa.getNome());
    }

    // <editor-fold defaultstate="collapsed" desc="HttpServlet methods. Click on the + sign on the left to edit the code.">
    /**
     * Handles the HTTP
     * <code>GET</code> method.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Handles the HTTP
     * <code>POST</code> method.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Returns a short description of the servlet.
     *
     * @return a String containing servlet description
     */
    @Override
    public String getServletInfo() {
        return "Short description";
    }// </editor-fold>
}
