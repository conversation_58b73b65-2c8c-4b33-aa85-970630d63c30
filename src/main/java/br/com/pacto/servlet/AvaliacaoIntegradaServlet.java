package br.com.pacto.servlet;

import br.com.pacto.bean.avaliacao.ItemAvaliacaoFisica;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.objeto.AvaliacaoFisicaIntegradaPDF;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.intf.avaliacao.AvaliacaoFisicaService;
import br.com.pacto.service.intf.usuario.UsuarioService;
import br.com.pacto.util.UtilContext;
import br.com.pacto.util.ViewUtils;
import br.com.pacto.util.impl.JSFUtilities;
import org.json.JSONObject;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.List;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;


public class AvaliacaoIntegradaServlet extends HttpServlet {

    /**
     * @see HttpServlet#service(HttpServletRequest request, HttpServletResponse response)
     */
    protected void service(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        // Aqui
        boolean isComparativo =  Boolean.valueOf(request.getParameter("isComparativo"));

        // Cliente

        try {
            final String j = request.getParameter("j");
            String key = (String) request.getSession().getAttribute(JSFUtilities.KEY);
            String cript0p4r4msint = Uteis.desencriptar(j, "cript0p4r4msint");
            JSONObject params = new JSONObject(cript0p4r4msint);
            if (key == null && !params.isNull("k")) {
                key = params.getString("k");
            }
            AvaliacaoFisicaService service = (AvaliacaoFisicaService) UtilContext.getBean(AvaliacaoFisicaService.class);
            UsuarioService usuarioService = (UsuarioService) UtilContext.getBean(UsuarioService.class);
            Usuario usuario = (Usuario) request.getSession().getAttribute(JSFUtilities.LOGGED);
            if (usuario == null && !params.isNull("u")) {
                usuario = usuarioService.obterPorId(key, params.getInt("u"));
            }
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            if (!isComparativo){
                ItemAvaliacaoFisica itemAvaliacaoFisica = service.montarVisualizacao(key, params.getInt("i"));

                AvaliacaoFisicaIntegradaPDF.criarPDFAvaliacaoIntegrada(key, usuario, itemAvaliacaoFisica, UtilContext.getBean(ViewUtils.class),
                        getServletContext(), baos);
            }else {
                List<Integer> codigos = new ArrayList<Integer>();
                for (String i : params.getString("i").split("_")){
                    codigos.add(Integer.parseInt(i));
                }

                List<ItemAvaliacaoFisica> itensAvaliacaoFisica = new ArrayList<ItemAvaliacaoFisica>();
                for (Integer i : codigos){
                    ItemAvaliacaoFisica itemAvaliacaoFisicaComp = service.montarVisualizacao(key,i);
                    itensAvaliacaoFisica.add(itemAvaliacaoFisicaComp);
                }
                AvaliacaoFisicaIntegradaPDF.criarPDFComparacaoAvaliacaoIntegrada(key, usuario, itensAvaliacaoFisica, UtilContext.getBean(ViewUtils.class),
                        getServletContext(), baos);
            }

            // setting some response headers
            response.setHeader("Expires", "0");
            response.setHeader("Cache-Control",
                    "must-revalidate, post-check=0, pre-check=0");
            response.setHeader("Pragma", "public");
            // setting the content type
            response.setContentType("application/pdf");
            // the contentlength
            response.setContentLength(baos.size());
            // write ByteArrayOutputStream to the ServletOutputStream
            OutputStream os = response.getOutputStream();
            baos.writeTo(os);
            os.flush();
            os.close();
        }
        catch(Exception e) {
            throw new IOException(e.getMessage());
        }
    }

    /**
     * Serial version UID.
     */
    private static final long serialVersionUID = 6067021675155015602L;

}
