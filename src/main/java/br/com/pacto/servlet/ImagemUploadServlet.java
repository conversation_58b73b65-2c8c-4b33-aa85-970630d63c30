package br.com.pacto.servlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;

/**
 * Created by alcides on 13/10/2017.
 */

public class ImagemUploadServlet extends HttpServlet {

    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        String id = request.getParameter("id");
        response.setContentType("image/jpg");
        OutputStream o = response.getOutputStream();
        o.write((byte[]) request.getSession().getAttribute(id));
        o.flush();
        o.close();
    }
}
