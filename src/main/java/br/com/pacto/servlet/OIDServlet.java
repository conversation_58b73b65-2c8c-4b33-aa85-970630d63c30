    /*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pacto.servlet;

    import br.com.pacto.bean.cliente.ClienteSintetico;
    import br.com.pacto.bean.configuracoes.ConfiguracaoSistema;
    import br.com.pacto.bean.configuracoes.ConfiguracoesEnum;
    import br.com.pacto.bean.empresa.Empresa;
    import br.com.pacto.bean.professor.ProfessorSintetico;
    import br.com.pacto.bean.usuario.Usuario;
    import br.com.pacto.objeto.Aplicacao;
    import br.com.pacto.objeto.Calendario;
    import br.com.pacto.objeto.Uteis;
    import br.com.pacto.service.intf.cliente.ClienteSinteticoService;
    import br.com.pacto.service.intf.configuracoes.ConfiguracaoSistemaService;
    import br.com.pacto.service.intf.empresa.EmpresaService;
    import br.com.pacto.service.intf.professor.Professor<PERSON><PERSON><PERSON><PERSON>Ser<PERSON>;
    import br.com.pacto.service.intf.usuario.UsuarioService;
    import br.com.pacto.util.UteisValidacao;
    import br.com.pacto.util.UtilContext;
    import br.com.pacto.util.impl.JSFUtilities;
    import org.json.JSONException;
    import org.json.JSONObject;
    import org.springframework.beans.factory.annotation.Autowired;
    import servicos.integracao.adm.client.EmpresaWS;
    import servicos.integracao.zw.IntegracaoCadastrosWSConsumer;
    import servicos.integracao.zw.json.AddClienteJSON;

    import javax.servlet.ServletException;
    import javax.servlet.http.HttpServlet;
    import javax.servlet.http.HttpServletRequest;
    import javax.servlet.http.HttpServletResponse;
    import java.io.IOException;
    import java.io.PrintWriter;
    import java.util.ArrayList;
    import java.util.Calendar;
    import java.util.List;
    import java.util.logging.Level;
    import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
public class OIDServlet extends HttpServlet {

    public static final String URL_ZW_DIFERENTE_TREINO = "urlZwDiferenteTreino";
    public static final String URL_ZW_LOCAL = "urlZwLocal";

    @Autowired
    private UsuarioService usuarioService;

    /**
     * Processes requests for both HTTP
     * <code>GET</code> and
     * <code>POST</code> methods.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    protected void processRequest(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        response.setContentType("text/html;charset=UTF-8");
        PrintWriter out = response.getWriter();
        String timevld;
        try {
            String lgn = request.getParameter("lgn");
            if (lgn != null) {
                lgn = Uteis.desencriptar(lgn, "chave_login_unificado");
            }
            JSONObject json = new JSONObject(lgn);
            final String key = json.getString(JSFUtilities.KEY);
            final String codUsuario = json.getString("idUserZW");
            final String codUsuarioTr = json.getString("idUserTR");
            String codEmpresa;
            try {
                codEmpresa = json.getString("idEmpresa");
            }catch (Exception e){
                codEmpresa = String.valueOf(json.getInt("idEmpresa"));
            }

            final String login = json.getString("login");
            Integer sessionId;
            String ip = "N/C";
            String userOamd;
            String modulo = "";
            String redirect = "";
            String matricula = "";
            boolean incluirAluno = false;
            Integer codClienteAbrirTreino = 0;
            try {
                modulo = json.getString("modulo");
            } catch (Exception e) {
            }
            try {
                sessionId = json.getInt("sessionId");
                request.getSession().setAttribute("sessionId", sessionId);
            } catch (Exception e) {
            }
            try {
                timevld = json.getString("timevld");
            } catch (Exception e) {
                timevld = "";
            }
            try {
                userOamd = json.getString("userOamd");
            } catch (Exception e) {
                userOamd = "";
            }
            try {
                ip = json.getString("ip");
            } catch (Exception e) {
            }
            try {
                redirect = json.getString("urlRedirect");
            } catch (Exception e) {
            }
            try {
                matricula = json.getString("matricula");
                incluirAluno = json.getBoolean("incluirAluno");

                if (!UteisValidacao.emptyString(matricula)) {

                    ClienteSinteticoService clienteSinteticoService = (ClienteSinteticoService) UtilContext.getBean(ClienteSinteticoService.class);
                    ClienteSintetico cliente = clienteSinteticoService.consultarPorMatricula(key, matricula);

                    if (cliente == null && incluirAluno) {

                        IntegracaoCadastrosWSConsumer integracaoWS = (IntegracaoCadastrosWSConsumer) UtilContext.getBean(IntegracaoCadastrosWSConsumer.class);
                        ProfessorSinteticoService professorSinteticoService = (ProfessorSinteticoService) UtilContext.getBean(ProfessorSinteticoService.class);

                        final String url = Aplicacao.getProp(key, Aplicacao.urlZillyonWebInteg);
                        List<AddClienteJSON> clientes = integracaoWS.consultarClientesZW(url, key, Integer.parseInt(codEmpresa), "", "", Integer.parseInt(matricula));
                        if (UteisValidacao.emptyList(clientes) || clientes.size() > 1) {
                            throw new Exception("Erro ao incluir aluno no TreinoWeb.");
                        }

                        ClienteSintetico clienteEscolhido = integracaoWS.consultarClienteSintetico(url, key, clientes.get(0).getCodigoCliente());

                        List<ProfessorSintetico> todosProfessores = professorSinteticoService.consultarProfessores(key, clienteEscolhido.getEmpresa());
                        if (UteisValidacao.emptyList(todosProfessores)) {
                            throw new Exception("Nenhum professor cadastrado!");
                        }
                        EmpresaService empresaSer = UtilContext.getBean(EmpresaService.class);
                        Empresa empresa = empresaSer.obterPorId(clienteEscolhido.getKey(),clienteEscolhido.getEmpresa());
                        String retorno = clienteSinteticoService.concluirCadastro(key, clienteEscolhido, new ArrayList<>(), integracaoWS,
                                "", "", todosProfessores.get(0).getCodigoColaborador(), false, Integer.parseInt(codUsuario),
                                empresa == null ? "false" : empresa.getNome());
                        if (retorno != null && retorno.toUpperCase().contains("SUCESSO")) {
                            cliente = clienteSinteticoService.consultarSimplesPorCodigoCliente(key, clienteEscolhido.getCodigoCliente());
                            codClienteAbrirTreino = cliente.getCodigo();
                        }


                    } else if (cliente != null) {

                        codClienteAbrirTreino = cliente.getCodigo();

                    }
                }
            } catch (Exception ignored) {
            }

            final String urlLogin;
            try {
                urlLogin= json.getString("urlLogin");
                request.getSession().setAttribute("urlLogin", urlLogin);
            } catch (Exception ignored) {
            }

            try {
                if (json.getBoolean(URL_ZW_DIFERENTE_TREINO)) {
                    request.getSession().setAttribute(URL_ZW_DIFERENTE_TREINO, true);
                    request.getSession().setAttribute(URL_ZW_LOCAL, json.getString(URL_ZW_LOCAL));
                }
            } catch (Exception ignored) {
                request.getSession().setAttribute(URL_ZW_DIFERENTE_TREINO, false);
            }

            if ((codUsuario != null && !codUsuario.isEmpty())
                    || (codUsuarioTr != null && !codUsuarioTr.isEmpty())) {
                try {
                    if (!timevld.equals("") && Long.parseLong(timevld) < Calendar.getInstance().getTimeInMillis()) {
                        throw new Exception("Validação do usuário expirada, favor retorne ao Login e informe novamente senha e usuário");
                    }
                    UsuarioService us = (UsuarioService) UtilContext.getBean(UsuarioService.class);
                    List<Usuario> users = us.obterListaPorAtributo(key,
                            (codUsuario == null || codUsuario.equals("0")) ? "codigo" : "usuarioZW",
                            (codUsuario == null || codUsuario.equals("0")) ? Integer.valueOf(codUsuarioTr) : Integer.valueOf(codUsuario));

                    Usuario usuario = null;

                    if (users != null && !users.isEmpty()) {
                        usuario = us.prepararUsuario(users.get(0), key, "");
                    }
                    if (usuario != null) {
                        if (usuario.getEmpresasZW() != null) {
                            if (!UteisValidacao.emptyString(codEmpresa)) {
                                for (EmpresaWS empresaWs : usuario.getEmpresasZW()) {
                                    if (empresaWs.getCodigo().toString().equals(codEmpresa)) {
                                        usuario.setEmpresaLogada(empresaWs);
                                        usuario.setEmpresaZW(empresaWs.getCodigo());
                                        if(empresaWs.isBloqueioTemporario()){
                                            response.sendError(HttpServletResponse.SC_BAD_REQUEST);
                                            return;
                                        }
                                    }
                                }
                            }
                        }
                        if (usuario.getCodigo() != null && usuario.getCodigo() > 0) {
                            us.verificarUsoModulos(key, usuario);
                            String mod = Aplicacao.getProp(key,Aplicacao.modulos);
                            boolean ind = mod != null && !mod.contains("ZW");
                            request.getSession().setAttribute(JSFUtilities.TREINO_INDEPENDENTE, ind);
                            request.getSession().setAttribute(JSFUtilities.TZ, usuario.getEmpresaDefault() == null
                                    ? ""
                                    : usuario.getEmpresaDefault().getTimeZoneDefault());

                            try {
                                EmpresaService empresaSer = UtilContext.getBean(EmpresaService.class);
                                int codEmpresaInt = Integer.parseInt(codEmpresa);
                                Empresa empresa;
                                if (UteisValidacao.emptyNumber(codEmpresaInt) && ind) {
                                    empresa = empresaSer.obterTodos(key).get(0);
                                } else {
                                    empresa = empresaSer.obterPorId(key, codEmpresaInt);
                                }
                                if (empresa.getDataExpiracao() != null) {
                                    long quantidadeDiasExpirar = Uteis.nrDiasEntreDatas(Calendario.hoje(), empresa.getDataExpiracao());
                                    if (quantidadeDiasExpirar == 0) {
                                        if (Calendario.maiorOuIgual(Calendario.hoje(), Uteis.getDataHoraJDBC(Calendario.hoje(), "12:00"))) {
                                            quantidadeDiasExpirar += -1L;
                                        }
                                    }
                                    request.getSession().setAttribute(JSFUtilities.DIAS_PARA_EXPIRAR, (int) quantidadeDiasExpirar);
                                }
                            } catch (Exception ignored) {

                            }

                            ConfiguracaoSistemaService css = UtilContext.getBean(ConfiguracaoSistemaService.class);
                            try {
                                ConfiguracaoSistema cfgIntegracao = css.consultarPorTipo(key, ConfiguracoesEnum.INTEGRACAO_SISTEMA_OLYMPIA);
                                request.getSession().setAttribute(JSFUtilities.INTEGRACAO_OLYMPIA, cfgIntegracao.getValorAsBoolean());
                            } catch (Exception ignored) {
                            }

                            try {
                                ConfiguracaoSistema cfgMontagem = css.consultarPorTipo(key, ConfiguracoesEnum.USAR_NOVA_MONTAGEM);
                                request.getSession().setAttribute(JSFUtilities.USAR_NOVA_MONTAGEM, cfgMontagem.getValorAsBoolean());
                            } catch (Exception ignored) {
                            }
                            request.getSession().setAttribute(JSFUtilities.LOGGED, usuario);
                            request.getSession().setAttribute(JSFUtilities.KEY, key);
                            request.getSession().setAttribute(JSFUtilities.USER_OAMD, userOamd);
                            request.getSession().setAttribute(JSFUtilities.IP_ADDR, ip);
                            if (login != null && !login.isEmpty()) {
                                request.getSession().setAttribute("vindoLogin", true);
                                if (modulo != null && modulo.equals("aulacheia")) {
                                    request.getSession().setAttribute("moduloAula", Boolean.TRUE);
                                } else {
                                    request.getSession().setAttribute("moduloAula", Boolean.FALSE);
                                }
                            }
                        }
                    }
                } catch (Exception ex) {
                    Logger.getLogger(OIDServlet.class.getName()).log(Level.SEVERE, null, ex);
                }

                if (!UteisValidacao.emptyNumber(codClienteAbrirTreino)) {
                    response.sendRedirect(request.getContextPath() + "/aluno/" + codClienteAbrirTreino);
                } else if(redirect.equals(Uteis.URI_REDIRECT_BI_TREINO)){
                    response.sendRedirect(request.getContextPath() + "/BI");
                } else {
                    response.sendRedirect(request.getContextPath() + "/inicio");
                }
            }
        } catch (JSONException ex) {
            Logger.getLogger(OIDServlet.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            out.close();
        }
    }

    // <editor-fold defaultstate="collapsed" desc="HttpServlet methods. Click on the + sign on the left to edit the code.">
    /**
     * Handles the HTTP
     * <code>GET</code> method.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Handles the HTTP
     * <code>POST</code> method.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Returns a short description of the servlet.
     *
     * @return a String containing servlet description
     */
    @Override
    public String getServletInfo() {
        return "Short description";
    }// </editor-fold>
}
