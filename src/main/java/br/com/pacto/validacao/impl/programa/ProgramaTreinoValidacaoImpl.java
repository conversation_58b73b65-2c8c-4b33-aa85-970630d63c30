/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.validacao.impl.programa;

import br.com.pacto.bean.configuracoes.ConfiguracaoSistema;
import br.com.pacto.bean.configuracoes.ConfiguracoesEnum;
import br.com.pacto.bean.programa.ProgramaTreino;
import br.com.pacto.bean.programa.ProgramaTreinoFicha;
import br.com.pacto.bean.programa.TipoExecucaoEnum;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.service.intf.Validacao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.exception.ValidacaoException;
import br.com.pacto.service.intf.configuracoes.ConfiguracaoSistemaService;
import br.com.pacto.service.intf.programa.ProgramaTreinoService;
import br.com.pacto.validacao.intf.programa.ProgramaTreinoValidacao;
import java.util.ArrayList;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR>
 */
@Service
public class ProgramaTreinoValidacaoImpl implements ProgramaTreinoValidacao {

    @Autowired
    private Validacao validacao;
    @Autowired
    private ProgramaTreinoService ps;
    @Autowired
    private ConfiguracaoSistemaService css;

    @Override
    public void validarDadosBasicos(final String ctx, ProgramaTreino objeto, Boolean verificarConfiguracaoSistema) throws ValidacaoException {
        List<String> msgs = new ArrayList<String>();
        boolean predefinido = objeto.getPreDefinido() != null && objeto.getPreDefinido() ? true : false;
        if (validacao.emptyString(objeto.getNome())) {
            msgs.add("obrigatorio.nome");
        }
        if (!predefinido && validacao.isNull(objeto.getDataInicio())) {
            msgs.add("obrigatorio.dataInicio");
        }
        if (validacao.isNull(objeto.getDiasPorSemana()) || objeto.getDiasPorSemana() <= 0)  {
            msgs.add("obrigatorio.diasPorSemana");
        }
        if (validacao.isNull(objeto.getDiasPorSemana()) || objeto.getDiasPorSemana() > 7) {
            msgs.add("obrigatorio.diasSemanaMaior7");
        }
        if (!predefinido && validacao.isNull(objeto.getDataProximaRevisao())) {
            msgs.add("obrigatorio.dataProximaRevisao");
        }
        if (!predefinido && validacao.isNull(objeto.getDataTerminoPrevisto())) {
            msgs.add("obrigatorio.dataTermino");
        }
        if (validacao.isNull(objeto.getProfessorMontou()) || validacao.isNull(objeto.getProfessorMontou().getCodigo())
                || objeto.getProfessorMontou().getCodigo().intValue() == 0) {
            msgs.add("obrigatorio.professorMontou");
        }
        if (!predefinido && Calendario.maior(objeto.getDataInicio(), objeto.getDataTerminoPrevisto())) {
            msgs.add("datainicio.maior.datafim");
        }
        if (verificarConfiguracaoSistema) {
            try {
                ConfiguracaoSistema cfgAlunosAtivos = css.consultarPorTipo(ctx, ConfiguracoesEnum.PERMITIR_APENAS_ALUNOS_ATIVOS);
                if (validacao.emptyNumber(objeto.getCodigo())) {
                    if ((objeto.getCliente().getSituacao().equals("VI")
                            || objeto.getCliente().getSituacaoContrato().equals("DE")
                            || objeto.getCliente().getSituacaoContrato().equals("CA")) && cfgAlunosAtivos.getValorAsBoolean()) {
                        msgs.add("obrigatorio.alunoSemSituacaoCompativel");
                    }
                }
            } catch (ServiceException ex) {
                msgs.add(ex.getMessage());
            }
        }
        
        if (msgs.isEmpty()) {
            return;
        }
        throw new ValidacaoException(msgs);
    }

    @Override
    public void validarConcomitante(ProgramaTreino objeto, String key) throws ValidacaoException, ServiceException {
        List<ProgramaTreino> programasConc = ps.obterProgramasPorCliente(key, objeto.getCliente().getCodigo(), objeto.getDataInicio(), objeto.getDataTerminoPrevisto(), objeto.getCodigo(), null);
        if (!programasConc.isEmpty()) {
            throw new ValidacaoException("concomitante.programa");
        }

    }

    @Override
    public void validarInsercao(ProgramaTreino objeto, String key, Boolean verificarConfiguracaoSistema) throws ValidacaoException, ServiceException {
        if(objeto.getCliente() == null){
            return;
        }
        validarDadosBasicos(key,objeto, verificarConfiguracaoSistema);
        boolean predefinido = objeto.getPreDefinido() != null && objeto.getPreDefinido() ? true : false;
        if (!predefinido) {
            validarConcomitante(objeto, key);
        }
    }

    @Override
    public String validarFicha(ProgramaTreino objeto, String key, Integer codigoProgFicha, String nomeFicha,
            boolean somenteNr, TipoExecucaoEnum execucaoEnum, boolean adicionandoFichaPreDefinida) throws ServiceException, ValidacaoException {
        List<String> msgs = new ArrayList<String>();
        List<ProgramaTreinoFicha> fichas = ps.obterFichaPorPrograma(key, objeto.getCodigo(), codigoProgFicha, true);
        if (!somenteNr) {
            if (execucaoEnum == null && !adicionandoFichaPreDefinida) {
                msgs.add("validacao.tipoexecucao");
            }
            for (ProgramaTreinoFicha ficha : fichas) {
                if (ficha.getFicha().getNome().equals(nomeFicha)) {
                    if (adicionandoFichaPreDefinida) {
                        nomeFicha = mudarNome(fichas, nomeFicha);
                    } else {
                        msgs.add("validacao.ficha.nomeunico");
                        break;
                    }

                }
            }
        }

        if (msgs.isEmpty()) {
            return nomeFicha;
        }
        throw new ValidacaoException(msgs);
    }

    private String mudarNome(List<ProgramaTreinoFicha> fichas, String nome) {
        int numero = 0;
        String novoNome = "";
        boolean igual;
        do {
            numero++;
            novoNome = nome + " " + numero;
            igual = false;
            for (ProgramaTreinoFicha ficha : fichas) {
                if (ficha.getNomeFicha().equals(novoNome)) {
                    igual = true;
                }
            }
        } while (igual);
        return novoNome;
    }

    public Validacao getValidacao() {
        return validacao;
    }

    public void setValidacao(Validacao validacao) {
        this.validacao = validacao;
    }
}
