/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.validacao.intf.programa;

import br.com.pacto.bean.programa.ProgramaTreino;
import br.com.pacto.bean.programa.TipoExecucaoEnum;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.exception.ValidacaoException;
import br.com.pacto.service.intf.Validacao;

/**
 *
 * <AUTHOR>
 */
public interface ProgramaTreinoValidacao{
    
    void validarDadosBasicos(final String key, ProgramaTreino objeto, Boolean verificarConfiguracaoSistema) throws ValidacaoException;

    void validarConcomitante(ProgramaTreino objeto, String key) throws ValidacaoException, ServiceException;

    void validarInsercao(ProgramaTreino objeto, String key, Boolean verificarConfiguracaoSistema) throws ValidacaoException, ServiceException;

    String validarFicha(ProgramaTreino objeto, String key, Integer codigo<PERSON>rog<PERSON>ich<PERSON>, String nomeFicha,
     boolean somenteNr, TipoExecucaoEnum execucaoEnum, boolean adicionandoFichaPreDefinida) throws ServiceException,ValidacaoException;

    Validacao getValidacao();
    
}
