/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.validacao.intf.ficha;

import br.com.pacto.bean.ficha.Ficha;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.exception.ValidacaoException;

/**
 *
 * <AUTHOR>
 */
public interface FichaValidacaoService {
    public void validarNomeFichaPreDefinida(String key, Integer codigo, String nome) throws ValidacaoException, ServiceException;
    
    public void validarDadosBasicos(Ficha ficha) throws ValidacaoException;
}
