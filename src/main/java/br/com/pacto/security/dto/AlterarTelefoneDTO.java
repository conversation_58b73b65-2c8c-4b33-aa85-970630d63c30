package br.com.pacto.security.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class AlterarTelefoneDTO {

    private String chave;
    private String username;
    private Integer usuario_zw;
    private Integer usuario_tw;
    private String telefone;

    public String getChave() {
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    public Integer getUsuario_zw() {
        return usuario_zw;
    }

    public void setUsuario_zw(Integer usuario_zw) {
        this.usuario_zw = usuario_zw;
    }

    public Integer getUsuario_tw() {
        return usuario_tw;
    }

    public void setUsuario_tw(Integer usuario_tw) {
        this.usuario_tw = usuario_tw;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getTelefone() {
        return telefone;
    }

    public void setTelefone(String telefone) {
        this.telefone = telefone;
    }
}
