package br.com.pacto.security.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * DTO de Login no sistema
 *
 * <AUTHOR>
 * @since 19/07/2018
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UsuarioLoginV2DTO {

    private String chave;
    private Integer usuario_zw;
    private Integer usuario_tw;
    private String usuario_username;

    public String getChave() {
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    public Integer getUsuario_zw() {
        return usuario_zw;
    }

    public void setUsuario_zw(Integer usuario_zw) {
        this.usuario_zw = usuario_zw;
    }

    public Integer getUsuario_tw() {
        return usuario_tw;
    }

    public void setUsuario_tw(Integer usuario_tw) {
        this.usuario_tw = usuario_tw;
    }

    public String getUsuario_username() {
        return usuario_username;
    }

    public void setUsuario_username(String usuario_username) {
        this.usuario_username = usuario_username;
    }
}
