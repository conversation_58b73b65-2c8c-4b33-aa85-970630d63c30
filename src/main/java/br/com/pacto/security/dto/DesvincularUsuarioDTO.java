package br.com.pacto.security.dto;

import br.com.pacto.service.login.TokenSolicitacaoDTO;
import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class DesvincularUsuarioDTO {
    private String idUsarioGeral;
    private Integer codigoZw;
    private Integer codigoTw;
    private Integer idUsuarioResponsavel;
    private TokenSolicitacaoDTO tokenSolicitacaoDTO;
    private String chaveDesvincular;

    public String getIdUsarioGeral() {
        return idUsarioGeral;
    }

    public void setIdUsarioGeral(String idUsarioGeral) {
        this.idUsarioGeral = idUsarioGeral;
    }

    public Integer getIdUsuarioResponsavel() {
        return idUsuarioResponsavel;
    }

    public void setIdUsuarioResponsavel(Integer idUsuarioResponsavel) {
        this.idUsuarioResponsavel = idUsuarioResponsavel;
    }

    public TokenSolicitacaoDTO getTokenSolicitacaoDTO() {
        return tokenSolicitacaoDTO;
    }

    public void setTokenSolicitacaoDTO(TokenSolicitacaoDTO tokenSolicitacaoDTO) {
        this.tokenSolicitacaoDTO = tokenSolicitacaoDTO;
    }

    public String getChaveDesvincular() {
        return chaveDesvincular;
    }

    public void setChaveDesvincular(String chaveDesvincular) {
        this.chaveDesvincular = chaveDesvincular;
    }

    public Integer getCodigoZw() {
        return codigoZw;
    }

    public void setCodigoZw(Integer codigoZw) {
        this.codigoZw = codigoZw;
    }

    public Integer getCodigoTw() {
        return codigoTw;
    }

    public void setCodigoTw(Integer codigoTw) {
        this.codigoTw = codigoTw;
    }
}
