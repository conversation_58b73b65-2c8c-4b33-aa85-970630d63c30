package br.com.pacto.security.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class UsuarioAppDTO {

    private String chave;
    private String userName;
    private String idClienteApp;
    private String versaoDoApp;
    private String appUtilizado;
    private Integer codUsuario;
    private Integer matricula;
    private Integer codEmpresa;

    public String getChave() {
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getIdClienteApp() {
        return idClienteApp;
    }

    public void setIdClienteApp(String idClienteApp) {
        this.idClienteApp = idClienteApp;
    }

    public String getVersaoDoApp() {
        return versaoDoApp;
    }

    public void setVersaoDoApp(String versaoDoApp) {
        this.versaoDoApp = versaoDoApp;
    }

    public String getAppUtilizado() {
        return appUtilizado;
    }

    public void setAppUtilizado(String appUtilizado) {
        this.appUtilizado = appUtilizado;
    }

    public Integer getCodUsuario() {
        return codUsuario;
    }

    public void setCodUsuario(Integer codUsuario) {
        this.codUsuario = codUsuario;
    }

    public Integer getMatricula() {
        return matricula;
    }

    public void setMatricula(Integer matricula) {
        this.matricula = matricula;
    }

    public Integer getCodEmpresa() {
        return codEmpresa;
    }

    public void setCodEmpresa(Integer codEmpresa) {
        this.codEmpresa = codEmpresa;
    }
}
