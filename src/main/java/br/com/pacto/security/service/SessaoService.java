package br.com.pacto.security.service;

import br.com.pacto.security.dto.UsuarioSimplesDTO;

/**
 * Contrato para os serviços que lidam com a sessão dos usuários
 *
 * <AUTHOR>
 * @since 18/07/2018
 */
public interface SessaoService {
    /**
     * @return Os dados do usuário atual
     */
    UsuarioSimplesDTO getUsuarioAtual();

    /**
     * @param usuarioSimplesDTO {@link UsuarioSimplesDTO}
     */
    void setUsuarioAtual(UsuarioSimplesDTO usuarioSimplesDTO);

    /**
     * Limpa a sessão atual do ThreadLocal
     */
    void limparSessaoAtual();

}
