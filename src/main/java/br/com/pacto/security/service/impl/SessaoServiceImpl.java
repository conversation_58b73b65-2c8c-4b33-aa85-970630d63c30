package br.com.pacto.security.service.impl;

import br.com.pacto.security.dto.UsuarioSimplesDTO;
import br.com.pacto.security.service.SessaoService;
import org.springframework.stereotype.Service;

/**
 * Serviço de sessão que guarda os dados do usuário corrente
 * <br/><br/>
 * <b>ATENÇÃO:</b> este serviço utiliza um {@link ThreadLocal} para guardar as sessões dos usuários, então se um thread
 * pool for criado e algum {@link Runnable} tentar acessar esse serviço para tentar recuperar a sessão corrente,
 * provavelmente vai falhar. Caso seja necessário esse tipo de abordagem, passe a sessão corrente para o thread pool.
 *
 * <AUTHOR>
 * @since 18/07/2018
 */
@Service
public class SessaoServiceImpl implements SessaoService {

    private final ThreadLocal<UsuarioSimplesDTO> usuario;

    /**
     * Inicializa o threadlocal
     */
    public SessaoServiceImpl() {
        usuario = new ThreadLocal<UsuarioSimplesDTO>();
    }

    @Override
    public UsuarioSimplesDTO getUsuarioAtual() {
        return usuario.get();
    }

    @Override
    public void setUsuarioAtual(UsuarioSimplesDTO usuarioSimplesDTO) {
        usuario.set(usuarioSimplesDTO);
    }

    @Override
    public void limparSessaoAtual() {
        usuario.remove();
        setUsuarioAtual(null);
    }

}
