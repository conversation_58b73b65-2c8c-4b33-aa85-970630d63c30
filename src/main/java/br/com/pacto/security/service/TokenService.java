package br.com.pacto.security.service;

import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.security.dto.UsuarioSimplesDTO;
import br.com.pacto.security.exceptions.SecretException;
import br.com.pacto.security.exceptions.TokenExpiradoException;
import br.com.pacto.security.exceptions.TokenInvalidoException;
import br.com.pacto.service.exception.ServiceException;
import com.auth0.jwt.interfaces.DecodedJWT;

import java.util.Set;

/**
 * Contrato para as regras de negócio de um token
 *
 * <AUTHOR>
 * @since 19/07/2018
 */
public interface TokenService {

    String gerarTokenSimples(String chave, Integer empresa, Usuario usuario) throws ServiceException;

    /**
     * Gera o token de um usuário
     *
     * @param usuario Usuário para o qual o token será gerado
     * @param chave
     * @return Uma String com o token gerado
     * @throws ServiceException Caso ocorra qualquer problema na geração do token do usuário
     */
    String gerarToken(Usuario usuario, String chave) throws ServiceException;

    /**
     * Através do TOKEN informado, recupera as informações do usuário
     *
     * @param token TOKEN gerado previamente pela API
     * @return O usuário corrente ou <b>null</b> caso nenhum seja encontrado
     * @throws TokenInvalidoException Caso ocorra algum problema na validação ou na recuperação
     */
    UsuarioSimplesDTO validarRecuperandoUsuario(String token, boolean guardarMapa) throws TokenInvalidoException, TokenExpiradoException, SecretException;

    UsuarioSimplesDTO validarRecuperandoUsuario(String token, String chave, String empresaId, boolean guardarMapa) throws TokenInvalidoException, TokenExpiradoException, SecretException;

    void addUsuarioComToken(String token,
                                   Integer codigo,
                                   String username,
                                   Integer colaboradorId,
                                   String chave,
                                   Set<RecursoEnum> recursos) throws ServiceException;

    String chaveFromToken(String token) throws Exception;

    DecodedJWT validarToken(String token) throws TokenInvalidoException, TokenExpiradoException, SecretException;
}
