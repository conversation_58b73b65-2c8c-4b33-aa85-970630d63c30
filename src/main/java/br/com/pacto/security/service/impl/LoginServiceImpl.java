package br.com.pacto.security.service.impl;

import br.com.pacto.bean.usuario.StatusEnum;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.bean.usuario.UsuarioCacheTO;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.security.dto.UsuarioEncriptadoDTO;
import br.com.pacto.security.dto.UsuarioLoginV2DTO;
import br.com.pacto.security.service.LoginService;
import br.com.pacto.security.service.TokenService;
import br.com.pacto.security.service.UsuarioAutenticadoDTO;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.conexao.ConexaoZWServiceImpl;
import br.com.pacto.service.intf.usuario.UsuarioService;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import servicos.integracao.adm.AdmWSConsumer;

import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;

/**
 * Implementação padrão do {@link LoginService}
 *
 * <AUTHOR> Karlus
 * @since 18/07/2018
 */
@Service
public class LoginServiceImpl implements LoginService {

    private final UsuarioService usuarioService;
    private final TokenService tokenService;
    @Autowired
    private ConexaoZWServiceImpl conexaoZWService;
    public static final Map<String, UsuarioCacheTO> MAPA_USUARIOS = new HashMap<>();

    /**
     * @param usuarioService {@link UsuarioService}
     * @param tokenService   {@link TokenService}
     */
    @Autowired
    public LoginServiceImpl(UsuarioService usuarioService, TokenService tokenService) {
        this.usuarioService = usuarioService;
        this.tokenService = tokenService;
    }


    @Override
    public String login(String chave, String username, String senha, Boolean isEncript) throws ServiceException {
        if (StringUtils.isBlank(chave)) {
            throw new ServiceException("login_chave_nao_informada", "A chave de login não foi informada.");
        }

        if (StringUtils.isBlank(username)) {
            throw new ServiceException("login_nome_usuario_nao_informado", "O nome do usuário não foi informado.");
        }

        if (StringUtils.isBlank(senha)) {
            throw new ServiceException("login_senha_nao_informada", "A senha do usuário não foi informada.");
        }

        return tokenService.gerarToken(usuarioService.validarUsuario(chave, username, senha, isEncript), chave);
    }

    @Override
    public String validarUsuarioMovel(String chave, String username) throws ServiceException {
        if (StringUtils.isBlank(chave)) {
            throw new ServiceException("login_chave_nao_informada", "A chave de login não foi informada.");
        }

        if (StringUtils.isBlank(username)) {
            throw new ServiceException("login_nome_usuario_nao_informado", "O nome do usuário não foi informado.");
        }

        return tokenService.gerarToken(usuarioService.validarUsuarioEmail(chave, username, true), chave);
    }

    @Override
    public String gerarTokenZw(UsuarioEncriptadoDTO dados) throws ServiceException {
        try {
            Integer usuarioZW = Integer.valueOf(Uteis.desencriptar(dados.getCodigo(), "chave_login_unificado"));
            Usuario usuario = usuarioService.obterPorId(dados.getChave(), usuarioZW);
            try {
                usuario.setEmpresasZW(UtilContext.getBean(AdmWSConsumer.class).obterEmpresasZW(dados.getChave(), usuarioZW));
            }catch (Exception e){
                Uteis.logar(e, LoginServiceImpl.class);
            }
            return tokenService.gerarToken(usuario, dados.getChave());        } catch (Exception ex) {
            throw new ServiceException("token_zw_nao_gerado", "Não foi possível gerar o token ZW.");
        }
    }

    @Override
    public UsuarioAutenticadoDTO login(String chave, String username, String senha) throws ServiceException {
        if (StringUtils.isBlank(chave)) {
            throw new ServiceException("login_chave_nao_informada", "A chave de login não foi informada.");
        }

        if (StringUtils.isBlank(username)) {
            throw new ServiceException("login_nome_usuario_nao_informado", "O nome do usuário não foi informado.");
        }

        if (StringUtils.isBlank(senha)) {
            throw new ServiceException("login_senha_nao_informada", "A senha do usuário não foi informada.");
        }
        Usuario usuario = usuarioService.validarUsuario(chave, username, senha);
        return new UsuarioAutenticadoDTO(usuario);
    }

    @Override
    public UsuarioAutenticadoDTO usuarioZw(String chave, Integer usuarioZw) throws ServiceException {
        Usuario usuario = usuarioService.obterPorId(chave, usuarioZw, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (usuario == null) {
            throw new ServiceException("Usuário não encontrado com o usuarioZw: " + usuarioZw);
        }
        return new UsuarioAutenticadoDTO(usuario);
    }

    @Override
    public void addUsuarioSessao(String chave, UsuarioAutenticadoDTO usuario) throws ServiceException {
        tokenService.addUsuarioComToken(usuario.getToken(),
                usuario.getCodigo(),
                usuario.getUsername(),
                usuario.getColaboradorId(),
                chave,
                new HashSet<>(Arrays.asList(usuario.getRecursos())));
    }

    @Override
    public String app(String chave, String username) throws ServiceException {
        if (StringUtils.isBlank(chave)) {
            throw new ServiceException("login_chave_nao_informada", "A chave de login não foi informada.");
        }

        if (StringUtils.isBlank(username)) {
            throw new ServiceException("login_nome_usuario_nao_informado", "O nome do usuário não foi informado.");
        }
        try {

            Usuario usuario = null;
            String chaveUserName = String.format("%s_%s", chave, username);

            boolean tokenExpirado = true;
            if (MAPA_USUARIOS.containsKey(chaveUserName)) {
                UsuarioCacheTO usuarioCacheTO = MAPA_USUARIOS.get(chaveUserName);
                usuario = usuarioCacheTO.getUsuario();
                tokenExpirado = usuarioCacheTO.isTokenExpirado();
            }

            if (usuario == null) {
                Uteis.logarDebug(String.format("Consultar o usuário: %s", username));

                usuario = usuarioService.consultarPorUserName(chave, username);
                if (usuario == null) {
                    usuario = usuarioService.consultarPorUserName(chave, username, false, true);
                    if (usuario != null && usuario.getCliente() == null) {
                        throw new ServiceException("Usuário não encontrado.");
                    }
                }
            }

            if (usuario != null && tokenExpirado) {
                Uteis.logarDebug(String.format("Gerar token para a chave: %s", chaveUserName));

                String token = tokenService.gerarToken(usuario, chave);
                usuario.setToken(token);
                MAPA_USUARIOS.put(chaveUserName, new UsuarioCacheTO(usuario));
            }

            if (usuario == null) {
                throw new ServiceException("Usuário não encontrado.");
            }

            return usuario.getToken();
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    @Override
    public String app(String chave, Integer codUsuario) throws ServiceException {
        if(codUsuario == null || codUsuario <= 0){
            throw new ServiceException("login_cod_usuario_nao_informado", "O código do usuário não foi informado.");
        }
        if(StringUtils.isBlank(chave)){
            throw new ServiceException("login_chave_nao_informada", "A chave de login não foi informada.");
        }

        try {
            Usuario usuario = null;
            String chaveCodUsuario = String.format("%s_%s", chave, codUsuario);

            boolean tokenExpirado = true;
            if (MAPA_USUARIOS.containsKey(chaveCodUsuario)) {
                UsuarioCacheTO usuarioCacheTO = MAPA_USUARIOS.get(chaveCodUsuario);
                usuario = usuarioCacheTO.getUsuario();
                tokenExpirado = usuarioCacheTO.isTokenExpirado();
            }

            if (usuario == null) {
                Uteis.logarDebug(String.format("Consultar o usuário: %s", codUsuario));
                usuario = usuarioService.consultaPorId(chave, codUsuario);

                if(usuario != null && !UteisValidacao.emptyNumber(usuario.getUsuarioZW())) {
                    String usernameUsuarioZW = usuarioService.obterUsernameUsuarioZW(usuario.getUsuarioZW(), chave);
                    usuario = usuarioService.consultarPorUserName(chave, usernameUsuarioZW);
                    if (usuario == null) {
                        usuario = usuarioService.consultarPorUserName(chave, usernameUsuarioZW, false, true);
                        if (usuario != null && usuario.getCliente() == null) {
                            throw new ServiceException("Usuário não encontrado.");
                        }
                    }
                }
            }

            if(usuario != null) {
                /**
                 * 13/03/2025: Desativado por não ter critério para essa regra no APP Treino não faz sentido
                 */
                /*if(usuario.getCliente() != null && usuario.getCliente().getSituacao().equals("IN")) {
                    throw new ServiceException("Cliente inativo.");
                }*/
                if(usuario.getProfessor() != null && !usuario.getProfessor().isAtivo()) {
                    throw new ServiceException("Colaborador inativo.");
                }
            }

            if (usuario != null && tokenExpirado && usuario.getStatus() != null && StatusEnum.ATIVO == usuario.getStatus()) {
                Uteis.logarDebug(String.format("Gerar token para a chave: %s", chaveCodUsuario));

                String token = tokenService.gerarToken(usuario, chave);
                usuario.setToken(token);
                MAPA_USUARIOS.put(chaveCodUsuario, new UsuarioCacheTO(usuario));
            }

            if(usuario == null){
                throw new ServiceException("Usuário não encontrado.");
            }
            return tokenService.gerarToken(usuario, chave);
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    public UsuarioAutenticadoDTO loginV2(final UsuarioLoginV2DTO dto) throws ServiceException {
        if (StringUtils.isBlank(dto.getChave())) {
            throw new ServiceException("login_chave_nao_informada", "A chave de login não foi informada.");
        }
        return new UsuarioAutenticadoDTO(usuarioService.validarUsuarioLoginV2(dto));
    }
}
