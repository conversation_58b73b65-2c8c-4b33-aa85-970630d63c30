package br.com.pacto.security.service;

import br.com.pacto.bean.perfil.permissao.Permissao;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.bean.usuario.Usuario;

import java.util.HashSet;
import java.util.Set;

public class UsuarioAutenticadoDTO {

    private Integer codigo;
    private String username;
    private Integer colaboradorId;
    private RecursoEnum[] recursos;
    private String token;

    public UsuarioAutenticadoDTO() {

    }
    public UsuarioAutenticadoDTO(Usuario usuario) {
        Set<RecursoEnum> recursos = new HashSet<>();
        if(usuario.getPerfil() != null){
            for (Permissao permissao : usuario.getPerfil().getPermissoes()) {
                recursos.add(permissao.getRecurso());
            }
        }
        this.codigo = usuario.getCodigo();
        this.username = usuario.getUserName();
        this.colaboradorId = usuario.getProfessor().getCodigoColaborador() == null ?
                usuario.getProfessor().getCodigo() : usuario.getProfessor().getCodigoColaborador();
        this.recursos = recursos.toArray(new RecursoEnum[recursos.size()]);
        this.token = "";
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public RecursoEnum[] getRecursos() {
        return recursos;
    }

    public void setRecursos(RecursoEnum[] recursos) {
        this.recursos = recursos;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getColaboradorId() {
        return colaboradorId;
    }

    public void setColaboradorId(Integer colaboradorId) {
        this.colaboradorId = colaboradorId;
    }

}
