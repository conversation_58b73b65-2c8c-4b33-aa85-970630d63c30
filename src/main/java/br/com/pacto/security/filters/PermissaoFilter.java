package br.com.pacto.security.filters;

import br.com.pacto.base.oamd.BetaTestersService;
import br.com.pacto.security.dto.UsuarioSimplesDTO;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.security.service.TokenService;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;
import br.com.pacto.util.impl.AuditUtilities;
import org.springframework.http.HttpStatus;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * Filtro que coloca o usuário na sessão atual para que o mesmo possa ser recuperado pela thread de requisição atual
 * em qualquer parte da aplicação
 *
 * <AUTHOR>
 * @since 17/07/2018
 */
public class PermissaoFilter implements Filter {

    private static final String HEADER_AUTENTICACAO = "Authorization";
    private static final String CHAVE = "chave";
    private static final String KEY = "key";
    private static final String EMPRESA = "empresaId";
    private static final String UTF_8 = "UTF-8";
    private static final String OPTIONS = "OPTIONS";
    private static final String PSEC = "/psec/";
    private static final String LOGIN_REDE_SOCIAL_CRYPT = "fV7iK6fA1yP8pK0bQ4hI8wW4xY8yP2tU".toLowerCase();
    private static final String LOGIN_APP = "/login/app";
    private static final String PSEC_EMPRESAS_ATIVAR = "/psec/empresas/ativar";
    private static final String TESTERS = "testers";
    private static final String STATUS = "status";
    private static final String HEALTH = "health";
    private static final String LOGIN = "login";
    private static final String SWAGGER = "swagger";
    private static final String BLANK = " ";
    private static final String SEM_PERMISSAO = "Suas permissoes nao te dao acesso ao TREINO";
    private static final String X_FORWARDED_FOR = "X-FORWARDED-FOR";
    private static final String WL_Proxy_Client_IP = "WL-Proxy-Client-IP";
    private static final String HTTP_CLIENT_IP = "HTTP_CLIENT_IP";
    private String permissao = "TREINO";

    @Override
    public void init(FilterConfig filterConfig) {
    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain)
            throws IOException, ServletException {

        if (servletRequest.getCharacterEncoding() == null) {
            servletRequest.setCharacterEncoding(UTF_8);
            servletResponse.setCharacterEncoding(UTF_8);
        }

        HttpServletRequest request = (HttpServletRequest) servletRequest;
        boolean psec = request.getRequestURI().contains(PSEC);

        boolean loginAppTreino = request.getRequestURI().toLowerCase().contains(LOGIN_REDE_SOCIAL_CRYPT);

        if (!loginAppTreino) {
            if ((!checkBetaTesters(request) && !psec)
                    || request.getMethod().equalsIgnoreCase(OPTIONS)
                    || request.getPathInfo().equals(PSEC_EMPRESAS_ATIVAR)
                    || request.getRequestURI().contains(TESTERS)
                    || request.getRequestURI().contains(STATUS)
                    || request.getRequestURI().contains(HEALTH)
                    || request.getRequestURI().contains(SWAGGER)
                    || (request.getRequestURI().contains(LOGIN))
            ) {
                filterChain.doFilter(servletRequest, servletResponse);
                return;
            }
        }

        String token = request.getHeader(HEADER_AUTENTICACAO);
        String chave = request.getHeader(CHAVE);
        String empresaId = request.getHeader(EMPRESA);

        try {
            if (loginAppTreino) {
                UtilContext.getBeanTyped(TokenService.class).validarToken(
                        token.contains(BLANK) ? token.split(BLANK)[1] : token);
            } else {
                UsuarioSimplesDTO usuario = UtilContext.getBeanTyped(TokenService.class).validarRecuperandoUsuario(
                        token.contains(BLANK) ? token.split(BLANK)[1] : token, chave, empresaId, psec);

                if (UteisValidacao.emptyNumber(usuario.getId()) && !usuario.getPermissoesApis().contains(permissao)) {
                    ((HttpServletResponse) servletResponse).sendError(
                            HttpStatus.UNAUTHORIZED.value(), SEM_PERMISSAO);
                    return;
                }

                UtilContext.getBeanTyped(SessaoService.class).setUsuarioAtual(usuario);
                AuditUtilities.putUsuarioFromCurrentThread(Thread.currentThread().getId(), usuario);
            }
        } catch (Exception e) {
            ((HttpServletResponse) servletResponse).sendError(
                    HttpStatus.UNAUTHORIZED.value(),
                    e.getMessage());
            return;
        }

        AuditUtilities.putIpFromCurrentThread(Thread.currentThread().getId(), getIp(request));
        filterChain.doFilter(servletRequest, servletResponse);
    }

    private static String getIp(HttpServletRequest request){
        String ip = request.getHeader(X_FORWARDED_FOR);

        if( ip == null){
            ip = request.getHeader(WL_Proxy_Client_IP);
        }

        if( ip == null){
            ip = request.getHeader(HTTP_CLIENT_IP);
        }

        if (ip == null) {
            ip = request.getRemoteAddr();
        }

        return ip;
    }

    @Override
    public void destroy() {
    }

    public boolean checkBetaTesters(HttpServletRequest request) {
        boolean tester = false;
        try {
            String chave = extractKeyRequest(request);
            if (BetaTestersService.applyAll) {
                return true;
            } else if (UteisValidacao.emptyString(chave)) {
                for(String k : BetaTestersService.testers){
                    if(request.getRequestURI().contains(k)){
                        tester = true;
                        break;
                    }
                }
            } else {
                return BetaTestersService.isBetaTester(chave);
            }
        } catch (
                Exception e) {
            e.printStackTrace();
        }
        return tester;
    }

    private String extractKeyRequest(HttpServletRequest request){
        String chave;
        try {
            chave = obterParametroString(request, KEY);
            if(chave == null){
                chave = obterParametroString(request, CHAVE);
            }
        }catch (Exception e){
            chave = null;
        }
        return chave;
    }

    private String obterParametroString(HttpServletRequest request, String chave) {
        String parameter = request.getParameter(chave);
        if (parameter != null) {
            return parameter.toString();
        }
        return null;
    }


}
