package br.com.pacto.security.exceptions;

import br.com.pacto.service.exception.ServiceException;

/**
 * Exceção para tokens inválidos
 *
 * <AUTHOR>
 * @since 19/07/2018
 */
public class TokenInvalidoException extends ServiceException {

    private static final String CHAVE_EXCECAO = "token_invalido";
    private static final String MENSAGEM_EXCECAO = "O token informado não é valido";

    public TokenInvalidoException() {
        super(CHAVE_EXCECAO, MENSAGEM_EXCECAO);
    }

    public TokenInvalidoException(Throwable causa) {
        super(CHAVE_EXCECAO, MENSAGEM_EXCECAO, causa);
    }

}
