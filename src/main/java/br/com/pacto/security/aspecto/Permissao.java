package br.com.pacto.security.aspecto;

import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.bean.perfil.permissao.TipoPermissaoEnum;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Anotação usada em endpoints para demarcar as perfis necessárias para acessar aquele usuário.
 * <br>O aspecto que possibilita a verificação de proteção é o {@link PermissaoAspect}
 *
 * <AUTHOR>
 * @since 17/07/2018
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
public @interface Permissao {

    /**
     * @return Os recursos acessíveis pelo usuário
     */
    RecursoEnum[] recursos();

    /**
     * @return Os tipos de permissão recursos acessíveis pelo usuário
     */
    TipoPermissaoEnum[] tiposPermissao() default TipoPermissaoEnum.TOTAL;

}
