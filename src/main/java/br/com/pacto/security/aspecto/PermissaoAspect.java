package br.com.pacto.security.aspecto;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.security.dto.UsuarioSimplesDTO;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.util.UtilContext;
import br.com.pacto.util.impl.AuditUtilities;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.http.ResponseEntity;

/**
 * Aspecto que possibilita a funcionalidade de proteger os endpoints através da anotação {@link Permissao}
 *
 * <AUTHOR>
 * @since 17/07/2018
 */
@Aspect
public class PermissaoAspect {

    @Around("execution(* *(..)) && @annotation(permissao)")
    public ResponseEntity<EnvelopeRespostaDTO> verificarPermissoes(ProceedingJoinPoint joinPoint, Permissao permissao) {
        UsuarioSimplesDTO usuario = UtilContext.getBeanTyped(SessaoService.class).getUsuarioAtual();

        if (possuiRecurso(usuario, permissao)) {
            return executarEndpoint(joinPoint);
        }

        return ResponseEntityFactory.proibido();
    }

    @SuppressWarnings("unchecked")
    private ResponseEntity<EnvelopeRespostaDTO> executarEndpoint(ProceedingJoinPoint joinPoint) {
        try {
            return (ResponseEntity<EnvelopeRespostaDTO>) joinPoint.proceed();
        } catch (Throwable throwable) {
            throwable.printStackTrace();
            return ResponseEntityFactory.erroInterno(
                    "erro_inesperado",
                    "Ocorreu um erro inesperado durante a chamada ao endpoint.");
        } finally {
            UtilContext.getBeanTyped(SessaoService.class).limparSessaoAtual();
            AuditUtilities.leaveAll();
        }
    }

    private boolean possuiRecurso(UsuarioSimplesDTO usuario, Permissao permissao) {
//        for (RecursoEnum recursoUsuario : usuario.getRecursos()) {
//            for (RecursoEnum recursoNecessario : permissao.recursos()) {
//                if (recursoNecessario == recursoUsuario) {
//                    return true;
//                }
//            }
//        }
        return true;
    }

}
