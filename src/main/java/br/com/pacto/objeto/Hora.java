package br.com.pacto.objeto;

public class Hora {

    public static String calcularDiferencaHoras(String inicio, String fim) {
        int horasInicio = Integer.parseInt(inicio.split(":")[0]);
        int horasFim = Integer.parseInt(fim.split(":")[0]);

        int minutosInicio = Integer.parseInt(inicio.split(":")[1]);
        int minutosFim = Integer.parseInt(fim.split(":")[1]);

        int diffHoras = horasFim - horasInicio;
        int diffMinutos = minutosFim - minutosInicio;

        if (diffMinutos < 0) {
            diffHoras--;
            diffMinutos = 60 + diffMinutos;
        }

        String horasDiff = String.valueOf(diffHoras) + ":"
                + String.valueOf(diffMinutos);
        return horasDiff;
    }

    public static int calcularMinutos(String hora) {
        int horas = Integer.parseInt(hora.split(":")[0]);
        int minutos = Integer.parseInt(hora.split(":")[1]);

        int total = horas * 60;
        total += minutos;

        return total;
    }

    public static String calcularHora(int minutos) {
        int hr = (int) (minutos / 60);
        int min = minutos - (hr * 60);

        String hrStr = String.valueOf(hr);
        String minStr = String.valueOf(min);
        if (hrStr.length() == 1) {
            hrStr = "0" + hrStr;
        }
        if (minStr.length() == 1) {
            minStr = "0" + minStr;
        }
        return hrStr + ":" + minStr;
    }
}
