/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.objeto;

import br.com.pacto.base.impl.EntityManagerFactoryService;
import br.com.pacto.base.oamd.ManyDataBasesException;
import br.com.pacto.controller.json.base.SuperControle;
import br.com.pacto.service.intf.usuario.FotoService;
import br.com.pacto.util.FileUtilities;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;
import br.com.pacto.util.aws.AwsConsoleApp;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.integracao.midias.MidiaService;
import servicos.integracao.midias.commons.MidiaEntidadeEnum;

import javax.faces.context.FacesContext;
import javax.servlet.ServletContext;
import java.io.File;
import java.io.FileOutputStream;
import java.io.FileReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Properties;
import java.util.Set;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 */
public final class Aplicacao {

    private static final String path = "/br/com/pacto/util/resources/OpcoesGlobais.properties";

    public static final String version = "version";
    public static final String urlZillyonWeb = "urlZillyonWeb".toLowerCase();
    public static final String urlZillyonWebInteg = "urlZillyonWebInteg".toLowerCase();
    public static final String urlOAMD = "urlOAMD";
    public static final String discoveryUrls = "discoveryUrls";
    public static final String urlOAMDSegura = "urlOAMDSegura";
    public static final String urlOAMDApiUsuarioApp = "urlOAMDApiUsuarioApp";
    public static final String urlServicoDadosApp = "urlServicoDadosApp";
    public static final String urlAdmApp = "urlAdmApp";
    public static final String keyUnitTests = "keyUnitTests";
    public static final String urlBaseMidias = "urlBaseMidias";
    public static final String urlObterBanners = "urlObterBanners";
    public static final String instanceSchedulingName = "instanceSchedulingName";
    public static final String redirecionar = "redirecionar";
    public static final String url_redirecionar = "url_redirecionar";
    public static final String loadFactoryOnStart = "loadFactoryOnStart";
    public static final String instanciasPropagar = "instanciasPropagar";
    public static final String loadInstancesFromCloud = "loadInstancesFromCloud";
    public static final String forceUrlFotos = "forceUrlFotos";
    public static final String cookieFailover = "cookieFailover";
    public static final String dirMidiasEmbedded = "dirMidiasEmbedded";
    public static final String modulos = "modulos";
    public static final String urlTreinoGooglePlay = "urlTreinoGooglePlay";
    public static final String urlTreinoAppleStore = "urlTreinoAppleStore";
    public static final String fotosParaNuvem = "fotosParaNuvem";
    public static final String urlFotosNuvem = "urlFotosNuvem";
    public static final String urlAquivosNuvem = "urlArquivoNuvem";
    public static final String typeMidiasService = "typeMidiasService";
    public static final String diretorioFotos = "diretorioFotos";
    public static final String diretorioArquivos = "diretorioArquivos";
    public static final String redirecionarLogin = "redirecionarLogin";
    public static final String urlLogin = "urlLogin";
    public static final String urlSolicitacao = "urlSolicitacao";
    public static final String urlMidias = "urlMidias";
    public static final String urlAPIGymPassBookingToken = "urlAPIGymPassBookingToken";
    public static final String urlAPIGymPassBookingSandbox = "urlAPIGymPassBookingSandbox";
    public static final String urlAPIGymPassBookingProduction = "urlAPIGymPassBookingProduction";
    //
    public static final String smtpEmailNoReply = "smtpEmailNoReply";
    public static final String smtpEmailRobo = "smtpEmailRobo";
    public static final String smtpLoginRobo = "smtpLoginRobo";
    public static final String smtpSenhaRobo = "smtpSenhaRobo";
    public static final String smtpConexaoSeguraRobo = "smtpConexaoSeguraRobo";
    public static final String smtpServerRobo = "smtpServerRobo";

    public static final String senhaPactoBr = "senhaPactoBr";
    public static final String senhaPactoBrTeste = "senhaPactoBrTeste";

    public static final String buscarConhecimentoUCP = "buscarConhecimentoUCP";
    public static final String usarUrlRecursoEmpresa = "usarUrlRecursoEmpresa";
    public static final String myUpUrlBase = "myUpUrlBase";
    public static final String urlsocketaux = "urlsocketaux";
    public static final String importandoClientesZw = "importandoclienteszw";
    public static final String sincronizandoGympass = "sincronizandoGympass";
    public static final String processarTreinosRealizados = "processarTreinosRealizados";
    public static final String AUTH_SECRET_PATH = "AUTH_SECRET_PATH";
    public static final String hbm2ddlAuto = "hibernate.hbm2ddl.auto";
    public static final String hbmValidatorApplyToDDL = "hibernate.validator.apply_to_ddl";
    public static final String URL_HTTPS_PLATAFORMA_PACTO = "URL_HTTPS_PLATAFORMA_PACTO";
    public static final String URL_HTTP_PLATAFORMA_PACTO = "URL_HTTP_PLATAFORMA_PACTO";
    public static final String NUMEROS_WHATSAPP_PACTO = "NUMEROS_WHATSAPP_PACTO";
    public static final String urlZillyonWebIntegProp = "urlZillyonWebIntegProp";
    public static final String logarDao = "logarDao";
    public static final String useBetaTesters = "useBetaTesters";
    public static final String instanceAllowDDL = "instanceClassAllowDDL";
    public static final String instanceName = System.getProperty("com.sun.aas.instanceName", "");
    public static final String ipServidoresMemCached = "ipServidoresMemCached";

    public static final String enableNewLogin = "enableNewLogin";
    public static final String ambienteTeste = "ambienteTeste";
    public static final String tokenPushMobile = "tokenPushMobile";
    public static final String tokenPushAppAluno = "tokenPushAppAluno";
    public static final String urlAppDoAlunoUnificado = "urlAppDoAlunoUnificado";
    public static final String urlTreinoIa = "urlTreinoIa";
    public static final String accessTokenTreinoIa = "accessTokenTreinoIa";
    public static final String usarUsuarioServlet = "usarUsuarioServlet";
    public static final String usarUsuarioZwBoot = "usarUsuarioZwBoot";
    public static final String usarUsuarioCache = "usarUsuarioCache";
    public static final String CHAVE_CRIPTO_ZW_UI = "CHAVE_CRIPTO_ZW_UI";
    public static final String tokenAcessoAPICliente = "tokenAcessoAPICliente";
    public static final String modoConsultaAgenda = "modoConsultaAgenda";
    public static final String tempoOciosoPool = "tempoOciosoPool";
    public static final String maximumPoolSize = "maximumPoolSize";
    public static final String tokenGymPassV3 = "tokenGymPassV3";
    public static final String consultaContratoAssinaturaDigital = "consultaContratoAssinaturaDigital";

    public static final String consultaUsuariosAppPeloFireBase = "consultaUsuariosAppPeloFireBase";
    public static final String modoConsultaDashBI = "modoConsultaDashBI";

    private static final String CDN_PREFIX = "cdn1.pactorian.net";
    private static final String URL_S3_PREFIX = "https://s3-sa-east-1.amazonaws.com/prod-zwphotos";

    public static final String aesSecretKeyAppTreino = "aesSecretKeyAppTreino";

    private static final String S3_SUFFIX_TIME = "\\?time=";
    private static final String FMT_URL = "%s/%s";

    public static final long UM_DIA = 86400000L;// em millisegundos

    public static final String totalPassApiValidate = "totalPassApiValidate";
    public static final String totalPassApi = "totalPassApi";
    public static final String urlApiStaging = "urlApiStaging";
    public static final String urlValidateStaging = "urlValidateStaging";
    public static final String urlAvaliacaoFisicaApi = "urlAvaliacaoFisicaApi";
    public static final String urlOauthTokenSelfloops = "urlOauthTokenSelfloops";
    public static final String urlApiSelfloops = "urlApiSelfloops";
    public static final String pacto_client_id_selfloops = "pacto_client_id_selfloops";
    public static final String pacto_secret_id_selfloops = "pacto_secret_id_selfloops";

    public static final String PREFIX_URL_IMAGEM_CDN = "https://cdn1.pactorian.net/";
    public static final String PREFIX_URL_IMAGEM_CLOUDFRONT = "https://dt39m1atv5spm.cloudfront.net/";
    public static final String PREFIX_URL_IMAGEM_S3 = "https://s3-sa-east-1.amazonaws.com/prod-zwphotos/";

    private static Properties props;

    static {
        init();
    }

    public static void init() {
        props = new Properties();
        try {
            props.load(new FileReader(new File(SuperControle.class.getResource(
                    path).toURI())));
        } catch (Exception ex) {
            Logger.getLogger(Aplicacao.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public static List<String> listGlobalOptions() {
        List<String> retorno = new ArrayList<String>();
        Set<String> propsNames = props.stringPropertyNames();
        for (String p : propsNames) {
            retorno.add(String.format("%s=%s",
                    p,
                    props.getProperty(p)));
        }
        return retorno;
    }

    public static String getProp(final String name) {
        try {
            return props.getProperty(name);
        } catch (Exception e) {
        }
        return "";
    }

    public static boolean isTrue(final String name) {
        String value = getProp(name);
        return value.equals("true");
    }

    public static boolean isEmpty(final String name) {
        String value = getProp(name);
        return value.isEmpty();
    }

    public static boolean isAmbienteTeste(){
        return Aplicacao.isTrue(Aplicacao.ambienteTeste);
    }

    public static void setProp(final String name, final String value) {
        props.setProperty(name, value);
    }

    public static void save() {
        FileOutputStream fos;
        try {
            File f = new File(SuperControle.class.getResource(path).toURI());
            fos = new FileOutputStream(f);
            props.store(fos, null);
            init();
        } catch (Exception ex) {
            Logger.getLogger(Aplicacao.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public static String getProp(final String key, final String name) {
        Object value = EntityManagerFactoryService.getPropertyOAMD(key, name);
        if (value != null) {
            return value.toString();
        }
        return getProp(name);
    }

    public static Boolean independente(final String key) {
        try {
            String mod = getProp(key,Aplicacao.modulos);
            boolean ind = mod != null && !mod.contains("ZW");
            return ind;
        }catch (Exception e){
            Logger.getLogger(Aplicacao.class.getName()).log(Level.SEVERE, null, e);
            return false;
        }
    }

    public static String getPropOAMD(final String key, final String name)
            throws ManyDataBasesException {
        Object value = EntityManagerFactoryService.getPropertyOAMD(key, name);
        if (value != null) {
            Uteis.logar(null, String.format("READING PROPERTY OAMD => %s - %s = %s ", key, name, value));
            return value.toString();
        } else {
            throw new ManyDataBasesException("Parâmetros não definidos para chave: " + key);
        }
    }

    public static void putProp(final String key, final String attribute, final String value) {
        EntityManagerFactoryService.putPropertyOAMD(key, attribute, value);
    }

    public static String preencherFotoTreinoIndependente(final String key, final Integer codigoPessoa,
                                                         boolean forceDownloadIfNotExists, boolean fotoPersonal, boolean reload, boolean treinoIndependente) {
        try {
            return preencherFotoTreinoIndependente(key, codigoPessoa, forceDownloadIfNotExists, fotoPersonal, reload,
                    ((ServletContext) FacesContext.getCurrentInstance().getExternalContext().getContext()), treinoIndependente);
        } catch (Exception ex) {
            Uteis.logar(null, "Não foi possível obter a foto -> " + key + " pessoa " + codigoPessoa + " motivo: " + ex.getMessage());
        }
        return "fotoPadrao.jpg";
    }


    public static String preencherFoto(final String key, final Integer codigoPessoa,
            boolean forceDownloadIfNotExists, boolean fotoPersonal, boolean reload) {
        return preencherFoto(key, codigoPessoa, forceDownloadIfNotExists, fotoPersonal, reload, MidiaEntidadeEnum.FOTO_PESSOA);
    }

    public static String preencherFoto(final String key, final Integer codigoPessoa,
                                       boolean forceDownloadIfNotExists, boolean fotoPersonal, boolean reload, MidiaEntidadeEnum midiaEntidadeEnum) {
        try {
            return preencherFoto(key, codigoPessoa, forceDownloadIfNotExists, fotoPersonal, reload,
                    ((ServletContext) UtilContext.getBean(ServletContext.class)), midiaEntidadeEnum);
        } catch (Exception ex) {
            Uteis.logar(null, "Não foi possível obter a foto -> " + key + " pessoa " + codigoPessoa + " motivo: " + ex.getMessage());
        }
        return "fotoPadrao.jpg";
    }

    public static String preencherFotoTreinoIndependente(final String key, final Integer codigoPessoa,
                                                         boolean forceDownloadIfNotExists, boolean fotoPersonal, boolean reload, ServletContext servletContext, boolean treinoIndependente) {
        try {
            final String prefixo = servletContext.getRealPath("resources") + File.separator + "img";
            String caminho = prefixo;
            String sufixo_logo = key + File.separator + (fotoPersonal ? ("pers" + codigoPessoa) : codigoPessoa) + ".jpg";
            caminho += File.separator + sufixo_logo;
            File f = null;
            try {
                f = new File(caminho);
                if (!f.exists() || reload) {
                    if (forceDownloadIfNotExists) {
                        boolean gravou = saveImage(fotoPersonal ? getUrlFotoPersonal(key, codigoPessoa) : getUrlFotoTreinoIndependente(key, codigoPessoa, true, treinoIndependente),
                                caminho);
                        if (!gravou) {//no ZW não possui a foto ou nao foi possivel obte-la
                            sufixo_logo = "fotoPadrao.jpg";
                        }
                    } else {
                        sufixo_logo = "fotoPadrao.jpg";
                    }

                }
            } finally {
             }
            return sufixo_logo;
        } catch (Exception ex) {
            Uteis.logar(null, "Não foi possível obter a foto -> " + key + " pessoa " + codigoPessoa + " motivo: " + ex.getMessage());
        }
        return "fotoPadrao.jpg";
    }

    public static String preencherFoto(final String key, final Integer codigoPessoa,
            boolean forceDownloadIfNotExists, boolean fotoPersonal, boolean reload, ServletContext servletContext) {
        return preencherFoto(key, codigoPessoa, forceDownloadIfNotExists, fotoPersonal, reload, servletContext, MidiaEntidadeEnum.FOTO_PESSOA);

    }

    public static String preencherFoto(final String key, final Integer codigoPessoa,
                                       boolean forceDownloadIfNotExists, boolean fotoPersonal,
                                       boolean reload, ServletContext servletContext, MidiaEntidadeEnum midiaEntidadeEnum) {
        try {
            final String prefixo = servletContext.getRealPath("resources") + File.separator + "img";
            String caminho = prefixo;
            String sufixo_logo = key + File.separator + (fotoPersonal ? ("pers" + codigoPessoa) : getSufixoLogo(midiaEntidadeEnum, codigoPessoa)) + ".jpg";
            caminho += File.separator + sufixo_logo;
            File f = null;
            try {
                f = new File(caminho);
                if (!f.exists() || reload) {
                    if (forceDownloadIfNotExists) {
                        boolean gravou = saveImage(fotoPersonal ? getUrlFotoPersonal(key, codigoPessoa) : getUrlFoto(key, codigoPessoa, midiaEntidadeEnum == MidiaEntidadeEnum.FOTO_PESSOA, midiaEntidadeEnum),
                                caminho);
                        if (!gravou) {//no ZW não possui a foto ou nao foi possivel obte-la
                            sufixo_logo = "fotoPadrao.jpg";
                        }
                    } else {
                        sufixo_logo = "fotoPadrao.jpg";
                    }
                }
            } finally {
            }
            return sufixo_logo;
        } catch (Exception ex) {
            Uteis.logar(null, "Não foi possível obter a foto -> " + key + " pessoa " + codigoPessoa + " motivo: " + ex.getMessage());
        }
        return "fotoPadrao.jpg";
    }

    public static String getSufixoLogo(MidiaEntidadeEnum midiaEntidadeEnum, Integer codigo) {
        return midiaEntidadeEnum == MidiaEntidadeEnum.FOTO_PESSOA ? codigo.toString() : midiaEntidadeEnum.getNomeCampo() + codigo;
    }

    public static String preencherFotoJSON(ServletContext context, final String key, final Integer codigoPessoa) {
        try {
            final String prefixo = context.getRealPath("resources") + File.separator + "img";
            String caminho = prefixo;
            String sufixo_logo = key + "/" + codigoPessoa + ".jpg";
            caminho += "/" + sufixo_logo;
            File f = new File(caminho);
            if (!f.exists()) {
                boolean gravou = saveImage(getUrlFoto(key, codigoPessoa, true), caminho);
                if (!gravou) {//no ZW não possui a foto ou nao foi possivel obte-la
                    sufixo_logo = "fotoPadrao.jpg";
                }
            } else {
                sufixo_logo += "?" + f.lastModified();
            }
            return sufixo_logo;
        } catch (Exception ex) {
            Uteis.logar(null, "Não foi possível obter a foto -> " + key + " pessoa " + codigoPessoa + " motivo: " + ex.getMessage());
        }
        return "fotoPadrao.jpg";
    }

    public static void baixarTodasAsFotos(ServletContext sc, final String key) {
        try {
            final String url = String.format("%s/av?key=%s&get_persons=true", new Object[]{
                Aplicacao.getProp(key, Aplicacao.urlZillyonWeb),
                key});
            String json = HttpRequestUtil.executeRequestInner(url, null, 10000);
            JSONArray arr = new JSONArray(json);
            for (int i = 0; i < arr.length(); i++) {
                JSONObject o = arr.getJSONObject(i);
                int id_pessoa = o.getInt("id_pessoa");
                String fotokey = o.getString("fotokey");
                if (!StringUtils.isBlank(fotokey)) {
                    ((FotoService) UtilContext.getBean(FotoService.class)).migracaoFotoPessoa(sc, key, id_pessoa, fotokey);
                }
            }
        } catch (Exception ex) {
            Uteis.logar(null, "Não foi baixar fotos para empresa -> " + key + " -> motivo: " + ex.getMessage());
        }
    }

    public static boolean saveImage(String imageUrl, String destinationFile) throws IOException {
        URL url = new URL(imageUrl);
        HttpURLConnection urlConn = (HttpURLConnection) url.openConnection();
        urlConn.setConnectTimeout(1000);
        urlConn.setReadTimeout(5000);
        urlConn.setAllowUserInteraction(false);
        urlConn.setDoOutput(true);

        boolean redirect = false;

        // testar redirect positivo
        int status = urlConn.getResponseCode();
        if (status != HttpURLConnection.HTTP_OK) {
            if (status == HttpURLConnection.HTTP_MOVED_TEMP
                    || status == HttpURLConnection.HTTP_MOVED_PERM
                    || status == HttpURLConnection.HTTP_SEE_OTHER)
                redirect = true;
        }

        System.out.println("Response Code ... " + status);

        if (redirect) {

            // get redirect url
            String tmpUrl = urlConn.getHeaderField("Location");
            if (tmpUrl.contains("fotoPadrao")){
                return false;
            }
            URL novaURL = new URL(tmpUrl);
            final String[] arrURL = novaURL.getPath().split("/");
            final StringBuilder sbURL = new StringBuilder();
            for (int i = 0; i < arrURL.length; i++) {
                final String u = arrURL[i];
                sbURL.append(URLEncoder.encode(u, "UTF-8")).append(((i + 1) < arrURL.length) ? "/" : "");
            }

            final String newUrl = String.format("%s://%s%s",
                    novaURL.getProtocol(), novaURL.getHost(), sbURL.toString(), "UTF-8");

            urlConn = (HttpURLConnection) new URL(newUrl).openConnection();

            System.out.println("Redirect to URL : " + newUrl);
        }

        InputStream inStream = urlConn.getInputStream();

        try {
            byte[] b = new byte[4096];
            int length;
            int bytesEscritos = 0;
            OutputStream os = null;
            try {
                while ((length = inStream.read(b)) != -1) {
                    if (os == null) {
                        final String dir = destinationFile.substring(0, destinationFile.lastIndexOf("/"));
                        FileUtilities.forceDirectory(dir);
                        os = new FileOutputStream(destinationFile);
                    }
                    os.write(b, 0, length);
                    bytesEscritos += length;
                }
            } catch (IOException ex) {
                ex.printStackTrace();
                int respCode = ((HttpURLConnection) urlConn).getResponseCode();
                InputStreamReader es = new InputStreamReader(((HttpURLConnection) urlConn).getErrorStream());
                int ret = 0;
                // read the response body
                char[] buf = null;
                while ((ret = es.read(buf)) > 0) {
                }
                // close the errorstream
                es.close();
            } finally {
                if (os != null) {
                    os.close();
                }
            }

            if (bytesEscritos > 0) {
                System.out.println("TREINO -> gravei FOTO -> " + bytesEscritos + " " + destinationFile);
                return true;
            } else {
                System.out.println("TREINO -> NÃO gravei FOTO -> " + destinationFile);
                return false;
            }
        } finally {
            inStream.close();
        }
    }

    /**
     * @param chave
     * @param codigoPessoa
     * @param forceEmptyToGasper responsavel por sinalizar ao ZW quando uma
     * pessoa não possui foto, retornar VAZIO na RESPONSE
     * @return
     */
    public static String getUrlFotoTreinoIndependente(final String chave, final Integer codigoPessoa, boolean forceEmptyToGasper, boolean treinoIndependente) {
        if (Aplicacao.isTrue(Aplicacao.fotosParaNuvem) || treinoIndependente) {
            try {
                String identificador = MidiaService.getInstance().genKey(chave,
                        MidiaEntidadeEnum.FOTO_PESSOA, codigoPessoa.toString());
                return Aplicacao.obterUrlFotoDaNuvem(identificador);
            } catch (Exception ex) {
                Logger.getLogger(Aplicacao.class.getName()).log(Level.SEVERE, null, ex);
                return "";
            }
        } else {
            final String url = forceEmptyToGasper ? "%s/av?key=%s&av_pes=%s&forceEmptyToGasper=true" : "%s/av?key=%s&av_pes=%s";
            return String.format(url, new Object[]{
                Aplicacao.getProp(chave, Aplicacao.urlZillyonWeb),
                chave,
                codigoPessoa});
        }
    }

    /**
     * Consulta foto pessoas
     *
     * @param chave
     * @param codigo
     * @param forceEmptyToGasper
     * @return
     */
    public static String getUrlFoto(final String chave, final Integer codigo, boolean forceEmptyToGasper) {
        return getUrlFoto(chave, codigo, forceEmptyToGasper, MidiaEntidadeEnum.FOTO_PESSOA);
    }

    /**
     * Consulta por um tipo de midia
     *
     * @param chave
     * @param codigoPessoa
     * @param forceEmptyToGasper
     * @param midiaEntidadeEnum
     * @return
     */
    public static String getUrlFoto(final String chave, final Integer codigoPessoa, boolean forceEmptyToGasper, MidiaEntidadeEnum midiaEntidadeEnum) {
        if (Aplicacao.isTrue(Aplicacao.fotosParaNuvem)) {
            try {
                String identificador = MidiaService.getInstance().genKey(chave,
                        midiaEntidadeEnum, codigoPessoa.toString());
                return Aplicacao.obterUrlFotoDaNuvem(identificador);
            } catch (Exception ex) {
                Logger.getLogger(Aplicacao.class.getName()).log(Level.SEVERE, null, ex);
                return "";
            }
        } else {
            final String url = forceEmptyToGasper ? "%s/av?key=%s&av_pes=%s&forceEmptyToGasper=true&midia_entidade=%s" : "%s/av?key=%s&av_pes=%s&midia_entidade=%s";
            return String.format(url, new Object[]{
                Aplicacao.getProp(chave, Aplicacao.urlZillyonWeb),
                chave,
                    codigoPessoa,
                    midiaEntidadeEnum});
        }
    }

    public static String getUrlFoto(final String urlBase,
            final String chave, final Integer codigoPessoa, boolean forceEmptyToGasper) {
        if (Aplicacao.isTrue(Aplicacao.fotosParaNuvem)) {
            try {
                String identificador = MidiaService.getInstance().downloadObject(
                        chave, MidiaEntidadeEnum.FOTO_PESSOA, codigoPessoa.toString());
                return String.format("%s/%s", new Object[]{
                    urlBase,
                    identificador});
            } catch (Exception ex) {
                Logger.getLogger(Aplicacao.class.getName()).log(Level.SEVERE, null, ex);
                return "";
            }
        } else {
            final String url = forceEmptyToGasper ? "%s/av?key=%s&av_pes=%s&forceEmptyToGasper=true" : "%s/av?key=%s&av_pes=%s";
            return String.format(url, new Object[]{
                urlBase,
                chave,
                codigoPessoa});
        }
    }

    public static String saveImageLocal(ServletContext context, final String key, String fotoKey, Boolean updateImage, Integer codigoPessoa, Boolean fotoPersonal) {
        try {
            final String prefixo = context.getRealPath("resources") + File.separator + "img";
            String caminho = prefixo;
            String sufixo_logo = "";
            if (fotoPersonal) {
                sufixo_logo = key + File.separator + "pers" + codigoPessoa + ".jpg";
                caminho += File.separator + sufixo_logo;
                File filePersonal = new File(caminho);
                if (!filePersonal.exists()) {
                    boolean gravou = saveImage(getUrlFotoPersonal(key, codigoPessoa), caminho);
                    if (!gravou) {
                        fotoKey = "";
                    }
                } else {
                    fotoKey = sufixo_logo;
                   return fotoKey;
                }
            } else {
                if (!StringUtils.isBlank(fotoKey)) {
                    sufixo_logo = fotoKey + (fotoKey.contains(".jpg") ? "" : ".jpg");
                    caminho += "/" + sufixo_logo;
                    File f = new File(caminho);
                    if (!f.exists() || updateImage) {
                        boolean gravou = saveImage(Aplicacao.obterUrlFotoDaNuvem(fotoKey), caminho);
                        if (!gravou) {
                            fotoKey = "";
                        }
                    }
                    return fotoKey;
                }
            }
        } catch (Exception ex) {
            Uteis.logar(null, "Não foi possível gravar a foto -> " + key + " pessoa " + fotoKey + " motivo: " + ex.getMessage());
        }
        return "";
    }

    public static String getUrlFotoPersonal(final String chave, final Integer codigoColaborador) {
        final String url = "%s/av?key=%s&av_personal=%s";
        return String.format(url, new Object[]{
            Aplicacao.getProp(chave, Aplicacao.urlZillyonWeb), chave, codigoColaborador});
    }

    public static String getInstanceName() {
        return System.getProperty("com.sun.aas.instanceName", "");
    }

    public static boolean isInstanciaAgendamento() {
        if (!Aplicacao.getProp(Aplicacao.instanceSchedulingName).equals("disable")) {
            final String instanceName = Aplicacao.getInstanceName();
            if (instanceName.isEmpty()
                    || instanceName.contains(Aplicacao.getProp(Aplicacao.instanceSchedulingName))) {
                return true;
            }
        }
        return false;
    }

    public static boolean isTemInstanciaAgendExclusiva() {
        return !Aplicacao.getInstanceName().isEmpty();
    }

    public static List<String> atualizarInfoInstancias() {
        List<String> lista = AwsConsoleApp.getInstancesAddresses("prod-tr-*");
        String aux = lista.toString();
        aux = aux.replaceAll(" ", "").replaceAll("\\[", "").replaceAll("\\]", "");
        Aplicacao.setProp(Aplicacao.instanciasPropagar, aux);
        Aplicacao.save();
        return lista;
    }

    public static boolean deletarFoto(final String key, final String fotoKey, boolean fotoPersonal, ServletContext servletContext) {
        boolean deletou = false;
        try {
            if (!StringUtils.isBlank(fotoKey) && !fotoKey.equals("fotoPadrao.jpg")) {
                String caminho = servletContext.getRealPath("resources") + File.separator + "img";
                String sufixo_logo = "";
                if (fotoPersonal) {
                    String[] partSufixo_logo = fotoKey.split(File.separator);
                    partSufixo_logo[partSufixo_logo.length - 1] = "pers" + partSufixo_logo[partSufixo_logo.length - 1];
                    for (String partSufixo : partSufixo_logo) {
                        sufixo_logo += File.separator + partSufixo;
                    }
                } else {
                    sufixo_logo = File.separator + fotoKey;
                }
                caminho += sufixo_logo;
                File f = new File(caminho);
                if (f.exists()) {
                    deletou = f.delete();
                }
            }
        } catch (Exception ex) {
            Uteis.logar(null, "Não foi possível remover a foto -> " + key + " motivo: " + ex.getMessage());
        }
        return deletou;
    }

    private static String prependBaseUrlIfRecent(String urlBase, String fotokey) {
        // Extract the timestamp part from the URL
        String[] parts = fotokey.split(S3_SUFFIX_TIME);
        if (parts.length < 2) {
            return String.format(FMT_URL, urlBase, fotokey);
        }

        String originalPart = parts[0];
        String timestampStr = parts[1];

        try {
            long last_modified = Long.parseLong(timestampStr);
            long currentTime = System.currentTimeMillis();

            //aca438e8c9e947e64db2236bb2f1f7a9/97jxGKuba0*PGeIPqAkibQ==/ilgEtXDcyc@39CTz2x@Oow==.jpg?time=1715104079301

            // verifica se a foto foi alterada em menos de 24h, caso positivo força a URL do S3
            if ((currentTime - last_modified ) < UM_DIA) {
                return String.format(FMT_URL, URL_S3_PREFIX, fotokey);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        // caso contrário, returna URL original
        return String.format(FMT_URL, urlBase, fotokey);
    }

    public static String obterUrlFotoDaNuvem(final String fotokey) {
        if (UteisValidacao.emptyString(fotokey)) {
            return String.format(FMT_URL,
                    Aplicacao.getProp(Aplicacao.urlFotosNuvem),
                    "fotoPadrao.jpg");
        } else {
            if (Aplicacao.getProp(Aplicacao.urlFotosNuvem).contains(CDN_PREFIX)) {
                return prependBaseUrlIfRecent(Aplicacao.getProp(Aplicacao.urlFotosNuvem), fotokey);
            } else
                return String.format(FMT_URL,
                        Aplicacao.getProp(Aplicacao.urlFotosNuvem),
                        fotokey);
        }
    }

}
