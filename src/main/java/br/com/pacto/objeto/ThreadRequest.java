/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.objeto;

import java.io.IOException;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
public class ThreadRequest extends Thread {

    private String url;
    private Map params;
    private Integer sleepon = -1;

    public ThreadRequest(final String url, Map params, Integer sleep) {
        this.url = url;
        this.params = params;
        this.sleepon = sleep;
    }

    @Override
    public void run() {
        try {
            HttpRequestUtil.executeRequestInner(url, params, 15000);
            if (sleepon > 0) {
                try {
                    Thread.sleep(sleepon);
                } catch (InterruptedException ex) {
                    Logger.getLogger(ThreadRequest.class.getName()).log(Level.SEVERE, null, ex);
                }
            }
        } catch (IOException ex) {
            Logger.getLogger(ThreadRequest.class.getName()).log(Level.SEVERE, "Erro ao processar Requisição da URL -> " + url + params, ex);
        }
    }
}
