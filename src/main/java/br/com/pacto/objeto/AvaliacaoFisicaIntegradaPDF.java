package br.com.pacto.objeto;

import br.com.pacto.bean.anamnese.*;
import br.com.pacto.bean.avaliacao.ItemAvaliacaoFisica;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.enumerador.anamnese.TiposPerguntaEnum;
import br.com.pacto.util.ViewUtils;
import com.lowagie.text.*;
import com.lowagie.text.Font;
import com.lowagie.text.Rectangle;
import com.lowagie.text.pdf.GrayColor;
import com.lowagie.text.pdf.PdfPCell;
import com.lowagie.text.pdf.PdfPTable;
import com.lowagie.text.pdf.PdfWriter;
import servicos.integracao.midias.MidiaService;
import servicos.integracao.midias.commons.MidiaEntidadeEnum;

import javax.servlet.ServletContext;
import java.awt.*;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.util.ArrayList;
import java.util.List;

public class AvaliacaoFisicaIntegradaPDF extends UteisPDF {
    public static void criarPDFAvaliacaoIntegrada(String key, Usuario usuario, ItemAvaliacaoFisica item,
                                                  ViewUtils viewUtils, ServletContext servletContext,
                                                  final ByteArrayOutputStream baos) throws Exception {
        // criação do documento
        Document document = new Document();
        //icone treino
        final String imagemTreino = servletContext.getRealPath("resources") + File.separator + "imagens" + File.separator + "pacto_home.png";
        PdfWriter pdfWriter = PdfWriter.getInstance(document, baos);
        document.open();
        GrayColor background = new GrayColor(0.8f);
        float[] columns = {1, 7, 1};
        PdfPTable cabecalho = new PdfPTable(columns);
        PdfPCell cellImgEmpresa = new PdfPCell();
        PdfPCell cellDados = new PdfPCell();
        if (usuario.getEmpresaDefault() != null) {
            String genKey = MidiaService.getInstance().genKey(key, MidiaEntidadeEnum.FOTO_EMPRESA_RELATORIO, usuario.getEmpresaDefault().getCodigo().toString());
            String urlFoto = Aplicacao.obterUrlFotoDaNuvem(genKey);
            try {
                cellImgEmpresa = createImageCell(document, urlFoto);
            }catch (Exception e){

            }

            Font font = new Font();
            font.setSize(7);
            cellDados.addElement(new Paragraph(usuario.getEmpresaDefault().getNome(), font));
            cellDados.addElement(new Paragraph(usuario.getEmpresaDefault().getEndereco(), font));
            cellDados.addElement(new Paragraph(usuario.getEmpresaDefault().getCidade() + " - " + usuario.getEmpresaDefault().getEstado(), font));
            cellDados.addElement(new Paragraph(usuario.getEmpresaDefault().getSite(), font));

        }
        adicionarPaddingFull(cellImgEmpresa, 5);
        adicionarPaddingFull(cellDados, 5);
        cellDados.setBorder(0);
        cellImgEmpresa.setBorder(0);
        cabecalho.addCell(cellImgEmpresa);
        cabecalho.addCell(cellDados);

//        PdfPCell cellFotoTreino = createImageCell(document, imagemTreino);
        PdfPCell cellFotoTreino = addCellVazia(0);
        cellFotoTreino.setBorder(0);
        adicionarPaddingFull(cellFotoTreino, 5);
        cellFotoTreino.setPaddingTop(18);
        cabecalho.addCell(cellFotoTreino);

        cabecalho.setWidthPercentage(100);
        PdfPCell cellNome = addCell(criarTexto(12, "bold",
                viewUtils.getLabel("AVALIACAO_INTEGRADA").concat(": ").concat(item.getCliente().getNome())), 3, 0,
                Element.ALIGN_LEFT, Element.ALIGN_MIDDLE, null, Rectangle.NO_BORDER);
        cellNome.setPaddingTop(20);
        cellNome.setPaddingBottom(20);
        cabecalho.addCell(cellNome);

        PdfPCell cellData = addCell(criarTexto(10, "normal",
                viewUtils.getLabel("respondida.em").concat(": ").concat(Uteis.getData(item.getDataResposta()))), 3, 0,
                Element.ALIGN_LEFT, Element.ALIGN_MIDDLE, null, Rectangle.NO_BORDER);
        cellData.setPaddingTop(0);
        cellData.setPaddingBottom(10);
        cabecalho.addCell(cellData);

        PdfPTable geral = criarTabelaPerguntas(item, viewUtils, background, AgrupamentoAvaliacaoIntegradaEnum.GERAL, new float[]{0.3f, 3, 0.3f, 3, 0.3f, 3}, new Color(211, 211, 211));
        PdfPTable vida = criarTabelaPerguntas(item, viewUtils, background, AgrupamentoAvaliacaoIntegradaEnum.QUALIDADE_VIDA, new float[]{0.3f, 4.5f, 0.3f, 4.5f}, new Color(247, 238, 199));
        PdfPTable movimento = criarTabelaPerguntas(item, viewUtils, background, AgrupamentoAvaliacaoIntegradaEnum.QUALIDADE_MOVIMENTO, new float[]{0.3f, 4.5f, 0.3f, 4.5f}, new Color(216, 194, 247));
        movimento.addCell(addCellVazia(20));
        PdfPTable movimento3d = criarTabelaMovimento3D(item, viewUtils, background);
        PdfPTable resultado = tabelaResultados(item, viewUtils, background);

        document.add(cabecalho);
        document.add(geral);
        document.add(vida);
        document.add(movimento);

        document.add(movimento3d);
        document.add(resultado);
        document.close();
    }

    private static PdfPTable criarTabelaPerguntas(ItemAvaliacaoFisica item,
                                                   ViewUtils viewUtils, GrayColor background, AgrupamentoAvaliacaoIntegradaEnum agru,
                                                   float[] cols,
                                                   Color cor) throws Exception {
        PdfPTable table = new PdfPTable(1);
        table.setWidthPercentage(100);
        PdfPCell header = addCell(criarTexto(12, "bold", viewUtils.getLabel(agru.name())),
                Element.ALIGN_LEFT, background);
        header.setPadding(10);
        header.setPaddingTop(5);
        table.addCell(header);

        for (PerguntaAnamnese pa : item.getAnamnese().getPerguntas()) {
            if (pa.getPergunta().getAgrupamento() != null
                    && pa.getPergunta().getAgrupamento().equals(agru)) {
                PdfPCell pergunta = addCellSemBorda(criarTexto(10, "bold", pa.getPergunta().getDescricao()), 0);

                PdfPTable opcoes = new PdfPTable(cols);
                table.setWidthPercentage(100);
                if (pa.getPergunta().getTipoPergunta().equals(TiposPerguntaEnum.TEXTUAL)) {
                    PdfPCell oppercell = addCellSemBorda(criarTexto(7, "normal",pa.getResposta()));
                    oppercell.setColspan(cols.length);
                    oppercell.setPadding(3);
                    oppercell.setVerticalAlignment(Element.ALIGN_CENTER);
                    opcoes.addCell(oppercell);
                } else {
                    for (OpcaoPergunta op : pa.getPergunta().getOpcoes()) {
                        PdfPCell oppercell = addCellSemBorda(criarTexto(7, "normal",
                                (op.getPeso() == null ? "" : (op.getPeso().toString().concat(" - "))).concat(op.getOpcao())));
                        oppercell.setPadding(3);
                        oppercell.setVerticalAlignment(Element.ALIGN_CENTER);
                        Boolean marcado = false;
                        if (pa.getPergunta().getTipoPergunta().equals(TiposPerguntaEnum.MULTIPLA_ESCOLHA)) {
                            if (pa.getRespostas().contains(op.getCodigo().toString())) {
                                marcado = true;
                            }
                        } else {
                            marcado = pa.getResposta() != null && pa.getResposta().equals(op.getCodigo().toString());
                        }
                        if (marcado) {
                            PdfPCell cellMarcada = addCellSemBorda(criarTexto(7, "bold", marcado ? "X" : ""), 0);
                            cellMarcada.setPadding(3);
                            cellMarcada.setBackgroundColor(cor);
                            oppercell.setBackgroundColor(cor);
                            opcoes.addCell(cellMarcada);
                        } else {
                            opcoes.addCell(addCellVazia(5));
                        }
                        opcoes.addCell(oppercell);
                    }
                }

                pergunta.setPaddingTop(10);
                table.addCell(pergunta);
                PdfPCell opcell = new PdfPCell(opcoes);
                opcell.setBorder(0);
                opcell.setPadding(10);
                table.addCell(opcell);
            }
        }
        return table;
    }


    public static PdfPTable criarTabelaMovimento3D(ItemAvaliacaoFisica item,
                                                   ViewUtils viewUtils, GrayColor background) throws Exception {
        PdfPTable table = new PdfPTable(1);
        table.setWidthPercentage(100);
        PdfPCell header = addCell(criarTexto(12, "bold", viewUtils.getLabel("movimento3d")),
                Element.ALIGN_LEFT, background);
        header.setPadding(10);
        header.setPaddingTop(5);
        table.addCell(header);
        table.addCell(addCellVazia(5));
        table.addCell(addCellSemBorda(criarTexto(8, "bold", viewUtils.getLabel("hint_movimento3d"))));
        table.addCell(addCellSemBorda(criarTexto(8, "normal", viewUtils.getLabel("placar0"))));
        table.addCell(addCellSemBorda(criarTexto(8, "normal", viewUtils.getLabel("placar1"))));
        table.addCell(addCellSemBorda(criarTexto(8, "normal", viewUtils.getLabel("placar2"))));
        table.addCell(addCellSemBorda(criarTexto(8, "normal", viewUtils.getLabel("placar3"))));
        table.addCell(addCellVazia(5));

        PdfPTable mobilidade = new PdfPTable(new float[]{8, 1, 1});
        mobilidade.addCell(addCellSemBorda(criarTexto(8, "bold", viewUtils.getLabel("mobilidade")), 0, background));
        mobilidade.addCell(addCellSemBorda(criarTexto(8, "bold", viewUtils.getLabel("esq")), 0, background));
        mobilidade.addCell(addCellSemBorda(criarTexto(8, "bold", viewUtils.getLabel("dir")), 0, background));

        for (Movimento3D m : item.getMobilidade()) {
            mobilidade.addCell(addCellSemBorda(criarTexto(8, "normal", viewUtils.getLabel(m.getMovimento().name())), 0));
            mobilidade.addCell(addCellSemBorda(criarTexto(8, "normal", m.getEsquerda().toString()), 0));
            mobilidade.addCell(addCellSemBorda(criarTexto(8, "normal", m.getDireita().toString()), 0));
        }
        Color cor = new Color(216, 194, 247);
        mobilidade.addCell(addCellVazia(5));
        mobilidade.addCell(addCellVazia(5));
        mobilidade.addCell(addCellVazia(5));
        mobilidade.addCell(addCellSemBorda(criarTexto(8, "bold", viewUtils.getLabel("placar_soma")), 0));
        mobilidade.addCell(addCellSemBorda(criarTexto(8, "bold", item.getSomaMobilidadeEsq().toString()), 0));
        mobilidade.addCell(addCellSemBorda(criarTexto(8, "bold", item.getSomaMobilidadeDir().toString()), 0));
        mobilidade.addCell(addCellSemBorda(criarTexto(8, "bold", viewUtils.getLabel("placar_media")), cor));
        mobilidade.addCell(addCellSemBorda(criarTexto(8, "bold", item.getMediaMobilidadeEsq().toString()), cor));
        mobilidade.addCell(addCellSemBorda(criarTexto(8, "bold", item.getMediaMobilidadeDir().toString()), cor));


        table.addCell(new PdfPCell(mobilidade));
        table.addCell(addCellVazia(5));

        PdfPTable estabilidade = new PdfPTable(new float[]{8, 1, 1});
        estabilidade.addCell(addCellSemBorda(criarTexto(8, "bold", viewUtils.getLabel("estabilidade_controle")), 0, background));
        estabilidade.addCell(addCellSemBorda(criarTexto(8, "bold", viewUtils.getLabel("esq")), 0, background));
        estabilidade.addCell(addCellSemBorda(criarTexto(8, "bold", viewUtils.getLabel("dir")), 0, background));

        for (Movimento3D e : item.getEstabilidade()) {
            estabilidade.addCell(addCellSemBorda(criarTexto(8, "normal", viewUtils.getLabel(e.getMovimento().name())), 0));
            estabilidade.addCell(addCellSemBorda(criarTexto(8, "normal", e.getEsquerda().toString()), 0));
            estabilidade.addCell(addCellSemBorda(criarTexto(8, "normal", e.getDireita().toString()), 0));
        }
        estabilidade.addCell(addCellVazia(5));
        estabilidade.addCell(addCellVazia(5));
        estabilidade.addCell(addCellVazia(5));


        estabilidade.addCell(addCellSemBorda(criarTexto(8, "bold", viewUtils.getLabel("placar_soma")), 0));
        estabilidade.addCell(addCellSemBorda(criarTexto(8, "bold", item.getSomaEstabilidadeEsq().toString()), 0));
        estabilidade.addCell(addCellSemBorda(criarTexto(8, "bold", item.getSomaEstabilidadeDir().toString()), 0));
        estabilidade.addCell(addCellSemBorda(criarTexto(8, "bold", viewUtils.getLabel("placar_media")), cor));
        estabilidade.addCell(addCellSemBorda(criarTexto(8, "bold", item.getMediaEstabilidadeEsq().toString()), cor));
        estabilidade.addCell(addCellSemBorda(criarTexto(8, "bold", item.getMediaEstabilidadeDir().toString()), cor));

        table.addCell(new PdfPCell(estabilidade));


        return table;
    }

    public static PdfPTable tabelaResultados(ItemAvaliacaoFisica item, ViewUtils viewUtils, GrayColor background) throws Exception {
        PdfPTable table = new PdfPTable(6);
        table.setWidthPercentage(100);
        PdfPCell vazia = addCellVazia(20);
        vazia.setColspan(6);
        PdfPCell header = addCell(criarTexto(12, "bold", viewUtils.getLabel("cadastros.aluno.resultado")),
                Element.ALIGN_LEFT, background);
        header.setPadding(10);
        header.setColspan(6);
        header.setPaddingTop(5);
        table.addCell(vazia);
        table.addCell(header);
        vazia.setPadding(10);
        table.addCell(vazia);
        table.addCell(addCellVazia(0));

        Color amarelo = new Color(247, 238, 199);
        Color roxo = new Color(216, 194, 247);

        table.addCell(addCell(criarTextoBold(viewUtils.getLabel("resultado_qualidade_movimento")), 5, 0, Element.ALIGN_CENTER, Element.ALIGN_MIDDLE, roxo, null));

        table.addCell(addCell(criarTextoBold(viewUtils.getLabel("resultado_qualidade_vida")), amarelo));
        for (ResultadoMovimentoEnum rm : ResultadoMovimentoEnum.values()) {
            table.addCell(addCell(criarTextoBold(viewUtils.getLabel(rm.getTitle())), roxo));
        }
        for (ResultadoVidaEnum rv : ResultadoVidaEnum.values()) {
            table.addCell(addCell(criarTextoBold(viewUtils.getLabel(rv.getTitle())), amarelo));
            for (ResultadoMovimentoEnum rm : ResultadoMovimentoEnum.values()) {
                if (rm.equals(item.getQualidadeMovimento()) && rv.equals(item.getQualidadeVida())) {
                    PdfPTable t = new PdfPTable(1);
                    PdfPCell cell1 = new PdfPCell(criarTexto(6, "normal",
                            viewUtils.getLabel("resultado_qualidade_vida") + ":" + viewUtils.getLabel(rv.name())
                                    + " (" + item.getSomaQualidadeVida() + " pts)"));
                    cell1.setBorder(0);
                    t.addCell(cell1);
                    PdfPCell cell2 = new PdfPCell(criarTexto(6, "normal",
                            viewUtils.getLabel("resultado_qualidade_movimento") + ":" + viewUtils.getLabel(rm.name())
                                    + " (" + item.getSomaQualidadeMovimento() + " pts)"));
                    cell2.setBorder(0);
                    t.addCell(cell2);
                    table.addCell(new PdfPCell(t));
                } else {
                    table.addCell(criarTexto(""));
                }
            }
        }

        return table;

    }

    public static void criarPDFComparacaoAvaliacaoIntegrada(String key, Usuario usuario, List<ItemAvaliacaoFisica> itens,
                                                            ViewUtils viewUtils, ServletContext servletContext,
                                                            final ByteArrayOutputStream baos) throws Exception {
        // criação do documento
        Document document = new Document();
        PdfWriter pdfWriter = PdfWriter.getInstance(document, baos);
        document.open();

        for (int i = 0; itens.size() > i; i++) {
            //icone treino
            final String imagemTreino = servletContext.getRealPath("resources") + File.separator + "imagens" + File.separator + "pacto_home.png";

            GrayColor background = new GrayColor(0.8f);
            float[] columns = {1, 7, 1};
            PdfPTable cabecalho = new PdfPTable(columns);
            PdfPCell cellImgEmpresa = new PdfPCell();
            PdfPCell cellDados = new PdfPCell();
            if (usuario.getEmpresaDefault() != null) {
                String genKey = MidiaService.getInstance().genKey(key, MidiaEntidadeEnum.FOTO_EMPRESA_RELATORIO, usuario.getEmpresaDefault().getCodigo().toString());
                String urlFoto = Aplicacao.obterUrlFotoDaNuvem(genKey);
                try {
                    cellImgEmpresa = createImageCell(document, urlFoto);
                } catch (Exception e) {

                }

                Font font = new Font();
                font.setSize(7);
                cellDados.addElement(new Paragraph(usuario.getEmpresaDefault().getNome(), font));
                cellDados.addElement(new Paragraph(usuario.getEmpresaDefault().getEndereco(), font));
                cellDados.addElement(new Paragraph(usuario.getEmpresaDefault().getCidade() + " - " + usuario.getEmpresaDefault().getEstado(), font));
                cellDados.addElement(new Paragraph(usuario.getEmpresaDefault().getSite(), font));

            }
            adicionarPaddingFull(cellImgEmpresa, 5);
            adicionarPaddingFull(cellDados, 5);
            cellDados.setBorder(0);
            cellImgEmpresa.setBorder(0);
            cabecalho.addCell(cellImgEmpresa);
            cabecalho.addCell(cellDados);

//        PdfPCell cellFotoTreino = createImageCell(document, imagemTreino);
            PdfPCell cellFotoTreino = addCellVazia(0);
            cellFotoTreino.setBorder(0);
            adicionarPaddingFull(cellFotoTreino, 5);
            cellFotoTreino.setPaddingTop(18);
            cabecalho.addCell(cellFotoTreino);

            cabecalho.setWidthPercentage(100);
            PdfPCell cellNome = addCell(criarTexto(12, "bold",
                    viewUtils.getLabel("AVALIACAO_INTEGRADA").concat(": ").concat(itens.get(0).getCliente().getNome())), 3, 0,
                    Element.ALIGN_LEFT, Element.ALIGN_MIDDLE, null, Rectangle.NO_BORDER);
            cellNome.setPaddingTop(20);
            cellNome.setPaddingBottom(20);
            cabecalho.addCell(cellNome);

            PdfPCell cellData = addCell(criarTexto(10, "normal",
                    viewUtils.getLabel("respondida.em").concat(": ").concat(Uteis.getData(itens.get(i).getDataResposta()))), 3, 0,
                    Element.ALIGN_LEFT, Element.ALIGN_MIDDLE, null, Rectangle.NO_BORDER);
            cellData.setPaddingTop(0);
            cellData.setPaddingBottom(10);
            cabecalho.addCell(cellData);

            ItemAvaliacaoFisica item = itens.get(i);
            PdfPTable geral = criarTabelaPerguntasComparativo(item, viewUtils, background, AgrupamentoAvaliacaoIntegradaEnum.GERAL, new float[]{0.3f, 3, 0.3f, 3, 0.3f, 3}, new Color(211, 211, 211));
            PdfPTable vida = criarTabelaPerguntasComparativo(item, viewUtils, background, AgrupamentoAvaliacaoIntegradaEnum.QUALIDADE_VIDA, new float[]{0.3f, 4.5f, 0.3f, 4.5f}, new Color(247, 238, 199));
            PdfPTable movimento = criarTabelaPerguntasComparativo(item, viewUtils, background, AgrupamentoAvaliacaoIntegradaEnum.QUALIDADE_MOVIMENTO, new float[]{0.3f, 4.5f, 0.3f, 4.5f}, new Color(216, 194, 247));
            movimento.addCell(addCellVazia(15));
            PdfPTable movimento3d = criarTabelaMovimento3D(item, viewUtils, background);
            PdfPTable resultado = tabelaResultados(item, viewUtils, background);

            document.add(cabecalho);
            document.add(geral);
            document.add(vida);
            document.add(movimento);

            document.add(movimento3d);
            document.add(resultado);
            movimento.addCell(addCellVazia(25));
        }

        document.close();
    }


    private static PdfPTable criarTabelaPerguntasComparativo(ItemAvaliacaoFisica item,
                                                  ViewUtils viewUtils, GrayColor background, AgrupamentoAvaliacaoIntegradaEnum agru,
                                                  float[] cols,
                                                  Color cor) throws Exception {
        PdfPTable table = new PdfPTable(1);
        table.setWidthPercentage(100);
        PdfPCell header = addCell(criarTexto(12, "bold", viewUtils.getLabel(agru.name())),
                Element.ALIGN_LEFT, background);
        header.setPadding(10);
        header.setPaddingTop(5);
        table.addCell(header);

        for (PerguntaAnamnese pa : item.getAnamnese().getPerguntas()) {
            if (pa.getPergunta().getAgrupamento() != null
                    && pa.getPergunta().getAgrupamento().equals(agru)) {
                PdfPCell pergunta = addCellSemBorda(criarTexto(10, "bold", pa.getPergunta().getDescricao()), 0);

                PdfPTable opcoes = new PdfPTable(cols);
                table.setWidthPercentage(100);
                if (pa.getPergunta().getTipoPergunta().equals(TiposPerguntaEnum.TEXTUAL)) {
                    PdfPCell oppercell = addCellSemBorda(criarTexto(7, "normal",pa.getResposta()));
                    oppercell.setColspan(cols.length);
                    oppercell.setPadding(3);
                    oppercell.setVerticalAlignment(Element.ALIGN_CENTER);
                    opcoes.addCell(oppercell);
                } else {
                    for (OpcaoPergunta op : pa.getPergunta().getOpcoes()) {
                        PdfPCell oppercell = addCellSemBorda(criarTexto(7, "normal",
                                (op.getPeso() == null ? "" : (op.getPeso().toString().concat(" - "))).concat(op.getOpcao())));
                        oppercell.setPadding(3);
                        oppercell.setVerticalAlignment(Element.ALIGN_CENTER);
                        Boolean marcado = false;
                        if (pa.getPergunta().getTipoPergunta().equals(TiposPerguntaEnum.MULTIPLA_ESCOLHA)) {
                            if (pa.getRespostas().contains(op.getCodigo().toString())) {
                                marcado = true;
                            }
                        } else {
                            marcado = pa.getResposta() != null && pa.getResposta().equals(op.getCodigo().toString());
                        }
                        if (marcado) {
                            PdfPCell cellMarcada = addCellSemBorda(criarTexto(7, "bold", marcado ? "" : ""), 0);
                            cellMarcada.setPadding(3);
                            cellMarcada.setBackgroundColor(cor);
                            oppercell.setBackgroundColor(cor);
                            opcoes.addCell(cellMarcada);
                        } else {
                            opcoes.addCell(addCellVazia(5));
                        }
                        opcoes.addCell(oppercell);
                    }
                }

                pergunta.setPaddingTop(10);
                table.addCell(pergunta);
                PdfPCell opcell = new PdfPCell(opcoes);
                opcell.setBorder(0);
                opcell.setPadding(10);
                table.addCell(opcell);
            }
        }
        return table;
    }
}
