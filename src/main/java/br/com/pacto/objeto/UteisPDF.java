/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.objeto;

import br.com.pacto.bean.anamnese.AgrupamentoAvaliacaoIntegradaEnum;
import br.com.pacto.bean.anamnese.Movimento3D;
import br.com.pacto.bean.anamnese.OpcaoPergunta;
import br.com.pacto.bean.anamnese.PerguntaAnamnese;
import br.com.pacto.bean.avaliacao.AvaliacaoFisica;
import br.com.pacto.bean.avaliacao.ItemAvaliacaoFisica;
import br.com.pacto.bean.avaliacao.ItemImpressaoAvaliacao;
import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.enumerador.anamnese.TiposPerguntaEnum;
import br.com.pacto.util.ViewUtils;
import com.lowagie.text.*;
import com.lowagie.text.Font;
import com.lowagie.text.Image;
import com.lowagie.text.Rectangle;
import com.lowagie.text.pdf.*;
import servicos.integracao.midias.MidiaService;
import servicos.integracao.midias.commons.MidiaEntidadeEnum;

import javax.imageio.ImageIO;
import javax.servlet.ServletContext;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.net.URL;
import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.Locale;

/**
 * <AUTHOR>
 */
public class UteisPDF {


    public static Phrase criarTexto(String texto) {
        return criarTexto(10, "", texto);
    }

    public static Phrase criarTextoBold(String texto) {
        return criarTexto(8, "bold", texto);
    }

    public static Phrase criarTexto(float fontSize, String estilo, String texto) {
        Phrase phrase = new Phrase();
        Font font = new Font();
        font.setStyle(estilo);
        font.setSize(fontSize);
        phrase.setFont(font);
        phrase.add(texto);
        return phrase;
    }

    public static void adicionarPaddingFull(PdfPCell cell, int padding) {
        cell.setPaddingTop(padding);
        cell.setPaddingBottom(padding);
        cell.setPaddingLeft(padding);
        cell.setPaddingRight(padding);
    }

    public static PdfPCell createImageCell(Document document, String path) throws DocumentException, IOException {
        Image img = Image.getInstance(path);
        float scaler = ((document.getPageSize().getWidth() - document.leftMargin()
                - document.rightMargin() - 100) / img.getWidth()) * 100;

        img.scalePercent(scaler);
        PdfPCell cell = new PdfPCell(img, true);
        return cell;
    }

    public static PdfPCell addCell(Phrase texto, int colspan, int rowspan, int horizontal, int vertical, Color background, Integer border) {
        PdfPCell cell = new PdfPCell(texto);
        if (rowspan > 0) {
            cell.setRowspan(rowspan);
        }
        if (colspan > 0) {
            cell.setColspan(colspan);
        }
        cell.setHorizontalAlignment(horizontal);
        cell.setVerticalAlignment(vertical);
        if (background != null) {
            cell.setBackgroundColor(background);
        }
        if (border != null) {
            cell.setBorder(border);
        }

        return cell;
    }
    public static PdfPCell addCellVazia(int padding) throws Exception{
        PdfPCell cell = new PdfPCell(criarTexto(""));
        adicionarPaddingFull(cell, padding);
        cell.setBorder(0);
        return cell;
    }

    public static PdfPCell addCell(Phrase texto, Color cor) {
        return addCell(texto, 0, 0, Element.ALIGN_LEFT, Element.ALIGN_MIDDLE, cor, null);
    }
    public static PdfPCell addCell(Phrase texto, int horizontal) {
        return addCell(texto, 0, 0, horizontal, Element.ALIGN_MIDDLE, null, null);
    }


    public static PdfPCell addCell(Phrase texto, int horizontal, GrayColor background) {
        return addCell(texto, 0, 0, horizontal, Element.ALIGN_MIDDLE, background, null);
    }

    public static PdfPCell addCell(Phrase texto) {
        return addCell(texto, 0, 0, Element.ALIGN_LEFT, Element.ALIGN_MIDDLE, null, null);
    }

    public static PdfPCell addCellSemBorda(Phrase texto) {
        return addCellSemBorda(texto, 0 , null);
    }
    public static PdfPCell addCellSemBorda(Phrase texto, int colspan) {
        return addCellSemBorda(texto, colspan, null);
    }

    public static PdfPCell addCellSemBorda(Phrase texto, Color background) {
        return addCell(texto, 0, 0, Element.ALIGN_LEFT, Element.ALIGN_MIDDLE, background, Rectangle.NO_BORDER);
    }
    public static PdfPCell addCellSemBorda(Phrase texto, int colspan, Color background) {
        return addCell(texto, colspan, 0, Element.ALIGN_LEFT, Element.ALIGN_MIDDLE, background, Rectangle.NO_BORDER);
    }

    public static PdfPTable cabecalho(float[] columns, Usuario usuario, String key, Document document) throws Exception{
        PdfPTable cabecalho = new PdfPTable(columns);
        PdfPCell cellImgEmpresa = new PdfPCell();
        PdfPCell cellDados = new PdfPCell();
        if (usuario.getEmpresaDefault() != null) {
            String genKey = MidiaService.getInstance().genKey(key, MidiaEntidadeEnum.FOTO_EMPRESA_RELATORIO, usuario.getEmpresaDefault().getCodigo().toString());
            String urlFoto = Aplicacao.obterUrlFotoDaNuvem(genKey);
            cellImgEmpresa = createImageCell(document, urlFoto);
            Font font = new Font();
            font.setSize(7);
            cellDados.addElement(new Paragraph(usuario.getEmpresaDefault().getNome(), font));
            cellDados.addElement(new Paragraph(usuario.getEmpresaDefault().getEndereco(), font));
            cellDados.addElement(new Paragraph(usuario.getEmpresaDefault().getCidade() + " - " + usuario.getEmpresaDefault().getEstado(), font));
            cellDados.addElement(new Paragraph(usuario.getEmpresaDefault().getSite(), font));

        }
        adicionarPaddingFull(cellImgEmpresa, 5);
        adicionarPaddingFull(cellDados, 5);
        cellDados.setBorder(0);
        cellImgEmpresa.setBorder(0);
        cabecalho.addCell(cellImgEmpresa);
        cabecalho.addCell(cellDados);

//        PdfPCell cellFotoTreino = createImageCell(document, imagemTreino);
        PdfPCell cellFotoTreino = addCellVazia(0);
        cellFotoTreino.setBorder(0);
        adicionarPaddingFull(cellFotoTreino, 5);
        cellFotoTreino.setPaddingTop(18);
        cabecalho.addCell(cellFotoTreino);

        cabecalho.setWidthPercentage(100);

        return cabecalho;
    }


}
