/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.objeto;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;
import java.util.TimeZone;

/**
 *
 * <AUTHOR>
 */
public class JSONMapper {

    public static <T> List<T> getList(final JSONArray array, Class<T> clazz) throws Exception {
        return getList(array, clazz, false);
    }
    public static <T> List<T> getList(final JSONArray array, Class<T> clazz, boolean ignoreUNKNOWN) throws Exception {
        ObjectMapper mapper = new ObjectMapper();
        if (ignoreUNKNOWN) {
            mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        }
        List lista = new ArrayList();
        for (int i = 0; i < array.length(); i++) {
            JSONObject o = array.getJSONObject(i);
            Object bean = mapper.readValue(o.toString(), clazz);
            lista.add(bean);
        }
        return lista;
    }

    public static <T> List<T> getListFromTimeZone(final JSONArray array, Class<T> clazz, String timeZone) throws Exception {
        ObjectMapper mapper = new ObjectMapper();
        if(!Uteis.valorVazioString(timeZone)){
            mapper.setTimeZone(TimeZone.getTimeZone(timeZone));
        }

        List lista = new ArrayList();
        for (int i = 0; i < array.length(); i++) {
            JSONObject o = array.getJSONObject(i);
            Object bean = mapper.readValue(o.toString(), clazz);
            lista.add(bean);
        }
        return lista;
    }

    public static <T> T getObject(final JSONObject object, Class<T> clazz) throws Exception {
        ObjectMapper mapper = new ObjectMapper();
        return mapper.readValue(object.toString(), clazz);
    }
}
