/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.objeto;

import br.com.pacto.base.impl.EntityManagerFactoryService;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.io.Serializable;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLConnection;
import java.net.URLEncoder;
import java.util.Map;
import java.util.Set;

/**
 *
 * <AUTHOR>
 */
public class HttpRequestUtil implements Serializable {

    public static String executeRequestInner(String urlRequest,
            Map<String, String> params, final int timeout) throws IOException {
        String parametrosCodificados = "";
        if (params != null) {
            Uteis.logar(null, params.toString());
            Set<String> s = params.keySet();
            String separador = "";

            for (String paramName : s) {
                String valor = params.get(paramName);
                if (valor != null) {
                    parametrosCodificados +=
                            separador
                            + paramName + "="
                            + URLEncoder.encode(valor, "iso-8859-1");
                    if (separador.isEmpty()) {
                        separador = "&";
                    }
                }

            }
        }

        // Envia parametros
        URL url = new URL(urlRequest);
        URLConnection conn = url.openConnection();
        if (timeout != 0) {
            conn.setReadTimeout(timeout);
            conn.setConnectTimeout(timeout);
        }
        conn.setDoOutput(true);

        OutputStreamWriter wr = new OutputStreamWriter(conn.getOutputStream());
        String resposta = "";
        try {
            wr.write(parametrosCodificados);
            wr.flush();
            // Pega a Resposta
            BufferedReader rd = new BufferedReader(new InputStreamReader(conn.getInputStream()));
            try {
                String line;
                while ((line = rd.readLine()) != null) {
                    // Processa linha a linha
                    resposta += line + "\n";
                }
            } catch (IOException io) {
                int respCode = ((HttpURLConnection) conn).getResponseCode();
                InputStreamReader es = new InputStreamReader(((HttpURLConnection) conn).getErrorStream());
                int ret = 0;
                // read the response body
                char[] buf = null;
                while ((ret = es.read(buf)) > 0) {
                }
                // close the errorstream
                es.close();
            } finally {
                rd.close();
            }
        } finally {
            wr.close();
        }
        Uteis.logar(null, resposta);
        return resposta;
    }

    public static void main(String... o) {
        Set<String> keys = EntityManagerFactoryService.getEmpresas().keySet();
        for (String k : keys) {
            ThreadRequest t = new ThreadRequest("http://localhost:8084/TreinoWeb/prest/filaImpressao/" + k + "/get", null, -1);
            t.start();
        }

    }
}
