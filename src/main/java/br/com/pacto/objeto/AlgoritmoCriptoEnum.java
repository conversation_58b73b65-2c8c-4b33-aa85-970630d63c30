/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pacto.objeto;

/**
 *
 * <AUTHOR>
 */
public enum AlgoritmoCriptoEnum {

    ALGORITMO_TRIPLE_DES("DESede", "DESede/CBC/PKCS5Padding", 24),
    ALGORITMO_DES("DES", "DES/ECB/PKCS5Padding", 8),
    ALGORITMO_BLOWFISH("Blowfish", "Blowfish/CBC/PKCS5Padding", 16),
    ALGORITMO_AES("AES", "AES/CBC/PKCS5Padding", 16);
    private String id;
    private String cbc;
    private int tamanho;

    private AlgoritmoCriptoEnum(String id, String idCBC, int tamanho) {
        this.id = id;
        this.cbc = idCBC;
        this.tamanho = tamanho;
    }

    public String getCbc() {
        return cbc;
    }

    public void setCbc(String cbc) {
        this.cbc = cbc;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public int getTamanho() {
        return tamanho;
    }

    public void setTamanho(int tamanho) {
        this.tamanho = tamanho;
    }
}
