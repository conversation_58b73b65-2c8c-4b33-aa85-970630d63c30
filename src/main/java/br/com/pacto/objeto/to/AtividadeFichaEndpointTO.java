package br.com.pacto.objeto.to;

import br.com.pacto.bean.atividade.MetodoExecucaoEnum;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.List;

/**
 * Created by ulisses on 05/08/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AtividadeFichaEndpointTO {

    private Integer id;
    private List<Integer> atividadesFichaId;
    private Integer sequencia;
    private Integer atividadeId;
    private Integer esforco;
    private MetodoExecucaoEnum metodoExecucao;
    private Integer fichaId;
    private Integer series;
    private String repeticoes;
    private String carga;
    private String cadencia;
    private Integer descanso = 0;
    private String complemento;
    private Double velocidade = 0.0;
    private Integer duracao = 0;
    private Integer distancia = 0;
    private List<Integer> setAtividadesIds;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public List<Integer> getAtividadesFichaId() {
        return atividadesFichaId;
    }

    public void setAtividadesFichaId(List<Integer> atividadesFichaId) {
        this.atividadesFichaId = atividadesFichaId;
    }

    public Integer getSequencia() {
        return sequencia;
    }

    public void setSequencia(Integer sequencia) {
        this.sequencia = sequencia;
    }

    public Integer getAtividadeId() {
        return atividadeId;
    }

    public void setAtividadeId(Integer atividadeId) {
        this.atividadeId = atividadeId;
    }

    public Integer getEsforco() {
        return esforco;
    }

    public void setEsforco(Integer esforco) {
        this.esforco = esforco;
    }

    public MetodoExecucaoEnum getMetodoExecucao() {
        return metodoExecucao;
    }

    public void setMetodoExecucao(MetodoExecucaoEnum metodoExecucao) {
        this.metodoExecucao = metodoExecucao;
    }

    public Integer getFichaId() {
        return fichaId;
    }

    public void setFichaId(Integer fichaId) {
        this.fichaId = fichaId;
    }

    public Integer getSeries() {
        return series;
    }

    public void setSeries(Integer series) {
        this.series = series;
    }

    public String getRepeticoes() {
        return repeticoes == null ? "" : repeticoes;
    }

    public void setRepeticoes(String repeticoes) {
        this.repeticoes = repeticoes;
    }

    public String getCarga() {
        return carga == null ? "" : carga;
    }

    public void setCarga(String carga) {
        this.carga = carga;
    }

    public String getCadencia() {
        return cadencia;
    }

    public void setCadencia(String cadencia) {
        this.cadencia = cadencia;
    }

    public Integer getDescanso() {
        return descanso == null ? 0 : descanso;
    }

    public void setDescanso(Integer descanso) {
        this.descanso = descanso;
    }

    public String getComplemento() {
        return complemento;
    }

    public void setComplemento(String complemento) {
        this.complemento = complemento;
    }

    public Double getVelocidade() {
        return velocidade == null ? 0.0 : velocidade;
    }

    public void setVelocidade(Double velocidade) {
        this.velocidade = velocidade;
    }

    public Integer getDuracao() {
        return duracao == null ? 0 : duracao;
    }

    public void setDuracao(Integer duracao) {
        this.duracao = duracao;
    }

    public Integer getDistancia() {
        return distancia == null ? 0 : distancia;
    }

    public void setDistancia(Integer distancia) {
        this.distancia = distancia;
    }

    public List<Integer> getSetAtividadesIds() {
        return setAtividadesIds;
    }

    public void setSetAtividadesIds(List<Integer> setAtividadesIds) {
        this.setAtividadesIds = setAtividadesIds;
    }
}
