/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.objeto.to;

/**
 *
 * <AUTHOR>
 */
public class AtividadeFichaTO {

    private Integer codigoAtividade;
    private String nomeAtividade;
    private boolean aerobica = false;
    private String ficha;
    private String fichaOriginal;
    private String ordem = "";
    private Integer series = 0;
    private Integer repeticoes = 0;
    private Double carga = 0.0;
    private Integer duracao = 0;
    private Double velocidade = 0.0;
    private Integer descanso = 0;
    private Integer codigoAtividadeFicha;
    private String cargaVetor = "";
    private String repeticoesVetor = "";

    public Integer getCodigoAtividade() {
        return codigoAtividade;
    }

    public void setCodigoAtividade(Integer codigoAtividade) {
        this.codigoAtividade = codigoAtividade;
    }

    public String getNomeAtividade() {
        return nomeAtividade;
    }

    public void setNomeAtividade(String nomeAtividade) {
        this.nomeAtividade = nomeAtividade;
    }

    public String getFicha() {
        return ficha;
    }

    public void setFicha(String ficha) {
        this.ficha = ficha;
    }

    public String getOrdem() {
        return ordem;
    }

    public void setOrdem(String ordem) {
        this.ordem = ordem;
    }

    public Integer getSeries() {
        return series;
    }

    public void setSeries(Integer series) {
        this.series = series;
    }

    public Integer getRepeticoes() {
        return repeticoes;
    }

    public void setRepeticoes(Integer repeticoes) {
        this.repeticoes = repeticoes;
    }

    public Double getCarga() {
        return carga;
    }

    public void setCarga(Double carga) {
        this.carga = carga;
    }

    public Integer getDuracao() {
        return duracao;
    }

    public void setDuracao(Integer duracao) {
        this.duracao = duracao;
    }

    public Double getVelocidade() {
        return velocidade;
    }

    public void setVelocidade(Double velocidade) {
        this.velocidade = velocidade;
    }

    public Integer getDescanso() {
        return descanso;
    }

    public void setDescanso(Integer descanso) {
        this.descanso = descanso;
    }

    public boolean isAerobica() {
        return aerobica;
    }

    public void setAerobica(boolean aerobica) {
        this.aerobica = aerobica;
    }

    public String getInformacoes(){
        return aerobica ? ("<span>"+((duracao/60) < 10 ? "D. 0" : "D. ")+(duracao/60)+"min</span> <span style=\"margin-left:20px;\">"
                + (velocidade < 10.0 ? "V. 0" : "V. ")+velocidade.intValue()+"km/h</span>")
                : ("<span>"+(series < 10 ? "S. 0" : "S. ")+series+"</span> <span style=\"margin-left:20px;\">"
                + ("R. ")+repeticoesVetor+"</span> <span style=\"margin-left:20px;\">"
                + ("C. ")+cargaVetor+" kg</span>");
    }

    public Integer getCodigoAtividadeFicha() {
        return codigoAtividadeFicha;
    }

    public void setCodigoAtividadeFicha(Integer codigoAtividadeFicha) {
        this.codigoAtividadeFicha = codigoAtividadeFicha;
    }

    public String getFichaOriginal() {
        return fichaOriginal;
    }

    public void setFichaOriginal(String fichaOriginal) {
        this.fichaOriginal = fichaOriginal;
    }

    public Integer getOrdemInt(){
        try {
            return Integer.valueOf(ordem.replaceAll(ficha, ""));
        } catch (Exception e) {
            return 99;
        }
    }

    public String getCargaVetor() {
        return cargaVetor;
    }

    public void setCargaVetor(String cargaVetor) {
        this.cargaVetor = cargaVetor;
    }

    public String getRepeticoesVetor() {
        return repeticoesVetor;
    }

    public void setRepeticoesVetor(String repeticoesVetor) {
        this.repeticoesVetor = repeticoesVetor;
    }
}
