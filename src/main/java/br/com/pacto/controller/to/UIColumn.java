package br.com.pacto.controller.to;

import java.io.IOException;
import java.util.List;

import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;

public interface UIColumn {
    Object getValueExpression(String var1);

    String getContainerClientId(FacesContext var1);

    String getColumnKey();

    String getClientId();

    String getClientId(FacesContext var1);

    String getSelectionMode();

    boolean isResizable();

    String getStyle();

    String getStyleClass();

    int getRowspan();

    int getColspan();

    boolean isDisabledSelection();

    String getFilterPosition();

    UIComponent getFacet(String var1);

    String getHeaderText();

    String getFooterText();

    String getFilterStyleClass();

    String getFilterStyle();

    String getFilterMatchMode();

    int getFilterMaxLength();

    Object getFilterOptions();

    CellEditor getCellEditor();

    boolean isDynamic();

    Object getSortFunction();

    List<UIComponent> getChildren();

    boolean isExportable();

    boolean isRendered();

    void encodeAll(FacesContext var1) throws IOException;

    String getWidth();
}
