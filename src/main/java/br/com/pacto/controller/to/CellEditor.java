package br.com.pacto.controller.to;

import java.util.ArrayList;
import java.util.List;
import javax.faces.application.ResourceDependencies;
import javax.faces.component.UIComponentBase;
import javax.faces.context.FacesContext;

@ResourceDependencies({})
public class CellEditor extends UIComponentBase {
    public static final String COMPONENT_TYPE = "org.primefaces.component.CellEditor";
    public static final String COMPONENT_FAMILY = "org.primefaces.component";
    private static final String DEFAULT_RENDERER = "org.primefaces.component.CellEditorRenderer";
    private static final String OPTIMIZED_PACKAGE = "org.primefaces.component.";

    public CellEditor() {
        this.setRendererType("org.primefaces.component.CellEditorRenderer");
    }

    public String getFamily() {
        return "org.primefaces.component";
    }

    public void processDecodes(FacesContext context) {
        if (this.isEditRequest(context)) {
            super.processDecodes(context);
        }

    }

    public void processValidators(FacesContext context) {
        if (this.isEditRequest(context)) {
            super.processValidators(context);
        }

    }

    public void processUpdates(FacesContext context) {
        if (this.isEditRequest(context)) {
            super.processUpdates(context);
        }

    }

    public boolean isEditRequest(FacesContext context) {
        return context.getExternalContext().getRequestParameterMap().containsKey(this.getClientId(context));
    }

    protected FacesContext getFacesContext() {
        return FacesContext.getCurrentInstance();
    }

    public void handleAttribute(String name, Object value) {


    }

    protected static enum PropertyKeys {
    }
}
