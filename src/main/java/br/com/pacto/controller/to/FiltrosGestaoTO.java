/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.controller.to;

import br.com.pacto.bean.gestao.SituacaoAlunoEnum;
import br.com.pacto.bean.gestao.SituacaoContratoEnum;
import br.com.pacto.bean.tipoEvento.TipoEvento;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.util.bean.GenericoTO;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;
import java.util.Set;
import java.util.HashSet;
import javax.faces.model.SelectItem;

/**
 *
 * <AUTHOR>
 */
public class FiltrosGestaoTO {

    private Date inicio = Calendario.inicioMes(Calendario.anterior(Calendar.MONTH, Calendario.hoje()));
    private Date fim = Calendario.fimMes(Calendario.hoje());
    private Integer mes;
    private Integer ano;
    private Set<String> professoresSelecionados = new HashSet<>();
    private List<String> alunosSelecionados = new ArrayList<String>();
    private List<SelectItem> professores = new ArrayList<SelectItem>();
    private List<SelectItem> alunos = new ArrayList<SelectItem>();
    private List<SelectItem> tiposNotificacao = new ArrayList<SelectItem>();
    private List<GenericoTO> gravidades = new ArrayList<GenericoTO>();
    private List<GenericoTO> situacoesAluno = new ArrayList<GenericoTO>();
    private List<GenericoTO> situacoesContrato = new ArrayList<GenericoTO>();
    private List<GenericoTO> opcoesProfessores = new ArrayList<GenericoTO>();
    private boolean novo = false;
    private boolean renovado = false;
    private boolean revisado = false;
    private boolean naoRevisado = false;
    private boolean vazio = false;

    private List<TipoEvento> tiposEvento = new ArrayList<TipoEvento>();
    private String horaInicio = "00:00";
    private String horaFim = "23:59";

    public Date getInicio() {
        return inicio;
    }
    
    public void montarSituacoes(){
        situacoesAluno = new ArrayList<GenericoTO>();
        situacoesContrato = new ArrayList<GenericoTO>();
        opcoesProfessores = new ArrayList<GenericoTO>();
        opcoesProfessores.add(new GenericoTO("IA", "Incluir Professores Inativos"));

        for(SituacaoAlunoEnum sitAl : SituacaoAlunoEnum.values()){
            situacoesAluno.add(new GenericoTO(sitAl.getCodigo(), sitAl.name()));
        }
        for(SituacaoContratoEnum sitCon : SituacaoContratoEnum.values()){
            situacoesContrato.add(new GenericoTO(sitCon.getCodigo(), sitCon.name()));
        }
    }

    public void setInicio(Date inicio) {
        this.inicio = inicio;
    }

    public Date getFim() {
        return fim;
    }

    public Integer getInicioMes(){
        return Uteis.getMesData(inicio);
    }
    
    public Integer getInicioAno(){
        return Uteis.getAnoData(inicio);
    }
    
    public void setInicioMes(Integer mes){
        Calendar cal = new GregorianCalendar();
        cal.setTime(inicio);
        cal.set(Calendar.MONTH, (mes-1));
        inicio = cal.getTime();
    }
    
    public void setInicioAno(Integer ano){
        Calendar cal = new GregorianCalendar();
        cal.setTime(inicio);
        cal.set(Calendar.YEAR, ano);
        inicio = cal.getTime();
    }

    public void resolveTempo() {
        inicio = Calendario.getDataComHoraZerada(inicio);
        fim = Calendario.getDataComHora(fim, "23:59:59");
    }

    public void setFim(Date fim) {
        this.fim = fim;
    }

    public Set<String> getProfessoresSelecionados() {
        return professoresSelecionados;
    }

    public void setProfessoresSelecionados(Set<String> professoresSelecionados) {
        this.professoresSelecionados = professoresSelecionados;
    }

    public List<SelectItem> getProfessores() {
        return professores;
    }

    public void setProfessores(List<SelectItem> professores) {
        this.professores = professores;
    }

    public List<TipoEvento> getTiposEvento() {
        return tiposEvento;
    }

    public void setTiposEvento(List<TipoEvento> tiposEvento) {
        this.tiposEvento = tiposEvento;
    }

    public List<GenericoTO> getGravidades() {
        return gravidades;
    }

    public void setGravidades(List<GenericoTO> gravidades) {
        this.gravidades = gravidades;
    }

    public List<SelectItem> getTiposNotificacao() {
        return tiposNotificacao;
    }

    public void setTiposNotificacao(List<SelectItem> tiposNotificacao) {
        this.tiposNotificacao = tiposNotificacao;
    }

    public String getHoraInicio() {
        return horaInicio;
    }

    public void setHoraInicio(String horaInicio) {
        this.horaInicio = horaInicio;
    }

    public String getHoraFim() {
        return horaFim;
    }

    public void setHoraFim(String horaFim) {
        this.horaFim = horaFim;
    }

    public List<GenericoTO> getSituacoesAluno() {
        return situacoesAluno;
    }

    public void setSituacoesAluno(List<GenericoTO> situacoesAluno) {
        this.situacoesAluno = situacoesAluno;
    }

    public List<GenericoTO> getSituacoesContrato() {
        return situacoesContrato;
    }

    public List<GenericoTO> getOpcoesProfessores() {
        return opcoesProfessores;
    }

    public void setOpcoesProfessores(List<GenericoTO> opcoesProfessores) {
        this.opcoesProfessores = opcoesProfessores;
    }

    public void setSituacoesContrato(List<GenericoTO> situacoesContrato) {
        this.situacoesContrato = situacoesContrato;
    }

    public boolean isNovo() {
        return novo;
    }

    public void setNovo(boolean novo) {
        this.novo = novo;
    }

    public boolean isRenovado() {
        return renovado;
    }

    public void setRenovado(boolean renovado) {
        this.renovado = renovado;
    }

    public boolean isRevisado() {
        return revisado;
    }

    public void setRevisado(boolean revisado) {
        this.revisado = revisado;
    }

    public boolean isNaoRevisado() {
        return naoRevisado;
    }

    public void setNaoRevisado(boolean naoRevisado) {
        this.naoRevisado = naoRevisado;
    }

    public boolean isVazio() { return vazio; }

    public void setVazio(boolean vazio) { this.vazio = vazio; }

    public List<SelectItem> getAlunos() {
        return alunos;
    }

    public void setAlunos(List<SelectItem> alunos) {
        this.alunos = alunos;
    }

    public List<String> getAlunosSelecionados() {
        return alunosSelecionados;
    }

    public void setAlunosSelecionados(List<String> alunosSelecionados) {
        this.alunosSelecionados = alunosSelecionados;
    }


    public boolean isApenasProfessoresAtivos() {
        //Joao Alcides: estou modificando o que o FRED fez, justamente pra manter como default o valor TRUE e não FALSE como estava
        return getOpcoesProfessores().isEmpty() || !getOpcoesProfessores().get(0).getEscolhido();
    }

    public Integer getMes() {
        return mes;
    }

    public void setMes(Integer mes) {
        this.mes = mes;
    }

    public Integer getAno() {
        return ano;
    }

    public void setAno(Integer ano) {
        this.ano = ano;
    }
}
