package br.com.pacto.controller.to;

import java.io.Serializable;
import java.util.Date;

public class DefaultScheduleEvent implements ScheduleEvent, Serializable {
    private String id;
    private String title;
    private Date startDate;
    private Date endDate;
    private boolean allDay = false;
    private String styleClass;
    private Object data;
    private boolean editable = true;

    public DefaultScheduleEvent() {
    }

    public DefaultScheduleEvent(String title, Date start, Date end) {
        this.title = title;
        this.startDate = start;
        this.endDate = end;
    }

    public DefaultScheduleEvent(String title, Date start, Date end, boolean allDay) {
        this.title = title;
        this.startDate = start;
        this.endDate = end;
        this.allDay = allDay;
    }

    public DefaultScheduleEvent(String title, Date start, Date end, String styleClass) {
        this.title = title;
        this.startDate = start;
        this.endDate = end;
        this.styleClass = styleClass;
    }

    public DefaultScheduleEvent(String title, Date start, Date end, Object data) {
        this.title = title;
        this.startDate = start;
        this.endDate = end;
        this.data = data;
    }

    public String getId() {
        return this.id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTitle() {
        return this.title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Date getStartDate() {
        return this.startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return this.endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public boolean isAllDay() {
        return this.allDay;
    }

    public void setAllDay(boolean allDay) {
        this.allDay = allDay;
    }

    public void setStyleClass(String styleClass) {
        this.styleClass = styleClass;
    }

    public String getStyleClass() {
        return this.styleClass;
    }

    public Object getData() {
        return this.data;
    }

    public void setData(Object data) {
        this.data = data;
    }

    public boolean isEditable() {
        return this.editable;
    }

    public void setEditable(boolean editable) {
        this.editable = editable;
    }

    public boolean equals(Object obj) {
        if (obj == null) {
            return false;
        } else if (this.getClass() != obj.getClass()) {
            return false;
        } else {
            DefaultScheduleEvent other;
            label44: {
                other = (DefaultScheduleEvent)obj;
                if (this.title == null) {
                    if (other.title == null) {
                        break label44;
                    }
                } else if (this.title.equals(other.title)) {
                    break label44;
                }

                return false;
            }

            if (this.startDate == other.startDate || this.startDate != null && this.startDate.equals(other.startDate)) {
                return this.endDate == other.endDate || this.endDate != null && this.endDate.equals(other.endDate);
            } else {
                return false;
            }
        }
    }

    public int hashCode() {
        int hash = 61 * 5 + (this.title != null ? this.title.hashCode() : 0);
        hash = 61 * hash + (this.startDate != null ? this.startDate.hashCode() : 0);
        hash = 61 * hash + (this.endDate != null ? this.endDate.hashCode() : 0);
        return hash;
    }

    public String toString() {
        return "DefaultScheduleEvent{title=" + this.title + ",startDate=" + this.startDate + ",endDate=" + this.endDate + "}";
    }
}
