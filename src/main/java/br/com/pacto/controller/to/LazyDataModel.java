package br.com.pacto.controller.to;

import javax.faces.model.DataModel;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

public abstract class LazyDataModel<T> extends DataModel<T> implements SelectableDataModel<T>, Serializable {
    private int rowIndex = -1;
    private int rowCount;
    private int pageSize;
    private List<T> data;

    public LazyDataModel() {
    }

    public boolean isRowAvailable() {
        if (this.data == null) {
            return false;
        } else {
            return this.rowIndex >= 0 && this.rowIndex < this.data.size();
        }
    }

    public int getRowCount() {
        return this.rowCount;
    }

    public T getRowData() {
        return this.data.get(this.rowIndex);
    }

    public int getRowIndex() {
        return this.rowIndex;
    }

    public void setRowIndex(int rowIndex) {
        this.rowIndex = rowIndex == -1 ? rowIndex : rowIndex % this.pageSize;
    }

    public Object getWrappedData() {
        return this.data;
    }

    public void setWrappedData(Object list) {
        this.data = (List)list;
    }

    public int getPageSize() {
        return this.pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public void setRowCount(int rowCount) {
        this.rowCount = rowCount;
    }

    public List<T> load(int first, int pageSize, String sortField, SortOrder sortOrder, Map<String, String> filters) {
        throw new UnsupportedOperationException("Lazy loading is not implemented.");
    }

    public List<T> load(int first, int pageSize, List<SortMeta> multiSortMeta, Map<String, String> filters) {
        throw new UnsupportedOperationException("Lazy loading is not implemented.");
    }

    public T getRowData(String rowKey) {
        throw new UnsupportedOperationException("getRowData(String rowKey) must be implemented when basic rowKey algorithm is not used.");
    }

    public Object getRowKey(T object) {
        throw new UnsupportedOperationException("getRowKey(T object) must be implemented when basic rowKey algorithm is not used.");
    }
}
