package br.com.pacto.controller.json.aluno;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * Created by paulo in 09/05/2019
 */

@JsonInclude(JsonInclude.Include.NON_NULL)
public class AlunoZWDTO {

    private Integer alunoId;
    private Integer professorId;
    private Boolean usarAplicativo;
    private String email;
    private String senha;


    public Integer getAlunoId() {
        return alunoId;
    }

    public void setAlunoId(Integer alunoId) {
        this.alunoId = alunoId;
    }

    public Integer getProfessorId() {
        return professorId;
    }

    public void setProfessorId(Integer professorId) {
        this.professorId = professorId;
    }

    public Boolean getUsarAplicativo() {
        return usarAplicativo;
    }

    public void setUsarAplicativo(Boolean usarAplicativo) {
        this.usarAplicativo = usarAplicativo;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getSenha() {
        return senha;
    }

    public void setSenha(String senha) {
        this.senha = senha;
    }
}
