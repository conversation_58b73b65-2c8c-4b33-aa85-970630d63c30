package br.com.pacto.controller.json.agendamento;

import br.com.pacto.bean.Agendamento;
import br.com.pacto.bean.colaborador.ColaboradorSimplesTO;
import br.com.pacto.controller.json.tipoEvento.TipoAgendamentoDTO;
import br.com.pacto.objeto.Calendario;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by paulo 03/08/2019
 */

public class ServicoAgendamentoDisponibilidadeDTO {

    Integer id;
    String dia;
    String horarioInicial;
    String horarioFinal;
    ColaboradorSimplesTO professor;
    TipoAgendamentoDTO tipoAgendamento;
    List<ServicoAgendamentoConflitanteDTO> agendamentosConflitantes = new ArrayList<>();

    public ServicoAgendamentoDisponibilidadeDTO(Agendamento agendamento, List<Agendamento> agendamentosConflitantes, Boolean treinoIndependente) {
        this.id = agendamento.getCodigo();
        this.dia = Calendario.getData(agendamento.getInicio(), "yyyyMMdd");
        this.horarioInicial = agendamento.getHoraInicioApresentar();
        this.horarioFinal = agendamento.getHoraFimApresentar();
        this.professor = new ColaboradorSimplesTO(agendamento.getProfessor(), treinoIndependente);
        this.tipoAgendamento = new TipoAgendamentoDTO(agendamento.getTipoEvento());
        for (Agendamento agendamentoConflitante : agendamentosConflitantes) {
            getAgendamentosConflitantes().add(new ServicoAgendamentoConflitanteDTO(agendamentoConflitante, treinoIndependente));
        }
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getDia() {
        return dia;
    }

    public void setDia(String dia) {
        this.dia = dia;
    }

    public String getHorarioInicial() {
        return horarioInicial;
    }

    public void setHorarioInicial(String horarioInicial) {
        this.horarioInicial = horarioInicial;
    }

    public String getHorarioFinal() {
        return horarioFinal;
    }

    public void setHorarioFinal(String horarioFinal) {
        this.horarioFinal = horarioFinal;
    }

    public ColaboradorSimplesTO getProfessor() {
        return professor;
    }

    public void setProfessor(ColaboradorSimplesTO professor) {
        this.professor = professor;
    }

    public TipoAgendamentoDTO getTipoAgendamento() {
        return tipoAgendamento;
    }

    public void setTipoAgendamento(TipoAgendamentoDTO tipoAgendamento) {
        this.tipoAgendamento = tipoAgendamento;
    }

    public List<ServicoAgendamentoConflitanteDTO> getAgendamentosConflitantes() {
        return agendamentosConflitantes;
    }

    public void setAgendamentosConflitantes(List<ServicoAgendamentoConflitanteDTO> agendamentosConflitantes) {
        this.agendamentosConflitantes = agendamentosConflitantes;
    }



}
