package br.com.pacto.controller.json.aluno;

/**
 * Created by paulo on 09/07/2019.
 */
public enum TipoContratoZWEnum {

    MATRICULA("MA"),
    REMATRICULA("RE"),
    RENOVACAO("RN");

    private String codigo;

    TipoContratoZWEnum(String codigo) {
        this.codigo = codigo;
    }

    public String getCodigo() {
        return codigo;
    }

    public void setCodigo(String codigo) {
        this.codigo = codigo;
    }

    public static TipoContratoZWEnum getInstance(String codigo) {
        for (TipoContratoZWEnum tipoContrato : TipoContratoZWEnum.values()) {
            if (codigo.equals(tipoContrato.getCodigo())) {
                return tipoContrato;
            }
        }
        return null;
    }
}
