package br.com.pacto.controller.json.aluno;

import br.com.pacto.bean.cliente.ClienteObservacao;
import br.com.pacto.bean.usuario.Usuario;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.Date;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class ClienteObservacaoAnexosDTO {

    private Integer id;
    private Boolean importante;
    private String observacao;
    private Date data;
    private Boolean avaliacaoFisica;
    private String anexo;
    private String usuario;
    private String nomeArquivo;
    private String formatoArquivo;
    private String origem;

    public ClienteObservacaoAnexosDTO() {
    }

    public ClienteObservacaoAnexosDTO(ClienteObservacao clienteObservacao, Usuario usu) {

        this.id = clienteObservacao.getCodigo();
        this.importante = clienteObservacao.getImportante();
        this.observacao = clienteObservacao.getObservacao();
        this.data = clienteObservacao.getDataObservacao();
        this.avaliacaoFisica = clienteObservacao.getAvaliacaoFisica();
        this.anexo = clienteObservacao.getAnexoKey();
        this.usuario = usu == null ? "" : usu.getNome();
        this.nomeArquivo = "";
        if (anexo != null && !anexo.isEmpty()) {
            this.nomeArquivo = clienteObservacao.getNomeArquivo();
            this.formatoArquivo = clienteObservacao.getFormatoArquivo();
            try {
                String anexo = this.anexo.split("/")[this.anexo.split("/").length - 1];
                if (this.nomeArquivo == null || this.nomeArquivo.isEmpty()) {
                    this.nomeArquivo = anexo.split("\\.")[0];
                }
                if (this.formatoArquivo == null || this.formatoArquivo.isEmpty()) {
                    this.formatoArquivo = anexo.substring(anexo.lastIndexOf("."));
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Boolean getImportante() {
        return importante;
    }

    public void setImportante(Boolean importante) {
        this.importante = importante;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public Date getData() {
        return data;
    }

    public void setData(Date data) {
        this.data = data;
    }

    public Boolean getAvaliacaoFisica() {
        return avaliacaoFisica;
    }

    public void setAvaliacaoFisica(Boolean avaliacaoFisica) {
        this.avaliacaoFisica = avaliacaoFisica;
    }

    public String getAnexo() {
        return anexo;
    }

    public void setAnexo(String anexo) {
        this.anexo = anexo;
    }

    public String getUsuario() {
        return usuario;
    }

    public void setUsuario(String usuario) {
        this.usuario = usuario;
    }

    public String getNomeArquivo() {
        return nomeArquivo;
    }

    public void setNomeArquivo(String nomeArquivo) {
        this.nomeArquivo = nomeArquivo;
    }

    public String getFormatoArquivo() {
        return formatoArquivo;
    }

    public void setFormatoArquivo(String formatoArquivo) {
        this.formatoArquivo = formatoArquivo;
    }

    public String getOrigem() {
        return origem;
    }

    public void setOrigem(String origem) {
        this.origem = origem;
    }
}