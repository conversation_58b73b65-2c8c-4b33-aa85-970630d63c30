package br.com.pacto.controller.json.gestao;

import java.sql.Timestamp;

public class AvaliacaoTreinoDTO {

    private Integer matricula;
    private String nomeAluno;
    private Timestamp dataHoraInicioApresentar;
    private String comentario;
    private String nota;
    private String professorCarteiraApresentar;

    public AvaliacaoTreinoDTO() {}

    public AvaliacaoTreinoDTO(Integer matricula, String nomeAluno, Timestamp data, String comentario, String nota, String nomeProfessor) {
        this.matricula = matricula;
        this.nomeAluno = nomeAluno;
        this.dataHoraInicioApresentar = data;
        this.comentario = comentario;
        this.nota = nota;
        this.professorCarteiraApresentar = nomeProfessor;
    }

    public Integer getMatricula() {
        return matricula;
    }

    public void setMatricula(Integer matricula) {
        this.matricula = matricula;
    }

    public String getNomeAluno() {
        return nomeAluno;
    }

    public void setNomeAluno(String nomeAluno) {
        this.nomeAluno = nomeAluno;
    }

    public Timestamp getDataHoraInicioApresentar() {
        return dataHoraInicioApresentar;
    }

    public void setDataHoraInicioApresentar(Timestamp data) {
        this.dataHoraInicioApresentar = data;
    }

    public String getComentario() {
        return comentario;
    }

    public void setComentario(String comentario) {
        this.comentario = comentario;
    }

    public String getNota() {
        return nota;
    }

    public void setNota(String nota) {
        this.nota = nota;
    }

    public String getProfessorCarteiraApresentar() {
        return professorCarteiraApresentar;
    }

    public void setProfessorCarteiraApresentar(String nomeProfessor) {
        this.professorCarteiraApresentar = nomeProfessor;
    }
}
