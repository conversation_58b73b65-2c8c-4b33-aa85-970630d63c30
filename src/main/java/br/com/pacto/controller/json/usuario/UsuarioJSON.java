/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.controller.json.usuario;

import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.base.SuperJSON;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.util.UteisValidacao;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
public class UsuarioJSON extends SuperJSON {

    private Integer cod;
    private Integer codUsuario;
    private Integer codUsuarioZW;
    private Integer idade;
    private String nome;
    private String username;
    private String srcImg;
    private String nivel = "";
    private String email = "";
    private String professor = "";
    private String token;
    private Integer versao = 0;
    private String avatarEmpresa;
    private String urlSiteEmpresa = "";
    private String urlHomeBackground640x551 = "";
    private String urlHomeBackground320x276 = "";
    private String nomeEmpresa;
    private Integer codigoPessoa;
    private Integer codigoProfessor;
    private Integer codigoColaborador;
    private String vencPlano = "";
    private Integer codEmpresa;
    private String matricula = "";
    private Integer codigoPessoaCliente;
    private String perfilUsuario = "";
    //DADOS CONTRATO
    private String inicioPlano = "";
    private String nomePlano = "";
    private String descricaoDuracao = "";
    private Integer duracaoMeses;
    private Integer saldoCreditos;
    //TODO tornar o valor dinamico
    private Integer totalCreditos;
    //TODO tornar o valor da label dinamica
    private String label = "créditos";
    private Integer codigoContrato;
    private Integer codigoConvidado;
    private Integer codigoConvite;
    private Boolean convidado = false;
    private String modalidade;
    private String nomenclaturaVendaCredito;
    private String situacaoContratoOperacao;
    private String urlRedirectAdm;
    private String urlRedirectCRM;
    private String urlRedirectTreino;
    private String urlRedirectFinan;
    private String urlRedirectRankingUCP;
    private List<String> objetivos;
    private List<UsuarioEmpresaApp> empresas = new ArrayList<>();
    private Integer codigoCliente;
    private Boolean contratoCredito = false;
    private Boolean treinoIndependente = false;
    private String status;
    private String statusAluno;
    private String telefone;
    private int spiviClientID;
    private String codigoAcesso;
    private Long dataCadastro;
    private Boolean acessoAcademia;
    private String validadeCREF;
    private Long dia;
    private List<UsuarioDependenteDTO> dependenteDTOS;
    private String cpf;

    public UsuarioJSON(final Usuario usuario, List<UsuarioEmpresaApp> empresas)  {
        boolean independente = Aplicacao.independente(usuario.getChave());
        this.codUsuario = usuario.getCodigo();
        this.codUsuarioZW = usuario.getUsuarioZW();
        if (!UteisValidacao.emptyString(usuario.getFotoKeyApp())) {
            this.srcImg = usuario.getFotoKeyApp();
        } else {
            this.srcImg = StringUtils.isBlank(usuario.getAvatar()) ? "" : usuario.getAvatar();
        }
        this.username = usuario.getUserName();
        this.token = usuario.getToken();
        this.avatarEmpresa = usuario.getAvatarEmpresaApp();
        this.urlSiteEmpresa = usuario.getUrlSite() == null ? "" : usuario.getUrlSite().toLowerCase();
        this.urlHomeBackground320x276 = usuario.getUrlHomeBackground320x276();
        this.urlHomeBackground640x551 = usuario.getUrlHomeBackground640x551();
        this.nomeEmpresa = usuario.getNomeEmpresa();
        this.codEmpresa = usuario.getEmpresaZW();
        this.status = usuario.getStatus().name();

        if (usuario.getCliente() == null) {
            this.username = usuario.getUserName();
            if (usuario.getProfessor() != null) {
                if ( null != usuario.getProfessor().getEmail() && !usuario.getProfessor().getEmail().isEmpty() && usuario.getProfessor().getEmail() != null){
                    this.email = usuario.getProfessor().getEmail();
                }else{
                    this.email = usuario.getUsuarioEmail() == null || UteisValidacao.emptyNumber(usuario.getUsuarioEmail().getCodigo()) ?  this.username : usuario.getUsuarioEmail().getEmail();
                }
            }
            if (usuario.getProfessor() != null && usuario.getProfessor().getPessoa() != null && !UteisValidacao.emptyList(usuario.getProfessor().getPessoa().getTelefones())){
                this.telefone = usuario.getProfessor().getPessoa().getTelefones().get(0).getTelefone();
            }
            this.avatarEmpresa = usuario.getAvatarEmpresaApp();
            this.urlHomeBackground320x276 = usuario.getUrlHomeBackground320x276();
            this.urlHomeBackground640x551 = usuario.getUrlHomeBackground640x551();
            this.nomeEmpresa = usuario.getNomeEmpresa();
            this.codEmpresa = usuario.getEmpresaZW();
            this.convidado = true;

            this.cod = usuario.getCodigo();
            this.nome = usuario.getProfessor().getNome();
            this.codigoPessoa = usuario.getProfessor().getCodigoPessoa();
            this.codigoProfessor = usuario.getProfessor().getCodigo();
            this.codigoColaborador = usuario.getProfessor().getCodigoColaborador();
            this.perfilUsuario = usuario.getPerfil() == null ? "" : usuario.getPerfil().getNome();
            this.validadeCREF = Uteis.getData(usuario.getProfessor().getValidadeCREF());
            if(!UteisValidacao.emptyList(empresas)) {
                this.empresas.addAll(empresas);
            }
            this.codigoAcesso = usuario.getProfessor().getCodigoAcesso();
            return;
        }
        this.idade = usuario.getCliente().getIdade();
        this.codEmpresa = usuario.getCliente().getEmpresa();
        this.totalCreditos = usuario.getCliente().getTotalCreditoTreino();
        this.cod = usuario.getCliente().getCodigo();
        this.codigoPessoaCliente = usuario.getCliente().getCodigoPessoa();
        this.codigoCliente = usuario.getCliente().getCodigoCliente();
        this.nome = usuario.getCliente().getNome();
        this.nivel = usuario.getCliente().getNivelAluno() != null ? usuario.getCliente().getNivelAluno().getNome() : "N/C";
        this.professor = usuario.getCliente().getProfessorSintetico() == null ? "(Sem professor)" : usuario.getCliente().getProfessorSintetico().getNome();
        this.versao = usuario.getCliente() != null ? usuario.getCliente().getVersao() : 0;
        this.vencPlano = usuario.getCliente().getDataVigenciaAteAjustadaApresentar();
        this.matricula = usuario.getCliente().getMatriculaString();
        this.inicioPlano = independente ? (usuario.getCliente().getDataMatricula()  == null ? "" : Uteis.getData(usuario.getCliente().getDataMatricula())) :
                usuario.getCliente().getDataVigenciaDeApresentar();
        this.saldoCreditos = usuario.getCliente().getSaldoCreditoTreino();
        this.nomePlano = usuario.getCliente().getNomeplano();
        this.duracaoMeses = usuario.getCliente().getDuracaoContratoMeses();

        if(usuario.getCliente().getDescricaoDuracao() != null &&
                usuario.getCliente().getDuracaoContratoMeses() != null){
            this.descricaoDuracao = usuario.getCliente().getDescricaoDuracao() == null
                    ? (usuario.getCliente().getDuracaoContratoMeses() == 1 ? "1 mês"
                    : (usuario.getCliente().getDuracaoContratoMeses() + " meses")) : usuario.getCliente().getDescricaoDuracao();
        }

        this.codigoContrato = usuario.getCliente().getCodigoContrato();
        this.modalidade = usuario.getCliente().getDescricoesModalidades();
        this.situacaoContratoOperacao = usuario.getCliente().getSituacaoContratoOperacao();
        this.objetivos = usuario.getCliente().getObjetivosLista();
        this.codigoAcesso = usuario.getCliente().getCodigoAcesso();
        this.dataCadastro = usuario.getCliente().getDataCadastro() != null
                ? usuario.getCliente().getDataCadastro().getTime(): null;
        this.statusAluno = usuario.getCliente() != null ? usuario.getCliente().getSituacao() : "";
    }
    private String formatUrl(final String urlBase , final Usuario usuario,final String uri) {
        try {
            return  urlBase + Uteis.formatUrlRedirect(usuario.getChave(),"","",usuario.getUsuarioZW(),usuario.getEmpresaZW(),uri, "");
        } catch (Exception ex) {
            Logger.getLogger(UsuarioJSON.class.getName()).log(Level.SEVERE, null, ex);
            return "";
        }
    }
    private String formatUrlUCP(final String urlBase , final Usuario usuario) {
        try {
            return  urlBase + Uteis.formatUrlRedirectUCP(usuario.getChave(),usuario);
        } catch (Exception ex) {
            Logger.getLogger(UsuarioJSON.class.getName()).log(Level.SEVERE, null, ex);
            return "";
        }
    }
    public void montarUrlRedirect(Usuario usuario,String urlBaseTreino){
        this.urlRedirectTreino = formatUrl(urlBaseTreino,usuario,Uteis.URI_REDIRECT_BI_TREINO);
        this.urlRedirectAdm = formatUrl(Aplicacao.getProp(usuario.getChave(), Aplicacao.urlZillyonWeb),usuario,Uteis.URI_REDIRECT_BI_DETALHADO);
        this.urlRedirectFinan = formatUrl(Aplicacao.getProp(usuario.getChave(), Aplicacao.urlZillyonWeb),usuario,Uteis.URI_REDIRECT_FINAN);
        this.urlRedirectCRM = formatUrl(Aplicacao.getProp(usuario.getChave(), Aplicacao.urlZillyonWeb),usuario,Uteis.URI_REDIRECT_CRM_BI);
        this.urlRedirectRankingUCP = formatUrlUCP(Aplicacao.getProp(usuario.getChave(), Aplicacao.myUpUrlBase),usuario);
    }
    public Integer getCod() {
        return cod;
    }

    public void setCod(Integer cod) {
        this.cod = cod;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getSrcImg() {
        return srcImg;
    }

    public void setSrcImg(String srcImg) {
        this.srcImg = srcImg;
    }

    public String getNivel() {
        return nivel;
    }

    public void setNivel(String nivel) {
        this.nivel = nivel;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getProfessor() {
        return professor;
    }

    public void setProfessor(String professor) {
        this.professor = professor;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public Integer getVersao() {
        if (versao == null) {
            versao = 0;
        }
        return versao;
    }

    public void setVersao(Integer versao) {
        this.versao = versao;
    }

    public String getAvatarEmpresa() {
        return avatarEmpresa;
    }

    public void setAvatarEmpresa(String avatarEmpresa) {
        this.avatarEmpresa = avatarEmpresa;
    }

    public String getNomeEmpresa() {
        return nomeEmpresa;
    }

    public void setNomeEmpresa(String nomeEmpresa) {
        this.nomeEmpresa = nomeEmpresa;
    }

    public Integer getCodigoPessoa() {
        return codigoPessoa;
    }

    public void setCodigoPessoa(Integer codigoPessoa) {
        this.codigoPessoa = codigoPessoa;
    }

    public Integer getCodEmpresa() {
        return codEmpresa;
    }

    public void setCodEmpresa(Integer codEmpresa) {
        this.codEmpresa = codEmpresa;
    }

    public String getVencPlano() {
        return vencPlano;
    }

    public void setVencPlano(String vencPlano) {
        this.vencPlano = vencPlano;
    }

    public String getUrlHomeBackground640x551() {
        return urlHomeBackground640x551;
    }

    public void setUrlHomeBackground640x551(String urlHomeBackground640x551) {
        this.urlHomeBackground640x551 = urlHomeBackground640x551;
    }

    public String getUrlHomeBackground320x276() {
        return urlHomeBackground320x276;
    }

    public void setUrlHomeBackground320x276(String urlHomeBackground320x276) {
        this.urlHomeBackground320x276 = urlHomeBackground320x276;
    }

    public Integer getCodigoProfessor() {
        return codigoProfessor;
    }

    public void setCodigoProfessor(Integer codigoProfessor) {
        this.codigoProfessor = codigoProfessor;
    }

    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public String getInicioPlano() {
        return inicioPlano;
    }

    public void setInicioPlano(String inicioPlano) {
        this.inicioPlano = inicioPlano;
    }

    public String getNomePlano() {
        return nomePlano;
    }

    public void setNomePlano(String nomePlano) {
        this.nomePlano = nomePlano;
    }

    public Integer getDuracaoMeses() {
        return duracaoMeses;
    }

    public void setDuracaoMeses(Integer duracaoMeses) {
        this.duracaoMeses = duracaoMeses;
    }

    public Integer getSaldoCreditos() {
        return saldoCreditos;
    }

    public void setSaldoCreditos(Integer saldoCreditos) {
        this.saldoCreditos = saldoCreditos;
    }

    public Integer getCodigoContrato() {
        return codigoContrato;
    }

    public void setCodigoContrato(Integer codigoContrato) {
        this.codigoContrato = codigoContrato;
    }

    public String getDescricaoDuracao() {
        return descricaoDuracao;
    }

    public void setDescricaoDuracao(String descricaoDuracao) {
        this.descricaoDuracao = descricaoDuracao;
    }

    public Integer getTotalCreditos() {
        return totalCreditos;
    }

    public void setTotalCreditos(Integer totalCreditos) {
        this.totalCreditos = totalCreditos;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public Boolean getConvidado() {
        return convidado;
    }

    public void setConvidado(Boolean convidado) {
        this.convidado = convidado;
    }

    public Integer getCodigoConvidado() {
        return codigoConvidado;
    }

    public void setCodigoConvidado(Integer codigoConvidado) {
        this.codigoConvidado = codigoConvidado;
    }

    public Integer getCodigoConvite() {
        return codigoConvite;
    }

    public void setCodigoConvite(Integer codigoConvite) {
        this.codigoConvite = codigoConvite;
    }

    public String getModalidade() {
        return modalidade;
    }

    public void setModalidade(String modalidade) {
        this.modalidade = modalidade;
    }

    public String getNomenclaturaVendaCredito() {
        return nomenclaturaVendaCredito;
    }

    public void setNomenclaturaVendaCredito(String nomenclaturaVendaCredito) {
        this.nomenclaturaVendaCredito = nomenclaturaVendaCredito;
    }

    public String getSituacaoContratoOperacao() {
        return situacaoContratoOperacao;
    }

    public void setSituacaoContratoOperacao(String situacaoContratoOperacao) {
        this.situacaoContratoOperacao = situacaoContratoOperacao;
    }

    public List<UsuarioEmpresaApp> getEmpresas() {
        return empresas;
    }

    public void setEmpresas(List<UsuarioEmpresaApp> empresas) {
        this.empresas = empresas;
    }

    public String getUrlRedirectAdm() {
        return urlRedirectAdm;
    }

    public void setUrlRedirectAdm(String urlRedirectAdm) {
        this.urlRedirectAdm = urlRedirectAdm;
    }

    public String getUrlRedirectCRM() {
        return urlRedirectCRM;
    }

    public void setUrlRedirectCRM(String urlRedirectCRM) {
        this.urlRedirectCRM = urlRedirectCRM;
    }

    public String getUrlRedirectTreino() {
        return urlRedirectTreino;
    }

    public void setUrlRedirectTreino(String urlRedirectTreino) {
        this.urlRedirectTreino = urlRedirectTreino;
    }

    public String getUrlRedirectFinan() {
        return urlRedirectFinan;
    }

    public void setUrlRedirectFinan(String urlRedirectFinan) {
        this.urlRedirectFinan = urlRedirectFinan;
    }

    public String getPerfilUsuario() {
        return perfilUsuario;
    }

    public void setPerfilUsuario(String perfilUsuario) {
        this.perfilUsuario = perfilUsuario;
    }

    public String getUrlSiteEmpresa() {
        return urlSiteEmpresa;
    }

    public void setUrlSiteEmpresa(String urlSiteEmpresa) {
        this.urlSiteEmpresa = urlSiteEmpresa;
    }

    public String getUrlRedirectRankingUCP() {
        return urlRedirectRankingUCP;
    }

    public void setUrlRedirectRankingUCP(String urlRedirectRankingUCP) {
        this.urlRedirectRankingUCP = urlRedirectRankingUCP;
    }

    public List<String> getObjetivos() {
        return objetivos;
    }

    public void setObjetivos(List<String> objetivos) {
        this.objetivos = objetivos;
    }

    public Integer getCodigoPessoaCliente() {
        return codigoPessoaCliente;
    }

    public void setCodigoPessoaCliente(Integer codigoPessoaCliente) {
        this.codigoPessoaCliente = codigoPessoaCliente;
    }

    public Integer getCodigoCliente() {
        return codigoCliente;
    }

    public void setCodigoCliente(Integer codigoCliente) {
        this.codigoCliente = codigoCliente;
    }

    public Integer getIdade() {
        return idade;
    }

    public void setIdade(Integer idade) {
        this.idade = idade;
    }

    public Boolean getContratoCredito() {
        if (contratoCredito == null) {
            contratoCredito = false;
        }
        return contratoCredito;
    }

    public void setContratoCredito(Boolean contratoCredito) {
        this.contratoCredito = contratoCredito;
    }

    public Integer getCodUsuario() {
        return codUsuario;
    }

    public void setCodUsuario(Integer codUsuario) {
        this.codUsuario = codUsuario;
    }

    public Boolean getTreinoIndependente() {
        return treinoIndependente;
    }

    public void setTreinoIndependente(Boolean treinoIndependente) {
        this.treinoIndependente = treinoIndependente;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getTelefone() {
        return telefone;
    }

    public void setTelefone(String telefone) {
        this.telefone = telefone;
    }

    public int getSpiviClientID() {
        return spiviClientID;
    }

    public void setSpiviClientID(int spiviClientID) {
        this.spiviClientID = spiviClientID;
    }

    public String getCodigoAcesso() {
        return codigoAcesso;
    }

    public void setCodigoAcesso(String codigoAcesso) {
        this.codigoAcesso = codigoAcesso;
    }

    public Long getDataCadastro() {
        return dataCadastro;
    }

    public void setDataCadastro(Long dataCadastro) {
        this.dataCadastro = dataCadastro;
    }

    public Integer getCodigoColaborador() {
        return codigoColaborador;
    }

    public void setCodigoColaborador(Integer codigoColaborador) {
        this.codigoColaborador = codigoColaborador;
    }

    public Integer getCodUsuarioZW() {
        return codUsuarioZW;
    }

    public void setCodUsuarioZW(Integer codUsuarioZW) {
        this.codUsuarioZW = codUsuarioZW;
    }

    public Boolean getAcessoAcademia() {
        return acessoAcademia;
    }

    public void setAcessoAcademia(Boolean acessoAcademia) {
        this.acessoAcademia = acessoAcademia;
    }

    public String getValidadeCREF() {
        return validadeCREF;
    }

    public void setValidadeCREF(String validadeCREF) {
        this.validadeCREF = validadeCREF;
    }

    public String getStatusAluno() {
        return statusAluno;
    }

    public void setStatusAluno(String statusAluno) {
        this.statusAluno = statusAluno;
    }

    public Long getDia() {
        return dia;
    }

    public void setDia(Long dia) {
        this.dia = dia;
    }

    public List<UsuarioDependenteDTO> getDependenteDTOS() {
        return dependenteDTOS;
    }

    public void setDependenteDTOS(List<UsuarioDependenteDTO> dependenteDTOS) {
        this.dependenteDTOS = dependenteDTOS;
    }

    public String getCpf() {
        return cpf;
    }

    public void setCpf(String cpf) {
        this.cpf = cpf;
    }
}
