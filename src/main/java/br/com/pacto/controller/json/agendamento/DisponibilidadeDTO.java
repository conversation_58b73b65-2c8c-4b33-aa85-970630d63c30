package br.com.pacto.controller.json.agendamento;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.List;


@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class DisponibilidadeDTO {

    private Long dataInicial;
    private Long dataFinal;
    private String dataInicialFormatada;
    private String dataFinalFormatada;
    private List<AgendaDiaSemana> diasSemana;
    private String horarioInicial;
    private String horarioFinal;
    private List<Integer> professores;
    private List<Integer> tipos;

    public String getDataInicialFormatada() {
        return dataInicialFormatada;
    }

    public void setDataInicialFormatada(String dataInicialFormatada) {
        this.dataInicialFormatada = dataInicialFormatada;
    }

    public String getDataFinalFormatada() {
        return dataFinalFormatada;
    }

    public void setDataFinalFormatada(String dataFinalFormatada) {
        this.dataFinalFormatada = dataFinalFormatada;
    }

    public Long getDataInicial() {
        return dataInicial;
    }

    public void setDataInicial(Long dataInicial) {
        this.dataInicial = dataInicial;
    }

    public Long getDataFinal() {
        return dataFinal;
    }

    public void setDataFinal(Long dataFinal) {
        this.dataFinal = dataFinal;
    }

    public List<AgendaDiaSemana> getDiasSemana() {
        return diasSemana;
    }

    public void setDiasSemana(List<AgendaDiaSemana> diasSemana) {
        this.diasSemana = diasSemana;
    }

    public String getHorarioInicial() {
        return horarioInicial;
    }

    public void setHorarioInicial(String horarioInicial) {
        this.horarioInicial = horarioInicial;
    }

    public String getHorarioFinal() {
        return horarioFinal;
    }

    public void setHorarioFinal(String horarioFinal) {
        this.horarioFinal = horarioFinal;
    }

    public List<Integer> getProfessores() {
        return professores;
    }

    public void setProfessores(List<Integer> professores) {
        this.professores = professores;
    }

    public List<Integer> getTipos() {
        return tipos;
    }

    public void setTipos(List<Integer> tipos) {
        this.tipos = tipos;
    }
}
