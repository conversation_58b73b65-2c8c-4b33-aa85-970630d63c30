package br.com.pacto.controller.json.aulaDia;

import br.com.pacto.bean.aula.AulaDiaExclusao;
import br.com.pacto.bean.colaborador.ColaboradorSimplesTO;
import br.com.pacto.controller.json.agendamento.TurmaResponseDTO;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class AulaAlunoDTO implements Comparable<AulaAlunoDTO> {
    private String nomeAula;
    private String nomeTurma;
    private String nomeProfessor;
    private String dia;
    private Integer codigoHorarioTurma;
    private Boolean desmarcada = false;
    private String horario;
    private Boolean aulaTurma = false;
    private Boolean temIntegracaoSelfLoops = false;


    public String getNomeAula() {
        return nomeAula;
    }

    public void setNomeAula(String nomeAula) {
        this.nomeAula = nomeAula;
    }

    public String getNomeTurma() {
        return nomeTurma;
    }

    public void setNomeTurma(String nomeTurma) {
        this.nomeTurma = nomeTurma;
    }

    public Integer getCodigoHorarioTurma() {
        return codigoHorarioTurma;
    }

    public String getHorario() {
        return horario;
    }

    public void setHorario(String horario) {
        this.horario = horario;
    }

    public Boolean getDesmarcada() {
        return desmarcada;
    }

    public void setDesmarcada(Boolean desmarcada) {
        this.desmarcada = desmarcada;
    }

    public void setCodigoHorarioTurma(Integer codigoHorarioTurma) {
        this.codigoHorarioTurma = codigoHorarioTurma;
    }

    public String getNomeProfessor() {
        return nomeProfessor;
    }

    public void setNomeProfessor(String nomeProfessor) {
        this.nomeProfessor = nomeProfessor;
    }

    public String getDia() {
        return dia;
    }

    public Boolean getAulaTurma() {
        return aulaTurma;
    }

    public void setAulaTurma(Boolean aulaTurma) {
        this.aulaTurma = aulaTurma;
    }

    public void setDia(String dia) {
        this.dia = dia;
    }

    @Override
    public int compareTo(AulaAlunoDTO other) {
        // Implementação do compareTo, caso necessário
        return 0;
    }

    public static void ordenarPorDataEHora(List<AulaAlunoDTO> lista) {
        DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("HH:mm");
        SimpleDateFormat dateFormatter = new SimpleDateFormat("yyyy-MM-dd");

        Comparator<AulaAlunoDTO> comparator = (a1, a2) -> {
            try {
                Date date1 = dateFormatter.parse(a1.getDia());
                Date date2 = dateFormatter.parse(a2.getDia());
                int dateComparison = date1.compareTo(date2);

                if (dateComparison != 0) {
                    return dateComparison;
                }

                LocalTime time1 = LocalTime.parse(a1.getHorario().split(" - ")[0], timeFormatter);
                LocalTime time2 = LocalTime.parse(a2.getHorario().split(" - ")[0], timeFormatter);
                return time1.compareTo(time2);
            } catch (ParseException | DateTimeParseException e) {
                throw new IllegalArgumentException("Formato de data ou horário inválido", e);
            }
        };

        Collections.sort(lista, comparator);
    }

    public static void ordenarPorData(List<AulaAlunoDTO> lista) {
        SimpleDateFormat dateFormatter = new SimpleDateFormat("yyyy-MM-dd");

        Comparator<AulaAlunoDTO> comparator = (a1, a2) -> {
            try {
                Date date1 = dateFormatter.parse(a1.getDia());
                Date date2 = dateFormatter.parse(a2.getDia());
                return date1.compareTo(date2);
            } catch (ParseException e) {
                throw new IllegalArgumentException("Formato de data inválido", e);
            }
        };

        Collections.sort(lista, comparator);
    }

    public Boolean getTemIntegracaoSelfLoops() {
        return temIntegracaoSelfLoops;
    }

    public void setTemIntegracaoSelfLoops(Boolean temIntegracaoSelfLoops) {
        this.temIntegracaoSelfLoops = temIntegracaoSelfLoops;
    }
}

