/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.controller.json.ficha.read;

import br.com.pacto.controller.json.cliente.ClienteJSON;
import br.com.pacto.controller.json.programa.read.ProgramaTreinoJSON;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class FilaImpressaoJSON {

    private ClienteJSON cliente;
    private ProgramaTreinoJSON programa;
    private Date data;

    public FilaImpressaoJSON(Date data, ClienteJSON cliente, ProgramaTreinoJSON programa) {
        this.cliente = cliente;
        this.programa = programa;
        this.data = data;
    }

    public ClienteJSON getCliente() {
        return cliente;
    }

    public void setCliente(ClienteJSON cliente) {
        this.cliente = cliente;
    }

    public ProgramaTreinoJSON getPrograma() {
        return programa;
    }

    public void setPrograma(ProgramaTreinoJSON programa) {
        this.programa = programa;
    }

    public Date getData() {
        return data;
    }

    public void setData(Date data) {
        this.data = data;
    }
}
