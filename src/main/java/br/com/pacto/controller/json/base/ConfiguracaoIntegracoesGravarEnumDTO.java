package br.com.pacto.controller.json.base;

import br.com.pacto.util.UteisValidacao;
import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * <AUTHOR> 24/02/2020
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ConfiguracaoIntegracoesGravarEnumDTO {

    private String usar_integracao_bioimpedancia;
    private String usarIntegracaoMyzone;
    private String integracao_sistema_olympia;
    private String url_sistema_olympia;
    private String token_sistema_olympia;
    private String usuario_sistema_olympia;
    private String senha_sistema_olympia;

    public ConfiguracaoIntegracoesGravarEnumDTO constructor(ConfiguracaoIntegracoesDTO configDTO){
        ConfiguracaoIntegracoesGravarEnumDTO config = new ConfiguracaoIntegracoesGravarEnumDTO();
        config.setUsar_integracao_bioimpedancia(configDTO.getUsar_integracao_bioimpedancia().toString());
        config.setUsarIntegracaoMyzone(configDTO.getUsarIntegracaoMyzone().toString());
        config.setIntegracao_sistema_olympia(configDTO.getIntegracao_sistema_olympia().toString());
        config.setUrl_sistema_olympia(configDTO.getUrl_sistema_olympia());
        config.setToken_sistema_olympia(configDTO.getToken_sistema_olympia());
        config.setUsuario_sistema_olympia(configDTO.getUsuario_sistema_olympia());
        config.setSenha_sistema_olympia(configDTO.getSenha_sistema_olympia());
        return config;
    }

    public Boolean getUsar_integracao_bioimpedancia() {
        if (!UteisValidacao.emptyString(usar_integracao_bioimpedancia)) {
            return usar_integracao_bioimpedancia.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setUsar_integracao_bioimpedancia(String usar_integracao_bioimpedancia) {
        this.usar_integracao_bioimpedancia = usar_integracao_bioimpedancia;
    }

    public Boolean getIntegracao_sistema_olympia() {
        if (!UteisValidacao.emptyString(integracao_sistema_olympia)) {
            return integracao_sistema_olympia.trim().equals("true");
        } else {
            return false;
        }
    }

    public String getUsarIntegracaoMyzone() {
        return usarIntegracaoMyzone;
    }

    public void setUsarIntegracaoMyzone(String usarIntegracaoMyzone) {
        this.usarIntegracaoMyzone = usarIntegracaoMyzone;
    }

    public void setIntegracao_sistema_olympia(String integracao_sistema_olympia) {
        this.integracao_sistema_olympia = integracao_sistema_olympia;
    }

    public String getUrl_sistema_olympia() {
        return url_sistema_olympia;
    }

    public void setUrl_sistema_olympia(String url_sistema_olympia) {
        this.url_sistema_olympia = url_sistema_olympia;
    }

    public String getToken_sistema_olympia() {
        return token_sistema_olympia;
    }

    public void setToken_sistema_olympia(String token_sistema_olympia) {
        this.token_sistema_olympia = token_sistema_olympia;
    }

    public String getUsuario_sistema_olympia() {
        return usuario_sistema_olympia;
    }

    public void setUsuario_sistema_olympia(String usuario_sistema_olympia) {
        this.usuario_sistema_olympia = usuario_sistema_olympia;
    }

    public String getSenha_sistema_olympia() {
        return senha_sistema_olympia;
    }

    public void setSenha_sistema_olympia(String senha_sistema_olympia) {
        this.senha_sistema_olympia = senha_sistema_olympia;
    }
}
