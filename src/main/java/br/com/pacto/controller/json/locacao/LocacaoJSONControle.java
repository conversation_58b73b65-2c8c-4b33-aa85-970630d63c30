package br.com.pacto.controller.json.locacao;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.locacao.AlunoLocacaoHorarioPlayCheckinCheckoutDTO;
import br.com.pacto.bean.locacao.AlunoLocacaoHorarioPlayDTO;
import br.com.pacto.controller.json.aulaDia.AulasController;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.locacao.AlunoLocacaoHorarioPlayService;
import br.com.pacto.service.intf.locacao.LocacaoService;
import br.com.pacto.util.UteisValidacao;
import org.json.JSONException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.text.ParseException;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

@Controller
@RequestMapping("/locacoes")
public class LocacaoJSONControle {

    private final LocacaoService locacaoService;
    private final AlunoLocacaoHorarioPlayService alunoLocacaoHorarioPlayService;

    @Autowired
    public LocacaoJSONControle(
            LocacaoService locacaoService,
            AlunoLocacaoHorarioPlayService alunoLocacaoHorarioPlayService
    ) {
        this.locacaoService = locacaoService;
        this.alunoLocacaoHorarioPlayService = alunoLocacaoHorarioPlayService;
    }

    @ResponseBody
    @RequestMapping(value = "/{ctx}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> getLocacoes(
            @RequestHeader("empresaId") Integer empresaId,
            @PathVariable("ctx") String ctx,
            PaginadorDTO paginadorDTO
    ) {
        try {
            return ResponseEntityFactory.ok(locacaoService.getLocacoes(empresaId, ctx, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/{ctx}/disponibilidades/{dia}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> disponibilidades(
            @RequestHeader("empresaId") Integer empresaId,
            @PathVariable("dia") String dia,
            @PathVariable("ctx") String ctx,
            @RequestParam(value = "locacaoCodigo", required = false, defaultValue = "-1") Integer locacaoCodigo,
            PaginadorDTO paginadorDTO
    ) {
        try {
            return ResponseEntityFactory.ok(locacaoService.disponibilidades(empresaId, ctx, new Date(Long.parseLong(dia)), "", locacaoCodigo, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/{ctx}/config/agendamento/{idHorario}/{agendamento}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> aulaDetalhada(
            @PathVariable("idHorario") Integer idHorario,
            @PathVariable("ctx") String ctx,
            @PathVariable("agendamento") Integer agendamento,
            @RequestParam(value = "data", defaultValue = "", required = false) String data) {
        try {
            if (UteisValidacao.emptyNumber(agendamento)) {
                return ResponseEntityFactory.ok(locacaoService.configAgendamento(idHorario, data, ctx));
            }
            return ResponseEntityFactory.ok(locacaoService.editAgendamento(agendamento, data, ctx));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/{ctx}/agendar/{dia}", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> agendar(
            @RequestHeader (value = "empresaId") Integer empresaId,
            @PathVariable String dia,
            @RequestBody AgendamentoLocacaoDTO agendamentoLocacaoDTO,
            @PathVariable("ctx") String ctx
    ) {
        try {
            return ResponseEntityFactory.ok(locacaoService.agendar(
                    Calendario.getDate("yyyyMMdd", dia),
                    agendamentoLocacaoDTO, empresaId, ctx, null));
        } catch (ParseException e) {
            Logger.getLogger(AulasController.class.getName()).log(Level.SEVERE, "Erro ao tentar agendar a locação", e);
            return ResponseEntityFactory.erroInterno("error_date", e.getMessage());
        } catch (ServiceException e) {
            Logger.getLogger(AulasController.class.getName()).log(Level.SEVERE, "Erro ao tentar agendar a locação", e);
            if (e.getCause() == null || e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroConhecido(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ResponseBody
    @RequestMapping(value = "/{ctx}/finalizar-agendamento-locacao/{codigoAgendamentoLocacao}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> finalizarAgendamentoLocacao(
            @PathVariable("codigoAgendamentoLocacao") Integer codigoAgendamentoLocacao,
            @RequestParam(value = "data") String data,
            @RequestHeader(value = "empresaId") Integer empresaId,
            @RequestBody(required = false) AgendamentoLocacaoDTO agendamentoLocacaoDTO,
            @PathVariable("ctx") String ctx
    ) {
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
            return ResponseEntityFactory.ok(
                    locacaoService.finalizarAgendamentoLocacao(ctx,
                            null,
                            codigoAgendamentoLocacao,
                            agendamentoLocacaoDTO, Date.from(LocalDate.parse(data, formatter).atStartOfDay(ZoneId.systemDefault()).toInstant()), empresaId)
            );
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/{ctx}/cancelar-agendamento-locacao/{codigoAgendamentoLocacao}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cancelarAgendamentoLocacao(
            @PathVariable("codigoAgendamentoLocacao") Integer codigoAgendamentoLocacao,
            @PathVariable("ctx") String ctx,
            @RequestBody Map<String, String> requestBody) {
        try {
            return ResponseEntityFactory.ok(locacaoService.cancelarAgendamentoLocacao(ctx, null, true, codigoAgendamentoLocacao, requestBody.get("justificativa")));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/{ctx}/validar-reagendamento/{codigoAgendamentoLocacao}/{novaData}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> validarDataReagendamento(
            @PathVariable("codigoAgendamentoLocacao") Integer codigoAgendamentoLocacao,
            @PathVariable("novaData") String novaData,
            @PathVariable("ctx") String ctx
    ) {
        try {
            Date date;
            try {
                date = new Date(Long.parseLong(novaData));
            } catch (Exception e) {
                throw new ServiceException("A data fornecida não está no formato válido.");
            }

            return ResponseEntityFactory.ok(locacaoService.validarDataReagendamento(ctx, codigoAgendamentoLocacao, date));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/{ctx}/reagendar/{codigoAgendamentoLocacao}/{dia}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> reagendar(
            @PathVariable("codigoAgendamentoLocacao") Integer codigoAgendamentoLocacao,
            @PathVariable("dia") String dia,
            @PathVariable("ctx") String ctx,
            @RequestBody AgendamentoLocacaoDTO agendamentoLocacaoDTO
    ) {
        try {
            Date date;
            try {
                date = new Date(Long.parseLong(dia));
            } catch (Exception e) {
                throw new ServiceException("A data fornecida não está no formato válido.");
            }
            locacaoService.reagendar(codigoAgendamentoLocacao, agendamentoLocacaoDTO, date, ctx);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(AulasController.class.getName()).log(Level.SEVERE, "Erro ao tentar reagendar a locação", e);
            if (e.getCause() == null || e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroConhecido(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ResponseBody
    @RequestMapping(value = "/{ctx}/addAlunoHorarioPlayCheckinCheckout", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> addAlunoHorarioPlayCheckinCheckout(
            @RequestBody AlunoLocacaoHorarioPlayCheckinCheckoutDTO dto,
            @PathVariable("ctx") String ctx
    ) {
        try {
            return ResponseEntityFactory.ok(alunoLocacaoHorarioPlayService.addAlunoHorarioPlayCheckinCheckout(dto, ctx));
        } catch (ServiceException e) {
            Logger.getLogger(AulasController.class.getName()).log(Level.SEVERE, "Erro ao tentar cadastrar a aula", e);
            if (e.getCause() == null || e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroConhecido(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ResponseBody
    @RequestMapping(value = "/{ctx}/addAlunoHorarioPlay", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> addAlunoHorarioPlay(
            @RequestBody AlunoLocacaoHorarioPlayDTO alunoLocacaoHorarioPlayDTO,
            @PathVariable("ctx") String ctx
    ) {
        try {
            return ResponseEntityFactory.ok(alunoLocacaoHorarioPlayService.addAlunoHorarioPlay(alunoLocacaoHorarioPlayDTO, ctx));
        } catch (ServiceException e) {
            Logger.getLogger(AulasController.class.getName()).log(Level.SEVERE, "Erro ao tentar cadastrar a aula", e);
            if (e.getCause() == null || e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroConhecido(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ResponseBody
    @RequestMapping(value = "/{ctx}/aluno-locacao-horario-play/{codigo}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> deleteAlunoLocacaoHorarioPlay(
            @PathVariable Integer codigo,
            @PathVariable("ctx") String ctx
    ) throws JSONException {
        try {
            return ResponseEntityFactory.ok(alunoLocacaoHorarioPlayService.deleteAlunoLocacaoHorarioPlay(codigo, ctx));
        } catch (ServiceException e) {
            Logger.getLogger(LocacaoController.class.getName()).log(Level.SEVERE, "Erro ao tentar excluir locação horário", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    //obtem locacao por usuario
    @ResponseBody
    @RequestMapping(value = "/{ctx}/locacoes-realizadas-por-dia", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> getLocacaoPorUsuario(
            @RequestHeader("empresaId") Integer empresaId,
            @PathVariable("ctx") String ctx,
            @RequestParam String dia,
            @RequestParam Integer codUsuario
    ) {
        try {
            return ResponseEntityFactory.ok(locacaoService.getLocacaoPorUsuario(empresaId, ctx, new Date(Long.parseLong(dia)), codUsuario));
        } catch (Exception e) {
            return ResponseEntityFactory.erroInterno("error_locacao_usuario", "Erro ao obter locações por usuário: " + e.getMessage());
        }
    }


}
