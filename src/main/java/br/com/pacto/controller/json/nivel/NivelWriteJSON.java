package br.com.pacto.controller.json.nivel;

import br.com.pacto.bean.nivel.Nivel;

/**
 *
 * <AUTHOR>
 */
public class NivelWriteJSON {
    private Integer codigo;
    private String nome;
    private Integer ordem;

    public NivelWriteJSON()
    {

    }

    public NivelWriteJSON(Nivel nivel) {
        if (nivel != null) {
            this.codigo = nivel.getCodigo();
            this.nome = nivel.getNome();
            this.ordem = nivel.getOrdem();
        }
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Integer getOrdem() {
        return ordem;
    }

    public void setOrdem(Integer ordem) {
        this.ordem = ordem;
    }
}
