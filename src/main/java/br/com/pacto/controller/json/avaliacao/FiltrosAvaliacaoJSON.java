package br.com.pacto.controller.json.avaliacao;

import br.com.pacto.controller.json.base.SuperJSON;
import br.com.pacto.service.exception.ServiceException;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.Date;

public class FiltrosAvaliacaoJSON extends SuperJSON {

    private String parametro;
    private Date dataInicio;
    private Date dataFim;
    private int codProfessor;
    private String indicador;
    private String objetivo;
    private String tipoAvaliacao;
    private Integer codAvaliador;

    public FiltrosAvaliacaoJSON(JSONObject filters) throws ServiceException {
        if (filters != null) {
            try {
                if(!filters.isNull("quicksearchValue")){
                    parametro = filters.getString("quicksearchValue");
                }
                if(!filters.isNull("dataInicio")){
                    dataInicio = new Date(filters.getLong("dataInicio"));
                }
                if(!filters.isNull("dataFim")){
                    dataFim = new Date(filters.getLong("dataFim"));
                }
                if(!filters.isNull("professorId")){
                    codProfessor = filters.getInt("professorId");
                }
                if(!filters.isNull("indicador")){
                    indicador = filters.getString("indicador");
                }
                if(!filters.isNull("objetivo")){
                    objetivo = filters.getString("objetivo");
                }
                if(!filters.isNull("tipoAvaliacao")){
                    tipoAvaliacao = filters.getString("tipoAvaliacao");
                }

                if(!filters.isNull("avaliadorId")){
                    codAvaliador = filters.getInt("avaliadorId");
                }
            } catch (JSONException e) {
                throw new ServiceException(e);
            }
        }
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Date getDataFim() {
        return dataFim;
    }

    public void setDataFim(Date dataFim) {
        this.dataFim = dataFim;
    }

    public int getCodProfessor() {
        return codProfessor;
    }

    public Integer getCodAvaliador() {
        return codAvaliador;
    }

    public void setCodProfessor(Integer codProfessor) {
        this.codProfessor = codProfessor;
    }

    public String getIndicador() {
        return indicador;
    }

    public void setIndicador(String indicador) {
        this.indicador = indicador;
    }

    public String getParametro() {
        return parametro;
    }

    public void setParametro(String parametro) {
        this.parametro = parametro;
    }

    public String getObjetivo() {
        return objetivo;
    }

    public void setObjetivo(String objetivo) {
        this.objetivo = objetivo;
    }

    public String getTipoAvaliacao() {
        return tipoAvaliacao;
    }

    public void setTipoAvaliacao(String tipoAvaliacao) {
        this.tipoAvaliacao = tipoAvaliacao;
    }
}
