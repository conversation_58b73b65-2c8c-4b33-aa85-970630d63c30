package br.com.pacto.controller.json.token;

import br.com.pacto.bean.perfil.Perfil;
import br.com.pacto.bean.programa.ProfessorResponseTO;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.objeto.Uteis;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by <PERSON><PERSON> on 28/09/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UsuarioResponseTO {

    private Integer id;
    private String username;
    private String nome;
    private String imageUri;
    private List<String> perfis;
    private ProfessorResponseTO professorResponse;
    private Integer usuarioZw;

    public UsuarioResponseTO(Usuario usuario, boolean treinoIndependente) {
        this.id = usuario.getCodigo();
        this.username = usuario.getUserName();
        this.nome = usuario.getProfessor().getNome();
        this.imageUri = usuario.getProfessor().getUriImagem();
        Uteis.logar(null, "Perfil do usuário " + usuario.getPerfil());
        this.perfis = new ArrayList<>();
        perfis.add(usuario.getPerfil().getNome());
        this.professorResponse = new ProfessorResponseTO(usuario,treinoIndependente);
        this.usuarioZw = usuario.getUsuarioZW();
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getImageUri() {
        return imageUri;
    }

    public void setImageUri(String imageUri) {
        this.imageUri = imageUri;
    }

    public List<String> getPerfis() {
        return perfis;
    }

    public void setPerfis(List<String> perfis) {
        this.perfis = perfis;
    }

    public ProfessorResponseTO getProfessorResponse() {
        return professorResponse;
    }

    public void setProfessorResponse(ProfessorResponseTO professorResponse) {
        this.professorResponse = professorResponse;
    }

    public Integer getUsuarioZw() {
        return usuarioZw;
    }

    public void setUsuarioZw(Integer usuarioZw) {
        this.usuarioZw = usuarioZw;
    }

}
