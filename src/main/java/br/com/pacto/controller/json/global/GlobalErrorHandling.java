package br.com.pacto.controller.json.global;

import org.apache.http.HttpStatus;
import org.springframework.core.Ordered;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.support.DefaultHandlerExceptionResolver;
import org.springframework.web.servlet.view.json.MappingJackson2JsonView;

import javax.persistence.OptimisticLockException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR> Siqueira 21/02/2019
 */
public class GlobalErrorHandling extends DefaultHandlerExceptionResolver implements Ordered {
    private static final Logger LOGGER = Logger.getLogger(GlobalErrorHandling.class.getSimpleName());
    private static final String APPLICATION_JSON = "application/json";
    private static final String ERRO_ATTRIBUTE = "erro";

    public int getOrder() {
        return Integer.MIN_VALUE;
    }

    protected ModelAndView doResolveException(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        try {

            if (ex instanceof HttpMessageNotReadableException) {
                LOGGER.log(Level.SEVERE, "Erro ao tentar ler mensagem do client", ex);
                return error(response, ex, HttpStatus.SC_BAD_REQUEST);
            }

            if (ex instanceof OptimisticLockException) {
                LOGGER.log(Level.SEVERE, "Conflito ao tentar alterar registro entity", ex);
                return error(response, ex, HttpStatus.SC_CONFLICT);
            }

        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Erro durante tratamento de erros...", e);
        }

        return super.doResolveException(request, response, handler, ex);
    }

    private ModelAndView error(HttpServletResponse response, Exception ex, int httpStatus) {
        final MappingJackson2JsonView mappingJackson2JsonView = new MappingJackson2JsonView();
        mappingJackson2JsonView.addStaticAttribute(ERRO_ATTRIBUTE, ex.getMessage());
        response.setContentType(APPLICATION_JSON);
        response.setStatus(httpStatus);
        mappingJackson2JsonView.addStaticAttribute(ERRO_ATTRIBUTE, ex.getMessage());
        return new ModelAndView(mappingJackson2JsonView);
    }
}