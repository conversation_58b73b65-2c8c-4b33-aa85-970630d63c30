package br.com.pacto.controller.json.aluno;

import br.com.pacto.bean.colaborador.ColaboradorSimplesTO;
import br.com.pacto.bean.configuracoes.TimeZoneEnum;
import br.com.pacto.bean.ficha.FichaResponseTO;
import br.com.pacto.bean.programa.ProgramaTreino;
import br.com.pacto.bean.programa.ProgramaTreinoFicha;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.intf.usuario.FotoService;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;
import br.com.pacto.util.impl.Ordenacao;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.TimeZone;

@ApiModel(description = "Detalhes do treino do aluno")
public class ProgramaTreinoAlunoResponseDTO {

    @ApiModelProperty(value = "ID do aluno", example = "1")
    private Integer id;
    @ApiModelProperty(value = "Nome completo do aluno", example = "João Silva")
    private String nome;
    @ApiModelProperty(value = "ID do aluno", example = "1")
    private Integer alunoId;
    @ApiModelProperty(value = "Colaborador")
    private ColaboradorSimplesTO professorMontou;
    @ApiModelProperty(value = "Data de início")
    private Date inicio;
    @ApiModelProperty(value = "Data de término")
    private Date termino;
    @ApiModelProperty(value = "Data de lançamento")
    private Date dataLancamento;
    @ApiModelProperty(value = "Total de Treinos", example = "1")
    private Integer totalTreinos;
    @ApiModelProperty(value = "Quantidade de dias na semana", example = "1")
    private Integer qtdDiasSemana;
    @ApiModelProperty(value = "Revisão")
    private Date revisao;
    @ApiModelProperty(value = "Treinos concluídos", example = "1")
    private Integer treinosConcluidos;
    @ApiModelProperty(value = "Fichas")
    private List<FichaResponseTO> fichas;
    @ApiModelProperty(value = "Quantidade de fichas", example = "1")
    private Integer qtFichas;
    @ApiModelProperty(value = "Porcentagem completo")
    private String porcentagemCompleto;
    @ApiModelProperty(value = "Gerado por IA")
    private boolean geradoPorIa;
    @ApiModelProperty(value = "Revisado pelo professor")
    private boolean revisadoProfessor;

    public ProgramaTreinoAlunoResponseDTO() {
    }

    public ProgramaTreinoAlunoResponseDTO(ProgramaTreino programaTreino, String tz, Boolean independente) {
        this.id = programaTreino.getCodigo();
        this.nome = programaTreino.getNome();
        this.alunoId = programaTreino.getCliente().getCodigo();
        this.professorMontou = programaTreino.getProfessorMontou() != null ? new ColaboradorSimplesTO(
                independente ? programaTreino.getProfessorMontou().getCodigo() : programaTreino.getProfessorMontou().getCodigoColaborador(),
                programaTreino.getProfessorMontou().getCodigoColaborador(),
                programaTreino.getProfessorMontou().getNome(),
                programaTreino.getProfessorMontou().getUriImagem()
        ) : null;
        this.inicio = getDate(programaTreino.getDataInicio(), tz);
        this.termino = getDate(programaTreino.getDataTerminoPrevisto(), tz);
        this.dataLancamento = getDate(programaTreino.getDataLancamento(), tz);
        this.totalTreinos = programaTreino.getTotalAulasPrevistas();
        this.qtdDiasSemana = programaTreino.getDiasPorSemana();
        this.revisao = getDate(programaTreino.getDataProximaRevisao(), tz);
        this.treinosConcluidos = programaTreino.getNrTreinosRealizados();
        List<FichaResponseTO> fichas = new ArrayList<>();
        for (ProgramaTreinoFicha programaFicha : programaTreino.getProgramaFichas()) {
            if(programaFicha.getFicha() != null){
                fichas.add(new FichaResponseTO(programaFicha.getFicha(), programaFicha.getDiaSemana()));
            }
        }

        getFichas().addAll(Ordenacao.ordenarLista(fichas, "id"));
    }

    private Date getDate(Date data, String tz){
        if(data == null){
            return null;
        }
        try {
            return Calendario.dataTz(data, tz);
        }catch (Exception e){
            return data;
        }

    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Integer getAlunoId() {
        return alunoId;
    }

    public void setAlunoId(Integer alunoId) {
        this.alunoId = alunoId;
    }

    public ColaboradorSimplesTO getProfessorMontou() {
        return professorMontou;
    }

    public void setProfessorMontou(ColaboradorSimplesTO professorMontou) {
        this.professorMontou = professorMontou;
    }

    public Date getInicio() {
        return inicio;
    }

    public void setInicio(Date inicio) {
        this.inicio = inicio;
    }

    public Date getTermino() {
        return termino;
    }

    public void setTermino(Date termino) {
        this.termino = termino;
    }

    public Date getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public Integer getTotalTreinos() {
        return totalTreinos;
    }

    public void setTotalTreinos(Integer totalTreinos) {
        this.totalTreinos = totalTreinos;
    }

    public Integer getQtdDiasSemana() {
        return qtdDiasSemana;
    }

    public void setQtdDiasSemana(Integer qtdDiasSemana) {
        this.qtdDiasSemana = qtdDiasSemana;
    }

    public Date getRevisao() {
        return revisao;
    }

    public void setRevisao(Date
                                   revisao) {
        this.revisao = revisao;
    }

    public Integer getTreinosConcluidos() {
        return treinosConcluidos;
    }

    public void setTreinosConcluidos(Integer treinosConcluidos) {
        this.treinosConcluidos = treinosConcluidos;
    }

    public List<FichaResponseTO> getFichas() {
        if (fichas == null) {
            fichas = new ArrayList<>();
        }
        return fichas;
    }

    public void setFichas(List<FichaResponseTO> fichas) {
        getFichas().clear();
        if (!UteisValidacao.emptyList(fichas)) {
            getFichas().addAll(fichas);
        }
    }

    public Integer getQtFichas() {
        return qtFichas;
    }

    public void setQtFichas(Integer qtFichas) {
        this.qtFichas = qtFichas;
    }

    public String getPorcentagemCompleto() {
        return porcentagemCompleto;
    }

    public void setPorcentagemCompleto(String porcentagemCompleto) {
        this.porcentagemCompleto = porcentagemCompleto;
    }

    public boolean isGeradoPorIa() {
        return geradoPorIa;
    }

    public void setGeradoPorIa(boolean geradoPorIa) {
        this.geradoPorIa = geradoPorIa;
    }

    public boolean isRevisadoProfessor() {
        return revisadoProfessor;
    }

    public void setRevisadoProfessor(boolean revisadoProfessor) {
        this.revisadoProfessor = revisadoProfessor;
    }
}
