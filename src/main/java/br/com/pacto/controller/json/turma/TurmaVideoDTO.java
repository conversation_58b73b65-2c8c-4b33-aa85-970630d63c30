package br.com.pacto.controller.json.turma;

import br.com.pacto.bean.atividade.AtividadeVideo;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.Objects;

/**
 * Created by ulisses on 23/08/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TurmaVideoDTO {

    private Integer id;

    private Integer turma_codigo;
    private String linkVideo;
    private Boolean professor = false;

    public TurmaVideoDTO(){
    }
    public TurmaVideoDTO(TurmaVideoDTO aa) {
        this.id = aa.getId();
        this.turma_codigo = aa.getTurma_codigo();
        this.linkVideo = aa.getLinkVideo();
        this.professor = aa.getProfessor();
    }

    public Integer getId() {return id;}

    public void setId(Integer id) {this.id = id;}

    public String getLinkVideo() {
        return linkVideo;
    }

    public void setLinkVideo(String linkVideo) {
        this.linkVideo = linkVideo;
    }

    public Boolean getProfessor() {
        return professor;
    }

    public void setProfessor(Boolean professor) {
        this.professor = professor;
    }

    public Integer getTurma_codigo() {
        return turma_codigo;
    }

    public void setTurma_codigo(Integer turma_codigo) {
        this.turma_codigo = turma_codigo;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        TurmaVideoDTO that = (TurmaVideoDTO) o;
        return Objects.equals(id, that.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }
}
