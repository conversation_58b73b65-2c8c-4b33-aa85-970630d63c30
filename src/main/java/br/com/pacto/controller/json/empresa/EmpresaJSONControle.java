/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pacto.controller.json.empresa;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.controller.json.base.SuperControle;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.empresa.ConfiguracaoConvenioEmpresaVO;
import br.com.pacto.service.intf.empresa.EmpresaService;
import br.com.pacto.util.UtilContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import servicos.integracao.zw.IntegracaoTurmasWSConsumer;

import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("/empresa")
public class EmpresaJSONControle extends SuperControle {

    @Autowired
    private EmpresaService service;

    public EmpresaService getService() {
        return service;
    }

    public void setUs(EmpresaService service) {
        this.service = service;
    }

    @RequestMapping(value = "{ctx}/validarChave", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap validarChave(@PathVariable String ctx) {
        ModelMap mm = new ModelMap();
        try {
            EmpresaJSON obj = service.validarChave(ctx);
            mm.addAttribute(RETURN, obj);
        } catch (ServiceException ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(EmpresaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/buscarUnidades", method = {RequestMethod.POST, RequestMethod.GET})
    public @ResponseBody
    ModelMap buscarUnidades(@PathVariable String ctx) {
        ModelMap mm = new ModelMap();
        try {
            IntegracaoTurmasWSConsumer integracaoWS = UtilContext.getBean(IntegracaoTurmasWSConsumer.class);
            return integracaoWS.buscarUnidades(ctx);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(EmpresaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/versao", method = {RequestMethod.POST, RequestMethod.GET})
    public @ResponseBody
    ModelMap versao(@PathVariable String ctx) {
        ModelMap mm = new ModelMap();
        mm.addAttribute(RETURN, Aplicacao.getProp(Aplicacao.version));
        return mm;
    }
    
    
    @RequestMapping(value = "{ctx}/modulos", method = {RequestMethod.POST, RequestMethod.GET})
    public @ResponseBody
    ModelMap modulos(@PathVariable String ctx) {
        ModelMap mm = new ModelMap();
        String modulos = Aplicacao.getProp(ctx, Aplicacao.modulos);
        String[] vModulos = StringUtils.commaDelimitedListToStringArray(modulos);     
        mm.addAttribute(RETURN, vModulos);
        return mm;
    }

    @RequestMapping(value = "{ctx}/empresa", method = {RequestMethod.GET})
    public @ResponseBody
    ResponseEntity<EnvelopeRespostaDTO> consultarEmpresaPorCodFinanceiro(@PathVariable String ctx,
                                                                       @RequestParam Integer codFinanceiro) {
        try {
            Empresa empresa = service.obterPorCodFinanceiro(ctx, codFinanceiro);
            if (empresa == null) {
                throw new Exception("Empresa não encontrada pelo código " + codFinanceiro);
            }
            return ResponseEntityFactory.ok(empresa);
        } catch (Exception ex) {
            return ResponseEntityFactory.erroRegistroNotFoun("Registro não encontrado", ex.getMessage());
        }
    }

    @RequestMapping(value = "{ctx}/tokenSMS", method = {RequestMethod.POST})
    public @ResponseBody
    ResponseEntity<EnvelopeRespostaDTO> atualizarTokenSms(@PathVariable String ctx,
                                                          @RequestParam Integer codFinanceiro,
                                                          @RequestParam String token) {
        try {
            Empresa empresa = service.obterPorCodFinanceiro(ctx, codFinanceiro);
            if (empresa == null) {
                throw new Exception("Empresa não encontrada pelo código " + codFinanceiro);
            }
            empresa.setTokenSMS(token);
            service.alterar(ctx, empresa);
            return ResponseEntityFactory.ok(empresa);
        } catch (Exception ex) {
            return ResponseEntityFactory.erroRegistroNotFoun("Registro não encontrado", ex.getMessage());
        }
    }

    @RequestMapping(value = "{ctx}/tokenSMSShortcode", method = {RequestMethod.POST})
    public @ResponseBody
    ResponseEntity<EnvelopeRespostaDTO> tokenSMSShortcode(@PathVariable String ctx,
                                                          @RequestParam Integer codFinanceiro,
                                                          @RequestParam String token) {
        try {
            Empresa empresa = service.obterPorCodFinanceiro(ctx, codFinanceiro);
            if (empresa == null) {
                throw new Exception("Empresa não encontrada pelo código " + codFinanceiro);
            }
            empresa.setTokenSMSShortcode(token);
            service.alterar(ctx, empresa);
            return ResponseEntityFactory.ok(empresa);
        } catch (Exception ex) {
            return ResponseEntityFactory.erroRegistroNotFoun("Registro não encontrado", ex.getMessage());
        }
    }

    @RequestMapping(value = "{ctx}/convenioPagamento", method = {RequestMethod.POST})
    public @ResponseBody
    ResponseEntity<EnvelopeRespostaDTO> convenioPagamento(@PathVariable String ctx,
                                                          @RequestParam Integer codEmpresa) {
        try {
            if (codEmpresa == null) {
                throw new Exception("Código da empresa não informado");
            }
            Empresa empresa = service.obterPorId(ctx, codEmpresa);
            if (empresa == null) {
                throw new Exception("Empresa não encontrada pelo código " + empresa);
            }
            ConfiguracaoConvenioEmpresaVO configuracaoConvenioEmpresaVO = service.obterConfiguracaoConvenioEmpresa(ctx, codEmpresa);

            return ResponseEntityFactory.ok(configuracaoConvenioEmpresaVO);
        } catch (Exception ex) {
            return ResponseEntityFactory.erroRegistroNotFoun("Registro não encontrado", ex.getMessage());
        }
    }

        @RequestMapping(value = "{ctx}/descricaoTotem", method = {RequestMethod.GET})
    public @ResponseBody
    ResponseEntity<EnvelopeRespostaDTO> descricaoTotem(@PathVariable String ctx, @RequestParam (required = false) Integer empresa) {
        try {
            List<String> totem = service.obterTotens(ctx, empresa);
            return ResponseEntityFactory.ok(totem);
        } catch (Exception ex) {
            return ResponseEntityFactory.erroRegistroNotFoun("Registro não encontrado", ex.getMessage());
        }
    }
}
