/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.controller.json.configuracao;

import br.com.pacto.bean.configuracoes.ConfiguracaoSistema;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class ConfiguracaoSistemaJSON {

    private String key;
    private List<ConfigJSON> configuracoes;
    private Long lastUpdate = 0L;

    public ConfiguracaoSistemaJSON() {
    }

    public ConfiguracaoSistemaJSON(String key, List<ConfiguracaoSistema> configuracoes) {
        this.key = key;
        this.configuracoes = new ArrayList<ConfigJSON>();
        for (ConfiguracaoSistema conf : configuracoes) {
            ConfigJSON cJson = new ConfigJSON(conf.getConfiguracao().name(),
                    conf.getValor(), conf.getConfiguracao().getTipo().name());
            this.configuracoes.add(cJson);
        }
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public List<ConfigJSON> getConfiguracoes() {
        return configuracoes;
    }

    public void setConfiguracoes(List<ConfigJSON> configuracoes) {
        this.configuracoes = configuracoes;
    }

    public Long getLastUpdate() {
        return lastUpdate;
    }

    public void setLastUpdate(Long lastUpdate) {
        this.lastUpdate = lastUpdate;
    }
}
