package br.com.pacto.controller.json.programa;

import br.com.pacto.bean.atividade.AtividadeGrupoMuscular;
import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * Created by ulisses on 03/08/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AtividadeGrupoMuscularResponseTO {

    private Integer id;
    private String nome;

    public AtividadeGrupoMuscularResponseTO(){

    }

    public AtividadeGrupoMuscularResponseTO(AtividadeGrupoMuscular agm){
        this.id = agm.getGrupoMuscular().getCodigo();
        this.nome = agm.getGrupoMuscular().getNome();
    }

    public AtividadeGrupoMuscularResponseTO(Integer id, String nome){
        this.id = id;
        this.nome = nome;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }
}


