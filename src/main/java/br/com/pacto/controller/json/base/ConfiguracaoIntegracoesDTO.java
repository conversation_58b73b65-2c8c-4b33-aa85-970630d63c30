package br.com.pacto.controller.json.base;

import br.com.pacto.controller.json.gogood.ConfigGoGoodDTO;
import br.com.pacto.controller.json.gympass.ConfigGymPassDTO;
import br.com.pacto.util.UteisValidacao;
import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * <AUTHOR> 24/02/2020
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ConfiguracaoIntegracoesDTO {

    private String usar_integracao_bioimpedancia;
    private String usarIntegracaoMyzone;
    private String integracao_sistema_olympia;
    private String url_sistema_olympia;
    private String token_sistema_olympia;
    private String usuario_sistema_olympia;
    private String senha_sistema_olympia;
    private String usar_gympass_booking;
    private String codigo_gympass_booking;
    private String token_academy_gogood;

    public ConfiguracaoIntegracoesDTO constructor(ConfigGymPassDTO configDTO){
        ConfiguracaoIntegracoesDTO config = new ConfiguracaoIntegracoesDTO();
        config.setCodigo_gympass_booking(configDTO.getCodigoGymPass().toString());
        config.setUsar_gympass_booking(configDTO.getCodigoGymPass() == null || UteisValidacao.emptyString(configDTO.getCodigoGymPass()) ? "false" : "true");
        return config;
    }

    public ConfiguracaoIntegracoesDTO constructor(ConfigGoGoodDTO configDTO){
        ConfiguracaoIntegracoesDTO config = new ConfiguracaoIntegracoesDTO();
        config.setToken_academy_gogood(configDTO.getTokenAcademyGoGood());
        return config;
    }

    public Boolean getUsarIntegracaoMyzone() {
        if (!UteisValidacao.emptyString(usarIntegracaoMyzone)) {
            return usarIntegracaoMyzone.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setUsarIntegracaoMyzone(String usarIntegracaoMyzone) {
        this.usarIntegracaoMyzone = usarIntegracaoMyzone;
    }

    public Boolean getUsar_integracao_bioimpedancia() {
        if (!UteisValidacao.emptyString(usar_integracao_bioimpedancia)) {
            return usar_integracao_bioimpedancia.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setUsar_integracao_bioimpedancia(String usar_integracao_bioimpedancia) {
        this.usar_integracao_bioimpedancia = usar_integracao_bioimpedancia;
    }

    public Boolean getIntegracao_sistema_olympia() {
        if (!UteisValidacao.emptyString(integracao_sistema_olympia)) {
            return integracao_sistema_olympia.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setIntegracao_sistema_olympia(String integracao_sistema_olympia) {
        this.integracao_sistema_olympia = integracao_sistema_olympia;
    }

    public String getUrl_sistema_olympia() {
        return url_sistema_olympia;
    }

    public void setUrl_sistema_olympia(String url_sistema_olympia) {
        this.url_sistema_olympia = url_sistema_olympia;
    }

    public String getToken_sistema_olympia() {
        return token_sistema_olympia;
    }

    public void setToken_sistema_olympia(String token_sistema_olympia) {
        this.token_sistema_olympia = token_sistema_olympia;
    }

    public String getUsuario_sistema_olympia() {
        return usuario_sistema_olympia;
    }

    public void setUsuario_sistema_olympia(String usuario_sistema_olympia) {
        this.usuario_sistema_olympia = usuario_sistema_olympia;
    }

    public String getSenha_sistema_olympia() {
        return senha_sistema_olympia;
    }

    public void setSenha_sistema_olympia(String senha_sistema_olympia) {
        this.senha_sistema_olympia = senha_sistema_olympia;
    }

    public Boolean getUsar_gympass_booking() {
        if (!UteisValidacao.emptyString(usar_gympass_booking)) {
            return usar_gympass_booking.trim().equalsIgnoreCase("true");
        } else {
            return false;
        }
    }

    public void setUsar_gympass_booking(String usar_gympass_booking) {
        this.usar_gympass_booking = usar_gympass_booking;
    }

    public String getCodigo_gympass_booking() {
        if (codigo_gympass_booking == null) {
            codigo_gympass_booking = "";
        }
        return codigo_gympass_booking;
    }

    public void setCodigo_gympass_booking(String codigo_gympass_booking) {
        this.codigo_gympass_booking = codigo_gympass_booking;
    }

    public String getToken_academy_gogood() {
        return token_academy_gogood;
    }

    public void setToken_academy_gogood(String token_academy_gogood) {
        this.token_academy_gogood = token_academy_gogood;
    }
}
