package br.com.pacto.controller.json.retiraFicha;

import br.com.pacto.bean.retiraficha.ConfiguracaoRetiraFicha;
import br.com.pacto.controller.json.base.SuperJSON;
import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class ConfiguracaoRetiraFichaJson extends SuperJSON {

    private Integer codigo;

    private Integer codigoEmpresa;


    private String descricao;
    //Campos existentes nas Configurações do Retira Ficha
    //Geral

    private String pastaFotos;
    private String pastaDados;
    private Integer monitor;
    private Boolean barraFerramentas;
    private Boolean modoCompartilhado;
    private Boolean habilitarModoCompartilhado;
    private Boolean mostrarFrequenciaemPorcentagem;
    private Boolean aumentarInformacoesAluno;
    private Boolean mostrarNotificacoes;
    private Boolean naoImprimirProgramaVencido;
    private Boolean habitlitarModoVisualizacao;
    private Integer tempoAguarde;
    private String componenteVideo;

    //Impressao
    private String layoutFicha;
    private Boolean utilizarImpressoraMatricial;
    private String tipoImpressora;
    private String porta;
    private String fonte;

    //Conexao
    private String chave;
    private Boolean desatBuscaURLAutomatica;

    //Digital
    private Boolean ativarDigitalNeokoros;
    private Boolean utilizaServidorUnificado;
    private String serial;
    private Integer portaDigital;
    private Integer resolucao;
    private Boolean ativarDigitalNitigen;
    private String equipamento;
    private String bancoDados;
    private String usuarioBDDigital;
    private String senhaBDDigital;
    private String servidorDigital;

    //Turmas
    private String uRLTurmas;
    private String layoutFichaTurma;
    private Boolean ativarModuloAulas;
    private Boolean desativarModuloRetiraFichas;
    private Boolean fecharTelaAposInclusao;
    private Boolean permitirImpressaoSegundaVia;

    //Unidades

    // Tela Principal
    private String logoCabecalho;
    private Boolean naoMostrarLogoCabecalho;
    private String logoRodape;
    private Boolean naoMostrarRodape;
    private String linkFacebook;
    private String linkTwitter;
    private Boolean aguardarCarregamentoSistema;
    private String pastaBanner;
    private String pastaBannerCustom;
    private Boolean naoUsarBannerSistema;
    private String versao;

    public ConfiguracaoRetiraFichaJson() {
    }

    public ConfiguracaoRetiraFichaJson(ConfiguracaoRetiraFicha configuracaoRetiraFicha) {
        this.codigo = configuracaoRetiraFicha.getCodigo();
        this.descricao = configuracaoRetiraFicha.getDescricao();
        this.pastaFotos = configuracaoRetiraFicha.getPastaFotos();
        this.pastaDados = configuracaoRetiraFicha.getPastaDados();
        this.monitor = configuracaoRetiraFicha.getMonitor();
        this.barraFerramentas = configuracaoRetiraFicha.getBarraFerramentas();
        this.modoCompartilhado = configuracaoRetiraFicha.getModoCompartilhado();;
        this.habilitarModoCompartilhado = configuracaoRetiraFicha.getHabilitarModoCompartilhado();
        this.mostrarFrequenciaemPorcentagem = configuracaoRetiraFicha.getMostrarFrequenciaemPorcentagem();
        this.aumentarInformacoesAluno = configuracaoRetiraFicha.getAumentarInformacoesAluno();
        this.mostrarNotificacoes = configuracaoRetiraFicha.getMostrarNotificacoes();
        this.naoImprimirProgramaVencido = configuracaoRetiraFicha.getNaoImprimirProgramaVencido();
        this.habitlitarModoVisualizacao = configuracaoRetiraFicha.getHabitlitarModoVisualizacao();
        this.tempoAguarde = configuracaoRetiraFicha.getTempoAguarde();
        this.componenteVideo = configuracaoRetiraFicha.getComponenteVideo();
        this.layoutFicha = configuracaoRetiraFicha.getLayoutFicha();
        this.utilizarImpressoraMatricial = configuracaoRetiraFicha.getUtilizarImpressoraMatricial();
        this.tipoImpressora = configuracaoRetiraFicha.getTipoImpressora();
        this.porta = configuracaoRetiraFicha.getPorta();
        this.fonte = configuracaoRetiraFicha.getFonte();
        this.chave = configuracaoRetiraFicha.getChave();
        this.desatBuscaURLAutomatica = configuracaoRetiraFicha.getDesatBuscaURLAutomatica();
        this.ativarDigitalNeokoros = configuracaoRetiraFicha.getAtivarDigitalNeokoros();
        this.utilizaServidorUnificado = configuracaoRetiraFicha.getUtilizaServidorUnificado();
        this.serial = configuracaoRetiraFicha.getSerial();
        this.portaDigital = configuracaoRetiraFicha.getPortaDigital();
        this.resolucao = configuracaoRetiraFicha.getResolucao();
        this.ativarDigitalNitigen = configuracaoRetiraFicha.getAtivarDigitalNitigen();
        this.equipamento = configuracaoRetiraFicha.getEquipamento();
        this.bancoDados = configuracaoRetiraFicha.getBancoDados();
        this.usuarioBDDigital = configuracaoRetiraFicha.getUsuarioBDDigital();
        this.senhaBDDigital = configuracaoRetiraFicha.getSenhaBDDigital();
        this.uRLTurmas = configuracaoRetiraFicha.getuRLTurmas();
        this.layoutFichaTurma = configuracaoRetiraFicha.getLayoutFichaTurma();
        this.ativarModuloAulas = configuracaoRetiraFicha.getAtivarModuloAulas();
        this.desativarModuloRetiraFichas = configuracaoRetiraFicha.getDesativarModuloRetiraFichas();
        this.fecharTelaAposInclusao = configuracaoRetiraFicha.getFecharTelaAposInclusao();
        this.permitirImpressaoSegundaVia = configuracaoRetiraFicha.getPermitirImpressaoSegundaVia();
        this.logoCabecalho = configuracaoRetiraFicha.getLogoCabecalho();
        this.naoMostrarLogoCabecalho = configuracaoRetiraFicha.getNaoMostrarLogoCabecalho();
        this.logoRodape = configuracaoRetiraFicha.getLogoRodape();
        this.naoMostrarRodape = configuracaoRetiraFicha.getNaoMostrarRodape();
        this.linkFacebook = configuracaoRetiraFicha.getLinkFacebook();
        this.linkTwitter = configuracaoRetiraFicha.getLinkTwitter();
        this.aguardarCarregamentoSistema = configuracaoRetiraFicha.getAguardarCarregamentoSistema();
        this.pastaBanner = configuracaoRetiraFicha.getPastaBanner();
        this.pastaBannerCustom = configuracaoRetiraFicha.getPastaBannerCustom();
        this.naoUsarBannerSistema = configuracaoRetiraFicha.getNaoUsarBannerSistema();
        this.servidorDigital = configuracaoRetiraFicha.getServidorDigital();
        this.versao = configuracaoRetiraFicha.getVersao();
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getCodigoEmpresa() {
        return codigoEmpresa;
    }

    public void setCodigoEmpresa(Integer codigoEmpresa) {
        this.codigoEmpresa = codigoEmpresa;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getPastaFotos() {
        return pastaFotos;
    }

    public void setPastaFotos(String pastaFotos) {
        this.pastaFotos = pastaFotos;
    }

    public String getPastaDados() {
        return pastaDados;
    }

    public void setPastaDados(String pastaDados) {
        this.pastaDados = pastaDados;
    }

    public Integer getMonitor() {
        return monitor;
    }

    public void setMonitor(Integer monitor) {
        this.monitor = monitor;
    }

    public Boolean getBarraFerramentas() {
        return barraFerramentas;
    }

    public void setBarraFerramentas(Boolean barraFerramentas) {
        this.barraFerramentas = barraFerramentas;
    }

    public Boolean getModoCompartilhado() {
        return modoCompartilhado;
    }

    public void setModoCompartilhado(Boolean modoCompartilhado) {
        this.modoCompartilhado = modoCompartilhado;
    }

    public Boolean getHabilitarModoCompartilhado() {
        return habilitarModoCompartilhado;
    }

    public void setHabilitarModoCompartilhado(Boolean habilitarModoCompartilhado) {
        this.habilitarModoCompartilhado = habilitarModoCompartilhado;
    }

    public Boolean getMostrarFrequenciaemPorcentagem() {
        return mostrarFrequenciaemPorcentagem;
    }

    public void setMostrarFrequenciaemPorcentagem(Boolean mostrarFrequenciaemPorcentagem) {
        this.mostrarFrequenciaemPorcentagem = mostrarFrequenciaemPorcentagem;
    }

    public Boolean getAumentarInformacoesAluno() {
        return aumentarInformacoesAluno;
    }

    public void setAumentarInformacoesAluno(Boolean aumentarInformacoesAluno) {
        this.aumentarInformacoesAluno = aumentarInformacoesAluno;
    }

    public Boolean getMostrarNotificacoes() {
        return mostrarNotificacoes;
    }

    public void setMostrarNotificacoes(Boolean mostrarNotificacoes) {
        this.mostrarNotificacoes = mostrarNotificacoes;
    }

    public Boolean getNaoImprimirProgramaVencido() {
        return naoImprimirProgramaVencido;
    }

    public void setNaoImprimirProgramaVencido(Boolean naoImprimirProgramaVencido) {
        this.naoImprimirProgramaVencido = naoImprimirProgramaVencido;
    }

    public Boolean getHabitlitarModoVisualizacao() {
        return habitlitarModoVisualizacao;
    }

    public void setHabitlitarModoVisualizacao(Boolean habitlitarModoVisualizacao) {
        this.habitlitarModoVisualizacao = habitlitarModoVisualizacao;
    }

    public Integer getTempoAguarde() {
        return tempoAguarde;
    }

    public void setTempoAguarde(Integer tempoAguarde) {
        this.tempoAguarde = tempoAguarde;
    }

    public String getComponenteVideo() {
        return componenteVideo;
    }

    public void setComponenteVideo(String componenteVideo) {
        this.componenteVideo = componenteVideo;
    }

    public String getLayoutFicha() {
        return layoutFicha;
    }

    public void setLayoutFicha(String layoutFicha) {
        this.layoutFicha = layoutFicha;
    }

    public Boolean getUtilizarImpressoraMatricial() {
        return utilizarImpressoraMatricial;
    }

    public void setUtilizarImpressoraMatricial(Boolean utilizarImpressoraMatricial) {
        this.utilizarImpressoraMatricial = utilizarImpressoraMatricial;
    }

    public String getTipoImpressora() {
        return tipoImpressora;
    }

    public void setTipoImpressora(String tipoImpressora) {
        this.tipoImpressora = tipoImpressora;
    }

    public String getPorta() {
        return porta;
    }

    public void setPorta(String porta) {
        this.porta = porta;
    }

    public String getFonte() {
        return fonte;
    }

    public void setFonte(String fonte) {
        this.fonte = fonte;
    }

    public String getChave() {
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    public Boolean getDesatBuscaURLAutomatica() {
        return desatBuscaURLAutomatica;
    }

    public void setDesatBuscaURLAutomatica(Boolean desatBuscaURLAutomatica) {
        this.desatBuscaURLAutomatica = desatBuscaURLAutomatica;
    }

    public Boolean getAtivarDigitalNeokoros() {
        return ativarDigitalNeokoros;
    }

    public void setAtivarDigitalNeokoros(Boolean ativarDigitalNeokoros) {
        this.ativarDigitalNeokoros = ativarDigitalNeokoros;
    }

    public Boolean getUtilizaServidorUnificado() {
        return utilizaServidorUnificado;
    }

    public void setUtilizaServidorUnificado(Boolean utilizaServidorUnificado) {
        this.utilizaServidorUnificado = utilizaServidorUnificado;
    }

    public String getSerial() {
        return serial;
    }

    public void setSerial(String serial) {
        this.serial = serial;
    }

    public Integer getPortaDigital() {
        return portaDigital;
    }

    public void setPortaDigital(Integer portaDigital) {
        this.portaDigital = portaDigital;
    }

    public Integer getResolucao() {
        return resolucao;
    }

    public void setResolucao(Integer resolucao) {
        this.resolucao = resolucao;
    }

    public Boolean getAtivarDigitalNitigen() {
        return ativarDigitalNitigen;
    }

    public void setAtivarDigitalNitigen(Boolean ativarDigitalNitigen) {
        this.ativarDigitalNitigen = ativarDigitalNitigen;
    }

    public String getEquipamento() {
        return equipamento;
    }

    public void setEquipamento(String equipamento) {
        this.equipamento = equipamento;
    }

    public String getBancoDados() {
        return bancoDados;
    }

    public void setBancoDados(String bancoDados) {
        this.bancoDados = bancoDados;
    }

    public String getUsuarioBDDigital() {
        return usuarioBDDigital;
    }

    public void setUsuarioBDDigital(String usuarioBDDigital) {
        this.usuarioBDDigital = usuarioBDDigital;
    }

    public String getSenhaBDDigital() {
        return senhaBDDigital;
    }

    public void setSenhaBDDigital(String senhaBDDigital) {
        this.senhaBDDigital = senhaBDDigital;
    }

    public String getuRLTurmas() {
        return uRLTurmas;
    }

    public void setuRLTurmas(String uRLTurmas) {
        this.uRLTurmas = uRLTurmas;
    }

    public String getLayoutFichaTurma() {
        return layoutFichaTurma;
    }

    public void setLayoutFichaTurma(String layoutFichaTurma) {
        this.layoutFichaTurma = layoutFichaTurma;
    }

    public Boolean getAtivarModuloAulas() {
        return ativarModuloAulas;
    }

    public void setAtivarModuloAulas(Boolean ativarModuloAulas) {
        this.ativarModuloAulas = ativarModuloAulas;
    }

    public Boolean getDesativarModuloRetiraFichas() {
        return desativarModuloRetiraFichas;
    }

    public void setDesativarModuloRetiraFichas(Boolean desativarModuloRetiraFichas) {
        this.desativarModuloRetiraFichas = desativarModuloRetiraFichas;
    }

    public Boolean getFecharTelaAposInclusao() {
        return fecharTelaAposInclusao;
    }

    public void setFecharTelaAposInclusao(Boolean fecharTelaAposInclusao) {
        this.fecharTelaAposInclusao = fecharTelaAposInclusao;
    }

    public Boolean getPermitirImpressaoSegundaVia() {
        return permitirImpressaoSegundaVia;
    }

    public void setPermitirImpressaoSegundaVia(Boolean permitirImpressaoSegundaVia) {
        this.permitirImpressaoSegundaVia = permitirImpressaoSegundaVia;
    }

    public String getLogoCabecalho() {
        return logoCabecalho;
    }

    public void setLogoCabecalho(String logoCabecalho) {
        this.logoCabecalho = logoCabecalho;
    }

    public Boolean getNaoMostrarLogoCabecalho() {
        return naoMostrarLogoCabecalho;
    }

    public void setNaoMostrarLogoCabecalho(Boolean naoMostrarLogoCabecalho) {
        this.naoMostrarLogoCabecalho = naoMostrarLogoCabecalho;
    }

    public String getLogoRodape() {
        return logoRodape;
    }

    public void setLogoRodape(String logoRodape) {
        this.logoRodape = logoRodape;
    }

    public Boolean getNaoMostrarRodape() {
        return naoMostrarRodape;
    }

    public void setNaoMostrarRodape(Boolean naoMostrarRodape) {
        this.naoMostrarRodape = naoMostrarRodape;
    }

    public String getLinkFacebook() {
        return linkFacebook;
    }

    public void setLinkFacebook(String linkFacebook) {
        this.linkFacebook = linkFacebook;
    }

    public String getLinkTwitter() {
        return linkTwitter;
    }

    public void setLinkTwitter(String linkTwitter) {
        this.linkTwitter = linkTwitter;
    }

    public Boolean getAguardarCarregamentoSistema() {
        return aguardarCarregamentoSistema;
    }

    public void setAguardarCarregamentoSistema(Boolean aguardarCarregamentoSistema) {
        this.aguardarCarregamentoSistema = aguardarCarregamentoSistema;
    }

    public String getPastaBanner() {
        return pastaBanner;
    }

    public void setPastaBanner(String pastaBanner) {
        this.pastaBanner = pastaBanner;
    }

    public String getPastaBannerCustom() {
        return pastaBannerCustom;
    }

    public void setPastaBannerCustom(String pastaBannerCustom) {
        this.pastaBannerCustom = pastaBannerCustom;
    }

    public Boolean getNaoUsarBannerSistema() {
        return naoUsarBannerSistema;
    }

    public void setNaoUsarBannerSistema(Boolean naoUsarBannerSistema) {
        this.naoUsarBannerSistema = naoUsarBannerSistema;
    }

    public String getServidorDigital() {
        return servidorDigital;
    }

    public void setServidorDigital(String servidorDigital) {
        this.servidorDigital = servidorDigital;
    }

    public String getVersao() {
        return versao;
    }

    public void setVersao(String versao) {
        this.versao = versao;
    }

}
