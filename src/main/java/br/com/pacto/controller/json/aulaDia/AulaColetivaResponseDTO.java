package br.com.pacto.controller.json.aulaDia;

import br.com.pacto.bean.nivel.NivelTO;
import br.com.pacto.controller.json.turma.HorarioTurmaResponseDTO;
import br.com.pacto.controller.json.turma.TurmaVideoDTO;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class AulaColetivaResponseDTO {

    private Integer codigo;
    private String descricao;
    private String identificador;
    private String cor;
    private Integer empresa; // código zw da empresa
    private Double meta;
    private Integer pontuacaoBonus;
    private Double bonificacao;
    private String mensagem;
    private Integer ocupacao;
    private Integer modalidadeId;
    private Long dataInicio; // dataInicio: 1538362800000
    private Long dataFinal; // dataFinal: 1540954800000
    private Integer toleranciaMin;
    private Integer tipoTolerancia;
    private Boolean validarRestricoesMarcacao;
    private Boolean naoValidarModalidadeContrato;
    private Integer produtoGymPass;
    private Integer idClasseGymPass;
    private String urlTurmaVirtual;
    private String imageUrl;
    private String imageDataUpload;
    private Boolean permiteFixar;
    private Boolean aulaIntegracaoSelfloops;
    private Boolean visualizarProdutosGympass;
    private Boolean visualizarProdutosTotalpass;
    private List<NivelTO> niveis;
    private Integer idadeMaximaAnos;
    private Integer idadeMaximaMeses;
    private Integer idadeMinimaAnos;
    private Integer idadeMinimaMeses;
    private List<TurmaVideoDTO> linkVideos;
    private String tipoReservaEquipamento;
    private String mapaEquipamentos;
    private List<TurmaMapaEquipamentoAparelhoDTO> turmaMapaEquipamentoAparelho;
    private List<MapaEquipamentoAparelhoDTO> listaMapaEquipamentoAparelho;
    private List<HorarioTurmaResponseDTO> horarios;

    public AulaColetivaResponseDTO() { }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getIdentificador() {
        return identificador;
    }

    public void setIdentificador(String identificador) {
        this.identificador = identificador;
    }

    public String getCor() {
        return cor;
    }

    public void setCor(String cor) {
        this.cor = cor;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public Double getMeta() {
        return meta;
    }

    public void setMeta(Double meta) {
        this.meta = meta;
    }

    public Integer getPontuacaoBonus() {
        return pontuacaoBonus;
    }

    public void setPontuacaoBonus(Integer pontuacaoBonus) {
        this.pontuacaoBonus = pontuacaoBonus;
    }

    public Double getBonificacao() {
        return bonificacao;
    }

    public void setBonificacao(Double bonificacao) {
        this.bonificacao = bonificacao;
    }

    public String getMensagem() {
        return mensagem;
    }

    public void setMensagem(String mensagem) {
        this.mensagem = mensagem;
    }

    public Integer getOcupacao() {
        return ocupacao;
    }

    public void setOcupacao(Integer ocupacao) {
        this.ocupacao = ocupacao;
    }

    public Integer getModalidadeId() {
        return modalidadeId;
    }

    public void setModalidadeId(Integer modalidadeId) {
        this.modalidadeId = modalidadeId;
    }

    public Long getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Long dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Long getDataFinal() {
        return dataFinal;
    }

    public void setDataFinal(Long dataFinal) {
        this.dataFinal = dataFinal;
    }

    public Integer getToleranciaMin() {
        return toleranciaMin;
    }

    public void setToleranciaMin(Integer toleranciaMin) {
        this.toleranciaMin = toleranciaMin;
    }

    public Integer getTipoTolerancia() {
        return tipoTolerancia;
    }

    public void setTipoTolerancia(Integer tipoTolerancia) {
        this.tipoTolerancia = tipoTolerancia;
    }

    public Boolean getValidarRestricoesMarcacao() {
        if (validarRestricoesMarcacao == null) {
            return false;
        }
        return validarRestricoesMarcacao;
    }

    public void setValidarRestricoesMarcacao(Boolean validarRestricoesMarcacao) {
        this.validarRestricoesMarcacao = validarRestricoesMarcacao;
    }

    public Boolean getNaoValidarModalidadeContrato() {
        if (naoValidarModalidadeContrato == null) {
            return false;
        }
        return naoValidarModalidadeContrato;
    }

    public void setNaoValidarModalidadeContrato(Boolean naoValidarModalidadeContrato) {
        this.naoValidarModalidadeContrato = naoValidarModalidadeContrato;
    }

    public Integer getProdutoGymPass() {
        return produtoGymPass;
    }

    public void setProdutoGymPass(Integer produtoGymPass) {
        this.produtoGymPass = produtoGymPass;
    }

    public Integer getIdClasseGymPass() {
        return idClasseGymPass;
    }

    public void setIdClasseGymPass(Integer idClasseGymPass) {
        this.idClasseGymPass = idClasseGymPass;
    }

    public String getUrlTurmaVirtual() {
        return urlTurmaVirtual;
    }

    public void setUrlTurmaVirtual(String urlTurmaVirtual) {
        this.urlTurmaVirtual = urlTurmaVirtual;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public String getImageDataUpload() {
        return imageDataUpload;
    }

    public void setImageDataUpload(String imageDataUpload) {
        this.imageDataUpload = imageDataUpload;
    }

    public Boolean getPermiteFixar() {
        if (permiteFixar == null) {
            return false;
        }
        return permiteFixar;
    }

    public void setPermiteFixar(Boolean permiteFixar) {
        this.permiteFixar = permiteFixar;
    }

    public Boolean getAulaIntegracaoSelfloops() {
        if (aulaIntegracaoSelfloops == null) {
            return false;
        }
        return aulaIntegracaoSelfloops;
    }

    public void setAulaIntegracaoSelfloops(Boolean aulaIntegracaoSelfloops) {
        this.aulaIntegracaoSelfloops = aulaIntegracaoSelfloops;
    }

    public Boolean getVisualizarProdutosGympass() {
        if (visualizarProdutosGympass == null) {
            return false;
        }
        return visualizarProdutosGympass;
    }

    public void setVisualizarProdutosGympass(Boolean visualizarProdutosGympass) {
        this.visualizarProdutosGympass = visualizarProdutosGympass;
    }

    public Boolean getVisualizarProdutosTotalpass() {
        if (visualizarProdutosTotalpass == null) {
            return false;
        }
        return visualizarProdutosTotalpass;
    }

    public void setVisualizarProdutosTotalpass(Boolean visualizarProdutosTotalpass) {
        this.visualizarProdutosTotalpass = visualizarProdutosTotalpass;
    }

    public List<NivelTO> getNiveis() {
        return niveis;
    }

    public void setNiveis(List<NivelTO> niveis) {
        this.niveis = niveis;
    }

    public Integer getIdadeMaximaAnos() {
        return idadeMaximaAnos;
    }

    public void setIdadeMaximaAnos(Integer idadeMaximaAnos) {
        this.idadeMaximaAnos = idadeMaximaAnos;
    }

    public Integer getIdadeMaximaMeses() {
        return idadeMaximaMeses;
    }

    public void setIdadeMaximaMeses(Integer idadeMaximaMeses) {
        this.idadeMaximaMeses = idadeMaximaMeses;
    }

    public Integer getIdadeMinimaAnos() {
        return idadeMinimaAnos;
    }

    public void setIdadeMinimaAnos(Integer idadeMinimaAnos) {
        this.idadeMinimaAnos = idadeMinimaAnos;
    }

    public Integer getIdadeMinimaMeses() {
        return idadeMinimaMeses;
    }

    public void setIdadeMinimaMeses(Integer idadeMinimaMeses) {
        this.idadeMinimaMeses = idadeMinimaMeses;
    }

    public List<TurmaVideoDTO> getLinkVideos() {
        return linkVideos;
    }

    public void setLinkVideos(List<TurmaVideoDTO> linkVideos) {
        this.linkVideos = linkVideos;
    }

    public String getTipoReservaEquipamento() {
        return tipoReservaEquipamento;
    }

    public void setTipoReservaEquipamento(String tipoReservaEquipamento) {
        this.tipoReservaEquipamento = tipoReservaEquipamento;
    }

    public String getMapaEquipamentos() {
        return mapaEquipamentos;
    }

    public void setMapaEquipamentos(String mapaEquipamentos) {
        this.mapaEquipamentos = mapaEquipamentos;
    }

    public List<TurmaMapaEquipamentoAparelhoDTO> getTurmaMapaEquipamentoAparelho() {
        return turmaMapaEquipamentoAparelho;
    }

    public void setTurmaMapaEquipamentoAparelho(List<TurmaMapaEquipamentoAparelhoDTO> turmaMapaEquipamentoAparelho) {
        this.turmaMapaEquipamentoAparelho = turmaMapaEquipamentoAparelho;
    }

    public List<MapaEquipamentoAparelhoDTO> getListaMapaEquipamentoAparelho() {
        return listaMapaEquipamentoAparelho;
    }

    public void setListaMapaEquipamentoAparelho(List<MapaEquipamentoAparelhoDTO> listaMapaEquipamentoAparelho) {
        this.listaMapaEquipamentoAparelho = listaMapaEquipamentoAparelho;
    }

    public List<HorarioTurmaResponseDTO> getHorarios() {
        return horarios;
    }

    public void setHorarios(List<HorarioTurmaResponseDTO> horarios) {
        this.horarios = horarios;
    }
}
