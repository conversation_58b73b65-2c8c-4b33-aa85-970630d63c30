package br.com.pacto.controller.json.aulaDia;

import br.com.pacto.controller.json.base.SuperJSON;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;


import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class FiltroAulasJSON extends SuperJSON {

    private Boolean nome = false;
    private String parametro;
    private List<Integer> professorIds;
    private List<Integer> ambienteIds;
    private List<Integer> modalidadeIds;

    private Date inicio;
    private Date fim;



    public FiltroAulasJSON(JSONObject filters) throws JSONException {

        if (filters != null) {

            this.parametro = filters.optString("quicksearchValue");
            JSONArray colunasVisiveis = filters.optJSONArray("quicksearchFields");

            if (colunasVisiveis != null) {
                for (int i = 0; i < colunasVisiveis.length(); i++) {
                    if (colunasVisiveis.get(i).equals("nome")) {
                        this.nome = true;
                    }
                }
            }

            JSONArray modalidadeIds = filters.optJSONArray("modalidadeIds");
            this.modalidadeIds = new ArrayList<>();
            if (modalidadeIds != null) {
                for (int i = 0; i < modalidadeIds.length(); i++) {
                    getModalidadeIds().add(modalidadeIds.getInt(i));
                }
            }
            JSONArray professor = filters.optJSONArray("professor");
            this.professorIds = new ArrayList<>();
            if (professor != null) {
                for (int i = 0; i < professor.length(); i++) {
                    getProfessorIds().add(professor.getInt(i));
                }
            }

            JSONArray ambiente = filters.optJSONArray("ambiente");
            this.ambienteIds = new ArrayList<>();
            if (ambiente != null) {
                for (int i = 0; i < ambiente.length(); i++) {
                    getAmbienteIds().add(ambiente.getInt(i));
                }
            }

            this.inicio = filters.optLong("dataInicio") == 0 ? null : new Date(filters.optLong("dataInicio"));
            this.fim = filters.optLong("dataFim") == 0 ? null : new Date(filters.optLong("dataFim"));
        }

    }

    public Boolean getNome() {
        return nome;
    }

    public void setNome(Boolean nome) {
        this.nome = nome;
    }

    public String getParametro() {
        return parametro;
    }

    public void setParametro(String parametro) {this.parametro = parametro;}

    public List<Integer> getProfessorIds() {
        return professorIds;
    }

    public void setProfessorIds(List<Integer> professorIds) {
        this.professorIds = professorIds;
    }

    public List<Integer> getAmbienteIds() {
        return ambienteIds;
    }

    public void setAmbienteIds(List<Integer> ambienteIds) {
        this.ambienteIds = ambienteIds;
    }

    public Date getInicio() {
        return inicio;
    }

    public void setInicio(Date inicio) {
        this.inicio = inicio;
    }

    public Date getFim() {
        return fim;
    }

    public void setFim(Date fim) {
        this.fim = fim;
    }

    public List<Integer> getModalidadeIds() {
        return modalidadeIds;
    }

    public void setModalidadeIds(List<Integer> modalidadeIds) {
        this.modalidadeIds = modalidadeIds;
    }
}
