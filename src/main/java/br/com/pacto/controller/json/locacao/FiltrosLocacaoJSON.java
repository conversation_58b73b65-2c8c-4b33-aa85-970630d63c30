package br.com.pacto.controller.json.locacao;

import br.com.pacto.controller.json.agendamento.FiltrosNewDTO;
import br.com.pacto.objeto.JSONMapper;
import org.json.JSONObject;

public class FiltrosLocacaoJSON {

    private String quicksearchValue;
    private FiltrosNewDTO filterNew;

    FiltrosLocacaoJSON(JSONObject json) {
        try {
            this.filterNew = JSONMapper.getObject(json.getJSONObject("filtrosNew"), FiltrosNewDTO.class);
        } catch (Exception ex) {
        }

        if (json != null) {
            this.quicksearchValue = json.optString("quicksearchValue");
        }
    }

    public String getQuicksearchValue() {
        return quicksearchValue;
    }

    public void setQuicksearchValue(String quicksearchValue) {
        this.quicksearchValue = quicksearchValue;
    }

    public FiltrosNewDTO getFilterNew() {
        return filterNew;
    }

    public void setFilterNew(FiltrosNewDTO filterNew) {
        this.filterNew = filterNew;
    }
}
