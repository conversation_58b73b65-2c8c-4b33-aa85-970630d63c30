package br.com.pacto.controller.json.gestao;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.gestao.CategoriaIndicadorEnum;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.bean.programa.ExecucoesTreinoTO;
import br.com.pacto.bean.programa.ProgramaTreinoAndamentoTO;
import br.com.pacto.bean.tipoEvento.TipoEvento;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.base.SuperControle;
import br.com.pacto.controller.json.professor.FiltroGestaoJSON;
import br.com.pacto.controller.json.professor.ProfessorController;
import br.com.pacto.security.aspecto.Permissao;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.gestao.GestaoServiceImpl;
import br.com.pacto.service.intf.agenda.AgendamentoService;
import br.com.pacto.service.intf.professor.ProfessorSinteticoService;
import br.com.pacto.service.intf.tipoEvento.TipoEventoService;
import br.com.pacto.service.intf.usuario.UsuarioService;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

@Controller
@RequestMapping("/psec/gestao")
public class GestaoController extends SuperControle {

    @Autowired
    private ProfessorSinteticoService professorSinteticoService;
    @Autowired
    private TipoEventoService tipoEventoService;
    @Autowired
    private SessaoService sessaoService;
    @Autowired
    private UsuarioService usuarioService;
    @Autowired
    private AgendamentoService agendamentoService;
    @Autowired
    private GestaoServiceImpl gestaoService;

    @ResponseBody
    @Permissao(recursos = RecursoEnum.COLABORADORES)
    @RequestMapping(value = "/indicadores-agenda", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> indicadoresAgenda(
            @RequestParam(value = "filters", required = false) JSONObject filters,
            @RequestParam(value = "configs", required = false) JSONObject configs,
            @RequestParam(value = "sort", required = false) String sort,
            @RequestHeader("empresaId") Integer empresaId,
            HttpServletRequest request
    ) {
        try {
            FiltroGestaoJSON filtroGestaoJSON = new FiltroGestaoJSON(filters, configs);
            filtroGestaoJSON.setCategoria(CategoriaIndicadorEnum.AGENDA);

            return ResponseEntityFactory.ok(professorSinteticoService.carregarIndicadores(request, null, empresaId, filtroGestaoJSON, sort));
        } catch (ServiceException e) {
            Logger.getLogger(ProfessorController.class.getName()).log(Level.SEVERE, "Erro ao tentar carregar indicadores da agenda", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }


    @ResponseBody
    @Permissao(recursos = RecursoEnum.COLABORADORES)
    @RequestMapping(value = "/indicadores-agenda/alunos", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> indicadoresAgendaAlunos(
            @RequestParam(value = "filters", required = false) JSONObject filters,
            @RequestParam(value = "sort", required = false) String sort,
            @RequestHeader("empresaId") Integer empresaId
    ) {
        try {
            FiltroGestaoJSON filtroGestaoJSON = new FiltroGestaoJSON(filters);
            filtroGestaoJSON.setCategoria(CategoriaIndicadorEnum.AGENDA);

            return ResponseEntityFactory.ok(agendamentoService.obterPorProfessor(empresaId, filtroGestaoJSON, sort));
        } catch (ServiceException e) {
            Logger.getLogger(ProfessorController.class.getName()).log(Level.SEVERE, "Erro ao tentar carregar indicadores da agenda", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }


    @ResponseBody
    @Permissao(recursos = RecursoEnum.COLABORADORES)
    @RequestMapping(value = "/tiposDeEvento", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> tiposEvento(
            @RequestHeader("empresaId") Integer empresaId,
            @RequestParam(value = "incluirInativos") Boolean incluirInativos
    ) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            Usuario usuario = usuarioService.obterPorId(ctx, sessaoService.getUsuarioAtual().getId());
            List<TipoEvento> tipoEventoList = tipoEventoService.consultarQuaisTiposUsuarioTemPermissao(ctx, usuario, !incluirInativos);
            List<TipoDeEventosDTO> tipoDeEventosDTOS = new ArrayList<>();
            for (TipoEvento te : tipoEventoList){
                tipoDeEventosDTOS.add(new TipoDeEventosDTO(te));
            }
            return ResponseEntityFactory.ok(tipoDeEventosDTOS);
        } catch (ServiceException e) {
            Logger.getLogger(ProfessorController.class.getName()).log(Level.SEVERE, "Erro ao tentar carregar indicadores da agenda", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.COLABORADORES)
    @RequestMapping(value = "/andamentos", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> gestaoAndamento(
            @RequestParam(value = "filters", required = false) JSONObject filters,
            @RequestParam(value = "size", required = false) Integer size,
            @RequestParam(value = "page", required = false) Integer page,
            @RequestHeader("empresaId") Integer empresaId,
            PaginadorDTO paginadorDTO) {

        FiltroGestaoProgramaDTO filtroGestaoProgramaDTO;
        try {
            filtroGestaoProgramaDTO = new FiltroGestaoProgramaDTO(filters);
        } catch (JSONException e) {
            filtroGestaoProgramaDTO = null;
            e.printStackTrace();
        }

        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            List<ProgramaTreinoAndamentoTO> programas = gestaoService.gestaoAndamento(ctx, empresaId, filtroGestaoProgramaDTO, paginadorDTO);
            return ResponseEntityFactory.ok(programas, paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(ProfessorController.class.getName()).log(Level.SEVERE, "Erro ao tentar carregar indicadores da agenda", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/execucoes-treino", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> gestaoExecucoesTreino(@RequestParam(value = "filters", required = false) JSONObject filters,
                                                                     @RequestParam(value = "size", required = false) Integer size,
                                                                     @RequestParam(value = "page", required = false) Integer page,
                                                                     @RequestHeader("empresaId") Integer empresaId,
                                                                     PaginadorDTO paginadorDTO) {

        FiltroGestaoProgramaDTO filtroGestaoProgramaDTO;
        try {
            filtroGestaoProgramaDTO = new FiltroGestaoProgramaDTO(filters);
        } catch (JSONException e) {
            filtroGestaoProgramaDTO = null;
            e.printStackTrace();
        }
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            List<ExecucoesTreinoTO> programas = gestaoService.gestaoExecucoesTreino(ctx, empresaId, filtroGestaoProgramaDTO, paginadorDTO);
            return ResponseEntityFactory.ok(programas, paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(ProfessorController.class.getName()).log(Level.SEVERE, "Erro ao tentar carregar relatório de execuções de treino", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

}
