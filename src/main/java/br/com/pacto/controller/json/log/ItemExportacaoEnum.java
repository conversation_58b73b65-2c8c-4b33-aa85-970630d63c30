package br.com.pacto.controller.json.log;

import org.springframework.util.StringUtils;

public enum ItemExportacaoEnum {

    PLANO("plano", "PLANO", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    INTEGRACAOACESSO("integracaoAcesso", "INTEGRAÇÃO DE ACESSO", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    REPLICARPLANO("replicarPlano", "REPLICAR PLANO", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    PACOTE("pacote", "PACOTE", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    SERVIDORFACIAL("servidorFacial", "SERVIDOR FACIAL", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    BALANCO("balanco", "BALANÇO", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    DESCONTO("desconto", "DESCON<PERSON>", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    PRODUTOESTOQUE("produtoEstoque", "Configurar Produto Estoque", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    CATEGORIA("categoria", "Categoria de Clientes", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    CIDADE("cidade", "Cidade", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    CLASSIFICACAO("classificacao", "Classificação", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    DEPARTAMENTO("departamento", "Departamento", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    GRUPOCOMDESCONTO("grupoComDesconto", "Grupo com Desconto", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    INDICEFINANCEIRO("indiceFinanceiro", "Índice Financeiro", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    GRAUINSTRUCAO("grauInstrucao", "Grau de Instrução", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    PAIS("pais", "País", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    PARENTESCO("parentesco", "Parentesco", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    PERGUNTA("pergunta", "Pergunta", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    PESQUISAOSASTIFACAO("pesquisaoSastifacao", "Pesquisa de Sastifação / NPS", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    PROFISSAO("profissao", "Profissão", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    QUESTIONARIO("questionario", "Questionário", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    BRINDE("brinde", "Brinde", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    JUSTIFICATIVAOPERACAO("justificativaOperacao", "Justificativa de Operação", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    MODELOCONTRATO("modeloContrato", "Modelo de COntrato", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    REPLICAREMPRESAMODELOCONTRATO("replicarEmpresaModeloContrato", "Replicar Empresa (Modelo de Contrato)", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    ADQUIRENTE("adquirente", "Adquirente", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    CONTACORRENTE("contaCorrente", "Conta Corrente", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    FORMAPAGAMENTO("formaPagamento", "Formas de Pagamento", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    METAFINANCEIRA("metaFinanceira", "Meta Financeira de Vendas", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    OPERADORACARTAO("operadoraCartao", "Operadora de Cartão", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    CONVENIODESCONTO("convenioDesconto", "Convênio de Desconto", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    BANCO("banco", "Banco", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    PERFILACESSOUSUARIO("perfilAcessoUsuario", "Usuário ativos que tem o perfil de acesso", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    CONDICAOPAGAMENTO("condicaoPagamento", "Condição de Pagamento", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    HORARIO("horario", "Horário", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    TIPOPLANO("tipoPlano", "Tipo de Plano", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    CATEGORIAPRODUTO("categoriaProduto", "Categoria de Produto", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    TAMANHOARMARIO("tamanhoArmario", "Tamanho de Armário", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    RELCONTRATODURACAOALUNO("relContratoDuracaoAluno", "Relatório Contratos por Duração (Aluno)", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    RELCONTRATOSPORDURACAO("relContratosPorDuracao", "Relatório Contratos por Duração", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    TOTALIZADORACESSO("totalizadorAcesso", "Totalizador de Acessos", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    LISTAPESSOAS("listaPessoas", "Lista de Pessoas", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    PROFESSORESALUNOSAVISOMEDICO("professoresAlunosAvisoMedico", "Professores alunos aviso médico", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    TREINOANDAMENTO("treinoAnadamento", "Andamento", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    EXECUCOESDETREINO("execucoesdetreino", "Execuções de Treino", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    TODOSALUNOSATIVOS("todosalunosativos", "Total de Alunos Ativos", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    USAMAPP("usamapp", "Alunos ativos que tem o aplicativo instalado", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    NAOUSAMAPP("naousamapp", "Alunos ativos que não tem o aplicativo instalado", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    INATIVOSAPP("inativosapp", "Alunos inativos e visitantes com o aplicativo instalado", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    INDICADORESATIVIDADESPROFESSORES("indicadoresatividadesprofessores", "Indicadores das atividades dos professores", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    INDICADORESDACARTEIRADOSPROFESSORES("indicadoresCarteiradosProfessores", "Indicadores da carteira dos professores", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    PROFESSORESSUBSTITUIDOS("professoresSubstituidos", "Professores substituídos", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    AMBIENTESCADASTRADOS("ambientescadastrados", "Ambientes cadastrados", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    MODALIDADE("modalidade", "Modalidade", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    RELATORIODOSSERVICOSAGENDADOS("relatoriodosservicosagendados", "Relatório dos Serviços Agendados", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    AULAEXCLUIDA("aulaexcluida", "Aula Excluída", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    AULACADASTRADA("aulaCadastrada", "Aula Cadastrada", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    TURMACADASTRADA("turmaCadastrada", "Turma Cadastrada", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    TIPOSAGENDAMENTOS("tiposAgendamentos", "Tipos de Agendamentos", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    AVALIACOESREALIZADASPERIODO("avaliacoesRealizadasPeriodo", "Avaliações Realizadas Período", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    AVALIACOESNOVASREALIZADASPERIODO("avaliacoesNovasRealizadasPeriodo", "Avaliações Novas Realizadas Período", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    REAVALIACOESREALIZADASPERIODO("reavaliacoesRealizadasPeriodo", "Reavaliações Realizadas Período", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    PREVISAOREAVALIACOES("previsaoReavaliacoes", "Previsão Reavaliações", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    REAVALIACAOPREVISTAREALIZADAS("reavaliacaoPrevistaRealizadas", "Reavaliação Prevista Realizadas", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    REAVALIACAOPREVISTAATRASADAS("reavaliacaoPrevistaAtrasadas", "Reavaliação Prevista Atrasadas", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    REAVALIACAOPREVISTAFUTURAS("reavaliacaoPrevistaFuturas", "Reavaliação Prevista Futuras", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    ATIVOSSEMAVALIACAO("ativosSemAvaliacao", "Ativos Sem Avaliação", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    ATIVOSCOMAVALIACAOATRASADA("ativosComAvaliacaoAtrasada", "Ativos Com Avaliação Atrasada", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    ALUNOSPERDERAMPERCGORDURA("alunosPerderamPercGordura", "Alunos Perderam Perc Gordura", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    ALUNOSPERDERAMPESO("alunosPerderamPeso", "Alunos Perderam Peso", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    ALUNOSGANHARAMMASSAMAGRA("alunosGanharamMassaMagra", "Alunos Ganharam Massa Magra", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    ALUNOSPARQPOSITIVO("alunosParqPositivo", "Alunos Parq Positivo", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    OBJETIVOALUNOS("objetivoAlunos", "Objetivo Alunos", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    ANAMNESES("anamneses", "Anamneses", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    OBJETIVO("objetivo", "Objetivo", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    ALUNOSBICROSS("alunosbicross", "Alunos", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    RESULTADOSBICROSS("resultadosbicross", "Resultados", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    AGENDAMENTOSBICROSS("agendamentosbicross", "Agendamentos", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    FREQUENCIABICROSS("frequenciabicross", "Frequência", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    OCUPACAOBICROSS("ocupacaobicross", "Ocupação", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    WOD("wod", "Wod", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    CROSSAPARELHOS("crossAparelhos", "Aparelhos", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    CROSSATIVIDADES("crossAtividades", "Atividades", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    CROSSBENCHMARKS("crossBenchmarks", "Benchmarks", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    CROSSTIPOWOD("crossTipoWod", "Tipo Wod", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    CROSSTIPOBENCHMARKS("crossTipoBenchmarks", "Tipo Benchmarks", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    AVALIACOESDEPROGRESSO("avaliacoesDeProgresso", "Tipo Benchmarks", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    GRADUACAOATIVIDADES("graduacaoAtividades", "Atividades", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    TOTALALUNOS("totalAlunosTitle", "Total Alunos", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    ALUNOSATIVOS("alunosAtivosTitle", "Alunos Ativos", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    ALUNOSINATIVOS("alunosInativosTitle", "Alunos Inativos", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    ALUNOSSEMACOMPANHAMENTO("alunosSemAcompanhamentoTitle", "Alunos Sem Acompanhamento", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    TREINOVENCIDO("treinoVencidoTitle", "Treino Vencido", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    TREINOEMDIA("treinoEmDiaTitle", "Treino Em Dia", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    ALUNOSEMTREINO("alunoSemTreinoTitle", "Aluno Sem Treino", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    TREINOCOMTREINO("treinoComTreinoTitle", "Treino Com Treino", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    TREINOAVENCER("treinoAVencerTitle", "Treino a Vencer", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    CONTRATOAVENCER("contratoAVencerTitle", "Contrato a Vencer", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    CONTRATORENOVADO("contratoRenovadoTitle", "Contrato Renovado", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    PRESCRICAOTREINO("prescricaoTreino", "Prescrição de Treino", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    RANKING("ranking", "Ranking", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    TREINOEMCASA("treinoEmCasa", "Treino Em Casa", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    TREINOATIVIDADE("treinoAtividade", "Atividade", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    CATEGORIAATIVIDADE("categoriaAtividade", "Categoria Atividade", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    FICHASPREDEFINIDAS("fichasPredefinidas", "Fichas Predefinidas", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    TREINONIVEIS("treinoNiveis", "Níveis", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    PERFILDEACESSO("perfilDeAcesso", "Perfil de Acesso", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    PROGRAMASPREDEFINIDOS("programasPredefinidos", "Programas Predefinidos", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    AGENDAAULAS("aulasTitle", "Aulas", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    AGENDAMODALIDADES("modalidadesTitle", "Modalidades", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    AGENDAPROFESSORESDASH("professoresDashAgendaTitle", "Professores Dash Agenda", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    AGENDAFREQUENCIAALUNOS("frequenciaAlunos", "Frequencia Alunos", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    AGENDAPROFESSORES("professoresTitle", "Agenda Professores", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    AGENDAAVALIACOESFISICA("avaliacoesFisicaTitle", "Avaliação Física", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    AGENDARENOVADOSTREINOS("renovadosTreinosTitle", "Treinos Renovados", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    AGENDANOVOTREINO("novosTreinosTitle", "Treinos Novos", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    AGENDAAGENDADOS("agendadosTitle", "Resumo da agenda - AGENDADOS", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    AGENDAEXECUTADOS("executadosTitle", "Resumo da agenda - EXECUTADOS", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    AGENDAFALTARAM("faltaramTitle", "Resumo da agenda - FALTARAM", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    AGENDACANCELADOS("canceladosTitle", "Resumo da agenda - CANCELADOS", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    BONIFICACAOPROFESSOR("bonificacaoProfessor", "Bonificação Professor", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    EXPORTACAOPRESCRICAOTREINO("exportacaoPrescricaoTreino", "Prescrição de treino", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    EXECUCOESTREINOULTIMOSDIASMANHA("execucoesTreinoUltimosDiasManha", "Execuções de treino nos últimos dias Manhã", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    EXECUCOESTREINOULTIMOSDIASTARDE("execucoesTreinoUltimosDiasTarde", "Execuções de treino nos últimos dias Tarde", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    EXECUCOESTREINOULTIMOSDIASNOITE("execucoesTreinoUltimosDiasNoite", "Execuções de treino nos últimos dias Noite", "PermitirExportarDados", "9.98 - Permitir Exportar Dados"),
    ;

    private String id;
    private String descricaoLog;
    private String permissao;
    private String descricaoPermissao;

    ItemExportacaoEnum(String id, String descricaoLog, String permissao, String descricaoPermissao) {
        this.id = id;
        this.descricaoLog = descricaoLog;
        this.permissao = permissao;
        this.descricaoPermissao =descricaoPermissao;
    }

    public String getId() {
        return id;
    }

    public String getDescricaoLog() {
        return descricaoLog;
    }

    public String getPermissao() {
        return permissao;
    }

    public String getDescricaoPermissao() {
        return descricaoPermissao;
    }

    public static ItemExportacaoEnum obterPorId(final String id) {
        if(StringUtils.hasText(id)) {
            for (ItemExportacaoEnum i : ItemExportacaoEnum.values()) {
                if (i.getId().equals(id)) {
                    return i;
                }
            }
        }
        return null;
    }

}
