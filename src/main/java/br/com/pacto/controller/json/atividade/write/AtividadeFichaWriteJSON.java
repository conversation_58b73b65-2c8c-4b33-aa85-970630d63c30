/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.controller.json.atividade.write;

import br.com.pacto.controller.json.base.SuperJSON;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class AtividadeFichaWriteJSON extends SuperJSON {

    private Integer codigo;
    private Integer ficha;
    private Integer atividade;
    private String idIA;
    private String nomeAtividade;
    private Integer ordem;
    private List<SerieWriteJSON> series = new ArrayList<SerieWriteJSON>();
    private List<AtividadeFichaAjusteWriteJSON> ajustes = new ArrayList<AtividadeFichaAjusteWriteJSON>();
    private Integer metodoExecucao;
    private Integer tipoAtividade;
    private String setid;

    public String getIdIA() {
        return idIA;
    }

    public void setIdIA(String idIA) {
        this.idIA = idIA;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getFicha() {
        return ficha;
    }

    public void setFicha(Integer ficha) {
        this.ficha = ficha;
    }

    public Integer getAtividade() {
        return atividade;
    }

    public void setAtividade(Integer atividade) {
        this.atividade = atividade;
    }

    public Integer getOrdem() {
        return ordem;
    }

    public void setOrdem(Integer ordem) {
        this.ordem = ordem;
    }

    public List<SerieWriteJSON> getSeries() {
        return series;
    }

    public void setSeries(List<SerieWriteJSON> series) {
        this.series = series;
    }

    public List<AtividadeFichaAjusteWriteJSON> getAjustes() {
        return ajustes;
    }

    public void setAjustes(List<AtividadeFichaAjusteWriteJSON> ajustes) {
        this.ajustes = ajustes;
    }

    public Integer getMetodoExecucao() {
        return metodoExecucao;
    }

    public void setMetodoExecucao(Integer metodoExecucao) {
        this.metodoExecucao = metodoExecucao;
    }

    public String getNomeAtividade() {
        return nomeAtividade;
    }

    public void setNomeAtividade(String nomeAtividade) {
        this.nomeAtividade = nomeAtividade;
    }

    public void setTipoAtividade(Integer tipoAtividade) {
        this.tipoAtividade = tipoAtividade;
    }

    public Integer getTipoAtividade() {
        return tipoAtividade;
    }

    public String getSetid() {
        return setid;
    }

    public void setSetid(String setid) {
        this.setid = setid;
    }
}
