package br.com.pacto.controller.json.aluno;

import br.com.pacto.bean.cliente.ClienteObservacao;
import br.com.pacto.bean.usuario.Usuario;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.Date;

@JsonInclude (JsonInclude.Include.NON_NULL)
public class ClienteObservacaoDTO {

    private Integer id;
    private Boolean importante;
    private String observacao;
    private Date data;
    private Boolean avaliacaoFisica;
    private String usuario;

    public ClienteObservacaoDTO(ClienteObservacao clienteObservacao, Usuario usu) {
        this.id = clienteObservacao.getCodigo();
        this.importante = clienteObservacao.getImportante();
        this.observacao = clienteObservacao.getObservacao();
        this.data = clienteObservacao.getDataObservacao();
        this.avaliacaoFisica = clienteObservacao.getAvaliacaoFisica();
        this.usuario = usu != null ? usu.getNome() : "";
    }


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Boolean getImportante() {
        return importante;
    }

    public void setImportante(Boolean importante) {
        this.importante = importante;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public Date getData() {
        return data;
    }

    public void setData(Date data) {
        this.data = data;
    }


    public Boolean getAvaliacaoFisica() {
        return avaliacaoFisica;
    }

    public void setAvaliacaoFisica(Boolean avaliacaoFisica) {
        this.avaliacaoFisica = avaliacaoFisica;
    }

    public String getUsuario() {
        return usuario;
    }

    public void setUsuario(String usuario) {
        this.usuario = usuario;
    }
}
