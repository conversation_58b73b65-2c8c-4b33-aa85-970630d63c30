package br.com.pacto.controller.json.agendamento;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;


@ApiModel(description = "DTO para representar o horário de uma agenda, com os itens agrupados por dia.")
public class AgendaHorarioDocumentacaoDTO {
    @ApiModelProperty(value = "Horário (por exemplo, '00:00')", example = "09:00")
    private String horario;

    @ApiModelProperty(
            value = "Mapeamento dos dias com a lista de itens de agenda. A chave é a data (no formato YYYYMMDD) e o valor é uma lista de HorarioItemAgendaDTO.",
            dataType = "HorarioItemAgendaDocumentacaoDTO"
    )
    private HorarioItemAgendaDocumentacaoDTO dias;

    public String getHorario() {
        return horario;
    }

    public void setHorario(String horario) {
        this.horario = horario;
    }

    public HorarioItemAgendaDocumentacaoDTO getDias() {
        return dias;
    }

    public void setDias(HorarioItemAgendaDocumentacaoDTO dias) {
        this.dias = dias;
    }
}
