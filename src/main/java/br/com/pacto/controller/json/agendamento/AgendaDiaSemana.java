package br.com.pacto.controller.json.agendamento;


public enum AgendaDiaSemana {
    DOMINGO,
    SEGUNDA,
    TERCA,
    QUARTA,
    QUINTA,
    SEXTA,
    SABADO;

    public static AgendaDiaSemana getFromOrdinal(int numeral) {
        AgendaDiaSemana diaSemana = null;
        for (AgendaDiaSemana dia : AgendaDiaSemana.values()) {
            if ((dia.ordinal() + 1) == numeral) {
                diaSemana = dia;
            }
        }
        return diaSemana;
    }

}
