package br.com.pacto.controller.json.gympass;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.bean.gympass.ConfigGymPass;
import br.com.pacto.controller.json.SuperController;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.empresa.EmpresaService;
import br.com.pacto.service.intf.gympass.ConfigGymPassService;
import br.com.pacto.util.UteisValidacao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 23/03/2020
 */
@Controller
@RequestMapping("/psec/configGympass")
public class ConfigGymPassController extends SuperController {

    private final ConfigGymPassService service;
    @Autowired
    private EmpresaService empresaService;
    @Autowired
    private SessaoService sessaoService;

    @Autowired
    public ConfigGymPassController(ConfigGymPassService service) {
        Assert.notNull(service, "O serviço de Configuração de GymPass não foi injetado corretamente");
        this.service = service;
    }

    @ResponseBody
    @RequestMapping(value = "", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> todas(@RequestParam(required = false) Integer empresa) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            if (!UteisValidacao.emptyNumber(empresa)) {
                Empresa empresaObj = empresaService.obterPorId(ctx, empresa);
                ConfigGymPass configGymPass = service.obterPorEmpresa(ctx, empresaObj);
                return ResponseEntityFactory.ok(new ConfigGymPassDTO(configGymPass));
            } else {
                return ResponseEntityFactory.ok(service.obterTodosDTO(ctx));
            }
        } catch (ServiceException e) {
            Logger.getLogger(ConfigGymPassController.class.getName()).log(Level.SEVERE, "Erro ao consultar a configuração GymPass", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alterar(@RequestBody ConfigGymPassDTO dto) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            return ResponseEntityFactory.ok(service.alterarDTO(ctx, dto));
        } catch (ServiceException e) {
            Logger.getLogger(ConfigGymPassController.class.getName()).log(Level.SEVERE, "Erro ao tentar alterar Configuração GymPass", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

}
