package br.com.pacto.controller.json.disponibilidade;

import br.com.pacto.bean.programa.ProfessorResponseTO;
import br.com.pacto.controller.json.turma.AmbienteDTO;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class HorarioDisponibilidadeDTO {

    private Integer codigo;
    private ProfessorResponseTO professor;
    private AmbienteDTO ambiente;
    private String horaInicial;
    private String horaFinal;
    private String diaSemana;
    private Boolean permieAgendarAppTreino;
    private Boolean apenasAlunosCarteira;
    private Boolean ativo;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public ProfessorResponseTO getProfessor() {
        return professor;
    }

    public void setProfessor(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> professor) {
        this.professor = professor;
    }

    public AmbienteDTO getAmbiente() {
        return ambiente;
    }

    public void setAmbiente(AmbienteDTO ambiente) {
        this.ambiente = ambiente;
    }

    public String getHoraInicial() {
        return horaInicial;
    }

    public void setHoraInicial(String horaInicial) {
        this.horaInicial = horaInicial;
    }

    public String getHoraFinal() {
        return horaFinal;
    }

    public void setHoraFinal(String horaFinal) {
        this.horaFinal = horaFinal;
    }

    public String getDiaSemana() {
        return diaSemana;
    }

    public void setDiaSemana(String diaSemana) {
        this.diaSemana = diaSemana;
    }

    public Boolean getPermieAgendarAppTreino() {
        return permieAgendarAppTreino;
    }

    public void setPermieAgendarAppTreino(Boolean permieAgendarAppTreino) {
        this.permieAgendarAppTreino = permieAgendarAppTreino;
    }

    public Boolean getApenasAlunosCarteira() {
        return apenasAlunosCarteira;
    }

    public void setApenasAlunosCarteira(Boolean apenasAlunosCarteira) {
        this.apenasAlunosCarteira = apenasAlunosCarteira;
    }

    public Boolean getAtivo() {
        return ativo;
    }

    public void setAtivo(Boolean ativo) {
        this.ativo = ativo;
    }
}
