package br.com.pacto.controller.json.aulaDia;

import br.com.pacto.bean.nivel.NivelTO;
import br.com.pacto.controller.json.turma.TurmaVideoDTO;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.ArrayList;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class AulaDTO {

    private String ambienteId;
//    ambienteId: "1"
    private String bonificacao;
//    bonificacao: "R$150"
    private String capacidade;
//    capacidade: "12"
    private Integer limiteVagasAgregados;
    private Long dataFinal;
//    dataFinal: 1540954800000
    private Long dataInicio;
//    dataInicio: 1538362800000
    private String[] diasSemana;
//    diasSemana: ["domingo", "segunda"]
    private HorarioDTO[] horarios;
//    horarios: [{inicio: "12:00", fim: "13:00"}, {inicio: "14:00", fim: "15:00"}]
    private String mensagem;
//    mensagem: "Opa asaoisa"
    private String urlVideoYoutube;
//    link do youtube para o cliente transmitir aulas online
    private String meta;
//    meta: "80%"
    private String modalidadeId;
//    modalidadeId: "1"
    private String nome;
//    nome: "vfcvcvc"
    private String ocupacao;
//    ocupacao: "ALTAFREQUENCIA"
    private String pontuacaoBonus;
//    pontuacaoBonus: "122"
    private String professorId;
//    professorId: "57"
    private String toleranciaMin;
    private String tipoTolerancia;
//    toleranciaMin: "12"
    private Boolean validarRestricoesMarcacao;
    private Boolean naoValidarModalidadeContrato;

    private Integer produtoGymPass;
    private String urlTurmaVirtual;
    private Integer idClasseGymPass;
    private boolean visualizarProdutosGympass;
    private boolean visualizarProdutosTotalpass;
    private Boolean permiteFixar;
    private Boolean aulaIntegracaoSelfloops;
    private byte[] image;
    private String imageUrl;
    private Boolean manterFotoAnterior;
    private List<NivelTO> niveis;

    private Integer idadeMaximaAnos;
    private Integer idadeMaximaMeses;
    private Integer idadeMinimaAnos;
    private Integer idadeMinimaMeses;
    private List<TurmaVideoDTO> linkVideos;
    private Integer idHorarioTurmaEdicao;
    private String diaHorarioTurmaEdicao;
    private String diaLimiteTurmaEdicao;
    private String tipoEscolhaEdicao;
    private String horarioInicial;
    private String horarioFinal;
    private String diaSemana;
    private String tipoReservaEquipamento;
    private String mapaEquipamentos;
    private List<TurmaMapaEquipamentoAparelhoDTO> turmaMapaEquipamentoAparelho;

    public Integer getIdadeMaximaAnos() {
        return idadeMaximaAnos;
    }

    public void setIdadeMaximaAnos(Integer idadeMaximaAnos) {
        this.idadeMaximaAnos = idadeMaximaAnos;
    }

    public Integer getIdadeMaximaMeses() {
        return idadeMaximaMeses;
    }

    public void setIdadeMaximaMeses(Integer idadeMaximaMeses) {
        this.idadeMaximaMeses = idadeMaximaMeses;
    }

    public Integer getIdadeMinimaAnos() {
        return idadeMinimaAnos;
    }

    public void setIdadeMinimaAnos(Integer idadeMinimaAnos) {
        this.idadeMinimaAnos = idadeMinimaAnos;
    }

    public Integer getIdadeMinimaMeses() {
        return idadeMinimaMeses;
    }

    public void setIdadeMinimaMeses(Integer idadeMinimaMeses) {
        this.idadeMinimaMeses = idadeMinimaMeses;
    }

    public List<NivelTO> getNiveis() {
        if( niveis == null ){
            niveis = new ArrayList<>();
        }
        return niveis;
    }

    public void setNiveis(List<NivelTO> niveis) {
        this.niveis = niveis;
    }

    public String getTipoTolerancia() {
        return tipoTolerancia;
    }

    public void setTipoTolerancia(String tipoTolerancia) {
        this.tipoTolerancia = tipoTolerancia;
    }

    public String getAmbienteId() {
        return ambienteId;
    }

    public void setAmbienteId(String ambienteId) {
        this.ambienteId = ambienteId;
    }

    public String getBonificacao() {
        return bonificacao;
    }

    public void setBonificacao(String bonificacao) {
        this.bonificacao = bonificacao;
    }

    public String getMensagem() {
        return mensagem;
    }

    public void setMensagem(String mensagem) {
        this.mensagem = mensagem;
    }

    public String getMeta() {
        return meta;
    }

    public void setMeta(String meta) {
        this.meta = meta;
    }

    public String getModalidadeId() {
        return modalidadeId;
    }

    public void setModalidadeId(String modalidadeId) {
        this.modalidadeId = modalidadeId;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getOcupacao() {
        return ocupacao;
    }

    public void setOcupacao(String ocupacao) {
        this.ocupacao = ocupacao;
    }

    public String getPontuacaoBonus() {
        return pontuacaoBonus;
    }

    public void setPontuacaoBonus(String pontuacaoBonus) {
        this.pontuacaoBonus = pontuacaoBonus;
    }

    public String getCapacidade() {
        return capacidade;
    }

    public void setCapacidade(String capacidade) {
        this.capacidade = capacidade;
    }

    public Integer getLimiteVagasAgregados() {
        return limiteVagasAgregados;
    }

    public void setLimiteVagasAgregados(Integer limiteVagasAgregados) {
        this.limiteVagasAgregados = limiteVagasAgregados;
    }

    public String[] getDiasSemana() {
        return diasSemana;
    }

    public void setDiasSemana(String[] diasSemana) {
        this.diasSemana = diasSemana;
    }

    public String getUrlVideoYoutube() { return urlVideoYoutube; }

    public void setUrlVideoYoutube(String urlVideoYoutube) { this.urlVideoYoutube = urlVideoYoutube; }

    public HorarioDTO[] getHorarios() {
        return horarios;
    }

    public void setHorarios(HorarioDTO[] horarios) {
        this.horarios = horarios;
    }

    public Long getDataFinal() {
        return dataFinal;
    }

    public void setDataFinal(Long dataFinal) {
        this.dataFinal = dataFinal;
    }

    public Long getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Long dataInicio) {
        this.dataInicio = dataInicio;
    }

    public String getProfessorId() {
        return professorId;
    }

    public void setProfessorId(String professorId) {
        this.professorId = professorId;
    }

    public String getToleranciaMin() {
        return toleranciaMin;
    }

    public void setToleranciaMin(String toleranciaMin) {
        this.toleranciaMin = toleranciaMin;
    }

    public Boolean getValidarRestricoesMarcacao() {
        return validarRestricoesMarcacao;
    }

    public void setValidarRestricoesMarcacao(Boolean validarRestricoesMarcacao) {
        this.validarRestricoesMarcacao = validarRestricoesMarcacao;
    }

    public Integer getProdutoGymPass() {
        return produtoGymPass;
    }

    public void setProdutoGymPass(Integer produtoGymPass) {
        this.produtoGymPass = produtoGymPass;
    }

    public Integer getIdClasseGymPass() {
        return idClasseGymPass;
    }

    public void setIdClasseGymPass(Integer idClasseGymPass) {
        this.idClasseGymPass = idClasseGymPass;
    }

    public String getUrlTurmaVirtual() {
        return urlTurmaVirtual;
    }

    public void setUrlTurmaVirtual(String urlTurmaVirtual) {
        this.urlTurmaVirtual = urlTurmaVirtual;
    }

    public Boolean getNaoValidarModalidadeContrato() {
        if(naoValidarModalidadeContrato == null){
            naoValidarModalidadeContrato = false;
        }
        return naoValidarModalidadeContrato;
    }

    public void setNaoValidarModalidadeContrato(Boolean naoValidarModalidadeContrato) {
        this.naoValidarModalidadeContrato = naoValidarModalidadeContrato;
    }

    public byte[] getImage() {
        return image;
    }

    public void setImage(byte[] image) {
        this.image = image;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public boolean getVisualizarProdutosGympass() {return visualizarProdutosGympass; }

    public void setVisualizarProdutosGympass(boolean visualizarProdutosGympass) {
        this.visualizarProdutosGympass = visualizarProdutosGympass;
    }

    public boolean getVisualizarProdutosTotalpass() {return visualizarProdutosTotalpass; }

    public void setVisualizarProdutosTotalpass(boolean visualizarProdutosTotalpass) {
        this.visualizarProdutosTotalpass = visualizarProdutosTotalpass;
    }

    public Boolean getPermiteFixar() {
        return permiteFixar;
    }

    public void setPermiteFixar(Boolean permiteFixar) {
        this.permiteFixar = permiteFixar;
    }

       public Boolean getManterFotoAnterior() {
        return manterFotoAnterior;
    }

    public void setManterFotoAnterior(Boolean manterFotoAnterior) {
        this.manterFotoAnterior = manterFotoAnterior;
    }

    public List<TurmaVideoDTO> getLinkVideos() {
        return linkVideos;
    }

    public void setLinkVideos(List<TurmaVideoDTO> linkVideos) {
        this.linkVideos = linkVideos;
    }

    public boolean isVisualizarProdutosGympass() {
        return visualizarProdutosGympass;
    }

    public boolean isVisualizarProdutosTotalpass() {
        return visualizarProdutosTotalpass;
    }

    public Integer getIdHorarioTurmaEdicao() {
        return idHorarioTurmaEdicao;
    }

    public void setIdHorarioTurmaEdicao(Integer idHorarioTurmaEdicao) {
        this.idHorarioTurmaEdicao = idHorarioTurmaEdicao;
    }

    public String getDiaHorarioTurmaEdicao() {
        return diaHorarioTurmaEdicao;
    }

    public void setDiaHorarioTurmaEdicao(String diaHorarioTurmaEdicao) {
        this.diaHorarioTurmaEdicao = diaHorarioTurmaEdicao;
    }

    public String getTipoEscolhaEdicao() {
        return tipoEscolhaEdicao;
    }

    public void setTipoEscolhaEdicao(String tipoEscolhaEdicao) {
        this.tipoEscolhaEdicao = tipoEscolhaEdicao;
    }

    public String getDiaLimiteTurmaEdicao() {
        return diaLimiteTurmaEdicao;
    }

    public void setDiaLimiteTurmaEdicao(String diaLimiteTurmaEdicao) {
        this.diaLimiteTurmaEdicao = diaLimiteTurmaEdicao;
    }

    public String getHorarioInicial() {
        return horarioInicial;
    }

    public void setHorarioInicial(String horarioInicial) {
        this.horarioInicial = horarioInicial;
    }

    public String getHorarioFinal() {
        return horarioFinal;
    }

    public void setHorarioFinal(String horarioFinal) {
        this.horarioFinal = horarioFinal;
    }

    public String getDiaSemana() {
        return diaSemana;
    }

    public void setDiaSemana(String diaSemana) {
        this.diaSemana = diaSemana;
    }

    public String getTipoReservaEquipamento() {
        return tipoReservaEquipamento;
    }

    public void setTipoReservaEquipamento(String tipoReservaEquipamento) {
        this.tipoReservaEquipamento = tipoReservaEquipamento;
    }

    public String getMapaEquipamentos() {
        return mapaEquipamentos;
    }

    public void setMapaEquipamentos(String mapaEquipamentos) {
        this.mapaEquipamentos = mapaEquipamentos;
    }

    public List<TurmaMapaEquipamentoAparelhoDTO> getTurmaMapaEquipamentoAparelho() {
        return turmaMapaEquipamentoAparelho;
    }

    public void setTurmaMapaEquipamentoAparelho(List<TurmaMapaEquipamentoAparelhoDTO> turmaMapaEquipamentoAparelho) {
        this.turmaMapaEquipamentoAparelho = turmaMapaEquipamentoAparelho;
    }

    public Boolean getAulaIntegracaoSelfloops() {
        return aulaIntegracaoSelfloops;
    }

    public void setAulaIntegracaoSelfloops(Boolean aulaIntegracaoSelfloops) {
        this.aulaIntegracaoSelfloops = aulaIntegracaoSelfloops;
    }
}

