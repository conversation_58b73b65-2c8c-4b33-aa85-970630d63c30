package br.com.pacto.controller.json.locacao;

import br.com.pacto.bean.aula.AgendamentoLocacaoProduto;
import br.com.pacto.bean.locacao.LocacaoProdutoSugerido;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class LocacaoProdutoSugeridoTO {

    private Integer codigo;
    private ProdutoTO produto;
    private Double valor;
    private Boolean obrigatorio;
    private QuantidadeDTO quantidade;

    public LocacaoProdutoSugeridoTO() {}

    public LocacaoProdutoSugeridoTO(LocacaoProdutoSugerido locacaoProdutoSugerido) {
        this.codigo = locacaoProdutoSugerido.getCodigo();
        this.valor = locacaoProdutoSugerido.getValor();
        this.obrigatorio = locacaoProdutoSugerido.getObrigatorio();
        this.produto = new ProdutoTO();
        this.produto.setCodigo(locacaoProdutoSugerido.getCodigoProduto());
    }

    public LocacaoProdutoSugeridoTO(AgendamentoLocacaoProduto agendamentoLocacaoProduto) {
        this.codigo = agendamentoLocacaoProduto.getCodigo();
        this.valor = agendamentoLocacaoProduto.getValorUnitario();
        this.obrigatorio = agendamentoLocacaoProduto.getObrigatorio();
        this.produto = new ProdutoTO();
        this.produto.setCodigo(agendamentoLocacaoProduto.getProduto());
    }

    public String getNomeProduto(){
        return produto.getDescricao();
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public ProdutoTO getProduto() {
        return produto;
    }

    public void setProduto(ProdutoTO produto) {
        this.produto = produto;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public Boolean getObrigatorio() {
        return obrigatorio;
    }

    public void setObrigatorio(Boolean obrigatorio) {
        this.obrigatorio = obrigatorio;
    }

    public QuantidadeDTO getQuantidade() {
        return quantidade;
    }

    public void setQuantidade(QuantidadeDTO quantidade) {
        this.quantidade = quantidade;
    }
}
