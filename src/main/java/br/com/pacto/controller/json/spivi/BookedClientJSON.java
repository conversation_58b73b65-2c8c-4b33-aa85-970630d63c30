package br.com.pacto.controller.json.spivi;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import flex.messaging.io.ArrayList;

import java.util.List;

public class BookedClientJSON {
    private String UserName;
    private String ClientName;
    private Integer ClientID;
    private Integer SeatID;


    public BookedClientJSON(JSONObject json) throws JSONException {
        UserName = json.getString("UserName");
        ClientName = json.getString("ClientName");
        ClientID = json.getInt("ClientID");
        SeatID = json.getInt("SeatID");
    }

    public static List<BookedClientJSON> mapper(JSONArray boockedClients) throws JSONException {
        List<BookedClientJSON> boockedClientsJSON = new ArrayList();

        for (int i = 0; i < boockedClients.length(); i++) {
            boockedClientsJSON.add( new BookedClientJSON( (JSONObject) boockedClients.get(i)) );
        }

        return boockedClientsJSON;
    }

    public String getUserName() {
        return UserName;
    }

    public void setUserName(final String userName) {
        UserName = userName;
    }

    public String getClientName() {
        return ClientName;
    }

    public void setClientName(final String clientName) {
        ClientName = clientName;
    }

    public Integer getClientID() {
        return ClientID;
    }

    public void setClientID(final Integer clientID) {
        ClientID = clientID;
    }

    public Integer getSeatID() {
        return SeatID;
    }

    public void setSeatID(final Integer seatID) {
        SeatID = seatID;
    }
}
