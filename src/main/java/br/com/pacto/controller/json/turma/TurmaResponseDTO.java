package br.com.pacto.controller.json.turma;

import br.com.pacto.controller.json.empresa.EmpresaDTO;
import br.com.pacto.controller.json.modalidade.ModalidadeTO;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class TurmaResponseDTO {

    private Integer id;
    private String nome;
    private String cor;
    private String identificador;
    private ModalidadeTO modalidade;
    private EmpresaDTO empresa;
    private Integer idadeMinima;
    private Integer idadeMinimaMeses;
    private Integer idadeMaxima;
    private Integer idadeMaximaMeses;
    private boolean integracaoSpiv;
    private String dataInicial;
    private String dataFinal;
    private Integer minutosAntecedenciaDesmarcarAula;
    private Integer minutosAposInicioApp;
    private Integer tipoAntecedenciaMarcarAula;
    private Integer minutosAntecedenciaMarcarAula;
    private Integer qtdeNivelOcupacao;
    private Double percDescOcupacaoNivel1;
    private Double percDescOcupacaoNivel2;
    private Double percDescOcupacaoNivel3;
    private Double percDescOcupacaoNivel4;
    private Double percDescOcupacaoNivel5;
    private boolean bloquearMatriculasAcimaLimite;
    private boolean permitirDesmarcarReposicoes;
    private boolean permiteAlunoOutraEmpresa;
    private boolean monitorada;
    private boolean bloquearReposicaoAcimaLimite;
    private boolean permitirAulaExperimental;
    private boolean bloquearLotacaoFutura;
    private String urlVideoYoutube;
    private List<TurmaVideoDTO> linkVideos;

    public TurmaResponseDTO() {
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getCor() {
        return cor;
    }

    public void setCor(String cor) {
        this.cor = cor;
    }

    public String getIdentificador() {
        return identificador;
    }

    public void setIdentificador(String identificador) {
        this.identificador = identificador;
    }

    public ModalidadeTO getModalidade() {
        return modalidade;
    }

    public void setModalidade(ModalidadeTO modalidade) {
        this.modalidade = modalidade;
    }

    public EmpresaDTO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaDTO empresa) {
        this.empresa = empresa;
    }

    public Integer getIdadeMinima() {
        return idadeMinima;
    }

    public void setIdadeMinima(Integer idadeMinima) {
        this.idadeMinima = idadeMinima;
    }

    public Integer getIdadeMaxima() {
        return idadeMaxima;
    }

    public void setIdadeMaxima(Integer idadeMaxima) {
        this.idadeMaxima = idadeMaxima;
    }

    public boolean isIntegracaoSpiv() {
        return integracaoSpiv;
    }

    public void setIntegracaoSpiv(boolean integracaoSpiv) {
        this.integracaoSpiv = integracaoSpiv;
    }

    public String getDataInicial() {
        return dataInicial;
    }

    public void setDataInicial(String dataInicial) {
        this.dataInicial = dataInicial;
    }

    public String getDataFinal() {
        return dataFinal;
    }

    public void setDataFinal(String dataFinal) {
        this.dataFinal = dataFinal;
    }

    public Integer getIdadeMinimaMeses() {
        return idadeMinimaMeses;
    }

    public void setIdadeMinimaMeses(Integer idadeMinimaMeses) {
        this.idadeMinimaMeses = idadeMinimaMeses;
    }

    public Integer getIdadeMaximaMeses() {
        return idadeMaximaMeses;
    }

    public void setIdadeMaximaMeses(Integer idadeMaximaMeses) {
        this.idadeMaximaMeses = idadeMaximaMeses;
    }

    public Integer getMinutosAntecedenciaDesmarcarAula() {
        return minutosAntecedenciaDesmarcarAula;
    }

    public void setMinutosAntecedenciaDesmarcarAula(Integer minutosAntecedenciaDesmarcarAula) {
        this.minutosAntecedenciaDesmarcarAula = minutosAntecedenciaDesmarcarAula;
    }

    public Integer getMinutosAposInicioApp() {
        return minutosAposInicioApp;
    }

    public void setMinutosAposInicioApp(Integer minutosAposInicioApp) {
        this.minutosAposInicioApp = minutosAposInicioApp;
    }

    public Integer getTipoAntecedenciaMarcarAula() {
        return tipoAntecedenciaMarcarAula;
    }

    public void setTipoAntecedenciaMarcarAula(Integer tipoAntecedenciaMarcarAula) {
        this.tipoAntecedenciaMarcarAula = tipoAntecedenciaMarcarAula;
    }

    public Integer getMinutosAntecedenciaMarcarAula() {
        return minutosAntecedenciaMarcarAula;
    }

    public void setMinutosAntecedenciaMarcarAula(Integer minutosAntecedenciaMarcarAula) {
        this.minutosAntecedenciaMarcarAula = minutosAntecedenciaMarcarAula;
    }

    public Integer getQtdeNivelOcupacao() {
        return qtdeNivelOcupacao;
    }

    public void setQtdeNivelOcupacao(Integer qtdeNivelOcupacao) {
        this.qtdeNivelOcupacao = qtdeNivelOcupacao;
    }

    public Double getPercDescOcupacaoNivel1() {
        return percDescOcupacaoNivel1;
    }

    public void setPercDescOcupacaoNivel1(Double percDescOcupacaoNivel1) {
        this.percDescOcupacaoNivel1 = percDescOcupacaoNivel1;
    }

    public Double getPercDescOcupacaoNivel2() {
        return percDescOcupacaoNivel2;
    }

    public void setPercDescOcupacaoNivel2(Double percDescOcupacaoNivel2) {
        this.percDescOcupacaoNivel2 = percDescOcupacaoNivel2;
    }

    public Double getPercDescOcupacaoNivel3() {
        return percDescOcupacaoNivel3;
    }

    public void setPercDescOcupacaoNivel3(Double percDescOcupacaoNivel3) {
        this.percDescOcupacaoNivel3 = percDescOcupacaoNivel3;
    }

    public Double getPercDescOcupacaoNivel4() {
        return percDescOcupacaoNivel4;
    }

    public void setPercDescOcupacaoNivel4(Double percDescOcupacaoNivel4) {
        this.percDescOcupacaoNivel4 = percDescOcupacaoNivel4;
    }

    public Double getPercDescOcupacaoNivel5() {
        return percDescOcupacaoNivel5;
    }

    public void setPercDescOcupacaoNivel5(Double percDescOcupacaoNivel5) {
        this.percDescOcupacaoNivel5 = percDescOcupacaoNivel5;
    }

    public boolean isBloquearMatriculasAcimaLimite() {
        return bloquearMatriculasAcimaLimite;
    }

    public boolean getBloquearMatriculasAcimaLimite() {
        return bloquearMatriculasAcimaLimite;
    }

    public void setBloquearMatriculasAcimaLimite(boolean bloquearMatriculasAcimaLimite) {
        this.bloquearMatriculasAcimaLimite = bloquearMatriculasAcimaLimite;
    }

    public boolean isPermitirDesmarcarReposicoes() {
        return permitirDesmarcarReposicoes;
    }

    public boolean getPermitirDesmarcarReposicoes() {
        return permitirDesmarcarReposicoes;
    }

    public void setPermitirDesmarcarReposicoes(boolean permitirDesmarcarReposicoes) {
        this.permitirDesmarcarReposicoes = permitirDesmarcarReposicoes;
    }

    public boolean isPermiteAlunoOutraEmpresa() {
        return permiteAlunoOutraEmpresa;
    }

    public boolean getPermiteAlunoOutraEmpresa() {
        return permiteAlunoOutraEmpresa;
    }

    public void setPermiteAlunoOutraEmpresa(boolean permiteAlunoOutraEmpresa) {
        this.permiteAlunoOutraEmpresa = permiteAlunoOutraEmpresa;
    }

    public boolean isMonitorada() {
        return monitorada;
    }

    public boolean getMonitorada() {
        return monitorada;
    }

    public void setMonitorada(boolean monitorada) {
        this.monitorada = monitorada;
    }

    public boolean isBloquearReposicaoAcimaLimite() {
        return bloquearReposicaoAcimaLimite;
    }

    public boolean getBloquearReposicaoAcimaLimite() {
        return bloquearReposicaoAcimaLimite;
    }

    public void setBloquearReposicaoAcimaLimite(boolean bloquearReposicaoAcimaLimite) {
        this.bloquearReposicaoAcimaLimite = bloquearReposicaoAcimaLimite;
    }

    public boolean isPermitirAulaExperimental() {
        return permitirAulaExperimental;
    }

    public boolean getPermitirAulaExperimental() {
        return permitirAulaExperimental;
    }

    public void setPermitirAulaExperimental(boolean permitirAulaExperimental) {
        this.permitirAulaExperimental = permitirAulaExperimental;
    }

    public boolean isBloquearLotacaoFutura() {
        return bloquearLotacaoFutura;
    }

    public boolean getBloquearLotacaoFutura() {
        return bloquearLotacaoFutura;
    }

    public void setBloquearLotacaoFutura(boolean bloquearLotacaoFutura) {
        this.bloquearLotacaoFutura = bloquearLotacaoFutura;
    }

    public String getUrlVideoYoutube() {
        return urlVideoYoutube;
    }

    public void setUrlVideoYoutube(String urlVideoYoutube) {
        this.urlVideoYoutube = urlVideoYoutube;
    }

    public List<TurmaVideoDTO> getLinkVideos() {
        return linkVideos;
    }

    public void setLinkVideos(List<TurmaVideoDTO> linkVideos) {
        this.linkVideos = linkVideos;
    }
}
