package br.com.pacto.controller.json.programa;

import br.com.pacto.bean.atividade.*;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.ArrayList;

/**
 * Created by joao moita on 03/10/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AtividadeAparelhoResponseTO {

    private ArrayList<AtividadeNivelResponseTO> niveis;
    private ArrayList<AtividadeMusculoResponseTO> musculos;
    private ArrayList<AtividadeGrupoMuscularResponseTO> gruposMusculares;
    private ArrayList<AtividadeAparelhoResponseRecursiveTO> aparelhos;
    private ArrayList<AtividadeCategoriaAtividadeResponseTO> categoriasAtividade;
    private ArrayList<AtividadeImagemResponseTO> images;
    private String videoUri;
    private ArrayList<AtividadeEmpresaResponseTO> empresas;
    private String descricao;
    private TipoAtividadeEnum tipo;
    private Boolean serieApenasDuracao;
    private Integer id;
    private String nome;
    private boolean ativo;

    public AtividadeAparelhoResponseTO(){

    }

    public AtividadeAparelhoResponseTO(Atividade atividade){
        this.id = atividade.getCodigo();
        this.nome = atividade.getNome();
        this.ativo = atividade.isAtivo();
        this.serieApenasDuracao = atividade.getSelecionado();
        this.tipo = atividade.getTipo();
        this.descricao = atividade.getDescricao();
        this.empresas = new ArrayList<AtividadeEmpresaResponseTO>();
        for(AtividadeEmpresa atividadeEmpresa : atividade.getEmpresasHabilitadas()) {
            empresas.add(new AtividadeEmpresaResponseTO(atividadeEmpresa));
        }
        this.videoUri = atividade.getLinkVideo();
        if(atividade.getTemImagem()) {
            this.images = new ArrayList<AtividadeImagemResponseTO>();
            for(AtividadeAnimacao atividadeAnimacao : atividade.getAnimacoes()) {
                this.images.add(new AtividadeImagemResponseTO(atividadeAnimacao));
            }
        }
        if(atividade.getCategorias().isEmpty() == false){
            this.categoriasAtividade = new ArrayList<AtividadeCategoriaAtividadeResponseTO>();
            for(AtividadeCategoriaAtividade atividadeCategoriaAtividade :
                atividade.getCategorias()) {
                categoriasAtividade.add(new AtividadeCategoriaAtividadeResponseTO(atividadeCategoriaAtividade));
            }
        }
        if(atividade.getAparelhos().isEmpty() == false) {
            this.aparelhos = new ArrayList<AtividadeAparelhoResponseRecursiveTO>();
            for(AtividadeAparelho atividadeAparelho : atividade.getAparelhos()) {
                if (atividadeAparelho.getAparelho() != null) {
                    this.aparelhos.add(new AtividadeAparelhoResponseRecursiveTO(atividadeAparelho));
                }
            }
        }
        if(atividade.getGruposMusculares().isEmpty() == false) {
            this.gruposMusculares = new ArrayList<AtividadeGrupoMuscularResponseTO>();
            for(AtividadeGrupoMuscular atividadeGrupoMuscular : atividade.getGruposMusculares()) {
                this.gruposMusculares.add(new AtividadeGrupoMuscularResponseTO(atividadeGrupoMuscular));
            }
        }
        if(atividade.getMusculos().isEmpty() == false) {
            this.musculos = new ArrayList<AtividadeMusculoResponseTO>();
            for(AtividadeMusculo atividadeMusculo : atividade.getMusculos()) {
                this.musculos.add(new AtividadeMusculoResponseTO(atividadeMusculo));
            }
        }
        if(atividade.getNiveis().isEmpty() == false) {
            this.niveis = new ArrayList<AtividadeNivelResponseTO>();
            for(AtividadeNivel atividadeNivel : atividade.getNiveis()) {
                this.niveis.add(new AtividadeNivelResponseTO(atividadeNivel));
            }
        }
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public ArrayList<AtividadeNivelResponseTO> getNiveis() {
        return niveis;
    }

    public void setNiveis(ArrayList<AtividadeNivelResponseTO> niveis) {
        this.niveis = niveis;
    }

    public ArrayList<AtividadeMusculoResponseTO> getMusculos() {
        return musculos;
    }

    public void setMusculos(ArrayList<AtividadeMusculoResponseTO> musculos) {
        this.musculos = musculos;
    }

    public ArrayList<AtividadeGrupoMuscularResponseTO> getGruposMusculares() {
        return gruposMusculares;
    }

    public void setGruposMusculares(ArrayList<AtividadeGrupoMuscularResponseTO> gruposMusculares) {
        this.gruposMusculares = gruposMusculares;
    }

    public ArrayList<AtividadeAparelhoResponseRecursiveTO> getAparelhos() {
        return aparelhos;
    }

    public void setAparelhos(ArrayList<AtividadeAparelhoResponseRecursiveTO> aparelhos) {
        this.aparelhos = aparelhos;
    }

    public ArrayList<AtividadeCategoriaAtividadeResponseTO> getCategoriasAtividade() {
        return categoriasAtividade;
    }

    public void setCategoriasAtividade(ArrayList<AtividadeCategoriaAtividadeResponseTO> categoriasAtividade) {
        this.categoriasAtividade = categoriasAtividade;
    }

    public ArrayList<AtividadeImagemResponseTO> getImages() {
        return images;
    }

    public void setImages(ArrayList<AtividadeImagemResponseTO> images) {
        this.images = images;
    }

    public String getVideoUri() {
        return videoUri;
    }

    public void setVideoUri(String videoUri) {
        this.videoUri = videoUri;
    }

    public ArrayList<AtividadeEmpresaResponseTO> getEmpresas() {
        return empresas;
    }

    public void setEmpresas(ArrayList<AtividadeEmpresaResponseTO> empresas) {
        this.empresas = empresas;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public TipoAtividadeEnum getTipo() {
        return tipo;
    }

    public void setTipo(TipoAtividadeEnum tipo) {
        this.tipo = tipo;
    }

    public Boolean getSerieApenasDuracao() {
        return serieApenasDuracao;
    }

    public void setSerieApenasDuracao(Boolean serieApenasDuracao) {
        this.serieApenasDuracao = serieApenasDuracao;
    }

    public boolean isAtivo() {
        return ativo;
    }

    public void setAtivo(boolean ativo) {
        this.ativo = ativo;
    }
}
