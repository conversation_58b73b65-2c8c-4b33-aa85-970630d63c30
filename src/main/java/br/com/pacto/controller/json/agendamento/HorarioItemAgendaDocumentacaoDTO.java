package br.com.pacto.controller.json.agendamento;

import br.com.pacto.service.intf.agendatotal.HorarioItemAgendaDTO;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "DTO para representar um item de agenda")
public class HorarioItemAgendaDocumentacaoDTO {
    @ApiModelProperty(value = "Lista de itens de agenda para o dia 10/03/2025", dataType = "List[HorarioItemAgendaDTO]")
    @JsonProperty("20250310")
    private List<HorarioItemAgendaDTO> data20250310;
}
