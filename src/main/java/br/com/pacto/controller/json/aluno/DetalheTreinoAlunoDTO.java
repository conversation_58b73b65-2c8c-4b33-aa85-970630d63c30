package br.com.pacto.controller.json.aluno;

import java.util.List;

/**
 * <AUTHOR> 16/01/2019
 */
public class DetalheTreinoAlunoDTO {

    private String nome;
    private Integer totalTreinoPrevisto;
    private Integer totalTreinoRealizado;
    private Double frequencia;
    private PorcentagemDiaSemana segunda = new PorcentagemDiaSemana();
    private PorcentagemDiaSemana terca = new PorcentagemDiaSemana();
    private PorcentagemDiaSemana quarta = new PorcentagemDiaSemana();
    private PorcentagemDiaSemana quinta = new PorcentagemDiaSemana();
    private PorcentagemDiaSemana sexta = new PorcentagemDiaSemana();
    private PorcentagemDiaSemana sabado = new PorcentagemDiaSemana();
    private PorcentagemDiaSemana domingo = new PorcentagemDiaSemana();
    private FichaExecutada ultimaFichaExecutada;
    private FichaExecutada fichaAtual;
    private FichaExecutada fichaProxima;
    private List<DistribuicaoMusculoDTO> distribuicao;


    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Integer getTotalTreinoPrevisto() {
        return totalTreinoPrevisto;
    }

    public void setTotalTreinoPrevisto(Integer totalTreinoPrevisto) {
        this.totalTreinoPrevisto = totalTreinoPrevisto;
    }

    public Integer getTotalTreinoRealizado() {
        return totalTreinoRealizado;
    }

    public void setTotalTreinoRealizado(Integer totalTreinoRealizado) {
        this.totalTreinoRealizado = totalTreinoRealizado;
    }

    public Double getFrequencia() {
        return frequencia;
    }

    public void setFrequencia(Double frequencia) {
        this.frequencia = frequencia;
    }

    public PorcentagemDiaSemana getSegunda() {
        return segunda;
    }

    public void setSegunda(PorcentagemDiaSemana segunda) {
        this.segunda = segunda;
    }

    public PorcentagemDiaSemana getTerca() {
        return terca;
    }

    public void setTerca(PorcentagemDiaSemana terca) {
        this.terca = terca;
    }

    public PorcentagemDiaSemana getQuarta() {
        return quarta;
    }

    public void setQuarta(PorcentagemDiaSemana quarta) {
        this.quarta = quarta;
    }

    public PorcentagemDiaSemana getQuinta() {
        return quinta;
    }

    public void setQuinta(PorcentagemDiaSemana quinta) {
        this.quinta = quinta;
    }

    public PorcentagemDiaSemana getSexta() {
        return sexta;
    }

    public void setSexta(PorcentagemDiaSemana sexta) {
        this.sexta = sexta;
    }

    public PorcentagemDiaSemana getSabado() {
        return sabado;
    }

    public void setSabado(PorcentagemDiaSemana sabado) {
        this.sabado = sabado;
    }

    public PorcentagemDiaSemana getDomingo() {
        return domingo;
    }

    public void setDomingo(PorcentagemDiaSemana domingo) {
        this.domingo = domingo;
    }

    public FichaExecutada getUltimaFichaExecutada() {
        return ultimaFichaExecutada;
    }

    public void setUltimaFichaExecutada(FichaExecutada ultimaFichaExecutada) {
        this.ultimaFichaExecutada = ultimaFichaExecutada;
    }

    public FichaExecutada getFichaAtual() {
        return fichaAtual;
    }

    public void setFichaAtual(FichaExecutada fichaAtual) {
        this.fichaAtual = fichaAtual;
    }

    public List<DistribuicaoMusculoDTO> getDistribuicao() {
        return distribuicao;
    }

    public void setDistribuicao(List<DistribuicaoMusculoDTO> distribuicao) {
        this.distribuicao = distribuicao;
    }

    public FichaExecutada getFichaProxima() {
        return fichaProxima;
    }

    public void setFichaProxima(FichaExecutada fichaProxima) {
        this.fichaProxima = fichaProxima;
    }
}
