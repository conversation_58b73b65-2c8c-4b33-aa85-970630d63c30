package br.com.pacto.controller.json.objetivos;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.programa.ObjetivoPredefinido;
import br.com.pacto.bean.programa.ObjetivoPredefinidoDTO;
import br.com.pacto.security.dto.UsuarioSimplesDTO;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.exception.ValidacaoException;
import br.com.pacto.service.intf.programa.ObjetivoPredefinidoService;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.util.logging.Level;
import java.util.logging.Logger;

@Controller
@RequestMapping("/psec/avaliacao-objetivos")
public class ObjetivosPredefinidosController {

    private final SessaoService sessaoService;
    private final ObjetivoPredefinidoService objService;

    @Autowired
    public ObjetivosPredefinidosController(ObjetivoPredefinidoService objService, SessaoService sessaoService){
        Assert.notNull(objService, "O serviço de objetivos não foi injetado corretamente");
        Assert.notNull(sessaoService, "O serviço de sessaoService não foi injetado corretamente");
        this.objService = objService;
        this.sessaoService = sessaoService;
    }

    @ResponseBody
    @RequestMapping(value = "", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> consultarAnamneses(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                                  PaginadorDTO paginadorDTO) throws JSONException {
        try {
            FiltroObjetivosJSON filters = new FiltroObjetivosJSON(filtros);
            return ResponseEntityFactory.ok(objService.consultar(filters.getParametro(), paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(ObjetivosPredefinidosController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar os objetivos", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> excluir(@PathVariable("id") final Integer id){
        try {
            UsuarioSimplesDTO usuarioAtual = sessaoService.getUsuarioAtual();
            String chave = usuarioAtual.getChave();
            objService.excluir(chave, objService.obterPorId(chave, id));
            return ResponseEntityFactory.ok(true);
        } catch (ValidacaoException e) {
            return ResponseEntityFactory.erroConhecido(e.getMessage(), e.getMessage());
        } catch (Exception e) {
            Logger.getLogger(ObjetivosPredefinidosController.class.getName()).log(Level.SEVERE, "Erro ao tentar excluir objetivo", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }


    @ResponseBody
    @RequestMapping(value = "/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> consultarAnamnese(@PathVariable("id") final Integer id) {
        try {
            UsuarioSimplesDTO usuarioAtual = sessaoService.getUsuarioAtual();
            String chave = usuarioAtual.getChave();
            return ResponseEntityFactory.ok(new ObjetivoPredefinidoDTO(objService.obterPorId(chave, id)));
        } catch (ServiceException e) {
            Logger.getLogger(ObjetivosPredefinidosController.class.getName()).log(Level.SEVERE, "Erro ao tentar consultar o objetivo", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> incluirAnamneses(@RequestBody ObjetivoPredefinidoDTO obj) {
        try {
            UsuarioSimplesDTO usuarioAtual = sessaoService.getUsuarioAtual();
            String chave = usuarioAtual.getChave();
            ObjetivoPredefinido objetivoPredefinido = new ObjetivoPredefinido();
            objetivoPredefinido.setNome(obj.getNome().trim());

            objetivoPredefinido = objService.inserir(chave, objetivoPredefinido);

            return ResponseEntityFactory.ok(new ObjetivoPredefinidoDTO(objetivoPredefinido));
        } catch (ValidacaoException e) {
            return ResponseEntityFactory.erroConhecido(e.getMessage(), e.getMessage());
        } catch (ServiceException e) {
            Logger.getLogger(ObjetivosPredefinidosController.class.getName()).log(Level.SEVERE, "Erro ao tentar incluir objetivo", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }


    @ResponseBody
    @RequestMapping(value = "/{id}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alterarAnamneses(@PathVariable("id") final Integer id,
                                                                @RequestBody ObjetivoPredefinidoDTO obj) {
        try {
            UsuarioSimplesDTO usuarioAtual = sessaoService.getUsuarioAtual();
            String chave = usuarioAtual.getChave();

            ObjetivoPredefinido objetivoPredefinido = objService.obterPorId(chave, id);
            objetivoPredefinido.setNome(obj.getNome().trim());
            objService.alterar(chave, objetivoPredefinido);

            return ResponseEntityFactory.ok(new ObjetivoPredefinidoDTO(objetivoPredefinido));

        } catch (ServiceException e) {
            Logger.getLogger(ObjetivosPredefinidosController.class.getName()).log(Level.SEVERE, "Erro ao tentar alterar objetivo", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/todos", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> obterTodasAnamneses(){
        try{
            return ResponseEntityFactory.ok(objService.obterTodos());
        }catch (ServiceException e){
            Logger.getLogger(ObjetivosPredefinidosController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter todas anamneses", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }


}
