package br.com.pacto.controller.json.base;

import br.com.pacto.util.UteisValidacao;
import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class ConfiguracoesIaDTO {
    private String permitir_criar_treino_automatizado_ia;
    private String tempo_aprovacao_automatica;
    private String tempo_maximo_revisao;
    private String habilitar_obrigatoriedade_aprovacao_professor;
    private String quantidade_fichas_habilitar_treino_ia;
    private String nivel_iniciante;
    private String nivel_intermediario;
    private String nivel_avancado;
    private String permitir_aluno_criar_treino_ia_app;

    public Boolean getPermitir_criar_treino_automatizado_ia() {
        if (!UteisValidacao.emptyString(permitir_criar_treino_automatizado_ia)) {
            return permitir_criar_treino_automatizado_ia.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setPermitir_criar_treino_automatizado_ia(String permitir_criar_treino_automatizado_ia) {
        this.permitir_criar_treino_automatizado_ia = permitir_criar_treino_automatizado_ia;
    }

    public String getTempo_aprovacao_automatica() {
        return tempo_aprovacao_automatica;
    }

    public void setTempo_aprovacao_automatica(String tempo_aprovacao_automatica) {
        this.tempo_aprovacao_automatica = tempo_aprovacao_automatica;
    }

    public String getTempo_maximo_revisao() {
        return tempo_maximo_revisao;
    }

    public void setTempo_maximo_revisao(String tempo_maximo_revisao) {
        this.tempo_maximo_revisao = tempo_maximo_revisao;
    }

    public Boolean getHabilitar_obrigatoriedade_aprovacao_professor() {
        if (!UteisValidacao.emptyString(habilitar_obrigatoriedade_aprovacao_professor)) {
            return habilitar_obrigatoriedade_aprovacao_professor.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setHabilitar_obrigatoriedade_aprovacao_professor(String habilitar_obrigatoriedade_aprovacao_professor) {
        this.habilitar_obrigatoriedade_aprovacao_professor = habilitar_obrigatoriedade_aprovacao_professor;
    }

    public Boolean getQuantidade_fichas_habilitar_treino_ia() {
        if (!UteisValidacao.emptyString(quantidade_fichas_habilitar_treino_ia)) {
            return quantidade_fichas_habilitar_treino_ia.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setQuantidade_fichas_habilitar_treino_ia(String quantidade_fichas_habilitar_treino_ia) {
        this.quantidade_fichas_habilitar_treino_ia = quantidade_fichas_habilitar_treino_ia;
    }

    public String getNivel_iniciante() {
        return nivel_iniciante;
    }

    public void setNivel_iniciante(String nivel_iniciante) {
        this.nivel_iniciante = nivel_iniciante;
    }

    public String getNivel_intermediario() {
        return nivel_intermediario;
    }

    public void setNivel_intermediario(String nivel_intermediario) {
        this.nivel_intermediario = nivel_intermediario;
    }

    public String getNivel_avancado() {
        return nivel_avancado;
    }

    public void setNivel_avancado(String nivel_avancado) {
        this.nivel_avancado = nivel_avancado;
    }

    public Boolean getPermitir_aluno_criar_treino_ia_app() {
        if (!UteisValidacao.emptyString(permitir_aluno_criar_treino_ia_app)) {
            return permitir_aluno_criar_treino_ia_app.trim().equals("true");
        }
        return false;
    }

    public void setPermitir_aluno_criar_treino_ia_app(String permitir_aluno_criar_treino_ia_app) {
        this.permitir_aluno_criar_treino_ia_app = permitir_aluno_criar_treino_ia_app;
    }
}
