package br.com.pacto.controller.json.ambiente;

import br.com.pacto.bean.locacao.AmbienteHorarioLocacao;
import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class AmbienteHorarioLocacaoTO {

    private Integer codigo;
    private AmbienteTO ambiente;

    public AmbienteHorarioLocacaoTO() {}

    public AmbienteHorarioLocacaoTO(AmbienteHorarioLocacao ambiente) {
        this.codigo = ambiente.getCodigo();
        this.ambiente = new AmbienteTO(ambiente.getAmbiente());
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public AmbienteTO getAmbiente() {
        return ambiente;
    }

    public void setAmbiente(AmbienteTO ambiente) {
        this.ambiente = ambiente;
    }
}
