/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.controller.json.programa.read;

import br.com.pacto.controller.json.ficha.read.FichaVersaoJSON;
import br.com.pacto.controller.json.base.SuperJSON;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class ProgramaVersaoJSON extends SuperJSON {

    private Integer codPrograma;
    private Integer versao;
    private List<FichaVersaoJSON> fichas = new ArrayList<FichaVersaoJSON>();

    public ProgramaVersaoJSON() {
    }

    public ProgramaVersaoJSON(Integer codPrograma, Integer versao) {
        this.codPrograma = codPrograma;
        this.versao = versao;
    }

    public Integer getCodPrograma() {
        return codPrograma;
    }

    public void setCodPrograma(Integer codPrograma) {
        this.codPrograma = codPrograma;
    }

    public Integer getVersao() {
        return versao;
    }

    public void setVersao(Integer versao) {
        this.versao = versao;
    }

    public List<FichaVersaoJSON> getFichas() {
        return fichas;
    }

    public void setFichas(List<FichaVersaoJSON> fichas) {
        this.fichas = fichas;
    }
}
