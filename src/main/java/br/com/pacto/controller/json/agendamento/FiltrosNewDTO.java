package br.com.pacto.controller.json.agendamento;

import br.com.pacto.controller.json.base.SuperJSON;
import br.com.pacto.controller.json.disponibilidade.ItemValidacaoDisponibilidadeDTO;
import br.com.pacto.controller.json.tipoEvento.TipoAgendamentoDTO;
import br.com.pacto.controller.json.tipoEvento.VigenciaDTO;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.List;
import java.util.Map;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class FiltrosNewDTO extends SuperJSON {

    private List<VigenciaDTO> vigencia;
    private List<TipoAgendamentoDTO> comportamentoFc;
    private Map<Object, Object> tipoValidacaoFc;
    private List<ItemValidacaoDisponibilidadeDTO> itemValidacaoFc;
    private Boolean tipoHorarioPreDefinidoFc;
    private Boolean tipoHorarioLivreFc;
    private Boolean tipoHorarioIntervaloTempoFc;
    private Boolean tipoHorarioPlayFc;
    private Boolean segFc;
    private Boolean terFc;
    private Boolean quaFc;
    private Boolean quiFc;
    private Boolean sexFc;
    private Boolean sabFc;
    private Boolean domFc;

    public List<TipoAgendamentoDTO> getComportamentoFc() {
        return comportamentoFc;
    }

    public void setComportamentoFc(List<TipoAgendamentoDTO> comportamentoFc) {
        this.comportamentoFc = comportamentoFc;
    }

    public Map<Object, Object> getTipoValidacaoFc() {
        return tipoValidacaoFc;
    }

    public void setTipoValidacaoFc(Map<Object, Object> tipoValidacaoFc) {
        this.tipoValidacaoFc = tipoValidacaoFc;
    }

    public List<ItemValidacaoDisponibilidadeDTO> getItemValidacaoFc() {
        return itemValidacaoFc;
    }

    public void setItemValidacaoFc(List<ItemValidacaoDisponibilidadeDTO> itemValidacaoFc) {
        this.itemValidacaoFc = itemValidacaoFc;
    }

    public Boolean getTipoHorarioPreDefinidoFc() {
        return tipoHorarioPreDefinidoFc;
    }

    public void setTipoHorarioPreDefinidoFc(Boolean tipoHorarioPreDefinidoFc) {
        this.tipoHorarioPreDefinidoFc = tipoHorarioPreDefinidoFc;
    }

    public Boolean getTipoHorarioLivreFc() {
        return tipoHorarioLivreFc;
    }

    public void setTipoHorarioLivreFc(Boolean tipoHorarioLivreFc) {
        this.tipoHorarioLivreFc = tipoHorarioLivreFc;
    }

    public Boolean getTipoHorarioIntervaloTempoFc() {
        return tipoHorarioIntervaloTempoFc;
    }

    public void setTipoHorarioIntervaloTempoFc(Boolean tipoHorarioIntervaloTempoFc) {
        this.tipoHorarioIntervaloTempoFc = tipoHorarioIntervaloTempoFc;
    }

    public Boolean getSegFc() {
        return segFc;
    }

    public void setSegFc(Boolean segFc) {
        this.segFc = segFc;
    }

    public Boolean getTerFc() {
        return terFc;
    }

    public void setTerFc(Boolean terFc) {
        this.terFc = terFc;
    }

    public Boolean getQuaFc() {
        return quaFc;
    }

    public void setQuaFc(Boolean quaFc) {
        this.quaFc = quaFc;
    }

    public Boolean getQuiFc() {
        return quiFc;
    }

    public void setQuiFc(Boolean quiFc) {
        this.quiFc = quiFc;
    }

    public Boolean getSexFc() {
        return sexFc;
    }

    public void setSexFc(Boolean sexFc) {
        this.sexFc = sexFc;
    }

    public Boolean getSabFc() {
        return sabFc;
    }

    public void setSabFc(Boolean sabFc) {
        this.sabFc = sabFc;
    }

    public Boolean getDomFc() {
        return domFc;
    }

    public void setDomFc(Boolean domFc) {
        this.domFc = domFc;
    }

    public List<VigenciaDTO> getVigencia() {
        return vigencia;
    }

    public void setVigencia(List<VigenciaDTO> vigencia) {
        this.vigencia = vigencia;
    }

    public Boolean getTipoHorarioPlayFc() {
        return tipoHorarioPlayFc;
    }

    public void setTipoHorarioPlayFc(Boolean tipoHorarioPlayFc) {
        this.tipoHorarioPlayFc = tipoHorarioPlayFc;
    }
}
