package br.com.pacto.controller.json.spivi;

import br.com.pacto.controller.json.base.SuperJSON;
import br.com.pacto.objeto.Uteis;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import flex.messaging.io.ArrayList;

import java.util.List;

public class SeatJSON {
    private Integer seatID;
    private Integer teamNumber;
    private String UserName;
    private String ClientName;
    private Integer ClientID;
    private boolean available = true;

    public SeatJSON(JSONObject json) throws JSONException {
        seatID = json.getInt("SeatID");
        teamNumber = json.getInt("TeamNumber");
    }

    public SeatJSON(final Integer seatID, final String userName, final String clientName, final Integer clientID) {
        this.seatID = seatID;
        UserName = userName;
        ClientName = clientName;
        ClientID = clientID;
        available = ClientID == null ? true : false;
    }

    public static List<SeatJSON> mapper(JSONArray seats) throws JSONException {
        List<SeatJSON> seatJSON = new ArrayList();

        for (int i = 0; i < seats.length(); i++) {
            seatJSON.add( new SeatJSON( (JSONObject) seats.get(i)) );
        }

        return seatJSON;
    }

    public Integer getSeatID() {
        return seatID;
    }

    public void setSeatID(final Integer seatID) {
        this.seatID = seatID;
    }

    public Integer getTeamNumber() {
        return teamNumber;
    }

    public void setTeamNumber(final Integer teamNumber) {
        this.teamNumber = teamNumber;
    }

    public String getUserName() {
        return UserName;
    }

    public void setUserName(final String userName) {
        UserName = userName;
    }

    public String getClientName() {
        return ClientName;
    }

    public String getClientFirstName(){
        if(ClientName == null || ClientName.length() == 0){
            return "";
        }

        return Uteis.obterPrimeiroNomeConcatenadoSobreNome(ClientName, true);
    }

    public void setClientName(final String clientName) {
        ClientName = clientName;
    }

    public Integer getClientID() {
        return ClientID;
    }

    public void setClientID(final Integer clientID) {
        ClientID = clientID;
    }

    public boolean isAvailable() {
        return available;
    }

    public void setAvailable(final boolean available) {
        this.available = available;
    }
}
