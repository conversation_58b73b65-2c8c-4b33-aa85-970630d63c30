package br.com.pacto.controller.json.benchmark;

import br.com.pacto.bean.benchmark.Benchmark;
import br.com.pacto.bean.benchmark.TipoBenchmarkTO;
import br.com.pacto.enumerador.crossfit.TipoWodEnum;
import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * Created by joao moita on 27/09/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BenchmarkResponseTO {

    private TipoBenchmarkTO tipoBenchmark;
    private Integer id;
    private Boolean preDefinido;
    private String nome;
    private TipoWodEnum tipoExercicio;
    private String exercicios;
    private String observacao;
    private String imagemUri;
    private String videoUri;

    public BenchmarkResponseTO(Benchmark benchmark) {
        this.tipoBenchmark = new TipoBenchmarkTO(benchmark.getTipoBenchmark());
        this.preDefinido = benchmark.getPreDefinido();
        this.id = benchmark.getCodigo();
        this.nome = benchmark.getNome();
        this.tipoExercicio = benchmark.getTipoWod();
        this.exercicios = benchmark.getDescricaoExercicios();
        this.observacao = benchmark.getObservacao();
        if(benchmark.isMidiaImagem()) {
            this.imagemUri = benchmark.getUrlMidia();
        } else {
            this.videoUri = benchmark.getUrlMidia();
        }
    }

    public TipoBenchmarkTO getTipoBenchmark() {
        return tipoBenchmark;
    }

    public void setTipoBenchmark(TipoBenchmarkTO tipoBenchmark) {
        this.tipoBenchmark = tipoBenchmark;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Boolean getPreDefinido() {
        return preDefinido;
    }

    public void setPreDefinido(Boolean preDefinido) {
        this.preDefinido = preDefinido;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public TipoWodEnum getTipoExercicio() {
        return tipoExercicio;
    }

    public void setTipoExercicio(TipoWodEnum tipoExercicio) {
        this.tipoExercicio = tipoExercicio;
    }

    public String getExercicios() {
        return exercicios;
    }

    public void setExercicios(String exercicios) {
        this.exercicios = exercicios;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public String getImagemUri() {
        return imagemUri;
    }

    public void setImagemUri(String imagemUri) {
        this.imagemUri = imagemUri;
    }

    public String getVideoUri() {
        return videoUri;
    }

    public void setVideoUri(String videoUri) {
        this.videoUri = videoUri;
    }
}
