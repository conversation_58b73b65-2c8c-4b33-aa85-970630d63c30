package br.com.pacto.controller.json.gestao;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.controller.json.SuperController;
import br.com.pacto.controller.json.wod.WodController;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.security.aspecto.Permissao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.crossfit.BICrossfitService;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.logging.Level;
import java.util.logging.Logger;

@Controller
@RequestMapping("/psec/crossfit-bi")
public class BICrossfitController{


    private final BICrossfitService biService;

    @Autowired
    public BICrossfitController(BICrossfitService bi){
        Assert.notNull(bi, "O serviço de wod não foi injetado corretamente");
        this.biService = bi;
    }


    @ResponseBody
    @Permissao(recursos = RecursoEnum.WOD)
    @RequestMapping(value = "/dados",params = {"idProfessor","mes","ano"}, method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> dados(
            @RequestHeader(value = "empresaId", required = false) Integer empresaId,
            @RequestParam("idProfessor") Integer idProfessor,
            @RequestParam("mes") Integer mes,
            @RequestParam("ano") Integer ano) throws Exception {
        try {
            return ResponseEntityFactory.ok(biService.gerarBI(mes, ano, idProfessor, empresaId));
        } catch (ServiceException e) {
            Logger.getLogger(WodController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter BU", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.WOD)
    @RequestMapping(value = "/alunos-resultados", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> obterAlunosPorResultados(
            @RequestParam(value = "filters", required = false) JSONObject filters,
            PaginadorDTO paginadorDTO
    ) throws JSONException {
        FiltrosBiCrossfitJSON filtrosBiCrossfitJSON = new FiltrosBiCrossfitJSON(filters);
        try {
            return ResponseEntityFactory.ok(biService.alunosComResultadosLancados(filtrosBiCrossfitJSON, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(WodController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter alunos com resultados", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.WOD)
    @RequestMapping(value = "/alunos-agendamentos", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> obterAlunosPorAgendamentosAula (
            @RequestHeader(value = "empresaId", required = false) Integer empresaId,
            @RequestParam(value = "filters", required = false) JSONObject filters,
            PaginadorDTO paginadorDTO
    ) throws JSONException {
        FiltrosBiCrossfitJSON filtrosBiCrossfitJSON = new FiltrosBiCrossfitJSON(filters);
        try {
            return ResponseEntityFactory.ok(biService.alunosPorAgendamentos(filtrosBiCrossfitJSON, empresaId, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(WodController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter alunos por agendamentos de aula", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.WOD)
    @RequestMapping(value = "/alunos-frequentam-aula/professor", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> obterAlunosFrequentamAulaProfessor (
            @RequestHeader(value = "empresaId", required = false) Integer empresaId,
            @RequestParam(value = "filters", required = false) JSONObject filters,
            PaginadorDTO paginadorDTO) throws JSONException {
        FiltrosBiCrossfitJSON filtrosBiCrossfitJSON = new FiltrosBiCrossfitJSON(filters);
        try {
            return ResponseEntityFactory.ok(biService.alunosFrequentaAulaProfessor(empresaId, filtrosBiCrossfitJSON, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(WodController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter alunos que frequentaram as aulas do professor", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.WOD)
    @RequestMapping(value = "/ranking", params = {"idProfessor","mes","ano"}, method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> rankingGeral(
            @RequestHeader(value = "empresaId", required = false) Integer empresaId,
            @RequestParam("idProfessor") Integer idProfessor,
            @RequestParam("mes") Integer mes,
            @RequestParam("ano") Integer ano,
            HttpServletRequest request) {
        try {
            try {
                Date dia = Calendario.getInstance(ano, mes, 1).getTime();
                Date ultimaHora = Uteis.obterUltimoDiaMesUltimaHora(dia);
                return ResponseEntityFactory.ok(biService.rankingGeral(dia, ultimaHora, request, empresaId));
            }catch (Exception e){
                throw new ServiceException(e);
            }

        } catch (ServiceException e) {
            Logger.getLogger(WodController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter BU", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }
}
