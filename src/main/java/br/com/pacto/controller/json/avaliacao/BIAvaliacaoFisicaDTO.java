package br.com.pacto.controller.json.avaliacao;

import br.com.pacto.service.impl.avaliacao.BIAvaliacaoFisicaTO;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.Collection;

/**
 * <AUTHOR> Si<PERSON>ira 16/01/2019
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BIAvaliacaoFisicaDTO {

    private Integer avaliacoes;
    private Integer novas ;
    private Integer reavaliacoes ;
    private Integer previstas ;
    private Integer realizadas ;
    private Integer atrasadas ;
    private Integer futuras ;
    private Integer semAvaliacao ;
    private Integer ativosAtrasada;
    private Integer perderamPeso ;
    private Integer ganharamMassaMagra ;
    private Integer perderamGordura ;
    private Integer alunosParq ;
    private Collection<GraficoDTO> grafico;

    public BIAvaliacaoFisicaDTO(final BIAvaliacaoFisicaTO obj, final Collection<GraficoDTO> grafico) {
        this.avaliacoes = obj.getAvaliacoes();
        this.novas = obj.getNovas();
        this.reavaliacoes = obj.getReavaliacoes();
        this.previstas = obj.getPrevistas();
        this.realizadas = obj.getRealizadas();
        this.atrasadas = obj.getAtrasadas();
        this.futuras = obj.getFuturas();
        this.semAvaliacao = obj.getSemAvaliacao();
        this.ativosAtrasada = obj.getAtivosAtrasada();
        this.perderamPeso = obj.getPerderamPeso();
        this.ganharamMassaMagra = obj.getGanharamMassaMagra();
        this.perderamGordura = obj.getPerderamGordura();
        this.alunosParq = obj.getAlunosParq();
        this.grafico = grafico;
    }

    public Integer getAvaliacoes() {
        return avaliacoes;
    }

    public void setAvaliacoes(Integer avaliacoes) {
        this.avaliacoes = avaliacoes;
    }

    public Integer getNovas() {
        return novas;
    }

    public void setNovas(Integer novas) {
        this.novas = novas;
    }

    public Integer getReavaliacoes() {
        return reavaliacoes;
    }

    public void setReavaliacoes(Integer reavaliacoes) {
        this.reavaliacoes = reavaliacoes;
    }

    public Integer getPrevistas() {
        return previstas;
    }

    public void setPrevistas(Integer previstas) {
        this.previstas = previstas;
    }

    public Integer getRealizadas() {
        return realizadas;
    }

    public void setRealizadas(Integer realizadas) {
        this.realizadas = realizadas;
    }

    public Integer getAtrasadas() {
        return atrasadas;
    }

    public void setAtrasadas(Integer atrasadas) {
        this.atrasadas = atrasadas;
    }

    public Integer getFuturas() {
        return futuras;
    }

    public void setFuturas(Integer futuras) {
        this.futuras = futuras;
    }

    public Integer getSemAvaliacao() {
        return semAvaliacao;
    }

    public void setSemAvaliacao(Integer semAvaliacao) {
        this.semAvaliacao = semAvaliacao;
    }

    public Integer getAtivosAtrasada() {
        return ativosAtrasada;
    }

    public void setAtivosAtrasada(Integer ativosAtrasada) {
        this.ativosAtrasada = ativosAtrasada;
    }

    public Integer getPerderamPeso() {
        return perderamPeso;
    }

    public void setPerderamPeso(Integer perderamPeso) {
        this.perderamPeso = perderamPeso;
    }

    public Integer getGanharamMassaMagra() {
        return ganharamMassaMagra;
    }

    public void setGanharamMassaMagra(Integer ganharamMassaMagra) {
        this.ganharamMassaMagra = ganharamMassaMagra;
    }

    public Integer getPerderamGordura() {
        return perderamGordura;
    }

    public void setPerderamGordura(Integer perderamGordura) {
        this.perderamGordura = perderamGordura;
    }

    public Integer getAlunosParq() {
        return alunosParq;
    }

    public void setAlunosParq(Integer alunosParq) {
        this.alunosParq = alunosParq;
    }

    public Collection<GraficoDTO> getGrafico() {
        return grafico;
    }

    public void setGrafico(Collection<GraficoDTO> grafico) {
        this.grafico = grafico;
    }

    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class GraficoDTO {

        private String nome;
        private Integer valor;

        public GraficoDTO(String nome, Integer valor) {
            this.nome = nome;
            this.valor = valor;
        }

        public String getNome() {
            return nome;
        }

        public void setNome(String nome) {
            this.nome = nome;
        }

        public Integer getValor() {
            return valor;
        }

        public void setValor(Integer valor) {
            this.valor = valor;
        }
    }
}
