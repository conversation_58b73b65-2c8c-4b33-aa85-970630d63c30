package br.com.pacto.controller.json.crossfit;

import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.bean.wod.ScoreTreino;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.util.UteisValidacao;

import static java.util.Objects.isNull;

/**
 * Created by <PERSON><PERSON> on 16/02/2017.
 */
public class RankingJSON {

    private Integer posicao;
    private String nome;
    private String matricula;
    private String foto;
    private boolean rx;
    private Integer codigoWOD;
    private String tipoWOD;
    private String campos;
    private Integer tempo;
    private Double peso;
    private Integer repeticoes;
    private Integer rounds;
    private Integer nivel;
    private String nivelDescricao;
    private String nivelSigla;
    private String sexo;
    private Integer codUsuario;
    private Boolean visitante;

    public RankingJSON(ScoreTreino scoreTreino) {
        this.posicao = scoreTreino.getPosicao();
        if(scoreTreino.getUsuario() == null){
            scoreTreino.setUsuario(new Usuario());
        }
        this.nome = !isNull(scoreTreino.getUsuario().getSuperNome()) ? scoreTreino.getUsuario().getSuperNome() : "";
        if (scoreTreino.getUsuario().getCliente() != null) {
            this.matricula = scoreTreino.getUsuario().getCliente().getMatriculaString();
            this.visitante = scoreTreino.getUsuario().getCliente().getSituacao().equals("VI");
            this.sexo = scoreTreino.getUsuario().getCliente() == null ?
                    "" : scoreTreino.getUsuario().getCliente().getSexo();
        } else {
            this.matricula = "";
        }
        this.codUsuario = scoreTreino.getUsuario().getCodigo();
        this.foto = "";
        this.rx = scoreTreino.getRx();
        this.codigoWOD = scoreTreino.getWod().getCodigo();
        this.tipoWOD = scoreTreino.getWod().getTipoWodTabela().getNome();
        this.campos = scoreTreino.getWod().getTipoWodTabela().getCamposResultado();
        this.tempo = scoreTreino.getTempo();
        this.peso = scoreTreino.getPeso();
        this.repeticoes = scoreTreino.getRepeticoes();
        this.rounds = scoreTreino.getRounds();
        if (scoreTreino.getNivelcrossfit() != null) {
            this.nivel = scoreTreino.getNivelcrossfit().getCodigo();
            this.nivelDescricao = scoreTreino.getNivelcrossfit().getNome();
            this.nivelSigla = scoreTreino.getNivelcrossfit().getNome().substring(0,2).toUpperCase();
        }

    }

    public String getDescricao(){
        StringBuilder d = new StringBuilder();
        try {
            if(campos.contains("tempo")){
                d.append("Tempo: ").append(Uteis.converterSegundosEmMinutos(tempo)).append(" - ");
            }
            if(campos.contains("peso")){
                d.append("Peso: ").append(peso).append(" kg - ");
            }
            if(campos.contains("rounds")){
                d.append("Rounds: ").append(rounds).append(" - ");
            }
            if(campos.contains("repeticoes")){
                d.append("Repetições: ").append(repeticoes).append(" - ");
            }

        }catch (Exception e){
        }
        d.append(nivelDescricao);
        return d.toString();
    }

    public String getDescricaoSigla(){
        StringBuilder d = new StringBuilder();
        try {
            if(campos.contains("tempo")){
                d.append(" - Tempo: ").append(Uteis.converterSegundosEmMinutos(tempo));
            }
            if(campos.contains("peso")){
                d.append(" - Peso: ").append(peso).append(" kg");
            }
            if(campos.contains("rounds")){
                d.append(" - Rounds: ").append(rounds);
            }
            if(campos.contains("repeticoes")){
                d.append(" - Repetições: ").append(repeticoes);
            }

        }catch (Exception e){
        }
        return d.toString().replaceFirst(" - ", "");
    }

    public RankingJSON() {
    }

    public Integer getPosicao() {
        return posicao;
    }

    public void setPosicao(Integer posicao) {
        this.posicao = posicao;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public String getFoto() {
        return foto;
    }

    public void setFoto(String foto) {
        this.foto = foto;
    }

    public boolean isRx() {
        return rx;
    }

    public void setRx(boolean rx) {
        this.rx = rx;
    }

    public Integer getCodigoWOD() {
        return codigoWOD;
    }

    public void setCodigoWOD(Integer codigoWOD) {
        this.codigoWOD = codigoWOD;
    }

    public Integer getTempo() {
        return tempo;
    }

    public void setTempo(Integer tempo) {
        this.tempo = tempo;
    }

    public Double getPeso() {
        return peso;
    }

    public void setPeso(Double peso) {
        this.peso = peso;
    }

    public Integer getRepeticoes() {
        return repeticoes;
    }

    public void setRepeticoes(Integer repeticoes) {
        this.repeticoes = repeticoes;
    }

    public Integer getRounds() {
        return rounds;
    }

    public void setRounds(Integer rounds) {
        this.rounds = rounds;
    }

    public Integer getNivel() {
        if (nivel == null) {
            nivel = 0;
        }
        return nivel;
    }

    public void setNivel(Integer nivel) {
        this.nivel = nivel;
    }

    public String getNivelDescricao() {
        if (nivelDescricao == null) {
            nivelDescricao = "";
        }
        return nivelDescricao;
    }

    public void setNivelDescricao(String nivelDescricao) {
        this.nivelDescricao = nivelDescricao;
    }

    public String getTipoWOD() {
        return tipoWOD;
    }

    public void setTipoWOD(String tipoWOD) {
        this.tipoWOD = tipoWOD;
    }

    public String getCampos() {
        return campos;
    }

    public void setCampos(String campos) {
        this.campos = campos;
    }

    public String getSexo() {
        return sexo;
    }

    public void setSexo(String sexo) {
        this.sexo = sexo;
    }

    public String getPrimeiroNome() {
        if (nome != null) {
            return Uteis.getPrimeiroNome(nome);
        } else {
            return "";
        }
    }

    public String getNivelSigla() {
        return nivelSigla;
    }

    public void setNivelSigla(String nivelSigla) {
        this.nivelSigla = nivelSigla;
    }

    public Integer getCodUsuario() {
        if (codUsuario == null) {
            codUsuario = 0;
        }
        return codUsuario;
    }

    public void setCodUsuario(Integer codUsuario) {
        this.codUsuario = codUsuario;
    }

    public Boolean getvisitante() {
        return visitante;
    }

    public void setSituacaoAluno(Boolean visitante) {
        this.visitante = visitante;
    }
}
