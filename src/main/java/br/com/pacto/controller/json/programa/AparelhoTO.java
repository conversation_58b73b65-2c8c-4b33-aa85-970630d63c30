package br.com.pacto.controller.json.programa;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by joao moita on 03/10/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AparelhoTO {

    private Integer id;
    private String nome;
    private List<Integer> ajusteIds = new ArrayList<>();
    private List<Integer> atividadeIds = new ArrayList<>();
    private List<String> novosAjustes = new ArrayList<>();
    private Boolean crossfit = false;
    private String sigla;
    private String icone;
    private Boolean usarEmReservaEquipamentos = false;
    private String sensorSelfloops;

    public AparelhoTO(){

    }

    public AparelhoTO(Integer id, String nome){
        this.id = id;
        this.nome = nome;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public List<Integer> getAjusteIds() {
        return ajusteIds;
    }

    public void setAjusteIds(List<Integer> ajusteIds) {
        this.ajusteIds = ajusteIds;
    }

    public List<Integer> getAtividadeIds() {
        return atividadeIds;
    }

    public void setAtividadeIds(List<Integer> atividadeIds) {
        this.atividadeIds = atividadeIds;
    }

    public List<String> getNovosAjustes() {
        return novosAjustes;
    }

    public void setNovosAjustes(List<String> novosAjustes) {
        this.novosAjustes = novosAjustes;
    }

    public Boolean getCrossfit() {
        return crossfit;
    }

    public void setCrossfit(Boolean crossfit) {
        this.crossfit = crossfit;
    }

    public String getSigla() {
        return sigla;
    }

    public void setSigla(String sigla) {
        this.sigla = sigla;
    }

    public String getIcone() {
        return icone;
    }

    public void setIcone(String icone) {
        this.icone = icone;
    }

    public Boolean getUsarEmReservaEquipamentos() {
        if (usarEmReservaEquipamentos == null) {
            return false;
        }
        return usarEmReservaEquipamentos;
    }

    public void setUsarEmReservaEquipamentos(Boolean usarEmReservaEquipamentos) {
        this.usarEmReservaEquipamentos = usarEmReservaEquipamentos;
    }

    public String getSensorSelfloops() {
        return sensorSelfloops;
    }

    public void setSensorSelfloops(String sensorSelfloops) {
        this.sensorSelfloops = sensorSelfloops;
    }
}
