package br.com.pacto.controller.json.selfloops;

import java.util.List;

public class CourseScheduleDetailsDTO {

    private String course_name;
    private String course_type;
    private String course_description;
    private String from_date;
    private String to_date;
    private String start_time;
    private String end_time;
    private boolean day_sunday;
    private boolean day_monday;
    private boolean day_tuesday;
    private boolean day_wednesday;
    private boolean day_thursday;
    private boolean day_friday;
    private boolean day_saturday;
    private String room;
    private String instructor;
    private int max_capacity;
    private List<String> sensors;

    public CourseScheduleDetailsDTO() {}

    public String getCourse_name() {
        return course_name;
    }

    public void setCourse_name(String course_name) {
        this.course_name = course_name;
    }

    public String getCourse_type() {
        return course_type;
    }

    public void setCourse_type(String course_type) {
        this.course_type = course_type;
    }

    public String getCourse_description() {
        return course_description;
    }

    public void setCourse_description(String course_description) {
        this.course_description = course_description;
    }

    public String getFrom_date() {
        return from_date;
    }

    public void setFrom_date(String from_date) {
        this.from_date = from_date;
    }

    public String getTo_date() {
        return to_date;
    }

    public void setTo_date(String to_date) {
        this.to_date = to_date;
    }

    public String getStart_time() {
        return start_time;
    }

    public void setStart_time(String start_time) {
        this.start_time = start_time;
    }

    public String getEnd_time() {
        return end_time;
    }

    public void setEnd_time(String end_time) {
        this.end_time = end_time;
    }

    public boolean isDay_sunday() {
        return day_sunday;
    }

    public void setDay_sunday(boolean day_sunday) {
        this.day_sunday = day_sunday;
    }

    public boolean isDay_monday() {
        return day_monday;
    }

    public void setDay_monday(boolean day_monday) {
        this.day_monday = day_monday;
    }

    public boolean isDay_tuesday() {
        return day_tuesday;
    }

    public void setDay_tuesday(boolean day_tuesday) {
        this.day_tuesday = day_tuesday;
    }

    public boolean isDay_wednesday() {
        return day_wednesday;
    }

    public void setDay_wednesday(boolean day_wednesday) {
        this.day_wednesday = day_wednesday;
    }

    public boolean isDay_thursday() {
        return day_thursday;
    }

    public void setDay_thursday(boolean day_thursday) {
        this.day_thursday = day_thursday;
    }

    public boolean isDay_friday() {
        return day_friday;
    }

    public void setDay_friday(boolean day_friday) {
        this.day_friday = day_friday;
    }

    public boolean isDay_saturday() {
        return day_saturday;
    }

    public void setDay_saturday(boolean day_saturday) {
        this.day_saturday = day_saturday;
    }

    public String getRoom() {
        return room;
    }

    public void setRoom(String room) {
        this.room = room;
    }

    public String getInstructor() {
        return instructor;
    }

    public void setInstructor(String instructor) {
        this.instructor = instructor;
    }

    public int getMax_capacity() {
        return max_capacity;
    }

    public void setMax_capacity(int max_capacity) {
        this.max_capacity = max_capacity;
    }

    public List<String> getSensors() {
        return sensors;
    }

    public void setSensors(List<String> sensors) {
        this.sensors = sensors;
    }
}