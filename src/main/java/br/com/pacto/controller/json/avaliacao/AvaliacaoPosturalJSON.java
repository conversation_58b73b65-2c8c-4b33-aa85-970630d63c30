package br.com.pacto.controller.json.avaliacao;

import java.util.List;

/**
 * Created by alcides on 21/09/2017.
 */
public class AvaliacaoPosturalJSON {

    private Integer codigo;
    private String observacao;
    private String direito;
    private String esquerdo;
    private String costas;
    private String frente;
    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public String getDireito() {
        return direito;
    }

    public void setDireito(String direito) {
        this.direito = direito;
    }

    public String getEsquerdo() {
        return esquerdo;
    }

    public void setEsquerdo(String esquerdo) {
        this.esquerdo = esquerdo;
    }

    public String getCostas() {
        return costas;
    }

    public void setCostas(String costas) {
        this.costas = costas;
    }

    public String getFrente() {
        return frente;
    }

    public void setFrente(String frente) {
        this.frente = frente;
    }
}
