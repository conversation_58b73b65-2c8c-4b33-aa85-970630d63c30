package br.com.pacto.controller.json.gestao;

import br.com.pacto.bean.Agendamento;
import br.com.pacto.bean.bi.DashboardBI;
import br.com.pacto.bean.bi.DiasSemanaDashboardBI;
import br.com.pacto.bean.bi.FiltrosDashboard;
import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.bean.professor.ProfessorSintetico;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.base.SuperControle;
import br.com.pacto.controller.json.professor.ProfessorJSON;
import br.com.pacto.controller.json.professor.Professor<PERSON>implesJSON;
import br.com.pacto.controller.json.professor.ProfessorSinteticoJSONControle;
import br.com.pacto.enumerador.agenda.TipoAgendamentoEnum;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.cliente.ClienteSinteticoService;
import br.com.pacto.service.intf.gestao.DashboardBIService;
import br.com.pacto.service.intf.professor.ProfessorSinteticoService;
import br.com.pacto.service.intf.usuario.UsuarioService;
import br.com.pacto.util.UtilContext;
import br.com.pacto.util.impl.Ordenacao;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by Rafael on 15/11/2016.
 */
@Controller
@RequestMapping("/gestao")
public class GestaoJSONControle extends SuperControle {

    @Autowired
    private DashboardBIService dashboardBIService;

    @RequestMapping(value = "{ctx}/ultimoBI", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap obterUltimoBI(@PathVariable String ctx,
                                @RequestParam Integer codigoProfessor,
                                @RequestParam Integer codEmpresa) {
        ModelMap mm = new ModelMap();
        try {
            FiltrosDashboard filtro = new FiltrosDashboard();
            filtro.setEmpresa(codEmpresa);
            DashboardBI obj = dashboardBIService.processarGestao(ctx,codigoProfessor,Calendario.hoje(),filtro,true, false, codEmpresa);
            JSONObject dados = new JSONObject(obj != null ? obj.toJSON() : new GestaoTreinoJSON());
            JSONArray array = new JSONArray();
            array.put(dados);
            mm.addAttribute(RETURN, array.toString());
        } catch (ServiceException ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ProfessorSinteticoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }
    @RequestMapping(value = "{ctx}/obterBIsPeriodo", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap obterBIsPeriodo(@PathVariable String ctx,
                                @RequestParam Integer codigoProfessor,
                                @RequestParam Integer codEmpresa,
                                @RequestParam String mesAnoInicio,
                                @RequestParam String mesAnoFim) {
        ModelMap mm = new ModelMap();
        try {
            List<DashboardBI> dadoDashboardBIs = dashboardBIService.obterBIsPeriodo(ctx,codigoProfessor, codEmpresa, mesAnoInicio,mesAnoFim);
            Ordenacao.ordenarLista(dadoDashboardBIs,"ordem");
            JSONArray dados = new JSONArray();
            for(DashboardBI obj : dadoDashboardBIs){
                dados.put(new JSONObject(obj.toJSON()));
            }
            mm.addAttribute(RETURN, dados.toString());
        } catch (ServiceException ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ProfessorSinteticoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }
    @RequestMapping(value = "{ctx}/obterProfessores", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap obterProfessores(@PathVariable String ctx,
            @RequestParam String username,@RequestParam Integer codEmpresa, HttpServletRequest request) {
        ModelMap mm = new ModelMap();
        try {
            ProfessorSinteticoService ps = (ProfessorSinteticoService) UtilContext.getBean(ProfessorSinteticoService.class);
            UsuarioService us = (UsuarioService) UtilContext.getBean(UsuarioService.class);
            HashMap<String,Object> params = new HashMap<String,Object>();
            params.put("username",username);
            Usuario usuario = us.obterObjetoPorParam(ctx,"Select obj from Usuario obj where obj.userName = :username",params);
            List<ProfessorSintetico> professores = ps.consultarProfessores(ctx, codEmpresa);
            List<ProfessorJSON> professoresItens = new ArrayList<ProfessorJSON>();
            // Adiciona item "Todos"
            Ordenacao.ordenarLista(professores, "nome");
            Boolean BItodosprofessores = usuario.isFuncionalidadeHabilitado(RecursoEnum.VER_BI_OUTROS_PROFESSORES.name());
            for (ProfessorSintetico pss : professores) {
                if(!pss.getCodigo().equals(usuario.getProfessor().getCodigo()) &&
                        BItodosprofessores) {
                    pss.setAvatar(defineUrlFotoJSON(ctx,pss.getCodigoPessoa(), request));
                    professoresItens.add(ProfessorJSON.obterJSON(pss));
                }
            }
            if(!BItodosprofessores){
                professoresItens.add(ProfessorJSON.obterJSON(usuario.getProfessor()));
            }
            mm.addAttribute(RETURN, professoresItens);
        } catch (ServiceException ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ProfessorSinteticoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }


    @RequestMapping(value = "{ctx}/obterTodosProfessores", method = RequestMethod.GET)
    public @ResponseBody
    ModelMap obterTodosProfessores(@PathVariable String ctx, @RequestParam Integer cliente, @RequestParam(required = false) Integer codigoPessoa,
                                   HttpServletRequest request) {
        ModelMap mm = new ModelMap();
        try {
            ProfessorSinteticoService ps = UtilContext.getBean(ProfessorSinteticoService.class);
            ClienteSinteticoService cs = UtilContext.getBean(ClienteSinteticoService.class);
            ClienteSintetico clienteSintetico = cs.obterPorCodigo(ctx,cliente);

            List<ProfessorSintetico> professores = new ArrayList<>();
            ProfessorSintetico professorCarteira = new ProfessorSintetico();

            /*if (null == clienteSintetico){
                throw new ServiceException("Cliente com esse código não encontrado.");
            }*/

            if(clienteSintetico != null){
                professores = ps.consultarProfessores(ctx, clienteSintetico.getEmpresa());
                professorCarteira = ps.consultarProfessorCarteiraPorCliente(ctx, cliente);
            }else{
                professorCarteira = ps.consultarProfessorPorCodigoPessoa(ctx, codigoPessoa);
                professores = ps.consultarProfessores(ctx, professorCarteira.getEmpresa().getCodigo());
            }

            List<ProfessorSimplesJSON> professoresItens = new ArrayList<ProfessorSimplesJSON>();

            Ordenacao.ordenarLista(professores, "nome");
            for (ProfessorSintetico pss : professores) {
                pss.setAvatar(defineUrlFotoJSON(ctx,pss.getCodigoPessoa(),request));
                    professoresItens.add(ProfessorSimplesJSON.obterJSON(pss,(professorCarteira != null && pss.getCodigo().equals(professorCarteira.getCodigo()))));
            }
            mm.addAttribute(RETURN, professoresItens);
        } catch (ServiceException ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ProfessorSinteticoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/app/consultarDashColaborador", method = {RequestMethod.GET})
    public @ResponseBody
    ModelMap consultarDashColaboradorApp(@PathVariable final String ctx,
                                      @RequestParam Integer codigoProfessor, @RequestParam Integer empresa) {
        ModelMap mm = new ModelMap();
        DashboardBIService dashboardBIService = (DashboardBIService) UtilContext.getBean(DashboardBIService.class);

        try {
            FiltrosDashboard filtros = new FiltrosDashboard();
            filtros.setEmpresa(empresa);
            DashboardBI dashboardBI = dashboardBIService.processarGestao(ctx, codigoProfessor, Calendario.hoje(), filtros, false, SuperControle.independente(ctx), empresa);

            dashboardBIService.processarAlunosAtivosSemTreino(ctx, codigoProfessor, filtros, dashboardBI);
            ModelMap biTreinosEmDia = new ModelMap();
            biTreinosEmDia.addAttribute("alunosAtivos", dashboardBI.getTotalAlunosAtivos());
            biTreinosEmDia.addAttribute("alunosAtivosSemTreino", dashboardBI.getTotalAlunosAtivosForaTreino());
            Date dataInicioPesquisa = Uteis.somarDias(Calendario.hoje(), -(filtros.getDiasParaTras()));
            ModelMap dashAvaliacaoFisica = new ModelMap();
            List<Agendamento> agendamentos = dashboardBIService.obterAgendamentos(ctx, codigoProfessor, dashboardBI,
                    dataInicioPesquisa,
                    Calendario.hoje(), true, TipoAgendamentoEnum.AVALIACAO_FISICA, null,null,null);
            dashAvaliacaoFisica.addAttribute("totalSemAgendamento", 0);
            dashAvaliacaoFisica.addAttribute("totalAgendada", agendamentos.size());

            List<DiasSemanaDashboardBI> dias = new ArrayList<DiasSemanaDashboardBI>();
            dias = dashboardBIService.obterDias(ctx, Uteis.getAnoData(Calendario.hoje()),
                    Uteis.getMesData(Calendario.hoje()), codigoProfessor, empresa);

            ModelMap dashExecsTreino = new ModelMap();
            for(DiasSemanaDashboardBI dia: dias)
            {
                int totalDia = dia.getTotalManha() + dia.getTotalTarde() + dia.getTotalNoite();
                dashExecsTreino.addAttribute(dia.getDiaSemana().getMin().toUpperCase(), totalDia);
            }

            ModelMap dashPrograma = new ModelMap();
            dashPrograma.addAttribute("vencidos", dashboardBI.getTotalTreinosVencidos());
            dashPrograma.addAttribute("aRenovar", dashboardBI.getTotalTreinosRenovar());
            dashPrograma.addAttribute("emDia", dashboardBI.getTotalTreinosEmdia());
            dashPrograma.addAttribute("total", dashboardBI.getTotalAlunos());
            dashPrograma.addAttribute("biTreinosEmDia", biTreinosEmDia);
            dashPrograma.addAttribute("dashAvaliacaoFisica", dashAvaliacaoFisica);
            dashPrograma.addAttribute("dashExecsTreino", dashExecsTreino);
            dashPrograma.addAttribute("avaliacaoMediaTreinos", dashboardBI.getMediaValorAvaliacao());


            mm.addAttribute("sucesso", dashPrograma);
        }catch (Exception ex)
        {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ProfessorSinteticoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }
}
