package br.com.pacto.controller.json.gestao;

import br.com.pacto.bean.bi.DashboardBI;

public class BITreinoAvaliacaoTreinoDTO {

    private Integer nrAvaliacoesTreino;
    private Integer nr1estrela;
    private Integer nr2estrelas;
    private Integer nr3estrelas;
    private Integer nr4estrelas;
    private Integer nr5estrelas;

    public BITreinoAvaliacaoTreinoDTO(DashboardBI dash) {
        this.nrAvaliacoesTreino = dash.getNrAvaliacoesTreino();
        this.nr1estrela = dash.getNr1estrelas();
        this.nr2estrelas = dash.getNr2estrelas();
        this.nr3estrelas = dash.getNr3estrelas();
        this.nr4estrelas = dash.getNr4estrelas();
        this.nr5estrelas = dash.getNr5estrelas();
    }

    public Integer getNrAvaliacoesTreino() {
        return nrAvaliacoesTreino;
    }

    public void setNrAvaliacoesTreino(Integer nrAvaliacoesTreino) {
        this.nrAvaliacoesTreino = nrAvaliacoesTreino;
    }

    public Integer getNr1estrela() {
        return nr1estrela;
    }

    public void setNr1estrela(Integer nr1estrela) {
        this.nr1estrela = nr1estrela;
    }

    public Integer getNr2estrelas() {
        return nr2estrelas;
    }

    public void setNr2estrelas(Integer nr2estrelas) {
        this.nr2estrelas = nr2estrelas;
    }

    public Integer getNr3estrelas() {
        return nr3estrelas;
    }

    public void setNr3estrelas(Integer nr3estrelas) {
        this.nr3estrelas = nr3estrelas;
    }

    public Integer getNr4estrelas() {
        return nr4estrelas;
    }

    public void setNr4estrelas(Integer nr4estrelas) {
        this.nr4estrelas = nr4estrelas;
    }

    public Integer getNr5estrelas() {
        return nr5estrelas;
    }

    public void setNr5estrelas(Integer nr5estrelas) {
        this.nr5estrelas = nr5estrelas;
    }
}
