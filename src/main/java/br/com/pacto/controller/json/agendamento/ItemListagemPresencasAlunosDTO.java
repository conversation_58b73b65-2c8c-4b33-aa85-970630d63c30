package br.com.pacto.controller.json.agendamento;

import org.json.JSONObject;

public class ItemListagemPresencasAlunosDTO {

        private String nomeAluno;
        private String nomeProfessor;
        private String nomeModalidade;
        private String aula;
        private Long dia;
        private String horario;

        public ItemListagemPresencasAlunosDTO(JSONObject object) throws Exception {
            this.nomeAluno = object.optString("nomeAluno");
            this.nomeProfessor = object.optString("nomePessoa");
            this.nomeModalidade = object.optString("nomeModalidade");
            this.aula = object.optString("aula") + " - " + object.optString("horario");
            this.dia = object.optLong("dia");
        }

        public String getNome() {
            return nomeAluno;
        }

        public void setNome(String nome) {
            this.nomeAluno = nome;
        }

        public String getNomeAluno() {
            return nomeAluno;
        }

        public void setNomeAluno(String nomeAluno) {
            this.nomeAluno = nomeAluno;
        }

        public String getNomeProfessor() {
            return nomeProfessor;
        }

        public void setNomeProfessor(String nomeProfessor) {
            this.nomeProfessor = nomeProfessor;
        }

        public String getNomeModalidade() {
            return nomeModalidade;
        }

        public void setNomeModalidade(String nomeModalidade) {
            this.nomeModalidade = nomeModalidade;
        }

        public String getAula() {
            return aula;
        }

        public void setAula(String aula) {
            this.aula = aula;
        }

        public Long getDia() {
            return dia;
        }

        public void setDia(Long dia) {
            this.dia = dia;
        }

        public String getHorario() {
            return horario;
        }

        public void setHorario(String horario) {
            this.horario = horario;
        }
    }
