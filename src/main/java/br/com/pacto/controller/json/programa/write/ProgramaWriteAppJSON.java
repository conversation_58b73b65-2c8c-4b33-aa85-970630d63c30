
/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pacto.controller.json.programa.write;

import br.com.pacto.bean.programa.ObjetivoPrograma;
import br.com.pacto.bean.programa.ProgramaSituacaoEnum;
import br.com.pacto.bean.programa.ProgramaTreino;
import br.com.pacto.bean.programa.ProgramaTreinoFicha;
import br.com.pacto.controller.json.acompanhamento.AcompanhamentoSimplesJSON;
import br.com.pacto.controller.json.base.SuperJSON;
import br.com.pacto.controller.json.programa.read.ObjetivoProgramaJSON;
import br.com.pacto.controller.json.programa.read.ProgramaTreinoFichaAppJSON;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class ProgramaWriteAppJSON extends SuperJSON {

    private Integer codigo;
    private String username;
    private Long dataInicio;
    private Long dataLancamento;
    private Long dataProximaRevisao;
    private Long dataTerminoPrevisto;
    private Integer diasPorSemana;
    private String nome;
    private Integer situacao;
    private Integer totalAulasPrevistas;
    private Integer cliente;
    private Integer professorCargeira;
    private String professorCargeiraNome;
    private Integer professorMontou;
    private String professorMontouNome;
    private Integer programaTreinoRenovacao;
    private Long dataRenovacao;
    private Integer programaTreinoRenovado;
    private List<ProgramaTreinoFichaAppJSON> programaFichas = new ArrayList();
    private List<ObjetivoProgramaJSON> objetivosPrograma = new ArrayList();
    private AcompanhamentoSimplesJSON frequencia = new AcompanhamentoSimplesJSON();

    public ProgramaWriteAppJSON() {
    }

    public ProgramaWriteAppJSON(ProgramaTreino p) {
        this.codigo = p.getCodigo();
        this.dataInicio = p.getDataInicio() != null ? p.getDataInicio().getTime() : null;
        this.dataLancamento = p.getDataLancamento() != null ? p.getDataLancamento().getTime() : null;
        this.dataProximaRevisao = p.getDataProximaRevisao() != null ? p.getDataProximaRevisao().getTime() : null;
        this.dataTerminoPrevisto = p.getDataTerminoPrevisto() != null ? p.getDataTerminoPrevisto().getTime() : null;
        this.diasPorSemana = p.getDiasPorSemana();
        this.nome = p.getNome();
        this.situacao = p.getSituacao() != null ? p.getSituacao().getId() : ProgramaSituacaoEnum.ATIVO.getId();
        this.totalAulasPrevistas = p.getTotalAulasPrevistas();
        this.cliente = p.getCliente().getCodigo();
        if (p.getProfessorCarteira() != null) {
            this.professorCargeira = p.getProfessorCarteira().getCodigo();
        }
        if (p.getProfessorCarteira() != null) {
            this.professorCargeiraNome = p.getProfessorCarteira().getNome();
        }
        if (p.getProfessorMontou() != null) {
            this.professorMontou = p.getProfessorMontou().getCodigo();
        }
        if (p.getProfessorMontou() != null) {
            this.professorMontouNome = p.getProfessorMontou().getNome();
        }
        this.programaTreinoRenovacao = p.getProgramaTreinoRenovacao();

        this.dataRenovacao = (p.getDataRenovacao() != null) ? p.getDataRenovacao().getTime() : null;
        this.programaTreinoRenovado = p.getProgramaTreinoRenovacao();

    }

    public void preencherProgramaTreinoFichas(ProgramaTreino prog, final String urlBase) {
        if (prog.getProgramaFichas() != null) {
            for (ProgramaTreinoFicha ptf : prog.getProgramaFichas()) {
                this.programaFichas.add(new ProgramaTreinoFichaAppJSON(ptf, urlBase));
            }
        }
    }

    public void preencherObjetivosPrograma(ProgramaTreino prog) {
        if (prog.getObjetivos() != null) {
            for (ObjetivoPrograma ob : prog.getObjetivos()) {
                this.objetivosPrograma.add(new ObjetivoProgramaJSON(ob));
            }
        }
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public Long getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Long dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Long getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Long dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public Long getDataProximaRevisao() {
        return dataProximaRevisao;
    }

    public void setDataProximaRevisao(Long dataProximaRevisao) {
        this.dataProximaRevisao = dataProximaRevisao;
    }

    public Long getDataTerminoPrevisto() {
        return dataTerminoPrevisto;
    }

    public void setDataTerminoPrevisto(Long dataTerminoPrevisto) {
        this.dataTerminoPrevisto = dataTerminoPrevisto;
    }

    public Integer getDiasPorSemana() {
        return diasPorSemana;
    }

    public void setDiasPorSemana(Integer diasPorSemana) {
        this.diasPorSemana = diasPorSemana;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Integer getSituacao() {
        return situacao;
    }

    public void setSituacao(Integer situacao) {
        this.situacao = situacao;
    }

    public Integer getTotalAulasPrevistas() {
        return totalAulasPrevistas;
    }

    public void setTotalAulasPrevistas(Integer totalAulasPrevistas) {
        this.totalAulasPrevistas = totalAulasPrevistas;
    }

    public Integer getCliente() {
        return cliente;
    }

    public void setCliente(Integer cliente) {
        this.cliente = cliente;
    }

    public Integer getProfessorCargeira() {
        if (professorCargeira == null) {
            professorCargeira = 0;
        }
        return professorCargeira;
    }

    public void setProfessorCargeira(Integer professorCargeira) {
        this.professorCargeira = professorCargeira;
    }

    public Integer getProfessorMontou() {
        return professorMontou;
    }

    public void setProfessorMontou(Integer professorMontou) {
        this.professorMontou = professorMontou;
    }

    public Integer getProgramaTreinoRenovacao() {
        if (programaTreinoRenovacao == null) {
            programaTreinoRenovacao = 0;
        }
        return programaTreinoRenovacao;
    }

    public void setProgramaTreinoRenovacao(Integer programaTreinoRenovacao) {
        this.programaTreinoRenovacao = programaTreinoRenovacao;
    }

    public Long getDataRenovacao() {
        return dataRenovacao;
    }

    public void setDataRenovacao(Long dataRenovacao) {
        this.dataRenovacao = dataRenovacao;
    }

    public Integer getProgramaTreinoRenovado() {
        if (programaTreinoRenovado == null) {
            programaTreinoRenovado = 0;
        }
        return programaTreinoRenovado;
    }

    public void setProgramaTreinoRenovado(Integer programaTreinoRenovado) {
        this.programaTreinoRenovado = programaTreinoRenovado;
    }

    public List<ProgramaTreinoFichaAppJSON> getProgramaFichas() {
        return programaFichas;
    }

    public void setProgramaFichas(List<ProgramaTreinoFichaAppJSON> programaFichas) {
        this.programaFichas = programaFichas;
    }

    public List<ObjetivoProgramaJSON> getObjetivosPrograma() {
        return objetivosPrograma;
    }

    public void setObjetivosPrograma(List<ObjetivoProgramaJSON> objetivosPrograma) {
        this.objetivosPrograma = objetivosPrograma;
    }

    public AcompanhamentoSimplesJSON getFrequencia() {
        return frequencia;
    }

    public void setFrequencia(AcompanhamentoSimplesJSON frequencia) {
        this.frequencia = frequencia;
    }

    public String getProfessorCargeiraNome() {
        return professorCargeiraNome;
    }

    public void setProfessorCargeiraNome(String professorCargeiraNome) {
        this.professorCargeiraNome = professorCargeiraNome;
    }

    public String getProfessorMontouNome() {
        return professorMontouNome;
    }

    public void setProfessorMontouNome(String professorMontouNome) {
        this.professorMontouNome = professorMontouNome;
    }
}
