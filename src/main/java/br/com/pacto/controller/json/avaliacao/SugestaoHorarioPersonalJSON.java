package br.com.pacto.controller.json.avaliacao;

public class SugestaoHorarioPersonalJSON {

    public Integer tipoEvento;
    public String nomeEvento;
    public Integer idAgendamento;
    public Integer idProfessor;
    public HorarioPersonalJSON horario;

    public SugestaoHorarioPersonalJSON(Integer tipoEvento, String nomeEvento, HorarioPersonalJSON horario, Integer idAgendamento, Integer idProfessor) {
        this.tipoEvento = tipoEvento;
        this.nomeEvento = nomeEvento;
        this.idAgendamento = idAgendamento;
        this.horario = horario;
        this.idProfessor = idProfessor;
    }

    public Integer getTipoEvento() {
        return tipoEvento;
    }

    public void setTipoEvento(Integer tipoEvento) {
        this.tipoEvento = tipoEvento;
    }

    public HorarioPersonalJSON getHorario() {
        return horario;
    }

    public void setHorario(HorarioPersonalJSON horario) {
        this.horario = horario;
    }

    public String getNomeEvento() {
        return nomeEvento;
    }

    public void setNomeEvento(String nomeEvento) {
        this.nomeEvento = nomeEvento;
    }

    public Integer getIdAgendamento() {
        return idAgendamento;
    }

    public void setIdAgendamento(Integer idAgendamento) {
        this.idAgendamento = idAgendamento;
    }

    public Integer getIdProfessor() {
        return idProfessor;
    }

    public void setIdProfessor(Integer idProfessor) {
        this.idProfessor = idProfessor;
    }
}
