package br.com.pacto.controller.json.colaborador;

import br.com.pacto.bean.perfil.permissao.Permissao;

import java.util.Objects;

public class FuncionalidadesDTO {

    private String nome;
    private Boolean possuiFuncionalidade;

    public FuncionalidadesDTO(Permissao permissao) {
        this.nome = permissao.getRecurso().name().toLowerCase();
        this.possuiFuncionalidade = permissao.getTipoPermissoes().size() > 0;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Boolean getPossuiFuncionalidade() {
        return possuiFuncionalidade;
    }

    public void setPossuiFuncionalidade(Boolean possuiFuncionalidade) {
        this.possuiFuncionalidade = possuiFuncionalidade;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        FuncionalidadesDTO that = (FuncionalidadesDTO) o;
        return nome.equals(that.nome);
    }

    @Override
    public int hashCode() {
        return Objects.hash(nome);
    }
}
