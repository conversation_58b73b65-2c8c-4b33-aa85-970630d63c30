package br.com.pacto.controller.json.aluno;

import br.com.pacto.bean.cliente.ClienteObservacao;
import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class AlunoObservacaoDTO {

    private Integer codigo;
    private Integer usuarioId;
    private Integer clienteId;
    private Boolean importante;
    private String observacao;
    private String data;
    private Boolean avaliacaoFisica;
    private String anexo;
    private String arquivoUpload;
    private String nomeArquivo;
    private String formatoArquivo;


    public AlunoObservacaoDTO(){

    }
    public AlunoObservacaoDTO (ClienteObservacao clienteObservacao){

        this.codigo = clienteObservacao.getCodigo();
        this.usuarioId = clienteObservacao.getUsuario_codigo();
        this.clienteId = clienteObservacao.getCliente().getCodigo();
        this.importante = clienteObservacao.getImportante();
        this.observacao = clienteObservacao.getObservacao();
        this.data = clienteObservacao.getDataObservacao().toString();
        this.avaliacaoFisica = clienteObservacao.getAvaliacaoFisica();
        this.anexo = clienteObservacao.getAnexoKey();

    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getUsuarioId() {
        return usuarioId;
    }

    public void setUsuarioId(Integer usuarioId) {
        this.usuarioId = usuarioId;
    }

    public Integer getClienteId() {
        return clienteId;
    }

    public void setClienteId(Integer clienteId) {
        this.clienteId = clienteId;
    }

    public Boolean getImportante() {
        return importante;
    }

    public void setImportante(Boolean importante) {
        this.importante = importante;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public Boolean getAvaliacaoFisica() {
        return avaliacaoFisica;
    }

    public void setAvaliacaoFisica(Boolean avaliacaoFisica) {
        this.avaliacaoFisica = avaliacaoFisica;
    }

    public String getArquivoUpload() {
        return arquivoUpload;
    }

    public void setArquivoUpload(String arquivoUpload) {
        this.arquivoUpload = arquivoUpload;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public String getAnexo() {
        return anexo;
    }

    public void setAnexo(String anexo) {
        this.anexo = anexo;
    }

    public String getNomeArquivo() {
        return nomeArquivo;
    }

    public void setNomeArquivo(String nomeArquivo) {
        this.nomeArquivo = nomeArquivo;
    }

    public String getFormatoArquivo() {
        return formatoArquivo;
    }

    public void setFormatoArquivo(String formatoArquivo) {
        this.formatoArquivo = formatoArquivo;
    }
}
