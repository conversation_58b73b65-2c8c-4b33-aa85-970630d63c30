package br.com.pacto.controller.json.base;

import br.com.pacto.util.UteisValidacao;
import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class ConfiguracoesManutencaoDTO {

    private String aplicativo_personalizado;
    private String aplicativo_personalizado_nome;
    private String aplicativo_personalizado_url;

    public Boolean getAplicativo_personalizado() {
        if (!UteisValidacao.emptyString(aplicativo_personalizado)) {
            return aplicativo_personalizado.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setAplicativo_personalizado(String aplicativo_personalizado) {
        this.aplicativo_personalizado = aplicativo_personalizado;
    }

    public String getAplicativo_personalizado_nome() {
        return aplicativo_personalizado_nome;
    }

    public void setAplicativo_personalizado_nome(String aplicativo_personalizado_nome) {
        this.aplicativo_personalizado_nome = aplicativo_personalizado_nome;
    }

    public String getAplicativo_personalizado_url() {
        return aplicativo_personalizado_url;
    }

    public void setAplicativo_personalizado_url(String aplicativo_personalizado_url) {
        this.aplicativo_personalizado_url = aplicativo_personalizado_url;
    }
}
