package br.com.pacto.controller.json.log;


import br.com.pacto.controller.json.base.SuperJSON;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 05/11/2018
 */
public class LogJSON extends SuperJSON {

    private Integer codigo;
    private Integer pessoa;
    private Integer cliente;

    private String nomeEntidade;
    private String nomeEntidadeDescricao;
    private String chavePrimaria;
    private String chavePrimariaEntidadeSubordinada;
    private String nomeCampo;
    private String valorCampoAnterior;
    private String valorCampoAlterado;
    private String responsavelAlteracao;
    private String operacao;
    private String descricao;
    private String userOAMD = "";
    private String clienteNome;
    private String nomePessoa;

    private Date dataAlteracao;


    public LogJSON() {
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNomeEntidade() {
        return nomeEntidade;
    }

    public void setNomeEntidade(String nomeEntidade) {
        this.nomeEntidade = nomeEntidade;
    }

    public String getNomeEntidadeDescricao() {
        return nomeEntidadeDescricao;
    }

    public void setNomeEntidadeDescricao(String nomeEntidadeDescricao) {
        this.nomeEntidadeDescricao = nomeEntidadeDescricao;
    }

    public String getChavePrimaria() {
        return chavePrimaria;
    }

    public void setChavePrimaria(String chavePrimaria) {
        this.chavePrimaria = chavePrimaria;
    }

    public String getChavePrimariaEntidadeSubordinada() {
        return chavePrimariaEntidadeSubordinada;
    }

    public void setChavePrimariaEntidadeSubordinada(String chavePrimariaEntidadeSubordinada) {
        this.chavePrimariaEntidadeSubordinada = chavePrimariaEntidadeSubordinada;
    }

    public String getNomeCampo() {
        return nomeCampo;
    }

    public void setNomeCampo(String nomeCampo) {
        this.nomeCampo = nomeCampo;
    }

    public String getValorCampoAnterior() {
        return valorCampoAnterior;
    }

    public void setValorCampoAnterior(String valorCampoAnterior) {
        this.valorCampoAnterior = valorCampoAnterior;
    }

    public String getValorCampoAlterado() {
        return valorCampoAlterado;
    }

    public void setValorCampoAlterado(String valorCampoAlterado) {
        this.valorCampoAlterado = valorCampoAlterado;
    }

    public Date getDataAlteracao() {
        return dataAlteracao;
    }

    public void setDataAlteracao(Date dataAlteracao) {
        this.dataAlteracao = dataAlteracao;
    }

    public String getResponsavelAlteracao() {
        return responsavelAlteracao;
    }

    public void setResponsavelAlteracao(String responsavelAlteracao) {
        this.responsavelAlteracao = responsavelAlteracao;
    }

    public String getOperacao() {
        return operacao;
    }

    public void setOperacao(String operacao) {
        this.operacao = operacao;
    }

    public Integer getPessoa() {
        return pessoa;
    }

    public void setPessoa(Integer pessoa) {
        this.pessoa = pessoa;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getUserOAMD() {
        return userOAMD;
    }

    public void setUserOAMD(String userOAMD) {
        this.userOAMD = userOAMD;
    }

    public Integer getCliente() {
        return cliente;
    }

    public void setCliente(Integer cliente) {
        this.cliente = cliente;
    }

    public String getClienteNome() {
        return clienteNome;
    }

    public void setClienteNome(String clienteNome) {
        this.clienteNome = clienteNome;
    }

    public String getNomePessoa() {
        return nomePessoa;
    }

    public void setNomePessoa(String nomePessoa) {
        this.nomePessoa = nomePessoa;
    }
}
