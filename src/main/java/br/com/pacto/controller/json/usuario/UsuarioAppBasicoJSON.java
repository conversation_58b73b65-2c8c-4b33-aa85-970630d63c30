package br.com.pacto.controller.json.usuario;

import br.com.pacto.controller.json.base.SuperJSON;

/**
 *
 * <AUTHOR>
 */

public class UsuarioAppBasicoJSON extends SuperJSON {
    private Integer codigocliente;
    private Integer codigocolaborador;
    private String nome;
    private String email;
    private String celular;
    private Integer codigousuariomovel;
    private String nomeusuariomovel;
    private Boolean statususuariomovel;
    private String urlfoto;
    private String matricula;
    private String nomeusuario;
    private Integer codigousuariotreino;

    public Integer getCodigocliente() {
        return codigocliente;
    }

    public void setCodigocliente(Integer codigocliente) {
        this.codigocliente = codigocliente;
    }

    public Integer getCodigocolaborador() {
        return codigocolaborador;
    }

    public void setCodigocolaborador(Integer codigocolaborador) {
        this.codigocolaborador = codigocolaborador;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getCelular() {
        return celular;
    }

    public void setCelular(String celular) {
        this.celular = celular;
    }

    public Integer getCodigousuariomovel() {
        return codigousuariomovel;
    }

    public String getNomeusuario() {
        return nomeusuario;
    }

    public void setNomeusuario(String nomeusuario) {
        this.nomeusuario = nomeusuario;
    }

    public void setCodigousuariomovel(Integer codigousuariomovel) {
        this.codigousuariomovel = codigousuariomovel;
    }

    public String getNomeusuariomovel() {
        return nomeusuariomovel;
    }

    public void setNomeusuariomovel(String nomeusuariomovel) {
        this.nomeusuariomovel = nomeusuariomovel;
    }

    public Boolean getStatususuariomovel() {
        return statususuariomovel;
    }

    public void setStatususuariomovel(Boolean statususuariomovel) {
        this.statususuariomovel = statususuariomovel;
    }

    public String getUrlfoto() {
        return urlfoto;
    }

    public void setUrlfoto(String urlfoto) {
        this.urlfoto = urlfoto;
    }

    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public Integer getCodigousuariotreino() {
        return codigousuariotreino;
    }

    public void setCodigousuariotreino(Integer codigousuariotreino) {
        this.codigousuariotreino = codigousuariotreino;
    }
}
