package br.com.pacto.controller.json.avaliacao;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.bi.IndicadorAvaliacaoFisicaEnum;
import br.com.pacto.bean.professor.ProfessorSintetic<PERSON>;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.avaliacao.bi_enum.TipoBIAvaliacaoEnum;
import br.com.pacto.dao.intf.professor.ProfessorSinteticoDao;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.avaliacao.ItemAvaliacaoFisicaTO;
import br.com.pacto.service.intf.avaliacao.BIAvaliacaoFisicaService;
import br.com.pacto.service.intf.usuario.UsuarioService;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR> Siqueira 16/01/2019
 */

@Controller
@RequestMapping("/psec/avaliacao-fisica-bi")
public class BIAvalicaoFisicaController {

    @Autowired
    private BIAvaliacaoFisicaService biAvaliacaoFisicaService;
    @Autowired
    private SessaoService sessaoService;
    @Autowired
    private UsuarioService usuarioService;

    @ResponseBody
    @RequestMapping(method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> motarBI(@RequestHeader("empresaId") Integer empresaId,
                                                       @RequestParam(value = "dataInicio") Long dataInicio,
                                                       @RequestParam(value = "dataFim") Long dataFim,
                                                       @RequestParam(value = "codigoProfessor", required = false) Integer codigoProfessor,
                                                       @RequestParam(value = "tipoAvaliacao", required = false) TipoBIAvaliacaoEnum tipoAvaliacao,
                                                       @RequestParam(value = "codigoAvaliador", required = false) Integer codigoAvaliador){
        try{
            final String chave = sessaoService.getUsuarioAtual().getChave();

            if (UteisValidacao.notEmptyNumber(codigoAvaliador) && codigoAvaliador > 0) {
                try {
                    Usuario us = usuarioService.consultarProColaborador(chave, codigoAvaliador);
                    if (us != null) {
                        codigoAvaliador = us.getCodigo();
                    }
                } catch (Exception ignore) {}
            }
            return ResponseEntityFactory.ok(biAvaliacaoFisicaService.getBI(chave, empresaId, new Date(dataInicio), new Date(dataFim), codigoProfessor, tipoAvaliacao, codigoAvaliador));
        }catch (ServiceException e){
            Logger.getLogger(BIAvalicaoFisicaController.class.getName()).log(Level.SEVERE, "Erro ao consultar BI Avaliacao Fisica", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/avaliacoes", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> motarListaAvaliacoesBI(@RequestHeader("empresaId") Integer empresaId,
                                                                      @RequestParam(value = "filters", required = false) JSONObject filtros,
                                                                      PaginadorDTO paginadorDTO){
        try{
            FiltrosAvaliacaoJSON filtro = new FiltrosAvaliacaoJSON(filtros);
            if ( null != filtro.getTipoAvaliacao() && filtro.getTipoAvaliacao().equals("ALUNOS_SEM_VINCULO")){
                filtro.setCodProfessor(TipoBIAvaliacaoEnum.ALUNOS_SEM_VINCULO.getId());
            }
            if (null != filtro.getTipoAvaliacao() && filtro.getTipoAvaliacao().equals("TODOS_PROFESSORES_INATIVOS")){
                filtro.setCodProfessor(TipoBIAvaliacaoEnum.TODOS_PROFESSORES_INATIVOS.getId());
            }
            final String chave = sessaoService.getUsuarioAtual().getChave();
            if ( filtro.getCodProfessor() != 0 && filtro.getCodProfessor() != Integer.MAX_VALUE && filtro.getCodProfessor() != Integer.MAX_VALUE-1){
                ProfessorSinteticoDao professorSinteticoDao = UtilContext.getBean(ProfessorSinteticoDao.class);
                ProfessorSintetico professorSintetico = professorSinteticoDao.obterPorIdColaborador(chave,filtro.getCodProfessor());
                filtro.setCodProfessor(professorSintetico == null ? 0 : professorSintetico.getCodigo());
            }
            Integer codigoAvaliador = filtro.getCodAvaliador();

            if (filtro.getCodAvaliador() == null) {
                try {
                    Integer usuarioLogado = sessaoService.getUsuarioAtual().getId();
                    codigoAvaliador = usuarioLogado;
                } catch (Exception ignore) {}
            }
            if (UteisValidacao.notEmptyNumber(filtro.getCodAvaliador()) && filtro.getCodAvaliador() > 0) {
                try {
                    Usuario us = usuarioService.consultarProColaborador(chave, filtro.getCodAvaliador());
                    if (us != null) {
                        codigoAvaliador = us.getCodigo();
                    }
                } catch (Exception ignore) {}
            }
            return ResponseEntityFactory.ok(biAvaliacaoFisicaService.listaBI(
                    chave,
                    empresaId,
                    IndicadorAvaliacaoFisicaEnum.valueOf(filtro.getIndicador()),
                    filtro.getDataInicio(),
                    filtro.getDataFim(),
                    filtro.getObjetivo(),
                    filtro.getCodProfessor(),
                    filtro.getParametro(),
                    paginadorDTO,
                    codigoAvaliador), paginadorDTO);

        }catch (ServiceException e){
            Logger.getLogger(BIAvalicaoFisicaController.class.getName()).log(Level.SEVERE, "Erro ao consultar BI Avaliacao Fisica", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/avaliacoes/exportar", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> exportarListaAvaliacoesBI(@RequestHeader("empresaId") Integer empresaId,
                                                                      @RequestParam(value = "filtros", required = false) JSONObject filtros,
                                                                      @RequestParam(value = "urlTreino", required = false) String urlTreino,
                                                                      @RequestParam(value = "format", required = false) String format,
                                                                      PaginadorDTO paginadorDTO) throws Exception {
        try{
            FiltrosAvaliacaoJSON filtro = new FiltrosAvaliacaoJSON(filtros);
            final String chave = sessaoService.getUsuarioAtual().getChave();
            List<ItemAvaliacaoFisicaTO> listaObj = biAvaliacaoFisicaService.listaBI(
                    chave,
                    empresaId,
                    IndicadorAvaliacaoFisicaEnum.valueOf(filtro.getIndicador()),
                    filtro.getDataInicio(),
                    filtro.getDataFim(),
                    filtro.getObjetivo(),
                    filtro.getCodProfessor(),
                    filtro.getParametro(),
                    paginadorDTO,
                    filtro.getCodAvaliador());

            List<Map<String, Object>> lista = new ArrayList<>();
            for (ItemAvaliacaoFisicaTO item : listaObj) {
                Map<String, Object> map = new HashMap<>();
                if (null != item.getCodCliente()){
                    map.put("Codigo do cliente", item.getCodCliente());
                }
                if (null != item.getNome()){
                    map.put("Aluno", item.getNome());
                }
                if (null != item.getDataAvaliacao()){
                    map.put("Dia", item.getDataAvaliacao());
                }
                if (null != item.getAvaliador()){
                    map.put("Avaliador", item.getAvaliador());
                }
                if (null != item.getProfessor()){
                    map.put("Professor", item.getProfessor());
                }
                if (null != item.getObs()){
                    map.put("Observação", item.getObs());
                }
                if (null != item.getDiff()){
                    map.put("Diff", item.getDiff());
                }
                if (null != item.getMatricula()){
                    map.put("Matricula do cliente", item.getMatricula());
                }
                lista.add(map);
            }

            String url = "";
//            url = processarUrl( url, format, urlTreino, lista);
            return ResponseEntityFactory.ok(url);

        }catch (ServiceException e){
            Logger.getLogger(BIAvalicaoFisicaController.class.getName()).log(Level.SEVERE, "Erro ao consultar BI Avaliacao Fisica", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }


}
