/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.controller.json.ficha;

import br.com.pacto.controller.json.ficha.read.FilaImpressaoJSON;
import br.com.pacto.base.util.Propagador;
import br.com.pacto.bean.configuracoes.ConfiguracaoSistema;
import br.com.pacto.bean.configuracoes.ConfiguracoesEnum;
import br.com.pacto.bean.ficha.Ficha;
import br.com.pacto.bean.ficha.FilaImpressao;
import br.com.pacto.bean.programa.ProgramaTreinoFicha;
import br.com.pacto.bean.sincronizacao.ObjetoSincronizarTO;
import br.com.pacto.bean.sincronizacao.TipoObjetoSincronizarEnum;
import br.com.pacto.controller.json.base.SuperControle;
import static br.com.pacto.controller.json.base.SuperControle.RETURN;
import br.com.pacto.controller.json.cliente.ClienteJSON;
import br.com.pacto.controller.json.programa.read.ProgramaTreinoJSON;
import static br.com.pacto.controller.json.programa.ProgramaTreinoJSONControle.preencherProgramaJSON;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.configuracoes.ConfiguracaoSistemaService;
import br.com.pacto.service.intf.ficha.FichaService;
import br.com.pacto.service.intf.ficha.FilaImpressaoService;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;

/**
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("/filaImpressao")
public class FilaImpressaoJSONControle extends SuperControle {

    @Autowired
    private FilaImpressaoService service;
    @Autowired
    private FichaService fichaService;
    @Autowired
    private ConfiguracaoSistemaService configService;

    @RequestMapping(value = "{ctx}/get", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap filaImpressao(@PathVariable String ctx, HttpServletRequest request) {
        ModelMap mm = new ModelMap();
        try {
            List<FilaImpressao> fila = service.obterTodosFilaImpressao(ctx);
            if (fila != null && !fila.isEmpty()) {
                List<FilaImpressaoJSON> arr = new ArrayList<FilaImpressaoJSON>();
                for (FilaImpressao f : fila) {
                    ProgramaTreinoFicha progFicha = f.getFicha().getProgramas().get(0);
                    ClienteJSON clienteJSON = new ClienteJSON(progFicha.getPrograma().getCliente());
                    ConfiguracaoSistema configSeriesSet = configService.consultarPorTipo(ctx, ConfiguracoesEnum.AGRUPAMENTO_SERIES_SET);
                    ProgramaTreinoJSON programaJSON = preencherProgramaJSON(progFicha.getPrograma(), ctx, f.getFicha(), configSeriesSet.getValorAsBoolean(), "", null);
                    arr.add(new FilaImpressaoJSON(f.getDataRegistro(), clienteJSON, programaJSON));
                }
                service.excluirTodosFilaImpressao(ctx);
                mm.addAttribute("filaImpressao", arr);
                Propagador.propagar(request);
            }
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(FilaImpressaoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/verify", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap verify(@PathVariable String ctx) {
        ModelMap mm = new ModelMap();
        try {
            mm.addAttribute(RETURN, service.count(ctx));
        } catch (ServiceException ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(FilaImpressaoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/sync", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap sync(@PathVariable String ctx, @RequestParam Integer empresaZW) {
        ModelMap mm = new ModelMap();
        try {
            if (empresaZW == null) {
                mm.addAttribute(RETURN, getViewUtils().getMensagem("sync.empresaNaoInformada"));
            } else {
                List<ObjetoSincronizarTO> lista = service.obterTodosSincronizar(ctx, empresaZW);

                if (lista == null) {
                    mm.addAttribute(RETURN, getViewUtils().getMensagem("sync.nadaASincronizar"));
                } else {
                    List<ObjetoSincronizarTO> sincronizarEmpresa = new ArrayList<>();
                    for(ObjetoSincronizarTO objeto : lista){
                        if (objeto.getEmpresa() == null || objeto.getEmpresa().equals(empresaZW)) {
                            sincronizarEmpresa.add(objeto);
                        }
                    }
                    if (sincronizarEmpresa.size() > 0) {
                        mm.addAttribute(RETURN, sincronizarEmpresa);
                        service.excluirTodosSincronizar(ctx, empresaZW);
                    } else {
                        mm.addAttribute(RETURN, getViewUtils().getMensagem("sync.semSincronizacaoParaEmpresaInformada"));
                    }
                }
            }
        } catch (ServiceException ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(FilaImpressaoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/enfileirar", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap enfileirar(@PathVariable String ctx, @RequestParam final Integer idFicha, HttpServletRequest request) {
        ModelMap mm = new ModelMap();
        try {
            Ficha f = fichaService.obterPorId(ctx, idFicha);
            if (f != null) {
                if (service.enfileirar(ctx, new FilaImpressao(f)) != null) {
                    mm.addAttribute(RETURN, STATUS_SUCESSO);
                    Propagador.propagar(request);
                    return mm;
                }
            }
            mm.addAttribute(STATUS_ERRO, getViewUtils().getMensagem("filaImpressao.nadaEnfileirado"));
        } catch (ServiceException ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(FilaImpressaoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/enfileirarSync", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap enfileirarSync(@PathVariable String ctx, @RequestParam final String tipo,
            @RequestParam final String chaveBusca) {
        ModelMap mm = new ModelMap();
        try {
            if (service.enfileirar(ctx, TipoObjetoSincronizarEnum.valueOf(tipo), chaveBusca) != null) {
                mm.addAttribute(RETURN, STATUS_SUCESSO);
                return mm;
            }
            mm.addAttribute(STATUS_ERRO, getViewUtils().getMensagem("filaImpressao.nadaEnfileirado"));
        } catch (ServiceException ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(FilaImpressaoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/limpar", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap limpar(@PathVariable String ctx, HttpServletRequest request) {
        ModelMap mm = new ModelMap();
        try {
            service.excluirTodosFilaImpressao(ctx);
            mm.addAttribute(RETURN, STATUS_SUCESSO);
            Propagador.propagar(request);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(FilaImpressaoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }
}
