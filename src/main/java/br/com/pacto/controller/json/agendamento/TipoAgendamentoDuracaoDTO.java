package br.com.pacto.controller.json.agendamento;

import br.com.pacto.bean.disponibilidade.Disponibilidade;
import br.com.pacto.bean.tipoEvento.TipoEvento;
import br.com.pacto.enumerador.agenda.TipoDuracaoEvento;

public class TipoAgendamentoDuracaoDTO {
    private TipoDuracaoEvento tipo;
    private Integer min;
    private Integer max;
    private Integer fixo;
    private Boolean tipoEvento;
    private Boolean ambienteDisponivel;

    public TipoAgendamentoDuracaoDTO(TipoEvento tipoEvento) {
        this.tipo = tipoEvento.getDuracao();
        if(this.tipo.equals(TipoDuracaoEvento.DURACAO_PREDEFINIDA)){
            this.fixo = tipoEvento.getDuracaoMinutosMin();
        } else {
            this.min = tipoEvento.getDuracaoMinutosMin();
            this.max = tipoEvento.getDuracaoMinutosMin();
        }
        this.tipoEvento = true;
        this.ambienteDisponivel = true;
    }

    public TipoAgendamentoDuracaoDTO(Disponibilidade disponibilidade, Boolean ambienteDisponivel) {
        this.tipo = TipoDuracaoEvento.getFromId(disponibilidade.getTipoHorario());
        if(this.tipo.equals(TipoDuracaoEvento.DURACAO_PREDEFINIDA)){
            this.fixo = disponibilidade.getDuracao();
        } else {
            this.min = disponibilidade.getDuracaoMinima();
            this.max = disponibilidade.getDuracaoMaxima();
        }
        this.tipoEvento = false;
        this.ambienteDisponivel = ambienteDisponivel;
    }

    public TipoDuracaoEvento getTipo() {
        return tipo;
    }

    public void setTipo(TipoDuracaoEvento tipo) {
        this.tipo = tipo;
    }

    public Integer getMin() {
        return min;
    }

    public void setMin(Integer min) {
        this.min = min;
    }

    public Integer getMax() {
        return max;
    }

    public void setMax(Integer max) {
        this.max = max;
    }

    public Integer getFixo() {
        return fixo;
    }

    public void setFixo(Integer fixo) {
        this.fixo = fixo;
    }

    public Boolean getTipoEvento() {
        return tipoEvento;
    }

    public void setTipoEvento(Boolean tipoEvento) {
        this.tipoEvento = tipoEvento;
    }

    public Boolean getAmbienteDisponivel() {
        return ambienteDisponivel;
    }

    public void setAmbienteDisponivel(Boolean ambienteDisponivel) {
        this.ambienteDisponivel = ambienteDisponivel;
    }
}
