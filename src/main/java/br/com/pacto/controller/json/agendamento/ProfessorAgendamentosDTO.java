package br.com.pacto.controller.json.agendamento;

import java.util.List;

public class ProfessorAgendamentosDTO {

    private Integer professorId;
    private List<AgendamentoEventoDTO> agendamentos;

    public ProfessorAgendamentosDTO() {
    }

    public ProfessorAgendamentosDTO(Integer professorId, List<AgendamentoEventoDTO> agendamentos) {
        this.professorId = professorId;
        this.agendamentos = agendamentos;
    }

    public Integer getProfessorId() {
        return professorId;
    }

    public void setProfessorId(Integer professorId) {
        this.professorId = professorId;
    }

    public List<AgendamentoEventoDTO> getAgendamentos() {
        return agendamentos;
    }

    public void setAgendamentos(List<AgendamentoEventoDTO> agendamentos) {
        this.agendamentos = agendamentos;
    }
}
