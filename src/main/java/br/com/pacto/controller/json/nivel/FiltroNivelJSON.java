package br.com.pacto.controller.json.nivel;

import br.com.pacto.controller.json.base.SuperJSON;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

public class FiltroNivelJSON extends SuperJSON {

    private Boolean nome = false;
    private String parametro;
    private Boolean situacao;

    public FiltroNivelJSON(JSONObject filters) throws JSONException {

        if (filters != null) {

            this.parametro = filters.optString("quicksearchValue");
            JSONArray situacoes = filters.optJSONArray("situacoes");
            JSONArray colunasVisiveis = filters.optJSONArray("quicksearchFields");
            if (situacoes != null && situacoes.length() <= 1) {
                try {
                    this.situacao = situacoes.get(0).toString().equalsIgnoreCase("ATIVO") ? true : false;
                } catch (Exception e) {
                    this.situacao = filters.optString("situacoes").contains("ATIVO") ? true : false;
                }
            }

            if (colunasVisiveis != null) {
                for (int i = 0; i < colunasVisiveis.length(); i++) {
                    if (colunasVisiveis.get(i).equals("nome")) {
                        this.nome = true;
                    }
                }
            }
        }
    }

    public Boolean getNome() {
        return nome;
    }

    public void setNome(Boolean nome) {
        this.nome = nome;
    }

    public String getParametro() {
        return parametro;
    }

    public void setParametro(String parametro) {this.parametro = parametro;}

    public Boolean getSituacao() {
        return situacao;
    }

    public void setSituacao(Boolean situacao) {
        this.situacao = situacao;
    }
}
