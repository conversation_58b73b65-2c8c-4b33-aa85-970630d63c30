package br.com.pacto.controller.json.gestao;

import br.com.pacto.bean.bi.TipoEventoDisponibilidadeBI;

public class AgendamentoPorTipoComportamentoDTO {
    private String tipo;
    private Integer disponibilidade;
    private Integer executaram;
    private Integer cancelaram;
    private Integer faltaram;
    private Integer aguardandoConfirmacao;
    private Integer confirmado;

    public AgendamentoPorTipoComportamentoDTO() {

    }

    public AgendamentoPorTipoComportamentoDTO(TipoEventoDisponibilidadeBI tipo) {
        this.tipo = tipo.getTipo().getDescricao();
        this.disponibilidade = tipo.getHorasDisponibilidade();
        this.executaram = tipo.getHorasExecutaram();
        this.cancelaram = tipo.getHorasCancelaram();
        this.faltaram = tipo.getHorasFaltaram();
        this.aguardandoConfirmacao = tipo.getHorasAguardando();
        this.confirmado = tipo.getHorasConfirmado();
    }

    public String getTipo() {
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }

    public Integer getDisponibilidade() {
        return disponibilidade;
    }

    public void setDisponibilidade(Integer disponibilidade) {
        this.disponibilidade = disponibilidade;
    }

    public Integer getExecutaram() {
        return executaram;
    }

    public void setExecutaram(Integer executaram) {
        this.executaram = executaram;
    }

    public Integer getCancelaram() {
        return cancelaram;
    }

    public void setCancelaram(Integer cancelaram) {
        this.cancelaram = cancelaram;
    }

    public Integer getFaltaram() {
        return faltaram;
    }

    public void setFaltaram(Integer faltaram) {
        this.faltaram = faltaram;
    }

    public Integer getAguardandoConfirmacao() {
        return aguardandoConfirmacao;
    }

    public void setAguardandoConfirmacao(Integer aguardandoConfirmacao) {
        this.aguardandoConfirmacao = aguardandoConfirmacao;
    }

    public Integer getConfirmado() {
        return confirmado;
    }

    public void setConfirmado(Integer confirmado) {
        this.confirmado = confirmado;
    }
}
