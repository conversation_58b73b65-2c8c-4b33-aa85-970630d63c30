package br.com.pacto.controller.json.programa;

import br.com.pacto.bean.atividade.AtividadeEmpresa;
import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * Created by joa<PERSON> moita on 03/10/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AtividadeEmpresaResponseTO {

    private Integer id;
    private String identificador;
    private EmpresaBasicaResponseTO empresa;

    public AtividadeEmpresaResponseTO(AtividadeEmpresa ae) {
        this.id = ae.getCodigo();
        this.identificador = ae.getIdentificador();
        this.empresa = new EmpresaBasicaResponseTO(ae.getEmpresa());
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getIdentificador() {
        return identificador;
    }

    public void setIdentificador(String identificador) {
        this.identificador = identificador;
    }

    public EmpresaBasicaResponseTO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaBasicaResponseTO empresa) {
        this.empresa = empresa;
    }

}