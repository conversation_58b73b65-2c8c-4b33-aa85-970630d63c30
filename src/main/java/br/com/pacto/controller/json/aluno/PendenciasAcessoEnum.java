package br.com.pacto.controller.json.aluno;

import br.com.pacto.bean.configuracoes.NotificacoesAcessoUsuario;

public enum PendenciasAcessoEnum {
    SEM_PROGRAMA_DE_TREINO("Sem programa de treino"),
    FALTOSOS("Faltosos"),
    QUATRO_DIAS_OU_MAIS_SEM_EXECUTAR_O_TREINO("4 dias ou mais sem executar o treino"),
    PROGRAMA_DE_TREINO_VENCIDO("Programa de treino vencido"),
    PROGRAMA_DE_TREINO_A_VENCER("Programa de treino a Vencer"),
    ALUNO_SEM_VINCULO_DE_PROFESSOR_TREINOWEB("Aluno sem vínculo de professor treinoweb"),
    PARCELAS_EM_ATRASO("Parcelas em atraso"),
    AVALIACAO_FISICA_ATRASADA("Avaliação física atrasada"),
    SEM_ASSINATURA_DE_CONTRATO("Sem assinatura de contrato"),
    PARQ_NAO_RESPONDIDO("Não preenchido"),
    PARQ_NEGATIVO("Negativo, assinado"),
    PARQ_POSITIVO("Positivo, assinado"),
    CADASTRO_INCOMPLETO("Cadastro incompleto");

    private final String descricao;

    PendenciasAcessoEnum(String descricao) {
        this.descricao = descricao;
    }

    public String getDescricao() {
        return descricao;
    }

    public static Boolean getValor(NotificacoesAcessoUsuario notificacoesAcessoUsuario, PendenciasAcessoEnum pendencia) {
        switch (pendencia) {
            case SEM_PROGRAMA_DE_TREINO:
                return notificacoesAcessoUsuario.getSemProgramaTreino();
            case FALTOSOS:
                return notificacoesAcessoUsuario.getFaltosos();
            case QUATRO_DIAS_OU_MAIS_SEM_EXECUTAR_O_TREINO:
                return notificacoesAcessoUsuario.getQuatrosDiasSemAcesso();
            case PROGRAMA_DE_TREINO_VENCIDO:
                return notificacoesAcessoUsuario.getTreinoVencido();
            case PROGRAMA_DE_TREINO_A_VENCER:
                return notificacoesAcessoUsuario.getTreinoVencer();
            case ALUNO_SEM_VINCULO_DE_PROFESSOR_TREINOWEB:
                return notificacoesAcessoUsuario.getAlunoSemVinculoProfessor();
            case PARCELAS_EM_ATRASO:
                return notificacoesAcessoUsuario.getParcelasAtrasadas();
            case AVALIACAO_FISICA_ATRASADA:
                return notificacoesAcessoUsuario.getAvaliacaoFisicaAtrasada();
            case SEM_ASSINATURA_DE_CONTRATO:
                return notificacoesAcessoUsuario.getSemAssinaturaContrato();
            case CADASTRO_INCOMPLETO:
                return notificacoesAcessoUsuario.getCadastroIncompleto();
            default:
                return false;
        }
    }
}
