package br.com.pacto.controller.json.agendamento;

import br.com.pacto.enumerador.agenda.StatusAgendamentoEnum;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.Date;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class AgendamentoDTO {

    private String data; //No formato yyyyMMdd
    private Integer duracao;
    private String horarioInicial;
    private String horarioFinal;
    private Integer tipo;
    private Integer professor;
    private Integer alunoId;
    private StatusAgendamentoEnum status;
    private String observacao;
    private Boolean horarioAlterado;
    private Boolean tipoEvento;
    private Integer horarioDisponibilidadeCod;
    private Boolean seg;
    private Boolean ter;
    private Boolean qua;
    private Boolean qui;
    private Boolean sex;
    private Boolean sab;
    private Boolean dom;
    private Integer opcSemanaMes;
    private Integer opcPeriodicidade;
    private Integer nrVezes;
    private Date dataFim;
    private Integer opcSemanaOuMes;


    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public Integer getTipo() {
        return tipo;
    }

    public void setTipo(Integer tipo) {
        this.tipo = tipo;
    }

    public String getHorarioInicial() {
        return horarioInicial;
    }

    public void setHorarioInicial(String horarioInicial) {
        this.horarioInicial = horarioInicial;
    }

    public String getHorarioFinal() {
        return horarioFinal;
    }

    public void setHorarioFinal(String horarioFinal) {
        this.horarioFinal = horarioFinal;
    }

    public Integer getAlunoId() {
        return alunoId;
    }

    public void setAlunoId(Integer alunoId) {
        this.alunoId = alunoId;
    }

    public StatusAgendamentoEnum getStatus() {
        return status;
    }

    public void setStatus(StatusAgendamentoEnum status) {
        this.status = status;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public Integer getDuracao() {
        return duracao;
    }

    public void setDuracao(Integer duracao) {
        this.duracao = duracao;
    }

    public Integer getProfessor() {
        return professor;
    }

    public void setProfessor(Integer professor) {
        this.professor = professor;
    }

    public Boolean getHorarioAlterado() {
        return horarioAlterado;
    }

    public void setHorarioAlterado(Boolean horarioAlterado) {
        this.horarioAlterado = horarioAlterado;
    }

    public Boolean getTipoEvento() {
        return tipoEvento;
    }

    public void setTipoEvento(Boolean tipoEvento) {
        this.tipoEvento = tipoEvento;
    }

    public Integer getHorarioDisponibilidadeCod() {
        return horarioDisponibilidadeCod;
    }

    public void setHorarioDisponibilidadeCod(Integer horarioDisponibilidadeCod) {
        this.horarioDisponibilidadeCod = horarioDisponibilidadeCod;
    }

    public Boolean getSeg() {
        return seg;
    }

    public void setSeg(Boolean seg) {
        this.seg = seg;
    }

    public Boolean getTer() {
        return ter;
    }

    public void setTer(Boolean ter) {
        this.ter = ter;
    }

    public Boolean getQua() {
        return qua;
    }

    public void setQua(Boolean qua) {
        this.qua = qua;
    }

    public Boolean getQui() {
        return qui;
    }

    public void setQui(Boolean qui) {
        this.qui = qui;
    }

    public Boolean getSex() {
        return sex;
    }

    public void setSex(Boolean sex) {
        this.sex = sex;
    }

    public Boolean getSab() {
        return sab;
    }

    public void setSab(Boolean sab) {
        this.sab = sab;
    }

    public Boolean getDom() {
        return dom;
    }

    public void setDom(Boolean dom) {
        this.dom = dom;
    }

    public Integer getOpcSemanaMes() {
        return opcSemanaMes;
    }

    public void setOpcSemanaMes(Integer opcSemanaMes) {
        this.opcSemanaMes = opcSemanaMes;
    }

    public Integer getOpcPeriodicidade() {
        return opcPeriodicidade;
    }

    public void setOpcPeriodicidade(Integer opcPeriodicidade) {
        this.opcPeriodicidade = opcPeriodicidade;
    }

    public Integer getNrVezes() {
        return nrVezes;
    }

    public void setNrVezes(Integer nrVezes) {
        this.nrVezes = nrVezes;
    }

    public Date getDataFim() {
        return dataFim;
    }

    public void setDataFim(Date dataFim) {
        this.dataFim = dataFim;
    }

    public Integer getOpcSemanaOuMes() {
        return opcSemanaOuMes;
    }

    public void setOpcSemanaOuMes(Integer opcSemanaOuMes) {
        this.opcSemanaOuMes = opcSemanaOuMes;
    }
}
