package br.com.pacto.controller.json.locacao;

import br.com.pacto.controller.json.aluno.AlunoResponseTO;
import br.com.pacto.controller.json.turma.AmbienteDTO;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.ArrayList;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class ConfigAgendamentoLocacaoDTO {

    private String nomeLocacao;
    private List<AmbienteDTO> ambientes = new ArrayList<>();
    private AlunoResponseTO aluno;
    private AmbienteDTO ambiente;
    private String responsavel;
    private String produto;
    private Integer codigoProduto;
    private Double valorHora;
    private Double valorLocacao;
    private Double valorExtrasObrigatorios;
    private Double valorExtrasAdicionais;
    private Double valorTotal;
    private List<LocacaoHorarioTO> horariosAdicionados = new ArrayList<>();
    private List<LocacaoHorarioTO> horarios = new ArrayList<>();
    private List<LocacaoProdutoSugeridoTO> produtosObrigatorios = new ArrayList<>();
    private List<LocacaoProdutoSugeridoTO> produtosSugeridos = new ArrayList<>();
    private String data;
    private TipoHorarioLocacaoEnum tipoHorario;
    private boolean isCancelado;
    private String justificativa;
    private String dataCancelamento;
    private String usuarioCancelamento;
    private boolean isFinalizado;
    private Integer tempoMinimoMinutos;
    private Integer codigoPessoaClienteZW;
    private Boolean bloqueado;

    public String getNomeLocacao() {
        return nomeLocacao;
    }

    public void setNomeLocacao(String nomeLocacao) {
        this.nomeLocacao = nomeLocacao;
    }

    public List<AmbienteDTO> getAmbientes() {
        return ambientes;
    }

    public void setAmbientes(List<AmbienteDTO> ambientes) {
        this.ambientes = ambientes;
    }

    public Double getValorHora() {
        if(this.valorHora == null){
            this.valorHora = 0.0;
        }
        return valorHora;
    }

    public void setValorHora(Double valorHora) {
        this.valorHora = valorHora;
    }

    public Double getValorLocacao() {
        if(this.valorLocacao == null){
            this.valorLocacao = 0.0;
        }
        return valorLocacao;
    }

    public void setValorLocacao(Double valorLocacao) {
        this.valorLocacao = valorLocacao;
    }

    public Double getValorExtrasObrigatorios() {
        if(this.valorExtrasObrigatorios == null){
            this.valorExtrasObrigatorios = 0.0;
        }
        return valorExtrasObrigatorios;
    }

    public void setValorExtrasObrigatorios(Double valorExtrasObrigatorios) {
        this.valorExtrasObrigatorios = valorExtrasObrigatorios;
    }

    public Double getValorExtrasAdicionais() {
        return valorExtrasAdicionais;
    }

    public void setValorExtrasAdicionais(Double valorExtrasAdicionais) {
        this.valorExtrasAdicionais = valorExtrasAdicionais;
    }

    public Double getValorTotal() {
        return valorTotal;
    }

    public void setValorTotal(Double valorTotal) {
        this.valorTotal = valorTotal;
    }

    public List<LocacaoHorarioTO> getHorariosAdicionados() {
        return horariosAdicionados;
    }

    public void setHorariosAdicionados(List<LocacaoHorarioTO> horariosAdicionados) {
        this.horariosAdicionados = horariosAdicionados;
    }

    public List<LocacaoProdutoSugeridoTO> getProdutosObrigatorios() {
        return produtosObrigatorios;
    }

    public void setProdutosObrigatorios(List<LocacaoProdutoSugeridoTO> produtosObrigatorios) {
        this.produtosObrigatorios = produtosObrigatorios;
    }

    public String getProduto() {
        return produto;
    }

    public void setProduto(String produto) {
        this.produto = produto;
    }

    public List<LocacaoHorarioTO> getHorarios() {
        return horarios;
    }

    public void setHorarios(List<LocacaoHorarioTO> horarios) {
        this.horarios = horarios;
    }

    public List<LocacaoProdutoSugeridoTO> getProdutosSugeridos() {
        return produtosSugeridos;
    }

    public void setProdutosSugeridos(List<LocacaoProdutoSugeridoTO> produtosSugeridos) {
        this.produtosSugeridos = produtosSugeridos;
    }

    public AmbienteDTO getAmbiente() {
        return ambiente;
    }

    public void setAmbiente(AmbienteDTO ambiente) {
        this.ambiente = ambiente;
    }

    public AlunoResponseTO getAluno() {
        return aluno;
    }

    public void setAluno(AlunoResponseTO aluno) {
        this.aluno = aluno;
    }

    public String getResponsavel() {
        return responsavel;
    }

    public void setResponsavel(String responsavel) {
        this.responsavel = responsavel;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public TipoHorarioLocacaoEnum getTipoHorario() {
        return tipoHorario;
    }

    public void setTipoHorario(TipoHorarioLocacaoEnum tipoHorario) {
        this.tipoHorario = tipoHorario;
    }

    public boolean isCancelado() {
        return isCancelado;
    }

    public void setCancelado(boolean cancelado) {
        isCancelado = cancelado;
    }

    public String getJustificativa() {
        return justificativa;
    }

    public void setJustificativa(String justificativa) {
        this.justificativa = justificativa;
    }

    public String getDataCancelamento() {
        return dataCancelamento;
    }

    public void setDataCancelamento(String dataCancelamento) {
        this.dataCancelamento = dataCancelamento;
    }

    public String getUsuarioCancelamento() {
        return usuarioCancelamento;
    }

    public void setUsuarioCancelamento(String usuarioCancelamento) {
        this.usuarioCancelamento = usuarioCancelamento;
    }

    public boolean isFinalizado() {
        return isFinalizado;
    }

    public void setFinalizado(boolean finalizado) {
        isFinalizado = finalizado;
    }

    public Integer getTempoMinimoMinutos() {
        return tempoMinimoMinutos;
    }

    public void setTempoMinimoMinutos(Integer tempoMinimoMinutos) {
        this.tempoMinimoMinutos = tempoMinimoMinutos;
    }

    public Integer getCodigoProduto() {
        return codigoProduto;
    }

    public void setCodigoProduto(Integer codigoProduto) {
        this.codigoProduto = codigoProduto;
    }

    public Integer getCodigoPessoaClienteZW() {
        return codigoPessoaClienteZW;
    }

    public void setCodigoPessoaClienteZW(Integer codigoPessoaClienteZW) {
        this.codigoPessoaClienteZW = codigoPessoaClienteZW;
    }

    public Boolean getBloqueado() {
        return bloqueado;
    }

    public void setBloqueado(Boolean bloqueado) {
        this.bloqueado = bloqueado;
    }
}
