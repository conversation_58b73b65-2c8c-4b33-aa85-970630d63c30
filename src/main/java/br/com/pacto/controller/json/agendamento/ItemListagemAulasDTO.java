package br.com.pacto.controller.json.agendamento;

import br.com.pacto.controller.json.gestao.BITreinoAgendaDTO;
import br.com.pacto.objeto.Uteis;
import org.json.JSONObject;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;

public class ItemListagemAulasDTO {

    private String aula;
    private String modalidade;
    private Integer presencas;
    private String diaSemana;
    private Integer capacidade;
    private Integer ocupacaoAcumulada;
    private Double ocupacaoPercentual;
    private Double frequenciaPercentual;
    private String ocupacao;
    private String frequencia;
    private String horario;
    private String professor;
    private long dia;
    private String nomeAula;


    public ItemListagemAulasDTO(JSONObject json,
                                Map<Integer, String> modalidades) throws Exception{
        this.aula = json.getString("aula") + " - " + json.getString("horario");
        this.modalidade = modalidades.get(json.getInt("modalidade"));
        this.capacidade = json.optInt("capacidadeAcumulada");
        this.ocupacaoAcumulada = json.optInt("ocupacaoAcumulada");
        this.ocupacaoPercentual = (ocupacaoAcumulada.doubleValue()/capacidade.doubleValue()) * 100.0 ;
        this.ocupacao = this.ocupacaoPercentual.intValue() + "%";
        this.presencas = json.optInt("presentesAcumulada");
        this.frequenciaPercentual = (presencas.doubleValue()/ocupacaoAcumulada.doubleValue()) * 100.0 ;
        this.frequencia = this.frequenciaPercentual.intValue() +  "%";
        this.diaSemana = json.optString("diaSemana");
        this.nomeAula = json.getString("aula");
        this.horario = json.getString("horario");
        this.professor = Uteis.getNomeAbreviado(json.getString("nomePessoa"));
        this.dia = json.getLong("dia");
    }

    public String getAula() {
        return aula;
    }

    public void setAula(String aula) {
        this.aula = aula;
    }

    public String getModalidade() {
        return modalidade;
    }

    public void setModalidade(String modalidade) {
        this.modalidade = modalidade;
    }

    public String getOcupacao() {
        return ocupacao;
    }

    public void setOcupacao(String ocupacao) {
        this.ocupacao = ocupacao;
    }

    public Integer getPresencas() {
        return presencas;
    }

    public void setPresencas(Integer presencas) {
        this.presencas = presencas;
    }

    public String getFrequencia() {
        return frequencia;
    }

    public void setFrequencia(String frequencia) {
        this.frequencia = frequencia;
    }

    public Integer getCapacidade() {
        return capacidade;
    }

    public void setCapacidade(Integer capacidade) {
        this.capacidade = capacidade;
    }

    public Integer getOcupacaoAcumulada() {
        return ocupacaoAcumulada;
    }

    public void setOcupacaoAcumulada(Integer ocupacaoAcumulada) {
        this.ocupacaoAcumulada = ocupacaoAcumulada;
    }

    public Double getOcupacaoPercentual() {
        return ocupacaoPercentual;
    }

    public void setOcupacaoPercentual(Double ocupacaoPercentual) {
        this.ocupacaoPercentual = ocupacaoPercentual;
    }

    public Double getFrequenciaPercentual() {
        return frequenciaPercentual;
    }

    public void setFrequenciaPercentual(Double frequenciaPercentual) {
        this.frequenciaPercentual = frequenciaPercentual;
    }

    public String getDiaSemana() {
        return diaSemana;
    }

    public void setDiaSemana(String diaSemana) {
        this.diaSemana = diaSemana;
    }

    public String getHorario() {
        return horario;
    }

    public void setHorario(String horario) {
        this.horario = horario;
    }

    public String getProfessor() {
        return professor;
    }

    public void setProfessor(String professor) {
        this.professor = professor;
    }

    public long getDia() {
        return dia;
    }

    public void setDia(long dia) {
        this.dia = dia;
    }

    public String getNomeAula() { return nomeAula; }

    public void setNomeAula(String nomeAula) { this.nomeAula = nomeAula; }
}
