package br.com.pacto.controller.json.atividade;

import br.com.pacto.objeto.Aplicacao;
/*
    TEMA cadastrado no OAMD Principal e/ou na INFRA agora se torna obsoleto, fazendo necessário ter o tema cadastrado nessa classe.
    Mudança motivada pelo aumento da performance e custo-benefício.
 */
public class TemaAtividade {
    public static final String TEMA_DEFAULT = "/DEFAULT";
    public static final String CLUBWELL = "cc0d6e86020ed1705970f4d92de191b";
    public static final String TEMA_CLUBWELL = "/CLUBWELL";
    public static final String IRONBOX = "********************************";
    public static final String TEMA_IRONBOX = "/IRONBOX";

    public static String getURLBase(String ctx) {
        final String urlBase = Aplicacao.getProp(Aplicacao.urlMidias);
        if (Aplicacao.getProp(ctx, Aplicacao.urlBaseMidias) == null) {
            Aplicacao.putProp(ctx, Aplicacao.urlBaseMidias, urlBase);
        }
        //
        switch (ctx) {
            default: {
                return urlBase + TEMA_DEFAULT;
            }
            case CLUBWELL: {
                return urlBase + TEMA_CLUBWELL;
            }
            case IRONBOX: {
                return urlBase + TEMA_IRONBOX;
            }
        }
    }
}
