package br.com.pacto.controller.json.atividade;

import br.com.pacto.service.impl.atividade.SerieGeradaPorIADTO;
import br.com.pacto.controller.json.atividade.AtividadeAlternativaDTO;

import java.util.ArrayList;
import java.util.List;

public class AtividadeTreinoGeradoPorIADTO {
    private int sequencia;
    private AtividadeGeradaPorIADTO atividade;
    private String metodoExecucao;
    private List<SerieGeradaPorIADTO> series;
    private List<AtividadeAlternativaDTO> atividadeAlternativa = new ArrayList<>();
    private List<String> grupoMuscular = new ArrayList<>();

    public int getSequencia() {
        return sequencia;
    }

    public void setSequencia(int sequencia) {
        this.sequencia = sequencia;
    }

    public AtividadeGeradaPorIADTO getAtividade() {
        return atividade;
    }

    public void setAtividade(AtividadeGeradaPorIADTO atividade) {
        this.atividade = atividade;
    }

    public String getMetodoExecucao() {
        return metodoExecucao;
    }

    public void setMetodoExecucao(String metodoExecucao) {
        this.metodoExecucao = metodoExecucao;
    }

    public List<SerieGeradaPorIADTO> getSeries() {
        return series;
    }

    public void setSeries(List<SerieGeradaPorIADTO> series) {
        this.series = series;
    }

    public List<AtividadeAlternativaDTO> getAtividadeAlternativa() {
        return atividadeAlternativa;
    }

    public void setAtividadeAlternativa(List<AtividadeAlternativaDTO> atividadeAlternativa) {
        this.atividadeAlternativa = atividadeAlternativa;
    }

    public List<String> getGrupoMuscular() {
        return grupoMuscular;
    }

    public void setGrupoMuscular(List<String> grupoMuscular) {
        this.grupoMuscular = grupoMuscular;
    }
}
