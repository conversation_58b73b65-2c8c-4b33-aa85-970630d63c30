package br.com.pacto.controller.json.programa.read;

import java.sql.Timestamp;

/**
 *
 * <AUTHOR>
 */
public class TreinoRealizadoHistoricoAppJSON {
    private Integer cod;
    private String nomePrograma;
    private String nomeFicha;
    private String nomeProfessor;
    private Integer totalAtividades;
    private Integer totalAtividadesConcluidas;
    private String nivel;
    private Timestamp inicio;

    public TreinoRealizadoHistoricoAppJSON(Integer cod, String nomePrograma, String nomeFicha, String nomeProfessor,
                                           Integer totalAtividades, Integer totalAtividadesConcluidas, String nivel,
                                           Timestamp inicio) {
        this.cod = cod;
        this.nomePrograma = nomePrograma;
        this.nomeFicha = nomeFicha;
        this.nomeProfessor = nomeProfessor;
        this.totalAtividades = totalAtividades;
        this.totalAtividadesConcluidas = totalAtividadesConcluidas;
        this.nivel = nivel;
        this.inicio = inicio;
    }

    public Integer getCod() {
        return cod;
    }

    public void setCod(Integer cod) {
        this.cod = cod;
    }

    public String getNomePrograma() {
        return nomePrograma;
    }

    public void setNomePrograma(String nomePrograma) {
        this.nomePrograma = nomePrograma;
    }

    public String getNomeFicha() {
        return nomeFicha;
    }

    public void setNomeFicha(String nomeFicha) {
        this.nomeFicha = nomeFicha;
    }

    public String getNomeProfessor() {
        return nomeProfessor;
    }

    public void setNomeProfessor(String nomeProfessor) {
        this.nomeProfessor = nomeProfessor;
    }

    public Integer getTotalAtividades() {
        return totalAtividades;
    }

    public void setTotalAtividades(Integer totalAtividades) {
        this.totalAtividades = totalAtividades;
    }

    public Integer getTotalAtividadesConcluidas() {
        return totalAtividadesConcluidas;
    }

    public void setTotalAtividadesConcluidas(Integer totalAtividadesConcluidas) {
        this.totalAtividadesConcluidas = totalAtividadesConcluidas;
    }

    public String getNivel() {
        return nivel;
    }

    public void setNivel(String nivel) {
        this.nivel = nivel;
    }

    public Timestamp getInicio() {
        return inicio;
    }

    public void setInicio(Timestamp inicio) {
        this.inicio = inicio;
    }
}
