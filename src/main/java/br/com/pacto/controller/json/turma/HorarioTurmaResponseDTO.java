package br.com.pacto.controller.json.turma;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class HorarioTurmaResponseDTO {

    private Integer codigo;
    private String professor;
    private String identificadorTurma;
    private Integer professorId;
    private Integer turma;
    private String ambiente;
    private Integer ambienteId;
    private String horarioTurma;
    private String horaInicial;
    private String horaFinal;
    private String duracao;
    private String dia;
    private Integer maxAlunos;
    private Integer toleranciaMin;
    private Integer toleranciaAposMin;
    private String nivelTurma;
    private String situacao;
    private Integer nivelTurmaId;
    private Integer diaSemanaNumero;
    private Boolean horarioDisponivelVenda;
    private Boolean liberadoMarcacaoApp;
    private String dataEntrouTurma;
    private String dataSaiuTurma;
    private Integer nrAlunoEntraramPorReposicao;
    private Integer nrAlunoSairamPorReposicao;
    private Integer limiteVagasAgregados;
    private List<HorarioCapacidadeCategoriaDTO> horarioCapacidadeCategoria;
    private Integer qtdeMaximaAlunoExperimental;

    public HorarioTurmaResponseDTO() {
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getProfessor() {
        return professor;
    }

    public void setProfessor(String professor) {
        this.professor = professor;
    }

    public String getAmbiente() {
        return ambiente;
    }

    public void setAmbiente(String ambiente) {
        this.ambiente = ambiente;
    }

    public String getHorarioTurma() {
        return horarioTurma;
    }

    public void setHorarioTurma(String horarioTurma) {
        this.horarioTurma = horarioTurma;
    }

    public String getDuracao() {
        return duracao;
    }

    public void setDuracao(String duracao) {
        this.duracao = duracao;
    }

    public String getDia() {
        return dia;
    }

    public void setDia(String dia) {
        this.dia = dia;
    }

    public Integer getMaxAlunos() {
        return maxAlunos;
    }

    public void setMaxAlunos(Integer maxAlunos) {
        this.maxAlunos = maxAlunos;
    }

    public Integer getToleranciaMin() {
        return toleranciaMin;
    }

    public void setToleranciaMin(Integer toleranciaMin) {
        this.toleranciaMin = toleranciaMin;
    }

    public Integer getToleranciaAposMin() {
        return toleranciaAposMin;
    }

    public void setToleranciaAposMin(Integer toleranciaAposMin) {
        this.toleranciaAposMin = toleranciaAposMin;
    }

    public String getNivelTurma() {
        return nivelTurma;
    }

    public void setNivelTurma(String nivelTurma) {
        this.nivelTurma = nivelTurma;
    }

    public Integer getProfessorId() {
        return professorId;
    }

    public void setProfessorId(Integer professorId) {
        this.professorId = professorId;
    }

    public Integer getAmbienteId() {
        return ambienteId;
    }

    public void setAmbienteId(Integer ambienteId) {
        this.ambienteId = ambienteId;
    }

    public Integer getNivelTurmaId() {
        return nivelTurmaId;
    }

    public void setNivelTurmaId(Integer nivelTurmaId) {
        this.nivelTurmaId = nivelTurmaId;
    }

    public String getHoraInicial() {
        return horaInicial;
    }

    public void setHoraInicial(String horaInicial) {
        this.horaInicial = horaInicial;
    }

    public String getHoraFinal() {
        return horaFinal;
    }

    public void setHoraFinal(String horaFinal) {
        this.horaFinal = horaFinal;
    }

    public Boolean getHorarioDisponivelVenda() {
        if (this.horarioDisponivelVenda == null) {
            return Boolean.FALSE;
        }
        return horarioDisponivelVenda;
    }

    public void setHorarioDisponivelVenda(Boolean horarioDisponivelVenda) {
        this.horarioDisponivelVenda = horarioDisponivelVenda;
    }

    public Boolean getLiberadoMarcacaoApp() {
        if (this.liberadoMarcacaoApp == null) {
            return Boolean.FALSE;
        }
        return liberadoMarcacaoApp;
    }

    public void setLiberadoMarcacaoApp(Boolean liberadoMarcacaoApp) {
        this.liberadoMarcacaoApp = liberadoMarcacaoApp;
    }

    public String getIdentificadorTurma() {
        return identificadorTurma;
    }

    public void setIdentificadorTurma(String identificadorTurma) {
        this.identificadorTurma = identificadorTurma;
    }

    public Integer getTurma() {
        return turma;
    }

    public void setTurma(Integer turma) {
        this.turma = turma;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public Integer getDiaSemanaNumero() {
        return diaSemanaNumero;
    }

    public void setDiaSemanaNumero(Integer diaSemanaNumero) {
        this.diaSemanaNumero = diaSemanaNumero;
    }

    public String getDataEntrouTurma() {
        return dataEntrouTurma;
    }

    public void setDataEntrouTurma(String dataEntrouTurma) {
        this.dataEntrouTurma = dataEntrouTurma;
    }

    public String getDataSaiuTurma() {
        return dataSaiuTurma;
    }

    public void setDataSaiuTurma(String dataSaiuTurma) {
        this.dataSaiuTurma = dataSaiuTurma;
    }

    public Integer getNrAlunoEntraramPorReposicao() {
        return nrAlunoEntraramPorReposicao;
    }

    public void setNrAlunoEntraramPorReposicao(Integer nrAlunoEntraramPorReposicao) {
        this.nrAlunoEntraramPorReposicao = nrAlunoEntraramPorReposicao;
    }

    public Integer getNrAlunoSairamPorReposicao() {
        return nrAlunoSairamPorReposicao;
    }

    public void setNrAlunoSairamPorReposicao(Integer nrAlunoSairamPorReposicao) {
        this.nrAlunoSairamPorReposicao = nrAlunoSairamPorReposicao;
    }

    public String getDiaSemana_Apresentar() {
        if (this.dia == null) {
            this.dia = "";
        }
        if (this.dia.equals("DM")) {
            return "Domingo";
        }
        if (this.dia.equals("SG")) {
            return "Segunda";
        }
        if (this.dia.equals("TR")) {
            return "Terça";
        }
        if (this.dia.equals("QA")) {
            return "Quarta";
        }
        if (this.dia.equals("QI")) {
            return "Quinta";
        }
        if (this.dia.equals("SX")) {
            return "Sexta";
        }
        if (this.dia.equals("SB")) {
            return "Sábado";
        }
        return (this.dia);
    }

    public Integer getLimiteVagasAgregados() {
        return limiteVagasAgregados;
    }

    public void setLimiteVagasAgregados(Integer limiteVagasAgregados) {
        this.limiteVagasAgregados = limiteVagasAgregados;
    }


    public List<HorarioCapacidadeCategoriaDTO> getHorarioCapacidadeCategoria() {
        return horarioCapacidadeCategoria;
    }

    public void setHorarioCapacidadeCategoria(List<HorarioCapacidadeCategoriaDTO> horarioCapacidadeCategoria) {
        this.horarioCapacidadeCategoria = horarioCapacidadeCategoria;
    }

    public Integer getQtdeMaximaAlunoExperimental() {
        return qtdeMaximaAlunoExperimental;
    }

    public void setQtdeMaximaAlunoExperimental(Integer qtdeMaximaAlunoExperimental) {
        this.qtdeMaximaAlunoExperimental = qtdeMaximaAlunoExperimental;
    }
}
