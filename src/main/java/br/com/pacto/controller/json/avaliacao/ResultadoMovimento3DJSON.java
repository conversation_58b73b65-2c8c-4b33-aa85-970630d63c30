package br.com.pacto.controller.json.avaliacao;

import br.com.pacto.bean.anamnese.Movimento3D;
import br.com.pacto.util.ViewUtils;

public class ResultadoMovimento3DJSON {

    private String descricao;
    private Integer esquerda;
    private Integer direita;

    public ResultadoMovimento3DJSON(Movimento3D m3d, ViewUtils vu) {
        this.descricao = vu.getLabel(m3d.getMovimento().name());
        this.esquerda = m3d.getEsquerda();
        this.direita = m3d.getDireita();
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Integer getEsquerda() {
        return esquerda;
    }

    public void setEsquerda(Integer esquerda) {
        this.esquerda = esquerda;
    }

    public Integer getDireita() {
        return direita;
    }

    public void setDireita(Integer direita) {
        this.direita = direita;
    }
}
