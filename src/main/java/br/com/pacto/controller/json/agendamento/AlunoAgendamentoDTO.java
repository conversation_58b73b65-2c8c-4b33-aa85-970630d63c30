package br.com.pacto.controller.json.agendamento;

import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.pessoa.Telefone;
import br.com.pacto.bean.pessoa.TipoTelefoneEnum;
import br.com.pacto.controller.json.aluno.SituacaoAlunoEnum;
import br.com.pacto.controller.json.aluno.SituacaoContratoZWEnum;
import br.com.pacto.controller.json.aluno.TelefoneDTO;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.util.UteisValidacao;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by paulo 20/08/2019
 */
public class AlunoAgendamentoDTO {
    private Integer id;
    private Integer matriculaZW;
    private String nome;
    private String imageUri;
    private SituacaoAlunoEnum situacaoAluno;
    private SituacaoContratoZWEnum situacaoContrato;
    private List<TelefoneDTO> telefones;

    public AlunoAgendamentoDTO(ClienteSintetico cs, Boolean treinoIndependente) {
        this.id = cs.getCodigo();
        this.matriculaZW = cs.getMatricula();
        this.nome = cs.getNome();
        this.imageUri = cs.getUrlFoto();
        this.situacaoAluno = SituacaoAlunoEnum.getInstance(cs.getSituacao());
        this.situacaoContrato = SituacaoContratoZWEnum.getInstance(cs.getSituacaoContrato());
        this.telefones = new ArrayList<>();
        if (treinoIndependente) {
            for (Telefone telefone : cs.getPessoa().getTelefones()) {
                TelefoneDTO telefoneDTO = new TelefoneDTO();
                telefoneDTO.setNumero(telefone.getTelefone());
                if (Uteis.validarTelefoneCelular(telefone.getTelefone())) {
                    telefoneDTO.setTipo(TipoTelefoneEnum.CELULAR);
                } else {
                    telefoneDTO.setTipo(TipoTelefoneEnum.FIXO);
                }
                this.telefones.add(telefoneDTO);
            }
        } else {
            for (String telefone : cs.getListaTelefones()) {
                TelefoneDTO telefoneDTO = new TelefoneDTO();
                telefoneDTO.setNumero(telefone);
                if (telefone.length() == 13) {
                    telefoneDTO.setTipo(TipoTelefoneEnum.CELULAR);
                } else {
                    telefoneDTO.setTipo(TipoTelefoneEnum.FIXO);
                }
                this.telefones.add(telefoneDTO);
            }
        }
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getMatriculaZW() {
        return matriculaZW;
    }

    public void setMatriculaZW(Integer matriculaZW) {
        this.matriculaZW = matriculaZW;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getImageUri() {
        return imageUri;
    }

    public void setImageUri(String imageUri) {
        this.imageUri = imageUri;
    }

    public SituacaoAlunoEnum getSituacaoAluno() {
        return situacaoAluno;
    }

    public void setSituacaoAluno(SituacaoAlunoEnum situacaoAluno) {
        this.situacaoAluno = situacaoAluno;
    }

    public SituacaoContratoZWEnum getSituacaoContrato() {
        return situacaoContrato;
    }

    public void setSituacaoContrato(SituacaoContratoZWEnum situacaoContrato) {
        this.situacaoContrato = situacaoContrato;
    }

    public List<TelefoneDTO> gettelefones() {
        if (telefones == null) {
            telefones = new ArrayList<>();
        }
        return telefones;
    }

    public void settelefones(List<TelefoneDTO> telefones) {
        this.telefones = telefones;
    }
}
