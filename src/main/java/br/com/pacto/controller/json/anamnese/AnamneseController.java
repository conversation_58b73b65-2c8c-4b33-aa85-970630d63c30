package br.com.pacto.controller.json.anamnese;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.anamnese.AnamneseTO;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.controller.json.avaliacao.FiltrosJSON;
import br.com.pacto.security.aspecto.Permissao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.exception.ValidacaoException;
import br.com.pacto.service.intf.anamnese.AnamneseService;
import org.json.JSONException;
import org.json.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by ulisses on 03/08/2018.
 */
@Controller
@RequestMapping("/psec/anamneses")
public class AnamneseController {

    private final AnamneseService anamneseService;

    @Autowired
    public AnamneseController(AnamneseService anamneseService){
        Assert.notNull(anamneseService, "O serviço de anamnese não foi injetado corretamente");
        this.anamneseService = anamneseService;
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.ANAMNESE)
    @RequestMapping(value = "", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> consultarAnamneses(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                                  PaginadorDTO paginadorDTO) throws JSONException {
        try {
            FiltrosJSON filtroAnamneseJSON = new FiltrosJSON(filtros);
            return ResponseEntityFactory.ok(anamneseService.consultarAnamneses(filtroAnamneseJSON, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(AnamneseController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar os anamneses", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.ANAMNESE)
    @RequestMapping(value = "/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> consultarAnamnese(@PathVariable("id") final Integer id) {
        try {
            return ResponseEntityFactory.ok(anamneseService.consultarAnamnese(id));
        } catch (ServiceException e) {
            Logger.getLogger(AnamneseController.class.getName()).log(Level.SEVERE, "Erro ao tentar consultar a anamnese", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.ANAMNESE)
    @RequestMapping(value = "", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> incluirAnamneses(@RequestBody AnamneseTO anamneseTO) {
        try {
            return ResponseEntityFactory.ok(anamneseService.inserir(anamneseTO));
        } catch (ServiceException e) {
            Logger.getLogger(AnamneseController.class.getName()).log(Level.SEVERE, "Erro ao tentar incluir anamnese", e);
            return ResponseEntityFactory.erroConhecido(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.ANAMNESE)
    @RequestMapping(value = "/{id}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alterarAnamneses(@PathVariable("id") final Integer id,
                                                                @RequestBody AnamneseTO anamneseTO) {
        try {
            anamneseTO.setId(id);
            return ResponseEntityFactory.ok(anamneseService.alterar(anamneseTO));

        } catch (ServiceException e) {
            Logger.getLogger(AnamneseController.class.getName()).log(Level.SEVERE, "Erro ao tentar alterar anamnese", e);
            if (StringUtils.isBlank(e.getChaveExcecao()) && e.getChaveExcecao().equalsIgnoreCase("validacao_anamnese_ja_existe")) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroEstaSendoUtilizado(e.getChaveExcecao(), e.getMessage());
            }
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.ANAMNESE)
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> excluirAnamnese(@PathVariable("id") final Integer id){
        try {
            anamneseService.excluir(id);
            return ResponseEntityFactory.ok(true);
        } catch (ValidacaoException e) {
            return ResponseEntityFactory.erroConhecido(e.getMessage(), e.getMessage());
        } catch (Exception e) {
            Logger.getLogger(AnamneseController.class.getName()).log(Level.SEVERE, "Erro ao tentar excluir anamnese", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.ANAMNESE)
    @RequestMapping(value = "/todas", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> obterTodas(){
        try{
            return  ResponseEntityFactory.ok(anamneseService.obterTodas(false, false));
        }catch (Exception e) {
            Logger.getLogger(AnamneseController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter todas anamnese", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.ANAMNESE)
    @RequestMapping(value = "/todas-integradas", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> obterTodasIntegradas(){
        try{
            return  ResponseEntityFactory.ok(anamneseService.obterTodas(true, false));
        }catch (Exception e) {
            Logger.getLogger(AnamneseController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter todas anamneses integradas", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.ANAMNESE)
    @RequestMapping(value = "/todas-integradas-ativas", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> obterTodasIntegradasAtivas(){
        try{
            return  ResponseEntityFactory.ok(anamneseService.obterTodas(true, true));
        }catch (Exception e) {
            Logger.getLogger(AnamneseController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter todas anamneses integradas", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

}
