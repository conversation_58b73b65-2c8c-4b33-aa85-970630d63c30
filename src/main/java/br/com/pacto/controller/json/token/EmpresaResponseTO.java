package br.com.pacto.controller.json.token;

import br.com.pacto.bean.empresa.Empresa;
import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * Created by <PERSON><PERSON> on 28/09/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EmpresaResponseTO {

    private Integer id;
    private String nome;

    public EmpresaResponseTO(Empresa empresa, Boolean treinoIndependente) {
        this.id = treinoIndependente ? empresa.getCodigo() : empresa.getCodZW();
        this.nome = empresa.getNome();
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }
}
