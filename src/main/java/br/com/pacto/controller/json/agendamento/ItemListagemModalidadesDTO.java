package br.com.pacto.controller.json.agendamento;

import org.json.JSONObject;

public class ItemListagemModalidadesDTO {

    private String modalidade;
    private String professores;
    private Integer aulas;
    private Integer presencas;
    private Integer capacidade;
    private Integer ocupacaoAcumulada;
    private Double ocupacaoPercentual;
    private Double frequenciaPercentual;
    private String ocupacao;
    private String frequencia;

    public ItemListagemModalidadesDTO(JSONObject object) throws Exception {
        this.modalidade = object.getString("nome");
        this.professores = object.getString("professores");
        this.aulas = object.getInt("aulas");
        this.capacidade = object.optInt("capacidadeAcumulada");
        this.ocupacaoAcumulada = object.optInt("ocupacaoAcumulada");
        this.ocupacaoPercentual = (ocupacaoAcumulada.doubleValue()/capacidade.doubleValue()) * 100.0 ;
        this.ocupacao = this.ocupacaoPercentual.intValue() + "%";
        this.presencas = object.optInt("presentesAcumulada");
        this.frequenciaPercentual = (presencas.doubleValue()/ocupacaoAcumulada.doubleValue()) * 100.0 ;
        this.frequencia = this.frequenciaPercentual.intValue() +  "%";
    }

    public String getModalidade() {
        return modalidade;
    }

    public void setModalidade(String modalidade) {
        this.modalidade = modalidade;
    }

    public Integer getCapacidade() {
        return capacidade;
    }

    public void setCapacidade(Integer capacidade) {
        this.capacidade = capacidade;
    }

    public String getProfessores() {
        return professores;
    }

    public void setProfessores(String professores) {
        this.professores = professores;
    }

    public Integer getAulas() {
        return aulas;
    }

    public void setAulas(Integer aulas) {
        this.aulas = aulas;
    }

    public Integer getPresencas() {
        return presencas;
    }

    public void setPresencas(Integer presencas) {
        this.presencas = presencas;
    }

    public Integer getOcupacaoAcumulada() {
        return ocupacaoAcumulada;
    }

    public void setOcupacaoAcumulada(Integer ocupacaoAcumulada) {
        this.ocupacaoAcumulada = ocupacaoAcumulada;
    }

    public Double getOcupacaoPercentual() {
        return ocupacaoPercentual;
    }

    public void setOcupacaoPercentual(Double ocupacaoPercentual) {
        this.ocupacaoPercentual = ocupacaoPercentual;
    }

    public Double getFrequenciaPercentual() {
        return frequenciaPercentual;
    }

    public void setFrequenciaPercentual(Double frequenciaPercentual) {
        this.frequenciaPercentual = frequenciaPercentual;
    }

    public String getOcupacao() {
        return ocupacao;
    }

    public void setOcupacao(String ocupacao) {
        this.ocupacao = ocupacao;
    }

    public String getFrequencia() {
        return frequencia;
    }

    public void setFrequencia(String frequencia) {
        this.frequencia = frequencia;
    }
}
