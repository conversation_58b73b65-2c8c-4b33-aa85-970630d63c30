package br.com.pacto.controller.json.atividade;

import br.com.pacto.controller.json.base.SuperJSON;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;


public class FiltroFichaPredefinidaJSON extends SuperJSON {

    private String parametro;
    private Boolean nome = false;
    private String situacaoList;
    private Boolean ativo;

    public FiltroFichaPredefinidaJSON(JSONObject filters) throws JSONException {
        this.parametro = filters.optString("quicksearchValue");

        JSONArray colunasVisiveis = filters.optJSONArray("quicksearchFields");

        if (colunasVisiveis != null) {
            for (int i = 0; i < colunasVisiveis.length(); i++) {
                if (colunasVisiveis.get(i).equals("nome")) {
                    this.nome = true;
                }
            }

            JSONArray situacao = filters.optJSONArray("situacaoFichaPreDefinida");
            if (situacao != null) {
                for (int i = 0; i < situacao.length(); i++) {
                    situacaoList = situacao.getString(i);
                    if (situacao.length() == 1) {
                        if (situacaoList.equals("false")) {
                            setAtivo(false);
                        } else {
                            setAtivo(true);
                        }
                    }
                }
            }

        }
    }

    public String getParametro() {
        return parametro;
    }

    public void setParametro(String parametro) {
        this.parametro = parametro;
    }

    public Boolean getNome() {
        return nome;
    }

    public void setNome(Boolean nome) {
        this.nome = nome;
    }

    public String getSituacaoList() {
        return situacaoList;
    }

    public void setSituacaoList(String situacaoList) {
        this.situacaoList = situacaoList;
    }

    public Boolean getAtivo() {
        return ativo;
    }

    public void setAtivo(Boolean ativo) {
        this.ativo = ativo;
    }
}
