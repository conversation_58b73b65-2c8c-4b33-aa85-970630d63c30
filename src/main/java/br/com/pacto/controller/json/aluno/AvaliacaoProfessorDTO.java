package br.com.pacto.controller.json.aluno;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.Date;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class AvaliacaoProfessorDTO {
    private Integer codigo;
    private Date dataRegistro;
    private Date dataUpdate;
    private Integer codUsuario;
    private String clienteUsername;
    private Integer professorCodigo;
    private String professorNome;
    private Integer notaAvaliada;
    private Integer empresa;
    private String comentario;

    public String getComentario() {
        return comentario;
    }

    public void setComentario(String comentario) {
        this.comentario = comentario;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Date getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(Date dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public Date getDataUpdate() {
        return dataUpdate;
    }

    public void setDataUpdate(Date dataUpdate) {
        this.dataUpdate = dataUpdate;
    }

    public Integer getCodUsuario() {
        return codUsuario;
    }

    public void setCodUsuario(Integer codUsuario) {
        this.codUsuario = codUsuario;
    }

    public String getClienteUsername() {
        return clienteUsername;
    }

    public void setClienteUsername(String clienteUsername) {
        this.clienteUsername = clienteUsername;
    }

    public Integer getProfessorCodigo() {
        return professorCodigo;
    }

    public void setProfessorCodigo(Integer professorCodigo) {
        this.professorCodigo = professorCodigo;
    }

    public String getProfessorNome() {
        return professorNome;
    }

    public void setProfessorNome(String professorNome) {
        this.professorNome = professorNome;
    }

    public Integer getNotaAvaliada() {
        return notaAvaliada;
    }

    public void setNotaAvaliada(Integer notaAvaliada) {
        this.notaAvaliada = notaAvaliada;
    }
}
