package br.com.pacto.controller.json.gympass;

import br.com.pacto.bean.gympass.ConfigGymPass;
import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 23/03/2020
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ConfigGymPassDTO {

    private Integer codigo;
    private Integer empresa;
    private String nome;
    private String codigoGymPass;
    private boolean usarGymPassBooking;
    private boolean permitirWod;
    private Integer limiteDeAcessosPorDia;
    private Integer limiteDeAulasPorDia;

    public ConfigGymPassDTO() {
    }

    public ConfigGymPassDTO(ConfigGymPass config) {
        this.codigo = config.getCodigo();
        this.empresa = config.getEmpresa().getCodigo();
        this.nome = config.getEmpresa().getNome();
        this.codigoGymPass = config.getCodigoGymPass();
        this.usarGymPassBooking = config.isUsarGymPassBooking();
        this.limiteDeAcessosPorDia = config.getLimiteDeAcessosPorDia();
        this.limiteDeAulasPorDia = config.getLimiteDeAulasPorDia();
        this.permitirWod = config.getPermitirWod();
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getCodigoGymPass() {
        return codigoGymPass;
    }

    public void setCodigoGymPass(String codigoGymPass) {
        this.codigoGymPass = codigoGymPass;
    }

    public boolean isUsarGymPassBooking() {
        return usarGymPassBooking;
    }

    public void setUsarGymPassBooking(boolean usarGymPassBooking) {
        this.usarGymPassBooking = usarGymPassBooking;
    }

    public Integer getLimiteDeAcessosPorDia() {
        return limiteDeAcessosPorDia;
    }

    public void setLimiteDeAcessosPorDia(Integer limiteDeAcessosPorDia) {
        this.limiteDeAcessosPorDia = limiteDeAcessosPorDia;
    }

    public Integer getLimiteDeAulasPorDia() {
        return limiteDeAulasPorDia;
    }

    public void setLimiteDeAulasPorDia(Integer limiteDeAulasPorDia) {
        this.limiteDeAulasPorDia = limiteDeAulasPorDia;
    }

    public boolean isPermitirWod() {
        return permitirWod;
    }

    public void setPermitirWod(boolean permitirWod) {
        this.permitirWod = permitirWod;
    }
}
