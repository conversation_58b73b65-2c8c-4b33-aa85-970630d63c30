package br.com.pacto.controller.json.avaliacao;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.avaliacao.AvaliacaoIntegradaDTOUptade;
import br.com.pacto.controller.json.base.SuperControle;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.intf.avaliacao.AvaliacaoIntegradaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import java.util.logging.Level;
import java.util.logging.Logger;

@Controller
@RequestMapping("/psec/avaliacao-integrada")
public class AvaliacaoIntegradaController extends SuperControle {

    private final SessaoService sessaoService;
    private final AvaliacaoIntegradaService avaliacaoIntegradaService;

    @Autowired
    private ServletContext sc;

    @Override
    public void setServletContext(ServletContext servletContext) {
        this.sc = servletContext;
    }

    @Autowired
    public AvaliacaoIntegradaController(SessaoService sessaoService,
                                        AvaliacaoIntegradaService avaliacaoIntegradaService) {
        this.sessaoService = sessaoService;
        this.avaliacaoIntegradaService = avaliacaoIntegradaService;
    }

    @ResponseBody
    @RequestMapping(value = "/aluno/{id}/all-av-integradas", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> allByAluno(@PathVariable("id") final Integer id) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            return ResponseEntityFactory.ok(avaliacaoIntegradaService.allByAluno(ctx, id));
        } catch (Exception e) {
            Logger.getLogger(AvaliacaoFisicaController.class.getName()).log(Level.SEVERE, "Não foi possível obter as avaliações integradas", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> findAvaliacaoIntegradaById(@PathVariable("id") final Integer id) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            return ResponseEntityFactory.ok(avaliacaoIntegradaService.findAvIntegradaById(ctx, id));
        } catch (Exception e) {
            Logger.getLogger(AvaliacaoFisicaController.class.getName()).log(Level.SEVERE, "Não foi possível obter a avaliação integrada", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/aluno/{id}", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> saveOrUpdate(@PathVariable(value = "id") Integer id,
                                                    @RequestBody AvaliacaoIntegradaDTOUptade avaliacaoIntegrada) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            Integer idUsuario = sessaoService.getUsuarioAtual().getId();
            return ResponseEntityFactory.ok(avaliacaoIntegradaService.saveOrUpdate(ctx, id, idUsuario, avaliacaoIntegrada));
        } catch (Exception e) {
            Logger.getLogger(AvaliacaoFisicaController.class.getName()).log(Level.SEVERE, "Não foi possível salvar a avaliaçõe integrada", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> delete(@PathVariable(value = "id") Integer id) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            avaliacaoIntegradaService.delete(ctx, id);
            return ResponseEntityFactory.ok(true);
        } catch (Exception e) {
            Logger.getLogger(AvaliacaoFisicaController.class.getName()).log(Level.SEVERE, "Não foi possível excluir a avaliaçõe integrada", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/{id}/print-individual-av-integrada", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> printIndividualAvIntegrada(@PathVariable("id") final Integer id,
                                                                          HttpServletRequest request) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            Integer idUsuario = sessaoService.getUsuarioAtual().getId();
            return ResponseEntityFactory.ok(avaliacaoIntegradaService.printIndividualAvIntegrada(ctx, id, idUsuario, request));
        } catch (Exception e) {
            Logger.getLogger(AvaliacaoFisicaController.class.getName()).log(Level.SEVERE, "Não foi possível gerar o pdf da avaliação integrada individual", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/{codigos}/print-group-av-integrada", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> printGroupAvIntegrada(@PathVariable("codigos") final String codigos,
                                                                     HttpServletRequest request) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            Integer idUsuario = sessaoService.getUsuarioAtual().getId();
            return ResponseEntityFactory.ok(avaliacaoIntegradaService.printGroupAvIntegrada(ctx, idUsuario, codigos, request));
        } catch (Exception e) {
            Logger.getLogger(AvaliacaoFisicaController.class.getName()).log(Level.SEVERE, "Não foi possível gerar o pdf da avaliação integrada em grupo", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/aluno/{idAluno}/{idAvaliacao}/send-individual-av-integrada", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> sendIndividualAvIntegrada(@PathVariable(value = "idAluno") Integer idAluno,
                                                                         @PathVariable(value = "idAvaliacao") Integer idAvaliacao,
                                                                         HttpServletRequest request) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            Integer idUsuario = sessaoService.getUsuarioAtual().getId();
            avaliacaoIntegradaService.sendIndividualAvIntegrada(ctx, idAluno, idAvaliacao, idUsuario, request, sc);
            return ResponseEntityFactory.ok(true);
        } catch (Exception e) {
            Logger.getLogger(AvaliacaoFisicaController.class.getName()).log(Level.SEVERE, "Não foi possível enviar a avaliação integrada por email", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/aluno/{idAluno}/{idAvaliacoes}/send-grupo-av-integrada", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> sendGrupoAvIntegrada(@PathVariable(value = "idAluno") Integer idAluno,
                                                                    @PathVariable(value = "idAvaliacoes") String idAvaliacoes,
                                                                    HttpServletRequest request) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            Integer idUsuario = sessaoService.getUsuarioAtual().getId();
            avaliacaoIntegradaService.sendGrupoAvIntegrada(ctx, idAluno, idAvaliacoes, idUsuario, request, sc);
            return ResponseEntityFactory.ok(true);
        } catch (Exception e) {
            Logger.getLogger(AvaliacaoFisicaController.class.getName()).log(Level.SEVERE, "Não foi possível enviar as avaliações integradas por email", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

}
