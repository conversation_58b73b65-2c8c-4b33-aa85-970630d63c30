package br.com.pacto.controller.json.graduacao;

import java.util.List;

public class ImpressaoAtividadeDTO {

    private Integer id;
    private String nome;
    private String descricao;
    private Boolean possuiSubAtividade;
    private String imageUri;
    private List<ImpressaoSubAtividadeDTO> subAtividades;
    private String resposta;
    private String observacao;

    public ImpressaoAtividadeDTO() { }

    public ImpressaoAtividadeDTO(Integer id, String nome, Boolean possuiSubAtividade, List<ImpressaoSubAtividadeDTO> subAtividades, String resposta, String observacao) {
        this.id = id;
        this.nome = nome;
        this.possuiSubAtividade = possuiSubAtividade;
        this.subAtividades = subAtividades;
        this.resposta = resposta;
        this.observacao = observacao;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Boolean getPossuiSubAtividade() {
        return possuiSubAtividade;
    }

    public void setPossuiSubAtividade(Boolean possuiSubAtividade) {
        this.possuiSubAtividade = possuiSubAtividade;
    }

    public List<ImpressaoSubAtividadeDTO> getSubAtividades() {
        return subAtividades;
    }

    public void setSubAtividades(List<ImpressaoSubAtividadeDTO> subAtividades) {
        this.subAtividades = subAtividades;
    }

    public String getResposta() {
        return resposta;
    }

    public void setResposta(String resposta) {
        this.resposta = resposta;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public String getImageUri() {
        return imageUri;
    }

    public void setImageUri(String imageUri) {
        this.imageUri = imageUri;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

}
