package br.com.pacto.controller.json.locacao;

import br.com.pacto.bean.usuario.Usuario;
import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class UsuarioTO {

    private Integer codigo;
    private String nome;

    public UsuarioTO() {}

    public UsuarioTO(Usuario usuario) {
        this.codigo = usuario.getCodigo();
        this.nome = usuario.getProfessor().getNome();
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }
}
