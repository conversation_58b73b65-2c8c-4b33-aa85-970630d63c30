package br.com.pacto.controller.json.avaliacao;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * Created by paulo on 01/07/2019.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AvaliadorDTO {

    public AvaliadorDTO(Integer id, String nome) {
        this.id = id;
        this.nome = nome;
    }

    private Integer id;
    private String nome;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }
}
