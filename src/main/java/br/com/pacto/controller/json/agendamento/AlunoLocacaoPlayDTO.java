package br.com.pacto.controller.json.agendamento;

import br.com.pacto.controller.json.aluno.SituacaoAlunoEnum;

public class AlunoLocacaoPlayDTO {

    private Integer id;
    private Integer matriculaZW;
    private String nome;
    private String unidade;
    private String imageUri;
    private SituacaoAlunoEnum situacaoAluno;
    private Boolean checkin;

    public AlunoLocacaoPlayDTO() {
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getMatriculaZW() {
        return matriculaZW;
    }

    public void setMatriculaZW(Integer matriculaZW) {
        this.matriculaZW = matriculaZW;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getUnidade() {
        return unidade;
    }

    public void setUnidade(String unidade) {
        this.unidade = unidade;
    }

    public String getImageUri() {
        return imageUri;
    }

    public void setImageUri(String imageUri) {
        this.imageUri = imageUri;
    }

    public SituacaoAlunoEnum getSituacaoAluno() {
        return situacaoAluno;
    }

    public void setSituacaoAluno(SituacaoAlunoEnum situacaoAluno) {
        this.situacaoAluno = situacaoAluno;
    }

    public Boolean getCheckin() {
        return checkin;
    }

    public void setCheckin(Boolean checkin) {
        this.checkin = checkin;
    }
}
