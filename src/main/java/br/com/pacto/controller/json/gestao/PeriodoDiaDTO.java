package br.com.pacto.controller.json.gestao;

import br.com.pacto.bean.bi.DiasSemanaDashboardBI;

import java.util.List;

public class PeriodoDiaDTO {
    private Integer manha;
    private Integer tarde;
    private Integer noite;
    private Integer total;
    private Integer manhaApp;
    private Integer tardeApp;
    private Integer noiteApp;
    private Integer totalApp;

    public PeriodoDiaDTO() {
    }

    public PeriodoDiaDTO(DiasSemanaDashboardBI diasSemanaDashboardBI) {
        this.manha = diasSemanaDashboardBI.getTotalManha();
        this.tarde = diasSemanaDashboardBI.getTotalTarde();
        this.noite = diasSemanaDashboardBI.getTotalNoite();
        this.total = this.manha + this.tarde + this.noite;
    }

    public PeriodoDiaDTO(DiasSemanaDashboardBI diasSemanaDashboardBI, TreinoRealizadoAppDTO treinoRealizadoAppDTO) {
        this.manha = diasSemanaDashboardBI.getTotalManha();
        this.tarde = diasSemanaDashboardBI.getTotalTarde();
        this.noite = diasSemanaDashboardBI.getTotalNoite();
        this.total = this.manha + this.tarde + this.noite;

        this.manhaApp = treinoRealizadoAppDTO.getPeriodo().getManhaApp();
        this.tardeApp = treinoRealizadoAppDTO.getPeriodo().getTardeApp();
        this.noiteApp = treinoRealizadoAppDTO.getPeriodo().getNoiteApp();
        this.totalApp = this.manhaApp + this.tardeApp + this.noiteApp;
    }

    public Integer getManha() {
        return manha;
    }

    public void setManha(Integer manha) {
        this.manha = manha;
    }

    public Integer getTarde() {
        return tarde;
    }

    public void setTarde(Integer tarde) {
        this.tarde = tarde;
    }

    public Integer getNoite() {
        return noite;
    }

    public Integer getManhaApp() {
        if(manhaApp == null)
            return 0;
        return manhaApp;
    }

    public void setManhaApp(Integer manhaApp) {
        this.manhaApp = manhaApp;
    }

    public Integer getTardeApp() {
        if(tardeApp == null)
            return 0;
        return tardeApp;
    }

    public void setTardeApp(Integer tardeApp) {
        this.tardeApp = tardeApp;
    }

    public Integer getNoiteApp() {
        if(noiteApp == null)
            return 0;
        return noiteApp;
    }

    public void setNoiteApp(Integer noiteApp) {
        this.noiteApp = noiteApp;
    }

    public Integer getTotalApp() {
        return totalApp;
    }

    public void setTotalApp(Integer totalApp) {
        this.totalApp = totalApp;
    }

    public void setNoite(Integer noite) {
        this.noite = noite;
    }

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }
}
