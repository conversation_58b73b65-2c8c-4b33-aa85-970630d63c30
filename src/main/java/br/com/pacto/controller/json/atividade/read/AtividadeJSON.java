/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.controller.json.atividade.read;

import br.com.pacto.bean.atividade.AtividadeVideoTO;
import br.com.pacto.bean.atividade.ImgMediumUrlsTO;
import br.com.pacto.controller.json.base.SuperJSON;

import java.util.List;
/**
 *
 * <AUTHOR>
 */
public class AtividadeJSON extends SuperJSON {

    private Integer cod;
    private String  iaID;
    private Integer  iaID2;
    private Integer codigoAtividade;
    private boolean ativo = true;
    private String nome;
    private String descricao = "";
    private String versao = "";
    private Integer tipo;
    private String thumb = "";
    private String img = "";
    private Integer ordem = 0;
    private String imgMedium = "";
    private String urlVideo = "";
    private List<String> urlVideos;
    private String aparelhos = "";
    private String identificador = "";
    private String camposFiltro = "";
    private List<String> imgMediumUrls;
    private String complementoNomeAtividade;
    private List<AjusteJSON> ajustes;
    private List<String> niveis;
    private List<String> categorias;
    private List<String> listAparelhos;
    private List<String> gruposMusculares;
    private List<String> animacoes;
    private List<Integer> atividadesAlternativas;
    private List<AtividadeVideoTO> urlLinkVideos;
    private List<ImgMediumUrlsTO> listImgMediumUrls;

    public List<Integer> getAtividadesAlternativas() {
        return atividadesAlternativas;
    }

    public void setAtividadesAlternativas(List<Integer> atividadesAlternativas) {
        this.atividadesAlternativas = atividadesAlternativas;
    }

    public Integer getCod() {
        return cod;
    }

    public void setCod(Integer cod) {
        this.cod = cod;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public boolean isAtivo() {
        return ativo;
    }

    public void setAtivo(boolean ativo) {
        this.ativo = ativo;
    }

    public String getVersao() {
        return versao;
    }

    public void setVersao(String versao) {
        this.versao = versao;
    }

    public Integer getTipo() {
        return tipo;
    }

    public void setTipo(Integer tipo) {
        this.tipo = tipo;
    }

    public String getThumb() {
        return thumb;
    }

    public void setThumb(String thumb) {
        this.thumb = thumb;
    }

    public String getImg() {
        return img;
    }

    public void setImg(String img) {
        this.img = img;
    }

    public Integer getOrdem() {
        return ordem;
    }

    public void setOrdem(Integer ordem) {
        this.ordem = ordem;
    }

    public String getImgMedium() {
        return imgMedium;
    }

    public void setImgMedium(String imgMedium) {
        this.imgMedium = imgMedium;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getAparelhos() {
        return aparelhos;
    }

    public void setAparelhos(String aparelhos) {
        this.aparelhos = aparelhos;
    }

    public String getIdentificador() {
        return identificador;
    }

    public void setIdentificador(String identificador) {
        this.identificador = identificador;
    }

    public String getCamposFiltro() {
        return camposFiltro;
    }

    public void setCamposFiltro(String camposFiltro) {
        this.camposFiltro = camposFiltro;
    }

    public Integer getCodigoAtividade() {
        return codigoAtividade;
    }

    public void setCodigoAtividade(Integer codigoAtividade) {
        this.codigoAtividade = codigoAtividade;
    }

    public List<String> getImgMediumUrls() {
        return imgMediumUrls;
    }

    public void setImgMediumUrls(List<String> imgMediumUrls) {
        this.imgMediumUrls = imgMediumUrls;
    }

    public String getUrlVideo() {
        return urlVideo;
    }

    public void setUrlVideo(String urlVideo) {
        this.urlVideo = urlVideo;
    }

    public List<String> getUrlVideos() { return urlVideos; }

    public void setUrlVideos(List<String> urlVideos) { this.urlVideos = urlVideos; }

    public String getComplementoNomeAtividade() {
        return complementoNomeAtividade;
    }

    public void setComplementoNomeAtividade(String complementoNomeAtividade) {
            this.complementoNomeAtividade = complementoNomeAtividade;
    }

    public List<AjusteJSON> getAjustes() {
        return ajustes;
    }

    public void setAjustes(List<AjusteJSON> ajustes) {
        this.ajustes = ajustes;
    }

    public List<String> getNiveis() {
        return niveis;
    }

    public void setNiveis(List<String> niveis) {
        this.niveis = niveis;
    }

    public List<String> getCategorias() {
        return categorias;
    }

    public void setCategorias(List<String> categorias) {
        this.categorias = categorias;
    }

    public List<String> getListAparelhos() {
        return listAparelhos;
    }

    public void setListAparelhos(List<String> listAparelhos) {
        this.listAparelhos = listAparelhos;
    }

    public List<String> getGruposMusculares() {
        return gruposMusculares;
    }

    public void setGruposMusculares(List<String> gruposMusculares) {
        this.gruposMusculares = gruposMusculares;
    }

    public List<String> getAnimacoes() {
        return animacoes;
    }

    public void setAnimacoes(List<String> animacoes) {
        this.animacoes = animacoes;
    }

    public String getIaID() {
        return iaID;
    }

    public void setIaID(String iaID) {
        this.iaID = iaID;
    }

    public List<AtividadeVideoTO> getUrlLinkVideos() {
        return urlLinkVideos;
    }

    public void setUrlLinkVideos(List<AtividadeVideoTO> urlLinkVideos) {
        this.urlLinkVideos = urlLinkVideos;
    }

    public List<ImgMediumUrlsTO> getListImgMediumUrls() {
        return listImgMediumUrls;
    }

    public void setListImgMediumUrls(List<ImgMediumUrlsTO> listImgMediumUrls) {
        this.listImgMediumUrls = listImgMediumUrls;
    }

    public Integer getIaID2() {
        return iaID2;
    }

    public void setIaID2(Integer iaID2) {
        this.iaID2 = iaID2;
    }
}
