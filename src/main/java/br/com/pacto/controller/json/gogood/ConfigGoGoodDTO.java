package br.com.pacto.controller.json.gogood;

import br.com.pacto.bean.gogood.ConfigGoGood;
import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class ConfigGoGoodDTO {

    private Integer codigo;
    private Integer empresa;
    private String nome;
    private String tokenAcademyGoGood;

    public ConfigGoGoodDTO() {
    }

    public ConfigGoGoodDTO(ConfigGoGood config) {
        this.codigo = config.getCodigo();
        this.empresa = config.getEmpresa().getCodigo();
        this.nome = config.getEmpresa().getNome();
        this.tokenAcademyGoGood = config.getTokenAcademyGoGood();
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getTokenAcademyGoGood() {
        return tokenAcademyGoGood;
    }

    public void setTokenAcademyGoGood(String tokenAcademyGoGood) {
        this.tokenAcademyGoGood = tokenAcademyGoGood;
    }
}
