package br.com.pacto.controller.json.gestao;


import java.util.Date;

public class ConfigTotalPassDTO {

    private Integer codigo;

    private Integer empresa_codigo;
    private String nome;
    private String codigoTotalPass;
    private Integer usuarioLancou;
    private Boolean inativo;
    private Boolean permitirWod;
    private Date dataLancamento;
    private Integer limiteDeAcessosPorDia;
    private Integer limiteDeAulasPorDia;
    private String apikey;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getEmpresa_codigo() {
        return empresa_codigo;
    }

    public void setEmpresa_codigo(Integer empresa_codigo) {
        this.empresa_codigo = empresa_codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getCodigoTotalPass() {
        return codigoTotalPass;
    }

    public void setCodigoTotalPass(String codigoTotalPass) {
        this.codigoTotalPass = codigoTotalPass;
    }

    public Integer getUsuarioLancou() {
        return usuarioLancou;
    }

    public void setUsuarioLancou(Integer usuarioLancou) {
        this.usuarioLancou = usuarioLancou;
    }

    public Boolean getInativo() {
        return inativo;
    }

    public void setInativo(Boolean inativo) {
        this.inativo = inativo;
    }

    public Boolean getPermitirWod() {
        return permitirWod;
    }

    public void setPermitirWod(Boolean permitirWod) {
        this.permitirWod = permitirWod;
    }

    public Date getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public Integer getLimiteDeAcessosPorDia() {
        return limiteDeAcessosPorDia;
    }

    public void setLimiteDeAcessosPorDia(Integer limiteDeAcessosPorDia) {
        this.limiteDeAcessosPorDia = limiteDeAcessosPorDia;
    }

    public Integer getLimiteDeAulasPorDia() {
        return limiteDeAulasPorDia;
    }

    public void setLimiteDeAulasPorDia(Integer limiteDeAulasPorDia) {
        this.limiteDeAulasPorDia = limiteDeAulasPorDia;
    }

    public String getApikey() {
        return apikey;
    }

    public void setApikey(String apikey) {
        this.apikey = apikey;
    }
}