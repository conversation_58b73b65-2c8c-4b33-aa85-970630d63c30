package br.com.pacto.controller.json.locacao;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class AgendamentoLocacaoDTO {

    private Integer codigoPessoa;
    private String matricula;
    private Integer ambiente;
    private Double valorTotal;
    private Double valorLocacao;
    private Double valorHora;
    private List<LocacaoHorarioTO> horarios;
    private List<LocacaoProdutoSugeridoTO> servicos;

    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public Integer getAmbiente() {
        return ambiente;
    }

    public void setAmbiente(Integer ambiente) {
        this.ambiente = ambiente;
    }

    public List<LocacaoHorarioTO> getHorarios() {
        return horarios;
    }

    public void setHorarios(List<LocacaoHorarioTO> horarios) {
        this.horarios = horarios;
    }

    public List<LocacaoProdutoSugeridoTO> getServicos() {
        return servicos;
    }

    public void setServicos(List<LocacaoProdutoSugeridoTO> servicos) {
        this.servicos = servicos;
    }

    public Double getValorTotal() {
        return valorTotal;
    }

    public void setValorTotal(Double valorTotal) {
        this.valorTotal = valorTotal;
    }

    public Double getValorLocacao() {
        return valorLocacao;
    }

    public void setValorLocacao(Double valorLocacao) {
        this.valorLocacao = valorLocacao;
    }

    public Integer getCodigoPessoa() {
        return codigoPessoa;
    }

    public void setCodigoPessoa(Integer codigoPessoa) {
        this.codigoPessoa = codigoPessoa;
    }

    public Double getValorHora() {
        return valorHora;
    }

    public void setValorHora(Double valorHora) {
        this.valorHora = valorHora;
    }
}
