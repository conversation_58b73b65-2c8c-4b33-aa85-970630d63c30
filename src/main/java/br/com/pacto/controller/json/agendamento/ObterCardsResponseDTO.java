package br.com.pacto.controller.json.agendamento;

import br.com.pacto.service.intf.agendatotal.AgendaCardsDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "DTO para representar a resposta que contém os cards de agenda.")
public class ObterCardsResponseDTO {

    @ApiModelProperty(value = "Conteúdo da resposta, contendo os cards de agenda", required = true)
    private AgendaCardsDocumentacaoDTO content;

    public AgendaCardsDocumentacaoDTO getContent() {
        return content;
    }

    public void setContent(AgendaCardsDocumentacaoDTO content) {
        this.content = content;
    }
}
