package br.com.pacto.controller.json.tvGestor.controlador;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.controller.json.tvGestor.dto.FiltroTvGestorJSON;
import br.com.pacto.security.aspecto.Permissao;
import br.com.pacto.security.dto.UsuarioSimplesDTO;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.tvGestor.TvGestorService;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by erik-qo on 21/10/2019.
 */

@Controller
@RequestMapping("/psec/tv-gestor")
public class TvGestorController {

    @Autowired
    private TvGestorService tvGestorServiceIntegrado;
    @Autowired
    private SessaoService sessaoService;

    @ResponseBody
    @Permissao(recursos = RecursoEnum.TVGESTOR)
    @RequestMapping(value = "/bi-semana-atual", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> biSemanaAtual(
            @RequestParam(value = "professorIds", required = false) List<Integer> professorIds,
            @RequestParam(value = "modalidadeIds", required = false) List<Integer> modalidadeIds,
            @RequestParam(value = "ambienteIds", required = false) List<Integer> ambienteIds,
            @RequestHeader("empresaId") Integer empresaId) {
        try {
            String chave = sessaoService.getUsuarioAtual().getChave();
            FiltroTvGestorJSON filtro = new FiltroTvGestorJSON(modalidadeIds, professorIds, ambienteIds);
            return ResponseEntityFactory.ok(tvGestorServiceIntegrado.biSemana(chave, empresaId, filtro));
        } catch (ServiceException e) {
            Logger.getLogger(TvGestorController.class.getName()).log(Level.SEVERE, "Erro ao tentar gerar bi da semana", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.TVGESTOR)
    @RequestMapping(value = "/bi-periodo", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> biPeriodo(
            @RequestParam(value = "professorIds", required = false) List<Integer> professorIds,
            @RequestParam(value = "modalidadeIds", required = false) List<Integer> modalidadeIds,
            @RequestParam(value = "ambienteIds", required = false) List<Integer> ambienteIds,
            @RequestHeader("empresaId") Integer empresaId) {
        try {
            String chave = sessaoService.getUsuarioAtual().getChave();
            FiltroTvGestorJSON filtro = new FiltroTvGestorJSON(modalidadeIds, professorIds, ambienteIds);
            return ResponseEntityFactory.ok(tvGestorServiceIntegrado.biPeriodo(chave, empresaId, filtro));
        } catch (ServiceException e) {
            Logger.getLogger(TvGestorController.class.getName()).log(Level.SEVERE, "Erro ao tentar gerar bi do periodo", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.TVGESTOR)
    @RequestMapping(value = "/alunos", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alunos(
            @RequestParam(value = "filters") JSONObject filtros,
            @RequestHeader("empresaId") Integer empresaId,
            PaginadorDTO paginadorDTO,
            HttpServletRequest request) {
        try {
            String chave = sessaoService.getUsuarioAtual().getChave();
            return ResponseEntityFactory.ok(tvGestorServiceIntegrado.getAlunosTvGestor(chave, empresaId, new FiltroTvGestorJSON(filtros), paginadorDTO, request), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(TvGestorController.class.getName()).log(Level.SEVERE, "Erro ao tentar gerar bi do periodo", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.TVGESTOR)
    @RequestMapping(value = "/acessos", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> acessos(
            @RequestParam(value = "professorIds", required = false) List<Integer> professorIds,
            @RequestParam(value = "modalidadeIds", required = false) List<Integer> modalidadeIds,
            @RequestParam(value = "ambienteIds", required = false) List<Integer> ambienteIds,
            @RequestHeader("empresaId") Integer empresaId,
            HttpServletRequest request) {
        try {
            String chave = sessaoService.getUsuarioAtual().getChave();
            FiltroTvGestorJSON filtro = new FiltroTvGestorJSON(modalidadeIds, professorIds, ambienteIds);
            return ResponseEntityFactory.ok(tvGestorServiceIntegrado.biAcessos(chave, empresaId, filtro, request));
        } catch (ServiceException e) {
            Logger.getLogger(TvGestorController.class.getName()).log(Level.SEVERE, "Erro ao tentar gerar bi do periodo", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.TVGESTOR)
    @RequestMapping(value = "/filtros", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> filtros(@RequestHeader("empresaId") Integer empresaId, HttpServletRequest request) {
        try {
            UsuarioSimplesDTO usuarioSimplesDTO = sessaoService.getUsuarioAtual();
            return ResponseEntityFactory.ok(tvGestorServiceIntegrado.getFiltro(usuarioSimplesDTO.getChave(), empresaId, usuarioSimplesDTO.getId(), request));
        } catch (ServiceException e) {
            Logger.getLogger(TvGestorController.class.getName()).log(Level.SEVERE, "Erro ao tentar gerar bi do periodo", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.TVGESTOR)
    @RequestMapping(value = "/filtros", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> filtros(@RequestBody FiltroTvGestorJSON filtro,
                                                       @RequestHeader("empresaId") Integer empresaId,
                                                       HttpServletRequest request) {
        try {
            UsuarioSimplesDTO usuarioSimplesDTO = sessaoService.getUsuarioAtual();
            return ResponseEntityFactory.ok(tvGestorServiceIntegrado.atualizarFiltros(usuarioSimplesDTO.getChave(), empresaId, usuarioSimplesDTO.getId(), filtro,request));
        } catch (ServiceException e) {
            Logger.getLogger(TvGestorController.class.getName()).log(Level.SEVERE, "Erro ao tentar gerar bi do periodo", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }
}
