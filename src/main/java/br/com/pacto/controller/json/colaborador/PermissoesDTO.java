package br.com.pacto.controller.json.colaborador;

import br.com.pacto.bean.perfil.permissao.Permissao;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.bean.perfil.permissao.TipoPermissaoEnum;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.ArrayList;
import java.util.Objects;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class PermissoesDTO {

    private String recurso;
    private ArrayList<String> tipoPermissoes;

    public PermissoesDTO(Permissao p) {
        if(p.getRecurso() != null && p.getRecurso().getNome() != null) {
            this.recurso = p.getRecurso().name().toLowerCase();
        }

        tipoPermissoes = new ArrayList<>();
        for (TipoPermissaoEnum tipo : p.getTipoPermissoes()){
            tipoPermissoes.add(tipo.name());
        }
    }

    public String getRecurso() {
        return recurso;
    }

    public void setRecurso(String recurso) {
        this.recurso = recurso;
    }

    public ArrayList<String> getTipoPermissoes() {
        return tipoPermissoes;
    }

    public void setTipoPermissoes(ArrayList<String> tipoPermissoes) {
        this.tipoPermissoes = tipoPermissoes;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        PermissoesDTO that = (PermissoesDTO) o;
        return recurso.equals(that.recurso);
    }

    @Override
    public int hashCode() {
        return Objects.hash(recurso);
    }
}
