package br.com.pacto.controller.json.gestao;

import br.com.pacto.bean.bi.DashboardBI;
import br.com.pacto.bean.bi.DiasSemanaDashboardBI;
import br.com.pacto.bean.bi.MicroCharts;
import br.com.pacto.bean.bi.TipoEventoDisponibilidadeJSON;
import br.com.pacto.bean.cliente.ClienteSintetico;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @since 29/11/2018
 */
public class BITreinoResponseDTO {

    private Integer tempoMedioPermanenciaTreino = 0;
    private Integer totalAlunosSemTreino = 0;
    private Integer totalAlunosComTreino = 0;
    private Double percUtilizamApp = 0.0;
    private Integer totalAlunos = 0;
    private Integer totalAlunosAtivos = 0;
    private Integer totalAlunosInativos = 0;
    private Integer totalAlunosVisitantes = 0;
    private Integer percentualEmDia = 0;
    private Integer totalTreinosVencidos = 0;
    private Integer totalTreinosRenovar = 0;
    private Integer totalTreinosEmdia = 0;
    private Integer agendamentos = 0;
    private Integer compareceram = 0;
    private Integer confirmados = 0;
    private Integer aguardandoConfirmacao = 0;
    private Integer percentualAvaliacoes = 0;
    private Integer faltaram = 0;
    private Integer cancelaram = 0;
    private Integer nr5estrelas = 0;
    private Integer nr4estrelas = 0;
    private Integer nr3estrelas = 0;
    private Integer nr2estrelas = 0;
    private Integer nr1estrelas = 0;
    private Integer professores = 0;
    private Integer horasDisponibilidade = 0;
    private Integer horasAtendimento = 0;
    private Integer ocupacao = 0;
    private Integer novosTreinos = 0;
    private Integer treinosRevisados = 0;
    private Integer treinosRenovados = 0;
    private Integer avaliacoesFisicas = 0;
    private List<TipoEventoDisponibilidadeJSON> listaTipos;
    private MicroCharts micro;
    private Integer diaPermanenciaCarteira  = 0;
    private Integer mesPremanenciaCarteira =0;
    private List<DiasSemanaDashboardBI> listDiasExecucao;
    private Integer percentualRenovacoes = 0;
    private Integer totalAlunosAvencer;

     public BITreinoResponseDTO (DashboardBI bi, List<TipoEventoDisponibilidadeJSON> listaTipos, MicroCharts micro,List<DiasSemanaDashboardBI> listDiasExecucao ){

        this.tempoMedioPermanenciaTreino = bi.getTempoMedioPermanenciaTreino();
        this.totalAlunosSemTreino = bi.getTotalAlunosSemTreino();
        this.totalAlunosComTreino = bi.getTotalAlunosTreino();
        this.percUtilizamApp = bi.getPercUtilizamApp();
        this.totalAlunos = bi.getTotalAlunos();
        this.totalAlunosAtivos = bi.getTotalAlunosAtivos();
        this.totalAlunosInativos = bi.getTotalAlunosInativos();
        this.totalAlunosVisitantes = bi.getTotalAlunosVisitantes();
        this.percentualEmDia = bi.getPercentualEmDia();
        this.totalTreinosVencidos = bi.getTotalTreinosVencidos();
        this.totalTreinosRenovar = bi.getTotalTreinosRenovar();
        this.totalTreinosEmdia = bi.getTotalTreinosEmdia();
        this.agendamentos = bi.getAgendamentos();
        this.compareceram = bi.getCompareceram();
        this.faltaram = bi.getFaltaram();
        this.cancelaram = bi.getCancelaram();
        this.nr5estrelas = bi.getNr5estrelas();
        this.nr4estrelas = bi.getNr4estrelas();
        this.nr3estrelas = bi.getNr3estrelas();
        this.nr2estrelas = bi.getNr2estrelas();
        this.nr1estrelas = bi.getNr1estrelas();
        this.professores = bi.getProfessores();
        this.horasDisponibilidade = bi.getHorasDisponibilidade();
        this.horasAtendimento = bi.getHorasAtendimento();
        this.ocupacao = bi.getOcupacao();
        this.novosTreinos = bi.getNovosTreinos();
        this.treinosRevisados = bi.getTreinosRevisados();
        this.treinosRenovados = bi.getTreinosRenovados();
        this.avaliacoesFisicas = bi.getAvaliacoesFisicas();
        this.listaTipos = listaTipos;
        this.micro = micro;
        this.totalAlunosAvencer =  bi.getTotalAlunosAvencer();
        this.percentualRenovacoes = bi.getPercentualRenovacoes();
        if(bi.getTempoMedianaPermanenciaCarteira() != null){
            this.diaPermanenciaCarteira = bi.getDiaPermanenciaCarteira();
            this.mesPremanenciaCarteira = bi.getMesPermanenciaCarteira();
        }
        this.listDiasExecucao = listDiasExecucao;
        this.setConfirmados(bi.getConfirmados());
        this.setAguardandoConfirmacao(bi.getAguardandoConfirmacao());
     }

    public Integer getTotalAlunosAvencer() {
        return totalAlunosAvencer;
    }

    public void setTotalAlunosAvencer(Integer totalAlunosAvencer) {
        this.totalAlunosAvencer = totalAlunosAvencer;
    }

    public Integer getPercentualRenovacoes() {
        return percentualRenovacoes;
    }

    public void setPercentualRenovacoes(Integer percentualRenovacoes) {
        this.percentualRenovacoes = percentualRenovacoes;
    }

    public List<DiasSemanaDashboardBI> getListDiasExecucao() {
        return listDiasExecucao;
    }

    public void setListDiasExecucao(List<DiasSemanaDashboardBI> listDiasExecucao) {
        this.listDiasExecucao = listDiasExecucao;
    }

    public Integer getDiaPermanenciaCarteira() {
        return diaPermanenciaCarteira;
    }

    public void setDiaPermanenciaCarteira(Integer diaPermanenciaCarteira) {
        this.diaPermanenciaCarteira = diaPermanenciaCarteira;
    }

    public Integer getMesPremanenciaCarteira() {
        return mesPremanenciaCarteira;
    }

    public void setMesPremanenciaCarteira(Integer mesPremanenciaCarteira) {
        this.mesPremanenciaCarteira = mesPremanenciaCarteira;
    }

    public MicroCharts getMicro() {
        return micro;
    }

    public void setMicro(MicroCharts micro) {
        this.micro = micro;
    }

    public List<TipoEventoDisponibilidadeJSON> getListaTipos() {
        return listaTipos;
    }

    public void setListaTipos(List<TipoEventoDisponibilidadeJSON> listaTipos) {
        this.listaTipos = listaTipos;
    }

    public Integer getProfessores() {
        return professores;
    }

    public void setProfessores(Integer professores) {
        this.professores = professores;
    }

    public Integer getHorasDisponibilidade() {
        return horasDisponibilidade;
    }

    public void setHorasDisponibilidade(Integer horasDisponibilidade) {
        this.horasDisponibilidade = horasDisponibilidade;
    }

    public Integer getHorasAtendimento() {
        return horasAtendimento;
    }

    public void setHorasAtendimento(Integer horasAtendimento) {
        this.horasAtendimento = horasAtendimento;
    }

    public Integer getOcupacao() {
        return ocupacao;
    }

    public void setOcupacao(Integer ocupacao) {
        this.ocupacao = ocupacao;
    }

    public Integer getNovosTreinos() {
        return novosTreinos;
    }

    public void setNovosTreinos(Integer novosTreinos) {
        this.novosTreinos = novosTreinos;
    }

    public Integer getTreinosRevisados() {
        return treinosRevisados;
    }

    public void setTreinosRevisados(Integer treinosRevisados) {
        this.treinosRevisados = treinosRevisados;
    }

    public Integer getTreinosRenovados() {
        return treinosRenovados;
    }

    public void setTreinosRenovados(Integer treinosRenovados) {
        this.treinosRenovados = treinosRenovados;
    }

    public Integer getAvaliacoesFisicas() {
        return avaliacoesFisicas;
    }

    public void setAvaliacoesFisicas(Integer avaliacoesFisicas) {
        this.avaliacoesFisicas = avaliacoesFisicas;
    }


    public Integer getPercentualAvaliacoes() {
        return percentualAvaliacoes;
    }

    public void setPercentualAvaliacoes(Integer percentualAvaliacoes) {
        this.percentualAvaliacoes = percentualAvaliacoes;
    }


    public Integer getTempoMedioPermanenciaTreino() {
        return tempoMedioPermanenciaTreino;
    }

    public void setTempoMedioPermanenciaTreino(Integer tempoMedioPermanenciaTreino) {
        this.tempoMedioPermanenciaTreino = tempoMedioPermanenciaTreino;
    }

    public Integer getTotalAlunosSemTreino() {
        return totalAlunosSemTreino;
    }

    public void setTotalAlunosSemTreino(Integer totalAlunosSemTreino) {
        this.totalAlunosSemTreino = totalAlunosSemTreino;
    }

    public Double getPercUtilizamApp() {
        return percUtilizamApp;
    }

    public void setPercUtilizamApp(Double percUtilizamApp) {
        this.percUtilizamApp = percUtilizamApp;
    }

    public Integer getTotalAlunos() {
        return totalAlunos;
    }

    public void setTotalAlunos(Integer totalAlunos) {
        this.totalAlunos = totalAlunos;
    }

    public Integer getTotalAlunosAtivos() {
        return totalAlunosAtivos;
    }

    public void setTotalAlunosAtivos(Integer totalAlunosAtivos) {
        this.totalAlunosAtivos = totalAlunosAtivos;
    }

    public Integer getTotalAlunosInativos() {
        return totalAlunosInativos;
    }

    public void setTotalAlunosInativos(Integer totalAlunosInativos) {
        this.totalAlunosInativos = totalAlunosInativos;
    }

    public Integer getPercentualEmDia() {
        return percentualEmDia;
    }

    public void setPercentualEmDia(Integer percentualEmDia) {
        this.percentualEmDia = percentualEmDia;
    }

    public Integer getTotalTreinosVencidos() {
        return totalTreinosVencidos;
    }

    public void setTotalTreinosVencidos(Integer totalTreinosVencidos) {
        this.totalTreinosVencidos = totalTreinosVencidos;
    }

    public Integer getTotalTreinosRenovar() {
        return totalTreinosRenovar;
    }

    public void setTotalTreinosRenovar(Integer totalTreinosRenovar) {
        this.totalTreinosRenovar = totalTreinosRenovar;
    }

    public Integer getTotalTreinosEmdia() {
        return totalTreinosEmdia;
    }

    public void setTotalTreinosEmdia(Integer totalTreinosEmdia) {
        this.totalTreinosEmdia = totalTreinosEmdia;
    }

    public Integer getAgendamentos() {
        return agendamentos;
    }

    public void setAgendamentos(Integer agendamentos) {
        this.agendamentos = agendamentos;
    }

    public Integer getCompareceram() {
        return compareceram;
    }

    public void setCompareceram(Integer compareceram) {
        this.compareceram = compareceram;
    }

    public Integer getFaltaram() {
        return faltaram;
    }

    public void setFaltaram(Integer faltaram) {
        this.faltaram = faltaram;
    }

    public Integer getCancelaram() {
        return cancelaram;
    }

    public void setCancelaram(Integer cancelaram) {
        this.cancelaram = cancelaram;
    }

    public Integer getNr5estrelas() {
        return nr5estrelas;
    }

    public void setNr5estrelas(Integer nr5estrelas) {
        this.nr5estrelas = nr5estrelas;
    }

    public Integer getNr4estrelas() {
        return nr4estrelas;
    }

    public void setNr4estrelas(Integer nr4estrelas) {
        this.nr4estrelas = nr4estrelas;
    }

    public Integer getNr3estrelas() {
        return nr3estrelas;
    }

    public void setNr3estrelas(Integer nr3estrelas) {
        this.nr3estrelas = nr3estrelas;
    }

    public Integer getNr2estrelas() {
        return nr2estrelas;
    }

    public void setNr2estrelas(Integer nr2estrelas) {
        this.nr2estrelas = nr2estrelas;
    }

    public Integer getNr1estrelas() {
        return nr1estrelas;
    }

    public void setNr1estrelas(Integer nr1estrelas) {
        this.nr1estrelas = nr1estrelas;
    }

    public Integer getTotalAlunosComTreino() {
        return totalAlunosComTreino;
    }

    public void setTotalAlunosComTreino(Integer totalAlunosComTreino) {
        this.totalAlunosComTreino = totalAlunosComTreino;
    }

    public Integer getTotalAlunosVisitantes() {
        return totalAlunosVisitantes;
    }

    public void setTotalAlunosVisitantes(Integer totalAlunosVisitantes) {
        this.totalAlunosVisitantes = totalAlunosVisitantes;
    }

    public Integer getConfirmados() {
        return confirmados;
    }

    public void setConfirmados(Integer confirmados) {
        this.confirmados = confirmados;
    }

    public Integer getAguardandoConfirmacao() {
        return aguardandoConfirmacao;
    }

    public void setAguardandoConfirmacao(Integer aguardandoConfirmacao) {
        this.aguardandoConfirmacao = aguardandoConfirmacao;
    }
}
