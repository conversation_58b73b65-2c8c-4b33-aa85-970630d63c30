package br.com.pacto.controller.json.notificacao;

import br.com.pacto.base.util.ExecuteRequestHttpService;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.service.impl.notificacao.NotificacaoServiceImpl;
import br.com.pacto.util.UteisValidacao;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.http.client.ResponseHandler;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.BasicResponseHandler;
import org.apache.http.impl.client.CloseableHttpClient;
import org.json.JSONArray;
import org.json.JSONObject;

import java.io.BufferedReader;
import java.io.Closeable;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR> Siqueira on 08/11/18.
 * @project TreinoWeb
 */
public class PushMobileRunnable {

    public static final Integer TIMEOUT_PUSH = 15000;
    private static final ExecutorService executorService = Executors.newFixedThreadPool(10);

    public static void executarNoticacao(final String ctx, final String titulo, final String message, final String usuario) {
        executorService.submit(new Callable() {
            @Override
            public Object call() {
                try {
                    JSONArray alunoJsonArray = new JSONArray();
                    alunoJsonArray.put(usuario);

                    JSONObject json = new JSONObject();
                    json.put("refClienteApp", ctx);
                    json.put("refUsuarioApp", ctx + "_" + usuario);
                    json.put("chaveZw", ctx);
                    json.put("titulo", titulo);
                    json.put("mensagem", message);

                    HttpURLConnection httpUrlConnection = (HttpURLConnection) new URL(
                            Aplicacao.getProp(Aplicacao.urlAppDoAlunoUnificado) + "/geral/pushService").openConnection();
                    httpUrlConnection.setRequestMethod("POST");
                    httpUrlConnection.setDoOutput(true);
                    httpUrlConnection.setRequestProperty("Content-Type", "application/json");
                    httpUrlConnection.setRequestProperty("Accept", "application/json");
                    httpUrlConnection.setRequestProperty("Authorization", "Bearer " + Aplicacao.getProp(Aplicacao.tokenPushMobile));

                    OutputStreamWriter wr = new OutputStreamWriter(httpUrlConnection.getOutputStream(), StandardCharsets.UTF_8);
                    wr.write(json.toString());
                    wr.flush();

                    System.out.println("Response code: " + httpUrlConnection.getResponseCode());

                    InputStreamReader inputStreamReader = new InputStreamReader(httpUrlConnection.getInputStream());
                    System.out.println(new BufferedReader(inputStreamReader).readLine());

                } catch (Exception ex) {
                    Logger.getLogger(NotificacaoServiceImpl.class.getName()).log(Level.SEVERE,
                            String.format("ERRO AO ENVIAR PUSH => \nMEnsagem: %s\nEmail: %s",
                                    message, usuario), ex);
                }
                return null;
            }
        });
    }

    public static String executarNoticacaoAppDoAluno(final String ctx, final String titulo, final String message, final String usuario, final String horario) {
        String retorno = "";
        try {
            JSONArray alunoJsonArray = new JSONArray();
            alunoJsonArray.put(usuario);

            JSONObject json = new JSONObject();
            json.put("chave", ctx);
            json.put("userNameUsuario", usuario);
            json.put("titulo", titulo);
            json.put("content", message);

            String horarioFormatado = horario;
            if (UteisValidacao.emptyString(horarioFormatado)) {
                long dataNotificacaoMillis = (Calendario.hoje().getTime()) + (2 * 60 * 1000);
                Date dataNotificacao = new Date(dataNotificacaoMillis);
                SimpleDateFormat formatador = new SimpleDateFormat("dd/MM/yyyy HH:mm");
                horarioFormatado = formatador.format(dataNotificacao);
            }

            JSONArray horarios = new JSONArray();
            horarios.put(horarioFormatado);
            json.put("horarios", horarios);

            retorno = enviarRequisicao(json, Aplicacao.getProp(Aplicacao.urlAppDoAlunoUnificado) + "/geral/pushService", Aplicacao.getProp(Aplicacao.tokenPushAppAluno));
        } catch (Exception ex) {
            Logger.getLogger(NotificacaoServiceImpl.class.getName()).log(Level.SEVERE, "Erro ao enviar push", ex);
            return "";
        }
        String refPush = "";

        try {
            System.out.println("Retorno do firebase: " + retorno);
            ObjectMapper mapper = new ObjectMapper();
            JsonNode root = mapper.readTree(retorno);
            JsonNode refPushNode = root.path("sucesso").get(0).path("refPush");

            refPush = refPushNode.asText();
            System.out.println("refPush: " + refPush);

        } catch (IOException e) {
            Logger.getLogger(NotificacaoServiceImpl.class.getName()).log(Level.SEVERE, "Erro ao processar retorno do push", e);
        }

        return refPush;
    }

    public static void cancelarNotificacaoAulaAgendadaAluno(String refPush) {
        executorService.submit(() -> {
            try {
                JSONArray jsonArray = new JSONArray();
                JSONObject jsonRequest = new JSONObject();
                jsonRequest.put("refPush", refPush);
                jsonRequest.put("excluir", true);
                jsonArray.put(jsonRequest);

                enviarRequisicao(jsonRequest, Aplicacao.getProp(Aplicacao.urlAppDoAlunoUnificado) + "/geral/pushService", Aplicacao.getProp(Aplicacao.tokenPushAppAluno));
            } catch (Exception e) {
                Logger.getLogger(NotificacaoServiceImpl.class.getName()).log(Level.SEVERE, "Erro ao cancelar notificação", e);
            }
        });
    }

    private static String enviarRequisicao(JSONObject json, String urlString, String token) throws IOException {
        CloseableHttpClient client = ExecuteRequestHttpService.createConnector();

        HttpPost httpPost = new HttpPost(urlString);
        httpPost.setHeader("Content-Type", "application/json");
        httpPost.setHeader("Accept", "application/json");
        httpPost.setHeader("Authorization", "Bearer " + token);
        JSONArray jsonArray = new JSONArray();
        jsonArray.put(json);
        StringEntity entity = new StringEntity(jsonArray.toString(), StandardCharsets.UTF_8);
        httpPost.setEntity(entity);

        CloseableHttpResponse response = client.execute(httpPost);
        ResponseHandler<String> handler = new BasicResponseHandler();
        return handler.handleResponse(response);
    }
}
