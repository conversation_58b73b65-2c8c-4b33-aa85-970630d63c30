package br.com.pacto.controller.json.aluno;

import br.com.pacto.bean.avaliacao.AvaliacaoFisica;

import java.util.Date;

/**
 * Created paulo 07/11/2018
 */
public class PesoXGorduraDTO {

    private Date data;
    private Double peso;
    private Double massaGorda;

    public PesoXGorduraDTO(AvaliacaoFisica avaliacaoFisica) {
        this.data = avaliacaoFisica.getDataAvaliacao();
        this.peso = avaliacaoFisica.getPeso();
        this.massaGorda = avaliacaoFisica.getMassaGorda();
    }

    public Date getData() {
        return data;
    }

    public void setData(Date data) {
        this.data = data;
    }

    public Double getPeso() {
        return peso;
    }

    public void setPeso(Double peso) {
        this.peso = peso;
    }

    public Double getMassaGorda() {
        return massaGorda;
    }

    public void setMassaGorda(Double massaGorda) {
        this.massaGorda = massaGorda;
    }
}
