package br.com.pacto.controller.json.acompanhamento;

import java.io.Serializable;

public class ClienteAcompanhamentoAvaliacaoDTO implements Serializable {

    private Integer acompanhamentoId;
    private Integer clienteSinteticoId;
    private Integer nota;
    private String comentario;

    public ClienteAcompanhamentoAvaliacaoDTO() {
    }

    public Integer getAcompanhamentoId() {
        return acompanhamentoId;
    }

    public void setAcompanhamentoId(Integer acompanhamentoId) {
        this.acompanhamentoId = acompanhamentoId;
    }

    public Integer getClienteSinteticoId() {
        return clienteSinteticoId;
    }

    public void setClienteSinteticoId(Integer clienteSinteticoId) {
        this.clienteSinteticoId = clienteSinteticoId;
    }

    public Integer getNota() {
        return nota;
    }

    public void setNota(Integer nota) {
        this.nota = nota;
    }

    public String getComentario() {
        return comentario;
    }

    public void setComentario(String comentario) {
        this.comentario = comentario;
    }
}
