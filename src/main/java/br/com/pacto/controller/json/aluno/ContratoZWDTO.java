package br.com.pacto.controller.json.aluno;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.Date;

/**
 * Created by paulo on 09/07/2019.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ContratoZWDTO {

    private Date vencimento;
    private TipoContratoZWEnum tipo;

    public ContratoZWDTO(Date vencimento, TipoContratoZWEnum tipo) {
        this.vencimento = vencimento;
        this.tipo = tipo;
    }

    public Date getVencimento() {
        return vencimento;
    }

    public void setVencimento(Date vencimento) {
        this.vencimento = vencimento;
    }

    public TipoContratoZWEnum getTipo() {
        return tipo;
    }

    public void setTipo(TipoContratoZWEnum tipo) {
        this.tipo = tipo;
    }
}
