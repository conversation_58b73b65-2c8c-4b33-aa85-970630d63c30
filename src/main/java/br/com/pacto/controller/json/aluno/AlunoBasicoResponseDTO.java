package br.com.pacto.controller.json.aluno;

import br.com.pacto.bean.cliente.ClienteSintetico;
import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * Created by paulo 31/10/2018
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AlunoBasicoResponseDTO {

    private Integer id;
    private String nome;

    public AlunoBasicoResponseDTO(ClienteSintetico clienteSintetico) {
        this.id = clienteSintetico.getCodigo();
        this.nome = clienteSintetico.getNome();
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }
}
