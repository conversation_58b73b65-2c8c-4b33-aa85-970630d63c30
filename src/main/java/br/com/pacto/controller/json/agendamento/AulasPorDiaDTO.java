package br.com.pacto.controller.json.agendamento;

import java.util.List;

public class AulasPorDiaDTO {

    private Integer dia;
    private Integer ano;
    private Integer mes;
    private List<EventoAulaDTO> aulas;

    public AulasPorDiaDTO(Integer dia, Integer mes, Integer ano, List<EventoAulaDTO> aulas) {
        this.dia = dia;
        this.ano = ano;
        this.mes = mes;
        this.aulas = aulas;
    }

    public AulasPorDiaDTO() {
    }

    public Integer getDia() {
        return dia;
    }

    public void setDia(Integer dia) {
        this.dia = dia;
    }

    public Integer getAno() {
        return ano;
    }

    public void setAno(Integer ano) {
        this.ano = ano;
    }

    public Integer getMes() {
        return mes;
    }

    public void setMes(Integer mes) {
        this.mes = mes;
    }

    public List<EventoAulaDTO> getAulas() {
        return aulas;
    }

    public void setAulas(List<EventoAulaDTO> aulas) {
        this.aulas = aulas;
    }
}
