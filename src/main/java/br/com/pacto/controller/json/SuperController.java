package br.com.pacto.controller.json;

import br.com.pacto.base.impl.EntityManagerService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.util.UtilContext;

public class SuperController {

    protected void prepare(final String ctx) throws ServiceException {
        EntityManagerService ems = (EntityManagerService) UtilContext.getBean(EntityManagerService.class);
        try {
            ems.closeQuietly(ctx);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }
}
