package br.com.pacto.controller.json.aulaExcluida;

import br.com.pacto.bean.aula.AulaDiaExclusao;
import br.com.pacto.bean.usuario.Usuario;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.Date;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class AulaExcluidaResponseDTO {

    private Integer codigo;
    private Integer codigoHorarioTurma;
    private Date dataAulaDia;
    private Date dataExclusao;
    private String justificativa;
    private Integer usuarioCodigo;
    private Integer usuario;

    public AulaExcluidaResponseDTO() {
    }

    public AulaExcluidaResponseDTO(AulaDiaExclusao aulaDiaExclusao) {
        this.codigo = aulaDiaExclusao.getCodigo();
        this.codigoHorarioTurma = aulaDiaExclusao.getCodigoHorarioTurma();
        this.dataAulaDia = aulaDiaExclusao.getDataAulaDia();
        this.dataExclusao = aulaDiaExclusao.getDataExclusao();
        this.justificativa = aulaDiaExclusao.getJustificativa();
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getCodigoHorarioTurma() {
        return codigoHorarioTurma;
    }

    public void setCodigoHorarioTurma(Integer codigoHorarioTurma) {
        this.codigoHorarioTurma = codigoHorarioTurma;
    }

    public Date getDataAulaDia() {
        return dataAulaDia;
    }

    public void setDataAulaDia(Date dataAulaDia) {
        this.dataAulaDia = dataAulaDia;
    }

    public Date getDataExclusao() {
        return dataExclusao;
    }

    public void setDataExclusao(Date dataExclusao) {
        this.dataExclusao = dataExclusao;
    }

    public String getJustificativa() {
        return justificativa;
    }

    public void setJustificativa(String justificativa) {
        this.justificativa = justificativa;
    }

    public Integer getUsuarioCodigo() {
        return usuarioCodigo;
    }

    public void setUsuarioCodigo(Integer usuarioCodigo) {
        this.usuarioCodigo = usuarioCodigo;
    }

    public Integer getUsuario() { return usuario; }

    public void setUsuario(Integer usuario) { this.usuario = usuario; }
}
