package br.com.pacto.controller.json.avaliacao;

import br.com.pacto.bean.anamnese.RespostaCliente;
import br.com.pacto.objeto.Uteis;

public class RespostaClienteJSON {

    private Integer codigo;
    private String resposta;
    private String obs;
    private String urlAssinatura;
    private String dataResposta;
    private Integer ordem;

    public RespostaClienteJSON() {
    }

    public RespostaClienteJSON(RespostaCliente rc) {
        this.codigo = rc.getCodigo();
        this.resposta = rc.getResposta();
        this.obs = rc.getObs();
        if(rc.getRespostaClienteParQ() != null) {
            this.dataResposta = Uteis.getDataAplicandoFormatacao(rc.getRespostaClienteParQ().getDataResposta(), "dd/MM/yyyy HH:mm:ss");
            this.urlAssinatura = Uteis.valorVazioString(rc.getRespostaClienteParQ().getUrlAssinatura()) ?
                    "" : Uteis.getPaintFotoDaNuvem(rc.getRespostaClienteParQ().getUrlAssinatura());
        }
        if(rc.getPerguntaAnamnese() != null && rc.getPerguntaAnamnese().getPergunta() != null && rc.getPerguntaAnamnese().getPergunta().getOrdem() != null) {
            this.ordem = rc.getPerguntaAnamnese().getPergunta().getOrdem();
        }
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getResposta() {
        return resposta == null ? "" : this.resposta;
    }

    public void setResposta(String resposta) {
        this.resposta = resposta;
    }

    public String getObs() {
        return obs;
    }

    public void setObs(String obs) {
        this.obs = obs;
    }

    public String getUrlAssinatura() {
        return urlAssinatura;
    }

    public void setUrlAssinatura(String urlAssinatura) {
        this.urlAssinatura = urlAssinatura;
    }

    public String getDataResposta() {
        return dataResposta;
    }

    public void setDataResposta(String dataResposta) {
        this.dataResposta = dataResposta;
    }

    public Integer getOrdem() {
        return ordem;
    }

    public void setOrdem(Integer ordem) {
        this.ordem = ordem;
    }
}
