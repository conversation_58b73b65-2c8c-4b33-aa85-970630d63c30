package br.com.pacto.controller.json.gestao;

import br.com.pacto.bean.bi.DashboardBI;

public class FluxoCarteiraZWDTO {
    private Integer novos;
    private Integer trocaram;
    private Integer sairam;

    public FluxoCarteiraZWDTO() {

    }

    public FluxoCarteiraZWDTO(DashboardBI dash) {
        this.novos = dash.getTotalNovosCarteiraNovos();
        this.trocaram = dash.getTotalNovosCarteiraTrocaram();
        this.sairam = dash.getTotalTrocaramCarteira();
    }

    public Integer getNovos() {
        return novos;
    }

    public void setNovos(Integer novos) {
        this.novos = novos;
    }

    public Integer getTrocaram() {
        return trocaram;
    }

    public void setTrocaram(Integer trocaram) {
        this.trocaram = trocaram;
    }

    public Integer getSairam() {
        return sairam;
    }

    public void setSairam(Integer sairam) {
        this.sairam = sairam;
    }
}
