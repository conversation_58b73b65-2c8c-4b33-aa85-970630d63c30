package br.com.pacto.controller.json.agendamento;

import org.json.JSONObject;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;

public class ItemListagemFrequenciaAlunosDTO {

    private String nome;
    private String modalidades;
    private Integer aulas;
    private Integer presencas;
    private Double frequenciaPercentual;
    private String frequencia;


    public ItemListagemFrequenciaAlunosDTO(JSONObject object) throws Exception {
        this.nome = object.optString("nome");
        this.modalidades = object.optString("modalidades");
        this.aulas = object.optInt("aulas");
        this.presencas = object.optInt("presencas");
        this.frequenciaPercentual = (presencas.doubleValue()/aulas.doubleValue()) * 100.0 ;
        this.frequencia = this.frequenciaPercentual.intValue() +  "%";
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getModalidades() {
        return modalidades;
    }

    public void setModalidades(String modalidades) {
        this.modalidades = modalidades;
    }

    public Integer getAulas() {
        return aulas;
    }

    public void setAulas(Integer aulas) {
        this.aulas = aulas;
    }

    public Integer getPresencas() {
        return presencas;
    }

    public void setPresencas(Integer presencas) {
        this.presencas = presencas;
    }

    public Double getFrequenciaPercentual() {
        return frequenciaPercentual;
    }

    public void setFrequenciaPercentual(Double frequenciaPercentual) {
        this.frequenciaPercentual = frequenciaPercentual;
    }

    public String getFrequencia() {
        return frequencia;
    }

    public void setFrequencia(String frequencia) {
        this.frequencia = frequencia;
    }
}
