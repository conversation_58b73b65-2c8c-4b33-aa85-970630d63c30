/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.controller.json.cliente;

import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.controller.json.serialization.JsonDateSerializerYYYYMMDD;
import br.com.pacto.controller.json.base.SuperJSON;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.util.DataUtils;
import br.com.pacto.util.json.ClienteSintenticoJson;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.text.ParseException;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class ClienteJSON extends SuperJSON {

    private Integer codigo;
    private Integer codigoPessoaZW;
    private Integer codigoClienteZW;
    private Integer codigoContrato;
    private Integer matricula;
    private String nome;
    private Date dataNascimento;
    private Integer idade;
    private String profissao;
    private String colaboradores;
    private String professor;
    private String situacao;
    private String situacaoContrato;
    private Date dataUltimoAcesso;
    private Date dataInicioPeriodoAcesso;
    private Date dataFimPeriodoAcesso;
    private Integer empresa;
    private String email = "";
    private String telefones;
    private String andamentoTreino;
    private String urlFoto;
    private String userName;
    private Date dia;
    private String nivel;
    private String nomeEmpresa;
    private Integer versao;
    private String codigoAcesso;
    private String vencPlano;
    private List<String> listaTelefones;
    private List<String> listaEmails;
    private String sexo;
    private String objetivos;
    private String dadosAvaliacao;
    private Boolean parQ;
    private String fotoKeyApp;
    private Integer tempoDeAula;
    private Integer posicaoRankingAluno;
    private Integer calories;
    private Integer averagePower;

    public ClienteJSON() {
    }

    public ClienteJSON(final ClienteSintetico clienteSintetico) {
        this.andamentoTreino = clienteSintetico.getAndamentoTreino();
        this.codigo = clienteSintetico.getCodigo();
        this.codigoClienteZW = clienteSintetico.getCodigoCliente();
        this.codigoContrato = clienteSintetico.getCodigoContrato();
        this.codigoPessoaZW = clienteSintetico.getCodigoPessoa();
        this.colaboradores = clienteSintetico.getColaboradores();
        this.dataFimPeriodoAcesso = clienteSintetico.getDataFimPeriodoAcesso();
        this.dataInicioPeriodoAcesso = clienteSintetico.getDataFimPeriodoAcesso();
        this.dataNascimento = clienteSintetico.getDataNascimento();
        this.dataUltimoAcesso = clienteSintetico.getDataUltimoacesso();
        this.empresa = clienteSintetico.getEmpresa();
        this.idade = clienteSintetico.getIdade();
        this.matricula = clienteSintetico.getMatricula();
        this.nome = clienteSintetico.getNome();
        this.professor = clienteSintetico.getProfessorSintetico() != null ? clienteSintetico.getProfessorSintetico().getNome() : "";
        this.profissao = clienteSintetico.getProfissao();
        this.situacao = clienteSintetico.getSituacao();
        this.situacaoContrato = clienteSintetico.getSituacaoContrato();
        this.dia = clienteSintetico.getDia();
        this.nivel = clienteSintetico.getNivelAluno() != null ? clienteSintetico.getNivelAluno().getNome() : "";
        this.versao = clienteSintetico.getVersao();
        this.codigoAcesso = clienteSintetico.getCodigoAcesso();
        this.vencPlano = clienteSintetico.getDataVigenciaAteAjustadaApresentar();
        this.listaEmails = clienteSintetico.getListaEmails();
        this.listaTelefones = clienteSintetico.getListaTelefones();
        this.sexo = clienteSintetico.getSexo();
        this.objetivos = clienteSintetico.getObjetivos() != null ? clienteSintetico.getObjetivos().toString() : "";
        this.dadosAvaliacao = clienteSintetico.getDadosAvaliacao() != null ? clienteSintetico.getDadosAvaliacao() : "";
        this.parQ = clienteSintetico.getParq() == null ? false : clienteSintetico.getParq();
        this.urlFoto = clienteSintetico.getUrlFoto();
        this.fotoKeyApp = clienteSintetico.getFotoKeyApp();

    }

    public ClienteJSON(final ClienteSintenticoJson cliente) {
        this.codigo = cliente.getCodigo();
        this.codigoClienteZW = cliente.getCodigoClienteZW();
        this.codigoContrato = cliente.getCodigoContrato();
        this.codigoPessoaZW = cliente.getCodigoPessoaZW();
        this.colaboradores = cliente.getColaboradores();
        this.dataFimPeriodoAcesso = cliente.getDataFimPeriodoAcesso();
        this.dataInicioPeriodoAcesso = cliente.getDataFimPeriodoAcesso();
        this.dataNascimento = cliente.getDataNascimento();
        this.dataUltimoAcesso = cliente.getDataUltimoAcesso();
        this.empresa = cliente.getEmpresa();
        this.idade = cliente.getIdade();
        this.matricula = cliente.getMatricula();
        this.nome = cliente.getNome();
        this.professor = cliente.getProfessor();
        this.profissao = cliente.getProfissao();
        this.situacao = cliente.getSituacao();
        this.situacaoContrato = cliente.getSituacaoContrato();
        this.dia = cliente.getDia();
        this.nivel = cliente.getNivel();
        this.versao = cliente.getVersao();
        this.codigoAcesso = cliente.getCodigoAcesso();
        this.vencPlano = Uteis.getData(cliente.getDataVirgencia());
        this.listaEmails = cliente.getListaEmails();
        this.listaTelefones = cliente.getListaTelefones();
        this.sexo = cliente.getSexo();
        this.objetivos = cliente.getObjetivos();
        this.dadosAvaliacao = cliente.getDadosAvaliacao();
        this.parQ = cliente.getParQ() == null ? false : cliente.getParQ();
        this.urlFoto = cliente.getUrlFoto();
    }

    public ClienteJSON(Integer codigoPessoaZw, Integer codigoClienteZW, Integer codigoContrato, String urlFoto,
                       String nome, Integer matricula, String telefones, String situacaoContrato, String situacao,
                       String fotoKey, String dataNascimento, String email, String sexo) throws ParseException {
        this.setCodigoPessoaZW(codigoPessoaZw);
        if(codigoClienteZW != null){
            this.setCodigoClienteZW(codigoClienteZW);
        }
        if(codigoContrato != null){
            this.setCodigoContrato(codigoContrato);
        }
        if(urlFoto != null){
            this.setUrlFoto(urlFoto);
        }
        if(nome != null){
            this.setNome(nome);
        }
        if(matricula != null){
            this.setMatricula(matricula);
        }
        if(telefones != null){
            this.setTelefones(telefones);
        }
        if(situacaoContrato != null){
            this.setSituacaoContrato(situacaoContrato);
        }
        if(situacao != null){
            this.setSituacao(situacao);
        }
        if(fotoKey != null){
            this.setFotoKeyApp(fotoKey);
        }
        if(dataNascimento != null){
            Date data = DataUtils.stringToDate(dataNascimento, "yyyy-MM-dd");
            this.setDataNascimento(data);
        }
        if(email != null){
            this.setEmail(email);
        }
        if(sexo != null){
            this.setSexo(sexo);
        }
    }


    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getCodigoPessoaZW() {
        return codigoPessoaZW;
    }

    public void setCodigoPessoaZW(Integer codigoPessoaZW) {
        this.codigoPessoaZW = codigoPessoaZW;
    }

    public Integer getCodigoClienteZW() {
        return codigoClienteZW;
    }

    public void setCodigoClienteZW(Integer codigoClienteZW) {
        this.codigoClienteZW = codigoClienteZW;
    }

    public Integer getCodigoContrato() {
        return codigoContrato;
    }

    public void setCodigoContrato(Integer codigoContrato) {
        this.codigoContrato = codigoContrato;
    }

    public Integer getMatricula() {
        return matricula;
    }

    public void setMatricula(Integer matricula) {
        this.matricula = matricula;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    @JsonSerialize(using = JsonDateSerializerYYYYMMDD.class, nullsUsing = JsonDateSerializerYYYYMMDD.class)
    public Date getDataNascimento() {
        return dataNascimento;
    }

    public void setDataNascimento(Date dataNascimento) {
        this.dataNascimento = dataNascimento;
    }

    public Integer getIdade() {
        return idade;
    }

    public void setIdade(Integer idade) {
        this.idade = idade;
    }

    public String getProfissao() {
        return profissao;
    }

    public void setProfissao(String profissao) {
        this.profissao = profissao;
    }

    public String getColaboradores() {
        return colaboradores;
    }

    public void setColaboradores(String colaboradores) {
        this.colaboradores = colaboradores;
    }

    public String getProfessor() {
        return professor;
    }

    public void setProfessor(String professor) {
        this.professor = professor;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public String getSituacaoContrato() {
        return situacaoContrato;
    }

    public void setSituacaoContrato(String situacaoContrato) {
        this.situacaoContrato = situacaoContrato;
    }

    @JsonSerialize(using = JsonDateSerializerYYYYMMDD.class, nullsUsing = JsonDateSerializerYYYYMMDD.class)
    public Date getDataUltimoAcesso() {        
        return dataUltimoAcesso;
    }

    public void setDataUltimoAcesso(Date dataUltimoAcesso) {
        this.dataUltimoAcesso = dataUltimoAcesso;
    }

    @JsonSerialize(using = JsonDateSerializerYYYYMMDD.class, nullsUsing = JsonDateSerializerYYYYMMDD.class)
    public Date getDataInicioPeriodoAcesso() {
        return dataInicioPeriodoAcesso;
    }

    public void setDataInicioPeriodoAcesso(Date dataInicioPeriodoAcesso) {
        this.dataInicioPeriodoAcesso = dataInicioPeriodoAcesso;
    }

    @JsonSerialize(using = JsonDateSerializerYYYYMMDD.class, nullsUsing = JsonDateSerializerYYYYMMDD.class)
    public Date getDataFimPeriodoAcesso() {
        return dataFimPeriodoAcesso;
    }

    public void setDataFimPeriodoAcesso(Date dataFimPeriodoAcesso) {
        this.dataFimPeriodoAcesso = dataFimPeriodoAcesso;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getTelefones() {
        if (telefones == null) {
            telefones = "";
        }
        return telefones;
    }

    public void setTelefones(String telefones) {
        this.telefones = telefones;
    }

    public String getAndamentoTreino() {
        return andamentoTreino;
    }

    public void setAndamentoTreino(String andamentoTreino) {
        this.andamentoTreino = andamentoTreino;
    }

    public String getUrlFoto() {
        return urlFoto;
    }

    public void setUrlFoto(String urlFoto) {
        this.urlFoto = urlFoto;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    @JsonSerialize(using = JsonDateSerializerYYYYMMDD.class)
    public Date getDia() {
        return dia;
    }

    public void setDia(Date dia) {
        this.dia = dia;
    }

    public String getNivel() {
        return nivel;
    }

    public void setNivel(String nivel) {
        this.nivel = nivel;
    }

    public String getNomeEmpresa() {
        return nomeEmpresa;
    }

    public void setNomeEmpresa(String nomeEmpresa) {
        this.nomeEmpresa = nomeEmpresa;
    }

    public Integer getVersao() {
        return versao;
    }

    public void setVersao(Integer versao) {
        this.versao = versao;
    }

    public String getCodigoAcesso() {
        return codigoAcesso;
    }

    public void setCodigoAcesso(String codigoAcesso) {
        this.codigoAcesso = codigoAcesso;
    }

    public String getVencPlano() {
        return vencPlano;
    }

    public void setVencPlano(String vencPlano) {
        this.vencPlano = vencPlano;
    }

    public String getSexo() {
        return sexo;
    }

    public void setSexo(String sexo) {
        this.sexo = sexo;
    }

    public String getObjetivos() {
        return objetivos;
    }

    public void setObjetivos(String objetivos) {
        this.objetivos = objetivos;
    }

    public List<String> getListaTelefones() {
        return listaTelefones;
    }

    public void setListaTelefones(List<String> listaTelefones) {
        this.listaTelefones = listaTelefones;
    }

    public List<String> getListaEmails() {
        return listaEmails;
    }

    public void setListaEmails(List<String> listaEmails) {
        this.listaEmails = listaEmails;
    }

    public String getDadosAvaliacao() {
        return dadosAvaliacao;
    }

    public void setDadosAvaliacao(String dadosAvaliacao) {
        this.dadosAvaliacao = dadosAvaliacao;
    }

    public Boolean getParQ() {
        return parQ;
    }

    public void setParQ(Boolean parQ) {
        this.parQ = parQ;
    }

    public String getFotoKeyApp() {
        return fotoKeyApp;
    }

    public void setFotoKeyApp(String fotoKeyApp) {
        this.fotoKeyApp = fotoKeyApp;
    }

    public Integer getTempoDeAula() {
        return tempoDeAula;
    }

    public void setTempoDeAula(Integer tempoDeAula) {
        this.tempoDeAula = tempoDeAula;
    }

    public Integer getPosicaoRankingAluno() {
        return posicaoRankingAluno;
    }

    public void setPosicaoRankingAluno(Integer posicaoRankingAluno) {
        this.posicaoRankingAluno = posicaoRankingAluno;
    }

    public Integer getCalories() {
        return calories;
    }

    public void setCalories(Integer calories) {
        this.calories = calories;
    }

    public Integer getAveragePower() {
        return averagePower;
    }

    public void setAveragePower(Integer averagePower) {
        this.averagePower = averagePower;
    }
}
