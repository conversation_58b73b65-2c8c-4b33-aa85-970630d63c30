package br.com.pacto.controller.json.lesao;

import br.com.pacto.bean.lesao.Lesao;
import br.com.pacto.bean.lesao.LesaoAppDTO;
import br.com.pacto.bean.lesao.LesaoAppVO;
import br.com.pacto.bean.lesao.LesaoDTO;
import br.com.pacto.bean.lesao.StatusLesaoEnum;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.base.SuperControle;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.service.intf.empresa.EmpresaService;
import br.com.pacto.service.intf.lesao.LesaoService;

import br.com.pacto.service.intf.usuario.UsuarioService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

@Controller
@RequestMapping("/lesao")
public class LesaoJSONControle extends SuperControle {

    @Autowired
    private LesaoService lesaoService;
    @Autowired
    private UsuarioService usuarioService;
    @Autowired
    private EmpresaService empresaService;

    @RequestMapping(value = "{ctx}/gravar-lesao", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap gravarLesaoAPP(@PathVariable String ctx,
                            @RequestParam final Integer codigoUsuarioZw,
                            @RequestBody LesaoDTO lesaoDTO) {
        ModelMap mm = new ModelMap();
        try {
            lesaoDTO =  lesaoService.gravarLesao(lesaoDTO, ctx, codigoUsuarioZw, true);
            mm.addAttribute(RETURN, lesaoDTO);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(LesaoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/editar-lesao", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap editarLesaoAPP(@PathVariable String ctx,
                            @RequestParam final Integer codigoUsuarioZw,
                            @RequestBody LesaoDTO lesaoDTO) {
        ModelMap mm = new ModelMap();
        try {
            lesaoDTO =  lesaoService.gravarLesao(lesaoDTO, ctx, codigoUsuarioZw, true);
            mm.addAttribute(RETURN, lesaoDTO);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(LesaoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/app", method = RequestMethod.GET)
    public @ResponseBody
    ModelMap consultarLesaoAPP(@PathVariable String ctx,
                               @RequestParam final Integer codigoUsuarioTreino) {
        ModelMap mm = new ModelMap();
        try {
            List<LesaoAppVO> lesaoAppVOS = lesaoService.consultarLesaoPorCliente(codigoUsuarioTreino, ctx);
            mm.addAttribute(RETURN, lesaoAppVOS);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(LesaoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/app", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap gravarLesao(@PathVariable String ctx,
                         @RequestParam final Integer codigoUsuarioTreino,
                         @RequestBody LesaoAppDTO lesaoDTO) {
        ModelMap mm = new ModelMap();
        try {
            LesaoAppVO lesaoAppVO = lesaoService.cadastraLesao(lesaoDTO, ctx, codigoUsuarioTreino);
            mm.addAttribute(RETURN, lesaoAppVO);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(LesaoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/app/{id}", method = RequestMethod.PUT)
    public @ResponseBody
    ModelMap editarLesaoAPP(@PathVariable String ctx,
                            @RequestParam final Integer codigoUsuarioTreino,
                            @PathVariable Integer id,
                            @RequestBody LesaoAppDTO lesaoAppDTO) {
        ModelMap mm = new ModelMap();
        try {
            LesaoAppVO lesaoAppVO = lesaoService.editarLesao(lesaoAppDTO, ctx, codigoUsuarioTreino, id);
            mm.addAttribute(RETURN, lesaoAppVO);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(LesaoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/app/inativar-lesao/{id}", method = RequestMethod.PATCH)
    public @ResponseBody
    ModelMap inativarLesaoAPP(@PathVariable String ctx,
                              @RequestParam final Integer codigoUsuarioTreino,
                              @PathVariable Integer id) {
        ModelMap mm = new ModelMap();
        try {
            Usuario usuario = usuarioService.obterPorId(ctx, codigoUsuarioTreino, true);
            if (usuario == null) {
                mm.addAttribute(STATUS_ERRO, "Usuário não encontrado.");
                return mm;
            }

            Lesao lesao = lesaoService.consultaLesaoPorId(id, ctx);
            if (lesao != null) {
                lesao.setStatus(StatusLesaoEnum.RECUPERADA);
                LesaoAppDTO lesaoAppDTO = new LesaoAppDTO(lesao);
                lesaoAppDTO.setDataRecuperacao(Calendario.getData(Calendario.hoje(empresaService.obterFusoHorarioEmpresa(ctx, lesao.getCliente().getEmpresa())), "yyyy-MM-dd"));
                LesaoAppVO lesaoAppVO = lesaoService.editarLesao(lesaoAppDTO, ctx, codigoUsuarioTreino, lesao.getCodigo());
                mm.addAttribute(RETURN, lesaoAppVO);
            } else {
                mm.addAttribute(STATUS_ERRO, "Lesão não encontrada.");
            }
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(LesaoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/app/{id}", method = RequestMethod.DELETE)
    public @ResponseBody
    ModelMap excluirLesaoAPP(@PathVariable String ctx,
                             @RequestParam final Integer codigoUsuarioTreino,
                             @PathVariable Integer id) {
        ModelMap mm = new ModelMap();
        try {
            Usuario usuario = usuarioService.obterPorId(ctx, codigoUsuarioTreino, true);
            if (usuario == null) {
                mm.addAttribute(STATUS_ERRO, "Usuário não encontrado.");
                return mm;
            }

            Lesao lesao = lesaoService.consultaLesaoPorId(id, ctx);
            if (lesao != null) {
                lesaoService.excluirLesao(lesao, ctx, codigoUsuarioTreino);
                mm.addAttribute(RETURN, "Lesão excluída com sucesso.");
            } else {
                mm.addAttribute(STATUS_ERRO, "Lesão não encontrada.");
            }
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(LesaoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

}
