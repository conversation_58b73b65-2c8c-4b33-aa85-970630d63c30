package br.com.pacto.controller.json.aluno;

import br.com.pacto.bean.cliente.ClienteSintetico;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.ArrayList;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class ListaRapidaAcessoDTO {

    private String idMemcached;
    private Integer codigoCliente;
    private Integer matricula;
    private String hora;
    private String nome;
    private boolean parq;
    private PendenciaAcessoDTO parqAssinado;
    private String plano;
    private String foto;
    private String cor;
    private boolean aniversariante;
    private boolean divisor;
    private List<PendenciaAcessoDTO> avisos = new ArrayList<>();
    private PendenciaAcessoDTO aviso;
    private String situacao;
    private String situacaoContrato;
    private boolean gympass;
    private boolean totalpass;
    private boolean diaria;
    private boolean freepass;
    private boolean aulaAvulsa;

    public void addPendencia(PendenciasAcessoEnum pendencia) {
        PendenciaAcessoDTO dto = new PendenciaAcessoDTO();
        dto.setChave(pendencia.name());
        dto.setDescricao(pendencia.getDescricao());
        if(pendencia.equals(PendenciasAcessoEnum.PARQ_NEGATIVO) || pendencia.equals(PendenciasAcessoEnum.PARQ_NAO_RESPONDIDO)
                || pendencia.equals(PendenciasAcessoEnum.PARQ_POSITIVO)){
            parqAssinado = dto;
        } else if(aviso == null){
            aviso = dto;
        } else {
            avisos.add(dto);
        }
    }

    public boolean isDivisor() {
        return divisor;
    }

    public void setDivisor(boolean divisor) {
        this.divisor = divisor;
    }

    public String getHora() {
        return hora;
    }

    public void setHora(String hora) {
        this.hora = hora;
    }

    public Integer getMatricula() {
        return matricula;
    }

    public void setMatricula(Integer matricula) {
        this.matricula = matricula;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public boolean isParq() {
        return parq;
    }

    public void setParq(boolean parq) {
        this.parq = parq;
    }

    public PendenciaAcessoDTO getParqAssinado() {
        return parqAssinado;
    }

    public void setParqAssinado(PendenciaAcessoDTO parqAssinado) {
        this.parqAssinado = parqAssinado;
    }

    public String getPlano() {
        return plano;
    }

    public void setPlano(String plano) {
        this.plano = plano;
    }

    public String getFoto() {
        return foto;
    }

    public void setFoto(String foto) {
        this.foto = foto;
    }

    public String getCor() {
        return cor;
    }

    public void setCor(String cor) {
        this.cor = cor;
    }

    public boolean isAniversariante() {
        return aniversariante;
    }

    public void setAniversariante(boolean aniversariante) {
        this.aniversariante = aniversariante;
    }


    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public String getSituacaoContrato() {
        return situacaoContrato;
    }

    public void setSituacaoContrato(String situacaoContrato) {
        this.situacaoContrato = situacaoContrato;
    }

    public List<PendenciaAcessoDTO> getAvisos() {
        return avisos;
    }

    public void setAvisos(List<PendenciaAcessoDTO> avisos) {
        this.avisos = avisos;
    }

    public PendenciaAcessoDTO getAviso() {
        return aviso;
    }

    public void setAviso(PendenciaAcessoDTO aviso) {
        this.aviso = aviso;
    }

    public Integer getCodigoCliente() {
        return codigoCliente;
    }

    public void setCodigoCliente(Integer codigoCliente) {
        this.codigoCliente = codigoCliente;
    }

    public boolean isGympass() {
        return gympass;
    }

    public void setGympass(boolean gympass) {
        this.gympass = gympass;
    }

    public boolean isTotalpass() {
        return totalpass;
    }

    public void setTotalpass(boolean totalpass) {
        this.totalpass = totalpass;
    }

    public boolean isDiaria() {
        return diaria;
    }

    public void setDiaria(boolean diaria) {
        this.diaria = diaria;
    }

    public boolean isFreepass() {
        return freepass;
    }

    public void setFreepass(boolean freepass) {
        this.freepass = freepass;
    }

    public boolean isAulaAvulsa() {
        return aulaAvulsa;
    }

    public void setAulaAvulsa(boolean aulaAvulsa) {
        this.aulaAvulsa = aulaAvulsa;
    }

    public String getIdMemcached() {
        return idMemcached;
    }

    public void setIdMemcached(String idMemcached) {
        this.idMemcached = idMemcached;
    }
}
