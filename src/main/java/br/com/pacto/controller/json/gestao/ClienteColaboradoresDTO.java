package br.com.pacto.controller.json.gestao;

import br.com.pacto.controller.json.empresa.EmpresaDTO;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.List;

@JsonInclude
public class ClienteColaboradoresDTO {


    private String nome;
    private String matricula;
    private String urlFoto;
    private Integer pessoa;
    private Integer cliente;
    private Integer colaborador;
    private String  situacaoCliente;
    private String  situacaoClienteContrato;
    private String  situacaoColaborador;
    private boolean gympass = false;
    private boolean totalpass = false;
    private List<String> tipos;
    private List<String> emails;
    private List<String> telefones;
    private List<ClienteColaboradoresSituacaoDTO>  situacao;
    private List<EmpresaDTO> empresas;
    private boolean programaVigente;
    private String dataNascimento;
    private long dataUltimoAcesso;
    private long dataInicioProgramaTreino;
    private long dataFimProgramaTreino;
    private Integer pesoRisco;
    private Integer qtdAcompanhamentos;

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public List<String> getEmails() {
        return emails;
    }

    public void setEmails(List<String> emails) {
        this.emails = emails;
    }

    public List<ClienteColaboradoresSituacaoDTO> getSituacao() {
        return situacao;
    }

    public void setSituacao(List<ClienteColaboradoresSituacaoDTO> situacao) {
        this.situacao = situacao;
    }

    public List<EmpresaDTO> getEmpresas() {
        return empresas;
    }

    public void setEmpresas(List<EmpresaDTO> empresas) {
        this.empresas = empresas;
    }

    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public Integer getPessoa() {
        return pessoa;
    }

    public void setPessoa(Integer pessoa) {
        this.pessoa = pessoa;
    }

    public Integer getCliente() {
        return cliente;
    }

    public void setCliente(Integer cliente) {
        this.cliente = cliente;
    }

    public Integer getColaborador() {
        return colaborador;
    }

    public void setColaborador(Integer colaborador) {
        this.colaborador = colaborador;
    }

    public List<String> getTelefones() {
        return telefones;
    }

    public void setTelefones(List<String> telefones) {
        this.telefones = telefones;
    }

    public List<String> getTipos() {
        return tipos;
    }

    public void setTipos(List<String> tipos) {
        this.tipos = tipos;
    }

    public String getUrlFoto() {
        return urlFoto;
    }

    public void setUrlFoto(String urlFoto) {
        this.urlFoto = urlFoto;
    }

    public String getSituacaoCliente() {
        return situacaoCliente;
    }

    public void setSituacaoCliente(String situacaoCliente) {
        this.situacaoCliente = situacaoCliente;
    }

    public String getSituacaoClienteContrato() {
        return situacaoClienteContrato;
    }

    public void setSituacaoClienteContrato(String situacaoClienteContrato) {
        this.situacaoClienteContrato = situacaoClienteContrato;
    }

    public String getSituacaoColaborador() {
        return situacaoColaborador;
    }

    public void setSituacaoColaborador(String situacaoColaborador) {
        this.situacaoColaborador = situacaoColaborador;
    }

    public boolean isGympass() {
        return gympass;
    }

    public void setGympass(boolean gympass) {
        this.gympass = gympass;
    }

    public boolean isTotalpass() {
        return totalpass;
    }

    public void setTotalpass(boolean totalpass) {
        this.totalpass = totalpass;
    }

    public void setProgramaVigente(boolean programaVigente) {
        this.programaVigente = programaVigente;
    }

    public boolean isProgramaVigente() {
        return programaVigente;
    }

    public void setDataNascimento(String dataNascimento) {
        this.dataNascimento = dataNascimento;
    }

    public String getDataNascimento() {
        return dataNascimento;
    }

    public void setDataUltimoAcesso(long dataUltimoAcesso) {
        this.dataUltimoAcesso = dataUltimoAcesso;
    }

    public long getDataInicioProgramaTreino() {
        return dataInicioProgramaTreino;
    }

    public void setDataInicioProgramaTreino(long dataInicioProgramaTreino) {
        this.dataInicioProgramaTreino = dataInicioProgramaTreino;
    }

    public long getDataFimProgramaTreino() {
        return dataFimProgramaTreino;
    }

    public void setDataFimProgramaTreino(long dataFimProgramaTreino) {
        this.dataFimProgramaTreino = dataFimProgramaTreino;
    }

    public long getDataUltimoAcesso() {
        return dataUltimoAcesso;
    }

    public Integer getPesoRisco() {
        return pesoRisco;
    }

    public void setPesoRisco(Integer pesoRisco) {
        this.pesoRisco = pesoRisco;
    }

    public Integer getQtdAcompanhamentos() {
        return qtdAcompanhamentos;
    }

    public void setQtdAcompanhamentos(Integer qtdAcompanhamentos) {
        this.qtdAcompanhamentos = qtdAcompanhamentos;
    }
}
