/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pacto.controller.json.usuario;

import br.com.pacto.controller.json.base.SuperJSON;

/**
 *
 * <AUTHOR>
 */
public class UsuarioVersaoJSON extends SuperJSON {
    private Integer codUsuario;
    private String userName;
    private Integer versao;
    private String fotoAppUsuario;
    private Integer versaoFotoApp;

    public Integer getCodUsuario() {
        return codUsuario;
    }

    public void setCodUsuario(Integer codUsuario) {
        this.codUsuario = codUsuario;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public Integer getVersao() {
        return versao;
    }

    public void setVersao(Integer versao) {
        this.versao = versao;
    }

    public String getFotoAppUsuario() {
        if (fotoAppUsuario == null) {
            fotoAppUsuario = "";
        }
        return fotoAppUsuario;
    }

    public void setFotoAppUsuario(String fotoAppUsuario) {
        this.fotoAppUsuario = fotoAppUsuario;
    }

    public Integer getVersaoFotoApp() {
        if (versaoFotoApp == null) {
            versaoFotoApp = 0;
        }
        return versaoFotoApp;
    }

    public void setVersaoFotoApp(Integer versaoFotoApp) {
        this.versaoFotoApp = versaoFotoApp;
    }
}
