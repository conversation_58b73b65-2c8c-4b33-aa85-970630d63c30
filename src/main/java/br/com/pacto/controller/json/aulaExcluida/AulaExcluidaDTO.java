package br.com.pacto.controller.json.aulaExcluida;

import br.com.pacto.bean.aula.AulaDiaExclusao;
import br.com.pacto.bean.colaborador.ColaboradorSimplesTO;
import br.com.pacto.controller.json.agendamento.TurmaResponseDTO;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.Date;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class AulaExcluidaDTO {

    private Integer codigo;
    private Integer codigoHorarioTurma;
    private String nome;
    private ColaboradorSimplesTO professor;
    private ColaboradorSimplesTO professorSubstituto;
    private String nomeUsuario;
    private String dia;
    private Date dataAulaDia;
    private Date dataExclusao;

    public AulaExcluidaDTO() {
    }

    public AulaExcluidaDTO(AulaDiaExclusao aulaDiaExclusao, TurmaResponseDTO aula) {
        this.codigo = aulaDiaExclusao.getCodigo();
        this.codigoHorarioTurma = aulaDiaExclusao.getCodigoHorarioTurma();
        this.nome = aula.getNome();
        this.professor = aula.getProfessor();
        this.professorSubstituto = aula.getProfessorSubstituto();
        this.dia = aula.getDia();
        this.dataAulaDia = aulaDiaExclusao.getDataAulaDia();
        this.dataExclusao = aulaDiaExclusao.getDataExclusao();
    }

    public Integer getCodigo() { return codigo; }

    public void setCodigo(Integer codigo) { this.codigo = codigo; }

    public Integer getCodigoHorarioTurma() { return codigoHorarioTurma; }

    public void setCodigoHorarioTurma(Integer codigoHorarioTurma) { this.codigoHorarioTurma = codigoHorarioTurma; }

    public String getNome() { return nome; }

    public void setNome(String nome) { this.nome = nome; }

    public ColaboradorSimplesTO getProfessor() { return professor; }

    public void setProfessor(ColaboradorSimplesTO professor) { this.professor = professor; }

    public ColaboradorSimplesTO getProfessorSubstituto() { return professorSubstituto; }

    public void setProfessorSubstituto(ColaboradorSimplesTO professorSubstituto) { this.professorSubstituto = professorSubstituto; }

    public String getNomeUsuario() { return nomeUsuario; }

    public void setNomeUsuario(String nomeUsuario) { this.nomeUsuario = nomeUsuario; }

    public String getDia() { return dia; }

    public void setDia(String dia) { this.dia = dia; }

    public Date getDataAulaDia() { return dataAulaDia; }

    public void setDataAulaDia(Date dataAulaDia) { this.dataAulaDia = dataAulaDia; }

    public Date getDataExclusao() { return dataExclusao; }

    public void setDataExclusao(Date dataExclusao) { this.dataExclusao = dataExclusao; }
}
