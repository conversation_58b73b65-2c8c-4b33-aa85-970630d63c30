package br.com.pacto.controller.json.agendamento;

import br.com.pacto.controller.json.base.SuperJSON;
import br.com.pacto.objeto.JSONMapper;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

public class FiltroDisponibilidadeDTO extends SuperJSON {

    private List<Integer> professorIds;
    private List<Integer> tipoAgendamentoIds;
    private String parametro;
    private Boolean nome;
    private FiltrosNewDTO filterNew;

    public FiltroDisponibilidadeDTO(JSONObject filters) throws JSONException {
        if (filters != null) {
            try {
                this.filterNew = JSONMapper.getObject(filters.getJSONObject("filtrosNew"), FiltrosNewDTO.class);
            } catch (Exception ex) {
            }
            this.parametro = filters.optString("quicksearchValue");

            JSONArray colunasVisiveis = filters.optJSONArray("quicksearchFields");
            if (colunasVisiveis != null) {
                for (int i = 0; i < colunasVisiveis.length(); i++) {
                    if (colunasVisiveis.get(i).equals("nome")) {
                        this.nome = true;
                    }
                }
            }

            JSONArray colaboradorIds = filters.optJSONArray("professoresId");
            this.professorIds = new ArrayList<>();
            if (colaboradorIds != null) {
                for (int i = 0; i < colaboradorIds.length(); i++) {
                    getProfessorIds().add(colaboradorIds.getInt(i));
                }
            }

            JSONArray tiposAgendamentoId = filters.optJSONArray("tiposAgendamentoId");
            this.tipoAgendamentoIds = new ArrayList<>();
            if (tiposAgendamentoId != null) {
                for (int i = 0; i < tiposAgendamentoId.length(); i++) {
                    getTipoAgendamentoIds().add(tiposAgendamentoId.getInt(i));
                }
            }
        }
    }

    public List<Integer> getProfessorIds() {
        return professorIds;
    }

    public void setProfessorIds(List<Integer> professorIds) {
        this.professorIds = professorIds;
    }

    public List<Integer> getTipoAgendamentoIds() {
        return tipoAgendamentoIds;
    }

    public void setTipoAgendamentoIds(List<Integer> tipoAgendamentoIds) {
        this.tipoAgendamentoIds = tipoAgendamentoIds;
    }

    public String getParametro() {
        return parametro;
    }

    public void setParametro(String parametro) {
        this.parametro = parametro;
    }

    public Boolean getNome() {
        return nome;
    }

    public void setNome(Boolean nome) {
        this.nome = nome;
    }

    public FiltrosNewDTO getFilterNew() {
        return filterNew;
    }

    public void setFilterNew(FiltrosNewDTO filterNew) {
        this.filterNew = filterNew;
    }
}
