package br.com.pacto.controller.json.biapp;

import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class BiAppDTO {

    private Integer ativos;
    private Integer ativosComApp;
    private Integer ativosSemApp;
    private Integer inativosComApp;
    private Integer percentualAlunosUsamApp;
    private String ultimaAtualizacao;

    public Integer getAtivos() {
        return ativos;
    }

    public void setAtivos(Integer ativos) {
        this.ativos = ativos;
    }

    public Integer getAtivosComApp() {
        return ativosComApp;
    }

    public void setAtivosComApp(Integer ativosComApp) {
        this.ativosComApp = ativosComApp;
    }

    public Integer getAtivosSemApp() {
        return ativosSemApp;
    }

    public void setAtivosSemApp(Integer ativosSemApp) {
        this.ativosSemApp = ativosSemApp;
    }

    public Integer getInativosComApp() {
        return inativosComApp;
    }

    public void setInativosComApp(Integer inativosComApp) {
        this.inativosComApp = inativosComApp;
    }

    public Integer getPercentualAlunosUsamApp() {
        return percentualAlunosUsamApp;
    }

    public void setPercentualAlunosUsamApp(Integer percentualAlunosUsamApp) {
        this.percentualAlunosUsamApp = percentualAlunosUsamApp;
    }

    public String getUltimaAtualizacao() {
        return ultimaAtualizacao;
    }

    public void setUltimaAtualizacao(String ultimaAtualizacao) {
        this.ultimaAtualizacao = ultimaAtualizacao;
    }
}
