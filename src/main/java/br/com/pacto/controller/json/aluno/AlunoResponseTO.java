package br.com.pacto.controller.json.aluno;

import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.configuracoes.TimeZoneEnum;
import br.com.pacto.bean.nivel.NivelResponseTO;
import br.com.pacto.bean.pessoa.Email;
import br.com.pacto.bean.pessoa.SexoEnum;
import br.com.pacto.bean.pessoa.Telefone;
import br.com.pacto.bean.pessoa.TipoTelefoneEnum;
import br.com.pacto.bean.professor.ProfessorSintetico;
import br.com.pacto.bean.programa.ProfessorResponseTO;
import br.com.pacto.bean.programa.ProgramaTreino;
import br.com.pacto.bean.programa.ProgramaTreinoAndamento;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import com.fasterxml.jackson.annotation.JsonInclude;

import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by ulisses on 28/08/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AlunoResponseTO {

    private Integer id;
    private Integer matriculaZW;
    private String nome;
    private SituacaoAlunoEnum situacaoAluno;
    private TipoAcessoAulaEnum tipoAcesso;
    private NivelResponseTO nivel;
    private ProfessorResponseTO professor;
    private Date dataNascimento;
    private Date dataInclusao;
    private SexoEnum sexo;
    private List<String> emails = new ArrayList<>();
    private List<TelefoneDTO> fones = new ArrayList<>();
    private PlanoZWDTO planoZW;
    private ContratoZWDTO contratoZW;
    private SituacaoContratoZWEnum situacaoContratoZW;

    private Boolean usarApp = false;
    private String appUsername;
    private String nomeEmpresa;
    private String pressaoApresentar;
    private String fcApresentar;
    private List<String> listaObjetivos;

    private Integer idade;
    private String imageUri;

    private String nomeProgramaAtual;
    private Integer totalAulasPrevistas;
    private Integer nrTreinosRealizados;
    private String frequencia;
    private String codigoExterno;
    private Boolean resultadoParq;
    private String infoParQ; //NAO_ASSINADO - NEGATIVO - POSITIVO
    private Integer empresaCodigo;
    private Integer empresaCodZW;
    private Integer codigoCliente;
    private Integer codigoPassivo;
    private Integer codigoPessoa;

    private Boolean confirmado = Boolean.FALSE;
    private  List<ProgramaTreinoAlunoResponseDTO> programas;
    @Temporal(TemporalType.TIMESTAMP)
    private Date dataUltimoTreino;
    @Temporal(TemporalType.TIMESTAMP)
    private Date terminoPrograma;

    private Boolean autorizado = false;
    private String codAcesso = "";

    private String justificativa;
    private Boolean parq_status;

    public AlunoResponseTO() {
    }

    public AlunoResponseTO(ClienteSintetico cs, Boolean confirmado, Boolean treinoIndependente) {
        this(cs,  null,  null,null, null,null,null, confirmado, null, treinoIndependente, null,null );
    }
    public AlunoResponseTO(ClienteSintetico cs, Boolean confirmado, Boolean treinoIndependente, ProfessorSintetico professorSintetico, Boolean parq_status) {
        this(cs,  null,  null,null, null,null,null, confirmado, null, treinoIndependente, professorSintetico, parq_status);
    }

    public AlunoResponseTO(ClienteSintetico cs, Usuario usuarioAluno, ProgramaTreinoAndamento programaTreinoAndamento, Usuario usuarioProfessor, Boolean treinoIndependente) {
       this(cs,  usuarioAluno,  usuarioProfessor,programaTreinoAndamento, null,null,null, null, null, treinoIndependente, null, null);
    }

    public AlunoResponseTO(
            ClienteSintetico cs,
            Usuario usuarioAluno,
            Usuario usuarioProfessor,
            ProgramaTreinoAndamento programaTreinoAndamento,
            List<ProgramaTreino> listProgramas ,
            Date dataUltimoTreino,
            Date terminoPrograma,
            Boolean confirmado, Boolean resultadoParq,
            Boolean treinoIndependente, ProfessorSintetico professorSintetico, Boolean parq_status) {

                    this.confirmado = confirmado;
                    this.id = cs.getCodigo();
                    try {
                        this.codigoCliente = cs.getCodigoCliente();
                    }catch (Exception e){
                        Uteis.logar(e, AlunoResponseTO.class);
                    }
                    this.matriculaZW = cs.getMatricula();
                    this.nome = cs.getNome();
                    this.situacaoAluno = SituacaoAlunoEnum.getInstance(cs.getSituacao());
                    this.pressaoApresentar = cs.getPressaoApresentar();
                    this.fcApresentar = cs.getFcApresentar();
                    this.listaObjetivos = cs.getObjetivosLista();
                    this.dataUltimoTreino = dataUltimoTreino;
                    this.terminoPrograma =terminoPrograma;
                    this.codigoExterno = cs.getCodigoExterno();
                    this.codigoPessoa = cs.getCodigoPessoa();

                    if (cs.getProgramaVigente() != null) {
                        this.nomeProgramaAtual = cs.getProgramaVigente().getNome();
                        this.totalAulasPrevistas = cs.getProgramaVigente().getTotalAulasPrevistas() == null ? 0 : cs.getProgramaVigente().getTotalAulasPrevistas();

                        if (programaTreinoAndamento != null) {
                            this.frequencia = programaTreinoAndamento.getFrequenciaPercent();
                            this.nrTreinosRealizados = programaTreinoAndamento.getNrTreinos() == null ? 0 : programaTreinoAndamento.getNrTreinos();
                        }
                    }

                    List<ProgramaTreinoAlunoResponseDTO> listProg = new ArrayList<>();
                    if (listProgramas != null && listProgramas.size() > 0) {
                        for (ProgramaTreino programa : listProgramas) {
                            ProgramaTreinoAlunoResponseDTO programaAluno = new ProgramaTreinoAlunoResponseDTO(programa,
                                    cs.getEmpresaTreino() == null ? TimeZoneEnum.Brazil_GTM_3.getId() : cs.getEmpresaTreino().getTimeZoneDefault(), treinoIndependente);
                            listProg.add(programaAluno);
                        }
                    }
                    this.programas = listProg;

                    if(cs.getNivelAluno() != null){
                        this.nivel = new NivelResponseTO(cs.getNivelAluno());
                    }
                    if (usuarioProfessor != null) {
                        this.professor = new ProfessorResponseTO(usuarioProfessor, treinoIndependente);
                    } else if (professorSintetico != null) {
                        this.professor = new ProfessorResponseTO(professorSintetico, treinoIndependente, false);
                    }
                    this.dataNascimento = cs.getDataNascimento();
                    this.idade = cs.getMesesIdade();
                    if (cs.getSexo() != null && !cs.getSexo().trim().isEmpty()) {
                        try {
                            this.sexo = SexoEnum.valueOf(cs.getSexo().substring(0, 1).toUpperCase());
                        } catch (IllegalArgumentException e) {
                            this.sexo = SexoEnum.N;
                        }
                    } else {
                        this.sexo = SexoEnum.N;
                    }
                    if (treinoIndependente) {
                        for (Email email : cs.getPessoa().getEmails()) {
                            this.emails.add(email.getEmail());
                        }

                        for (Telefone telefone : cs.getPessoa().getTelefones()) {
                            TelefoneDTO telefoneDTO = new TelefoneDTO();
                            telefoneDTO.setNumero(telefone.getTelefone());
                            telefoneDTO.setTipo(telefone.getTipo());
                            this.fones.add(telefoneDTO);
                        }
                        this.dataInclusao = cs.getDataMatricula();
                    } else {
                        this.planoZW = new PlanoZWDTO(cs.getNomeplano());
                        this.contratoZW = new ContratoZWDTO(cs.getDataVigenciaAteAjustada(), TipoContratoZWEnum.getInstance(cs.getSituacaoMatriculaContrato()));
                        this.situacaoContratoZW = SituacaoContratoZWEnum.getInstance(cs.getSituacaoContrato());
                        this.emails = cs.getListaEmails();
                        for (String telefone : cs.getListaTelefones()) {
                            TelefoneDTO telefoneDTO = new TelefoneDTO();
                            telefoneDTO.setNumero(telefone);
                            if (telefone.length() == 13) {
                                telefoneDTO.setTipo(TipoTelefoneEnum.CELULAR);
                            } else {
                                telefoneDTO.setTipo(TipoTelefoneEnum.FIXO);
                            }
                            this.fones.add(telefoneDTO);
                        }
                    }

                    if (usuarioAluno != null) {
                        try {
                            Integer.parseInt(usuarioAluno.getUserName());
                        } catch (Exception e) {
                            this.appUsername = usuarioAluno.getUserName();
                            this.usarApp = true;
                        }
                    }
                    this.imageUri= cs.getUrlFoto();
                    this.resultadoParq = resultadoParq;
                    this.parq_status = parq_status;

                    if (cs.getFreePassInicio() != null && cs.getFreePassFim() != null && Calendario.entreComHoraZerada(Calendario.hoje(), cs.getFreePassInicio(), cs.getFreePassFim())
                            && (this.situacaoAluno == SituacaoAlunoEnum.VISITANTE || this.situacaoAluno == SituacaoAlunoEnum.INATIVO)) {
                        this.situacaoContratoZW = SituacaoContratoZWEnum.FREE_PASS;
                    }

    }

    public AlunoResponseTO(ClienteSintetico clienteSintetico) {
        this.id = id;
        this.nome = nome;
        this.dataNascimento = dataNascimento;
        this.sexo = sexo;
        this.emails = emails;
        this.fones = fones;
    }

    public AlunoResponseTO(Integer id, String nome, Boolean autorizado, String codAcesso) {
        this.id = id;
        this.nome = nome;
        if(autorizado){
            this.matriculaZW = id;
        }
        this.autorizado = autorizado;
        this.codAcesso = codAcesso;
    }


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getMatriculaZW() {
        return matriculaZW;
    }

    public void setMatriculaZW(Integer matriculaZW) {
        this.matriculaZW = matriculaZW;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public TipoAcessoAulaEnum getTipoAcesso() {
        return tipoAcesso;
    }

    public void setTipoAcesso(TipoAcessoAulaEnum tipoAcesso) {
        this.tipoAcesso = tipoAcesso;
    }

    public NivelResponseTO getNivel() {
        return nivel;
    }

    public void setNivel(NivelResponseTO nivel) {
        this.nivel = nivel;
    }

    public ProfessorResponseTO getProfessor() {
        return professor;
    }

    public void setProfessor(ProfessorResponseTO professor) {
        this.professor = professor;
    }

    public Date getDataNascimento() {
        return dataNascimento;
    }

    public void setDataNascimento(Date dataNascimento) {
        this.dataNascimento = dataNascimento;
    }

    public Date getDataInclusao() {
        return dataInclusao;
    }

    public void setDataInclusao(Date dataInclusao) {
        this.dataInclusao = dataInclusao;
    }

    public Integer getIdade() {
        return idade;
    }

    public void setIdade(Integer idade) {
        this.idade = idade;
    }

    public SexoEnum getSexo() {
        return sexo;
    }

    public void setSexo(SexoEnum sexo) {
        this.sexo = sexo;
    }

    public List<String> getEmails() {
        return emails;
    }

    public void setEmails(List<String> emails) {
        this.emails = emails;
    }

    public List<TelefoneDTO> getFones() {
        return fones;
    }

    public void setFones(List<TelefoneDTO> fones) {
        this.fones = fones;
    }

    public String getAppUsername() {
        return appUsername;
    }

    public void setAppUsername(String appUsername) {
        this.appUsername = appUsername;
    }

    public SituacaoAlunoEnum getSituacaoAluno() {
        return situacaoAluno;
    }

    public void setSituacaoAluno(SituacaoAlunoEnum situacaoAluno) {
        this.situacaoAluno = situacaoAluno;
    }

    public String getImageUri() {
        return imageUri;
    }

    public void setImageUri(String imageUri) {
        this.imageUri = imageUri;
    }

    public String getPressaoApresentar() {
        return pressaoApresentar;
    }

    public void setPressaoApresentar(String pressaoApresentar) {
        this.pressaoApresentar = pressaoApresentar;
    }

    public String getFcApresentar() {
        return fcApresentar;
    }

    public void setFcApresentar(String fcApresentar) {
        this.fcApresentar = fcApresentar;
    }

    public List<String> getListaObjetivos() {
        return listaObjetivos;
    }

    public void setListaObjetivos(List<String> listaObjetivos) {
        this.listaObjetivos = listaObjetivos;
    }

    public String getNomeProgramaAtual() {
        return nomeProgramaAtual;
    }

    public void setNomeProgramaAtual(String nomeProgramaAtual) {
        this.nomeProgramaAtual = nomeProgramaAtual;
    }

    public Integer getNrTreinosRealizados() {
        return nrTreinosRealizados;
    }

    public void setNrTreinosRealizados(Integer nrTreinosRealizados) {
        this.nrTreinosRealizados = nrTreinosRealizados;
    }

    public Integer getTotalAulasPrevistas() {
        return totalAulasPrevistas;
    }

    public void setTotalAulasPrevistas(Integer totalAulasPrevistas) {
        this.totalAulasPrevistas = totalAulasPrevistas;
    }

    public String getFrequencia() {
        return frequencia;
    }

    public void setFrequencia(String frequencia) {
        this.frequencia = frequencia;
    }

    public List<ProgramaTreinoAlunoResponseDTO> getProgramas() {
        return programas;
    }

    public void setProgramas(List<ProgramaTreinoAlunoResponseDTO> programas) {
        this.programas = programas;
    }

    public Date getDataUltimoTreino() { return dataUltimoTreino;  }

    public void setDataUltimoTreino(Date dataUltimoTreino) { this.dataUltimoTreino = dataUltimoTreino; }

    public Date getTerminoPrograma() { return terminoPrograma; }

    public void setTerminoPrograma(Date terminoPrograma) { this.terminoPrograma = terminoPrograma; }

    public Boolean getConfirmado() {
        return confirmado;
    }

    public void setConfirmado(Boolean confirmado) {
        this.confirmado = confirmado;
    }

    public String getCodigoExterno() {
        return codigoExterno;
    }

    public void setCodigoExterno(String codigoExterno) {
        this.codigoExterno = codigoExterno;
    }

    public Boolean getUsarApp() {
        return usarApp;
    }

    public void setUsarApp(Boolean usarApp) {
        this.usarApp = usarApp;
    }

    public Boolean getResultadoParq() {
        return resultadoParq;
    }

    public void setResultadoParq(Boolean resultadoParq) {
        this.resultadoParq = resultadoParq;
    }

    public PlanoZWDTO getPlanoZW() {
        return planoZW;
    }

    public void setPlanoZW(PlanoZWDTO planoZW) {
        this.planoZW = planoZW;
    }

    public ContratoZWDTO getContratoZW() {
        return contratoZW;
    }

    public void setContratoZW(ContratoZWDTO contratoZW) {
        this.contratoZW = contratoZW;
    }

    public SituacaoContratoZWEnum getSituacaoContratoZW() {
        return situacaoContratoZW;
    }

    public void setSituacaoContratoZW(SituacaoContratoZWEnum situacaoContratoZW) {
        this.situacaoContratoZW = situacaoContratoZW;
    }

    public Integer getCodigoCliente() {
        return codigoCliente;
    }

    public void setCodigoCliente(Integer codigoCliente) {
        this.codigoCliente = codigoCliente;
    }

    public Boolean getAutorizado() {
        return autorizado;
    }

    public void setAutorizado(Boolean autorizado) {
        this.autorizado = autorizado;
    }

    public String getCodAcesso() {
        return codAcesso;
    }

    public void setCodAcesso(String codAcesso) {
        this.codAcesso = codAcesso;
    }

    public String getJustificativa() { return justificativa; }

    public void setJustificativa(String justificativa) { this.justificativa = justificativa; }

    public String getNomeEmpresa() {
        return nomeEmpresa;
    }

    public void setNomeEmpresa(String nomeEmpresa) {
        this.nomeEmpresa = nomeEmpresa;
    }

    public Integer getCodigoPassivo() {
        return codigoPassivo;
    }

    public void setCodigoPassivo(Integer codigoPassivo) {
        this.codigoPassivo = codigoPassivo;
    }

    public String getInfoParQ() {
        return infoParQ;
    }

    public void setInfoParQ(String infoParQ) {
        //NAO_ASSINADO - NEGATIVO - POSITIVO
        this.infoParQ = infoParQ;
    }

    public Integer getEmpresaCodigo() {
        return empresaCodigo;
    }

    public void setEmpresaCodigo(Integer empresaCodigo) {
        this.empresaCodigo = empresaCodigo;
    }

    public Integer getEmpresaCodZW() {
        return empresaCodZW;
    }

    public void setEmpresaCodZW(Integer empresaCodZW) {
        this.empresaCodZW = empresaCodZW;
    }

    public Integer getCodigoPessoa() {
        return codigoPessoa;
    }

    public void setCodigoPessoa(Integer codigoPessoa) {
        this.codigoPessoa = codigoPessoa;
    }

    public Boolean getParq_status() {
        return parq_status;
    }

    public void setParq_status(Boolean parq_status) {
        this.parq_status = parq_status;
    }
}
