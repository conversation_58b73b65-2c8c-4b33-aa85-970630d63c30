package br.com.pacto.controller.json.mgb;

import br.com.pacto.controller.json.base.SuperJSON;

public class ConfigMgb extends SuperJSON {
    private Integer empresa; // referencia ao codzw da empresa
    private String token;
    private String nome;

    public ConfigMgb() {
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

}
