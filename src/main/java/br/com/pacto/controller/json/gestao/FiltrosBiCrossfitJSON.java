package br.com.pacto.controller.json.gestao;

import br.com.pacto.controller.json.base.SuperJSON;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.util.enumeradores.DiasSemana;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

/**
 * <AUTHOR> 02/04/2019
 */
public class FiltrosBiCrossfitJSON extends SuperJSON {

    private Integer mes;
    private Integer ano;
    private Integer idProfessor;
    private boolean filtroNome;
    private String filtro;
    private DiasSemana diasSemana;
    private String turno;

    public FiltrosBiCrossfitJSON(JSONObject filtros) throws JSONException {
        this.mes = filtros.optInt("mes");
        this.ano = filtros.optInt("ano");
        this.idProfessor = filtros.optInt("idProfessor");
        this.filtro = filtros.optString("quicksearchValue").trim();
        this.diasSemana = DiasSemana.getDiaSemanaChave(filtros.optString("diasSemana").toLowerCase().trim());
        this.turno = filtros.optString("turno").toLowerCase().trim();
        setTurno(Uteis.retirarAcentuacaoRegex(getTurno().replace("£", "")));

        JSONArray colunasVisiveis = filtros.optJSONArray("quicksearchFields");
        if (colunasVisiveis != null) {
            for (int i = 0; i < colunasVisiveis.length(); i++) {
                if (colunasVisiveis.get(i).equals("nome")) {
                    this.filtroNome = true;
                }
            }

        }
    }

    public Integer getMes() {
        return mes;
    }

    public void setMes(Integer mes) {
        this.mes = mes;
    }

    public Integer getAno() {
        return ano;
    }

    public void setAno(Integer ano) {
        this.ano = ano;
    }

    public Integer getIdProfessor() {
        return idProfessor;
    }

    public void setIdProfessor(Integer idProfessor) {
        this.idProfessor = idProfessor;
    }

    public boolean isFiltroNome() {
        return filtroNome;
    }

    public void setFiltroNome(boolean filtroNome) {
        this.filtroNome = filtroNome;
    }

    public String getFiltro() {
        return filtro;
    }

    public void setFiltro(String filtro) {
        this.filtro = filtro;
    }

    public DiasSemana getDiasSemana() {
        return diasSemana;
    }

    public void setDiasSemana(DiasSemana diasSemana) {
        this.diasSemana = diasSemana;
    }

    public String getTurno() {
        return turno;
    }

    public void setTurno(String turno) {
        this.turno = turno;
    }
}
