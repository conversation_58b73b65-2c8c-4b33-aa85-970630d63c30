/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pacto.controller.json.usuario;

import br.com.pacto.base.impl.EntityManagerFactoryService;
import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.pessoa.Pessoa;
import br.com.pacto.controller.json.base.SuperControle;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.intf.cliente.ClienteSinteticoService;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.logging.Level;
import java.util.logging.Logger;
import javax.servlet.ServletContext;

import br.com.pacto.service.intf.pessoa.PessoaService;
import br.com.pacto.service.intf.usuario.FotoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("/fotos")
public class FotosJSONControle extends SuperControle {

    private ServletContext sc;
    @Autowired
    private ClienteSinteticoService cs;
    @Autowired
    private PessoaService pessoaService;
    @Autowired
    private FotoService fotoService;

    @Override
    public void setServletContext(ServletContext servletContext) {
        this.sc = servletContext;
    }

    @RequestMapping(value = "{ctx}/atualizarEmpresa", method = RequestMethod.GET)
    public @ResponseBody
    ModelMap atualizarEmpresa(@PathVariable String ctx) {
        ModelMap mm = new ModelMap();
        try {
            if (SuperControle.independente(ctx)) {
                List<Pessoa> pessoas = pessoaService.obterTodas(ctx);
                pessoas.stream().forEach(e -> fotoService.migracaoFotoPessoa(sc, ctx, e.getCodigo(), e.getFotoKey()));
            } else {
                Aplicacao.baixarTodasAsFotos(sc, ctx);
            }
            mm.addAttribute(RETURN, ctx + " " + STATUS_SUCESSO);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(FotosJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "/atualizarTodas", method = RequestMethod.GET)
    public @ResponseBody
    ModelMap atualizarTodas() {
        ModelMap mm = new ModelMap();
        try {
            Set<String> keys = EntityManagerFactoryService.getEmpresas().keySet();
            for (String k : keys) {
                Aplicacao.baixarTodasAsFotos(sc, k);
                mm.addAttribute(RETURN, k + " " + STATUS_SUCESSO);
            }
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(FotosJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }
}
