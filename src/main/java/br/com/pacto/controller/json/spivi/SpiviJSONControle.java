package br.com.pacto.controller.json.spivi;

import br.com.pacto.controller.json.aulaDia.AulaDiaJSONControle;
import br.com.pacto.controller.json.base.SuperControle;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.service.impl.spivi.SpiviService;
import br.com.pacto.service.intf.empresa.EmpresaService;
import br.com.pacto.util.UtilContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import servicos.integracao.zw.IntegracaoTurmasWSConsumer;
import servicos.integracao.zw.client.EventJSON;

import java.util.logging.Level;
import java.util.logging.Logger;

@Controller
@RequestMapping("/spivi")
public class SpiviJSONControle extends SuperControle {

    @RequestMapping(value = "{ctx}/aula", method = RequestMethod.GET)
    public @ResponseBody
    ModelMap obterAulaSpivi(@PathVariable final String ctx,
                            @RequestParam final Integer horarioTurmaCodigo,
                            @RequestParam final String data) {

        ModelMap mm = new ModelMap();
        try {
            IntegracaoTurmasWSConsumer integracaoWS = UtilContext.getBean(IntegracaoTurmasWSConsumer.class);

            EventJSON event = integracaoWS.obterAulaSpivi( ctx, horarioTurmaCodigo, data);
            mm.addAttribute("aula", event);

        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage().replaceFirst("ERRO:", ""));
            Logger.getLogger(AulaDiaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }
}
