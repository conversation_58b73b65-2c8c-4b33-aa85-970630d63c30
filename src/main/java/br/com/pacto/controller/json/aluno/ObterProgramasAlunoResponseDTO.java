package br.com.pacto.controller.json.aluno;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "DTO de resposta para a consulta de programas de treino do aluno")
public class ObterProgramasAlunoResponseDTO {

    @ApiModelProperty(value = "Lista de programas de treino do aluno", dataType = "List[ProgramaTreinoAlunoResponseDTO]")
    private List<ProgramaTreinoAlunoResponseDTO> content;

    @ApiModelProperty(value = "Total de elementos", example = "2")
    private Integer totalElements;

    @ApiModelProperty(value = "Total de páginas", example = "0")
    private Integer totalPages;

    @ApiModelProperty(value = "Indica se é a primeira página", example = "true")
    private Boolean first;

    @ApiModelProperty(value = "Indica se é a última página", example = "true")
    private Boolean last;

    @ApiModelProperty(value = "Tamanho da página", example = "50")
    private Integer size;

    @ApiModelProperty(value = "Número da página atual", example = "0")
    private Integer number;

    // Getters e Setters
    public List<ProgramaTreinoAlunoResponseDTO> getContent() {
        return content;
    }
    public void setContent(List<ProgramaTreinoAlunoResponseDTO> content) {
        this.content = content;
    }
    public Integer getTotalElements() {
        return totalElements;
    }
    public void setTotalElements(Integer totalElements) {
        this.totalElements = totalElements;
    }
    public Integer getTotalPages() {
        return totalPages;
    }
    public void setTotalPages(Integer totalPages) {
        this.totalPages = totalPages;
    }
    public Boolean getFirst() {
        return first;
    }
    public void setFirst(Boolean first) {
        this.first = first;
    }
    public Boolean getLast() {
        return last;
    }
    public void setLast(Boolean last) {
        this.last = last;
    }
    public Integer getSize() {
        return size;
    }
    public void setSize(Integer size) {
        this.size = size;
    }
    public Integer getNumber() {
        return number;
    }
    public void setNumber(Integer number) {
        this.number = number;
    }
}
