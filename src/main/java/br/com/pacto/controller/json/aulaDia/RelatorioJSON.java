/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.controller.json.aulaDia;

import br.com.pacto.bean.aula.AulaAluno;
import br.com.pacto.controller.json.base.SuperJSON;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.util.json.AgendaTotalJSON;

/**
 *
 * <AUTHOR>
 */
public class RelatorioJSON extends SuperJSON {
        
    private String data; 
    private String diaSemana; 
    private String modalidade; 
    private String horario; 
    private String professor; 
    private Integer bonus; 
    
    public RelatorioJSON(AulaAluno aluno) {
        try {
//            data = Uteis.getData(aluno.getAula().getInicio());
//            diaSemana = Uteis.getDiaDaSemana(aluno.getAula().getInicio());
//            modalidade = aluno.getAula().getAula().getModalidade().getNome();
//            horario = Uteis.getDataAplicandoFormatacao(aluno.getAula().getInicio(), "HH:mm");
//            professor = aluno.getProfessor().getNomeAbreviado();
//            bonus = aluno.getAula().getAula().getPontosBonus();
        } catch (Exception e) {
        }
    }
    public RelatorioJSON(AgendaTotalJSON aluno) {
        try {
            //TODO: JOÃO ALCIDES
            data = aluno.getInicio();
//            diaSemana = Uteis.getDiaDaSemana(aluno.getAula().getInicio());
            modalidade = aluno.getTipo();
//            horario = Uteis.getDataAplicandoFormatacao(aluno., "HH:mm");
            professor = aluno.getResponsavel();
//            bonus = aluno.getAula().getAula().getPontosBonus();
        } catch (Exception e) {
        }
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public String getDiaSemana() {
        return diaSemana;
    }

    public void setDiaSemana(String diaSemana) {
        this.diaSemana = diaSemana;
    }

    public String getModalidade() {
        return modalidade;
    }

    public void setModalidade(String modalidade) {
        this.modalidade = modalidade;
    }

    public String getHorario() {
        return horario;
    }

    public void setHorario(String horario) {
        this.horario = horario;
    }

    public String getProfessor() {
        return professor;
    }

    public void setProfessor(String professor) {
        this.professor = professor;
    }

    public Integer getBonus() {
        return bonus;
    }

    public void setBonus(Integer bonus) {
        this.bonus = bonus;
    }
    
    

}
