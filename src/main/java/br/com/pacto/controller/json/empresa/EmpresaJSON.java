/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.controller.json.empresa;

import servicos.integracao.adm.client.EmpresaWS;

/**
 *
 * <AUTHOR>
 */
public class EmpresaJSON {

    private String id;
    private Long codigo;
    private String nome;
    private String telefone;

    public EmpresaJSON(){}

    public EmpresaJSON(EmpresaWS empresaWS){
      this.id = empresaWS.getCodigo().toString();
      this.nome = empresaWS.getNome();
      this.telefone = empresaWS.getNumero();
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getTelefone() {
        return telefone;
    }

    public void setTelefone(String telefone) {
        this.telefone = telefone;
    }

    public Long getCodigo() {
        return codigo;
    }

    public void setCodigo(Long codigo) {
        this.codigo = codigo;
    }
}
