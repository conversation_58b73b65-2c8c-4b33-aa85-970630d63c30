package br.com.pacto.controller.json.aluno;

import br.com.pacto.bean.pessoa.SexoEnum;
import br.com.pacto.bean.pessoa.TipoTelefoneEnum;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.util.UteisValidacao;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.math.BigInteger;
import java.security.SecureRandom;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by ulisses on 28/08/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AlunoDTO {

    private Integer codigo;
    private String nome;
    private Integer nivelId;
    private Integer professorId;
    private Date dataNascimento;
    private SexoEnum sexo;
    private List<String> emails;
    private List<TelefoneDTO> fones;
    private Boolean usarApp;
    private String appUsername;
    private String appPassword;
    private String situacaoAluno;
    private String imagemData;
    private String extensaoImagem;
    private String codigoExterno;

    public AlunoDTO() {
    }

    public AlunoDTO(AlunoCadastroSimplesDTO alunoCadastroSimplesDTO) {
        this.nome = alunoCadastroSimplesDTO.getNome();
        try {
            this.dataNascimento = new Date(alunoCadastroSimplesDTO.getDataNascimento());
        }catch (Exception e){
            Uteis.logar(e, AlunoDTO.class);
        }
        if (alunoCadastroSimplesDTO.getSexo() != null && !alunoCadastroSimplesDTO.getSexo().isEmpty()) {
            try {
                this.sexo = SexoEnum.valueOf(alunoCadastroSimplesDTO.getSexo().substring(0, 1).toUpperCase());
            } catch (IllegalArgumentException e) {
                this.sexo = SexoEnum.N;
            }
        } else {
            this.sexo = SexoEnum.N;
        }
        if(!UteisValidacao.emptyString(alunoCadastroSimplesDTO.getEmail())) {
            this.appUsername = alunoCadastroSimplesDTO.getEmail();
            SecureRandom random = new SecureRandom();
            this.appPassword = new BigInteger(130, random).toString(32);
            this.appPassword = this.appPassword.length() > 8 ? this.appPassword.substring(0, 8) : this.appPassword;
            this.emails = new ArrayList<String>() {{
                add(alunoCadastroSimplesDTO.getEmail());
            }};
        }
        this.fones = new ArrayList<TelefoneDTO>(){{
            if(!UteisValidacao.emptyString(alunoCadastroSimplesDTO.getCelular())){
                TelefoneDTO telefoneDTO = new TelefoneDTO();
                telefoneDTO.setTipo(TipoTelefoneEnum.CELULAR);
                telefoneDTO.setNumero(alunoCadastroSimplesDTO.getCelular());
                add(telefoneDTO);
            }
        }};
        this.usarApp = true;
        this.situacaoAluno = "ATIVO";
        this.imagemData = alunoCadastroSimplesDTO.getImagemData();
        this.extensaoImagem = ".jpg";
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Integer getNivelId() {
        return nivelId;
    }

    public void setNivelId(Integer nivelId) {
        this.nivelId = nivelId;
    }

    public Integer getProfessorId() {
        return professorId;
    }

    public void setProfessorId(Integer professorId) {
        this.professorId = professorId;
    }

    public Date getDataNascimento() {
        return dataNascimento;
    }

    public void setDataNascimento(Date dataNascimento) {
        this.dataNascimento = dataNascimento;
    }

    public SexoEnum getSexo() {
        return sexo;
    }

    public void setSexo(SexoEnum sexo) {
        this.sexo = sexo;
    }

    public List<String> getEmails() {
        return emails;
    }

    public void setEmails(List<String> emails) {
        this.emails = emails;
    }

    public List<TelefoneDTO> getFones() {
        return fones;
    }

    public void setFones(List<TelefoneDTO> fones) {
        this.fones = fones;
    }

    public Boolean getUsarApp() {
        return usarApp;
    }

    public void setUsarApp(Boolean usarApp) {
        this.usarApp = usarApp;
    }

    public String getAppUsername() {
        return appUsername;
    }

    public void setAppUsername(String appUsername) {
        this.appUsername = appUsername;
    }

    public String getAppPassword() {
        return appPassword;
    }

    public void setAppPassword(String appPassword) {
        this.appPassword = appPassword;
    }

    public String getSituacaoAluno() {
        return situacaoAluno;
    }

    public void setSituacaoAluno(String situacaoAluno) {
        this.situacaoAluno = situacaoAluno;
    }

    public String getImagemData() {
        return imagemData;
    }

    public void setImagemData(String imagemData) {
        this.imagemData = imagemData;
    }

    public String getExtensaoImagem() {
        return extensaoImagem;
    }

    public void setExtensaoImagem(String extensaoImagem) {
        this.extensaoImagem = extensaoImagem;
    }

    public String getCodigoExterno() {
        return codigoExterno;
    }

    public void setCodigoExterno(String codigoExterno) {
        this.codigoExterno = codigoExterno;
    }
}
