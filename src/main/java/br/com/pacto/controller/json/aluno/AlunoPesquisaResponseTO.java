package br.com.pacto.controller.json.aluno;

import br.com.pacto.bean.cliente.ClientePesquisa;
import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class AlunoPesquisaResponseTO {
    private Integer matricula;
    private String nome;
    private String imageUri;

    public AlunoPesquisaResponseTO() {
    }

    public AlunoPesquisaResponseTO(ClientePesquisa clientePesquisa) {
        this.matricula = clientePesquisa.getMatricula();
        this.nome = clientePesquisa.getNome();
        this.imageUri = clientePesquisa.getUrlFoto();
    }

    public Integer getMatricula() {
        return matricula;
    }

    public void setMatricula(Integer matricula) {
        this.matricula = matricula;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getImageUri() {
        return imageUri;
    }

    public void setImageUri(String imageUri) {
        this.imageUri = imageUri;
    }
}
