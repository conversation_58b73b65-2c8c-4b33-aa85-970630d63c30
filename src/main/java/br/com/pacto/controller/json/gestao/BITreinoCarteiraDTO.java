package br.com.pacto.controller.json.gestao;

import br.com.pacto.bean.bi.DashboardBI;

public class BITreinoCarteiraDTO {

//            - totalAlunos
    private Integer totalAlunos;
    private Integer ativos;
    private Integer inativos;
    private Integer visitantes;
    private Integer totalRenovacoesCarteira;
    private Integer totalNaoRenovacoesCarteira;
    private Integer totalAlunosSemAcompanhamento;
    private Integer totalAlunosEmAcompanhamento;

    public BITreinoCarteiraDTO(DashboardBI dash) {
        this.totalAlunos = dash.getTotalAlunos();
        this.ativos = dash.getTotalAlunosAtivos();
        this.inativos = dash.getTotalAlunosInativos();
        this.visitantes = dash.getTotalAlunosVisitantes();
        this.taxaRenovacaoZW = dash.getPercentualRenovacoes();
        this.aVencerZW = dash.getTotalAlunosAvencer();
        this.tempoPermanenciaCarteiraZW = new TempoPermanenciaDTO(dash);
        this.fluxoCarteiraZW = new FluxoCarteiraZWDTO(dash);
        this.totalRenovacoesCarteira = dash.getTotalRenovacoesCarteira();
        this.totalNaoRenovacoesCarteira = dash.getTotalNaoRenovaramCarteira();
        this.totalAlunosSemAcompanhamento = dash.getTotalAlunosSemAcompanhamento();
    }

    //    - Nº de contratos vencidos e renovados nos últimos 30 dias / nº de contratos vencidos nos últimos 30 dias).
    private Integer taxaRenovacaoZW;
    //- Alunos cujos contratos estão a vencer em 30 dias.
    private Integer aVencerZW;

//    Caso um professor tenha sido definido, mostrar os seguintes campos no dto:
    private TempoPermanenciaDTO tempoPermanenciaCarteiraZW = new TempoPermanenciaDTO();

    private FluxoCarteiraZWDTO fluxoCarteiraZW = new FluxoCarteiraZWDTO();

    public Integer getTotalAlunos() {
        return totalAlunos;
    }

    public void setTotalAlunos(Integer totalAlunos) {
        this.totalAlunos = totalAlunos;
    }

    public Integer getAtivos() {
        return ativos;
    }

    public void setAtivos(Integer ativos) {
        this.ativos = ativos;
    }

    public Integer getInativos() {
        return inativos;
    }

    public void setInativos(Integer inativos) {
        this.inativos = inativos;
    }

    public Integer getVisitantes() {
        return visitantes;
    }

    public void setVisitantes(Integer visitantes) {
        this.visitantes = visitantes;
    }

    public Integer getTaxaRenovacaoZW() {
        return taxaRenovacaoZW;
    }

    public void setTaxaRenovacaoZW(Integer taxaRenovacaoZW) {
        this.taxaRenovacaoZW = taxaRenovacaoZW;
    }

    public Integer getaVencerZW() {
        return aVencerZW;
    }

    public void setaVencerZW(Integer aVencerZW) {
        this.aVencerZW = aVencerZW;
    }

    public TempoPermanenciaDTO getTempoPermanenciaCarteiraZW() {
        return tempoPermanenciaCarteiraZW;
    }

    public void setTempoPermanenciaCarteiraZW(TempoPermanenciaDTO tempoPermanenciaCarteiraZW) {
        this.tempoPermanenciaCarteiraZW = tempoPermanenciaCarteiraZW;
    }

    public FluxoCarteiraZWDTO getFluxoCarteiraZW() {
        return fluxoCarteiraZW;
    }

    public void setFluxoCarteiraZW(FluxoCarteiraZWDTO fluxoCarteiraZW) {
        this.fluxoCarteiraZW = fluxoCarteiraZW;
    }

    public Integer getTotalRenovacoesCarteira() {
        return totalRenovacoesCarteira;
    }

    public void setTotalRenovacoesCarteira(Integer totalRenovacoesCarteira) {
        this.totalRenovacoesCarteira = totalRenovacoesCarteira;
    }

    public Integer getTotalNaoRenovacoesCarteira() {
        return totalNaoRenovacoesCarteira;
    }

    public void setTotalNaoRenovacoesCarteira(Integer totalNaoRenovacoesCarteira) {
        this.totalNaoRenovacoesCarteira = totalNaoRenovacoesCarteira;
    }

    public Integer getTotalAlunosSemAcompanhamento() { return totalAlunosSemAcompanhamento; }

    public void setTotalAlunosSemAcompanhamento(Integer totalAlunosSemAcompanhamento) { this.totalAlunosSemAcompanhamento = totalAlunosSemAcompanhamento; }

    public Integer getTotalAlunosEmAcompanhamento() {
        return totalAlunosEmAcompanhamento;
    }

    public void setTotalAlunosEmAcompanhamento(Integer totalAlunosEmAcompanhamento) {
        this.totalAlunosEmAcompanhamento = totalAlunosEmAcompanhamento;
    }
}
