package br.com.pacto.controller.json.agendamento;

import org.apache.commons.lang3.StringUtils;

public enum TipoProdutoZWEnum {

    FREEPASS("FR"),
    DIARIA("DI");

    private String codigo;

    TipoProdutoZWEnum(String codigo) {
        this.codigo = codigo;
    }

    public String getCodigo() {
        return codigo;
    }

    public void setCodigo(String codigo) {
        this.codigo = codigo;
    }

    public static TipoProdutoZWEnum getInstance(String codigo) {
        if (!StringUtils.isBlank(codigo)) {
            for (TipoProdutoZWEnum produtoZWEnum : TipoProdutoZWEnum.values()) {
                if (codigo.equals(produtoZWEnum.getCodigo())) {
                    return produtoZWEnum;
                }
            }
        }

        return null;
    }
}
