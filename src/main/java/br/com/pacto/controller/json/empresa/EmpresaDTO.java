package br.com.pacto.controller.json.empresa;

import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class EmpresaDTO {

    private int codigo;
    private String nome;
    private String timeZoneDefault;
    private String keyImgEmpresa;

    public int getCodigo() {
        return codigo;
    }

    public void setCodigo(int codigo) {
        this.codigo = codigo;
    }

    public String getKeyImgEmpresa() {
        return keyImgEmpresa;
    }

    public void setKeyImgEmpresa(String keyImgEmpresa) {
        this.keyImgEmpresa = keyImgEmpresa;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getTimeZoneDefault() {
        return timeZoneDefault;
    }

    public void setTimeZoneDefault(String timeZoneDefault) {
        this.timeZoneDefault = timeZoneDefault;
    }
}
