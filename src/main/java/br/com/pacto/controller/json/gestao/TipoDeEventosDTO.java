package br.com.pacto.controller.json.gestao;

import br.com.pacto.bean.tipoEvento.TipoEvento;

public class TipoDeEventosDTO{

    private Integer id;
    private String descricao;


    public TipoDeEventosDTO(TipoEvento tipoEvento){
        this.id = tipoEvento.getCodigo();
        this.descricao = tipoEvento.getNome();
    }


    public Integer getId() {return id;}
    public void setId(Integer id) {this.id = id;}
    public String getDescricao() {return descricao;}
    public void setDescricao(String descricao) {this.descricao = descricao;}
}