package br.com.pacto.controller.json.agendamento;

import br.com.pacto.bean.cliente.PerfilDISC;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class PerfilDISCVO {
    private Integer codigo;
    private Double dominancia;
    private Double influencia;
    private Double estabilidade;
    private Double conformidade;
    private Integer matricula;

    public PerfilDISCVO(PerfilDISC perfilDisc) {
        this.codigo = perfilDisc.getCodigo();
        this.dominancia = perfilDisc.getDominancia();
        this.influencia = perfilDisc.getInfluencia();
        this.estabilidade = perfilDisc.getEstabilidade();
        this.conformidade = perfilDisc.getConformidade();
        this.matricula = perfilDisc.getCliente().getMatricula();
    }

    public Double getDominancia() {
        return dominancia;
    }

    public void setDominancia(Double dominancia) {
        this.dominancia = dominancia;
    }

    public Double getInfluencia() {
        return influencia;
    }

    public void setInfluencia(Double influencia) {
        this.influencia = influencia;
    }

    public Double getEstabilidade() {
        return estabilidade;
    }

    public void setEstabilidade(Double estabilidade) {
        this.estabilidade = estabilidade;
    }

    public Double getConformidade() {
        return conformidade;
    }

    public void setConformidade(Double conformidade) {
        this.conformidade = conformidade;
    }

    public Integer getMatricula() {
        return matricula;
    }

    public void setMatricula(Integer matricula) {
        this.matricula = matricula;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }
}
