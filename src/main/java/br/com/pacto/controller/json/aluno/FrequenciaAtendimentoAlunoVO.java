package br.com.pacto.controller.json.aluno;

import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class FrequenciaAtendimentoAlunoVO {
    private Integer presencas;
    private Integer treinos;
    private Integer atendimentos;

    public Integer getPresencas() {
        return presencas;
    }

    public void setPresencas(Integer presencas) {
        this.presencas = presencas;
    }

    public Integer getTreinos() {
        return treinos;
    }

    public void setTreinos(Integer treinos) {
        this.treinos = treinos;
    }

    public Integer getAtendimentos() {
        return atendimentos;
    }

    public void setAtendimentos(Integer atendimentos) {
        this.atendimentos = atendimentos;
    }
}
