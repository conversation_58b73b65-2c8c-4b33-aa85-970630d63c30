package br.com.pacto.controller.json.atividade;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.security.aspecto.Permissao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.atividade.AtividadeCrossfitService;
import org.json.JSONException;
import org.json.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.util.logging.Level;
import java.util.logging.Logger;


/**
 * <AUTHOR> 31/01/2019
 */
@Controller
@RequestMapping("/psec/atividades-crossfit")
public class AtividadeCrossfitController {

    private final AtividadeCrossfitService atividadeCrossfitService;

    @Autowired
    public AtividadeCrossfitController(final AtividadeCrossfitService atividadeCrossfitService) {
        Assert.notNull(atividadeCrossfitService, "O serviço da atividade crossfit não foi injetado corretamente");
        this.atividadeCrossfitService = atividadeCrossfitService;
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.ATIVIDADES_WOD)
    @RequestMapping(method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> criarAtividadeCrossfit(@RequestBody AtividadeCrossfitDTO atividadeCrossfitDTO) {
        try {
            return ResponseEntityFactory.ok(atividadeCrossfitService.insert(atividadeCrossfitDTO));
        } catch (ServiceException e) {
            Logger.getLogger(AtividadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar criar atividade do crossfit", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.ATIVIDADES_WOD)
    @RequestMapping(value = "/{id}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> atualizarAtividadeCrossfit(
            @PathVariable("id") Integer id,
            @RequestBody AtividadeCrossfitDTO atividadeCrossfitDTO) {
        try {
            atividadeCrossfitDTO.setId(id);
            return ResponseEntityFactory.ok(atividadeCrossfitService.update(atividadeCrossfitDTO));
        } catch (ServiceException e) {
            Logger.getLogger(AtividadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar atualizar atividade do crossfit", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.ATIVIDADES_WOD)
    @RequestMapping(value = "/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> consultarAtividadeCrossfit(
            @PathVariable("id") Integer id) {
        try {
            return ResponseEntityFactory.ok(atividadeCrossfitService.obterPorId(id));
        } catch (ServiceException e) {
            Logger.getLogger(AtividadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar consultar atividade do crossfit", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.ATIVIDADES_WOD)
    @RequestMapping(method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listarAtividadeCrossfit(
            @RequestParam(value = "filters", required = false) JSONObject filtros,
            PaginadorDTO paginadorDTO) throws JSONException {
        try {
            FiltroAtividadeCrossfitJSON filtroAtividadeCrossfitJSON = new FiltroAtividadeCrossfitJSON(filtros);
            return ResponseEntityFactory.ok(atividadeCrossfitService.listarAtividadesCrossfit(filtroAtividadeCrossfitJSON, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(AtividadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar atividades do crossfit", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.ATIVIDADES_WOD)
    @RequestMapping(value = "/{id}",method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> inativarAtividadeCrossfit(
            @PathVariable("id") Integer id) {
        try {
            atividadeCrossfitService.excluir(id);
            return ResponseEntityFactory.ok(true);
        } catch (ServiceException e) {
            Logger.getLogger(AtividadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar inativar atividade do crossfit", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }
}
