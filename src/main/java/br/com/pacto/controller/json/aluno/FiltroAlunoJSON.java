package br.com.pacto.controller.json.aluno;

import br.com.pacto.controller.json.base.SuperJSON;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> 14/02/2019
 */
public class FiltroAlunoJSON extends SuperJSON {

    private Boolean nome = false;
    private List<Integer> niveisIds;
    private List<String> situacoesEnum;
    private Boolean email = false;
    private String parametro;
    private List<Integer> colaboradorIds;
    private List<Integer> colaboradorZw;
    private List<String> statusClienteEnum;

    public FiltroAlunoJSON(JSONObject filters) throws JSONException {

        if (filters != null) {
            this.parametro = filters.optString("quicksearchValue");
            JSONArray colunasVisiveis = filters.optJSONArray("quicksearchFields");
            if (colunasVisiveis != null) {
                for (int i = 0; i < colunasVisiveis.length(); i++) {
                    if (colunasVisiveis.get(i).equals("nome")) {
                        this.nome = true;
                    }
                    if (colunasVisiveis.get(i).equals("email")) {
                        this.email = true;
                    }
                }

            }
            JSONArray colaboradorIds = filters.optJSONArray("colaboradorIds");
            this.colaboradorIds = new ArrayList<>();
            if (colaboradorIds != null) {
                for (int i = 0; i < colaboradorIds.length(); i++) {
                    getColaboradorIds().add(colaboradorIds.getInt(i));
                }
            }
            JSONArray niveis = filters.optJSONArray("niveisIds");
            this.niveisIds = new ArrayList<>();
            if (niveis != null) {
                for (int i = 0; i < niveis.length(); i++) {
                    getNiveisIds().add(niveis.getInt(i));
                }
            }
            JSONArray situacoes = filters.optJSONArray("situacoesEnuns");
            this.situacoesEnum = new ArrayList<>();
            if (situacoes != null) {
                for (int i = 0; i < situacoes.length(); i++) {
                    if(situacoes.getString(i).length()==2){
                        getSituacoesEnum().add(situacoes.getString(i));
                    } else if(situacoes.getString(i).length()==4){ // filter composition
                        getSituacoesEnum().add(situacoes.getString(i).substring(0, 2)+" and "+situacoes.getString(i).substring(2, 4));
                    }

                }
            }
            JSONArray statusCliente = filters.optJSONArray("statusClienteEnuns");
            this.statusClienteEnum = new ArrayList<>();
            if (statusCliente != null) {
                for (int i = 0; i < statusCliente.length(); i++) {
                    getStatusClienteEnum().add(statusCliente.getString(i));
                }
            }
        }
    }

    public Boolean getNome() {
        return nome;
    }

    public void setNome(Boolean nome) {
        this.nome = nome;
    }

    public List<Integer> getNiveisIds() {
        return niveisIds;
    }

    public void setNiveisIds(List<Integer> niveisIds) {
        this.niveisIds = niveisIds;
    }

    public String getParametro() {
        return parametro;
    }

    public void setParametro(String parametro) {
        this.parametro = parametro;
    }

    public Boolean getEmail() {
        return email;
    }

    public void setEmail(Boolean email) {
        this.email = email;
    }

    public List<String> getSituacoesEnum() {
        return situacoesEnum;
    }

    public void setSituacoesEnum(List<String> situacoesEnum) {
        this.situacoesEnum = situacoesEnum;
    }

    public List<Integer> getColaboradorIds() {
        return colaboradorIds;
    }

    public void setColaboradorIds(List<Integer> colaboradorIds) {
        this.colaboradorIds = colaboradorIds;
    }

    public List<String> getStatusClienteEnum() {
        return statusClienteEnum;
    }

    public void setStatusClienteEnum(List<String> statusClienteEnum) {
        this.statusClienteEnum = statusClienteEnum;
    }

    public List<Integer> getColaboradorZw() {
        return colaboradorZw;
    }

    public void setColaboradorZw(List<Integer> colaboradorZw) {
        this.colaboradorZw = colaboradorZw;
    }
}
