package br.com.pacto.controller.json.gestao;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 18/10/2019
 */
public class BIGraficoResponseGeralSemProfessorDTO {
    private String id;
    private String nome;
    private boolean padrao;
    private ArrayList<String> indicadores;
    private Map<String,String> professores;
    private Integer periodoMeses;
    private List<BIGraficoResponseSemProfessorDTO> dados;

     public BIGraficoResponseGeralSemProfessorDTO( ){

     }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public boolean isPadrao() {
        return padrao;
    }

    public void setPadrao(boolean padrao) {
        this.padrao = padrao;
    }

    public ArrayList<String> getIndicadores() {
        return indicadores;
    }

    public void setIndicadores(ArrayList<String> indicadores) {
        this.indicadores = indicadores;
    }

    public Map<String, String> getProfessores() {
        return professores;
    }

    public void setProfessores(Map<String, String> professores) {
        this.professores = professores;
    }

    public Integer getPeriodoMeses() {
        return periodoMeses;
    }

    public void setPeriodoMeses(Integer periodoMeses) {
        this.periodoMeses = periodoMeses;
    }

    public List<BIGraficoResponseSemProfessorDTO> getDados() {
        return dados;
    }

    public void setDados(List<BIGraficoResponseSemProfessorDTO> dados) {
        this.dados = dados;
    }
}
