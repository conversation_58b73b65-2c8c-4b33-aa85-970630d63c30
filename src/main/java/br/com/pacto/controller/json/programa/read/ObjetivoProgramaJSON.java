/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pacto.controller.json.programa.read;

import br.com.pacto.bean.programa.ObjetivoPrograma;
import br.com.pacto.controller.json.base.SuperJSON;

/**
 *
 * <AUTHOR>
 */
public class ObjetivoProgramaJSON extends SuperJSON {

    private Integer codigo;
    private Integer objetivo;
    private Integer programa;
    private String nome;
    public ObjetivoProgramaJSON() {
    }

    public ObjetivoProgramaJSON(ObjetivoPrograma o) {
        this.codigo = o.getCodigo();
        this.objetivo = o.getObjetivo().getCodigo();
        this.programa = o.getPrograma().getCodigo();
        this.nome = o.getObjetivo() != null ? o.getObjetivo().getNome() : null;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getObjetivo() {
        return objetivo;
    }

    public void setObjetivo(Integer objetivo) {
        this.objetivo = objetivo;
    }

    public Integer getPrograma() {
        return programa;
    }

    public void setPrograma(Integer programa) {
        this.programa = programa;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }
}
