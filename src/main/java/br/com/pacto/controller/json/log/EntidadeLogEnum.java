package br.com.pacto.controller.json.log;

import org.apache.commons.lang3.StringUtils;

public enum EntidadeLogEnum {

    PROGRAMA,
    CONFIGURACAOSISTEMA,
    ANAMNESE,
    FICHA,
    FICHAPREDEFINIDA,
    ATIVIDADE,
    TURMA,
    HORARIO_TURMA,
    APARELHO,
    WOD,
    ITEMAVALIACAOFISICA,
    AVALIACAOFISICA,
    PR<PERSON><PERSON>AMAP<PERSON>DEFINIDO,
    PROGRAMAFICHA,
    NIVEL,
    ALUNO,

    AGENDADESERVICO,
    DISPONIBILIDADE,
    TIPOEVENTO,
    NIVELWOD,
    PERMISSAO,
    PERFIL,
    LOCACAO,
    LOCACAO_HORARIO,
    LOCACAO_PRODUTO_SUGERIDO,
    LOCACAO_PRODUTO_VALIDACAO,
    LOCACAO_PLANO_VALIDACAO,
    CATEGORIA_FICHA_PREDEFINIDA,
    LOCACAO_REAGENDAMENTO,
    LOCACAO_CANCELADA,
    LOCACA<PERSON>_FINALIZADA,
    PARQ,
    AULA_COLETIVA,
    LESAO;

    public static EntidadeLogEnum fromEntidade(String entidade) {
        if (StringUtils.isBlank(entidade)) {
            return null;
        }
        for (br.com.pacto.controller.json.log.EntidadeLogEnum entidadeLogEnum : br.com.pacto.controller.json.log.EntidadeLogEnum.values()) {
            if (entidadeLogEnum.toString().equals(entidade)) {
                return entidadeLogEnum;
            }
        }

        return null;
    }

}
