package br.com.pacto.controller.json.aluno;

import br.com.pacto.bean.cliente.ClienteSintetico;

public class ClienteSinteticoDTO {

    private Integer matricula;
    private String nome;
    private String email;
    private String telefones;
    private String cpf;
    private String sexo;

    public ClienteSinteticoDTO(ClienteSintetico entity) {
        this.matricula = entity.getMatricula();
        this.nome = entity.getNome();
        this.email = entity.getEmail();
        this.telefones = entity.getTelefones();
        this.cpf = entity.getCPF();
        this.sexo = entity.getSexo();
    }

    public Integer getMatricula() {
        return matricula;
    }

    public void setMatricula(Integer matricula) {
        this.matricula = matricula;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getTelefones() {
        return telefones;
    }

    public void setTelefones(String telefones) {
        this.telefones = telefones;
    }

    public String getCpf() {
        return cpf;
    }

    public void setCpf(String cpf) {
        this.cpf = cpf;
    }

    public String getSexo() {
        return sexo;
    }

    public void setSexo(String sexo) {
        this.sexo = sexo;
    }
}
