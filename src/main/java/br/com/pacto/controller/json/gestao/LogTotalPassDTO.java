package br.com.pacto.controller.json.gestao;

import java.sql.Timestamp;
import java.time.Instant;

public class LogTotalPassDTO {

    private Integer codigo;
    private Integer pessoa;
    private Timestamp dataregistro;
    private String uri;
    private String apikey;
    private String json;
    private String resposta;
    private Long tempoResposta;
    private String tipo;
    private Integer empresa;
    private String ip;
    private Integer usuario;
    private String origem;
    private String status;
    private String respostaApi;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getPessoa() {
        return pessoa;
    }

    public void setPessoa(Integer pessoa) {
        this.pessoa = pessoa;
    }

    public String getUri() {
        return uri;
    }

    public void setUri(String uri) {
        this.uri = uri;
    }

    public String getApikey() {
        return apikey;
    }

    public void setApikey(String apikey) {
        this.apikey = apikey;
    }

    public String getJson() {
        return json;
    }

    public void setJson(String json) {
        this.json = json;
    }

    public String getResposta() {
        return resposta;
    }

    public void setResposta(String resposta) {
        this.resposta = resposta;
    }

    public Long getTempoResposta() {
        return tempoResposta;
    }

    public void setTempoResposta(Long tempoResposta) {
        this.tempoResposta = tempoResposta;
    }

    public String getTipo() {
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public Integer getUsuario() {
        return usuario;
    }

    public void setUsuario(Integer usuario) {
        this.usuario = usuario;
    }

    public String getOrigem() {
        return origem;
    }

    public void setOrigem(String origem) {
        this.origem = origem;
    }


    public String getStatus() {
        if(resposta.equals("204")){
            status = "Liberado";
        } else{
            status = "Negado";
        }
        return status;
    }

    public void setStatus(String status) {
        status = status;
    }

    public void setDataRegistroInstant(Instant instant) {
        this.dataregistro = java.sql.Timestamp.from(instant);
    }

    public Timestamp getDataregistro() {
        return dataregistro;
    }

    public void setDataregistro(Timestamp dataregistro) {
        this.dataregistro = dataregistro;
    }

    public String getRespostaApi() {
        return respostaApi;
    }

    public void setRespostaApi(String respostaApi) {
        this.respostaApi = respostaApi;
    }
}