package br.com.pacto.controller.json.aluno;

import br.com.pacto.service.impl.cliente.perfil.ProgramaAtualDTO;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "DTO de resposta para o programa atual")
public class ProgramaAtualResponseDTO {

    @ApiModelProperty(value = "Programa atual", required = true)
    @JsonProperty("content")
    private ProgramaAtualDTO content;

    public ProgramaAtualDTO getContent() {
        return content;
    }

    public void setContent(ProgramaAtualDTO content) {
        this.content = content;
    }
}
