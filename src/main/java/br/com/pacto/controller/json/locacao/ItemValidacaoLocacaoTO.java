package br.com.pacto.controller.json.locacao;

import br.com.pacto.bean.locacao.ItemValidacaoLocacao;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.io.Serializable;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class ItemValidacaoLocacaoTO implements Serializable {

    private Integer codigo;
    private Integer plano;
    private Integer produto;
    private String descricao;
    private Double valorFinal;

    public ItemValidacaoLocacaoTO() {}

    public ItemValidacaoLocacaoTO(ItemValidacaoLocacao itemValidacaoLocacao) {
        this.codigo = itemValidacaoLocacao.getCodigo();
        this.plano = itemValidacaoLocacao.getPlano();
        this.produto = itemValidacaoLocacao.getProduto();
        this.descricao = itemValidacaoLocacao.getDescricao();
        this.valorFinal = itemValidacaoLocacao.getValorFinal();
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getPlano() {
        return plano;
    }

    public void setPlano(Integer plano) {
        this.plano = plano;
    }

    public Integer getProduto() {
        return produto;
    }

    public void setProduto(Integer produto) {
        this.produto = produto;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Double getValorFinal() {
        return valorFinal;
    }

    public void setValorFinal(Double valorFinal) {
        this.valorFinal = valorFinal;
    }
}
