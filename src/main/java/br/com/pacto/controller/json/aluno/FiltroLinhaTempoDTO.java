package br.com.pacto.controller.json.aluno;

import br.com.pacto.util.json.TipoLinhaEnum;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.Date;
import java.util.List;

/**
 * Created paulo 07/11/2018
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FiltroLinhaTempoDTO {

    private List<TipoLinhaEnum> tiposEvento;
    private Date dataInicio;
    private Date dataFim;

    public FiltroLinhaTempoDTO(List<TipoLinhaEnum> tiposEvento, Long dataInicio, Long dataFim) {
        this.tiposEvento = tiposEvento;
        this.dataInicio = dataInicio == null ? null : new Date(dataInicio);
        this.dataFim = dataFim == null ? null : new Date(dataFim);
    }

    public List<TipoLinhaEnum> getTiposEvento() {
        return tiposEvento;
    }

    public void setTiposEvento(List<TipoLinhaEnum> tiposEvento) {
        this.tiposEvento = tiposEvento;
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Date getDataFim() {
        return dataFim;
    }

    public void setDataFim(Date dataFim) {
        this.dataFim = dataFim;
    }
}
