/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.controller.json.aulaDia;

import br.com.pacto.bean.aula.AulaAluno;
import br.com.pacto.bean.aula.AulaDia;
import br.com.pacto.controller.json.base.SuperJSON;
import br.com.pacto.controller.json.turma.TurmaVideoDTO;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.bean.AgendaTotalTO;
import br.com.pacto.util.json.AgendaTotalJSON;
import br.com.pacto.util.AgendadoJSON;

import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class AulaDiaJSON extends SuperJSON {
    
    private Integer codigo;
    private String nomeEmpresa;
    private String logoEmpresa;
    private String inicio;
    private String fim;
    private String nome;
    private String nivel;
    private String modalidade;
    private String professor;
    private String ambiente;
    private Integer pontos;
    private Integer capacidade;
    private Integer vagasRestantes;
    private Integer ocupacao;
    private boolean restamVagas;
    private String urlIcone;
    private String dia;
    private String diaSemana;
    private boolean jaMarcouEuQuero = false;
    private boolean alunoEstaNaAula = false;
    private boolean coletiva = false;
    private Date diaDate;
    private Date datalancamento;
    private String corModalidade;
    private String fotoProfessor;
    private String fotoModalidade;
    private Boolean integracaoSpivi;

    private Integer codigoAulaOrigemReposicao;
    private String vigencia;
    private String urlVideoYoutube;
    private String imageUrl;
    private List<TurmaVideoDTO> linkVideos;
    private String tipoReservaEquipamento;
    private String equipamentosOcupados;
    private String mapaEquipamentos;
    private List<MapaEquipamentoAparelhoDTO> listaMapaEquipamentoAparelho;
    private Boolean apresentarEquipamentoPorNumeracao = false;

    public AulaDiaJSON(AulaDia aulaDia, List<AulaAluno> alunos){
        dia = Uteis.getData(aulaDia.getInicio());
        codigo = aulaDia.getCodigo();
        inicio = Uteis.getDataAplicandoFormatacao(aulaDia.getInicio(), "HH:mm");
        fim = Uteis.getDataAplicandoFormatacao(aulaDia.getFim(), "HH:mm");
        modalidade = aulaDia.getAula().getNome();
        professor = aulaDia.getProfessor().getNome();
        ambiente = aulaDia.getAula().getAmbiente().getNome();
        pontos = aulaDia.getAula().getPontosBonus();
        ocupacao = alunos == null ? 0 : alunos.size();
        capacidade = aulaDia.getAula().getCapacidade();
        vagasRestantes = capacidade - ocupacao; 
        restamVagas = ocupacao < capacidade;

        try {
            urlIcone = aulaDia.getAula().getModalidade().getAnimacao().getUrl();
        } catch (Exception e) {
            urlIcone = "";
        }
    }

    public AulaDiaJSON(AgendadoJSON json){
        inicio = json.getInicio();
        fim = json.getFim();
    }
    
    public AulaDiaJSON(AgendaTotalJSON json) throws Exception{
        nome = json.getTitulo();
        try {
            diaDate = Uteis.getDate(json.getInicio(), "dd/MM/yyyy HH:mm");
            dia = Uteis.getData(diaDate);
            inicio = Uteis.getDataAplicandoFormatacao(Uteis.getDate(json.getInicio(), "dd/MM/yyyy HH:mm"), "HH:mm");
            fim = Uteis.getDataAplicandoFormatacao(Uteis.getDate(json.getFim(), "dd/MM/yyyy HH:mm"), "HH:mm");
        } catch (Exception e) {
            inicio = json.getInicio();
            fim = json.getFim();
        }
        codigo = Integer.valueOf(json.getId());
        nomeEmpresa = json.getNomeEmpresa();
        logoEmpresa = json.getLogoEmpresa();
        datalancamento = json.getDatalancamento();
        modalidade = json.getTipo();
        professor = json.getResponsavel();
        ambiente = json.getLocal();
        diaSemana = json.getDiaSemana();
        capacidade = json.getNrVagas();
        coletiva = json.getAulaCheia();
        ocupacao = json.getNrVagasPreenchidas() == null ? 0 : json.getNrVagasPreenchidas();
        restamVagas = ocupacao < capacidade;
        vagasRestantes = capacidade - ocupacao; 
        pontos = json.getPontosBonus();
        jaMarcouEuQuero = json.isJaMarcouEuQuero();
        corModalidade = json.getCor();
        fotoProfessor = json.getFotoProfessor();
        fotoModalidade = json.getFotoModalidade();
        nivel = json.getNivel() == null ? "" : json.getNivel();
        codigoAulaOrigemReposicao = json.getCodigoAulaOrigemReposicao();
        vigencia = Uteis.getData(json.getFimVigencia(), "dd/MM/yyyy");
        alunoEstaNaAula = json.isAlunoEstaNaAula();
        urlVideoYoutube = json.getUrlVideoYoutube();
        imageUrl = json.getImageUrl();
        linkVideos = json.getLinkVideos();
        tipoReservaEquipamento = json.getTipoReservaEquipamento();
        mapaEquipamentos = json.getMapaEquipamentos();
    }
    
    public AulaDiaJSON(AgendaTotalTO to) throws Exception{
        nome = to.getTitulo();
        try {
            dia = Uteis.getData(Uteis.getDate(to.getDia(), "dd/MM/yyyy"));
            inicio = Uteis.getDataAplicandoFormatacao(Uteis.getDate(to.getInicio(), "dd/MM/yyyy HH:mm"), "HH:mm");
            fim = Uteis.getDataAplicandoFormatacao(Uteis.getDate(to.getFim(), "dd/MM/yyyy HH:mm"), "HH:mm");
        } catch (Exception e) {
            inicio = to.getInicio();
            fim = to.getFim();
        }
        diaDate = Uteis.getDate(dia + " "+inicio, "dd/MM/yyyy HH:mm");
        codigo = Integer.valueOf(to.getId());
        modalidade = to.getTipo();
        professor = to.getResponsavel();
        ambiente = to.getLocal();
        diaSemana = to.getDiaSemana();
        capacidade = to.getNrVagas();
        ocupacao = to.getNrVagasPreenchidas() == null ? 0 : to.getNrVagasPreenchidas();
        restamVagas = ocupacao < capacidade;
        vagasRestantes = capacidade - ocupacao;
        jaMarcouEuQuero = to.getJaMarcouEuQuero();
        fotoProfessor = to.getFotoProfessor();
        fotoModalidade = to.getFotoModalidade();
        nivel = to.getNivel() == null ? "" : to.getNivel();

        vigencia = Uteis.getData(to.getVigencia(), "dd/MM/yyyy");
        integracaoSpivi = to.isIntegracaoSpivi();
        alunoEstaNaAula = to.isAlunoEstaNaAula();
        linkVideos = to.getLinkVideos();
    }

    public String getUrlIcone() {
        return urlIcone;
    }

    public void setUrlIcone(String urlIcone) {
        this.urlIcone = urlIcone;
    }
    
    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNomeEmpresa() {
        return nomeEmpresa;
    }

    public void setNomeEmpresa(String nomeEmpresa) {
        this.nomeEmpresa = nomeEmpresa;
    }

    public String getLogoEmpresa() { return logoEmpresa; }

    public void setLogoEmpresa(String logoEmpresa) { this.logoEmpresa = logoEmpresa; }

    public String getInicio() {
        return inicio;
    }

    public void setInicio(String inicio) {
        this.inicio = inicio;
    }

    public String getFim() {
        return fim;
    }

    public void setFim(String fim) {
        this.fim = fim;
    }

    public String getModalidade() {
        return modalidade;
    }

    public void setModalidade(String modalidade) {
        this.modalidade = modalidade;
    }

    public String getProfessor() {
        return professor;
    }

    public void setProfessor(String professor) {
        this.professor = professor;
    }

    public String getAmbiente() {
        return ambiente;
    }

    public void setAmbiente(String ambiente) {
        this.ambiente = ambiente;
    }

    public Integer getPontos() {
        return pontos;
    }

    public void setPontos(Integer pontos) {
        this.pontos = pontos;
    }

    public Integer getCapacidade() {
        return capacidade;
    }

    public void setCapacidade(Integer capacidade) {
        this.capacidade = capacidade;
    }

    public Integer getOcupacao() {
        return ocupacao;
    }

    public void setOcupacao(Integer ocupacao) {
        this.ocupacao = ocupacao;
    }

    public boolean isRestamVagas() {
        return restamVagas;
    }

    public void setRestamVagas(boolean restamVagas) {
        this.restamVagas = restamVagas;
    }

    public String getDia() {
        return dia;
    }

    public void setDia(String dia) {
        this.dia = dia;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getDiaSemana() {
        return diaSemana;
    }

    public void setDiaSemana(String diaSemana) {
        this.diaSemana = diaSemana;
    }

    public boolean isJaMarcouEuQuero() {
        return jaMarcouEuQuero;
    }

    public void setJaMarcouEuQuero(boolean jaMarcouEuQuero) {
        this.jaMarcouEuQuero = jaMarcouEuQuero;
    }

    public Date getDiaDate() {
        return diaDate;
    }

    public void setDiaDate(Date diaDate) {
        this.diaDate = diaDate;
    }

    public Date getDatalancamento() { return datalancamento; }

    public void setDatalancamento(Date datalancamento) { this.datalancamento = datalancamento; }

    public Integer getVagasRestantes() {
        return vagasRestantes;
    }

    public void setVagasRestantes(Integer vagasRestantes) {
        this.vagasRestantes = vagasRestantes;
    }

    public boolean isAlunoEstaNaAula() {
        return alunoEstaNaAula;
    }

    public void setAlunoEstaNaAula(boolean alunoEstaNaAula) {
        this.alunoEstaNaAula = alunoEstaNaAula;
    }

    public String getCodDia(){
        try {
            return UteisValidacao.emptyString(getDia()) ? "" : (codigo + "_"+getDia());
        }catch (Exception e){
            return "";
        }
    }

    public String getNivel() {
        return nivel;
    }

    public void setNivel(String nivel) {
        this.nivel = nivel;
    }

    public String getCorModalidade() {
        return corModalidade;
    }

    public void setCorModalidade(String corModalidade) {
        this.corModalidade = corModalidade;
    }

    public String getFotoProfessor() {
        return fotoProfessor;
    }

    public void setFotoProfessor(String fotoProfessor) {
        this.fotoProfessor = fotoProfessor;
    }

    public String getFotoModalidade() {
        return fotoModalidade;
    }

    public void setFotoModalidade(String fotoModalidade) {
        this.fotoModalidade = fotoModalidade;
    }

    public boolean isColetiva() {
        return coletiva;
    }

    public void setColetiva(boolean coletiva) {
        this.coletiva = coletiva;
    }

    public Integer getCodigoAulaOrigemReposicao() {
        return codigoAulaOrigemReposicao;
    }

    public void setCodigoAulaOrigemReposicao(Integer codigoAulaOrigemReposicao) {
        this.codigoAulaOrigemReposicao = codigoAulaOrigemReposicao;
    }

    public String getVigencia() {
        return vigencia;
    }

    public void setVigencia(String vigencia) {
        this.vigencia = vigencia;
    }

    public Boolean getIntegracaoSpivi() {
        return integracaoSpivi;
    }

    public void setIntegracaoSpivi(Boolean integracaoSpivi) {
        this.integracaoSpivi = integracaoSpivi;
    }

    public String getUrlVideoYoutube() {
        return urlVideoYoutube;
    }

    public void setUrlVideoYoutube(String urlVideoYoutube) {
        this.urlVideoYoutube = urlVideoYoutube;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public List<TurmaVideoDTO> getLinkVideos() {
        return linkVideos;
    }

    public void setLinkVideos(List<TurmaVideoDTO> linkVideos) {
        this.linkVideos = linkVideos;
    }

    public String getTipoReservaEquipamento() {
        return tipoReservaEquipamento;
    }

    public void setTipoReservaEquipamento(String tipoReservaEquipamento) {
        this.tipoReservaEquipamento = tipoReservaEquipamento;
    }

    public String getEquipamentosOcupados() {
        return equipamentosOcupados;
    }

    public void setEquipamentosOcupados(String equipamentosOcupados) {
        this.equipamentosOcupados = equipamentosOcupados;
    }

    public String getMapaEquipamentos() {
        return mapaEquipamentos;
    }

    public void setMapaEquipamentos(String mapaEquipamentos) {
        this.mapaEquipamentos = mapaEquipamentos;
    }

    public List<MapaEquipamentoAparelhoDTO> getListaMapaEquipamentoAparelho() {
        return listaMapaEquipamentoAparelho;
    }

    public void setListaMapaEquipamentoAparelho(List<MapaEquipamentoAparelhoDTO> listaMapaEquipamentoAparelho) {
        this.listaMapaEquipamentoAparelho = listaMapaEquipamentoAparelho;
    }

    public Boolean getApresentarEquipamentoPorNumeracao() {
        if (apresentarEquipamentoPorNumeracao == null) {
            return false;
        }
        return apresentarEquipamentoPorNumeracao;
    }

    public void setApresentarEquipamentoPorNumeracao(Boolean apresentarEquipamentoPorNumeracao) {
        this.apresentarEquipamentoPorNumeracao = apresentarEquipamentoPorNumeracao;
    }

}
