/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.controller.json.acompanhamento;

import br.com.pacto.controller.json.base.SuperJSON;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class DesempenhoJSON extends SuperJSON {

    private String nome;
    private List<SerieDesempenhoJSON> dados = new ArrayList<SerieDesempenhoJSON>();

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public List<SerieDesempenhoJSON> getDados() {
        return dados;
    }

    public void setDados(List<SerieDesempenhoJSON> dados) {
        this.dados = dados;
    }
}
