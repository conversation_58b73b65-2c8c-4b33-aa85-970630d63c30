package br.com.pacto.controller.json.spivi;

import br.com.pacto.controller.json.base.SuperJSON;
import br.com.pacto.objeto.Uteis;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import flex.messaging.io.ArrayList;
import org.apache.poi.ss.formula.functions.Even;

import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;

public class EventJSON extends SuperJSON {
    private int eventID;
    private int scheduleID;
    private int siteID;
    private String title;
    private String description;
    private String startDateTime;
    private String endDateTime;
    private int isCancelled;
    private String instructorName;
    private int instructorClientID;
    private int boxID;
    private String venueName;
    private int virtualClass;
    private int programType;
    private List<SeatJSON> availableSeats = new ArrayList();
    private List<BookedClientJSON> bookedClients = new ArrayList();
    private List<SeatJSON> allSeats = new ArrayList();
    private SeatJSON seatTeacher;

    public EventJSON() {
    }

    public EventJSON(JSONObject json) throws JSONException {
        eventID = json.getInt("EventID");
        scheduleID = json.getInt("ScheduleID");
        siteID = json.getInt("SiteID");
        title = json.getString("Title");
        description = json.getString("Description");
        startDateTime = json.getString("StartDateTime");
        endDateTime = json.getString("EndDateTime");
        isCancelled = json.getInt("IsCancelled");
        instructorName = json.getString("InstructorName");
        instructorClientID = json.getInt("InstructorClientID");
        boxID = json.getInt("BoxID");
        venueName = json.getString("VenueName");
        virtualClass = json.getInt("VirtualClass");
        programType = json.getInt("ProgramType");
        availableSeats = SeatJSON.mapper(json.getJSONArray("AvailableSeats"));
        bookedClients = BookedClientJSON.mapper(json.getJSONArray("BookedClients"));
        allSeats.addAll(availableSeats);

       for (BookedClientJSON booked: bookedClients) {
           allSeats.add(new SeatJSON(booked.getSeatID(), booked.getUserName(), booked.getClientName(), booked.getClientID()));
       }

       findSeatTeacher();
       sortAllSeats();
    }

    private void findSeatTeacher(){
        for (int i = 0; allSeats.size() > i; i++){
            if(allSeats.get(i).getSeatID() == 99){
                seatTeacher = allSeats.get(i);
                allSeats.remove(i);
            }
        }
    }

    public void sortAllSeats(){
        Collections.sort(allSeats, new Comparator<SeatJSON>() {
            @Override
            public int compare(final SeatJSON o1, final SeatJSON o2) {
                return o1.getSeatID().compareTo(o2.getSeatID());
            }
        });
    }

    public static List<EventJSON> mapper(JSONArray events) throws JSONException {
       List<EventJSON> eventJSON = new ArrayList();

        for (int i = 0; i < events.length(); i++) {
            eventJSON.add( new EventJSON( (JSONObject) events.get(i)) );
        }

        return eventJSON;
    }

    public int getEventID() {
        return eventID;
    }

    public void setEventID(final int eventID) {
        this.eventID = eventID;
    }

    public int getScheduleID() {
        return scheduleID;
    }

    public void setScheduleID(final int scheduleID) {
        this.scheduleID = scheduleID;
    }

    public int getSiteID() {
        return siteID;
    }

    public void setSiteID(final int siteID) {
        this.siteID = siteID;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(final String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(final String description) {
        this.description = description;
    }

    public String getStartDateTime() {
        return startDateTime;
    }

    public String getStartDateTimePtbr() {
        return startDateTime.replace("-", "/").replace("T", " ");
    }

    public void setStartDateTime(final String startDateTime) {
        this.startDateTime = startDateTime;
    }

    public String getEndDateTime() {
        return endDateTime;
    }

    public String getEndDateTimePrbr() {
        return endDateTime.replace("-", "/").replace("T", " ");
    }

    public void setEndDateTime(final String endDateTime) {
        this.endDateTime = endDateTime;
    }

    public int getIsCancelled() {
        return isCancelled;
    }

    public void setIsCancelled(final int isCancelled) {
        this.isCancelled = isCancelled;
    }

    public String getInstructorName() {
        return instructorName;
    }

    public void setInstructorName(final String instructorName) {
        this.instructorName = instructorName;
    }

    public int getInstructorClientID() {
        return instructorClientID;
    }

    public void setInstructorClientID(final int instructorClientID) {
        this.instructorClientID = instructorClientID;
    }

    public int getBoxID() {
        return boxID;
    }

    public void setBoxID(final int boxID) {
        this.boxID = boxID;
    }

    public String getVenueName() {
        return venueName;
    }

    public void setVenueName(final String venueName) {
        this.venueName = venueName;
    }

    public int getVirtualClass() {
        return virtualClass;
    }

    public void setVirtualClass(final int virtualClass) {
        this.virtualClass = virtualClass;
    }

    public int getProgramType() {
        return programType;
    }

    public void setProgramType(final int programType) {
        this.programType = programType;
    }

    public List<SeatJSON> getAvailableSeats() {
        return availableSeats;
    }

    public void setAvailableSeats(final List<SeatJSON> availableSeats) {
        this.availableSeats = availableSeats;
    }

    public List<BookedClientJSON> getBookedClients() {
        return bookedClients;
    }

    public void setBookedClients(final List<BookedClientJSON> bookedClients) {
        this.bookedClients = bookedClients;
    }

    public List<SeatJSON> getAllSeats() {
        return allSeats;
    }

    public void setAllSeats(final List<SeatJSON> allSeats) {
        this.allSeats = allSeats;
    }

    public SeatJSON getSeatTeacher() {
        return seatTeacher;
    }

    public void setSeatTeacher(final SeatJSON seatTeacher) {
        this.seatTeacher = seatTeacher;
    }
}
