package br.com.pacto.controller.json.ambiente;

import br.com.pacto.bean.aula.Ambiente;
import br.com.pacto.controller.json.base.SuperControle;
import com.fasterxml.jackson.annotation.JsonInclude;
/**
 * Created by <PERSON><PERSON> on 26/09/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AmbienteResponseTO {

    private Integer id;
    private String nome;
    private Integer capacidade;

    public AmbienteResponseTO() {
    }

    public AmbienteResponseTO (Integer id, String nome) {
        this.id = id;
        this.nome = nome;
    }
    public AmbienteResponseTO (Ambiente ambiente, Boolean treinoIndependente, Boolean validarNoTreino) {
        if(validarNoTreino != null && validarNoTreino){
            this.id = ambiente.getCodigo();
        }else{
            this.id = treinoIndependente ? ambiente.getCodigo() : ambiente.getCodigoZW();
        }
        this.nome = ambiente.getNome();
        this.capacidade = ambiente.getCapacidade();
    }
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Integer getCapacidade() {
        return capacidade;
    }

    public void setCapacidade(Integer capacidade) {
        this.capacidade = capacidade;
    }
}
