package br.com.pacto.controller.json.atividade;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.atividade.CategoriaAtividadeTO;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.controller.json.musculo.GrupoMuscularController;
import br.com.pacto.security.aspecto.Permissao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.atividade.CategoriaAtividadeService;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by ulisses on 23/08/2018.
 */

@Controller
@RequestMapping("/psec/categoria-atividade")
public class CategoriaAtividadeController {

    private CategoriaAtividadeService categoriaAtividadeService;

    @Autowired
    public CategoriaAtividadeController(CategoriaAtividadeService categoriaAtividadeService){
        Assert.notNull(categoriaAtividadeService, "O serviço de categoria atividade não foi injetado corretamente");
        this.categoriaAtividadeService = categoriaAtividadeService;
    }


    @ResponseBody
    @Permissao(recursos = RecursoEnum.CATEGORIA_ATIVIDADE)
    @RequestMapping(value = "", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> consultarCategoriaAtividades(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                                         PaginadorDTO paginadorDTO) throws JSONException {
        try {
            FiltroCategoriaAtividadeJSON filtroCategoriaAtividadeJSON = new FiltroCategoriaAtividadeJSON(filtros);
            return ResponseEntityFactory.ok(categoriaAtividadeService.consultarCategoriaAtividades(filtroCategoriaAtividadeJSON,paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(GrupoMuscularController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar as categorias de atividades.", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }


    @ResponseBody
    @Permissao(recursos = RecursoEnum.CATEGORIA_ATIVIDADE)
    @RequestMapping(value = "/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> consultarCategoriaAtividade(@PathVariable("id") Integer id){
        try {
            return ResponseEntityFactory.ok(categoriaAtividadeService.consultarCategoriaAtividade(id));
        } catch (ServiceException e) {
            Logger.getLogger(CategoriaAtividadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar consultar o Categoria de Atividade", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.CATEGORIA_ATIVIDADE)
    @RequestMapping(value = "", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> incluirCategoriaAtividade(@RequestBody CategoriaAtividadeTO categoriaAtividadeTO) {
        try {
            return ResponseEntityFactory.ok(categoriaAtividadeService.inserir(categoriaAtividadeTO));
        } catch (ServiceException e) {
            Logger.getLogger(CategoriaAtividadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar incluir Categoria de Atividade", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.CATEGORIA_ATIVIDADE)
    @RequestMapping(value = "/{id}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alterarCategoriaAtividade(@PathVariable("id") final Integer id,
                                                                    @RequestBody CategoriaAtividadeTO categoriaAtividadeTO) {
        try {
            return ResponseEntityFactory.ok(categoriaAtividadeService.alterar(id, categoriaAtividadeTO));

        } catch (ServiceException e) {
            Logger.getLogger(CategoriaAtividadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar alterar Categoria de Atividade", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }


    @ResponseBody
    @Permissao(recursos = RecursoEnum.CATEGORIA_ATIVIDADE)
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> excluirCategoriaAtividade(@PathVariable("id") final Integer id){
        try {
            categoriaAtividadeService.excluir(id);
            return ResponseEntityFactory.ok(true);
        } catch (ServiceException e) {
            Logger.getLogger(CategoriaAtividadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar excluir Categoria de Atividade", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.CATEGORIA_ATIVIDADE)
    @RequestMapping(value = "all", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> consultarTodos() {
        try {
            return ResponseEntityFactory.ok(categoriaAtividadeService.consultarTodos());
        } catch (ServiceException e) {
            Logger.getLogger(CategoriaAtividadeController.class.getName()).log(Level.SEVERE, "Erro ao consultar todas as categorias de atividades", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }
}
