package br.com.pacto.controller.json.aulaDia;

import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class MapaEquipamentoAparelhoDTO {
    private String posicaoMapa;
    private Integer posicaoNumeroSequencial;
    private Integer codigoAparelho;
    private String nomeAparelho;
    private String siglaAparelho;
    private String iconeAparelho;
    private Boolean ocupado;

    public MapaEquipamentoAparelhoDTO() { }

    public MapaEquipamentoAparelhoDTO(String posicaoMapa) {
        this.posicaoMapa = posicaoMapa;
    }

    public MapaEquipamentoAparelhoDTO(String posicaoMapa,Integer posicaoNumeroSequencial, Integer codigoAparelho, String nomeAparelho, String siglaAparelho, String iconeAparelho, Boolean ocupado) {
        this.posicaoMapa = posicaoMapa;
        this.posicaoNumeroSequencial = posicaoNumeroSequencial;
        this.codigoAparelho = codigoAparelho;
        this.nomeAparelho = nomeAparelho;
        this.siglaAparelho = siglaAparelho;
        this.iconeAparelho = iconeAparelho;
        this.ocupado = ocupado;
    }

    public String getPosicaoMapa() {
        return posicaoMapa;
    }

    public void setPosicaoMapa(String posicaoMapa) {
        this.posicaoMapa = posicaoMapa;
    }

    public Integer getPosicaoNumeroSequencial() {
        return posicaoNumeroSequencial;
    }

    public void setPosicaoNumeroSequencial(Integer posicaoNumeroSequencial) {
        this.posicaoNumeroSequencial = posicaoNumeroSequencial;
    }

    public Integer getCodigoAparelho() {
        return codigoAparelho;
    }

    public void setCodigoAparelho(Integer codigoAparelho) {
        this.codigoAparelho = codigoAparelho;
    }

    public String getNomeAparelho() {
        return nomeAparelho;
    }

    public void setNomeAparelho(String nomeAparelho) {
        this.nomeAparelho = nomeAparelho;
    }

    public String getSiglaAparelho() {
        return siglaAparelho;
    }

    public void setSiglaAparelho(String siglaAparelho) {
        this.siglaAparelho = siglaAparelho;
    }

    public String getIconeAparelho() {
        return iconeAparelho;
    }

    public void setIconeAparelho(String iconeAparelho) {
        this.iconeAparelho = iconeAparelho;
    }

    public Boolean getOcupado() {
        return ocupado;
    }

    public void setOcupado(Boolean ocupado) {
        this.ocupado = ocupado;
    }

}
