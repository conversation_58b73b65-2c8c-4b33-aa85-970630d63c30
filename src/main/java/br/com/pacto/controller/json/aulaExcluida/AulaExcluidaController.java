package br.com.pacto.controller.json.aulaExcluida;


import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.aulaExcluida.AulaExcluidaService;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.logging.Level;
import java.util.logging.Logger;

@Controller
@RequestMapping("/psec/aula-excluida")
public class AulaExcluidaController {

    private AulaExcluidaService aulaExcluidaService;

    @Autowired
    public AulaExcluidaController(AulaExcluidaService aulaExcluidaService){
        Assert.notNull(aulaExcluidaService, "O serviço de aula excçuiída não foi injetado corretamente");
        this.aulaExcluidaService = aulaExcluidaService;
    }

    @ResponseBody
    @RequestMapping(value = "", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> consultarAulaExcluida(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                                 @RequestHeader(value = "empresaId", required = true) Integer empresaId,
                                                                 PaginadorDTO paginadorDTO, HttpServletRequest request) throws JSONException {
        try {
            FiltroAulaExcluidaJSON filtroAulaExcluidaJSON = new FiltroAulaExcluidaJSON(filtros);

            return ResponseEntityFactory.ok(aulaExcluidaService.listaAulaExcluida(filtroAulaExcluidaJSON, paginadorDTO, empresaId, request), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(AulaExcluidaController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar as atividades", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "{id}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> removerAulaExcluida(
            @PathVariable("id") Integer id){
        try {
            aulaExcluidaService.removerAulaExcluida(id);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(AulaExcluidaController.class.getName()).log(Level.SEVERE, "Erro ao tentar desfazer a aula excluída", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

}
