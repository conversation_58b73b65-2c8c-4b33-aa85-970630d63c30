package br.com.pacto.controller.json.agendamento;

import java.util.ArrayList;
import java.util.List;

public class BiAulaDTO {

    private List<ItemBiAulaDTO> aulas = new ArrayList<>();
    private List<ItemBiAulaDTO> modalidades = new ArrayList<>();
    private List<ItemBiAulaDTO> professores = new ArrayList<>();
    private List<ItemBiAulaDTO> frequencia = new ArrayList<>();
    private Integer alunos;
    private Integer totalAulas;
    private String totalBonificado;

    public List<ItemBiAulaDTO> getAulas() {
        return aulas;
    }

    public void setAulas(List<ItemBiAulaDTO> aulas) {
        this.aulas = aulas;
    }

    public List<ItemBiAulaDTO> getModalidades() {
        return modalidades;
    }

    public void setModalidades(List<ItemBiAulaDTO> modalidades) {
        this.modalidades = modalidades;
    }

    public List<ItemBiAulaDTO> getProfessores() {
        return professores;
    }

    public void setProfessores(List<ItemBiAulaDTO> professores) {
        this.professores = professores;
    }

    public List<ItemBiAulaDTO> getFrequencia() {
        return frequencia;
    }

    public void setFrequencia(List<ItemBiAulaDTO> frequencia) {
        this.frequencia = frequencia;
    }

    public Integer getAlunos() {
        return alunos;
    }

    public void setAlunos(Integer alunos) {
        this.alunos = alunos;
    }

    public Integer getTotalAulas() {
        return totalAulas;
    }

    public void setTotalAulas(Integer totalAulas) {
        this.totalAulas = totalAulas;
    }

    public String getTotalBonificado() {
        return totalBonificado;
    }

    public void setTotalBonificado(String totalBonificado) {
        this.totalBonificado = totalBonificado;
    }
}
