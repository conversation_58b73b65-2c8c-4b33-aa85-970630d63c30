/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pacto.controller.json.base;

import br.com.pacto.base.impl.EntityManagerService;
import br.com.pacto.bean.configuracoes.ConfiguracaoSistema;
import br.com.pacto.bean.configuracoes.ConfiguracoesEnum;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.bean.perfil.permissao.TipoPermissaoEnum;
import br.com.pacto.bean.usuario.Usuario;

import br.com.pacto.controller.json.usuario.UsuarioJSONControle;
import br.com.pacto.notificacao.EnfileiradorNotificadorRecursoSistemaSingleton;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.CriptografiaUtil;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.intf.configuracoes.ConfiguracaoSistemaService;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.util.ViewUtils;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.Date;
import java.util.logging.Level;
import java.util.logging.Logger;
import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;

import br.com.pacto.util.impl.AuditUtilities;
import br.com.pacto.util.impl.JSFUtilities;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.ui.ModelMap;
import org.springframework.web.context.ServletContextAware;
import servicos.integracao.admapp.AdmAppWSConsumer;

/**
 *
 * <AUTHOR>
 */
public class SuperControle implements ServletContextAware {

    public static final String STATUS = "status";
    public static final String STATUS_SUCESSO = "sucesso";
    public static final String STATUS_ERRO = "erro";
    public static final String RETURN = "return";
    public static final String MSG_DADOS_GRAVADOS = "Dados gravados com sucesso!";
    public static final String MSG_DADOS_EXCLUIDOS = "Dados excluídos com sucesso!";
    public static final String MSG_DADOS_NAOENCONTRADOS = "Dados não encontrados!";
    public static final String PATH_RESOURCE_IMG = "javax.faces.resource";
    public static final String SUFIXO_RESOURCE_JSF = ".xhtml?ln=img";
    public static final Class COMPONENT_NAME_VIEWUTILS = ViewUtils.class;
    private ServletContext context;
    public static String versao = "";
    @Autowired
    private EntityManagerService entityManagerService;

    public ServletContext getContext() {
        return context;
    }

    public EntityManagerService getEntityManagerService() {
        return entityManagerService;
    }
    
    public ViewUtils getViewUtils() {
        return UtilContext.getBean(ViewUtils.class);
    }

    public static String getUrlImagem(final String key) {
        return AdmAppWSConsumer.obterUrlBase(key);
    }

    public String defineUrlFotoJSON(final String chave, Integer codigoPessoa, HttpServletRequest request) {
        return defineUrlFotoJSON(chave, codigoPessoa, request, context);
    }

    public static String defineUrlFotoJSON(final String chave, Integer codigoPessoa, HttpServletRequest request, ServletContext context) {
        try {
            URL urlRequisitada = new URL(request.getRequestURL().toString());
            String[] result = Aplicacao.preencherFotoJSON(context, chave, codigoPessoa).split("\\?");
            String image = result[0];
            String timeModified = "";
            if (result.length > 1) {
                timeModified = result[1];
            }
            return String.format("%s://%s" +
                    (urlRequisitada.getPort() > 0 ? ":" : "") +
                    "%s%s/%s/%s%s" + (StringUtils.isBlank(timeModified) ? "" : "&time=%s"), new Object[]{
                    urlRequisitada.getProtocol(),
                    urlRequisitada.getHost(),
                    urlRequisitada.getPort() > 0 ? urlRequisitada.getPort() : "",
                    request.getContextPath(),
                    PATH_RESOURCE_IMG,
                    image,
                    SUFIXO_RESOURCE_JSF,
                    (StringUtils.isBlank(timeModified) ? "" : timeModified)
            });
        } catch (MalformedURLException ex) {
                Logger.getLogger(SuperControle.class.getName()).log(Level.SEVERE, null, ex);
                return null;
        }
    }

    protected void init(final String ctx, final String token) throws ServiceException {
        /*
         * String.format("%s_%s_%s", new Object[]{chave, u.getCodigo(), Calendario.getInstance().getTimeInMillis()}),
         CriptografiaUtil.CHAVE_CRIPTO, CriptografiaUtil.ALGORITMO_AES));
         */
        final String dec = CriptografiaUtil.decrypt(token,
                CriptografiaUtil.CHAVE_CRIPTO, CriptografiaUtil.ALGORITMO_AES);
        if (dec != null && !dec.isEmpty()) {
            final String[] hash = dec.split("_");
            if (hash.length >= 3) {
                final String chaveToken = hash[0];
                final String dataToken = hash[2];
                Date d = Calendario.getInstance().getTime();
                d.setTime(Long.valueOf(dataToken));

                if (!(chaveToken.equals(ctx) && Calendario.maiorOuIgual(Calendario.hoje(), d))) {
                    throw new ServiceException(getViewUtils().getMensagem("mobile.token.invalido"));
                }
            } else {
                throw new ServiceException(getViewUtils().getMensagem("mobile.token.invalido"));
            }
        }
    }

    protected void accessControl(final Usuario user, final RecursoEnum rec, final TipoPermissaoEnum tipo) throws ServiceException {
        if (user.getPerfil().isItemHabilitado(rec, tipo)) {
            entityManagerService.putUserFromCurrentThread(user);
        } else {
            throw new ServiceException(String.format(getViewUtils().getMensagem("permissao"), tipo.getNome(),
                    rec.getDescricao()));
        }
    }

    protected void leaveAccessControl() {
        try {
            entityManagerService.leaveAccessControlFromCurrentThread();
        } catch (Exception e) {
            Uteis.logar(e, this.getClass());
        }
    }

    protected void acao(final String acao) throws ServiceException {
        AuditUtilities.putAcaoFromCurrentThread(Thread.currentThread().getId(), acao);
    }

    protected void leaveAcao() {
        try {
            AuditUtilities.leaveAcaoFromCurrentThread();
        } catch (Exception e) {
            Uteis.logar(e, this.getClass());
        }
    }

    protected void prepare(final String ctx) throws ServiceException {
        EntityManagerService ems = (EntityManagerService) UtilContext.getBean(EntityManagerService.class);
        try {
            ems.closeQuietly(ctx);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    public ModelMap reportarErro(ModelMap modelMap, String mensagemErroID) {
        final String mensagemErro = getViewUtils().getMensagem(mensagemErroID);
        modelMap.addAttribute(STATUS_ERRO, mensagemErro);
        Logger.getLogger(this.getClass().getName()).log(Level.SEVERE, mensagemErro);
        return modelMap;
    }

    public static String getAppUrlEmail(String key) {
        try {
            ConfiguracaoSistemaService css = UtilContext.getBean(ConfiguracaoSistemaService.class);
            ConfiguracaoSistema cfgUsarMenuColaborador = css.consultarPorTipo(UteisValidacao.emptyString(key) ? JSFUtilities.getKey() : key, ConfiguracoesEnum.LINK_APP_EMAIL);
            return cfgUsarMenuColaborador.getValor();
        } catch (Exception e) {
        }
        return "";
    }

    public static String getUrlGooglePlay() {
        try {
            ConfiguracaoSistemaService css = (ConfiguracaoSistemaService) UtilContext.getBean(ConfiguracaoSistemaService.class);
            ConfiguracaoSistema cfgUsarMenuColaborador = css.consultarPorTipo(JSFUtilities.getKey(), ConfiguracoesEnum.URL_GOOGLE_PLAY);
            return cfgUsarMenuColaborador.getValor();
        } catch (Exception e) {
        }
        return "";
    }
    public static String getUrlItunes() {
        try {
            ConfiguracaoSistemaService css = (ConfiguracaoSistemaService) UtilContext.getBean(ConfiguracaoSistemaService.class);
            ConfiguracaoSistema cfgUsarMenuColaborador = css.consultarPorTipo(JSFUtilities.getKey(), ConfiguracoesEnum.URL_ITUNES);
            return cfgUsarMenuColaborador.getValor();
        } catch (Exception e) {
        }
        return "";
    }

    public static String getUrlGooglePlay(String ctx) {
        try {
            ConfiguracaoSistemaService css = (ConfiguracaoSistemaService) UtilContext.getBean(ConfiguracaoSistemaService.class);
            ConfiguracaoSistema cfgUsarMenuColaborador = css.consultarPorTipo(ctx, ConfiguracoesEnum.URL_GOOGLE_PLAY);
            return cfgUsarMenuColaborador.getValor();
        } catch (Exception e) {
        }
        return "";
    }
    public static String getUrlItunes(String ctx) {
        try {
            ConfiguracaoSistemaService css = (ConfiguracaoSistemaService) UtilContext.getBean(ConfiguracaoSistemaService.class);
            ConfiguracaoSistema cfgUsarMenuColaborador = css.consultarPorTipo(ctx, ConfiguracoesEnum.URL_ITUNES);
            return cfgUsarMenuColaborador.getValor();
        } catch (Exception e) {
        }
        return "";
    }

    public static String getNomeAppParaEmail(String key) {
        try {
            ConfiguracaoSistemaService css = (ConfiguracaoSistemaService) UtilContext.getBean(ConfiguracaoSistemaService.class);
            ConfiguracaoSistema cfgUsarMenuColaborador = css.consultarPorTipo(UteisValidacao.emptyString(key) ? JSFUtilities.getKey() : key, ConfiguracoesEnum.NOME_APLICATIVO_PARA_ENVIO_EMAIL);

            return UteisValidacao.emptyString(cfgUsarMenuColaborador.getValor()) ? "" : cfgUsarMenuColaborador.getValor();

        } catch (Exception e) {
        }
        return "";
    }

    public static boolean independente(final String ctx) {
        try {
            String mod = Aplicacao.getProp(ctx, Aplicacao.modulos);
            return mod != null && !mod.contains("ZW");
        } catch (Exception e) {
            Logger.getLogger(ConfigJSONControle.class.getName()).log(Level.SEVERE, null, e);
            return false;
        }
    }

    @Override
    public void setServletContext(ServletContext servletContext) {
        this.context = servletContext;
    }

    public static void notificarRecursoEmpresa(final String chave, final String recursoString, final String username, final Integer empresaId,
                                               final String nomeEmpresa,
                                               final String cidade, final String estado, final String pais) {
        try {
            EnfileiradorNotificadorRecursoSistemaSingleton.getInstance().enfileirarNotificacaoRecursoEmpresa(
                    null,
                    recursoString,
                    chave,
                    false,
                    isUsarUrlRecursoEmpresa(),
                    empresaId,
                    username,
                    nomeEmpresa,
                    cidade,
                    estado,
                    pais
            );
        } catch (Exception e) {
            Logger.getLogger(SuperControle.class.getName()).log(Level.SEVERE, null, e);
        }
    }

    public static boolean isUsarUrlRecursoEmpresa() {
        return Aplicacao.isTrue(Aplicacao.usarUrlRecursoEmpresa);
    }

}
