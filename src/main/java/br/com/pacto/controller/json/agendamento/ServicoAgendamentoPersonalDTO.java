package br.com.pacto.controller.json.agendamento;

import br.com.pacto.bean.Agendamento;
import br.com.pacto.bean.colaborador.ColaboradorSimplesTO;
import br.com.pacto.controller.json.tipoEvento.TipoAgendamentoDTO;
import br.com.pacto.enumerador.agenda.StatusAgendamentoEnum;
import br.com.pacto.objeto.Calendario;
import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class ServicoAgendamentoPersonalDTO {

    private Integer id;
    private String dia;
    private StatusAgendamentoEnum status;
    private TipoAgendamentoDTO tipoAgendamento;
    private String horarioInicial;
    private String horarioFinal;
    private Colaborador<PERSON>implesTO professor;
    private AlunoAgendamentoDTO aluno;
    private String observacao;
    private Integer horarioDisponibilidadeCod;

    public ServicoAgendamentoPersonalDTO(Agendamento agendamento, Boolean treinoIndependente) {
        this.id = agendamento.getCodigo();
        this.dia = Calendario.getData(agendamento.getInicio(), "dd/MM/yyyy");
        this.status = agendamento.getStatus();
        if (agendamento.getTipoEvento() != null) {
            this.tipoAgendamento = new TipoAgendamentoDTO(agendamento.getTipoEvento());
        } else {
            this.tipoAgendamento = new TipoAgendamentoDTO(agendamento.getHorarioDisponibilidade().getDisponibilidade());
            this.tipoAgendamento.setPermitir_app(agendamento.getHorarioDisponibilidade().getPermieAgendarAppTreino());
            this.tipoAgendamento.setSomente_carteira_professor(agendamento.getHorarioDisponibilidade().getApenasAlunosCarteira());
            this.horarioDisponibilidadeCod = agendamento.getHorarioDisponibilidade().getCodigo();
        }
        this.horarioInicial = agendamento.getHoraInicioApresentar();
        this.horarioFinal = agendamento.getHoraFimApresentar();
        this.professor = new ColaboradorSimplesTO(agendamento.getProfessor(), treinoIndependente);
        this.aluno = new AlunoAgendamentoDTO(agendamento.getCliente(), treinoIndependente);
        this.observacao = agendamento.getObservacao();
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getDia() {
        return dia;
    }

    public void setDia(String dia) {
        this.dia = dia;
    }

    public StatusAgendamentoEnum getStatus() {
        return status;
    }

    public void setStatus(StatusAgendamentoEnum status) {
        this.status = status;
    }

    public TipoAgendamentoDTO getTipoAgendamento() {
        return tipoAgendamento;
    }

    public void setTipoAgendamento(TipoAgendamentoDTO tipoAgendamento) {
        this.tipoAgendamento = tipoAgendamento;
    }

    public String getHorarioInicial() {
        return horarioInicial;
    }

    public void setHorarioInicial(String horarioInicial) {
        this.horarioInicial = horarioInicial;
    }

    public String getHorarioFinal() {
        return horarioFinal;
    }

    public void setHorarioFinal(String horarioFinal) {
        this.horarioFinal = horarioFinal;
    }

    public ColaboradorSimplesTO getProfessor() {
        return professor;
    }

    public void setProfessor(ColaboradorSimplesTO professor) {
        this.professor = professor;
    }

    public AlunoAgendamentoDTO getAluno() {
        return aluno;
    }

    public void setAluno(AlunoAgendamentoDTO aluno) {
        this.aluno = aluno;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public Integer getHorarioDisponibilidadeCod() {
        return horarioDisponibilidadeCod;
    }

    public void setHorarioDisponibilidadeCod(Integer horarioDisponibilidadeCod) {
        this.horarioDisponibilidadeCod = horarioDisponibilidadeCod;
    }
}
