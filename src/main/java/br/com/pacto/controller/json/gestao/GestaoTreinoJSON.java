package br.com.pacto.controller.json.gestao;

import br.com.pacto.controller.json.base.SuperJSON;

/**
 * Created by <PERSON> on 15/11/2016.
 */
public class GestaoTreinoJSON extends SuperJSON {

    private Integer codigo = 0;
    private Integer mes = 0;
    private Integer ano = 0;
    private Integer dia = 0;
    private Integer empresa = 0;
    private Integer professor = 0;
    private Integer totalTreinosVencidos = 0;
    private Integer totalTreinosAvencer = 0;
    private Integer totalTreinosEmdia = 0;
    private Integer renovados = 0;
    private Integer naoRenovados = 0;
    private String mesAno = "";

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getMes() {
        return mes;
    }

    public void setMes(Integer mes) {
        this.mes = mes;
    }

    public Integer getAno() {
        return ano;
    }

    public void setAno(Integer ano) {
        this.ano = ano;
    }

    public Integer getDia() {
        return dia;
    }

    public void setDia(Integer dia) {
        this.dia = dia;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public Integer getProfessor() {
        return professor;
    }

    public void setProfessor(Integer professor) {
        this.professor = professor;
    }

    public Integer getTotalTreinosVencidos() {
        return totalTreinosVencidos;
    }

    public void setTotalTreinosVencidos(Integer totalTreinosVencidos) {
        this.totalTreinosVencidos = totalTreinosVencidos;
    }

    public Integer getTotalTreinosAvencer() {
        return totalTreinosAvencer;
    }

    public void setTotalTreinosAvencer(Integer totalTreinosAvencer) {
        this.totalTreinosAvencer = totalTreinosAvencer;
    }

    public Integer getTotalTreinosEmdia() {
        return totalTreinosEmdia;
    }

    public void setTotalTreinosEmdia(Integer totalTreinosEmdia) {
        this.totalTreinosEmdia = totalTreinosEmdia;
    }

    public Integer getRenovados() {
        return renovados;
    }

    public void setRenovados(Integer renovados) {
        this.renovados = renovados;
    }

    public Integer getNaoRenovados() {
        return naoRenovados;
    }

    public void setNaoRenovados(Integer naoRenovados) {
        this.naoRenovados = naoRenovados;
    }

    public String getMesAno() {
        return mesAno;
    }

    public void setMesAno(String mesAno) {
        this.mesAno = mesAno;
    }
}
