package br.com.pacto.controller.json.exportar;
import br.com.pacto.objeto.Calendario;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.olap4j.impl.ArrayMap;



import java.io.File;
import java.io.IOException;
import java.text.DateFormat;
import java.util.*;
import java.io.FileOutputStream;

import javax.servlet.http.HttpServletRequest;

import static org.apache.poi.ss.usermodel.CellStyle.*;
import static org.apache.poi.ss.usermodel.Font.BOLDWEIGHT_BOLD;

/**
 * A very simple program that writes some data to an Excel file
 * using the Apache POI library.
 * <AUTHOR>
 *
 */
public class ExportarXLS {

    public String visualizarRelatorioExcel(final String key,
                                           List<LinkedHashMap<String, Object>> lista,
                                           HttpServletRequest request,
                                           String tituloRel,
                                           String nomeRelatorio,
                                           String urlTreino) throws Exception {
        String nomeEXCEL = nomeRelatorio
                + "-" + key
                + "-" + Calendario.hoje().getTime()
                + ".xls";
        String nomeRelEXCEL = "temp";
        File EXCELFolder = new File(obterCaminhoWebAplicacao(request) + File.separator + nomeRelEXCEL);
        EXCELFolder.mkdirs();
        nomeRelEXCEL = nomeRelEXCEL + File.separator + nomeEXCEL;
        File EXCELFile = new File(obterCaminhoWebAplicacao(request) + File.separator + nomeRelEXCEL);
        if (EXCELFile.exists()) {
            try {
                EXCELFile.delete();
            } catch (Exception e) {
                DateFormat formatador = DateFormat.getDateInstance(DateFormat.MEDIUM);
                String dataStr = formatador.format(Calendario.hoje());
                nomeEXCEL = nomeEXCEL + dataStr + ".xls";
                nomeRelEXCEL = "relatorios" + File.separator + nomeEXCEL;
                EXCELFile = new File(obterCaminhoWebAplicacao(request) + File.separator + nomeRelEXCEL);
            }

        }
        Workbook workbook = new HSSFWorkbook();
        writeExcel(lista,obterCaminhoWebAplicacao(request) + File.separator + nomeRelEXCEL,workbook, tituloRel);

        System.out.println("EXPORTANDO EXCEL: " + EXCELFile.getAbsolutePath());
        return urlTreino.replace("prest/","") + nomeRelEXCEL.replaceAll("\\\\", "/");
    }

    public void writeExcel(List<LinkedHashMap<String, Object>> lista, String excelFilePath, Workbook workbook, String tituloRel) throws IOException {
        Sheet sheet = workbook.createSheet();
        sheet.setColumnWidth(15,20);
        Row titleRow = sheet.createRow(0);
        Map<String, CellStyle> styles = createStyles(workbook);
        titleRow.setHeightInPoints(45);
        Cell titleCell = titleRow.createCell(0);
        titleCell.setCellValue(tituloRel);
        titleCell.setCellStyle(styles.get("title"));
        sheet.addMergedRegion(CellRangeAddress.valueOf("$A$1:$L$1"));

        int rowCount = 0;
        int rowTotal = lista.get(0).size();

        boolean criarNomeColuna = true;

        for ( Map<String,Object> informacoes : lista){
            if (criarNomeColuna) {
                Map<String, Object> inf =  new ArrayMap<>();
                inf.putAll(informacoes);
                inf.put("empty",new Object());
                Row row = sheet.createRow(++rowCount);
                for (int i = 0; i < rowTotal; i++) {
                    Map.Entry<String, Object> top = inf.entrySet().iterator().next();
                    Cell cell = row.createCell(i);
                    cell.setCellValue(trocarTitulo(top.getKey()));
                    cell.setCellStyle(styles.get("header"));
                    sheet.autoSizeColumn(i);
                    inf.remove(top.getKey());
                }
            }
            criarNomeColuna = false;
            Row row2 = sheet.createRow(++rowCount);
            for(int i=0; i < rowTotal; i++) {
                Map.Entry<String,Object> entry = informacoes.entrySet().iterator().next();
                Cell cell = row2.createCell(i);
                cell.setCellValue(entry.getValue().toString());
                cell.setCellStyle(styles.get("cell"));
                informacoes.remove(entry.getKey());
            }
        }

        rowCount++;
        Row sumRow = sheet.createRow(rowCount);
        sumRow.setHeightInPoints(25);
        Cell cell;
        cell = sumRow.createCell(0);
        cell.setCellValue("Total:");
        cell.setCellStyle(styles.get("formula"));
        cell = sumRow.createCell(1);
        cell.setCellValue(lista.size());
        cell.setCellStyle(styles.get("formula_2"));
        try (FileOutputStream outputStream = new FileOutputStream(excelFilePath)) {
            workbook.write(outputStream);
        }
    }

    public String obterCaminhoWebAplicacao(HttpServletRequest request) throws Exception {
        return new File(request.getSession().getServletContext().getRealPath("")).getAbsolutePath();
    }

    private static Map<String, CellStyle> createStyles(Workbook wb){
        Map<String, CellStyle> styles = new HashMap<>();
        CellStyle style;
        Font titleFont = wb.createFont();
        titleFont.setFontHeightInPoints((short)18);
        titleFont.setBoldweight(BOLDWEIGHT_BOLD);
        style = wb.createCellStyle();
        style.setAlignment(ALIGN_CENTER);
        style.setVerticalAlignment(ALIGN_CENTER);
        style.setFont(titleFont);
        styles.put("title", style);

        Font monthFont = wb.createFont();
        monthFont.setFontHeightInPoints((short)11);
        monthFont.setColor(IndexedColors.WHITE.getIndex());
        style = wb.createCellStyle();
        style.setAlignment(ALIGN_CENTER);
        style.setVerticalAlignment(ALIGN_CENTER);
        style.setFillForegroundColor(IndexedColors.GREY_50_PERCENT.getIndex());
        style.setFillPattern(SOLID_FOREGROUND);
        style.setFont(monthFont);
        style.setWrapText(true);
        styles.put("header", style);

        style = wb.createCellStyle();
        style.setAlignment(ALIGN_CENTER);
        style.setWrapText(true);
        style.setBorderRight(BORDER_THIN);
        style.setRightBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderLeft(BORDER_THIN);
        style.setLeftBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderTop(BORDER_THIN);
        style.setTopBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderBottom(BORDER_THIN);
        style.setBottomBorderColor(IndexedColors.BLACK.getIndex());
        styles.put("cell", style);

        style = wb.createCellStyle();
        style.setAlignment(ALIGN_CENTER);
        style.setVerticalAlignment(ALIGN_CENTER);
        style.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        style.setFillPattern(SOLID_FOREGROUND);
        style.setDataFormat(wb.createDataFormat().getFormat("0.00"));
        styles.put("formula", style);

        style = wb.createCellStyle();
        style.setAlignment(ALIGN_CENTER);
        style.setVerticalAlignment(ALIGN_CENTER);
        style.setFillForegroundColor(IndexedColors.GREY_40_PERCENT.getIndex());
        style.setFillPattern(SOLID_FOREGROUND);
        style.setDataFormat(wb.createDataFormat().getFormat("0.00"));
        styles.put("formula_2", style);

        return styles;
    }

    private static String trocarTitulo(String label) {
        switch (label) {
            case "matricula":
                return "Matrícula";
            case "nomeProfessor":
                return "Nome do Professor";
            case "nomeAbreviado":
            case "nomeAluno":
                return "Nome do Aluno";
            case "dataPrograma":
                return "Venc. Programa";
            case "dataUltimoacesso":
                return "Último Acesso";
            case "dataVigenciaAteAjustadaApresentar":
                return "Data Venciemnto";
            case "evento":
                return "Evento";
            case "inicio":
                return "Data de início";
            case "dataLancamento":
                return "Data de Lançamento";
            case "codigo":
                return "Código";
            case "nome":
                return "Nome";
        }
        return label;
    }

}
