package br.com.pacto.controller.json.colaborador;

import br.com.pacto.bean.perfil.Perfil;
import br.com.pacto.bean.perfil.permissao.Permissao;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.bean.perfil.permissao.TipoPermissaoEnum;
import br.com.pacto.bean.perfil.permissao.TipoRecurso;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class PerfilUsuarioDTO {

    private String nome;
    private List<PermissoesDTO> recursos = new ArrayList<>();
    private List<FuncionalidadesDTO> funcionalidades = new ArrayList<>();

    public PerfilUsuarioDTO(Perfil perfil) {
        this.nome = perfil.getNome();

        Set<PermissoesDTO> rucursoList = new HashSet<>();
        Set<FuncionalidadesDTO> funcionalidadeList = new HashSet<>();
        for (Permissao p : perfil.getPermissoes()){
            if (p.getRecurso().getTipo().equals(TipoRecurso.ENTIDADE)) {
                rucursoList.add(new PermissoesDTO(p));
            } else if (p.getRecurso().getTipo().equals(TipoRecurso.FUNCIONALIDADE)) {
                funcionalidadeList.add(new FuncionalidadesDTO(p));
            }
        }
        Set<TipoPermissaoEnum> tipoPermissoes = new HashSet<>();
        tipoPermissoes.add(TipoPermissaoEnum.INCLUIR);

        if(!funcionalidadeList.contains(new FuncionalidadesDTO(new Permissao(RecursoEnum.TELA_PRESCRICAO_TREINO,tipoPermissoes,perfil)))){
            funcionalidadeList.add(new FuncionalidadesDTO(new Permissao(RecursoEnum.TELA_PRESCRICAO_TREINO,tipoPermissoes,perfil)));
        }

        this.recursos.addAll(rucursoList);
        this.funcionalidades.addAll(funcionalidadeList);

    }


    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public List<PermissoesDTO> getRecursos() {
        return recursos;
    }

    public void setRecursos(List<PermissoesDTO> recursos) {
        this.recursos = recursos;
    }

    public List<FuncionalidadesDTO> getFuncionalidades() {
        return funcionalidades;
    }

    public void setFuncionalidades(List<FuncionalidadesDTO> funcionalidades) {
        this.funcionalidades = funcionalidades;
    }
}
