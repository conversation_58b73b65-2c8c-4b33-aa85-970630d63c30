package br.com.pacto.controller.json.aluno;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class ConfigListaRapidaAcessoDTO {

    private List<ListaRapidaAcessoDTO> lista = new ArrayList<>();
    private Boolean verPendencias = false;
    private Boolean verTodos = false;
    private Map<String, Boolean> mapConfigs = new HashMap<>();

    public Boolean getVerPendencias() {
        return verPendencias;
    }

    public Map<String, Boolean> getMapConfigs() {
        return mapConfigs;
    }

    public void setMapConfigs(Map<String, Boolean> mapConfigs) {
        this.mapConfigs = mapConfigs;
    }

    public void setVerPendencias(Boolean verPendencias) {
        this.verPendencias = verPendencias;
    }

    public Boolean getVerTodos() {
        return verTodos;
    }

    public void setVerTodos(Boolean verTodos) {
        this.verTodos = verTodos;
    }

    public List<ListaRapidaAcessoDTO> getLista() {
        return lista;
    }

    public void setLista(List<ListaRapidaAcessoDTO> lista) {
        this.lista = lista;
    }
}
