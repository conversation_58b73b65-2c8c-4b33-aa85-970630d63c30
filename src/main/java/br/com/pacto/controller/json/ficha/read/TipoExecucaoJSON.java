/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.controller.json.ficha.read;

import br.com.pacto.controller.json.base.SuperJSON;

/**
 *
 * <AUTHOR>
 */
public class TipoExecucaoJSON extends SuperJSON {

    private Integer tipo;
    private String nome;

    public TipoExecucaoJSON() {
    }

    public TipoExecucaoJSON(Integer tipo, String nome) {
        this.tipo = tipo;
        this.nome = nome;
    }

    public Integer getTipo() {
        return tipo;
    }

    public void setTipo(Integer tipo) {
        this.tipo = tipo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }
}
