package br.com.pacto.controller.json.aulaDia;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.configuracoes.ConfiguracaoSistema;
import br.com.pacto.bean.configuracoes.ConfiguracoesEnum;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.controller.json.SuperController;
import br.com.pacto.controller.json.turma.HorarioTurmaResponseDTO;
import br.com.pacto.controller.json.turma.TurmasController;
import br.com.pacto.objeto.JSONMapper;
import br.com.pacto.security.aspecto.Permissao;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.notificacao.excecao.AgendaExcecoes;
import br.com.pacto.service.intf.agenda.AgendaService;
import br.com.pacto.service.intf.agendatotal.AgendaTotalService;
import br.com.pacto.service.intf.aula.AulaService;
import br.com.pacto.service.intf.configuracoes.ConfiguracaoSistemaService;
import br.com.pacto.service.intf.gympass.BookingGymPassService;
import br.com.pacto.service.intf.gympass.GymPassBookingService;
import br.com.pacto.service.intf.gympass.LogGymPassService;
import br.com.pacto.util.UtilContext;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import static br.com.pacto.controller.json.base.SuperControle.STATUS_ERRO;

@Controller
@RequestMapping("/psec/aulas")
public class AulasController extends SuperController {


    private AulaService aulaService;
    private BookingGymPassService bookingGymPassService;
    private LogGymPassService logGymPassService;
    private GymPassBookingService gymPassBookingService;
    private final AgendaService agendaNovaService;
    private final AgendaTotalService agendaTotalService;
    private final ConfiguracaoSistemaService configService;
    private final SessaoService sessaoService;
    private List<FilaDeEsperaDTO> filaOrdenada;


    @Autowired
    public AulasController(AulaService aulaService,
                           LogGymPassService logGymPassService,
                           BookingGymPassService bookingGymPassService,
                           GymPassBookingService gymPassBookingService, AgendaService agendaNovaService, AgendaTotalService agendaTotalService, ConfiguracaoSistemaService configService, SessaoService sessaoService){
        this.agendaNovaService = agendaNovaService;
        this.agendaTotalService = agendaTotalService;
        this.configService = configService;
        this.sessaoService = sessaoService;
        Assert.notNull(aulaService, "O serviço de aulas não foi injetado corretamente");
        this.aulaService = aulaService;
        this.bookingGymPassService = bookingGymPassService;
        this.logGymPassService = logGymPassService;
        this.gymPassBookingService = gymPassBookingService;
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listarAulas(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                           PaginadorDTO paginadorDTO,
                                                           @RequestHeader(value = "empresaId", required = true) Integer empresaId)throws JSONException {
        try {
            return ResponseEntityFactory.ok(aulaService.listarAulas(filtros, paginadorDTO, empresaId), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(AulasController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar aulas", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "gympass/log", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> logGympass(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                           PaginadorDTO paginadorDTO,
                                                           @RequestHeader(value = "empresaId", required = true) Integer empresaId)throws JSONException {
        try {
            return ResponseEntityFactory.ok(bookingGymPassService.listarLog(paginadorDTO), paginadorDTO);
        } catch (Exception e) {
            Logger.getLogger(AulasController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar logs do booking", e);
            return ResponseEntityFactory.erroInterno("erro_log", e.getMessage());
        }
    }


    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "gympass/log/sinc", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> logGympassSinc(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                           PaginadorDTO paginadorDTO,
                                                           @RequestHeader(value = "empresaId", required = true) Integer empresaId)throws JSONException {
        try {
            return ResponseEntityFactory.ok(logGymPassService.listarLog(paginadorDTO), paginadorDTO);
        } catch (Exception e) {
            Logger.getLogger(AulasController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar logs do booking", e);
            return ResponseEntityFactory.erroInterno("erro_log", e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "log/gympass/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> logGympassDetalhe(@PathVariable Integer id)throws JSONException {
        try {
            return ResponseEntityFactory.ok(bookingGymPassService.listarLogDetalhe(id));
        } catch (Exception e) {
            Logger.getLogger(AulasController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar logs do booking", e);
            return ResponseEntityFactory.erroInterno("erro_log", e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/v2/criar-aula", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cadastroAulaV2(@RequestHeader (value = "empresaId", required = true) Integer empresaId,
                                                              @RequestBody AulaColetivaResponseDTO aulaDTO) {
        try {
            return ResponseEntityFactory.ok(aulaService.cadastroAulaV2(aulaDTO, null, empresaId));
        } catch (ServiceException e) {
            Logger.getLogger(AulasController.class.getName()).log(Level.SEVERE, "Erro ao tentar cadastrar a aula", e);
            if (e.getCause() == null || e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroConhecido(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cadastroAula(
            @RequestHeader (value = "empresaId", required = true) Integer empresaId,
            @RequestBody AulaDTO aulaDTO){
        try {
            return ResponseEntityFactory.ok(aulaService.cadastroAula(aulaDTO, null, empresaId));
        } catch (ServiceException e) {
            Logger.getLogger(AulasController.class.getName()).log(Level.SEVERE, "Erro ao tentar cadastrar a aula", e);
            if (e.getCause() == null || e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroConhecido(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "{id}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> removerAula(@PathVariable("id") Integer id){
        try {
            aulaService.removerAula(id);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(AulasController.class.getName()).log(Level.SEVERE, "Erro ao tentar remover a aula", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "obter-aula-coletiva/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> detalhesAulaColetiva(@PathVariable("id") Integer id,
                                                                    @RequestHeader(value = "empresaId", required = true) Integer empresaId) {
        try {
            return ResponseEntityFactory.ok(aulaService.detalhesAulaColetiva(id, empresaId, null));
        } catch (ServiceException e) {
            Logger.getLogger(AulasController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter os detalhes da aula coletiva", e);
            return ResponseEntityFactory.erroRegistroDuplicado("mensagem_nao_tratada", e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "obter-horarios/{codigoAula}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> obterHorariosAulaColetiva(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                                         PaginadorDTO paginadorDTO,
                                                                         @PathVariable Integer codigoAula)throws JSONException {
        try {
            return ResponseEntityFactory.ok(aulaService.obterHorariosAulaColetiva(filtros, paginadorDTO, codigoAula), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(TurmasController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar os horários da aula", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/horario-aula/validar/{codigoHorario}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> validarHorarioAula(@PathVariable Integer codigoHorario)throws JSONException {
        try {
            return ResponseEntityFactory.ok(aulaService.existeAlunosHorarioAulaColetivaFutura(codigoHorario));
        } catch (ServiceException e) {
            Logger.getLogger(TurmasController.class.getName()).log(Level.SEVERE, "Erro ao tentar validar se existem alunos futuros no horário", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> detalhesAula(@PathVariable("id") Integer id, @RequestHeader(value = "empresaId", required = true) Integer empresaId){
        try {
            return ResponseEntityFactory.ok(aulaService.detalhesAula(id, empresaId, null));
        } catch (ServiceException e) {
            Logger.getLogger(AulasController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter os detalhes da aula", e);
            return ResponseEntityFactory.erroRegistroDuplicado("mensagem_nao_tratada", e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "horario/{id}/{dataAula}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> detalhesAulaHorario(@PathVariable("id") Integer id,
                                                                   @PathVariable("dataAula") String dataAula,
                                                                   @RequestHeader(value = "empresaId", required = true) Integer empresaId){
        try {
            AulaResponseDTO editada = aulaService.edicaoAulaTemporaria(id, dataAula);
            if(editada == null){
                return ResponseEntityFactory.ok(aulaService.detalhesAula(null, empresaId, id));
            }
            return ResponseEntityFactory.ok(editada);
        } catch (ServiceException e) {
            Logger.getLogger(AulasController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter os detalhes da aula", e);
            return ResponseEntityFactory.erroRegistroDuplicado("mensagem_nao_tratada", e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "v2/atualizar/{id}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alterarAulaV2(@PathVariable("id") Integer codigoAula,
                                                             @RequestBody AulaColetivaResponseDTO aulaDTO,
                                                             @RequestHeader (value = "empresaId", required = true) Integer empresaId) {
        try {
            return ResponseEntityFactory.ok(aulaService.cadastroAulaV2(aulaDTO, codigoAula, empresaId));
        } catch (ServiceException e) {
            Logger.getLogger(AulasController.class.getName()).log(Level.SEVERE, "Erro ao tentar alterar a aula", e);
            if (e.getCause() == null || e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/save-or-update-horarios", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> saveOrUpdateHorarios(@RequestBody List<HorarioTurmaResponseDTO> horarioDTO) {
        try {
            return ResponseEntityFactory.ok(aulaService.saveOrUpdateHorarios(horarioDTO));
        } catch (ServiceException e) {
            Logger.getLogger(TurmasController.class.getName()).log(Level.SEVERE, "Erro ao tentar salvar ou alterar horário da aula coletiva", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/remover-horario/{id}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> removerHorarioTurma(@PathVariable Integer id,
                                                                   @RequestHeader(value = "empresaId", required = true) Integer empresaId)throws JSONException {
        try {
            return ResponseEntityFactory.ok(aulaService.removerHorarioAula(id, empresaId));
        } catch (ServiceException e) {
            Logger.getLogger(TurmasController.class.getName()).log(Level.SEVERE, "Erro ao tentar remover horario da aula coletiva", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "{id}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alterarAula(@PathVariable("id") Integer id,
                                                           @RequestBody AulaDTO aulaDTO,
                                                           @RequestHeader (value = "empresaId", required = true) Integer empresaId){
        try {
            return ResponseEntityFactory.ok(aulaService.cadastroAula(aulaDTO, id, empresaId));
        } catch (ServiceException e) {
            Logger.getLogger(AulasController.class.getName()).log(Level.SEVERE, "Erro ao tentar alterar a aula", e);
            if (e.getCause() == null || e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "{id}/temporaria/{dataAula}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alterarAulaTemporariamente(@PathVariable("id") Integer id,
                                                                          @PathVariable("dataAula") String dataAula,
                                                           @RequestBody AulaDTO aulaDTO){
        try {
            return ResponseEntityFactory.ok(aulaService.editarAulaTemporariamente(aulaDTO, id, dataAula));
        } catch (ServiceException e) {
            Logger.getLogger(AulasController.class.getName()).log(Level.SEVERE, "Erro ao tentar alterar a aula", e);
            if (e.getCause() == null || e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "{id}/replicar/{dataAula}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> replicarAulaTemporariamente(@PathVariable("id") Integer id,
                                                                          @PathVariable("dataAula") String dataAula,
                                                           @RequestBody AulaDTO aulaDTO){
        try {
            return ResponseEntityFactory.ok(aulaService.replicarAulaTemporariamente(aulaDTO, id, dataAula));
        } catch (ServiceException e) {
            Logger.getLogger(AulasController.class.getName()).log(Level.SEVERE, "Erro ao tentar alterar a aula", e);
            if (e.getCause() == null || e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "tv-aula", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> aulasParaTvAula(@RequestHeader("empresaId") Integer empresaId, HttpServletRequest request) {
        try {
            return ResponseEntityFactory.ok(aulaService.listaAulasHorario(empresaId, request));
        } catch (ServiceException e) {
            Logger.getLogger(AulasController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar aulas para a tv aula", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "tv-aula/{horarioAulaId}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> detalhesAulaParaTvAula(@RequestHeader("empresaId") Integer empresaId,
                                                                      @PathVariable("horarioAulaId") Integer horarioAulaId,
                                                                      HttpServletRequest request) {
        try {
            return ResponseEntityFactory.ok(aulaService.detalhesAulaHorario(request, empresaId, horarioAulaId));
        } catch (ServiceException e) {
            Logger.getLogger(AulasController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter detalhes da aula", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "reset/gympass/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> reset(@PathVariable Integer id,
                                                     @RequestHeader(value = "empresaId", required = true) Integer empresaId)throws JSONException {
        try {
            aulaService.resetAula(id, empresaId);
            return ResponseEntityFactory.ok();
        } catch (Exception e) {
            Logger.getLogger(AulasController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar logs do booking", e);
            return ResponseEntityFactory.erroInterno("erro_log", e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "gympass/listar-produtos", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listarProdutosGympass(@RequestHeader(value = "empresaId", required = true) Integer empresaId)throws JSONException {
        try {
            return ResponseEntityFactory.ok(gymPassBookingService.listarProdutos(empresaId));
        } catch (Exception e) {
            Logger.getLogger(AulasController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar produtos gympass", e);
            return ResponseEntityFactory.erroInterno("erro_log", e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/clonar-aula/{id}", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> clonarAula(@PathVariable("id") Integer codigoAulaOriginal,
                                                          @RequestHeader (value = "empresaId", required = true) Integer empresaIdZw) {
        try {
            return ResponseEntityFactory.ok(aulaService.clonarAula(codigoAulaOriginal, empresaIdZw));
        } catch (ServiceException e) {
            Logger.getLogger(AulasController.class.getName()).log(Level.SEVERE, "Erro ao tentar clonar a aula", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/inserirNaFila", method = RequestMethod.POST)
    public ModelMap inserirNaFila(@RequestParam final Integer codigoHorarioTurma,
                                  @RequestParam final String dia,
                                  @RequestParam final Integer codigoAluno) throws ServiceException {
        ModelMap mm = new ModelMap();
        String retorno = "";
        String ctx = sessaoService.getUsuarioAtual().getChave();
        ConfiguracaoSistema cfg = configService.consultarPorTipo(ctx, ConfiguracoesEnum.HABILITAR_FILA_ESPERA);

        try {
            if (!cfg.getValorAsBoolean()) {
                throw new ServiceException(AgendaExcecoes.FILA_DE_ESPERA_DESABILITADA.name());
            }
            retorno = agendaTotalService.inserirNaFilaDeEspera(ctx, codigoHorarioTurma, dia, codigoAluno);
            if (retorno.contains("Erro")) {
                return mm.addAttribute(STATUS_ERRO, retorno);
            } else {
                mm.addAttribute("Fila", retorno);
            }
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(AulasController.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ResponseBody
    @RequestMapping(value = "/removerDaFila", method = RequestMethod.DELETE)
    public ModelMap removerDaFila(@RequestParam final Integer codigoHorarioTurma,
                                  @RequestParam final String dia,
                                  @RequestParam final Integer codigoAluno) {
        ModelMap mm = new ModelMap();
        String result = "";
        String ctx = sessaoService.getUsuarioAtual().getChave();
        try {
            result = agendaTotalService.removerDaFilaDeEspera(ctx, codigoHorarioTurma, dia, codigoAluno);
            if (result.contains("Erro")) {
                return mm.addAttribute(STATUS_ERRO, result);
            } else {
                mm.addAttribute("Fila", result);
            }
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(AulasController.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ResponseBody
    @RequestMapping(value = "/removerDaFilaV2", method = RequestMethod.DELETE)
    public ModelMap removerDaFilaV2(@RequestParam final Integer codigoHorarioTurma,
                                    @RequestParam final String dia,
                                    @RequestParam final Integer codigoAluno,
                                    @RequestParam(required = false) final Integer codigoUsuario) {
        ModelMap mm = new ModelMap();
        String result = "";
        String ctx = sessaoService.getUsuarioAtual().getChave();
        try {
            result = agendaTotalService.removerDaFilaDeEsperaV2(ctx, codigoHorarioTurma, dia, codigoAluno, codigoUsuario);
            if (result.contains("Erro")) {
                return mm.addAttribute(STATUS_ERRO, result);
            } else {
                mm.addAttribute("Fila", result);
            }
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(AulasController.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ResponseBody
    @RequestMapping(value = "/consultar-fila-espera/{codigoHorarioTurma}/{dia}", method = RequestMethod.GET)
    public  ResponseEntity<EnvelopeRespostaDTO> consultarFila(@PathVariable final Integer codigoHorarioTurma,
                                               @PathVariable final String dia, PaginadorDTO paginadorDTO)throws ServiceException {
        try {
            List<FilaDeEsperaDTO> filaEspera = new ArrayList<>();
            String ctx = sessaoService.getUsuarioAtual().getChave();
            ConfiguracaoSistemaService css = UtilContext.getBean(ConfiguracaoSistemaService.class);
            ConfiguracaoSistema cfg = css.consultarPorTipo(ctx, ConfiguracoesEnum.HABILITAR_FILA_ESPERA);
            if (!cfg.getValorAsBoolean()) {
                throw new ServiceException(AgendaExcecoes.FILA_DE_ESPERA_DESABILITADA.name());
            }
            filaEspera = aulaService.consultarFilaEspera(codigoHorarioTurma, dia);
            paginadorDTO.setQuantidadeTotalElementos(1l);
            return ResponseEntityFactory.ok(filaEspera, paginadorDTO);
        } catch (ServiceException ex) {
            Logger.getLogger(AulasController.class.getName()).log(Level.SEVERE, null, ex);
            return ResponseEntityFactory.erroInterno(ex.getChaveExcecao(), ex.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/ordenar-fila-espera", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> ordenarFilaEspera(
            @RequestParam final Integer codigoHorarioTurma,
            @RequestParam final String dia,
            @RequestParam final Integer matricula,
            @RequestParam(required = false) final String tipoOrdenacao,
            @RequestParam(required = false) final Integer novaOrdem
                ) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            ConfiguracaoSistema cfg = configService.consultarPorTipo(ctx, ConfiguracoesEnum.HABILITAR_FILA_ESPERA);

            if (!cfg.getValorAsBoolean()) {
                throw new ServiceException(AgendaExcecoes.FILA_DE_ESPERA_DESABILITADA.name());
            }
            List<FilaDeEsperaDTO> filaOrdenada = aulaService.ordenarFilaEspera(codigoHorarioTurma, dia, matricula, tipoOrdenacao, novaOrdem);
            return ResponseEntityFactory.ok(filaOrdenada);
        } catch (ServiceException ex) {
            Logger.getLogger(AulasController.class.getName()).log(Level.SEVERE, null, ex);
            return ResponseEntityFactory.erroInterno(ex.getChaveExcecao(), ex.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/removerDaFilaTurmaCrm", method = RequestMethod.DELETE)
    public ModelMap removerDaFila(@RequestParam final Integer codigoHorarioTurma,
                                  @RequestParam(required = false) final Integer codigoAluno,
                                  @RequestParam(required = false) final Integer passivo) {
        ModelMap mm = new ModelMap();
        String result = "";
        String ctx = sessaoService.getUsuarioAtual().getChave();
        try {
            result = agendaTotalService.removerDaFilaDeEsperaTurmaCrm(ctx, codigoHorarioTurma, codigoAluno, passivo);
            if (result.contains("Erro")) {
                return mm.addAttribute(STATUS_ERRO, result);
            } else {
                mm.addAttribute("Fila", result);
            }
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(AulasController.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ResponseBody
    @RequestMapping(value = "/consultar-fila-espera-turma-crm/{codigoHorarioTurma}", method = RequestMethod.GET)
    public  ResponseEntity<EnvelopeRespostaDTO> consultarFilaTurmaCrm(@PathVariable final Integer codigoHorarioTurma,
                                                                      PaginadorDTO paginadorDTO)throws ServiceException {
        try {
            List<FilaDeEsperaDTO> filaEspera;
            filaEspera = aulaService.consultarFilaEsperaTurmaCrm(codigoHorarioTurma);
            paginadorDTO.setQuantidadeTotalElementos(1L);
            return ResponseEntityFactory.ok(filaEspera, paginadorDTO);
        } catch (ServiceException ex) {
            Logger.getLogger(AulasController.class.getName()).log(Level.SEVERE, null, ex);
            return ResponseEntityFactory.erroInterno(ex.getChaveExcecao(), ex.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/ordenar-fila-espera-turma-crm", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> ordenarFilaEspera(
            @RequestParam final Integer codigoHorarioTurma,
            @RequestParam(required = false) final Integer matricula,
            @RequestParam(required = false) final Integer passivo,
            @RequestParam(required = false) final String tipoOrdenacao,
            @RequestParam(required = false) final Integer novaOrdem
    ) {
        try {
            List<FilaDeEsperaDTO> filaOrdenada = aulaService.ordenarFilaEsperaTurmaCrm(codigoHorarioTurma, matricula, passivo, tipoOrdenacao, novaOrdem);
            return ResponseEntityFactory.ok(filaOrdenada);
        } catch (ServiceException ex) {
            Logger.getLogger(AulasController.class.getName()).log(Level.SEVERE, null, ex);
            return ResponseEntityFactory.erroInterno(ex.getChaveExcecao(), ex.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/inserirNaFilaTurmaCrm", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> inserirNaFilaTurmaCrm(@RequestParam Integer codigoHorarioTurma,
                                                                     @RequestParam(value = "codigoAluno", required = false) Integer codigoAluno,
                                                                     @RequestParam(value = "codigoPassivo", required = false) Integer codigoPassivo,
                                                                     @RequestBody String passivo){
        String ctx = sessaoService.getUsuarioAtual().getChave();
        try {
            PassivoDTO passivoDTO = null;
            try{
                passivoDTO = JSONMapper.getObject(new JSONObject(passivo), PassivoDTO.class);
                passivoDTO.setResponsavelCadastro(sessaoService.getUsuarioAtual().getId());
                passivoDTO.setEmpresa(sessaoService.getUsuarioAtual().getEmpresaAtual());
            }catch (Exception ignore){}

            return ResponseEntityFactory.ok(agendaTotalService.inserirNaFilaDeEsperaTurmaCrm(ctx, codigoHorarioTurma, codigoAluno, passivoDTO, codigoPassivo));
        } catch (ServiceException e) {
            Logger.getLogger(AulasController.class.getName()).log(Level.SEVERE, "Erro ao tentar inserir na fila de turma crm", e);
            if (e.getCause() == null || e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroConhecido(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }


}
