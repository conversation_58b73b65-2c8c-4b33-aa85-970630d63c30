package br.com.pacto.controller.json.colaborador;

import br.com.pacto.bean.aula.GestaoSalaCheiaAnaliticoTO;
import br.com.pacto.objeto.Calendario;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.text.ParseException;
import java.util.Date;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class ProfessorSubstituidoDTO {

    private Date data;
    private String horario;
    private String aula;
    private String substituido;
    private String substituto;
    private String justificativa;

    public ProfessorSubstituidoDTO() {
    }

    public ProfessorSubstituidoDTO(GestaoSalaCheiaAnaliticoTO gestao) throws ParseException {
        this.data = Calendario.getDate("dd/MM/yyyy", gestao.getDataAula());
        this.horario = gestao.getHoraAula();
        this.aula = gestao.getAula().getNome();
        this.substituido = gestao.getProfessorSub();
        this.substituto = gestao.getProfessor();
        this.justificativa = gestao.getJustificativa();
    }

    public Date getData() {
        return data;
    }

    public void setData(Date data) {
        this.data = data;
    }

    public String getHorario() {
        return horario;
    }

    public void setHorario(String horario) {
        this.horario = horario;
    }

    public String getAula() {
        return aula;
    }

    public void setAula(String aula) {
        this.aula = aula;
    }

    public String getSubstituido() {
        return substituido;
    }

    public void setSubstituido(String substituido) {
        this.substituido = substituido;
    }

    public String getSubstituto() {
        return substituto;
    }

    public void setSubstituto(String substituto) {
        this.substituto = substituto;
    }

    public String getJustificativa() {
        return justificativa;
    }

    public void setJustificativa(String justificativa) {
        this.justificativa = justificativa;
    }
}
