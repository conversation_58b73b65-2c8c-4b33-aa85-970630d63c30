/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.controller.json.professor;

import br.com.pacto.bean.professor.ProfessorSintetico;
import br.com.pacto.controller.json.base.SuperJSON;

/**
 *
 * <AUTHOR>
 */
public class Professor<PERSON>implesJSON extends SuperJSON {

    private Integer codigo;
    private String nome;
    private String avatar;
    private String uriImagem;
    private Boolean isCarteira;

    public ProfessorSimplesJSON() {

    }

    public ProfessorSimplesJSON(ProfessorSintetico ps, boolean isCarteira) {
        this.setCodigo(ps.getCodigo());
        this.setNome(ps.getNome());
        this.setAvatar(ps.getAvatar());
        this.isCarteira = isCarteira;
        this.uriImagem = ps.getUriImagem();
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public void setIsCarteira(Boolean isCarteira) {
        this.isCarteira = isCarteira;
    }

    public Boolean isCarteira() {
        return isCarteira;
    }

    public String getUriImagem() {
        return uriImagem;
    }

    public void setUriImagem(String uriImagem) {
        this.uriImagem = uriImagem;
    }

    public static ProfessorSimplesJSON obterJSON(ProfessorSintetico ps, Boolean isCarteira) {
        ProfessorSimplesJSON json = new ProfessorSimplesJSON();
        json.setCodigo(ps.getCodigo());
        json.setNome(ps.getNome());
        json.setAvatar(ps.getAvatar());
//        json.setAvatar(ps.getUriImagem() != null ? ps.getUriImagem() : ps.getFotoKey());
        json.setIsCarteira(isCarteira);
        json.setUriImagem(ps.getUriImagem());
        return json;
    }
}
