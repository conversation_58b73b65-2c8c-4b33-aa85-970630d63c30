package br.com.pacto.controller.json.crossfit;

import br.com.pacto.bean.aparelho.Aparelho;
import br.com.pacto.bean.aparelho.AparelhoWod;
import br.com.pacto.bean.atividade.AtividadeWod;

/**
 * Created by <PERSON><PERSON>
 */
public class AparelhoWodJSON {

    private Integer codigo;
    private Integer codAparelho;
    private String nomeAparelho;
    private Integer codWod;


    public AparelhoWodJSON(AparelhoWod aparelhoWod) {
        this.codigo = aparelhoWod.getCodigo();
        this.codAparelho = aparelhoWod.getAparelho().getCodigo();
        this.nomeAparelho = aparelhoWod.getAparelho().getNome();
        this.codWod = aparelhoWod.getWod().getCodigo();
    }

    public AparelhoWodJSON(Aparelho aparelho) {
        this.codAparelho = aparelho.getCodigo();
        this.nomeAparelho = aparelho.getNome();
    }

    public AparelhoWodJSON() {
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getCodWod() {
        return codWod;
    }

    public void setCodWod(Integer codWod) {
        this.codWod = codWod;
    }

    public Integer getCodAparelho() {
        return codAparelho;
    }

    public void setCodAparelho(Integer codAparelho) {
        this.codAparelho = codAparelho;
    }

    public String getNomeAparelho() {
        return nomeAparelho;
    }

    public void setNomeAparelho(String nomeAparelho) {
        this.nomeAparelho = nomeAparelho;
    }
}
