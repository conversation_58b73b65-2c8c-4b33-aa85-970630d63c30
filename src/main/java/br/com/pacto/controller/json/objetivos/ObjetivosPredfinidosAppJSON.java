package br.com.pacto.controller.json.objetivos;
/**
 *
 * <AUTHOR>
 */

import br.com.pacto.bean.programa.ObjetivoPredefinido;
import br.com.pacto.controller.json.base.SuperJSON;

public class ObjetivosPredfinidosAppJSON extends SuperJSON {
    private Integer codigo;
    private String nome;
    private Boolean selecionado;
    private String nomeAbreviado;
    private String nomeMin;

    public ObjetivosPredfinidosAppJSON(ObjetivoPredefinido objetivo)
    {
        if(objetivo == null){
            return;
        }
        this.codigo = objetivo.getCodigo();
        this.nome = objetivo.getNome();
        this.selecionado = objetivo.getSelecionado();
        this.nomeAbreviado = objetivo.getNomeAbreviado();
        this.nomeMin = objetivo.getNomeMin();
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Boolean getSelecionado() {
        return selecionado;
    }

    public void setSelecionado(Boolean selecionado) {
        this.selecionado = selecionado;
    }

    public String getNomeAbreviado() {
        return nomeAbreviado;
    }

    public void setNomeAbreviado(String nomeAbreviado) {
        this.nomeAbreviado = nomeAbreviado;
    }

    public String getNomeMin() {
        return nomeMin;
    }

    public void setNomeMin(String nomeMin) {
        this.nomeMin = nomeMin;
    }
}
