package br.com.pacto.controller.json.agendamento;

import br.com.pacto.bean.disponibilidade.Disponibilidade;
import br.com.pacto.bean.tipoEvento.TipoEvento;
import br.com.pacto.enumerador.agenda.TipoAgendamentoEnum;

public class TipoAtividadeDTO {
    private Integer id;
    private String nome;
    private String cor;
    private TipoAgendamentoEnum comportamentoEnum;
    private Boolean tipoEvento;

    public TipoAtividadeDTO() {

    }

    public TipoAtividadeDTO(TipoEvento tipoEvento) {
        this.id = tipoEvento.getCodigo();
        this.nome = tipoEvento.getNome();
        this.cor = tipoEvento.getCor().getCor();
        this.comportamentoEnum = tipoEvento.getComportamento();
        this.tipoEvento = true;
    }

    public TipoAtividadeDTO(Disponibilidade disponibilidadeConfig) {
        this.id = disponibilidadeConfig.getCodigo();
        this.nome = disponibilidadeConfig.getNome();
        this.cor = disponibilidadeConfig.getCor();
        this.comportamentoEnum = TipoAgendamentoEnum.getFromId(disponibilidadeConfig.getComportamento());
        this.tipoEvento = false;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getCor() {
        return cor;
    }

    public void setCor(String cor) {
        this.cor = cor;
    }

    public TipoAgendamentoEnum getComportamentoEnum() {
        return comportamentoEnum;
    }

    public String getComportamentoNome() {
        return comportamentoEnum == null ? "" : comportamentoEnum.getDescricao();
    }

    public void setComportamentoEnum(TipoAgendamentoEnum comportamentoEnum) {
        this.comportamentoEnum = comportamentoEnum;
    }

    public Boolean getTipoEvento() {
        return tipoEvento;
    }

    public void setTipoEvento(Boolean tipoEvento) {
        this.tipoEvento = tipoEvento;
    }
}
