package br.com.pacto.controller.json.programa.read;

import br.com.pacto.bean.aparelho.AparelhoAjuste;
import br.com.pacto.bean.atividade.AtividadeAparelho;
import br.com.pacto.bean.atividade.AtividadeFicha;
import br.com.pacto.bean.atividade.AtividadeFichaAjuste;
import br.com.pacto.bean.serie.Serie;
import br.com.pacto.controller.json.atividade.read.AjusteAppJSON;
import br.com.pacto.controller.json.atividade.write.SerieWriteAppJSON;
import br.com.pacto.controller.json.base.SuperJSON;
import br.com.pacto.util.UteisValidacao;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class AtividadeAppJSON extends SuperJSON {
    private Integer codigo;
    private Integer ficha;
    private Integer atividade;
    private String nomeAtividade;
    private Integer ordem;
    private List<SerieWriteAppJSON> series;
    private List<AjusteAppJSON> ajustes = new ArrayList<>();
    private Integer metodoExecucao;
    private Integer tipoAtividade;
    private String thumb;
    private String urlVideo;
    private String media = null;
    private String setid;
    private List<String> aparelhos;

    public AtividadeAppJSON(AtividadeFicha atividadeFicha)
    {
        if(atividadeFicha == null)
        {
            return;
        }

        this.codigo = atividadeFicha.getCodigo();
        this.ficha = atividadeFicha.getFicha() != null ? atividadeFicha.getFicha().getCodigo() : null;
        this.nomeAtividade = atividadeFicha.getNome();
        this.ordem = atividadeFicha.getOrdem();
        this.series = atividadeFicha.getSeries() != null ? preencheSeries(atividadeFicha.getSeries()) : null;
        this.ajustes = atividadeFicha.getAjustes() != null ? preencheAjustes(atividadeFicha.getAjustes()) : new ArrayList<>();
        this.metodoExecucao = atividadeFicha.getMetodoExecucao() != null ? atividadeFicha.getMetodoExecucao().getId() : null;
        this.setid = atividadeFicha.getSetId();
        this.media = null;
        if(ajustes == null)
        {
            ajustes = new ArrayList<>();
        }
        if(atividadeFicha.getAtividade() != null)
        {
            this.atividade = atividadeFicha.getAtividade().getCodigo();
            this.tipoAtividade = atividadeFicha.getAtividade().getTipoCod();
            this.thumb = atividadeFicha.getAtividade().getFotoKeyPequena();
            this.urlVideo = atividadeFicha.getAtividade().getUrlDefault();
            this.aparelhos = new ArrayList<>();

            List <AtividadeAparelho> aparelhos  = atividadeFicha.getAtividade().getAparelhos();
            for(AtividadeAparelho atividadeAparelho: aparelhos)
            {
                List<AparelhoAjuste> aparelhoAjustes = atividadeAparelho.getAparelho().getAjustes();
                for(AparelhoAjuste aparelhoAjuste: aparelhoAjustes)
                {
                    AjusteAppJSON ajusteAppJSON = new AjusteAppJSON();
                    ajusteAppJSON.setAtividadeFicha(this.atividade);
                    ajusteAppJSON.setNome(aparelhoAjuste.getNome());
                    Boolean novoAjuste = true;
                    for(AjusteAppJSON ajuste: this.ajustes)
                    {
                        if (ajuste.getNome() != null && ajusteAppJSON.getNome() != null) {
                            if (ajusteAppJSON.getNome().compareTo(ajuste.getNome()) != 0) {
                                continue;
                            }
                            novoAjuste = false;
                            break;
                        }
                    }
                    if(novoAjuste){
                        ajustes.add(ajusteAppJSON);
                    }
                }
                this.aparelhos.add(atividadeAparelho.getAparelho().getNome());
            }
        }
    }

    List<SerieWriteAppJSON> preencheSeries(List <Serie> series)
    {
        if(UteisValidacao.emptyList(series))
        {
            return null;
        }
        List<SerieWriteAppJSON> serieWriteAppJSONS = new ArrayList<>();
        for(Serie serie : series)
        {
            SerieWriteAppJSON serieWriteAppJSON = new SerieWriteAppJSON();

            serieWriteAppJSON.setCodigo(serie.getCodigo());
            serieWriteAppJSON.setAtividadeFicha(serie.getAtividadeFicha() != null ? serie.getAtividadeFicha().getCodigo() : null);
            serieWriteAppJSON.setRepeticao(serie.getRepeticao());
            serieWriteAppJSON.setRepeticaoComp(serie.getRepeticaoComp());
            serieWriteAppJSON.setRepeticaoApp(serie.getRepeticaoApp());
            serieWriteAppJSON.setCarga(serie.getCarga());
            serieWriteAppJSON.setCargaComp(serie.getCargaComp());
            serieWriteAppJSON.setCargaApp(serie.getCargaApp());
            serieWriteAppJSON.setDuracao(serie.getDuracao());
            serieWriteAppJSON.setDistancia(serie.getDistancia());
            serieWriteAppJSON.setVelocidade(serie.getVelocidade());
            serieWriteAppJSON.setDescanso(serie.getDescanso());
            serieWriteAppJSON.setOrdem(serie.getOrdem());
            serieWriteAppJSON.setCadencia(serie.getCadencia());

            serieWriteAppJSONS.add(serieWriteAppJSON);
        }
        return serieWriteAppJSONS;
    }

    private List<AjusteAppJSON> preencheAjustes(List <AtividadeFichaAjuste> ajustes)
    {
        if(UteisValidacao.emptyList(ajustes))
        {
            return null;
        }
        List<AjusteAppJSON> ajusteJSONS = new ArrayList<>();
        for(AtividadeFichaAjuste ajuste : ajustes)
        {
            ajusteJSONS.add(new AjusteAppJSON(ajuste));
        }
        return ajusteJSONS;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getFicha() {
        return ficha;
    }

    public void setFicha(Integer ficha) {
        this.ficha = ficha;
    }

    public Integer getAtividade() {
        return atividade;
    }

    public void setAtividade(Integer atividade) {
        this.atividade = atividade;
    }

    public String getNomeAtividade() {
        return nomeAtividade;
    }

    public void setNomeAtividade(String nomeAtividade) {
        this.nomeAtividade = nomeAtividade;
    }

    public List<SerieWriteAppJSON> getSeries() {
        return series;
    }

    public void setSeries(List<SerieWriteAppJSON> series) {
        this.series = series;
    }

    public List<AjusteAppJSON> getAjustes() {
        return ajustes;
    }

    public void setAjustes(List<AjusteAppJSON> ajustes) {
        this.ajustes = ajustes;
    }

    public Integer getOrdem() {
        return ordem;
    }

    public void setOrdem(Integer ordem) {
        this.ordem = ordem;
    }

    public Integer getMetodoExecucao() {
        return metodoExecucao;
    }

    public void setMetodoExecucao(Integer metodoExecucao) {
        this.metodoExecucao = metodoExecucao;
    }

    public Integer getTipoAtividade() {
        return tipoAtividade;
    }

    public void setTipoAtividade(Integer tipoAtividade) {
        this.tipoAtividade = tipoAtividade;
    }

    public String getThumb() {
        return thumb;
    }

    public void setThumb(String thumb) {
        this.thumb = thumb;
    }

    public String getUrlVideo() {
        return urlVideo;
    }

    public void setUrlVideo(String urlVideo) {
        this.urlVideo = urlVideo;
    }

    public String getMedia() {
        return media;
    }

    public void setMedia(String media) {
        this.media = media;
    }

    @JsonProperty("setid")
    public String getSetId() {
        return setid;
    }

    public void setSetId(String setid) {
        this.setid = setid;
    }

    public List<String> getAparelhos() {
        return aparelhos;
    }

    public void setAparelhos(List<String> aparelhos) {
        this.aparelhos = aparelhos;
    }
}
