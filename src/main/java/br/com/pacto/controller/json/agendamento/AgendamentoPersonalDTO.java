package br.com.pacto.controller.json.agendamento;

import br.com.pacto.enumerador.agenda.StatusAgendamentoEnum;
import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * created to paulo 31/10/2018
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AgendamentoPersonalDTO {

    private String dia; //No formato dd/MM/yyyy
    private String horarioInicial;
    private String horarioFinal;
    private Integer tipoAgendamentoId;
    private Integer professorId;
    private Integer matricula;
    private StatusAgendamentoEnum status;
    private String observacao;
    private Integer empresa;
    private Boolean tipoEvento;
    private Integer horarioDisponibilidadeCod;

    public String getDia() {
        return dia;
    }

    public void setDia(String dia) {
        this.dia = dia;
    }

    public String getHorarioInicial() {
        return horarioInicial;
    }

    public void setHorarioInicial(String horarioInicial) {
        this.horarioInicial = horarioInicial;
    }

    public String getHorarioFinal() {
        return horarioFinal;
    }

    public void setHorarioFinal(String horarioFinal) {
        this.horarioFinal = horarioFinal;
    }

    public Integer getTipoAgendamentoId() {
        return tipoAgendamentoId;
    }

    public void setTipoAgendamentoId(Integer tipoAgendamentoId) {
        this.tipoAgendamentoId = tipoAgendamentoId;
    }

    public Integer getProfessorId() {
        return professorId;
    }

    public void setProfessorId(Integer professorId) {
        this.professorId = professorId;
    }

    public Integer getMatricula() {
        return matricula;
    }

    public void setMatricula(Integer matricula) {
        this.matricula = matricula;
    }

    public StatusAgendamentoEnum getStatus() {
        return status;
    }

    public void setStatus(StatusAgendamentoEnum status) {
        this.status = status;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public Boolean getTipoEvento() {
        return tipoEvento;
    }

    public void setTipoEvento(Boolean tipoEvento) {
        this.tipoEvento = tipoEvento;
    }

    public Integer getHorarioDisponibilidadeCod() {
        return horarioDisponibilidadeCod;
    }

    public void setHorarioDisponibilidadeCod(Integer horarioDisponibilidadeCod) {
        this.horarioDisponibilidadeCod = horarioDisponibilidadeCod;
    }
}
