package br.com.pacto.controller.json.agendamento;

import br.com.pacto.bean.Agendamento;
import br.com.pacto.controller.json.aluno.AlunoBasicoResponseDTO;
import br.com.pacto.controller.json.professor.ProfessorSimplesResponseDTO;
import br.com.pacto.controller.json.tipoEvento.TipoEventoDTO;
import br.com.pacto.enumerador.agenda.StatusAgendamentoEnum;
import br.com.pacto.util.DataUtils;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.Date;

/**
 * created to paulo 31/10/2018
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AgendamentoResponseDTO {

    private Integer id;
    private ProfessorSimplesResponseDTO professor;
    private Date dia;
    private String horarioInicial;
    private String horarioFinal;
    private TipoEventoDTO tipoEvento;
    private StatusAgendamentoEnum statusAgendamento;
    private AlunoBasicoResponseDTO aluno;

    public AgendamentoResponseDTO(Agendamento agendamento) {
        this.id = agendamento.getCodigo();
        this.professor = new ProfessorSimplesResponseDTO(agendamento.getProfessor());
        this.dia = agendamento.getInicio();
        this.horarioInicial = DataUtils.dateToString(agendamento.getInicio(), "HH:mm");
        this.horarioFinal = DataUtils.dateToString(agendamento.getFim(), "HH:mm");
        this.tipoEvento = new TipoEventoDTO(agendamento.getTipoEvento());
        this.statusAgendamento = agendamento.getStatus();
        this.aluno = new AlunoBasicoResponseDTO(agendamento.getCliente());
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public ProfessorSimplesResponseDTO getProfessor() {
        return professor;
    }

    public void setProfessor(ProfessorSimplesResponseDTO professor) {
        this.professor = professor;
    }

    public Date getDia() {
        return dia;
    }

    public void setDia(Date dia) {
        this.dia = dia;
    }

    public String getHorarioInicial() {
        return horarioInicial;
    }

    public void setHorarioInicial(String horarioInicial) {
        this.horarioInicial = horarioInicial;
    }

    public String getHorarioFinal() {
        return horarioFinal;
    }

    public void setHorarioFinal(String horarioFinal) {
        this.horarioFinal = horarioFinal;
    }

    public TipoEventoDTO getTipoEvento() {
        return tipoEvento;
    }

    public void setTipoEvento(TipoEventoDTO tipoEvento) {
        this.tipoEvento = tipoEvento;
    }

    public StatusAgendamentoEnum getStatusAgendamento() {
        return statusAgendamento;
    }

    public void setStatusAgendamento(StatusAgendamentoEnum statusAgendamento) {
        this.statusAgendamento = statusAgendamento;
    }

    public AlunoBasicoResponseDTO getAluno() {
        return aluno;
    }

    public void setAluno(AlunoBasicoResponseDTO aluno) {
        this.aluno = aluno;
    }
}
