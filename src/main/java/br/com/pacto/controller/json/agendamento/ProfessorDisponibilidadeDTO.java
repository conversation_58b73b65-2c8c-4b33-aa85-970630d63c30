package br.com.pacto.controller.json.agendamento;

import java.util.List;

public class ProfessorDisponibilidadeDTO {

    private Integer professorId;
    private String param;
    private List<TipoAgendamentoDisponibilidadeDTO> disponibilidades;

    public ProfessorDisponibilidadeDTO() {
    }

    public ProfessorDisponibilidadeDTO(Integer professorId, List<TipoAgendamentoDisponibilidadeDTO> disponibilidades) {
        this.professorId = professorId;
        this.disponibilidades = disponibilidades;
    }

    public Integer getProfessorId() {
        return professorId;
    }

    public void setProfessorId(Integer professorId) {
        this.professorId = professorId;
    }

    public List<TipoAgendamentoDisponibilidadeDTO> getDisponibilidades() {
        return disponibilidades;
    }

    public void setDisponibilidades(List<TipoAgendamentoDisponibilidadeDTO> disponibilidades) {
        this.disponibilidades = disponibilidades;
    }

    public String getParam() {
        return param;
    }

    public void setParam(String param) {
        this.param = param;
    }
}
