package br.com.pacto.controller.json.turma;

import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class AmbienteDTO {

    private Integer id;
    private String nome;
    private Integer tipoAmbiente;
    private Integer capacidadeMaximaConvidados;
    private Integer situacao;
    private Integer capacidade;
    private String tipoModulo;
    private Integer coletor;
    private Boolean ativo;

    public AmbienteDTO() {
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Integer getTipoAmbiente() {
        return tipoAmbiente;
    }

    public void setTipoAmbiente(Integer tipoAmbiente) {
        this.tipoAmbiente = tipoAmbiente;
    }

    public Integer getCapacidadeMaximaConvidados() {
        return capacidadeMaximaConvidados;
    }

    public void setCapacidadeMaximaConvidados(Integer capacidadeMaximaConvidados) {
        this.capacidadeMaximaConvidados = capacidadeMaximaConvidados;
    }

    public Integer getSituacao() {
        return situacao;
    }

    public void setSituacao(Integer situacao) {
        this.situacao = situacao;
    }

    public Integer getCapacidade() {
        return capacidade;
    }

    public void setCapacidade(Integer capacidade) {
        this.capacidade = capacidade;
    }

    public String getTipoModulo() {
        return tipoModulo;
    }

    public void setTipoModulo(String tipoModulo) {
        this.tipoModulo = tipoModulo;
    }

    public Integer getColetor() {
        return coletor;
    }

    public void setColetor(Integer coletor) {
        this.coletor = coletor;
    }

    public Boolean getAtivo() {
        return ativo;
    }

    public void setAtivo(Boolean ativo) {
        this.ativo = ativo;
    }
}
