package br.com.pacto.controller.json.ambiente;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.security.aspecto.Permissao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.ambiente.AmbienteService;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by <PERSON><PERSON> on 26/09/2018.
 */
@Controller
@RequestMapping("/psec/ambientes")
public class AmbienteController {

    private AmbienteService ambienteService;

    @Autowired
    public AmbienteController(AmbienteService ambienteService){
        Assert.notNull(ambienteService, "O serviço de ambiente não foi injetado corretamente");
        this.ambienteService = ambienteService;
    }

    @ResponseBody
//    @Permissao(recursos = RecursoEnum.AMBIENTES)
    @RequestMapping(value = "", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cadastroAmbiente(@RequestBody AmbienteTO ambienteTO) {
        try {
            return ResponseEntityFactory.ok(
                    ambienteService.cadastroAmbiente(ambienteTO)
            );
        } catch (ServiceException e) {
            Logger.getLogger(AmbienteController.class.getName()).log(Level.SEVERE, "Erro ao tentar cadastrar aluno", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ResponseBody
    @RequestMapping(value = "", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> consultarAmbiente(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                                 @RequestHeader(value = "empresaId", required = true) Integer empresaId,
                                                                 PaginadorDTO paginadorDTO) throws JSONException {
        try {
            FiltroAmbienteJSON filtroAmbienteJSON = new FiltroAmbienteJSON(filtros);

            return ResponseEntityFactory.ok(ambienteService.listaAmbientes(filtroAmbienteJSON, paginadorDTO, empresaId), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(AmbienteController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar as atividades", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
//    @Permissao(recursos = RecursoEnum.AMBIENTES)
    @RequestMapping(value = "{id}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alterarAmbiente(
            @PathVariable("id") Integer id,
            @RequestBody AmbienteTO ambienteTO) {
        try {
            return ResponseEntityFactory.ok(
                    ambienteService.alterarAmbiente(id, ambienteTO)
            );
        } catch (ServiceException e) {
            Logger.getLogger(AmbienteController.class.getName()).log(Level.SEVERE, "Erro ao tentar cadastrar aluno", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ResponseBody
//    @Permissao(recursos = RecursoEnum.AMBIENTES)
    @RequestMapping(value = "{id}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> removerAmbiente(
            @PathVariable("id") Integer id){
        try {

            ambienteService.removerAmbiente(id);
            return ResponseEntityFactory.ok(
            );
        } catch (ServiceException e) {
            Logger.getLogger(AmbienteController.class.getName()).log(Level.SEVERE, "Erro ao tentar cadastrar aluno", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AMBIENTES)
    @RequestMapping(value = "/all", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listarTodosAmbientes(
            @RequestHeader(value = "empresaId", required = true) Integer empresaId,
            @RequestParam(value = "validarNoTreino", required = false) Boolean validarNoTreino
    ) {
        try {
            return ResponseEntityFactory.ok(ambienteService.obterTodos(empresaId, validarNoTreino));
        } catch (ServiceException e) {
            Logger.getLogger(AmbienteController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar ambientes", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AMBIENTES)
    @RequestMapping(value = "/all/ativo", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listarTodosAmbientesAtivos(@RequestParam(value = "filters", required = false) JSONObject filters) {
        try {
            FiltroAmbienteJSON filtros = new FiltroAmbienteJSON(filters);
            return ResponseEntityFactory.ok(ambienteService.obterTodosAtivos(filtros));
        } catch (ServiceException e) {
            Logger.getLogger(AmbienteController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar ambientes", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception ex){
            Logger.getLogger(AmbienteController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar ambientes", ex);
            return ResponseEntityFactory.erroInterno("", ex.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AMBIENTES)
    @RequestMapping(value = "tiposAmbiente/all", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listarTodosTiposAmbientes() {
        try {
            return ResponseEntityFactory.ok(ambienteService.obterTodosTiposAmbiente());
        } catch (ServiceException e) {
            Logger.getLogger(AmbienteController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar ambientes", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception ex){
            Logger.getLogger(AmbienteController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar tipos ambientes", ex);
            return ResponseEntityFactory.erroInterno("", ex.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AMBIENTES)
    @RequestMapping(value = "coletores/all", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listarTodosColetoresAmbiente() {
        try {
            return ResponseEntityFactory.ok(ambienteService.obterColetoresAmbiente());
        } catch (ServiceException e) {
            Logger.getLogger(AmbienteController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar coletores", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception ex){
            Logger.getLogger(AmbienteController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar coletores", ex);
            return ResponseEntityFactory.erroInterno("", ex.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AMBIENTES)
    @RequestMapping(value = "niveisTurma/all", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listarTodosNiveisTurmaAmbiente() {
        try {
            return ResponseEntityFactory.ok(ambienteService.obterNiveisTurmaAmbiente());
        } catch (ServiceException e) {
            Logger.getLogger(AmbienteController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar niveis turma", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception ex){
            Logger.getLogger(AmbienteController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar niveis turma", ex);
            return ResponseEntityFactory.erroInterno("", ex.getMessage());
        }
    }
}
