/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.controller.json.notificacao;

import br.com.pacto.bean.notificacao.Notificacao;
import br.com.pacto.bean.notificacao.TipoNotificacaoEnum;
import br.com.pacto.bean.notificacao.VisibilidadeNotificacaoEnum;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.base.SuperControle;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.notificacao.NotificacaoService;
import br.com.pacto.service.intf.usuario.UsuarioService;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import br.com.pacto.util.UteisValidacao;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("/notificacoes")
public class NotificacaoJSONControle extends SuperControle {

    @Autowired
    private NotificacaoService service;
    @Autowired
    private UsuarioService usuarioService;

    @RequestMapping(value = "{ctx}/get", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap get(@PathVariable String ctx,
            @RequestParam String username,
            @RequestParam(required = false, defaultValue = "false") Boolean apenasNaoLidas,
            @RequestParam(required = false) String matricula
                 ) {
        ModelMap mm = new ModelMap();
        try {
            //super.init(ctx, token);
            //Notificacao obj = service...;
            Usuario usuario;
            if(UteisValidacao.emptyString(matricula)){
                usuario = usuarioService.obterPorAtributo(ctx, "username", username);
            } else {
                usuario = usuarioService.consultarPorMatricula(ctx, Integer.valueOf(matricula));
            }
            if (usuario != null) {
                List<Notificacao> lista = service.obterMaisNotificacoes(ctx, null, usuario.getCliente().getCodigo(), apenasNaoLidas,
                        10, 0, VisibilidadeNotificacaoEnum.ALUNO);
                List<NotificacaoJSON> arrJSON = new ArrayList<NotificacaoJSON>();
                for (Notificacao notificacao : lista) {
                    //verificar se a notificação ñ se tornou obsoleta
                    if ((notificacao.getTipo() == TipoNotificacaoEnum.SOLICITAR_RENOVACAO
                            || notificacao.getTipo() == TipoNotificacaoEnum.LEMBRAR_ALUNO_COMPROMISSO) && notificacao.getCliente() != null
                            && notificacao.getPrograma() != null) {
                        if (notificacao.getPrograma().getDataRenovacao() != null) {
                            continue;
                        }
                    }
                    if(notificacao.getTipo() == TipoNotificacaoEnum.SOLICITAR_RENOVACAO
                            && service.isTreinoVigente(ctx, usuario.getCliente().getCodigo())) {
                        return null;
                    }

                    arrJSON.add(notificacao.toJSONObject());
                }
                mm.addAttribute("academia", arrJSON);
                mm.addAttribute("professor", new ArrayList<NotificacaoJSON>());
            } else {
                throw new ServiceException(getViewUtils().getMensagem("mobile.usuarioinvalido"));
            }
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(NotificacaoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/historico", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap historico(@PathVariable final String ctx, @RequestParam final Integer codigoCliente,
            @RequestParam final Integer index, @RequestParam final Integer maxResults) {
        ModelMap mm = new ModelMap();
        try {
            //super.init(ctx, token);
            if (codigoCliente != null && codigoCliente > 0) {
                List<Notificacao> lista = service.obterMaisNotificacoes(ctx, null, codigoCliente, false,
                        maxResults, index, VisibilidadeNotificacaoEnum.ALUNO);
                List<NotificacaoJSON> arrJSON = new ArrayList<NotificacaoJSON>();
                for (Notificacao notificacao : lista) {
                    //verificar se a notificação ñ se tornou obsoleta
                    if ((notificacao.getTipo() == TipoNotificacaoEnum.SOLICITAR_RENOVACAO
                            || notificacao.getTipo() == TipoNotificacaoEnum.LEMBRAR_ALUNO_COMPROMISSO) && notificacao.getCliente() != null
                            && notificacao.getPrograma() != null) {
                        if (notificacao.getPrograma().getDataRenovacao() != null) {
                            continue;
                        }
                    }
                    arrJSON.add(notificacao.toJSONObject());
                }
                mm.addAttribute(RETURN, arrJSON);
            } else {
                throw new ServiceException(getViewUtils().getMensagem("mobile.usuarioinvalido"));
            }
        } catch (ServiceException ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(NotificacaoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }
    
    
    @RequestMapping(value = "{ctx}/respostaNotificacao", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap respostaNotificacao(@PathVariable String ctx,
            @RequestParam String idNotf, @RequestParam String reposta) {
        ModelMap mm = new ModelMap();
        try {
            Notificacao notf = service.gravarResposta(ctx, Integer.valueOf(idNotf), reposta);
            mm.addAttribute(RETURN, notf.toJSON());
        } catch (ServiceException ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(NotificacaoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }
    
    @RequestMapping(value = "{ctx}/gerarNotificacao", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap gerarNotificacaoCRM(@PathVariable String ctx,
            @RequestParam String idCliente, 
            @RequestParam String titulo,
            @RequestParam String textoCRM,
            @RequestParam String opcoes) {
        ModelMap mm = new ModelMap();
        try {
            Notificacao notf = service.gerarNotificacao(ctx, Integer.valueOf(idCliente), Calendario.hoje(),
                    titulo, 
                    textoCRM, 
                    TipoNotificacaoEnum.CONTATO_CRM,
                    opcoes, false);
            mm.addAttribute(RETURN, notf.toJSON());
        } catch (ServiceException ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(NotificacaoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/marcarLida", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap marcarLida(@PathVariable String ctx,
                        @RequestParam String username,
                        @RequestParam(required = false) Integer idNotificacao,
                        @RequestParam(required = false) String matricula,
                        @RequestParam boolean marcarTodas) {
        ModelMap mm = new ModelMap();
        try {
            if (!marcarTodas && UteisValidacao.emptyNumber(idNotificacao)) {
                throw new ServiceException("idNotificacao não informada.");
            }
            mm.addAttribute(RETURN, "Notificaçoes marcadas como lida. Total " +
                    service.marcarLidas(ctx, username, matricula, idNotificacao, marcarTodas));
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(NotificacaoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/gerarNotificacaoEmail", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap gerarNotificacaoEmail(@PathVariable String ctx,
                                   @RequestParam String email,
                                   @RequestParam String titulo,
                                   @RequestParam String texto,
                                   @RequestParam String opcoes,
                                   @RequestParam Integer tipoNotificacao) {
        ModelMap mm = new ModelMap();
        try {
//            String msgHomologacao = "[sandbox]" + texto;
            Notificacao notf = service.gerarNotificacaoPorEmail(ctx, email, titulo, texto, opcoes, Calendario.hoje(), TipoNotificacaoEnum.obterPorID(tipoNotificacao));
            mm.addAttribute(RETURN, notf.toJSON());
        } catch (ServiceException ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(NotificacaoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/delete", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap gerarNotificacaoEmail(@PathVariable String ctx,
                                   @RequestParam Integer codigo) {
        ModelMap mm = new ModelMap();
        try {
            Notificacao notf = service.obterPorId(ctx, codigo);
            service.excluir(ctx, notf);
            mm.addAttribute(RETURN, "Notificação removida com sucesso.");
        } catch (ServiceException ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(NotificacaoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }
}
