package br.com.pacto.controller.json.locacao;

import br.com.pacto.bean.locacao.Locacao;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.enumeradores.DiasSemana;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.apache.commons.lang3.StringUtils.isNotBlank;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class LocacaoTO {

    private Integer codigo;
    private String nome;
    private String descricao;
    private Boolean ativo;
    private Integer tipoHorario;
    private String tipoHorarioDescricao;
    private String cor;
    private Integer duracaoMinutos;
    private Integer tempoMinimoMinutos;
    private ProdutoTO produto;
    private String base64Imagem;
    private String formControlNomeImagem;
    private String urlImagem;
    private Date dataInicial;
    private Date dataFinal;
    private String dias = "";
    private Integer tipoValidacao;
    private List<LocacaoHorarioTO> horarios;
    private List<LocacaoProdutoSugeridoTO> produtosSugeridos;
    private List<ItemValidacaoLocacaoTO> itensValidacao;

    public LocacaoTO() {}

    public LocacaoTO(Locacao locacaoEntity, boolean apenasDiasVigentes) {
        if (locacaoEntity != null) {
            this.codigo = locacaoEntity.getCodigo();
            this.nome = locacaoEntity.getNome();
            this.ativo = locacaoEntity.getAtivo();
            this.descricao = locacaoEntity.getDescricao();
            if (locacaoEntity.getTipoHorario() != null) {
                this.tipoHorario = locacaoEntity.getTipoHorario().getCodigo();
                this.tipoHorarioDescricao = locacaoEntity.getTipoHorario().getDescricao();
            }
            this.cor = locacaoEntity.getCor();
            this.duracaoMinutos = locacaoEntity.getDuracaoMinutos();
            this.tempoMinimoMinutos = locacaoEntity.getTempoMinimoMinutos();
            this.dataInicial = locacaoEntity.getDataInicial();
            this.dataFinal = locacaoEntity.getDataFinal();
            this.tipoValidacao = locacaoEntity.getTipoValidacao();
            this.produto = new ProdutoTO();
            this.produto.setCodigo(locacaoEntity.getProduto());

            if (locacaoEntity.getHorarios() != null) {
                setHorarios(new ArrayList<>());
                locacaoEntity.getHorarios().forEach(lh -> {
                    LocacaoHorarioTO locacaoHorarioTO = new LocacaoHorarioTO(lh);
                    getHorarios().add(locacaoHorarioTO);
                    if (!UteisValidacao.emptyString(locacaoHorarioTO.getDiaSemana())) {
                        DiasSemana diasSemana = DiasSemana.getDiaSemana(locacaoHorarioTO.getDiaSemana());
                        if (!apenasDiasVigentes || (apenasDiasVigentes && lh.getAtivo())) {
                            if (UteisValidacao.emptyString(this.dias)) {
                                this.dias = diasSemana.getMin();
                            } else if (!this.dias.contains(diasSemana.getMin())) {
                                this.dias += ", " + diasSemana.getMin();
                            }
                        }
                    }
                });
            }
            StringBuilder diasOrdenados = new StringBuilder();
            for(DiasSemana dia: DiasSemana.values()) {
                if (this.dias.contains(dia.getMin())) {
                    diasOrdenados.append(", ").append(dia.getMin());
                }
            }
            this.dias = diasOrdenados.toString().replaceFirst(",", "");
            if (locacaoEntity.getProdutosSugeridos() != null) {
                setProdutosSugeridos(new ArrayList<>());
                locacaoEntity.getProdutosSugeridos().forEach(ps -> {
                    this.getProdutosSugeridos().add(new LocacaoProdutoSugeridoTO(ps));
                });
            }

            if (isNotBlank(locacaoEntity.getFotoKey())) {
                setUrlImagem(Aplicacao.getProp(Aplicacao.urlFotosNuvem) + "/" + locacaoEntity.getFotoKey());
            }

            if (locacaoEntity.getItensValidacao() != null) {
                setItensValidacao(new ArrayList<>());
                locacaoEntity.getItensValidacao().forEach(pv -> {
                    this.getItensValidacao().add(new ItemValidacaoLocacaoTO(pv));
                });
            }
        }
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Boolean getAtivo() {
        return ativo;
    }

    public void setAtivo(Boolean ativo) {
        this.ativo = ativo;
    }

    public Integer getTipoHorario() {
        return tipoHorario;
    }

    public void setTipoHorario(Integer tipoHorario) {
        this.tipoHorario = tipoHorario;
    }

    public String getTipoHorarioDescricao() {
        return tipoHorarioDescricao;
    }

    public void setTipoHorarioDescricao(String tipoHorarioDescricao) {
        this.tipoHorarioDescricao = tipoHorarioDescricao;
    }

    public String getCor() {
        return cor;
    }

    public void setCor(String cor) {
        this.cor = cor;
    }

    public Integer getDuracaoMinutos() {
        return duracaoMinutos;
    }

    public void setDuracaoMinutos(Integer duracaoMinutos) {
        this.duracaoMinutos = duracaoMinutos;
    }

    public Integer getTempoMinimoMinutos() {
        return tempoMinimoMinutos;
    }

    public void setTempoMinimoMinutos(Integer tempoMinimoMinutos) {
        this.tempoMinimoMinutos = tempoMinimoMinutos;
    }

    public ProdutoTO getProduto() {
        return produto;
    }

    public void setProduto(ProdutoTO produto) {
        this.produto = produto;
    }

    public String getBase64Imagem() {
        return base64Imagem;
    }

    public void setBase64Imagem(String base64Imagem) {
        this.base64Imagem = base64Imagem;
    }

    public String getUrlImagem() {
        return urlImagem;
    }

    public void setUrlImagem(String urlImagem) {
        this.urlImagem = urlImagem;
    }

    public Date getDataInicial() {
        return dataInicial;
    }

    public void setDataInicial(Date dataInicial) {
        this.dataInicial = dataInicial;
    }

    public Date getDataFinal() {
        return dataFinal;
    }

    public void setDataFinal(Date dataFinal) {
        this.dataFinal = dataFinal;
    }

    public String getDias() {
        return dias;
    }

    public void setDias(String dias) {
        this.dias = dias;
    }

    public Integer getTipoValidacao() {
        return tipoValidacao;
    }

    public void setTipoValidacao(Integer tipoValidacao) {
        this.tipoValidacao = tipoValidacao;
    }

    public List<LocacaoHorarioTO> getHorarios() {
        return horarios;
    }

    public void setHorarios(List<LocacaoHorarioTO> horarios) {
        this.horarios = horarios;
    }

    public List<LocacaoProdutoSugeridoTO> getProdutosSugeridos() {
        return produtosSugeridos;
    }

    public void setProdutosSugeridos(List<LocacaoProdutoSugeridoTO> produtosSugeridos) {
        this.produtosSugeridos = produtosSugeridos;
    }

    public List<ItemValidacaoLocacaoTO> getItensValidacao() {
        return itensValidacao;
    }

    public void setItensValidacao(List<ItemValidacaoLocacaoTO> itensValidacao) {
        this.itensValidacao = itensValidacao;
    }

    public String getFormControlNomeImagem() {
        return formControlNomeImagem;
    }

    public void setFormControlNomeImagem(String formControlNomeImagem) {
        this.formControlNomeImagem = formControlNomeImagem;
    }
}
