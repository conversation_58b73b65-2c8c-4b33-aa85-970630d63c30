package br.com.pacto.controller.json.atividade;

import br.com.pacto.controller.json.base.SuperJSON;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

/**
 * <AUTHOR> 01/02/2019
 */
public class FiltroAtividadeCrossfitJSON  extends SuperJSON {

    private Boolean nome = false;
    private Boolean ativo = false;
    private String parametro;
    private Integer maxResults;
    private Integer index;
    private boolean crossfit;

    public FiltroAtividadeCrossfitJSON(JSONObject filters) throws JSONException {
        if (filters != null) {
            this.parametro = filters.optString("quicksearchValue");
            this.maxResults = filters.optInt("maxResults");
            this.index = filters.optInt("index");
            this.crossfit = filters.optBoolean("crossfit");
            JSONArray colunasVisiveis = filters.optJSONArray("quicksearchFields");
            if (colunasVisiveis != null) {
                for (int i = 0; i < colunasVisiveis.length(); i++) {
                    if (colunasVisiveis.get(i).equals("nome")) {
                        this.nome = true;
                    }
                    if (colunasVisiveis.get(i).equals("")) {
                        this.ativo = true;
                    }
                }
            }
        }
    }

    public Boolean getNome() {
        return nome;
    }

    public void setNome(Boolean nome) {
        this.nome = nome;
    }

    public Boolean getAtivo() {
        return ativo;
    }

    public void setAtivo(Boolean ativo) {
        this.ativo = ativo;
    }

    public String getParametro() {
        return parametro;
    }

    public void setParametro(String parametro) {
        this.parametro = parametro;
    }

    public Integer getMaxResults() {
        return maxResults;
    }

    public void setMaxResults(Integer maxResults) {
        this.maxResults = maxResults;
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public boolean isCrossfit() {
        return crossfit;
    }

    public void setCrossfit(boolean crossfit) {
        this.crossfit = crossfit;
    }
}
