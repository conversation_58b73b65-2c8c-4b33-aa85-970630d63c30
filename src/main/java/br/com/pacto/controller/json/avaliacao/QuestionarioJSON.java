package br.com.pacto.controller.json.avaliacao;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

public class QuestionarioJSON {
    private int codigo;
    private String tipoQuestionario;
    private String descricao;
    private List<PerguntaJSON> perguntas;

    public static QuestionarioJSON montarJSON(String str) throws Exception{
        JSONObject json = new JSONObject(str);
        QuestionarioJSON q = new QuestionarioJSON();
        q.setCodigo(json.getInt("codigo"));
        q.setTipoQuestionario(json.getString("tipoQuestionario"));
        q.setDescricao(json.getString   ("descricao"));
        q.setPerguntas(new ArrayList<PerguntaJSON>());
        JSONArray perguntaVOs = json.getJSONArray("questionarioPerguntaVOs");
        for(int i = 0; i < perguntaVOs.length(); i++){
            JSONObject jsonPergunta = perguntaVOs.getJSONObject(i).getJSONObject("pergunta");
            PerguntaJSON p = new PerguntaJSON();
            p.setCodigo(jsonPergunta.getInt("codigo"));
            p.setTipoPergunta(jsonPergunta.getString("tipoPergunta"));
            p.setDescricao(jsonPergunta.getString("descricao"));
            p.setRespostas(new ArrayList<RespostaPerguntaJSON>());
            JSONArray respostaPerguntaVOs = jsonPergunta.getJSONArray("respostaPerguntaVOs");
            for(int j = 0; j < respostaPerguntaVOs.length(); j++){
                RespostaPerguntaJSON r = new RespostaPerguntaJSON();
                JSONObject jsonresp = respostaPerguntaVOs.getJSONObject(j);
                r.setCodigo(jsonresp.getInt("codigo"));
                r.setDescricao(jsonresp.getString("descricaoRespota"));
                p.getRespostas().add(r);
            }
            q.getPerguntas().add(p);
        }
        return q;
    }

    public int getCodigo() {
        return codigo;
    }

    public void setCodigo(int codigo) {
        this.codigo = codigo;
    }

    public String getTipoQuestionario() {
        return tipoQuestionario;
    }

    public void setTipoQuestionario(String tipoQuestionario) {
        this.tipoQuestionario = tipoQuestionario;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public List<PerguntaJSON> getPerguntas() {
        return perguntas;
    }

    public void setPerguntas(List<PerguntaJSON> perguntas) {
        this.perguntas = perguntas;
    }
}
