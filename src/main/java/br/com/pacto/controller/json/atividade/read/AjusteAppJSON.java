/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.controller.json.atividade.read;

import br.com.pacto.bean.atividade.AtividadeFichaAjuste;
import br.com.pacto.controller.json.base.SuperJSON;

/**
 *
 * <AUTHOR>
 */
public class AjusteAppJSON extends SuperJSON {

    private Integer codigo;
    private Integer atividadeFicha;
    private String nome;
    private String valor;

    public AjusteAppJSON() {
    }

    public AjusteAppJSON(String nome, String valor) {
        this.nome = nome;
        this.valor = valor;
    }

    public AjusteAppJSON(AtividadeFichaAjuste ajuste) {
        if(ajuste == null) {
            return;
        }
        this.nome = ajuste.getNome();
        this.valor = ajuste.getValor();
        this.atividadeFicha = ajuste.getAtividadeFicha() != null ? ajuste.getAtividadeFicha().getCodigo() : null;
        this.codigo = ajuste.getCodigo();
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getValor() {
        return valor;
    }

    public void setValor(String valor) {
        this.valor = valor;
    }

    public Integer getAtividadeFicha() {
        if (atividadeFicha == null) {
            atividadeFicha = 0;
        }
        return atividadeFicha;
    }

    public void setAtividadeFicha(Integer atividadeFicha) {
        this.atividadeFicha = atividadeFicha;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }
}
