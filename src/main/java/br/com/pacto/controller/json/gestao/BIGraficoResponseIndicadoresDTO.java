package br.com.pacto.controller.json.gestao;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR>
 * @since 18/10/2019
 */
public class BIGraficoResponseIndicadoresDTO {
    //o Humberto que falou que assim que funciona
    @JsonProperty("TOTAL")
    private Integer total;
    @JsonProperty("ATIVOS")
    private Integer ativos;
    @JsonProperty("INATIVOS")
    private Integer inativos;
    @JsonProperty("ATIVOSTREINO")
    private Integer ativostreino;
    @JsonProperty("EMDIA")
    private Integer emdia;
    @JsonProperty("TREINOSVENCIDOS")
    private Integer treinosvencidos;
    @JsonProperty("TREINOSRENOVAR")
    private Integer treinosrenovar ;
    @JsonProperty("AGENDAMENTOS")
    private Integer agendamentos;
    @JsonProperty("AGEXECUTARAM")
    private Integer agexecutaram;
    @JsonProperty("AGFALTARAM")
    private Integer agfaltaram;
    @JsonProperty("AGCANCELARAM")
    private Integer agcancelaram;
    @JsonProperty("RENOVADOS")
    private Integer renovados;
    @JsonProperty("NAORENOVADOS")
    private Integer naorenovados;
    @JsonProperty("AVENCER")
    private Integer avencer;
    @JsonProperty("NOVOSCARTEIRA")
    private Integer novoscarteira;
    @JsonProperty("TROCARAMCARTEIRA")
    private Integer trocaramcarteira;
    @JsonProperty("MEDIOCARTEIRA")
    private Integer mediocarteira;
    @JsonProperty("SEMTREINO")
    private Integer semtreino;
    @JsonProperty("PERCENTUALEMDIA")
    private Integer percentualemdia;
    @JsonProperty("PERCENTUALVENCIDOS")
    private Integer percentualvencidos;
    @JsonProperty("MEDIORPROGRAMA")
    private Integer mediorprograma;
    @JsonProperty("NRAVALIACOES")
    private Integer nravaliacoes;
    @JsonProperty("AVALIACOES")
    private Double avaliacoes;
    @JsonProperty("ESTRELAUM")
    private Integer estrelaum;
    @JsonProperty("ESTRELADOIS")
    private Integer estreladois;
    @JsonProperty("ESTRELATRES")
    private Integer estrelatres;
    @JsonProperty("ESTRELAQUATRO")
    private Integer estrelaquatro;
    @JsonProperty("ESTRELACINCO")
    private Integer estrelacinco;
    @JsonProperty("COMAVALIACAO")
    private Integer comavaliacao;
    @JsonProperty("SEMAVALIACAO")
    private Integer semavaliacao;
    @JsonProperty("AGPROFESSORES")
    private Integer agprofessores;
    @JsonProperty("HORASDISPONIBILIDADE")
    private Integer horasdisponibilidade;
    @JsonProperty("HORASEXECUTADAS")
    private Integer horasexecutadas;
    @JsonProperty("PERCOCUPACAO")
    private Integer percocupacao;
    @JsonProperty("AGNOVOSTREINOS")
    private Integer agnovostreinos;
    @JsonProperty("AGTREINOSRENOVADOS")
    private Integer agtreinosrevisados;
    @JsonProperty("AGTREINOSREVISADOS")
    private Integer agtreinosrenovados;
    @JsonProperty("AGAVALIACAOFISICA")
    private Integer agavaliacaofisica;
    @JsonProperty("PERCENTUALRENOVACAO")
    private Integer percentualrenovacao;

    public BIGraficoResponseIndicadoresDTO( ){

     }

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }

    public Integer getAtivos() {
        return ativos;
    }

    public void setAtivos(Integer ativos) {
        this.ativos = ativos;
    }

    public Integer getInativos() {
        return inativos;
    }

    public void setInativos(Integer inativos) {
        this.inativos = inativos;
    }

    public Integer getAtivostreino() {
        return ativostreino;
    }

    public void setAtivostreino(Integer ativostreino) {
        this.ativostreino = ativostreino;
    }

    public Integer getEmdia() {
        return emdia;
    }

    public void setEmdia(Integer emdia) {
        this.emdia = emdia;
    }

    public Integer getTreinosvencidos() {
        return treinosvencidos;
    }

    public void setTreinosvencidos(Integer treinosvencidos) {
        this.treinosvencidos = treinosvencidos;
    }

    public Integer getTreinosrenovar() {
        return treinosrenovar;
    }

    public void setTreinosrenovar(Integer treinosrenovar) {
        this.treinosrenovar = treinosrenovar;
    }

    public Integer getAgendamentos() {
        return agendamentos;
    }

    public void setAgendamentos(Integer agendamentos) {
        this.agendamentos = agendamentos;
    }

    public Integer getAgexecutaram() {
        return agexecutaram;
    }

    public void setAgexecutaram(Integer agexecutaram) {
        this.agexecutaram = agexecutaram;
    }

    public Integer getAgfaltaram() {
        return agfaltaram;
    }

    public void setAgfaltaram(Integer agfaltaram) {
        this.agfaltaram = agfaltaram;
    }

    public Integer getAgcancelaram() {
        return agcancelaram;
    }

    public void setAgcancelaram(Integer agcancelaram) {
        this.agcancelaram = agcancelaram;
    }

    public Integer getRenovados() {
        return renovados;
    }

    public void setRenovados(Integer renovados) {
        this.renovados = renovados;
    }

    public Integer getNaorenovados() {
        return naorenovados;
    }

    public void setNaorenovados(Integer naorenovados) {
        this.naorenovados = naorenovados;
    }

    public Integer getAvencer() {
        return avencer;
    }

    public void setAvencer(Integer avencer) {
        this.avencer = avencer;
    }

    public Integer getNovoscarteira() {
        return novoscarteira;
    }

    public void setNovoscarteira(Integer novoscarteira) {
        this.novoscarteira = novoscarteira;
    }

    public Integer getTrocaramcarteira() {
        return trocaramcarteira;
    }

    public void setTrocaramcarteira(Integer trocaramcarteira) {
        this.trocaramcarteira = trocaramcarteira;
    }

    public Integer getMediocarteira() {
        return mediocarteira;
    }

    public void setMediocarteira(Integer mediocarteira) {
        this.mediocarteira = mediocarteira;
    }

    public Integer getSemtreino() {
        return semtreino;
    }

    public void setSemtreino(Integer semtreino) {
        this.semtreino = semtreino;
    }

    public Integer getPercentualemdia() {
        return percentualemdia;
    }

    public void setPercentualemdia(Integer percentualemdia) {
        this.percentualemdia = percentualemdia;
    }

    public Integer getPercentualvencidos() {
        return percentualvencidos;
    }

    public void setPercentualvencidos(Integer percentualvencidos) {
        this.percentualvencidos = percentualvencidos;
    }

    public Integer getMediorprograma() {
        return mediorprograma;
    }

    public void setMediorprograma(Integer mediorprograma) {
        this.mediorprograma = mediorprograma;
    }

    public Integer getNravaliacoes() {
        return nravaliacoes;
    }

    public void setNravaliacoes(Integer nravaliacoes) {
        this.nravaliacoes = nravaliacoes;
    }

    public Double getAvaliacoes() {
        return avaliacoes;
    }

    public void setAvaliacoes(Double avaliacoes) {
        this.avaliacoes = avaliacoes;
    }

    public Integer getEstrelaum() {
        return estrelaum;
    }

    public void setEstrelaum(Integer estrelaum) {
        this.estrelaum = estrelaum;
    }

    public Integer getEstreladois() {
        return estreladois;
    }

    public void setEstreladois(Integer estreladois) {
        this.estreladois = estreladois;
    }

    public Integer getEstrelatres() {
        return estrelatres;
    }

    public void setEstrelatres(Integer estrelatres) {
        this.estrelatres = estrelatres;
    }

    public Integer getEstrelaquatro() {
        return estrelaquatro;
    }

    public void setEstrelaquatro(Integer estrelaquatro) {
        this.estrelaquatro = estrelaquatro;
    }

    public Integer getEstrelacinco() {
        return estrelacinco;
    }

    public void setEstrelacinco(Integer estrelacinco) {
        this.estrelacinco = estrelacinco;
    }

    public Integer getComavaliacao() {
        return comavaliacao;
    }

    public void setComavaliacao(Integer comavaliacao) {
        this.comavaliacao = comavaliacao;
    }

    public Integer getSemavaliacao() {
        return semavaliacao;
    }

    public void setSemavaliacao(Integer semavaliacao) {
        this.semavaliacao = semavaliacao;
    }

    public Integer getAgprofessores() {
        return agprofessores;
    }

    public void setAgprofessores(Integer agprofessores) {
        this.agprofessores = agprofessores;
    }

    public Integer getHorasdisponibilidade() {
        return horasdisponibilidade;
    }

    public void setHorasdisponibilidade(Integer horasdisponibilidade) {
        this.horasdisponibilidade = horasdisponibilidade;
    }

    public Integer getHorasexecutadas() {
        return horasexecutadas;
    }

    public void setHorasexecutadas(Integer horasexecutadas) {
        this.horasexecutadas = horasexecutadas;
    }

    public Integer getPercocupacao() {
        return percocupacao;
    }

    public void setPercocupacao(Integer percocupacao) {
        this.percocupacao = percocupacao;
    }

    public Integer getAgnovostreinos() {
        return agnovostreinos;
    }

    public void setAgnovostreinos(Integer agnovostreinos) {
        this.agnovostreinos = agnovostreinos;
    }

    public Integer getAgtreinosrevisados() {
        return agtreinosrevisados;
    }

    public void setAgtreinosrevisados(Integer agtreinosrevisados) {
        this.agtreinosrevisados = agtreinosrevisados;
    }

    public Integer getAgtreinosrenovados() {
        return agtreinosrenovados;
    }

    public void setAgtreinosrenovados(Integer agtreinosrenovados) {
        this.agtreinosrenovados = agtreinosrenovados;
    }

    public Integer getAgavaliacaofisica() {
        return agavaliacaofisica;
    }

    public void setAgavaliacaofisica(Integer agavaliacaofisica) {
        this.agavaliacaofisica = agavaliacaofisica;
    }

    public Integer getPercentualrenovacao() {
        return percentualrenovacao;
    }

    public void setPercentualrenovacao(Integer percentualrenovacao) {
        this.percentualrenovacao = percentualrenovacao;
    }
}
