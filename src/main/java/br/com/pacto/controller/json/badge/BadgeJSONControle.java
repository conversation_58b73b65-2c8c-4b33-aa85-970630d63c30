/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pacto.controller.json.badge;

import br.com.pacto.bean.badge.Badge;
import br.com.pacto.bean.badge.TipoBadgeEnum;
import br.com.pacto.bean.programa.ProgramaTreino;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.base.SuperControle;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.badge.BadgeService;
import br.com.pacto.service.intf.programa.ProgramaTreinoService;
import br.com.pacto.service.intf.usuario.UsuarioService;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("/badges")
public class BadgeJSONControle extends SuperControle {

    @Autowired
    private ProgramaTreinoService programaService;
    @Autowired
    private UsuarioService usuarioService;
    @Autowired
    private BadgeService badgeService;

    @RequestMapping(value = "{ctx}/get", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap badges(@PathVariable String ctx, @RequestParam String username) {
        ModelMap mm = new ModelMap();
        try {
            Usuario usuario = usuarioService.obterPorAtributo(ctx, "username", username);
            if (usuario != null) {
                List<Badge> l = badgeService.consultarPorAluno(ctx, usuario.getCliente().getCodigo());
                if (l != null) {
                    List<ConquistaJSON> listaJSON = new ArrayList<ConquistaJSON>();
                    for (Badge badge : l) {
                        ConquistaJSON c = new ConquistaJSON();
                        c.setBadge(new BadgeJSON(badge.getTipo().getId().toString(), badge.getTipo().getNome(),
                                badge.getTipo().getImg(), badge.getTipo().getDescricao1(), badge.getTipo().getDescricao2()));
                        listaJSON.add(c);
                    }
                    mm.addAttribute("badges", listaJSON);
                }
            } else {
                throw new ServiceException(getViewUtils().getMensagem("mobile.usuarioinvalido"));
            }
        } catch (Exception e) {
            mm.addAttribute(STATUS_ERRO, e.getMessage());
            Logger.getLogger(BadgeJSONControle.class.getName()).log(Level.SEVERE, null, e);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/conquista", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap conquistaBadge(@PathVariable String ctx, @RequestParam String username) {
        ModelMap mm = new ModelMap();
        try {
            Usuario usuario = usuarioService.obterPorAtributo(ctx, "username", username);
            if (usuario != null) {
                ProgramaTreino programa = programaService.obterUltimoProgramaVigente(ctx, usuario.getCliente());
                if (programa != null) {
                    List<Badge> l = programaService.calcularBadges(ctx, programa);
                    if (l != null && !l.isEmpty()) {
                        ConquistaJSON conquista = new ConquistaJSON();
                        Badge b = l.get(0);
                        BadgeJSON bJSON = new BadgeJSON(b.getCodigo().toString(), b.getTipo().getNome(), b.getTipo().getImg(),
                                b.getDescricao(), b.getTipo().getDescricao2());
                        conquista.setBadge(bJSON);
                        mm.addAttribute("conquista", conquista);
                    }
                } else {
                    throw new ServiceException(getViewUtils().getMensagem("mobile.programanaoencontrado"));
                }
            } else {
                throw new ServiceException(getViewUtils().getMensagem("mobile.usuarioinvalido"));
            }
        } catch (Exception e) {
            mm.addAttribute(STATUS_ERRO, e.getMessage());
            Logger.getLogger(BadgeJSONControle.class.getName()).log(Level.SEVERE, null, e);
        }
        return mm;
    }
}