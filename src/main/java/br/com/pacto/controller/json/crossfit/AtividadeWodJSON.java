package br.com.pacto.controller.json.crossfit;

import br.com.pacto.bean.atividade.Atividade;
import br.com.pacto.bean.atividade.AtividadeWod;
import br.com.pacto.bean.atividade.CategoriaAtividadeWodEnum;
import br.com.pacto.bean.atividade.UnidadeMedidaEnum;

/**
 * Created by <PERSON><PERSON>
 */
public class AtividadeWodJSON {

    private Integer codigo;
    private Integer codAtividade;
    private String nomeAtividade;
    private Integer codWod;
    private String linkVideo;
    private String videoID;
    private Integer codUnidadeMedida;
    private String unidadeMedida;
    private Integer codCategoriaAtividadeWod;
    private String categoriaAtividadeWod;
    private String descricaoAtividade;


    public AtividadeWodJSON(AtividadeWod atividadeWod) {
        this.codigo = atividadeWod.getCodigo();
        this.codAtividade = atividadeWod.getAtividade().getCodigo();
        this.nomeAtividade = atividadeWod.getAtividade().getNome();
        this.descricaoAtividade = atividadeWod.getAtividade().getDescricao();
        this.codWod = atividadeWod.getWod().getCodigo();
        this.linkVideo = atividadeWod.getAtividade().getLinkVideo();
        this.videoID = atividadeWod.getAtividade().obterCodigoYouTube();
        this.codUnidadeMedida = atividadeWod.getAtividade().getUnidadeMedida().getId();
        this.unidadeMedida = atividadeWod.getAtividade().getUnidadeMedida().getDescricao();
        this.codCategoriaAtividadeWod = atividadeWod.getAtividade().getCategoriaAtividadeWod().getId();
        this.categoriaAtividadeWod = atividadeWod.getAtividade().getCategoriaAtividadeWod().getDescricao();
    }

    public AtividadeWodJSON(Atividade atividade) {
        this.codAtividade = atividade.getCodigo();
        this.nomeAtividade = atividade.getNome();
        this.linkVideo = atividade.getLinkVideo();
        this.descricaoAtividade = atividade.getDescricao();
        this.videoID = atividade.obterCodigoYouTube();
        this.codUnidadeMedida = atividade.getUnidadeMedida().getId();
        this.unidadeMedida = atividade.getUnidadeMedida().getDescricao();
        this.codCategoriaAtividadeWod = atividade.getCategoriaAtividadeWod().getId();
        this.categoriaAtividadeWod = atividade.getCategoriaAtividadeWod().getDescricao();
    }

    public AtividadeWodJSON() {
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getCodAtividade() {
        return codAtividade;
    }

    public void setCodAtividade(Integer codAtividade) {
        this.codAtividade = codAtividade;
    }

    public Integer getCodWod() {
        return codWod;
    }

    public void setCodWod(Integer codWod) {
        this.codWod = codWod;
    }

    public String getNomeAtividade() {
        return nomeAtividade;
    }

    public void setNomeAtividade(String nomeAtividade) {
        this.nomeAtividade = nomeAtividade;
    }

    public String getLinkVideo() {
        if (linkVideo == null) {
            linkVideo = "";
        }
        return linkVideo;
    }

    public void setLinkVideo(String linkVideo) {
        this.linkVideo = linkVideo;
    }

    public String getVideoID() {
        if (videoID == null) {
            videoID = "";
        }
        return videoID;
    }

    public void setVideoID(String videoID) {
        this.videoID = videoID;
    }

    public Integer getCodUnidadeMedida() {
        if (codUnidadeMedida == null) {
            codUnidadeMedida = UnidadeMedidaEnum.NENHUM.getId();
        }
        return codUnidadeMedida;
    }

    public void setCodUnidadeMedida(Integer codUnidadeMedida) {
        this.codUnidadeMedida = codUnidadeMedida;
    }

    public String getUnidadeMedida() {
        if (unidadeMedida == null) {
            unidadeMedida = "";
        }
        return unidadeMedida;
    }

    public void setUnidadeMedida(String unidadeMedida) {
        this.unidadeMedida = unidadeMedida;
    }

    public Integer getCodCategoriaAtividadeWod() {
        if (codCategoriaAtividadeWod == null) {
            codCategoriaAtividadeWod = CategoriaAtividadeWodEnum.NENHUM.getId();
        }
        return codCategoriaAtividadeWod;
    }

    public void setCodCategoriaAtividadeWod(Integer codCategoriaAtividadeWod) {
        this.codCategoriaAtividadeWod = codCategoriaAtividadeWod;
    }

    public String getCategoriaAtividadeWod() {
        if (categoriaAtividadeWod == null) {
            categoriaAtividadeWod = "";
        }
        return categoriaAtividadeWod;
    }

    public void setCategoriaAtividadeWod(String categoriaAtividadeWod) {
        this.categoriaAtividadeWod = categoriaAtividadeWod;
    }

    public String getDescricaoAtividade() {
        if (descricaoAtividade == null) {
            descricaoAtividade = "";
        }
        return descricaoAtividade;
    }

    public void setDescricaoAtividade(String descricaoAtividade) {
        this.descricaoAtividade = descricaoAtividade;
    }
}
