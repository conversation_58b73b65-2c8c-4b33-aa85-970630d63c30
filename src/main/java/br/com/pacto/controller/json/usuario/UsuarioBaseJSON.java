package br.com.pacto.controller.json.usuario;

import br.com.pacto.bean.usuario.TipoUsuarioEnum;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.base.SuperJSON;

import java.util.ArrayList;
import java.util.List;

public class UsuarioBaseJSON extends SuperJSON {

    private Integer codigo;
    private String nome;
    private String username;
    private String tipoUsuario;
    private String email;
    private Boolean administrador;
    private String telefone;
    private List<String> perfis;
    private List<String> tipoPerfis;
    private String cpf;
    private boolean pendencia;

    public UsuarioBaseJSON(Usuario usuario) {
        this.codigo = usuario.getCodigo();
        this.nome = usuario.getNome();
        this.username = usuario.getUserName();
        this.tipoUsuario = usuario.getTipo().getDescricao();
        if (usuario.getUsuarioEmail() != null) {
            this.email = usuario.getUsuarioEmail().getEmail();
        }
        this.administrador = TipoUsuarioEnum.COORDENADOR.equals(usuario.getTipo());
        this.telefone = "";
        this.perfis = new ArrayList<>();
        this.perfis.add(usuario.getTipo().toString());

        this.tipoPerfis = new ArrayList<>();
        if (TipoUsuarioEnum.COORDENADOR.equals(usuario.getTipo()) ||
                TipoUsuarioEnum.ROOT.equals(usuario.getTipo())) {
            this.tipoPerfis.add("ADMINISTRADOR");
        } else {
            this.tipoPerfis.add(usuario.getTipo().toString());
        }
        this.cpf = usuario.getCpf();

    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getTipoUsuario() {
        return tipoUsuario;
    }

    public void setTipoUsuario(String tipoUsuario) {
        this.tipoUsuario = tipoUsuario;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Boolean getAdministrador() {
        return administrador;
    }

    public void setAdministrador(Boolean administrador) {
        this.administrador = administrador;
    }

    public String getTelefone() {
        return telefone;
    }

    public void setTelefone(String telefone) {
        this.telefone = telefone;
    }

    public List<String> getPerfis() {
        return perfis;
    }

    public void setPerfis(List<String> perfis) {
        this.perfis = perfis;
    }

    public List<String> getTipoPerfis() {
        return tipoPerfis;
    }

    public void setTipoPerfis(List<String> tipoPerfis) {
        this.tipoPerfis = tipoPerfis;
    }

    public String getCpf() {
        return cpf;
    }

    public void setCpf(String cpf) {
        this.cpf = cpf;
    }

    public boolean isPendencia() {
        return pendencia;
    }

    public void setPendencia(boolean pendencia) {
        this.pendencia = pendencia;
    }
}
