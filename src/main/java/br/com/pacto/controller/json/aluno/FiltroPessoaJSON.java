package br.com.pacto.controller.json.aluno;

import br.com.pacto.controller.json.base.SuperJSON;
import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

public class FiltroPessoaJSON extends SuperJSON {

    private String parametro;
    private String rg;
    private String cpf;
    private String matricula;
    private String treino;
    private Integer contrato;
    private Integer pessoa;
    private Integer cliente;
    private Integer colaborador;
    private String passaporte;
    private String placa;
    private String rne;
    private String telefone;
    private String email;
    private String codigoAcesso;
    private String responsavel;
    private String tipoDeConsulta;
    private boolean visualizarOutrasCarteiras = false;
    private List<String> situacoes;
    private List<Integer> empresas;
    private List<Integer> tipos;
    private List<Integer> consultores;
    private List<Integer> professores;
    private List<Integer> categorias;
    private List<Integer> classificacoes;
    private List<Integer> profissoes;
    private List<Integer> grupos;
    private List<Integer> niveis;

    public FiltroPessoaJSON(JSONObject filters) {
        if (filters != null) {
            this.parametro = filters.optString("parametro");
            this.rg = filters.optString("rg");
            this.cpf = filters.optString("cpf");
            this.matricula = filters.optString("matricula");
            this.treino = filters.optString("treino");
            this.contrato = filters.optInt("contrato");
            this.pessoa = filters.optInt("pessoa");
            this.cliente = filters.optInt("cliente");
            this.colaborador = filters.optInt("colaborador");
            this.passaporte = filters.optString("passaporte");
            this.placa = filters.optString("placa");
            this.rne = filters.optString("rne");
            this.telefone = filters.optString("telefone");
            this.email = filters.optString("email");
            this.codigoAcesso = filters.optString("codigoAcesso");
            this.responsavel = filters.optString("responsavel");
            this.tipoDeConsulta = filters.optString("tipoDeConsulta");

            JSONArray gruposFiltro = filters.optJSONArray("grupos");
            this.grupos = this.obterListaCodigosInteger(gruposFiltro);

            JSONArray profissoesFiltro = filters.optJSONArray("profissoes");
            this.profissoes = this.obterListaCodigosInteger(profissoesFiltro);

            JSONArray classificacoesFiltro = filters.optJSONArray("classificacoes");
            this.classificacoes = this.obterListaCodigosInteger(classificacoesFiltro);

            JSONArray consultoresFiltro = filters.optJSONArray("consultores");
            this.consultores = this.obterListaCodigosInteger(consultoresFiltro);

            JSONArray professoresFiltro = filters.optJSONArray("professores");
            this.professores = this.obterListaCodigosInteger(professoresFiltro);

            JSONArray empresasFiltro = filters.optJSONArray("empresas");
            this.empresas = this.obterListaCodigosInteger(empresasFiltro);

            JSONArray situacaoFiltro = filters.optJSONArray("situacoes");
            this.situacoes = this.obterListaCodigosString(situacaoFiltro);

            JSONArray tipoFiltro = filters.optJSONArray("tipos");
            this.tipos = this.obterListaCodigosInteger(tipoFiltro);

            JSONArray categoriasFiltro = filters.optJSONArray("categorias");
            this.categorias = this.obterListaCodigosInteger(categoriasFiltro);

            JSONArray niveisFiltro = filters.optJSONArray("niveis");
            this.niveis = this.obterListaCodigosInteger(niveisFiltro);
        }
    }


    private List<Integer> obterListaCodigosInteger(JSONArray jsonArray) {
        List<Integer> lista = new ArrayList<>();
        if (jsonArray != null) {
            for (int i = 0; i < jsonArray.length(); i++) {
                try {
                    if (jsonArray.get(i) instanceof JSONObject) {
                        if (((JSONObject) jsonArray.get(i)).get("id") != null) {
                            lista.add(((JSONObject) jsonArray.get(i)).getInt("id"));
                        } else if (((JSONObject) jsonArray.get(i)).get("codigo") != null) {
                            lista.add(((JSONObject) jsonArray.get(i)).getInt("codigo"));
                        }
                    } else {
                        lista.add(jsonArray.getInt(i));
                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }
        }
        return lista;
    }

    private List<String> obterListaCodigosString(JSONArray jsonArray) {
        List<String> lista = new ArrayList<>();
        if (jsonArray != null) {
            for (int i = 0; i < jsonArray.length(); i++) {
                try {
                    if (jsonArray.get(i) instanceof JSONObject) {
                        if (((JSONObject) jsonArray.get(i)).get("id") != null) {
                            lista.add(((JSONObject) jsonArray.get(i)).getString("id"));
                        } else if (((JSONObject) jsonArray.get(i)).get("codigo") != null) {
                            lista.add(((JSONObject) jsonArray.get(i)).getString("codigo"));
                        }
                    } else {
                        lista.add(jsonArray.getString(i));
                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }
        }
        return lista;
    }


    public String getParametro() {
        return parametro;
    }

    public void setParametro(String parametro) {
        this.parametro = parametro;
    }

    public String getRg() {
        return rg;
    }

    public void setRg(String rg) {
        this.rg = rg;
    }

    public String getCpf() {
        return cpf;
    }

    public void setCpf(String cpf) {
        this.cpf = cpf;
    }

    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public Integer getContrato() {
        return contrato;
    }

    public void setContrato(Integer contrato) {
        this.contrato = contrato;
    }

    public Integer getPessoa() {
        return pessoa;
    }

    public void setPessoa(Integer pessoa) {
        this.pessoa = pessoa;
    }

    public Integer getCliente() {
        return cliente;
    }

    public void setCliente(Integer cliente) {
        this.cliente = cliente;
    }

    public Integer getColaborador() {
        return colaborador;
    }

    public void setColaborador(Integer colaborador) {
        this.colaborador = colaborador;
    }

    public String getPassaporte() {
        return passaporte;
    }

    public void setPassaporte(String passaporte) {
        this.passaporte = passaporte;
    }

    public String getPlaca() {
        return placa;
    }

    public void setPlaca(String placa) {
        this.placa = placa;
    }

    public String getRne() {
        return rne;
    }

    public void setRne(String rne) {
        this.rne = rne;
    }

    public String getTelefone() {
        return telefone;
    }

    public void setTelefone(String telefone) {
        this.telefone = telefone;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public List<String> getSituacoes() {
        return situacoes;
    }

    public void setSituacoes(List<String> situacoes) {
        this.situacoes = situacoes;
    }

    public List<Integer> getEmpresas() {
        return empresas;
    }

    public void setEmpresas(List<Integer> empresas) {
        this.empresas = empresas;
    }

    public List<Integer> getTipos() {
        return tipos;
    }

    public void setTipos(List<Integer> tipos) {
        this.tipos = tipos;
    }

    public List<Integer> getConsultores() {
        return consultores;
    }

    public void setConsultores(List<Integer> consultores) {
        this.consultores = consultores;
    }

    public List<Integer> getProfessores() {
        return professores;
    }

    public void setProfessores(List<Integer> professores) {
        this.professores = professores;
    }

    public List<Integer> getCategorias() {
        return categorias;
    }

    public void setCategorias(List<Integer> categorias) {
        this.categorias = categorias;
    }

    public List<Integer> getClassificacoes() {
        return classificacoes;
    }

    public void setClassificacoes(List<Integer> classificacoes) {
        this.classificacoes = classificacoes;
    }

    public List<Integer> getProfissoes() {
        return profissoes;
    }

    public void setProfissoes(List<Integer> profissoes) {
        this.profissoes = profissoes;
    }

    public String getCodigoAcesso() {
        return codigoAcesso;
    }

    public void setCodigoAcesso(String codigoAcesso) {
        this.codigoAcesso = codigoAcesso;
    }

    public List<Integer> getGrupos() {
        return grupos;
    }

    public void setGrupos(List<Integer> grupos) {
        this.grupos = grupos;
    }

    public String getResponsavel() {
        return responsavel;
    }

    public void setResponsavel(String responsavel) {
        this.responsavel = responsavel;
    }

    public String getTipoDeConsulta() {
        return tipoDeConsulta;
    }

    public void setTipoDeConsulta(String tipoDeConsulta) {
        this.tipoDeConsulta = tipoDeConsulta;
    }

    public String getTreino() {
        return treino;
    }

    public void setTreino(String treino) {
        this.treino = treino;
    }

    public List<Integer> getNiveis() {
        return niveis;
    }

    public void setNiveis(List<Integer> niveis) {
        this.niveis = niveis;
    }

    public boolean isVisualizarOutrasCarteiras() {
        return visualizarOutrasCarteiras;
    }

    public void setVisualizarOutrasCarteiras(boolean visualizarOutrasCarteiras) {
        this.visualizarOutrasCarteiras = visualizarOutrasCarteiras;
    }
}
