package br.com.pacto.controller.json.aluno;

import org.apache.commons.lang3.StringUtils;

/**
 * Created by paulo on 09/07/2019.
 */
public enum SituacaoAlunoEnum {

    ATIVO("AT"),
    INATIVO("IN"),
    VISITANTE("VI"),
    TRANCADO("TR"),
    ATESTADO("AE"),
    CANCELADO("CA"),
    CARENCIA("CR"),
    DESISTENTE("DE"),
    VENCIDO("VE"),
    OUTROS("OU");

    private String codigo;

    SituacaoAlunoEnum(String codigo) {
        this.codigo = codigo;
    }

    public String getCodigo() {
        return codigo;
    }

    public void setCodigo(String codigo) {
        this.codigo = codigo;
    }

    public static SituacaoAlunoEnum getInstance(String codigo) {
        if (!StringUtils.isBlank(codigo)) {
            for (SituacaoAlunoEnum situacaoAluno : SituacaoAlunoEnum.values()) {
                if (codigo.equals(situacaoAluno.getCodigo())) {
                    return situacaoAluno;
                }
            }
        }
        return null;
    }
}
