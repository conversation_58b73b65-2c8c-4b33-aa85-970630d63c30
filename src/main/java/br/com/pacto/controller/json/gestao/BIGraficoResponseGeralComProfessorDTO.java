package br.com.pacto.controller.json.gestao;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 18/10/2019
 */
public class BIGraficoResponseGeralComProfessorDTO {
    private String id;
    private String nome;
    private boolean padrao;
    private ArrayList<String> indicadores;
    private List<BIGraficoResponseProfessorDTO> professores;
    private List<BIGraficoResponseComProfessorDTO> dados;

     public BIGraficoResponseGeralComProfessorDTO( ){

     }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public boolean isPadrao() {
        return padrao;
    }

    public void setPadrao(boolean padrao) {
        this.padrao = padrao;
    }

    public ArrayList<String> getIndicadores() {
        return indicadores;
    }

    public void setIndicadores(ArrayList<String> indicadores) {
        this.indicadores = indicadores;
    }

    public List<BIGraficoResponseProfessorDTO> getProfessores() {
        return professores;
    }

    public void setProfessores(List<BIGraficoResponseProfessorDTO> professores) {
        this.professores = professores;
    }

    public List<BIGraficoResponseComProfessorDTO> getDados() {
        return dados;
    }

    public void setDados(List<BIGraficoResponseComProfessorDTO> dados) {
        this.dados = dados;
    }
}
