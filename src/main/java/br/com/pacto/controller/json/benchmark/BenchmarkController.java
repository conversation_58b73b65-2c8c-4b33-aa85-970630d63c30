package br.com.pacto.controller.json.benchmark;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.security.aspecto.Permissao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.benchmark.BenchmarkService;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by joao moita on 28/09/2018.
 */
@Controller
@RequestMapping("/psec/benchmarks")
public class BenchmarkController {

    private final BenchmarkService benchmarkService;

    @Autowired
    public BenchmarkController(BenchmarkService benchmarkService) {
        Assert.notNull(benchmarkService, "O serviço de benchmark não foi injetado corretamente");
        this.benchmarkService = benchmarkService;
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.BENCHMARK)
    @RequestMapping(method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cadastroBenchmark(@RequestBody BenchmarkTO benchmarkTO) {
        try {
            return ResponseEntityFactory.ok(benchmarkService.cadastroBenchmark(benchmarkTO));
        } catch (ServiceException e) {
            Logger.getLogger(BenchmarkController.class.getName()).log(Level.SEVERE, "Erro ao tentar cadastrar benchmark", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.BENCHMARK)
    @RequestMapping(value = "all", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listaTodosBenchmarks() {
        try {
            return ResponseEntityFactory.ok(benchmarkService.listarTodosBenchmarks());
        } catch (ServiceException e) {
            Logger.getLogger(BenchmarkController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar todos benchmark", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.BENCHMARK)
    @RequestMapping(value = "{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> detalhesBenchmark(@PathVariable("id") Integer integer) {
        try {
            return ResponseEntityFactory.ok(benchmarkService.detalhesBenchmark(integer));
        } catch (ServiceException e) {
            Logger.getLogger(BenchmarkController.class.getName()).log(Level.SEVERE, "Erro ao tentar detalhes benchmark", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.BENCHMARK)
    @RequestMapping(method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listaBenchmark(
            @RequestParam(value = "filters", required = false) JSONObject filtros,PaginadorDTO paginadorDTO)throws JSONException {
        try {
            FiltroBenchmarkJSON  filtroBenchmarkJSON = new FiltroBenchmarkJSON(filtros);

            return ResponseEntityFactory.ok(benchmarkService.listaBenchmark(filtroBenchmarkJSON, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(BenchmarkController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar benchmark", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.BENCHMARK)
    @RequestMapping(value = "{id}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alterarBenchmark(
            @PathVariable(value = "id") Integer id,
            @RequestBody BenchmarkTO benchmarkTO) {
        try {
            return ResponseEntityFactory.ok(benchmarkService.alterarBenchmark(id, benchmarkTO));
        } catch (ServiceException e) {
            Logger.getLogger(BenchmarkController.class.getName()).log(Level.SEVERE, "Erro ao tentar alterar benchmark", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.BENCHMARK)
    @RequestMapping(value = "{id}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> removerBenchmark(
            @PathVariable(value = "id") Integer id) {
        try {
            benchmarkService.removerBenchmark(id);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(BenchmarkController.class.getName()).log(Level.SEVERE, "Erro ao tentar remover benchmark", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }
}
