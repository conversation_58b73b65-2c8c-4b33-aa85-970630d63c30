package br.com.pacto.controller.json.agendamento;

import br.com.pacto.objeto.Uteis;
import br.com.pacto.util.UteisValidacao;
import org.json.JSONObject;

import java.util.Map;

public class ItemListagemBonusDTO {

    private String aula;
    private String horario;
    private String professor;
    private String meta;
    private String ocupacao;
    private String bonus;
    private Double bonusDb = 0.0;
    private long dia;
    private Integer codProf;
    private Integer metaAtingir;


    public ItemListagemBonusDTO(JSONObject json) throws Exception{
        this.aula = json.getString("aula");
        this.horario = json.getString("horario");
        this.professor = Uteis.getNomeAbreviado(json.getString("nomePessoa"));
        this.dia = json.getLong("dia");
        this.codProf = json.getInt("professor");
        this.ocupacao = json.getLong("ocupacao") + "/" + json.getLong("capacidade") ;
        this.bonusDb = json.getDouble("valorBonus");
        this.bonus = "R$ " + Uteis.arrendondarForcando2CadasDecimaisComVirgula(json.getDouble("valorBonus")) ;
        Double taxa = json.optDouble("taxa");
        Double metaEstipulada = json.optDouble("meta");
        this.metaAtingir = metaEstipulada == null ? 0 : metaEstipulada.intValue();
        this.meta = UteisValidacao.emptyNumber(metaEstipulada) ? "nada" : (taxa >= metaEstipulada ? "atingiu" : "falhou");
    }

    public String getAula() {
        return aula;
    }

    public void setAula(String aula) {
        this.aula = aula;
    }

    public String getHorario() {
        return horario;
    }

    public void setHorario(String horario) {
        this.horario = horario;
    }

    public String getProfessor() {
        return professor;
    }

    public void setProfessor(String professor) {
        this.professor = professor;
    }

    public String getMeta() {
        return meta;
    }

    public void setMeta(String meta) {
        this.meta = meta;
    }

    public String getOcupacao() {
        return ocupacao;
    }

    public void setOcupacao(String ocupacao) {
        this.ocupacao = ocupacao;
    }

    public String getBonus() {
        return bonus;
    }

    public void setBonus(String bonus) {
        this.bonus = bonus;
    }

    public long getDia() {
        return dia;
    }

    public void setDia(long dia) {
        this.dia = dia;
    }

    public Integer getCodProf() {
        return codProf;
    }

    public void setCodProf(Integer codProf) {
        this.codProf = codProf;
    }

    public Double getBonusDb() {
        return bonusDb;
    }

    public void setBonusDb(Double bonusDb) {
        this.bonusDb = bonusDb;
    }

    public Integer getMetaAtingir() {
        return metaAtingir;
    }

    public void setMetaAtingir(Integer metaAtingir) {
        this.metaAtingir = metaAtingir;
    }
}
