package br.com.pacto.controller.json.gestao;

import java.io.Serializable;
import java.util.Date;

public class AvaliacaoAcompanhamentoDTO implements Serializable {

    private Integer matricula;
    private String nomeAluno;
    private Date dataHoraInicioApresentar;
    private String comentario;
    private String nota;
    private String professorCarteiraApresentar;

    public Integer getMatricula() {
        return matricula;
    }

    public void setMatricula(Integer matricula) {
        this.matricula = matricula;
    }

    public String getNomeAluno() {
        return nomeAluno;
    }

    public void setNomeAluno(String nomeAluno) {
        this.nomeAluno = nomeAluno;
    }

    public Date getDataHoraInicioApresentar() {
        return dataHoraInicioApresentar;
    }

    public void setDataHoraInicioApresentar(Date dataHoraInicioApresentar) {
        this.dataHoraInicioApresentar = dataHoraInicioApresentar;
    }

    public String getComentario() {
        return comentario;
    }

    public void setComentario(String comentario) {
        this.comentario = comentario;
    }

    public String getNota() {
        return nota;
    }

    public void setNota(String nota) {
        this.nota = nota;
    }

    public String getProfessorCarteiraApresentar() {
        return professorCarteiraApresentar;
    }

    public void setProfessorCarteiraApresentar(String professorCarteiraApresentar) {
        this.professorCarteiraApresentar = professorCarteiraApresentar;
    }
}