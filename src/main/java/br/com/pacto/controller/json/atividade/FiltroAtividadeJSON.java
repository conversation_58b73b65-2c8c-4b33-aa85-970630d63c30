package br.com.pacto.controller.json.atividade;

import br.com.pacto.bean.atividade.TipoAtividadeEnum;
import br.com.pacto.controller.json.base.SuperJSON;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

public class FiltroAtividadeJSON extends SuperJSON {

    private String parametro;
    private Boolean nome = false;
    private String situacaoList;
    private String ativo;
    private List<TipoAtividadeEnum> tipo;
    private List<Integer> grupoMuscularesIds;
    private List<Integer> aparelhosIds;
    private List<Integer> categoriasIds;
    private List<Integer> niveisIds;
    private List<Integer> empresasIds;
    private List<Integer> avoid;
    private List<String> treinoia;
    private String ordenacao;
    private List<String> atividades;

    public List<String> getTreinoia() {
        if (treinoia == null) {
            treinoia = new ArrayList<>();
        }
        return treinoia;
    }

    public List<Integer> getAvoid() {
        if (avoid == null) {
            avoid = new ArrayList<>();
        }
        return avoid;
    }

    public void setAvoid(List<Integer> avoid) {
        this.avoid = avoid;
    }

    public void setTreinoia(List<String> treinoia) {
        this.treinoia = treinoia;
    }

    public List<Integer> getEmpresasIds() {
        if (empresasIds == null) {
            empresasIds = new ArrayList<>();
        }
        return empresasIds;
    }

    public void setEmpresasIds(List<Integer> empresasIds) {
        this.empresasIds = empresasIds;
    }

    public FiltroAtividadeJSON(JSONObject filters) throws JSONException {

        if (filters != null) {

            this.parametro = filters.optString("quicksearchValue");

            JSONArray colunasVisiveis = filters.optJSONArray("quicksearchFields");

            if (colunasVisiveis != null) {
                for (int i = 0; i < colunasVisiveis.length(); i++) {
                    if (colunasVisiveis.get(i).equals("nome")) {
                        this.nome = true;
                    }
                }
            }

            JSONArray situacao = filters.optJSONArray("situacaoAtividade");
            if (situacao != null) {
                if( situacao.length() >1){
                    setAtivo(null);
                }else{
                    setAtivo(situacao.getString(0));
                }
            }

            JSONArray tipos = filters.optJSONArray("tiposEnuns");
            this.tipo = new ArrayList<>();
            if (tipos != null) {
                for (int i = 0; i < tipos.length(); i++) {
                    getTipo().add(TipoAtividadeEnum.valueOf(tipos.get(i).toString()));
                }
            }

            JSONArray grupoMusculares = filters.optJSONArray("grupoMuscularesIds");
            if (grupoMusculares != null) {
                for (int i = 0; i < grupoMusculares.length(); i++) {
                    getGrupoMuscularesIds().add(grupoMusculares.getInt(i));
                }
            }

            JSONArray aparelhos = filters.optJSONArray("aparelhos");
            if(aparelhos != null) {
                for(int i = 0; i < aparelhos.length(); i++) {
                    getAparelhosIds().add(aparelhos.getInt(i));
                }
            }

            JSONArray categorias = filters.optJSONArray("categorias");
            if(categorias != null) {
                for(int i = 0; i < categorias.length(); i++) {
                    getCategoriasIds().add(categorias.getInt(i));
                }
            }

            JSONArray niveis = filters.optJSONArray("niveis");
            if(niveis != null) {
                for(int i = 0; i < niveis.length(); i++) {
                    getNiveisIds().add(niveis.getInt(i));
                }
            }

            JSONArray empresas = filters.optJSONArray("empresas");

            if(empresas != null) {
                for(int i = 0; i < empresas.length(); i++) {
                    Integer t = empresas.getInt(i);
                    getEmpresasIds().add(empresas.getInt(i));
                }
            }

            JSONArray treinoia = filters.optJSONArray("treinoia");

            if(treinoia != null) {
                for(int i = 0; i < treinoia.length(); i++) {
                    getTreinoia().add(treinoia.getString(i));
                }
            }

            JSONArray avoid = filters.optJSONArray("avoid");

            if(avoid != null) {
                for(int i = 0; i < avoid.length(); i++) {
                    getAvoid().add(avoid.getInt(i));
                }
            }
            if (filters.has("ordenacao")) {
                setOrdenacao(filters.get("ordenacao").toString());
            }

            JSONArray atv = filters.optJSONArray("atividades");
            this.atividades = new ArrayList<>();
            if (atv != null) {
                for (int i = 0; i < atv.length(); i++) {
                    getAtividades().add(atv.get(i).toString());
                }
            }
        }
    }

    public String getParametro() {
        return parametro;
    }

    public void setParametro(String parametro) {
        this.parametro = parametro;
    }

    public Boolean getNome() {
        return nome;
    }

    public void setNome(Boolean nome) {
        this.nome = nome;
    }

    public String getSituacaoList() {
        return situacaoList;
    }

    public void setSituacaoList(String situacaoList) {
        this.situacaoList = situacaoList;
    }

    public String getAtivo() {
        return ativo;
    }

    public void setAtivo(String ativo) {
        this.ativo = ativo;
    }

    public List<TipoAtividadeEnum> getTipo() {
        return tipo;
    }

    public void setTipo(List<TipoAtividadeEnum> tipo) {
        this.tipo = tipo;
    }

    public List<Integer> getGrupoMuscularesIds() {
        if (grupoMuscularesIds == null) {
            grupoMuscularesIds = new ArrayList<>();
        }
        return grupoMuscularesIds;
    }

    public void setGrupoMuscularesIds(List<Integer> grupoMuscularesIds) {
        this.grupoMuscularesIds = grupoMuscularesIds;
    }

    public List<Integer> getAparelhosIds() {
        if(aparelhosIds == null) {
            aparelhosIds = new ArrayList<>();
        }
        return aparelhosIds;
    }

    public void setAparelhosIds(List<Integer> aparelhosIds) {
        this.aparelhosIds = aparelhosIds;
    }

    public List<Integer> getCategoriasIds() {
        if(categoriasIds == null) {
            categoriasIds = new ArrayList<>();
        }
        return categoriasIds;
    }

    public void setCategoriasIds(List<Integer> categoriasIds) {
        this.categoriasIds = categoriasIds;
    }

    public List<Integer> getNiveisIds() {
        if(niveisIds == null) {
            niveisIds = new ArrayList<>();
        }
        return niveisIds;
    }

    public void setNiveisIds(List<Integer> niveisIds) {
        this.niveisIds = niveisIds;
    }

    public String getOrdenacao() {
        return ordenacao;
    }

    public void setOrdenacao(String ordenacao) {
        this.ordenacao = ordenacao;
    }

    public List<String> getAtividades() {
        return atividades;
    }

    public void setAtividades(List<String> atividades) {
        this.atividades = atividades;
    }
}
