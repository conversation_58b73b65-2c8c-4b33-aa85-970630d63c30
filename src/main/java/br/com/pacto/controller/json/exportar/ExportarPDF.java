package br.com.pacto.controller.json.exportar;

import br.com.pacto.objeto.Calendario;
import com.itextpdf.text.*;
import com.itextpdf.text.pdf.*;
import org.json.JSONException;
import org.olap4j.impl.ArrayMap;



import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.FileOutputStream;
import java.text.DateFormat;
import java.util.*;
import java.util.List;

public class ExportarPDF {

    private static Font catFont = new Font(Font.FontFamily.TIMES_ROMAN, 18,
            Font.BOLD);
    private static Font redFont = new Font(Font.FontFamily.TIMES_ROMAN, 12,
            Font.NORMAL, BaseColor.RED);
    private static Font subFont = new Font(Font.FontFamily.TIMES_ROMAN, 16,
            Font.BOLD);
    private static Font smallBold = new Font(Font.FontFamily.TIMES_ROMAN, 12,
            Font.BOLD);

    public String visualizarRelatorioPDF(final String key,
                                         List<LinkedHashMap<String, Object>> lista,
                                         HttpServletRequest request,
                                         String titulo,
                                         String nomeRelatorio,
                                         String urlTreino) throws Exception {
        String nomePDF = nomeRelatorio
                + "-" + key
                + "-" + Calendario.hoje().getTime()
                + ".pdf";
        String nomeRelPDF = "temp";
        File pdfFolder = new File(obterCaminhoWebAplicacao(request) + File.separator + nomeRelPDF);
        pdfFolder.mkdirs();
        nomeRelPDF = nomeRelPDF + File.separator + nomePDF;
        File pdfFile = new File(obterCaminhoWebAplicacao(request) + File.separator + nomeRelPDF);
        if (pdfFile.exists()) {
            try {
                pdfFile.delete();
            } catch (Exception e) {
                DateFormat formatador = DateFormat.getDateInstance(DateFormat.MEDIUM);
                String dataStr = formatador.format(Calendario.hoje());
                nomePDF = nomePDF + dataStr + ".pdf";
                nomeRelPDF = "relatorios" + File.separator + nomePDF;
                pdfFile = new File(obterCaminhoWebAplicacao(request) + File.separator + nomeRelPDF);
            }

        }

        Document document = new Document();
        PdfWriter writer = PdfWriter.getInstance(document, new FileOutputStream(obterCaminhoWebAplicacao(request) + File.separator + nomeRelPDF));
        HeaderFooterPageEvent event = new HeaderFooterPageEvent();
        writer.setPageEvent(event);
        document.open();
        addMetaData(document);
        addPage(document, titulo, lista);
        document.close();
        System.out.println("EXPORTANDO PDF: " + pdfFile.getAbsolutePath());
        return urlTreino.replace("prest/","") + nomeRelPDF.replaceAll("\\\\", "/");
    }

    private static void addMetaData(Document document) {
        document.addTitle("Relatório em pdf");
        document.addSubject("Pacto soluções");
        document.addKeywords("Java, PDF, iText, Pacto, soluções, solucoes, academia, fitness");
        document.addAuthor("Pacto soluções");
        document.addCreator("Pacto soluções");
    }

    private static void addPage(Document document, String titulo, List<LinkedHashMap<String, Object>> lista)
            throws DocumentException, JSONException {
        Paragraph preface = new Paragraph();
        addEmptyLine(preface);
        Paragraph tituloDoc = new Paragraph(titulo, catFont);
        tituloDoc.setAlignment(Element.ALIGN_CENTER);
        preface.add(tituloDoc);
        addEmptyLine(preface);
        createTable(preface, lista);
        document.add(preface);
    }

    private static void createTable(Paragraph subCatPart, List<LinkedHashMap<String, Object>> lista) throws JSONException {
        int colunas = lista.get(0).size();
        PdfPTable table = new PdfPTable(colunas);
        table.setHeaderRows(1);
        addInfoTabela(lista, table, colunas);
        subCatPart.add(table);
    }

    private static void addInfoTabela(List<LinkedHashMap<String, Object>> lista, PdfPTable table, int colunas) {

        boolean criarNomeColuna = true;
        for ( Map<String,Object> informacoes : lista){
            PdfPCell c1;
            if (criarNomeColuna) {
                Map<String, Object> inf =  new ArrayMap<>();
                inf.putAll(informacoes);
                inf.put("empty",new Object());
                for (int i = 0; i < colunas; i++) {
                    Map.Entry<String, Object> top = inf.entrySet().iterator().next();
                    c1 = new PdfPCell(new Phrase(trocarTitulo(top.getKey())));
                    c1.setHorizontalAlignment(Element.ALIGN_CENTER);
                    table.addCell(c1);
                    inf.remove(top.getKey());
                }
            }
            criarNomeColuna = false;
            for(int i=0; i < colunas; i++) {
                Map.Entry<String,Object> entry = informacoes.entrySet().iterator().next();
                if (null == entry.getValue()){
                    entry.setValue("");
                }
                table.addCell(entry.getValue().toString());
                informacoes.remove(entry.getKey());
            }
        }
        for(int i=0; i < colunas; i++) {
            if (i == 0){
                table.addCell("TOTAL");
            } else if (i == (colunas - 1)){
                table.addCell(String.valueOf(lista.size()));
            }else {
                table.addCell("");
            }
        }
    }

    private static String trocarTitulo(String label) {
        switch (label) {
            case "matricula":
                return "Matrícula";
            case "nomeProfessor":
                return "Nome do Professor";
            case "nomeAbreviado":
            case "nomeAluno":
                return "Nome do Aluno";
            case "dataPrograma":
                return "Venc. Programa";
            case "dataUltimoacesso":
                return "Último Acesso";
            case "dataVigenciaAteAjustadaApresentar":
                return "Data Venciemnto";
            case "evento":
                return "Evento";
            case "inicio":
                return "Data de início";
            case "dataLancamento":
                return "Data de Lançamento";
            case "codigo":
                return "Código";
            case "nome":
                return "Nome";
        }
        return label;
    }


    private static void addEmptyLine(Paragraph paragraph) {
        for (int i = 0; i < 1; i++) {
            paragraph.add(new Paragraph(" "));
        }
    }
    public String obterCaminhoWebAplicacao(HttpServletRequest request) throws Exception {
        return new File(request.getSession().getServletContext().getRealPath("")).getAbsolutePath();
    }
}
