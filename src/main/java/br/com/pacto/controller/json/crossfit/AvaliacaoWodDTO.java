package br.com.pacto.controller.json.crossfit;


import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class AvaliacaoWodDTO {
    private Integer wod;
    private Integer usuario;
    private Integer empresa;
    private String comentario;
    private Integer nota;
    private Integer percepcaoEsforco;

    public Integer getWod() {

        return wod;
    }

    public void setWod(Integer wod) {
        this.wod = wod;
    }

    public Integer getUsuario() {
        return usuario;
    }

    public void setUsuario(Integer usuario) {
        this.usuario = usuario;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public String getComentario() {
        return comentario;
    }

    public void setComentario(String comentario) {
        this.comentario = comentario;
    }

    public Integer getNota() {
        return nota;
    }

    public void setNota(Integer nota) {
        this.nota = nota;
    }

    public Integer getPercepcaoEsforco() {
        return percepcaoEsforco;
    }

    public void setPercepcaoEsforco(Integer percepcaoEsforco) {
        this.percepcaoEsforco = percepcaoEsforco;
    }
}
