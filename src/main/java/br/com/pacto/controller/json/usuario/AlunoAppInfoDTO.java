package br.com.pacto.controller.json.usuario;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.Date;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class AlunoAppInfoDTO {

    private boolean usaApp = false;
    private Date dataRegistroUsoApp;
    private String idClienteApp;

    public AlunoAppInfoDTO() {
        this.usaApp = false;
    }

    public boolean isUsaApp() {
        return usaApp;
    }

    public void setUsaApp(boolean usaApp) {
        this.usaApp = usaApp;
    }

    public Date getDataRegistroUsoApp() {
        return dataRegistroUsoApp;
    }

    public void setDataRegistroUsoApp(Date dataRegistroUsoApp) {
        this.dataRegistroUsoApp = dataRegistroUsoApp;
    }

    public String getIdClienteApp() {
        return idClienteApp;
    }

    public void setIdClienteApp(String idClienteApp) {
        this.idClienteApp = idClienteApp;
    }
}
