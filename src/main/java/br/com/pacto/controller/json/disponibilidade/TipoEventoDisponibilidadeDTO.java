package br.com.pacto.controller.json.disponibilidade;

import br.com.pacto.controller.json.tipoEvento.TipoEventoDTO;

import java.io.Serializable;

public class TipoEventoDisponibilidadeDTO implements Serializable {

    private Integer codigo;
    private TipoEventoDTO tipoEvento;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public TipoEventoDTO getTipoEvento() {
        return tipoEvento;
    }

    public void setTipoEvento(TipoEventoDTO tipoEvento) {
        this.tipoEvento = tipoEvento;
    }
}
