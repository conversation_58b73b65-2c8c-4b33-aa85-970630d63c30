package br.com.pacto.controller.json.gestao;

import br.com.pacto.bean.bi.AcessosExecucoesBI;

public class ExecucoesTreinoDTO {
    private Integer execucoesTreino;
    private Integer alunosDoTreino;
    private Integer smartphone;
    private Integer acessos;

    public ExecucoesTreinoDTO() {

    }
    public ExecucoesTreinoDTO(AcessosExecucoesBI acessos) {
        this.execucoesTreino = acessos.getExecucoes();
        this.alunosDoTreino = acessos.getAcessotreino();
        this.smartphone = acessos.getSmartphone();
        this.acessos = acessos.getAcessos();
    }

    public Integer getExecucoesTreino() {
        return execucoesTreino;
    }

    public void setExecucoesTreino(Integer execucoesTreino) {
        this.execucoesTreino = execucoesTreino;
    }

    public Integer getAlunosDoTreino() {
        return alunosDoTreino;
    }

    public void setAlunosDoTreino(Integer alunosDoTreino) {
        this.alunosDoTreino = alunosDoTreino;
    }

    public Integer getSmartphone() {
        return smartphone;
    }

    public void setSmartphone(Integer smartphone) {
        this.smartphone = smartphone;
    }

    public Integer getAcessos() {
        return acessos;
    }

    public void setAcessos(Integer acessos) {
        this.acessos = acessos;
    }
}
