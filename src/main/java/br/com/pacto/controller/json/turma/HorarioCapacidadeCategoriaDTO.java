package br.com.pacto.controller.json.turma;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class HorarioCapacidadeCategoriaDTO {

    private Integer codigo;
    private Integer horarioTurma;
    private String tipoCategoria;
    private Integer capacidade;

    public HorarioCapacidadeCategoriaDTO() {
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getHorarioTurma() {
        return horarioTurma;
    }

    public void setHorarioTurma(Integer horarioTurma) {
        this.horarioTurma = horarioTurma;
    }

    public Integer getCapacidade() {
        return capacidade;
    }

    public void setCapacidade(Integer capacidade) {
        this.capacidade = capacidade;
    }

    public String getTipoCategoria() {
        return tipoCategoria;
    }

    public void setTipoCategoria(String tipoCategoria) {
        this.tipoCategoria = tipoCategoria;
    }
}
