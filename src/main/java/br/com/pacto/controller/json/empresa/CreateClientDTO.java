package br.com.pacto.controller.json.empresa;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * Created by paulo on 12/11/2019
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CreateClientDTO {

    private String nome;
    private String email;
    private String celular;
    private String senha;

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getCelular() {
        return celular;
    }

    public void setCelular(String celular) {
        this.celular = celular;
    }

    public String getSenha() {
        return senha;
    }

    public void setSenha(String senha) {
        this.senha = senha;
    }

}
