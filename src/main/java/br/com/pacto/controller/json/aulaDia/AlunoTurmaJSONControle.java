/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.controller.json.aulaDia;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.aula.Modalidade;
import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.configuracoes.ConfiguracaoSistema;
import br.com.pacto.bean.configuracoes.ConfiguracoesEnum;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.bean.programa.ObjetivoAlunoDTO;
import br.com.pacto.bean.programa.ObjetivoIntermediarioAlunoDTO;
import br.com.pacto.controller.json.agendamento.AgendaController;
import br.com.pacto.controller.json.agendamento.AgendamentoPersonalDTO;
import br.com.pacto.controller.json.agendamento.FiltrosAgendamentosDTO;
import br.com.pacto.controller.json.agendamento.PeriodoFiltrarEnum;
import br.com.pacto.controller.json.avaliacao.AvaliacaoJSONControle;
import br.com.pacto.controller.json.avaliacao.SugestaoHorarioPersonalJSON;
import br.com.pacto.controller.json.base.SuperControle;
import br.com.pacto.controller.json.tipoEvento.TipoAgendamentoDTO;
import br.com.pacto.controller.json.turma.TurmaVideoDTO;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.JSONMapper;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.security.aspecto.Permissao;
import br.com.pacto.service.exception.HorarioConcomitanteException;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.agenda.AgendamentoService;
import br.com.pacto.service.intf.agenda.DisponibilidadeService;
import br.com.pacto.service.intf.agendatotal.AgendaTotalService;
import br.com.pacto.service.intf.avaliacao.AvaliacaoFisicaService;
import br.com.pacto.service.intf.cliente.ClienteSinteticoService;
import br.com.pacto.service.intf.configuracoes.ConfiguracaoSistemaService;
import br.com.pacto.service.intf.empresa.EmpresaService;
import br.com.pacto.service.intf.tipoEvento.TipoEventoService;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;
import br.com.pacto.util.bean.AgendaTotalTO;
import br.com.pacto.util.enumeradores.OrigemSistemaEnum;
import br.com.pacto.util.impl.Ordenacao;
import br.com.pacto.util.json.AgendaTotalJSON;
import br.com.pacto.util.json.ControleCreditoTreinoJSON;
import org.json.JSONArray;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import servicos.integracao.zw.IntegracaoTurmasWSConsumer;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("/alunoTurma")
public class AlunoTurmaJSONControle extends SuperControle {

    @Autowired
    private AgendaTotalService agendaService;
    @Autowired
    private AvaliacaoFisicaService avaliacaoService;
    @Autowired
    private AgendamentoService agendamentoService;
    @Autowired
    private ClienteSinteticoService clienteService;
    @Autowired
    private ConfiguracaoSistemaService configuracaoSistemaService;
    @Autowired
    private EmpresaService empService;
    @Autowired
    private TipoEventoService tipoEventoService;
    @Autowired
    private DisponibilidadeService disponibilidadeService;

    @RequestMapping(value = "{ctx}/consultarAulas", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap consultarAulas(@PathVariable String ctx,
                            @RequestParam final Integer matricula,
                            @RequestParam(required = false) final String contrato,
                            @RequestParam final String aulasFuturas) {
        ModelMap mm = new ModelMap();
        try {
            Integer contratoInt = null;
            try {
                contratoInt = Integer.valueOf(contrato);
            }catch (Exception e){
                contratoInt = null;
            }
            empService.validarChave(ctx);
            String retorno = agendaService.obterProximasAulasVerificandoModoConsulta(ctx, matricula,Boolean.parseBoolean(aulasFuturas));
            List<Modalidade> listaModalidades = agendaService.todasModalidades(ctx);

            if(retorno.startsWith("ERRO:")){
                mm.addAttribute("aulas", new JSONArray().toString());
            } else {
                List<AgendaTotalJSON> proximasAulas = JSONMapper.getList(new JSONArray(retorno), AgendaTotalJSON.class);

                if (proximasAulas == null) {
                    proximasAulas = new ArrayList<AgendaTotalJSON>();
                }
                List<AgendaTotalTO> listaTO = new ArrayList<AgendaTotalTO>();
                Integer empresa = !UteisValidacao.emptyList(proximasAulas) ? proximasAulas.get(0).getEmpresa() : null;
                Date inicio = Calendario.hoje();
                Date fim = Uteis.somarDias(inicio, 30);
                if (empresa != null) {
                    for (AgendaTotalJSON a : proximasAulas) {
                        AgendaTotalTO agenda = new AgendaTotalTO(a);
                        listaTO.add(agenda);
                    }
                    agendaService.montarMapaAgendados(ctx, inicio, fim, empresa, listaTO, true);
                }
                List<AulaDiaJSON> jsonArray = new ArrayList<AulaDiaJSON>();
                List<Integer> modalidadeFiltrar = new ArrayList<>();
                if(!UteisValidacao.emptyNumber(contratoInt)){
                    modalidadeFiltrar = agendaService.modalidadesContrato(ctx, contratoInt, matricula);
                }
                for(AgendaTotalJSON aula : proximasAulas) {
                    if(!UteisValidacao.emptyNumber(contratoInt) && !modalidadeFiltrar.contains(aula.getCodigoTipo())){
                        continue;
                    }
                    if(!aula.getAulaCheia()){
                        aula.setNrVagasPreenchidas(aula.getOcupacao());
                        AulaDiaJSON aulaDiaJSON = new AulaDiaJSON(aula);
                        for (Modalidade modalidade : listaModalidades) {
                            if (modalidade.getCodigoZW().equals(aula.getCodigoTipo())) {
                                aulaDiaJSON.setCorModalidade(modalidade.getCor().getCor());
                                break;
                            }
                        }

                        List<TurmaVideoDTO> listaLinkVideosSalvos =  agendaService.obterListaTurmaVideo(ctx,aula.getCodigoTurma());
                        aulaDiaJSON.setLinkVideos(listaLinkVideosSalvos);

                        jsonArray.add(aulaDiaJSON);
                    }
                }
                jsonArray = Ordenacao.ordenarLista(jsonArray, "diaDate");
                mm.addAttribute("aulas", jsonArray);
            }
        } catch (Exception ex) {
            mm.addAttribute("aulas", new JSONArray().toString());
            Logger.getLogger(AulaDiaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/consultarAulasDesmarcadas", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap consultarAulasDesmarcadas(@PathVariable String ctx,
                                       @RequestParam final Integer matricula) {
        ModelMap mm = new ModelMap();
        try {
            List<AgendaTotalJSON> proximasAulas = agendaService.consultarAulasDesmarcadas(ctx, matricula, null);
            if (proximasAulas == null) {
                proximasAulas = new ArrayList<AgendaTotalJSON>();
            }
            List<AulaDiaJSON> jsonArray = new ArrayList<AulaDiaJSON>();
            for(AgendaTotalJSON aula : proximasAulas) {
                jsonArray.add(new AulaDiaJSON(aula));
            }
            jsonArray = Ordenacao.ordenarLista(jsonArray, "inicio");
            mm.addAttribute("aulas", jsonArray);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage().replaceFirst("ERRO:", ""));
            Logger.getLogger(AulaDiaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/desmarcarAula", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap desmarcarAula(@PathVariable String ctx,
                           @RequestParam final Integer matricula,
                           @RequestParam final Integer codigoHorarioTurma,
                           @RequestParam final String data,
                           @RequestParam(required = false) final Integer contrato) {
        ModelMap mm = new ModelMap();
        try {
            String result = agendaService.marcarDesmarcar(ctx, matricula,
                    Uteis.getDate(data, "dd/MM/yyyy"), codigoHorarioTurma, false, false,
                    false, contrato);
            if(result.startsWith("ERRO:")){
                throw new Exception(result);
            }
            mm.addAttribute(STATUS_SUCESSO, result);
            ConfiguracaoSistema cfTipo = configuracaoSistemaService.consultarPorTipo(ctx, ConfiguracoesEnum.ALUNO_MARCAR_PROPRIA_AULA);
            if (cfTipo.getValorAsBoolean()) {
                AgendaTotalService agendaTotalService = (AgendaTotalService) UtilContext.getBean(AgendaTotalService.class);
                agendaTotalService.presencaAlunoAula(ctx, matricula, codigoHorarioTurma, data, null, OrigemSistemaEnum.APP_TREINO, false);
            }
        } catch (Exception ex) {
            Logger.getLogger(AulaDiaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
            mm.addAttribute(STATUS_ERRO, ex.getMessage().replaceFirst("ERRO:", ""));
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/consultarAulasModalidadeAluno", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap consultarAulasModalidadeAluno(@PathVariable String ctx,
                                           @RequestParam final String inicio,
                                           @RequestParam final String fim,
                                           @RequestParam final Integer matricula) {
        ModelMap mm = new ModelMap();
        try {
            ClienteSintetico cliente = clienteService.consultarPorMatricula(ctx,
                    matricula.toString());
            if (cliente != null) {
                List<AulaDiaJSON> proximasAulas = agendaService.consultarAgendamentosModalidadesAluno(ctx, Uteis.getDate(inicio, "dd/MM/yyyy"),
                        Uteis.getDate(fim, "dd/MM/yyyy"), matricula, cliente.getEmpresa());
                proximasAulas = Ordenacao.ordenarLista(proximasAulas, "diaDate");
                mm.addAttribute("aulas", proximasAulas);
            } else {
                throw new ServiceException(String.format("Aluno não encontrado na empresa %s com a matrícula: %s", ctx, matricula));
            }
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage().replaceFirst("ERRO:", ""));
            Logger.getLogger(AulaDiaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/consultarTurmasAluno", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap consultarTurmasAluno(@PathVariable String ctx,
                                  @RequestParam final Integer matricula) {
        ModelMap mm = new ModelMap();
        try {
            List<AgendaTotalJSON> proximasAulas = agendaService.consultarTurmasAluno(ctx, matricula);
            if (proximasAulas == null) {
                proximasAulas = new ArrayList<AgendaTotalJSON>();
            }
            List<AulaDiaJSON> jsonArray = new ArrayList<AulaDiaJSON>();
            for(AgendaTotalJSON aula : proximasAulas) {
                jsonArray.add(new AulaDiaJSON(aula));
            }
            mm.addAttribute("aulas", jsonArray);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage().replaceFirst("ERRO:", ""));
            Logger.getLogger(AulaDiaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/alunosTurma", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap alunosTurma(@PathVariable String ctx,
                         @RequestParam(required = false) final Integer empresa,
                         @RequestParam final Integer codigoHorarioTurma,
                         @RequestParam final String data, HttpServletRequest request) {
        ModelMap mm = new ModelMap();
        try {
            if (empresa == null) {
                throw new Exception("Não foi possível consultar os alunos da sua Turma. Código da empresa não informado. Por favor, atualize o APP!");
            }
            List<Map<String, String>> alunosTurma = agendaService.fotosAlunosTurma(
                    ctx, empresa, codigoHorarioTurma, data, request);
            mm.addAttribute("alunos", alunosTurma);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(AulaDiaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/marcarAula", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap marcarAula(@PathVariable String ctx,
                        @RequestParam final Integer matricula,
                        @RequestParam final Integer codigoHorarioTurma,
                        @RequestParam final String data,
                        @RequestParam(required = false) final Integer contrato) {
        ModelMap mm = new ModelMap();
        try {
            ConfiguracaoSistema cfInacimplente = configuracaoSistemaService.consultarPorTipo(ctx,
                    ConfiguracoesEnum.PROIBIR_MARCAR_AULA_PARCELA_VENCIDA);
            ConfiguracaoSistema cfgProibirMarcarAulaAntesPagamentoPrimeiraParc = configuracaoSistemaService.consultarPorTipo(ctx, ConfiguracoesEnum.PROIBIR_MARCAR_AULA_ANTES_PAGAMENTO_PRIMEIRA_PARCELA);
            String result = agendaService.marcarDesmarcar(ctx, matricula, Uteis.getDate(data, "dd/MM/yyyy"),
                    codigoHorarioTurma, true, cfInacimplente.getValorAsBoolean(),
                    cfgProibirMarcarAulaAntesPagamentoPrimeiraParc.getValorAsBoolean(), contrato);
            if(result.startsWith("ERRO")){
                mm.addAttribute(STATUS_ERRO, result.replaceFirst("ERRO:", ""));
            }else{
                mm.addAttribute(STATUS_SUCESSO, result);
            }
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage().replaceFirst("ERRO:", ""));
            Logger.getLogger(AulaDiaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/extrato", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap extrato(@PathVariable String ctx,
                     @RequestParam final Integer matricula,
                     @RequestParam final String data) {
        ModelMap mm = new ModelMap();
        try {
            List<ControleCreditoTreinoJSON> extrato = agendaService.consultarExtratoCreditos(ctx, matricula, Uteis.getDate(data, "dd/MM/yyyy HH:mm:ss"));
            if (extrato == null || extrato.isEmpty()) {
                extrato = new ArrayList<ControleCreditoTreinoJSON>();
            }
            mm.addAttribute(RETURN, extrato);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage().replaceFirst("ERRO:", ""));
            Logger.getLogger(AulaDiaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }


    @RequestMapping(value = "{ctx}/saldoAluno", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap saldoAluno(@PathVariable String ctx,
                        @RequestParam final Integer matricula,
                        @RequestParam(required = false) final Integer contrato) {
        ModelMap mm = new ModelMap();
        try {
            String result = agendaService.consultarSaldoAlunoVerificandoModoConsulta(ctx, matricula, contrato);
            if(result.startsWith("ERRO:")){
                mm.addAttribute(STATUS_ERRO, result.replaceAll("ERRO:", ""));
            }else{
                mm.addAttribute(STATUS_SUCESSO, result);
            }
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(AulaDiaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }


    @RequestMapping(value = "{ctx}/saldoAlunoReporEMarcar", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap saldoAlunoRepor(@PathVariable String ctx,
                             @RequestParam final Integer matricula,
                             @RequestParam(required = false) final Integer contrato) {
        ModelMap mm = new ModelMap();
        try {
            String result = agendaService.consultarSaldoAlunoReporEMarcar(ctx, matricula, contrato != null ? contrato : 0);
            if(result.startsWith("ERRO:")){
                mm.addAttribute(STATUS_ERRO, result.replaceAll("ERRO:", ""));
            }else{
                mm.addAttribute("saldoTurma(reposiçoes)", result.split(";")[0]);
                mm.addAttribute("creditos", result.split(";")[2]);
            }
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(AulaDiaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }


    @RequestMapping(value = "{ctx}/aulasConfirmadas", method = RequestMethod.GET)
    public @ResponseBody
    ModelMap aulasConfirmadasPorAluno(@PathVariable String ctx,
                                      @RequestParam final Integer matricula,
                                      @RequestParam(required = false) final String dataInicial,
                                      @RequestParam(required = false) final String dataFinal,
                                      PaginadorDTO paginadorDTO) {
        ModelMap mm = new ModelMap();
        try {

            mm.addAttribute(STATUS_SUCESSO, agendaService.consultarAulasConfirmadas(ctx, matricula, dataInicial, dataFinal, paginadorDTO));
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(AulaDiaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/marcarEuQueroHorario", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap marcarEuQueroHorario(@PathVariable String ctx,
                                  @RequestParam final Integer codigoAluno,
                                  @RequestParam final Integer codigoHorarioTurma,
                                  @RequestParam final String data) {
        ModelMap mm = new ModelMap();
        try {
            ClienteSintetico clienteSintetico = clienteService.obterPorCodigo(ctx, codigoAluno);

            if (clienteSintetico == null)
                throw new Exception("Não existe cliente(TreinoWeb) com código=" + codigoAluno);
            IntegracaoTurmasWSConsumer integracaoWS = UtilContext.getBean(IntegracaoTurmasWSConsumer.class);
            String result = integracaoWS.marcarEuQueroHorario( ctx, clienteSintetico.getCodigoCliente(), codigoHorarioTurma, data);
            registrarLogSolicitacao(ctx, codigoAluno, codigoHorarioTurma, data, "MARCOU");
            regsitrarStatusSolicitacao(mm, result);
        } catch (Exception ex) {
            registraLogErro(mm, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/desmarcarEuQueroHorario", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap desmarcarEuQueroHorario(@PathVariable String ctx,
                                     @RequestParam final Integer codigoAluno,
                                     @RequestParam final Integer codigoHorarioTurma,
                                     @RequestParam final String data) {
        ModelMap mm = new ModelMap();
        try {
            ClienteSintetico clienteSintetico = clienteService.obterPorCodigo(ctx, codigoAluno);

            if (clienteSintetico == null)
                throw new Exception("Não existe cliente(TreinoWeb) com código=" + codigoAluno);
            IntegracaoTurmasWSConsumer integracaoWS = UtilContext.getBean(IntegracaoTurmasWSConsumer.class);
            String result = integracaoWS.desmarcarEuQueroHorario( ctx, clienteSintetico.getCodigoCliente(), codigoHorarioTurma, data);
            registrarLogSolicitacao(ctx, codigoAluno, codigoHorarioTurma, data, "DESMARCOU");
            registrarStatusSolicitacao(mm, result);
        } catch (Exception ex) {
            registraLogErro(mm, ex);
        }
        return mm;
    }

    private void registrarStatusSolicitacao(ModelMap mm, String result) {
        if (result.contains("ERRO:"))
            mm.addAttribute(STATUS_ERRO, result);
        else
            mm.addAttribute(STATUS_SUCESSO, "Solicitação enviada.");
    }

    private void registraLogErro(ModelMap mm, Exception ex) {
        mm.addAttribute(STATUS_ERRO, ex.getMessage());
        Logger.getLogger(AlunoTurmaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
    }

    private void registrarLogSolicitacao(@PathVariable String ctx, @RequestParam Integer codigoAluno,
                                         @RequestParam Integer codigoHorarioTurma, @RequestParam String data, String opcao) {
        StringBuilder msg = new StringBuilder();
        msg.append(opcao);
        msg.append(" EU QUERO HORARIO: ctx").append("key:").append(ctx).append(" - códigoAluno:").append(codigoAluno);
        msg.append(" - codigoHorarioTurma:").append(codigoHorarioTurma).append(" - data:").append(data);
        Logger.getLogger(AlunoTurmaJSONControle.class.getName()).log(Level.SEVERE, msg.toString(), "");
    }

    private void regsitrarStatusSolicitacao(ModelMap mm, String result) {
        if (result.contains("ERRO:"))
            mm.addAttribute(STATUS_ERRO, result);
        else
            mm.addAttribute(STATUS_SUCESSO, "Solicitação enviada.");
    }

    @RequestMapping(value = "{ctx}/app/consultarTurmasDisponiveis", method = {RequestMethod.GET, RequestMethod.POST})
    public @ResponseBody
    ModelMap consultarTurmasDisponiveisApp(@PathVariable String ctx,
                                           @RequestParam final String inicio,
                                           @RequestParam final String fim,
                                           @RequestParam(required = false) final String contrato,
                                           @RequestParam final Integer matricula,
                                           @RequestParam (required = false) Integer empresa) {
        ModelMap mm = new ModelMap();
        try {
            Integer contratoInt;
            try {
                contratoInt = Integer.valueOf(contrato);
            }catch (Exception e){
                contratoInt = null;
            }
            ClienteSintetico cliente = clienteService.consultarPorMatricula(ctx,
                    matricula.toString());
            if (cliente != null) {
                empresa = empresa != null ? empresa : cliente.getEmpresa();
                List<AulaDiaJSON> proximasAulas = agendaService.consultarAgendamentosModalidadesAlunoApp(ctx, Uteis.getDate(inicio, "dd/MM/yyyy"),
                        Uteis.getDate(fim, "dd/MM/yyyy"), matricula, empresa, contratoInt);
                proximasAulas = Ordenacao.ordenarLista(proximasAulas, "diaDate");
                mm.addAttribute("aulas", proximasAulas);
            } else {
                throw new ServiceException(String.format("Aluno não encontrado na empresa %s com a matrícula: %s", ctx, matricula));
            }
        } catch (Exception ex) {
            mm.addAttribute("aulas", new ArrayList<>());
            Logger.getLogger(AulaDiaJSONControle.class.getName()).log(Level.SEVERE, String.format("Inicio: %s - Fim %s; Contrato %s, Matrícula %d;", inicio, fim, contrato, matricula));
            Logger.getLogger(AulaDiaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/horariosSugeridos", method = RequestMethod.GET)
    public @ResponseBody
    ModelMap horariosSugeridos(@PathVariable String ctx,
                               @RequestParam final String data,
                               @RequestParam final Integer empresa) {
        ModelMap mm = new ModelMap();
        try {
            List<SugestaoHorarioPersonalJSON> horarioJSONS = avaliacaoService.sugerirHorariosApp(ctx, Calendario.getDataComHoraZerada(Uteis.getDate(data)), empresa);
            mm.addAttribute(RETURN, horarioJSONS);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, getViewUtils().getMensagem(ex.getMessage()));
            Logger.getLogger(AvaliacaoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "{ctx}/disponibilidades", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> disponibilidades(@PathVariable String ctx,
                                                                @RequestParam("empresaId") Integer empresaId,
                                                                @RequestParam("dia") String dia,
                                                                @RequestParam("periodo") PeriodoFiltrarEnum periodo,
                                                                @RequestParam(required = false, defaultValue = "false") Boolean appTreino,
                                                                @RequestParam(required = false) Integer matricula,
                                                                HttpServletRequest request) {
        try {
            List<TipoAgendamentoDTO> tipoAgendamentoIds = tipoEventoService.obterTodosAtivosApp(ctx);
            FiltrosAgendamentosDTO filtrosAgendamentos = new FiltrosAgendamentosDTO(tipoAgendamentoIds);
            return ResponseEntityFactory.ok(disponibilidadeService.disponibilidadesApp(ctx, empresaId, Calendario.getDate("dd/MM/yyyy", dia), periodo, filtrosAgendamentos, request, tipoAgendamentoIds.size(), appTreino, matricula));
        } catch (ServiceException e) {
            Logger.getLogger(AgendaController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter disponibilidades", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            Logger.getLogger(AgendaController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter disponibilidades", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "{ctx}/v1/disponibilidades", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> disponibilidadesAgrupadasPorComportamento(@PathVariable String ctx,
                                                                                         @RequestParam("empresaId") Integer empresaId,
                                                                                         @RequestParam("dia") String dia,
                                                                                         @RequestParam("periodo") PeriodoFiltrarEnum periodo,
                                                                                         HttpServletRequest request) {
        try {
            List<TipoAgendamentoDTO> tipoAgendamentoIds = tipoEventoService.obterTodosAtivosApp(ctx);
            FiltrosAgendamentosDTO filtrosAgendamentos = new FiltrosAgendamentosDTO(tipoAgendamentoIds);
            return ResponseEntityFactory.ok(disponibilidadeService.disponibilidadesPorComportamentoApp(ctx, empresaId, Calendario.getDate("dd/MM/yyyy", dia), periodo, filtrosAgendamentos, request, tipoAgendamentoIds.size()));
        } catch (ServiceException e) {
            Logger.getLogger(AgendaController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter disponibilidades", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            Logger.getLogger(AgendaController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter disponibilidades", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "{ctx}/criar-agendamento", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> criarAgendamento(
            @PathVariable String ctx,
            @RequestBody AgendamentoPersonalDTO agendamentoDTO,
            HttpServletRequest request
    ) {
        try {
            return ResponseEntityFactory.ok(agendamentoService.criarAgendamentoAlunoPersonal(request, agendamentoDTO.getEmpresa(), agendamentoDTO, ctx));
        } catch (ServiceException e) {
            Logger.getLogger(AgendaController.class.getName()).log(Level.SEVERE, "Erro ao tentar salvar agendamento de aluno", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ResponseBody
    @RequestMapping(value = "{ctx}/agendamento/{id}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> removerAgendamento(@PathVariable String ctx,@PathVariable("id") Integer id) {
        try {
            agendamentoService.removerAgendamentoAlunoPersonal(ctx,id);
            return ResponseEntityFactory.ok("sucesso");
        } catch (HorarioConcomitanteException e) {
            return ResponseEntityFactory.erroConhecido(e.getMessage(), e.getMessage());
        } catch (ServiceException e) {
            Logger.getLogger(AgendaController.class.getName()).log(Level.SEVERE, "Erro ao tentar salvar agendamento de aluno", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "{ctx}/agendamento/{id}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alterarAgendamento(  @PathVariable String ctx,
                                                                    @PathVariable("id") Integer id,
                                                                    @RequestBody AgendamentoPersonalDTO agendamentoDTO,
                                                                    HttpServletRequest request
    ) {
        try {
            return ResponseEntityFactory.ok(agendamentoService.alterarAgendamentoAlunoPersonal(request, agendamentoDTO.getEmpresa(), id, agendamentoDTO, ctx));
        } catch (HorarioConcomitanteException e) {
            return ResponseEntityFactory.erroConhecido(e.getMessage(), e.getMessage());
        } catch (ServiceException e) {
            Logger.getLogger(AgendaController.class.getName()).log(Level.SEVERE, "Erro ao tentar salvar agendamento de aluno", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "{ctx}/treinos-aluno/{codigoProfessor}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> buscaFrequenciaAlunoPorCarteiraProfessor(@PathVariable String ctx,
                                                                                        @PathVariable("codigoProfessor") Integer codigoProfessor,
                                                                                        @RequestParam("periodo") Integer periodo,
                                                                                        HttpServletRequest request
    ) {
        try {
            return ResponseEntityFactory.ok(agendamentoService.calculaMediaFrequenciaAluno(request, codigoProfessor, ctx, periodo));
        } catch (Exception e) {
            Logger.getLogger(AgendaController.class.getName()).log(Level.SEVERE, "Erro ao buscar treino", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "{ctx}/frequencia/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alterarAgendamento(  @PathVariable String ctx,
                                                                    @PathVariable("id") Integer id,
                                                                    @RequestParam("periodo") Integer periodo,
                                                                    HttpServletRequest request
    ) {
        try {
            return ResponseEntityFactory.ok(agendamentoService.frequenciaTreinoEAtendimentoAluno(ctx, id, periodo, request));
        } catch (Exception e) {
            Logger.getLogger(AgendaController.class.getName()).log(Level.SEVERE, "Erro ao buscar frequencias", e);
            return ResponseEntityFactory.erroInterno(e.getStackTrace().toString(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "{ctx}/objetivos/{matricula}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> objetivos(@PathVariable String ctx,
                                                         @PathVariable Integer matricula,
                                                         @RequestParam (required = false) Integer status,
                                                         @RequestParam (required = false) Boolean primario) {
        try {
            return ResponseEntityFactory.ok(agendamentoService.objetivos(ctx, matricula, status, primario));
        } catch (Exception e) {
            Logger.getLogger(AgendaController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter objetivos", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "{ctx}/objetivos/by-codigo/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> objetivo(@PathVariable String ctx,
                                                        @PathVariable Integer id) {
        try {
            return ResponseEntityFactory.ok(agendamentoService.objetivoPorCodigo(ctx, id));
        } catch (Exception e) {
            Logger.getLogger(AgendaController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter objetivo", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "{ctx}/objetivos/{matricula}", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> criarObjetivo(@PathVariable String ctx,
                                                             @PathVariable Integer matricula,
                                                             @RequestBody ObjetivoAlunoDTO objetivoDTO) {
        try {
            return ResponseEntityFactory.ok(agendamentoService.criarObjetivo(ctx, matricula, objetivoDTO));
        } catch (Exception e) {
            Logger.getLogger(AgendaController.class.getName()).log(Level.SEVERE, "Erro ao tentar criar objetivo", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "{ctx}/objetivos/{id}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alterarObjetivo(@PathVariable String ctx,
                                                              @PathVariable Integer id,
                                                              @RequestBody ObjetivoAlunoDTO objetivoDTO) {
        try {
            return ResponseEntityFactory.ok(agendamentoService.alterarObjetivo(ctx, id, objetivoDTO));
        } catch (Exception e) {
            Logger.getLogger(AgendaController.class.getName()).log(Level.SEVERE, "Erro ao tentar alterar objetivo", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "{ctx}/objetivos/{id}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> removerObjetivo(@PathVariable String ctx,
                                                              @PathVariable Integer id) {
        try {
            agendamentoService.removerObjetivo(ctx, id);
            return ResponseEntityFactory.ok();
        } catch (Exception e) {
            Logger.getLogger(AgendaController.class.getName()).log(Level.SEVERE, "Erro ao tentar remover objetivo", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "{ctx}/objetivosIntermediarios", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> criarObjetivoIntermediario(@PathVariable String ctx,
                                                                          @RequestBody ObjetivoIntermediarioAlunoDTO objetivoIntermediarioDTO) {
        try {
            return ResponseEntityFactory.ok(agendamentoService.criarObjetivoIntermediario(ctx, objetivoIntermediarioDTO));
        } catch (Exception e) {
            Logger.getLogger(AgendaController.class.getName()).log(Level.SEVERE, "Erro ao tentar criar objetivo intermediário", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "{ctx}/objetivosIntermediarios/{id}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alterarObjetivoIntermediario(@PathVariable String ctx,
                                                                           @PathVariable Integer id,
                                                                           @RequestBody ObjetivoIntermediarioAlunoDTO objetivoIntermediarioDTO) {
        try {
            return ResponseEntityFactory.ok(agendamentoService.alterarObjetivoIntermediario(ctx, id, objetivoIntermediarioDTO));
        } catch (Exception e) {
            Logger.getLogger(AgendaController.class.getName()).log(Level.SEVERE, "Erro ao tentar alterar objetivo intermediário", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "{ctx}/objetivosIntermediarios/{id}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> removerObjetivoIntermediario(@PathVariable String ctx,
                                                                           @PathVariable Integer id) {
        try {
            agendamentoService.removerObjetivoIntermediario(ctx, id);
            return ResponseEntityFactory.ok();
        } catch (Exception e) {
            Logger.getLogger(AgendaController.class.getName()).log(Level.SEVERE, "Erro ao tentar remover objetivo intermediário", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "{ctx}/objetivosIntermediarios/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> objetivoIntermediario(@PathVariable String ctx,
                                                                    @PathVariable Integer id) {
        try {
            return ResponseEntityFactory.ok(agendamentoService.objetivoIntermediario(ctx, id));
        } catch (Exception e) {
            Logger.getLogger(AgendaController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter objetivo intermediário", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }


    @RequestMapping(value = "{ctx}/saldo-aulas-coletivas", method = RequestMethod.GET)
    public @ResponseBody
    ModelMap saldoAlunoRepor(@PathVariable String ctx,
                             @RequestParam final Integer matricula) {
        ModelMap mm = new ModelMap();
        try {
            SaldoAulaColetivaVO result = agendaService.consultarSaldoAlunoAulasColetivas(ctx, matricula);
            mm.addAttribute(STATUS_SUCESSO, result);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(AulaDiaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }
}
