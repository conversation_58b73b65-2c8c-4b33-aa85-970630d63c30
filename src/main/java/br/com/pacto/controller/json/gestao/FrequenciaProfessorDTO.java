package br.com.pacto.controller.json.gestao;

import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class FrequenciaProfessorDTO {


    //    id: 1,
    private Integer id;
//    nome: 'Prof 1',
    private String nome = "";
//    imageUri: 'https://randomuser.me/api/portraits/women/71.jpg',
    private String imageUri = "";
//    frequencia: 45
    private Integer frequencia;

    public FrequenciaProfessorDTO() {

    }
    public FrequenciaProfessorDTO(Integer id, String nome, String imageUri, Integer frequencia) {
        this.id = id;
        this.nome = nome;
        this.imageUri = imageUri;
        this.frequencia = frequencia;
    }



    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getImageUri() {
        return imageUri;
    }

    public void setImageUri(String imageUri) {
        this.imageUri = imageUri;
    }

    public Integer getFrequencia() {
        return frequencia;
    }

    public void setFrequencia(Integer frequencia) {
        this.frequencia = frequencia;
    }
}
