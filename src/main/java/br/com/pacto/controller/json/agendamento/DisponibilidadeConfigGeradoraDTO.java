package br.com.pacto.controller.json.agendamento;

import java.util.List;

public class DisponibilidadeConfigGeradoraDTO {

    private Integer id;
    private Long dia;
    private Integer professorId;
    private Integer tipoAgendamentoId;
    private Integer horarioInicial; //informações em minutos
    private Integer horarioFinal; //informações em minutos
    private List<AgendaDiaSemana> repetirDias;
    private Long repetirDataLimite;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Long getDia() {
        return dia;
    }

    public void setDia(Long dia) {
        this.dia = dia;
    }

    public Integer getProfessorId() {
        return professorId;
    }

    public void setProfessorId(Integer professorId) {
        this.professorId = professorId;
    }

    public Integer getTipoAgendamentoId() {
        return tipoAgendamentoId;
    }

    public void setTipoAgendamentoId(Integer tipoAgendamentoId) {
        this.tipoAgendamentoId = tipoAgendamentoId;
    }

    public Integer getHorarioInicial() {
        return horarioInicial;
    }

    public void setHorarioInicial(Integer horarioInicial) {
        this.horarioInicial = horarioInicial;
    }

    public Integer getHorarioFinal() {
        return horarioFinal;
    }

    public void setHorarioFinal(Integer horarioFinal) {
        this.horarioFinal = horarioFinal;
    }

    public List<AgendaDiaSemana> getRepetirDias() {
        return repetirDias;
    }

    public void setRepetirDias(List<AgendaDiaSemana> repetirDias) {
        this.repetirDias = repetirDias;
    }

    public Long getRepetirDataLimite() {
        return repetirDataLimite;
    }

    public void setRepetirDataLimite(Long repetirDataLimite) {
        this.repetirDataLimite = repetirDataLimite;
    }
}
