package br.com.pacto.controller.json.spivi;

import br.com.pacto.controller.json.base.SuperJSON;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import flex.messaging.io.ArrayList;

import java.util.Date;
import java.util.List;

public class ClientJSON {
    private int ClientID = 0;
    private String Email;
    private String FirstName;
    private String LastName;
    private String DisplayName;
    private String Gender;
    private String Height;
    private String Weight;
    private String Address;
    private String City;
    private String PostalCode;
    private String Country;
    private String Phone;
    private String BirthDate;
    private String ProfilePicture;
    private int FTP;
    private int LTHR;
    private int RHR;

    public ClientJSON() {
    }

    public ClientJSON(JSONObject json) throws JSONException {
        ClientID = json.getInt("ClientID");
        Email = json.getString("Email");
        FirstName = json.getString("FirstName");
        LastName = json.getString("LastName");
        DisplayName = json.getString("DisplayName");
        Gender = json.getString("Gender");
        Height = json.getString("Height");
        Weight = json.getString("Weight");
        Address = json.getString("Address");
        City = json.getString("City");
        PostalCode = json.getString("PostalCode");
        Country = json.getString("Country");
        Phone = json.getString("Phone");
        BirthDate = json.getString("BirthDate");
        ProfilePicture = json.getString("ProfilePicture");
        this.FTP = json.getInt("FTP");
        this.LTHR = json.getInt("LTHR");
    }

    public int getClientID() {
        return ClientID;
    }

    public void setClientID(int clientID) {
        ClientID = clientID;
    }

    public String getEmail() {
        return Email;
    }

    public void setEmail(String email) {
        Email = email;
    }

    public String getFirstName() {
        return FirstName;
    }

    public void setFirstName(String firstName) {
        FirstName = firstName;
    }

    public String getLastName() {
        return LastName;
    }

    public void setLastName(String lastName) {
        LastName = lastName;
    }

    public String getDisplayName() {
        return DisplayName;
    }

    public void setDisplayName(String displayName) {
        DisplayName = displayName;
    }

    public String getGender() {
        return Gender;
    }

    public void setGender(String gender) {
        Gender = gender;
    }

    public String getHeight() {
        return Height;
    }

    public void setHeight(String height) {
        Height = height;
    }

    public String getWeight() {
        return Weight;
    }

    public void setWeight(String weight) {
        Weight = weight;
    }

    public String getAddress() {
        return Address;
    }

    public void setAddress(String address) {
        Address = address;
    }

    public String getCity() {
        return City;
    }

    public void setCity(String city) {
        City = city;
    }

    public String getPostalCode() {
        return PostalCode;
    }

    public void setPostalCode(String postalCode) {
        PostalCode = postalCode;
    }

    public String getCountry() {
        return Country;
    }

    public void setCountry(String country) {
        Country = country;
    }

    public String getPhone() {
        return Phone;
    }

    public void setPhone(String phone) {
        Phone = phone;
    }

    public String getBirthDate() {
        return BirthDate;
    }

    public void setBirthDate(final String birthDate) {
        BirthDate = birthDate;
    }

    public String getProfilePicture() {
        return ProfilePicture;
    }

    public void setProfilePicture(String profilePicture) {
        ProfilePicture = profilePicture;
    }

    public int getFTP() {
        return FTP;
    }

    public void setFTP(int FTP) {
        this.FTP = FTP;
    }

    public int getLTHR() {
        return LTHR;
    }

    public void setLTHR(int LTHR) {
        this.LTHR = LTHR;
    }

    public int getRHR() {
        return RHR;
    }

    public void setRHR(int RHR) {
        this.RHR = RHR;
    }
}
