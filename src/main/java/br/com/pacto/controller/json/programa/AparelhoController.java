package br.com.pacto.controller.json.programa;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.bean.selfloops.SelfLoopsConfiguracoes;
import br.com.pacto.controller.json.atividade.AtividadeController;
import br.com.pacto.security.aspecto.Permissao;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.aparelho.FiltroAparelhoJSON;
import br.com.pacto.service.intf.aparelho.AparelhoService;
import br.com.pacto.service.intf.selfloops.SelfloopsConfiguracoesService;
import br.com.pacto.util.UteisValidacao;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.ArrayList;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by ulisses on 26/07/2018.
 */
@Controller
@RequestMapping("/psec/aparelhos")
public class AparelhoController {

    private final AparelhoService aparelhoService;
    private final SelfloopsConfiguracoesService selfloopsConfiguracoesService;
    private final SessaoService sessaoService;

    @Autowired
    public AparelhoController(AparelhoService aparelhoService, SelfloopsConfiguracoesService selfloopsConfiguracoesService, SessaoService sessaoService){
        this.selfloopsConfiguracoesService = selfloopsConfiguracoesService;
        this.sessaoService = sessaoService;
        Assert.notNull(aparelhoService, "O serviço de aparelho não foi injetado corretamente");
        this.aparelhoService = aparelhoService;
    }


    @ResponseBody
    @Permissao(recursos = RecursoEnum.APARELHOS)
    @RequestMapping(value = "", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> consultarAparelhos(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                                  PaginadorDTO paginadorDTO) throws JSONException {
        try {
            FiltroAparelhoJSON filtroGestaoJSON = new FiltroAparelhoJSON(filtros);

            return ResponseEntityFactory.ok(aparelhoService.consultarAparelhos(filtroGestaoJSON, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(AparelhoController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar os aparelhos", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.APARELHOS)
    @RequestMapping(value = "all", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listaTodosAparelhos(boolean crossfit) {
        try {
            return ResponseEntityFactory.ok(aparelhoService.listaTodosAparelhos(crossfit));
        } catch (ServiceException e) {
            Logger.getLogger(AparelhoController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar os aparelhos", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.APARELHOS)
    @RequestMapping(value = "/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> consultarAparelho(@PathVariable("id") final Integer id) {
        try {
            return ResponseEntityFactory.ok(aparelhoService.consultarAparelho(id));
        } catch (ServiceException e) {
            Logger.getLogger(AparelhoController.class.getName()).log(Level.SEVERE, "Erro ao tentar consultar o aparelho", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }


    @ResponseBody
    @Permissao(recursos = RecursoEnum.APARELHOS)
    @RequestMapping(value = "", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> incluirAparelhos(
            @RequestBody AparelhoTO aparelhoTO) throws Exception {
        try {
            return ResponseEntityFactory.ok(aparelhoService.inserir(aparelhoTO));
        } catch (ServiceException e) {
            Logger.getLogger(AparelhoController.class.getName()).log(Level.SEVERE, "Erro ao tentar incluir aparelho", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.APARELHOS)
    @RequestMapping(value = "/{id}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alterarAparelhos(@PathVariable("id") Integer id,
                                                                @RequestBody AparelhoTO aparelhoTO) {
        try {
            return ResponseEntityFactory.ok(aparelhoService.alterar(id, aparelhoTO));

        } catch (ServiceException e) {
            Logger.getLogger(AparelhoController.class.getName()).log(Level.SEVERE, "Erro ao tentar alterar aparelho", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.APARELHOS)
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> excluirAparelho(@PathVariable("id") final Integer id){
        try {
            aparelhoService.excluir(id);
            return ResponseEntityFactory.ok(true);
        } catch (ServiceException e) {
            Logger.getLogger(AparelhoController.class.getName()).log(Level.SEVERE, "Erro ao tentar excluir aparelho", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.APARELHOS)
    @RequestMapping(value = "/habilitados-reserva-equipamento", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> aparelhosHabilitadosReservaEquipamento(@RequestHeader(value = "empresaId", required = true) Integer empresaId) {
        try {
            return ResponseEntityFactory.ok(aparelhoService.aparelhosHabilitadosReservaEquipamento());
        } catch (ServiceException e) {
            Logger.getLogger(AparelhoController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar os aparelhos habilitados para reserva de equipamento", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.APARELHOS)
    @RequestMapping(value = "/integracaoSelfloop/sensores", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listarSensores() throws JSONException {
        try {
            String chave = sessaoService.getUsuarioAtual().getChave();
            Integer empresaId = sessaoService.getUsuarioAtual().getEmpresaAtual();
            SelfLoopsConfiguracoes configs = selfloopsConfiguracoesService.obterPorEmpresa(chave, empresaId);
            if(configs != null && !UteisValidacao.emptyString(configs.getCodeSelfloops()) && !UteisValidacao.emptyString(configs.getEmpresaSelfloops())){
                return ResponseEntityFactory.ok(selfloopsConfiguracoesService.obterSensoresSelfloops(configs, chave, configs.getEmpresa().getCodigo()));
            }else {
                return ResponseEntityFactory.ok(new ArrayList<>());
            }
        } catch (ServiceException e) {
            Logger.getLogger(AtividadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar as atividades", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

}

