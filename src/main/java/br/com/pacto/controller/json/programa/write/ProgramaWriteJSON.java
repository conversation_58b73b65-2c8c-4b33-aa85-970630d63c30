/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pacto.controller.json.programa.write;

import br.com.pacto.bean.programa.ObjetivoPrograma;
import br.com.pacto.bean.programa.ProgramaSituacaoEnum;
import br.com.pacto.bean.programa.ProgramaTreino;
import br.com.pacto.bean.programa.ProgramaTreinoFicha;
import br.com.pacto.controller.json.acompanhamento.AcompanhamentoSimplesJSON;
import br.com.pacto.controller.json.base.SuperJSON;
import br.com.pacto.controller.json.programa.read.ObjetivoProgramaJSON;
import br.com.pacto.controller.json.programa.read.ProgramaTreinoFichaJSON;
import br.com.pacto.controller.json.serialization.JsonDateDeserializerYYYYMMDD;
import br.com.pacto.controller.json.serialization.JsonDateSerializerYYYYMMDD;
import br.com.pacto.controller.json.serialization.JsonDateTimeDeserializerYYYYMMDDHHNN;
import br.com.pacto.controller.json.serialization.JsonDateTimeSerializerYYYYMMDDHHNN;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class ProgramaWriteJSON extends SuperJSON {

    private Integer codigo;
    private String username;
    private Date dataInicio;
    private Date dataLancamento;
    private Date dataProximaRevisao;
    private Date dataTerminoPrevisto;
    private Integer diasPorSemana;
    private String nome;
    private Integer situacao;
    private Integer totalAulasPrevistas;
    private Integer cliente;
    private Integer professorCargeira;
    private Integer professorMontou;
    private Integer programaTreinoRenovacao;
    private Date dataRenovacao;
    private Integer programaTreinoRenovado;
    private List<ProgramaTreinoFichaJSON> programaFichas = new ArrayList();
    private List<ObjetivoProgramaJSON> objetivosPrograma = new ArrayList();
    private AcompanhamentoSimplesJSON frequencia = new AcompanhamentoSimplesJSON();
    private String nomeProfessorCarteira;
    private String nomeProfessorMontou;

    public ProgramaWriteJSON() {
    }

    public ProgramaWriteJSON(ProgramaTreino p) {
        this.codigo = p.getCodigo();
        this.dataInicio = p.getDataInicio();
        this.dataLancamento = p.getDataLancamento();
        this.dataProximaRevisao = p.getDataProximaRevisao();
        this.dataTerminoPrevisto = p.getDataTerminoPrevisto();
        this.diasPorSemana = p.getDiasPorSemana();
        this.nome = p.getNome();
        this.situacao = p.getSituacao() != null ? p.getSituacao().getId() : ProgramaSituacaoEnum.ATIVO.getId();
        this.totalAulasPrevistas = p.getTotalAulasPrevistas();
        this.cliente = p.getCliente().getCodigo();
        if (p.getProfessorCarteira() != null) {
            this.nomeProfessorCarteira = p.getProfessorCarteira().getNome();
            this.professorCargeira = p.getProfessorCarteira().getCodigo();
        }
        if (p.getProfessorMontou() != null) {
            this.nomeProfessorMontou = p.getProfessorMontou().getNome();
            this.professorMontou = p.getProfessorMontou().getCodigo();
        }
        this.programaTreinoRenovacao = p.getProgramaTreinoRenovacao();
        this.dataRenovacao = p.getDataRenovacao();
        this.programaTreinoRenovado = p.getProgramaTreinoRenovacao();
    }

    public void preencherProgramaTreinoFichas(ProgramaTreino prog) {
        if (prog.getProgramaFichas() != null) {
            for (ProgramaTreinoFicha ptf : prog.getProgramaFichas()) {
                this.programaFichas.add(new ProgramaTreinoFichaJSON(ptf));
            }
        }
    }

    public void preencherObjetivosPrograma(ProgramaTreino prog) {
        if (prog.getObjetivos() != null) {
            for (ObjetivoPrograma ob : prog.getObjetivos()) {
                this.objetivosPrograma.add(new ObjetivoProgramaJSON(ob));
            }
        }
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    @JsonSerialize(using = JsonDateSerializerYYYYMMDD.class)
    public Date getDataInicio() {
        return dataInicio;
    }

    @JsonDeserialize(using = JsonDateDeserializerYYYYMMDD.class)
    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    @JsonSerialize(using = JsonDateTimeSerializerYYYYMMDDHHNN.class)
    public Date getDataLancamento() {
        return dataLancamento;
    }

    @JsonDeserialize(using = JsonDateTimeDeserializerYYYYMMDDHHNN.class)
    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    @JsonSerialize(using = JsonDateSerializerYYYYMMDD.class)
    public Date getDataProximaRevisao() {
        return dataProximaRevisao;
    }

    @JsonDeserialize(using = JsonDateDeserializerYYYYMMDD.class)
    public void setDataProximaRevisao(Date dataProximaRevisao) {
        this.dataProximaRevisao = dataProximaRevisao;
    }

    @JsonSerialize(using = JsonDateSerializerYYYYMMDD.class)
    public Date getDataTerminoPrevisto() {
        return dataTerminoPrevisto;
    }

    @JsonDeserialize(using = JsonDateDeserializerYYYYMMDD.class)
    public void setDataTerminoPrevisto(Date dataTerminoPrevisto) {
        this.dataTerminoPrevisto = dataTerminoPrevisto;
    }

    public Integer getDiasPorSemana() {
        return diasPorSemana;
    }

    public void setDiasPorSemana(Integer diasPorSemana) {
        this.diasPorSemana = diasPorSemana;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Integer getSituacao() {
        return situacao;
    }

    public void setSituacao(Integer situacao) {
        this.situacao = situacao;
    }

    public Integer getTotalAulasPrevistas() {
        return totalAulasPrevistas;
    }

    public void setTotalAulasPrevistas(Integer totalAulasPrevistas) {
        this.totalAulasPrevistas = totalAulasPrevistas;
    }

    public Integer getCliente() {
        return cliente;
    }

    public void setCliente(Integer cliente) {
        this.cliente = cliente;
    }

    public Integer getProfessorCargeira() {
        if (professorCargeira == null) {
            professorCargeira = 0;
        }
        return professorCargeira;
    }

    public void setProfessorCargeira(Integer professorCargeira) {
        this.professorCargeira = professorCargeira;
    }

    public Integer getProfessorMontou() {
        return professorMontou;
    }

    public void setProfessorMontou(Integer professorMontou) {
        this.professorMontou = professorMontou;
    }

    public Integer getProgramaTreinoRenovacao() {
        if (programaTreinoRenovacao == null) {
            programaTreinoRenovacao = 0;
        }
        return programaTreinoRenovacao;
    }

    public void setProgramaTreinoRenovacao(Integer programaTreinoRenovacao) {
        this.programaTreinoRenovacao = programaTreinoRenovacao;
    }

    @JsonSerialize(using = JsonDateTimeSerializerYYYYMMDDHHNN.class,
            nullsUsing = JsonDateTimeSerializerYYYYMMDDHHNN.class)
    public Date getDataRenovacao() {
        return dataRenovacao;
    }

    @JsonDeserialize(using = JsonDateDeserializerYYYYMMDD.class)
    public void setDataRenovacao(Date dataRenovacao) {
        this.dataRenovacao = dataRenovacao;
    }

    public Integer getProgramaTreinoRenovado() {
        if (programaTreinoRenovado == null) {
            programaTreinoRenovado = 0;
        }
        return programaTreinoRenovado;
    }

    public void setProgramaTreinoRenovado(Integer programaTreinoRenovado) {
        this.programaTreinoRenovado = programaTreinoRenovado;
    }

    public List<ProgramaTreinoFichaJSON> getProgramaFichas() {
        return programaFichas;
    }

    public void setProgramaFichas(List<ProgramaTreinoFichaJSON> programaFichas) {
        this.programaFichas = programaFichas;
    }

    public List<ObjetivoProgramaJSON> getObjetivosPrograma() {
        return objetivosPrograma;
    }

    public void setObjetivosPrograma(List<ObjetivoProgramaJSON> objetivosPrograma) {
        this.objetivosPrograma = objetivosPrograma;
    }

    public AcompanhamentoSimplesJSON getFrequencia() {
        return frequencia;
    }

    public void setFrequencia(AcompanhamentoSimplesJSON frequencia) {
        this.frequencia = frequencia;
    }

    public String getNomeProfessorCarteira() {
        return nomeProfessorCarteira;
    }

    public void setNomeProfessorCarteira(String nomeProfessorCarteira) {
        this.nomeProfessorCarteira = nomeProfessorCarteira;
    }

    public String getNomeProfessorMontou() {
        return nomeProfessorMontou;
    }

    public void setNomeProfessorMontou(String nomeProfessorMontou) {
        this.nomeProfessorMontou = nomeProfessorMontou;
    }
}
