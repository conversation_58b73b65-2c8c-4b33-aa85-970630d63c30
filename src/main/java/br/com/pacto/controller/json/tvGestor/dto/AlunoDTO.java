package br.com.pacto.controller.json.tvGestor.dto;

import br.com.pacto.objeto.Uteis;
import com.fasterxml.jackson.annotation.JsonInclude;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class AlunoDTO {
    private Long codigoAluno;
    private String matriculaAluno;
    private String nomeAluno;
    private String nomeTurma;
    private String dataHoraInicioTurma;
    private String dataHoraFimTurma;
    private String dataHoraAcesso;
    private String imageUri;

    public Long getCodigoAluno() {
        return codigoAluno;
    }

    public void setCodigoAluno(Long codigoAluno) {
        this.codigoAluno = codigoAluno;
    }

    public String getMatriculaAluno() {
        return matriculaAluno;
    }

    public void setMatriculaAluno(String matriculaAluno) {
        this.matriculaAluno = matriculaAluno;
    }

    public String getNomeAluno() {
        if (StringUtils.isNotBlank(nomeAluno)) {
            return Uteis.getNomeAbreviandoSomentePrimeiroSobrenome(nomeAluno);
        } else {
            return "";
        }
    }

    public void setNomeAluno(String nomeAluno) {
        this.nomeAluno = nomeAluno;
    }

    public String getNomeTurma() {
        return nomeTurma;
    }

    public void setNomeTurma(String nomeTurma) {
        this.nomeTurma = nomeTurma;
    }

    public String getDataHoraInicioTurma() {
        return dataHoraInicioTurma;
    }

    public void setDataHoraInicioTurma(String dataHoraInicioTurma) {
        this.dataHoraInicioTurma = dataHoraInicioTurma;
    }

    public String getDataHoraFimTurma() {
        return dataHoraFimTurma;
    }

    public void setDataHoraFimTurma(String dataHoraFimTurma) {
        this.dataHoraFimTurma = dataHoraFimTurma;
    }

    public String getImageUri() {
        return imageUri;
    }

    public void setImageUri(String imageUri) {
        this.imageUri = imageUri;
    }

    public String getDataHoraAcesso() {
        return dataHoraAcesso;
    }

    public void setDataHoraAcesso(String dataHoraAcesso) {
        this.dataHoraAcesso = dataHoraAcesso;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        AlunoDTO alunoDTO = (AlunoDTO) o;
        return Objects.equals(codigoAluno, alunoDTO.codigoAluno) &&
                Objects.equals(dataHoraInicioTurma, alunoDTO.dataHoraInicioTurma);
    }

    @Override
    public int hashCode() {
        return Objects.hash(codigoAluno, dataHoraInicioTurma);
    }
}
