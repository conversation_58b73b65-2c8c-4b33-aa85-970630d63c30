package br.com.pacto.controller.json.aluno;

import org.apache.commons.lang3.StringUtils;

/**
 * Created by paulo on 09/07/2019.
 */
public enum SituacaoContratoZWEnum {

    NORMAL("NO"),
    A_VENCER("AV"),
    ATESTADO_MEDICO("ATM"),
    CARENCIA("CR"),
    CANCELADO("CA"),
    DESISTENTE("DE"),
    INATIVO_VENCIDO("VE"),
    TRANCADO_VENCIDO("TV"),
    DIARIA("DI"),
    FREE_PASS("PE"),
    AULA_AVULSA("AA");

    private String codigo;

    SituacaoContratoZWEnum(String codigo) {
        this.codigo = codigo;
    }

    public String getCodigo() {
        return codigo;
    }

    public void setCodigo(String codigo) {
        this.codigo = codigo;
    }

    public static SituacaoContratoZWEnum getInstance(String codigo) {
        if (!StringUtils.isBlank(codigo)) {
            for (SituacaoContratoZWEnum situacaoContrato : SituacaoContratoZWEnum.values()) {
                if (codigo.equals(situacaoContrato.getCodigo())) {
                    return situacaoContrato;
                }
            }
        }

        return null;
    }
}
