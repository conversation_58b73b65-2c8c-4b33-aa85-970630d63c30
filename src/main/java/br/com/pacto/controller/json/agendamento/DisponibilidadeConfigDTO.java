package br.com.pacto.controller.json.agendamento;

import br.com.pacto.bean.colaborador.ColaboradorSimplesTO;
import br.com.pacto.controller.json.tipoEvento.TipoAgendamentoDTO;

import java.util.List;

public class DisponibilidadeConfigDTO {

    private Integer id;
    private ColaboradorSimplesTO professor;
    private TipoAgendamentoDTO tipoAgendamento;
    private Long dia;
    private String horarioInicial; //No formato HH:mm
    private String horarioFinal; //No formato HH:mm
    private List<AgendaDiaSemana> repetirDias;
    private Long repetirDataLimite;

    public DisponibilidadeConfigDTO() {
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public ColaboradorSimplesTO getProfessor() {
        return professor;
    }

    public void setProfessor(ColaboradorSimplesTO professor) {
        this.professor = professor;
    }

    public TipoAgendamentoDTO getTipoAgendamento() {
        return tipoAgendamento;
    }

    public void setTipoAgendamento(TipoAgendamentoDTO tipoAgendamento) {
        this.tipoAgendamento = tipoAgendamento;
    }

    public Long getDia() {
        return dia;
    }

    public void setDia(Long dia) {
        this.dia = dia;
    }

    public String getHorarioInicial() {
        return horarioInicial;
    }

    public void setHorarioInicial(String horarioInicial) {
        this.horarioInicial = horarioInicial;
    }

    public String getHorarioFinal() {
        return horarioFinal;
    }

    public void setHorarioFinal(String horarioFinal) {
        this.horarioFinal = horarioFinal;
    }

    public List<AgendaDiaSemana> getRepetirDias() {
        return repetirDias;
    }

    public void setRepetirDias(List<AgendaDiaSemana> repetirDias) {
        this.repetirDias = repetirDias;
    }

    public Long getRepetirDataLimite() {
        return repetirDataLimite;
    }

    public void setRepetirDataLimite(Long repetirDataLimite) {
        this.repetirDataLimite = repetirDataLimite;
    }

}
