package br.com.pacto.controller.json.aluno;

import br.com.pacto.service.impl.cliente.perfil.FichaDoDiaDTO;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "DTO para representar a resposta contendo a ficha do dia")
public class FichaDiaResponseDTO {

    @ApiModelProperty(value = "Ficha do dia", required = true)
    @JsonProperty("content")
    private FichaDoDiaDTO content;

    public FichaDoDiaDTO getContent() {
        return content;
    }

    public void setContent(FichaDoDiaDTO content) {
        this.content = content;
    }
}
