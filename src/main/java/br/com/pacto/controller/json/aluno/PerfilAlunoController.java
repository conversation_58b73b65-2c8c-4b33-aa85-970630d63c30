package br.com.pacto.controller.json.aluno;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.cliente.perfil.FichaDoDiaDTO;
import br.com.pacto.service.impl.cliente.perfil.ProgramaAtualDTO;
import br.com.pacto.service.intf.cliente.PerfilAlunoService;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import springfox.documentation.annotations.ApiIgnore;

import java.util.logging.Level;
import java.util.logging.Logger;

@Controller
@Api(value = "PerfilAlunoController")
@RequestMapping("/psec/perfil/aluno")
public class PerfilAlunoController {

    private final PerfilAlunoService perfilAlunoService;

    @Autowired
    public PerfilAlunoController(PerfilAlunoService perfilAlunoService) {
        this.perfilAlunoService = perfilAlunoService;
    }

    @ApiIgnore
    @ResponseBody
    @RequestMapping(value = "/{matricula}/avaliacao", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> avaliacao(@PathVariable("matricula") Integer matricula){
        try {
            return ResponseEntityFactory.ok(perfilAlunoService.avaliacaoFisica(matricula));
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter a avaliação do aluno aluno", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @ApiOperation(value="Retorna o programa atual do aluno", notes = "Este método retorna os dados do programa atual do aluno cuja matrícula foi passada por parâmetro", tags = {"Treino"})
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "Perfil encontrado", response = ProgramaAtualResponseDTO.class),
    })
    @RequestMapping(value = "/{matricula}/programa-atual", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> programaAtual(@ApiParam(value = "Matrícula do aluno", defaultValue = "000100", required = true) @PathVariable("matricula") Integer matricula){
        try {
            return ResponseEntityFactory.ok(perfilAlunoService.programaAtual(matricula));
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter o programa atual do aluno", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @ApiOperation(value="Retorna a ficha do dia do aluno", notes = "Este método retorna os dados da ficha do dia do aluno cuja matrícula foi passada por parâmetro", tags = {"Treino"})
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "Perfil encontrado", response = FichaDiaResponseDTO.class),
    })
    @RequestMapping(value = "/{matricula}/ficha-dia", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> fichaDia(@ApiParam(value = "Matrícula do aluno", defaultValue = "000100", required = true) @PathVariable("matricula") Integer matricula,
                                                        @ApiParam(value = "Buscar ficha não vigente", defaultValue = "false", required = false) @RequestParam(value = "buscarNaoVigente", required = false) Boolean buscarNaoVigente){
        try {
            return ResponseEntityFactory.ok(perfilAlunoService.fichaDoDia(matricula, buscarNaoVigente));
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter a ficha do dia do aluno", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiIgnore
    @ResponseBody
    @RequestMapping(value = "/{matricula}/fichas-relacionadas", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> fichasRelacionadas(@PathVariable("matricula") Integer matricula){
        try {
            return ResponseEntityFactory.ok(perfilAlunoService.fichasRelacionadas(matricula));
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter as fichas relacionadas do aluno", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiIgnore
    @ResponseBody
    @RequestMapping(value = "/{matricula}/dias-treinou-programa-atual", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> diasQueTreinouProgramaAtual(@PathVariable("matricula") Integer matricula){
        try {
            return ResponseEntityFactory.ok(perfilAlunoService.diasQueTreinouProgramaAtual(matricula));
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter os dias que o aluno treinou no programa atual", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiIgnore
    @ResponseBody
    @RequestMapping(value = "/{matricula}/horarios-treinou-programa-atual", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> horariosQueTreinouProgramaAtual(@PathVariable("matricula") Integer matricula){
        try {
            return ResponseEntityFactory.ok(perfilAlunoService.horariosQueTreinouProgramaAtual(matricula));
        } catch (ServiceException e) {
            Logger.getLogger(AlunoController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter os horários que o aluno treinou no programa atual", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }
}
