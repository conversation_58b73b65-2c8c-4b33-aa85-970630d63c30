package br.com.pacto.controller.json.professor;

import br.com.pacto.bean.bi.ConfiguracaoRankingProfessores;
import org.springframework.beans.BeanUtils;

/**
 * Created by paulo 28/11/2018
 */
public class ConfiguracaoRankingBuilder {

    public static ConfiguracaoRankingProfessores convertDTOToEntitie(ConfiguracaoRankingDTO configuracaoRankingDTO, Integer empresaId) {
        ConfiguracaoRankingProfessores config = new ConfiguracaoRankingProfessores();
        config.setCodigo(configuracaoRankingDTO.getId());
        config.setEmpresa(empresaId);
        config.setIndicador(configuracaoRankingDTO.getIndicador());
        config.setPeso(configuracaoRankingDTO.getPeso());
        config.setPositivo(configuracaoRankingDTO.getOperacao());
        config.setAtivo(configuracaoRankingDTO.getAtiva());
        return config;
    }

    public static ConfiguracaoRankingDTO convertEntitieToDTO(ConfiguracaoRankingProfessores configuracaoRankingProfessores) {
        ConfiguracaoRankingDTO config = new ConfiguracaoRankingDTO();
        config.setId(configuracaoRankingProfessores.getCodigo());
        config.setOperacao(configuracaoRankingProfessores.getPositivo());
        config.setAtiva(configuracaoRankingProfessores.getAtivo());
        BeanUtils.copyProperties(configuracaoRankingProfessores, config);
        return config;
    }
}
