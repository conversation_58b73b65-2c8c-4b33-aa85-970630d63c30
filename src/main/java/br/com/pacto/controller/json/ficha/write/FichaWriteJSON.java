/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pacto.controller.json.ficha.write;

import br.com.pacto.bean.atividade.AtividadeFicha;
import br.com.pacto.bean.atividade.AtividadeFichaAjuste;
import br.com.pacto.bean.ficha.Ficha;
import br.com.pacto.bean.pessoa.SexoEnum;
import br.com.pacto.bean.programa.ProgramaTreinoFicha;
import br.com.pacto.bean.serie.Serie;
import br.com.pacto.controller.json.atividade.write.AtividadeFichaAjusteWriteJSON;
import br.com.pacto.controller.json.atividade.write.AtividadeFichaWriteJSON;
import br.com.pacto.controller.json.atividade.write.SerieWriteJSON;
import br.com.pacto.controller.json.base.SuperJSON;
import br.com.pacto.controller.json.nivel.NivelWriteJSON;
import br.com.pacto.controller.json.programa.read.ProgramaTreinoFichaJSON;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class FichaWriteJSON extends SuperJSON {

    private Integer codigo;
    private String username;
    private String nome;
    private String mensagemAluno;
    private Integer categoria;
    private String categoriaFicha;
    private Boolean usarComoPredefinida = false;
    private Integer versao;
    private List<AtividadeFichaWriteJSON> atividadesFicha = new ArrayList<AtividadeFichaWriteJSON>();
    private List<ProgramaTreinoFichaJSON> programasFicha = new ArrayList<ProgramaTreinoFichaJSON>();
    private Boolean ativo = true;
    private NivelWriteJSON nivel;
    private SexoEnum sexo;

    public FichaWriteJSON() {
    }

    public FichaWriteJSON(Ficha f) {
        this.codigo = f.getCodigo();
        this.nome = f.getNome();
        this.mensagemAluno = f.getMensagemAluno();
        this.categoria = f.getCategoria() != null ? f.getCategoria().getCodigo() : null;
        this.categoriaFicha = f.getCategoria() != null ? f.getCategoria().getNome() : null;
        this.usarComoPredefinida = f.isUsarComoPredefinida();
        this.versao = f.getVersao();
        this.nivel = new NivelWriteJSON(f.getNivel());
        this.sexo = f.getSexo() != null ? f.getSexo() : SexoEnum.N;
        List<AtividadeFicha> listaAtividades = f.getAtividades();
        if (listaAtividades != null) {
            for (AtividadeFicha af : listaAtividades) {
                AtividadeFichaWriteJSON atvFichaJ = new AtividadeFichaWriteJSON();
                atvFichaJ.setCodigo(af.getCodigo());
                atvFichaJ.setAtividade(af.getAtividade().getCodigo());
                atvFichaJ.setNomeAtividade(af.getAtividade().getNome());
                atvFichaJ.setIdIA(af.getAtividade().getIdIA());
                atvFichaJ.setFicha(af.getFicha().getCodigo());
                atvFichaJ.setOrdem(af.getOrdem());
                atvFichaJ.setTipoAtividade(af.getAtividade().getTipo().getId());
                atvFichaJ.setSetid(af.getSetId());
                if (af.getMetodoExecucao() != null) {
                    atvFichaJ.setMetodoExecucao(af.getMetodoExecucao().getId());
                }
                List<Serie> series = af.getSeries();
                if (series != null) {
                    for (Serie s : series) {
                        SerieWriteJSON sJ = new SerieWriteJSON();
                        sJ.setCadencia(s.getCadencia());
                        sJ.setAtividadeFicha(af.getCodigo());
                        sJ.setCarga(s.getCarga());
                        sJ.setCodigo(s.getCodigo());
                        sJ.setComplemento(s.getComplemento());
                        sJ.setDescanso(s.getDescanso());
                        sJ.setDistancia(s.getDistancia());
                        sJ.setDuracao(s.getDuracao());
                        sJ.setOrdem(s.getOrdem());
                        sJ.setRepeticao(s.getRepeticao());
                        sJ.setVelocidade(s.getVelocidade());
                        atvFichaJ.getSeries().add(sJ);
                    }
                }
                List<AtividadeFichaAjuste> ajustes = af.getAjustes();
                if (ajustes != null) {
                    for (AtividadeFichaAjuste aj : ajustes) {
                        AtividadeFichaAjusteWriteJSON ajJ = new AtividadeFichaAjusteWriteJSON();
                        ajJ.setCodigo(aj.getCodigo());
                        ajJ.setAtividadeFicha(aj.getAtividadeFicha().getCodigo());
                        ajJ.setNome(aj.getNome());
                        ajJ.setValor(aj.getValor());
                        atvFichaJ.getAjustes().add(ajJ);
                    }
                }
                this.atividadesFicha.add(atvFichaJ);
            }
        }
        List<ProgramaTreinoFicha> listaProgramas = f.getProgramas();
        if (listaProgramas != null) {
            for (ProgramaTreinoFicha ptf : listaProgramas) {
                ProgramaTreinoFichaJSON ptfJ = new ProgramaTreinoFichaJSON(ptf);
                this.programasFicha.add(ptfJ);
            }
        }
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getUsername() {
        if (username == null) {
            username = "";
        }
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getMensagemAluno() {
        return mensagemAluno;
    }

    public void setMensagemAluno(String mensagemAluno) {
        this.mensagemAluno = mensagemAluno;
    }

    public Integer getCategoria() {
        if (categoria == null) {
            categoria = 0;
        }
        return categoria;
    }

    public void setCategoria(Integer categoria) {
        this.categoria = categoria;
    }

    public Boolean getUsarComoPredefinida() {
        return usarComoPredefinida;
    }

    public void setUsarComoPredefinida(Boolean usarComoPredefinida) {
        this.usarComoPredefinida = usarComoPredefinida;
    }

    public Integer getVersao() {
        if (versao == null) {
            versao = 0;
        }
        return versao;
    }

    public void setVersao(Integer versao) {
        this.versao = versao;
    }

    public List<AtividadeFichaWriteJSON> getAtividadesFicha() {
        return atividadesFicha;
    }

    public void setAtividadesFicha(List<AtividadeFichaWriteJSON> atividadesFicha) {
        this.atividadesFicha = atividadesFicha;
    }

    public List<ProgramaTreinoFichaJSON> getProgramasFicha() {
        return programasFicha;
    }

    public void setProgramasFicha(List<ProgramaTreinoFichaJSON> programasFicha) {
        this.programasFicha = programasFicha;
    }

    public Boolean getAtivo() {
        return ativo;
    }

    public void setAtivo(Boolean ativo) {
        this.ativo = ativo;
    }

    public NivelWriteJSON getNivel() {
        return nivel;
    }

    public void setNivel(NivelWriteJSON nivel) {
        this.nivel = nivel;
    }

    public SexoEnum getSexo() {
        return sexo;
    }

    public void setSexo(SexoEnum sexo) {
        this.sexo = sexo;
    }

    public String getCategoriaFicha() {
        return categoriaFicha;
    }

    public void setCategoriaFicha(String categoriaFicha) {
        this.categoriaFicha = categoriaFicha;
    }
}
