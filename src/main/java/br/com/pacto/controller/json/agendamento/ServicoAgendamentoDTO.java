package br.com.pacto.controller.json.agendamento;

import br.com.pacto.bean.Agendamento;
import br.com.pacto.bean.colaborador.ColaboradorSimplesTO;
import br.com.pacto.controller.json.tipoEvento.TipoAgendamentoDTO;
import br.com.pacto.enumerador.agenda.StatusAgendamentoEnum;
import br.com.pacto.objeto.Calendario;
import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class ServicoAgendamentoDTO {

    private Integer id;
    private String data;
    private StatusAgendamentoEnum status;
    private TipoAgendamentoDTO tipoAgendamento;
    private String horarioInicial;
    private String horarioFinal;
    private Colaborador<PERSON>implesTO professor;
    private AlunoAgendamentoDTO aluno;
    private String observacao;

    public ServicoAgendamentoDTO(Agendamento agendamento, Boolean treinoIndependente) {
        this.id = agendamento.getCodigo();
        this.data = Calendario.getData(agendamento.getInicio(), "yyyyMMdd");
        this.status = agendamento.getStatus();
        this.tipoAgendamento = (agendamento.getTipoEvento() != null && agendamento.getTipoEvento().getCodigo() != null && agendamento.getTipoEvento().getCodigo() > 0) ?
                new TipoAgendamentoDTO(agendamento.getTipoEvento()) : new TipoAgendamentoDTO(agendamento.getHorarioDisponibilidade().getDisponibilidade());
        this.horarioInicial = agendamento.getHoraInicioApresentar();
        this.horarioFinal = agendamento.getHoraFimApresentar();
        this.professor = new ColaboradorSimplesTO(agendamento.getProfessor(), treinoIndependente);
        this.aluno = new AlunoAgendamentoDTO(agendamento.getCliente(), treinoIndependente);
        this.observacao = agendamento.getObservacao();
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public StatusAgendamentoEnum getStatus() {
        return status;
    }

    public void setStatus(StatusAgendamentoEnum status) {
        this.status = status;
    }

    public TipoAgendamentoDTO getTipoAgendamento() {
        return tipoAgendamento;
    }

    public void setTipoAgendamento(TipoAgendamentoDTO tipoAgendamento) {
        this.tipoAgendamento = tipoAgendamento;
    }

    public String getHorarioInicial() {
        return horarioInicial;
    }

    public void setHorarioInicial(String horarioInicial) {
        this.horarioInicial = horarioInicial;
    }

    public String getHorarioFinal() {
        return horarioFinal;
    }

    public void setHorarioFinal(String horarioFinal) {
        this.horarioFinal = horarioFinal;
    }

    public ColaboradorSimplesTO getProfessor() {
        return professor;
    }

    public void setProfessor(ColaboradorSimplesTO professor) {
        this.professor = professor;
    }

    public AlunoAgendamentoDTO getAluno() {
        return aluno;
    }

    public void setAluno(AlunoAgendamentoDTO aluno) {
        this.aluno = aluno;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }
}
