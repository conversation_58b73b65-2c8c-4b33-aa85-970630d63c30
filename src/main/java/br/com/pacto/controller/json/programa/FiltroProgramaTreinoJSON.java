package br.com.pacto.controller.json.programa;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import java.util.ArrayList;
import java.util.List;
@ApiModel(description = "Filtro Programa Treino")
public class FiltroProgramaTreinoJSON {

    @ApiModelProperty(value = "Parâmetro para a consulta", example = "Joao")
    private String parametro;
    @ApiModelProperty(value = "Informa se a consulta é para ser realizada pelo nome", example = "true")
    private Boolean nome;
    @ApiModelProperty(value = "Ativo", example = "0")
    private String ativo;
    @ApiModelProperty(value = "Gêneros", example = "[\"M\", \"F\"]")
    private List<String> generoList = new ArrayList<>();

    @ApiModelProperty(value = "Ordenação")
    private String ordenacao;
    public FiltroProgramaTreinoJSON(JSONObject filters) throws JSONException {

        if (filters != null) {

            this.parametro = filters.optString("quicksearchValue");

            JSONArray colunasVisiveis = filters.optJSONArray("quicksearchFields");

            if (colunasVisiveis != null) {
                for (int i = 0; i < colunasVisiveis.length(); i++) {
                    if (colunasVisiveis.get(i).equals("nome")) {
                        this.nome = true;
                    }
                }

                JSONArray situacao = filters.optJSONArray("situacaoPrograma");
                if (situacao != null) {
                    if( situacao.length() >1){
                        setAtivo(null);
                    }else{
                        setAtivo(situacao.getString(0));
                    }
                }

                JSONArray generoFilter = filters.optJSONArray("generoPrograma");
                if (generoFilter != null) {
                    for (int i = 0; i < generoFilter.length(); i++) {
                        generoList.add(generoFilter.getString(i));
                    }
                }

                setOrdenacao(filters.has("ordenacao") ? filters.get("ordenacao").toString() : "");
            }
        }
    }

    public String getParametro() {
        return parametro;
    }

    public void setParametro(String parametro) {
        this.parametro = parametro;
    }

    public Boolean getNome() {
        return nome;
    }

    public void setNome(Boolean nome) {
        this.nome = nome;
    }

    public String getAtivo() {
        return ativo;
    }

    public void setAtivo(String ativo) {
        this.ativo = ativo;
    }

    public List<String> getGeneroList() {
        return generoList;
    }

    public void setGeneroList(List<String> generoList) {
        this.generoList = generoList;
    }

    public String getOrdenacao() {
        return ordenacao;
    }

    public void setOrdenacao(String ordenacao) {
        this.ordenacao = ordenacao;
    }
}
