package br.com.pacto.controller.json.atividade.write;

public class SerieWriteAppJSON {
    private Integer codigo;
    private Integer atividadeFicha;
    private Integer repeticao = 0;
    private String repeticaoComp;
    private Double carga = 0.0;//gramas
    private String cargaComp;
    private Integer duracao = 0;//minutos
    private Integer distancia = 0;//metros
    private Double velocidade = 0.0;//km/h
    private String complemento;
    private Integer descanso = 0;//segundos
    private Integer ordem = null;
    private String cadencia;
    private String cargaApp;
    private String repeticaoApp;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getAtividadeFicha() {
        return atividadeFicha;
    }

    public void setAtividadeFicha(Integer atividadeFicha) {
        this.atividadeFicha = atividadeFicha;
    }

    public Integer getRepeticao() {
        return repeticao;
    }

    public void setRepeticao(Integer repeticao) {
        this.repeticao = repeticao;
    }

    public Double getCarga() {
        return carga;
    }

    public void setCarga(Double carga) {
        this.carga = carga;
    }

    public Integer getDuracao() {
        return duracao;
    }

    public void setDuracao(Integer duracao) {
        this.duracao = duracao;
    }

    public Integer getDistancia() {
        return distancia;
    }

    public void setDistancia(Integer distancia) {
        this.distancia = distancia;
    }

    public Double getVelocidade() {
        return velocidade;
    }

    public void setVelocidade(Double velocidade) {
        this.velocidade = velocidade;
    }

    public String getComplemento() {
        if (complemento == null) {
            complemento = "";
        }
        return complemento;
    }

    public void setComplemento(String complemento) {
        this.complemento = complemento;
    }

    public Integer getDescanso() {
        return descanso;
    }

    public void setDescanso(Integer descanso) {
        this.descanso = descanso;
    }

    public Integer getOrdem() {
        return ordem;
    }

    public void setOrdem(Integer ordem) {
        this.ordem = ordem;
    }

    public String getRepeticaoComp() {
        if (repeticaoComp == null) {
            repeticaoComp = "";
        }
        return repeticaoComp;
    }

    public void setRepeticaoComp(String repeticaoComp) {
        this.repeticaoComp = repeticaoComp;
    }

    public String getCargaComp() {
        if (cargaComp == null) {
            cargaComp = "";
        }
        return cargaComp;
    }

    public void setCargaComp(String cargaComp) {
        this.cargaComp = cargaComp;
    }

    public String getCadencia() {
        return cadencia;
    }

    public void setCadencia(String cadencia) {
        this.cadencia = cadencia;
    }

    public String getCargaApp() {
        return cargaApp;
    }

    public void setCargaApp(String cargaApp) {
        this.cargaApp = cargaApp;
    }

    public String getRepeticaoApp() {
        return repeticaoApp;
    }

    public void setRepeticaoApp(String repeticaoApp) {
        this.repeticaoApp = repeticaoApp;
    }
}
