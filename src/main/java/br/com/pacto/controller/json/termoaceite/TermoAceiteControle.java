package br.com.pacto.controller.json.termoaceite;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.termoaceite.TermoAceiteService;
import br.com.pacto.util.UteisValidacao;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import springfox.documentation.annotations.ApiIgnore;

import java.util.logging.Level;
import java.util.logging.Logger;

@Controller
@RequestMapping("/psec/termo-aceite")
public class TermoAceiteControle {

    private TermoAceiteService service;

    @Autowired
    public TermoAceiteControle(TermoAceiteService service) {
        this.service = service;
    }

    @ResponseBody
    @RequestMapping(method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> buscaTermos() {
        try {
            return ResponseEntityFactory.ok(service.findAll());
        } catch (Exception e) {
            Logger.getLogger(TermoAceiteControle.class.getName()).log(Level.SEVERE, "Erro ao consultar termos de aceite", e);
            return ResponseEntityFactory.erroInterno("erro_obter_termo_aceite_app", e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/{codigo}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> buscaTermosPorCodigo(@PathVariable Integer codigo) {
        try {
            return ResponseEntityFactory.ok(service.encontraTermoPorCodigo(codigo));
        } catch (Exception e) {
            Logger.getLogger(TermoAceiteControle.class.getName()).log(Level.SEVERE, "Erro ao consultar termos de aceite", e);
            return ResponseEntityFactory.erroInterno("erro_obter_termo_aceite_app", e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/assinaturas", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> buscaTermosAssinados() {
        try {
            return ResponseEntityFactory.ok(service.findAllAssinados());
        } catch (Exception e) {
            Logger.getLogger(TermoAceiteControle.class.getName()).log(Level.SEVERE, "Erro ao consultar termos de aceite", e);
            return ResponseEntityFactory.erroInterno("erro_obter_termo_aceite_app", e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/assinaturas/{codigoMatricula}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)

    public ResponseEntity<EnvelopeRespostaDTO> buscaTermosAssinados(@PathVariable Integer codigoMatricula) {
        try {
            return ResponseEntityFactory.ok(service.findByCodigoMatricula(codigoMatricula));
        } catch (Exception e) {
            Logger.getLogger(TermoAceiteControle.class.getName()).log(Level.SEVERE, "Erro ao consultar termos de aceite", e);
            return ResponseEntityFactory.erroInterno("erro_obter_termo_aceite_app", e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> save(@RequestParam String termo) {
        try {
            if (UteisValidacao.emptyString(termo)) {
                throw new Exception("A descrição do termo não pode ser vazia.");
            }
            service.save(termo);
            return ResponseEntityFactory.ok("Salvo com sucesso!");
        } catch (Exception e) {
            Logger.getLogger(TermoAceiteControle.class.getName()).log(Level.SEVERE, "Erro ao consultar termos de aceite", e);
            return ResponseEntityFactory.erroInterno("erro_obter_termo_aceite_app", e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/assinatura", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> saveAssinatura(@RequestParam String ip,
                                                              @RequestParam String nome,
                                                              @RequestParam (required = false) String cpf,
                                                              @RequestParam String data,
                                                              @RequestParam Integer termo,
                                                              @RequestParam (required = false) String email,
                                                              @RequestParam Integer codigoMatricula) {
        try {
            if (UteisValidacao.emptyString(ip) || UteisValidacao.emptyString(nome) || UteisValidacao.emptyString(data)) {
                throw new ServiceException("Nenhum dos parâmetros obritatórios (nome, ip, data e código de matrícula) pode ser vazio.");
            }
            service.saveAssinatura(ip, nome, cpf, data, termo, email, codigoMatricula);
            return ResponseEntityFactory.ok("Salvo com sucesso!");
        } catch (Exception e) {
            Logger.getLogger(TermoAceiteControle.class.getName()).log(Level.SEVERE, "Erro ao consultar termos de aceite", e);
            return ResponseEntityFactory.erroInterno("erro_obter_termo_aceite_app", e.getMessage());
        }
    }

    @ApiIgnore
    @ResponseBody
    @RequestMapping(value = "/dd5047f18244c2a7b7a6d8c83c85999c", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> saveAssinaturaCriptografado(@RequestBody String content) {
        try {
            JSONObject json = new JSONObject(Uteis.decryptUserData(content));

            String ip = json.optString("ip");
            String nome = json.optString("nome");
            String cpf = json.optString("cpf");
            String data = json.optString("data");
            Integer termo = json.optInt("termo");
            String email = json.optString("email");
            Integer codigoMatricula = json.optInt("codigoMatricula");
            String time = json.optString("time");

            if (UteisValidacao.emptyString(ip) || UteisValidacao.emptyString(nome)
                    || UteisValidacao.emptyString(data) || UteisValidacao.emptyString(time) || UteisValidacao.emptyNumber(termo) || UteisValidacao.emptyNumber(codigoMatricula)) {
                throw new ServiceException("Parâmetros obrigatórios (nome, ip, data, time, matricula, termo) não podem estar vazios.");
            }

            service.saveAssinatura(ip, nome, cpf, data, termo, email, codigoMatricula);

            JSONObject resposta = new JSONObject();
            resposta.put("mensagem", "Salvo com sucesso!");

            String encryptedResponse = Uteis.encryptUserData(resposta.toString());
            return ResponseEntityFactory.ok(encryptedResponse);
        } catch (Exception e) {
            Logger.getLogger(TermoAceiteControle.class.getName())
                    .log(Level.SEVERE, "Erro ao salvar assinatura", e);

            JSONObject erro = new JSONObject();
            try {
                erro.put("erro", e.getMessage());
            } catch (Exception ignored) {
            }

            String encryptedError;
            try {
                encryptedError = Uteis.encryptUserData(erro.toString());
            } catch (Exception encryptionException) {
                encryptedError = "{\"erro\": \"Erro interno ao criptografar\"}";
            }

            return ResponseEntityFactory.erroInterno("erro_salvar_assinatura", encryptedError);
        }
    }

}
