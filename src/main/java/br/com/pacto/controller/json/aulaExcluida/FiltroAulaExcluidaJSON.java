package br.com.pacto.controller.json.aulaExcluida;

import br.com.pacto.controller.json.base.SuperJSON;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.notificacao.excecao.AulaExcluidaExcecoes;
import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class FiltroAulaExcluidaJSON extends SuperJSON {

    private Date inicio;
    private Date fim;
    private Integer professorId;
    private List<Integer> professoresIds;

    public FiltroAulaExcluidaJSON(JSONObject filters) throws ServiceException {
        try {
        if (filters != null) {

            this.inicio = filters.optLong("dataInicio") == 0 ? null : new Date(filters.optLong("dataInicio"));
            this.fim = filters.optLong("dataFim") == 0 ? null : new Date(filters.optLong("dataFim"));

            JSONArray professores = filters.optJSONArray("professoresIds");
            this.professoresIds = new ArrayList<>();
            if (professores != null) {
                for (int i = 0; i < professores.length(); i++) {
                    getProfessoresIds().add(professores.getInt(i));
                }
            }
            this.professoresIds = getProfessoresIds();
            this.professorId =  filters.optInt("professorId" ) == 0 ? null : filters.optInt("professorId");

        }
        } catch (Exception ex) {
            throw new ServiceException(AulaExcluidaExcecoes.ERRO_CRIAR_FILTRO);
        }
    }

    public Date getInicio() {
        return inicio;
    }

    public void setInicio(Date inicio) {
        this.inicio = inicio;
    }

    public Date getFim() {
        return fim;
    }

    public void setFim(Date fim) {
        this.fim = fim;
    }

    public List<Integer> getProfessoresIds() { return professoresIds; }

    public void setProfessoresIds(List<Integer> professoresIds) {
        this.professoresIds = professoresIds;
    }

    public Integer getProfessorId() {
        return professorId;
    }

    public void setProfessorId(Integer professorId) {
        this.professorId = professorId;
    }
}
