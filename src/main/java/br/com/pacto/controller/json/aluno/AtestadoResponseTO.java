package br.com.pacto.controller.json.aluno;

import com.fasterxml.jackson.annotation.JsonInclude;
import servicos.integracao.zw.json.AtestadoClienteJSON;

import java.util.Date;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class AtestadoResponseTO {

    private Integer id;
    private String descricao;
    private Date dataInicio;
    private Date dataFinal;
    private String observacao;

    private Boolean parq;
    private String nomeArquivo;
    private String urlAnexo;

    public AtestadoResponseTO (AtestadoClienteJSON atestadoClienteJSON){
        this.id = atestadoClienteJSON.getCodAtestado();
        this.dataInicio = atestadoClienteJSON.getDataInicio();
        this.dataFinal = atestadoClienteJSON.getDataFinal() ;
        this.descricao = atestadoClienteJSON.getDescricao();
        this.observacao = atestadoClienteJSON.getObservacao();
        this.parq = atestadoClienteJSON.getParq();
        if(atestadoClienteJSON.getExtensao() != null && !atestadoClienteJSON.getExtensao().isEmpty()) {
            this.nomeArquivo = atestadoClienteJSON.getNomeArquivoGerado();
        }
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Date getDataFinal() {
        return dataFinal;
    }

    public void setDataFinal(Date dataFinal) {
        this.dataFinal = dataFinal;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Boolean getParq() {
        return parq;
    }

    public void setParq(Boolean parq) {
        this.parq = parq;
    }

    public String getNomeArquivo() {
        return nomeArquivo;
    }

    public void setNomeArquivo(String nomeArquivo) {
        this.nomeArquivo = nomeArquivo;
    }

    public String getUrlAnexo() {
        return urlAnexo;
    }

    public void setUrlAnexo(String urlAnexo) {
        this.urlAnexo = urlAnexo;
    }
}
