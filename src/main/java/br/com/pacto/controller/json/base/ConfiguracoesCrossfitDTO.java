package br.com.pacto.controller.json.base;

import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class ConfiguracoesCrossfitDTO {

    private String produto_inscricao_game;
    private String troca_nomenclatura_crossfit;

    public String getProduto_inscricao_game() {
        return produto_inscricao_game;
    }

    public void setProduto_inscricao_game(String produto_inscricao_game) {
        this.produto_inscricao_game = produto_inscricao_game;
    }

    public String getTroca_nomenclatura_crossfit() {
        return troca_nomenclatura_crossfit;
    }

    public void setTroca_nomenclatura_crossfit(String troca_nomenclatura_crossfit) {
        this.troca_nomenclatura_crossfit = troca_nomenclatura_crossfit;
    }
}
