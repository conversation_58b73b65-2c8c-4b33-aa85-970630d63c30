package br.com.pacto.controller.json.base;

import br.com.pacto.util.UteisValidacao;
import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class ConfiguracoesTreinoDTO {

    private String emitir_ficha_apos_vencimento_treino;
    private String numero_impressao_ficha;
    private String bloquear_impressao_ficha_apos_todas_execucoes;
    private String dias_antes_vencimento;
    private String agrupamento_series_set;
    private String permitir_apenas_alunos_ativos;
    private String visualizar_mensagem_aviso;
    private String permitir_visualizar_wod;
    private String permitir_visualizar_cref;
    private String permitir_visualizar_par_q_10_perguntas;
    private String permitir_visualizar_lei_parq;
    private String permitir_visualizar_aviso_de_pendencias;
    private String forcar_criar_novo_programa;

    public Boolean getAgrupamento_series_set() {
        if (!UteisValidacao.emptyString(agrupamento_series_set)) {
            return agrupamento_series_set.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setAgrupamento_series_set(String agrupamento_series_set) {
        this.agrupamento_series_set = agrupamento_series_set;
    }

    public Boolean getEmitir_ficha_apos_vencimento_treino() {
        if (!UteisValidacao.emptyString(emitir_ficha_apos_vencimento_treino)) {
            return emitir_ficha_apos_vencimento_treino.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setEmitir_ficha_apos_vencimento_treino(String emitir_ficha_apos_vencimento_treino) {
        this.emitir_ficha_apos_vencimento_treino = emitir_ficha_apos_vencimento_treino;
    }

    public String getNumero_impressao_ficha() {
        return numero_impressao_ficha;
    }

    public void setNumero_impressao_ficha(String numero_impressao_ficha) {
        this.numero_impressao_ficha = numero_impressao_ficha;
    }

    public Boolean getBloquear_impressao_ficha_apos_todas_execucoes() {
        if (!UteisValidacao.emptyString(bloquear_impressao_ficha_apos_todas_execucoes)) {
            return bloquear_impressao_ficha_apos_todas_execucoes.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setBloquear_impressao_ficha_apos_todas_execucoes(String bloquear_impressao_ficha_apos_todas_execucoes) {
        this.bloquear_impressao_ficha_apos_todas_execucoes = bloquear_impressao_ficha_apos_todas_execucoes;
    }

    public String getDias_antes_vencimento() {
        return dias_antes_vencimento;
    }

    public void setDias_antes_vencimento(String dias_antes_vencimento) {
        this.dias_antes_vencimento = dias_antes_vencimento;
    }

    public Boolean getPermitir_apenas_alunos_ativos() {
        if (!UteisValidacao.emptyString(permitir_apenas_alunos_ativos)) {
            return permitir_apenas_alunos_ativos.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setPermitir_apenas_alunos_ativos(String permitir_apenas_alunos_ativos) {
        this.permitir_apenas_alunos_ativos = permitir_apenas_alunos_ativos;
    }

    public Boolean getVisualizar_mensagem_aviso() {
        if (!UteisValidacao.emptyString(visualizar_mensagem_aviso)) {
            return visualizar_mensagem_aviso.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setVisualizar_mensagem_aviso(String visualizar_mensagem_aviso) {
        this.visualizar_mensagem_aviso = visualizar_mensagem_aviso;
    }

    public Boolean getPermitir_visualizar_wod() {
        if (!UteisValidacao.emptyString(permitir_visualizar_wod)) {
            return permitir_visualizar_wod.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setPermitir_visualizar_wod(String permitir_visualizar_wod) {
        this.permitir_visualizar_wod = permitir_visualizar_wod;
    }

    public Boolean getPermitir_visualizar_cref() {
        if (!UteisValidacao.emptyString(permitir_visualizar_cref)) {
            return permitir_visualizar_cref.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setPermitir_visualizar_cref(String permitir_visualizar_cref) {
        this.permitir_visualizar_cref = permitir_visualizar_cref;
    }

    public Boolean getPermitir_visualizar_par_q_10_perguntas() {
        if (!UteisValidacao.emptyString(permitir_visualizar_par_q_10_perguntas)) {
            return permitir_visualizar_par_q_10_perguntas.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setPermitir_visualizar_par_q_10_perguntas(String permitir_visualizar_par_q_10_perguntas) {
        this.permitir_visualizar_par_q_10_perguntas = permitir_visualizar_par_q_10_perguntas;
    }

    public Boolean getPermitir_visualizar_aviso_de_pendencias() {
        if (!UteisValidacao.emptyString(permitir_visualizar_aviso_de_pendencias)) {
            return permitir_visualizar_aviso_de_pendencias.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setPermitir_visualizar_aviso_de_pendencias(String permitir_visualizar_aviso_de_pendencias) {
        this.permitir_visualizar_aviso_de_pendencias = permitir_visualizar_aviso_de_pendencias;
    }

    public Boolean getForcar_criar_novo_programa() {
        if (!UteisValidacao.emptyString(forcar_criar_novo_programa)) {
            return forcar_criar_novo_programa.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setForcar_criar_novo_programa(String forcar_criar_novo_programa) {
        this.forcar_criar_novo_programa = forcar_criar_novo_programa;
    }

    public Boolean getPermitir_visualizar_lei_parq() {
        if (!UteisValidacao.emptyString(permitir_visualizar_lei_parq)) {
            return permitir_visualizar_lei_parq.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setPermitir_visualizar_lei_parq(String permitir_visualizar_lei_parq) {
        this.permitir_visualizar_lei_parq = permitir_visualizar_lei_parq;
    }
}
