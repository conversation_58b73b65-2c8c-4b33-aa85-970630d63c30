package br.com.pacto.controller.json.avaliacao;

import java.util.List;

public class PerguntaJSON {

    private List<RespostaPerguntaJSON> respostas;
    private int codigo;
    private String tipoPergunta;
    private String descricao;

    public List<RespostaPerguntaJSON> getRespostas() {
        return respostas;
    }

    public void setRespostas(List<RespostaPerguntaJSON> respostas) {
        this.respostas = respostas;
    }

    public int getCodigo() {
        return codigo;
    }

    public void setCodigo(int codigo) {
        this.codigo = codigo;
    }

    public String getTipoPergunta() {
        return tipoPergunta;
    }

    public void setTipoPergunta(String tipoPergunta) {
        this.tipoPergunta = tipoPergunta;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

}
