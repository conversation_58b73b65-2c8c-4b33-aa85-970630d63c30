package br.com.pacto.controller.json.benchmark;

import br.com.pacto.controller.json.base.SuperJSON;
import br.com.pacto.enumerador.crossfit.TipoWodEnum;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

public class FiltroBenchmarkJSON extends SuperJSON {

    private String parametro;
    private Boolean nome = false;
    private List<Integer> tipoBenchmark = new ArrayList<>();
    private List<TipoWodEnum> tipoExercicios = new ArrayList<>();


    public FiltroBenchmarkJSON(JSONObject filters) throws JSONException {

        if (filters != null) {

            this.parametro = filters.optString("quicksearchValue");

            JSONArray colunasVisiveis = filters.optJSONArray("quicksearchFields");

            if (colunasVisiveis != null) {
                for (int i = 0; i < colunasVisiveis.length(); i++) {
                    if (colunasVisiveis.get(i).equals("nome")) {
                        this.nome = true;
                    }
                }
            }

            JSONArray tiposBenchmark = filters.optJSONArray("tipoBenchmark");
            if (tiposBenchmark != null) {
                for (int i = 0; i < tiposBenchmark.length(); i++) {
                    getTipoBenchmark().add(Integer.parseInt(tiposBenchmark.get(i).toString()));
                }
            }

            JSONArray tipoExercicios = filters.optJSONArray("tipoExercicios");
            if (tipoExercicios != null) {
                for (int i = 0; i < tipoExercicios.length(); i++) {
                    getTipoExercicios().add(TipoWodEnum.valueOf(tipoExercicios.get(i).toString()));
                }
            }

        }

    }

    public String getParametro() {
        return parametro;
    }

    public void setParametro(String parametro) {
        this.parametro = parametro;
    }

    public Boolean getNome() {
        return nome;
    }

    public void setNome(Boolean nome) {
        this.nome = nome;
    }

    public List<Integer> getTipoBenchmark() {
        return tipoBenchmark;
    }

    public void setTipoBenchmark(List<Integer> tipoBenchmark) {
        this.tipoBenchmark = tipoBenchmark;
    }

    public List<TipoWodEnum> getTipoExercicios() {
        return tipoExercicios;
    }

    public void setTipoExercicios(List<TipoWodEnum> tipoExercicios) {
        this.tipoExercicios = tipoExercicios;
    }
}
