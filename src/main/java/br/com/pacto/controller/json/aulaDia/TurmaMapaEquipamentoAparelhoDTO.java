package br.com.pacto.controller.json.aulaDia;

import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class TurmaMapaEquipamentoAparelhoDTO {
    private Integer codigo;
    private Integer codigo_aparelhotreino;
    private Integer turma;
    private String mapaequipamento;

    public TurmaMapaEquipamentoAparelhoDTO() { }

    public TurmaMapaEquipamentoAparelhoDTO(Integer codigo, Integer codigo_aparelhotreino, Integer turma, String mapaequipamento) {
        this.codigo = codigo;
        this.codigo_aparelhotreino = codigo_aparelhotreino;
        this.turma = turma;
        this.mapaequipamento = mapaequipamento;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getCodigo_aparelhotreino() {
        return codigo_aparelhotreino;
    }

    public void setCodigo_aparelhotreino(Integer codigo_aparelhotreino) {
        this.codigo_aparelhotreino = codigo_aparelhotreino;
    }

    public Integer getTurma() {
        return turma;
    }

    public void setTurma(Integer turma) {
        this.turma = turma;
    }

    public String getMapaequipamento() {
        return mapaequipamento;
    }

    public void setMapaequipamento(String mapaequipamento) {
        this.mapaequipamento = mapaequipamento;
    }

}
