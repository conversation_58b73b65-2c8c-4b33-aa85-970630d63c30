package br.com.pacto.controller.json.programa;

import br.com.pacto.bean.atividade.AtividadeNivel;
import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * Created by ulisses on 27/08/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AtividadeNivelResponseTO {

    private Integer id;
    private String nome;

    public AtividadeNivelResponseTO() {

    }

    public AtividadeNivelResponseTO(AtividadeNivel an) {
        this.id = an.getNivel().getCodigo();
        this.nome = an.getNivel().getNome();
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }
}
