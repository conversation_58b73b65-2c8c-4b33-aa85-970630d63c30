package br.com.pacto.controller.json.base;

import br.com.pacto.util.UteisValidacao;
import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class ConfiguracoesAulasDTO {

    private String controlar_por_freepass;
    private String nr_aula_experimental_aluno;
    private String minutos_agendar_com_antecedencia;
    private String minutos_desmarcar_com_antecedencia;
    private String minutos_alterar_equipamento_com_antecedencia;
    private String validar_modalidade;
    private String validar_horario_contrato;
    private String permitir_aluno_marcar_aula_por_tipo_modalidade;
    private String bloquear_mesmo_ambiente;
    private String bloquear_aula_coletiva_nao_pertence_modalidade;
    private String desmarcar_aulas_futuras_parcela_atrasada;
    private String manter_reposicao_aula_coletiva;
    private String limite_dias_reposicao_aula_coletiva;
    private String nr_validar_vezes_modalidade;

    private String qtd_faltas_bloqueio_aluno;
    private String qtd_tempo_bloqueio_aluno;

    private String descontar_credito_ao_marcar_aula_sem_confirmar_presenca;
    private String proibir_marcar_aula_antes_pagamento_primeira_parcela;
    private String bloquear_gerar_reposicao_aula_ja_reposta;
    private String utilizar_numeracao_sequencial_identificador_equipamento;

    public String getNr_aula_experimental_aluno() {
        return nr_aula_experimental_aluno;
    }

    public void setNr_aula_experimental_aluno(String nr_aula_experimental_aluno) {
        this.nr_aula_experimental_aluno = nr_aula_experimental_aluno;
    }

    public String getMinutos_agendar_com_antecedencia() {
        return minutos_agendar_com_antecedencia;
    }

    public void setMinutos_agendar_com_antecedencia(String minutos_agendar_com_antecedencia) {
        this.minutos_agendar_com_antecedencia = minutos_agendar_com_antecedencia;
    }

    public String getMinutos_desmarcar_com_antecedencia() {
        return minutos_desmarcar_com_antecedencia;
    }

    public void setMinutos_desmarcar_com_antecedencia(String minutos_desmarcar_com_antecedencia) {
        this.minutos_desmarcar_com_antecedencia = minutos_desmarcar_com_antecedencia;
    }

    public Boolean getValidar_modalidade() {
        if (!UteisValidacao.emptyString(validar_modalidade)) {
            return validar_modalidade.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setValidar_modalidade(String validar_modalidade) {
        this.validar_modalidade = validar_modalidade;
    }

    public Boolean getValidar_horario_contrato() {
        if (!UteisValidacao.emptyString(validar_horario_contrato)) {
            return validar_horario_contrato.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setValidar_horario_contrato(String validar_horario_contrato) {
        this.validar_horario_contrato = validar_horario_contrato;
    }

    public Boolean getControlar_por_freepass() {
        if (!UteisValidacao.emptyString(controlar_por_freepass)) {
            return controlar_por_freepass.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setControlar_por_freepass(String controlar_por_freepass) {
        this.controlar_por_freepass = controlar_por_freepass;
    }

    public Boolean getPermitir_aluno_marcar_aula_por_tipo_modalidade() {
        if (!UteisValidacao.emptyString(permitir_aluno_marcar_aula_por_tipo_modalidade)) {
            return permitir_aluno_marcar_aula_por_tipo_modalidade.trim().equals("true");
        } else {
            return false;
        }
    }

    public Boolean getBloquear_mesmo_ambiente() {
        if (!UteisValidacao.emptyString(bloquear_mesmo_ambiente)) {
            return bloquear_mesmo_ambiente.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setPermitir_aluno_marcar_aula_por_tipo_modalidade(String permitir_aluno_marcar_aula_por_tipo_modalidade) {
        this.permitir_aluno_marcar_aula_por_tipo_modalidade = permitir_aluno_marcar_aula_por_tipo_modalidade;
    }

    public void setBloquear_mesmo_ambiente(String bloquear_mesmo_ambiente) {
        this.bloquear_mesmo_ambiente = bloquear_mesmo_ambiente;
    }

    public Boolean getBloquear_aula_coletiva_nao_pertence_modalidade() {
        if (!UteisValidacao.emptyString(bloquear_aula_coletiva_nao_pertence_modalidade)) {
            return bloquear_aula_coletiva_nao_pertence_modalidade.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setBloquear_aula_coletiva_nao_pertence_modalidade(String bloquear_aula_coletiva_nao_pertence_modalidade) {
        this.bloquear_aula_coletiva_nao_pertence_modalidade = bloquear_aula_coletiva_nao_pertence_modalidade;
    }

    public String getDesmarcar_aulas_futuras_parcela_atrasada() {
        return desmarcar_aulas_futuras_parcela_atrasada;
    }

    public void setDesmarcar_aulas_futuras_parcela_atrasada(String desmarcar_aulas_futuras_parcela_atrasada) {
        this.desmarcar_aulas_futuras_parcela_atrasada = desmarcar_aulas_futuras_parcela_atrasada;
    }

    public Boolean getManter_reposicao_aula_coletiva() {
        if (!UteisValidacao.emptyString(manter_reposicao_aula_coletiva)) {
            return manter_reposicao_aula_coletiva.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setManter_reposicao_aula_coletiva(String manter_reposicao_aula_coletiva) {
        this.manter_reposicao_aula_coletiva = manter_reposicao_aula_coletiva;
    }

    public String getLimite_dias_reposicao_aula_coletiva() {
        return limite_dias_reposicao_aula_coletiva;
    }

    public void setLimite_dias_reposicao_aula_coletiva(String limite_dias_reposicao_aula_coletiva) {
        this.limite_dias_reposicao_aula_coletiva = limite_dias_reposicao_aula_coletiva;
    }

    public String getNr_validar_vezes_modalidade() {
        return nr_validar_vezes_modalidade;
    }

    public void setNr_validar_vezes_modalidade(String nr_validar_vezes_modalidade) {
        this.nr_validar_vezes_modalidade = nr_validar_vezes_modalidade;
    }

    public String getQtd_faltas_bloqueio_aluno() {
        return qtd_faltas_bloqueio_aluno;
    }

    public void setQtd_faltas_bloqueio_aluno(String qtd_faltas_bloqueio_aluno) {
        this.qtd_faltas_bloqueio_aluno = qtd_faltas_bloqueio_aluno;
    }

    public String getQtd_tempo_bloqueio_aluno() {
        return qtd_tempo_bloqueio_aluno;
    }

    public void setQtd_tempo_bloqueio_aluno(String qtd_tempo_bloqueio_aluno) {
        this.qtd_tempo_bloqueio_aluno = qtd_tempo_bloqueio_aluno;
    }


    public Boolean getDescontar_credito_ao_marcar_aula_sem_confirmar_presenca() {
        if (!UteisValidacao.emptyString(descontar_credito_ao_marcar_aula_sem_confirmar_presenca)) {
            return descontar_credito_ao_marcar_aula_sem_confirmar_presenca.trim().equals("true");
        }
        return false;
    }

    public void setDescontar_credito_ao_marcar_aula_sem_confirmar_presenca(String descontar_credito_ao_marcar_aula_sem_confirmar_presenca) {
        this.descontar_credito_ao_marcar_aula_sem_confirmar_presenca = descontar_credito_ao_marcar_aula_sem_confirmar_presenca;
    }

    public Boolean getProibir_marcar_aula_antes_pagamento_primeira_parcela() {
        if (!UteisValidacao.emptyString(proibir_marcar_aula_antes_pagamento_primeira_parcela)) {
            return proibir_marcar_aula_antes_pagamento_primeira_parcela.trim().equals("true");
        }
        return false;
    }

    public void setProibir_marcar_aula_antes_pagamento_primeira_parcela(String proibir_marcar_aula_antes_pagamento_primeira_parcela) {
        this.proibir_marcar_aula_antes_pagamento_primeira_parcela = proibir_marcar_aula_antes_pagamento_primeira_parcela;
    }

    public Boolean getBloquear_gerar_reposicao_aula_ja_reposta() {
        if (!UteisValidacao.emptyString(bloquear_gerar_reposicao_aula_ja_reposta)) {
            return bloquear_gerar_reposicao_aula_ja_reposta.trim().equals("true");
        }
        return false;
    }

    public void setBloquear_gerar_reposicao_aula_ja_reposta(String bloquear_gerar_reposicao_aula_ja_reposta) {
        this.bloquear_gerar_reposicao_aula_ja_reposta = bloquear_gerar_reposicao_aula_ja_reposta;
    }

    public Boolean getUtilizar_numeracao_sequencial_identificador_equipamento() {
        if (!UteisValidacao.emptyString(utilizar_numeracao_sequencial_identificador_equipamento)) {
            return utilizar_numeracao_sequencial_identificador_equipamento.trim().equals("true");
        }
        return false;
    }

    public void setUtilizar_numeracao_sequencial_identificador_equipamento(String utilizar_numeracao_sequencial_identificador_equipamento) {
        this.utilizar_numeracao_sequencial_identificador_equipamento = utilizar_numeracao_sequencial_identificador_equipamento;
    }

    public String getMinutos_alterar_equipamento_com_antecedencia() {
        return minutos_alterar_equipamento_com_antecedencia;
    }

    public void setMinutos_alterar_equipamento_com_antecedencia(String minutos_alterar_equipamento_com_antecedencia) {
        this.minutos_alterar_equipamento_com_antecedencia = minutos_alterar_equipamento_com_antecedencia;
    }
}

