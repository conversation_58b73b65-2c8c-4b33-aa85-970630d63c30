package br.com.pacto.controller.json.disponibilidade;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

public class DisponibilidadeDTO implements Serializable {

    private Integer codigo;
    private String nome;
    private String cor;
    private Integer intervaloDiasEntreAgendamentos;
    private Integer intervaloMinimoDiasCasoFalta;
    private Integer comportamento;
    private List<HorarioDisponibilidadeDTO> horarios;
    private List<ItemValidacaoDisponibilidadeDTO> itensValidacao;
    private Integer tipoHorario;
    private Integer duracao;
    private Integer duracaoMinima;
    private Integer duracaoMaxima;
    private Integer tipoValidacao;
    private Date dataInicial;
    private Date dataFinal;
    private String diasSemana;
    private String descricao;
    private String base64Imagem;
    private String formControlNomeImagem;
    private String urlImagem;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getCor() {
        return cor;
    }

    public void setCor(String cor) {
        this.cor = cor;
    }

    public Integer getIntervaloDiasEntreAgendamentos() {
        return intervaloDiasEntreAgendamentos;
    }

    public void setIntervaloDiasEntreAgendamentos(Integer intervaloDiasEntreAgendamentos) {
        this.intervaloDiasEntreAgendamentos = intervaloDiasEntreAgendamentos;
    }

    public Integer getIntervaloMinimoDiasCasoFalta() {
        return intervaloMinimoDiasCasoFalta;
    }

    public void setIntervaloMinimoDiasCasoFalta(Integer intervaloMinimoDiasCasoFalta) {
        this.intervaloMinimoDiasCasoFalta = intervaloMinimoDiasCasoFalta;
    }

    public Integer getComportamento() {
        return comportamento;
    }

    public void setComportamento(Integer comportamento) {
        this.comportamento = comportamento;
    }

    public List<ItemValidacaoDisponibilidadeDTO> getItensValidacao() {
        return itensValidacao;
    }

    public void setItensValidacao(List<ItemValidacaoDisponibilidadeDTO> itensValidacao) {
        this.itensValidacao = itensValidacao;
    }

    public Integer getTipoHorario() {
        return tipoHorario;
    }

    public void setTipoHorario(Integer tipoHorario) {
        this.tipoHorario = tipoHorario;
    }

    public Integer getTipoValidacao() {
        return tipoValidacao;
    }

    public void setTipoValidacao(Integer tipoValidacao) {
        this.tipoValidacao = tipoValidacao;
    }

    public Date getDataInicial() {
        return dataInicial;
    }

    public void setDataInicial(Date dataInicial) {
        this.dataInicial = dataInicial;
    }

    public Date getDataFinal() {
        return dataFinal;
    }

    public void setDataFinal(Date dataFinal) {
        this.dataFinal = dataFinal;
    }

    public String getDiasSemana() {
        return diasSemana;
    }

    public void setDiasSemana(String diasSemana) {
        this.diasSemana = diasSemana;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getBase64Imagem() {
        return base64Imagem;
    }

    public void setBase64Imagem(String base64Imagem) {
        this.base64Imagem = base64Imagem;
    }

    public Integer getDuracao() {
        return duracao;
    }

    public void setDuracao(Integer duracao) {
        this.duracao = duracao;
    }

    public Integer getDuracaoMinima() {
        return duracaoMinima;
    }

    public void setDuracaoMinima(Integer duracaoMinima) {
        this.duracaoMinima = duracaoMinima;
    }

    public Integer getDuracaoMaxima() {
        return duracaoMaxima;
    }

    public void setDuracaoMaxima(Integer duracaoMaxima) {
        this.duracaoMaxima = duracaoMaxima;
    }

    public String getFormControlNomeImagem() {
        return formControlNomeImagem;
    }

    public void setFormControlNomeImagem(String formControlNomeImagem) {
        this.formControlNomeImagem = formControlNomeImagem;
    }

    public List<HorarioDisponibilidadeDTO> getHorarios() {
        return horarios;
    }

    public void setHorarios(List<HorarioDisponibilidadeDTO> horarios) {
        this.horarios = horarios;
    }

    public String getUrlImagem() {
        return urlImagem;
    }

    public void setUrlImagem(String urlImagem) {
        this.urlImagem = urlImagem;
    }
}
