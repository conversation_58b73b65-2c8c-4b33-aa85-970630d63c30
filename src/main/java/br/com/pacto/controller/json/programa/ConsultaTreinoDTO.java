package br.com.pacto.controller.json.programa;

import br.com.pacto.bean.programa.ProgramaTreino;
import br.com.pacto.controller.json.programa.read.ProgramaTreinoJSON;

import java.util.List;

public class ConsultaTreinoDTO {
    private Integer codEmpresa;
    private List<ProgramaTreinoJSON> programasGerados;
    private String dataGerado;
    private String matricula;
    private String vaiSerAprovadoAutomaticamenteEm;
    private String nomeAluno;
    private String fotoAluno;
    private Long idAluno;
    private Integer configTempoMaxProfAprovar;
    private Integer configTempoMaxAprovAut;

    public Integer getCodEmpresa() {
        return codEmpresa;
    }

    public void setCodEmpresa(Integer codEmpresa) {
        this.codEmpresa = codEmpresa;
    }

    public List<ProgramaTreinoJSON> getProgramasGerados() {
        return programasGerados;
    }

    public void setProgramasGerados(List<ProgramaTreinoJSON> programasGerados) {
        this.programasGerados = programasGerados;
    }

    public String getDataGerado() {
        return dataGerado;
    }

    public void setDataGerado(String dataGerado) {
        this.dataGerado = dataGerado;
    }

    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public String getVaiSerAprovadoAutomaticamenteEm() {
        return vaiSerAprovadoAutomaticamenteEm;
    }

    public void setVaiSerAprovadoAutomaticamenteEm(String vaiSerAprovadoAutomaticamenteEm) {
        this.vaiSerAprovadoAutomaticamenteEm = vaiSerAprovadoAutomaticamenteEm;
    }

    public String getNomeAluno() {
        return nomeAluno;
    }

    public void setNomeAluno(String nomeAluno) {
        this.nomeAluno = nomeAluno;
    }

    public String getFotoAluno() {
        return fotoAluno;
    }

    public void setFotoAluno(String fotoAluno) {
        this.fotoAluno = fotoAluno;
    }

    public Long getIdAluno() {
        return idAluno;
    }

    public void setIdAluno(Long idAluno) {
        this.idAluno = idAluno;
    }

    public Integer getConfigTempoMaxProfAprovar() {
        return configTempoMaxProfAprovar;
    }

    public void setConfigTempoMaxProfAprovar(Integer configTempoMaxProfAprovar) {
        this.configTempoMaxProfAprovar = configTempoMaxProfAprovar;
    }

    public Integer getConfigTempoMaxAprovAut() {
        return configTempoMaxAprovAut;
    }

    public void setConfigTempoMaxAprovAut(Integer configTempoMaxAprovAut) {
        this.configTempoMaxAprovAut = configTempoMaxAprovAut;
    }
}
