package br.com.pacto.controller.json.programa.read;

public enum OrigemProgramaTreinoEnum {
    WEB(1),
    APP(2);

    private final int codigo;

    OrigemProgramaTreinoEnum(int codigo) {
        this.codigo = codigo;
    }

    public int getCodigo() {
        return codigo;
    }

    public static OrigemProgramaTreinoEnum fromCodigo(int codigo) {
        for (OrigemProgramaTreinoEnum origem : values()) {
            if (origem.codigo == codigo) {
                return origem;
            }
        }
        throw new IllegalArgumentException("Código de origem inválido: " + codigo);
    }
}
