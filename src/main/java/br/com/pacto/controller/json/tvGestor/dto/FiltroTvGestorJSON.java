package br.com.pacto.controller.json.tvGestor.dto;

import br.com.pacto.controller.json.base.SuperJSON;
import br.com.pacto.service.exception.ServiceException;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> 21/10/2019
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FiltroTvGestorJSON extends SuperJSON {

    private String parametro;
    private Boolean filtrarAluno;
    private Boolean filtrarTurma;
    private List<Integer> modalidadeIds;
    private List<Integer> professorIds;
    private List<Integer> ambienteIds;
    private Date data;
    private Integer hora;

    public FiltroTvGestorJSON() {
    }

    public FiltroTvGestorJSON(List<Integer> modalidadeIds, List<Integer> professorIds, List<Integer> ambienteIds) {
        this.modalidadeIds = modalidadeIds;
        this.professorIds = professorIds;
        this.ambienteIds = ambienteIds;
    }

    public FiltroTvGestorJSON(JSONObject filters) throws ServiceException {

        if (filters != null) {
            try {
                this.parametro = filters.optString("quicksearchValue");

                JSONArray camposAserFiltrado = filters.optJSONArray("quicksearchFields");
                if (camposAserFiltrado != null) {
                    for (int i = 0; i < camposAserFiltrado.length(); i++) {
                        if (camposAserFiltrado.get(i).equals("nomeAluno")) {
                            this.filtrarAluno = true;
                        } else if (camposAserFiltrado.get(i).equals("nomeTurma")) {
                            this.filtrarTurma = true;
                        }
                    }
                }
                JSONArray professorIds = filters.optJSONArray("professorIds");
                this.professorIds = new ArrayList<>();
                if (professorIds != null) {
                    for (int i = 0; i < professorIds.length(); i++) {
                        getProfessorIds().add(professorIds.getInt(i));
                    }
                }

                JSONArray modalidadeIds = filters.optJSONArray("modalidadeIds");
                this.modalidadeIds = new ArrayList<>();
                if (modalidadeIds != null) {
                    for (int i = 0; i < modalidadeIds.length(); i++) {
                        getModalidadeIds().add(modalidadeIds.getInt(i));
                    }
                }

                JSONArray ambienteIds = filters.optJSONArray("ambienteIds");
                this.ambienteIds = new ArrayList<>();
                if (ambienteIds != null) {
                    for (int i = 0; i < ambienteIds.length(); i++) {
                        getAmbienteIds().add(ambienteIds.getInt(i));
                    }
                }
                this.data = new Date(filters.optLong("data"));
                this.hora = filters.optInt("hora");
            } catch (JSONException e) {
                throw new ServiceException(e);
            }
        }
    }

    public String getParametro() {
        return parametro;
    }

    public void setParametro(String parametro) {
        this.parametro = parametro;
    }

    public Boolean getFiltrarAluno() {
        if (filtrarAluno == null) {
            filtrarAluno = false;
        }
        return filtrarAluno;
    }

    public void setFiltrarAluno(Boolean filtrarAluno) {
        this.filtrarAluno = filtrarAluno;
    }

    public Boolean getFiltrarTurma() {
        if (filtrarTurma == null) {
            filtrarTurma = false;
        }
        return filtrarTurma;
    }

    public void setFiltrarTurma(Boolean filtrarTurma) {
        this.filtrarTurma = filtrarTurma;
    }

    public List<Integer> getModalidadeIds() {
        if (modalidadeIds == null) {
            modalidadeIds = new ArrayList<>();
        }
        return modalidadeIds;
    }

    @JsonIgnore
    public List<String> getModalidadeIdsString() {
        List<String> modalidadeIdsString = new ArrayList<>();
        for(Integer modalidades : getModalidadeIds()){
            modalidadeIdsString.add(modalidades.toString());
        }
        return modalidadeIdsString;
    }

    public void setModalidadeIds(List<Integer> modalidadeIds) {
        this.modalidadeIds = modalidadeIds;
    }

    public List<Integer> getProfessorIds() {
        if (professorIds == null) {
            professorIds = new ArrayList<>();
        }
        return professorIds;
    }

    @JsonIgnore
    public List<String> getProfessorIdsString() {
        List<String> professorIdsString = new ArrayList<>();
        for(Integer professor : getProfessorIds()){
            professorIdsString.add(professor.toString());
        }
        return professorIdsString;
    }

    public void setProfessorIds(List<Integer> professorIds) {
        this.professorIds = professorIds;
    }

    public List<Integer> getAmbienteIds() {
        if (ambienteIds == null) {
            ambienteIds = new ArrayList<>();
        }
        return ambienteIds;
    }

    @JsonIgnore
    public List<String> getAmbienteIdsString() {
        List<String> ambienteIdsString = new ArrayList<>();
        for(Integer ambiente : getAmbienteIds()){
            ambienteIdsString.add(ambiente.toString());
        }
        return ambienteIdsString;
    }

    public void setAmbienteIds(List<Integer> ambienteIds) {
        this.ambienteIds = ambienteIds;
    }

    public Date getData() {
        return data;
    }

    public void setData(Date data) {
        this.data = data;
    }

    public Integer getHora() {
        return hora;
    }

    public void setHora(Integer hora) {
        this.hora = hora;
    }
}
