package br.com.pacto.controller.json.disponibilidade;

import br.com.pacto.controller.json.turma.AmbienteDTO;

import java.io.Serializable;

public class AmbienteDisponibilidadeDTO implements Serializable {

    private Integer codigo;
    private AmbienteDTO ambiente;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public AmbienteDTO getAmbiente() {
        return ambiente;
    }

    public void setAmbiente(AmbienteDTO ambiente) {
        this.ambiente = ambiente;
    }
}
