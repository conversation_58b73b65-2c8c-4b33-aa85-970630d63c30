package br.com.pacto.controller.json.aluno;

import br.com.pacto.bean.avaliacao.AvaliacaoFisica;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created paulo 06/11/2018
 */
public class AvaliacaoAlunoResponseDTO {

    private Integer avaliacaoId;
    private Integer totalAvaliacoes;
    private Integer periodo;
    private ComposicaoCorporalDTO composicaoCorporal;
    private String imc;
    private String altura;
    private String peso;
    private Double circunferenciaAbdominal;
    private String percentualMassaMagra;
    private String percentualMassaGorda;
    private Date ultimaAvaliacao;
    private Date proximaAvaliacao;
    private List<PesoXGorduraDTO> pesoXGordura;

    public AvaliacaoAlunoResponseDTO(AvaliacaoFisica avaliacaoFisica, int periodoAvaliacoes, int quantAvaliacoes, List<AvaliacaoFisica> listaAvaliacoes) {
        this.avaliacaoId = avaliacaoFisica.getCodigo();
        this.totalAvaliacoes = quantAvaliacoes;
        this.periodo = periodoAvaliacoes;
        this.composicaoCorporal = new ComposicaoCorporalDTO(avaliacaoFisica);
        this.imc = avaliacaoFisica.getImcApresentar();
        this.altura = avaliacaoFisica.getAlturaApresentar();
        this.peso = avaliacaoFisica.getPesoApresentar();
        this.circunferenciaAbdominal = avaliacaoFisica.getCircunferenciaAbdominal();
        this.percentualMassaMagra = avaliacaoFisica.getPercentualMassaMagraApresentar();
        this.percentualMassaGorda = avaliacaoFisica.getPercentualGorduraApresentar();
        this.ultimaAvaliacao = avaliacaoFisica.getDataAvaliacao();
        this.proximaAvaliacao = avaliacaoFisica.getDataProxima();
        this.pesoXGordura = listaAvaliacoes == null ? null : popularListaPesoXGordura(listaAvaliacoes);
    }

    private List<PesoXGorduraDTO> popularListaPesoXGordura(List<AvaliacaoFisica> avaliacoes) {
        List<PesoXGorduraDTO> listaReturn = new ArrayList<>();

        for (AvaliacaoFisica avaliacaoFisica : avaliacoes) {
            listaReturn.add(new PesoXGorduraDTO(avaliacaoFisica));
        }

        return listaReturn;
    }

    public Integer getAvaliacaoId() {
        return avaliacaoId;
    }

    public void setAvaliacaoId(Integer avaliacaoId) {
        this.avaliacaoId = avaliacaoId;
    }

    public Integer getTotalAvaliacoes() {
        return totalAvaliacoes;
    }

    public void setTotalAvaliacoes(Integer totalAvaliacoes) {
        this.totalAvaliacoes = totalAvaliacoes;
    }

    public Integer getPeriodo() {
        return periodo;
    }

    public void setPeriodo(Integer periodo) {
        this.periodo = periodo;
    }

    public ComposicaoCorporalDTO getComposicaoCorporal() {
        return composicaoCorporal;
    }

    public void setComposicaoCorporal(ComposicaoCorporalDTO composicaoCorporal) {
        this.composicaoCorporal = composicaoCorporal;
    }

    public String getImc() {
        return imc;
    }

    public void setImc(String imc) {
        this.imc = imc;
    }

    public String getAltura() {
        return altura;
    }

    public void setAltura(String altura) {
        this.altura = altura;
    }

    public String getPeso() {
        return peso;
    }

    public void setPeso(String peso) {
        this.peso = peso;
    }

    public Double getCircunferenciaAbdominal() {
        return circunferenciaAbdominal;
    }

    public void setCircunferenciaAbdominal(Double circunferenciaAbdominal) {
        this.circunferenciaAbdominal = circunferenciaAbdominal;
    }

    public String getPercentualMassaMagra() {
        return percentualMassaMagra;
    }

    public void setPercentualMassaMagra(String percentualMassaMagra) {
        this.percentualMassaMagra = percentualMassaMagra;
    }

    public String getPercentualMassaGorda() {
        return percentualMassaGorda;
    }

    public void setPercentualMassaGorda(String percentualMassaGorda) {
        this.percentualMassaGorda = percentualMassaGorda;
    }

    public Date getUltimaAvaliacao() {
        return ultimaAvaliacao;
    }

    public void setUltimaAvaliacao(Date ultimaAvaliacao) {
        this.ultimaAvaliacao = ultimaAvaliacao;
    }

    public Date getProximaAvaliacao() {
        return proximaAvaliacao;
    }

    public void setProximaAvaliacao(Date proximaAvaliacao) {
        this.proximaAvaliacao = proximaAvaliacao;
    }

    public List<PesoXGorduraDTO> getPesoXGordura() {
        return pesoXGordura;
    }

    public void setPesoXGordura(List<PesoXGorduraDTO> pesoXGordura) {
        this.pesoXGordura = pesoXGordura;
    }
}
