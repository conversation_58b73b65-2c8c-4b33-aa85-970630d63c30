package br.com.pacto.controller.json.colaborador;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.bi.DashboardBI;
import br.com.pacto.bean.colaborador.ColaboradorTO;
import br.com.pacto.bean.colaborador.FiltroColaboradorJSON;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.bean.programa.ProfessorResponseTO;
import br.com.pacto.security.aspecto.Permissao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.professor.ProfessorSinteticoService;
import br.com.pacto.util.UteisValidacao;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by Joao Moita on 26/09/2018.
 */

@Controller
@RequestMapping("/psec/colaboradores")
public class ColaboradorController {

    private final ProfessorSinteticoService professorSinteticoService;

    @Autowired
    public ColaboradorController (ProfessorSinteticoService professorSinteticoService) {
        Assert.notNull(professorSinteticoService, "O serviço de professor sintético não foi injetado corretamente");
        this.professorSinteticoService = professorSinteticoService;
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.COLABORADORES)
    @RequestMapping( method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listaColaborador(
            @RequestParam(value = "filters", required = false) JSONObject filters,
            PaginadorDTO paginadorDTO,
            @RequestHeader(value = "empresaId", required = true) Integer empresaId,
            @RequestParam(value = "todosTipos", required = false) String todosTipos,
            HttpServletRequest request
            ) throws JSONException {
        try {
            FiltroColaboradorJSON filtros = new FiltroColaboradorJSON(filters);
            return ResponseEntityFactory.ok(
                    professorSinteticoService.listarColaboradores(request, filtros, paginadorDTO, empresaId,
                            todosTipos == null ? Boolean.FALSE : Boolean.valueOf(todosTipos)),
                    paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(ColaboradorController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar colaboradores", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }


    @ResponseBody
    @Permissao(recursos = RecursoEnum.COLABORADORES)
    @RequestMapping(value = "/professores-dados-basicos/{idProfessorMontou}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listaColaboradorDadosBasicos(@PathVariable("idProfessorMontou") Integer idProfessorMontou,
                                                                            @RequestParam(value = "filters", required = false) JSONObject filters,
                                                                            @RequestHeader(value = "empresaId", required = true) Integer empresaZwId
                                                                            ) throws JSONException {
        // retornar somente nome e codigo professor;
        try {
            FiltroColaboradorJSON filtros = new FiltroColaboradorJSON(filters);
            return ResponseEntityFactory.ok(professorSinteticoService.listaColaboradorDadosBasicos(idProfessorMontou, filtros, empresaZwId));
        } catch (ServiceException e) {
            Logger.getLogger(ColaboradorController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar colaboradores", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (JSONException e) {
            Logger.getLogger(ColaboradorController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar colaboradores", e);
            return ResponseEntityFactory.erroInterno("erro_colaboradores_", e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.COLABORADORES)
    @RequestMapping(value = "/all-simple", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> todosSimples(@RequestParam(value = "filters", required = false) JSONObject filters,
                                                            PaginadorDTO paginadorDTO,
                                                            @RequestHeader(value = "empresaId", required = true) Integer empresaId,
                                                            @RequestParam(value = "todosTipos", required = false) String todosTipos,
                                                            HttpServletRequest request){
        try {

            return ResponseEntityFactory.ok(professorSinteticoService.listarColaboradoresSimples(request, new FiltroColaboradorJSON(filters), paginadorDTO, empresaId));
        } catch (ServiceException e) {
            Logger.getLogger(ColaboradorController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar os colaboradores", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            Logger.getLogger(ColaboradorController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar os colaboradores", e);
            return ResponseEntityFactory.erroInterno("erro_colaboradores_", e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.COLABORADORES)
    @RequestMapping(method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cadastroColaborador(
            @RequestBody ColaboradorTO colaboradorTO,
            @RequestHeader("empresaId") Integer empresaId,
            HttpServletRequest request) {
        try {
            return ResponseEntityFactory.ok(professorSinteticoService.cadastrarColaborador(colaboradorTO, empresaId, request));
        } catch (ServiceException e) {
            Logger.getLogger(ColaboradorController.class.getName()).log(Level.SEVERE, "Erro ao tentar cadastrar o colaborador", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.COLABORADORES)
    @RequestMapping(value = "/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> detalhesColaborador(@PathVariable("id") Integer id, HttpServletRequest request,
                                                                   @RequestHeader(value = "empresaId") Integer empresaId,
                                                                   @RequestParam(value = "origemIsTelaColaborador", required = false) boolean origemIsTelaColaboradorZWUI) {
        try {
            return ResponseEntityFactory.ok(professorSinteticoService.buscarColaborador(id, empresaId, request, origemIsTelaColaboradorZWUI));
        } catch (ServiceException e) {
            Logger.getLogger(ColaboradorController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter detalhes do colaborador", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.COLABORADORES)
    @RequestMapping(value = "edit-situacao/{id}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> editSituacaoColaborador(@PathVariable("id") Integer id){
        try {
            professorSinteticoService.editSituacaoColaborador(id);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(ColaboradorController.class.getName()).log(Level.SEVERE, "Erro ao tentar editar situação do colaborador", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.COLABORADORES)
    @RequestMapping(value = "/{id}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> atualizarColaborador(
            @PathVariable("id") Integer id,
            @RequestBody ColaboradorTO colaboradorTO,
            @RequestHeader("empresaId") Integer empresaId){
        try {

            return ResponseEntityFactory.ok(professorSinteticoService.atualizarColaborador(id, empresaId, colaboradorTO));
        } catch (ServiceException e) {
            Logger.getLogger(ColaboradorController.class.getName()).log(Level.SEVERE, "Erro ao tentar alterar o colaborador", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.COLABORADORES)
    @RequestMapping(value = "/is-null-usuario/all", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> colaboradorIsNullUsuario(HttpServletRequest request){
        try {

            return ResponseEntityFactory.ok(professorSinteticoService.colaboradorIsNullUsuario(request));
        } catch (ServiceException e) {
            Logger.getLogger(ColaboradorController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar os colaboradores", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.COLABORADORES)
    @RequestMapping(value = "/all", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listarTodosColaborador(
            @RequestHeader(value = "empresaId", required = true) Integer empresaId,
            @RequestParam(value = "incluirInativos" ,required = false) Boolean incluirInativos,
            @RequestParam(value = "todosTipos", required = false) String todosTipos,
            @RequestParam(value = "validarNoTreino", required = false) Boolean validarNoTreino,
            HttpServletRequest request){
        try {

            return ResponseEntityFactory.ok(professorSinteticoService.listarTodosColaborador(request, empresaId, incluirInativos,
                    todosTipos == null ? Boolean.FALSE : Boolean.valueOf(todosTipos),
                    validarNoTreino == null ? Boolean.FALSE : Boolean.valueOf(validarNoTreino)));
        } catch (ServiceException e) {
            Logger.getLogger(ColaboradorController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar os colaboradores", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.COLABORADORES)
    @RequestMapping(value = "zw/all", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listarTodosColaboradorZW(
            @RequestHeader(value = "empresaId", required = true) Integer empresaId,
            @RequestParam(value = "incluirInativos" ,required = false) Boolean incluirInativos,
            @RequestParam(value = "todosTipos", required = false) String todosTipos,
            HttpServletRequest request){
        try {

            return ResponseEntityFactory.ok(professorSinteticoService.consultarTodosProfessoresZW(empresaId, incluirInativos,
                    todosTipos == null ? Boolean.FALSE : Boolean.valueOf(todosTipos)));
        } catch (ServiceException e) {
            Logger.getLogger(ColaboradorController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar os colaboradores", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.COLABORADORES)
    @RequestMapping(value = "/professores-aulas/all", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listarTodosColaboradorAptoAAula(
            @RequestHeader(value = "empresaId") Integer empresaId,
            @RequestParam(value = "filters", required = false) JSONObject filters,
            @RequestParam(value = "incluirInativos" ,required = false) Boolean incluirInativos,
            HttpServletRequest request){
        try {
            String nome = (filters != null && !UteisValidacao.emptyString(filters.optString("quicksearchValue"))) ? filters.optString("quicksearchValue") : null;

            return ResponseEntityFactory.ok(professorSinteticoService.listarTodosColaboradorAptoAAula(request, empresaId, incluirInativos, nome));
        } catch (ServiceException e) {
            Logger.getLogger(ColaboradorController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar os colaboradores", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }


    @ResponseBody
    @Permissao(recursos = RecursoEnum.COLABORADORES)
    @RequestMapping(value = "/usuario/{usuarioId}/colaborador", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> consultarColaboradoradorPorUsuario(
            @PathVariable(value = "usuarioId") Integer usuarioId){
        try {

            return ResponseEntityFactory.ok(professorSinteticoService.consultarColaboradorPorUsuario(usuarioId));
        } catch (ServiceException e) {
            Logger.getLogger(ColaboradorController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar os colaboradores", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }


    @ResponseBody
    @Permissao(recursos = RecursoEnum.COLABORADORES)
    @RequestMapping(value = "/professores-vinculos", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> professoresComVinculos(
            @RequestHeader(value = "empresaId") Integer empresaId,
            HttpServletRequest request){
        try {
            return ResponseEntityFactory.ok(professorSinteticoService.consultarProfessoresComVinculos(request, empresaId));
        } catch (ServiceException e) {
            Logger.getLogger(ColaboradorController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar os colaboradores", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.COLABORADORES)
    @RequestMapping(value = "/bi-professores-vinculos", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> biProfessoresComVinculos(
            @RequestHeader(value = "empresaId") Integer empresaId,
            HttpServletRequest request){
        try {
            return ResponseEntityFactory.ok(professorSinteticoService.consultarBiProfessoresComVinculos(request, empresaId));
        } catch (ServiceException e) {
            Logger.getLogger(ColaboradorController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar os colaboradores", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "zw/todos-por-empresa", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listarTodosColaboradoresPorEmpresa(@RequestHeader(value = "empresaId", required = true) Integer empresaId,
                                                                                  @RequestParam(value = "ids", required = false) String ids) {
        try {
            return ResponseEntityFactory.ok(professorSinteticoService.listarTodosColaboradoresPorEmpresa(ids, empresaId));
        } catch (ServiceException e) {
            Logger.getLogger(ColaboradorController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar os colaboradores do zw", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }
}
