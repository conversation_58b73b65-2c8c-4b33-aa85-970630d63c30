package br.com.pacto.controller.json.agendamento;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by paulo 13/08/2019
 */

public class AdicionarAlunoTurmaResponseDTO {

    private SituacaoAdicionarAlunoTurmaDTO status;
    private Map<String, Object> conteudo = new HashMap<>();

    public SituacaoAdicionarAlunoTurmaDTO getStatus() {
        return status;
    }

    public void setStatus(SituacaoAdicionarAlunoTurmaDTO status) {
        this.status = status;
    }

    public Map<String, Object> getConteudo() {
        return conteudo;
    }

    public void setConteudo(Map<String, Object> conteudo) {
        this.conteudo = conteudo;
    }
}
