package br.com.pacto.controller.json.tvGestor.dto;

import br.com.pacto.bean.colaborador.ColaboradorSimplesTO;
import br.com.pacto.controller.json.ambiente.AmbienteResponseTO;
import br.com.pacto.controller.json.modalidade.ModalidadeResponseTO;

import java.util.List;

public class TvGestorFiltroResponse {
    private List<ModalidadeResponseTO> modalidades;
    private List<AmbienteResponseTO> ambientes;
    private List<ColaboradorSimplesTO> colaboradores;

    public TvGestorFiltroResponse(List<ModalidadeResponseTO> modalidades, List<AmbienteResponseTO> ambientes, List<ColaboradorSimplesTO> colaboradores) {
        this.modalidades = modalidades;
        this.ambientes = ambientes;
        this.colaboradores = colaboradores;
    }

    public List<ModalidadeResponseTO> getModalidades() {
        return modalidades;
    }

    public void setModalidades(List<ModalidadeResponseTO> modalidades) {
        this.modalidades = modalidades;
    }

    public List<AmbienteResponseTO> getAmbientes() {
        return ambientes;
    }

    public void setAmbientes(List<AmbienteResponseTO> ambientes) {
        this.ambientes = ambientes;
    }

    public List<ColaboradorSimplesTO> getColaboradores() {
        return colaboradores;
    }

    public void setColaboradores(List<ColaboradorSimplesTO> colaboradores) {
        this.colaboradores = colaboradores;
    }
}
