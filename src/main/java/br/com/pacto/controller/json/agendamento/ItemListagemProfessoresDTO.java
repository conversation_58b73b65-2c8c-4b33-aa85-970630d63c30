package br.com.pacto.controller.json.agendamento;

import org.json.JSONObject;

public class ItemListagemProfessoresDTO {

    private String professor;
    private String modalidades;
    private Integer aulas;
    private Integer presencas;
    private Integer capacidade;
    private Integer ocupacaoAcumulada;
    private Double ocupacaoPercentual;
    private Double frequenciaPercentual;
    private String ocupacao;
    private String frequencia;

    public ItemListagemProfessoresDTO(JSONObject object) throws Exception {
        this.professor = object.getString("professor");
        this.modalidades = object.getString("modalidades");
        this.aulas = object.getInt("aulas");
        this.capacidade = object.optInt("capacidadeAcumulada");
        this.ocupacaoAcumulada = object.optInt("ocupacaoAcumulada");
        this.ocupacaoPercentual = (ocupacaoAcumulada.doubleValue()/capacidade.doubleValue()) * 100.0 ;
        this.ocupacao = this.ocupacaoPercentual.intValue() + "%";
        this.presencas = object.optInt("presentesAcumulada");
        this.frequenciaPercentual = (presencas.doubleValue()/ocupacaoAcumulada.doubleValue()) * 100.0 ;
        this.frequencia = this.frequenciaPercentual.intValue() +  "%";
    }

    public String getProfessor() {
        return professor;
    }

    public void setProfessor(String professor) {
        this.professor = professor;
    }

    public String getModalidades() {
        return modalidades;
    }

    public void setModalidades(String modalidades) {
        this.modalidades = modalidades;
    }

    public Integer getAulas() {
        return aulas;
    }

    public void setAulas(Integer aulas) {
        this.aulas = aulas;
    }

    public Integer getPresencas() {
        return presencas;
    }

    public void setPresencas(Integer presencas) {
        this.presencas = presencas;
    }

    public Integer getCapacidade() {
        return capacidade;
    }

    public void setCapacidade(Integer capacidade) {
        this.capacidade = capacidade;
    }

    public Integer getOcupacaoAcumulada() {
        return ocupacaoAcumulada;
    }

    public void setOcupacaoAcumulada(Integer ocupacaoAcumulada) {
        this.ocupacaoAcumulada = ocupacaoAcumulada;
    }

    public Double getOcupacaoPercentual() {
        return ocupacaoPercentual;
    }

    public void setOcupacaoPercentual(Double ocupacaoPercentual) {
        this.ocupacaoPercentual = ocupacaoPercentual;
    }

    public Double getFrequenciaPercentual() {
        return frequenciaPercentual;
    }

    public void setFrequenciaPercentual(Double frequenciaPercentual) {
        this.frequenciaPercentual = frequenciaPercentual;
    }

    public String getOcupacao() {
        return ocupacao;
    }

    public void setOcupacao(String ocupacao) {
        this.ocupacao = ocupacao;
    }

    public String getFrequencia() {
        return frequencia;
    }

    public void setFrequencia(String frequencia) {
        this.frequencia = frequencia;
    }
}
