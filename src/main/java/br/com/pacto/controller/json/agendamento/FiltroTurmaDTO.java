package br.com.pacto.controller.json.agendamento;

import br.com.pacto.bean.aula.TipoAulaCheiaOrigemEnum;
import br.com.pacto.util.UteisValidacao;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import com.fasterxml.jackson.annotation.JsonInclude;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by paulo 12/08/2019
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FiltroTurmaDTO {

    private TipoAulaCheiaOrigemEnum tipo;
    private List<Integer> professoresIds;
    private List<Integer> ambientesIds;
    private List<Integer> modalidadesIds;
    private Integer turmaId;
    private TurnoEnum turno;
    private String search;
    private String situacaoHorario;

    public FiltroTurmaDTO (JSONObject filtros) throws JSONException {
        if (filtros != null) {
            if (!StringUtils.isBlank(filtros.optString("tipo"))) {
                this.tipo = TipoAulaCheiaOrigemEnum.valueOf(filtros.optString("tipo"));
            } else {
                this.tipo = TipoAulaCheiaOrigemEnum.TODAS;
            }
            JSONArray professoresIds = filtros.optJSONArray("professoresIds");
            this.professoresIds = new ArrayList<>();
            if (professoresIds != null) {
                for (int i = 0; i < professoresIds.length(); i++) {
                    getProfessoresIds().add(professoresIds.getInt(i));
                }
            }
            JSONArray ambientesIds = filtros.optJSONArray("ambientesIds");
            this.ambientesIds = new ArrayList<>();
            if (ambientesIds != null) {
                for (int i = 0; i < ambientesIds.length(); i++) {
                    getAmbientesIds().add(ambientesIds.getInt(i));
                }
            }
            JSONArray modalidadesIds = filtros.optJSONArray("modalidadesIds");
            this.modalidadesIds = new ArrayList<>();
            if (modalidadesIds != null) {
                for (int i = 0; i < modalidadesIds.length(); i++) {
                    getModalidadesIds().add(modalidadesIds.getInt(i));
                }
            }

            if(!UteisValidacao.emptyString(filtros.optString("turno"))){
                try {
                    turno = TurnoEnum.valueOf(filtros.getString("turno"));
                }catch (Exception e){}
            }

            search = filtros.optString("search");

            JSONArray situacoes = filtros.optJSONArray("situacaoHorario");
            StringBuilder stringBuilder = new StringBuilder();
            if (situacoes != null) {
                for (int i = 0; i < situacoes.length(); i++) {
                    if (i > 0) {
                        stringBuilder.append(",");
                    }
                    stringBuilder.append("'").append(situacoes.getString(i)).append("'");
                }
                this.situacaoHorario = stringBuilder.toString();
            }
        }
    }

    public String getSearch() {
        return search;
    }

    public void setSearch(String search) {
        this.search = search;
    }

    public TipoAulaCheiaOrigemEnum getTipo() {
        return tipo;
    }

    public void setTipo(TipoAulaCheiaOrigemEnum tipo) {
        this.tipo = tipo;
    }

    public List<Integer> getProfessoresIds() {
        return professoresIds;
    }

    public void setProfessoresIds(List<Integer> professoresIds) {
        this.professoresIds = professoresIds;
    }

    public List<Integer> getAmbientesIds() {
        return ambientesIds;
    }

    public void setAmbientesIds(List<Integer> ambientesIds) {
        this.ambientesIds = ambientesIds;
    }

    public List<Integer> getModalidadesIds() {
        return modalidadesIds;
    }

    public void setModalidadesIds(List<Integer> modalidadesIds) {
        this.modalidadesIds = modalidadesIds;
    }

    public Integer getTurmaId() {
        return turmaId;
    }

    public void setTurmaId(Integer turmaId) {
        this.turmaId = turmaId;
    }

    public TurnoEnum getTurno() {
        return turno;
    }

    public void setTurno(TurnoEnum turno) {
        this.turno = turno;
    }

    public String getSituacaoHorario() { return situacaoHorario; }

    public void setSituacaoHorario(String situacaoHorario) { this.situacaoHorario = situacaoHorario; }
}
