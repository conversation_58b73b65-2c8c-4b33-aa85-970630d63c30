package br.com.pacto.controller.json.gestao;

import br.com.pacto.controller.json.base.SuperJSON;
import br.com.pacto.objeto.Calendario;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

public class FiltroGestaoProgramaDTO extends SuperJSON {

    private Date inicio = Calendario.inicioMes(Calendario.anterior(Calendar.MONTH, Calendario.hoje()));
    private Date fim = Calendario.fimMes(Calendario.hoje());
    private Integer mes;
    private Integer ano;

    private Boolean nome = false;
    private Boolean aluno = false;
    private String parametro;
    private List<Integer> colaboradorIds;
    private List<String> origemExecucaoFicha;

    public FiltroGestaoProgramaDTO(JSONObject filters) throws JSONException {

        if (filters != null) {
            this.parametro = filters.optString("quicksearchValue");

            JSONArray colunasVisiveis = filters.optJSONArray("quicksearchFields");
            if (colunasVisiveis != null) {
                for (int i = 0; i < colunasVisiveis.length(); i++) {
                    if (colunasVisiveis.get(i).equals("aluno")) {
                        this.aluno = true;
                    }
                    if (colunasVisiveis.get(i).equals("nome")) {
                        this.nome = true;
                    }
                }
            }

            JSONArray colaboradorIds = filters.optJSONArray("Professores");
            if (colaboradorIds == null) {
                colaboradorIds = filters.optJSONArray("professoresIds");
            }
            this.colaboradorIds = new ArrayList<>();
            if (colaboradorIds != null) {
                for (int i = 0; i < colaboradorIds.length(); i++) {
                    getColaboradorIds().add(colaboradorIds.getInt(i));
                }
            }

            if (filters.has("dataInicio")) {
                this.inicio = Calendario.getInstanceDate(filters.optLong("dataInicio"));
            }
            if (filters.has("dataFim")) {
                this.fim = Calendario.getInstanceDate(filters.optLong("dataFim"));
            }
            JSONArray origemExecucoes = filters.optJSONArray("origemExecucao");
            if (origemExecucoes != null) {
                List<String> origemList = new ArrayList<>();
                for (int i = 0; i < origemExecucoes.length(); i++) {
                    origemList.add(origemExecucoes.getString(i));
                }
                this.origemExecucaoFicha = origemList;
            }
        }
    }

    public Date getInicio() {
        return inicio;
    }

    public void setInicio(Date inicio) {
        this.inicio = inicio;
    }

    public Date getFim() {
        return fim;
    }

    public void setFim(Date fim) {
        this.fim = fim;
    }

    public Integer getMes() {
        return mes;
    }

    public void setMes(Integer mes) {
        this.mes = mes;
    }

    public Integer getAno() {
        return ano;
    }

    public void setAno(Integer ano) {
        this.ano = ano;
    }

    public Boolean getNome() {
        return nome;
    }

    public void setNome(Boolean nome) {
        this.nome = nome;
    }

    public String getParametro() {
        return parametro;
    }

    public void setParametro(String parametro) {
        this.parametro = parametro;
    }

    public List<Integer> getColaboradorIds() {
        return colaboradorIds;
    }

    public void setColaboradorIds(List<Integer> colaboradorIds) {
        this.colaboradorIds = colaboradorIds;
    }

    public Boolean getAluno() {
        return aluno;
    }

    public void setAluno(Boolean aluno) {
        this.aluno = aluno;
    }

    public List<String> getOrigemExecucaoFicha() {
        return origemExecucaoFicha;
    }

    public void setOrigemExecucaoFicha(List<String> origemExecucaoFicha) {
        this.origemExecucaoFicha = origemExecucaoFicha;
    }
}
