package br.com.pacto.controller.json.agendamento;

import br.com.pacto.controller.json.base.SuperJSON;
import br.com.pacto.objeto.Calendario;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

public class FiltroAlunosFaltososDTO extends SuperJSON {

    private Date dataInicio;
    private Date dataFim;
    private Boolean nomeRender = false;
    private Boolean dataFaltaRender = false;
    private Boolean modalidadeRender = false;
    private Boolean aulaRender = false;
    private String parametro;
    public FiltroAlunosFaltososDTO(JSONObject filters) throws JSONException {

        if (filters != null) {
            this.parametro = filters.optString("quicksearchValue");

            JSONArray colunasVisiveis = filters.optJSONArray("quicksearchFields");
            if (colunasVisiveis != null) {
                for (int i = 0; i < colunasVisiveis.length(); i++) {
                    if (colunasVisiveis.get(i).equals("nomeRender")) {
                        this.nomeRender = true;
                    }
                    if (colunasVisiveis.get(i).equals("dataFaltaRender")) {
                        this.dataFaltaRender = true;
                    }
                    if (colunasVisiveis.get(i).equals("modalidadeRender")) {
                        this.modalidadeRender = true;
                    }
                    if (colunasVisiveis.get(i).equals("aulaRender")) {
                        this.aulaRender = true;
                    }
                }
            }

            if (filters.has("dataInicio")) {
                this.dataInicio = Calendario.getInstanceDate(filters.optLong("dataInicio"));
            }

            if (filters.has("dataFim")) {
                this.dataFim = Calendario.getInstanceDate(filters.optLong("dataFim"));
            }
        }
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Date getDataFim() {
        return dataFim;
    }

    public void setDataFim(Date dataFim) {
        this.dataFim = dataFim;
    }

    public Boolean getNomeRender() {
        return nomeRender;
    }

    public void setNomeRender(Boolean nomeRender) {
        this.nomeRender = nomeRender;
    }

    public Boolean getDataFaltaRender() {
        return dataFaltaRender;
    }

    public void setDataFaltaRender(Boolean dataFaltaRender) {
        this.dataFaltaRender = dataFaltaRender;
    }

    public Boolean getModalidadeRender() {
        return modalidadeRender;
    }

    public void setModalidadeRender(Boolean modalidadeRender) {
        this.modalidadeRender = modalidadeRender;
    }

    public Boolean getAulaRender() {
        return aulaRender;
    }

    public void setAulaRender(Boolean aulaRender) {
        this.aulaRender = aulaRender;
    }

    public String getParametro() {
        return parametro;
    }

    public void setParametro(String parametro) {
        this.parametro = parametro;
    }
}
