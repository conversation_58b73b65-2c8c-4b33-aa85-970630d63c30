package br.com.pacto.controller.json.agendamento;

import br.com.pacto.controller.json.tipoEvento.TipoAgendamentoDTO;
import br.com.pacto.enumerador.agenda.StatusAgendamentoEnum;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by paulo 20/08/2019
 */

public class FiltrosAgendamentosDTO {

    private List<Integer> professoresId = new ArrayList<>();
    private List<Integer> tiposServicoId = new ArrayList<>();
    private List<StatusAgendamentoEnum> statusAgendamentos = new ArrayList<>();

    public FiltrosAgendamentosDTO(JSONObject filtros) throws JSONException {
        if (filtros != null) {
            JSONArray professores = filtros.optJSONArray("professoresIds");
            if (professores != null) {
                for (int i = 0; i < professores.length(); i++) {
                    getProfessoresId().add(professores.getInt(i));
                }
            }
            JSONArray tiposServico = filtros.optJSONArray("tipoAgendamentoIds");
            if (tiposServico != null) {
                for (int i = 0; i < tiposServico.length(); i++) {
                    getTiposServicoId().add(tiposServico.getInt(i));
                }
            }
            JSONArray statusAgendamentos = filtros.optJSONArray("status");
            if (statusAgendamentos != null) {
                for (int i = 0; i < statusAgendamentos.length(); i++) {
                    getStatusAgendamentos().add(StatusAgendamentoEnum.valueOf(statusAgendamentos.getString(i)));
                }
            }
        }
    }

    public FiltrosAgendamentosDTO(List<TipoAgendamentoDTO> filtros) {
        if (filtros != null && filtros.size() > 0) {
            for (TipoAgendamentoDTO tipo : filtros) {
                getTiposServicoId().add(tipo.getId());
            }
        }
    }

    public List<Integer> getProfessoresId() {
        return professoresId;
    }

    public void setProfessoresId(List<Integer> professoresId) {
        this.professoresId = professoresId;
    }

    public List<Integer> getTiposServicoId() {
        return tiposServicoId;
    }

    public void setTiposServicoId(List<Integer> tiposServicoId) {
        this.tiposServicoId = tiposServicoId;
    }

    public List<StatusAgendamentoEnum> getStatusAgendamentos() {
        return statusAgendamentos;
    }

    public void setStatusAgendamentos(List<StatusAgendamentoEnum> statusAgendamentos) {
        this.statusAgendamentos = statusAgendamentos;
    }
}
