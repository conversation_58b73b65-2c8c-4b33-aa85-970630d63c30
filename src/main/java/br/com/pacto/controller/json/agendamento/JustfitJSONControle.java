/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.controller.json.agendamento;

import br.com.pacto.bean.aula.TipoAulaCheiaOrigemEnum;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.base.SuperControle;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.intf.agendatotal.AgendaTotalService;
import br.com.pacto.service.intf.usuario.UsuarioService;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.bean.AgendaJustfitDTO;
import br.com.pacto.util.bean.AgendaTotalTO;
import br.com.pacto.util.bean.AgendadoTO;
import br.com.pacto.util.enumeradores.OrigemSistemaEnum;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller
@RequestMapping("/justfit")
public class JustfitJSONControle extends SuperControle {

    @Autowired
    private AgendaTotalService service;
    @Autowired
    private UsuarioService usuarioService;

    @RequestMapping(value = "{ctx}/treino", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE + ";charset=utf-8")
    public @ResponseBody
    String treino(@PathVariable String ctx, @RequestBody String json) throws JSONException {
        try {

            JSONObject jsonObject = new JSONObject(json);

            String operacao = jsonObject.optString("operacao");
            Integer empresaZW = jsonObject.optInt("empresa");
            Integer cliente = jsonObject.optInt("cliente");
            String inicio = jsonObject.optString("inicio");
            String fim = jsonObject.optString("fim");
            String identificador = jsonObject.optString("identificador");

            Object retorno = null;
            if (operacao.equalsIgnoreCase("consultar")) {
                retorno = consultar(ctx, empresaZW, cliente, inicio, fim);
            } else if (operacao.equalsIgnoreCase("marcar")) {
                retorno = marcar(ctx, empresaZW, cliente, identificador);
            } else if (operacao.equalsIgnoreCase("desmarcar")) {
                retorno = desmarcar(ctx, empresaZW, cliente, identificador);
            } else {
                throw new Exception("Operação não encontrada.");
            }

            return this.toJSON(true, retorno).toString();
        } catch (Exception ex) {
            ex.printStackTrace();
            return this.toJSON(false, ex.getMessage()).toString();
        }
    }

    private JSONObject toJSON(boolean sucesso, Object obj) throws JSONException {
        JSONObject json = new JSONObject();
        json.put("sucesso", sucesso);
        json.put("dados", obj);
        return json;
    }

    private Object consultar(String ctx, Integer empresaZW, Integer codigoCliente, String inicio, String fim) throws Exception {

        Date inicioConsultaZW = Uteis.getDate(inicio, "ddMMyyyy");
        Date fimConsultaZW = Uteis.getDate(fim, "ddMMyyyy");
        TipoAulaCheiaOrigemEnum tipoAulas = TipoAulaCheiaOrigemEnum.AC;

        Map<String, AgendaTotalTO> mapa = service.montarAgenda(ctx, inicioConsultaZW, fimConsultaZW, empresaZW, 0, tipoAulas, null, null);

        Map<String, List<AgendadoTO>> mapaAgendados = new HashMap<String, List<AgendadoTO>>();
        if (!UteisValidacao.emptyNumber(codigoCliente)) {
            mapaAgendados = service.montarMapaAgendados(ctx, inicioConsultaZW, fimConsultaZW, empresaZW, new ArrayList<AgendaTotalTO>(mapa.values()), false);
        }

        List<AgendaJustfitDTO> listaRetorno = new ArrayList<AgendaJustfitDTO>();
        for (String key : mapa.keySet()) {
            AgendaTotalTO agendaTotalTO = mapa.get(key);

            boolean alunoMarcou = false;

            if (!UteisValidacao.emptyNumber(codigoCliente)) {
                List<AgendadoTO> alunos = mapaAgendados.get(key);
                for (AgendadoTO agendadoTO : alunos) {
                    try {
                        if (agendadoTO.getCodigoCliente().equals(codigoCliente)) {
                            alunoMarcou = true;
                            break;
                        }
                    } catch (Exception ex) {
                    }
                }
            }

            AgendaJustfitDTO agendaJustfitDTO = new AgendaJustfitDTO(agendaTotalTO);
            agendaJustfitDTO.setAlunoMarcou(alunoMarcou);
            listaRetorno.add(agendaJustfitDTO);
        }

        return listaRetorno;
    }

    private Object marcar(String ctx, Integer empresaZW, Integer codigoCliente, String identificador) throws Exception {

        Usuario usuario = usuarioService.consultarPorUserName(ctx, "pactobr");

        String[] split = identificador.split("_");

        Integer id = Integer.parseInt(split[0]);
        Date dia = Calendario.getDataComHoraZerada(Uteis.getDate(split[1], "dd/MM/yy"));

        String retorno = service.inserirAlunoAulaCheia(ctx, codigoCliente,
                id, dia, OrigemSistemaEnum.AULA_CHEIA, usuario, empresaZW, false, false, null);

        if (retorno.toUpperCase().startsWith("ERRO:")) {
            throw new Exception(retorno.replace("ERRO:", ""));
        }

        boolean erro = false;
        try {
            JSONObject jsonObject = new JSONObject(retorno);
        } catch (Exception ex) {
            erro = true;
        }

        if (erro) {
            throw new Exception(retorno);
        }
        return "Aula marcada.";
    }

    private Object desmarcar(String ctx, Integer empresaZW, Integer codigoCliente, String identificador) throws Exception {
        Usuario usuario = usuarioService.consultarPorUserName(ctx, "pactobr");

        String[] split = identificador.split("_");

        Integer id = Integer.parseInt(split[0]);
        Date dia = Calendario.getDataComHoraZerada(Uteis.getDate(split[1], "dd/MM/yy"));

        String retorno = service.excluirAlunoAulaCheia(ctx, codigoCliente, null, id, dia,
                OrigemSistemaEnum.AULA_CHEIA, usuario.getUsuarioZW(), empresaZW, null);

        if (retorno.toUpperCase().startsWith("ERRO:")) {
            throw new Exception(retorno.replace("ERRO:", ""));
        }

        if (retorno.equalsIgnoreCase("ok")) {
            return "Aula desmarcada.";
        } else {
            throw new Exception(retorno);
        }
    }
}
