package br.com.pacto.controller.json.biapp;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.controller.json.avaliacao.BIAvalicaoFisicaController;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.intf.gestao.BiAppService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.logging.Level;
import java.util.logging.Logger;

@Controller
@RequestMapping("/psec/bi-app")
public class BiAppController {

    @Autowired
    private SessaoService sessaoService;
    @Autowired
    private BiAppService biAppService;

    @ResponseBody
    @RequestMapping(method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> bi(@RequestHeader("empresaId") Integer empresaId) {
        try {
            final String chave = sessaoService.getUsuarioAtual().getChave();
            return ResponseEntityFactory.ok(biAppService.biApp(chave, empresaId, false));
        } catch (Exception e) {
            Logger.getLogger(BIAvalicaoFisicaController.class.getName()).log(Level.SEVERE, "Erro ao consultar Dash de app", e);
            return ResponseEntityFactory.erroInterno("erro_obter_dash_app", e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/reload",method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> biReload(@RequestHeader("empresaId") Integer empresaId) {
        try {
            final String chave = sessaoService.getUsuarioAtual().getChave();
            return ResponseEntityFactory.ok(biAppService.biApp(chave, empresaId, true));
        } catch (Exception e) {
            Logger.getLogger(BIAvalicaoFisicaController.class.getName()).log(Level.SEVERE, "Erro ao consultar Dash de app", e);
            return ResponseEntityFactory.erroInterno("erro_obter_dash_app", e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/usam-app", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> usam(@RequestParam(value = "professorId", required = false) Integer idProfessor,
                                                    @RequestParam(value = "filters", required = false) String filter,
                                                           @RequestHeader("empresaId") Integer empresaId, PaginadorDTO paginadorDTO) {
        try {
            final String chave = sessaoService.getUsuarioAtual().getChave();
            return ResponseEntityFactory.ok(biAppService.listaUsuariosApp(chave,
                    filter,
                    empresaId,
                    idProfessor,
                    true,
                    false, false,
                    false, paginadorDTO), paginadorDTO);
        } catch (Exception e) {
            Logger.getLogger(BIAvalicaoFisicaController.class.getName()).log(Level.SEVERE, "Erro ao consultar Dash de app", e);
            return ResponseEntityFactory.erroInterno("erro_obter_dash_app", e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/ativos", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> ativosTotal(@RequestParam(value = "professorId", required = false) Integer idProfessor,
                                                    @RequestParam(value = "filters", required = false) String filter,
                                                    @RequestHeader("empresaId") Integer empresaId, PaginadorDTO paginadorDTO) {
        try {
            final String chave = sessaoService.getUsuarioAtual().getChave();
            return ResponseEntityFactory.ok(biAppService.listaUsuariosApp(chave,
                    filter,
                    empresaId,
                    idProfessor,
                    false,
                    false, false,
                    true, paginadorDTO), paginadorDTO);
        } catch (Exception e) {
            Logger.getLogger(BIAvalicaoFisicaController.class.getName()).log(Level.SEVERE, "Erro ao consultar Dash de app", e);
            return ResponseEntityFactory.erroInterno("erro_obter_dash_app", e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/nao-usam-app", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> naoUsam(@RequestParam(value = "professorId", required = false) Integer idProfessor,
                                                       @RequestParam(value = "filters", required = false) String filter,
                                                           @RequestHeader("empresaId") Integer empresaId, PaginadorDTO paginadorDTO) {
        try {
            final String chave = sessaoService.getUsuarioAtual().getChave();
            return ResponseEntityFactory.ok(biAppService.listaUsuariosApp(chave,
                    filter,
                    empresaId,
                    idProfessor,
                    false,
                    false, true,
                    false, paginadorDTO), paginadorDTO);
        } catch (Exception e) {
            Logger.getLogger(BIAvalicaoFisicaController.class.getName()).log(Level.SEVERE, "Erro ao consultar Dash de app", e);
            return ResponseEntityFactory.erroInterno("erro_obter_dash_app", e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/inativos-app", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> inativosUsam(@RequestParam(value = "professorId", required = false) Integer idProfessor,
                                                            @RequestParam(value = "filters", required = false) String filter,
                                                           @RequestHeader("empresaId") Integer empresaId, PaginadorDTO paginadorDTO) {
        try {
            final String chave = sessaoService.getUsuarioAtual().getChave();
            return ResponseEntityFactory.ok(biAppService.listaUsuariosApp(chave,
                    filter,
                    empresaId,
                    idProfessor,
                    false,
                    true, false,
                    false, paginadorDTO), paginadorDTO);
        } catch (Exception e) {
            Logger.getLogger(BIAvalicaoFisicaController.class.getName()).log(Level.SEVERE, "Erro ao consultar Dash de app", e);
            return ResponseEntityFactory.erroInterno("erro_obter_dash_app", e.getMessage());
        }
    }


}
