package br.com.pacto.controller.json.aluno;

import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.controller.json.base.SuperControle;

import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;

/**
 * Created by paulo in 08/05/2019
 */

public class AlunoZWResponseDTO {

    private Integer alunoZWId;
    private Integer matriculaZW;
    private Integer treinoId;
    private String nome;
    private String email;
    private String imageUri;

    public AlunoZWResponseDTO(ClienteSintetico cliente, Integer clienteTreinoCodigo) {
        this.alunoZWId = cliente.getCodigoCliente();
        this.matriculaZW = cliente.getMatricula();
        this.treinoId = clienteTreinoCodigo;
        this.nome = cliente.getNome();
        this.email = cliente.getEmail();
        this.imageUri = cliente.getUrlFoto();
    }

    public Integer getAlunoZWId() {
        return alunoZWId;
    }

    public void setAlunoZWId(Integer alunoZWId) {
        this.alunoZWId = alunoZWId;
    }

    public Integer getMatriculaZW() {
        return matriculaZW;
    }

    public void setMatriculaZW(Integer matriculaZW) {
        this.matriculaZW = matriculaZW;
    }

    public Integer getTreinoId() {
        return treinoId;
    }

    public void setTreinoId(Integer treinoId) {
        this.treinoId = treinoId;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getImageUri() {
        return imageUri;
    }

    public void setImageUri(String imageUri) {
        this.imageUri = imageUri;
    }
}
