package br.com.pacto.controller.json.programa;

import br.com.pacto.bean.atividade.AtividadeMusculo;
import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * Created by joao moita on 03/10/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AtividadeMusculoResponseTO {

    private Integer id;
    private String nome;

    public AtividadeMusculoResponseTO() {

    }

    public AtividadeMusculoResponseTO(AtividadeMusculo am) {
        this.id = am.getMusculo().getCodigo();
        this.nome = am.getMusculo().getNome();
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }
}
