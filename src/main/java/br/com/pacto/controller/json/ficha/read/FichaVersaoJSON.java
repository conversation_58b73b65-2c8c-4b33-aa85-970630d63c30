/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.controller.json.ficha.read;

import br.com.pacto.controller.json.base.SuperJSON;
import br.com.pacto.controller.json.serialization.JsonDateTimeSerializerYYYYMMDDHHNN;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class FichaVersaoJSON extends SuperJSON {

    private Integer codFicha;
    private Integer versao;
    private Date ultimaExecucao;

    public FichaVersaoJSON(Integer codFicha, Integer versao, Date ultimaExecucao) {
        this.codFicha = codFicha;
        this.versao = versao;
        this.ultimaExecucao = ultimaExecucao;
    }

    public Integer getCodFicha() {
        return codFicha;
    }

    public void setCodFicha(Integer codFicha) {
        this.codFicha = codFicha;
    }

    public Integer getVersao() {
        return versao;
    }

    public void setVersao(Integer versao) {
        this.versao = versao;
    }

    @JsonSerialize(using = JsonDateTimeSerializerYYYYMMDDHHNN.class)
    public Date getUltimaExecucao() {
        return ultimaExecucao;
    }

    public void setUltimaExecucao(Date ultimaExecucao) {
        this.ultimaExecucao = ultimaExecucao;
    }
}
