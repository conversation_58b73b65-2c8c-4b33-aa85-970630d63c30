/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.controller.json.base;

import br.com.pacto.base.oamd.BetaTestersService;
import br.com.pacto.base.util.Propagador;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.logging.Level;
import java.util.logging.Logger;

@Controller
@RequestMapping("/testers")
public class BetaTesterJSONControle extends SuperControle {


    @RequestMapping(method = {RequestMethod.GET})
    public @ResponseBody
    ModelMap get(@RequestParam(required = false) String reinit, @RequestParam(required = false) String all, HttpServletRequest request) {
        ModelMap mm = new ModelMap();
        try {

            if(reinit != null && reinit.equals("y")){
                BetaTestersService betaTestersService = new BetaTestersService();
                betaTestersService.init();
                HashMap hashMap = new HashMap();
                hashMap.put("reinit", "y");
                try {
                    Propagador.propagar(request);
                }catch (Exception e){
                    e.printStackTrace();
                }
            }

            if(all != null){
                BetaTestersService.applyAll =  all.equals("y");
            }

            mm.addAttribute("testers", BetaTestersService.applyAll ? "para todas as empresas" : BetaTestersService.testers);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(BetaTesterJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

}
