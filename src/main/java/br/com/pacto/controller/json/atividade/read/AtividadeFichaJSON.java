/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.controller.json.atividade.read;

import br.com.pacto.controller.json.base.SuperJSON;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class AtividadeFichaJSON extends SuperJSON {
    private String atividade;
    private String codigoAtividade;
    private Integer ordem;
    private String intensidade;
    private String batimentos;
    private List<SerieJSON> series = new ArrayList<SerieJSON>();
    @JsonDeserialize(as = ArrayList.class, contentAs = AjusteJSON.class)
    private List<AjusteJSON> ajustes = new ArrayList<AjusteJSON>();
    private Integer codMetodoExecucao;
    private String nomeMetodoExecucao;
    private Integer codFicha;

    public String getAtividade() {
        return atividade;
    }

    public void setAtividade(String atividade) {
        this.atividade = atividade;
    }

    public Integer getOrdem() {
        return ordem;
    }

    public void setOrdem(Integer ordem) {
        this.ordem = ordem;
    }

    public List<SerieJSON> getSeries() {
        return series;
    }

    public void setSeries(List<SerieJSON> series) {
        this.series = series;
    }

    public List<AjusteJSON> getAjustes() {
        return ajustes;
    }

    public void setAjustes(List<AjusteJSON> ajustes) {
        this.ajustes = ajustes;
    }

    public Integer getCodMetodoExecucao() {
        if (codMetodoExecucao == null) {
            codMetodoExecucao = 0;
        }
        return codMetodoExecucao;
    }

    public void setCodMetodoExecucao(Integer codMetodoExecucao) {
        this.codMetodoExecucao = codMetodoExecucao;
    }

    public String getNomeMetodoExecucao() {
        if (nomeMetodoExecucao == null) {
            nomeMetodoExecucao = "";
        }
        return nomeMetodoExecucao;
    }

    public void setNomeMetodoExecucao(String nomeMetodoExecucao) {
        this.nomeMetodoExecucao = nomeMetodoExecucao;
    }

    public String getCodigoAtividade() {
        return codigoAtividade;
    }

    public void setCodigoAtividade(String codigoAtividade) {
        this.codigoAtividade = codigoAtividade;
    }

    public String getIntensidade() {
        return intensidade;
    }

    public void setIntensidade(String intensidade) {
        this.intensidade = intensidade;
    }

    public String getBatimentos() {
        return batimentos;
    }

    public void setBatimentos(String batimentos) {
        this.batimentos = batimentos;
    }

    public Integer getCodFicha() {
        if (codFicha == null) {
            codFicha = 0;
        }
        return codFicha;
    }

    public void setCodFicha(Integer codFicha) {
        this.codFicha = codFicha;
    }
}
