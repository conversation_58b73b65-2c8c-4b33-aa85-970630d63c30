package br.com.pacto.controller.json.agendamento;

import br.com.pacto.bean.Agendamento;
import br.com.pacto.controller.json.aluno.AlunoSimplesDTO;
import br.com.pacto.controller.json.tipoEvento.TipoAgendamentoDTO;

public class AgendamentoEventoDTO{


    private Integer id;
    private Long dia;
    private String status;
    private TipoAgendamentoDTO tipoAgendamento;
    private Integer horarioInicial; //Informações em minutos
    private Integer horarioFinal; //Informações em minutos
    private ProfessorSubstitutoDTO professor;
    private AlunoSimplesDTO aluno;

    public AgendamentoEventoDTO(Agendamento agendamento) {
        this.id = agendamento.getCodigo();
        this.dia = agendamento.getInicio().getTime();
        this.status = agendamento.getStatus().getName();
        this.tipoAgendamento = new TipoAgendamentoDTO(agendamento.getTipoEvento());
        this.horarioInicial = agendamento.getMinutosInicio();
        this.horarioFinal = agendamento.getMinutosFim();
        this.professor = new ProfessorSubstitutoDTO(agendamento.getProfessor());
        this.aluno = new AlunoSimplesDTO(agendamento.getCliente());
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Long getDia() {
        return dia;
    }

    public void setDia(Long dia) {
        this.dia = dia;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public TipoAgendamentoDTO getTipoAgendamento() {
        return tipoAgendamento;
    }

    public void setTipoAgendamento(TipoAgendamentoDTO tipoAgendamento) {
        this.tipoAgendamento = tipoAgendamento;
    }

    public Integer getHorarioInicial() {
        return horarioInicial;
    }

    public void setHorarioInicial(Integer horarioInicial) {
        this.horarioInicial = horarioInicial;
    }

    public Integer getHorarioFinal() {
        return horarioFinal;
    }

    public void setHorarioFinal(Integer horarioFinal) {
        this.horarioFinal = horarioFinal;
    }

    public ProfessorSubstitutoDTO getProfessor() {
        return professor;
    }

    public void setProfessor(ProfessorSubstitutoDTO professor) {
        this.professor = professor;
    }

    public AlunoSimplesDTO getAluno() {
        return aluno;
    }

    public void setAluno(AlunoSimplesDTO aluno) {
        this.aluno = aluno;
    }
}
