package br.com.pacto.controller.json.aulaDia;

import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class SaldoAulaColetivaVO {
    private Integer disponiveis;
    private Integer utilizadas;
    private Integer expiradas;

    public Integer getDisponiveis() {
        return disponiveis;
    }

    public void setDisponiveis(Integer disponiveis) {
        this.disponiveis = disponiveis;
    }

    public Integer getUtilizadas() {
        return utilizadas;
    }

    public void setUtilizadas(Integer utilizadas) {
        this.utilizadas = utilizadas;
    }

    public Integer getExpiradas() {
        return expiradas;
    }

    public void setExpiradas(Integer expiradas) {
        this.expiradas = expiradas;
    }
}
