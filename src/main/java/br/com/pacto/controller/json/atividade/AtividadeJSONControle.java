/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pacto.controller.json.atividade;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.jpa.dto.TreinoAtividadeDTO;
import br.com.pacto.base.jpa.service.intf.PovoadorAtividadeService;
import br.com.pacto.base.jpa.service.intf.PovoadorService;
import br.com.pacto.base.util.Propagador;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.aparelho.AparelhoAjuste;
import br.com.pacto.bean.atividade.*;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.controller.json.atividade.read.AjusteJSON;
import br.com.pacto.controller.json.atividade.read.AtividadeJSON;
import br.com.pacto.bean.sincronizacao.TipoClassSincronizarEnum;
import br.com.pacto.controller.json.atividade.read.MetodoExecucaoJSON;
import br.com.pacto.controller.json.base.SuperControle;

import br.com.pacto.controller.json.programa.AtividadeFichaJSON;
import br.com.pacto.dao.intf.animacao.AnimacaoDao;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.security.aspecto.Permissao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.animacao.AnimacaoServiceImpl;
import br.com.pacto.service.intf.atividade.AtividadeService;
import br.com.pacto.service.intf.sincronizacao.SincronizacaoService;
import br.com.pacto.util.enumeradores.MidiasNiveisEnum;

import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;

/**
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("/atividades")
public class AtividadeJSONControle extends SuperControle {

    @Autowired
    private AtividadeService as;
    @Autowired
    private SincronizacaoService ss;
    @Autowired
    private PovoadorService povoadorService;
    @Autowired
    private PovoadorAtividadeService povoadorAtividadeService;
    @Autowired
    private AnimacaoServiceImpl animacaoService;
    @Autowired
    private AnimacaoDao animacaoDao;

    @RequestMapping(value = "{ctx}/todas", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap todas(@PathVariable String ctx, @RequestParam(required = false) Boolean crossfit) {
        ModelMap mm = new ModelMap();
        try {
            final String urlBase = getUrlImagem(ctx);
            List<Atividade> todas = as.obterTodos(ctx, true, false, crossfit == null ? false : crossfit);
            List<AtividadeJSON> listaJSON = new ArrayList<AtividadeJSON>();
            for (Atividade atividade : todas) {
                    listaJSON.add(preencherJSON(atividade, true, urlBase));
            }
            mm.addAttribute(RETURN, listaJSON);
        } catch (ServiceException ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(AtividadeJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/todas-full-relacionamentos", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap todasFullRelacionamentos(@PathVariable String ctx, @RequestParam(required = false) Boolean crossfit) {
        ModelMap mm = new ModelMap();
        try {
            final String urlBase = getUrlImagem(ctx);
            List<Atividade> todas = as.obterTodos(ctx, true, false, crossfit == null ? false : crossfit);
            List<AtividadeJSON> listaJSON = new ArrayList<AtividadeJSON>();
            for (Atividade atividade : todas) {
                listaJSON.add(preencherJSONFullRelacionamentos(ctx, atividade, urlBase));
            }
            mm.addAttribute(RETURN, listaJSON);
        } catch (ServiceException ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(AtividadeJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/inserir", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap inserir(@PathVariable String ctx) {
        ModelMap mm = new ModelMap();
        try {
            povoadorService.run(ctx);
            mm.addAttribute(STATUS_SUCESSO, ctx);
        } catch (ServiceException e) {
            mm.addAttribute(STATUS_ERRO, e.getMessage());
            Logger.getLogger(AtividadeJSONControle.class.getName()).log(Level.SEVERE, null, e);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/importar-atividades-by-csv", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap importarAtividadesByCsv(@PathVariable String ctx, @RequestBody Map<String, String> requestBody) {
        /*
        * OBS-01: para este fluxo deve-se obrigatoriamente o arquivo ser do tipo .csv e a divisão das colunas estarem com ; e não ,
        * OBS-02: para que este fluxo atenda a sua necessidade, o arquivo enviado deve estar com as colunas organizadas do seguinte modo:
        * NomeAtividade; Tipo; CategoriaDeAtividade; GrupoMuscular
        */
        ModelMap mm = new ModelMap();
        try {
            String csvBase64Data = requestBody.get("fileBase64");
            String retorno = povoadorAtividadeService.runPovoarByCsv(ctx, csvBase64Data);
            mm.addAttribute(STATUS_SUCESSO, retorno);
        } catch (ServiceException e) {
            mm.addAttribute(STATUS_ERRO, e.getMessage());
            Logger.getLogger(AtividadeJSONControle.class.getName()).log(Level.SEVERE, null, e);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/importar-atividades-by-dto", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap importarAtividadesByCsvV2(@PathVariable String ctx, @RequestBody List<TreinoAtividadeDTO> atividades) {

        ModelMap mm = new ModelMap();
        try {
            String retorno = povoadorAtividadeService.runPovoarByCsvV2(ctx, atividades);
            mm.addAttribute(STATUS_SUCESSO, retorno);
        } catch (ServiceException e) {
            mm.addAttribute(STATUS_ERRO, e.getMessage());
            Logger.getLogger(AtividadeJSONControle.class.getName()).log(Level.SEVERE, null, e);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/importar-atividades-ficha-series-by-dto", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap importarAtividadeFichaSerieJson(@PathVariable String ctx, @RequestBody List<AtividadeFichaJSON> atividadesFicha) {

        ModelMap mm = new ModelMap();
        try {
            String retorno = povoadorAtividadeService.importarAtividadeFichaSerieJson(ctx, atividadesFicha);
            mm.addAttribute(STATUS_SUCESSO, retorno);
        } catch (ServiceException e) {
            mm.addAttribute(STATUS_ERRO, e.getMessage());
            Logger.getLogger(AtividadeJSONControle.class.getName()).log(Level.SEVERE, null, e);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/metodosExecucao", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap metodosExecucao(@PathVariable String ctx) {
        ModelMap mm = new ModelMap();
        try {
            List<MetodoExecucaoEnum> metodos = Arrays.asList(MetodoExecucaoEnum.values());
            List<MetodoExecucaoJSON> retorno = new ArrayList<MetodoExecucaoJSON>();
            for (MetodoExecucaoEnum m : metodos) {
                retorno.add(new MetodoExecucaoJSON(m.getDescricao(), m.getId()));
            }
            mm.addAttribute(RETURN, retorno);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(AtividadeJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/sync", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap sync(@PathVariable String ctx, @RequestParam String dataHora) {
        ModelMap mm = new ModelMap();
        List<AtividadeJSON> arrJSON = new ArrayList();
        try {
            //super.init(ctx, token);
            final String urlBase = getUrlImagem(ctx);
            Date dataBase = Calendario.getDate(Calendario.MASC_DATAHORA, dataHora);
            List<Atividade> atividades = ss.obterLista(ctx,
                    TipoClassSincronizarEnum.Atividade, dataBase);
            for (Atividade atv : atividades) {
                AtividadeJSON json = preencherJSON(atv, true, urlBase);
                arrJSON.add(json);
            }
            mm.addAttribute(RETURN, arrJSON);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(AtividadeJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    private AtividadeJSON preencherJSON(Atividade atividade, boolean preencherCampoFiltro,
                                        final String urlBase) {
        AtividadeJSON atividadeJSON = new AtividadeJSON();
        atividadeJSON.setCod(atividade.getCodigo());
        atividadeJSON.setTipo(atividade.getTipo().getId());
        atividadeJSON.setNome(atividade.getNome());
        atividadeJSON.setDescricao(atividade.getDescricao());
        if (atividade.getFotoKey() != null && !atividade.getFotoKey().isEmpty()) {
            atividadeJSON.setImg(atividade.getFotoKey());
            atividadeJSON.setThumb(atividade.getFotoKeyPequena());
            atividadeJSON.setImgMedium(atividade.getFotoKeyMin());
        } else if (!atividade.getURLImg().isEmpty()) {
            atividadeJSON.setImg(urlBase + MidiasNiveisEnum.MOBILE_RETANGULO.getLocal() + atividade.getURLImg());
            atividadeJSON.setThumb(urlBase + MidiasNiveisEnum.MOBILE_QUADRADA.getLocal() + atividade.getURLImg());
            atividadeJSON.setImgMedium(urlBase + MidiasNiveisEnum.MEDIA_QUADRADA.getLocal() + atividade.getURLImg());
        }
        atividadeJSON.setImgMediumUrls(atividade.getImagemAtiviadeAssocidada(urlBase));
        if (preencherCampoFiltro) {
            StringBuilder sb = new StringBuilder();
            List<AtividadeCategoriaAtividade> categorias = atividade.getCategorias();
            for (AtividadeCategoriaAtividade atividadeCategoriaAtividade : categorias) {
                sb.append(atividadeCategoriaAtividade.getCategoriaAtividade().getNome()).append("|");
            }
            List<AtividadeGrupoMuscular> gruposMusculares = atividade.getGruposMusculares();
            for (AtividadeGrupoMuscular atividadeGrupoMuscular : gruposMusculares) {
                sb.append(atividadeGrupoMuscular.getGrupoMuscular().getNome()).append("|");
            }
            List<AtividadeMusculo> atividadeMusculos = atividade.getMusculos();
            for (AtividadeMusculo atividadeMusculo : atividadeMusculos) {
                sb.append(atividadeMusculo.getMusculo().getNome()).append("|");
            }
            List<AtividadeAparelho> aparelhos = atividade.getAparelhos();
            for (AtividadeAparelho atividadeAparelho : aparelhos) {
                sb.append(atividadeAparelho.getAparelho().getNome()).append("|");
            }
            atividadeJSON.setCamposFiltro(sb.toString());
            atividadeJSON.setImgMediumUrls(getImagemAtividadeUrls(urlBase, atividade));
        }
        return atividadeJSON;
    }

    private List<String> getImagemAtividadeUrls(String urlBase, Atividade atividade) {
        List<String> list = new ArrayList<String>();
        for (AtividadeAnimacao atvAnimacao : atividade.getAnimacoes()) {
            if (atvAnimacao.getFotoKey() == null) {
                if (atvAnimacao.getAnimacao() != null) {
                    if (atvAnimacao.getAnimacao().toString() != null && !atvAnimacao.getAnimacao().toString().equalsIgnoreCase(""))
                        list.add(urlBase + MidiasNiveisEnum.MEDIA_QUADRADA.getLocal() + atvAnimacao.getAnimacao());
                }
            } else {
                if (atvAnimacao.getUrlFotoPeq() == null || atvAnimacao.getUrlFotoPeq().isEmpty())
                    list.add(Aplicacao.obterUrlFotoDaNuvem(atvAnimacao.getFotoKey()));
                else
                    list.add(atvAnimacao.getUrlFotoPeq());
            }
        }
        return list;
    }

    private AtividadeJSON preencherAppJSON(Atividade atividade, boolean preencherCampoFiltro,
                                        final String urlBase) {
        AtividadeJSON atividadeJSON = new AtividadeJSON();
        atividadeJSON.setCod(atividade.getCodigo());
        atividadeJSON.setIaID(atividade.getIdIA());
        atividadeJSON.setTipo(atividade.getTipo().getId());
        atividadeJSON.setNome(atividade.getNome());
        atividadeJSON.setDescricao(atividade.getDescricao());
        if (atividade.getFotoKey() != null && !atividade.getFotoKey().isEmpty()) {
            atividadeJSON.setImg(atividade.getFotoKey());
            atividadeJSON.setThumb(atividade.getFotoKeyPequena());
            atividadeJSON.setImgMedium(atividade.getFotoKeyMin());
        } else if (!atividade.getURLImg().isEmpty()) {
            atividadeJSON.setImg(urlBase + MidiasNiveisEnum.MOBILE_RETANGULO.getLocal() + atividade.getURLImg());
            atividadeJSON.setThumb(urlBase + MidiasNiveisEnum.MOBILE_QUADRADA.getLocal() + atividade.getURLImg());
            atividadeJSON.setImgMedium(urlBase + MidiasNiveisEnum.MEDIA_QUADRADA.getLocal() + atividade.getURLImg());
        }
        atividadeJSON.setImgMediumUrls(atividade.getImagemAtiviadeAssocidada(urlBase));

        List <AtividadeAparelho> atividadeAparelhos = atividade.getAparelhos();
        List<AjusteJSON> ajusteJSONS = new ArrayList<>();
        for(AtividadeAparelho atividadeAparelho : atividadeAparelhos)
        {
            List<AparelhoAjuste> aparelhoAjustes = atividadeAparelho.getAparelho().getAjustes();
            for(AparelhoAjuste aparelhoAjuste: aparelhoAjustes)
            {
                AjusteJSON ajuste = new AjusteJSON();
                ajuste.setCodAtividade(atividade.getCodigo());
                ajuste.setNome(aparelhoAjuste.getNome());
                Boolean novoAjuste = true;
                for(AjusteJSON ajusteJSON: ajusteJSONS)
                {
                    if(ajusteJSON.getNome().compareTo(ajuste.getNome()) != 0)
                    {
                        continue;
                    }
                    novoAjuste = false;
                    break;
                }
                if(novoAjuste){
                    ajusteJSONS.add(ajuste);
                }
            }
        }
        atividadeJSON.setAjustes(ajusteJSONS);
        if (preencherCampoFiltro) {
            StringBuilder sb = new StringBuilder();
            List<AtividadeCategoriaAtividade> categorias = atividade.getCategorias();
            for (AtividadeCategoriaAtividade atividadeCategoriaAtividade : categorias) {
                sb.append(atividadeCategoriaAtividade.getCategoriaAtividade().getNome()).append("|");
            }
            List<AtividadeGrupoMuscular> gruposMusculares = atividade.getGruposMusculares();
            for (AtividadeGrupoMuscular atividadeGrupoMuscular : gruposMusculares) {
                sb.append(atividadeGrupoMuscular.getGrupoMuscular().getNome()).append("|");
            }
            List<AtividadeMusculo> atividadeMusculos = atividade.getMusculos();
            for (AtividadeMusculo atividadeMusculo : atividadeMusculos) {
                sb.append(atividadeMusculo.getMusculo().getNome()).append("|");
            }
            List<AtividadeAparelho> aparelhos = atividade.getAparelhos();
            for (AtividadeAparelho atividadeAparelho : aparelhos) {
                sb.append(atividadeAparelho.getAparelho().getNome()).append("|");
            }
            atividadeJSON.setCamposFiltro(sb.toString());
            atividadeJSON.setImgMediumUrls(getImagemAtividadeUrls(urlBase, atividade));
        }
        return atividadeJSON;
    }


    @RequestMapping(value = "{ctx}/app/consultarTodasAtividades", method = RequestMethod.GET)
    public @ResponseBody
    ModelMap consultarTodasAtividadesApp(@PathVariable String ctx, @RequestParam(required = false) Boolean crossfit) {
        ModelMap mm = new ModelMap();
        try {
            if (as.isAtividadesCached(ctx, crossfit)) {
                Uteis.logarDebug("Todas atividades from CACHE %s for %s crossfit: ".format(ctx, crossfit));
                return mm.addAttribute(STATUS_SUCESSO, as.getAtividadesCache(ctx, crossfit));
            }
            final String urlBase = TemaAtividade.getURLBase(ctx);
            List<Atividade> todas = as.obterTodos(ctx, true, false, crossfit == null ? false : crossfit);
            List<AtividadeJSON> listaJSON = new ArrayList<AtividadeJSON>();
            for (Atividade atividade : todas) {
                listaJSON.add(preencherAppJSON(atividade, true, urlBase));
            }
            as.storeAtividadesCache(ctx, listaJSON, crossfit);
            mm.addAttribute(STATUS_SUCESSO, listaJSON);
        } catch (ServiceException ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(AtividadeJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/app/reloadCache", method = RequestMethod.GET)
    public @ResponseBody
    ModelMap refreshCache(HttpServletRequest request, @PathVariable String ctx) {
        ModelMap mm = new ModelMap();
        if (as.isAtividadesCached(ctx, false) || as.isAtividadesCached(ctx, true)) {
            as.purgeAtividadesCache(ctx);
            Propagador.propagar(request);
        }
        return mm.addAttribute(STATUS_SUCESSO, "Cache reloaded!");
    }

    private static String tratarMensagem(String mensagemOriginal, String trecho) {
        int indice = mensagemOriginal.indexOf(trecho);
        if (indice != -1) {
            return mensagemOriginal.substring(0, indice) +
                    mensagemOriginal.substring(indice + trecho.length());
        }
        return mensagemOriginal;
    }

    private AtividadeJSON preencherJSONFullRelacionamentos(String ctx, Atividade atividade, final String urlBase) throws ServiceException {
        AtividadeJSON atividadeJSON = new AtividadeJSON();
        atividadeJSON.setCod(atividade.getCodigo());
        atividadeJSON.setTipo(atividade.getTipo().getId());
        atividadeJSON.setNome(atividade.getNome());
        atividadeJSON.setDescricao(atividade.getDescricao());
        atividadeJSON.setAtivo(atividade.isAtivo());
        if (atividade.getFotoKey() != null && !atividade.getFotoKey().isEmpty()) {
            String trecho = Aplicacao.getProp(Aplicacao.urlFotosNuvem) + '/';
            String fotoKey = tratarMensagem(atividade.getFotoKey(), trecho);
            String fotoKeyPequena = tratarMensagem(atividade.getFotoKeyPequena(), trecho);
            String fotoKeyMin = tratarMensagem(atividade.getFotoKeyMin(), trecho);
            atividadeJSON.setImg(fotoKey);
            atividadeJSON.setThumb(fotoKeyPequena);
            atividadeJSON.setImgMedium(fotoKeyMin);
        }
        atividadeJSON.setImgMediumUrls(atividade.getImagemAtiviadeAssocidada(urlBase));
        atividadeJSON.setUrlVideo(atividade.getLinkVideo());

        List<String> animacoes = new ArrayList<>();
        for(AtividadeAnimacao a : atividade.getAnimacoes()) {
            if(a.getAnimacao() != null) {
                animacoes.add(a.getAnimacao().getTitulo());
            }
        }
        atividadeJSON.setAnimacoes(animacoes);

        List<String> gruposMusculares = new ArrayList<>();
        for(AtividadeGrupoMuscular gm : atividade.getGruposMusculares()) {
            gruposMusculares.add(gm.getGrupoMuscular().getNome());
        }
        atividadeJSON.setGruposMusculares(gruposMusculares);

        List<String> categoriasAtividade = new ArrayList<>();
        for(AtividadeCategoriaAtividade ca : atividade.getCategorias()) {
            categoriasAtividade.add(ca.getCategoriaAtividade().getNome());
        }
        atividadeJSON.setCategorias(categoriasAtividade);

        List<String> niveisAtividade = new ArrayList<>();
        for(AtividadeNivel n : atividade.getNiveis()) {
            niveisAtividade.add(n.getNivel().getNome());
        }
        atividadeJSON.setNiveis(niveisAtividade);

        List<String> aparelhosAtividade = new ArrayList<>();
        for(AtividadeAparelho a : atividade.getAparelhos()) {
            aparelhosAtividade.add(a.getAparelho().getNome());
        }
        atividadeJSON.setListAparelhos(aparelhosAtividade);

        return atividadeJSON;
    }


    @RequestMapping(value = "{ctx}/syncIA/{ctxIA}", method = RequestMethod.PUT)
    public @ResponseBody
    ModelMap syncIA(@PathVariable String ctx, @PathVariable String ctxIA) {
        ModelMap mm = new ModelMap();
        try {
            as.syncAtividadesIA(ctx, ctxIA);
            mm.addAttribute(RETURN, "sync ok");
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(AtividadeJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ResponseBody
    @RequestMapping(value = "{ctx}/{id}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    ModelMap atualizaAtividadeFicha(@PathVariable String ctx,
                                                                      @PathVariable("id") final Integer id,
                                                                      @RequestParam final Integer codAtividadeOriginal,
                                                                      @RequestParam final Integer codAtividadeSubstituir) {
        ModelMap mm = new ModelMap();
        try {
            mm.addAttribute(RETURN, as.atualizarAtividadePorFicha(ctx, codAtividadeOriginal, codAtividadeSubstituir, id));
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(AtividadeJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }
}
