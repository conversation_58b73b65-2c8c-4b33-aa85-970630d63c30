/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.controller.json.professor;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.gestaopersonal.CreditoPersonal;
import br.com.pacto.bean.gestaopersonal.TipoOperacaoPersonalEnum;
import br.com.pacto.bean.professor.ProfessorDTO;
import br.com.pacto.bean.professor.ProfessorSintetico;
import br.com.pacto.controller.json.base.SuperControle;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.aulapersonal.AulaPersonalService;
import br.com.pacto.service.intf.creditopersonal.CreditoPersonalService;
import br.com.pacto.service.intf.gestao.DashboardBIService;
import br.com.pacto.service.intf.professor.ProfessorSinteticoService;
import br.com.pacto.util.UtilContext;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("/professor")
public class ProfessorSinteticoJSONControle extends SuperControle {

    @Autowired
    private ProfessorSinteticoService ps;

    @RequestMapping(value = "{ctx}/todos", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap professorSintetico(@PathVariable String ctx,
                                @RequestParam Integer codEmpresa,
                                @RequestParam(required = false) Boolean somenteAtivos,
                                @RequestParam(required = false) Boolean professorDTO) {
        ModelMap mm = new ModelMap();
        try {
            List<ProfessorSintetico> lista = ps.obterTodos(ctx, codEmpresa, (somenteAtivos == null || somenteAtivos));
            if (professorDTO != null && professorDTO) {
                List<ProfessorDTO> arrJSON = new ArrayList<>();
                for (ProfessorSintetico prof : lista) {
                    arrJSON.add(new ProfessorDTO(prof));
                }
                mm.addAttribute(RETURN, arrJSON);
            } else {
                List<ProfessorJSON> arrJSON = new ArrayList<>();
                for (ProfessorSintetico prof : lista) {
                    arrJSON.add(ProfessorJSON.obterJSON(prof));
                }
                mm.addAttribute(RETURN, arrJSON);
            }
        } catch (ServiceException ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ProfessorSinteticoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/professorDisponivel", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap professorDisponivel(@PathVariable String ctx,
            @RequestParam Integer codProfessor, @RequestParam Boolean disponivel) {
        ModelMap mm = new ModelMap();
        try {
            ProfessorSintetico prof = ps.obterPorId(ctx, codProfessor);
            if (prof != null) {
                ps.marcarProfessorDisponivel(ctx, prof, disponivel);
                mm.addAttribute(STATUS, STATUS_SUCESSO);
            } else {
                mm.addAttribute(STATUS_ERRO, getViewUtils().getMensagem("validacao.professor"));
            }
        } catch (ServiceException ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ProfessorSinteticoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/veriFicarProfessorDisponivel", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap veriFicarProfessorDisponivel(@PathVariable String ctx,
            @RequestParam Integer codProfessor) {
        ModelMap mm = new ModelMap();
        try {
            ProfessorSintetico prof = ps.obterPorId(ctx, codProfessor);
            if (prof != null) {
                mm.addAttribute(RETURN, ps.verificarProfessorDisponivel(ctx, prof));
            } else {
                mm.addAttribute(STATUS_ERRO, getViewUtils().getMensagem("validacao.professor"));
            }
        } catch (ServiceException ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ProfessorSinteticoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }
    
    @RequestMapping(value = "{ctx}/expirarCreditos", method = {RequestMethod.POST, RequestMethod.GET})
    public @ResponseBody
    ModelMap expirarCreditos(@PathVariable final String ctx) {
        ModelMap mm = new ModelMap();
        try {
            CreditoPersonalService cps = (CreditoPersonalService) UtilContext.getBean(CreditoPersonalService.class);
            cps.expirarCreditos(ctx, Calendario.hoje());
            mm.addAttribute(RETURN, "Processo de expirar créditos executado com sucesso!");
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ProfessorSinteticoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/corrigir-creditos", method = {RequestMethod.POST, RequestMethod.GET})
    public @ResponseBody
    ModelMap corrigirCreditos(@PathVariable final String ctx) {
        ModelMap mm = new ModelMap();
        try {
            AulaPersonalService cps = (AulaPersonalService) UtilContext.getBean(AulaPersonalService.class);
            cps.normalizarChekins(ctx);
            mm.addAttribute(RETURN, "Corrigidos!");
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ProfessorSinteticoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/creditos-aula", method = {RequestMethod.POST, RequestMethod.GET})
    public @ResponseBody
    ModelMap expirarCreditos(@PathVariable final String ctx, @RequestParam Integer p, @RequestParam String d ) {
        ModelMap mm = new ModelMap();
        try {
            AulaPersonalService cps = (AulaPersonalService) UtilContext.getBean(AulaPersonalService.class);
            cps.normalizarCreditos(ctx, p, d);
            mm.addAttribute(RETURN, "créditos atualizados!");
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ProfessorSinteticoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/processarBIProfessores", method = {RequestMethod.POST, RequestMethod.GET})
    public @ResponseBody
    ModelMap processarBIProfessores(@PathVariable final String ctx,@RequestParam(required = false) String dia) {
        ModelMap mm = new ModelMap();
        try {
            Date data = null;
            try{
                data = Uteis.getDate(dia);
            }catch (Exception ignored){}

            DashboardBIService dashboardBIService = (DashboardBIService) UtilContext.getBean(DashboardBIService.class);
            dashboardBIService.processarBIProfessores(ctx, data == null ? Calendario.hoje() : data);
            mm.addAttribute(RETURN, "BI dos Professores processado com sucesso!");
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ProfessorSinteticoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/processarBIProfessoresAcessosExecucoes", method = {RequestMethod.POST, RequestMethod.GET})
    public @ResponseBody
    ModelMap processarBIProfessoresAcessosExecucoes(@PathVariable final String ctx,
                                                    @RequestParam(required = false) Integer mes,
                                                    @RequestParam(required = false) Integer ano) {
        ModelMap mm = new ModelMap();
        try {

            Calendar dateInit = Calendario.getInstance(ano, mes, 1);

            DashboardBIService dashboardBIService = (DashboardBIService) UtilContext.getBean(DashboardBIService.class);

            while (dateInit.get(Calendar.MONTH) + 1 == mes
                    && Calendario.maiorOuIgual(Calendario.hoje(), dateInit.getTime())){
                dashboardBIService.ajustarAcessosExecucoesBI(ctx, dateInit.getTime());
                dateInit.add(Calendar.DAY_OF_MONTH, 1);
            }

            mm.addAttribute(RETURN, "BI dos Professores 'AcessosExecucoes' processado com sucesso!");
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ProfessorSinteticoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }


    @RequestMapping(value = "{ctx}/em-atendimento/{id}", method = {RequestMethod.GET})
    public @ResponseBody
    ModelMap emAtendimento(@PathVariable final String ctx,
                           @PathVariable Integer id) {
        ModelMap mm = new ModelMap();
        try {
            AulaPersonalService cps = UtilContext.getBean(AulaPersonalService.class);
            mm.addAttribute(RETURN, cps.personalEmAtendimento(ctx, id));
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ProfessorSinteticoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/credito-recibo", method = {RequestMethod.POST})
    public @ResponseBody
    ModelMap inserirCreditos(@PathVariable String ctx,
                                  @RequestParam Integer colaborador,
                                  @RequestParam Integer unidades,
                                  @RequestParam  Integer recibozw,
                                  @RequestParam  String dataExpiracao) {
        ModelMap mm = new ModelMap();
        try {
            Date expiracao = dataExpiracao == null || dataExpiracao.isEmpty() ? null : Uteis.getDate(dataExpiracao);
            CreditoPersonalService creditoService = UtilContext.getBean(CreditoPersonalService.class);
            CreditoPersonal inserido = creditoService.inserirCreditos(ctx, colaborador, unidades,
                    recibozw, null, TipoOperacaoPersonalEnum.COMPRA_CREDITOS, expiracao);
            mm.addAttribute(RETURN, inserido.getCodigo().toString());
        } catch (Exception ex) {
            Logger.getLogger(ProfessorSinteticoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
        }
        return mm;
    }

    @RequestMapping(value = "/sync/{ctx}/{empresa}", method = {RequestMethod.POST})
    public @ResponseBody
    ModelMap sync(@PathVariable String ctx,
                                  @PathVariable Integer empresa) {
        ModelMap mm = new ModelMap();
        try {
            new Thread() {
                @Override
                public void run() {
                    try {
                        ps.sincronizarTodosColaboradores(ctx, empresa);
                    } catch (Exception e) {
                        Uteis.logar(e, this.getClass());
                    }
                }
            }.start();
            mm.addAttribute(RETURN, "apitou, começou.");
        } catch (Exception ex) {
            Logger.getLogger(ProfessorSinteticoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
        }
        return mm;
    }

    @ResponseBody
    @RequestMapping(value = "/sincronizar-professores/{ctx}", method = {RequestMethod.POST})
    public ResponseEntity<EnvelopeRespostaDTO> sincronizarProfessores(@PathVariable String ctx,
                                                                      @RequestParam(required = false) Boolean assincrono) {
        try {
            if (assincrono == null || assincrono) {
                new Thread() {
                    @Override
                    public void run() {
                        try {
                            String ret = ps.sincronizarTodosProfessores(ctx);
                            Uteis.logar(true, null, "sincronizarProfessores | " + ret);
                        } catch (Exception e) {
                            Uteis.logar(e, this.getClass());
                        }
                    }
                }.start();
                return ResponseEntityFactory.ok("Começou sincronizar-professores");
            } else {
                String ret = ps.sincronizarTodosProfessores(ctx);
                Uteis.logar(true, null, "sincronizarProfessores | " + ret);
                return ResponseEntityFactory.ok(ret);
            }
        } catch (ServiceException e) {
            Logger.getLogger(ProfessorSinteticoJSONControle.class.getName()).log(Level.SEVERE, "Erro ao executar o processo", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }
}
