package br.com.pacto.controller.json.graduacao;

import br.com.pacto.controller.json.base.SuperControle;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.intf.graduacao.GraduacaoService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import java.net.URLDecoder;
import java.util.logging.Level;
import java.util.logging.Logger;

@Controller
@RequestMapping("/graduacao")
public class GraduacaoJSONControle extends SuperControle {

    @Autowired
    private GraduacaoService graduacaoService;
    @Autowired
    private ServletContext sc;

    @Override
    public void setServletContext(ServletContext servletContext) {
        this.sc = servletContext;
    }

    @RequestMapping(value = "/{ctx}/gerar-pdf-avaliacao-progresso-aluno", method = RequestMethod.POST)
    public
    @ResponseBody
    ModelMap gerarPdfAvaliacaoProgressoAluno(@PathVariable final String ctx,
                                             @RequestBody final String dadosImprimir,
                                             HttpServletRequest request) {
        ModelMap mm = new ModelMap();
        try {
            Uteis.logarDebug("#### IMPRESSÃO AVALIAÇÃO DE PROGRESSO ESTÁ SENDO CHAMADA PELA ROTA ANTIGA");
            String dadosImprimirDecoded = URLDecoder.decode(dadosImprimir, "UTF-8");
            ObjectMapper objectMapper = new ObjectMapper();
            ImpressaoAvaliacaoProgressoAlunoDTO dadosImprimirDTO = objectMapper.readValue(dadosImprimirDecoded, ImpressaoAvaliacaoProgressoAlunoDTO.class);

            String urlAplicacao = Uteis.getURLValidaProtocolo(request);
            String urlRemove = request.getPathInfo();
            urlRemove = urlRemove.replaceAll("/gerar-pdf-avaliacao-progresso-aluno", "");
            urlAplicacao = urlAplicacao.replaceAll("prest" + urlRemove, "");

            String urlPdf = graduacaoService.gerarPdfAvaliacaoProgresso(ctx, request, true, dadosImprimirDTO, urlAplicacao);
            mm.addAttribute("return", urlPdf);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }


}
