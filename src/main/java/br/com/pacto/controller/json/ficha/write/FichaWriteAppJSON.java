package br.com.pacto.controller.json.ficha.write;

import br.com.pacto.bean.atividade.AtividadeFicha;
import br.com.pacto.bean.ficha.Ficha;
import br.com.pacto.bean.pessoa.SexoEnum;
import br.com.pacto.bean.programa.ProgramaTreinoFicha;
import br.com.pacto.bean.serie.Serie;
import br.com.pacto.controller.json.base.SuperJSON;
import br.com.pacto.controller.json.nivel.NivelWriteJSON;
import br.com.pacto.controller.json.programa.read.AtividadeAppJSON;
import br.com.pacto.controller.json.programa.read.ProgramaTreinoFichaAppJSON;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.enumeradores.MidiasNiveisEnum;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
@JsonIgnoreProperties
public class FichaWriteAppJSON extends SuperJSON {
    private Integer codigo;
    private String username;
    private String nome;
    private String mensagemAluno;
    private Integer categoria;
    private String categoriaFicha;
    private Boolean usarComoPredefinida = false;
    private Integer versao;
    private List<AtividadeAppJSON> atividadesFicha = new ArrayList<AtividadeAppJSON>();
    private List<ProgramaTreinoFichaAppJSON> programasFicha = new ArrayList<ProgramaTreinoFichaAppJSON>();
    private Boolean ativo = true;
    private NivelWriteJSON nivel;
    private SexoEnum sexo;

    public FichaWriteAppJSON(Ficha ficha, String urlBase)
    {
        this.usarComoPredefinida = ficha.isUsarComoPredefinida();
        this.codigo = ficha.isUsarComoPredefinida() ? null : ficha.getCodigo();
        this.nome = ficha.getNome();
        this.mensagemAluno = ficha.getMensagemAluno();
        this.categoria = ficha.getCategoria() != null ? ficha.getCategoria().getCodigo() : null;
        this.categoriaFicha = ficha.getNomeCategoria();
        this.versao = ficha.getVersao();
        this.ativo = ficha.getAtivo();
        this.nivel = new NivelWriteJSON(ficha.getNivel());
        this.sexo = ficha.getSexo() != null ? ficha.getSexo() : SexoEnum.N;

        List<AtividadeFicha> afs = ficha.getAtividades();
        String urlThumb = urlBase + MidiasNiveisEnum.MOBILE_QUADRADA.getLocal();
        for(AtividadeFicha af: afs)
        {
            if(usarComoPredefinida)
            {
                af.setFicha(null);
                af.setCodigo(null);
            }
            List<Serie> series = af.getSeries();
            for(Serie serie : series)
            {
                if(usarComoPredefinida)
                {
                    serie.setCodigo(null);
                    serie.setAtividadeFicha(null);
                }
            }
            AtividadeAppJSON atividadeAppJSON = new AtividadeAppJSON(af);
            if(UteisValidacao.emptyString(atividadeAppJSON.getThumb()))
            {
                atividadeAppJSON.setThumb(urlThumb + af.getAtividade().getURLImg());
            }
            this.atividadesFicha.add(atividadeAppJSON);
        }

        List<ProgramaTreinoFicha> programaTreinoFichas = ficha.getProgramas();
        for(ProgramaTreinoFicha programaTreinoFicha : programaTreinoFichas){
            this.programasFicha.add((new ProgramaTreinoFichaAppJSON(programaTreinoFicha, urlBase)));
        }

    }


    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getMensagemAluno() {
        return mensagemAluno;
    }

    public void setMensagemAluno(String mensagemAluno) {
        this.mensagemAluno = mensagemAluno;
    }

    public Integer getCategoria() {
        return categoria;
    }

    public void setCategoria(Integer categoria) {
        this.categoria = categoria;
    }

    public String getCategoriaFicha() {
        return categoriaFicha;
    }

    public void setCategoriaFicha(String categoriaFicha) {
        this.categoriaFicha = categoriaFicha;
    }

    public Boolean getUsarComoPredefinida() {
        return usarComoPredefinida;
    }

    public void setUsarComoPredefinida(Boolean usarComoPredefinida) {
        this.usarComoPredefinida = usarComoPredefinida;
    }

    public Integer getVersao() {
        return versao;
    }

    public void setVersao(Integer versao) {
        this.versao = versao;
    }

    public List<AtividadeAppJSON> getAtividadesFicha() {
        return atividadesFicha;
    }

    public void setAtividadesFicha(List<AtividadeAppJSON> atividadesFicha) {
        this.atividadesFicha = atividadesFicha;
    }

    public List<ProgramaTreinoFichaAppJSON> getProgramasFicha() {
        return programasFicha;
    }

    public void setProgramasFicha(List<ProgramaTreinoFichaAppJSON> programasFicha) {
        this.programasFicha = programasFicha;
    }

    public Boolean getAtivo() {
        return ativo;
    }

    public void setAtivo(Boolean ativo) {
        this.ativo = ativo;
    }

    public NivelWriteJSON getNivel() {
        return nivel;
    }

    public void setNivel(NivelWriteJSON nivel) {
        this.nivel = nivel;
    }

    public SexoEnum getSexo() {
        return sexo;
    }

    public void setSexo(SexoEnum sexo) {
        this.sexo = sexo;
    }
}
