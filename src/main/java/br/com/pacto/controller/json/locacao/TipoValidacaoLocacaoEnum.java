package br.com.pacto.controller.json.locacao;

public enum TipoValidacaoLocacaoEnum {

    LIVRE(0, "Livre"),
    PLANO(1, "Plano"),
    PRODUTO(2, "Produto")
    ;

    private Integer codigo;
    private String descricao;

    TipoValidacaoLocacaoEnum(Integer codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public static TipoValidacaoLocacaoEnum getByCodigo(Integer codigo) {
        for (TipoValidacaoLocacaoEnum tipoValidacaoLocacaoEnum: TipoValidacaoLocacaoEnum.values()) {
            if (tipoValidacaoLocacaoEnum.getCodigo().equals(codigo)) {
                return tipoValidacaoLocacaoEnum;
            }
        }
        return null;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}
