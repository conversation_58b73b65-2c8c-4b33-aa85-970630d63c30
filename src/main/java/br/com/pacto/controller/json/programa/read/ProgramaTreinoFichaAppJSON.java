/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.controller.json.programa.read;

import br.com.pacto.bean.atividade.AtividadeFicha;
import br.com.pacto.bean.programa.ProgramaTreinoFicha;
import br.com.pacto.controller.json.base.SuperJSON;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.enumeradores.MidiasNiveisEnum;

import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class ProgramaTreinoFichaAppJSON extends SuperJSON {

    private Integer codigo;
    private Integer programa;
    private Integer ficha;
    private Integer versaoFicha;
    private String nomeFicha;
    private Integer tipoExecucao;
    private List<String> diaSemana = new ArrayList<String>();//<List>DiasSemana.class
    private Integer numeroAtividades;
    private Long ultimaExecucao;
    private List<AtividadeAppJSON> atividadesFicha = new ArrayList<AtividadeAppJSON>();
    private Integer categoria;
    private String categoriaFicha;

    public ProgramaTreinoFichaAppJSON() {
    }

    public ProgramaTreinoFichaAppJSON(ProgramaTreinoFicha programaTreinoFicha, final String urlBase) {
        this.codigo = programaTreinoFicha.getCodigo();
        this.programa =  programaTreinoFicha.getPrograma() != null ? programaTreinoFicha.getPrograma().getCodigo() : null;
        this.tipoExecucao = programaTreinoFicha.getTipoExecucao() != null ? programaTreinoFicha.getTipoExecucao().getId() : null;
        this.diaSemana = programaTreinoFicha.getDiaSemana();

        if(programaTreinoFicha.getFicha() != null)
        {
            this.ficha = programaTreinoFicha.getFicha().getCodigo();
            this.nomeFicha = programaTreinoFicha.getFicha().getNome();
            this.numeroAtividades = programaTreinoFicha.getFicha().getAtividades() == null ? 0 : programaTreinoFicha.getFicha().getAtividades().size();
            this.ultimaExecucao = programaTreinoFicha.getFicha().getUltimaExecucao() == null ? null : programaTreinoFicha.getFicha().getUltimaExecucao().getTime();
            this.versaoFicha = programaTreinoFicha.getFicha().getVersao();
            this.atividadesFicha = preencheAtividades(programaTreinoFicha.getFicha().getAtividades(), urlBase);
            this.categoria = programaTreinoFicha.getFicha().getCategoria() == null ? null : programaTreinoFicha.getFicha().getCategoria().getCodigo();
            this.categoriaFicha = programaTreinoFicha.getFicha().getNomeCategoria();
        }
    }

    private List<AtividadeAppJSON> preencheAtividades(List<AtividadeFicha> atividadeFichas, final String urlBase)
    {
        if(UteisValidacao.emptyList(atividadeFichas))
        {
           return null;
        }
        List<AtividadeAppJSON> atividadeAppJSONS = new ArrayList<>();
        String urlThumb = urlBase + MidiasNiveisEnum.MOBILE_QUADRADA.getLocal();
        for(AtividadeFicha atividadeFicha : atividadeFichas)
        {
            AtividadeAppJSON atividadeAppJSON = new AtividadeAppJSON(atividadeFicha);
            if(UteisValidacao.emptyString(atividadeAppJSON.getThumb()))
            {
                atividadeAppJSON.setThumb(urlThumb + atividadeFicha.getAtividade().getURLImg());
            }
            atividadeAppJSONS.add(atividadeAppJSON);
        }
        return atividadeAppJSONS;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getPrograma() {
        return programa;
    }

    public void setPrograma(Integer programa) {
        this.programa = programa;
    }

    public Integer getFicha() {
        return ficha;
    }

    public void setFicha(Integer ficha) {
        this.ficha = ficha;
    }

    public Integer getTipoExecucao() {
        return tipoExecucao;
    }

    public void setTipoExecucao(Integer tipoExecucao) {
        this.tipoExecucao = tipoExecucao;
    }

    public List<String> getDiaSemana() {
        return diaSemana;
    }

    public void setDiaSemana(List<String> diaSemana) {
        this.diaSemana = diaSemana;
    }

    public String getNomeFicha() {
        return nomeFicha;
    }

    public void setNomeFicha(String nomeFicha) {
        this.nomeFicha = nomeFicha;
    }

    public Integer getNumeroAtividades() {
        return numeroAtividades;
    }

    public void setNumeroAtividades(Integer numeroAtividades) {
        this.numeroAtividades = numeroAtividades;
    }

    public Long getUltimaExecucao() {
        return ultimaExecucao;
    }

    public void setUltimaExecucao(Long ultimaExecucao) {
        this.ultimaExecucao = ultimaExecucao;
    }

    public Integer getVersaoFicha() {
        return versaoFicha;
    }

    public void setVersaoFicha(Integer versaoFicha) {
        this.versaoFicha = versaoFicha;
    }

    public List<AtividadeAppJSON> getAtividadesFicha() {
        return atividadesFicha;
    }

    public void setAtividadesFicha(List<AtividadeAppJSON> atividadesFicha) {
        this.atividadesFicha = atividadesFicha;
    }

    public Integer getCategoria() {
        return categoria;
    }

    public void setCategoria(Integer categoria) {
        this.categoria = categoria;
    }

    public String getCategoriaFicha() {
        return categoriaFicha;
    }

    public void setCategoriaFicha(String categoriaFicha) {
        this.categoriaFicha = categoriaFicha;
    }
}
