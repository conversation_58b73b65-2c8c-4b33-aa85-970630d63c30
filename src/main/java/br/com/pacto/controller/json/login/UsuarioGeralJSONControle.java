/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.controller.json.login;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.base.SuperControle;
import br.com.pacto.security.dto.AlterarEmailDTO;
import br.com.pacto.security.dto.AlterarSenhaDTO;
import br.com.pacto.security.dto.AlterarTelefoneDTO;
import br.com.pacto.security.dto.AlterarUsuarioGeralDTO;
import br.com.pacto.security.dto.DesvincularUsuarioDTO;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.login.UsuarioGeralService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.logging.Level;
import java.util.logging.Logger;

@Controller
@RequestMapping("/v1/usuarioGeral")
public class UsuarioGeralJSONControle extends SuperControle {

    private final UsuarioGeralService usuarioGeralService;

    @Autowired
    public UsuarioGeralJSONControle(UsuarioGeralService usuarioGeralService) {
        this.usuarioGeralService = usuarioGeralService;
    }

    @RequestMapping(value = "{ctx}/senha", method = RequestMethod.POST)
    public ResponseEntity<EnvelopeRespostaDTO> senhaAlterar(@PathVariable String ctx,
                                                            @RequestBody AlterarSenhaDTO dto) {
        try {
            usuarioGeralService.alterarSenha(ctx, dto);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(UsuarioGeralJSONControle.class.getName()).log(Level.SEVERE, "Erro ao alterar senha usuário", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            Logger.getLogger(UsuarioGeralJSONControle.class.getName()).log(Level.SEVERE, "Erro ao alterar senha usuário", e);
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }

    @RequestMapping(value = "{ctx}/email", method = RequestMethod.POST)
    public ResponseEntity<EnvelopeRespostaDTO> emailAlterar(@PathVariable String ctx,
                                                            @RequestBody AlterarEmailDTO dto) {
        try {
            usuarioGeralService.alterarEmail(ctx, dto);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(UsuarioGeralJSONControle.class.getName()).log(Level.SEVERE, "Erro ao alterar senha usuário", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            Logger.getLogger(UsuarioGeralJSONControle.class.getName()).log(Level.SEVERE, "Erro ao alterar senha usuário", e);
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }

    @RequestMapping(value = "{ctx}/telefone", method = RequestMethod.POST)
    public ResponseEntity<EnvelopeRespostaDTO> telefoneAlterar(@PathVariable String ctx,
                                                               @RequestBody AlterarTelefoneDTO dto) {
        try {
            usuarioGeralService.alterarTelefone(ctx, dto);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(UsuarioGeralJSONControle.class.getName()).log(Level.SEVERE, "Erro ao alterar senha usuário", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            Logger.getLogger(UsuarioGeralJSONControle.class.getName()).log(Level.SEVERE, "Erro ao alterar senha usuário", e);
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }

    @RequestMapping(value = "{ctx}/usuarioGeral", method = RequestMethod.POST)
    public ResponseEntity<EnvelopeRespostaDTO> telefoneUsuarioGeral(@PathVariable String ctx,
                                                                    @RequestBody AlterarUsuarioGeralDTO dto) {
        try {
            usuarioGeralService.alterarUsuarioGeral(ctx, dto);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(UsuarioGeralJSONControle.class.getName()).log(Level.SEVERE, "Erro ao alterar senha usuário", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            Logger.getLogger(UsuarioGeralJSONControle.class.getName()).log(Level.SEVERE, "Erro ao alterar senha usuário", e);
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }

    @RequestMapping(value = "{ctx}/desvincular-usuario", method = RequestMethod.POST)
    public ResponseEntity<EnvelopeRespostaDTO> desvincularUsuario(@PathVariable String ctx,
                                                                    @RequestBody DesvincularUsuarioDTO dto) {
        try {
            usuarioGeralService.desvincularUsuario(dto);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(UsuarioGeralJSONControle.class.getName()).log(Level.SEVERE, "Erro ao desvincular usuário da chave " + ctx, e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            Logger.getLogger(UsuarioGeralJSONControle.class.getName()).log(Level.SEVERE, "Erro ao desvincular usuário da chave " + ctx, e);
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }

    @RequestMapping(value = "{ctx}/obter", method = RequestMethod.POST)
    public ResponseEntity<EnvelopeRespostaDTO> obterUsuarioGeral(@PathVariable String ctx,
                                                                 @RequestBody AlterarUsuarioGeralDTO dto) {
        try {
            Usuario usuario = usuarioGeralService.obterUsuario(ctx, dto.getUsuario_tw(), dto.getUsuario_zw(), dto.getUsername());
            return ResponseEntityFactory.ok(usuario.getUsuarioGeral());
        } catch (Exception e) {
            Logger.getLogger(UsuarioGeralJSONControle.class.getName()).log(Level.SEVERE, "Erro ao obter usuário geral", e);
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }

}
