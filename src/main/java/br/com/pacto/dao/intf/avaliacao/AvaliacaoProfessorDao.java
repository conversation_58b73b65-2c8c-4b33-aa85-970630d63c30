package br.com.pacto.dao.intf.avaliacao;

import br.com.pacto.bean.avaliacao.AvaliacaoProfessor;
import br.com.pacto.dao.intf.base.DaoGenerico;
import br.com.pacto.service.exception.ServiceException;

import java.util.List;

/**
 * Created by Rafael on 08/10/2016.
 */
public interface AvaliacaoProfessorDao extends DaoGenerico<AvaliacaoProfessor, Integer> {

    List<AvaliacaoProfessor> findByCodProfessor(String codProfessores, String ctx) throws Exception;

    List<AvaliacaoProfessor> findByCodUsuario(String ctx, Integer codUsuario) throws ServiceException;
    List<AvaliacaoProfessor> findByCodProfessorECodUsuario(String ctx, Integer codUsuario, Integer codProfessor) throws ServiceException;
}