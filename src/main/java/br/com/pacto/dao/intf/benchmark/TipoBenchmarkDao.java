package br.com.pacto.dao.intf.benchmark;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.benchmark.TipoBenchmark;
import br.com.pacto.bean.benchmark.TipoBenchmarkResponseTO;
import br.com.pacto.controller.json.crossfit.FiltroTipoBenchmarkJSON;
import br.com.pacto.dao.intf.base.DaoGenerico;
import br.com.pacto.service.exception.ServiceException;

import java.util.List;

/**
 * Created by Rafael on 11/07/2016.
 */
public interface TipoBenchmarkDao  extends DaoGenerico<TipoBenchmark, Integer> {

    List<TipoBenchmarkResponseTO> consultarTodos(String ctx, FiltroTipoBenchmarkJSON filtros, PaginadorDTO paginadorDTO)throws ServiceException;
}
