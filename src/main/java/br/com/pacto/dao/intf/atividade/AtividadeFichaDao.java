/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.dao.intf.atividade;

import br.com.pacto.bean.atividade.AtividadeFicha;
import br.com.pacto.dao.intf.base.DaoGenerico;
import br.com.pacto.service.exception.ServiceException;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
public interface AtividadeFichaDao extends DaoGenerico<AtividadeFicha, Integer> {

    void removeList(String ctx, List<Integer> atividadesFichaId) throws Exception;

    List<AtividadeFicha> atualizarLista(String ctx, List<AtividadeFicha> atividadeFichas) throws Exception;

    List<AtividadeFicha> obterPorFicha(String ctx, Integer fichaId) throws ServiceException;

    List<Integer> findFichaCodigosByAtividadeCodigo(String ctx, Integer atividadeCodigo) throws ServiceException;

    void deleteByAtividadeCodigos(String ctx, List<Integer> atividadeCodigos) throws ServiceException;
}
