package br.com.pacto.dao.intf.avaliacao;

import br.com.pacto.bean.avaliacao.RespostaClienteParQ;
import br.com.pacto.bean.log.Log;
import br.com.pacto.dao.intf.base.DaoGenerico;
import br.com.pacto.service.exception.ServiceException;
import org.json.JSONArray;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

public interface ParQDao extends DaoGenerico<RespostaClienteParQ, Integer>  {

    Boolean consultarParQPositivoAssinaturaDigital(String ctx, Integer codigoCliente) throws ServiceException;

    Boolean consultarResultadoParqVigente(String ctx, Integer codigoCliente) throws Exception;

    JSONArray consultarClientesParQPositivo(String ctx, Integer empresaZw, String nomeOuMatricula, HttpServletRequest request) throws Exception;

    JSONArray consultarClientesParQPositivoV2(String ctx, Integer empresaZw, String nomeOuMatricula, HttpServletRequest request) throws Exception;

    RespostaClienteParQ consultarRespostaParQPorCliente(String ctx, Integer codigoCliente) throws Exception;

    RespostaClienteParQ consultarRespostaParQPorCodigo(String ctx, Integer codigoRespostaParq) throws Exception;

    JSONArray consultarClientesParQAssinado(String ctx, Integer empresaZw, String nomeOuMatricula, HttpServletRequest request, Integer diasParaVencimentoParq) throws Exception;

    JSONArray consultarClientesParQAssinadoV2(String ctx, Integer empresaZw, String nomeOuMatricula, HttpServletRequest request, Integer diasParaVencimentoParq) throws Exception;

    List<RespostaClienteParQ> consultarTodosPorCliente(String ctx, Integer codigoCliente) throws Exception;

    Integer consultarNrTotalClientesParQAssinado(String ctx, Integer empresaZw, Integer diasParaVencimentoParq) throws Exception;

    Integer consultarNrTotalClientesParQAssinadoV2(String ctx, Integer empresaZw, Integer diasParaVencimentoParq) throws Exception;

    JSONArray consultarClientesParQNaoAssinado(String ctx, Integer empresaZw, String nomeOuMatricula, HttpServletRequest request) throws Exception;

    JSONArray consultarClientesParQNaoAssinadoV2(String ctx, Integer empresaZw, String nomeOuMatricula, HttpServletRequest request, String todos) throws Exception;

    Integer consultarNrTotalClientesParQNaoAssinado(String ctx, Integer empresaZw) throws Exception;

    Integer consultarNrTotalClientesParQNaoAssinadoV2(String ctx, Integer empresaZw) throws Exception;

    Integer consultarNrTotalClientesParQPositivos(String ctx, Integer empresaZw) throws Exception;

    Integer consultarNrTotalClientesParQPositivosV2(String ctx, Integer empresaZw) throws Exception;

    JSONArray alunoParQValido(String ctx, Integer empresaZw, Integer codigoCliente)  throws Exception;

    void gravarLogParq(String ctx, Log log) throws Exception;
}
