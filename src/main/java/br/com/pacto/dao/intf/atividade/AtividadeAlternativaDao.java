/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.pacto.dao.intf.atividade;

import br.com.pacto.bean.atividade.AtividadeAlternativa;
import br.com.pacto.dao.intf.base.DaoGenerico;

public interface AtividadeAlternativaDao extends DaoGenerico<AtividadeAlternativa, Integer>{
    boolean existsByAtividadeAndAtividadeAlternativa(String ctx, Integer atividadeId, Integer atividadeAlternativaId);
}
