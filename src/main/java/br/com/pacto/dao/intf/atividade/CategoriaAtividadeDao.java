/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pacto.dao.intf.atividade;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.atividade.CategoriaAtividade;
import br.com.pacto.bean.atividade.CategoriaAtividadeResponseTO;
import br.com.pacto.bean.musculo.GrupoMuscularResumidoResponseTO;
import br.com.pacto.controller.json.atividade.FiltroCategoriaAtividadeJSON;
import br.com.pacto.controller.json.musculo.FiltroGrupoMuscularJSON;
import br.com.pacto.bean.atividade.CategoriaAtividadeResponseTO;
import br.com.pacto.controller.json.atividade.FiltroCategoriaAtividadeJSON;
import br.com.pacto.dao.intf.base.DaoGenerico;
import br.com.pacto.service.exception.ServiceException;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
public interface CategoriaAtividadeDao extends DaoGenerico<CategoriaAtividade, Integer> {

    List<CategoriaAtividadeResponseTO> consultarCategoriaAtividades(final String ctx, FiltroCategoriaAtividadeJSON filtroCategoriaAtividadeJSON, PaginadorDTO paginadorDTO) throws ServiceException;

}
