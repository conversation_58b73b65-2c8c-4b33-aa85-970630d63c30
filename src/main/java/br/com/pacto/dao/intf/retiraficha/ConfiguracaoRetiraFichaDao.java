package br.com.pacto.dao.intf.retiraficha;

import br.com.pacto.bean.retiraficha.ConfiguracaoRetiraFicha;
import br.com.pacto.dao.intf.base.DaoGenerico;

import java.util.List;
import java.util.Map;

public interface ConfiguracaoRetiraFichaDao extends DaoGenerico<ConfiguracaoRetiraFicha, Integer> {

    List<ConfiguracaoRetiraFicha> listarLogsAlteracoes(String contexto, ConfiguracaoRetiraFicha configuracaoRetiraFicha) throws Exception;

    List<ConfiguracaoRetiraFicha> obterTodos(String contexto) throws Exception;


}
