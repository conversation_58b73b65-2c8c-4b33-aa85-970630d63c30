package br.com.pacto.dao.intf.retiraficha;

import br.com.pacto.bean.retiraficha.ConfiguracaoRetiraFicha;
import br.com.pacto.bean.retiraficha.ConfiguracaoRetiraFichaLog;
import br.com.pacto.dao.intf.base.DaoGenerico;

import java.util.List;

public interface ConfiguracaoRetiraFichaLogDao extends DaoGenerico<ConfiguracaoRetiraFichaLog, Integer> {

    List<ConfiguracaoRetiraFichaLog> listarLogsAlteracoes(String contexto, Integer codigoConfiguracaoRetiraFicha) throws Exception;

}
