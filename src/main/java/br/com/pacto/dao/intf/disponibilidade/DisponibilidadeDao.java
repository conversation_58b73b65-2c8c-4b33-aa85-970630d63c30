package br.com.pacto.dao.intf.disponibilidade;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.colaborador.ColaboradorSimplesTO;
import br.com.pacto.bean.disponibilidade.Disponibilidade;
import br.com.pacto.controller.json.agendamento.AgendaDisponibilidadeDTO;
import br.com.pacto.controller.json.agendamento.FiltroDisponibilidadeDTO;
import br.com.pacto.controller.json.agendamento.TipoAgendamentoDuracaoDTO;
import br.com.pacto.controller.json.tipoEvento.TipoAgendamentoDTO;
import br.com.pacto.dao.intf.base.DaoGenerico;
import br.com.pacto.service.exception.ServiceException;

import java.util.Date;
import java.util.List;

public interface DisponibilidadeDao extends DaoGenerico<Disponibilidade, Integer> {

    List<Disponibilidade> findAllDisponibilidades(String ctx, Integer empresaId, FiltroDisponibilidadeDTO filtro, PaginadorDTO paginadorDTO) throws Exception;

    List<TipoAgendamentoDTO> consultarDisponibilidadeTipoAgendamento(final String ctx, String nomeDisponibilidade,
                                                      Boolean ativos,
                                                      PaginadorDTO paginadorDTO) throws ServiceException;

    List<TipoAgendamentoDTO> consultarDisponibilidadesHorarioTipoAgendamento(String ctx, Date dia, Integer hora, Integer empresaId) throws ServiceException;

    List<ColaboradorSimplesTO> consultarDisponibilidadesHorarioProfessorTipoAgendamento(String ctx, Date dia, Integer hora, Integer tipo, Integer empresaId) throws ServiceException;

    TipoAgendamentoDuracaoDTO obterPorIdTipoAgendamentoDuracao(String ctx, Integer id, Integer empresaId, Integer ambienteId, String inicio, String fim) throws ServiceException;

    AgendaDisponibilidadeDTO detalheDisponibilidadeAgenda(String ctx, Date dia, Integer hora, Integer tipo, Integer professor, Integer empresaId) throws ServiceException;

    Boolean existeAgendamentoAlunoHorarioDisponibilidade(String ctx, Integer idHorario) throws ServiceException;
}
