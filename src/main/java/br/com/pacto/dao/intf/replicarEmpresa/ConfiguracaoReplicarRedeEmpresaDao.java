package br.com.pacto.dao.intf.replicarEmpresa;

import br.com.pacto.bean.replicarEmpresa.ConfiguracaoRedeEmpresa;
import br.com.pacto.dao.intf.base.DaoGenerico;
import br.com.pacto.service.exception.ServiceException;

import java.util.List;

public interface ConfiguracaoReplicarRedeEmpresaDao extends DaoGenerico<ConfiguracaoRedeEmpresa, Integer> {

    List<ConfiguracaoRedeEmpresa> findByChaveOrigem(String chave) throws ServiceException;

    ConfiguracaoRedeEmpresa findByChaveDestino(String chaveOrigem, String chaveDestino) throws ServiceException;
}
