package br.com.pacto.dao.intf.notificacao;

import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.notificacao.NotificacaoAulaAgendada;
import br.com.pacto.dao.intf.base.DaoGenerico;

import java.util.Date;
import java.util.List;

public interface NotificacaoAulaAgendadaDao extends DaoGenerico<NotificacaoAulaAgendada, Integer>{

    List<NotificacaoAulaAgendada> findByClienteHorarioDia(String ctx, ClienteSintetico cliente, Integer codigoHorarioTurma, Date data, Boolean canceladas) throws Exception;
}
