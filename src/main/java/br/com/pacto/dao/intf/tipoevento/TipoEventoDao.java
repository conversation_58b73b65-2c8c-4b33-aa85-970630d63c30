/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.pacto.dao.intf.tipoevento;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.tipoEvento.TipoEvento;
import br.com.pacto.controller.json.tipoEvento.TipoAgendamentoDTO;
import br.com.pacto.dao.intf.base.DaoGenerico;
import br.com.pacto.service.exception.ServiceException;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
public interface TipoEventoDao extends DaoGenerico<TipoEvento, Integer>{

    List<TipoAgendamentoDTO> consultarTipoEvento(final String ctx, String nome<PERSON>parelho,
                                                 Boolean ativos,
                                                 PaginadorDTO paginadorDTO)throws ServiceException;

}
