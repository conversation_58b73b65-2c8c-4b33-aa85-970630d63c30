/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.pacto.dao.intf.sincronizacao;

import br.com.pacto.bean.sincronizacao.HistoricoRevisao;
import br.com.pacto.bean.sincronizacao.TipoClassSincronizarEnum;
import br.com.pacto.bean.sincronizacao.TipoRevisaoEnum;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.dao.intf.base.DaoGenerico;

/**
 *
 * <AUTHOR>
 */
public interface HistoricoRevisaoDao extends DaoGenerico<HistoricoRevisao, Integer>{

    void saveHistoricoRevisao(TipoRevisaoEnum tipoRevisao, TipoClassSincronizarEnum tipoClassEnum, String key, Usuario u, Integer pk);

}
