/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.dao.intf.professor;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.colaborador.FiltroColaboradorJSON;
import br.com.pacto.bean.professor.ProfessorSintetico;
import br.com.pacto.dao.intf.base.DaoGenerico;
import br.com.pacto.service.exception.ServiceException;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
public interface ProfessorSinteticoDao extends DaoGenerico<ProfessorSintetico, Integer> {

    List<ProfessorSintetico>  listarColaboradores(final String ctx, FiltroColaboradorJSON filtros, PaginadorDTO paginadorDTO, Integer empresaId, Boolean todosTipos) throws ServiceException;

    List<ProfessorSintetico> colaboradores<PERSON>s<PERSON>ull<PERSON><PERSON><PERSON>(String ctx) throws ServiceException;

    <PERSON><PERSON><PERSON><PERSON><PERSON> obterPorId(String ctx, Integer id) throws ServiceException;

    Professor<PERSON><PERSON><PERSON><PERSON> obterPorIdColaborador (String ctx, Integer idColaborador) throws ServiceException;

}
