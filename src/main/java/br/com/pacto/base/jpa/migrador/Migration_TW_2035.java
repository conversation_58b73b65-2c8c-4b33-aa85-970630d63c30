package br.com.pacto.base.jpa.migrador;

import br.com.pacto.base.jpa.annotation.ClasseProcesso;
import br.com.pacto.dao.intf.usuario.UsuarioDao;
import br.com.pacto.objeto.Uteis;

@ClasseProcesso(
        autor = "<PERSON><PERSON><PERSON>",
        data = "24/04/2025",
        descricao = "Habilitar menus CADASTROS e RELATÓRIOS do Treino para perfis com acessos auxiliares",
        motivacao = "TW-2035"
)
public class Migration_TW_2035 implements MigracaoVersaoInterface {

    @Override
    public void executar(String ctx, UsuarioDao dao) throws Exception {
        try {
            dao.executeNativeSQL(ctx,
                    "INSERT INTO permissao (recurso, tipo, perfil_codigo) " +
                            "SELECT 'CADASTROS_TREINO', 'TOTAL', p.codigo " +
                            "FROM perfil p " +
                            "WHERE EXISTS ( " +
                            "   SELECT 1 FROM permissao pr " +
                            "   WHERE pr.perfil_codigo = p.codigo " +
                            "     AND pr.recurso IN ( " +
                            "       'MUSCULOS', 'GRUPOS_MUSCULARES', 'APARELHOS', 'ATIVIDADES', " +
                            "       'FICHAS_PRE_DEFINIDAS', 'CATEGORIA_ATIVIDADE', 'CATEGORIA_FICHAS', " +
                            "       'NIVEIS', 'OBJETIVOS', 'IMAGENS', 'BADGES', 'TIPO_EVENTO', " +
                            "       'ANAMNESE', 'EMPRESA', 'LOCAL_RETIRA_FICHA', 'PROGRAMAS_PREDEFINIDOS' " +
                            "     )" +
                            ") " +
                            "AND NOT EXISTS ( " +
                            "   SELECT 1 FROM permissao pr2 " +
                            "   WHERE pr2.perfil_codigo = p.codigo " +
                            "     AND pr2.recurso = 'CADASTROS_TREINO'" +
                            ");"
            );
        } catch (Exception e) {
            Uteis.logar(e, Migration_TW_2035.class);
        }

        try {
            dao.executeNativeSQL(ctx,
                    "INSERT INTO permissao (recurso, tipo, perfil_codigo) " +
                            "SELECT 'RELATORIOS_TREINO', 'TOTAL', p.codigo " +
                            "FROM perfil p " +
                            "WHERE EXISTS ( " +
                            "   SELECT 1 FROM permissao pr " +
                            "   WHERE pr.perfil_codigo = p.codigo " +
                            "     AND pr.recurso = 'VER_GESTAO_GERAL'" +
                            ") " +
                            "AND NOT EXISTS ( " +
                            "   SELECT 1 FROM permissao pr2 " +
                            "   WHERE pr2.perfil_codigo = p.codigo " +
                            "     AND pr2.recurso = 'RELATORIOS_TREINO'" +
                            ");"
            );
        } catch (Exception e) {
            Uteis.logar(e, Migration_TW_2035.class);
        }
    }
}
