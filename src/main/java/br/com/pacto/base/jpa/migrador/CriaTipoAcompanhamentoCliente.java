package br.com.pacto.base.jpa.migrador;

import br.com.pacto.base.jpa.annotation.ClasseProcesso;
import br.com.pacto.dao.intf.usuario.UsuarioDao;

@ClasseProcesso(autor = "<PERSON><PERSON>",
        data = "30/09/2024",
        descricao = "Cria tipo acompanhamento cliente",
        motivacao = "APPS-1618")
public class CriaTipoAcompanhamentoCliente implements MigracaoVersaoInterface {

    @Override
    public void executar(String ctx, UsuarioDao dao) throws Exception {
        try {
            dao.executeNativeSQL(ctx, "ALTER TABLE clienteacompanhamento  ADD COLUMN tipoAcompanhamentoCliente INT NOT NULL DEFAULT 1;");
        } catch (Exception e) {
        }
    }
}
