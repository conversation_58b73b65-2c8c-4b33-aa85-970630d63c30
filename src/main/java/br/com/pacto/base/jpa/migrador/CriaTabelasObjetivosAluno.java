package br.com.pacto.base.jpa.migrador;

import br.com.pacto.base.jpa.annotation.ClasseProcesso;
import br.com.pacto.dao.intf.usuario.UsuarioDao;

@ClasseProcesso(autor = "<PERSON><PERSON>",
        data = "26/11/2024",
        descricao = "Cria tabelas de objetivos do aluno",
        motivacao = "Criação de tabelas de objetivos do aluno - APPS-1621")
public class CriaTabelasObjetivosAluno implements MigracaoVersaoInterface {

    @Override
    public void executar(String ctx, UsuarioDao dao) throws Exception {
        try {
            dao.executeNativeSQL(ctx, "CREATE TABLE objetivoaluno\n" +
                    "(\n" +
                    "    codigo     SERIAL PRIMARY KEY,\n" +
                    "    primario   BOOLEAN default FALSE                       NOT NULL,\n" +
                    "    objetivo   INT REFERENCES objetivopredefinido (codigo) NOT NULL,\n" +
                    "    datainicio DATE                                        NOT NULL,\n" +
                    "    datafinal  DATE                                        NOT NULL,\n" +
                    "    descricao  TEXT,\n" +
                    "    status     INT     default 0                           not null,\n" +
                    "    cliente    INT REFERENCES clientesintetico (codigo)    NOT NULL\n" +
                    ")");

            dao.executeNativeSQL(ctx,"CREATE TABLE objetivointermediarioaluno\n" +
                    "(\n" +
                    "    codigo              SERIAL PRIMARY KEY,\n" +
                    "    objetivopredefinido INT REFERENCES objetivopredefinido (codigo) NOT NULL,\n" +
                    "    objetivoaluno       INT REFERENCES objetivoaluno (codigo)       NOT NULL,\n" +
                    "    datainicio          DATE                                        NOT NULL,\n" +
                    "    datafinal           DATE                                        NOT NULL,\n" +
                    "    descricao           TEXT,\n" +
                    "    objetivofinal       TEXT,\n" +
                    "    categoria           TEXT,\n" +
                    "    status              INT default 0                               not null\n" +
                    ")");
        } catch (Exception e) {
        }
    }
}
