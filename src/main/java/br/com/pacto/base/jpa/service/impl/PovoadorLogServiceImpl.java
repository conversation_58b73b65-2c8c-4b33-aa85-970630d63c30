package br.com.pacto.base.jpa.service.impl;

import br.com.pacto.base.jpa.service.intf.PovoadorLogService;
import br.com.pacto.bean.Agendamento;
import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.log.Log;
import br.com.pacto.bean.professor.ProfessorSintetico;
import br.com.pacto.bean.tipoEvento.TipoEvento;
import br.com.pacto.controller.json.log.EntidadeLogEnum;
import br.com.pacto.dao.intf.log.LogDao;
import br.com.pacto.enumerador.agenda.StatusAgendamentoEnum;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.exception.ServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.ResultSet;
import java.sql.Timestamp;
import java.util.Date;

import static br.com.pacto.objeto.Uteis.incluirLog;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

@Service
public class PovoadorLogServiceImpl implements PovoadorLogService {

    @Autowired
    private LogDao logDao;

    public void povoar(String chave, EntidadeLogEnum entidade, Date inicio, Date fim){
        try {
            switch (entidade){
                case AGENDADESERVICO:
                    povoarLogAgenda(chave, inicio, fim);
                    break;
            }
        } catch (ServiceException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void povoarLogAgenda(String chave, Date inicio, Date fim) throws ServiceException {
        StringBuilder query = new StringBuilder();
        query.append("select ua.username as usuarioalterou, a.codigo, u.username, a.usuarioultalteracao_codigo, a.usuariolancou_codigo, a.datalancamento, a.ultimaalteracao, a.inicio, a.fim, a.status, ");
        query.append("c.nome as cliente, p.nome as professor, t.nome as tipo, a.observacao ");
        query.append("from agendamento a ");
        query.append(" inner join usuario u on u.codigo = a.usuariolancou_codigo ");
        query.append(" left join usuario ua on ua.codigo = a.usuarioultalteracao_codigo  ");
        query.append("inner join tipoevento t on a.tipoevento_codigo = t.codigo ");
        query.append("inner join professorsintetico p on a.professor_codigo = p.codigo ");
        query.append("inner join clientesintetico c on a.cliente_codigo = c.codigo ");
        query.append("left join log l on l.chaveprimaria = a.codigo::text and l.nomeentidade = 'AGENDADESERVICO' ");
        query.append("where a.datalancamento between '");
        query.append(Uteis.getDataAplicandoFormatacao(inicio, "yyyy-MM-dd"));
        query.append("' and '");
        query.append(Uteis.getDataAplicandoFormatacao(fim, "yyyy-MM-dd"));
        query.append("' and not disponibilidade ");
        query.append("and l.codigo is null");

        try {
            try (ResultSet rs = logDao.createStatement(chave, query.toString())) {
                while (rs.next()) {
                    Timestamp datalancamento = rs.getTimestamp("datalancamento");

                    Agendamento agendamento = new Agendamento();
                    agendamento.setCodigo(rs.getInt("codigo"));
                    agendamento.setInicio(rs.getTimestamp("inicio"));
                    agendamento.setFim(rs.getTimestamp("fim"));
                    agendamento.setProfessor(new ProfessorSintetico(rs.getString("professor")));
                    agendamento.setTipoEvento(new TipoEvento(rs.getString("tipo")));
                    agendamento.setCliente(new ClienteSintetico(0, rs.getString("cliente")));
                    agendamento.setObservacao(rs.getString("observacao"));
                    agendamento.setStatus(StatusAgendamentoEnum.AGUARDANDO_CONFIRMACAO);
                    System.out.println("Incluido log para agendamento " + agendamento.getCodigo());
                    incluirLog(chave, agendamento.getCodigo().toString(),
                            "",
                            "",
                            agendamento.getDescricaoParaLog(null), "INCLUSÃO",
                            "INCLUSÃO DE AGENDAMENTO",
                            EntidadeLogEnum.AGENDADESERVICO,
                            "Agendamento", rs.getString("username"), datalancamento);

                    StatusAgendamentoEnum statusAtual = StatusAgendamentoEnum.getFromId(rs.getInt("status"));

                    if (statusAtual != null && !statusAtual.equals(StatusAgendamentoEnum.AGUARDANDO_CONFIRMACAO)) {
                        Timestamp ultimaalteracao = rs.getTimestamp("ultimaalteracao");

                        Agendamento alterado = agendamento.getClone(agendamento.getInicio(), agendamento.getFim(), true);
                        alterado.setStatus(statusAtual);

                        incluirLog(chave,
                                agendamento.getCodigo().toString(), "",
                                agendamento.getDescricaoParaLog(alterado),
                                alterado.getDescricaoParaLog(agendamento),
                                "ALTERAÇÃO",
                                "ALTERAÇÃO DE AGENDAMENTO",
                                EntidadeLogEnum.AGENDADESERVICO, "Agendamento",
                                rs.getString("usuarioalterou"), ultimaalteracao);
                        System.out.println("Incluido log alteração para agendamento " + agendamento.getCodigo());
                    }
                }
            }
        } catch (Exception e) {
            throw new ServiceException(e);
        }


    }


    public void incluirLog(String key, String chavePrimariaEntidade, String chavePrimariaSubordinado,
                                  String descAnterior, String descAtual,
                                  String operacao, String descricaoOperacao,
                                  EntidadeLogEnum entidade,
                                  String descricaoEntidade, String username, Date lancamento) {
        try{
            Log log = new Log();
            log.setOperacao(operacao);
            log.setDescricao(descricaoOperacao);
            log.setChavePrimaria(chavePrimariaEntidade);
            if(isNotBlank(chavePrimariaSubordinado)) {
                log.setChavePrimariaEntidadeSubordinada(chavePrimariaSubordinado);
            }
            log.setNomeEntidade(entidade.name());
            log.setNomeEntidadeDescricao(descricaoEntidade);
            log.setResponsavelAlteracao(username);
            log.setUserOAMD("");
            log.setNomeCampo("Campo(s)\n");
            log.setDataAlteracao(lancamento);
            log.setValorCampoAnterior(descAnterior);
            log.setValorCampoAlterado(descAtual);
            log.setPessoa(0);
            logDao.insert(key, log);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
