package br.com.pacto.base.jpa.migrador;

import br.com.pacto.base.jpa.annotation.ClasseProcesso;
import br.com.pacto.dao.intf.usuario.UsuarioDao;
import br.com.pacto.objeto.Uteis;

import java.sql.ResultSet;

@ClasseProcesso(
        autor = "Murillo Roseno",
        data = "07/04/2025",
        descricao = "Renomeando PROIBIR_MARCAR_AULA_ANTES_PAGAMENTO_PRIMEIRA_PARCELA",
        motivacao = "TW-1955"
)
public class Migration_TW_1955 implements MigracaoVersaoInterface {

    @Override
    public void executar(String ctx, UsuarioDao dao) throws Exception {
        try {
            try (ResultSet rs = dao.createStatement(ctx,
                    "SELECT EXISTS (SELECT * FROM configuracaosistema c WHERE configuracao = 'PROIBIR_MARCAR_AULA_ANTES_PAGAMENTO_PRIMEIRA_PARCELA_PIX') AS exists")) {
                if (rs.next() && rs.getBoolean("exists")) {
                    dao.executeNativeSQL(ctx,
                            "UPDATE configuracaosistema SET configuracao = 'PROIBIR_MARCAR_AULA_ANTES_PAGAMENTO_PRIMEIRA_PARCELA' " +
                                    "WHERE configuracao = 'PROIBIR_MARCAR_AULA_ANTES_PAGAMENTO_PRIMEIRA_PARCELA_PIX';");
                }
            }
        } catch (Exception e) {
            Uteis.logar(e, Migration_TW_1955.class);
        }
    }
}
