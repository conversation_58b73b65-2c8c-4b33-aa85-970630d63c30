package br.com.pacto.base.jpa.migrador;

import br.com.pacto.base.jpa.annotation.ClasseProcesso;
import br.com.pacto.dao.intf.usuario.UsuarioDao;
import br.com.pacto.objeto.Uteis;

import java.util.Arrays;
import java.util.List;

@ClasseProcesso(
        autor = "<PERSON>",
        data = "19/06/2025",
        descricao = "Criação de index Revisão das transações mais lentas",
        motivacao = "TW-2183 - Revisão das transações mais lentas")
public class Migration_TW_2183 implements MigracaoVersaoInterface {



    @Override
    public void executar(String ctx, UsuarioDao dao) throws Exception {
        try {
            dao.executeNativeSQL(ctx, "CREATE INDEX idx_email_pessoa_codigo ON email (pessoa_codigo);");
        } catch (Exception e) {
            Uteis.logar(e, Migration_TW_2183.class);
        }
        try {
            dao.executeNativeSQL(ctx, "CREATE INDEX idx_telefone_pessoacodigo ON telefone (pessoa_codigo);");
        } catch (Exception e) {
            Uteis.logar(e, Migration_TW_2183.class);
        }
        try {
            dao.executeNativeSQL(ctx, "CREATE INDEX idx_clienteobservacao_cliente_codigo ON clienteobservacao (cliente_codigo);");
        } catch (Exception e) {
            Uteis.logar(e, Migration_TW_2183.class);
        }
        try {
            dao.executeNativeSQL(ctx, "CREATE INDEX idx_pesoosseo_cliente_codigo ON pesoosseo (cliente_codigo);");
        } catch (Exception e) {
            Uteis.logar(e, Migration_TW_2183.class);
        }
        try {
            dao.executeNativeSQL(ctx, "CREATE INDEX idx_itemavaliacaofisica_cliente_codigo ON itemavaliacaofisica (cliente_codigo);");
        } catch (Exception e) {
            Uteis.logar(e, Migration_TW_2183.class);
        }
        try {
            dao.executeNativeSQL(ctx, "CREATE INDEX idx_professorsintetico_codigocolaborador ON professorsintetico (codigocolaborador);");
        } catch (Exception e) {
            Uteis.logar(e, Migration_TW_2183.class);
        }
        try {
            dao.executeNativeSQL(ctx, "CREATE INDEX idx_clientesintetico_codigocliente ON clientesintetico (codigocliente);");
        } catch (Exception e) {
            Uteis.logar(e, Migration_TW_2183.class);
        }
    }

}
