package br.com.pacto.base.jpa.migrador;

import br.com.pacto.base.jpa.annotation.ClasseProcesso;
import br.com.pacto.dao.intf.usuario.UsuarioDao;
import br.com.pacto.objeto.Uteis;

@ClasseProcesso(
        autor = "<PERSON>",
        data = "16/06/2025",
        descricao = "Criação de index para otimização do BI",
        motivacao = "IN-1346"
)
public class Migration_IN_1346 implements MigracaoVersaoInterface {

    @Override
    public void executar(String ctx, UsuarioDao dao) throws Exception {
        try {
            dao.executeNativeSQL(ctx, "create index idx_statuspessoa_data_inicio_evento on statuspessoa using btree (datainicioEvento asc nulls first);");
        } catch (Exception e) {
            Uteis.logar(e, Migration_IN_1346.class);
        }
        try {
            dao.executeNativeSQL(ctx, "create index idx_statuspessoa_data_fim_evento on statuspessoa using btree (dataFimEvento asc nulls first);");
        } catch (Exception e) {
            Uteis.logar(e, Migration_IN_1346.class);
        }
        try {
            dao.executeNativeSQL(ctx, "create index idx_clienteacompanhamento_inicio on ClienteAcompanhamento using btree (inicio asc nulls first);");
        } catch (Exception e) {
            Uteis.logar(e, Migration_IN_1346.class);
        }
        try {
            dao.executeNativeSQL(ctx, "create index idx_treinorealizado_datainicio on treinorealizado using btree (dataInicio asc nulls first);");
        } catch (Exception e) {
            Uteis.logar(e, Migration_IN_1346.class);
        }
        try {
            dao.executeNativeSQL(ctx, "create index idx_treinorealizado_datafim on treinorealizado using btree (dataFim asc nulls first);");
        } catch (Exception e) {
            Uteis.logar(e, Migration_IN_1346.class);
        }
        try {
            dao.executeNativeSQL(ctx, "create index idx_dashboardbi_codigoprofessor on dashboardbi using btree (codigoprofessor asc nulls first);");
        } catch (Exception e) {
            Uteis.logar(e, Migration_IN_1346.class);
        }
        try {
            dao.executeNativeSQL(ctx, "create index idx_dashboardbi_empresa on dashboardbi using btree (empresa asc nulls first);");
        } catch (Exception e) {
            Uteis.logar(e, Migration_IN_1346.class);
        }
    }
}
