package br.com.pacto.base.jpa.migrador;

import br.com.pacto.base.jpa.annotation.ClasseProcesso;
import br.com.pacto.dao.intf.usuario.UsuarioDao;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.impl.conexao.ConexaoZWServiceImpl;
import br.com.pacto.service.intf.conexao.ConexaoZWService;
import br.com.pacto.util.UtilContext;

import java.sql.Connection;
import java.sql.ResultSet;

@ClasseProcesso(autor = "Kaio Sanchez",
        data = "16/06/2025",
        descricao = "Ajusta o padrão de notificações de aula agendada para remover a coluna cliente_codigo e ajustar os dados existentes.",
        motivacao = "APPS-3574")
public class AjustaPadraoNotificacaoAulaAgendada implements MigracaoVersaoInterface {

    @Override
    public void executar(String ctx, UsuarioDao dao) throws Exception {
        try {
            dao.executeNativeSQL(ctx, "UPDATE notificacaoaulaagendada SET cliente = cliente_codigo WHERE cliente IS NULL AND cliente_codigo IS NOT NULL;");
            dao.executeNativeSQL(ctx, "ALTER TABLE notificacaoaulaagendada DROP COLUMN cliente_codigo;");
        } catch (Exception e) {
            Uteis.logar(e, AjustaPadraoNotificacaoAulaAgendada.class);
        }
    }
}
