package br.com.pacto.base.jpa.migrador;

import br.com.pacto.base.jpa.annotation.ClasseProcesso;
import br.com.pacto.dao.intf.usuario.UsuarioDao;
import br.com.pacto.objeto.Uteis;

@ClasseProcesso(
        autor = "Alisson Melo",
        data = "13/01/2025",
        descricao = "Evolução entidade aparelho para reserva de equipamentos na aula",
        motivacao = "TW-1387")
public class Migration_TW_1387 implements MigracaoVersaoInterface {

    @Override
    public void executar(String ctx, UsuarioDao dao) throws Exception {
        try {
            dao.executeNativeSQL(ctx, "alter table aparelho add column sigla varchar(20) default null;");
            dao.executeNativeSQL(ctx, "alter table aparelho add column icone varchar(50) default null;");
            dao.executeNativeSQL(ctx, "alter table aparelho add column usaremreservaequipamentos boolean default false;");
        } catch (Exception e) {
            Uteis.logar(e, Migration_TW_1387.class);
        }
    }

}
