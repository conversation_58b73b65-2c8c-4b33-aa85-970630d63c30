/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.base.jpa.service.impl;

import br.com.pacto.base.jpa.bean.Versao;
import br.com.pacto.base.jpa.dao.intf.VersaoDao;
import br.com.pacto.base.jpa.service.intf.VersaoService;
import br.com.pacto.service.intf.Validacao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.util.ViewUtils;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR>
 */
@Service
public class VersaoServiceImpl implements VersaoService {

    @Autowired
    private ViewUtils viewUtils;
    @Autowired
    private Validacao validacao;
    @Autowired
    private VersaoDao versaoDao;

    public ViewUtils getViewUtils() {
        return viewUtils;
    }

    public void setViewUtils(ViewUtils viewUtils) {
        this.viewUtils = viewUtils;
    }

    public Validacao getValidacao() {
        return validacao;
    }

    public void setValidacao(Validacao validacao) {
        this.validacao = validacao;
    }

    public VersaoDao getVersaoDao() {
        return this.versaoDao;
    }

    public void setVersaoDao(VersaoDao versaoDao) {
        this.versaoDao = versaoDao;
    }

    public Versao alterar(final String ctx, Versao object) throws ServiceException {
        try {
            return getVersaoDao().update(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    public void excluir(final String ctx, Versao object) throws ServiceException {
        try {
            getVersaoDao().delete(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    public Versao inserir(final String ctx, Versao object) throws ServiceException {
        try {
            return getVersaoDao().insert(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    public Versao obterObjetoPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException {
        try {
            return getVersaoDao().findObjectByParam(ctx, query, params);
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    public Versao obterPorId(final String ctx, Integer id) throws ServiceException {
        try {
            return getVersaoDao().findById(ctx, id);
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    public List<Versao> obterPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException {
        try {
            return getVersaoDao().findByParam(ctx, query, params);
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    public List<Versao> obterPorParam(final String ctx, String query,
            Map<String, Object> params, int max, int index)
            throws ServiceException {
        try {
            return getVersaoDao().findByParam(ctx, query, params, max, index);
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    public List<Versao> obterTodos(final String ctx) throws ServiceException {
        try {
            return getVersaoDao().findAll(ctx);
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }
}