package br.com.pacto.base.jpa.migrador;

import br.com.pacto.base.jpa.annotation.ClasseProcesso;
import br.com.pacto.dao.intf.usuario.UsuarioDao;
import br.com.pacto.objeto.Uteis;

@ClasseProcesso(
        autor = "Alisson Melo",
        data = "12/05/2025",
        descricao = "Remover todas atividades da primeira versão da IA",
        motivacao = "TW-2115")
public class RemoverAtividadesIaV1 implements MigracaoVersaoInterface {

    @Override
    public void executar(String ctx, UsuarioDao dao) throws Exception {
        try {
            // Excluir apenas as relacionamentos de atividades que NÃO estão relacionadas à tabela atividadeficha
            dao.executeNativeSQL(ctx,
                    "delete from atividadeaparelho " +
                            "where atividade_codigo in (" +
                            "   select codigo from atividade " +
                            "   where idia is not null and codigo not in (" +
                            "       select distinct atividade_codigo from atividadeficha" +
                            "   )" +
                            ");");

            dao.executeNativeSQL(ctx,
                    "delete from atividadecategoriaatividade " +
                            "where atividade_codigo in (" +
                            "   select codigo from atividade " +
                            "   where idia is not null and codigo not in (" +
                            "       select distinct atividade_codigo from atividadeficha" +
                            "   )" +
                            ");");

            dao.executeNativeSQL(ctx,
                    "delete from atividadeanimacao " +
                            "where atividade_codigo in (" +
                            "   select codigo from atividade " +
                            "   where idia is not null and codigo not in (" +
                            "       select distinct atividade_codigo from atividadeficha" +
                            "   )" +
                            ");");

            dao.executeNativeSQL(ctx,
                    "delete from atividademusculo " +
                            "where atividade_codigo in (" +
                            "   select codigo from atividade " +
                            "   where idia is not null and codigo not in (" +
                            "       select distinct atividade_codigo from atividadeficha" +
                            "   )" +
                            ");");

            dao.executeNativeSQL(ctx,
                    "delete from atividadegrupomuscular " +
                            "where atividade_codigo in (" +
                            "   select codigo from atividade " +
                            "   where idia is not null and codigo not in (" +
                            "       select distinct atividade_codigo from atividadeficha" +
                            "   )" +
                            ");");

            dao.executeNativeSQL(ctx,
                    "delete from atividadealternativa " +
                            "where atividade_codigo in (" +
                            "   select codigo from atividade " +
                            "   where idia is not null and codigo not in (" +
                            "       select distinct atividade_codigo from atividadeficha" +
                            "   )" +
                            ");");

            dao.executeNativeSQL(ctx,
                    "delete from atividadeempresa " +
                            "where atividade_codigo in (" +
                            "   select codigo from atividade " +
                            "   where idia is not null and codigo not in (" +
                            "       select distinct atividade_codigo from atividadeficha" +
                            "   )" +
                            ");");

            // Excluir apenas as atividades que NÃO estão relacionadas à tabela atividadeficha
            dao.executeNativeSQL(ctx,
                    "delete from atividade " +
                            "where idia is not null and codigo not in (" +
                            "   select distinct atividade_codigo from atividadeficha" +
                            ");");

            // Atividades da primeira versão que não foram excluídas serão transformadas em atividades normais
            dao.executeNativeSQL(ctx, "update atividade set idia = null where idia is not null;");
        } catch (Exception e) {
            Uteis.logar(e, RemoverAtividadesIaV1.class);
        }
    }
}
