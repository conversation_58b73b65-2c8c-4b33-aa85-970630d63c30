package br.com.pacto.base.jpa.migrador;

import br.com.pacto.base.jpa.annotation.ClasseProcesso;
import br.com.pacto.dao.intf.usuario.UsuarioDao;
import br.com.pacto.objeto.Uteis;

@ClasseProcesso(autor = "<PERSON><PERSON>",
        data = "08/07/2025",
        descricao = "Criação da extensão unaccent no banco de dados",
        motivacao = "APPS-3746")
public class Migration_APPS_3746 implements MigracaoVersaoInterface {

    @Override
    public void executar(String ctx, UsuarioDao dao) throws Exception {
        try {
            dao.executeNativeSQL(ctx, "CREATE EXTENSION IF NOT EXISTS unaccent;");
        } catch (Exception e) {
            Uteis.logar(e, Migration_APPS_3746.class);
        }
    }
}
