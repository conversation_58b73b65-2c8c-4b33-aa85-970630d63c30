package br.com.pacto.base.jpa.migrador;

import br.com.pacto.base.jpa.annotation.ClasseProcesso;
import br.com.pacto.dao.intf.usuario.UsuarioDao;
import br.com.pacto.objeto.Uteis;

@ClasseProcesso(autor = "<PERSON><PERSON><PERSON>",
        data = "24/06/2025",
        descricao = "Cria a tabela para armazenar a avaliação do acompanhamento de treino.",
        motivacao = "TW-2374")
public class Migration_TW_2374 implements MigracaoVersaoInterface {

    @Override
    public void executar(String ctx, UsuarioDao dao) throws Exception {
        try {
            String sqlCreateTable = "CREATE TABLE clienteacompanhamentoavaliacao (\n" +
                    "    codigo SERIAL PRIMARY KEY,\n" +
                    "    cliente_acompanhamento_codigo INTEGER NOT NULL UNIQUE REFERENCES clienteacompanhamento(codigo),\n" +
                    "    nota INTEGER,\n" +
                    "    comentario TEXT,\n" +
                    "    dataAvaliacao TIMESTAMP NOT NULL\n" +
                    ");";

            dao.executeNativeSQL(ctx, sqlCreateTable);
        } catch (Exception e) {
            Uteis.logar(e, Migration_TW_2374.class);
            throw e;
        }
    }
}