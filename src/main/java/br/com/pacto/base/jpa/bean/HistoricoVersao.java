/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.base.jpa.bean;

import br.com.pacto.objeto.Calendario;
import org.hibernate.annotations.Type;

import javax.persistence.*;
import java.io.Serializable;
import java.text.ParseException;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Entity
public class HistoricoVersao implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private String autor;
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    private Date dataCriacao;
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    private Date dataExecucao;
    private Integer versao;
    @Lob
    @Type(type = "org.hibernate.type.StringClobType")
    @Column(columnDefinition = "text", length = 99999)
    private String descricao;
    private String exception;
    private String classeMigrador;

    public HistoricoVersao() {
    }

    public HistoricoVersao(final String autor, final String dataCriacao,
                           Date dataExecucao, Integer versao, final String descricao,
                           final String exception, final String classeMigrador) throws ParseException {
        this.autor = autor;
        this.dataCriacao = Calendario.getDate(Calendario.MASC_DATA, dataCriacao);
        this.dataExecucao = dataExecucao;
        this.versao = versao;
        this.descricao = descricao;
        this.exception = exception;
        this.classeMigrador = classeMigrador;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getAutor() {
        return autor;
    }

    public void setAutor(String autor) {
        this.autor = autor;
    }

    public Date getDataCriacao() {
        return dataCriacao;
    }

    public void setDataCriacao(Date dataCriacao) {
        this.dataCriacao = dataCriacao;
    }

    public Date getDataExecucao() {
        return dataExecucao;
    }

    public void setDataExecucao(Date dataExecucao) {
        this.dataExecucao = dataExecucao;
    }

    public Integer getVersao() {
        return versao;
    }

    public void setVersao(Integer versao) {
        this.versao = versao;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getException() {
        return exception;
    }

    public void setException(String exception) {
        this.exception = exception;
    }

    public String getClasseMigrador() {
        return classeMigrador;
    }

    public void setClasseMigrador(String classeMigrador) {
        this.classeMigrador = classeMigrador;
    }
}
