package br.com.pacto.base.jpa.migrador;

import br.com.pacto.base.jpa.annotation.ClasseProcesso;
import br.com.pacto.dao.intf.usuario.UsuarioDao;
import br.com.pacto.objeto.Uteis;

import java.sql.ResultSet;

@ClasseProcesso(
        autor = "Alisson Melo",
        data = "31/03/2024",
        descricao = "Inicializando nova configuração sistema PROIBIR_MARCAR_AULA_ANTES_PAGAMENTO_PRIMEIRA_PARCELA_PIX como false",
        motivacao = "TW-1857")
public class Migration_TW_1857 implements MigracaoVersaoInterface {

    @Override
    public void executar(String ctx, UsuarioDao dao) throws Exception {
        try {
            try (ResultSet rs = dao.createStatement(ctx, "SELECT EXISTS (SELECT  * FROM configuracaosistema c WHERE configuracao = 'PROIBIR_MARCAR_AULA_ANTES_PAGAMENTO_PRIMEIRA_PARCELA_PIX')")) {
                if (rs.next()) {
                    if (!rs.getBoolean("exists")) {
                        dao.executeNativeSQL(ctx, "INSERT INTO configuracaosistema (configuracao, valor) VALUES ('PROIBIR_MARCAR_AULA_ANTES_PAGAMENTO_PRIMEIRA_PARCELA_PIX' , 'false');");
                    }
                }
            }
        } catch (Exception e) {
            Uteis.logar(e, Migration_TW_1857.class);
        }
    }

}
