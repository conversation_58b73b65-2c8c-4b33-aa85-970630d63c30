package br.com.pacto.base.jpa.migrador;

import br.com.pacto.base.jpa.annotation.ClasseProcesso;
import br.com.pacto.dao.intf.usuario.UsuarioDao;
import br.com.pacto.objeto.Uteis;

@ClasseProcesso(
        autor = "Anna Carolina",
        data = "10/12/2024",
        descricao = "Criação da coluna NivelWOD na tabela WOD",
        motivacao = "TW-160 - Cadastro de WOD"
)
public class CriaColunaNivelWod implements MigracaoVersaoInterface {

    @Override
    public void executar(String ctx, UsuarioDao dao) throws Exception {
        try {
            dao.executeNativeSQL(ctx, "ALTER TABLE wod ADD COLUMN NivelWOD VARCHAR(255);");
        } catch (Exception e) {
            Uteis.logar(e, CriaColunaNivelWod.class);
        }
    }
}
