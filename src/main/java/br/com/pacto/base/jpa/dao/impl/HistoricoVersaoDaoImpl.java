/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.base.jpa.dao.impl;

import br.com.pacto.base.jpa.bean.HistoricoVersao;
import br.com.pacto.base.jpa.dao.intf.HistoricoVersaoDao;
import br.com.pacto.dao.impl.base.DaoGenericoImpl;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public class HistoricoVersaoDaoImpl extends DaoGenericoImpl<HistoricoVersao, Integer> implements HistoricoVersaoDao {

    final static String QUERY_FIND_ALL_BY_CLASSES_MIGRADORAS = "SELECT hv FROM HistoricoVersao hv WHERE hv.classeMigrador IN (:classesMigradoras)";

    public List findAllByClassesMigradoras(String ctx, List<String> classesMigradoras) throws Exception {
        return getEntityManager(ctx).createQuery(QUERY_FIND_ALL_BY_CLASSES_MIGRADORAS)
                .setParameter("classesMigradoras", classesMigradoras)
                .getResultList();
    }
}
