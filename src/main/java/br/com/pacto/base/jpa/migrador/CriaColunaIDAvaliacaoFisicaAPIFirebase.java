package br.com.pacto.base.jpa.migrador;

import br.com.pacto.base.jpa.annotation.ClasseProcesso;
import br.com.pacto.dao.intf.usuario.UsuarioDao;

@ClasseProcesso(autor = "Kai<PERSON>",
        data = "02/12/2024",
        descricao = "Cria coluna ID na tabela de avaliação física API Firebase",
        motivacao = "Criação de coluna ID na tabela de avaliação física API Firebase - APPS-2305")
public class CriaColunaIDAvaliacaoFisicaAPIFirebase implements MigracaoVersaoInterface {

    @Override
    public void executar(String ctx, UsuarioDao dao) throws Exception {
        try {
            dao.executeNativeSQL(ctx, "ALTER TABLE avaliacaofisica ADD COLUMN idAvaliacaoFisicaApi TEXT;");
        } catch (Exception e) {
        }
    }
}
