package br.com.pacto.base.jpa.migrador;

import br.com.pacto.base.jpa.annotation.ClasseProcesso;
import br.com.pacto.dao.intf.usuario.UsuarioDao;
import br.com.pacto.objeto.Uteis;

@ClasseProcesso(autor = "Diego Rocha do Bonfim",
        data = "20/01/2025",
        descricao = "Criação das tabelas para horário play",
        motivacao = "TW-1286")
public class Migration_TW_1286 implements MigracaoVersaoInterface {

    @Override
    public void executar(String ctx, UsuarioDao dao) throws Exception {
        try {
            dao.executeNativeSQL(ctx, "create table alunolocacaohorarioplay\n" +
                    "(\n" +
                    "codigo SERIAL primary key,\n" +
                    "dataLancamento TIMESTAMP not null default NOW(),\n" +
                    "responsavelLancamento INTEGER not null,\n" +
                    "empresa_codigo INTEGER references empresa (codigo),\n" +
                    "cliente_codigo INTEGER references clientesintetico (codigo),\n" +
                    "locacaoHorario_codigo INTEGER references locacaoHorario (codigo),\n" +
                    "ambiente INTEGER not null,\n" +
                    "dia TIMESTAMP,\n" +
                    "checkin boolean default null\n" +
                    ");");

            dao.executeNativeSQL(ctx, "create table alunolocacaohorarioplaycheckincheckout\n" +
                    "(\n" +
                    "codigo SERIAL primary key,\n" +
                    "horaCheckin TIMESTAMP not null,\n" +
                    "horaCheckout TIMESTAMP,\n" +
                    "alunolocacaohorarioplay_codigo INTEGER references alunolocacaohorarioplay (codigo)\n" +
                    ");");

            dao.executeNativeSQL(ctx, "create table locacaoPlayCanceladaFinalizada (\n" +
                    "codigo SERIAL primary key,\n" +
                    "dataLancamento TIMESTAMP not null default NOW(),\n" +
                    "responsavelLancamento INTEGER not null,\n" +
                    "ambiente INTEGER not null,\n" +
                    "locacaoHorario_codigo INTEGER references locacaoHorario (codigo),\n" +
                    "dia TIMESTAMP,\n" +
                    "justificativa text,\n" +
                    "cancelada boolean default false,\n" +
                    "finalizada boolean default false\n" +
                    ");");
        } catch (Exception e) {
            Uteis.logar(e, Migration_TW_1286.class);
        }
    }
}
