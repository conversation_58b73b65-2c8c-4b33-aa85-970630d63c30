package br.com.pacto.base.jpa.migrador;

import br.com.pacto.base.jpa.annotation.ClasseProcesso;
import br.com.pacto.dao.intf.usuario.UsuarioDao;
import br.com.pacto.objeto.Uteis;

@ClasseProcesso(autor = "Diego Rocha do Bonfim",
        data = "14/03/2025",
        descricao = "Criar coluna modificadoPeloAluno para agendamentoLocacao",
        motivacao = "TW-1467")
public class Migration_TW_1467 implements MigracaoVersaoInterface {
    @Override
    public void executar(String ctx, UsuarioDao dao) throws Exception {
        try {
            dao.executeNativeSQL(ctx, "ALTER TABLE agendamentolocacao ADD COLUMN modificadopeloaluno BOOLEAN DEFAULT FALSE;");
        } catch (Exception e) {
            Uteis.logar(e, Migration_TW_1467.class);
        }
    }
}
