package br.com.pacto.base.jpa.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
public @interface ClasseProcesso {

    String autor();

    /**
     * Formato dd/MM/yyyy
     *
     * @return
     */
    String data();

    String descricao() default "N/A";

    String motivacao() default "N/A";

}
