package br.com.pacto.base.jpa.migrador;

import br.com.pacto.base.jpa.annotation.ClasseProcesso;
import br.com.pacto.dao.intf.usuario.UsuarioDao;
import br.com.pacto.objeto.Uteis;

@ClasseProcesso(
        autor = "Alisson Melo",
        data = "17/06/2025",
        descricao = "Remover anamneses de parq duplicadas indevidamente",
        motivacao = "TW-2422")
public class RemoverAnamnesesParQDuplicadas implements MigracaoVersaoInterface {

    @Override
    public void executar(String ctx, UsuarioDao dao) throws Exception {
        try {
            // Excluir registros de relacionamento perguntaanamnese onde parqpadraorj é null e parq é true e perguntaanamnese não está associado a nenhuma resposta
            dao.executeNativeSQL(ctx,
                    "DELETE FROM perguntaanamnese\n" +
                        "WHERE codigo IN (\n" +
                        "    SELECT pa.codigo\n" +
                        "    FROM perguntaanamnese pa\n" +
                        "    INNER JOIN anamnese a ON a.codigo = pa.anamnese_codigo\n" +
                        "    WHERE a.parqpadraorj IS NULL\n" +
                        "    AND a.parq = true\n" +
                        "    AND pa.codigo NOT IN (SELECT r.perguntaanamnese_codigo FROM respostacliente r)\n" +
                        ");");

            // Excluir registros anamneses que estão duplicadas
            dao.executeNativeSQL(ctx,
                    "DELETE FROM anamnese \n" +
                        "WHERE codigo NOT IN (\n" +
                        "   SELECT p.anamnese_codigo FROM perguntaanamnese p\n" +
                        ") AND parq = true;");
        } catch (Exception e) {
            Uteis.logar(e, RemoverAnamnesesParQDuplicadas.class);
        }
    }
}
