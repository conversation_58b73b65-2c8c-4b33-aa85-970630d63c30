package br.com.pacto.base.jpa.service.intf;

import br.com.pacto.base.jpa.dto.TreinoAtividadeDTO;
import br.com.pacto.bean.atividade.Atividade;
import br.com.pacto.controller.json.atividade.read.AtividadeJSON;
import br.com.pacto.controller.json.programa.AtividadeFichaJSON;
import br.com.pacto.service.exception.ServiceException;

import java.util.List;

public interface PovoadorAtividadeService {

    String runPovoarByCsv(final String ctx, final String csvBase64Data) throws Exception;

    String runPovoarByCsvV2(String ctx, List<TreinoAtividadeDTO> atividades) throws Exception;

    String importarAtividadeFichaSerieJson(String ctx, List<AtividadeFichaJSON> atividadeFichaJSONS) throws ServiceException;

    String salvarAtividadeImportadaDaMatrizNaFilial(String ctxFilial, AtividadeJSON atividadeJSON) throws Exception;

    String runImportarAtividadesCross(final String ctxOrigem, final String ctxDestino) throws Exception;

    void salvarAtividadeAnimacao(String ctx, Atividade atividade, String nomeAnimacao, String fotoKey, String fotoKeyPequena, String fotoKeyMin) throws ServiceException;


}
