package br.com.pacto.base.jpa.migrador;

import br.com.pacto.base.jpa.annotation.ClasseProcesso;
import br.com.pacto.dao.intf.usuario.UsuarioDao;

@ClasseProcesso(autor = "<PERSON><PERSON>",
        data = "07/03/2025",
        descricao = "Cria coluna comentario na tabela avaliacaoprofessor",
        motivacao = "APPS-2899")
public class CriaColunaComentarioAvaliacaoProfessor implements MigracaoVersaoInterface {

    @Override
    public void executar(String ctx, UsuarioDao dao) throws Exception {
        try {
            dao.executeNativeSQL(ctx, "ALTER TABLE avaliacaoprofessor \n" +
                    "ADD COLUMN comentario TEXT;\n");
        } catch (Exception e) {
        }
    }
}
