/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.base.jpa.service.intf;

import br.com.pacto.base.jpa.bean.Versao;
import br.com.pacto.service.exception.ServiceException;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public interface VersaoService {

    public static final String SERVICE_NAME = "VersaoService";

    public Versao inserir(final String ctx, Versao object) throws ServiceException;

    public Versao obterPorId(final String ctx, Integer id) throws ServiceException;

    public Versao alterar(final String ctx, Versao object) throws ServiceException;

    public void excluir(final String ctx, Versao object) throws ServiceException;

    public List<Versao> obterTodos(final String ctx) throws ServiceException;

    public List<Versao> obterPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException;

    public List<Versao> obterPorParam(final String ctx, String query,
            Map<String, Object> params, int max, int index)
            throws ServiceException;

    public Versao obterObjetoPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException;
}
