package br.com.pacto.base.jpa.migrador;

import br.com.pacto.base.jpa.annotation.ClasseProcesso;
import br.com.pacto.dao.intf.usuario.UsuarioDao;
import br.com.pacto.objeto.Uteis;

@ClasseProcesso(
        autor = "Alisson Melo",
        data = "21/03/2025",
        descricao = "Corrigir valores NaN da avaliacaofisica gerados pela integração de avaliação N2B pelo app",
        motivacao = "TW-1836"
)
public class AtualizarValoresNaNAvaliacaoFisica implements MigracaoVersaoInterface {

    @Override
    public void executar(String ctx, UsuarioDao dao) throws Exception {
        // ATUALIZAR DADOS NAN PARA 0 EM PESOOSSEO
        try {
            dao.executeNativeSQL(ctx, "UPDATE pesoosseo SET pesogordura = 0 WHERE CAST(pesogordura AS TEXT) = 'NaN';");
        } catch (Exception e) {
            Uteis.logar(e, AtualizarValoresNaNAvaliacaoFisica.class);
        }
        try {
            dao.executeNativeSQL(ctx, "UPDATE pesoosseo SET pesomuscular = 0 WHERE CAST(pesomuscular AS TEXT) = 'NaN';");
        } catch (Exception e) {
            Uteis.logar(e, AtualizarValoresNaNAvaliacaoFisica.class);
        }
        try {
            dao.executeNativeSQL(ctx, "UPDATE pesoosseo SET pesoosseo = 0 WHERE CAST(pesoosseo AS TEXT) = 'NaN';");
        } catch (Exception e) {
            Uteis.logar(e, AtualizarValoresNaNAvaliacaoFisica.class);
        }
        try {
            dao.executeNativeSQL(ctx, "UPDATE pesoosseo SET pesoresidual = 0 WHERE CAST(pesoresidual AS TEXT) = 'NaN';");
        } catch (Exception e) {
            Uteis.logar(e, AtualizarValoresNaNAvaliacaoFisica.class);
        }
        try {
            dao.executeNativeSQL(ctx, "UPDATE pesoosseo SET altura = 0 WHERE CAST(altura AS TEXT) = 'NaN';");
        } catch (Exception e) {
            Uteis.logar(e, AtualizarValoresNaNAvaliacaoFisica.class);
        }

        // ATUALIZAR DADOS NAN PARA 0 EM AVALIACAOFISICA
        try {
            dao.executeNativeSQL(ctx, "UPDATE avaliacaofisica SET altura = 0 WHERE CAST(altura AS TEXT) = 'NaN';");
        } catch (Exception e) {
            Uteis.logar(e, AtualizarValoresNaNAvaliacaoFisica.class);
        }
        try {
            dao.executeNativeSQL(ctx, "UPDATE avaliacaofisica SET imc = 0 WHERE CAST(imc AS TEXT) = 'NaN';");
        } catch (Exception e) {
            Uteis.logar(e, AtualizarValoresNaNAvaliacaoFisica.class);
        }
        try {
            dao.executeNativeSQL(ctx, "UPDATE avaliacaofisica SET gorduravisceral = 0 WHERE CAST(gorduravisceral AS TEXT) = 'NaN';");
        } catch (Exception e) {
            Uteis.logar(e, AtualizarValoresNaNAvaliacaoFisica.class);
        }
        try {
            dao.executeNativeSQL(ctx, "UPDATE avaliacaofisica SET gorduraideal = 0 WHERE CAST(gorduraideal AS TEXT) = 'NaN';");
        } catch (Exception e) {
            Uteis.logar(e, AtualizarValoresNaNAvaliacaoFisica.class);
        }
        try {
            dao.executeNativeSQL(ctx, "UPDATE avaliacaofisica SET pesomuscular = 0 WHERE CAST(pesomuscular AS TEXT) = 'NaN';");
        } catch (Exception e) {
            Uteis.logar(e, AtualizarValoresNaNAvaliacaoFisica.class);
        }
    }
}