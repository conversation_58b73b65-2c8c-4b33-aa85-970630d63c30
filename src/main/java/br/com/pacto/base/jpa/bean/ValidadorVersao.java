package br.com.pacto.base.jpa.bean;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import java.io.Serializable;
import java.util.Date;

@Entity
public class ValidadorVersao implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private Boolean sucesso;
    private String nomeProcesso;
    private Date dataExecucao;

    public ValidadorVersao() {
    }

    public ValidadorVersao(Boolean sucesso, String nomeProcesso) {
        this.sucesso = sucesso;
        this.nomeProcesso = nomeProcesso;
    }

    public ValidadorVersao(Boolean sucesso, String nomeProcesso, Date dataExecucao) {
        this.sucesso = sucesso;
        this.nomeProcesso = nomeProcesso;
        this.dataExecucao = dataExecucao;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Boolean getSucesso() {
        return sucesso;
    }

    public void setSucesso(Boolean sucesso) {
        this.sucesso = sucesso;
    }

    public String getNomeProcesso() {
        return nomeProcesso;
    }

    public void setNomeProcesso(String nomeProcesso) {
        this.nomeProcesso = nomeProcesso;
    }

    public Date getDataExecucao() {
        return dataExecucao;
    }

    public void setDataExecucao(Date dataExecucao) {
        this.dataExecucao = dataExecucao;
    }
}
