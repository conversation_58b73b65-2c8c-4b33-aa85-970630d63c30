package br.com.pacto.base.jpa.migrador;

import br.com.pacto.base.jpa.annotation.ClasseProcesso;
import br.com.pacto.dao.intf.usuario.UsuarioDao;
import br.com.pacto.objeto.Uteis;

@ClasseProcesso(autor = "<PERSON><PERSON>",
        data = "24/06/2025",
        descricao = "Ajusta o padrão de notificações de aula agendada para remover a coluna cliente_codigo e ajustar os dados existentes.",
        motivacao = "APPS-3637")
public class AjustaPadraoNotificacaoAulaAgendadaExcluiColunaClienteCodigo implements MigracaoVersaoInterface {

    @Override
    public void executar(String ctx, UsuarioDao dao) throws Exception {
        try {
            dao.executeNativeSQL(ctx, "DO $$\n" +
                    "BEGIN\n" +
                    "    IF NOT EXISTS (\n" +
                    "        SELECT 1 FROM information_schema.columns\n" +
                    "        WHERE table_name = 'notificacaoaulaagendada'\n" +
                    "          AND column_name = 'cliente'\n" +
                    "    ) THEN\n" +
                    "        ALTER TABLE notificacaoaulaagendada\n" +
                    "            ADD COLUMN cliente integer;\n" +
                    "    END IF;\n" +
                    "\n" +
                    "    IF EXISTS (\n" +
                    "        SELECT 1 FROM information_schema.columns\n" +
                    "        WHERE table_name = 'notificacaoaulaagendada'\n" +
                    "          AND column_name = 'cliente_codigo'\n" +
                    "    ) AND EXISTS (\n" +
                    "        SELECT 1 FROM information_schema.columns\n" +
                    "        WHERE table_name = 'notificacaoaulaagendada'\n" +
                    "          AND column_name = 'cliente'\n" +
                    "    ) THEN\n" +
                    "        UPDATE notificacaoaulaagendada\n" +
                    "        SET cliente = cliente_codigo\n" +
                    "        WHERE cliente IS NULL;\n" +
                    "    END IF;\n" +
                    "\n" +
                    "    IF EXISTS (\n" +
                    "        SELECT 1 FROM information_schema.columns\n" +
                    "        WHERE table_name = 'notificacaoaulaagendada'\n" +
                    "          AND column_name = 'cliente_codigo'\n" +
                    "    ) THEN\n" +
                    "        ALTER TABLE notificacaoaulaagendada\n" +
                    "            DROP COLUMN cliente_codigo;\n" +
                    "    END IF;\n" +
                    "\n" +
                    "    IF NOT EXISTS (\n" +
                    "        SELECT 1\n" +
                    "        FROM information_schema.table_constraints tc\n" +
                    "        JOIN information_schema.key_column_usage kcu\n" +
                    "          ON tc.constraint_name = kcu.constraint_name\n" +
                    "        WHERE tc.table_name = 'notificacaoaulaagendada'\n" +
                    "          AND tc.constraint_type = 'FOREIGN KEY'\n" +
                    "          AND kcu.column_name = 'cliente'\n" +
                    "    ) THEN\n" +
                    "        ALTER TABLE notificacaoaulaagendada\n" +
                    "            ADD CONSTRAINT fk_notificacao_cliente\n" +
                    "            FOREIGN KEY (cliente)\n" +
                    "            REFERENCES clientesintetico (codigo);\n" +
                    "    END IF;\n" +
                    "END\n" +
                    "$$;\n");
        } catch (Exception e) {
            Uteis.logar(e, AjustaPadraoNotificacaoAulaAgendadaExcluiColunaClienteCodigo.class);
        }
    }
}
