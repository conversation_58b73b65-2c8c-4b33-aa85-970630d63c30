/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.pacto.base.jpa.dao.intf;

import br.com.pacto.base.jpa.bean.HistoricoVersao;
import br.com.pacto.dao.intf.base.DaoGenerico;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface HistoricoVersaoDao extends DaoGenerico<HistoricoVersao, Integer> {

    List<HistoricoVersao> findAllByClassesMigradoras(String ctx, List<String> classesMigradoras) throws Exception;

}
