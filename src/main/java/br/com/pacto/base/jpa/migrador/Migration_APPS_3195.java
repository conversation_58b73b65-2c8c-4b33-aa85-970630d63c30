package br.com.pacto.base.jpa.migrador;

import br.com.pacto.base.jpa.annotation.ClasseProcesso;
import br.com.pacto.dao.intf.usuario.UsuarioDao;
import br.com.pacto.objeto.Uteis;

@ClasseProcesso(autor = "<PERSON><PERSON>",
        data = "15/04/2025",
        descricao = "Cria colunas lesao app",
        motivacao = "APPS-3195")
public class Migration_APPS_3195 implements MigracaoVersaoInterface {

    @Override
    public void executar(String ctx, UsuarioDao dao) throws Exception {
        try {
            dao.executeNativeSQL(ctx, "ALTER TABLE lesao ADD COLUMN app BOOLEAN DEFAULT FALSE;");
            dao.executeNativeSQL(ctx, "ALTER TABLE lesao ADD COLUMN dataatualizacao DATE;");
            dao.executeNativeSQL(ctx, "ALTER TABLE lesao ADD COLUMN datarecuperacao DATE;");
        } catch (Exception e) {
            Uteis.logar(e, AA_FirstMigration.class);
        }
    }
}
