package br.com.pacto.base.jpa.migrador;

import br.com.pacto.base.jpa.annotation.ClasseProcesso;
import br.com.pacto.dao.intf.usuario.UsuarioDao;
import br.com.pacto.objeto.Uteis;

@ClasseProcesso(
        autor = "Alisson Melo",
        data = "19/02/2025",
        descricao = "Criação de index consultas indicadores dash avaliação física",
        motivacao = "TW-1530 - otimizar consultas")
public class Migration_TW_1530 implements MigracaoVersaoInterface {

    @Override
    public void executar(String ctx, UsuarioDao dao) throws Exception {
        try {
            dao.executeNativeSQL(ctx, "CREATE INDEX idx_usuario_usuariozw_codigo ON usuario (usuariozw, codigo);");
        } catch (Exception e) {
            Uteis.logar(e, Migration_TW_1530.class);
        }
        try {
            dao.executeNativeSQL(ctx, "CREATE INDEX idx_avaliacaofisica_dataavaliacao ON avaliacaofisica (dataavaliacao);");
        } catch (Exception e) {
            Uteis.logar(e, Migration_TW_1530.class);
        }
        try {
            dao.executeNativeSQL(ctx, "CREATE INDEX idx_avaliacaofisica_dataproxima ON avaliacaofisica (dataproxima);");
        } catch (Exception e) {
            Uteis.logar(e, Migration_TW_1530.class);
        }
        try {
            dao.executeNativeSQL(ctx, "CREATE INDEX idx_avfisica_cli_codigo_codigo ON avaliacaofisica (cliente_codigo, codigo);");
        } catch (Exception e) {
            Uteis.logar(e, Migration_TW_1530.class);
        }
        try {
            dao.executeNativeSQL(ctx, "CREATE INDEX idx_clientesintetico_codigo_empresa ON clientesintetico (codigo, empresa);");
        } catch (Exception e) {
            Uteis.logar(e, Migration_TW_1530.class);
        }
        try {
            dao.executeNativeSQL(ctx, "CREATE INDEX idx_empresa_codzw ON empresa (codZW);");
        } catch (Exception e) {
            Uteis.logar(e, Migration_TW_1530.class);
        }
    }

}
