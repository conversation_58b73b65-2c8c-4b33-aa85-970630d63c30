package br.com.pacto.base.jpa.migrador;

import br.com.pacto.base.jpa.annotation.ClasseProcesso;
import br.com.pacto.dao.intf.usuario.UsuarioDao;
import br.com.pacto.objeto.Uteis;

@ClasseProcesso(autor = "Diego Rocha do Bonfim",
        data = "09/02/2025",
        descricao = "Criar colunas dataDisponibilidade e tempoMinimoMinutos para locacaoHorario",
        motivacao = "TW-1425")
public class Migration_TW_1425 implements MigracaoVersaoInterface {
    @Override
    public void executar(String ctx, UsuarioDao dao) throws Exception {
        try {
            dao.executeNativeSQL(ctx, "ALTER TABLE locacaohorario ADD COLUMN tempominimominutos INT DEFAULT null;");
        } catch (Exception e) {
            Uteis.logar(e, Migration_TW_1425.class);
        }

        try {
            dao.executeNativeSQL(ctx, "ALTER TABLE locacaohorario ADD COLUMN datadisponibilidade VARCHAR(10) DEFAULT null;");
        } catch (Exception e) {
            Uteis.logar(e, Migration_TW_1425.class);
        }
    }
}
