/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pacto.base.jpa.service.impl;

import br.com.pacto.base.jpa.service.intf.PovoadorService;
import br.com.pacto.bean.atividade.Atividade;
import br.com.pacto.bean.atividade.AtividadeCategoriaAtividade;
import br.com.pacto.bean.atividade.AtividadeFicha;
import br.com.pacto.bean.atividade.CategoriaAtividade;
import br.com.pacto.bean.atividade.MetodoExecucaoEnum;
import br.com.pacto.bean.atividade.TipoAtividadeEnum;
import br.com.pacto.bean.configuracoes.ConfiguracaoSistema;
import br.com.pacto.bean.configuracoes.ConfiguracoesEnum;
import br.com.pacto.bean.ficha.CategoriaFicha;
import br.com.pacto.bean.ficha.Ficha;
import br.com.pacto.bean.nivel.Nivel;
import br.com.pacto.bean.programa.ObjetivoPredefinido;
import br.com.pacto.bean.serie.Serie;
import br.com.pacto.bean.tipoEvento.TipoEvento;
import br.com.pacto.controller.json.atividade.read.AtividadeFichaJSON;
import br.com.pacto.controller.json.atividade.read.AtividadeJSON;
import br.com.pacto.controller.json.ficha.read.FichaJSON;
import br.com.pacto.controller.json.atividade.read.SerieJSON;
import br.com.pacto.dao.intf.animacao.AnimacaoDao;
import br.com.pacto.dao.intf.atividade.AtividadeDao;
import br.com.pacto.dao.intf.atividade.CategoriaAtividadeDao;
import br.com.pacto.dao.intf.ficha.CategoriaFichaDao;
import br.com.pacto.enumerador.agenda.TipoAgendamentoEnum;
import br.com.pacto.enumerador.agenda.TipoDuracaoEvento;
import br.com.pacto.objeto.ColecaoUtils;
import br.com.pacto.objeto.JSONMapper;
import br.com.pacto.objeto.Numero;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.intf.animacao.AnimacaoService;
import br.com.pacto.service.intf.atividade.AtividadeService;
import br.com.pacto.service.intf.configuracoes.ConfiguracaoSistemaService;
import br.com.pacto.service.intf.ficha.CategoriaFichaService;
import br.com.pacto.service.intf.ficha.FichaService;
import br.com.pacto.service.intf.nivel.NivelService;
import br.com.pacto.service.intf.programa.ObjetivoPredefinidoService;
import br.com.pacto.service.intf.tipoEvento.TipoEventoService;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.ViewUtils;
import br.com.pacto.util.enumeradores.PaletaCoresEnum;
import org.json.JSONObject;
import java.io.File;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.collections.Predicate;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import servicos.integracao.admapp.AdmAppWSConsumer;
import servicos.integracao.admapp.client.Midia;

/**
 *
 * <AUTHOR>
 */
@Service
public class PovoadorServiceImpl implements PovoadorService {

    private static final String path = "/br/com/pacto/util/resources/povoador";
    @Autowired
    private ViewUtils viewUtils;
    @Autowired
    private AtividadeService atvs;
    @Autowired
    private NivelService nivelService;
    @Autowired
    private CategoriaFichaService categoriaFichaService;
    @Autowired
    private ObjetivoPredefinidoService objetivoPredefinidoService;
    @Autowired
    private TipoEventoService tipoEventoService;
    @Autowired
    private FichaService fichaService;
    @Autowired
    private CategoriaFichaDao categoriaFichaDao;
    @Autowired
    private AtividadeDao atividadeDao;
    @Autowired
    private AnimacaoDao animacaoDao;
    @Autowired
    private CategoriaAtividadeDao categoriaAtividadeDao;
    @Autowired
    private ConfiguracaoSistemaService configuracaoSistemaService;

    public ViewUtils getViewUtils() {
        return viewUtils;
    }

    public void setViewUtils(ViewUtils viewUtils) {
        this.viewUtils = viewUtils;
    }

    public AnimacaoDao getAnimacaoDao() {
        return animacaoDao;
    }

    public CategoriaAtividadeDao getCategoriaAtividadeDao() {
        return categoriaAtividadeDao;
    }

    private String[] obterAtributos(final String reg) {
        return reg.replaceAll("\\{\\}", "{ }").replaceAll("\\{", "").replaceAll("\\}", "").split(";");
    }

    private List<Atividade> inserirAtividades(final String ctx) throws Exception {
        List<Atividade> lista = new ArrayList<Atividade>();
        File arq = new File(this.getClass().getResource(path + "/atividades").toURI());

        /*************************INSERIR A ANIMAÇÃO*******************************/
        //Obter Midias Remotas
        String ctxObterMidias = "f6346c926a547ad524cdccd5bfcceba0"; //CHAVE DO BANCO PRIMATAS, SERÁ O PADRÃO PARA AS DEMAIS
        List<Midia> listaMidias = AdmAppWSConsumer.obterMidias(ctxObterMidias);

        if (!UteisValidacao.emptyList(listaMidias)) {
            CategoriaAtividade categoriaAtividade = getCategoriaAtividadeDao().insertOrGetObjectForName(ctx, "NEUROMUSCULAR");
            for (Midia midia : listaMidias) {
                if (!midia.getNome().equals("230.jpg")) {
                    Atividade atividade = new Atividade();

                    atividade.setNome(midia.getTitulo());
                    atividade.setVersao(0);
                    atividade.setTipo(TipoAtividadeEnum.ANAEROBICO);

                    atividade.getCategorias().add(new AtividadeCategoriaAtividade(categoriaAtividade, atividade));

                    try {

                        atvs.add(ctx, atividade, midia);
                    } catch (Exception ex) {
                        System.out.println("Atividade: " + atividade.getNome() + " ++++++++++++++++++++++++++++++++++++" + ex);
                    }
                }
            }
        } else {
            System.out.println("Não foi possivel povoar as atividades");
        }

        return lista;
    }

    private List<Nivel> inserirNiveis(final String ctx) throws Exception {
        List<Nivel> lista = new ArrayList<Nivel>();
        File arq = new File(this.getClass().getResource(path + "/niveis").toURI());
        List<String> linhas = FileUtils.readLines(arq, "UTF-8");
        for (String reg : linhas) {
            String[] attrs = obterAtributos(reg);
            try {
                lista.add(nivelService.inserir(ctx, new Nivel(attrs[0], Integer.valueOf(attrs[1]))));
                Uteis.logar(null, "Inseriu Nivel -> " + reg);
            } catch (Exception e) {
                Uteis.logar(null, "Erro inserindo Nivel -> " + reg + "\n" + e.getMessage() + " - " + e.getLocalizedMessage());
            }
        }

        return lista;
    }

    private List<CategoriaFicha> inserirCategoriasFicha(final String ctx) throws Exception {
        List<CategoriaFicha> lista = new ArrayList<CategoriaFicha>();
        File arq = new File(this.getClass().getResource(path + "/categoria_fichas").toURI());
        List<String> linhas = FileUtils.readLines(arq, "UTF-8");
        for (String reg : linhas) {
            String[] attrs = obterAtributos(reg);
            try {
                lista.add(categoriaFichaService.inserir(ctx, new CategoriaFicha(attrs[0])));
                Uteis.logar(null, "Inseriu Categoria de Ficha -> " + reg);
            } catch (Exception e) {
                Uteis.logar(null, "Erro inserindo Categoria de Ficha -> " + reg + "\n" + e.getMessage() + " - " + e.getLocalizedMessage());
            }
        }

        return lista;
    }

    private List<ObjetivoPredefinido> inserirObjetivosPredefinidos(final String ctx) throws Exception {
        List<ObjetivoPredefinido> lista = new ArrayList<ObjetivoPredefinido>();
        File arq = new File(this.getClass().getResource(path + "/objetivos_predefinidos").toURI());
        List<String> linhas = FileUtils.readLines(arq, "UTF-8");
        for (String reg : linhas) {
            String[] attrs = obterAtributos(reg);
            try {
                lista.add(objetivoPredefinidoService.inserir(ctx, new ObjetivoPredefinido(attrs[0])));
                Uteis.logar(null, "Inseriu Objetivo Predefinido -> " + reg);
            } catch (Exception e) {
                Uteis.logar(null, "Erro inserindo Objetivo Predefinido -> " + reg + "\n" + e.getMessage() + " - " + e.getLocalizedMessage());
            }
        }
        return lista;
    }

    private List<TipoEvento> inserirTiposEvento(final String ctx) throws Exception {
        List<TipoEvento> lista = new ArrayList<TipoEvento>();
        File arq = new File(this.getClass().getResource(path + "/tipoevento").toURI());
        //comportamento,cor,nome,dias,nrAgendamentos,apenasAlunosCarteira,duracao,duracaoMinutosMax,duracaoMinutosMin,intervaloMinimoFalta
        //4;31;"Festa";0;10;t;2;90;50;0
        List<String> linhas = FileUtils.readLines(arq, "UTF-8");
        for (String reg : linhas) {
            int i = 0;
            String[] attrs = obterAtributos(reg);
            try {
                TipoAgendamentoEnum comportamento = TipoAgendamentoEnum.getFromId(Integer.valueOf(attrs[i++]));
                PaletaCoresEnum cor = PaletaCoresEnum.getFromId(Numero.getInteiro(attrs[i++]));
                final String nome = attrs[i++];
                Integer dias = Numero.getInteiro(attrs[i++]);
                Integer nrAgendamentos = Numero.getInteiro(attrs[i++]);
                boolean apenasAlunosCarteira = attrs[i++].equalsIgnoreCase("f") ? false : true;
                TipoDuracaoEvento duracao = TipoDuracaoEvento.getFromId(Numero.getInteiro(attrs[i++]));
                Integer duracaoMinutosMax = Numero.getInteiro(attrs[i++]);
                Integer duracaoMinutosMin = Numero.getInteiro(attrs[i++]);
                Integer intervaloMinimoFalta = Numero.getInteiro(attrs[i++]);
                //
                lista.add(tipoEventoService.inserir(ctx, new TipoEvento(comportamento,
                        cor, nome, dias, nrAgendamentos, apenasAlunosCarteira,
                        duracao, duracaoMinutosMax, duracaoMinutosMin, intervaloMinimoFalta)));
                Uteis.logar(null, "Inseriu Tipo Evento -> " + reg);
            } catch (Exception e) {
                Uteis.logar(null, "Erro inserindo Tipo Evento -> " + reg + "\n" + e.getMessage() + " - " + e.getLocalizedMessage());
            }
        }
        return lista;
    }

    private List<Ficha> inserirFichasPredefinidas(final String ctx) throws Exception {
        List<Ficha> lista = new ArrayList<Ficha>();
        File arq = new File(this.getClass().getResource(path + "/fichas_predefinidas").toURI());
        JSONObject root = new JSONObject(FileUtils.readFileToString(arq, "UTF-8"));

        List<FichaJSON> jsonFichas = JSONMapper.getList(root.getJSONArray("fichaJSONList"), FichaJSON.class);
        List<AtividadeJSON> jsonAtividades = JSONMapper.getList(root.getJSONArray("atividadeJSONList"), AtividadeJSON.class);

        for (FichaJSON fj : jsonFichas) {
            Ficha f = new Ficha();
            f.setUsarComoPredefinida(true);
            f.setNome(fj.getNome());
            f.setMensagemAluno(fj.getMensagemAluno());
            if (fj.getCategoria() != null && !fj.getCategoria().isEmpty()) {
                f.setCategoria(categoriaFichaDao.insertOrGetObjectForName(ctx, fj.getCategoria()));
            }
            f = fichaService.inserir(ctx, f);
            for (AtividadeFichaJSON atvj : fj.getAtividades()) {
                AtividadeFicha atv = new AtividadeFicha();
                final Integer codAtividade = Numero.getInteiro(atvj.getAtividade());
                AtividadeJSON detalheAtvJSON = (AtividadeJSON) ColecaoUtils.find(jsonAtividades, new Predicate() {
                    public boolean evaluate(Object o) {
                        return ((AtividadeJSON) o).getCod().intValue() == codAtividade;
                    }
                });
                atv.setAtividade(atividadeDao.insertOrGetObjectForName(ctx, detalheAtvJSON.getNome()));
                TipoAtividadeEnum tipo = TipoAtividadeEnum.ANAEROBICO;
                if (detalheAtvJSON.getTipo() != null) {
                    try {
                        tipo = TipoAtividadeEnum.getFromId(detalheAtvJSON.getTipo());
                    } catch (Exception e) {
                        tipo = TipoAtividadeEnum.ANAEROBICO;
                    }
                }
                atv.getAtividade().setTipo(tipo);
                atv.setNome(atvj.getAtividade());
                atv.setFicha(f);
                atv.setMetodoExecucao(atvj.getCodMetodoExecucao() != null
                        ? MetodoExecucaoEnum.getFromId(atvj.getCodMetodoExecucao()) : null);
                atv.setOrdem(atvj.getOrdem());
                for (SerieJSON sj : atvj.getSeries()) {
                    Serie s = new Serie();
                    s.setAtividadeFicha(atv);
                    s.setComplemento(sj.getComplemento());
                    s.setDescansoStr(sj.getDescanso());
                    s.ajustarDadosPorTipo(sj.getValor1(), sj.getValor2());
                    atv.getSeries().add(s);
                }
                f.getAtividades().add(atv);
            }
            fichaService.salvarAtividadesDaFicha(ctx, f);
            Uteis.logar(null, "Inseriu Ficha Predefinida " + f.getNome());

        }
        return lista;
    }

    private List<ConfiguracaoSistema> inserirConfiguracoesEmail(final String ctx) throws Exception {
        List<ConfiguracaoSistema> configuracoesEmail = new ArrayList<ConfiguracaoSistema>();

        ConfiguracaoSistema configuracaoSistemaLogin = new ConfiguracaoSistema();
        configuracaoSistemaLogin.setConfiguracao(ConfiguracoesEnum.LOGIN);
        configuracaoSistemaLogin.setValor("pactosolucoes2017");
        configuracoesEmail.add(configuracaoSistemaLogin);

        ConfiguracaoSistema configuracaoSistemaSenha = new ConfiguracaoSistema();
        configuracaoSistemaSenha.setConfiguracao(ConfiguracoesEnum.SENHA);
        configuracaoSistemaSenha.setValor("RlOHgUGw8905");
        configuracoesEmail.add(configuracaoSistemaSenha);

        ConfiguracaoSistema configuracaoSistemaRemetente = new ConfiguracaoSistema();
        configuracaoSistemaRemetente.setConfiguracao(ConfiguracoesEnum.REMETENTE);
        configuracaoSistemaRemetente.setValor("");
        configuracoesEmail.add(configuracaoSistemaRemetente);

        ConfiguracaoSistema configuracaoSistemaEmailPadrao = new ConfiguracaoSistema();
        configuracaoSistemaEmailPadrao.setConfiguracao(ConfiguracoesEnum.EMAIL_PADRAO);
        configuracaoSistemaEmailPadrao.setValor("<EMAIL>");
        configuracoesEmail.add(configuracaoSistemaEmailPadrao);

        ConfiguracaoSistema configuracaoSistemaMailServer = new ConfiguracaoSistema();
        configuracaoSistemaMailServer.setConfiguracao(ConfiguracoesEnum.MAIL_SERVER);
        configuracaoSistemaMailServer.setValor("smtplw.com.br");
        configuracoesEmail.add(configuracaoSistemaMailServer);

        ConfiguracaoSistema configuracaoSistemaConexaoSegura = new ConfiguracaoSistema();
        configuracaoSistemaConexaoSegura.setConfiguracao(ConfiguracoesEnum.CONEXAO_SEGURA);
        configuracaoSistemaConexaoSegura.setValor("true");
        configuracoesEmail.add(configuracaoSistemaConexaoSegura);

        ConfiguracaoSistema configuracaoSistemaIniciarTLS = new ConfiguracaoSistema();
        configuracaoSistemaIniciarTLS.setConfiguracao(ConfiguracoesEnum.INICIAR_TLS);
        configuracaoSistemaIniciarTLS.setValor("false");
        configuracoesEmail.add(configuracaoSistemaIniciarTLS);

        for (ConfiguracaoSistema configuracaoSistema: configuracoesEmail) {
            configuracaoSistemaService.alterar(ctx, configuracaoSistema);
        }
        return configuracoesEmail;
    }

    public void run(final String ctx) throws Exception {
        inserirAtividades(ctx);
        inserirNiveis(ctx);
        inserirCategoriasFicha(ctx);
        inserirObjetivosPredefinidos(ctx);
        inserirTiposEvento(ctx);
        inserirFichasPredefinidas(ctx);
        inserirConfiguracoesEmail(ctx);
    }
}