package br.com.pacto.base.jpa.migrador;

import br.com.pacto.base.jpa.annotation.ClasseProcesso;
import br.com.pacto.dao.intf.usuario.UsuarioDao;
import br.com.pacto.objeto.Uteis;

@ClasseProcesso(
        autor = "Alisson Melo",
        data = "30/05/2025",
        descricao = "Cria coluna workout_id no programa de treino",
        motivacao = "Criar coluna para registrar o id do programa de treino gerado pela IA")
public class CriarColunaWorkoutIDIAProgramaTreino implements MigracaoVersaoInterface {

    @Override
    public void executar(String ctx, UsuarioDao dao) throws Exception {
        try {
            dao.executeNativeSQL(ctx, "ALTER TABLE programatreino ADD COLUMN workoutidia VARCHAR(255) DEFAULT null;");
        } catch (Exception e) {
            Uteis.logar(e, CriarColunaWorkoutIDIAProgramaTreino.class);
        }
    }

}
