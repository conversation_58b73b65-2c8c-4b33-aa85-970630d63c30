package br.com.pacto.base.jpa.migrador;

import br.com.pacto.base.jpa.annotation.ClasseProcesso;
import br.com.pacto.dao.intf.usuario.UsuarioDao;

@ClasseProcesso(autor = "<PERSON><PERSON>",
        data = "05/03/2025",
        descricao = "Cria tabelas de historico de presença do aluno",
        motivacao = "APPS-2806")
public class CriaTabelaHistoricoPresencaApp implements MigracaoVersaoInterface {

    @Override
    public void executar(String ctx, UsuarioDao dao) throws Exception {
        try {
            dao.executeNativeSQL(ctx, "CREATE TABLE historicopresenca (\n" +
                    "    codigo SERIAL PRIMARY KEY,\n" +
                    "    cliente_codigo INTEGER NOT NULL,\n" +
                    "    totalaulasrealizadas INTEGER DEFAULT 0,\n" +
                    "    aulasmesatual INTEGER DEFAULT 0,\n" +
                    "    semanasconsecutivas INTEGER DEFAULT 0,\n" +
                    "    dataatualizacao DATE,\n" +
                    "    FOREIGN KEY (cliente_codigo) REFERENCES clientesintetico(codigo) ON DELETE CASCADE\n" +
                    ");");
        } catch (Exception e) {
        }
    }
}
