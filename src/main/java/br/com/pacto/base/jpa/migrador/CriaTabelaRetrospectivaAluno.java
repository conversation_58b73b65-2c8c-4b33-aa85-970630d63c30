package br.com.pacto.base.jpa.migrador;

import br.com.pacto.base.jpa.annotation.ClasseProcesso;
import br.com.pacto.dao.intf.usuario.UsuarioDao;

@ClasseProcesso(autor = "<PERSON><PERSON>",
        data = "02/12/2024",
        descricao = "Cria tabelas de retrospectiva do aluno",
        motivacao = "Criação de tabelas de retrospectiva do aluno - APPS-2305")
public class CriaTabelaRetrospectivaAluno implements MigracaoVersaoInterface {

    @Override
    public void executar(String ctx, UsuarioDao dao) throws Exception {
        try {
            dao.executeNativeSQL(ctx, "CREATE TABLE retrospectivacache (\n" +
                    "    codigo SERIAL PRIMARY KEY,\n" +
                    "    matricula INT NOT NULL,\n" +
                    "    ano INT NOT NULL,\n" +
                    "    dados TEXT NOT NULL,\n" +
                    "    ultimaatualizacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n" +
                    "    UNIQUE (matricula, ano)\n" +
                    ");\n");
        } catch (Exception e) {
        }
    }
}
