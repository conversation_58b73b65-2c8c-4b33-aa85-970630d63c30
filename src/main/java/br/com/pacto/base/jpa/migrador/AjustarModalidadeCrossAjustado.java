package br.com.pacto.base.jpa.migrador;

import br.com.pacto.base.jpa.annotation.ClasseProcesso;
import br.com.pacto.dao.intf.usuario.UsuarioDao;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.impl.conexao.ConexaoZWServiceImpl;
import br.com.pacto.service.intf.conexao.ConexaoZWService;
import br.com.pacto.util.UtilContext;

import java.sql.Connection;
import java.sql.ResultSet;

@ClasseProcesso(autor = "Joao Alcides",
        data = "20/02/2025",
        descricao = "Ajustar modalidade crossfit",
        motivacao = "Problema com o novo cadastro de modalidade")
public class AjustarModalidadeCrossAjustado implements MigracaoVersaoInterface {

    @Override
    public void executar(String ctx, UsuarioDao dao) throws Exception {
        try {
            String sql = "select\n" +
                    " con.codigo as contrato,\n" +
                    " cli.codigomatricula,\n" +
                    " cli.codigo,\n" +
                    " cli.pessoa,\n" +
                    " mod.crossfit\n" +
                    "from\n" +
                    " contrato con\n" +
                    "inner join cliente cli on\n" +
                    " con.pessoa = cli.pessoa\n" +
                    "inner join contratomodalidade conmod on\n" +
                    " conmod.contrato = con.codigo\n" +
                    "inner join modalidade mod on\n" +
                    " mod.codigo = conmod.modalidade\n" +
                    "where\n" +
                    " con.situacao = 'AT'\n" +
                    " and mod.codigo::text in (\n" +
                    "  select\n" +
                    "   chaveprimaria\n" +
                    "  from\n" +
                    "   log\n" +
                    "  where\n" +
                    "   nomeentidade = 'MODALIDADE'\n" +
                    "   and operacao = 'INCLUSÃO'\n" +
                    "   and dataalteracao >= '2024-11-01'\n" +
                    "   and chaveprimaria not in (\n" +
                    "   select\n" +
                    "    chaveprimaria\n" +
                    "   from\n" +
                    "    log\n" +
                    "   where\n" +
                    "    nomeentidade = 'MODALIDADE'\n" +
                    "    and operacao = 'EXCLUSÃO'\n" +
                    "    and nomeentidadedescricao = 'Modalidade'" +
                    "    and dataalteracao >= '2024-11-01')\n" +
                    "  order by\n" +
                    "   codigo desc) and mod.crossfit;";

            ConexaoZWService zwService = UtilContext.getBean(ConexaoZWService.class);
            try(Connection conexaoZw = zwService.conexaoZw(ctx);
                ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sql, conexaoZw)) {
                StringBuilder matriculas = new StringBuilder();
                StringBuilder contratos = new StringBuilder();
                while (rs.next()) {
                    matriculas.append(",").append(rs.getInt("codigomatricula"));
                    contratos.append(",").append(rs.getInt("contrato"));
                }
                if(matriculas.length() == 0){
                    return;
                }
                dao.executeNativeSQL(ctx, "update clientesintetico set crossfit = true " +
                        "where matricula in ("+
                        matriculas.toString().replaceFirst(",", "")+")");

                ConexaoZWServiceImpl.executarConsulta("update contrato set crossfit = true " +
                        "where codigo in ("+
                        contratos.toString().replaceFirst(",", "")+")", conexaoZw);
            }
            System.out.println("processo de ajuste de modalidade crossfit finalizado");
        } catch (Exception e) {
            Uteis.logar(e, AjustarModalidadeCrossAjustado.class);
        }
    }
}
