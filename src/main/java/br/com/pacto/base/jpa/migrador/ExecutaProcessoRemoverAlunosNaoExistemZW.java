package br.com.pacto.base.jpa.migrador;

import br.com.pacto.base.jpa.annotation.ClasseProcesso;
import br.com.pacto.dao.intf.usuario.UsuarioDao;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.intf.configuracoes.ConfiguracaoSistemaService;
import br.com.pacto.util.UtilContext;

import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@ClasseProcesso(autor = "Kaio Sanchez",
        data = "22/05/2025",
        descricao = "Executa processo para remover alunos que não existem na tabela ZW",
        motivacao = "APPS-3435")
public class ExecutaProcessoRemoverAlunosNaoExistemZW implements MigracaoVersaoInterface {

    @Override
    public void executar(String ctx, UsuarioDao dao) throws Exception {
        ConfiguracaoSistemaService configuracaoSistemaService =
                UtilContext.getBean(ConfiguracaoSistemaService.class);
        String sqlObtemCodigosEmpresaZW = "select codigo from empresa";

        int totalEmpresas = 0;
        List<Integer> codEmpresas = new ArrayList<>();

        try (ResultSet rsEmpresa = dao.createStatement(ctx, sqlObtemCodigosEmpresaZW)) {
            while (rsEmpresa.next()) {
                codEmpresas.add(rsEmpresa.getInt("codigo"));
            }
        }

        totalEmpresas = codEmpresas.size();
        ExecutorService executor = Executors.newFixedThreadPool(Math.min(totalEmpresas, 4));

        for (Integer codEmpresa : codEmpresas) {
            executor.submit(() -> {
                try {
                    configuracaoSistemaService.executarExclusaoCliNaoExisteZw(ctx, codEmpresa);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            });
        }
        executor.shutdown();
    }
}
