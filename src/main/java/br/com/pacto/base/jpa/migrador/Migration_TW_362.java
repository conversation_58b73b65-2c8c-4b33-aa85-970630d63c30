package br.com.pacto.base.jpa.migrador;
import br.com.pacto.base.jpa.annotation.ClasseProcesso;
import br.com.pacto.bean.perfil.Perfil;
import br.com.pacto.bean.perfil.permissao.Permissao;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.bean.perfil.permissao.TipoPermissaoEnum;
import br.com.pacto.dao.intf.perfil.PerfilDao;
import br.com.pacto.dao.intf.perfil.permissao.PermissaoDao;
import br.com.pacto.dao.intf.usuario.UsuarioDao;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.util.UtilContext;

@ClasseProcesso(autor = "Anna Carolina",
        data = "30/04/2025",
        descricao = "Habilita a permissão EDITAR_AULAS_DIA",
        motivacao = "TW-362")
public class Migration_TW_362 implements MigracaoVersaoInterface {

    @Override
    public void executar(String ctx, UsuarioDao dao) throws Exception {
        try {
            PerfilDao perfilDao = UtilContext.getBean(PerfilDao.class);
            PermissaoDao permissaoDao = UtilContext.getBean(PermissaoDao.class);

            Perfil perfilConsultor = perfilDao.insertOrGetObjectForName(ctx, Perfil.NOME_PERFIL_CONSULTOR);
            Perfil perfilProfessor = perfilDao.insertOrGetObjectForName(ctx, Perfil.NOME_PERFIL_PROFESSOR);
            Perfil perfilCoordenador = perfilDao.insertOrGetObjectForName(ctx, Perfil.NOME_PERFIL_COORDENADOR);

            if (perfilConsultor.getPermissoes().isEmpty()) {
                perfilConsultor.addPermissao(new Permissao(RecursoEnum.EDITAR_AULAS_DIA, new TipoPermissaoEnum[]{TipoPermissaoEnum.TOTAL}, perfilConsultor));
                perfilDao.update(ctx, perfilConsultor);
            }
            Permissao permissao1 = new Permissao(RecursoEnum.EDITAR_AULAS_DIA, new TipoPermissaoEnum[]{TipoPermissaoEnum.TOTAL}, perfilProfessor);
            permissaoDao.insert(ctx, permissao1);
            Permissao permissao2 = new Permissao(RecursoEnum.EDITAR_AULAS_DIA, new TipoPermissaoEnum[]{TipoPermissaoEnum.TOTAL}, perfilCoordenador);
            permissaoDao.insert(ctx, permissao2);
        } catch (Exception e) {
            Uteis.logar(e, Migration_TW_362.class);
        }
    }
}
