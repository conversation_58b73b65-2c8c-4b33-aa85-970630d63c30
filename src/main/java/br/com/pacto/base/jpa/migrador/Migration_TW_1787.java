package br.com.pacto.base.jpa.migrador;

import br.com.pacto.base.jpa.annotation.ClasseProcesso;
import br.com.pacto.dao.intf.usuario.UsuarioDao;
import br.com.pacto.objeto.Uteis;

import java.sql.ResultSet;

@ClasseProcesso(
        autor = "Alisson Melo",
        data = "20/03/2024",
        descricao = "Inicializando nova configuração sistema UTILIZAR_NUMERACAO_SEQUENCIAL_IDENTIFICADOR_EQUIPAMENTO como false",
        motivacao = "TW-1787")
public class Migration_TW_1787 implements MigracaoVersaoInterface {

    @Override
    public void executar(String ctx, UsuarioDao dao) throws Exception {
        try {
            try (ResultSet rs = dao.createStatement(ctx, "SELECT EXISTS (SELECT  * FROM configuracaosistema c WHERE configuracao = 'UTILIZAR_NUMERACAO_SEQUENCIAL_IDENTIFICADOR_EQUIPAMENTO')")) {
                if (rs.next()) {
                    if (!rs.getBoolean("exists")) {
                        dao.executeNativeSQL(ctx, "INSERT INTO configuracaosistema (configuracao, valor) VALUES ('UTILIZAR_NUMERACAO_SEQUENCIAL_IDENTIFICADOR_EQUIPAMENTO' , 'false');");
                    }
                }
            }
        } catch (Exception e) {
            Uteis.logar(e, Migration_TW_1787.class);
        }
    }

}
