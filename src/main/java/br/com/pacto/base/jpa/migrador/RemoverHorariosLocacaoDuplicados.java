package br.com.pacto.base.jpa.migrador;

import br.com.pacto.base.jpa.annotation.ClasseProcesso;
import br.com.pacto.dao.intf.usuario.UsuarioDao;
import br.com.pacto.objeto.Uteis;

@ClasseProcesso(
        autor = "",
        data = "30/04/2025",
        descricao = "Remover horarios de locação duplicados que foram registrados indevidamente",
        motivacao = "TW-2054"
)
public class RemoverHorariosLocacaoDuplicados implements MigracaoVersaoInterface {

    @Override
    public void executar(String ctx, UsuarioDao dao) throws Exception {
        try {
            // remover apenas horarios duplicados que não possuem relacionamento com registros de AgendamentoLocacao

            // remover registros relacionados com AmbienteHorarioLocacao
            dao.executeNativeSQL(ctx, "WITH registros_a_manter AS (\n" +
                    "    SELECT MIN(codigo) AS codigo\n" +
                    "    FROM LocacaoHorario\n" +
                    "    GROUP BY \n" +
                    "        locacao,\n" +
                    "        diaSemana,\n" +
                    "        horaInicio,\n" +
                    "        horaFim,\n" +
                    "        permiteAgendarPeloAppTreino,\n" +
                    "        ativo,\n" +
                    "        responsavel,\n" +
                    "        tempoMinimoMinutos\n" +
                    "),\n" +
                    "registros_a_excluir AS (\n" +
                    "    SELECT l.codigo\n" +
                    "    FROM LocacaoHorario l\n" +
                    "    WHERE l.codigo NOT IN (\n" +
                    "        SELECT codigo FROM registros_a_manter\n" +
                    "    )\n" +
                    "    AND l.codigo NOT IN (\n" +
                    "        SELECT DISTINCT locacaohorario\n" +
                    "        FROM AgendamentoLocacao\n" +
                    "    )\n" +
                    ")\n" +
                    "DELETE FROM AmbienteHorarioLocacao\n" +
                    "WHERE locacaohorario IN (SELECT codigo FROM registros_a_excluir);");

            // remover horarios duplicados
            dao.executeNativeSQL(ctx, "WITH registros_a_manter AS (\n" +
                    "    SELECT MIN(codigo) AS codigo\n" +
                    "    FROM LocacaoHorario\n" +
                    "    GROUP BY \n" +
                    "        locacao,\n" +
                    "        diaSemana,\n" +
                    "        horaInicio,\n" +
                    "        horaFim,\n" +
                    "        permiteAgendarPeloAppTreino,\n" +
                    "        ativo,\n" +
                    "        responsavel,\n" +
                    "        tempoMinimoMinutos\n" +
                    "),\n" +
                    "registros_a_excluir AS (\n" +
                    "    SELECT l.codigo\n" +
                    "    FROM LocacaoHorario l\n" +
                    "    WHERE l.codigo NOT IN (\n" +
                    "        SELECT codigo FROM registros_a_manter\n" +
                    "    )\n" +
                    "    AND l.codigo NOT IN (\n" +
                    "        SELECT DISTINCT locacaohorario\n" +
                    "        FROM AgendamentoLocacao\n" +
                    "    )\n" +
                    ")\n" +
                    "DELETE FROM LocacaoHorario\n" +
                    "WHERE codigo IN (SELECT codigo FROM registros_a_excluir);");
        } catch (Exception e) {
            Uteis.logar(e, RemoverHorariosLocacaoDuplicados.class);
        }
    }
}