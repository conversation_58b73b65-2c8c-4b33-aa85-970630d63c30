package br.com.pacto.base.jpa.migrador;

import br.com.pacto.base.jpa.annotation.ClasseProcesso;
import br.com.pacto.dao.intf.usuario.UsuarioDao;
import br.com.pacto.objeto.Uteis;

@ClasseProcesso(autor = "Glauco Troncha Camargo",
        data = "18/11/2024",
        descricao = "Primeira migração",
        motivacao = "Teste e exemplo de migração via classe")
public class AA_FirstMigration implements MigracaoVersaoInterface {

    @Override
    public void executar(String ctx, UsuarioDao dao) throws Exception {
        try {
            dao.executeNativeSQL(ctx, "CREATE INDEX idx_historicoversao_classemigrador_trgm ON historicoversao USING gin (classemigrador gin_trgm_ops);");
        } catch (Exception e) {
            Uteis.logar(e, AA_FirstMigration.class);
        }
    }
}
