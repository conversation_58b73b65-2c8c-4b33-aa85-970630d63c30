package br.com.pacto.base.jpa.migrador;

import br.com.pacto.base.jpa.annotation.ClasseProcesso;
import br.com.pacto.bean.perfil.Perfil;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.dao.intf.usuario.UsuarioDao;
import br.com.pacto.objeto.Uteis;

@ClasseProcesso(
        autor = "Denis <PERSON>",
        data = "06/05/2025",
        descricao = "Permitir acessar as configurações do Ranking perfil COORDENADOR",
        motivacao = "TW-1751"
)
public class Migration_TW_1751 implements MigracaoVersaoInterface {

    @Override
    public void executar(String ctx, UsuarioDao dao) throws Exception {
        try {
            RecursoEnum[] recursos = {
                    RecursoEnum.CONFIGURACOES_DO_RANKING
            };
            for (RecursoEnum recurso : recursos) {
                dao.executeNative(ctx, "INSERT INTO permissao (codigo, recurso, perfil_codigo) VALUES ((select max(codigo)+1 from permissao), "+recurso.getId()+", (select codigo from perfil p where nome ='"+ Perfil.NOME_PERFIL_COORDENADOR+"')    );");
                dao.executeNative(ctx,"insert into permissao_tipopermissoes(permissao_codigo, tipopermissoes) VALUES ((select max(codigo) from permissao),5)");
            }

        } catch (Exception e) {
            Uteis.logar(e, Migration_TW_1751.class);
        }
    }
}
