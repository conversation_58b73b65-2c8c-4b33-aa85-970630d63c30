package br.com.pacto.base.jpa.migrador;

import br.com.pacto.base.jpa.annotation.ClasseProcesso;
import br.com.pacto.dao.intf.usuario.UsuarioDao;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.intf.programa.ProgramaTreinoService;
import br.com.pacto.util.UtilContext;

import java.sql.ResultSet;

@ClasseProcesso(
        autor = "Alisson Melo",
        data = "29/05/2025",
        descricao = "Executar sincronização de grupos musculares de atividades IA",
        motivacao = "TW-2281")
public class SincronizarGruposMuscularesAtividadeIa implements MigracaoVersaoInterface {

    @Override
    public void executar(String ctx, UsuarioDao dao) throws Exception {
        try {
            ProgramaTreinoService programaTreinoService = UtilContext.getBean(ProgramaTreinoService.class);
            String sql = "select exists (select * from atividade a where a.idia2 is not null) as existeatividadeia;";
            try (ResultSet rs = dao.createStatement(ctx, sql)) {
                if (rs.next()) {
                    if (rs.getBoolean("existeatividadeia")) {
                        new Thread(() -> {
                            Uteis.logarDebug("#### [SincronizarGruposMuscularesAtividadeIa] ++ Iniciando execução do processo SincronizarGruposMuscularesAtividadeIa para sincronizar grupos musculares de atividades IA");
                            try {
                                programaTreinoService.atualizarBancoAtividadesIA(ctx);
                            } catch (Exception e) {
                                throw new RuntimeException(e);
                            }
                            Uteis.logarDebug("#### [SincronizarGruposMuscularesAtividadeIa] -- Finalizando execução do processo SincronizarGruposMuscularesAtividadeIa para sincronizar grupos musculares de atividades IA");
                        }).start();
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            Uteis.logarDebug("#### [SincronizarGruposMuscularesAtividadeIa] Erro ao executar processo SincronizarGruposMuscularesAtividadeIa: " + e.getMessage());
        }
    }
}
