package br.com.pacto.base.jpa.migrador;

import br.com.pacto.base.jpa.annotation.ClasseProcesso;
import br.com.pacto.dao.intf.usuario.UsuarioDao;
import br.com.pacto.objeto.Uteis;

import java.sql.ResultSet;

@ClasseProcesso(autor = "Diego Rocha do Bonfim",
        data = "31/03/2025",
        descricao = "Inicializando nova configuração sistema BLOQUEAR_GERAR_REPOSICAO_AULA_JA_REPOSTA como false",
        motivacao = "TW-1564")
public class Migration_TW_1564 implements MigracaoVersaoInterface {
    @Override
    public void executar(String ctx, UsuarioDao dao) throws Exception {
        try (ResultSet rs = dao.createStatement(ctx, "SELECT EXISTS (SELECT * FROM configuracaosistema c WHERE configuracao = 'BLOQUEAR_GERAR_REPOSICAO_AULA_JA_REPOSTA')")) {
            if (rs.next()) {
                if (!rs.getBoolean("exists")) {
                    dao.executeNativeSQL(ctx, "INSERT INTO configuracaosistema (configuracao, valor) VALUES ('BLOQUEAR_GERAR_REPOSICAO_AULA_JA_REPOSTA', 'false');");
                }
            }
        } catch (Exception e) {
            Uteis.logar(e, Migration_TW_1564.class);
        }
    }
}
