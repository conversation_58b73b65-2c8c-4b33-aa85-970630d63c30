package br.com.pacto.base.jpa.migrador;

import br.com.pacto.base.jpa.annotation.ClasseProcesso;
import br.com.pacto.dao.intf.usuario.UsuarioDao;
import br.com.pacto.objeto.Uteis;

@ClasseProcesso(autor = "Denis Silva",
        data = "12/02/2025",
        descricao = "Criando processo para alinhar codigo nivel wod com a tabela nivelWod eliminando o codigo do NivelCrossfitEnum",
        motivacao = "TW-706")
public class AlinharCodigoNivelWod implements MigracaoVersaoInterface {

    @Override
    public void executar(String ctx, UsuarioDao dao) throws Exception {
        try {
            dao.executeNativeSQL(ctx, "update ScoreTreino set nivelcrossfit = 4 where nivelcrossfit = 3;");
            dao.executeNativeSQL(ctx, "update ScoreTreino set nivelcrossfit = 3 where nivelcrossfit = 2;");
            dao.executeNativeSQL(ctx, "update ScoreT<PERSON>ino set nivelcrossfit = 2 where nivelcrossfit = 1;");
            dao.executeNativeSQL(ctx, "update ScoreTreino set nivelcrossfit = 1 where nivelcrossfit = 0;");
        } catch (Exception e) {
            Uteis.logar(e, AA_FirstMigration.class);
        }
    }
}
