package br.com.pacto.base.jpa.migrador;

import br.com.pacto.base.jpa.annotation.ClasseProcesso;
import br.com.pacto.dao.intf.usuario.UsuarioDao;
import br.com.pacto.objeto.Uteis;

@ClasseProcesso(autor = "<PERSON>sson Melo",
        data = "10/11/2024",
        descricao = "Criar coluna apenasalunoscarteira para nova disponibilidade",
        motivacao = "TW-1073")
public class Migration_TW_1073 implements MigracaoVersaoInterface {

    @Override
    public void executar(String ctx, UsuarioDao dao) throws Exception {
        try {
            dao.executeNativeSQL(ctx, "ALTER TABLE horariodisponibilidade ADD COLUMN apenasalunoscarteira BOOLEAN DEFAULT FALSE;");
        } catch (Exception e) {
            Uteis.logar(e, AA_FirstMigration.class);
        }
    }
}
