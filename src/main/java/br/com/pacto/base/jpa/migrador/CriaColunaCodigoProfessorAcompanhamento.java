package br.com.pacto.base.jpa.migrador;

import br.com.pacto.base.jpa.annotation.ClasseProcesso;
import br.com.pacto.dao.intf.usuario.UsuarioDao;
import br.com.pacto.objeto.Uteis;

@ClasseProcesso(autor = "<PERSON>",
        data = "11/02/2025",
        descricao = "<PERSON><PERSON><PERSON> coluna codigo_professor_acompanhamento",
        motivacao = "<PERSON><PERSON><PERSON> coluna codigo_professor_acompanhamento - TW-1470")
public class CriaColunaCodigoProfessorAcompanhamento implements MigracaoVersaoInterface {

    @Override
    public void executar(String ctx, UsuarioDao dao) throws Exception {
        try {
            dao.executeNativeSQL(ctx, "ALTER TABLE atividadeficha ADD COLUMN codigo_professor_acompanhamento INTEGER;");
        } catch (Exception e) {
            Uteis.logar(e, CriaColunaCodigoProfessorAcompanhamento.class);
        }
    }
}
