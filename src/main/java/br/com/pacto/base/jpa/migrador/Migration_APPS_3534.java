package br.com.pacto.base.jpa.migrador;

import br.com.pacto.base.jpa.annotation.ClasseProcesso;
import br.com.pacto.dao.intf.usuario.UsuarioDao;
import br.com.pacto.objeto.Uteis;

@ClasseProcesso(autor = "<PERSON><PERSON>",
        data = "23/06/2025",
        descricao = "Criação do índice idx_selfloops_horario_dia_user na tabela turmahorarioatividadesintegracaoselfloops",
        motivacao = "APPS-3534")
public class Migration_APPS_3534 implements MigracaoVersaoInterface {

    @Override
    public void executar(String ctx, UsuarioDao dao) throws Exception {
        try {
            dao.executeNativeSQL(ctx, "CREATE INDEX idx_selfloops_horario_dia_user \n" +
                    "ON turmahorarioatividadesintegracaoselfloops (horarioturma, diaaula, useridselfloops);");
        } catch (Exception e) {
            Uteis.logar(e, Migration_APPS_3534.class);
        }
    }
}
