package br.com.pacto.base.jpa.migrador;

import br.com.pacto.base.jpa.annotation.ClasseProcesso;
import br.com.pacto.dao.intf.usuario.UsuarioDao;

@ClasseProcesso(autor = "<PERSON><PERSON>",
        data = "26/12/2024",
        descricao = "Cria coluna origem no programa de treino",
        motivacao = "Criação de coluna origem no programa de treino - APPS-2305")
public class CriaColunaOrigemProgramaTreino implements MigracaoVersaoInterface {

    @Override
    public void executar(String ctx, UsuarioDao dao) throws Exception {
        try {
            dao.executeNativeSQL(ctx, "ALTER TABLE programatreino ADD COLUMN origem INTEGER;");
        } catch (Exception e) {
        }
    }

}
