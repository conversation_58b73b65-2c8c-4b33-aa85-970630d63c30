package br.com.pacto.base.jpa.migrador;

import br.com.pacto.base.jpa.annotation.ClasseProcesso;
import br.com.pacto.dao.intf.usuario.UsuarioDao;

@ClasseProcesso(
        autor = "Luis Antonio",
        data = "15/05/2025",
        descricao = "Adicionar origem sistema cliente sintetico",
        motivacao = "IA-995"
)
public class Migratition_IA_995 implements MigracaoVersaoInterface{
    @Override
    public void executar(String ctx, UsuarioDao dao) throws Exception {
        try {
            dao.executeNativeSQL(ctx,
                    "alter table clientesintetico add column origemcliente varchar null;"
            );
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
