/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pacto.base.scheduling.personal;

import br.com.pacto.base.scheduling.base.TaskBase;
import br.com.pacto.bean.configuracoes.ConfiguracaoSistema;
import br.com.pacto.bean.configuracoes.ConfiguracoesEnum;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.aulapersonal.AulaPersonalService;
import br.com.pacto.service.intf.configuracoes.ConfiguracaoSistemaService;
import br.com.pacto.util.UtilContext;
import it.sauronsoftware.cron4j.TaskExecutionContext;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
public class TaskCheckOutAutomatico extends TaskBase {

    public TaskCheckOutAutomatico(final String ctx) {
        super(ctx);
    }

    @Override
    public void execute(TaskExecutionContext executor) throws RuntimeException {
        try {
            Uteis.logar(null, "Started -> " + TaskCheckOutAutomatico.class.getSimpleName() + " " + getCtx());
            ConfiguracaoSistemaService configuracaoSistemaService = (ConfiguracaoSistemaService) UtilContext.getBean(ConfiguracaoSistemaService.class);
            ConfiguracaoSistema cfgUsarGestao = configuracaoSistemaService.consultarPorTipo(getCtx(), ConfiguracoesEnum.USAR_GESTAO_PERSONAL);
            if (cfgUsarGestao.getValorAsBoolean()) {
                AulaPersonalService aulaPersonalService = (AulaPersonalService) UtilContext.getBean(AulaPersonalService.class);
                aulaPersonalService.checkOutAutomatico(getCtx());
            }
        } catch (ServiceException ex) {
            Logger.getLogger(TaskCheckOutAutomatico.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            Uteis.logar(null, "Ended -> " + TaskCheckOutAutomatico.class.getSimpleName() + " " + getCtx());
        }
    }
}
