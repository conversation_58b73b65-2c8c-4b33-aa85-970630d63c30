/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pacto.base.scheduling.gympass;

import br.com.pacto.base.scheduling.base.TaskBase;
import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.notificacao.EnfileiradorNotificadorRecursoSistemaSingleton;
import br.com.pacto.notificacao.RecursoSistema;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.agenda.AgendaService;
import br.com.pacto.service.intf.creditopersonal.CreditoPersonalService;
import br.com.pacto.service.intf.empresa.EmpresaService;
import br.com.pacto.util.UtilContext;
import it.sauronsoftware.cron4j.TaskExecutionContext;

import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 */
public class TaskBookingGympass extends TaskBase {

    public TaskBookingGympass(final String ctx) {
        super(ctx);
    }

    @Override
    public void execute(TaskExecutionContext executor) throws RuntimeException {
        try {
            Uteis.logar(null, "Started -> " + TaskBookingGympass.class.getSimpleName() + " " + getCtx());
            AgendaService agendaService = UtilContext.getBean(AgendaService.class);
            agendaService.sincronizarTurmasGympassBooking(getCtx(), 0, null);
        } catch (Exception ex) {
            Logger.getLogger(TaskBookingGympass.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            Uteis.logar(null, "Ended -> " + TaskBookingGympass.class.getSimpleName() + " " + getCtx());
        }
    }
}
