package br.com.pacto.base.scheduling.personal;

import br.com.pacto.base.scheduling.base.TaskBase;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.gestao.DashboardBIService;
import br.com.pacto.util.UtilContext;
import it.sauronsoftware.cron4j.TaskExecutionContext;

import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by johny<PERSON> on 31/08/2016.
 */
public class TaskProcessarBIProfessores extends TaskBase{

    public TaskProcessarBIProfessores(String ctx) {
        super(ctx);
    }

    public DashboardBIService getService() {
        return (DashboardBIService) UtilContext.getBean(DashboardBIService.class);
    }

    @Override
    public void execute(TaskExecutionContext executor) throws RuntimeException {
        try {
            Uteis.logar(null, "Started -> " + TaskProcessarBIProfessores.class.getSimpleName() + " " + getCtx());
            getService().processarBIProfessores(getCtx(), Calendario.hoje());
        }catch (ServiceException ex){
            Logger.getLogger(TaskProcessarBIProfessores.class.getName()).log(Level.SEVERE, null, ex);
        }
        finally {
            Uteis.logar(null, "Ended -> " + TaskProcessarBIProfessores.class.getSimpleName() + " " + getCtx());
        }
    }
}
