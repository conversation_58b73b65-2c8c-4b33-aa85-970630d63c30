package br.com.pacto.base.scheduling.base;

import br.com.pacto.base.scheduling.agendamento.TaskAgendarRenovacaoTreino;
import br.com.pacto.base.scheduling.agendamento.TaskNotificationAgendamento;
import br.com.pacto.base.scheduling.gympass.TaskBookingGympass;
import br.com.pacto.base.scheduling.personal.TaskCheckOutAutomatico;
import br.com.pacto.base.scheduling.personal.TaskExpirarCreditos;
import br.com.pacto.base.scheduling.personal.TaskProcessarBIProfessores;
import br.com.pacto.base.scheduling.treino.TaskCompromissoTreino;
import it.sauronsoftware.cron4j.SchedulingPattern;
import it.sauronsoftware.cron4j.Task;
import it.sauronsoftware.cron4j.TaskCollector;
import it.sauronsoftware.cron4j.TaskTable;

/**
 * Declarar quais Tarefas (tasks) são executadas pela aplicação. Algumas como de
 * lembrar os agendamentos, precisam ser executadas mais frequentemente (10
 * minutos), outras uma vez por dia: <PERSON><PERSON>, Lembrar Faltosos do seu
 * compromisso de X vezes por semana, etc.
 */
public class TaskCollectorBase implements TaskCollector {

    private String key;

    public String getKey() {
        return key;
    }

    public TaskCollectorBase() {
    }

    public TaskCollectorBase(String key) {
        this.key = key;
    }

    @Override
    public TaskTable getTasks() {
        //A cada 10 minutos, verificar tarefas
        SchedulingPattern pattern10minutos = new SchedulingPattern("*/10 * * * *");
        SchedulingPattern pattern30minutos = new SchedulingPattern("*/30 * * * *");
        SchedulingPattern pattern1Dia = new SchedulingPattern("0 12 * * *");
        SchedulingPattern patternDiario5horas = new SchedulingPattern("0 5 * * *");
        SchedulingPattern patternDiario21horas = new SchedulingPattern("0 21 * * *");

        TaskTable ret = new TaskTable();
        //Verificar quais Agendamentos estão próximos de acontecer
        Task taskNotificationAgendamento = new TaskNotificationAgendamento(key);
        ret.add(pattern10minutos, taskNotificationAgendamento);
        //Verificar quais Programas de Treino estão próximos a Renovar
        Task taskAgendarRenovacaoTreino = new TaskAgendarRenovacaoTreino(key);
        ret.add(pattern1Dia, taskAgendarRenovacaoTreino);
        //verificar aulas de personal para fazer checkout automatico
        Task taskCheckoutAutomatico = new TaskCheckOutAutomatico(key);
        ret.add(pattern30minutos, taskCheckoutAutomatico);
        //expirar creditos de personal
        Task taskExpirarCreditos = new TaskExpirarCreditos(key);
        ret.add(pattern1Dia, taskExpirarCreditos);
            //Verificar quais alunos estão atrasados com seu compromisso ou não começaram a malhar
            /*13/06/2014 - Desativada até segunda ordem (Maximiliano)*/
        Task taskLembrarCompromisso = new TaskCompromissoTreino(key);
        ret.add(pattern1Dia, taskLembrarCompromisso);

        Task taskProcessarBIProfessores = new TaskProcessarBIProfessores(key);
        ret.add(patternDiario5horas, taskProcessarBIProfessores);

        Task taskBookingGympass = new TaskBookingGympass(key);
        ret.add(patternDiario21horas, taskBookingGympass);

        return ret;
    }
}
