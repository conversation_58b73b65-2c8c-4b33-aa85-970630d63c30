/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.base.scheduling.agendamento;

import br.com.pacto.base.scheduling.base.TaskBase;
import br.com.pacto.bean.agenda.TipoLembreteEnum;
import br.com.pacto.bean.notificacao.TipoNotificacaoEnum;
import br.com.pacto.bean.programa.ProgramaTreino;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.configuracoes.ConfiguracaoSistemaService;
import br.com.pacto.service.intf.programa.ProgramaTreinoService;
import br.com.pacto.util.UtilContext;
import br.com.pacto.util.ViewUtils;
import it.sauronsoftware.cron4j.TaskExecutionContext;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
public class TaskAgendarRenovacaoTreino extends TaskBase {

    public TaskAgendarRenovacaoTreino(final String ctx) {
        super(ctx);
    }

    @Override
    public void execute(TaskExecutionContext executor) throws RuntimeException {
        try {
            Uteis.logar(null, "Started -> " + TaskAgendarRenovacaoTreino.class.getSimpleName() + " " + getCtx());
            ConfiguracaoSistemaService configuracaoSistemaService = (ConfiguracaoSistemaService) UtilContext.getBean(ConfiguracaoSistemaService.class);
            if(configuracaoSistemaService.notificacaoConfigurada(getCtx(), TipoNotificacaoEnum.SOLICITAR_RENOVACAO)){
                ProgramaTreinoService programaService = (ProgramaTreinoService) UtilContext.getBean(ProgramaTreinoService.class);
                //
                programaService.desabilitarNotificacoesTreinoVencido(getCtx());
                List<ProgramaTreino> lista7Dias = programaService.consultarPrevistosRenovarNosProximosDias(getCtx(),
                        Calendario.hoje(), null, null, TipoLembreteEnum.SETE_DIAS);
                ViewUtils viewUtils = (ViewUtils) UtilContext.getBean(ViewUtils.class);
                for (ProgramaTreino programa : lista7Dias) {
                    programa = programaService.obterPorId(getCtx(), programa.getCodigo());
                    notificar(programa, TipoLembreteEnum.SETE_DIAS, TipoNotificacaoEnum.SOLICITAR_RENOVACAO,
                            viewUtils.getMensagem("programa.agendarRenovacao"));
                }
                //
            }

        } catch (ServiceException ex) {
            Logger.getLogger(TaskAgendarRenovacaoTreino.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            Uteis.logar(null, "Ended -> " + TaskAgendarRenovacaoTreino.class.getSimpleName() + " " + getCtx());
        }
    }
}
