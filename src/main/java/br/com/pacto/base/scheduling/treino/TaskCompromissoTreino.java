/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.base.scheduling.treino;

import br.com.pacto.base.scheduling.base.TaskBase;
import br.com.pacto.bean.agenda.TipoLembreteEnum;
import br.com.pacto.bean.notificacao.TipoNotificacaoEnum;
import br.com.pacto.bean.programa.ProgramaTreino;
import br.com.pacto.bean.programa.ProgramaTreinoAndamento;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.configuracoes.ConfiguracaoSistemaService;
import br.com.pacto.service.intf.programa.ProgramaTreinoService;
import br.com.pacto.util.UtilContext;
import br.com.pacto.util.ViewUtils;
import it.sauronsoftware.cron4j.TaskExecutionContext;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
public class TaskCompromissoTreino extends TaskBase {

    public TaskCompromissoTreino(final String ctx) {
        super(ctx);
    }

    @Override
    public void execute(TaskExecutionContext executor) throws RuntimeException {
        try {
            Uteis.logar(null, "Started -> " + TaskCompromissoTreino.class.getSimpleName() + " " + getCtx());
            ProgramaTreinoService programaService = (ProgramaTreinoService) UtilContext.getBean(ProgramaTreinoService.class);
            //
            ConfiguracaoSistemaService configuracaoSistemaService = (ConfiguracaoSistemaService) UtilContext.getBean(ConfiguracaoSistemaService.class);
            if(configuracaoSistemaService.notificacaoConfigurada(getCtx(), TipoNotificacaoEnum.LEMBRAR_ALUNO_COMPROMISSO)){
                List<ProgramaTreino> lista7Dias = programaService.consultarAtrasados(getCtx(),
                        Calendario.hoje(), null, null, TipoNotificacaoEnum.LEMBRAR_ALUNO_COMPROMISSO, TipoLembreteEnum.SETE_DIAS);
                ViewUtils viewUtils = (ViewUtils) UtilContext.getBean(ViewUtils.class);
                for (ProgramaTreino programa : lista7Dias) {
                    ProgramaTreinoAndamento programaTreinoAndamento = programaService.obterAndamento(getCtx(), programa);
                    if (programaTreinoAndamento != null) {
                        if (Calendario.maiorOuIgual(
                                Uteis.somarDias(Calendario.hoje(), - (TipoLembreteEnum.SETE_DIAS.getnMinutos() / 1440)),
                                programaTreinoAndamento.getUltimoTreino())){
                            final String msg = String.format(viewUtils.getMensagem("programa.lembrarCompromisso"), new Object[]{
                                    programa.getDiasPorSemana(),
                                    Calendario.getData(programaTreinoAndamento.getUltimoTreino(), Calendario.MASC_DATAHORA)});
                            notificar(programa, TipoLembreteEnum.SETE_DIAS,
                                    TipoNotificacaoEnum.LEMBRAR_ALUNO_COMPROMISSO, msg);
                        }
                    } else {
                        final String msg = String.format(viewUtils.getMensagem("programa.comecarCompromisso"), new Object[]{
                                programa.getDiasPorSemana()});
                        notificar(programa, TipoLembreteEnum.SETE_DIAS,
                                TipoNotificacaoEnum.LEMBRAR_ALUNO_COMPROMISSO, msg);
                    }
                }

            }
            //
        } catch (ServiceException ex) {
            Logger.getLogger(TaskCompromissoTreino.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            Uteis.logar(null, "Ended -> " + TaskCompromissoTreino.class.getSimpleName() + " " + getCtx());
        }
    }
}
