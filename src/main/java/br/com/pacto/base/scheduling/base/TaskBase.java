package br.com.pacto.base.scheduling.base;

import br.com.pacto.bean.Agendamento;
import br.com.pacto.bean.agenda.TipoLembreteEnum;
import br.com.pacto.bean.notificacao.TipoNotificacaoEnum;
import br.com.pacto.bean.programa.ProgramaTreino;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.notificacao.NotificacaoService;
import br.com.pacto.util.UtilContext;
import it.sauronsoftware.cron4j.Task;
import it.sauronsoftware.cron4j.TaskExecutionContext;

public class TaskBase extends Task {

    private String ctx;

    public TaskBase(final String ctx) {
        this.ctx = ctx;
    }

    public String getCtx() {
        return ctx;
    }

    public void setCtx(String ctx) {
        this.ctx = ctx;
    }

    @Override
    public boolean canBePaused() {
        return true;
    }

    @Override
    public boolean canBeStopped() {
        return true;
    }

    @Override
    public boolean supportsCompletenessTracking() {
        return true;
    }

    @Override
    public boolean supportsStatusTracking() {
        return true;
    }

    public void execute(TaskExecutionContext executor) throws RuntimeException {
        for (int i = 1; i <= 30; i++) {
            System.out.println("Task says: " + i);
            executor.setStatusMessage("i = " + i);
            executor.setCompleteness(i / 30D);
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                ;
            }
            executor.pauseIfRequested();
            if (executor.isStopped()) {
                break;
            }
        }
    }

    public void notificar(Agendamento agendamento, TipoLembreteEnum tipoLembrete) throws ServiceException {
        NotificacaoService notificacaoService = (NotificacaoService) UtilContext.getBean(NotificacaoService.class);
        notificacaoService.notificarLembreteAgendamento(getCtx(), agendamento, tipoLembrete);
    }

    public void notificar(Agendamento agendamento, final TipoLembreteEnum tipoLembrete,
            final TipoNotificacaoEnum tipoNotificacao,
            final String idMensagem) throws ServiceException {
        NotificacaoService notificacaoService = (NotificacaoService) UtilContext.getBean(NotificacaoService.class);
        notificacaoService.notificarAgendamentoNovoOuAlterado(getCtx(), agendamento, tipoLembrete, tipoNotificacao, idMensagem);
    }

    public void notificar(ProgramaTreino programa, TipoLembreteEnum tipoLembrete,
            TipoNotificacaoEnum tipoNotificacao, final String msg) throws ServiceException {
        NotificacaoService notificacaoService = (NotificacaoService) UtilContext.getBean(NotificacaoService.class);
        notificacaoService.notificarPrograma(getCtx(), programa, Calendario.hoje(), tipoLembrete, tipoNotificacao, msg);
    }
}
