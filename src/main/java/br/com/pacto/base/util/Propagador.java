/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.base.util;

import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.ThreadRequest;
import br.com.pacto.objeto.Uteis;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import javax.servlet.http.HttpServletRequest;

/**
 *
 * <AUTHOR>
 */
public class Propagador {

    public static void propagar(HttpServletRequest request) {
        if (request.getParameterMap().get("propagar") != null
                && ((String[]) request.getParameterMap().get("propagar")).length > 0
                && ((String[]) request.getParameterMap().get("propagar"))[0].equals("false")) {
            return;
        }
        Map<String, Object> params = new HashMap<String, Object>(request.getParameterMap());
        params.put("propagar", "false");
        Enumeration parameterNames = request.getParameterNames();
        while (parameterNames.hasMoreElements()) {
            Object object = parameterNames.nextElement();
            String[] value = (String[]) request.getParameterMap().get(object);
            if (value != null && value.length > 0) {
                params.put(object.toString(), value[0]);
            }
        }
        doExecuteRequestInstances(request.getRequestURL().toString(), params, -1);
    }

    public static void doExecuteRequestInstances(final String urlRequest, 
            Map<String, Object> params, Integer sleep) {
        String[] hostsPortas = Aplicacao.getProp(Aplicacao.instanciasPropagar).split(",");
        try {
            URL u = new URL(urlRequest);
            for (String hostPorta : hostsPortas) {
                if (hostPorta.isEmpty()) {
                    continue;
                }
                try {
//                    if ((u.getPort() != Integer.valueOf(porta)) || (!u.getHost().equals(host))) {
                        StringBuilder s = new StringBuilder("http");
                        s.append("://").append(hostPorta).append(u.getPath());
                        Uteis.logar(null, String.format("PROPAGANDO ====>> %s com parametros: %s", s, params));
                        new ThreadRequest(s.toString(), params, sleep).start();
//                    }
                } catch (Exception ex) {
                    Uteis.logar(ex, Propagador.class);
                }
            }
        } catch (MalformedURLException ex) {
            Logger.getLogger(Propagador.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

}
