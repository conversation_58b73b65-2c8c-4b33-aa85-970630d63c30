package br.com.pacto.base.oamd;

import br.com.pacto.service.discovery.ServiceMapDTO;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.json.JSONObject;

@JsonIgnoreProperties(ignoreUnknown = true)
public class RedeEmpresaVO {

    private Integer id;
    private String nome;
    private String chaverede;
    private Integer creditos;
    private Integer tipocobrancapacto;
    private Integer qtdCreditoUtilizadoPosPagoPacto;
    private Boolean creditoPorRede;
    private Boolean gerarCobrancaAutomaticaPacto;
    private Integer qtdCreditoRenovar;
    private Integer qtdParcelas;
    private Boolean gerarNotaFiscal;
    private String nomeClienteCobranca;
    private String celularClienteCobranca;
    private String emailClienteCobranca;
    private Double valorcreditopacto;
    private String chaveFranqueadora;
    private Integer codigoUnidadeFranqueadora;
    private Boolean gestaoRedes;
    private String urlRoboControle;
    private ServiceMapDTO serviceMap;

    public String toJSON(){
        JSONObject json = new JSONObject();
        json.put("id", this.id);
        json.put("nome", this.nome);
        json.put("chaverede", this.chaverede);
        json.put("chaveFranqueadora", this.chaveFranqueadora);
        json.put("creditos", this.creditos);
        json.put("tipocobrancapacto", this.tipocobrancapacto);
        json.put("qtdCreditoUtilizadoPosPagoPacto", this.qtdCreditoUtilizadoPosPagoPacto);
        json.put("creditoPorRede", this.creditoPorRede);
        json.put("gerarCobrancaAutomaticaPacto", this.gerarCobrancaAutomaticaPacto);
        json.put("qtdCreditoRenovar", this.qtdCreditoRenovar);
        json.put("qtdParcelas", this.qtdParcelas);
        json.put("gerarNotaFiscal", this.gerarNotaFiscal);
        json.put("nomeClienteCobranca", this.nomeClienteCobranca);
        json.put("celularClienteCobranca", this.celularClienteCobranca);
        json.put("emailClienteCobranca", this.emailClienteCobranca);
        json.put("valorcreditopacto", this.valorcreditopacto);
        json.put("gestaoRedes", this.gestaoRedes);
        return json.toString();
    }
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getChaverede() {
        return chaverede;
    }

    public void setChaverede(String chaverede) {
        this.chaverede = chaverede;
    }

    public Integer getCreditos() {
        return creditos;
    }

    public void setCreditos(Integer creditos) {
        this.creditos = creditos;
    }

    public Integer getTipocobrancapacto() {
        return tipocobrancapacto;
    }

    public void setTipocobrancapacto(Integer tipocobrancapacto) {
        this.tipocobrancapacto = tipocobrancapacto;
    }

    public Integer getQtdCreditoUtilizadoPosPagoPacto() {
        return qtdCreditoUtilizadoPosPagoPacto;
    }

    public void setQtdCreditoUtilizadoPosPagoPacto(Integer qtdCreditoUtilizadoPosPagoPacto) {
        this.qtdCreditoUtilizadoPosPagoPacto = qtdCreditoUtilizadoPosPagoPacto;
    }

    public Boolean getCreditoPorRede() {
        return creditoPorRede;
    }

    public void setCreditoPorRede(Boolean creditoPorRede) {
        this.creditoPorRede = creditoPorRede;
    }

    public Boolean getGerarCobrancaAutomaticaPacto() {
        return gerarCobrancaAutomaticaPacto;
    }

    public void setGerarCobrancaAutomaticaPacto(Boolean gerarCobrancaAutomaticaPacto) {
        this.gerarCobrancaAutomaticaPacto = gerarCobrancaAutomaticaPacto;
    }

    public Integer getQtdCreditoRenovar() {
        return qtdCreditoRenovar;
    }

    public void setQtdCreditoRenovar(Integer qtdCreditoRenovar) {
        this.qtdCreditoRenovar = qtdCreditoRenovar;
    }

    public Integer getQtdParcelas() {
        return qtdParcelas;
    }

    public void setQtdParcelas(Integer qtdParcelas) {
        this.qtdParcelas = qtdParcelas;
    }

    public Boolean getGerarNotaFiscal() {
        return gerarNotaFiscal;
    }

    public void setGerarNotaFiscal(Boolean gerarNotaFiscal) {
        this.gerarNotaFiscal = gerarNotaFiscal;
    }

    public String getNomeClienteCobranca() {
        return nomeClienteCobranca;
    }

    public void setNomeClienteCobranca(String nomeClienteCobranca) {
        this.nomeClienteCobranca = nomeClienteCobranca;
    }

    public String getCelularClienteCobranca() {
        return celularClienteCobranca;
    }

    public void setCelularClienteCobranca(String celularClienteCobranca) {
        this.celularClienteCobranca = celularClienteCobranca;
    }

    public String getEmailClienteCobranca() {
        return emailClienteCobranca;
    }

    public void setEmailClienteCobranca(String emailClienteCobranca) {
        this.emailClienteCobranca = emailClienteCobranca;
    }

    public Double getValorcreditopacto() {
        return valorcreditopacto;
    }

    public void setValorcreditopacto(Double valorcreditopacto) {
        this.valorcreditopacto = valorcreditopacto;
    }

    public Integer getCodigoUnidadeFranqueadora() {
        return codigoUnidadeFranqueadora;
    }

    public void setCodigoUnidadeFranqueadora(Integer codigoUnidadeFranqueadora) {
        this.codigoUnidadeFranqueadora = codigoUnidadeFranqueadora;
    }

    public String getChaveFranqueadora() {
        return chaveFranqueadora;
    }

    public void setChaveFranqueadora(String chaveFranqueadora) {
        this.chaveFranqueadora = chaveFranqueadora;
    }

    public Boolean getGestaoRedes() {
        if (gestaoRedes == null){
            gestaoRedes = false;
        }
        return gestaoRedes;
    }

    public void setGestaoRedes(Boolean gestaoRedes) {
        this.gestaoRedes = gestaoRedes;
    }

    public String getUrlRoboControle() {
        return urlRoboControle;
    }

    public void setUrlRoboControle(String urlRoboControle) {
        this.urlRoboControle = urlRoboControle;
    }

    public ServiceMapDTO getServiceMap() {
        if (serviceMap == null) {
            serviceMap = new ServiceMapDTO();
        }
        return serviceMap;
    }

    public void setServiceMap(ServiceMapDTO serviceMap) {
        this.serviceMap = serviceMap;
    }

}
