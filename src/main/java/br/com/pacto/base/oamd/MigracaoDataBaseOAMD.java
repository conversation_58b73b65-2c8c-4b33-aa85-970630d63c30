/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.base.oamd;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;

/**
 *
 * <AUTHOR>
 */
public class MigracaoDataBaseOAMD {

    private static int versaoBD = 7;

    private static void criaTabelaVersaoSeNecessario(final Connection c) throws SQLException {
        String sql = "select exists (select 1 from pg_class where relname = 'versao') as existe";
        ResultSet tabelaResultado;
        try (Statement stm = c.createStatement()) {
            tabelaResultado = stm.executeQuery(sql);
        }
        tabelaResultado.next();
        boolean existe = tabelaResultado.getBoolean("existe");
        if (!existe) {
            executarComandoDireto("CREATE TABLE versao(numero integer NOT NULL,"
                    + "CONSTRAINT pk_numero PRIMARY KEY (numero))", c);
            executarComandoDireto("insert into versao values(0)", c);
        }
    }

    private static int versaoAtual(final Connection c) throws SQLException {
        String sql = "select numero from versao";
        ResultSet tabelaResultado;
        try (Statement stm = c.createStatement()) {
            tabelaResultado = stm.executeQuery(sql);
        }
        tabelaResultado.next();
        return tabelaResultado.getInt("numero");
    }

    private static void atualizarVersao(final int novoValor, final Connection c)
            throws SQLException {
        String sql = "update versao set numero = ?";
        PreparedStatement pstm = c.prepareStatement(sql);
        pstm.setInt(1, novoValor);
        pstm.execute();
    }

    /**
     * - Atualizador Banco de dados OAMD, Self update;
     * <br>- Executa alguma atualiza��o apenas se houver necessidade;
     * <br>- Utilizando reflection as migracoes devem seguir o padrao de nomes
     * de m�todo, assim como a "migracaoVersao1()";
     * <br>- Deve ser atualizado o contador da versao atual "static int
     * versaoBD, declarado logo acima."
     *
     * @param con
     * @throws SQLException
     * @throws ClassNotFoundException
     * @throws IllegalAccessException
     * @throws IllegalArgumentException
     * @throws InvocationTargetException
     * @throws InstantiationException
     * @throws NoSuchMethodException
     */
    public static void atualizarBancoOAMD(Connection con) throws
            ClassNotFoundException,
            IllegalAccessException, IllegalArgumentException,
            InvocationTargetException, InstantiationException, NoSuchMethodException {
        try {

            criaTabelaVersaoSeNecessario(con);

            int versaoBDAtual = versaoAtual(con);

            for (versaoBDAtual++; versaoBDAtual <= versaoBD; versaoBDAtual++) {
                System.out.println("Atualizar OAMD para versao -> " + versaoBDAtual);

                Class partypes[] = new Class[1];
                partypes[0] = Connection.class;

                Connection[] parvalues = new Connection[1];
                parvalues[0] = con;

                Class cls = Class.forName(MigracaoDataBaseOAMD.class.getCanonicalName());
                Method meth = cls.getMethod("migracaoVersao" + versaoBDAtual, partypes);
                meth.invoke(cls, parvalues);
                atualizarVersao(versaoBDAtual, con);
                System.out.println("     Sucesso! " + versaoBDAtual);
            }
        } catch (SQLException ex) {
        }

    }

    public static boolean executarComandoDireto(final String sql, final Connection con)
            throws SQLException {
        try {
            try (Statement stm = con.createStatement()) {
                stm.execute(sql);
            }
            System.out.println("executarComandoDireto ->  " + sql + " -> OK");
            return true;
        } catch (SQLException ex) {
            System.out.println("executarComandoDireto ->  " + sql + " -> ERRO "
                    + ex.getMessage());
            return false;
        }

    }

    /**
     * Cria campo no OAMD para armazenar quais módulos estão habilitados para
     * cada empresa (chave) cliente do ZillyonWeb no m�ximo 03 (três) caracteres
     * para cada sigla (módulo) para até 12 módulos Valor | Módulo CRM CRM Web
     * CE Central de Eventos FIN Financeiro
     *
     * Depois dessa migração os módulos devem ser mantidos manualmente pelos
     * dados da tabela "Empresa".
     *
     * @param c
     */
    public static void migracaoVersao1(final Connection c) throws Exception {
        executarComandoDireto("ALTER TABLE empresa "
                + "ADD COLUMN modulos character varying(36) DEFAULT 'CRM'", c);
        //já preencher com os módulos padrão para cada cliente
        //1. Todos têm o CRM por isso no ADD COLUMN já inclui esse valor "CRM"
        //2. Hoje 15/06/2011 - Apenas Ipanema possui Central de Eventos, portanto atualizar para "CRM,CE"
        executarComandoDireto("UPDATE empresa set modulos = 'CRM,CE' "
                + "where chave = 'ce4c5aaa540c74b7167fc23283f7c42b'", c);
    }

    public static void migracaoVersao2(final Connection c) throws Exception {
        executarComandoDireto("ALTER TABLE empresa ADD COLUMN codigo serial NOT NULL", c);
    }

    public static void migracaoVersao3(final Connection c) throws Exception {
        executarComandoDireto("ALTER TABLE empresa ADD COLUMN permitemultiempresas boolean DEFAULT true", c);
    }

    public static void migracaoVersao4(final Connection c) throws Exception {
        executarComandoDireto("ALTER TABLE empresa ADD COLUMN urlZillyonWeb character varying DEFAULT 'https://app.pactosolucoes.com.br'", c);
    }
    
    public static void migracaoVersao5(final Connection c) throws Exception {
        executarComandoDireto("ALTER TABLE empresa ADD COLUMN urlZillyonWebInteg character varying DEFAULT 'https://app.pactosolucoes.com.br'", c);
        executarComandoDireto("UPDATE empresa set urlZillyonWebInteg = urlZillyonWeb", c);
    }

    public static void migracaoVersao6(final Connection c) throws Exception {
        executarComandoDireto("ALTER TABLE empresa ADD COLUMN urlOAMD character varying DEFAULT 'http://app.pactosolucoes.com.br/oamd'", c);
    }

    public static void migracaoVersao7(final Connection c) throws Exception {
        executarComandoDireto("ALTER TABLE empresa alter COLUMN modulos TYPE character varying;", c);
    }
}
