package br.com.pacto.base.oamd;

import br.com.pacto.base.util.ExecuteRequestHttpService;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.JSONMapper;
import br.com.pacto.objeto.Uteis;
import org.json.JSONObject;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
public class OAMDService {

    private final Map<String, RedeEmpresaVO> redePorChave = new HashMap<>();

    public RedeEmpresaVO obterRedePorChave(String chave) {
        try {
            if (redePorChave.containsKey(chave)) {
                return redePorChave.get(chave);
            } else {
                RedeEmpresaVO redeEmpresaVO = consultarRedeEmpresa(chave);
                redePorChave.put(chave, redeEmpresaVO);
                return redeEmpresaVO;
            }
        }catch (Exception e){
            Uteis.logar(e, OAMDService.class);
            redePorChave.put(chave, new RedeEmpresaVO());
            return new RedeEmpresaVO();
        }
    }


    public RedeEmpresaVO consultarRedeEmpresa(final String key) {
        try {
            final String url = Aplicacao.getProp(Aplicacao.urlOAMD) + "/prest/empresaFinanceiro/consultarRede?key=" + key;
//            final String url = "http://localhost:8073/NewOAMD" + "/prest/empresaFinanceiro/consultarRede?key=" + key;
            final String response = ExecuteRequestHttpService.executeRequestGet(url, new HashMap<>());
            JSONObject resposta = new JSONObject(response).optJSONObject("return");
            if (resposta == null) {
                return new RedeEmpresaVO();
            }
            return JSONMapper.getObject(resposta, RedeEmpresaVO.class);
        } catch (Exception ex) {
            Uteis.logar(ex, OAMDService.class);
        }
        return new RedeEmpresaVO();
    }

    public void limparMapaDeRedes() {
        redePorChave.clear();
    }


}
