package br.com.pacto.base.oamd;

import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.impl.JSFUtilities;
import javax.naming.Context;
import javax.naming.InitialContext;
import javax.servlet.http.HttpSession;
import javax.sql.DataSource;
import java.sql.*;
import java.util.Hashtable;
import java.util.Map;

/**
 * Esta classe encapsula a lógica de obtenção da conexão.
 *
 * <AUTHOR>
 */
public class Conexao {

    private String driverJDBC = "org.postgresql.Driver";
    private String ipServidor = "";
    private String nomeJNDI = "jdbc/BDZillyonWebCE";
    private String conexaoPadraoJDBC = "jdbc:postgresql:";
    private String nomeBD = "";
    private String hostBD = "localhost";
    private String usernameBD = "postgres";
    private String senhaBD = "pactodb";
    private String porta = "5432";
    private Boolean JNDI = false;
    private String url = "";
    private String urlOAMD = "";
    private boolean roboApenasComoServico = false;
    private boolean isNFe = false;
    private final String driverJDBC_SQLSERVER = "net.sourceforge.jtds.jdbc.Driver";
    private final String conexaoPadraoJDBC_SQLSERVER = "jdbc:jtds:sqlserver:";
    /**
     * <AUTHOR>
     * @See InicioControle.java
     * <p/>
     * Conexao estatica para ser utilizada por objetos J2SE como threads e ou,
     * objetos JEE como webservices; Será mantida uma conexão, para utilização
     * eventual e não concorrente, para evitar dead locks em transações longas
     */
    private static Connection conexaoForJ2SE;
    private static Map<String, Connection> mapaConexoesJ2SE = new Hashtable<String, Connection>();

    public Conexao() {
        try {
            // usa-se os parametros vindos do XML, caso na classe DAO
            // esteja inicializado o atributo usarCfgXML = true (apenas em
            // DESENV)
            inicializarCfgConexaoBD();
        } catch (Exception e) {
            ipServidor = ""; // localhost
        }
    }

    public Conexao(String url, String user, String pwd) {
        this.url = url;
        this.usernameBD = user;
        this.senhaBD = pwd;
    }

    /**
     * Esse m�todo � utilizado para retornar uma constante de desenvolvimento
     * setada no arquivo cfgBD.xml
     *
     * @return Boolean
     * @throws Exception
     */
    public static Boolean getDesv() throws Exception {
        String desvSession = (String) JSFUtilities.getFromSession("desv");
        String desvRequest = (String) JSFUtilities.getFromRequest("desv");
        if (desvSession != null && desvSession.equalsIgnoreCase("true")) {
            return true;
        } else if (desvRequest != null && desvRequest.equalsIgnoreCase("true")) {
            return true;
        } else {
            return false;
        }
    }

    private void inicializarCfgConexaoBD() throws Exception {
        String xml = Uteis.getXMLDocumentCFG(Uteis.nomeArqCFG);
        ipServidor = Uteis.getValorTAG(xml, "servidor");
        porta = Uteis.getValorTAG(xml, "porta");
        if (!porta.isEmpty()) {
            ipServidor = "//" + ipServidor + ":" + porta + "/";
        } else {
            ipServidor = "//" + ipServidor + "/";
        }
        nomeBD = Uteis.getValorTAG(xml, "nomeBD");
        usernameBD = Uteis.getValorTAG(xml, "username");
        senhaBD = Uteis.getValorTAG(xml, "senha");
        nomeJNDI = Uteis.getValorTAG(xml, "JNDI");
        urlOAMD = Uteis.getValorTAG(xml, "url-oamd");
        roboApenasComoServico = Uteis.getValorTAGBoolean(xml, "roboApenasComoServico");
        isNFe = Uteis.getValorTAGBoolean(xml, "NFE");
        JSFUtilities.storeOnSession("isNFE", isNFe);
    }

    private Connection getConexaoJDBC() throws Exception {
        String driver = driverJDBC;
        String conexao = conexaoPadraoJDBC;

        if (isNFe) {
            driver = driverJDBC_SQLSERVER;
            conexao = conexaoPadraoJDBC_SQLSERVER;
        }

        Class.forName(driver).newInstance();
        Connection conn = null;
        if (url.isEmpty()) {
            conn = DriverManager.getConnection(conexao + ipServidor
                    + nomeBD, usernameBD, senhaBD);
        } else {
            conn = DriverManager.getConnection(url, usernameBD, senhaBD);
        }
        return conn;
    }

    public static Connection obterConexaoBancoEmpresas() throws Exception {
        return obterConexaoBancoEmpresas(null);
    }

    public static Connection obterConexaoBancoEmpresas(String urlOamd) throws Exception {
        Conexao con = new Conexao();
        String url = urlOamd == null ? con.urlOAMD : urlOamd;

        String driver = "org.postgresql.Driver";

        Class.forName(driver);
        Uteis.logar(null, "url->" + url);
        Connection c = DriverManager.getConnection(url + "?ApplicationName=" + UteisValidacao.caseEmptyString(Aplicacao.getInstanceName(), "TR"),
                con.usernameBD,
                con.senhaBD);
        MigracaoDataBaseOAMD.atualizarBancoOAMD(c);
        return c;
    }

    private Connection getConexaoJNDI() throws Exception {
        Context ctx = new InitialContext();
        if (ctx == null) {
            throw new Exception("Servidor não disponível - Contexto JNDI não localizado.");
        }
        DataSource ds = (DataSource) ctx.lookup(nomeJNDI);
        if (ds == null) {
            throw new Exception("JNDI Name inexistente. Não foi possível conectar o BD.");
        }
        return ds.getConnection();
    }

    public Connection getConexao() throws Exception {
        if (!JNDI) {
            return getConexaoJDBC();
        } else {
            return getConexaoJNDI();
        }
    }

    public static int obterUltimoCodigoGeradoTabela(Connection con, String nomeTabela) throws Exception {
        // SELECT last_value FROM acessocliente_codigo_seq
        String csCodigoGerado = "SELECT last_value FROM " + nomeTabela + "_codigo_seq";
        ResultSet resultado;
        try (Statement stmt = con.createStatement(ResultSet.TYPE_SCROLL_INSENSITIVE,
                ResultSet.CONCUR_READ_ONLY)) {
            resultado = stmt.executeQuery(csCodigoGerado);
        }
        resultado.first();
        return (resultado.getInt(1));
    }

    public String getIpServidor() {
        return ipServidor;
    }

    public void setIpServidor(String ipServidor) {
        this.ipServidor = ipServidor;
    }

    public String getPorta() {
        return porta;
    }

    public void setPorta(String porta) {
        this.porta = porta;
    }

    public static Connection getConexaoForJ2SE() {
        return conexaoForJ2SE;
    }

    /**
     * Guarda uma conex�o em contexto est�tico para utiliza��o de outros objetos
     * n�o conhecedores do FacesContext
     *
     * @param conexao
     * @throws SQLException
     */
    public static void guardarConexaoForJ2SE(Connection conexao)
            throws SQLException {

        if (conexaoForJ2SE != null) {
            if (!conexaoForJ2SE.isClosed()) {
                conexaoForJ2SE.close();
                conexaoForJ2SE = null;
            }
        }
        conexaoForJ2SE = conexao;
    }

    public static void guardarConexaoForJ2SE(Connection conexao, String chave)
            throws SQLException {

        if (mapaConexoesJ2SE != null) {
            Connection con = mapaConexoesJ2SE.get(chave);
            if (con == null || con.isClosed()) {
                mapaConexoesJ2SE.put(chave, con);
            }
        }

    }

    public static Connection obterConexaoForJ2SE(String chave) throws Exception {
        Connection con = mapaConexoesJ2SE.get(chave);
        if (con == null) {
            throw new Exception("N�o foi poss�vel obter "
                    + "conex�o atrav�s para chave \"" + chave + "\" no contexto J2SE. "
                    + "Esta n�o foi criada anteriormente.");
        }
        return con;
    }

    public String getSenhaDefault() {
        return this.senhaBD;
    }

    public String getUrlOAMD() {
        return urlOAMD;
    }

    /**
     * Este deve ser o �nico ponto onde se obtem a conex�o da sess�o.
     *
     * @return
     */
    public static Connection getFromSession() {
        ConnectionSerializable conexao = (ConnectionSerializable) JSFUtilities.getFromSession("con");
        if (conexao != null) {
            return conexao.getCon();
        } else {
            return null;
        }
    }

    public static boolean isRoboApenasComoServico() {
        ConnectionSerializable conexao = (ConnectionSerializable) JSFUtilities.getFromSession("con");
        if (conexao != null) {
            return conexao.isRoboApenasComoServico();
        } else {
            return false;
        }
    }

    public static void storeOnSession(Connection con) {
        Conexao conexao = new Conexao();
        String pwd = (String) JSFUtilities.getFromSession("pwd");
        pwd = pwd == null ? conexao.getSenhaDefault() : pwd;
        ConnectionSerializable conSerial = new ConnectionSerializable(con, pwd,
                conexao.roboApenasComoServico);
        JSFUtilities.storeOnSession("con", conSerial);
    }

    public static Connection getFromSession(HttpSession session) {
        ConnectionSerializable conexao = (ConnectionSerializable) session.getAttribute("con");
        if (conexao != null && conexao.getCon() != null) {
            return conexao.getCon();
        } else {
            return null;
        }
    }

    public Connection obterNovaConexaoBaseadaOutra(Connection con) throws SQLException {
        String passwordBD = JSFUtilities.getFromSession("pwd") == null ? this.getSenhaDefault()
                : (String) JSFUtilities.getFromSession("pwd");
        String u = con.getMetaData().getURL();
        String userBD = con.getMetaData().getUserName();
        return DriverManager.getConnection(u, userBD, passwordBD);
    }
}
