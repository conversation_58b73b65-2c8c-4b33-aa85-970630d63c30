package br.com.pacto.base.oamd;

import br.com.pacto.base.impl.EntityManagerFactoryService;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.ColumnType;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.util.UteisValidacao;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

import java.util.List;

public final class OAMD {

    private static final String HEALTH_SQL = "SELECT 'ok' as health";

    public static String obterModulos(final String chave, final Connection c) throws SQLException {
        StringBuilder sb = new StringBuilder("select modulos from empresa ");
        sb.append("where chave = '").append(chave).append("'");

        PreparedStatement stm = c.prepareStatement(sb.toString());
        try (ResultSet data = stm.executeQuery()) {

            while (data.next()) {
                if (data.getString("modulos") != null) {
                    return data.getString("modulos").toUpperCase();
                }
            }
        }
        return "";

    }

    public static Map<String, Map<String, Object>> buscarListaEmpresasMapeado() throws Exception {
        return buscarListaEmpresasMapeado(null);
    }

    public static Map<String, Map<String, Object>> buscarListaEmpresasMapeado(String urlOamd) throws Exception {

        StringBuilder sb = new StringBuilder();
        sb.append("SELECT * ");
        sb.append("FROM empresa ").append(" where \"usarBDLocal\" = true and ativa is true");

        Map<String, Map<String, Object>> retorno;

        try (Connection connection = Conexao.obterConexaoBancoEmpresas(urlOamd);
             PreparedStatement stm = connection.prepareStatement(sb.toString());
             ResultSet data = stm.executeQuery()) {

            retorno = new HashMap<>();
            while (data.next()) {
                Map h = new HashMap<>();
                h.put("codigo", data.getInt("codigo"));
                h.put("chave", data.getString("chave"));
                h.put("hostBD", data.getString("hostBD"));
                h.put("porta", data.getInt("porta"));
                h.put("nomeBD", data.getString("nomeBD"));
                h.put("userBD", data.getString("userBD"));
                h.put("ativa", data.getBoolean("ativa"));
                h.put("usarBDLocal", data.getBoolean("usarBDLocal"));
                h.put("passwordBD", data.getString("passwordBD"));
                h.put("robocontrole", data.getString("robocontrole"));
                h.put("modulos", data.getString("modulos"));
                h.put("permitemultiempresas", data.getBoolean("permitemultiempresas"));
                h.put("urlzillyonweb", data.getString("urlzillyonweb"));
                h.put("urlzillyonwebinteg", data.getString("urlzillyonwebinteg"));
                try {
                    if (data.findColumn("urlgestaonotas") > 0) {
                        h.put("urlgestaonotas", data.getString("urlgestaonotas"));
                    }
                } catch (Exception ignored) {

                }
                try {
                    if (data.findColumn("urlgestaonotasrest") > 0) {
                        h.put("urlgestaonotasrest", data.getString("urlgestaonotasrest"));
                    }
                } catch (Exception ignored) {

                }
                //            for (ColumnType ct : colunas) {
                //                if (ct.getName().equals("urloamd")){
                //                    Aplicacao.setProp(Aplicacao.urlOAMD, String.valueOf(data.getObject(ct.getName())));
                //                }
                //            }

                h.put(Aplicacao.hbm2ddlAuto, Aplicacao.getProp(Aplicacao.hbm2ddlAuto));
                retorno.put(data.getString("chave"), h);
            }
        }

        return retorno;
    }

    public static String health() throws Exception {
        try (Connection connection = Conexao.obterConexaoBancoEmpresas(null);
             PreparedStatement stm = connection.prepareStatement(HEALTH_SQL);
             ResultSet result = stm.executeQuery()) {
            result.next();
            return result.getString("health");
        }
    }
}
