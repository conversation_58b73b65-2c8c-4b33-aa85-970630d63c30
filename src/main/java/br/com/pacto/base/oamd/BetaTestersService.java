package br.com.pacto.base.oamd;

import br.com.pacto.base.util.ExecuteRequestHttpService;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.util.UteisValidacao;
import org.json.JSONArray;
import org.json.JSONObject;
import org.apache.commons.io.Charsets;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

public class BetaTestersService {

    public static List<String> testers = new ArrayList<>();
    public static Boolean applyAll = Boolean.FALSE;


    public JSONObject caminhos() throws Exception {
        JSONObject result = getJSON(Aplicacao.getProp(Aplicacao.discoveryUrls) + "/paths");
        return result.getJSONObject("content");
    }

    public void init(){
        try {
            testers = new ArrayList<>();
            JSONArray resultOAMD = consultaOAMD();
            for(int i = 0; i < resultOAMD.length(); i++){
                testers.add(resultOAMD.getString(i));
            }
        }catch (Exception e){
            System.out.println("problema ao obter os testers");
            e.printStackTrace();
        }
    }

    public static boolean isBetaTester(String key){
        return applyAll ? true : (key != null && testers.contains(key));
    }

    public JSONArray consultaOAMD() throws Exception {
        JSONObject caminhos = caminhos();
        String oamdApp = caminhos.optString("oamd");
        if(UteisValidacao.emptyString(oamdApp)){
            throw new Exception(" o discovery não retornou a url do oamd, verifique se está preenchida no banco corretamente");
        }
        String url = oamdApp + "/prest/empresa/betatesters";
        JSONObject result = getJSON(url);
        return result.getJSONArray("return");
    }

    public JSONObject getJSON(String url) throws Exception {
        CloseableHttpClient client = ExecuteRequestHttpService.createConnectorCloseable();
        HttpGet httpGet = new HttpGet(url);
        httpGet.setHeader("Content-type", "application/json");
        CloseableHttpResponse response = client.execute(httpGet);
        String responseJSON = EntityUtils.toString(response.getEntity());
        return new JSONObject(responseJSON);
    }
}
