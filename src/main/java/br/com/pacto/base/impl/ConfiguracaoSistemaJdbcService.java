package br.com.pacto.base.impl;

import br.com.pacto.bean.configuracoes.ConfiguracoesEnum;
import br.com.pacto.objeto.Uteis;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Map;

public class ConfiguracaoSistemaJdbcService implements AutoCloseable {


    private final Connection trConnection;

    public ConfiguracaoSistemaJdbcService(Map<String, Object> companyProps) throws Exception {
        trConnection = createConnection(companyProps);
    }

    private Connection createConnection(Map<String, Object> empresa) throws Exception {
        String urlBD = "jdbc:postgresql://" + empresa.get("hostBD") + ":" + empresa.get("porta") + "/" + empresa.get("nomeBD") + "?ApplicationName=tr-entitymanager-factory_" + empresa.get("chave");
        return DriverManager.getConnection(urlBD, empresa.get("userBD").toString(), empresa.get("passwordBD").toString());
    }

    @Override
    public void close() throws Exception {
        if (trConnection != null && !trConnection.isClosed()) {
            trConnection.close();
        }
    }

    public String findByConfigEnum(ConfiguracoesEnum config) {
        String sql = "SELECT valor FROM configuracaosistema WHERE configuracao = ?";

        try (PreparedStatement ps = trConnection.prepareStatement(sql)) {
            ps.setString(1, config.name());
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    return rs.getString("valor");
                }
            }
        } catch (Exception ex) {
            return null;
        }
        return null;
    }

    public void updateConfigValue(ConfiguracoesEnum config, String value) throws SQLException {
        String sql = "UPDATE configuracaosistema SET valor = ? WHERE configuracao = ?";
        try (PreparedStatement ps = trConnection.prepareStatement(sql)) {
            ps.setString(1, value);
            ps.setString(2, config.name());
            ps.execute();
        } catch (Exception ex) {
            Uteis.logarDebug("ERRO AO ATUALIZAR CONFIGURAÇÃO: " + ex.getMessage());
            return;
        }
    }

    public void insertConfigValue(ConfiguracoesEnum config, String value) {
        String sql = "INSERT INTO configuracaosistema (configuracao, valor) VALUES (? , ?);";
        try (PreparedStatement ps = trConnection.prepareStatement(sql)) {
            ps.setString(1, config.name());
            ps.setString(2, value);
            ps.execute();
        } catch (Exception ex) {
            Uteis.logarDebug("ERRO AO INSERIR CONFIGURAÇÃO: " + ex.getMessage());
            return;
        }
    }

}
