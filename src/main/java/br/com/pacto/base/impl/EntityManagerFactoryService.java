/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.base.impl;

import br.com.pacto.base.oamd.ManyDataBasesException;
import br.com.pacto.base.oamd.OAMD;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.util.UteisValidacao;
import org.springframework.orm.jpa.JpaTransactionManager;

import javax.persistence.EntityManagerFactory;
import javax.persistence.Persistence;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

/**
 * <AUTHOR>
 */
public class EntityManagerFactoryService {

    private static final String DDL_UPDATE = "update";
    private static final String DDL_NONE = "none";
    private static final String TRUE = "none";
    private static final String FALSE = "none";

    private static final Map<String, EntityManagerFactory> factoriesEM = new HashMap<>();
    private static final Map<String, JpaTransactionManager> factoriesTM = new HashMap<>();
    private static final Map<String, String> mapaTimeZone = new HashMap<>();
    private static Map<String, Map<String, Object>> empresas = new HashMap<>();

    static {
        init();
    }

    /**
     * This method returns an entity manager factory for a target persistence
     * unit
     *
     * @param persistenceUnitName context (key)
     * @return an entity manager factory for a target persistence unit
     */

    public static EntityManagerFactory getEntityManagerFactory(final String persistenceUnitName) throws Exception {
        return getEntityManagerFactory(persistenceUnitName, false);
    }

    public static EntityManagerFactory getEntityManagerFactory(final String persistenceUnitName, boolean forceDDL) throws Exception {
        if (factoriesEM.containsKey(persistenceUnitName)) {
            EntityManagerFactory emf = factoriesEM.get(persistenceUnitName);
            Map<String, Object> factoryProperties = emf.getProperties();

            if (factoryProperties.containsKey(Aplicacao.hbm2ddlAuto)) {
                String ddlProperty = (String) factoryProperties.get(Aplicacao.hbm2ddlAuto);
                if (DDL_UPDATE.equalsIgnoreCase(ddlProperty)) {
                    emf.close();
                    factoriesEM.remove(persistenceUnitName);
                } else {
                    return emf;
                }
            } else {
                return emf;
            }
        }

        if (factoriesEM.containsKey(persistenceUnitName)) {
            return factoriesEM.get(persistenceUnitName);
        }

        Uteis.logarDebug(String.format("[FACTORY] Creating Entity Manager Factory for key %s", persistenceUnitName));

        Properties factoryProperties = getProps(persistenceUnitName, forceDDL);
        EntityManagerFactory emf = Persistence.createEntityManagerFactory("NewMuscPU", factoryProperties);
        JpaTransactionManager jpaTM = new JpaTransactionManager(emf);

        factoriesEM.put(persistenceUnitName, emf);
        factoriesTM.put(persistenceUnitName, jpaTM);
        return emf;
    }

    public static Map<String, Object> getPropsKey(final String key) throws ManyDataBasesException {
        Map<String, Object> params = empresas.get(key);
        if (params == null) {
            throw new ManyDataBasesException("Parâmetros não definidos para chave: " + key);
        }
        return empresas.get(key);
    }


    public static Properties resolveAutoDDL(Properties p, final boolean forceDDL) {
        p.put(Aplicacao.hbm2ddlAuto, DDL_NONE);
        p.put(Aplicacao.hbmValidatorApplyToDDL, FALSE);
        if (forceDDL) {
            p.put(Aplicacao.hbm2ddlAuto, DDL_UPDATE);
            p.put(Aplicacao.hbmValidatorApplyToDDL, TRUE);
        }
        return p;
    }

    private static Properties getProps(final String key, final boolean forceDDL) throws ManyDataBasesException {
        Properties p = new Properties();
        Map<String, Object> params = empresas.get(key);
        if (params == null) {
            throw new ManyDataBasesException("Parâmetros não definidos para chave: " + key);
        }
        if (key.equals("in-memory")) {
            p.put("hibernate.dialect", "org.hibernate.dialect.HSQLDialect");
            p.put("hibernate.connection.driver_class", "org.hsqldb.jdbcDriver");
            p.put("hibernate.connection.url", "********************************");
            p.put("hibernate.connection.username", "sa");
            p.put("hibernate.connection.password", "");
        } else {
            p.put("hibernate.connection.url", String.format("*********************************************",
                    params.get("hostBD"),
                    params.get("porta"),
                    params.get("nomeBD"),
                    UteisValidacao.caseEmptyString(Aplicacao.getInstanceName(), "TR")
            ));
            p.put("hibernate.connection.username", params.get("userBD"));
            p.put("hibernate.connection.password", params.get("passwordBD"));
        }

        p = resolveAutoDDL(p, forceDDL);

        return p;
    }

    /**
     * Close entity manager factories. Use this method when application life
     * cycle ends
     */
    public static void closeFactories() {
        for (Map.Entry<String, EntityManagerFactory> entry : factoriesEM.entrySet()) {
            entry.getValue().close();
        }
        factoriesEM.clear();
    }

    public static void close(final String key) {
        EntityManagerFactory emf = factoriesEM.get(key);
        if (emf != null) {
            emf.close();
            factoriesEM.remove(key);
            emf = null;
        }
        JpaTransactionManager jtm = factoriesTM.get(key);
        if (jtm != null) {
            factoriesTM.remove(key);
            jtm = null;
        }
    }

    public static Object getPropertyOAMD(final String key, final String attribute) {
        Map<String, Object> mapa = empresas.get(key);
        if (mapa != null && !mapa.isEmpty()) {
            return mapa.get(attribute);
        } else {
            return null;
        }
    }

    public static String getTimeZoneEmpresa(final String key, final Integer empresa) {
        return mapaTimeZone.get(key + "-" + empresa);
    }

    public static String setTimeZoneEmpresa(final String key, final Integer empresa, final String timeZone) {
        return mapaTimeZone.put(key + "-" + empresa, timeZone);
    }

    public static void resetMapaTimezone() {
        mapaTimeZone.clear();
    }

    public static void putPropertyOAMD(final String key, final String attribute, final String value) {
        Map<String, Object> mapa = empresas.get(key);
        if (mapa != null && !mapa.isEmpty()) {
            mapa.put(attribute, value);
        }
    }

    public static void init() {
        try {
            empresas = OAMD.buscarListaEmpresasMapeado();
        } catch (Exception e) {
            Uteis.logar(e, EntityManagerFactoryService.class);
        }
    }

    public static Map<String, Map<String, Object>> getEmpresas() {
        return empresas;
    }
}
