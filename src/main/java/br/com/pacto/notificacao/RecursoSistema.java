package br.com.pacto.notificacao;

import org.apache.commons.lang.StringUtils;

/**
 * <AUTHOR>
 * @since 12/02/19
 */
public enum RecursoSistema {

    TESTE("TESTE"),
    ENTROU_TELA_ALUNO("ENTROU_TELA_ALUNO"),
    MONTAR_PROGRAMA_TREINO_ANTIGO("MONTAR_PROGRAMA_TREINO_ANTIGO"),
    ENTROU_BI_GESTAO_TREINO_ANTIGO("ENTROU_BI_GESTAO_TREINO_ANTIGO"),
    ENTROU_BI_GESTAO_AULACHEIA_ANTIGO("ENTROU_BI_GESTAO_AULACHEIA_ANTIGO"),
    ENTROU_ACOMPANHAR_ALUNO_TREINO_ANTIGO("ENTROU_ACOMPANHAR_ALUNO_TREINO_ANTIGO"),
    AGENDOU_BIKE_SPIVI_TREINO_ANTIGO("AGENDOU_BIKE_SPIVI_TREINO_ANTIGO"),
    ENTROU_BI_AVALIACAO_FISICA_TREINO_ANTIGO("ENTROU_BI_AVALIACAO_FISICA_TREINO_ANTIGO"),
    CRIOU_AVALIACAO_FISICA_TREINO_ANTIGO("CRIOU_AVALIACAO_FISICA_TREINO_ANTIGO"),
    IMPRIMIU_AVALIACAO_FISICA_TREINO_ANTIGO("IMPRIMIU_AVALIACAO_FISICA_TREINO_ANTIGO"),
    ENTROU_GESTAO_PERSONAL_TREINO_ANTIGO("ENTROU_GESTAO_PERSONAL_TREINO_ANTIGO"),
    ENTROU_NOTIFICACOES_TREINO_ANTIGO("ENTROU_NOTIFICACOES_TREINO_ANTIGO"),
    INCLUIU_ALUNO_AGENDA_AULAS_TREINO_ANTIGO("INCLUIU_ALUNO_AGENDA_AULAS_TREINO_ANTIGO"),
    GRAVOU_AULA_COLETIVA_TREINO_ANTIGO("GRAVOU_AULA_COLETIVA_TREINO_ANTIGO"),
    SUBSTITUIU_PROFESSOR_AULA_COLETIVA_TREINO_ANTIGO("SUBSTITUIU_PROFESSOR_AULA_COLETIVA_TREINO_ANTIGO"),
    GRAVOU_DISPONIBILIDADE_TREINO_ANTIGO("GRAVOU_DISPONIBILIDADE_TREINO_ANTIGO"),
    BOOKING_GYMPASS("BOOKING_GYMPASS"),
    ENTROU_AGENDA_SERVICOS_TREINO_ANTIGO("ENTROU_AGENDA_SERVICOS_TREINO_ANTIGO"),
    ENTROU_AGENDA_AULAS_AULACHEIA_ANTIGO("ENTROU_AGENDA_AULAS_AULACHEIA_ANTIGO"),
    CLICOU_AVISO_AGENDA("CLICOU_AVISO_AGENDA"),
    GRAVOU_AVALIACAO_TREINO_ANTIGO("GRAVOU_AVALIACAO_TREINO_ANTIGO"),
    GRAVOU_AVALIACAO_TREINO_NOVO("GRAVOU_AVALIACAO_TREINO_NOVO"),
    SUCESSO_SINC_GYMPASS("SUCESSO_SINC_GYMPASS"),
    ERRO_SINC_GYMPASS("ERRO_SINC_GYMPASS"),
    NAV_AvaliacaoPollock3("NAV_AvaliacaoPollock3"),
    NAV_AvaliacaoPollock7("NAV_AvaliacaoPollock7"),
    NAV_AvaliacaoGuedes("NAV_AvaliacaoGuedes"),
    NAV_AvaliacaoFaukner4("NAV_AvaliacaoFaukner4"),
    NAV_AvaliacaoBioimpedancia("NAV_AvaliacaoBioimpedancia"),
    NAV_AvaliacaoSlaughter("NAV_AvaliacaoSlaughter"),
    NAV_AvaliacaoWeltman("NAV_AvaliacaoWeltman"),
    NAV_AvaliacaoPollockAdolescente("NAV_AvaliacaoPollockAdolescente"),
    NAV_AvaliacaoYuhasz6("NAV_AvaliacaoYuhasz6"),
    NAV_AvaliacaoTGLohman2("NAV_AvaliacaoTGLohman2"),
    SINCRONIZADOR_GYMPASS("SINCRONIZADOR_GYMPASS"),
    ;

    private final String descricao;

    RecursoSistema(String descricao) {
        this.descricao = descricao;
    }

    public String getDescricao() {
        return descricao;
    }

    public static RecursoSistema fromDescricao(final String descricao) {
        if (StringUtils.isBlank(descricao)) {
            return null;
        }

        for (RecursoSistema recursoSistema : RecursoSistema.values()) {
            if (recursoSistema.getDescricao().equalsIgnoreCase(descricao)) {
                return recursoSistema;
            }
        }

        return null;
    }

}
