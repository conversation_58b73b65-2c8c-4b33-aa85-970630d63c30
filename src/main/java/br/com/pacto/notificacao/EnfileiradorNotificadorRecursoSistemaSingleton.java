package br.com.pacto.notificacao;

import br.com.pacto.service.notificador.NotificadorRecursoEmpresaServiceControle;
import br.com.pacto.util.bean.RecursoEmpresaTO;

import javax.faces.context.FacesContext;

/**
 * Para uso dentro do contexto do ZW com JSF.
 *
 * <AUTHOR>
 * @since 14/03/2019
 */
public class EnfileiradorNotificadorRecursoSistemaSingleton extends AbstractNotificadorRecursoSistema {

    private static final EnfileiradorNotificadorRecursoSistemaSingleton INSTANCE = new EnfileiradorNotificadorRecursoSistemaSingleton();

    private EnfileiradorNotificadorRecursoSistemaSingleton() {
    }

    public static EnfileiradorNotificadorRecursoSistemaSingleton getInstance() {
        return INSTANCE;
    }

    /**
     * Enfileira a notifica��o. <br>
     * Usar para notificar recurso em um contexto dentro do JSF.
     */
    public void enfileirarNotificacaoRecursoEmpresa(final RecursoSistema recurso,
                                                    final String recursoString,
                                                    final String chave,
                                                    final boolean isAdministrador,
                                                    final boolean isUsarUrlRecursoEmpresa,
                                                    final Integer codigoEmpresaLogado,
                                                    final String usuarioLogado,
                                                    final String nomeEmpresa,
                                                    final String cidade,
                                                    final String uf,
                                                    final String pais) {
        enfileirarNotificacaoRecursoEmpresa(recurso, recursoString, chave, isAdministrador, isUsarUrlRecursoEmpresa, codigoEmpresaLogado, usuarioLogado, nomeEmpresa, cidade, uf, pais, -1l, -1);
    }

    public void enfileirarNotificacaoRecursoEmpresa(final RecursoSistema recurso,
                                                    final String chave,
                                                    final boolean isAdministrador,
                                                    final boolean isUsarUrlRecursoEmpresa,
                                                    final Integer codigoEmpresaLogado,
                                                    final String usuarioLogado,
                                                    final String nomeEmpresa,
                                                    final String cidade,
                                                    final String uf,
                                                    final String pais,
                                                    final Long tempoRequisicao) {
        notificarRecursoEmpresa(recurso, chave, isAdministrador, isUsarUrlRecursoEmpresa, codigoEmpresaLogado, usuarioLogado, nomeEmpresa, cidade, uf, pais, tempoRequisicao);
    }
    public void enfileirarNotificacaoRecursoEmpresa(final RecursoSistema recurso,
                                                    final String recursoString,
                                                    final String chave,
                                                    final boolean isAdministrador,
                                                    final boolean isUsarUrlRecursoEmpresa,
                                                    final Integer codigoEmpresaLogado,
                                                    final String usuarioLogado,
                                                    final String nomeEmpresa,
                                                    final String cidade,
                                                    final String uf,
                                                    final String pais,
                                                    final Long tempoRequisicao,
                                                    final int quantidadeDiasPesquisa) {
        notificarRecursoEmpresa(recurso, recursoString, chave, isAdministrador, isUsarUrlRecursoEmpresa, codigoEmpresaLogado, usuarioLogado, nomeEmpresa, cidade, uf, pais, tempoRequisicao, quantidadeDiasPesquisa);
    }

    @Override
    void processarNotificacao(RecursoEmpresaTO recursoEmpresaTO) {
        NotificadorRecursoEmpresaServiceControle.adicionarNotificacao(recursoEmpresaTO);
    }

}
