package br.com.pacto.notificacao;

import br.com.pacto.objeto.Uteis;
import br.com.pacto.threads.Callback;
import br.com.pacto.threads.ExecutorAssincronoUtil;
import br.com.pacto.util.bean.RecursoEmpresaTO;

import java.util.Map;

/**
 * Para uso fora do contexto do ZW sem JSF.
 *
 * <AUTHOR>
 * @since 14/03/2019
 */
public class NotificadorRecursoSistemaAssincronoSingleton extends AbstractNotificadorRecursoSistema {

    private static final NotificadorRecursoSistemaAssincronoSingleton INSTANCE = new NotificadorRecursoSistemaAssincronoSingleton();
    private static final Long TIMEOUT_EXECUCAO_ASSICRONA = 7000L;

    private NotificadorRecursoSistemaAssincronoSingleton() {
    }

    public static NotificadorRecursoSistemaAssincronoSingleton getInstance() {
        return INSTANCE;
    }

    /**
     * Executa o processamento da notifica��o de forma ass�ncrona. <br>
     * Usar para notificar recurso em um contexto fora do JSF.
     */
    public void notificarRecursoEmpresaAssincrono(final String chave,
                                                  final RecursoSistema recurso,
                                                  final Integer codigoEmpresaLogado,
                                                  final String usuarioLogado,
                                                  final String nomeEmpresa,
                                                  final String cidade,
                                                  final String uf,
                                                  final String pais) {
        notificarRecursoEmpresa(recurso, chave, false, true, codigoEmpresaLogado, usuarioLogado, nomeEmpresa, cidade, uf, pais, null);
    }

    @Override
    void processarNotificacao(final RecursoEmpresaTO recursoEmpresaTO) {
        ExecutorAssincronoUtil.executarAssincrono(
                new NotificadorRecursoSistemaAssincronoAcao(recursoEmpresaTO),
                new NotificadorRecursoSistemaAssincronoSucesso(recursoEmpresaTO),
                new NotificadorRecursoSistemaAssincronoFalha(recursoEmpresaTO),
                TIMEOUT_EXECUCAO_ASSICRONA
        );
    }

    private abstract class AbstractNotificadorRecursoSistemaAssincrono implements Callback {

        final RecursoEmpresaTO recursoEmpresaTO;

        AbstractNotificadorRecursoSistemaAssincrono(final RecursoEmpresaTO recursoEmpresaTO) {
            this.recursoEmpresaTO = recursoEmpresaTO;
        }

        @Override
        public Map<String, Object> getParametros() {
            return null;
        }
    }

    private class NotificadorRecursoSistemaAssincronoAcao extends AbstractNotificadorRecursoSistemaAssincrono {

        NotificadorRecursoSistemaAssincronoAcao(RecursoEmpresaTO recursoEmpresaTO) {
            super(recursoEmpresaTO);
        }

        @Override
        public void call() throws Exception {
            enviarNotificacaoOAMD(recursoEmpresaTO);
        }

    }

    private class NotificadorRecursoSistemaAssincronoSucesso extends AbstractNotificadorRecursoSistemaAssincrono {

        NotificadorRecursoSistemaAssincronoSucesso(RecursoEmpresaTO recursoEmpresaTO) {
            super(recursoEmpresaTO);
        }

        @Override
        public void call() {
            Uteis.logarDebug(
                    String.format("A��o (%s) executado com sucesso. O recurso: (%s) provavelmente foi salvo no OAMD!", NotificadorRecursoSistemaAssincronoSucesso.class.getSimpleName(), recursoEmpresaTO.getRecurso())
            );
        }

    }

    private class NotificadorRecursoSistemaAssincronoFalha extends AbstractNotificadorRecursoSistemaAssincrono {

        NotificadorRecursoSistemaAssincronoFalha(RecursoEmpresaTO recursoEmpresaTO) {
            super(recursoEmpresaTO);
        }

        @Override
        public void call() {
            Uteis.logarDebug(
                    String.format("A��o (%s) N�O foi executado com sucesso. O recurso: (%s) provavelmente N�O foi salvo no OAMD!", NotificadorRecursoSistemaAssincronoSucesso.class.getSimpleName(), recursoEmpresaTO.getRecurso())
            );
        }

    }

}
