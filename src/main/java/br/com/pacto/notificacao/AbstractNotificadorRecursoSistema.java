package br.com.pacto.notificacao;

import br.com.pacto.base.util.ExecuteRequestHttpService;
import br.com.pacto.controller.json.base.SuperControle;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;
import br.com.pacto.util.bean.RecursoEmpresaTO;
import org.json.JSONException;
import servicos.integracao.adm.AdmWSConsumer;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Respons�vel por centralizar a cria��o e notifica��o de recurso do sistema, mediante o {@link RecursoEmpresaTO}.
 *
 * <AUTHOR>
 * @since 18/03/2019
 */
public abstract class AbstractNotificadorRecursoSistema {

    private static final Integer TIMEOUT_CONEXAO_OAMD = 5000;
    private static final Integer TIMEOUT_LEITURA_OAMD = 60000;

    private static final String ENCODE_REQUEST = StandardCharsets.UTF_8.name();
    private static final String URL_OAMD_RECURSO_EMPRESA_PERSISTIR = Aplicacao.getProp(Aplicacao.urlOAMD) + "/prest/recursoEmpresa/persistir";
    private static final String MSG_ERRO_RECURSO_INFORMADO_NULO = "N�o notificarei pois o recurso informado � nulo.";

    abstract void processarNotificacao(RecursoEmpresaTO recursoEmpresaTO);

    public static String enviarNotificacaoOAMD(RecursoEmpresaTO recursoEmpresaTO) throws JSONException, IOException {
        return getExecuteRequestHttpService().executeRequestInner(
                URL_OAMD_RECURSO_EMPRESA_PERSISTIR,
                getParametrosEnvio(recursoEmpresaTO),
                ENCODE_REQUEST
        );
    }

    private static ExecuteRequestHttpService getExecuteRequestHttpService() {
        ExecuteRequestHttpService executeRequestHttpService = new ExecuteRequestHttpService();
        executeRequestHttpService.connectTimeout = TIMEOUT_CONEXAO_OAMD;
        executeRequestHttpService.readTimeout = TIMEOUT_LEITURA_OAMD;

        return executeRequestHttpService;
    }

    private static Map<String, String> getParametrosEnvio(RecursoEmpresaTO recursoEmpresaTO) throws JSONException {
        Map<String, String> params = new HashMap<String, String>();
        params.put("jsonObject", recursoEmpresaTO.toJSON());

        return params;
    }

    protected void notificarRecursoEmpresa(final RecursoSistema recurso,
                                           final String chave,
                                           final boolean isAdministrador,
                                           final boolean isUsarUrlRecursoEmpresa,
                                           final Integer codigoEmpresaLogado,
                                           final String usuarioLogado,
                                           final String nomeEmpresa,
                                           final String cidade,
                                           final String uf,
                                           final String pais) {
        if (!validarParametros(recurso)) {
            return;
        }

        try {
            if (!isAdministrador && isUsarUrlRecursoEmpresa) {
                RecursoEmpresaTO recursoEmpresaTO = criarRecursoEmpresaTO(chave, recurso, null, codigoEmpresaLogado, usuarioLogado, nomeEmpresa, cidade, uf, pais, -1l, -1);
                processarNotificacao(recursoEmpresaTO);
            }
        } catch (Exception e) {
            Logger.getLogger(NotificadorRecursoSistemaAssincronoSingleton.class.getName()).log(Level.SEVERE, null, e);
        }
    }

    protected void notificarRecursoEmpresa(final RecursoSistema recurso,
                                           final String chave,
                                           final boolean isAdministrador,
                                           final boolean isUsarUrlRecursoEmpresa,
                                           final Integer codigoEmpresaLogado,
                                           final String usuarioLogado,
                                           final String nomeEmpresa,
                                           final String cidade,
                                           final String uf,
                                           final String pais,
                                           final Long tempoRequisicao) {
        if (!validarParametros(recurso)) {
            return;
        }

        try {
            if (!isAdministrador && isUsarUrlRecursoEmpresa) {
                RecursoEmpresaTO recursoEmpresaTO = criarRecursoEmpresaTO(chave, recurso, null, codigoEmpresaLogado, usuarioLogado, nomeEmpresa, cidade, uf, pais, tempoRequisicao, -1);
                processarNotificacao(recursoEmpresaTO);
            }
        } catch (Exception e) {
            Logger.getLogger(NotificadorRecursoSistemaAssincronoSingleton.class.getName()).log(Level.SEVERE, null, e);
        }
    }
    protected void notificarRecursoEmpresa(final RecursoSistema recurso,
                                           final String recursoString,
                                           final String chave,
                                           final boolean isAdministrador,
                                           final boolean isUsarUrlRecursoEmpresa,
                                           final Integer codigoEmpresaLogado,
                                           final String usuarioLogado,
                                           final String nomeEmpresa,
                                           final String cidade,
                                           final String uf,
                                           final String pais,
                                           final Long tempoRequisicao,
                                           final int quantidadeDiasPesquisa) {
        if(UteisValidacao.emptyString(recursoString)) {
            if (!validarParametros(recurso)) {
                return;
            }
        }

        try {
            if (!isAdministrador && isUsarUrlRecursoEmpresa) {
                RecursoEmpresaTO recursoEmpresaTO = criarRecursoEmpresaTO(chave, recurso, recursoString, codigoEmpresaLogado, usuarioLogado, nomeEmpresa, cidade, uf, pais, tempoRequisicao, quantidadeDiasPesquisa);
                processarNotificacao(recursoEmpresaTO);
            }
            if (isAdministrador && isUsarUrlRecursoEmpresa &&
                    (recurso.equals(RecursoSistema.TESTE))) {
                RecursoEmpresaTO recursoEmpresaTO = criarRecursoEmpresaTO(chave, recurso, recursoString, codigoEmpresaLogado, usuarioLogado, nomeEmpresa, cidade, uf, pais, tempoRequisicao, quantidadeDiasPesquisa);
                processarNotificacao(recursoEmpresaTO);
            }
        } catch (Exception e) {
            Logger.getLogger(NotificadorRecursoSistemaAssincronoSingleton.class.getName()).log(Level.SEVERE, null, e);
        }
    }

    private boolean validarParametros(final RecursoSistema recurso) {
        if (recurso == null) {
            Logger.getLogger(SuperControle.class.getName()).log(Level.SEVERE, MSG_ERRO_RECURSO_INFORMADO_NULO);
            return false;
        }

        return true;
    }

    private RecursoEmpresaTO criarRecursoEmpresaTO(final String chave,
                                                   final RecursoSistema recurso,
                                                   final String recursoString,
                                                   final Integer codigoEmpresaLogado,
                                                   final String usuarioLogado,
                                                   final String nomeEmpresa,
                                                   final String cidade,
                                                   final String uf,
                                                   final String pais,
                                                   final Long tempoRequisicao,
                                                   final int intervaloTempoRequisicao) {
        RecursoEmpresaTO recursoEmpresaTO = new RecursoEmpresaTO();
        recursoEmpresaTO.setDate(Calendario.hoje());
        recursoEmpresaTO.setRecurso(recurso == null ? recursoString : recurso.getDescricao());
        recursoEmpresaTO.setEmpresa(codigoEmpresaLogado);
        recursoEmpresaTO.setUsuario(usuarioLogado);
        recursoEmpresaTO.setNomeEmpresa(nomeEmpresa);
        recursoEmpresaTO.setCidade(cidade);
        recursoEmpresaTO.setUf(uf);
        recursoEmpresaTO.setPais(pais);
        recursoEmpresaTO.setTempoRequisicao(tempoRequisicao);
        recursoEmpresaTO.setIntervaloDiasPesquisados(intervaloTempoRequisicao);
        recursoEmpresaTO.setChave(chave);
        recursoEmpresaTO.setModulos(Aplicacao.getProp(chave, Aplicacao.modulos));
        return recursoEmpresaTO;
    }
}
