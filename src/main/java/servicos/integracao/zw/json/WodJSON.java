package servicos.integracao.zw.json;

import br.com.pacto.bean.wod.ComentarioWod;
import br.com.pacto.bean.wod.Wod;
import br.com.pacto.controller.json.base.SuperJSON;
import br.com.pacto.controller.json.crossfit.ComentarioWodJSON;

import java.util.ArrayList;
import java.util.List;

/*
 * <AUTHOR>
 */
public class WodJSON extends SuperJSON {

    private Integer codigo;
    private String nome;
    private List<ComentarioWodJSON> comentarios;

    public WodJSON(Wod wod) {
        this.codigo = wod.getCodigo();
        this.nome = wod.getNome();
        for (ComentarioWod comentarioWod : wod.getComentarios()) {
            this.getComentarios().add(new ComentarioWodJSON(comentarioWod));
        }
    }

    public WodJSON() {
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public List<ComentarioWodJSON> getComentarios() {
        if (comentarios == null) {
            comentarios = new ArrayList<ComentarioWodJSON>();
        }
        return comentarios;
    }

    public void setComentarios(List<ComentarioWodJSON> comentarios) {
        this.comentarios = comentarios;
    }
}
