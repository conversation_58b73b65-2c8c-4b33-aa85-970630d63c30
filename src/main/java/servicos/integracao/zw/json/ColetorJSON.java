package servicos.integracao.zw.json;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.io.Serializable;

@JsonIgnoreProperties(ignoreUnknown = true)
public class ColetorJSON implements Serializable {

    @JsonAlias(value = "numeroTerminal")
    private Integer numeroTerminal;
    @JsonAlias(value = "codigo")
    private Integer codigoColetor;
    @JsonAlias(value = "localAcesso")
    private Integer codigoLocalAcesso;
    @JsonAlias(value = "codigoNFC")
    private String codigoNFC;
    @JsonAlias(value = "descricao")
    private String descricaoColetor;

    @JsonAlias(value = "longitude")
    private String longitude;
    @JsonAlias(value = "latitude")
    private String latitude;

    public Integer getCodigoColetor() {
        return codigoColetor;
    }

    public void setCodigoColetor(Integer codigoColetor) {
        this.codigoColetor = codigoColetor;
    }

    public Integer getNumeroTerminal() {
        return numeroTerminal;
    }

    public void setNumeroTerminal(Integer numeroTerminal) {
        this.numeroTerminal = numeroTerminal;
    }

    public Integer getCodigoLocalAcesso() {
        return codigoLocalAcesso;
    }

    public void setCodigoLocalAcesso(Integer codigoLocalAcesso) {
        this.codigoLocalAcesso = codigoLocalAcesso;
    }

    public String getCodigoNFC() {
        return codigoNFC;
    }

    public void setCodigoNFC(String codigoNFC) {
        this.codigoNFC = codigoNFC;
    }

    public String getDescricaoColetor() {
        return descricaoColetor;
    }

    public void setDescricaoColetor(String descricaoColetor) {
        this.descricaoColetor = descricaoColetor;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }
}