/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.integracao.zw.json;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class ModalidadeJSON implements Serializable {
    private Integer codigoModalidade;
    private String nome;
    private Boolean escolhido;

    public Boolean getEscolhido() {
        return escolhido;
    }

    public void setEscolhido(Boolean escolhido) {
        this.escolhido = escolhido;
    }
    
    public Integer getCodigoModalidade() {
        return codigoModalidade;
    }

    public void setCodigoModalidade(Integer codigoModalidade) {
        this.codigoModalidade = codigoModalidade;
    }

    public String getNome() {
        return nome;
    }
    
    public String getNomeAbreviado() {
        return nome == null || nome.length() < 20 ? nome : (nome.substring(0, 17)+"...");
    }
    

    public void setNome(String nome) {
        this.nome = nome;
    }
    
}
