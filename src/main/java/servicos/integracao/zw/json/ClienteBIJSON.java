/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.integracao.zw.json;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class ClienteBIJSON implements Serializable {
    
    private Integer matricula;
    private String nome;
    private Integer codigoPessoa;
    private Integer codigoProfessor;
    private String nomeProfessor;
    private String vigenciaate;
    @JsonDeserialize(as = ArrayList.class, contentAs = ModalidadeJSON.class)
    private List<ModalidadeJSON> modalidades;
    private Boolean selecionado = Boolean.FALSE;

    public String getNomesModalidades(){
        try {
            String nomemodalidades = "";
            for(ModalidadeJSON mod : modalidades){
                nomemodalidades +=mod.getNome()+"<br/>";
            }
            return nomemodalidades;
        } catch (Exception e) {
            return "";
        }
    }

    public Integer getMatricula() {
        return matricula;
    }

    public void setMatricula(Integer matricula) {
        this.matricula = matricula;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Integer getCodigoPessoa() {
        return codigoPessoa;
    }

    public void setCodigoPessoa(Integer codigoPessoa) {
        this.codigoPessoa = codigoPessoa;
    }

    public String getNomeProfessor() {
        return nomeProfessor;
    }

    public void setNomeProfessor(String nomeProfessor) {
        this.nomeProfessor = nomeProfessor;
    }

    public List<ModalidadeJSON> getModalidades() {
        return modalidades;
    }

    public void setModalidades(List<ModalidadeJSON> modalidades) {
        this.modalidades = modalidades;
    }

    public Integer getCodigoProfessor() {
        return codigoProfessor;
    }

    public void setCodigoProfessor(Integer codigoProfessor) {
        this.codigoProfessor = codigoProfessor;
    }

    public Boolean getSelecionado() {
        return selecionado;
    }

    public void setSelecionado(Boolean selecionado) {
        this.selecionado = selecionado;
    }

    public String getVigenciaate() {
        return vigenciaate;
    }

    public void setVigenciaate(String vigenciaate) {
        this.vigenciaate = vigenciaate;
    }
    
}
