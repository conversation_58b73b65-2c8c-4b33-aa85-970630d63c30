/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.integracao.zw.json;

import br.com.pacto.objeto.Uteis;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class AddClienteJSON implements Serializable {

    private Integer matricula;
    private Integer codigoCliente;
    private String nome;
    private Integer codigoPessoa;
    private String email;
    private String key;
    @JsonDeserialize(as = ArrayList.class, contentAs = VinculoJSON.class)
    private List<VinculoJSON> vinculos;

    public AddClienteJSON() {
    }

    public Integer getMatricula() {
        return matricula;
    }

    public void setMatricula(Integer matricula) {
        this.matricula = matricula;
    }

    public Integer getCodigoCliente() {
        return codigoCliente;
    }

    public void setCodigoCliente(Integer codigoCliente) {
        this.codigoCliente = codigoCliente;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Integer getCodigoPessoa() {
        return codigoPessoa;
    }

    public void setCodigoPessoa(Integer codigoPessoa) {
        this.codigoPessoa = codigoPessoa;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public List<VinculoJSON> getVinculos() {
        return vinculos;
    }

    public void setVinculos(List<VinculoJSON> vinculos) {
        this.vinculos = vinculos;
    }
    
    public String getPrimeiroNomeComLetraSobrenome() {
        if (nome != null) {
            return Uteis.getNomeAbreviandoSomentePrimeiroSobrenome(nome);
        }
        return "";
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }
}
