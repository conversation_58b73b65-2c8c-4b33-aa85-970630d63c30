package servicos.integracao.zw.json;

import br.com.pacto.controller.json.base.SuperJSON;
import br.com.pacto.objeto.Uteis;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.util.Date;

/**
 * Created by <PERSON> on 15/01/2016.
 */
public class AtestadoClienteJSON extends SuperJSON{
    private String descricao;
    @Temporal(TemporalType.DATE)
    private Date dataInicio;
    @Temporal(TemporalType.DATE)
    private Date dataFinal;
    private String observacao;
    private int codAtestado;
    private Boolean parq;
    private String nomeArquivoGerado;
    private String extensao;

    public String getDescricao() {
        return descricao;
    }

    public boolean getExisteAnexo(){
        return codAtestado > 0;
    }
    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Date getDataFinal() {
        return dataFinal;
    }
    public String getDataFinal_Apresentar(){
        return Uteis.getData(dataFinal);
    }
    public String getDataInicio_Apresentar(){
        return Uteis.getData(dataInicio);
    }
    public void setDataFinal(Date dataFinal) {
        this.dataFinal = dataFinal;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public int getCodAtestado() {
        return codAtestado;
    }

    public void setCodAtestado(int codAtestado) {
        this.codAtestado = codAtestado;
    }

    public boolean isExisteAnexo(){
        return codAtestado > 0;
    }
    public Boolean getParq() {
        return parq;
    }

    public void setParq(Boolean parq) {
        this.parq = parq;
    }

    public String getNomeArquivoGerado() {
        return nomeArquivoGerado;
    }

    public void setNomeArquivoGerado(String nomeArquivoGerado) {
        this.nomeArquivoGerado = nomeArquivoGerado;
    }

    public String getExtensao() {
        return extensao;
    }

    public void setExtensao(String extensao) {
        this.extensao = extensao;
    }
}
