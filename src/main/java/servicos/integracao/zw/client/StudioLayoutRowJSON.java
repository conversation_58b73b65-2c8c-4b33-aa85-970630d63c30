
package servicos.integracao.zw.client;

import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java de studioLayoutRowJSON complex type.
 * 
 * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
 * 
 * <pre>
 * &lt;complexType name="studioLayoutRowJSON">
 *   &lt;complexContent>
 *     &lt;extension base="{http://webservice.basico.comuns.negocio/}superJSON">
 *       &lt;sequence>
 *         &lt;element name="seats" type="{http://webservice.basico.comuns.negocio/}seatJSON" maxOccurs="unbounded" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/extension>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "studioLayoutRowJSON", propOrder = {
    "seats"
})
public class StudioLayoutRowJSON
    extends SuperJSON
{

    @XmlElement(nillable = true)
    protected List<SeatJSON> seats;

    /**
     * Gets the value of the seats property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the seats property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getSeats().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link SeatJSON }
     * 
     * 
     */
    public List<SeatJSON> getSeats() {
        if (seats == null) {
            seats = new ArrayList<SeatJSON>();
        }
        return this.seats;
    }

}
