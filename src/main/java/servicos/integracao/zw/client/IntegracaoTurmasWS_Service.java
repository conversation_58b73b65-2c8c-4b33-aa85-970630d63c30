
package servicos.integracao.zw.client;

import java.net.MalformedURLException;
import java.net.URL;
import javax.xml.namespace.QName;
import javax.xml.ws.Service;
import javax.xml.ws.WebEndpoint;
import javax.xml.ws.WebServiceClient;
import javax.xml.ws.WebServiceException;
import javax.xml.ws.WebServiceFeature;


/**
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.2.6b21 
 * Generated source version: 2.1
 * 
 */
@WebServiceClient(name = "IntegracaoTurmasWS", targetNamespace = "http://webservice.basico.comuns.negocio/", wsdlLocation = "https://app.pactosolucoes.com.br/app/IntegracaoTurmasWS?wsdl")
public class IntegracaoTurmasWS_Service
    extends Service
{

    private final static URL INTEGRACAOTURMASWS_WSDL_LOCATION;
    private final static WebServiceException INTEGRACAOTURMASWS_EXCEPTION;
    private final static QName INTEGRACAOTURMASWS_QNAME = new QName("http://webservice.basico.comuns.negocio/", "IntegracaoTurmasWS");

    static {
        URL url = null;
        WebServiceException e = null;
        try {
            url = new URL("https://app.pactosolucoes.com.br/app/IntegracaoTurmasWS?wsdl");
        } catch (MalformedURLException ex) {
            e = new WebServiceException(ex);
        }
        INTEGRACAOTURMASWS_WSDL_LOCATION = url;
        INTEGRACAOTURMASWS_EXCEPTION = e;
    }

    public IntegracaoTurmasWS_Service() {
        super(__getWsdlLocation(), INTEGRACAOTURMASWS_QNAME);
    }

    public IntegracaoTurmasWS_Service(URL wsdlLocation, QName serviceName) {
        super(wsdlLocation, serviceName);
    }

    /**
     * 
     * @return
     *     returns IntegracaoTurmasWS
     */
    @WebEndpoint(name = "IntegracaoTurmasWSPort")
    public IntegracaoTurmasWS getIntegracaoTurmasWSPort() {
        return super.getPort(new QName("http://webservice.basico.comuns.negocio/", "IntegracaoTurmasWSPort"), IntegracaoTurmasWS.class);
    }

    /**
     * 
     * @param features
     *     A list of {@link javax.xml.ws.WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return
     *     returns IntegracaoTurmasWS
     */
    @WebEndpoint(name = "IntegracaoTurmasWSPort")
    public IntegracaoTurmasWS getIntegracaoTurmasWSPort(WebServiceFeature... features) {
        return super.getPort(new QName("http://webservice.basico.comuns.negocio/", "IntegracaoTurmasWSPort"), IntegracaoTurmasWS.class, features);
    }

    private static URL __getWsdlLocation() {
        if (INTEGRACAOTURMASWS_EXCEPTION!= null) {
            throw INTEGRACAOTURMASWS_EXCEPTION;
        }
        return INTEGRACAOTURMASWS_WSDL_LOCATION;
    }

}
