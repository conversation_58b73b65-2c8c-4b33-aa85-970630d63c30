
package servicos.integracao.zw.client;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java de marcarAvaliacaoFeedGestao complex type.
 * 
 * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
 * 
 * <pre>
 * &lt;complexType name="marcarAvaliacaoFeedGestao">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="key" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="codigoHistorico" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="usuario" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="liked" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         &lt;element name="disliked" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "marcarAvaliacaoFeedGestao", propOrder = {
    "key",
    "codigoHistorico",
    "usuario",
    "liked",
    "disliked"
})
public class MarcarAvaliacaoFeedGestao {

    protected String key;
    protected Integer codigoHistorico;
    protected Integer usuario;
    protected boolean liked;
    protected boolean disliked;

    /**
     * Obtém o valor da propriedade key.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getKey() {
        return key;
    }

    /**
     * Define o valor da propriedade key.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setKey(String value) {
        this.key = value;
    }

    /**
     * Obtém o valor da propriedade codigoHistorico.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getCodigoHistorico() {
        return codigoHistorico;
    }

    /**
     * Define o valor da propriedade codigoHistorico.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setCodigoHistorico(Integer value) {
        this.codigoHistorico = value;
    }

    /**
     * Obtém o valor da propriedade usuario.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getUsuario() {
        return usuario;
    }

    /**
     * Define o valor da propriedade usuario.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setUsuario(Integer value) {
        this.usuario = value;
    }

    /**
     * Obtém o valor da propriedade liked.
     * 
     */
    public boolean isLiked() {
        return liked;
    }

    /**
     * Define o valor da propriedade liked.
     * 
     */
    public void setLiked(boolean value) {
        this.liked = value;
    }

    /**
     * Obtém o valor da propriedade disliked.
     * 
     */
    public boolean isDisliked() {
        return disliked;
    }

    /**
     * Define o valor da propriedade disliked.
     * 
     */
    public void setDisliked(boolean value) {
        this.disliked = value;
    }

}
