
package servicos.integracao.zw.client;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java de alterarEmail complex type.
 * 
 * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
 * 
 * <pre>
 * &lt;complexType name="alterarEmail">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="ctx" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="matricula" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="codigo" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="email" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="emailCorrespondencia" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         &lt;element name="excluir" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         &lt;element name="origemSistema" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "alterarEmail", propOrder = {
    "ctx",
    "matricula",
    "codigo",
    "email",
    "emailCorrespondencia",
    "excluir",
    "origemSistema"
})
public class AlterarEmail {

    protected String ctx;
    protected String matricula;
    protected Integer codigo;
    protected String email;
    protected boolean emailCorrespondencia;
    protected boolean excluir;
    protected Integer origemSistema;

    /**
     * Obtém o valor da propriedade ctx.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCtx() {
        return ctx;
    }

    /**
     * Define o valor da propriedade ctx.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCtx(String value) {
        this.ctx = value;
    }

    /**
     * Obtém o valor da propriedade matricula.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMatricula() {
        return matricula;
    }

    /**
     * Define o valor da propriedade matricula.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMatricula(String value) {
        this.matricula = value;
    }

    /**
     * Obtém o valor da propriedade codigo.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getCodigo() {
        return codigo;
    }

    /**
     * Define o valor da propriedade codigo.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setCodigo(Integer value) {
        this.codigo = value;
    }

    /**
     * Obtém o valor da propriedade email.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getEmail() {
        return email;
    }

    /**
     * Define o valor da propriedade email.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setEmail(String value) {
        this.email = value;
    }

    /**
     * Obtém o valor da propriedade emailCorrespondencia.
     * 
     */
    public boolean isEmailCorrespondencia() {
        return emailCorrespondencia;
    }

    /**
     * Define o valor da propriedade emailCorrespondencia.
     * 
     */
    public void setEmailCorrespondencia(boolean value) {
        this.emailCorrespondencia = value;
    }

    /**
     * Obtém o valor da propriedade excluir.
     * 
     */
    public boolean isExcluir() {
        return excluir;
    }

    /**
     * Define o valor da propriedade excluir.
     * 
     */
    public void setExcluir(boolean value) {
        this.excluir = value;
    }

    /**
     * Obtém o valor da propriedade origemSistema.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getOrigemSistema() {
        return origemSistema;
    }

    /**
     * Define o valor da propriedade origemSistema.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setOrigemSistema(Integer value) {
        this.origemSistema = value;
    }

}
