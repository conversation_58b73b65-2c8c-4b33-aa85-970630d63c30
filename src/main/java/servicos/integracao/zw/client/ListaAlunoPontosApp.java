
package servicos.integracao.zw.client;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java de listaAlunoPontosApp complex type.
 * 
 * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
 * 
 * <pre>
 * &lt;complexType name="listaAlunoPontosApp">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="Key" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="matriculacliente" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="analitico" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "listaAlunoPontosApp", propOrder = {
    "key",
    "matriculacliente",
    "analitico"
})
public class ListaAlunoPontosApp {

    @XmlElement(name = "Key")
    protected String key;
    protected String matriculacliente;
    protected boolean analitico;

    /**
     * Obtém o valor da propriedade key.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getKey() {
        return key;
    }

    /**
     * Define o valor da propriedade key.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setKey(String value) {
        this.key = value;
    }

    /**
     * Obtém o valor da propriedade matriculacliente.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMatriculacliente() {
        return matriculacliente;
    }

    /**
     * Define o valor da propriedade matriculacliente.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMatriculacliente(String value) {
        this.matriculacliente = value;
    }

    /**
     * Obtém o valor da propriedade analitico.
     * 
     */
    public boolean isAnalitico() {
        return analitico;
    }

    /**
     * Define o valor da propriedade analitico.
     * 
     */
    public void setAnalitico(boolean value) {
        this.analitico = value;
    }

}
