
package servicos.integracao.zw.client;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java de tiposMensagensEnum.
 * 
 * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
 * <p>
 * <pre>
 * &lt;simpleType name="tiposMensagensEnum">
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *     &lt;enumeration value="NENHUM"/>
 *     &lt;enumeration value="AVISO_CONSULTOR"/>
 *     &lt;enumeration value="CATRACA"/>
 *     &lt;enumeration value="AVISO_MEDICO"/>
 *     &lt;enumeration value="BOLETIM"/>
 *     &lt;enumeration value="DADOS_INCOMPLETOS"/>
 *     &lt;enumeration value="PARCELA_ATRASO"/>
 *     &lt;enumeration value="RISCO"/>
 *     &lt;enumeration value="OBJETIVO_CURTO"/>
 *     &lt;enumeration value="OBSERVACAO"/>
 *     &lt;enumeration value="OBSERVACAO_CLIENTE"/>
 *     &lt;enumeration value="PRODUTO_VENCIDO"/>
 *     &lt;enumeration value="CARTAO_VENCIDO"/>
 *     &lt;enumeration value="ARMARIO_ALUGUEL_VENCIDO_CHAVE_NAO_DEVOLVIDA"/>
 *     &lt;enumeration value="ESTORNO"/>
 *   &lt;/restriction>
 * &lt;/simpleType>
 * </pre>
 * 
 */
@XmlType(name = "tiposMensagensEnum")
@XmlEnum
public enum TiposMensagensEnum {

    NENHUM,
    AVISO_CONSULTOR,
    CATRACA,
    AVISO_MEDICO,
    BOLETIM,
    DADOS_INCOMPLETOS,
    PARCELA_ATRASO,
    RISCO,
    OBJETIVO_CURTO,
    OBSERVACAO,
    OBSERVACAO_CLIENTE,
    PRODUTO_VENCIDO,
    CARTAO_VENCIDO,
    ARMARIO_ALUGUEL_VENCIDO_CHAVE_NAO_DEVOLVIDA,
    ESTORNO;

    public String value() {
        return name();
    }

    public static TiposMensagensEnum fromValue(String v) {
        return valueOf(v);
    }

}
