
package servicos.integracao.zw.client;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import javax.xml.bind.annotation.XmlSeeAlso;
import javax.xml.ws.RequestWrapper;
import javax.xml.ws.ResponseWrapper;


/**
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.2.6b21 
 * Generated source version: 2.1
 * 
 */
@WebService(name = "ConviteAulaExperimentalWS", targetNamespace = "http://webservice.basico.comuns.negocio/")
@XmlSeeAlso({
    ObjectFactory.class
})
public interface ConviteAulaExperimentalWS {


    /**
     * 
     * @param codigoCliente
     * @param empresa
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarTipoConvitesAlunoPodeEnviar", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarTipoConvitesAlunoPodeEnviar")
    @ResponseWrapper(localName = "consultarTipoConvitesAlunoPodeEnviarResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarTipoConvitesAlunoPodeEnviarResponse")
    public String consultarTipoConvitesAlunoPodeEnviar(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa,
        @WebParam(name = "codigoCliente", targetNamespace = "")
        Integer codigoCliente);

    /**
     * 
     * @param codigoTipoConvite
     * @param codigoColaboradorResponsavelConvite
     * @param codigoClienteConvidado
     * @param codigoPassivoConvidado
     * @param codigoIndicadoConvidado
     * @param codigoUsuarioConvidou
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "gerarConviteViaWeb", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.GerarConviteViaWeb")
    @ResponseWrapper(localName = "gerarConviteViaWebResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.GerarConviteViaWebResponse")
    public String gerarConviteViaWeb(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "codigoUsuarioConvidou", targetNamespace = "")
        Integer codigoUsuarioConvidou,
        @WebParam(name = "codigoTipoConvite", targetNamespace = "")
        Integer codigoTipoConvite,
        @WebParam(name = "codigoIndicadoConvidado", targetNamespace = "")
        Integer codigoIndicadoConvidado,
        @WebParam(name = "codigoPassivoConvidado", targetNamespace = "")
        Integer codigoPassivoConvidado,
        @WebParam(name = "codigoClienteConvidado", targetNamespace = "")
        Integer codigoClienteConvidado,
        @WebParam(name = "codigoColaboradorResponsavelConvite", targetNamespace = "")
        Integer codigoColaboradorResponsavelConvite);

    /**
     * 
     * @param codigoCliente
     * @param empresa
     * @param key
     * @param codigoConvite
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "excluirConvite", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ExcluirConvite")
    @ResponseWrapper(localName = "excluirConviteResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ExcluirConviteResponse")
    public String excluirConvite(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa,
        @WebParam(name = "codigoConvite", targetNamespace = "")
        Integer codigoConvite,
        @WebParam(name = "codigoCliente", targetNamespace = "")
        Integer codigoCliente);

    /**
     * 
     * @param telefoneRes
     * @param nome
     * @param telefoneCelular
     * @param empresa
     * @param key
     * @param codigoConvite
     * @param email
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "alterarDadosCadastraisConvidado", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.AlterarDadosCadastraisConvidado")
    @ResponseWrapper(localName = "alterarDadosCadastraisConvidadoResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.AlterarDadosCadastraisConvidadoResponse")
    public String alterarDadosCadastraisConvidado(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa,
        @WebParam(name = "codigoConvite", targetNamespace = "")
        Integer codigoConvite,
        @WebParam(name = "nome", targetNamespace = "")
        String nome,
        @WebParam(name = "telefoneRes", targetNamespace = "")
        String telefoneRes,
        @WebParam(name = "telefoneCelular", targetNamespace = "")
        String telefoneCelular,
        @WebParam(name = "email", targetNamespace = "")
        String email);

    /**
     * 
     * @param codigoHorarioTurma
     * @param dataAula
     * @param key
     * @param codigoConvite
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "agendarAulaExperimental", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.AgendarAulaExperimental")
    @ResponseWrapper(localName = "agendarAulaExperimentalResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.AgendarAulaExperimentalResponse")
    public String agendarAulaExperimental(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "codigoConvite", targetNamespace = "")
        Integer codigoConvite,
        @WebParam(name = "dataAula", targetNamespace = "")
        String dataAula,
        @WebParam(name = "codigoHorarioTurma", targetNamespace = "")
        Integer codigoHorarioTurma);

    /**
     * 
     * @param codigoHorarioTurma
     * @param dataAula
     * @param key
     * @param codigoConvite
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "desmarcarAulaExperimental", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.DesmarcarAulaExperimental")
    @ResponseWrapper(localName = "desmarcarAulaExperimentalResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.DesmarcarAulaExperimentalResponse")
    public String desmarcarAulaExperimental(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "codigoConvite", targetNamespace = "")
        Integer codigoConvite,
        @WebParam(name = "dataAula", targetNamespace = "")
        String dataAula,
        @WebParam(name = "codigoHorarioTurma", targetNamespace = "")
        Integer codigoHorarioTurma);

    /**
     * 
     * @param consultarAulaCheia
     * @param consultarTurmaZW
     * @param inicio
     * @param fim
     * @param key
     * @param codigoConvite
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarTurmasParaAgendarAulaExperimental", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarTurmasParaAgendarAulaExperimental")
    @ResponseWrapper(localName = "consultarTurmasParaAgendarAulaExperimentalResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarTurmasParaAgendarAulaExperimentalResponse")
    public String consultarTurmasParaAgendarAulaExperimental(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "inicio", targetNamespace = "")
        String inicio,
        @WebParam(name = "fim", targetNamespace = "")
        String fim,
        @WebParam(name = "codigoConvite", targetNamespace = "")
        Integer codigoConvite,
        @WebParam(name = "consultarAulaCheia", targetNamespace = "")
        Integer consultarAulaCheia,
        @WebParam(name = "consultarTurmaZW", targetNamespace = "")
        Integer consultarTurmaZW);

    /**
     * 
     * @param consultarAulaCheia
     * @param consultarTurmaZW
     * @param key
     * @param codigoConvite
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarAulaExperimentalAgendada", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarAulaExperimentalAgendada")
    @ResponseWrapper(localName = "consultarAulaExperimentalAgendadaResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarAulaExperimentalAgendadaResponse")
    public String consultarAulaExperimentalAgendada(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "codigoConvite", targetNamespace = "")
        Integer codigoConvite,
        @WebParam(name = "consultarAulaCheia", targetNamespace = "")
        Integer consultarAulaCheia,
        @WebParam(name = "consultarTurmaZW", targetNamespace = "")
        Integer consultarTurmaZW);

    /**
     * 
     * @param codigoTipoConvite
     * @param codigoCliente
     * @param telefoneConvidado
     * @param empresa
     * @param nomeIndicado
     * @param key
     * @param emailConvidado
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "gerarConvite", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.GerarConvite")
    @ResponseWrapper(localName = "gerarConviteResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.GerarConviteResponse")
    public String gerarConvite(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa,
        @WebParam(name = "codigoCliente", targetNamespace = "")
        Integer codigoCliente,
        @WebParam(name = "codigoTipoConvite", targetNamespace = "")
        Integer codigoTipoConvite,
        @WebParam(name = "nomeIndicado", targetNamespace = "")
        String nomeIndicado,
        @WebParam(name = "telefoneConvidado", targetNamespace = "")
        String telefoneConvidado,
        @WebParam(name = "emailConvidado", targetNamespace = "")
        String emailConvidado);

    /**
     * 
     * @param empresa
     * @param key
     * @param codigoConvite
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "validarConvite", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ValidarConvite")
    @ResponseWrapper(localName = "validarConviteResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ValidarConviteResponse")
    public String validarConvite(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa,
        @WebParam(name = "codigoConvite", targetNamespace = "")
        Integer codigoConvite);

}
