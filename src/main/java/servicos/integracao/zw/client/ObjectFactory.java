
package servicos.integracao.zw.client;

import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlElementDecl;
import javax.xml.bind.annotation.XmlRegistry;
import javax.xml.namespace.QName;


/**
 * This object contains factory methods for each 
 * Java content interface and Java element interface 
 * generated in the servicos.integracao.zw.client package. 
 * <p>An ObjectFactory allows you to programatically 
 * construct new instances of the Java representation 
 * for XML content. The Java representation of XML 
 * content can consist of schema derived interfaces 
 * and classes representing the binding of schema 
 * type definitions, element declarations and model 
 * groups.  Factory methods for each of these are 
 * provided in this class.
 * 
 */
@XmlRegistry
public class ObjectFactory {

    private final static QName _PersistirClienteJSONResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "persistirClienteJSONResponse");
    private final static QName _ApiWeHelpResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "apiWeHelpResponse");
    private final static QName _ConsultarParcelasCliente_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarParcelasCliente");
    private final static QName _ObterPontosPorClienteResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "obterPontosPorClienteResponse");
    private final static QName _PersistirTokenLeadRDResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "persistirTokenLeadRDResponse");
    private final static QName _PersistirCodeLeadRD_QNAME = new QName("http://webservice.basico.comuns.negocio/", "persistirCodeLeadRD");
    private final static QName _ConsultarLocaisAcessoResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarLocaisAcessoResponse");
    private final static QName _ConsultarDadosOperacaoContrato_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarDadosOperacaoContrato");
    private final static QName _ConsultarClientePorCodigoAcessoResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarClientePorCodigoAcessoResponse");
    private final static QName _ObterVencimentosContratos_QNAME = new QName("http://webservice.basico.comuns.negocio/", "obterVencimentosContratos");
    private final static QName _ConsultarResumoPeriodoResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarResumoPeriodoResponse");
    private final static QName _AlterarUsuarioMovelAlunoNovoResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "alterarUsuarioMovelAlunoNovoResponse");
    private final static QName _SolicitacoesConcluidas_QNAME = new QName("http://webservice.basico.comuns.negocio/", "solicitacoesConcluidas");
    private final static QName _PersistirProdutoJSONResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "persistirProdutoJSONResponse");
    private final static QName _PersistirLogAuditoriaResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "persistirLogAuditoriaResponse");
    private final static QName _LancarProdutoClienteResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "lancarProdutoClienteResponse");
    private final static QName _ConsultarMetasCRM_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarMetasCRM");
    private final static QName _ObterContratoOperacao_QNAME = new QName("http://webservice.basico.comuns.negocio/", "obterContratoOperacao");
    private final static QName _ConsultarDadosBITreinoResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarDadosBITreinoResponse");
    private final static QName _ConsultarFaturamentoDuracao_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarFaturamentoDuracao");
    private final static QName _ConsultarModalidadesEmpresaResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarModalidadesEmpresaResponse");
    private final static QName _SenhaAlteradaAposGeracaoLinkResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "senhaAlteradaAposGeracaoLinkResponse");
    private final static QName _ConsultarParcelaVendaResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarParcelaVendaResponse");
    private final static QName _LiberarCobrancaBaixaProtheusResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "liberarCobrancaBaixaProtheusResponse");
    private final static QName _ConsultarClienteJson_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarClienteJson");
    private final static QName _EnviarMensagemAoUsuarioUsername_QNAME = new QName("http://webservice.basico.comuns.negocio/", "enviarMensagemAoUsuarioUsername");
    private final static QName _AlterarEndereco_QNAME = new QName("http://webservice.basico.comuns.negocio/", "alterarEndereco");
    private final static QName _ConsultarEstadoResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarEstadoResponse");
    private final static QName _ConsultarEstatisticaSolicitacaoResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarEstatisticaSolicitacaoResponse");
    private final static QName _MarcarStatusBG_QNAME = new QName("http://webservice.basico.comuns.negocio/", "marcarStatusBG");
    private final static QName _ListaProdutosAtestado_QNAME = new QName("http://webservice.basico.comuns.negocio/", "listaProdutosAtestado");
    private final static QName _ConsultarAlunos_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarAlunos");
    private final static QName _ConsultarClientesPeloNomeResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarClientesPeloNomeResponse");
    private final static QName _BuscarEstatisticasMovideskResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "buscarEstatisticasMovideskResponse");
    private final static QName _PersistirClienteJSON_QNAME = new QName("http://webservice.basico.comuns.negocio/", "persistirClienteJSON");
    private final static QName _ConsultarMeioRecuperarLoginResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarMeioRecuperarLoginResponse");
    private final static QName _AtualizarDadosGerenciaisPorMesResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "atualizarDadosGerenciaisPorMesResponse");
    private final static QName _AlterarSenhaUsuarioEmail_QNAME = new QName("http://webservice.basico.comuns.negocio/", "alterarSenhaUsuarioEmail");
    private final static QName _ConsultarClientesTreinoResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarClientesTreinoResponse");
    private final static QName _BuscarLocaisAcesso_QNAME = new QName("http://webservice.basico.comuns.negocio/", "buscarLocaisAcesso");
    private final static QName _ConsultarAtestadoClienteResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarAtestadoClienteResponse");
    private final static QName _ConsultarClientePorNomeCpfResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarClientePorNomeCpfResponse");
    private final static QName _EnderecosGeolocalizarResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "enderecosGeolocalizarResponse");
    private final static QName _ConsultarConsultoresResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarConsultoresResponse");
    private final static QName _PersistirVendaAvulsaJSONResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "persistirVendaAvulsaJSONResponse");
    private final static QName _ConsultarJustificativasOperacaoResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarJustificativasOperacaoResponse");
    private final static QName _ConsultarParcelasEmAbertoResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarParcelasEmAbertoResponse");
    private final static QName _ConsultarParcelasEmAberto_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarParcelasEmAberto");
    private final static QName _ConsultarGrupoDeRiscoPelaMatriculaResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarGrupoDeRiscoPelaMatriculaResponse");
    private final static QName _ConsultarResumoPeriodo_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarResumoPeriodo");
    private final static QName _ConsultarConsultores_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarConsultores");
    private final static QName _ListaAlunoPontosApp_QNAME = new QName("http://webservice.basico.comuns.negocio/", "listaAlunoPontosApp");
    private final static QName _AlterarEmailResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "alterarEmailResponse");
    private final static QName _ConsultarInfoFinanceiras_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarInfoFinanceiras");
    private final static QName _ObterConfiguracoesGameResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "obterConfiguracoesGameResponse");
    private final static QName _ConsultarInfoFinanceirasResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarInfoFinanceirasResponse");
    private final static QName _AnaliseVendaDuracaoResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "analiseVendaDuracaoResponse");
    private final static QName _ObterConfiguracoesGame_QNAME = new QName("http://webservice.basico.comuns.negocio/", "obterConfiguracoesGame");
    private final static QName _AtualizarDadosGerenciaisDia_QNAME = new QName("http://webservice.basico.comuns.negocio/", "atualizarDadosGerenciaisDia");
    private final static QName _ValidarDadosOperacaoContrato_QNAME = new QName("http://webservice.basico.comuns.negocio/", "validarDadosOperacaoContrato");
    private final static QName _AtualizarFotoPerfilAluno_QNAME = new QName("http://webservice.basico.comuns.negocio/", "atualizarFotoPerfilAluno");
    private final static QName _AlterarSenhaUsuario_QNAME = new QName("http://webservice.basico.comuns.negocio/", "alterarSenhaUsuario");
    private final static QName _EnviarEmailTokenAppResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "enviarEmailTokenAppResponse");
    private final static QName _LiberarCobrancaBaixaProtheus_QNAME = new QName("http://webservice.basico.comuns.negocio/", "liberarCobrancaBaixaProtheus");
    private final static QName _UpdateGeolocEmpresa_QNAME = new QName("http://webservice.basico.comuns.negocio/", "updateGeolocEmpresa");
    private final static QName _AlterarEmail_QNAME = new QName("http://webservice.basico.comuns.negocio/", "alterarEmail");
    private final static QName _ObterCliente_QNAME = new QName("http://webservice.basico.comuns.negocio/", "obterCliente");
    private final static QName _InserirWebHookClienteRD_QNAME = new QName("http://webservice.basico.comuns.negocio/", "inserirWebHookClienteRD");
    private final static QName _ConsultarProdutosClienteResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarProdutosClienteResponse");
    private final static QName _ObterQuestionarioResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "obterQuestionarioResponse");
    private final static QName _AlterarSenhaUsuarioEmailResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "alterarSenhaUsuarioEmailResponse");
    private final static QName _ConsultaGenericaPaginada_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultaGenericaPaginada");
    private final static QName _ConsultarAtestadoCliente_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarAtestadoCliente");
    private final static QName _ListaAlunoPontosData_QNAME = new QName("http://webservice.basico.comuns.negocio/", "listaAlunoPontosData");
    private final static QName _PersistirTokenLeadRD_QNAME = new QName("http://webservice.basico.comuns.negocio/", "persistirTokenLeadRD");
    private final static QName _NotificarRecursoEmpresaGeolocalizacao_QNAME = new QName("http://webservice.basico.comuns.negocio/", "notificarRecursoEmpresaGeolocalizacao");
    private final static QName _GerarUsuarioMovelColaborador_QNAME = new QName("http://webservice.basico.comuns.negocio/", "gerarUsuarioMovelColaborador");
    private final static QName _AnaliseVendaDuracao_QNAME = new QName("http://webservice.basico.comuns.negocio/", "analiseVendaDuracao");
    private final static QName _ConsultarDadosBITreino_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarDadosBITreino");
    private final static QName _ObterClienteResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "obterClienteResponse");
    private final static QName _PersistirProdutoJSON_QNAME = new QName("http://webservice.basico.comuns.negocio/", "persistirProdutoJSON");
    private final static QName _CheckInGymPassResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "checkInGymPassResponse");
    private final static QName _AlterarSenhaUsuarioMovel_QNAME = new QName("http://webservice.basico.comuns.negocio/", "alterarSenhaUsuarioMovel");
    private final static QName _ObterTiposColaboradoes_QNAME = new QName("http://webservice.basico.comuns.negocio/", "obterTiposColaboradoes");
    private final static QName _ConsultarClientes_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarClientes");
    private final static QName _ConsultarAcessosListaDia_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarAcessosListaDia");
    private final static QName _ConsultarModalidadesAtivosForaTreino_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarModalidadesAtivosForaTreino");
    private final static QName _ConsultarMensagensCliente_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarMensagensCliente");
    private final static QName _ConsultarClientesAtivosForaTreino_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarClientesAtivosForaTreino");
    private final static QName _RecuperarUsuarioMovelResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "recuperarUsuarioMovelResponse");
    private final static QName _ConsultarClientePorNomeCpf_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarClientePorNomeCpf");
    private final static QName _PersistirLeadGenericoResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "persistirLeadGenericoResponse");
    private final static QName _Profissoes_QNAME = new QName("http://webservice.basico.comuns.negocio/", "profissoes");
    private final static QName _EnviarCodigoVerificacao_QNAME = new QName("http://webservice.basico.comuns.negocio/", "enviarCodigoVerificacao");
    private final static QName _ConsultarMetasCRMResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarMetasCRMResponse");
    private final static QName _ConsistirException_QNAME = new QName("http://webservice.basico.comuns.negocio/", "ConsistirException");
    private final static QName _ConsultarAcessosDia_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarAcessosDia");
    private final static QName _ConsultarFotoKeyPessoaResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarFotoKeyPessoaResponse");
    private final static QName _ConsultarIntegracaoFeraJson_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarIntegracaoFeraJson");
    private final static QName _ConsultarCliente_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarCliente");
    private final static QName _AcessTokenValidoResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "acessTokenValidoResponse");
    private final static QName _ConsultarParcelaVenda_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarParcelaVenda");
    private final static QName _ValidarCodigoVerificacao_QNAME = new QName("http://webservice.basico.comuns.negocio/", "validarCodigoVerificacao");
    private final static QName _ConsultarNrAlunosAtivosForaTreinoResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarNrAlunosAtivosForaTreinoResponse");
    private final static QName _ConsultarAutorizacaoCobrancaResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarAutorizacaoCobrancaResponse");
    private final static QName _AtualizarDadosGerenciais_QNAME = new QName("http://webservice.basico.comuns.negocio/", "atualizarDadosGerenciais");
    private final static QName _CheckInGymPass_QNAME = new QName("http://webservice.basico.comuns.negocio/", "checkInGymPass");
    private final static QName _ConsultarFeedGestao_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarFeedGestao");
    private final static QName _ConsultarLocalAcessoPorNFCResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarLocalAcessoPorNFCResponse");
    private final static QName _ObterTiposColaboradoesResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "obterTiposColaboradoesResponse");
    private final static QName _MarcarStatusBGResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "marcarStatusBGResponse");
    private final static QName _EnviarEmailGenerico_QNAME = new QName("http://webservice.basico.comuns.negocio/", "enviarEmailGenerico");
    private final static QName _AlterarSenhaUsuarioResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "alterarSenhaUsuarioResponse");
    private final static QName _BuscaIDSecret_QNAME = new QName("http://webservice.basico.comuns.negocio/", "buscaIDSecret");
    private final static QName _DeletarContratoImportacaoJSON_QNAME = new QName("http://webservice.basico.comuns.negocio/", "deletarContratoImportacaoJSON");
    private final static QName _ObterEnderecoEmpresa_QNAME = new QName("http://webservice.basico.comuns.negocio/", "obterEnderecoEmpresa");
    private final static QName _ConsultarEmpresasResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarEmpresasResponse");
    private final static QName _EnviarEmailAngularResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "enviarEmailAngularResponse");
    private final static QName _ClassificacoesResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "classificacoesResponse");
    private final static QName _ConsultarClienteSinteticoPorEmailPessoaResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarClienteSinteticoPorEmailPessoaResponse");
    private final static QName _ObterEnderecoCodigoResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "obterEnderecoCodigoResponse");
    private final static QName _ConsultarDadosGame_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarDadosGame");
    private final static QName _ValidarAlunoProdutoVigente_QNAME = new QName("http://webservice.basico.comuns.negocio/", "validarAlunoProdutoVigente");
    private final static QName _EnviarEmailColaborador_QNAME = new QName("http://webservice.basico.comuns.negocio/", "enviarEmailColaborador");
    private final static QName _PersistirPagamentosContratoJSON_QNAME = new QName("http://webservice.basico.comuns.negocio/", "persistirPagamentosContratoJSON");
    private final static QName _ConsultarMensagensClienteResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarMensagensClienteResponse");
    private final static QName _EnviarMensagemAoUsuarioResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "enviarMensagemAoUsuarioResponse");
    private final static QName _BuscaIDSecretResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "buscaIDSecretResponse");
    private final static QName _PersistirPagamentosContratoJSONResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "persistirPagamentosContratoJSONResponse");
    private final static QName _LancarProdutoClienteVAlorResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "lancarProdutoClienteVAlorResponse");
    private final static QName _ConsultarAmbientes_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarAmbientes");
    private final static QName _ConsultaGenericaResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultaGenericaResponse");
    private final static QName _PersistirPagamentosVendaAvulsaJSONResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "persistirPagamentosVendaAvulsaJSONResponse");
    private final static QName _GravarDadosOperacaoContratoResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "gravarDadosOperacaoContratoResponse");
    private final static QName _ObterEnderecosResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "obterEnderecosResponse");
    private final static QName _ConsultarAmbientesResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarAmbientesResponse");
    private final static QName _ConsultarContratosRenovar_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarContratosRenovar");
    private final static QName _ConsultarConfiguracaoENotas_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarConfiguracaoENotas");
    private final static QName _ConsultarAutorizacaoCobranca_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarAutorizacaoCobranca");
    private final static QName _ConsultarColaboradoresResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarColaboradoresResponse");
    private final static QName _ConsultarFaturamentoDuracaoResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarFaturamentoDuracaoResponse");
    private final static QName _ConsultarModalidadesAtivosForaTreinoResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarModalidadesAtivosForaTreinoResponse");
    private final static QName _UrlFotoEmpresa_QNAME = new QName("http://webservice.basico.comuns.negocio/", "urlFotoEmpresa");
    private final static QName _AtualizarFotoPerfilAlunoResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "atualizarFotoPerfilAlunoResponse");
    private final static QName _Exception_QNAME = new QName("http://webservice.basico.comuns.negocio/", "Exception");
    private final static QName _AtualizarDadosGerenciaisResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "atualizarDadosGerenciaisResponse");
    private final static QName _ConsultarColetoresResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarColetoresResponse");
    private final static QName _AtualizarBoletoPJBank_QNAME = new QName("http://webservice.basico.comuns.negocio/", "atualizarBoletoPJBank");
    private final static QName _AlterarTelefone_QNAME = new QName("http://webservice.basico.comuns.negocio/", "alterarTelefone");
    private final static QName _MarcarComoLidaFeedGestaoResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "marcarComoLidaFeedGestaoResponse");
    private final static QName _ConsultarCupomDesconto_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarCupomDesconto");
    private final static QName _ValidarDadosOperacaoContratoResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "validarDadosOperacaoContratoResponse");
    private final static QName _LancarProdutoCliente_QNAME = new QName("http://webservice.basico.comuns.negocio/", "lancarProdutoCliente");
    private final static QName _ConsultarClientesPeloNomeNoLimitsResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarClientesPeloNomeNoLimitsResponse");
    private final static QName _LancarProdutoClienteVAlor_QNAME = new QName("http://webservice.basico.comuns.negocio/", "lancarProdutoClienteVAlor");
    private final static QName _ValidarAlunoProdutoVigenteResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "validarAlunoProdutoVigenteResponse");
    private final static QName _LogarUsuarioMovelResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "logarUsuarioMovelResponse");
    private final static QName _PersistirOperacoesContratoJSONResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "persistirOperacoesContratoJSONResponse");
    private final static QName _ObterQuestionario_QNAME = new QName("http://webservice.basico.comuns.negocio/", "obterQuestionario");
    private final static QName _ResponderQuestionario_QNAME = new QName("http://webservice.basico.comuns.negocio/", "responderQuestionario");
    private final static QName _ConsultarProfessoresPeloNome_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarProfessoresPeloNome");
    private final static QName _PersistirCodeLeadRDResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "persistirCodeLeadRDResponse");
    private final static QName _PersistirCancelarContratoJSONResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "persistirCancelarContratoJSONResponse");
    private final static QName _ConsultarEmpresasSimplesResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarEmpresasSimplesResponse");
    private final static QName _ObterContratoOperacaoResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "obterContratoOperacaoResponse");
    private final static QName _PersistirVendaAvulsaJSON_QNAME = new QName("http://webservice.basico.comuns.negocio/", "persistirVendaAvulsaJSON");
    private final static QName _PersistirAcessosClienteJSON_QNAME = new QName("http://webservice.basico.comuns.negocio/", "persistirAcessosClienteJSON");
    private final static QName _SituacaoUsuarioResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "situacaoUsuarioResponse");
    private final static QName _ConsultarColaboradores_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarColaboradores");
    private final static QName _ConsultarClientesForaTreino_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarClientesForaTreino");
    private final static QName _GerarVinculoProfessorAlunoResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "gerarVinculoProfessorAlunoResponse");
    private final static QName _ConsultarContratosClienteResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarContratosClienteResponse");
    private final static QName _BuscarClientesPesquisaTW_QNAME = new QName("http://webservice.basico.comuns.negocio/", "buscarClientesPesquisaTW");
    private final static QName _VerificarUsuarioMovel_QNAME = new QName("http://webservice.basico.comuns.negocio/", "verificarUsuarioMovel");
    private final static QName _NomenclaturaVendaCredito_QNAME = new QName("http://webservice.basico.comuns.negocio/", "nomenclaturaVendaCredito");
    private final static QName _ConsultarClienteJsonResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarClienteJsonResponse");
    private final static QName _DownloadBoleto_QNAME = new QName("http://webservice.basico.comuns.negocio/", "downloadBoleto");
    private final static QName _ConsultarQuantidadeAcessosClientesAgrupadosDiaResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarQuantidadeAcessosClientesAgrupadosDiaResponse");
    private final static QName _LancarConviteResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "lancarConviteResponse");
    private final static QName _MarcarAvaliacaoFeedGestao_QNAME = new QName("http://webservice.basico.comuns.negocio/", "marcarAvaliacaoFeedGestao");
    private final static QName _RecuperarLoginResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "recuperarLoginResponse");
    private final static QName _AlterarSenhaUsuarioMovelResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "alterarSenhaUsuarioMovelResponse");
    private final static QName _ConsultarProdutosCliente_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarProdutosCliente");
    private final static QName _ProfissoesResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "profissoesResponse");
    private final static QName _RetornoTrancamento_QNAME = new QName("http://webservice.basico.comuns.negocio/", "retornoTrancamento");
    private final static QName _ListaAlunoPontos_QNAME = new QName("http://webservice.basico.comuns.negocio/", "listaAlunoPontos");
    private final static QName _ConsultarCidade_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarCidade");
    private final static QName _GerarVinculoProfessorAluno_QNAME = new QName("http://webservice.basico.comuns.negocio/", "gerarVinculoProfessorAluno");
    private final static QName _ConsultarJustificativasOperacao_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarJustificativasOperacao");
    private final static QName _ListaClientesEstacionamentoSelfit_QNAME = new QName("http://webservice.basico.comuns.negocio/", "listaClientesEstacionamentoSelfit");
    private final static QName _ValidarUsuarioTelefone_QNAME = new QName("http://webservice.basico.comuns.negocio/", "validarUsuarioTelefone");
    private final static QName _DeletarPagamentosContratoImportacaoJSON_QNAME = new QName("http://webservice.basico.comuns.negocio/", "deletarPagamentosContratoImportacaoJSON");
    private final static QName _DeletarPagamentosContratoImportacaoJSONResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "deletarPagamentosContratoImportacaoJSONResponse");
    private final static QName _ConsultarClientesForaTreinoResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarClientesForaTreinoResponse");
    private final static QName _ConsultarClienteSinteticoResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarClienteSinteticoResponse");
    private final static QName _BuscarLocaisAcessoResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "buscarLocaisAcessoResponse");
    private final static QName _GerarVendaCreditosResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "gerarVendaCreditosResponse");
    private final static QName _PersistirClienteSite_QNAME = new QName("http://webservice.basico.comuns.negocio/", "persistirClienteSite");
    private final static QName _ConsultarIntegracaoFeraJsonResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarIntegracaoFeraJsonResponse");
    private final static QName _EnviarNotificacaoUsuarioResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "enviarNotificacaoUsuarioResponse");
    private final static QName _MarcarAvaliacaoFeedGestaoResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "marcarAvaliacaoFeedGestaoResponse");
    private final static QName _ObterDadosContratoPorDuracaoResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "obterDadosContratoPorDuracaoResponse");
    private final static QName _EnderecosGeolocalizarAposHabilitarChaveResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "enderecosGeolocalizarAposHabilitarChaveResponse");
    private final static QName _RecuperarUsuarioMovel_QNAME = new QName("http://webservice.basico.comuns.negocio/", "recuperarUsuarioMovel");
    private final static QName _LancarProdutoAtestadoResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "lancarProdutoAtestadoResponse");
    private final static QName _IncluirAssinaturaDigitalResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "incluirAssinaturaDigitalResponse");
    private final static QName _ConsultarClienteSintetico_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarClienteSintetico");
    private final static QName _ConsultarFrequenciaAluno_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarFrequenciaAluno");
    private final static QName _EnviarCodigoVerificacaoResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "enviarCodigoVerificacaoResponse");
    private final static QName _AtualizarGeolocalizacaoResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "atualizarGeolocalizacaoResponse");
    private final static QName _ConsultarGrupoDeRiscoPelaMatricula_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarGrupoDeRiscoPelaMatricula");
    private final static QName _EnderecosGeolocalizar_QNAME = new QName("http://webservice.basico.comuns.negocio/", "enderecosGeolocalizar");
    private final static QName _PersistirColaborador_QNAME = new QName("http://webservice.basico.comuns.negocio/", "persistirColaborador");
    private final static QName _PersistirColaboradorResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "persistirColaboradorResponse");
    private final static QName _LancarConvite_QNAME = new QName("http://webservice.basico.comuns.negocio/", "lancarConvite");
    private final static QName _AlterarUsuarioMovelAlunoResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "alterarUsuarioMovelAlunoResponse");
    private final static QName _PersistirContratoJSON_QNAME = new QName("http://webservice.basico.comuns.negocio/", "persistirContratoJSON");
    private final static QName _AlterarUsuarioMovelAluno_QNAME = new QName("http://webservice.basico.comuns.negocio/", "alterarUsuarioMovelAluno");
    private final static QName _ConsultarColaboradoresTreinoResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarColaboradoresTreinoResponse");
    private final static QName _ConsultarAcessosListaDiaResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarAcessosListaDiaResponse");
    private final static QName _ConsultarFeedGestaoResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarFeedGestaoResponse");
    private final static QName _ConsultarDadosSolicitarAtendimentoResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarDadosSolicitarAtendimentoResponse");
    private final static QName _ValidarUsuarioTelefoneResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "validarUsuarioTelefoneResponse");
    private final static QName _ConsultarModalidadesResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarModalidadesResponse");
    private final static QName _GerarVendaCreditos_QNAME = new QName("http://webservice.basico.comuns.negocio/", "gerarVendaCreditos");
    private final static QName _ConsultarClienteSinteticoPorEmailPessoa_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarClienteSinteticoPorEmailPessoa");
    private final static QName _ConsultarClientePorCodigoAcesso_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarClientePorCodigoAcesso");
    private final static QName _EnviarMensagemAoUsuario_QNAME = new QName("http://webservice.basico.comuns.negocio/", "enviarMensagemAoUsuario");
    private final static QName _ObterValorProdutoCfgEmpresaResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "obterValorProdutoCfgEmpresaResponse");
    private final static QName _ConsultarParcelasVencidasResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarParcelasVencidasResponse");
    private final static QName _CategoriasResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "categoriasResponse");
    private final static QName _AtualizarCadastroPersonalResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "atualizarCadastroPersonalResponse");
    private final static QName _AtualizarBoletoPJBankResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "atualizarBoletoPJBankResponse");
    private final static QName _ConsultarProfessoresPeloNomeResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarProfessoresPeloNomeResponse");
    private final static QName _DeletarClienteImportacaoJSON_QNAME = new QName("http://webservice.basico.comuns.negocio/", "deletarClienteImportacaoJSON");
    private final static QName _SolicitacoesEmAbertoResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "solicitacoesEmAbertoResponse");
    private final static QName _ConsultarQuantidadeAcessosClientesAgrupadosDia_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarQuantidadeAcessosClientesAgrupadosDia");
    private final static QName _Categorias_QNAME = new QName("http://webservice.basico.comuns.negocio/", "categorias");
    private final static QName _ConsultarAlunosResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarAlunosResponse");
    private final static QName _ConsultarFotoKeyPessoa_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarFotoKeyPessoa");
    private final static QName _GravarBaixaProtheus_QNAME = new QName("http://webservice.basico.comuns.negocio/", "gravarBaixaProtheus");
    private final static QName _ConsultarNrDiasParcelaEmAberto_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarNrDiasParcelaEmAberto");
    private final static QName _AtualizarFotoPerfilAlunoCPF_QNAME = new QName("http://webservice.basico.comuns.negocio/", "atualizarFotoPerfilAlunoCPF");
    private final static QName _ApiWeHelp_QNAME = new QName("http://webservice.basico.comuns.negocio/", "apiWeHelp");
    private final static QName _VerificarUsuarioMovelResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "verificarUsuarioMovelResponse");
    private final static QName _PersistirLeadRDResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "persistirLeadRDResponse");
    private final static QName _ConsultarParcelasVencidasSESCResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarParcelasVencidasSESCResponse");
    private final static QName _ConsultarColaboradoresTreino_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarColaboradoresTreino");
    private final static QName _GravarResposta_QNAME = new QName("http://webservice.basico.comuns.negocio/", "gravarResposta");
    private final static QName _ConsultarOperacoesExcecoesResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarOperacoesExcecoesResponse");
    private final static QName _ObterEnderecoEmpresaResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "obterEnderecoEmpresaResponse");
    private final static QName _PersistirContratoJSONResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "persistirContratoJSONResponse");
    private final static QName _ConsultarPrimeiraParcelaContratoResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarPrimeiraParcelaContratoResponse");
    private final static QName _ConsultarClientesPelaMatriculaResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarClientesPelaMatriculaResponse");
    private final static QName _DeletarContratoImportacaoJSONResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "deletarContratoImportacaoJSONResponse");
    private final static QName _ObterDuracaoCreditosEmpresaResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "obterDuracaoCreditosEmpresaResponse");
    private final static QName _ConsultarLocaisAcesso_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarLocaisAcesso");
    private final static QName _EnviarEmailAngular_QNAME = new QName("http://webservice.basico.comuns.negocio/", "enviarEmailAngular");
    private final static QName _ConfiguracaoTotem_QNAME = new QName("http://webservice.basico.comuns.negocio/", "configuracaoTotem");
    private final static QName _ValidarCodigoVerificacaoResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "validarCodigoVerificacaoResponse");
    private final static QName _AlterarTelefoneResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "alterarTelefoneResponse");
    private final static QName _VerificarStatusLeadBuzzResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "verificarStatusLeadBuzzResponse");
    private final static QName _SenhaAlteradaAposGeracaoLink_QNAME = new QName("http://webservice.basico.comuns.negocio/", "senhaAlteradaAposGeracaoLink");
    private final static QName _SolicitacoesConcluidasResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "solicitacoesConcluidasResponse");
    private final static QName _Classificacoes_QNAME = new QName("http://webservice.basico.comuns.negocio/", "classificacoes");
    private final static QName _PersistirAcessosClienteJSONResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "persistirAcessosClienteJSONResponse");
    private final static QName _ConsultarParcelasVencidasSESC_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarParcelasVencidasSESC");
    private final static QName _PersistirLeadRD_QNAME = new QName("http://webservice.basico.comuns.negocio/", "persistirLeadRD");
    private final static QName _NomenclaturaVendaCreditoResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "nomenclaturaVendaCreditoResponse");
    private final static QName _AtualizarFotoPerfilAlunoCPFResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "atualizarFotoPerfilAlunoCPFResponse");
    private final static QName _ListaAlunoPontosAppResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "listaAlunoPontosAppResponse");
    private final static QName _RetornoTrancamentoResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "retornoTrancamentoResponse");
    private final static QName _ObterEnderecoCodigo_QNAME = new QName("http://webservice.basico.comuns.negocio/", "obterEnderecoCodigo");
    private final static QName _InserirWebHookClienteRDResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "inserirWebHookClienteRDResponse");
    private final static QName _ConsultarClienteComTelefoneResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarClienteComTelefoneResponse");
    private final static QName _PersistirPagamentosVendaAvulsaJSON_QNAME = new QName("http://webservice.basico.comuns.negocio/", "persistirPagamentosVendaAvulsaJSON");
    private final static QName _ConsultarClienteResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarClienteResponse");
    private final static QName _ConsultarAcessosDiaResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarAcessosDiaResponse");
    private final static QName _AlterarEnderecoResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "alterarEnderecoResponse");
    private final static QName _ObterPontosPorCliente_QNAME = new QName("http://webservice.basico.comuns.negocio/", "obterPontosPorCliente");
    private final static QName _ConsultarCodigoAcesso_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarCodigoAcesso");
    private final static QName _ConsultarContratosRenovarResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarContratosRenovarResponse");
    private final static QName _ConsultarCidadeResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarCidadeResponse");
    private final static QName _ConsultarContratosCliente_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarContratosCliente");
    private final static QName _MarcarComoLidaFeedGestao_QNAME = new QName("http://webservice.basico.comuns.negocio/", "marcarComoLidaFeedGestao");
    private final static QName _AlterarUsuarioMovelProfessor_QNAME = new QName("http://webservice.basico.comuns.negocio/", "alterarUsuarioMovelProfessor");
    private final static QName _AlterarDadosPessoaisClienteSite_QNAME = new QName("http://webservice.basico.comuns.negocio/", "alterarDadosPessoaisClienteSite");
    private final static QName _ConsultarClienteComTelefone_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarClienteComTelefone");
    private final static QName _BuscarEstatisticasMovidesk_QNAME = new QName("http://webservice.basico.comuns.negocio/", "buscarEstatisticasMovidesk");
    private final static QName _AlterarUsuarioMovelProfessorResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "alterarUsuarioMovelProfessorResponse");
    private final static QName _GerarUsuarioMovelColaboradorResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "gerarUsuarioMovelColaboradorResponse");
    private final static QName _GravarBaixaProtheusResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "gravarBaixaProtheusResponse");
    private final static QName _LogarUsuarioMovel_QNAME = new QName("http://webservice.basico.comuns.negocio/", "logarUsuarioMovel");
    private final static QName _LancarProdutoAtestado_QNAME = new QName("http://webservice.basico.comuns.negocio/", "lancarProdutoAtestado");
    private final static QName _ConsultarClientesPeloNome_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarClientesPeloNome");
    private final static QName _ConsultarDadosCliente_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarDadosCliente");
    private final static QName _ListaAlunoPontosResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "listaAlunoPontosResponse");
    private final static QName _ObterVencimentosContratosResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "obterVencimentosContratosResponse");
    private final static QName _ListaProdutosServicosResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "listaProdutosServicosResponse");
    private final static QName _NotificarRecursoEmpresaGeolocalizacaoResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "notificarRecursoEmpresaGeolocalizacaoResponse");
    private final static QName _ConsultarCodigoAcessoResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarCodigoAcessoResponse");
    private final static QName _ConsultarPrimeiraParcelaContrato_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarPrimeiraParcelaContrato");
    private final static QName _AtualizarGeolocalizacao_QNAME = new QName("http://webservice.basico.comuns.negocio/", "atualizarGeolocalizacao");
    private final static QName _ListaAlunoPontosDataResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "listaAlunoPontosDataResponse");
    private final static QName _ObterValorVendasResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "obterValorVendasResponse");
    private final static QName _SalvarCampanhaResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "salvarCampanhaResponse");
    private final static QName _GravarRespostaResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "gravarRespostaResponse");
    private final static QName _ConsultarModalidadesEmpresa_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarModalidadesEmpresa");
    private final static QName _ValidarConvitesResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "validarConvitesResponse");
    private final static QName _ConsultarDetalhesDF_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarDetalhesDF");
    private final static QName _ConsultarParcelasClienteResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarParcelasClienteResponse");
    private final static QName _ConsultarClienteNaRedeEmpresaResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarClienteNaRedeEmpresaResponse");
    private final static QName _PersistirLeadBuzzResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "persistirLeadBuzzResponse");
    private final static QName _ValidarUsuarioResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "validarUsuarioResponse");
    private final static QName _ConsultarDadosGameResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarDadosGameResponse");
    private final static QName _ConsultarDadosOperacaoContratoResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarDadosOperacaoContratoResponse");
    private final static QName _ConsultarEmpresasSimples_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarEmpresasSimples");
    private final static QName _ConsultarOperacoesExcecoes_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarOperacoesExcecoes");
    private final static QName _ValidarProdutoVigenteResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "validarProdutoVigenteResponse");
    private final static QName _ConsultarModalidades_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarModalidades");
    private final static QName _AtualizarCadastroPersonal_QNAME = new QName("http://webservice.basico.comuns.negocio/", "atualizarCadastroPersonal");
    private final static QName _PersistirLeadBuzz_QNAME = new QName("http://webservice.basico.comuns.negocio/", "persistirLeadBuzz");
    private final static QName _ConsultarParcelasVencidas_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarParcelasVencidas");
    private final static QName _ObterConfiguracaoEmailPadraoResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "obterConfiguracaoEmailPadraoResponse");
    private final static QName _PersistirCancelarContratoJSON_QNAME = new QName("http://webservice.basico.comuns.negocio/", "persistirCancelarContratoJSON");
    private final static QName _ConsultarEmpresas_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarEmpresas");
    private final static QName _ConsultarEstatisticaSolicitacao_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarEstatisticaSolicitacao");
    private final static QName _ConsultarDetalhesDFResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarDetalhesDFResponse");
    private final static QName _ConfiguracaoTotemResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "configuracaoTotemResponse");
    private final static QName _ListaClientesEstacionamentoSelfitResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "listaClientesEstacionamentoSelfitResponse");
    private final static QName _ConsultarClientesTreino_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarClientesTreino");
    private final static QName _ListaProdutosAtestadoResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "listaProdutosAtestadoResponse");
    private final static QName _IncluirAssinaturaDigital_QNAME = new QName("http://webservice.basico.comuns.negocio/", "incluirAssinaturaDigital");
    private final static QName _ValidarConvites_QNAME = new QName("http://webservice.basico.comuns.negocio/", "validarConvites");
    private final static QName _SituacaoUsuario_QNAME = new QName("http://webservice.basico.comuns.negocio/", "situacaoUsuario");
    private final static QName _EnviarNotificacaoUsuario_QNAME = new QName("http://webservice.basico.comuns.negocio/", "enviarNotificacaoUsuario");
    private final static QName _ConsultarColetores_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarColetores");
    private final static QName _GravarConfiguracoesGameResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "gravarConfiguracoesGameResponse");
    private final static QName _BuscarClientesPesquisaTWResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "buscarClientesPesquisaTWResponse");
    private final static QName _ObterValorVendas_QNAME = new QName("http://webservice.basico.comuns.negocio/", "obterValorVendas");
    private final static QName _ValidarUsuario_QNAME = new QName("http://webservice.basico.comuns.negocio/", "validarUsuario");
    private final static QName _RecuperarLogin_QNAME = new QName("http://webservice.basico.comuns.negocio/", "recuperarLogin");
    private final static QName _ConsultarClientesResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarClientesResponse");
    private final static QName _SalvarCampanha_QNAME = new QName("http://webservice.basico.comuns.negocio/", "salvarCampanha");
    private final static QName _EnviarEmailColaboradorResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "enviarEmailColaboradorResponse");
    private final static QName _GravarDadosOperacaoContrato_QNAME = new QName("http://webservice.basico.comuns.negocio/", "gravarDadosOperacaoContrato");
    private final static QName _ResponderQuestionarioResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "responderQuestionarioResponse");
    private final static QName _ListaProdutosServicos_QNAME = new QName("http://webservice.basico.comuns.negocio/", "listaProdutosServicos");
    private final static QName _PersistirLeadGenerico_QNAME = new QName("http://webservice.basico.comuns.negocio/", "persistirLeadGenerico");
    private final static QName _ConsultarFrequenciaAlunoResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarFrequenciaAlunoResponse");
    private final static QName _AlterarUsuarioMovelAlunoNovo_QNAME = new QName("http://webservice.basico.comuns.negocio/", "alterarUsuarioMovelAlunoNovo");
    private final static QName _AcessTokenValido_QNAME = new QName("http://webservice.basico.comuns.negocio/", "acessTokenValido");
    private final static QName _ConsultarClientePorMatriculaExternaImportacaoResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarClientePorMatriculaExternaImportacaoResponse");
    private final static QName _ConsultarMeioRecuperarLogin_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarMeioRecuperarLogin");
    private final static QName _ObterConfiguracaoEmailPadrao_QNAME = new QName("http://webservice.basico.comuns.negocio/", "obterConfiguracaoEmailPadrao");
    private final static QName _ConsultarConfiguracaoENotasResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarConfiguracaoENotasResponse");
    private final static QName _ObterValorProdutoCfgEmpresa_QNAME = new QName("http://webservice.basico.comuns.negocio/", "obterValorProdutoCfgEmpresa");
    private final static QName _PersistirLogAuditoria_QNAME = new QName("http://webservice.basico.comuns.negocio/", "persistirLogAuditoria");
    private final static QName _ConsultarLocalAcessoPorNFC_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarLocalAcessoPorNFC");
    private final static QName _ConsultarConsultoresGame_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarConsultoresGame");
    private final static QName _PersistirClienteSiteResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "persistirClienteSiteResponse");
    private final static QName _AtualizarDadosGerenciaisDiaResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "atualizarDadosGerenciaisDiaResponse");
    private final static QName _VerificarStatusLeadBuzz_QNAME = new QName("http://webservice.basico.comuns.negocio/", "verificarStatusLeadBuzz");
    private final static QName _ConsultaGenerica_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultaGenerica");
    private final static QName _ConsultarEstado_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarEstado");
    private final static QName _AlterarDadosPessoaisClienteSiteResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "alterarDadosPessoaisClienteSiteResponse");
    private final static QName _ObterDadosContratoPorDuracao_QNAME = new QName("http://webservice.basico.comuns.negocio/", "obterDadosContratoPorDuracao");
    private final static QName _ConsultarNrAlunosAtivosForaTreino_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarNrAlunosAtivosForaTreino");
    private final static QName _ConsultarNrDiasParcelaEmAbertoResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarNrDiasParcelaEmAbertoResponse");
    private final static QName _EnviarMensagemAoUsuarioUsernameResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "enviarMensagemAoUsuarioUsernameResponse");
    private final static QName _ConsultarClientesRenovaramOuNao_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarClientesRenovaramOuNao");
    private final static QName _ValidarProdutoVigente_QNAME = new QName("http://webservice.basico.comuns.negocio/", "validarProdutoVigente");
    private final static QName _ConsultarClienteNaRedeEmpresa_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarClienteNaRedeEmpresa");
    private final static QName _EnderecosGeolocalizarAposHabilitarChave_QNAME = new QName("http://webservice.basico.comuns.negocio/", "enderecosGeolocalizarAposHabilitarChave");
    private final static QName _ObterDuracaoCreditosEmpresa_QNAME = new QName("http://webservice.basico.comuns.negocio/", "obterDuracaoCreditosEmpresa");
    private final static QName _SolicitacoesEmAberto_QNAME = new QName("http://webservice.basico.comuns.negocio/", "solicitacoesEmAberto");
    private final static QName _EnviarEmailGenericoResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "enviarEmailGenericoResponse");
    private final static QName _ConsultarClientesRenovaramOuNaoResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarClientesRenovaramOuNaoResponse");
    private final static QName _UrlFotoEmpresaResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "urlFotoEmpresaResponse");
    private final static QName _UpdateGeolocEmpresaResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "updateGeolocEmpresaResponse");
    private final static QName _ConsultaGenericaPaginadaResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultaGenericaPaginadaResponse");
    private final static QName _ConsultarClientesPelaMatricula_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarClientesPelaMatricula");
    private final static QName _ValidarUsuarioEmail_QNAME = new QName("http://webservice.basico.comuns.negocio/", "validarUsuarioEmail");
    private final static QName _ConsultarClientesPeloNomeNoLimits_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarClientesPeloNomeNoLimits");
    private final static QName _ConsultarConsultoresGameResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarConsultoresGameResponse");
    private final static QName _GerarUsuarioMovelAluno_QNAME = new QName("http://webservice.basico.comuns.negocio/", "gerarUsuarioMovelAluno");
    private final static QName _GerarUsuarioMovelAlunoResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "gerarUsuarioMovelAlunoResponse");
    private final static QName _PersistirOperacoesContratoJSON_QNAME = new QName("http://webservice.basico.comuns.negocio/", "persistirOperacoesContratoJSON");
    private final static QName _ConsultarClientePorMatriculaExternaImportacao_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarClientePorMatriculaExternaImportacao");
    private final static QName _AtualizarDadosGerenciaisPorMes_QNAME = new QName("http://webservice.basico.comuns.negocio/", "atualizarDadosGerenciaisPorMes");
    private final static QName _ConsultarClientesAtivosForaTreinoResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarClientesAtivosForaTreinoResponse");
    private final static QName _GravarConfiguracoesGame_QNAME = new QName("http://webservice.basico.comuns.negocio/", "gravarConfiguracoesGame");
    private final static QName _ValidarUsuarioEmailResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "validarUsuarioEmailResponse");
    private final static QName _ConsultarDadosClienteResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarDadosClienteResponse");
    private final static QName _EnviarEmailTokenApp_QNAME = new QName("http://webservice.basico.comuns.negocio/", "enviarEmailTokenApp");
    private final static QName _ObterEnderecos_QNAME = new QName("http://webservice.basico.comuns.negocio/", "obterEnderecos");
    private final static QName _ConsultarDadosSolicitarAtendimento_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarDadosSolicitarAtendimento");
    private final static QName _DeletarClienteImportacaoJSONResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "deletarClienteImportacaoJSONResponse");
    private final static QName _DownloadBoletoResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "downloadBoletoResponse");
    private final static QName _ConsultarCupomDescontoResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarCupomDescontoResponse");

    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: servicos.integracao.zw.client
     * 
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link ObterPontosPorCliente }
     * 
     */
    public ObterPontosPorCliente createObterPontosPorCliente() {
        return new ObterPontosPorCliente();
    }

    /**
     * Create an instance of {@link AlterarEnderecoResponse }
     * 
     */
    public AlterarEnderecoResponse createAlterarEnderecoResponse() {
        return new AlterarEnderecoResponse();
    }

    /**
     * Create an instance of {@link ConsultarClienteResponse }
     * 
     */
    public ConsultarClienteResponse createConsultarClienteResponse() {
        return new ConsultarClienteResponse();
    }

    /**
     * Create an instance of {@link ConsultarAcessosDiaResponse }
     * 
     */
    public ConsultarAcessosDiaResponse createConsultarAcessosDiaResponse() {
        return new ConsultarAcessosDiaResponse();
    }

    /**
     * Create an instance of {@link ConsultarCidadeResponse }
     * 
     */
    public ConsultarCidadeResponse createConsultarCidadeResponse() {
        return new ConsultarCidadeResponse();
    }

    /**
     * Create an instance of {@link ConsultarContratosCliente }
     * 
     */
    public ConsultarContratosCliente createConsultarContratosCliente() {
        return new ConsultarContratosCliente();
    }

    /**
     * Create an instance of {@link ConsultarContratosRenovarResponse }
     * 
     */
    public ConsultarContratosRenovarResponse createConsultarContratosRenovarResponse() {
        return new ConsultarContratosRenovarResponse();
    }

    /**
     * Create an instance of {@link ConsultarCodigoAcesso }
     * 
     */
    public ConsultarCodigoAcesso createConsultarCodigoAcesso() {
        return new ConsultarCodigoAcesso();
    }

    /**
     * Create an instance of {@link MarcarComoLidaFeedGestao }
     * 
     */
    public MarcarComoLidaFeedGestao createMarcarComoLidaFeedGestao() {
        return new MarcarComoLidaFeedGestao();
    }

    /**
     * Create an instance of {@link BuscarEstatisticasMovidesk }
     * 
     */
    public BuscarEstatisticasMovidesk createBuscarEstatisticasMovidesk() {
        return new BuscarEstatisticasMovidesk();
    }

    /**
     * Create an instance of {@link ConsultarClienteComTelefone }
     * 
     */
    public ConsultarClienteComTelefone createConsultarClienteComTelefone() {
        return new ConsultarClienteComTelefone();
    }

    /**
     * Create an instance of {@link AlterarUsuarioMovelProfessorResponse }
     * 
     */
    public AlterarUsuarioMovelProfessorResponse createAlterarUsuarioMovelProfessorResponse() {
        return new AlterarUsuarioMovelProfessorResponse();
    }

    /**
     * Create an instance of {@link GerarUsuarioMovelColaboradorResponse }
     * 
     */
    public GerarUsuarioMovelColaboradorResponse createGerarUsuarioMovelColaboradorResponse() {
        return new GerarUsuarioMovelColaboradorResponse();
    }

    /**
     * Create an instance of {@link GravarBaixaProtheusResponse }
     * 
     */
    public GravarBaixaProtheusResponse createGravarBaixaProtheusResponse() {
        return new GravarBaixaProtheusResponse();
    }

    /**
     * Create an instance of {@link LogarUsuarioMovel }
     * 
     */
    public LogarUsuarioMovel createLogarUsuarioMovel() {
        return new LogarUsuarioMovel();
    }

    /**
     * Create an instance of {@link AlterarDadosPessoaisClienteSite }
     * 
     */
    public AlterarDadosPessoaisClienteSite createAlterarDadosPessoaisClienteSite() {
        return new AlterarDadosPessoaisClienteSite();
    }

    /**
     * Create an instance of {@link AlterarUsuarioMovelProfessor }
     * 
     */
    public AlterarUsuarioMovelProfessor createAlterarUsuarioMovelProfessor() {
        return new AlterarUsuarioMovelProfessor();
    }

    /**
     * Create an instance of {@link SenhaAlteradaAposGeracaoLink }
     * 
     */
    public SenhaAlteradaAposGeracaoLink createSenhaAlteradaAposGeracaoLink() {
        return new SenhaAlteradaAposGeracaoLink();
    }

    /**
     * Create an instance of {@link VerificarStatusLeadBuzzResponse }
     * 
     */
    public VerificarStatusLeadBuzzResponse createVerificarStatusLeadBuzzResponse() {
        return new VerificarStatusLeadBuzzResponse();
    }

    /**
     * Create an instance of {@link PersistirAcessosClienteJSONResponse }
     * 
     */
    public PersistirAcessosClienteJSONResponse createPersistirAcessosClienteJSONResponse() {
        return new PersistirAcessosClienteJSONResponse();
    }

    /**
     * Create an instance of {@link PersistirLeadRD }
     * 
     */
    public PersistirLeadRD createPersistirLeadRD() {
        return new PersistirLeadRD();
    }

    /**
     * Create an instance of {@link ConsultarParcelasVencidasSESC }
     * 
     */
    public ConsultarParcelasVencidasSESC createConsultarParcelasVencidasSESC() {
        return new ConsultarParcelasVencidasSESC();
    }

    /**
     * Create an instance of {@link Classificacoes }
     * 
     */
    public Classificacoes createClassificacoes() {
        return new Classificacoes();
    }

    /**
     * Create an instance of {@link SolicitacoesConcluidasResponse }
     * 
     */
    public SolicitacoesConcluidasResponse createSolicitacoesConcluidasResponse() {
        return new SolicitacoesConcluidasResponse();
    }

    /**
     * Create an instance of {@link RetornoTrancamentoResponse }
     * 
     */
    public RetornoTrancamentoResponse createRetornoTrancamentoResponse() {
        return new RetornoTrancamentoResponse();
    }

    /**
     * Create an instance of {@link ListaAlunoPontosAppResponse }
     * 
     */
    public ListaAlunoPontosAppResponse createListaAlunoPontosAppResponse() {
        return new ListaAlunoPontosAppResponse();
    }

    /**
     * Create an instance of {@link ObterEnderecoCodigo }
     * 
     */
    public ObterEnderecoCodigo createObterEnderecoCodigo() {
        return new ObterEnderecoCodigo();
    }

    /**
     * Create an instance of {@link NomenclaturaVendaCreditoResponse }
     * 
     */
    public NomenclaturaVendaCreditoResponse createNomenclaturaVendaCreditoResponse() {
        return new NomenclaturaVendaCreditoResponse();
    }

    /**
     * Create an instance of {@link AtualizarFotoPerfilAlunoCPFResponse }
     * 
     */
    public AtualizarFotoPerfilAlunoCPFResponse createAtualizarFotoPerfilAlunoCPFResponse() {
        return new AtualizarFotoPerfilAlunoCPFResponse();
    }

    /**
     * Create an instance of {@link ConsultarClienteComTelefoneResponse }
     * 
     */
    public ConsultarClienteComTelefoneResponse createConsultarClienteComTelefoneResponse() {
        return new ConsultarClienteComTelefoneResponse();
    }

    /**
     * Create an instance of {@link PersistirPagamentosVendaAvulsaJSON }
     * 
     */
    public PersistirPagamentosVendaAvulsaJSON createPersistirPagamentosVendaAvulsaJSON() {
        return new PersistirPagamentosVendaAvulsaJSON();
    }

    /**
     * Create an instance of {@link InserirWebHookClienteRDResponse }
     * 
     */
    public InserirWebHookClienteRDResponse createInserirWebHookClienteRDResponse() {
        return new InserirWebHookClienteRDResponse();
    }

    /**
     * Create an instance of {@link ObterValorVendasResponse }
     * 
     */
    public ObterValorVendasResponse createObterValorVendasResponse() {
        return new ObterValorVendasResponse();
    }

    /**
     * Create an instance of {@link ListaAlunoPontosDataResponse }
     * 
     */
    public ListaAlunoPontosDataResponse createListaAlunoPontosDataResponse() {
        return new ListaAlunoPontosDataResponse();
    }

    /**
     * Create an instance of {@link AtualizarGeolocalizacao }
     * 
     */
    public AtualizarGeolocalizacao createAtualizarGeolocalizacao() {
        return new AtualizarGeolocalizacao();
    }

    /**
     * Create an instance of {@link ConsultarModalidadesEmpresa }
     * 
     */
    public ConsultarModalidadesEmpresa createConsultarModalidadesEmpresa() {
        return new ConsultarModalidadesEmpresa();
    }

    /**
     * Create an instance of {@link ValidarConvitesResponse }
     * 
     */
    public ValidarConvitesResponse createValidarConvitesResponse() {
        return new ValidarConvitesResponse();
    }

    /**
     * Create an instance of {@link GravarRespostaResponse }
     * 
     */
    public GravarRespostaResponse createGravarRespostaResponse() {
        return new GravarRespostaResponse();
    }

    /**
     * Create an instance of {@link SalvarCampanhaResponse }
     * 
     */
    public SalvarCampanhaResponse createSalvarCampanhaResponse() {
        return new SalvarCampanhaResponse();
    }

    /**
     * Create an instance of {@link ConsultarClienteNaRedeEmpresaResponse }
     * 
     */
    public ConsultarClienteNaRedeEmpresaResponse createConsultarClienteNaRedeEmpresaResponse() {
        return new ConsultarClienteNaRedeEmpresaResponse();
    }

    /**
     * Create an instance of {@link ConsultarDadosGameResponse }
     * 
     */
    public ConsultarDadosGameResponse createConsultarDadosGameResponse() {
        return new ConsultarDadosGameResponse();
    }

    /**
     * Create an instance of {@link PersistirLeadBuzzResponse }
     * 
     */
    public PersistirLeadBuzzResponse createPersistirLeadBuzzResponse() {
        return new PersistirLeadBuzzResponse();
    }

    /**
     * Create an instance of {@link ValidarUsuarioResponse }
     * 
     */
    public ValidarUsuarioResponse createValidarUsuarioResponse() {
        return new ValidarUsuarioResponse();
    }

    /**
     * Create an instance of {@link ConsultarDetalhesDF }
     * 
     */
    public ConsultarDetalhesDF createConsultarDetalhesDF() {
        return new ConsultarDetalhesDF();
    }

    /**
     * Create an instance of {@link ConsultarParcelasClienteResponse }
     * 
     */
    public ConsultarParcelasClienteResponse createConsultarParcelasClienteResponse() {
        return new ConsultarParcelasClienteResponse();
    }

    /**
     * Create an instance of {@link ConsultarOperacoesExcecoes }
     * 
     */
    public ConsultarOperacoesExcecoes createConsultarOperacoesExcecoes() {
        return new ConsultarOperacoesExcecoes();
    }

    /**
     * Create an instance of {@link ConsultarEmpresasSimples }
     * 
     */
    public ConsultarEmpresasSimples createConsultarEmpresasSimples() {
        return new ConsultarEmpresasSimples();
    }

    /**
     * Create an instance of {@link ConsultarDadosOperacaoContratoResponse }
     * 
     */
    public ConsultarDadosOperacaoContratoResponse createConsultarDadosOperacaoContratoResponse() {
        return new ConsultarDadosOperacaoContratoResponse();
    }

    /**
     * Create an instance of {@link ObterVencimentosContratosResponse }
     * 
     */
    public ObterVencimentosContratosResponse createObterVencimentosContratosResponse() {
        return new ObterVencimentosContratosResponse();
    }

    /**
     * Create an instance of {@link ListaAlunoPontosResponse }
     * 
     */
    public ListaAlunoPontosResponse createListaAlunoPontosResponse() {
        return new ListaAlunoPontosResponse();
    }

    /**
     * Create an instance of {@link LancarProdutoAtestado }
     * 
     */
    public LancarProdutoAtestado createLancarProdutoAtestado() {
        return new LancarProdutoAtestado();
    }

    /**
     * Create an instance of {@link ConsultarClientesPeloNome }
     * 
     */
    public ConsultarClientesPeloNome createConsultarClientesPeloNome() {
        return new ConsultarClientesPeloNome();
    }

    /**
     * Create an instance of {@link ConsultarDadosCliente }
     * 
     */
    public ConsultarDadosCliente createConsultarDadosCliente() {
        return new ConsultarDadosCliente();
    }

    /**
     * Create an instance of {@link NotificarRecursoEmpresaGeolocalizacaoResponse }
     * 
     */
    public NotificarRecursoEmpresaGeolocalizacaoResponse createNotificarRecursoEmpresaGeolocalizacaoResponse() {
        return new NotificarRecursoEmpresaGeolocalizacaoResponse();
    }

    /**
     * Create an instance of {@link ListaProdutosServicosResponse }
     * 
     */
    public ListaProdutosServicosResponse createListaProdutosServicosResponse() {
        return new ListaProdutosServicosResponse();
    }

    /**
     * Create an instance of {@link ConsultarCodigoAcessoResponse }
     * 
     */
    public ConsultarCodigoAcessoResponse createConsultarCodigoAcessoResponse() {
        return new ConsultarCodigoAcessoResponse();
    }

    /**
     * Create an instance of {@link ConsultarPrimeiraParcelaContrato }
     * 
     */
    public ConsultarPrimeiraParcelaContrato createConsultarPrimeiraParcelaContrato() {
        return new ConsultarPrimeiraParcelaContrato();
    }

    /**
     * Create an instance of {@link ConsultarClientePorCodigoAcesso }
     * 
     */
    public ConsultarClientePorCodigoAcesso createConsultarClientePorCodigoAcesso() {
        return new ConsultarClientePorCodigoAcesso();
    }

    /**
     * Create an instance of {@link EnviarMensagemAoUsuario }
     * 
     */
    public EnviarMensagemAoUsuario createEnviarMensagemAoUsuario() {
        return new EnviarMensagemAoUsuario();
    }

    /**
     * Create an instance of {@link ConsultarModalidadesResponse }
     * 
     */
    public ConsultarModalidadesResponse createConsultarModalidadesResponse() {
        return new ConsultarModalidadesResponse();
    }

    /**
     * Create an instance of {@link GerarVendaCreditos }
     * 
     */
    public GerarVendaCreditos createGerarVendaCreditos() {
        return new GerarVendaCreditos();
    }

    /**
     * Create an instance of {@link ConsultarClienteSinteticoPorEmailPessoa }
     * 
     */
    public ConsultarClienteSinteticoPorEmailPessoa createConsultarClienteSinteticoPorEmailPessoa() {
        return new ConsultarClienteSinteticoPorEmailPessoa();
    }

    /**
     * Create an instance of {@link CategoriasResponse }
     * 
     */
    public CategoriasResponse createCategoriasResponse() {
        return new CategoriasResponse();
    }

    /**
     * Create an instance of {@link ConsultarParcelasVencidasResponse }
     * 
     */
    public ConsultarParcelasVencidasResponse createConsultarParcelasVencidasResponse() {
        return new ConsultarParcelasVencidasResponse();
    }

    /**
     * Create an instance of {@link ObterValorProdutoCfgEmpresaResponse }
     * 
     */
    public ObterValorProdutoCfgEmpresaResponse createObterValorProdutoCfgEmpresaResponse() {
        return new ObterValorProdutoCfgEmpresaResponse();
    }

    /**
     * Create an instance of {@link AtualizarCadastroPersonalResponse }
     * 
     */
    public AtualizarCadastroPersonalResponse createAtualizarCadastroPersonalResponse() {
        return new AtualizarCadastroPersonalResponse();
    }

    /**
     * Create an instance of {@link AtualizarBoletoPJBankResponse }
     * 
     */
    public AtualizarBoletoPJBankResponse createAtualizarBoletoPJBankResponse() {
        return new AtualizarBoletoPJBankResponse();
    }

    /**
     * Create an instance of {@link ConsultarProfessoresPeloNomeResponse }
     * 
     */
    public ConsultarProfessoresPeloNomeResponse createConsultarProfessoresPeloNomeResponse() {
        return new ConsultarProfessoresPeloNomeResponse();
    }

    /**
     * Create an instance of {@link SolicitacoesEmAbertoResponse }
     * 
     */
    public SolicitacoesEmAbertoResponse createSolicitacoesEmAbertoResponse() {
        return new SolicitacoesEmAbertoResponse();
    }

    /**
     * Create an instance of {@link DeletarClienteImportacaoJSON }
     * 
     */
    public DeletarClienteImportacaoJSON createDeletarClienteImportacaoJSON() {
        return new DeletarClienteImportacaoJSON();
    }

    /**
     * Create an instance of {@link PersistirColaborador }
     * 
     */
    public PersistirColaborador createPersistirColaborador() {
        return new PersistirColaborador();
    }

    /**
     * Create an instance of {@link PersistirColaboradorResponse }
     * 
     */
    public PersistirColaboradorResponse createPersistirColaboradorResponse() {
        return new PersistirColaboradorResponse();
    }

    /**
     * Create an instance of {@link AlterarUsuarioMovelAluno }
     * 
     */
    public AlterarUsuarioMovelAluno createAlterarUsuarioMovelAluno() {
        return new AlterarUsuarioMovelAluno();
    }

    /**
     * Create an instance of {@link PersistirContratoJSON }
     * 
     */
    public PersistirContratoJSON createPersistirContratoJSON() {
        return new PersistirContratoJSON();
    }

    /**
     * Create an instance of {@link AlterarUsuarioMovelAlunoResponse }
     * 
     */
    public AlterarUsuarioMovelAlunoResponse createAlterarUsuarioMovelAlunoResponse() {
        return new AlterarUsuarioMovelAlunoResponse();
    }

    /**
     * Create an instance of {@link LancarConvite }
     * 
     */
    public LancarConvite createLancarConvite() {
        return new LancarConvite();
    }

    /**
     * Create an instance of {@link ConsultarAcessosListaDiaResponse }
     * 
     */
    public ConsultarAcessosListaDiaResponse createConsultarAcessosListaDiaResponse() {
        return new ConsultarAcessosListaDiaResponse();
    }

    /**
     * Create an instance of {@link ConsultarColaboradoresTreinoResponse }
     * 
     */
    public ConsultarColaboradoresTreinoResponse createConsultarColaboradoresTreinoResponse() {
        return new ConsultarColaboradoresTreinoResponse();
    }

    /**
     * Create an instance of {@link ConsultarFeedGestaoResponse }
     * 
     */
    public ConsultarFeedGestaoResponse createConsultarFeedGestaoResponse() {
        return new ConsultarFeedGestaoResponse();
    }

    /**
     * Create an instance of {@link ConsultarDadosSolicitarAtendimentoResponse }
     * 
     */
    public ConsultarDadosSolicitarAtendimentoResponse createConsultarDadosSolicitarAtendimentoResponse() {
        return new ConsultarDadosSolicitarAtendimentoResponse();
    }

    /**
     * Create an instance of {@link ValidarUsuarioTelefoneResponse }
     * 
     */
    public ValidarUsuarioTelefoneResponse createValidarUsuarioTelefoneResponse() {
        return new ValidarUsuarioTelefoneResponse();
    }

    /**
     * Create an instance of {@link DeletarContratoImportacaoJSONResponse }
     * 
     */
    public DeletarContratoImportacaoJSONResponse createDeletarContratoImportacaoJSONResponse() {
        return new DeletarContratoImportacaoJSONResponse();
    }

    /**
     * Create an instance of {@link ConsultarPrimeiraParcelaContratoResponse }
     * 
     */
    public ConsultarPrimeiraParcelaContratoResponse createConsultarPrimeiraParcelaContratoResponse() {
        return new ConsultarPrimeiraParcelaContratoResponse();
    }

    /**
     * Create an instance of {@link ConsultarClientesPelaMatriculaResponse }
     * 
     */
    public ConsultarClientesPelaMatriculaResponse createConsultarClientesPelaMatriculaResponse() {
        return new ConsultarClientesPelaMatriculaResponse();
    }

    /**
     * Create an instance of {@link EnviarEmailAngular }
     * 
     */
    public EnviarEmailAngular createEnviarEmailAngular() {
        return new EnviarEmailAngular();
    }

    /**
     * Create an instance of {@link ConsultarLocaisAcesso }
     * 
     */
    public ConsultarLocaisAcesso createConsultarLocaisAcesso() {
        return new ConsultarLocaisAcesso();
    }

    /**
     * Create an instance of {@link ObterDuracaoCreditosEmpresaResponse }
     * 
     */
    public ObterDuracaoCreditosEmpresaResponse createObterDuracaoCreditosEmpresaResponse() {
        return new ObterDuracaoCreditosEmpresaResponse();
    }

    /**
     * Create an instance of {@link ConfiguracaoTotem }
     * 
     */
    public ConfiguracaoTotem createConfiguracaoTotem() {
        return new ConfiguracaoTotem();
    }

    /**
     * Create an instance of {@link AlterarTelefoneResponse }
     * 
     */
    public AlterarTelefoneResponse createAlterarTelefoneResponse() {
        return new AlterarTelefoneResponse();
    }

    /**
     * Create an instance of {@link ValidarCodigoVerificacaoResponse }
     * 
     */
    public ValidarCodigoVerificacaoResponse createValidarCodigoVerificacaoResponse() {
        return new ValidarCodigoVerificacaoResponse();
    }

    /**
     * Create an instance of {@link ConsultarNrDiasParcelaEmAberto }
     * 
     */
    public ConsultarNrDiasParcelaEmAberto createConsultarNrDiasParcelaEmAberto() {
        return new ConsultarNrDiasParcelaEmAberto();
    }

    /**
     * Create an instance of {@link ConsultarAlunosResponse }
     * 
     */
    public ConsultarAlunosResponse createConsultarAlunosResponse() {
        return new ConsultarAlunosResponse();
    }

    /**
     * Create an instance of {@link ConsultarFotoKeyPessoa }
     * 
     */
    public ConsultarFotoKeyPessoa createConsultarFotoKeyPessoa() {
        return new ConsultarFotoKeyPessoa();
    }

    /**
     * Create an instance of {@link GravarBaixaProtheus }
     * 
     */
    public GravarBaixaProtheus createGravarBaixaProtheus() {
        return new GravarBaixaProtheus();
    }

    /**
     * Create an instance of {@link AtualizarFotoPerfilAlunoCPF }
     * 
     */
    public AtualizarFotoPerfilAlunoCPF createAtualizarFotoPerfilAlunoCPF() {
        return new AtualizarFotoPerfilAlunoCPF();
    }

    /**
     * Create an instance of {@link ConsultarQuantidadeAcessosClientesAgrupadosDia }
     * 
     */
    public ConsultarQuantidadeAcessosClientesAgrupadosDia createConsultarQuantidadeAcessosClientesAgrupadosDia() {
        return new ConsultarQuantidadeAcessosClientesAgrupadosDia();
    }

    /**
     * Create an instance of {@link Categorias }
     * 
     */
    public Categorias createCategorias() {
        return new Categorias();
    }

    /**
     * Create an instance of {@link PersistirLeadRDResponse }
     * 
     */
    public PersistirLeadRDResponse createPersistirLeadRDResponse() {
        return new PersistirLeadRDResponse();
    }

    /**
     * Create an instance of {@link VerificarUsuarioMovelResponse }
     * 
     */
    public VerificarUsuarioMovelResponse createVerificarUsuarioMovelResponse() {
        return new VerificarUsuarioMovelResponse();
    }

    /**
     * Create an instance of {@link ApiWeHelp }
     * 
     */
    public ApiWeHelp createApiWeHelp() {
        return new ApiWeHelp();
    }

    /**
     * Create an instance of {@link ConsultarParcelasVencidasSESCResponse }
     * 
     */
    public ConsultarParcelasVencidasSESCResponse createConsultarParcelasVencidasSESCResponse() {
        return new ConsultarParcelasVencidasSESCResponse();
    }

    /**
     * Create an instance of {@link ConsultarOperacoesExcecoesResponse }
     * 
     */
    public ConsultarOperacoesExcecoesResponse createConsultarOperacoesExcecoesResponse() {
        return new ConsultarOperacoesExcecoesResponse();
    }

    /**
     * Create an instance of {@link PersistirContratoJSONResponse }
     * 
     */
    public PersistirContratoJSONResponse createPersistirContratoJSONResponse() {
        return new PersistirContratoJSONResponse();
    }

    /**
     * Create an instance of {@link ObterEnderecoEmpresaResponse }
     * 
     */
    public ObterEnderecoEmpresaResponse createObterEnderecoEmpresaResponse() {
        return new ObterEnderecoEmpresaResponse();
    }

    /**
     * Create an instance of {@link ConsultarColaboradoresTreino }
     * 
     */
    public ConsultarColaboradoresTreino createConsultarColaboradoresTreino() {
        return new ConsultarColaboradoresTreino();
    }

    /**
     * Create an instance of {@link GravarResposta }
     * 
     */
    public GravarResposta createGravarResposta() {
        return new GravarResposta();
    }

    /**
     * Create an instance of {@link ConsultarClienteNaRedeEmpresa }
     * 
     */
    public ConsultarClienteNaRedeEmpresa createConsultarClienteNaRedeEmpresa() {
        return new ConsultarClienteNaRedeEmpresa();
    }

    /**
     * Create an instance of {@link ObterDuracaoCreditosEmpresa }
     * 
     */
    public ObterDuracaoCreditosEmpresa createObterDuracaoCreditosEmpresa() {
        return new ObterDuracaoCreditosEmpresa();
    }

    /**
     * Create an instance of {@link EnderecosGeolocalizarAposHabilitarChave }
     * 
     */
    public EnderecosGeolocalizarAposHabilitarChave createEnderecosGeolocalizarAposHabilitarChave() {
        return new EnderecosGeolocalizarAposHabilitarChave();
    }

    /**
     * Create an instance of {@link ConsultarClientesRenovaramOuNaoResponse }
     * 
     */
    public ConsultarClientesRenovaramOuNaoResponse createConsultarClientesRenovaramOuNaoResponse() {
        return new ConsultarClientesRenovaramOuNaoResponse();
    }

    /**
     * Create an instance of {@link SolicitacoesEmAberto }
     * 
     */
    public SolicitacoesEmAberto createSolicitacoesEmAberto() {
        return new SolicitacoesEmAberto();
    }

    /**
     * Create an instance of {@link EnviarEmailGenericoResponse }
     * 
     */
    public EnviarEmailGenericoResponse createEnviarEmailGenericoResponse() {
        return new EnviarEmailGenericoResponse();
    }

    /**
     * Create an instance of {@link ConsultaGenericaPaginadaResponse }
     * 
     */
    public ConsultaGenericaPaginadaResponse createConsultaGenericaPaginadaResponse() {
        return new ConsultaGenericaPaginadaResponse();
    }

    /**
     * Create an instance of {@link UrlFotoEmpresaResponse }
     * 
     */
    public UrlFotoEmpresaResponse createUrlFotoEmpresaResponse() {
        return new UrlFotoEmpresaResponse();
    }

    /**
     * Create an instance of {@link UpdateGeolocEmpresaResponse }
     * 
     */
    public UpdateGeolocEmpresaResponse createUpdateGeolocEmpresaResponse() {
        return new UpdateGeolocEmpresaResponse();
    }

    /**
     * Create an instance of {@link ConsultarEstado }
     * 
     */
    public ConsultarEstado createConsultarEstado() {
        return new ConsultarEstado();
    }

    /**
     * Create an instance of {@link ConsultaGenerica }
     * 
     */
    public ConsultaGenerica createConsultaGenerica() {
        return new ConsultaGenerica();
    }

    /**
     * Create an instance of {@link ConsultarClientesRenovaramOuNao }
     * 
     */
    public ConsultarClientesRenovaramOuNao createConsultarClientesRenovaramOuNao() {
        return new ConsultarClientesRenovaramOuNao();
    }

    /**
     * Create an instance of {@link EnviarMensagemAoUsuarioUsernameResponse }
     * 
     */
    public EnviarMensagemAoUsuarioUsernameResponse createEnviarMensagemAoUsuarioUsernameResponse() {
        return new EnviarMensagemAoUsuarioUsernameResponse();
    }

    /**
     * Create an instance of {@link ObterDadosContratoPorDuracao }
     * 
     */
    public ObterDadosContratoPorDuracao createObterDadosContratoPorDuracao() {
        return new ObterDadosContratoPorDuracao();
    }

    /**
     * Create an instance of {@link AlterarDadosPessoaisClienteSiteResponse }
     * 
     */
    public AlterarDadosPessoaisClienteSiteResponse createAlterarDadosPessoaisClienteSiteResponse() {
        return new AlterarDadosPessoaisClienteSiteResponse();
    }

    /**
     * Create an instance of {@link ConsultarNrAlunosAtivosForaTreino }
     * 
     */
    public ConsultarNrAlunosAtivosForaTreino createConsultarNrAlunosAtivosForaTreino() {
        return new ConsultarNrAlunosAtivosForaTreino();
    }

    /**
     * Create an instance of {@link ConsultarNrDiasParcelaEmAbertoResponse }
     * 
     */
    public ConsultarNrDiasParcelaEmAbertoResponse createConsultarNrDiasParcelaEmAbertoResponse() {
        return new ConsultarNrDiasParcelaEmAbertoResponse();
    }

    /**
     * Create an instance of {@link ValidarProdutoVigente }
     * 
     */
    public ValidarProdutoVigente createValidarProdutoVigente() {
        return new ValidarProdutoVigente();
    }

    /**
     * Create an instance of {@link ValidarUsuarioEmailResponse }
     * 
     */
    public ValidarUsuarioEmailResponse createValidarUsuarioEmailResponse() {
        return new ValidarUsuarioEmailResponse();
    }

    /**
     * Create an instance of {@link ConsultarDadosSolicitarAtendimento }
     * 
     */
    public ConsultarDadosSolicitarAtendimento createConsultarDadosSolicitarAtendimento() {
        return new ConsultarDadosSolicitarAtendimento();
    }

    /**
     * Create an instance of {@link ConsultarDadosClienteResponse }
     * 
     */
    public ConsultarDadosClienteResponse createConsultarDadosClienteResponse() {
        return new ConsultarDadosClienteResponse();
    }

    /**
     * Create an instance of {@link ObterEnderecos }
     * 
     */
    public ObterEnderecos createObterEnderecos() {
        return new ObterEnderecos();
    }

    /**
     * Create an instance of {@link EnviarEmailTokenApp }
     * 
     */
    public EnviarEmailTokenApp createEnviarEmailTokenApp() {
        return new EnviarEmailTokenApp();
    }

    /**
     * Create an instance of {@link DeletarClienteImportacaoJSONResponse }
     * 
     */
    public DeletarClienteImportacaoJSONResponse createDeletarClienteImportacaoJSONResponse() {
        return new DeletarClienteImportacaoJSONResponse();
    }

    /**
     * Create an instance of {@link DownloadBoletoResponse }
     * 
     */
    public DownloadBoletoResponse createDownloadBoletoResponse() {
        return new DownloadBoletoResponse();
    }

    /**
     * Create an instance of {@link ConsultarCupomDescontoResponse }
     * 
     */
    public ConsultarCupomDescontoResponse createConsultarCupomDescontoResponse() {
        return new ConsultarCupomDescontoResponse();
    }

    /**
     * Create an instance of {@link ConsultarClientesPelaMatricula }
     * 
     */
    public ConsultarClientesPelaMatricula createConsultarClientesPelaMatricula() {
        return new ConsultarClientesPelaMatricula();
    }

    /**
     * Create an instance of {@link ValidarUsuarioEmail }
     * 
     */
    public ValidarUsuarioEmail createValidarUsuarioEmail() {
        return new ValidarUsuarioEmail();
    }

    /**
     * Create an instance of {@link ConsultarConsultoresGameResponse }
     * 
     */
    public ConsultarConsultoresGameResponse createConsultarConsultoresGameResponse() {
        return new ConsultarConsultoresGameResponse();
    }

    /**
     * Create an instance of {@link GerarUsuarioMovelAluno }
     * 
     */
    public GerarUsuarioMovelAluno createGerarUsuarioMovelAluno() {
        return new GerarUsuarioMovelAluno();
    }

    /**
     * Create an instance of {@link GerarUsuarioMovelAlunoResponse }
     * 
     */
    public GerarUsuarioMovelAlunoResponse createGerarUsuarioMovelAlunoResponse() {
        return new GerarUsuarioMovelAlunoResponse();
    }

    /**
     * Create an instance of {@link ConsultarClientesPeloNomeNoLimits }
     * 
     */
    public ConsultarClientesPeloNomeNoLimits createConsultarClientesPeloNomeNoLimits() {
        return new ConsultarClientesPeloNomeNoLimits();
    }

    /**
     * Create an instance of {@link ConsultarClientePorMatriculaExternaImportacao }
     * 
     */
    public ConsultarClientePorMatriculaExternaImportacao createConsultarClientePorMatriculaExternaImportacao() {
        return new ConsultarClientePorMatriculaExternaImportacao();
    }

    /**
     * Create an instance of {@link PersistirOperacoesContratoJSON }
     * 
     */
    public PersistirOperacoesContratoJSON createPersistirOperacoesContratoJSON() {
        return new PersistirOperacoesContratoJSON();
    }

    /**
     * Create an instance of {@link ConsultarClientesAtivosForaTreinoResponse }
     * 
     */
    public ConsultarClientesAtivosForaTreinoResponse createConsultarClientesAtivosForaTreinoResponse() {
        return new ConsultarClientesAtivosForaTreinoResponse();
    }

    /**
     * Create an instance of {@link GravarConfiguracoesGame }
     * 
     */
    public GravarConfiguracoesGame createGravarConfiguracoesGame() {
        return new GravarConfiguracoesGame();
    }

    /**
     * Create an instance of {@link AtualizarDadosGerenciaisPorMes }
     * 
     */
    public AtualizarDadosGerenciaisPorMes createAtualizarDadosGerenciaisPorMes() {
        return new AtualizarDadosGerenciaisPorMes();
    }

    /**
     * Create an instance of {@link IncluirAssinaturaDigital }
     * 
     */
    public IncluirAssinaturaDigital createIncluirAssinaturaDigital() {
        return new IncluirAssinaturaDigital();
    }

    /**
     * Create an instance of {@link ValidarConvites }
     * 
     */
    public ValidarConvites createValidarConvites() {
        return new ValidarConvites();
    }

    /**
     * Create an instance of {@link SituacaoUsuario }
     * 
     */
    public SituacaoUsuario createSituacaoUsuario() {
        return new SituacaoUsuario();
    }

    /**
     * Create an instance of {@link ConsultarClientesTreino }
     * 
     */
    public ConsultarClientesTreino createConsultarClientesTreino() {
        return new ConsultarClientesTreino();
    }

    /**
     * Create an instance of {@link ListaProdutosAtestadoResponse }
     * 
     */
    public ListaProdutosAtestadoResponse createListaProdutosAtestadoResponse() {
        return new ListaProdutosAtestadoResponse();
    }

    /**
     * Create an instance of {@link ConsultarColetores }
     * 
     */
    public ConsultarColetores createConsultarColetores() {
        return new ConsultarColetores();
    }

    /**
     * Create an instance of {@link EnviarNotificacaoUsuario }
     * 
     */
    public EnviarNotificacaoUsuario createEnviarNotificacaoUsuario() {
        return new EnviarNotificacaoUsuario();
    }

    /**
     * Create an instance of {@link BuscarClientesPesquisaTWResponse }
     * 
     */
    public BuscarClientesPesquisaTWResponse createBuscarClientesPesquisaTWResponse() {
        return new BuscarClientesPesquisaTWResponse();
    }

    /**
     * Create an instance of {@link ObterValorVendas }
     * 
     */
    public ObterValorVendas createObterValorVendas() {
        return new ObterValorVendas();
    }

    /**
     * Create an instance of {@link ValidarUsuario }
     * 
     */
    public ValidarUsuario createValidarUsuario() {
        return new ValidarUsuario();
    }

    /**
     * Create an instance of {@link GravarConfiguracoesGameResponse }
     * 
     */
    public GravarConfiguracoesGameResponse createGravarConfiguracoesGameResponse() {
        return new GravarConfiguracoesGameResponse();
    }

    /**
     * Create an instance of {@link RecuperarLogin }
     * 
     */
    public RecuperarLogin createRecuperarLogin() {
        return new RecuperarLogin();
    }

    /**
     * Create an instance of {@link ConsultarParcelasVencidas }
     * 
     */
    public ConsultarParcelasVencidas createConsultarParcelasVencidas() {
        return new ConsultarParcelasVencidas();
    }

    /**
     * Create an instance of {@link ObterConfiguracaoEmailPadraoResponse }
     * 
     */
    public ObterConfiguracaoEmailPadraoResponse createObterConfiguracaoEmailPadraoResponse() {
        return new ObterConfiguracaoEmailPadraoResponse();
    }

    /**
     * Create an instance of {@link PersistirCancelarContratoJSON }
     * 
     */
    public PersistirCancelarContratoJSON createPersistirCancelarContratoJSON() {
        return new PersistirCancelarContratoJSON();
    }

    /**
     * Create an instance of {@link AtualizarCadastroPersonal }
     * 
     */
    public AtualizarCadastroPersonal createAtualizarCadastroPersonal() {
        return new AtualizarCadastroPersonal();
    }

    /**
     * Create an instance of {@link PersistirLeadBuzz }
     * 
     */
    public PersistirLeadBuzz createPersistirLeadBuzz() {
        return new PersistirLeadBuzz();
    }

    /**
     * Create an instance of {@link ValidarProdutoVigenteResponse }
     * 
     */
    public ValidarProdutoVigenteResponse createValidarProdutoVigenteResponse() {
        return new ValidarProdutoVigenteResponse();
    }

    /**
     * Create an instance of {@link ConsultarModalidades }
     * 
     */
    public ConsultarModalidades createConsultarModalidades() {
        return new ConsultarModalidades();
    }

    /**
     * Create an instance of {@link ConsultarEmpresas }
     * 
     */
    public ConsultarEmpresas createConsultarEmpresas() {
        return new ConsultarEmpresas();
    }

    /**
     * Create an instance of {@link ConsultarDetalhesDFResponse }
     * 
     */
    public ConsultarDetalhesDFResponse createConsultarDetalhesDFResponse() {
        return new ConsultarDetalhesDFResponse();
    }

    /**
     * Create an instance of {@link ConsultarEstatisticaSolicitacao }
     * 
     */
    public ConsultarEstatisticaSolicitacao createConsultarEstatisticaSolicitacao() {
        return new ConsultarEstatisticaSolicitacao();
    }

    /**
     * Create an instance of {@link ListaClientesEstacionamentoSelfitResponse }
     * 
     */
    public ListaClientesEstacionamentoSelfitResponse createListaClientesEstacionamentoSelfitResponse() {
        return new ListaClientesEstacionamentoSelfitResponse();
    }

    /**
     * Create an instance of {@link ConfiguracaoTotemResponse }
     * 
     */
    public ConfiguracaoTotemResponse createConfiguracaoTotemResponse() {
        return new ConfiguracaoTotemResponse();
    }

    /**
     * Create an instance of {@link ConsultarConfiguracaoENotasResponse }
     * 
     */
    public ConsultarConfiguracaoENotasResponse createConsultarConfiguracaoENotasResponse() {
        return new ConsultarConfiguracaoENotasResponse();
    }

    /**
     * Create an instance of {@link ObterConfiguracaoEmailPadrao }
     * 
     */
    public ObterConfiguracaoEmailPadrao createObterConfiguracaoEmailPadrao() {
        return new ObterConfiguracaoEmailPadrao();
    }

    /**
     * Create an instance of {@link ObterValorProdutoCfgEmpresa }
     * 
     */
    public ObterValorProdutoCfgEmpresa createObterValorProdutoCfgEmpresa() {
        return new ObterValorProdutoCfgEmpresa();
    }

    /**
     * Create an instance of {@link PersistirLogAuditoria }
     * 
     */
    public PersistirLogAuditoria createPersistirLogAuditoria() {
        return new PersistirLogAuditoria();
    }

    /**
     * Create an instance of {@link ConsultarConsultoresGame }
     * 
     */
    public ConsultarConsultoresGame createConsultarConsultoresGame() {
        return new ConsultarConsultoresGame();
    }

    /**
     * Create an instance of {@link PersistirClienteSiteResponse }
     * 
     */
    public PersistirClienteSiteResponse createPersistirClienteSiteResponse() {
        return new PersistirClienteSiteResponse();
    }

    /**
     * Create an instance of {@link ConsultarLocalAcessoPorNFC }
     * 
     */
    public ConsultarLocalAcessoPorNFC createConsultarLocalAcessoPorNFC() {
        return new ConsultarLocalAcessoPorNFC();
    }

    /**
     * Create an instance of {@link VerificarStatusLeadBuzz }
     * 
     */
    public VerificarStatusLeadBuzz createVerificarStatusLeadBuzz() {
        return new VerificarStatusLeadBuzz();
    }

    /**
     * Create an instance of {@link AtualizarDadosGerenciaisDiaResponse }
     * 
     */
    public AtualizarDadosGerenciaisDiaResponse createAtualizarDadosGerenciaisDiaResponse() {
        return new AtualizarDadosGerenciaisDiaResponse();
    }

    /**
     * Create an instance of {@link SalvarCampanha }
     * 
     */
    public SalvarCampanha createSalvarCampanha() {
        return new SalvarCampanha();
    }

    /**
     * Create an instance of {@link ConsultarClientesResponse }
     * 
     */
    public ConsultarClientesResponse createConsultarClientesResponse() {
        return new ConsultarClientesResponse();
    }

    /**
     * Create an instance of {@link ResponderQuestionarioResponse }
     * 
     */
    public ResponderQuestionarioResponse createResponderQuestionarioResponse() {
        return new ResponderQuestionarioResponse();
    }

    /**
     * Create an instance of {@link GravarDadosOperacaoContrato }
     * 
     */
    public GravarDadosOperacaoContrato createGravarDadosOperacaoContrato() {
        return new GravarDadosOperacaoContrato();
    }

    /**
     * Create an instance of {@link EnviarEmailColaboradorResponse }
     * 
     */
    public EnviarEmailColaboradorResponse createEnviarEmailColaboradorResponse() {
        return new EnviarEmailColaboradorResponse();
    }

    /**
     * Create an instance of {@link AlterarUsuarioMovelAlunoNovo }
     * 
     */
    public AlterarUsuarioMovelAlunoNovo createAlterarUsuarioMovelAlunoNovo() {
        return new AlterarUsuarioMovelAlunoNovo();
    }

    /**
     * Create an instance of {@link ConsultarFrequenciaAlunoResponse }
     * 
     */
    public ConsultarFrequenciaAlunoResponse createConsultarFrequenciaAlunoResponse() {
        return new ConsultarFrequenciaAlunoResponse();
    }

    /**
     * Create an instance of {@link PersistirLeadGenerico }
     * 
     */
    public PersistirLeadGenerico createPersistirLeadGenerico() {
        return new PersistirLeadGenerico();
    }

    /**
     * Create an instance of {@link ListaProdutosServicos }
     * 
     */
    public ListaProdutosServicos createListaProdutosServicos() {
        return new ListaProdutosServicos();
    }

    /**
     * Create an instance of {@link ConsultarMeioRecuperarLogin }
     * 
     */
    public ConsultarMeioRecuperarLogin createConsultarMeioRecuperarLogin() {
        return new ConsultarMeioRecuperarLogin();
    }

    /**
     * Create an instance of {@link AcessTokenValido }
     * 
     */
    public AcessTokenValido createAcessTokenValido() {
        return new AcessTokenValido();
    }

    /**
     * Create an instance of {@link ConsultarClientePorMatriculaExternaImportacaoResponse }
     * 
     */
    public ConsultarClientePorMatriculaExternaImportacaoResponse createConsultarClientePorMatriculaExternaImportacaoResponse() {
        return new ConsultarClientePorMatriculaExternaImportacaoResponse();
    }

    /**
     * Create an instance of {@link EnviarEmailTokenAppResponse }
     * 
     */
    public EnviarEmailTokenAppResponse createEnviarEmailTokenAppResponse() {
        return new EnviarEmailTokenAppResponse();
    }

    /**
     * Create an instance of {@link AlterarSenhaUsuario }
     * 
     */
    public AlterarSenhaUsuario createAlterarSenhaUsuario() {
        return new AlterarSenhaUsuario();
    }

    /**
     * Create an instance of {@link LiberarCobrancaBaixaProtheus }
     * 
     */
    public LiberarCobrancaBaixaProtheus createLiberarCobrancaBaixaProtheus() {
        return new LiberarCobrancaBaixaProtheus();
    }

    /**
     * Create an instance of {@link ObterCliente }
     * 
     */
    public ObterCliente createObterCliente() {
        return new ObterCliente();
    }

    /**
     * Create an instance of {@link AlterarEmail }
     * 
     */
    public AlterarEmail createAlterarEmail() {
        return new AlterarEmail();
    }

    /**
     * Create an instance of {@link ConsultarProdutosClienteResponse }
     * 
     */
    public ConsultarProdutosClienteResponse createConsultarProdutosClienteResponse() {
        return new ConsultarProdutosClienteResponse();
    }

    /**
     * Create an instance of {@link ObterQuestionarioResponse }
     * 
     */
    public ObterQuestionarioResponse createObterQuestionarioResponse() {
        return new ObterQuestionarioResponse();
    }

    /**
     * Create an instance of {@link InserirWebHookClienteRD }
     * 
     */
    public InserirWebHookClienteRD createInserirWebHookClienteRD() {
        return new InserirWebHookClienteRD();
    }

    /**
     * Create an instance of {@link UpdateGeolocEmpresa }
     * 
     */
    public UpdateGeolocEmpresa createUpdateGeolocEmpresa() {
        return new UpdateGeolocEmpresa();
    }

    /**
     * Create an instance of {@link ConsultaGenericaPaginada }
     * 
     */
    public ConsultaGenericaPaginada createConsultaGenericaPaginada() {
        return new ConsultaGenericaPaginada();
    }

    /**
     * Create an instance of {@link ConsultarAtestadoCliente }
     * 
     */
    public ConsultarAtestadoCliente createConsultarAtestadoCliente() {
        return new ConsultarAtestadoCliente();
    }

    /**
     * Create an instance of {@link ListaAlunoPontosData }
     * 
     */
    public ListaAlunoPontosData createListaAlunoPontosData() {
        return new ListaAlunoPontosData();
    }

    /**
     * Create an instance of {@link AlterarSenhaUsuarioEmailResponse }
     * 
     */
    public AlterarSenhaUsuarioEmailResponse createAlterarSenhaUsuarioEmailResponse() {
        return new AlterarSenhaUsuarioEmailResponse();
    }

    /**
     * Create an instance of {@link GerarUsuarioMovelColaborador }
     * 
     */
    public GerarUsuarioMovelColaborador createGerarUsuarioMovelColaborador() {
        return new GerarUsuarioMovelColaborador();
    }

    /**
     * Create an instance of {@link NotificarRecursoEmpresaGeolocalizacao }
     * 
     */
    public NotificarRecursoEmpresaGeolocalizacao createNotificarRecursoEmpresaGeolocalizacao() {
        return new NotificarRecursoEmpresaGeolocalizacao();
    }

    /**
     * Create an instance of {@link PersistirTokenLeadRD }
     * 
     */
    public PersistirTokenLeadRD createPersistirTokenLeadRD() {
        return new PersistirTokenLeadRD();
    }

    /**
     * Create an instance of {@link ConsultarResumoPeriodo }
     * 
     */
    public ConsultarResumoPeriodo createConsultarResumoPeriodo() {
        return new ConsultarResumoPeriodo();
    }

    /**
     * Create an instance of {@link ConsultarGrupoDeRiscoPelaMatriculaResponse }
     * 
     */
    public ConsultarGrupoDeRiscoPelaMatriculaResponse createConsultarGrupoDeRiscoPelaMatriculaResponse() {
        return new ConsultarGrupoDeRiscoPelaMatriculaResponse();
    }

    /**
     * Create an instance of {@link ConsultarParcelasEmAberto }
     * 
     */
    public ConsultarParcelasEmAberto createConsultarParcelasEmAberto() {
        return new ConsultarParcelasEmAberto();
    }

    /**
     * Create an instance of {@link ConsultarParcelasEmAbertoResponse }
     * 
     */
    public ConsultarParcelasEmAbertoResponse createConsultarParcelasEmAbertoResponse() {
        return new ConsultarParcelasEmAbertoResponse();
    }

    /**
     * Create an instance of {@link ConsultarConsultores }
     * 
     */
    public ConsultarConsultores createConsultarConsultores() {
        return new ConsultarConsultores();
    }

    /**
     * Create an instance of {@link ListaAlunoPontosApp }
     * 
     */
    public ListaAlunoPontosApp createListaAlunoPontosApp() {
        return new ListaAlunoPontosApp();
    }

    /**
     * Create an instance of {@link AlterarEmailResponse }
     * 
     */
    public AlterarEmailResponse createAlterarEmailResponse() {
        return new AlterarEmailResponse();
    }

    /**
     * Create an instance of {@link ConsultarInfoFinanceiras }
     * 
     */
    public ConsultarInfoFinanceiras createConsultarInfoFinanceiras() {
        return new ConsultarInfoFinanceiras();
    }

    /**
     * Create an instance of {@link ObterConfiguracoesGameResponse }
     * 
     */
    public ObterConfiguracoesGameResponse createObterConfiguracoesGameResponse() {
        return new ObterConfiguracoesGameResponse();
    }

    /**
     * Create an instance of {@link AnaliseVendaDuracaoResponse }
     * 
     */
    public AnaliseVendaDuracaoResponse createAnaliseVendaDuracaoResponse() {
        return new AnaliseVendaDuracaoResponse();
    }

    /**
     * Create an instance of {@link ConsultarInfoFinanceirasResponse }
     * 
     */
    public ConsultarInfoFinanceirasResponse createConsultarInfoFinanceirasResponse() {
        return new ConsultarInfoFinanceirasResponse();
    }

    /**
     * Create an instance of {@link AtualizarDadosGerenciaisDia }
     * 
     */
    public AtualizarDadosGerenciaisDia createAtualizarDadosGerenciaisDia() {
        return new AtualizarDadosGerenciaisDia();
    }

    /**
     * Create an instance of {@link ValidarDadosOperacaoContrato }
     * 
     */
    public ValidarDadosOperacaoContrato createValidarDadosOperacaoContrato() {
        return new ValidarDadosOperacaoContrato();
    }

    /**
     * Create an instance of {@link AtualizarFotoPerfilAluno }
     * 
     */
    public AtualizarFotoPerfilAluno createAtualizarFotoPerfilAluno() {
        return new AtualizarFotoPerfilAluno();
    }

    /**
     * Create an instance of {@link ObterConfiguracoesGame }
     * 
     */
    public ObterConfiguracoesGame createObterConfiguracoesGame() {
        return new ObterConfiguracoesGame();
    }

    /**
     * Create an instance of {@link ConsultarCliente }
     * 
     */
    public ConsultarCliente createConsultarCliente() {
        return new ConsultarCliente();
    }

    /**
     * Create an instance of {@link ConsistirException }
     * 
     */
    public ConsistirException createConsistirException() {
        return new ConsistirException();
    }

    /**
     * Create an instance of {@link ConsultarAcessosDia }
     * 
     */
    public ConsultarAcessosDia createConsultarAcessosDia() {
        return new ConsultarAcessosDia();
    }

    /**
     * Create an instance of {@link ConsultarFotoKeyPessoaResponse }
     * 
     */
    public ConsultarFotoKeyPessoaResponse createConsultarFotoKeyPessoaResponse() {
        return new ConsultarFotoKeyPessoaResponse();
    }

    /**
     * Create an instance of {@link ConsultarIntegracaoFeraJson }
     * 
     */
    public ConsultarIntegracaoFeraJson createConsultarIntegracaoFeraJson() {
        return new ConsultarIntegracaoFeraJson();
    }

    /**
     * Create an instance of {@link AcessTokenValidoResponse }
     * 
     */
    public AcessTokenValidoResponse createAcessTokenValidoResponse() {
        return new AcessTokenValidoResponse();
    }

    /**
     * Create an instance of {@link ConsultarParcelaVenda }
     * 
     */
    public ConsultarParcelaVenda createConsultarParcelaVenda() {
        return new ConsultarParcelaVenda();
    }

    /**
     * Create an instance of {@link ConsultarNrAlunosAtivosForaTreinoResponse }
     * 
     */
    public ConsultarNrAlunosAtivosForaTreinoResponse createConsultarNrAlunosAtivosForaTreinoResponse() {
        return new ConsultarNrAlunosAtivosForaTreinoResponse();
    }

    /**
     * Create an instance of {@link ValidarCodigoVerificacao }
     * 
     */
    public ValidarCodigoVerificacao createValidarCodigoVerificacao() {
        return new ValidarCodigoVerificacao();
    }

    /**
     * Create an instance of {@link ConsultarAutorizacaoCobrancaResponse }
     * 
     */
    public ConsultarAutorizacaoCobrancaResponse createConsultarAutorizacaoCobrancaResponse() {
        return new ConsultarAutorizacaoCobrancaResponse();
    }

    /**
     * Create an instance of {@link AtualizarDadosGerenciais }
     * 
     */
    public AtualizarDadosGerenciais createAtualizarDadosGerenciais() {
        return new AtualizarDadosGerenciais();
    }

    /**
     * Create an instance of {@link CheckInGymPassResponse }
     * 
     */
    public CheckInGymPassResponse createCheckInGymPassResponse() {
        return new CheckInGymPassResponse();
    }

    /**
     * Create an instance of {@link AlterarSenhaUsuarioMovel }
     * 
     */
    public AlterarSenhaUsuarioMovel createAlterarSenhaUsuarioMovel() {
        return new AlterarSenhaUsuarioMovel();
    }

    /**
     * Create an instance of {@link AnaliseVendaDuracao }
     * 
     */
    public AnaliseVendaDuracao createAnaliseVendaDuracao() {
        return new AnaliseVendaDuracao();
    }

    /**
     * Create an instance of {@link ConsultarDadosBITreino }
     * 
     */
    public ConsultarDadosBITreino createConsultarDadosBITreino() {
        return new ConsultarDadosBITreino();
    }

    /**
     * Create an instance of {@link ObterClienteResponse }
     * 
     */
    public ObterClienteResponse createObterClienteResponse() {
        return new ObterClienteResponse();
    }

    /**
     * Create an instance of {@link PersistirProdutoJSON }
     * 
     */
    public PersistirProdutoJSON createPersistirProdutoJSON() {
        return new PersistirProdutoJSON();
    }

    /**
     * Create an instance of {@link ConsultarModalidadesAtivosForaTreino }
     * 
     */
    public ConsultarModalidadesAtivosForaTreino createConsultarModalidadesAtivosForaTreino() {
        return new ConsultarModalidadesAtivosForaTreino();
    }

    /**
     * Create an instance of {@link ConsultarClientes }
     * 
     */
    public ConsultarClientes createConsultarClientes() {
        return new ConsultarClientes();
    }

    /**
     * Create an instance of {@link ObterTiposColaboradoes }
     * 
     */
    public ObterTiposColaboradoes createObterTiposColaboradoes() {
        return new ObterTiposColaboradoes();
    }

    /**
     * Create an instance of {@link ConsultarAcessosListaDia }
     * 
     */
    public ConsultarAcessosListaDia createConsultarAcessosListaDia() {
        return new ConsultarAcessosListaDia();
    }

    /**
     * Create an instance of {@link RecuperarUsuarioMovelResponse }
     * 
     */
    public RecuperarUsuarioMovelResponse createRecuperarUsuarioMovelResponse() {
        return new RecuperarUsuarioMovelResponse();
    }

    /**
     * Create an instance of {@link ConsultarClientePorNomeCpf }
     * 
     */
    public ConsultarClientePorNomeCpf createConsultarClientePorNomeCpf() {
        return new ConsultarClientePorNomeCpf();
    }

    /**
     * Create an instance of {@link ConsultarClientesAtivosForaTreino }
     * 
     */
    public ConsultarClientesAtivosForaTreino createConsultarClientesAtivosForaTreino() {
        return new ConsultarClientesAtivosForaTreino();
    }

    /**
     * Create an instance of {@link ConsultarMensagensCliente }
     * 
     */
    public ConsultarMensagensCliente createConsultarMensagensCliente() {
        return new ConsultarMensagensCliente();
    }

    /**
     * Create an instance of {@link ConsultarMetasCRMResponse }
     * 
     */
    public ConsultarMetasCRMResponse createConsultarMetasCRMResponse() {
        return new ConsultarMetasCRMResponse();
    }

    /**
     * Create an instance of {@link PersistirLeadGenericoResponse }
     * 
     */
    public PersistirLeadGenericoResponse createPersistirLeadGenericoResponse() {
        return new PersistirLeadGenericoResponse();
    }

    /**
     * Create an instance of {@link Profissoes }
     * 
     */
    public Profissoes createProfissoes() {
        return new Profissoes();
    }

    /**
     * Create an instance of {@link EnviarCodigoVerificacao }
     * 
     */
    public EnviarCodigoVerificacao createEnviarCodigoVerificacao() {
        return new EnviarCodigoVerificacao();
    }

    /**
     * Create an instance of {@link ConsultarMetasCRM }
     * 
     */
    public ConsultarMetasCRM createConsultarMetasCRM() {
        return new ConsultarMetasCRM();
    }

    /**
     * Create an instance of {@link LancarProdutoClienteResponse }
     * 
     */
    public LancarProdutoClienteResponse createLancarProdutoClienteResponse() {
        return new LancarProdutoClienteResponse();
    }

    /**
     * Create an instance of {@link ConsultarDadosBITreinoResponse }
     * 
     */
    public ConsultarDadosBITreinoResponse createConsultarDadosBITreinoResponse() {
        return new ConsultarDadosBITreinoResponse();
    }

    /**
     * Create an instance of {@link ObterContratoOperacao }
     * 
     */
    public ObterContratoOperacao createObterContratoOperacao() {
        return new ObterContratoOperacao();
    }

    /**
     * Create an instance of {@link ConsultarFaturamentoDuracao }
     * 
     */
    public ConsultarFaturamentoDuracao createConsultarFaturamentoDuracao() {
        return new ConsultarFaturamentoDuracao();
    }

    /**
     * Create an instance of {@link ConsultarModalidadesEmpresaResponse }
     * 
     */
    public ConsultarModalidadesEmpresaResponse createConsultarModalidadesEmpresaResponse() {
        return new ConsultarModalidadesEmpresaResponse();
    }

    /**
     * Create an instance of {@link ConsultarParcelaVendaResponse }
     * 
     */
    public ConsultarParcelaVendaResponse createConsultarParcelaVendaResponse() {
        return new ConsultarParcelaVendaResponse();
    }

    /**
     * Create an instance of {@link LiberarCobrancaBaixaProtheusResponse }
     * 
     */
    public LiberarCobrancaBaixaProtheusResponse createLiberarCobrancaBaixaProtheusResponse() {
        return new LiberarCobrancaBaixaProtheusResponse();
    }

    /**
     * Create an instance of {@link SenhaAlteradaAposGeracaoLinkResponse }
     * 
     */
    public SenhaAlteradaAposGeracaoLinkResponse createSenhaAlteradaAposGeracaoLinkResponse() {
        return new SenhaAlteradaAposGeracaoLinkResponse();
    }

    /**
     * Create an instance of {@link ConsultarParcelasCliente }
     * 
     */
    public ConsultarParcelasCliente createConsultarParcelasCliente() {
        return new ConsultarParcelasCliente();
    }

    /**
     * Create an instance of {@link ObterPontosPorClienteResponse }
     * 
     */
    public ObterPontosPorClienteResponse createObterPontosPorClienteResponse() {
        return new ObterPontosPorClienteResponse();
    }

    /**
     * Create an instance of {@link PersistirTokenLeadRDResponse }
     * 
     */
    public PersistirTokenLeadRDResponse createPersistirTokenLeadRDResponse() {
        return new PersistirTokenLeadRDResponse();
    }

    /**
     * Create an instance of {@link PersistirClienteJSONResponse }
     * 
     */
    public PersistirClienteJSONResponse createPersistirClienteJSONResponse() {
        return new PersistirClienteJSONResponse();
    }

    /**
     * Create an instance of {@link ApiWeHelpResponse }
     * 
     */
    public ApiWeHelpResponse createApiWeHelpResponse() {
        return new ApiWeHelpResponse();
    }

    /**
     * Create an instance of {@link ConsultarLocaisAcessoResponse }
     * 
     */
    public ConsultarLocaisAcessoResponse createConsultarLocaisAcessoResponse() {
        return new ConsultarLocaisAcessoResponse();
    }

    /**
     * Create an instance of {@link ConsultarClientePorCodigoAcessoResponse }
     * 
     */
    public ConsultarClientePorCodigoAcessoResponse createConsultarClientePorCodigoAcessoResponse() {
        return new ConsultarClientePorCodigoAcessoResponse();
    }

    /**
     * Create an instance of {@link ConsultarDadosOperacaoContrato }
     * 
     */
    public ConsultarDadosOperacaoContrato createConsultarDadosOperacaoContrato() {
        return new ConsultarDadosOperacaoContrato();
    }

    /**
     * Create an instance of {@link PersistirCodeLeadRD }
     * 
     */
    public PersistirCodeLeadRD createPersistirCodeLeadRD() {
        return new PersistirCodeLeadRD();
    }

    /**
     * Create an instance of {@link ObterVencimentosContratos }
     * 
     */
    public ObterVencimentosContratos createObterVencimentosContratos() {
        return new ObterVencimentosContratos();
    }

    /**
     * Create an instance of {@link PersistirProdutoJSONResponse }
     * 
     */
    public PersistirProdutoJSONResponse createPersistirProdutoJSONResponse() {
        return new PersistirProdutoJSONResponse();
    }

    /**
     * Create an instance of {@link PersistirLogAuditoriaResponse }
     * 
     */
    public PersistirLogAuditoriaResponse createPersistirLogAuditoriaResponse() {
        return new PersistirLogAuditoriaResponse();
    }

    /**
     * Create an instance of {@link AlterarUsuarioMovelAlunoNovoResponse }
     * 
     */
    public AlterarUsuarioMovelAlunoNovoResponse createAlterarUsuarioMovelAlunoNovoResponse() {
        return new AlterarUsuarioMovelAlunoNovoResponse();
    }

    /**
     * Create an instance of {@link ConsultarResumoPeriodoResponse }
     * 
     */
    public ConsultarResumoPeriodoResponse createConsultarResumoPeriodoResponse() {
        return new ConsultarResumoPeriodoResponse();
    }

    /**
     * Create an instance of {@link SolicitacoesConcluidas }
     * 
     */
    public SolicitacoesConcluidas createSolicitacoesConcluidas() {
        return new SolicitacoesConcluidas();
    }

    /**
     * Create an instance of {@link EnderecosGeolocalizarResponse }
     * 
     */
    public EnderecosGeolocalizarResponse createEnderecosGeolocalizarResponse() {
        return new EnderecosGeolocalizarResponse();
    }

    /**
     * Create an instance of {@link BuscarLocaisAcesso }
     * 
     */
    public BuscarLocaisAcesso createBuscarLocaisAcesso() {
        return new BuscarLocaisAcesso();
    }

    /**
     * Create an instance of {@link ConsultarAtestadoClienteResponse }
     * 
     */
    public ConsultarAtestadoClienteResponse createConsultarAtestadoClienteResponse() {
        return new ConsultarAtestadoClienteResponse();
    }

    /**
     * Create an instance of {@link ConsultarClientesTreinoResponse }
     * 
     */
    public ConsultarClientesTreinoResponse createConsultarClientesTreinoResponse() {
        return new ConsultarClientesTreinoResponse();
    }

    /**
     * Create an instance of {@link ConsultarClientePorNomeCpfResponse }
     * 
     */
    public ConsultarClientePorNomeCpfResponse createConsultarClientePorNomeCpfResponse() {
        return new ConsultarClientePorNomeCpfResponse();
    }

    /**
     * Create an instance of {@link ConsultarConsultoresResponse }
     * 
     */
    public ConsultarConsultoresResponse createConsultarConsultoresResponse() {
        return new ConsultarConsultoresResponse();
    }

    /**
     * Create an instance of {@link PersistirVendaAvulsaJSONResponse }
     * 
     */
    public PersistirVendaAvulsaJSONResponse createPersistirVendaAvulsaJSONResponse() {
        return new PersistirVendaAvulsaJSONResponse();
    }

    /**
     * Create an instance of {@link ConsultarJustificativasOperacaoResponse }
     * 
     */
    public ConsultarJustificativasOperacaoResponse createConsultarJustificativasOperacaoResponse() {
        return new ConsultarJustificativasOperacaoResponse();
    }

    /**
     * Create an instance of {@link EnviarMensagemAoUsuarioUsername }
     * 
     */
    public EnviarMensagemAoUsuarioUsername createEnviarMensagemAoUsuarioUsername() {
        return new EnviarMensagemAoUsuarioUsername();
    }

    /**
     * Create an instance of {@link ConsultarClienteJson }
     * 
     */
    public ConsultarClienteJson createConsultarClienteJson() {
        return new ConsultarClienteJson();
    }

    /**
     * Create an instance of {@link AlterarEndereco }
     * 
     */
    public AlterarEndereco createAlterarEndereco() {
        return new AlterarEndereco();
    }

    /**
     * Create an instance of {@link BuscarEstatisticasMovideskResponse }
     * 
     */
    public BuscarEstatisticasMovideskResponse createBuscarEstatisticasMovideskResponse() {
        return new BuscarEstatisticasMovideskResponse();
    }

    /**
     * Create an instance of {@link ConsultarAlunos }
     * 
     */
    public ConsultarAlunos createConsultarAlunos() {
        return new ConsultarAlunos();
    }

    /**
     * Create an instance of {@link ConsultarClientesPeloNomeResponse }
     * 
     */
    public ConsultarClientesPeloNomeResponse createConsultarClientesPeloNomeResponse() {
        return new ConsultarClientesPeloNomeResponse();
    }

    /**
     * Create an instance of {@link ListaProdutosAtestado }
     * 
     */
    public ListaProdutosAtestado createListaProdutosAtestado() {
        return new ListaProdutosAtestado();
    }

    /**
     * Create an instance of {@link ConsultarEstadoResponse }
     * 
     */
    public ConsultarEstadoResponse createConsultarEstadoResponse() {
        return new ConsultarEstadoResponse();
    }

    /**
     * Create an instance of {@link ConsultarEstatisticaSolicitacaoResponse }
     * 
     */
    public ConsultarEstatisticaSolicitacaoResponse createConsultarEstatisticaSolicitacaoResponse() {
        return new ConsultarEstatisticaSolicitacaoResponse();
    }

    /**
     * Create an instance of {@link MarcarStatusBG }
     * 
     */
    public MarcarStatusBG createMarcarStatusBG() {
        return new MarcarStatusBG();
    }

    /**
     * Create an instance of {@link AtualizarDadosGerenciaisPorMesResponse }
     * 
     */
    public AtualizarDadosGerenciaisPorMesResponse createAtualizarDadosGerenciaisPorMesResponse() {
        return new AtualizarDadosGerenciaisPorMesResponse();
    }

    /**
     * Create an instance of {@link ConsultarMeioRecuperarLoginResponse }
     * 
     */
    public ConsultarMeioRecuperarLoginResponse createConsultarMeioRecuperarLoginResponse() {
        return new ConsultarMeioRecuperarLoginResponse();
    }

    /**
     * Create an instance of {@link AlterarSenhaUsuarioEmail }
     * 
     */
    public AlterarSenhaUsuarioEmail createAlterarSenhaUsuarioEmail() {
        return new AlterarSenhaUsuarioEmail();
    }

    /**
     * Create an instance of {@link PersistirClienteJSON }
     * 
     */
    public PersistirClienteJSON createPersistirClienteJSON() {
        return new PersistirClienteJSON();
    }

    /**
     * Create an instance of {@link ConsultarContratosClienteResponse }
     * 
     */
    public ConsultarContratosClienteResponse createConsultarContratosClienteResponse() {
        return new ConsultarContratosClienteResponse();
    }

    /**
     * Create an instance of {@link ConsultarQuantidadeAcessosClientesAgrupadosDiaResponse }
     * 
     */
    public ConsultarQuantidadeAcessosClientesAgrupadosDiaResponse createConsultarQuantidadeAcessosClientesAgrupadosDiaResponse() {
        return new ConsultarQuantidadeAcessosClientesAgrupadosDiaResponse();
    }

    /**
     * Create an instance of {@link ConsultarClienteJsonResponse }
     * 
     */
    public ConsultarClienteJsonResponse createConsultarClienteJsonResponse() {
        return new ConsultarClienteJsonResponse();
    }

    /**
     * Create an instance of {@link DownloadBoleto }
     * 
     */
    public DownloadBoleto createDownloadBoleto() {
        return new DownloadBoleto();
    }

    /**
     * Create an instance of {@link BuscarClientesPesquisaTW }
     * 
     */
    public BuscarClientesPesquisaTW createBuscarClientesPesquisaTW() {
        return new BuscarClientesPesquisaTW();
    }

    /**
     * Create an instance of {@link VerificarUsuarioMovel }
     * 
     */
    public VerificarUsuarioMovel createVerificarUsuarioMovel() {
        return new VerificarUsuarioMovel();
    }

    /**
     * Create an instance of {@link NomenclaturaVendaCredito }
     * 
     */
    public NomenclaturaVendaCredito createNomenclaturaVendaCredito() {
        return new NomenclaturaVendaCredito();
    }

    /**
     * Create an instance of {@link LancarConviteResponse }
     * 
     */
    public LancarConviteResponse createLancarConviteResponse() {
        return new LancarConviteResponse();
    }

    /**
     * Create an instance of {@link MarcarAvaliacaoFeedGestao }
     * 
     */
    public MarcarAvaliacaoFeedGestao createMarcarAvaliacaoFeedGestao() {
        return new MarcarAvaliacaoFeedGestao();
    }

    /**
     * Create an instance of {@link RecuperarLoginResponse }
     * 
     */
    public RecuperarLoginResponse createRecuperarLoginResponse() {
        return new RecuperarLoginResponse();
    }

    /**
     * Create an instance of {@link ProfissoesResponse }
     * 
     */
    public ProfissoesResponse createProfissoesResponse() {
        return new ProfissoesResponse();
    }

    /**
     * Create an instance of {@link RetornoTrancamento }
     * 
     */
    public RetornoTrancamento createRetornoTrancamento() {
        return new RetornoTrancamento();
    }

    /**
     * Create an instance of {@link ConsultarProdutosCliente }
     * 
     */
    public ConsultarProdutosCliente createConsultarProdutosCliente() {
        return new ConsultarProdutosCliente();
    }

    /**
     * Create an instance of {@link AlterarSenhaUsuarioMovelResponse }
     * 
     */
    public AlterarSenhaUsuarioMovelResponse createAlterarSenhaUsuarioMovelResponse() {
        return new AlterarSenhaUsuarioMovelResponse();
    }

    /**
     * Create an instance of {@link ConsultarProfessoresPeloNome }
     * 
     */
    public ConsultarProfessoresPeloNome createConsultarProfessoresPeloNome() {
        return new ConsultarProfessoresPeloNome();
    }

    /**
     * Create an instance of {@link PersistirCodeLeadRDResponse }
     * 
     */
    public PersistirCodeLeadRDResponse createPersistirCodeLeadRDResponse() {
        return new PersistirCodeLeadRDResponse();
    }

    /**
     * Create an instance of {@link ConsultarEmpresasSimplesResponse }
     * 
     */
    public ConsultarEmpresasSimplesResponse createConsultarEmpresasSimplesResponse() {
        return new ConsultarEmpresasSimplesResponse();
    }

    /**
     * Create an instance of {@link ObterContratoOperacaoResponse }
     * 
     */
    public ObterContratoOperacaoResponse createObterContratoOperacaoResponse() {
        return new ObterContratoOperacaoResponse();
    }

    /**
     * Create an instance of {@link PersistirVendaAvulsaJSON }
     * 
     */
    public PersistirVendaAvulsaJSON createPersistirVendaAvulsaJSON() {
        return new PersistirVendaAvulsaJSON();
    }

    /**
     * Create an instance of {@link PersistirCancelarContratoJSONResponse }
     * 
     */
    public PersistirCancelarContratoJSONResponse createPersistirCancelarContratoJSONResponse() {
        return new PersistirCancelarContratoJSONResponse();
    }

    /**
     * Create an instance of {@link ConsultarColaboradores }
     * 
     */
    public ConsultarColaboradores createConsultarColaboradores() {
        return new ConsultarColaboradores();
    }

    /**
     * Create an instance of {@link PersistirAcessosClienteJSON }
     * 
     */
    public PersistirAcessosClienteJSON createPersistirAcessosClienteJSON() {
        return new PersistirAcessosClienteJSON();
    }

    /**
     * Create an instance of {@link SituacaoUsuarioResponse }
     * 
     */
    public SituacaoUsuarioResponse createSituacaoUsuarioResponse() {
        return new SituacaoUsuarioResponse();
    }

    /**
     * Create an instance of {@link ConsultarClientesForaTreino }
     * 
     */
    public ConsultarClientesForaTreino createConsultarClientesForaTreino() {
        return new ConsultarClientesForaTreino();
    }

    /**
     * Create an instance of {@link GerarVinculoProfessorAlunoResponse }
     * 
     */
    public GerarVinculoProfessorAlunoResponse createGerarVinculoProfessorAlunoResponse() {
        return new GerarVinculoProfessorAlunoResponse();
    }

    /**
     * Create an instance of {@link ConsultarIntegracaoFeraJsonResponse }
     * 
     */
    public ConsultarIntegracaoFeraJsonResponse createConsultarIntegracaoFeraJsonResponse() {
        return new ConsultarIntegracaoFeraJsonResponse();
    }

    /**
     * Create an instance of {@link EnviarNotificacaoUsuarioResponse }
     * 
     */
    public EnviarNotificacaoUsuarioResponse createEnviarNotificacaoUsuarioResponse() {
        return new EnviarNotificacaoUsuarioResponse();
    }

    /**
     * Create an instance of {@link EnderecosGeolocalizarAposHabilitarChaveResponse }
     * 
     */
    public EnderecosGeolocalizarAposHabilitarChaveResponse createEnderecosGeolocalizarAposHabilitarChaveResponse() {
        return new EnderecosGeolocalizarAposHabilitarChaveResponse();
    }

    /**
     * Create an instance of {@link MarcarAvaliacaoFeedGestaoResponse }
     * 
     */
    public MarcarAvaliacaoFeedGestaoResponse createMarcarAvaliacaoFeedGestaoResponse() {
        return new MarcarAvaliacaoFeedGestaoResponse();
    }

    /**
     * Create an instance of {@link ObterDadosContratoPorDuracaoResponse }
     * 
     */
    public ObterDadosContratoPorDuracaoResponse createObterDadosContratoPorDuracaoResponse() {
        return new ObterDadosContratoPorDuracaoResponse();
    }

    /**
     * Create an instance of {@link RecuperarUsuarioMovel }
     * 
     */
    public RecuperarUsuarioMovel createRecuperarUsuarioMovel() {
        return new RecuperarUsuarioMovel();
    }

    /**
     * Create an instance of {@link ConsultarFrequenciaAluno }
     * 
     */
    public ConsultarFrequenciaAluno createConsultarFrequenciaAluno() {
        return new ConsultarFrequenciaAluno();
    }

    /**
     * Create an instance of {@link EnviarCodigoVerificacaoResponse }
     * 
     */
    public EnviarCodigoVerificacaoResponse createEnviarCodigoVerificacaoResponse() {
        return new EnviarCodigoVerificacaoResponse();
    }

    /**
     * Create an instance of {@link ConsultarGrupoDeRiscoPelaMatricula }
     * 
     */
    public ConsultarGrupoDeRiscoPelaMatricula createConsultarGrupoDeRiscoPelaMatricula() {
        return new ConsultarGrupoDeRiscoPelaMatricula();
    }

    /**
     * Create an instance of {@link EnderecosGeolocalizar }
     * 
     */
    public EnderecosGeolocalizar createEnderecosGeolocalizar() {
        return new EnderecosGeolocalizar();
    }

    /**
     * Create an instance of {@link AtualizarGeolocalizacaoResponse }
     * 
     */
    public AtualizarGeolocalizacaoResponse createAtualizarGeolocalizacaoResponse() {
        return new AtualizarGeolocalizacaoResponse();
    }

    /**
     * Create an instance of {@link IncluirAssinaturaDigitalResponse }
     * 
     */
    public IncluirAssinaturaDigitalResponse createIncluirAssinaturaDigitalResponse() {
        return new IncluirAssinaturaDigitalResponse();
    }

    /**
     * Create an instance of {@link LancarProdutoAtestadoResponse }
     * 
     */
    public LancarProdutoAtestadoResponse createLancarProdutoAtestadoResponse() {
        return new LancarProdutoAtestadoResponse();
    }

    /**
     * Create an instance of {@link ConsultarClienteSintetico }
     * 
     */
    public ConsultarClienteSintetico createConsultarClienteSintetico() {
        return new ConsultarClienteSintetico();
    }

    /**
     * Create an instance of {@link ConsultarCidade }
     * 
     */
    public ConsultarCidade createConsultarCidade() {
        return new ConsultarCidade();
    }

    /**
     * Create an instance of {@link ListaAlunoPontos }
     * 
     */
    public ListaAlunoPontos createListaAlunoPontos() {
        return new ListaAlunoPontos();
    }

    /**
     * Create an instance of {@link ValidarUsuarioTelefone }
     * 
     */
    public ValidarUsuarioTelefone createValidarUsuarioTelefone() {
        return new ValidarUsuarioTelefone();
    }

    /**
     * Create an instance of {@link ConsultarJustificativasOperacao }
     * 
     */
    public ConsultarJustificativasOperacao createConsultarJustificativasOperacao() {
        return new ConsultarJustificativasOperacao();
    }

    /**
     * Create an instance of {@link ListaClientesEstacionamentoSelfit }
     * 
     */
    public ListaClientesEstacionamentoSelfit createListaClientesEstacionamentoSelfit() {
        return new ListaClientesEstacionamentoSelfit();
    }

    /**
     * Create an instance of {@link GerarVinculoProfessorAluno }
     * 
     */
    public GerarVinculoProfessorAluno createGerarVinculoProfessorAluno() {
        return new GerarVinculoProfessorAluno();
    }

    /**
     * Create an instance of {@link ConsultarClienteSinteticoResponse }
     * 
     */
    public ConsultarClienteSinteticoResponse createConsultarClienteSinteticoResponse() {
        return new ConsultarClienteSinteticoResponse();
    }

    /**
     * Create an instance of {@link DeletarPagamentosContratoImportacaoJSON }
     * 
     */
    public DeletarPagamentosContratoImportacaoJSON createDeletarPagamentosContratoImportacaoJSON() {
        return new DeletarPagamentosContratoImportacaoJSON();
    }

    /**
     * Create an instance of {@link DeletarPagamentosContratoImportacaoJSONResponse }
     * 
     */
    public DeletarPagamentosContratoImportacaoJSONResponse createDeletarPagamentosContratoImportacaoJSONResponse() {
        return new DeletarPagamentosContratoImportacaoJSONResponse();
    }

    /**
     * Create an instance of {@link ConsultarClientesForaTreinoResponse }
     * 
     */
    public ConsultarClientesForaTreinoResponse createConsultarClientesForaTreinoResponse() {
        return new ConsultarClientesForaTreinoResponse();
    }

    /**
     * Create an instance of {@link PersistirClienteSite }
     * 
     */
    public PersistirClienteSite createPersistirClienteSite() {
        return new PersistirClienteSite();
    }

    /**
     * Create an instance of {@link BuscarLocaisAcessoResponse }
     * 
     */
    public BuscarLocaisAcessoResponse createBuscarLocaisAcessoResponse() {
        return new BuscarLocaisAcessoResponse();
    }

    /**
     * Create an instance of {@link GerarVendaCreditosResponse }
     * 
     */
    public GerarVendaCreditosResponse createGerarVendaCreditosResponse() {
        return new GerarVendaCreditosResponse();
    }

    /**
     * Create an instance of {@link ConsultarMensagensClienteResponse }
     * 
     */
    public ConsultarMensagensClienteResponse createConsultarMensagensClienteResponse() {
        return new ConsultarMensagensClienteResponse();
    }

    /**
     * Create an instance of {@link EnviarMensagemAoUsuarioResponse }
     * 
     */
    public EnviarMensagemAoUsuarioResponse createEnviarMensagemAoUsuarioResponse() {
        return new EnviarMensagemAoUsuarioResponse();
    }

    /**
     * Create an instance of {@link BuscaIDSecretResponse }
     * 
     */
    public BuscaIDSecretResponse createBuscaIDSecretResponse() {
        return new BuscaIDSecretResponse();
    }

    /**
     * Create an instance of {@link ValidarAlunoProdutoVigente }
     * 
     */
    public ValidarAlunoProdutoVigente createValidarAlunoProdutoVigente() {
        return new ValidarAlunoProdutoVigente();
    }

    /**
     * Create an instance of {@link PersistirPagamentosContratoJSON }
     * 
     */
    public PersistirPagamentosContratoJSON createPersistirPagamentosContratoJSON() {
        return new PersistirPagamentosContratoJSON();
    }

    /**
     * Create an instance of {@link EnviarEmailColaborador }
     * 
     */
    public EnviarEmailColaborador createEnviarEmailColaborador() {
        return new EnviarEmailColaborador();
    }

    /**
     * Create an instance of {@link ConsultarAmbientes }
     * 
     */
    public ConsultarAmbientes createConsultarAmbientes() {
        return new ConsultarAmbientes();
    }

    /**
     * Create an instance of {@link LancarProdutoClienteVAlorResponse }
     * 
     */
    public LancarProdutoClienteVAlorResponse createLancarProdutoClienteVAlorResponse() {
        return new LancarProdutoClienteVAlorResponse();
    }

    /**
     * Create an instance of {@link ConsultaGenericaResponse }
     * 
     */
    public ConsultaGenericaResponse createConsultaGenericaResponse() {
        return new ConsultaGenericaResponse();
    }

    /**
     * Create an instance of {@link PersistirPagamentosContratoJSONResponse }
     * 
     */
    public PersistirPagamentosContratoJSONResponse createPersistirPagamentosContratoJSONResponse() {
        return new PersistirPagamentosContratoJSONResponse();
    }

    /**
     * Create an instance of {@link PersistirPagamentosVendaAvulsaJSONResponse }
     * 
     */
    public PersistirPagamentosVendaAvulsaJSONResponse createPersistirPagamentosVendaAvulsaJSONResponse() {
        return new PersistirPagamentosVendaAvulsaJSONResponse();
    }

    /**
     * Create an instance of {@link ConsultarConfiguracaoENotas }
     * 
     */
    public ConsultarConfiguracaoENotas createConsultarConfiguracaoENotas() {
        return new ConsultarConfiguracaoENotas();
    }

    /**
     * Create an instance of {@link ConsultarContratosRenovar }
     * 
     */
    public ConsultarContratosRenovar createConsultarContratosRenovar() {
        return new ConsultarContratosRenovar();
    }

    /**
     * Create an instance of {@link GravarDadosOperacaoContratoResponse }
     * 
     */
    public GravarDadosOperacaoContratoResponse createGravarDadosOperacaoContratoResponse() {
        return new GravarDadosOperacaoContratoResponse();
    }

    /**
     * Create an instance of {@link ConsultarAmbientesResponse }
     * 
     */
    public ConsultarAmbientesResponse createConsultarAmbientesResponse() {
        return new ConsultarAmbientesResponse();
    }

    /**
     * Create an instance of {@link ObterEnderecosResponse }
     * 
     */
    public ObterEnderecosResponse createObterEnderecosResponse() {
        return new ObterEnderecosResponse();
    }

    /**
     * Create an instance of {@link MarcarStatusBGResponse }
     * 
     */
    public MarcarStatusBGResponse createMarcarStatusBGResponse() {
        return new MarcarStatusBGResponse();
    }

    /**
     * Create an instance of {@link ObterTiposColaboradoesResponse }
     * 
     */
    public ObterTiposColaboradoesResponse createObterTiposColaboradoesResponse() {
        return new ObterTiposColaboradoesResponse();
    }

    /**
     * Create an instance of {@link CheckInGymPass }
     * 
     */
    public CheckInGymPass createCheckInGymPass() {
        return new CheckInGymPass();
    }

    /**
     * Create an instance of {@link ConsultarFeedGestao }
     * 
     */
    public ConsultarFeedGestao createConsultarFeedGestao() {
        return new ConsultarFeedGestao();
    }

    /**
     * Create an instance of {@link ConsultarLocalAcessoPorNFCResponse }
     * 
     */
    public ConsultarLocalAcessoPorNFCResponse createConsultarLocalAcessoPorNFCResponse() {
        return new ConsultarLocalAcessoPorNFCResponse();
    }

    /**
     * Create an instance of {@link DeletarContratoImportacaoJSON }
     * 
     */
    public DeletarContratoImportacaoJSON createDeletarContratoImportacaoJSON() {
        return new DeletarContratoImportacaoJSON();
    }

    /**
     * Create an instance of {@link EnviarEmailGenerico }
     * 
     */
    public EnviarEmailGenerico createEnviarEmailGenerico() {
        return new EnviarEmailGenerico();
    }

    /**
     * Create an instance of {@link AlterarSenhaUsuarioResponse }
     * 
     */
    public AlterarSenhaUsuarioResponse createAlterarSenhaUsuarioResponse() {
        return new AlterarSenhaUsuarioResponse();
    }

    /**
     * Create an instance of {@link BuscaIDSecret }
     * 
     */
    public BuscaIDSecret createBuscaIDSecret() {
        return new BuscaIDSecret();
    }

    /**
     * Create an instance of {@link ClassificacoesResponse }
     * 
     */
    public ClassificacoesResponse createClassificacoesResponse() {
        return new ClassificacoesResponse();
    }

    /**
     * Create an instance of {@link ConsultarClienteSinteticoPorEmailPessoaResponse }
     * 
     */
    public ConsultarClienteSinteticoPorEmailPessoaResponse createConsultarClienteSinteticoPorEmailPessoaResponse() {
        return new ConsultarClienteSinteticoPorEmailPessoaResponse();
    }

    /**
     * Create an instance of {@link ObterEnderecoCodigoResponse }
     * 
     */
    public ObterEnderecoCodigoResponse createObterEnderecoCodigoResponse() {
        return new ObterEnderecoCodigoResponse();
    }

    /**
     * Create an instance of {@link ObterEnderecoEmpresa }
     * 
     */
    public ObterEnderecoEmpresa createObterEnderecoEmpresa() {
        return new ObterEnderecoEmpresa();
    }

    /**
     * Create an instance of {@link ConsultarEmpresasResponse }
     * 
     */
    public ConsultarEmpresasResponse createConsultarEmpresasResponse() {
        return new ConsultarEmpresasResponse();
    }

    /**
     * Create an instance of {@link EnviarEmailAngularResponse }
     * 
     */
    public EnviarEmailAngularResponse createEnviarEmailAngularResponse() {
        return new EnviarEmailAngularResponse();
    }

    /**
     * Create an instance of {@link ConsultarDadosGame }
     * 
     */
    public ConsultarDadosGame createConsultarDadosGame() {
        return new ConsultarDadosGame();
    }

    /**
     * Create an instance of {@link AlterarTelefone }
     * 
     */
    public AlterarTelefone createAlterarTelefone() {
        return new AlterarTelefone();
    }

    /**
     * Create an instance of {@link MarcarComoLidaFeedGestaoResponse }
     * 
     */
    public MarcarComoLidaFeedGestaoResponse createMarcarComoLidaFeedGestaoResponse() {
        return new MarcarComoLidaFeedGestaoResponse();
    }

    /**
     * Create an instance of {@link AtualizarBoletoPJBank }
     * 
     */
    public AtualizarBoletoPJBank createAtualizarBoletoPJBank() {
        return new AtualizarBoletoPJBank();
    }

    /**
     * Create an instance of {@link ValidarDadosOperacaoContratoResponse }
     * 
     */
    public ValidarDadosOperacaoContratoResponse createValidarDadosOperacaoContratoResponse() {
        return new ValidarDadosOperacaoContratoResponse();
    }

    /**
     * Create an instance of {@link ConsultarCupomDesconto }
     * 
     */
    public ConsultarCupomDesconto createConsultarCupomDesconto() {
        return new ConsultarCupomDesconto();
    }

    /**
     * Create an instance of {@link LancarProdutoCliente }
     * 
     */
    public LancarProdutoCliente createLancarProdutoCliente() {
        return new LancarProdutoCliente();
    }

    /**
     * Create an instance of {@link ObterQuestionario }
     * 
     */
    public ObterQuestionario createObterQuestionario() {
        return new ObterQuestionario();
    }

    /**
     * Create an instance of {@link ResponderQuestionario }
     * 
     */
    public ResponderQuestionario createResponderQuestionario() {
        return new ResponderQuestionario();
    }

    /**
     * Create an instance of {@link LancarProdutoClienteVAlor }
     * 
     */
    public LancarProdutoClienteVAlor createLancarProdutoClienteVAlor() {
        return new LancarProdutoClienteVAlor();
    }

    /**
     * Create an instance of {@link ValidarAlunoProdutoVigenteResponse }
     * 
     */
    public ValidarAlunoProdutoVigenteResponse createValidarAlunoProdutoVigenteResponse() {
        return new ValidarAlunoProdutoVigenteResponse();
    }

    /**
     * Create an instance of {@link ConsultarClientesPeloNomeNoLimitsResponse }
     * 
     */
    public ConsultarClientesPeloNomeNoLimitsResponse createConsultarClientesPeloNomeNoLimitsResponse() {
        return new ConsultarClientesPeloNomeNoLimitsResponse();
    }

    /**
     * Create an instance of {@link PersistirOperacoesContratoJSONResponse }
     * 
     */
    public PersistirOperacoesContratoJSONResponse createPersistirOperacoesContratoJSONResponse() {
        return new PersistirOperacoesContratoJSONResponse();
    }

    /**
     * Create an instance of {@link LogarUsuarioMovelResponse }
     * 
     */
    public LogarUsuarioMovelResponse createLogarUsuarioMovelResponse() {
        return new LogarUsuarioMovelResponse();
    }

    /**
     * Create an instance of {@link ConsultarColaboradoresResponse }
     * 
     */
    public ConsultarColaboradoresResponse createConsultarColaboradoresResponse() {
        return new ConsultarColaboradoresResponse();
    }

    /**
     * Create an instance of {@link ConsultarFaturamentoDuracaoResponse }
     * 
     */
    public ConsultarFaturamentoDuracaoResponse createConsultarFaturamentoDuracaoResponse() {
        return new ConsultarFaturamentoDuracaoResponse();
    }

    /**
     * Create an instance of {@link ConsultarAutorizacaoCobranca }
     * 
     */
    public ConsultarAutorizacaoCobranca createConsultarAutorizacaoCobranca() {
        return new ConsultarAutorizacaoCobranca();
    }

    /**
     * Create an instance of {@link AtualizarFotoPerfilAlunoResponse }
     * 
     */
    public AtualizarFotoPerfilAlunoResponse createAtualizarFotoPerfilAlunoResponse() {
        return new AtualizarFotoPerfilAlunoResponse();
    }

    /**
     * Create an instance of {@link ConsultarModalidadesAtivosForaTreinoResponse }
     * 
     */
    public ConsultarModalidadesAtivosForaTreinoResponse createConsultarModalidadesAtivosForaTreinoResponse() {
        return new ConsultarModalidadesAtivosForaTreinoResponse();
    }

    /**
     * Create an instance of {@link UrlFotoEmpresa }
     * 
     */
    public UrlFotoEmpresa createUrlFotoEmpresa() {
        return new UrlFotoEmpresa();
    }

    /**
     * Create an instance of {@link Exception }
     * 
     */
    public Exception createException() {
        return new Exception();
    }

    /**
     * Create an instance of {@link AtualizarDadosGerenciaisResponse }
     * 
     */
    public AtualizarDadosGerenciaisResponse createAtualizarDadosGerenciaisResponse() {
        return new AtualizarDadosGerenciaisResponse();
    }

    /**
     * Create an instance of {@link ConsultarColetoresResponse }
     * 
     */
    public ConsultarColetoresResponse createConsultarColetoresResponse() {
        return new ConsultarColetoresResponse();
    }

    /**
     * Create an instance of {@link UsuarioTO }
     * 
     */
    public UsuarioTO createUsuarioTO() {
        return new UsuarioTO();
    }

    /**
     * Create an instance of {@link ClienteWS }
     * 
     */
    public ClienteWS createClienteWS() {
        return new ClienteWS();
    }

    /**
     * Create an instance of {@link EnderecoWS }
     * 
     */
    public EnderecoWS createEnderecoWS() {
        return new EnderecoWS();
    }

    /**
     * Create an instance of {@link EmailWS }
     * 
     */
    public EmailWS createEmailWS() {
        return new EmailWS();
    }

    /**
     * Create an instance of {@link TelefoneWS }
     * 
     */
    public TelefoneWS createTelefoneWS() {
        return new TelefoneWS();
    }

    /**
     * Create an instance of {@link AutorizacaoCobrancaClienteWS }
     * 
     */
    public AutorizacaoCobrancaClienteWS createAutorizacaoCobrancaClienteWS() {
        return new AutorizacaoCobrancaClienteWS();
    }

    /**
     * Create an instance of {@link SuperTO }
     * 
     */
    public SuperTO createSuperTO() {
        return new SuperTO();
    }

    /**
     * Create an instance of {@link ClienteMensagemWS }
     * 
     */
    public ClienteMensagemWS createClienteMensagemWS() {
        return new ClienteMensagemWS();
    }

    /**
     * Create an instance of {@link EmpresaWS }
     * 
     */
    public EmpresaWS createEmpresaWS() {
        return new EmpresaWS();
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link PersistirClienteJSONResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "persistirClienteJSONResponse")
    public JAXBElement<PersistirClienteJSONResponse> createPersistirClienteJSONResponse(PersistirClienteJSONResponse value) {
        return new JAXBElement<PersistirClienteJSONResponse>(_PersistirClienteJSONResponse_QNAME, PersistirClienteJSONResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ApiWeHelpResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "apiWeHelpResponse")
    public JAXBElement<ApiWeHelpResponse> createApiWeHelpResponse(ApiWeHelpResponse value) {
        return new JAXBElement<ApiWeHelpResponse>(_ApiWeHelpResponse_QNAME, ApiWeHelpResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarParcelasCliente }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarParcelasCliente")
    public JAXBElement<ConsultarParcelasCliente> createConsultarParcelasCliente(ConsultarParcelasCliente value) {
        return new JAXBElement<ConsultarParcelasCliente>(_ConsultarParcelasCliente_QNAME, ConsultarParcelasCliente.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ObterPontosPorClienteResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "obterPontosPorClienteResponse")
    public JAXBElement<ObterPontosPorClienteResponse> createObterPontosPorClienteResponse(ObterPontosPorClienteResponse value) {
        return new JAXBElement<ObterPontosPorClienteResponse>(_ObterPontosPorClienteResponse_QNAME, ObterPontosPorClienteResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link PersistirTokenLeadRDResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "persistirTokenLeadRDResponse")
    public JAXBElement<PersistirTokenLeadRDResponse> createPersistirTokenLeadRDResponse(PersistirTokenLeadRDResponse value) {
        return new JAXBElement<PersistirTokenLeadRDResponse>(_PersistirTokenLeadRDResponse_QNAME, PersistirTokenLeadRDResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link PersistirCodeLeadRD }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "persistirCodeLeadRD")
    public JAXBElement<PersistirCodeLeadRD> createPersistirCodeLeadRD(PersistirCodeLeadRD value) {
        return new JAXBElement<PersistirCodeLeadRD>(_PersistirCodeLeadRD_QNAME, PersistirCodeLeadRD.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarLocaisAcessoResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarLocaisAcessoResponse")
    public JAXBElement<ConsultarLocaisAcessoResponse> createConsultarLocaisAcessoResponse(ConsultarLocaisAcessoResponse value) {
        return new JAXBElement<ConsultarLocaisAcessoResponse>(_ConsultarLocaisAcessoResponse_QNAME, ConsultarLocaisAcessoResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarDadosOperacaoContrato }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarDadosOperacaoContrato")
    public JAXBElement<ConsultarDadosOperacaoContrato> createConsultarDadosOperacaoContrato(ConsultarDadosOperacaoContrato value) {
        return new JAXBElement<ConsultarDadosOperacaoContrato>(_ConsultarDadosOperacaoContrato_QNAME, ConsultarDadosOperacaoContrato.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarClientePorCodigoAcessoResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarClientePorCodigoAcessoResponse")
    public JAXBElement<ConsultarClientePorCodigoAcessoResponse> createConsultarClientePorCodigoAcessoResponse(ConsultarClientePorCodigoAcessoResponse value) {
        return new JAXBElement<ConsultarClientePorCodigoAcessoResponse>(_ConsultarClientePorCodigoAcessoResponse_QNAME, ConsultarClientePorCodigoAcessoResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ObterVencimentosContratos }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "obterVencimentosContratos")
    public JAXBElement<ObterVencimentosContratos> createObterVencimentosContratos(ObterVencimentosContratos value) {
        return new JAXBElement<ObterVencimentosContratos>(_ObterVencimentosContratos_QNAME, ObterVencimentosContratos.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarResumoPeriodoResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarResumoPeriodoResponse")
    public JAXBElement<ConsultarResumoPeriodoResponse> createConsultarResumoPeriodoResponse(ConsultarResumoPeriodoResponse value) {
        return new JAXBElement<ConsultarResumoPeriodoResponse>(_ConsultarResumoPeriodoResponse_QNAME, ConsultarResumoPeriodoResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link AlterarUsuarioMovelAlunoNovoResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "alterarUsuarioMovelAlunoNovoResponse")
    public JAXBElement<AlterarUsuarioMovelAlunoNovoResponse> createAlterarUsuarioMovelAlunoNovoResponse(AlterarUsuarioMovelAlunoNovoResponse value) {
        return new JAXBElement<AlterarUsuarioMovelAlunoNovoResponse>(_AlterarUsuarioMovelAlunoNovoResponse_QNAME, AlterarUsuarioMovelAlunoNovoResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link SolicitacoesConcluidas }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "solicitacoesConcluidas")
    public JAXBElement<SolicitacoesConcluidas> createSolicitacoesConcluidas(SolicitacoesConcluidas value) {
        return new JAXBElement<SolicitacoesConcluidas>(_SolicitacoesConcluidas_QNAME, SolicitacoesConcluidas.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link PersistirProdutoJSONResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "persistirProdutoJSONResponse")
    public JAXBElement<PersistirProdutoJSONResponse> createPersistirProdutoJSONResponse(PersistirProdutoJSONResponse value) {
        return new JAXBElement<PersistirProdutoJSONResponse>(_PersistirProdutoJSONResponse_QNAME, PersistirProdutoJSONResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link PersistirLogAuditoriaResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "persistirLogAuditoriaResponse")
    public JAXBElement<PersistirLogAuditoriaResponse> createPersistirLogAuditoriaResponse(PersistirLogAuditoriaResponse value) {
        return new JAXBElement<PersistirLogAuditoriaResponse>(_PersistirLogAuditoriaResponse_QNAME, PersistirLogAuditoriaResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link LancarProdutoClienteResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "lancarProdutoClienteResponse")
    public JAXBElement<LancarProdutoClienteResponse> createLancarProdutoClienteResponse(LancarProdutoClienteResponse value) {
        return new JAXBElement<LancarProdutoClienteResponse>(_LancarProdutoClienteResponse_QNAME, LancarProdutoClienteResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarMetasCRM }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarMetasCRM")
    public JAXBElement<ConsultarMetasCRM> createConsultarMetasCRM(ConsultarMetasCRM value) {
        return new JAXBElement<ConsultarMetasCRM>(_ConsultarMetasCRM_QNAME, ConsultarMetasCRM.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ObterContratoOperacao }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "obterContratoOperacao")
    public JAXBElement<ObterContratoOperacao> createObterContratoOperacao(ObterContratoOperacao value) {
        return new JAXBElement<ObterContratoOperacao>(_ObterContratoOperacao_QNAME, ObterContratoOperacao.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarDadosBITreinoResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarDadosBITreinoResponse")
    public JAXBElement<ConsultarDadosBITreinoResponse> createConsultarDadosBITreinoResponse(ConsultarDadosBITreinoResponse value) {
        return new JAXBElement<ConsultarDadosBITreinoResponse>(_ConsultarDadosBITreinoResponse_QNAME, ConsultarDadosBITreinoResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarFaturamentoDuracao }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarFaturamentoDuracao")
    public JAXBElement<ConsultarFaturamentoDuracao> createConsultarFaturamentoDuracao(ConsultarFaturamentoDuracao value) {
        return new JAXBElement<ConsultarFaturamentoDuracao>(_ConsultarFaturamentoDuracao_QNAME, ConsultarFaturamentoDuracao.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarModalidadesEmpresaResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarModalidadesEmpresaResponse")
    public JAXBElement<ConsultarModalidadesEmpresaResponse> createConsultarModalidadesEmpresaResponse(ConsultarModalidadesEmpresaResponse value) {
        return new JAXBElement<ConsultarModalidadesEmpresaResponse>(_ConsultarModalidadesEmpresaResponse_QNAME, ConsultarModalidadesEmpresaResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link SenhaAlteradaAposGeracaoLinkResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "senhaAlteradaAposGeracaoLinkResponse")
    public JAXBElement<SenhaAlteradaAposGeracaoLinkResponse> createSenhaAlteradaAposGeracaoLinkResponse(SenhaAlteradaAposGeracaoLinkResponse value) {
        return new JAXBElement<SenhaAlteradaAposGeracaoLinkResponse>(_SenhaAlteradaAposGeracaoLinkResponse_QNAME, SenhaAlteradaAposGeracaoLinkResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarParcelaVendaResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarParcelaVendaResponse")
    public JAXBElement<ConsultarParcelaVendaResponse> createConsultarParcelaVendaResponse(ConsultarParcelaVendaResponse value) {
        return new JAXBElement<ConsultarParcelaVendaResponse>(_ConsultarParcelaVendaResponse_QNAME, ConsultarParcelaVendaResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link LiberarCobrancaBaixaProtheusResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "liberarCobrancaBaixaProtheusResponse")
    public JAXBElement<LiberarCobrancaBaixaProtheusResponse> createLiberarCobrancaBaixaProtheusResponse(LiberarCobrancaBaixaProtheusResponse value) {
        return new JAXBElement<LiberarCobrancaBaixaProtheusResponse>(_LiberarCobrancaBaixaProtheusResponse_QNAME, LiberarCobrancaBaixaProtheusResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarClienteJson }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarClienteJson")
    public JAXBElement<ConsultarClienteJson> createConsultarClienteJson(ConsultarClienteJson value) {
        return new JAXBElement<ConsultarClienteJson>(_ConsultarClienteJson_QNAME, ConsultarClienteJson.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link EnviarMensagemAoUsuarioUsername }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "enviarMensagemAoUsuarioUsername")
    public JAXBElement<EnviarMensagemAoUsuarioUsername> createEnviarMensagemAoUsuarioUsername(EnviarMensagemAoUsuarioUsername value) {
        return new JAXBElement<EnviarMensagemAoUsuarioUsername>(_EnviarMensagemAoUsuarioUsername_QNAME, EnviarMensagemAoUsuarioUsername.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link AlterarEndereco }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "alterarEndereco")
    public JAXBElement<AlterarEndereco> createAlterarEndereco(AlterarEndereco value) {
        return new JAXBElement<AlterarEndereco>(_AlterarEndereco_QNAME, AlterarEndereco.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarEstadoResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarEstadoResponse")
    public JAXBElement<ConsultarEstadoResponse> createConsultarEstadoResponse(ConsultarEstadoResponse value) {
        return new JAXBElement<ConsultarEstadoResponse>(_ConsultarEstadoResponse_QNAME, ConsultarEstadoResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarEstatisticaSolicitacaoResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarEstatisticaSolicitacaoResponse")
    public JAXBElement<ConsultarEstatisticaSolicitacaoResponse> createConsultarEstatisticaSolicitacaoResponse(ConsultarEstatisticaSolicitacaoResponse value) {
        return new JAXBElement<ConsultarEstatisticaSolicitacaoResponse>(_ConsultarEstatisticaSolicitacaoResponse_QNAME, ConsultarEstatisticaSolicitacaoResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link MarcarStatusBG }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "marcarStatusBG")
    public JAXBElement<MarcarStatusBG> createMarcarStatusBG(MarcarStatusBG value) {
        return new JAXBElement<MarcarStatusBG>(_MarcarStatusBG_QNAME, MarcarStatusBG.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ListaProdutosAtestado }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "listaProdutosAtestado")
    public JAXBElement<ListaProdutosAtestado> createListaProdutosAtestado(ListaProdutosAtestado value) {
        return new JAXBElement<ListaProdutosAtestado>(_ListaProdutosAtestado_QNAME, ListaProdutosAtestado.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarAlunos }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarAlunos")
    public JAXBElement<ConsultarAlunos> createConsultarAlunos(ConsultarAlunos value) {
        return new JAXBElement<ConsultarAlunos>(_ConsultarAlunos_QNAME, ConsultarAlunos.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarClientesPeloNomeResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarClientesPeloNomeResponse")
    public JAXBElement<ConsultarClientesPeloNomeResponse> createConsultarClientesPeloNomeResponse(ConsultarClientesPeloNomeResponse value) {
        return new JAXBElement<ConsultarClientesPeloNomeResponse>(_ConsultarClientesPeloNomeResponse_QNAME, ConsultarClientesPeloNomeResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link BuscarEstatisticasMovideskResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "buscarEstatisticasMovideskResponse")
    public JAXBElement<BuscarEstatisticasMovideskResponse> createBuscarEstatisticasMovideskResponse(BuscarEstatisticasMovideskResponse value) {
        return new JAXBElement<BuscarEstatisticasMovideskResponse>(_BuscarEstatisticasMovideskResponse_QNAME, BuscarEstatisticasMovideskResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link PersistirClienteJSON }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "persistirClienteJSON")
    public JAXBElement<PersistirClienteJSON> createPersistirClienteJSON(PersistirClienteJSON value) {
        return new JAXBElement<PersistirClienteJSON>(_PersistirClienteJSON_QNAME, PersistirClienteJSON.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarMeioRecuperarLoginResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarMeioRecuperarLoginResponse")
    public JAXBElement<ConsultarMeioRecuperarLoginResponse> createConsultarMeioRecuperarLoginResponse(ConsultarMeioRecuperarLoginResponse value) {
        return new JAXBElement<ConsultarMeioRecuperarLoginResponse>(_ConsultarMeioRecuperarLoginResponse_QNAME, ConsultarMeioRecuperarLoginResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link AtualizarDadosGerenciaisPorMesResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "atualizarDadosGerenciaisPorMesResponse")
    public JAXBElement<AtualizarDadosGerenciaisPorMesResponse> createAtualizarDadosGerenciaisPorMesResponse(AtualizarDadosGerenciaisPorMesResponse value) {
        return new JAXBElement<AtualizarDadosGerenciaisPorMesResponse>(_AtualizarDadosGerenciaisPorMesResponse_QNAME, AtualizarDadosGerenciaisPorMesResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link AlterarSenhaUsuarioEmail }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "alterarSenhaUsuarioEmail")
    public JAXBElement<AlterarSenhaUsuarioEmail> createAlterarSenhaUsuarioEmail(AlterarSenhaUsuarioEmail value) {
        return new JAXBElement<AlterarSenhaUsuarioEmail>(_AlterarSenhaUsuarioEmail_QNAME, AlterarSenhaUsuarioEmail.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarClientesTreinoResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarClientesTreinoResponse")
    public JAXBElement<ConsultarClientesTreinoResponse> createConsultarClientesTreinoResponse(ConsultarClientesTreinoResponse value) {
        return new JAXBElement<ConsultarClientesTreinoResponse>(_ConsultarClientesTreinoResponse_QNAME, ConsultarClientesTreinoResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link BuscarLocaisAcesso }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "buscarLocaisAcesso")
    public JAXBElement<BuscarLocaisAcesso> createBuscarLocaisAcesso(BuscarLocaisAcesso value) {
        return new JAXBElement<BuscarLocaisAcesso>(_BuscarLocaisAcesso_QNAME, BuscarLocaisAcesso.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarAtestadoClienteResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarAtestadoClienteResponse")
    public JAXBElement<ConsultarAtestadoClienteResponse> createConsultarAtestadoClienteResponse(ConsultarAtestadoClienteResponse value) {
        return new JAXBElement<ConsultarAtestadoClienteResponse>(_ConsultarAtestadoClienteResponse_QNAME, ConsultarAtestadoClienteResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarClientePorNomeCpfResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarClientePorNomeCpfResponse")
    public JAXBElement<ConsultarClientePorNomeCpfResponse> createConsultarClientePorNomeCpfResponse(ConsultarClientePorNomeCpfResponse value) {
        return new JAXBElement<ConsultarClientePorNomeCpfResponse>(_ConsultarClientePorNomeCpfResponse_QNAME, ConsultarClientePorNomeCpfResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link EnderecosGeolocalizarResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "enderecosGeolocalizarResponse")
    public JAXBElement<EnderecosGeolocalizarResponse> createEnderecosGeolocalizarResponse(EnderecosGeolocalizarResponse value) {
        return new JAXBElement<EnderecosGeolocalizarResponse>(_EnderecosGeolocalizarResponse_QNAME, EnderecosGeolocalizarResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarConsultoresResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarConsultoresResponse")
    public JAXBElement<ConsultarConsultoresResponse> createConsultarConsultoresResponse(ConsultarConsultoresResponse value) {
        return new JAXBElement<ConsultarConsultoresResponse>(_ConsultarConsultoresResponse_QNAME, ConsultarConsultoresResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link PersistirVendaAvulsaJSONResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "persistirVendaAvulsaJSONResponse")
    public JAXBElement<PersistirVendaAvulsaJSONResponse> createPersistirVendaAvulsaJSONResponse(PersistirVendaAvulsaJSONResponse value) {
        return new JAXBElement<PersistirVendaAvulsaJSONResponse>(_PersistirVendaAvulsaJSONResponse_QNAME, PersistirVendaAvulsaJSONResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarJustificativasOperacaoResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarJustificativasOperacaoResponse")
    public JAXBElement<ConsultarJustificativasOperacaoResponse> createConsultarJustificativasOperacaoResponse(ConsultarJustificativasOperacaoResponse value) {
        return new JAXBElement<ConsultarJustificativasOperacaoResponse>(_ConsultarJustificativasOperacaoResponse_QNAME, ConsultarJustificativasOperacaoResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarParcelasEmAbertoResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarParcelasEmAbertoResponse")
    public JAXBElement<ConsultarParcelasEmAbertoResponse> createConsultarParcelasEmAbertoResponse(ConsultarParcelasEmAbertoResponse value) {
        return new JAXBElement<ConsultarParcelasEmAbertoResponse>(_ConsultarParcelasEmAbertoResponse_QNAME, ConsultarParcelasEmAbertoResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarParcelasEmAberto }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarParcelasEmAberto")
    public JAXBElement<ConsultarParcelasEmAberto> createConsultarParcelasEmAberto(ConsultarParcelasEmAberto value) {
        return new JAXBElement<ConsultarParcelasEmAberto>(_ConsultarParcelasEmAberto_QNAME, ConsultarParcelasEmAberto.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarGrupoDeRiscoPelaMatriculaResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarGrupoDeRiscoPelaMatriculaResponse")
    public JAXBElement<ConsultarGrupoDeRiscoPelaMatriculaResponse> createConsultarGrupoDeRiscoPelaMatriculaResponse(ConsultarGrupoDeRiscoPelaMatriculaResponse value) {
        return new JAXBElement<ConsultarGrupoDeRiscoPelaMatriculaResponse>(_ConsultarGrupoDeRiscoPelaMatriculaResponse_QNAME, ConsultarGrupoDeRiscoPelaMatriculaResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarResumoPeriodo }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarResumoPeriodo")
    public JAXBElement<ConsultarResumoPeriodo> createConsultarResumoPeriodo(ConsultarResumoPeriodo value) {
        return new JAXBElement<ConsultarResumoPeriodo>(_ConsultarResumoPeriodo_QNAME, ConsultarResumoPeriodo.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarConsultores }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarConsultores")
    public JAXBElement<ConsultarConsultores> createConsultarConsultores(ConsultarConsultores value) {
        return new JAXBElement<ConsultarConsultores>(_ConsultarConsultores_QNAME, ConsultarConsultores.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ListaAlunoPontosApp }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "listaAlunoPontosApp")
    public JAXBElement<ListaAlunoPontosApp> createListaAlunoPontosApp(ListaAlunoPontosApp value) {
        return new JAXBElement<ListaAlunoPontosApp>(_ListaAlunoPontosApp_QNAME, ListaAlunoPontosApp.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link AlterarEmailResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "alterarEmailResponse")
    public JAXBElement<AlterarEmailResponse> createAlterarEmailResponse(AlterarEmailResponse value) {
        return new JAXBElement<AlterarEmailResponse>(_AlterarEmailResponse_QNAME, AlterarEmailResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarInfoFinanceiras }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarInfoFinanceiras")
    public JAXBElement<ConsultarInfoFinanceiras> createConsultarInfoFinanceiras(ConsultarInfoFinanceiras value) {
        return new JAXBElement<ConsultarInfoFinanceiras>(_ConsultarInfoFinanceiras_QNAME, ConsultarInfoFinanceiras.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ObterConfiguracoesGameResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "obterConfiguracoesGameResponse")
    public JAXBElement<ObterConfiguracoesGameResponse> createObterConfiguracoesGameResponse(ObterConfiguracoesGameResponse value) {
        return new JAXBElement<ObterConfiguracoesGameResponse>(_ObterConfiguracoesGameResponse_QNAME, ObterConfiguracoesGameResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarInfoFinanceirasResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarInfoFinanceirasResponse")
    public JAXBElement<ConsultarInfoFinanceirasResponse> createConsultarInfoFinanceirasResponse(ConsultarInfoFinanceirasResponse value) {
        return new JAXBElement<ConsultarInfoFinanceirasResponse>(_ConsultarInfoFinanceirasResponse_QNAME, ConsultarInfoFinanceirasResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link AnaliseVendaDuracaoResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "analiseVendaDuracaoResponse")
    public JAXBElement<AnaliseVendaDuracaoResponse> createAnaliseVendaDuracaoResponse(AnaliseVendaDuracaoResponse value) {
        return new JAXBElement<AnaliseVendaDuracaoResponse>(_AnaliseVendaDuracaoResponse_QNAME, AnaliseVendaDuracaoResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ObterConfiguracoesGame }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "obterConfiguracoesGame")
    public JAXBElement<ObterConfiguracoesGame> createObterConfiguracoesGame(ObterConfiguracoesGame value) {
        return new JAXBElement<ObterConfiguracoesGame>(_ObterConfiguracoesGame_QNAME, ObterConfiguracoesGame.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link AtualizarDadosGerenciaisDia }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "atualizarDadosGerenciaisDia")
    public JAXBElement<AtualizarDadosGerenciaisDia> createAtualizarDadosGerenciaisDia(AtualizarDadosGerenciaisDia value) {
        return new JAXBElement<AtualizarDadosGerenciaisDia>(_AtualizarDadosGerenciaisDia_QNAME, AtualizarDadosGerenciaisDia.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ValidarDadosOperacaoContrato }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "validarDadosOperacaoContrato")
    public JAXBElement<ValidarDadosOperacaoContrato> createValidarDadosOperacaoContrato(ValidarDadosOperacaoContrato value) {
        return new JAXBElement<ValidarDadosOperacaoContrato>(_ValidarDadosOperacaoContrato_QNAME, ValidarDadosOperacaoContrato.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link AtualizarFotoPerfilAluno }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "atualizarFotoPerfilAluno")
    public JAXBElement<AtualizarFotoPerfilAluno> createAtualizarFotoPerfilAluno(AtualizarFotoPerfilAluno value) {
        return new JAXBElement<AtualizarFotoPerfilAluno>(_AtualizarFotoPerfilAluno_QNAME, AtualizarFotoPerfilAluno.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link AlterarSenhaUsuario }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "alterarSenhaUsuario")
    public JAXBElement<AlterarSenhaUsuario> createAlterarSenhaUsuario(AlterarSenhaUsuario value) {
        return new JAXBElement<AlterarSenhaUsuario>(_AlterarSenhaUsuario_QNAME, AlterarSenhaUsuario.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link EnviarEmailTokenAppResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "enviarEmailTokenAppResponse")
    public JAXBElement<EnviarEmailTokenAppResponse> createEnviarEmailTokenAppResponse(EnviarEmailTokenAppResponse value) {
        return new JAXBElement<EnviarEmailTokenAppResponse>(_EnviarEmailTokenAppResponse_QNAME, EnviarEmailTokenAppResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link LiberarCobrancaBaixaProtheus }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "liberarCobrancaBaixaProtheus")
    public JAXBElement<LiberarCobrancaBaixaProtheus> createLiberarCobrancaBaixaProtheus(LiberarCobrancaBaixaProtheus value) {
        return new JAXBElement<LiberarCobrancaBaixaProtheus>(_LiberarCobrancaBaixaProtheus_QNAME, LiberarCobrancaBaixaProtheus.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link UpdateGeolocEmpresa }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "updateGeolocEmpresa")
    public JAXBElement<UpdateGeolocEmpresa> createUpdateGeolocEmpresa(UpdateGeolocEmpresa value) {
        return new JAXBElement<UpdateGeolocEmpresa>(_UpdateGeolocEmpresa_QNAME, UpdateGeolocEmpresa.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link AlterarEmail }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "alterarEmail")
    public JAXBElement<AlterarEmail> createAlterarEmail(AlterarEmail value) {
        return new JAXBElement<AlterarEmail>(_AlterarEmail_QNAME, AlterarEmail.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ObterCliente }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "obterCliente")
    public JAXBElement<ObterCliente> createObterCliente(ObterCliente value) {
        return new JAXBElement<ObterCliente>(_ObterCliente_QNAME, ObterCliente.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link InserirWebHookClienteRD }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "inserirWebHookClienteRD")
    public JAXBElement<InserirWebHookClienteRD> createInserirWebHookClienteRD(InserirWebHookClienteRD value) {
        return new JAXBElement<InserirWebHookClienteRD>(_InserirWebHookClienteRD_QNAME, InserirWebHookClienteRD.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarProdutosClienteResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarProdutosClienteResponse")
    public JAXBElement<ConsultarProdutosClienteResponse> createConsultarProdutosClienteResponse(ConsultarProdutosClienteResponse value) {
        return new JAXBElement<ConsultarProdutosClienteResponse>(_ConsultarProdutosClienteResponse_QNAME, ConsultarProdutosClienteResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ObterQuestionarioResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "obterQuestionarioResponse")
    public JAXBElement<ObterQuestionarioResponse> createObterQuestionarioResponse(ObterQuestionarioResponse value) {
        return new JAXBElement<ObterQuestionarioResponse>(_ObterQuestionarioResponse_QNAME, ObterQuestionarioResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link AlterarSenhaUsuarioEmailResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "alterarSenhaUsuarioEmailResponse")
    public JAXBElement<AlterarSenhaUsuarioEmailResponse> createAlterarSenhaUsuarioEmailResponse(AlterarSenhaUsuarioEmailResponse value) {
        return new JAXBElement<AlterarSenhaUsuarioEmailResponse>(_AlterarSenhaUsuarioEmailResponse_QNAME, AlterarSenhaUsuarioEmailResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultaGenericaPaginada }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultaGenericaPaginada")
    public JAXBElement<ConsultaGenericaPaginada> createConsultaGenericaPaginada(ConsultaGenericaPaginada value) {
        return new JAXBElement<ConsultaGenericaPaginada>(_ConsultaGenericaPaginada_QNAME, ConsultaGenericaPaginada.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarAtestadoCliente }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarAtestadoCliente")
    public JAXBElement<ConsultarAtestadoCliente> createConsultarAtestadoCliente(ConsultarAtestadoCliente value) {
        return new JAXBElement<ConsultarAtestadoCliente>(_ConsultarAtestadoCliente_QNAME, ConsultarAtestadoCliente.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ListaAlunoPontosData }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "listaAlunoPontosData")
    public JAXBElement<ListaAlunoPontosData> createListaAlunoPontosData(ListaAlunoPontosData value) {
        return new JAXBElement<ListaAlunoPontosData>(_ListaAlunoPontosData_QNAME, ListaAlunoPontosData.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link PersistirTokenLeadRD }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "persistirTokenLeadRD")
    public JAXBElement<PersistirTokenLeadRD> createPersistirTokenLeadRD(PersistirTokenLeadRD value) {
        return new JAXBElement<PersistirTokenLeadRD>(_PersistirTokenLeadRD_QNAME, PersistirTokenLeadRD.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link NotificarRecursoEmpresaGeolocalizacao }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "notificarRecursoEmpresaGeolocalizacao")
    public JAXBElement<NotificarRecursoEmpresaGeolocalizacao> createNotificarRecursoEmpresaGeolocalizacao(NotificarRecursoEmpresaGeolocalizacao value) {
        return new JAXBElement<NotificarRecursoEmpresaGeolocalizacao>(_NotificarRecursoEmpresaGeolocalizacao_QNAME, NotificarRecursoEmpresaGeolocalizacao.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link GerarUsuarioMovelColaborador }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "gerarUsuarioMovelColaborador")
    public JAXBElement<GerarUsuarioMovelColaborador> createGerarUsuarioMovelColaborador(GerarUsuarioMovelColaborador value) {
        return new JAXBElement<GerarUsuarioMovelColaborador>(_GerarUsuarioMovelColaborador_QNAME, GerarUsuarioMovelColaborador.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link AnaliseVendaDuracao }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "analiseVendaDuracao")
    public JAXBElement<AnaliseVendaDuracao> createAnaliseVendaDuracao(AnaliseVendaDuracao value) {
        return new JAXBElement<AnaliseVendaDuracao>(_AnaliseVendaDuracao_QNAME, AnaliseVendaDuracao.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarDadosBITreino }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarDadosBITreino")
    public JAXBElement<ConsultarDadosBITreino> createConsultarDadosBITreino(ConsultarDadosBITreino value) {
        return new JAXBElement<ConsultarDadosBITreino>(_ConsultarDadosBITreino_QNAME, ConsultarDadosBITreino.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ObterClienteResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "obterClienteResponse")
    public JAXBElement<ObterClienteResponse> createObterClienteResponse(ObterClienteResponse value) {
        return new JAXBElement<ObterClienteResponse>(_ObterClienteResponse_QNAME, ObterClienteResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link PersistirProdutoJSON }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "persistirProdutoJSON")
    public JAXBElement<PersistirProdutoJSON> createPersistirProdutoJSON(PersistirProdutoJSON value) {
        return new JAXBElement<PersistirProdutoJSON>(_PersistirProdutoJSON_QNAME, PersistirProdutoJSON.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link CheckInGymPassResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "checkInGymPassResponse")
    public JAXBElement<CheckInGymPassResponse> createCheckInGymPassResponse(CheckInGymPassResponse value) {
        return new JAXBElement<CheckInGymPassResponse>(_CheckInGymPassResponse_QNAME, CheckInGymPassResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link AlterarSenhaUsuarioMovel }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "alterarSenhaUsuarioMovel")
    public JAXBElement<AlterarSenhaUsuarioMovel> createAlterarSenhaUsuarioMovel(AlterarSenhaUsuarioMovel value) {
        return new JAXBElement<AlterarSenhaUsuarioMovel>(_AlterarSenhaUsuarioMovel_QNAME, AlterarSenhaUsuarioMovel.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ObterTiposColaboradoes }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "obterTiposColaboradoes")
    public JAXBElement<ObterTiposColaboradoes> createObterTiposColaboradoes(ObterTiposColaboradoes value) {
        return new JAXBElement<ObterTiposColaboradoes>(_ObterTiposColaboradoes_QNAME, ObterTiposColaboradoes.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarClientes }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarClientes")
    public JAXBElement<ConsultarClientes> createConsultarClientes(ConsultarClientes value) {
        return new JAXBElement<ConsultarClientes>(_ConsultarClientes_QNAME, ConsultarClientes.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarAcessosListaDia }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarAcessosListaDia")
    public JAXBElement<ConsultarAcessosListaDia> createConsultarAcessosListaDia(ConsultarAcessosListaDia value) {
        return new JAXBElement<ConsultarAcessosListaDia>(_ConsultarAcessosListaDia_QNAME, ConsultarAcessosListaDia.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarModalidadesAtivosForaTreino }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarModalidadesAtivosForaTreino")
    public JAXBElement<ConsultarModalidadesAtivosForaTreino> createConsultarModalidadesAtivosForaTreino(ConsultarModalidadesAtivosForaTreino value) {
        return new JAXBElement<ConsultarModalidadesAtivosForaTreino>(_ConsultarModalidadesAtivosForaTreino_QNAME, ConsultarModalidadesAtivosForaTreino.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarMensagensCliente }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarMensagensCliente")
    public JAXBElement<ConsultarMensagensCliente> createConsultarMensagensCliente(ConsultarMensagensCliente value) {
        return new JAXBElement<ConsultarMensagensCliente>(_ConsultarMensagensCliente_QNAME, ConsultarMensagensCliente.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarClientesAtivosForaTreino }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarClientesAtivosForaTreino")
    public JAXBElement<ConsultarClientesAtivosForaTreino> createConsultarClientesAtivosForaTreino(ConsultarClientesAtivosForaTreino value) {
        return new JAXBElement<ConsultarClientesAtivosForaTreino>(_ConsultarClientesAtivosForaTreino_QNAME, ConsultarClientesAtivosForaTreino.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link RecuperarUsuarioMovelResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "recuperarUsuarioMovelResponse")
    public JAXBElement<RecuperarUsuarioMovelResponse> createRecuperarUsuarioMovelResponse(RecuperarUsuarioMovelResponse value) {
        return new JAXBElement<RecuperarUsuarioMovelResponse>(_RecuperarUsuarioMovelResponse_QNAME, RecuperarUsuarioMovelResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarClientePorNomeCpf }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarClientePorNomeCpf")
    public JAXBElement<ConsultarClientePorNomeCpf> createConsultarClientePorNomeCpf(ConsultarClientePorNomeCpf value) {
        return new JAXBElement<ConsultarClientePorNomeCpf>(_ConsultarClientePorNomeCpf_QNAME, ConsultarClientePorNomeCpf.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link PersistirLeadGenericoResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "persistirLeadGenericoResponse")
    public JAXBElement<PersistirLeadGenericoResponse> createPersistirLeadGenericoResponse(PersistirLeadGenericoResponse value) {
        return new JAXBElement<PersistirLeadGenericoResponse>(_PersistirLeadGenericoResponse_QNAME, PersistirLeadGenericoResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Profissoes }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "profissoes")
    public JAXBElement<Profissoes> createProfissoes(Profissoes value) {
        return new JAXBElement<Profissoes>(_Profissoes_QNAME, Profissoes.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link EnviarCodigoVerificacao }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "enviarCodigoVerificacao")
    public JAXBElement<EnviarCodigoVerificacao> createEnviarCodigoVerificacao(EnviarCodigoVerificacao value) {
        return new JAXBElement<EnviarCodigoVerificacao>(_EnviarCodigoVerificacao_QNAME, EnviarCodigoVerificacao.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarMetasCRMResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarMetasCRMResponse")
    public JAXBElement<ConsultarMetasCRMResponse> createConsultarMetasCRMResponse(ConsultarMetasCRMResponse value) {
        return new JAXBElement<ConsultarMetasCRMResponse>(_ConsultarMetasCRMResponse_QNAME, ConsultarMetasCRMResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsistirException }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "ConsistirException")
    public JAXBElement<ConsistirException> createConsistirException(ConsistirException value) {
        return new JAXBElement<ConsistirException>(_ConsistirException_QNAME, ConsistirException.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarAcessosDia }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarAcessosDia")
    public JAXBElement<ConsultarAcessosDia> createConsultarAcessosDia(ConsultarAcessosDia value) {
        return new JAXBElement<ConsultarAcessosDia>(_ConsultarAcessosDia_QNAME, ConsultarAcessosDia.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarFotoKeyPessoaResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarFotoKeyPessoaResponse")
    public JAXBElement<ConsultarFotoKeyPessoaResponse> createConsultarFotoKeyPessoaResponse(ConsultarFotoKeyPessoaResponse value) {
        return new JAXBElement<ConsultarFotoKeyPessoaResponse>(_ConsultarFotoKeyPessoaResponse_QNAME, ConsultarFotoKeyPessoaResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarIntegracaoFeraJson }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarIntegracaoFeraJson")
    public JAXBElement<ConsultarIntegracaoFeraJson> createConsultarIntegracaoFeraJson(ConsultarIntegracaoFeraJson value) {
        return new JAXBElement<ConsultarIntegracaoFeraJson>(_ConsultarIntegracaoFeraJson_QNAME, ConsultarIntegracaoFeraJson.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarCliente }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarCliente")
    public JAXBElement<ConsultarCliente> createConsultarCliente(ConsultarCliente value) {
        return new JAXBElement<ConsultarCliente>(_ConsultarCliente_QNAME, ConsultarCliente.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link AcessTokenValidoResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "acessTokenValidoResponse")
    public JAXBElement<AcessTokenValidoResponse> createAcessTokenValidoResponse(AcessTokenValidoResponse value) {
        return new JAXBElement<AcessTokenValidoResponse>(_AcessTokenValidoResponse_QNAME, AcessTokenValidoResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarParcelaVenda }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarParcelaVenda")
    public JAXBElement<ConsultarParcelaVenda> createConsultarParcelaVenda(ConsultarParcelaVenda value) {
        return new JAXBElement<ConsultarParcelaVenda>(_ConsultarParcelaVenda_QNAME, ConsultarParcelaVenda.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ValidarCodigoVerificacao }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "validarCodigoVerificacao")
    public JAXBElement<ValidarCodigoVerificacao> createValidarCodigoVerificacao(ValidarCodigoVerificacao value) {
        return new JAXBElement<ValidarCodigoVerificacao>(_ValidarCodigoVerificacao_QNAME, ValidarCodigoVerificacao.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarNrAlunosAtivosForaTreinoResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarNrAlunosAtivosForaTreinoResponse")
    public JAXBElement<ConsultarNrAlunosAtivosForaTreinoResponse> createConsultarNrAlunosAtivosForaTreinoResponse(ConsultarNrAlunosAtivosForaTreinoResponse value) {
        return new JAXBElement<ConsultarNrAlunosAtivosForaTreinoResponse>(_ConsultarNrAlunosAtivosForaTreinoResponse_QNAME, ConsultarNrAlunosAtivosForaTreinoResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarAutorizacaoCobrancaResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarAutorizacaoCobrancaResponse")
    public JAXBElement<ConsultarAutorizacaoCobrancaResponse> createConsultarAutorizacaoCobrancaResponse(ConsultarAutorizacaoCobrancaResponse value) {
        return new JAXBElement<ConsultarAutorizacaoCobrancaResponse>(_ConsultarAutorizacaoCobrancaResponse_QNAME, ConsultarAutorizacaoCobrancaResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link AtualizarDadosGerenciais }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "atualizarDadosGerenciais")
    public JAXBElement<AtualizarDadosGerenciais> createAtualizarDadosGerenciais(AtualizarDadosGerenciais value) {
        return new JAXBElement<AtualizarDadosGerenciais>(_AtualizarDadosGerenciais_QNAME, AtualizarDadosGerenciais.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link CheckInGymPass }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "checkInGymPass")
    public JAXBElement<CheckInGymPass> createCheckInGymPass(CheckInGymPass value) {
        return new JAXBElement<CheckInGymPass>(_CheckInGymPass_QNAME, CheckInGymPass.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarFeedGestao }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarFeedGestao")
    public JAXBElement<ConsultarFeedGestao> createConsultarFeedGestao(ConsultarFeedGestao value) {
        return new JAXBElement<ConsultarFeedGestao>(_ConsultarFeedGestao_QNAME, ConsultarFeedGestao.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarLocalAcessoPorNFCResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarLocalAcessoPorNFCResponse")
    public JAXBElement<ConsultarLocalAcessoPorNFCResponse> createConsultarLocalAcessoPorNFCResponse(ConsultarLocalAcessoPorNFCResponse value) {
        return new JAXBElement<ConsultarLocalAcessoPorNFCResponse>(_ConsultarLocalAcessoPorNFCResponse_QNAME, ConsultarLocalAcessoPorNFCResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ObterTiposColaboradoesResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "obterTiposColaboradoesResponse")
    public JAXBElement<ObterTiposColaboradoesResponse> createObterTiposColaboradoesResponse(ObterTiposColaboradoesResponse value) {
        return new JAXBElement<ObterTiposColaboradoesResponse>(_ObterTiposColaboradoesResponse_QNAME, ObterTiposColaboradoesResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link MarcarStatusBGResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "marcarStatusBGResponse")
    public JAXBElement<MarcarStatusBGResponse> createMarcarStatusBGResponse(MarcarStatusBGResponse value) {
        return new JAXBElement<MarcarStatusBGResponse>(_MarcarStatusBGResponse_QNAME, MarcarStatusBGResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link EnviarEmailGenerico }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "enviarEmailGenerico")
    public JAXBElement<EnviarEmailGenerico> createEnviarEmailGenerico(EnviarEmailGenerico value) {
        return new JAXBElement<EnviarEmailGenerico>(_EnviarEmailGenerico_QNAME, EnviarEmailGenerico.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link AlterarSenhaUsuarioResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "alterarSenhaUsuarioResponse")
    public JAXBElement<AlterarSenhaUsuarioResponse> createAlterarSenhaUsuarioResponse(AlterarSenhaUsuarioResponse value) {
        return new JAXBElement<AlterarSenhaUsuarioResponse>(_AlterarSenhaUsuarioResponse_QNAME, AlterarSenhaUsuarioResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link BuscaIDSecret }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "buscaIDSecret")
    public JAXBElement<BuscaIDSecret> createBuscaIDSecret(BuscaIDSecret value) {
        return new JAXBElement<BuscaIDSecret>(_BuscaIDSecret_QNAME, BuscaIDSecret.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link DeletarContratoImportacaoJSON }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "deletarContratoImportacaoJSON")
    public JAXBElement<DeletarContratoImportacaoJSON> createDeletarContratoImportacaoJSON(DeletarContratoImportacaoJSON value) {
        return new JAXBElement<DeletarContratoImportacaoJSON>(_DeletarContratoImportacaoJSON_QNAME, DeletarContratoImportacaoJSON.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ObterEnderecoEmpresa }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "obterEnderecoEmpresa")
    public JAXBElement<ObterEnderecoEmpresa> createObterEnderecoEmpresa(ObterEnderecoEmpresa value) {
        return new JAXBElement<ObterEnderecoEmpresa>(_ObterEnderecoEmpresa_QNAME, ObterEnderecoEmpresa.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarEmpresasResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarEmpresasResponse")
    public JAXBElement<ConsultarEmpresasResponse> createConsultarEmpresasResponse(ConsultarEmpresasResponse value) {
        return new JAXBElement<ConsultarEmpresasResponse>(_ConsultarEmpresasResponse_QNAME, ConsultarEmpresasResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link EnviarEmailAngularResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "enviarEmailAngularResponse")
    public JAXBElement<EnviarEmailAngularResponse> createEnviarEmailAngularResponse(EnviarEmailAngularResponse value) {
        return new JAXBElement<EnviarEmailAngularResponse>(_EnviarEmailAngularResponse_QNAME, EnviarEmailAngularResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ClassificacoesResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "classificacoesResponse")
    public JAXBElement<ClassificacoesResponse> createClassificacoesResponse(ClassificacoesResponse value) {
        return new JAXBElement<ClassificacoesResponse>(_ClassificacoesResponse_QNAME, ClassificacoesResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarClienteSinteticoPorEmailPessoaResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarClienteSinteticoPorEmailPessoaResponse")
    public JAXBElement<ConsultarClienteSinteticoPorEmailPessoaResponse> createConsultarClienteSinteticoPorEmailPessoaResponse(ConsultarClienteSinteticoPorEmailPessoaResponse value) {
        return new JAXBElement<ConsultarClienteSinteticoPorEmailPessoaResponse>(_ConsultarClienteSinteticoPorEmailPessoaResponse_QNAME, ConsultarClienteSinteticoPorEmailPessoaResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ObterEnderecoCodigoResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "obterEnderecoCodigoResponse")
    public JAXBElement<ObterEnderecoCodigoResponse> createObterEnderecoCodigoResponse(ObterEnderecoCodigoResponse value) {
        return new JAXBElement<ObterEnderecoCodigoResponse>(_ObterEnderecoCodigoResponse_QNAME, ObterEnderecoCodigoResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarDadosGame }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarDadosGame")
    public JAXBElement<ConsultarDadosGame> createConsultarDadosGame(ConsultarDadosGame value) {
        return new JAXBElement<ConsultarDadosGame>(_ConsultarDadosGame_QNAME, ConsultarDadosGame.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ValidarAlunoProdutoVigente }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "validarAlunoProdutoVigente")
    public JAXBElement<ValidarAlunoProdutoVigente> createValidarAlunoProdutoVigente(ValidarAlunoProdutoVigente value) {
        return new JAXBElement<ValidarAlunoProdutoVigente>(_ValidarAlunoProdutoVigente_QNAME, ValidarAlunoProdutoVigente.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link EnviarEmailColaborador }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "enviarEmailColaborador")
    public JAXBElement<EnviarEmailColaborador> createEnviarEmailColaborador(EnviarEmailColaborador value) {
        return new JAXBElement<EnviarEmailColaborador>(_EnviarEmailColaborador_QNAME, EnviarEmailColaborador.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link PersistirPagamentosContratoJSON }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "persistirPagamentosContratoJSON")
    public JAXBElement<PersistirPagamentosContratoJSON> createPersistirPagamentosContratoJSON(PersistirPagamentosContratoJSON value) {
        return new JAXBElement<PersistirPagamentosContratoJSON>(_PersistirPagamentosContratoJSON_QNAME, PersistirPagamentosContratoJSON.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarMensagensClienteResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarMensagensClienteResponse")
    public JAXBElement<ConsultarMensagensClienteResponse> createConsultarMensagensClienteResponse(ConsultarMensagensClienteResponse value) {
        return new JAXBElement<ConsultarMensagensClienteResponse>(_ConsultarMensagensClienteResponse_QNAME, ConsultarMensagensClienteResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link EnviarMensagemAoUsuarioResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "enviarMensagemAoUsuarioResponse")
    public JAXBElement<EnviarMensagemAoUsuarioResponse> createEnviarMensagemAoUsuarioResponse(EnviarMensagemAoUsuarioResponse value) {
        return new JAXBElement<EnviarMensagemAoUsuarioResponse>(_EnviarMensagemAoUsuarioResponse_QNAME, EnviarMensagemAoUsuarioResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link BuscaIDSecretResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "buscaIDSecretResponse")
    public JAXBElement<BuscaIDSecretResponse> createBuscaIDSecretResponse(BuscaIDSecretResponse value) {
        return new JAXBElement<BuscaIDSecretResponse>(_BuscaIDSecretResponse_QNAME, BuscaIDSecretResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link PersistirPagamentosContratoJSONResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "persistirPagamentosContratoJSONResponse")
    public JAXBElement<PersistirPagamentosContratoJSONResponse> createPersistirPagamentosContratoJSONResponse(PersistirPagamentosContratoJSONResponse value) {
        return new JAXBElement<PersistirPagamentosContratoJSONResponse>(_PersistirPagamentosContratoJSONResponse_QNAME, PersistirPagamentosContratoJSONResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link LancarProdutoClienteVAlorResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "lancarProdutoClienteVAlorResponse")
    public JAXBElement<LancarProdutoClienteVAlorResponse> createLancarProdutoClienteVAlorResponse(LancarProdutoClienteVAlorResponse value) {
        return new JAXBElement<LancarProdutoClienteVAlorResponse>(_LancarProdutoClienteVAlorResponse_QNAME, LancarProdutoClienteVAlorResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarAmbientes }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarAmbientes")
    public JAXBElement<ConsultarAmbientes> createConsultarAmbientes(ConsultarAmbientes value) {
        return new JAXBElement<ConsultarAmbientes>(_ConsultarAmbientes_QNAME, ConsultarAmbientes.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultaGenericaResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultaGenericaResponse")
    public JAXBElement<ConsultaGenericaResponse> createConsultaGenericaResponse(ConsultaGenericaResponse value) {
        return new JAXBElement<ConsultaGenericaResponse>(_ConsultaGenericaResponse_QNAME, ConsultaGenericaResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link PersistirPagamentosVendaAvulsaJSONResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "persistirPagamentosVendaAvulsaJSONResponse")
    public JAXBElement<PersistirPagamentosVendaAvulsaJSONResponse> createPersistirPagamentosVendaAvulsaJSONResponse(PersistirPagamentosVendaAvulsaJSONResponse value) {
        return new JAXBElement<PersistirPagamentosVendaAvulsaJSONResponse>(_PersistirPagamentosVendaAvulsaJSONResponse_QNAME, PersistirPagamentosVendaAvulsaJSONResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link GravarDadosOperacaoContratoResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "gravarDadosOperacaoContratoResponse")
    public JAXBElement<GravarDadosOperacaoContratoResponse> createGravarDadosOperacaoContratoResponse(GravarDadosOperacaoContratoResponse value) {
        return new JAXBElement<GravarDadosOperacaoContratoResponse>(_GravarDadosOperacaoContratoResponse_QNAME, GravarDadosOperacaoContratoResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ObterEnderecosResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "obterEnderecosResponse")
    public JAXBElement<ObterEnderecosResponse> createObterEnderecosResponse(ObterEnderecosResponse value) {
        return new JAXBElement<ObterEnderecosResponse>(_ObterEnderecosResponse_QNAME, ObterEnderecosResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarAmbientesResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarAmbientesResponse")
    public JAXBElement<ConsultarAmbientesResponse> createConsultarAmbientesResponse(ConsultarAmbientesResponse value) {
        return new JAXBElement<ConsultarAmbientesResponse>(_ConsultarAmbientesResponse_QNAME, ConsultarAmbientesResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarContratosRenovar }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarContratosRenovar")
    public JAXBElement<ConsultarContratosRenovar> createConsultarContratosRenovar(ConsultarContratosRenovar value) {
        return new JAXBElement<ConsultarContratosRenovar>(_ConsultarContratosRenovar_QNAME, ConsultarContratosRenovar.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarConfiguracaoENotas }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarConfiguracaoENotas")
    public JAXBElement<ConsultarConfiguracaoENotas> createConsultarConfiguracaoENotas(ConsultarConfiguracaoENotas value) {
        return new JAXBElement<ConsultarConfiguracaoENotas>(_ConsultarConfiguracaoENotas_QNAME, ConsultarConfiguracaoENotas.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarAutorizacaoCobranca }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarAutorizacaoCobranca")
    public JAXBElement<ConsultarAutorizacaoCobranca> createConsultarAutorizacaoCobranca(ConsultarAutorizacaoCobranca value) {
        return new JAXBElement<ConsultarAutorizacaoCobranca>(_ConsultarAutorizacaoCobranca_QNAME, ConsultarAutorizacaoCobranca.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarColaboradoresResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarColaboradoresResponse")
    public JAXBElement<ConsultarColaboradoresResponse> createConsultarColaboradoresResponse(ConsultarColaboradoresResponse value) {
        return new JAXBElement<ConsultarColaboradoresResponse>(_ConsultarColaboradoresResponse_QNAME, ConsultarColaboradoresResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarFaturamentoDuracaoResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarFaturamentoDuracaoResponse")
    public JAXBElement<ConsultarFaturamentoDuracaoResponse> createConsultarFaturamentoDuracaoResponse(ConsultarFaturamentoDuracaoResponse value) {
        return new JAXBElement<ConsultarFaturamentoDuracaoResponse>(_ConsultarFaturamentoDuracaoResponse_QNAME, ConsultarFaturamentoDuracaoResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarModalidadesAtivosForaTreinoResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarModalidadesAtivosForaTreinoResponse")
    public JAXBElement<ConsultarModalidadesAtivosForaTreinoResponse> createConsultarModalidadesAtivosForaTreinoResponse(ConsultarModalidadesAtivosForaTreinoResponse value) {
        return new JAXBElement<ConsultarModalidadesAtivosForaTreinoResponse>(_ConsultarModalidadesAtivosForaTreinoResponse_QNAME, ConsultarModalidadesAtivosForaTreinoResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link UrlFotoEmpresa }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "urlFotoEmpresa")
    public JAXBElement<UrlFotoEmpresa> createUrlFotoEmpresa(UrlFotoEmpresa value) {
        return new JAXBElement<UrlFotoEmpresa>(_UrlFotoEmpresa_QNAME, UrlFotoEmpresa.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link AtualizarFotoPerfilAlunoResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "atualizarFotoPerfilAlunoResponse")
    public JAXBElement<AtualizarFotoPerfilAlunoResponse> createAtualizarFotoPerfilAlunoResponse(AtualizarFotoPerfilAlunoResponse value) {
        return new JAXBElement<AtualizarFotoPerfilAlunoResponse>(_AtualizarFotoPerfilAlunoResponse_QNAME, AtualizarFotoPerfilAlunoResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Exception }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "Exception")
    public JAXBElement<Exception> createException(Exception value) {
        return new JAXBElement<Exception>(_Exception_QNAME, Exception.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link AtualizarDadosGerenciaisResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "atualizarDadosGerenciaisResponse")
    public JAXBElement<AtualizarDadosGerenciaisResponse> createAtualizarDadosGerenciaisResponse(AtualizarDadosGerenciaisResponse value) {
        return new JAXBElement<AtualizarDadosGerenciaisResponse>(_AtualizarDadosGerenciaisResponse_QNAME, AtualizarDadosGerenciaisResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarColetoresResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarColetoresResponse")
    public JAXBElement<ConsultarColetoresResponse> createConsultarColetoresResponse(ConsultarColetoresResponse value) {
        return new JAXBElement<ConsultarColetoresResponse>(_ConsultarColetoresResponse_QNAME, ConsultarColetoresResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link AtualizarBoletoPJBank }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "atualizarBoletoPJBank")
    public JAXBElement<AtualizarBoletoPJBank> createAtualizarBoletoPJBank(AtualizarBoletoPJBank value) {
        return new JAXBElement<AtualizarBoletoPJBank>(_AtualizarBoletoPJBank_QNAME, AtualizarBoletoPJBank.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link AlterarTelefone }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "alterarTelefone")
    public JAXBElement<AlterarTelefone> createAlterarTelefone(AlterarTelefone value) {
        return new JAXBElement<AlterarTelefone>(_AlterarTelefone_QNAME, AlterarTelefone.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link MarcarComoLidaFeedGestaoResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "marcarComoLidaFeedGestaoResponse")
    public JAXBElement<MarcarComoLidaFeedGestaoResponse> createMarcarComoLidaFeedGestaoResponse(MarcarComoLidaFeedGestaoResponse value) {
        return new JAXBElement<MarcarComoLidaFeedGestaoResponse>(_MarcarComoLidaFeedGestaoResponse_QNAME, MarcarComoLidaFeedGestaoResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarCupomDesconto }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarCupomDesconto")
    public JAXBElement<ConsultarCupomDesconto> createConsultarCupomDesconto(ConsultarCupomDesconto value) {
        return new JAXBElement<ConsultarCupomDesconto>(_ConsultarCupomDesconto_QNAME, ConsultarCupomDesconto.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ValidarDadosOperacaoContratoResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "validarDadosOperacaoContratoResponse")
    public JAXBElement<ValidarDadosOperacaoContratoResponse> createValidarDadosOperacaoContratoResponse(ValidarDadosOperacaoContratoResponse value) {
        return new JAXBElement<ValidarDadosOperacaoContratoResponse>(_ValidarDadosOperacaoContratoResponse_QNAME, ValidarDadosOperacaoContratoResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link LancarProdutoCliente }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "lancarProdutoCliente")
    public JAXBElement<LancarProdutoCliente> createLancarProdutoCliente(LancarProdutoCliente value) {
        return new JAXBElement<LancarProdutoCliente>(_LancarProdutoCliente_QNAME, LancarProdutoCliente.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarClientesPeloNomeNoLimitsResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarClientesPeloNomeNoLimitsResponse")
    public JAXBElement<ConsultarClientesPeloNomeNoLimitsResponse> createConsultarClientesPeloNomeNoLimitsResponse(ConsultarClientesPeloNomeNoLimitsResponse value) {
        return new JAXBElement<ConsultarClientesPeloNomeNoLimitsResponse>(_ConsultarClientesPeloNomeNoLimitsResponse_QNAME, ConsultarClientesPeloNomeNoLimitsResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link LancarProdutoClienteVAlor }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "lancarProdutoClienteVAlor")
    public JAXBElement<LancarProdutoClienteVAlor> createLancarProdutoClienteVAlor(LancarProdutoClienteVAlor value) {
        return new JAXBElement<LancarProdutoClienteVAlor>(_LancarProdutoClienteVAlor_QNAME, LancarProdutoClienteVAlor.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ValidarAlunoProdutoVigenteResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "validarAlunoProdutoVigenteResponse")
    public JAXBElement<ValidarAlunoProdutoVigenteResponse> createValidarAlunoProdutoVigenteResponse(ValidarAlunoProdutoVigenteResponse value) {
        return new JAXBElement<ValidarAlunoProdutoVigenteResponse>(_ValidarAlunoProdutoVigenteResponse_QNAME, ValidarAlunoProdutoVigenteResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link LogarUsuarioMovelResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "logarUsuarioMovelResponse")
    public JAXBElement<LogarUsuarioMovelResponse> createLogarUsuarioMovelResponse(LogarUsuarioMovelResponse value) {
        return new JAXBElement<LogarUsuarioMovelResponse>(_LogarUsuarioMovelResponse_QNAME, LogarUsuarioMovelResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link PersistirOperacoesContratoJSONResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "persistirOperacoesContratoJSONResponse")
    public JAXBElement<PersistirOperacoesContratoJSONResponse> createPersistirOperacoesContratoJSONResponse(PersistirOperacoesContratoJSONResponse value) {
        return new JAXBElement<PersistirOperacoesContratoJSONResponse>(_PersistirOperacoesContratoJSONResponse_QNAME, PersistirOperacoesContratoJSONResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ObterQuestionario }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "obterQuestionario")
    public JAXBElement<ObterQuestionario> createObterQuestionario(ObterQuestionario value) {
        return new JAXBElement<ObterQuestionario>(_ObterQuestionario_QNAME, ObterQuestionario.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ResponderQuestionario }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "responderQuestionario")
    public JAXBElement<ResponderQuestionario> createResponderQuestionario(ResponderQuestionario value) {
        return new JAXBElement<ResponderQuestionario>(_ResponderQuestionario_QNAME, ResponderQuestionario.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarProfessoresPeloNome }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarProfessoresPeloNome")
    public JAXBElement<ConsultarProfessoresPeloNome> createConsultarProfessoresPeloNome(ConsultarProfessoresPeloNome value) {
        return new JAXBElement<ConsultarProfessoresPeloNome>(_ConsultarProfessoresPeloNome_QNAME, ConsultarProfessoresPeloNome.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link PersistirCodeLeadRDResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "persistirCodeLeadRDResponse")
    public JAXBElement<PersistirCodeLeadRDResponse> createPersistirCodeLeadRDResponse(PersistirCodeLeadRDResponse value) {
        return new JAXBElement<PersistirCodeLeadRDResponse>(_PersistirCodeLeadRDResponse_QNAME, PersistirCodeLeadRDResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link PersistirCancelarContratoJSONResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "persistirCancelarContratoJSONResponse")
    public JAXBElement<PersistirCancelarContratoJSONResponse> createPersistirCancelarContratoJSONResponse(PersistirCancelarContratoJSONResponse value) {
        return new JAXBElement<PersistirCancelarContratoJSONResponse>(_PersistirCancelarContratoJSONResponse_QNAME, PersistirCancelarContratoJSONResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarEmpresasSimplesResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarEmpresasSimplesResponse")
    public JAXBElement<ConsultarEmpresasSimplesResponse> createConsultarEmpresasSimplesResponse(ConsultarEmpresasSimplesResponse value) {
        return new JAXBElement<ConsultarEmpresasSimplesResponse>(_ConsultarEmpresasSimplesResponse_QNAME, ConsultarEmpresasSimplesResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ObterContratoOperacaoResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "obterContratoOperacaoResponse")
    public JAXBElement<ObterContratoOperacaoResponse> createObterContratoOperacaoResponse(ObterContratoOperacaoResponse value) {
        return new JAXBElement<ObterContratoOperacaoResponse>(_ObterContratoOperacaoResponse_QNAME, ObterContratoOperacaoResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link PersistirVendaAvulsaJSON }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "persistirVendaAvulsaJSON")
    public JAXBElement<PersistirVendaAvulsaJSON> createPersistirVendaAvulsaJSON(PersistirVendaAvulsaJSON value) {
        return new JAXBElement<PersistirVendaAvulsaJSON>(_PersistirVendaAvulsaJSON_QNAME, PersistirVendaAvulsaJSON.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link PersistirAcessosClienteJSON }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "persistirAcessosClienteJSON")
    public JAXBElement<PersistirAcessosClienteJSON> createPersistirAcessosClienteJSON(PersistirAcessosClienteJSON value) {
        return new JAXBElement<PersistirAcessosClienteJSON>(_PersistirAcessosClienteJSON_QNAME, PersistirAcessosClienteJSON.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link SituacaoUsuarioResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "situacaoUsuarioResponse")
    public JAXBElement<SituacaoUsuarioResponse> createSituacaoUsuarioResponse(SituacaoUsuarioResponse value) {
        return new JAXBElement<SituacaoUsuarioResponse>(_SituacaoUsuarioResponse_QNAME, SituacaoUsuarioResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarColaboradores }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarColaboradores")
    public JAXBElement<ConsultarColaboradores> createConsultarColaboradores(ConsultarColaboradores value) {
        return new JAXBElement<ConsultarColaboradores>(_ConsultarColaboradores_QNAME, ConsultarColaboradores.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarClientesForaTreino }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarClientesForaTreino")
    public JAXBElement<ConsultarClientesForaTreino> createConsultarClientesForaTreino(ConsultarClientesForaTreino value) {
        return new JAXBElement<ConsultarClientesForaTreino>(_ConsultarClientesForaTreino_QNAME, ConsultarClientesForaTreino.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link GerarVinculoProfessorAlunoResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "gerarVinculoProfessorAlunoResponse")
    public JAXBElement<GerarVinculoProfessorAlunoResponse> createGerarVinculoProfessorAlunoResponse(GerarVinculoProfessorAlunoResponse value) {
        return new JAXBElement<GerarVinculoProfessorAlunoResponse>(_GerarVinculoProfessorAlunoResponse_QNAME, GerarVinculoProfessorAlunoResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarContratosClienteResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarContratosClienteResponse")
    public JAXBElement<ConsultarContratosClienteResponse> createConsultarContratosClienteResponse(ConsultarContratosClienteResponse value) {
        return new JAXBElement<ConsultarContratosClienteResponse>(_ConsultarContratosClienteResponse_QNAME, ConsultarContratosClienteResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link BuscarClientesPesquisaTW }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "buscarClientesPesquisaTW")
    public JAXBElement<BuscarClientesPesquisaTW> createBuscarClientesPesquisaTW(BuscarClientesPesquisaTW value) {
        return new JAXBElement<BuscarClientesPesquisaTW>(_BuscarClientesPesquisaTW_QNAME, BuscarClientesPesquisaTW.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link VerificarUsuarioMovel }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "verificarUsuarioMovel")
    public JAXBElement<VerificarUsuarioMovel> createVerificarUsuarioMovel(VerificarUsuarioMovel value) {
        return new JAXBElement<VerificarUsuarioMovel>(_VerificarUsuarioMovel_QNAME, VerificarUsuarioMovel.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link NomenclaturaVendaCredito }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "nomenclaturaVendaCredito")
    public JAXBElement<NomenclaturaVendaCredito> createNomenclaturaVendaCredito(NomenclaturaVendaCredito value) {
        return new JAXBElement<NomenclaturaVendaCredito>(_NomenclaturaVendaCredito_QNAME, NomenclaturaVendaCredito.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarClienteJsonResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarClienteJsonResponse")
    public JAXBElement<ConsultarClienteJsonResponse> createConsultarClienteJsonResponse(ConsultarClienteJsonResponse value) {
        return new JAXBElement<ConsultarClienteJsonResponse>(_ConsultarClienteJsonResponse_QNAME, ConsultarClienteJsonResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link DownloadBoleto }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "downloadBoleto")
    public JAXBElement<DownloadBoleto> createDownloadBoleto(DownloadBoleto value) {
        return new JAXBElement<DownloadBoleto>(_DownloadBoleto_QNAME, DownloadBoleto.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarQuantidadeAcessosClientesAgrupadosDiaResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarQuantidadeAcessosClientesAgrupadosDiaResponse")
    public JAXBElement<ConsultarQuantidadeAcessosClientesAgrupadosDiaResponse> createConsultarQuantidadeAcessosClientesAgrupadosDiaResponse(ConsultarQuantidadeAcessosClientesAgrupadosDiaResponse value) {
        return new JAXBElement<ConsultarQuantidadeAcessosClientesAgrupadosDiaResponse>(_ConsultarQuantidadeAcessosClientesAgrupadosDiaResponse_QNAME, ConsultarQuantidadeAcessosClientesAgrupadosDiaResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link LancarConviteResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "lancarConviteResponse")
    public JAXBElement<LancarConviteResponse> createLancarConviteResponse(LancarConviteResponse value) {
        return new JAXBElement<LancarConviteResponse>(_LancarConviteResponse_QNAME, LancarConviteResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link MarcarAvaliacaoFeedGestao }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "marcarAvaliacaoFeedGestao")
    public JAXBElement<MarcarAvaliacaoFeedGestao> createMarcarAvaliacaoFeedGestao(MarcarAvaliacaoFeedGestao value) {
        return new JAXBElement<MarcarAvaliacaoFeedGestao>(_MarcarAvaliacaoFeedGestao_QNAME, MarcarAvaliacaoFeedGestao.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link RecuperarLoginResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "recuperarLoginResponse")
    public JAXBElement<RecuperarLoginResponse> createRecuperarLoginResponse(RecuperarLoginResponse value) {
        return new JAXBElement<RecuperarLoginResponse>(_RecuperarLoginResponse_QNAME, RecuperarLoginResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link AlterarSenhaUsuarioMovelResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "alterarSenhaUsuarioMovelResponse")
    public JAXBElement<AlterarSenhaUsuarioMovelResponse> createAlterarSenhaUsuarioMovelResponse(AlterarSenhaUsuarioMovelResponse value) {
        return new JAXBElement<AlterarSenhaUsuarioMovelResponse>(_AlterarSenhaUsuarioMovelResponse_QNAME, AlterarSenhaUsuarioMovelResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarProdutosCliente }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarProdutosCliente")
    public JAXBElement<ConsultarProdutosCliente> createConsultarProdutosCliente(ConsultarProdutosCliente value) {
        return new JAXBElement<ConsultarProdutosCliente>(_ConsultarProdutosCliente_QNAME, ConsultarProdutosCliente.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ProfissoesResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "profissoesResponse")
    public JAXBElement<ProfissoesResponse> createProfissoesResponse(ProfissoesResponse value) {
        return new JAXBElement<ProfissoesResponse>(_ProfissoesResponse_QNAME, ProfissoesResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link RetornoTrancamento }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "retornoTrancamento")
    public JAXBElement<RetornoTrancamento> createRetornoTrancamento(RetornoTrancamento value) {
        return new JAXBElement<RetornoTrancamento>(_RetornoTrancamento_QNAME, RetornoTrancamento.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ListaAlunoPontos }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "listaAlunoPontos")
    public JAXBElement<ListaAlunoPontos> createListaAlunoPontos(ListaAlunoPontos value) {
        return new JAXBElement<ListaAlunoPontos>(_ListaAlunoPontos_QNAME, ListaAlunoPontos.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarCidade }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarCidade")
    public JAXBElement<ConsultarCidade> createConsultarCidade(ConsultarCidade value) {
        return new JAXBElement<ConsultarCidade>(_ConsultarCidade_QNAME, ConsultarCidade.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link GerarVinculoProfessorAluno }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "gerarVinculoProfessorAluno")
    public JAXBElement<GerarVinculoProfessorAluno> createGerarVinculoProfessorAluno(GerarVinculoProfessorAluno value) {
        return new JAXBElement<GerarVinculoProfessorAluno>(_GerarVinculoProfessorAluno_QNAME, GerarVinculoProfessorAluno.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarJustificativasOperacao }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarJustificativasOperacao")
    public JAXBElement<ConsultarJustificativasOperacao> createConsultarJustificativasOperacao(ConsultarJustificativasOperacao value) {
        return new JAXBElement<ConsultarJustificativasOperacao>(_ConsultarJustificativasOperacao_QNAME, ConsultarJustificativasOperacao.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ListaClientesEstacionamentoSelfit }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "listaClientesEstacionamentoSelfit")
    public JAXBElement<ListaClientesEstacionamentoSelfit> createListaClientesEstacionamentoSelfit(ListaClientesEstacionamentoSelfit value) {
        return new JAXBElement<ListaClientesEstacionamentoSelfit>(_ListaClientesEstacionamentoSelfit_QNAME, ListaClientesEstacionamentoSelfit.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ValidarUsuarioTelefone }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "validarUsuarioTelefone")
    public JAXBElement<ValidarUsuarioTelefone> createValidarUsuarioTelefone(ValidarUsuarioTelefone value) {
        return new JAXBElement<ValidarUsuarioTelefone>(_ValidarUsuarioTelefone_QNAME, ValidarUsuarioTelefone.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link DeletarPagamentosContratoImportacaoJSON }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "deletarPagamentosContratoImportacaoJSON")
    public JAXBElement<DeletarPagamentosContratoImportacaoJSON> createDeletarPagamentosContratoImportacaoJSON(DeletarPagamentosContratoImportacaoJSON value) {
        return new JAXBElement<DeletarPagamentosContratoImportacaoJSON>(_DeletarPagamentosContratoImportacaoJSON_QNAME, DeletarPagamentosContratoImportacaoJSON.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link DeletarPagamentosContratoImportacaoJSONResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "deletarPagamentosContratoImportacaoJSONResponse")
    public JAXBElement<DeletarPagamentosContratoImportacaoJSONResponse> createDeletarPagamentosContratoImportacaoJSONResponse(DeletarPagamentosContratoImportacaoJSONResponse value) {
        return new JAXBElement<DeletarPagamentosContratoImportacaoJSONResponse>(_DeletarPagamentosContratoImportacaoJSONResponse_QNAME, DeletarPagamentosContratoImportacaoJSONResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarClientesForaTreinoResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarClientesForaTreinoResponse")
    public JAXBElement<ConsultarClientesForaTreinoResponse> createConsultarClientesForaTreinoResponse(ConsultarClientesForaTreinoResponse value) {
        return new JAXBElement<ConsultarClientesForaTreinoResponse>(_ConsultarClientesForaTreinoResponse_QNAME, ConsultarClientesForaTreinoResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarClienteSinteticoResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarClienteSinteticoResponse")
    public JAXBElement<ConsultarClienteSinteticoResponse> createConsultarClienteSinteticoResponse(ConsultarClienteSinteticoResponse value) {
        return new JAXBElement<ConsultarClienteSinteticoResponse>(_ConsultarClienteSinteticoResponse_QNAME, ConsultarClienteSinteticoResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link BuscarLocaisAcessoResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "buscarLocaisAcessoResponse")
    public JAXBElement<BuscarLocaisAcessoResponse> createBuscarLocaisAcessoResponse(BuscarLocaisAcessoResponse value) {
        return new JAXBElement<BuscarLocaisAcessoResponse>(_BuscarLocaisAcessoResponse_QNAME, BuscarLocaisAcessoResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link GerarVendaCreditosResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "gerarVendaCreditosResponse")
    public JAXBElement<GerarVendaCreditosResponse> createGerarVendaCreditosResponse(GerarVendaCreditosResponse value) {
        return new JAXBElement<GerarVendaCreditosResponse>(_GerarVendaCreditosResponse_QNAME, GerarVendaCreditosResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link PersistirClienteSite }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "persistirClienteSite")
    public JAXBElement<PersistirClienteSite> createPersistirClienteSite(PersistirClienteSite value) {
        return new JAXBElement<PersistirClienteSite>(_PersistirClienteSite_QNAME, PersistirClienteSite.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarIntegracaoFeraJsonResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarIntegracaoFeraJsonResponse")
    public JAXBElement<ConsultarIntegracaoFeraJsonResponse> createConsultarIntegracaoFeraJsonResponse(ConsultarIntegracaoFeraJsonResponse value) {
        return new JAXBElement<ConsultarIntegracaoFeraJsonResponse>(_ConsultarIntegracaoFeraJsonResponse_QNAME, ConsultarIntegracaoFeraJsonResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link EnviarNotificacaoUsuarioResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "enviarNotificacaoUsuarioResponse")
    public JAXBElement<EnviarNotificacaoUsuarioResponse> createEnviarNotificacaoUsuarioResponse(EnviarNotificacaoUsuarioResponse value) {
        return new JAXBElement<EnviarNotificacaoUsuarioResponse>(_EnviarNotificacaoUsuarioResponse_QNAME, EnviarNotificacaoUsuarioResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link MarcarAvaliacaoFeedGestaoResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "marcarAvaliacaoFeedGestaoResponse")
    public JAXBElement<MarcarAvaliacaoFeedGestaoResponse> createMarcarAvaliacaoFeedGestaoResponse(MarcarAvaliacaoFeedGestaoResponse value) {
        return new JAXBElement<MarcarAvaliacaoFeedGestaoResponse>(_MarcarAvaliacaoFeedGestaoResponse_QNAME, MarcarAvaliacaoFeedGestaoResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ObterDadosContratoPorDuracaoResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "obterDadosContratoPorDuracaoResponse")
    public JAXBElement<ObterDadosContratoPorDuracaoResponse> createObterDadosContratoPorDuracaoResponse(ObterDadosContratoPorDuracaoResponse value) {
        return new JAXBElement<ObterDadosContratoPorDuracaoResponse>(_ObterDadosContratoPorDuracaoResponse_QNAME, ObterDadosContratoPorDuracaoResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link EnderecosGeolocalizarAposHabilitarChaveResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "enderecosGeolocalizarAposHabilitarChaveResponse")
    public JAXBElement<EnderecosGeolocalizarAposHabilitarChaveResponse> createEnderecosGeolocalizarAposHabilitarChaveResponse(EnderecosGeolocalizarAposHabilitarChaveResponse value) {
        return new JAXBElement<EnderecosGeolocalizarAposHabilitarChaveResponse>(_EnderecosGeolocalizarAposHabilitarChaveResponse_QNAME, EnderecosGeolocalizarAposHabilitarChaveResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link RecuperarUsuarioMovel }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "recuperarUsuarioMovel")
    public JAXBElement<RecuperarUsuarioMovel> createRecuperarUsuarioMovel(RecuperarUsuarioMovel value) {
        return new JAXBElement<RecuperarUsuarioMovel>(_RecuperarUsuarioMovel_QNAME, RecuperarUsuarioMovel.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link LancarProdutoAtestadoResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "lancarProdutoAtestadoResponse")
    public JAXBElement<LancarProdutoAtestadoResponse> createLancarProdutoAtestadoResponse(LancarProdutoAtestadoResponse value) {
        return new JAXBElement<LancarProdutoAtestadoResponse>(_LancarProdutoAtestadoResponse_QNAME, LancarProdutoAtestadoResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link IncluirAssinaturaDigitalResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "incluirAssinaturaDigitalResponse")
    public JAXBElement<IncluirAssinaturaDigitalResponse> createIncluirAssinaturaDigitalResponse(IncluirAssinaturaDigitalResponse value) {
        return new JAXBElement<IncluirAssinaturaDigitalResponse>(_IncluirAssinaturaDigitalResponse_QNAME, IncluirAssinaturaDigitalResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarClienteSintetico }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarClienteSintetico")
    public JAXBElement<ConsultarClienteSintetico> createConsultarClienteSintetico(ConsultarClienteSintetico value) {
        return new JAXBElement<ConsultarClienteSintetico>(_ConsultarClienteSintetico_QNAME, ConsultarClienteSintetico.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarFrequenciaAluno }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarFrequenciaAluno")
    public JAXBElement<ConsultarFrequenciaAluno> createConsultarFrequenciaAluno(ConsultarFrequenciaAluno value) {
        return new JAXBElement<ConsultarFrequenciaAluno>(_ConsultarFrequenciaAluno_QNAME, ConsultarFrequenciaAluno.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link EnviarCodigoVerificacaoResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "enviarCodigoVerificacaoResponse")
    public JAXBElement<EnviarCodigoVerificacaoResponse> createEnviarCodigoVerificacaoResponse(EnviarCodigoVerificacaoResponse value) {
        return new JAXBElement<EnviarCodigoVerificacaoResponse>(_EnviarCodigoVerificacaoResponse_QNAME, EnviarCodigoVerificacaoResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link AtualizarGeolocalizacaoResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "atualizarGeolocalizacaoResponse")
    public JAXBElement<AtualizarGeolocalizacaoResponse> createAtualizarGeolocalizacaoResponse(AtualizarGeolocalizacaoResponse value) {
        return new JAXBElement<AtualizarGeolocalizacaoResponse>(_AtualizarGeolocalizacaoResponse_QNAME, AtualizarGeolocalizacaoResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarGrupoDeRiscoPelaMatricula }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarGrupoDeRiscoPelaMatricula")
    public JAXBElement<ConsultarGrupoDeRiscoPelaMatricula> createConsultarGrupoDeRiscoPelaMatricula(ConsultarGrupoDeRiscoPelaMatricula value) {
        return new JAXBElement<ConsultarGrupoDeRiscoPelaMatricula>(_ConsultarGrupoDeRiscoPelaMatricula_QNAME, ConsultarGrupoDeRiscoPelaMatricula.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link EnderecosGeolocalizar }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "enderecosGeolocalizar")
    public JAXBElement<EnderecosGeolocalizar> createEnderecosGeolocalizar(EnderecosGeolocalizar value) {
        return new JAXBElement<EnderecosGeolocalizar>(_EnderecosGeolocalizar_QNAME, EnderecosGeolocalizar.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link PersistirColaborador }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "persistirColaborador")
    public JAXBElement<PersistirColaborador> createPersistirColaborador(PersistirColaborador value) {
        return new JAXBElement<PersistirColaborador>(_PersistirColaborador_QNAME, PersistirColaborador.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link PersistirColaboradorResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "persistirColaboradorResponse")
    public JAXBElement<PersistirColaboradorResponse> createPersistirColaboradorResponse(PersistirColaboradorResponse value) {
        return new JAXBElement<PersistirColaboradorResponse>(_PersistirColaboradorResponse_QNAME, PersistirColaboradorResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link LancarConvite }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "lancarConvite")
    public JAXBElement<LancarConvite> createLancarConvite(LancarConvite value) {
        return new JAXBElement<LancarConvite>(_LancarConvite_QNAME, LancarConvite.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link AlterarUsuarioMovelAlunoResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "alterarUsuarioMovelAlunoResponse")
    public JAXBElement<AlterarUsuarioMovelAlunoResponse> createAlterarUsuarioMovelAlunoResponse(AlterarUsuarioMovelAlunoResponse value) {
        return new JAXBElement<AlterarUsuarioMovelAlunoResponse>(_AlterarUsuarioMovelAlunoResponse_QNAME, AlterarUsuarioMovelAlunoResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link PersistirContratoJSON }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "persistirContratoJSON")
    public JAXBElement<PersistirContratoJSON> createPersistirContratoJSON(PersistirContratoJSON value) {
        return new JAXBElement<PersistirContratoJSON>(_PersistirContratoJSON_QNAME, PersistirContratoJSON.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link AlterarUsuarioMovelAluno }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "alterarUsuarioMovelAluno")
    public JAXBElement<AlterarUsuarioMovelAluno> createAlterarUsuarioMovelAluno(AlterarUsuarioMovelAluno value) {
        return new JAXBElement<AlterarUsuarioMovelAluno>(_AlterarUsuarioMovelAluno_QNAME, AlterarUsuarioMovelAluno.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarColaboradoresTreinoResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarColaboradoresTreinoResponse")
    public JAXBElement<ConsultarColaboradoresTreinoResponse> createConsultarColaboradoresTreinoResponse(ConsultarColaboradoresTreinoResponse value) {
        return new JAXBElement<ConsultarColaboradoresTreinoResponse>(_ConsultarColaboradoresTreinoResponse_QNAME, ConsultarColaboradoresTreinoResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarAcessosListaDiaResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarAcessosListaDiaResponse")
    public JAXBElement<ConsultarAcessosListaDiaResponse> createConsultarAcessosListaDiaResponse(ConsultarAcessosListaDiaResponse value) {
        return new JAXBElement<ConsultarAcessosListaDiaResponse>(_ConsultarAcessosListaDiaResponse_QNAME, ConsultarAcessosListaDiaResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarFeedGestaoResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarFeedGestaoResponse")
    public JAXBElement<ConsultarFeedGestaoResponse> createConsultarFeedGestaoResponse(ConsultarFeedGestaoResponse value) {
        return new JAXBElement<ConsultarFeedGestaoResponse>(_ConsultarFeedGestaoResponse_QNAME, ConsultarFeedGestaoResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarDadosSolicitarAtendimentoResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarDadosSolicitarAtendimentoResponse")
    public JAXBElement<ConsultarDadosSolicitarAtendimentoResponse> createConsultarDadosSolicitarAtendimentoResponse(ConsultarDadosSolicitarAtendimentoResponse value) {
        return new JAXBElement<ConsultarDadosSolicitarAtendimentoResponse>(_ConsultarDadosSolicitarAtendimentoResponse_QNAME, ConsultarDadosSolicitarAtendimentoResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ValidarUsuarioTelefoneResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "validarUsuarioTelefoneResponse")
    public JAXBElement<ValidarUsuarioTelefoneResponse> createValidarUsuarioTelefoneResponse(ValidarUsuarioTelefoneResponse value) {
        return new JAXBElement<ValidarUsuarioTelefoneResponse>(_ValidarUsuarioTelefoneResponse_QNAME, ValidarUsuarioTelefoneResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarModalidadesResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarModalidadesResponse")
    public JAXBElement<ConsultarModalidadesResponse> createConsultarModalidadesResponse(ConsultarModalidadesResponse value) {
        return new JAXBElement<ConsultarModalidadesResponse>(_ConsultarModalidadesResponse_QNAME, ConsultarModalidadesResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link GerarVendaCreditos }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "gerarVendaCreditos")
    public JAXBElement<GerarVendaCreditos> createGerarVendaCreditos(GerarVendaCreditos value) {
        return new JAXBElement<GerarVendaCreditos>(_GerarVendaCreditos_QNAME, GerarVendaCreditos.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarClienteSinteticoPorEmailPessoa }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarClienteSinteticoPorEmailPessoa")
    public JAXBElement<ConsultarClienteSinteticoPorEmailPessoa> createConsultarClienteSinteticoPorEmailPessoa(ConsultarClienteSinteticoPorEmailPessoa value) {
        return new JAXBElement<ConsultarClienteSinteticoPorEmailPessoa>(_ConsultarClienteSinteticoPorEmailPessoa_QNAME, ConsultarClienteSinteticoPorEmailPessoa.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarClientePorCodigoAcesso }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarClientePorCodigoAcesso")
    public JAXBElement<ConsultarClientePorCodigoAcesso> createConsultarClientePorCodigoAcesso(ConsultarClientePorCodigoAcesso value) {
        return new JAXBElement<ConsultarClientePorCodigoAcesso>(_ConsultarClientePorCodigoAcesso_QNAME, ConsultarClientePorCodigoAcesso.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link EnviarMensagemAoUsuario }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "enviarMensagemAoUsuario")
    public JAXBElement<EnviarMensagemAoUsuario> createEnviarMensagemAoUsuario(EnviarMensagemAoUsuario value) {
        return new JAXBElement<EnviarMensagemAoUsuario>(_EnviarMensagemAoUsuario_QNAME, EnviarMensagemAoUsuario.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ObterValorProdutoCfgEmpresaResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "obterValorProdutoCfgEmpresaResponse")
    public JAXBElement<ObterValorProdutoCfgEmpresaResponse> createObterValorProdutoCfgEmpresaResponse(ObterValorProdutoCfgEmpresaResponse value) {
        return new JAXBElement<ObterValorProdutoCfgEmpresaResponse>(_ObterValorProdutoCfgEmpresaResponse_QNAME, ObterValorProdutoCfgEmpresaResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarParcelasVencidasResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarParcelasVencidasResponse")
    public JAXBElement<ConsultarParcelasVencidasResponse> createConsultarParcelasVencidasResponse(ConsultarParcelasVencidasResponse value) {
        return new JAXBElement<ConsultarParcelasVencidasResponse>(_ConsultarParcelasVencidasResponse_QNAME, ConsultarParcelasVencidasResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link CategoriasResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "categoriasResponse")
    public JAXBElement<CategoriasResponse> createCategoriasResponse(CategoriasResponse value) {
        return new JAXBElement<CategoriasResponse>(_CategoriasResponse_QNAME, CategoriasResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link AtualizarCadastroPersonalResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "atualizarCadastroPersonalResponse")
    public JAXBElement<AtualizarCadastroPersonalResponse> createAtualizarCadastroPersonalResponse(AtualizarCadastroPersonalResponse value) {
        return new JAXBElement<AtualizarCadastroPersonalResponse>(_AtualizarCadastroPersonalResponse_QNAME, AtualizarCadastroPersonalResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link AtualizarBoletoPJBankResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "atualizarBoletoPJBankResponse")
    public JAXBElement<AtualizarBoletoPJBankResponse> createAtualizarBoletoPJBankResponse(AtualizarBoletoPJBankResponse value) {
        return new JAXBElement<AtualizarBoletoPJBankResponse>(_AtualizarBoletoPJBankResponse_QNAME, AtualizarBoletoPJBankResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarProfessoresPeloNomeResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarProfessoresPeloNomeResponse")
    public JAXBElement<ConsultarProfessoresPeloNomeResponse> createConsultarProfessoresPeloNomeResponse(ConsultarProfessoresPeloNomeResponse value) {
        return new JAXBElement<ConsultarProfessoresPeloNomeResponse>(_ConsultarProfessoresPeloNomeResponse_QNAME, ConsultarProfessoresPeloNomeResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link DeletarClienteImportacaoJSON }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "deletarClienteImportacaoJSON")
    public JAXBElement<DeletarClienteImportacaoJSON> createDeletarClienteImportacaoJSON(DeletarClienteImportacaoJSON value) {
        return new JAXBElement<DeletarClienteImportacaoJSON>(_DeletarClienteImportacaoJSON_QNAME, DeletarClienteImportacaoJSON.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link SolicitacoesEmAbertoResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "solicitacoesEmAbertoResponse")
    public JAXBElement<SolicitacoesEmAbertoResponse> createSolicitacoesEmAbertoResponse(SolicitacoesEmAbertoResponse value) {
        return new JAXBElement<SolicitacoesEmAbertoResponse>(_SolicitacoesEmAbertoResponse_QNAME, SolicitacoesEmAbertoResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarQuantidadeAcessosClientesAgrupadosDia }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarQuantidadeAcessosClientesAgrupadosDia")
    public JAXBElement<ConsultarQuantidadeAcessosClientesAgrupadosDia> createConsultarQuantidadeAcessosClientesAgrupadosDia(ConsultarQuantidadeAcessosClientesAgrupadosDia value) {
        return new JAXBElement<ConsultarQuantidadeAcessosClientesAgrupadosDia>(_ConsultarQuantidadeAcessosClientesAgrupadosDia_QNAME, ConsultarQuantidadeAcessosClientesAgrupadosDia.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Categorias }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "categorias")
    public JAXBElement<Categorias> createCategorias(Categorias value) {
        return new JAXBElement<Categorias>(_Categorias_QNAME, Categorias.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarAlunosResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarAlunosResponse")
    public JAXBElement<ConsultarAlunosResponse> createConsultarAlunosResponse(ConsultarAlunosResponse value) {
        return new JAXBElement<ConsultarAlunosResponse>(_ConsultarAlunosResponse_QNAME, ConsultarAlunosResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarFotoKeyPessoa }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarFotoKeyPessoa")
    public JAXBElement<ConsultarFotoKeyPessoa> createConsultarFotoKeyPessoa(ConsultarFotoKeyPessoa value) {
        return new JAXBElement<ConsultarFotoKeyPessoa>(_ConsultarFotoKeyPessoa_QNAME, ConsultarFotoKeyPessoa.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link GravarBaixaProtheus }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "gravarBaixaProtheus")
    public JAXBElement<GravarBaixaProtheus> createGravarBaixaProtheus(GravarBaixaProtheus value) {
        return new JAXBElement<GravarBaixaProtheus>(_GravarBaixaProtheus_QNAME, GravarBaixaProtheus.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarNrDiasParcelaEmAberto }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarNrDiasParcelaEmAberto")
    public JAXBElement<ConsultarNrDiasParcelaEmAberto> createConsultarNrDiasParcelaEmAberto(ConsultarNrDiasParcelaEmAberto value) {
        return new JAXBElement<ConsultarNrDiasParcelaEmAberto>(_ConsultarNrDiasParcelaEmAberto_QNAME, ConsultarNrDiasParcelaEmAberto.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link AtualizarFotoPerfilAlunoCPF }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "atualizarFotoPerfilAlunoCPF")
    public JAXBElement<AtualizarFotoPerfilAlunoCPF> createAtualizarFotoPerfilAlunoCPF(AtualizarFotoPerfilAlunoCPF value) {
        return new JAXBElement<AtualizarFotoPerfilAlunoCPF>(_AtualizarFotoPerfilAlunoCPF_QNAME, AtualizarFotoPerfilAlunoCPF.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ApiWeHelp }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "apiWeHelp")
    public JAXBElement<ApiWeHelp> createApiWeHelp(ApiWeHelp value) {
        return new JAXBElement<ApiWeHelp>(_ApiWeHelp_QNAME, ApiWeHelp.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link VerificarUsuarioMovelResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "verificarUsuarioMovelResponse")
    public JAXBElement<VerificarUsuarioMovelResponse> createVerificarUsuarioMovelResponse(VerificarUsuarioMovelResponse value) {
        return new JAXBElement<VerificarUsuarioMovelResponse>(_VerificarUsuarioMovelResponse_QNAME, VerificarUsuarioMovelResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link PersistirLeadRDResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "persistirLeadRDResponse")
    public JAXBElement<PersistirLeadRDResponse> createPersistirLeadRDResponse(PersistirLeadRDResponse value) {
        return new JAXBElement<PersistirLeadRDResponse>(_PersistirLeadRDResponse_QNAME, PersistirLeadRDResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarParcelasVencidasSESCResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarParcelasVencidasSESCResponse")
    public JAXBElement<ConsultarParcelasVencidasSESCResponse> createConsultarParcelasVencidasSESCResponse(ConsultarParcelasVencidasSESCResponse value) {
        return new JAXBElement<ConsultarParcelasVencidasSESCResponse>(_ConsultarParcelasVencidasSESCResponse_QNAME, ConsultarParcelasVencidasSESCResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarColaboradoresTreino }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarColaboradoresTreino")
    public JAXBElement<ConsultarColaboradoresTreino> createConsultarColaboradoresTreino(ConsultarColaboradoresTreino value) {
        return new JAXBElement<ConsultarColaboradoresTreino>(_ConsultarColaboradoresTreino_QNAME, ConsultarColaboradoresTreino.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link GravarResposta }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "gravarResposta")
    public JAXBElement<GravarResposta> createGravarResposta(GravarResposta value) {
        return new JAXBElement<GravarResposta>(_GravarResposta_QNAME, GravarResposta.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarOperacoesExcecoesResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarOperacoesExcecoesResponse")
    public JAXBElement<ConsultarOperacoesExcecoesResponse> createConsultarOperacoesExcecoesResponse(ConsultarOperacoesExcecoesResponse value) {
        return new JAXBElement<ConsultarOperacoesExcecoesResponse>(_ConsultarOperacoesExcecoesResponse_QNAME, ConsultarOperacoesExcecoesResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ObterEnderecoEmpresaResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "obterEnderecoEmpresaResponse")
    public JAXBElement<ObterEnderecoEmpresaResponse> createObterEnderecoEmpresaResponse(ObterEnderecoEmpresaResponse value) {
        return new JAXBElement<ObterEnderecoEmpresaResponse>(_ObterEnderecoEmpresaResponse_QNAME, ObterEnderecoEmpresaResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link PersistirContratoJSONResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "persistirContratoJSONResponse")
    public JAXBElement<PersistirContratoJSONResponse> createPersistirContratoJSONResponse(PersistirContratoJSONResponse value) {
        return new JAXBElement<PersistirContratoJSONResponse>(_PersistirContratoJSONResponse_QNAME, PersistirContratoJSONResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarPrimeiraParcelaContratoResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarPrimeiraParcelaContratoResponse")
    public JAXBElement<ConsultarPrimeiraParcelaContratoResponse> createConsultarPrimeiraParcelaContratoResponse(ConsultarPrimeiraParcelaContratoResponse value) {
        return new JAXBElement<ConsultarPrimeiraParcelaContratoResponse>(_ConsultarPrimeiraParcelaContratoResponse_QNAME, ConsultarPrimeiraParcelaContratoResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarClientesPelaMatriculaResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarClientesPelaMatriculaResponse")
    public JAXBElement<ConsultarClientesPelaMatriculaResponse> createConsultarClientesPelaMatriculaResponse(ConsultarClientesPelaMatriculaResponse value) {
        return new JAXBElement<ConsultarClientesPelaMatriculaResponse>(_ConsultarClientesPelaMatriculaResponse_QNAME, ConsultarClientesPelaMatriculaResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link DeletarContratoImportacaoJSONResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "deletarContratoImportacaoJSONResponse")
    public JAXBElement<DeletarContratoImportacaoJSONResponse> createDeletarContratoImportacaoJSONResponse(DeletarContratoImportacaoJSONResponse value) {
        return new JAXBElement<DeletarContratoImportacaoJSONResponse>(_DeletarContratoImportacaoJSONResponse_QNAME, DeletarContratoImportacaoJSONResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ObterDuracaoCreditosEmpresaResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "obterDuracaoCreditosEmpresaResponse")
    public JAXBElement<ObterDuracaoCreditosEmpresaResponse> createObterDuracaoCreditosEmpresaResponse(ObterDuracaoCreditosEmpresaResponse value) {
        return new JAXBElement<ObterDuracaoCreditosEmpresaResponse>(_ObterDuracaoCreditosEmpresaResponse_QNAME, ObterDuracaoCreditosEmpresaResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarLocaisAcesso }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarLocaisAcesso")
    public JAXBElement<ConsultarLocaisAcesso> createConsultarLocaisAcesso(ConsultarLocaisAcesso value) {
        return new JAXBElement<ConsultarLocaisAcesso>(_ConsultarLocaisAcesso_QNAME, ConsultarLocaisAcesso.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link EnviarEmailAngular }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "enviarEmailAngular")
    public JAXBElement<EnviarEmailAngular> createEnviarEmailAngular(EnviarEmailAngular value) {
        return new JAXBElement<EnviarEmailAngular>(_EnviarEmailAngular_QNAME, EnviarEmailAngular.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConfiguracaoTotem }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "configuracaoTotem")
    public JAXBElement<ConfiguracaoTotem> createConfiguracaoTotem(ConfiguracaoTotem value) {
        return new JAXBElement<ConfiguracaoTotem>(_ConfiguracaoTotem_QNAME, ConfiguracaoTotem.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ValidarCodigoVerificacaoResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "validarCodigoVerificacaoResponse")
    public JAXBElement<ValidarCodigoVerificacaoResponse> createValidarCodigoVerificacaoResponse(ValidarCodigoVerificacaoResponse value) {
        return new JAXBElement<ValidarCodigoVerificacaoResponse>(_ValidarCodigoVerificacaoResponse_QNAME, ValidarCodigoVerificacaoResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link AlterarTelefoneResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "alterarTelefoneResponse")
    public JAXBElement<AlterarTelefoneResponse> createAlterarTelefoneResponse(AlterarTelefoneResponse value) {
        return new JAXBElement<AlterarTelefoneResponse>(_AlterarTelefoneResponse_QNAME, AlterarTelefoneResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link VerificarStatusLeadBuzzResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "verificarStatusLeadBuzzResponse")
    public JAXBElement<VerificarStatusLeadBuzzResponse> createVerificarStatusLeadBuzzResponse(VerificarStatusLeadBuzzResponse value) {
        return new JAXBElement<VerificarStatusLeadBuzzResponse>(_VerificarStatusLeadBuzzResponse_QNAME, VerificarStatusLeadBuzzResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link SenhaAlteradaAposGeracaoLink }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "senhaAlteradaAposGeracaoLink")
    public JAXBElement<SenhaAlteradaAposGeracaoLink> createSenhaAlteradaAposGeracaoLink(SenhaAlteradaAposGeracaoLink value) {
        return new JAXBElement<SenhaAlteradaAposGeracaoLink>(_SenhaAlteradaAposGeracaoLink_QNAME, SenhaAlteradaAposGeracaoLink.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link SolicitacoesConcluidasResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "solicitacoesConcluidasResponse")
    public JAXBElement<SolicitacoesConcluidasResponse> createSolicitacoesConcluidasResponse(SolicitacoesConcluidasResponse value) {
        return new JAXBElement<SolicitacoesConcluidasResponse>(_SolicitacoesConcluidasResponse_QNAME, SolicitacoesConcluidasResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Classificacoes }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "classificacoes")
    public JAXBElement<Classificacoes> createClassificacoes(Classificacoes value) {
        return new JAXBElement<Classificacoes>(_Classificacoes_QNAME, Classificacoes.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link PersistirAcessosClienteJSONResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "persistirAcessosClienteJSONResponse")
    public JAXBElement<PersistirAcessosClienteJSONResponse> createPersistirAcessosClienteJSONResponse(PersistirAcessosClienteJSONResponse value) {
        return new JAXBElement<PersistirAcessosClienteJSONResponse>(_PersistirAcessosClienteJSONResponse_QNAME, PersistirAcessosClienteJSONResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarParcelasVencidasSESC }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarParcelasVencidasSESC")
    public JAXBElement<ConsultarParcelasVencidasSESC> createConsultarParcelasVencidasSESC(ConsultarParcelasVencidasSESC value) {
        return new JAXBElement<ConsultarParcelasVencidasSESC>(_ConsultarParcelasVencidasSESC_QNAME, ConsultarParcelasVencidasSESC.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link PersistirLeadRD }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "persistirLeadRD")
    public JAXBElement<PersistirLeadRD> createPersistirLeadRD(PersistirLeadRD value) {
        return new JAXBElement<PersistirLeadRD>(_PersistirLeadRD_QNAME, PersistirLeadRD.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link NomenclaturaVendaCreditoResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "nomenclaturaVendaCreditoResponse")
    public JAXBElement<NomenclaturaVendaCreditoResponse> createNomenclaturaVendaCreditoResponse(NomenclaturaVendaCreditoResponse value) {
        return new JAXBElement<NomenclaturaVendaCreditoResponse>(_NomenclaturaVendaCreditoResponse_QNAME, NomenclaturaVendaCreditoResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link AtualizarFotoPerfilAlunoCPFResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "atualizarFotoPerfilAlunoCPFResponse")
    public JAXBElement<AtualizarFotoPerfilAlunoCPFResponse> createAtualizarFotoPerfilAlunoCPFResponse(AtualizarFotoPerfilAlunoCPFResponse value) {
        return new JAXBElement<AtualizarFotoPerfilAlunoCPFResponse>(_AtualizarFotoPerfilAlunoCPFResponse_QNAME, AtualizarFotoPerfilAlunoCPFResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ListaAlunoPontosAppResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "listaAlunoPontosAppResponse")
    public JAXBElement<ListaAlunoPontosAppResponse> createListaAlunoPontosAppResponse(ListaAlunoPontosAppResponse value) {
        return new JAXBElement<ListaAlunoPontosAppResponse>(_ListaAlunoPontosAppResponse_QNAME, ListaAlunoPontosAppResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link RetornoTrancamentoResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "retornoTrancamentoResponse")
    public JAXBElement<RetornoTrancamentoResponse> createRetornoTrancamentoResponse(RetornoTrancamentoResponse value) {
        return new JAXBElement<RetornoTrancamentoResponse>(_RetornoTrancamentoResponse_QNAME, RetornoTrancamentoResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ObterEnderecoCodigo }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "obterEnderecoCodigo")
    public JAXBElement<ObterEnderecoCodigo> createObterEnderecoCodigo(ObterEnderecoCodigo value) {
        return new JAXBElement<ObterEnderecoCodigo>(_ObterEnderecoCodigo_QNAME, ObterEnderecoCodigo.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link InserirWebHookClienteRDResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "inserirWebHookClienteRDResponse")
    public JAXBElement<InserirWebHookClienteRDResponse> createInserirWebHookClienteRDResponse(InserirWebHookClienteRDResponse value) {
        return new JAXBElement<InserirWebHookClienteRDResponse>(_InserirWebHookClienteRDResponse_QNAME, InserirWebHookClienteRDResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarClienteComTelefoneResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarClienteComTelefoneResponse")
    public JAXBElement<ConsultarClienteComTelefoneResponse> createConsultarClienteComTelefoneResponse(ConsultarClienteComTelefoneResponse value) {
        return new JAXBElement<ConsultarClienteComTelefoneResponse>(_ConsultarClienteComTelefoneResponse_QNAME, ConsultarClienteComTelefoneResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link PersistirPagamentosVendaAvulsaJSON }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "persistirPagamentosVendaAvulsaJSON")
    public JAXBElement<PersistirPagamentosVendaAvulsaJSON> createPersistirPagamentosVendaAvulsaJSON(PersistirPagamentosVendaAvulsaJSON value) {
        return new JAXBElement<PersistirPagamentosVendaAvulsaJSON>(_PersistirPagamentosVendaAvulsaJSON_QNAME, PersistirPagamentosVendaAvulsaJSON.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarClienteResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarClienteResponse")
    public JAXBElement<ConsultarClienteResponse> createConsultarClienteResponse(ConsultarClienteResponse value) {
        return new JAXBElement<ConsultarClienteResponse>(_ConsultarClienteResponse_QNAME, ConsultarClienteResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarAcessosDiaResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarAcessosDiaResponse")
    public JAXBElement<ConsultarAcessosDiaResponse> createConsultarAcessosDiaResponse(ConsultarAcessosDiaResponse value) {
        return new JAXBElement<ConsultarAcessosDiaResponse>(_ConsultarAcessosDiaResponse_QNAME, ConsultarAcessosDiaResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link AlterarEnderecoResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "alterarEnderecoResponse")
    public JAXBElement<AlterarEnderecoResponse> createAlterarEnderecoResponse(AlterarEnderecoResponse value) {
        return new JAXBElement<AlterarEnderecoResponse>(_AlterarEnderecoResponse_QNAME, AlterarEnderecoResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ObterPontosPorCliente }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "obterPontosPorCliente")
    public JAXBElement<ObterPontosPorCliente> createObterPontosPorCliente(ObterPontosPorCliente value) {
        return new JAXBElement<ObterPontosPorCliente>(_ObterPontosPorCliente_QNAME, ObterPontosPorCliente.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarCodigoAcesso }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarCodigoAcesso")
    public JAXBElement<ConsultarCodigoAcesso> createConsultarCodigoAcesso(ConsultarCodigoAcesso value) {
        return new JAXBElement<ConsultarCodigoAcesso>(_ConsultarCodigoAcesso_QNAME, ConsultarCodigoAcesso.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarContratosRenovarResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarContratosRenovarResponse")
    public JAXBElement<ConsultarContratosRenovarResponse> createConsultarContratosRenovarResponse(ConsultarContratosRenovarResponse value) {
        return new JAXBElement<ConsultarContratosRenovarResponse>(_ConsultarContratosRenovarResponse_QNAME, ConsultarContratosRenovarResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarCidadeResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarCidadeResponse")
    public JAXBElement<ConsultarCidadeResponse> createConsultarCidadeResponse(ConsultarCidadeResponse value) {
        return new JAXBElement<ConsultarCidadeResponse>(_ConsultarCidadeResponse_QNAME, ConsultarCidadeResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarContratosCliente }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarContratosCliente")
    public JAXBElement<ConsultarContratosCliente> createConsultarContratosCliente(ConsultarContratosCliente value) {
        return new JAXBElement<ConsultarContratosCliente>(_ConsultarContratosCliente_QNAME, ConsultarContratosCliente.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link MarcarComoLidaFeedGestao }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "marcarComoLidaFeedGestao")
    public JAXBElement<MarcarComoLidaFeedGestao> createMarcarComoLidaFeedGestao(MarcarComoLidaFeedGestao value) {
        return new JAXBElement<MarcarComoLidaFeedGestao>(_MarcarComoLidaFeedGestao_QNAME, MarcarComoLidaFeedGestao.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link AlterarUsuarioMovelProfessor }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "alterarUsuarioMovelProfessor")
    public JAXBElement<AlterarUsuarioMovelProfessor> createAlterarUsuarioMovelProfessor(AlterarUsuarioMovelProfessor value) {
        return new JAXBElement<AlterarUsuarioMovelProfessor>(_AlterarUsuarioMovelProfessor_QNAME, AlterarUsuarioMovelProfessor.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link AlterarDadosPessoaisClienteSite }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "alterarDadosPessoaisClienteSite")
    public JAXBElement<AlterarDadosPessoaisClienteSite> createAlterarDadosPessoaisClienteSite(AlterarDadosPessoaisClienteSite value) {
        return new JAXBElement<AlterarDadosPessoaisClienteSite>(_AlterarDadosPessoaisClienteSite_QNAME, AlterarDadosPessoaisClienteSite.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarClienteComTelefone }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarClienteComTelefone")
    public JAXBElement<ConsultarClienteComTelefone> createConsultarClienteComTelefone(ConsultarClienteComTelefone value) {
        return new JAXBElement<ConsultarClienteComTelefone>(_ConsultarClienteComTelefone_QNAME, ConsultarClienteComTelefone.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link BuscarEstatisticasMovidesk }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "buscarEstatisticasMovidesk")
    public JAXBElement<BuscarEstatisticasMovidesk> createBuscarEstatisticasMovidesk(BuscarEstatisticasMovidesk value) {
        return new JAXBElement<BuscarEstatisticasMovidesk>(_BuscarEstatisticasMovidesk_QNAME, BuscarEstatisticasMovidesk.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link AlterarUsuarioMovelProfessorResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "alterarUsuarioMovelProfessorResponse")
    public JAXBElement<AlterarUsuarioMovelProfessorResponse> createAlterarUsuarioMovelProfessorResponse(AlterarUsuarioMovelProfessorResponse value) {
        return new JAXBElement<AlterarUsuarioMovelProfessorResponse>(_AlterarUsuarioMovelProfessorResponse_QNAME, AlterarUsuarioMovelProfessorResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link GerarUsuarioMovelColaboradorResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "gerarUsuarioMovelColaboradorResponse")
    public JAXBElement<GerarUsuarioMovelColaboradorResponse> createGerarUsuarioMovelColaboradorResponse(GerarUsuarioMovelColaboradorResponse value) {
        return new JAXBElement<GerarUsuarioMovelColaboradorResponse>(_GerarUsuarioMovelColaboradorResponse_QNAME, GerarUsuarioMovelColaboradorResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link GravarBaixaProtheusResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "gravarBaixaProtheusResponse")
    public JAXBElement<GravarBaixaProtheusResponse> createGravarBaixaProtheusResponse(GravarBaixaProtheusResponse value) {
        return new JAXBElement<GravarBaixaProtheusResponse>(_GravarBaixaProtheusResponse_QNAME, GravarBaixaProtheusResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link LogarUsuarioMovel }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "logarUsuarioMovel")
    public JAXBElement<LogarUsuarioMovel> createLogarUsuarioMovel(LogarUsuarioMovel value) {
        return new JAXBElement<LogarUsuarioMovel>(_LogarUsuarioMovel_QNAME, LogarUsuarioMovel.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link LancarProdutoAtestado }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "lancarProdutoAtestado")
    public JAXBElement<LancarProdutoAtestado> createLancarProdutoAtestado(LancarProdutoAtestado value) {
        return new JAXBElement<LancarProdutoAtestado>(_LancarProdutoAtestado_QNAME, LancarProdutoAtestado.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarClientesPeloNome }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarClientesPeloNome")
    public JAXBElement<ConsultarClientesPeloNome> createConsultarClientesPeloNome(ConsultarClientesPeloNome value) {
        return new JAXBElement<ConsultarClientesPeloNome>(_ConsultarClientesPeloNome_QNAME, ConsultarClientesPeloNome.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarDadosCliente }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarDadosCliente")
    public JAXBElement<ConsultarDadosCliente> createConsultarDadosCliente(ConsultarDadosCliente value) {
        return new JAXBElement<ConsultarDadosCliente>(_ConsultarDadosCliente_QNAME, ConsultarDadosCliente.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ListaAlunoPontosResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "listaAlunoPontosResponse")
    public JAXBElement<ListaAlunoPontosResponse> createListaAlunoPontosResponse(ListaAlunoPontosResponse value) {
        return new JAXBElement<ListaAlunoPontosResponse>(_ListaAlunoPontosResponse_QNAME, ListaAlunoPontosResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ObterVencimentosContratosResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "obterVencimentosContratosResponse")
    public JAXBElement<ObterVencimentosContratosResponse> createObterVencimentosContratosResponse(ObterVencimentosContratosResponse value) {
        return new JAXBElement<ObterVencimentosContratosResponse>(_ObterVencimentosContratosResponse_QNAME, ObterVencimentosContratosResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ListaProdutosServicosResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "listaProdutosServicosResponse")
    public JAXBElement<ListaProdutosServicosResponse> createListaProdutosServicosResponse(ListaProdutosServicosResponse value) {
        return new JAXBElement<ListaProdutosServicosResponse>(_ListaProdutosServicosResponse_QNAME, ListaProdutosServicosResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link NotificarRecursoEmpresaGeolocalizacaoResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "notificarRecursoEmpresaGeolocalizacaoResponse")
    public JAXBElement<NotificarRecursoEmpresaGeolocalizacaoResponse> createNotificarRecursoEmpresaGeolocalizacaoResponse(NotificarRecursoEmpresaGeolocalizacaoResponse value) {
        return new JAXBElement<NotificarRecursoEmpresaGeolocalizacaoResponse>(_NotificarRecursoEmpresaGeolocalizacaoResponse_QNAME, NotificarRecursoEmpresaGeolocalizacaoResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarCodigoAcessoResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarCodigoAcessoResponse")
    public JAXBElement<ConsultarCodigoAcessoResponse> createConsultarCodigoAcessoResponse(ConsultarCodigoAcessoResponse value) {
        return new JAXBElement<ConsultarCodigoAcessoResponse>(_ConsultarCodigoAcessoResponse_QNAME, ConsultarCodigoAcessoResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarPrimeiraParcelaContrato }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarPrimeiraParcelaContrato")
    public JAXBElement<ConsultarPrimeiraParcelaContrato> createConsultarPrimeiraParcelaContrato(ConsultarPrimeiraParcelaContrato value) {
        return new JAXBElement<ConsultarPrimeiraParcelaContrato>(_ConsultarPrimeiraParcelaContrato_QNAME, ConsultarPrimeiraParcelaContrato.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link AtualizarGeolocalizacao }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "atualizarGeolocalizacao")
    public JAXBElement<AtualizarGeolocalizacao> createAtualizarGeolocalizacao(AtualizarGeolocalizacao value) {
        return new JAXBElement<AtualizarGeolocalizacao>(_AtualizarGeolocalizacao_QNAME, AtualizarGeolocalizacao.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ListaAlunoPontosDataResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "listaAlunoPontosDataResponse")
    public JAXBElement<ListaAlunoPontosDataResponse> createListaAlunoPontosDataResponse(ListaAlunoPontosDataResponse value) {
        return new JAXBElement<ListaAlunoPontosDataResponse>(_ListaAlunoPontosDataResponse_QNAME, ListaAlunoPontosDataResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ObterValorVendasResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "obterValorVendasResponse")
    public JAXBElement<ObterValorVendasResponse> createObterValorVendasResponse(ObterValorVendasResponse value) {
        return new JAXBElement<ObterValorVendasResponse>(_ObterValorVendasResponse_QNAME, ObterValorVendasResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link SalvarCampanhaResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "salvarCampanhaResponse")
    public JAXBElement<SalvarCampanhaResponse> createSalvarCampanhaResponse(SalvarCampanhaResponse value) {
        return new JAXBElement<SalvarCampanhaResponse>(_SalvarCampanhaResponse_QNAME, SalvarCampanhaResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link GravarRespostaResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "gravarRespostaResponse")
    public JAXBElement<GravarRespostaResponse> createGravarRespostaResponse(GravarRespostaResponse value) {
        return new JAXBElement<GravarRespostaResponse>(_GravarRespostaResponse_QNAME, GravarRespostaResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarModalidadesEmpresa }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarModalidadesEmpresa")
    public JAXBElement<ConsultarModalidadesEmpresa> createConsultarModalidadesEmpresa(ConsultarModalidadesEmpresa value) {
        return new JAXBElement<ConsultarModalidadesEmpresa>(_ConsultarModalidadesEmpresa_QNAME, ConsultarModalidadesEmpresa.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ValidarConvitesResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "validarConvitesResponse")
    public JAXBElement<ValidarConvitesResponse> createValidarConvitesResponse(ValidarConvitesResponse value) {
        return new JAXBElement<ValidarConvitesResponse>(_ValidarConvitesResponse_QNAME, ValidarConvitesResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarDetalhesDF }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarDetalhesDF")
    public JAXBElement<ConsultarDetalhesDF> createConsultarDetalhesDF(ConsultarDetalhesDF value) {
        return new JAXBElement<ConsultarDetalhesDF>(_ConsultarDetalhesDF_QNAME, ConsultarDetalhesDF.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarParcelasClienteResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarParcelasClienteResponse")
    public JAXBElement<ConsultarParcelasClienteResponse> createConsultarParcelasClienteResponse(ConsultarParcelasClienteResponse value) {
        return new JAXBElement<ConsultarParcelasClienteResponse>(_ConsultarParcelasClienteResponse_QNAME, ConsultarParcelasClienteResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarClienteNaRedeEmpresaResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarClienteNaRedeEmpresaResponse")
    public JAXBElement<ConsultarClienteNaRedeEmpresaResponse> createConsultarClienteNaRedeEmpresaResponse(ConsultarClienteNaRedeEmpresaResponse value) {
        return new JAXBElement<ConsultarClienteNaRedeEmpresaResponse>(_ConsultarClienteNaRedeEmpresaResponse_QNAME, ConsultarClienteNaRedeEmpresaResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link PersistirLeadBuzzResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "persistirLeadBuzzResponse")
    public JAXBElement<PersistirLeadBuzzResponse> createPersistirLeadBuzzResponse(PersistirLeadBuzzResponse value) {
        return new JAXBElement<PersistirLeadBuzzResponse>(_PersistirLeadBuzzResponse_QNAME, PersistirLeadBuzzResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ValidarUsuarioResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "validarUsuarioResponse")
    public JAXBElement<ValidarUsuarioResponse> createValidarUsuarioResponse(ValidarUsuarioResponse value) {
        return new JAXBElement<ValidarUsuarioResponse>(_ValidarUsuarioResponse_QNAME, ValidarUsuarioResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarDadosGameResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarDadosGameResponse")
    public JAXBElement<ConsultarDadosGameResponse> createConsultarDadosGameResponse(ConsultarDadosGameResponse value) {
        return new JAXBElement<ConsultarDadosGameResponse>(_ConsultarDadosGameResponse_QNAME, ConsultarDadosGameResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarDadosOperacaoContratoResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarDadosOperacaoContratoResponse")
    public JAXBElement<ConsultarDadosOperacaoContratoResponse> createConsultarDadosOperacaoContratoResponse(ConsultarDadosOperacaoContratoResponse value) {
        return new JAXBElement<ConsultarDadosOperacaoContratoResponse>(_ConsultarDadosOperacaoContratoResponse_QNAME, ConsultarDadosOperacaoContratoResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarEmpresasSimples }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarEmpresasSimples")
    public JAXBElement<ConsultarEmpresasSimples> createConsultarEmpresasSimples(ConsultarEmpresasSimples value) {
        return new JAXBElement<ConsultarEmpresasSimples>(_ConsultarEmpresasSimples_QNAME, ConsultarEmpresasSimples.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarOperacoesExcecoes }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarOperacoesExcecoes")
    public JAXBElement<ConsultarOperacoesExcecoes> createConsultarOperacoesExcecoes(ConsultarOperacoesExcecoes value) {
        return new JAXBElement<ConsultarOperacoesExcecoes>(_ConsultarOperacoesExcecoes_QNAME, ConsultarOperacoesExcecoes.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ValidarProdutoVigenteResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "validarProdutoVigenteResponse")
    public JAXBElement<ValidarProdutoVigenteResponse> createValidarProdutoVigenteResponse(ValidarProdutoVigenteResponse value) {
        return new JAXBElement<ValidarProdutoVigenteResponse>(_ValidarProdutoVigenteResponse_QNAME, ValidarProdutoVigenteResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarModalidades }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarModalidades")
    public JAXBElement<ConsultarModalidades> createConsultarModalidades(ConsultarModalidades value) {
        return new JAXBElement<ConsultarModalidades>(_ConsultarModalidades_QNAME, ConsultarModalidades.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link AtualizarCadastroPersonal }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "atualizarCadastroPersonal")
    public JAXBElement<AtualizarCadastroPersonal> createAtualizarCadastroPersonal(AtualizarCadastroPersonal value) {
        return new JAXBElement<AtualizarCadastroPersonal>(_AtualizarCadastroPersonal_QNAME, AtualizarCadastroPersonal.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link PersistirLeadBuzz }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "persistirLeadBuzz")
    public JAXBElement<PersistirLeadBuzz> createPersistirLeadBuzz(PersistirLeadBuzz value) {
        return new JAXBElement<PersistirLeadBuzz>(_PersistirLeadBuzz_QNAME, PersistirLeadBuzz.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarParcelasVencidas }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarParcelasVencidas")
    public JAXBElement<ConsultarParcelasVencidas> createConsultarParcelasVencidas(ConsultarParcelasVencidas value) {
        return new JAXBElement<ConsultarParcelasVencidas>(_ConsultarParcelasVencidas_QNAME, ConsultarParcelasVencidas.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ObterConfiguracaoEmailPadraoResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "obterConfiguracaoEmailPadraoResponse")
    public JAXBElement<ObterConfiguracaoEmailPadraoResponse> createObterConfiguracaoEmailPadraoResponse(ObterConfiguracaoEmailPadraoResponse value) {
        return new JAXBElement<ObterConfiguracaoEmailPadraoResponse>(_ObterConfiguracaoEmailPadraoResponse_QNAME, ObterConfiguracaoEmailPadraoResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link PersistirCancelarContratoJSON }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "persistirCancelarContratoJSON")
    public JAXBElement<PersistirCancelarContratoJSON> createPersistirCancelarContratoJSON(PersistirCancelarContratoJSON value) {
        return new JAXBElement<PersistirCancelarContratoJSON>(_PersistirCancelarContratoJSON_QNAME, PersistirCancelarContratoJSON.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarEmpresas }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarEmpresas")
    public JAXBElement<ConsultarEmpresas> createConsultarEmpresas(ConsultarEmpresas value) {
        return new JAXBElement<ConsultarEmpresas>(_ConsultarEmpresas_QNAME, ConsultarEmpresas.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarEstatisticaSolicitacao }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarEstatisticaSolicitacao")
    public JAXBElement<ConsultarEstatisticaSolicitacao> createConsultarEstatisticaSolicitacao(ConsultarEstatisticaSolicitacao value) {
        return new JAXBElement<ConsultarEstatisticaSolicitacao>(_ConsultarEstatisticaSolicitacao_QNAME, ConsultarEstatisticaSolicitacao.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarDetalhesDFResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarDetalhesDFResponse")
    public JAXBElement<ConsultarDetalhesDFResponse> createConsultarDetalhesDFResponse(ConsultarDetalhesDFResponse value) {
        return new JAXBElement<ConsultarDetalhesDFResponse>(_ConsultarDetalhesDFResponse_QNAME, ConsultarDetalhesDFResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConfiguracaoTotemResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "configuracaoTotemResponse")
    public JAXBElement<ConfiguracaoTotemResponse> createConfiguracaoTotemResponse(ConfiguracaoTotemResponse value) {
        return new JAXBElement<ConfiguracaoTotemResponse>(_ConfiguracaoTotemResponse_QNAME, ConfiguracaoTotemResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ListaClientesEstacionamentoSelfitResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "listaClientesEstacionamentoSelfitResponse")
    public JAXBElement<ListaClientesEstacionamentoSelfitResponse> createListaClientesEstacionamentoSelfitResponse(ListaClientesEstacionamentoSelfitResponse value) {
        return new JAXBElement<ListaClientesEstacionamentoSelfitResponse>(_ListaClientesEstacionamentoSelfitResponse_QNAME, ListaClientesEstacionamentoSelfitResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarClientesTreino }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarClientesTreino")
    public JAXBElement<ConsultarClientesTreino> createConsultarClientesTreino(ConsultarClientesTreino value) {
        return new JAXBElement<ConsultarClientesTreino>(_ConsultarClientesTreino_QNAME, ConsultarClientesTreino.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ListaProdutosAtestadoResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "listaProdutosAtestadoResponse")
    public JAXBElement<ListaProdutosAtestadoResponse> createListaProdutosAtestadoResponse(ListaProdutosAtestadoResponse value) {
        return new JAXBElement<ListaProdutosAtestadoResponse>(_ListaProdutosAtestadoResponse_QNAME, ListaProdutosAtestadoResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link IncluirAssinaturaDigital }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "incluirAssinaturaDigital")
    public JAXBElement<IncluirAssinaturaDigital> createIncluirAssinaturaDigital(IncluirAssinaturaDigital value) {
        return new JAXBElement<IncluirAssinaturaDigital>(_IncluirAssinaturaDigital_QNAME, IncluirAssinaturaDigital.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ValidarConvites }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "validarConvites")
    public JAXBElement<ValidarConvites> createValidarConvites(ValidarConvites value) {
        return new JAXBElement<ValidarConvites>(_ValidarConvites_QNAME, ValidarConvites.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link SituacaoUsuario }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "situacaoUsuario")
    public JAXBElement<SituacaoUsuario> createSituacaoUsuario(SituacaoUsuario value) {
        return new JAXBElement<SituacaoUsuario>(_SituacaoUsuario_QNAME, SituacaoUsuario.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link EnviarNotificacaoUsuario }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "enviarNotificacaoUsuario")
    public JAXBElement<EnviarNotificacaoUsuario> createEnviarNotificacaoUsuario(EnviarNotificacaoUsuario value) {
        return new JAXBElement<EnviarNotificacaoUsuario>(_EnviarNotificacaoUsuario_QNAME, EnviarNotificacaoUsuario.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarColetores }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarColetores")
    public JAXBElement<ConsultarColetores> createConsultarColetores(ConsultarColetores value) {
        return new JAXBElement<ConsultarColetores>(_ConsultarColetores_QNAME, ConsultarColetores.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link GravarConfiguracoesGameResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "gravarConfiguracoesGameResponse")
    public JAXBElement<GravarConfiguracoesGameResponse> createGravarConfiguracoesGameResponse(GravarConfiguracoesGameResponse value) {
        return new JAXBElement<GravarConfiguracoesGameResponse>(_GravarConfiguracoesGameResponse_QNAME, GravarConfiguracoesGameResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link BuscarClientesPesquisaTWResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "buscarClientesPesquisaTWResponse")
    public JAXBElement<BuscarClientesPesquisaTWResponse> createBuscarClientesPesquisaTWResponse(BuscarClientesPesquisaTWResponse value) {
        return new JAXBElement<BuscarClientesPesquisaTWResponse>(_BuscarClientesPesquisaTWResponse_QNAME, BuscarClientesPesquisaTWResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ObterValorVendas }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "obterValorVendas")
    public JAXBElement<ObterValorVendas> createObterValorVendas(ObterValorVendas value) {
        return new JAXBElement<ObterValorVendas>(_ObterValorVendas_QNAME, ObterValorVendas.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ValidarUsuario }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "validarUsuario")
    public JAXBElement<ValidarUsuario> createValidarUsuario(ValidarUsuario value) {
        return new JAXBElement<ValidarUsuario>(_ValidarUsuario_QNAME, ValidarUsuario.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link RecuperarLogin }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "recuperarLogin")
    public JAXBElement<RecuperarLogin> createRecuperarLogin(RecuperarLogin value) {
        return new JAXBElement<RecuperarLogin>(_RecuperarLogin_QNAME, RecuperarLogin.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarClientesResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarClientesResponse")
    public JAXBElement<ConsultarClientesResponse> createConsultarClientesResponse(ConsultarClientesResponse value) {
        return new JAXBElement<ConsultarClientesResponse>(_ConsultarClientesResponse_QNAME, ConsultarClientesResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link SalvarCampanha }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "salvarCampanha")
    public JAXBElement<SalvarCampanha> createSalvarCampanha(SalvarCampanha value) {
        return new JAXBElement<SalvarCampanha>(_SalvarCampanha_QNAME, SalvarCampanha.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link EnviarEmailColaboradorResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "enviarEmailColaboradorResponse")
    public JAXBElement<EnviarEmailColaboradorResponse> createEnviarEmailColaboradorResponse(EnviarEmailColaboradorResponse value) {
        return new JAXBElement<EnviarEmailColaboradorResponse>(_EnviarEmailColaboradorResponse_QNAME, EnviarEmailColaboradorResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link GravarDadosOperacaoContrato }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "gravarDadosOperacaoContrato")
    public JAXBElement<GravarDadosOperacaoContrato> createGravarDadosOperacaoContrato(GravarDadosOperacaoContrato value) {
        return new JAXBElement<GravarDadosOperacaoContrato>(_GravarDadosOperacaoContrato_QNAME, GravarDadosOperacaoContrato.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ResponderQuestionarioResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "responderQuestionarioResponse")
    public JAXBElement<ResponderQuestionarioResponse> createResponderQuestionarioResponse(ResponderQuestionarioResponse value) {
        return new JAXBElement<ResponderQuestionarioResponse>(_ResponderQuestionarioResponse_QNAME, ResponderQuestionarioResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ListaProdutosServicos }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "listaProdutosServicos")
    public JAXBElement<ListaProdutosServicos> createListaProdutosServicos(ListaProdutosServicos value) {
        return new JAXBElement<ListaProdutosServicos>(_ListaProdutosServicos_QNAME, ListaProdutosServicos.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link PersistirLeadGenerico }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "persistirLeadGenerico")
    public JAXBElement<PersistirLeadGenerico> createPersistirLeadGenerico(PersistirLeadGenerico value) {
        return new JAXBElement<PersistirLeadGenerico>(_PersistirLeadGenerico_QNAME, PersistirLeadGenerico.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarFrequenciaAlunoResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarFrequenciaAlunoResponse")
    public JAXBElement<ConsultarFrequenciaAlunoResponse> createConsultarFrequenciaAlunoResponse(ConsultarFrequenciaAlunoResponse value) {
        return new JAXBElement<ConsultarFrequenciaAlunoResponse>(_ConsultarFrequenciaAlunoResponse_QNAME, ConsultarFrequenciaAlunoResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link AlterarUsuarioMovelAlunoNovo }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "alterarUsuarioMovelAlunoNovo")
    public JAXBElement<AlterarUsuarioMovelAlunoNovo> createAlterarUsuarioMovelAlunoNovo(AlterarUsuarioMovelAlunoNovo value) {
        return new JAXBElement<AlterarUsuarioMovelAlunoNovo>(_AlterarUsuarioMovelAlunoNovo_QNAME, AlterarUsuarioMovelAlunoNovo.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link AcessTokenValido }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "acessTokenValido")
    public JAXBElement<AcessTokenValido> createAcessTokenValido(AcessTokenValido value) {
        return new JAXBElement<AcessTokenValido>(_AcessTokenValido_QNAME, AcessTokenValido.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarClientePorMatriculaExternaImportacaoResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarClientePorMatriculaExternaImportacaoResponse")
    public JAXBElement<ConsultarClientePorMatriculaExternaImportacaoResponse> createConsultarClientePorMatriculaExternaImportacaoResponse(ConsultarClientePorMatriculaExternaImportacaoResponse value) {
        return new JAXBElement<ConsultarClientePorMatriculaExternaImportacaoResponse>(_ConsultarClientePorMatriculaExternaImportacaoResponse_QNAME, ConsultarClientePorMatriculaExternaImportacaoResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarMeioRecuperarLogin }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarMeioRecuperarLogin")
    public JAXBElement<ConsultarMeioRecuperarLogin> createConsultarMeioRecuperarLogin(ConsultarMeioRecuperarLogin value) {
        return new JAXBElement<ConsultarMeioRecuperarLogin>(_ConsultarMeioRecuperarLogin_QNAME, ConsultarMeioRecuperarLogin.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ObterConfiguracaoEmailPadrao }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "obterConfiguracaoEmailPadrao")
    public JAXBElement<ObterConfiguracaoEmailPadrao> createObterConfiguracaoEmailPadrao(ObterConfiguracaoEmailPadrao value) {
        return new JAXBElement<ObterConfiguracaoEmailPadrao>(_ObterConfiguracaoEmailPadrao_QNAME, ObterConfiguracaoEmailPadrao.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarConfiguracaoENotasResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarConfiguracaoENotasResponse")
    public JAXBElement<ConsultarConfiguracaoENotasResponse> createConsultarConfiguracaoENotasResponse(ConsultarConfiguracaoENotasResponse value) {
        return new JAXBElement<ConsultarConfiguracaoENotasResponse>(_ConsultarConfiguracaoENotasResponse_QNAME, ConsultarConfiguracaoENotasResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ObterValorProdutoCfgEmpresa }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "obterValorProdutoCfgEmpresa")
    public JAXBElement<ObterValorProdutoCfgEmpresa> createObterValorProdutoCfgEmpresa(ObterValorProdutoCfgEmpresa value) {
        return new JAXBElement<ObterValorProdutoCfgEmpresa>(_ObterValorProdutoCfgEmpresa_QNAME, ObterValorProdutoCfgEmpresa.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link PersistirLogAuditoria }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "persistirLogAuditoria")
    public JAXBElement<PersistirLogAuditoria> createPersistirLogAuditoria(PersistirLogAuditoria value) {
        return new JAXBElement<PersistirLogAuditoria>(_PersistirLogAuditoria_QNAME, PersistirLogAuditoria.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarLocalAcessoPorNFC }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarLocalAcessoPorNFC")
    public JAXBElement<ConsultarLocalAcessoPorNFC> createConsultarLocalAcessoPorNFC(ConsultarLocalAcessoPorNFC value) {
        return new JAXBElement<ConsultarLocalAcessoPorNFC>(_ConsultarLocalAcessoPorNFC_QNAME, ConsultarLocalAcessoPorNFC.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarConsultoresGame }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarConsultoresGame")
    public JAXBElement<ConsultarConsultoresGame> createConsultarConsultoresGame(ConsultarConsultoresGame value) {
        return new JAXBElement<ConsultarConsultoresGame>(_ConsultarConsultoresGame_QNAME, ConsultarConsultoresGame.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link PersistirClienteSiteResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "persistirClienteSiteResponse")
    public JAXBElement<PersistirClienteSiteResponse> createPersistirClienteSiteResponse(PersistirClienteSiteResponse value) {
        return new JAXBElement<PersistirClienteSiteResponse>(_PersistirClienteSiteResponse_QNAME, PersistirClienteSiteResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link AtualizarDadosGerenciaisDiaResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "atualizarDadosGerenciaisDiaResponse")
    public JAXBElement<AtualizarDadosGerenciaisDiaResponse> createAtualizarDadosGerenciaisDiaResponse(AtualizarDadosGerenciaisDiaResponse value) {
        return new JAXBElement<AtualizarDadosGerenciaisDiaResponse>(_AtualizarDadosGerenciaisDiaResponse_QNAME, AtualizarDadosGerenciaisDiaResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link VerificarStatusLeadBuzz }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "verificarStatusLeadBuzz")
    public JAXBElement<VerificarStatusLeadBuzz> createVerificarStatusLeadBuzz(VerificarStatusLeadBuzz value) {
        return new JAXBElement<VerificarStatusLeadBuzz>(_VerificarStatusLeadBuzz_QNAME, VerificarStatusLeadBuzz.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultaGenerica }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultaGenerica")
    public JAXBElement<ConsultaGenerica> createConsultaGenerica(ConsultaGenerica value) {
        return new JAXBElement<ConsultaGenerica>(_ConsultaGenerica_QNAME, ConsultaGenerica.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarEstado }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarEstado")
    public JAXBElement<ConsultarEstado> createConsultarEstado(ConsultarEstado value) {
        return new JAXBElement<ConsultarEstado>(_ConsultarEstado_QNAME, ConsultarEstado.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link AlterarDadosPessoaisClienteSiteResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "alterarDadosPessoaisClienteSiteResponse")
    public JAXBElement<AlterarDadosPessoaisClienteSiteResponse> createAlterarDadosPessoaisClienteSiteResponse(AlterarDadosPessoaisClienteSiteResponse value) {
        return new JAXBElement<AlterarDadosPessoaisClienteSiteResponse>(_AlterarDadosPessoaisClienteSiteResponse_QNAME, AlterarDadosPessoaisClienteSiteResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ObterDadosContratoPorDuracao }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "obterDadosContratoPorDuracao")
    public JAXBElement<ObterDadosContratoPorDuracao> createObterDadosContratoPorDuracao(ObterDadosContratoPorDuracao value) {
        return new JAXBElement<ObterDadosContratoPorDuracao>(_ObterDadosContratoPorDuracao_QNAME, ObterDadosContratoPorDuracao.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarNrAlunosAtivosForaTreino }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarNrAlunosAtivosForaTreino")
    public JAXBElement<ConsultarNrAlunosAtivosForaTreino> createConsultarNrAlunosAtivosForaTreino(ConsultarNrAlunosAtivosForaTreino value) {
        return new JAXBElement<ConsultarNrAlunosAtivosForaTreino>(_ConsultarNrAlunosAtivosForaTreino_QNAME, ConsultarNrAlunosAtivosForaTreino.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarNrDiasParcelaEmAbertoResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarNrDiasParcelaEmAbertoResponse")
    public JAXBElement<ConsultarNrDiasParcelaEmAbertoResponse> createConsultarNrDiasParcelaEmAbertoResponse(ConsultarNrDiasParcelaEmAbertoResponse value) {
        return new JAXBElement<ConsultarNrDiasParcelaEmAbertoResponse>(_ConsultarNrDiasParcelaEmAbertoResponse_QNAME, ConsultarNrDiasParcelaEmAbertoResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link EnviarMensagemAoUsuarioUsernameResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "enviarMensagemAoUsuarioUsernameResponse")
    public JAXBElement<EnviarMensagemAoUsuarioUsernameResponse> createEnviarMensagemAoUsuarioUsernameResponse(EnviarMensagemAoUsuarioUsernameResponse value) {
        return new JAXBElement<EnviarMensagemAoUsuarioUsernameResponse>(_EnviarMensagemAoUsuarioUsernameResponse_QNAME, EnviarMensagemAoUsuarioUsernameResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarClientesRenovaramOuNao }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarClientesRenovaramOuNao")
    public JAXBElement<ConsultarClientesRenovaramOuNao> createConsultarClientesRenovaramOuNao(ConsultarClientesRenovaramOuNao value) {
        return new JAXBElement<ConsultarClientesRenovaramOuNao>(_ConsultarClientesRenovaramOuNao_QNAME, ConsultarClientesRenovaramOuNao.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ValidarProdutoVigente }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "validarProdutoVigente")
    public JAXBElement<ValidarProdutoVigente> createValidarProdutoVigente(ValidarProdutoVigente value) {
        return new JAXBElement<ValidarProdutoVigente>(_ValidarProdutoVigente_QNAME, ValidarProdutoVigente.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarClienteNaRedeEmpresa }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarClienteNaRedeEmpresa")
    public JAXBElement<ConsultarClienteNaRedeEmpresa> createConsultarClienteNaRedeEmpresa(ConsultarClienteNaRedeEmpresa value) {
        return new JAXBElement<ConsultarClienteNaRedeEmpresa>(_ConsultarClienteNaRedeEmpresa_QNAME, ConsultarClienteNaRedeEmpresa.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link EnderecosGeolocalizarAposHabilitarChave }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "enderecosGeolocalizarAposHabilitarChave")
    public JAXBElement<EnderecosGeolocalizarAposHabilitarChave> createEnderecosGeolocalizarAposHabilitarChave(EnderecosGeolocalizarAposHabilitarChave value) {
        return new JAXBElement<EnderecosGeolocalizarAposHabilitarChave>(_EnderecosGeolocalizarAposHabilitarChave_QNAME, EnderecosGeolocalizarAposHabilitarChave.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ObterDuracaoCreditosEmpresa }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "obterDuracaoCreditosEmpresa")
    public JAXBElement<ObterDuracaoCreditosEmpresa> createObterDuracaoCreditosEmpresa(ObterDuracaoCreditosEmpresa value) {
        return new JAXBElement<ObterDuracaoCreditosEmpresa>(_ObterDuracaoCreditosEmpresa_QNAME, ObterDuracaoCreditosEmpresa.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link SolicitacoesEmAberto }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "solicitacoesEmAberto")
    public JAXBElement<SolicitacoesEmAberto> createSolicitacoesEmAberto(SolicitacoesEmAberto value) {
        return new JAXBElement<SolicitacoesEmAberto>(_SolicitacoesEmAberto_QNAME, SolicitacoesEmAberto.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link EnviarEmailGenericoResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "enviarEmailGenericoResponse")
    public JAXBElement<EnviarEmailGenericoResponse> createEnviarEmailGenericoResponse(EnviarEmailGenericoResponse value) {
        return new JAXBElement<EnviarEmailGenericoResponse>(_EnviarEmailGenericoResponse_QNAME, EnviarEmailGenericoResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarClientesRenovaramOuNaoResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarClientesRenovaramOuNaoResponse")
    public JAXBElement<ConsultarClientesRenovaramOuNaoResponse> createConsultarClientesRenovaramOuNaoResponse(ConsultarClientesRenovaramOuNaoResponse value) {
        return new JAXBElement<ConsultarClientesRenovaramOuNaoResponse>(_ConsultarClientesRenovaramOuNaoResponse_QNAME, ConsultarClientesRenovaramOuNaoResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link UrlFotoEmpresaResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "urlFotoEmpresaResponse")
    public JAXBElement<UrlFotoEmpresaResponse> createUrlFotoEmpresaResponse(UrlFotoEmpresaResponse value) {
        return new JAXBElement<UrlFotoEmpresaResponse>(_UrlFotoEmpresaResponse_QNAME, UrlFotoEmpresaResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link UpdateGeolocEmpresaResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "updateGeolocEmpresaResponse")
    public JAXBElement<UpdateGeolocEmpresaResponse> createUpdateGeolocEmpresaResponse(UpdateGeolocEmpresaResponse value) {
        return new JAXBElement<UpdateGeolocEmpresaResponse>(_UpdateGeolocEmpresaResponse_QNAME, UpdateGeolocEmpresaResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultaGenericaPaginadaResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultaGenericaPaginadaResponse")
    public JAXBElement<ConsultaGenericaPaginadaResponse> createConsultaGenericaPaginadaResponse(ConsultaGenericaPaginadaResponse value) {
        return new JAXBElement<ConsultaGenericaPaginadaResponse>(_ConsultaGenericaPaginadaResponse_QNAME, ConsultaGenericaPaginadaResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarClientesPelaMatricula }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarClientesPelaMatricula")
    public JAXBElement<ConsultarClientesPelaMatricula> createConsultarClientesPelaMatricula(ConsultarClientesPelaMatricula value) {
        return new JAXBElement<ConsultarClientesPelaMatricula>(_ConsultarClientesPelaMatricula_QNAME, ConsultarClientesPelaMatricula.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ValidarUsuarioEmail }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "validarUsuarioEmail")
    public JAXBElement<ValidarUsuarioEmail> createValidarUsuarioEmail(ValidarUsuarioEmail value) {
        return new JAXBElement<ValidarUsuarioEmail>(_ValidarUsuarioEmail_QNAME, ValidarUsuarioEmail.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarClientesPeloNomeNoLimits }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarClientesPeloNomeNoLimits")
    public JAXBElement<ConsultarClientesPeloNomeNoLimits> createConsultarClientesPeloNomeNoLimits(ConsultarClientesPeloNomeNoLimits value) {
        return new JAXBElement<ConsultarClientesPeloNomeNoLimits>(_ConsultarClientesPeloNomeNoLimits_QNAME, ConsultarClientesPeloNomeNoLimits.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarConsultoresGameResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarConsultoresGameResponse")
    public JAXBElement<ConsultarConsultoresGameResponse> createConsultarConsultoresGameResponse(ConsultarConsultoresGameResponse value) {
        return new JAXBElement<ConsultarConsultoresGameResponse>(_ConsultarConsultoresGameResponse_QNAME, ConsultarConsultoresGameResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link GerarUsuarioMovelAluno }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "gerarUsuarioMovelAluno")
    public JAXBElement<GerarUsuarioMovelAluno> createGerarUsuarioMovelAluno(GerarUsuarioMovelAluno value) {
        return new JAXBElement<GerarUsuarioMovelAluno>(_GerarUsuarioMovelAluno_QNAME, GerarUsuarioMovelAluno.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link GerarUsuarioMovelAlunoResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "gerarUsuarioMovelAlunoResponse")
    public JAXBElement<GerarUsuarioMovelAlunoResponse> createGerarUsuarioMovelAlunoResponse(GerarUsuarioMovelAlunoResponse value) {
        return new JAXBElement<GerarUsuarioMovelAlunoResponse>(_GerarUsuarioMovelAlunoResponse_QNAME, GerarUsuarioMovelAlunoResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link PersistirOperacoesContratoJSON }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "persistirOperacoesContratoJSON")
    public JAXBElement<PersistirOperacoesContratoJSON> createPersistirOperacoesContratoJSON(PersistirOperacoesContratoJSON value) {
        return new JAXBElement<PersistirOperacoesContratoJSON>(_PersistirOperacoesContratoJSON_QNAME, PersistirOperacoesContratoJSON.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarClientePorMatriculaExternaImportacao }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarClientePorMatriculaExternaImportacao")
    public JAXBElement<ConsultarClientePorMatriculaExternaImportacao> createConsultarClientePorMatriculaExternaImportacao(ConsultarClientePorMatriculaExternaImportacao value) {
        return new JAXBElement<ConsultarClientePorMatriculaExternaImportacao>(_ConsultarClientePorMatriculaExternaImportacao_QNAME, ConsultarClientePorMatriculaExternaImportacao.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link AtualizarDadosGerenciaisPorMes }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "atualizarDadosGerenciaisPorMes")
    public JAXBElement<AtualizarDadosGerenciaisPorMes> createAtualizarDadosGerenciaisPorMes(AtualizarDadosGerenciaisPorMes value) {
        return new JAXBElement<AtualizarDadosGerenciaisPorMes>(_AtualizarDadosGerenciaisPorMes_QNAME, AtualizarDadosGerenciaisPorMes.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarClientesAtivosForaTreinoResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarClientesAtivosForaTreinoResponse")
    public JAXBElement<ConsultarClientesAtivosForaTreinoResponse> createConsultarClientesAtivosForaTreinoResponse(ConsultarClientesAtivosForaTreinoResponse value) {
        return new JAXBElement<ConsultarClientesAtivosForaTreinoResponse>(_ConsultarClientesAtivosForaTreinoResponse_QNAME, ConsultarClientesAtivosForaTreinoResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link GravarConfiguracoesGame }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "gravarConfiguracoesGame")
    public JAXBElement<GravarConfiguracoesGame> createGravarConfiguracoesGame(GravarConfiguracoesGame value) {
        return new JAXBElement<GravarConfiguracoesGame>(_GravarConfiguracoesGame_QNAME, GravarConfiguracoesGame.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ValidarUsuarioEmailResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "validarUsuarioEmailResponse")
    public JAXBElement<ValidarUsuarioEmailResponse> createValidarUsuarioEmailResponse(ValidarUsuarioEmailResponse value) {
        return new JAXBElement<ValidarUsuarioEmailResponse>(_ValidarUsuarioEmailResponse_QNAME, ValidarUsuarioEmailResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarDadosClienteResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarDadosClienteResponse")
    public JAXBElement<ConsultarDadosClienteResponse> createConsultarDadosClienteResponse(ConsultarDadosClienteResponse value) {
        return new JAXBElement<ConsultarDadosClienteResponse>(_ConsultarDadosClienteResponse_QNAME, ConsultarDadosClienteResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link EnviarEmailTokenApp }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "enviarEmailTokenApp")
    public JAXBElement<EnviarEmailTokenApp> createEnviarEmailTokenApp(EnviarEmailTokenApp value) {
        return new JAXBElement<EnviarEmailTokenApp>(_EnviarEmailTokenApp_QNAME, EnviarEmailTokenApp.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ObterEnderecos }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "obterEnderecos")
    public JAXBElement<ObterEnderecos> createObterEnderecos(ObterEnderecos value) {
        return new JAXBElement<ObterEnderecos>(_ObterEnderecos_QNAME, ObterEnderecos.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarDadosSolicitarAtendimento }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarDadosSolicitarAtendimento")
    public JAXBElement<ConsultarDadosSolicitarAtendimento> createConsultarDadosSolicitarAtendimento(ConsultarDadosSolicitarAtendimento value) {
        return new JAXBElement<ConsultarDadosSolicitarAtendimento>(_ConsultarDadosSolicitarAtendimento_QNAME, ConsultarDadosSolicitarAtendimento.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link DeletarClienteImportacaoJSONResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "deletarClienteImportacaoJSONResponse")
    public JAXBElement<DeletarClienteImportacaoJSONResponse> createDeletarClienteImportacaoJSONResponse(DeletarClienteImportacaoJSONResponse value) {
        return new JAXBElement<DeletarClienteImportacaoJSONResponse>(_DeletarClienteImportacaoJSONResponse_QNAME, DeletarClienteImportacaoJSONResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link DownloadBoletoResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "downloadBoletoResponse")
    public JAXBElement<DownloadBoletoResponse> createDownloadBoletoResponse(DownloadBoletoResponse value) {
        return new JAXBElement<DownloadBoletoResponse>(_DownloadBoletoResponse_QNAME, DownloadBoletoResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarCupomDescontoResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarCupomDescontoResponse")
    public JAXBElement<ConsultarCupomDescontoResponse> createConsultarCupomDescontoResponse(ConsultarCupomDescontoResponse value) {
        return new JAXBElement<ConsultarCupomDescontoResponse>(_ConsultarCupomDescontoResponse_QNAME, ConsultarCupomDescontoResponse.class, null, value);
    }

}
