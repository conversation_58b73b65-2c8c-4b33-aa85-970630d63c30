
package servicos.integracao.zw.client;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java de gerarConviteViaWeb complex type.
 * 
 * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
 * 
 * <pre>
 * &lt;complexType name="gerarConviteViaWeb">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="key" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="codigoUsuarioConvidou" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="codigoTipoConvite" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="codigoIndicadoConvidado" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="codigoPassivoConvidado" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="codigoClienteConvidado" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="codigoColaboradorResponsavelConvite" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "gerarConviteViaWeb", propOrder = {
    "key",
    "codigoUsuarioConvidou",
    "codigoTipoConvite",
    "codigoIndicadoConvidado",
    "codigoPassivoConvidado",
    "codigoClienteConvidado",
    "codigoColaboradorResponsavelConvite"
})
public class GerarConviteViaWeb {

    protected String key;
    protected Integer codigoUsuarioConvidou;
    protected Integer codigoTipoConvite;
    protected Integer codigoIndicadoConvidado;
    protected Integer codigoPassivoConvidado;
    protected Integer codigoClienteConvidado;
    protected Integer codigoColaboradorResponsavelConvite;

    /**
     * Obtém o valor da propriedade key.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getKey() {
        return key;
    }

    /**
     * Define o valor da propriedade key.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setKey(String value) {
        this.key = value;
    }

    /**
     * Obtém o valor da propriedade codigoUsuarioConvidou.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getCodigoUsuarioConvidou() {
        return codigoUsuarioConvidou;
    }

    /**
     * Define o valor da propriedade codigoUsuarioConvidou.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setCodigoUsuarioConvidou(Integer value) {
        this.codigoUsuarioConvidou = value;
    }

    /**
     * Obtém o valor da propriedade codigoTipoConvite.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getCodigoTipoConvite() {
        return codigoTipoConvite;
    }

    /**
     * Define o valor da propriedade codigoTipoConvite.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setCodigoTipoConvite(Integer value) {
        this.codigoTipoConvite = value;
    }

    /**
     * Obtém o valor da propriedade codigoIndicadoConvidado.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getCodigoIndicadoConvidado() {
        return codigoIndicadoConvidado;
    }

    /**
     * Define o valor da propriedade codigoIndicadoConvidado.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setCodigoIndicadoConvidado(Integer value) {
        this.codigoIndicadoConvidado = value;
    }

    /**
     * Obtém o valor da propriedade codigoPassivoConvidado.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getCodigoPassivoConvidado() {
        return codigoPassivoConvidado;
    }

    /**
     * Define o valor da propriedade codigoPassivoConvidado.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setCodigoPassivoConvidado(Integer value) {
        this.codigoPassivoConvidado = value;
    }

    /**
     * Obtém o valor da propriedade codigoClienteConvidado.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getCodigoClienteConvidado() {
        return codigoClienteConvidado;
    }

    /**
     * Define o valor da propriedade codigoClienteConvidado.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setCodigoClienteConvidado(Integer value) {
        this.codigoClienteConvidado = value;
    }

    /**
     * Obtém o valor da propriedade codigoColaboradorResponsavelConvite.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getCodigoColaboradorResponsavelConvite() {
        return codigoColaboradorResponsavelConvite;
    }

    /**
     * Define o valor da propriedade codigoColaboradorResponsavelConvite.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setCodigoColaboradorResponsavelConvite(Integer value) {
        this.codigoColaboradorResponsavelConvite = value;
    }

}
