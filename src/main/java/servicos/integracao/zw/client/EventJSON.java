
package servicos.integracao.zw.client;

import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java de eventJSON complex type.
 * 
 * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
 * 
 * <pre>
 * &lt;complexType name="eventJSON">
 *   &lt;complexContent>
 *     &lt;extension base="{http://webservice.basico.comuns.negocio/}superJSON">
 *       &lt;sequence>
 *         &lt;element name="boxID" type="{http://www.w3.org/2001/XMLSchema}int"/>
 *         &lt;element name="description" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="endDateTime" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="error" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         &lt;element name="errorMsg" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="eventID" type="{http://www.w3.org/2001/XMLSchema}int"/>
 *         &lt;element name="instructorClientID" type="{http://www.w3.org/2001/XMLSchema}int"/>
 *         &lt;element name="instructorName" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="isCancelled" type="{http://www.w3.org/2001/XMLSchema}int"/>
 *         &lt;element name="programType" type="{http://www.w3.org/2001/XMLSchema}int"/>
 *         &lt;element name="scheduleID" type="{http://www.w3.org/2001/XMLSchema}int"/>
 *         &lt;element name="seatTeacher" type="{http://webservice.basico.comuns.negocio/}seatJSON" minOccurs="0"/>
 *         &lt;element name="siteID" type="{http://www.w3.org/2001/XMLSchema}int"/>
 *         &lt;element name="startDateTime" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="studioLayoutRows" type="{http://webservice.basico.comuns.negocio/}studioLayoutRowJSON" maxOccurs="unbounded" minOccurs="0"/>
 *         &lt;element name="title" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="venueName" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="virtualClass" type="{http://www.w3.org/2001/XMLSchema}int"/>
 *       &lt;/sequence>
 *     &lt;/extension>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "eventJSON", propOrder = {
    "boxID",
    "description",
    "endDateTime",
    "error",
    "errorMsg",
    "eventID",
    "instructorClientID",
    "instructorName",
    "isCancelled",
    "programType",
    "scheduleID",
    "seatTeacher",
    "siteID",
    "startDateTime",
    "studioLayoutRows",
    "title",
    "venueName",
    "virtualClass"
})
public class EventJSON
    extends SuperJSON
{

    protected int boxID;
    protected String description;
    protected String endDateTime;
    protected boolean error;
    protected String errorMsg;
    protected int eventID;
    protected int instructorClientID;
    protected String instructorName;
    protected int isCancelled;
    protected int programType;
    protected int scheduleID;
    protected SeatJSON seatTeacher;
    protected int siteID;
    protected String startDateTime;
    @XmlElement(nillable = true)
    protected List<StudioLayoutRowJSON> studioLayoutRows;
    protected String title;
    protected String venueName;
    protected int virtualClass;

    /**
     * Obtém o valor da propriedade boxID.
     * 
     */
    public int getBoxID() {
        return boxID;
    }

    /**
     * Define o valor da propriedade boxID.
     * 
     */
    public void setBoxID(int value) {
        this.boxID = value;
    }

    /**
     * Obtém o valor da propriedade description.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDescription() {
        return description;
    }

    /**
     * Define o valor da propriedade description.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDescription(String value) {
        this.description = value;
    }

    /**
     * Obtém o valor da propriedade endDateTime.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getEndDateTime() {
        return endDateTime;
    }

    /**
     * Define o valor da propriedade endDateTime.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setEndDateTime(String value) {
        this.endDateTime = value;
    }

    /**
     * Obtém o valor da propriedade error.
     * 
     */
    public boolean isError() {
        return error;
    }

    /**
     * Define o valor da propriedade error.
     * 
     */
    public void setError(boolean value) {
        this.error = value;
    }

    /**
     * Obtém o valor da propriedade errorMsg.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getErrorMsg() {
        return errorMsg;
    }

    /**
     * Define o valor da propriedade errorMsg.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setErrorMsg(String value) {
        this.errorMsg = value;
    }

    /**
     * Obtém o valor da propriedade eventID.
     * 
     */
    public int getEventID() {
        return eventID;
    }

    /**
     * Define o valor da propriedade eventID.
     * 
     */
    public void setEventID(int value) {
        this.eventID = value;
    }

    /**
     * Obtém o valor da propriedade instructorClientID.
     * 
     */
    public int getInstructorClientID() {
        return instructorClientID;
    }

    /**
     * Define o valor da propriedade instructorClientID.
     * 
     */
    public void setInstructorClientID(int value) {
        this.instructorClientID = value;
    }

    /**
     * Obtém o valor da propriedade instructorName.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getInstructorName() {
        return instructorName;
    }

    /**
     * Define o valor da propriedade instructorName.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setInstructorName(String value) {
        this.instructorName = value;
    }

    /**
     * Obtém o valor da propriedade isCancelled.
     * 
     */
    public int getIsCancelled() {
        return isCancelled;
    }

    /**
     * Define o valor da propriedade isCancelled.
     * 
     */
    public void setIsCancelled(int value) {
        this.isCancelled = value;
    }

    /**
     * Obtém o valor da propriedade programType.
     * 
     */
    public int getProgramType() {
        return programType;
    }

    /**
     * Define o valor da propriedade programType.
     * 
     */
    public void setProgramType(int value) {
        this.programType = value;
    }

    /**
     * Obtém o valor da propriedade scheduleID.
     * 
     */
    public int getScheduleID() {
        return scheduleID;
    }

    /**
     * Define o valor da propriedade scheduleID.
     * 
     */
    public void setScheduleID(int value) {
        this.scheduleID = value;
    }

    /**
     * Obtém o valor da propriedade seatTeacher.
     * 
     * @return
     *     possible object is
     *     {@link SeatJSON }
     *     
     */
    public SeatJSON getSeatTeacher() {
        return seatTeacher;
    }

    /**
     * Define o valor da propriedade seatTeacher.
     * 
     * @param value
     *     allowed object is
     *     {@link SeatJSON }
     *     
     */
    public void setSeatTeacher(SeatJSON value) {
        this.seatTeacher = value;
    }

    /**
     * Obtém o valor da propriedade siteID.
     * 
     */
    public int getSiteID() {
        return siteID;
    }

    /**
     * Define o valor da propriedade siteID.
     * 
     */
    public void setSiteID(int value) {
        this.siteID = value;
    }

    /**
     * Obtém o valor da propriedade startDateTime.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getStartDateTime() {
        return startDateTime;
    }

    /**
     * Define o valor da propriedade startDateTime.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setStartDateTime(String value) {
        this.startDateTime = value;
    }

    /**
     * Gets the value of the studioLayoutRows property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the studioLayoutRows property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getStudioLayoutRows().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link StudioLayoutRowJSON }
     * 
     * 
     */
    public List<StudioLayoutRowJSON> getStudioLayoutRows() {
        if (studioLayoutRows == null) {
            studioLayoutRows = new ArrayList<StudioLayoutRowJSON>();
        }
        return this.studioLayoutRows;
    }

    /**
     * Obtém o valor da propriedade title.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTitle() {
        return title;
    }

    /**
     * Define o valor da propriedade title.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTitle(String value) {
        this.title = value;
    }

    /**
     * Obtém o valor da propriedade venueName.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getVenueName() {
        return venueName;
    }

    /**
     * Define o valor da propriedade venueName.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setVenueName(String value) {
        this.venueName = value;
    }

    /**
     * Obtém o valor da propriedade virtualClass.
     * 
     */
    public int getVirtualClass() {
        return virtualClass;
    }

    /**
     * Define o valor da propriedade virtualClass.
     * 
     */
    public void setVirtualClass(int value) {
        this.virtualClass = value;
    }

}
