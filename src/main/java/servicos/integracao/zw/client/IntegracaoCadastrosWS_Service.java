
package servicos.integracao.zw.client;

import java.net.MalformedURLException;
import java.net.URL;
import javax.xml.namespace.QName;
import javax.xml.ws.Service;
import javax.xml.ws.WebEndpoint;
import javax.xml.ws.WebServiceClient;
import javax.xml.ws.WebServiceException;
import javax.xml.ws.WebServiceFeature;


/**
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.2.6b21 
 * Generated source version: 2.1
 * 
 */
@WebServiceClient(name = "IntegracaoCadastrosWS", targetNamespace = "http://webservice.basico.comuns.negocio/", wsdlLocation = "https://app.pactosolucoes.com.br/app/IntegracaoCadastrosWS?wsdl")
public class IntegracaoCadastrosWS_Service
    extends Service
{

    private final static URL INTEGRACAOCADASTROSWS_WSDL_LOCATION;
    private final static WebServiceException INTEGRACAOCADASTROSWS_EXCEPTION;
    private final static QName INTEGRACAOCADASTROSWS_QNAME = new QName("http://webservice.basico.comuns.negocio/", "IntegracaoCadastrosWS");

    static {
        URL url = null;
        WebServiceException e = null;
        try {
            url = new URL("https://app.pactosolucoes.com.br/app/IntegracaoCadastrosWS?wsdl");
        } catch (MalformedURLException ex) {
            e = new WebServiceException(ex);
        }
        INTEGRACAOCADASTROSWS_WSDL_LOCATION = url;
        INTEGRACAOCADASTROSWS_EXCEPTION = e;
    }

    public IntegracaoCadastrosWS_Service() {
        super(__getWsdlLocation(), INTEGRACAOCADASTROSWS_QNAME);
    }

    public IntegracaoCadastrosWS_Service(URL wsdlLocation, QName serviceName) {
        super(wsdlLocation, serviceName);
    }

    /**
     * 
     * @return
     *     returns IntegracaoCadastrosWS
     */
    @WebEndpoint(name = "IntegracaoCadastrosWSPort")
    public IntegracaoCadastrosWS getIntegracaoCadastrosWSPort() {
        return super.getPort(new QName("http://webservice.basico.comuns.negocio/", "IntegracaoCadastrosWSPort"), IntegracaoCadastrosWS.class);
    }

    /**
     * 
     * @param features
     *     A list of {@link javax.xml.ws.WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return
     *     returns IntegracaoCadastrosWS
     */
    @WebEndpoint(name = "IntegracaoCadastrosWSPort")
    public IntegracaoCadastrosWS getIntegracaoCadastrosWSPort(WebServiceFeature... features) {
        return super.getPort(new QName("http://webservice.basico.comuns.negocio/", "IntegracaoCadastrosWSPort"), IntegracaoCadastrosWS.class, features);
    }

    private static URL __getWsdlLocation() {
        if (INTEGRACAOCADASTROSWS_EXCEPTION!= null) {
            throw INTEGRACAOCADASTROSWS_EXCEPTION;
        }
        return INTEGRACAOCADASTROSWS_WSDL_LOCATION;
    }

}
