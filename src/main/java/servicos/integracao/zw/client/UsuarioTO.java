
package servicos.integracao.zw.client;

import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java de usuarioTO complex type.
 * 
 * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
 * 
 * <pre>
 * &lt;complexType name="usuarioTO">
 *   &lt;complexContent>
 *     &lt;extension base="{http://webservice.basico.comuns.negocio/}superTO">
 *       &lt;sequence>
 *         &lt;element name="acessoTeste" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         &lt;element name="administrador" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         &lt;element name="appGestor" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         &lt;element name="codigo" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="codigoPessoa" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="colaboradorId" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="credecialTreino" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="diaExpiracaoAcesso" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="empresaDefault" type="{http://webservice.basico.comuns.negocio/}empresaWS" minOccurs="0"/>
 *         &lt;element name="empresas" type="{http://webservice.basico.comuns.negocio/}empresaWS" maxOccurs="unbounded" minOccurs="0"/>
 *         &lt;element name="empresasPermissaoModuloNotas" type="{http://webservice.basico.comuns.negocio/}empresaWS" maxOccurs="unbounded" minOccurs="0"/>
 *         &lt;element name="expirado" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         &lt;element name="foraDoHorario" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         &lt;element name="mensagem" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="nome" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="nomeEmpresa" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="perfilAcesso" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="permissaoAlterarRPS" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         &lt;element name="tipoPerfilAcesso" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="typeMidiasService" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="urlFoto" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="urlFotosNuvem" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="urlTreino" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="userName" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="usuarioMovelTreino" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="usuarioTreino" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         &lt;element name="validarUserOamd" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *       &lt;/sequence>
 *     &lt;/extension>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "usuarioTO", propOrder = {
    "acessoTeste",
    "administrador",
    "appGestor",
    "codigo",
    "codigoPessoa",
    "colaboradorId",
    "credecialTreino",
    "diaExpiracaoAcesso",
    "empresaDefault",
    "empresas",
    "empresasPermissaoModuloNotas",
    "expirado",
    "foraDoHorario",
    "mensagem",
    "nome",
    "nomeEmpresa",
    "perfilAcesso",
    "permissaoAlterarRPS",
    "tipoPerfilAcesso",
    "typeMidiasService",
    "urlFoto",
    "urlFotosNuvem",
    "urlTreino",
    "userName",
    "usuarioMovelTreino",
    "usuarioTreino",
    "validarUserOamd"
})
public class UsuarioTO
    extends SuperTO
{

    protected boolean acessoTeste;
    protected boolean administrador;
    protected boolean appGestor;
    protected Integer codigo;
    protected Integer codigoPessoa;
    protected Integer colaboradorId;
    protected String credecialTreino;
    protected String diaExpiracaoAcesso;
    protected EmpresaWS empresaDefault;
    @XmlElement(nillable = true)
    protected List<EmpresaWS> empresas;
    @XmlElement(nillable = true)
    protected List<EmpresaWS> empresasPermissaoModuloNotas;
    protected boolean expirado;
    protected boolean foraDoHorario;
    protected String mensagem;
    protected String nome;
    protected String nomeEmpresa;
    protected String perfilAcesso;
    protected boolean permissaoAlterarRPS;
    protected String tipoPerfilAcesso;
    protected String typeMidiasService;
    protected String urlFoto;
    protected String urlFotosNuvem;
    protected String urlTreino;
    protected String userName;
    protected String usuarioMovelTreino;
    protected boolean usuarioTreino;
    protected boolean validarUserOamd;

    /**
     * Obtém o valor da propriedade acessoTeste.
     * 
     */
    public boolean isAcessoTeste() {
        return acessoTeste;
    }

    /**
     * Define o valor da propriedade acessoTeste.
     * 
     */
    public void setAcessoTeste(boolean value) {
        this.acessoTeste = value;
    }

    /**
     * Obtém o valor da propriedade administrador.
     * 
     */
    public boolean isAdministrador() {
        return administrador;
    }

    /**
     * Define o valor da propriedade administrador.
     * 
     */
    public void setAdministrador(boolean value) {
        this.administrador = value;
    }

    /**
     * Obtém o valor da propriedade appGestor.
     * 
     */
    public boolean isAppGestor() {
        return appGestor;
    }

    /**
     * Define o valor da propriedade appGestor.
     * 
     */
    public void setAppGestor(boolean value) {
        this.appGestor = value;
    }

    /**
     * Obtém o valor da propriedade codigo.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getCodigo() {
        return codigo;
    }

    /**
     * Define o valor da propriedade codigo.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setCodigo(Integer value) {
        this.codigo = value;
    }

    /**
     * Obtém o valor da propriedade codigoPessoa.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getCodigoPessoa() {
        return codigoPessoa;
    }

    /**
     * Define o valor da propriedade codigoPessoa.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setCodigoPessoa(Integer value) {
        this.codigoPessoa = value;
    }

    /**
     * Obtém o valor da propriedade colaboradorId.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getColaboradorId() {
        return colaboradorId;
    }

    /**
     * Define o valor da propriedade colaboradorId.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setColaboradorId(Integer value) {
        this.colaboradorId = value;
    }

    /**
     * Obtém o valor da propriedade credecialTreino.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCredecialTreino() {
        return credecialTreino;
    }

    /**
     * Define o valor da propriedade credecialTreino.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCredecialTreino(String value) {
        this.credecialTreino = value;
    }

    /**
     * Obtém o valor da propriedade diaExpiracaoAcesso.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDiaExpiracaoAcesso() {
        return diaExpiracaoAcesso;
    }

    /**
     * Define o valor da propriedade diaExpiracaoAcesso.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDiaExpiracaoAcesso(String value) {
        this.diaExpiracaoAcesso = value;
    }

    /**
     * Obtém o valor da propriedade empresaDefault.
     * 
     * @return
     *     possible object is
     *     {@link EmpresaWS }
     *     
     */
    public EmpresaWS getEmpresaDefault() {
        return empresaDefault;
    }

    /**
     * Define o valor da propriedade empresaDefault.
     * 
     * @param value
     *     allowed object is
     *     {@link EmpresaWS }
     *     
     */
    public void setEmpresaDefault(EmpresaWS value) {
        this.empresaDefault = value;
    }

    /**
     * Gets the value of the empresas property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the empresas property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getEmpresas().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link EmpresaWS }
     * 
     * 
     */
    public List<EmpresaWS> getEmpresas() {
        if (empresas == null) {
            empresas = new ArrayList<EmpresaWS>();
        }
        return this.empresas;
    }

    /**
     * Gets the value of the empresasPermissaoModuloNotas property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the empresasPermissaoModuloNotas property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getEmpresasPermissaoModuloNotas().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link EmpresaWS }
     * 
     * 
     */
    public List<EmpresaWS> getEmpresasPermissaoModuloNotas() {
        if (empresasPermissaoModuloNotas == null) {
            empresasPermissaoModuloNotas = new ArrayList<EmpresaWS>();
        }
        return this.empresasPermissaoModuloNotas;
    }

    /**
     * Obtém o valor da propriedade expirado.
     * 
     */
    public boolean isExpirado() {
        return expirado;
    }

    /**
     * Define o valor da propriedade expirado.
     * 
     */
    public void setExpirado(boolean value) {
        this.expirado = value;
    }

    /**
     * Obtém o valor da propriedade foraDoHorario.
     * 
     */
    public boolean isForaDoHorario() {
        return foraDoHorario;
    }

    /**
     * Define o valor da propriedade foraDoHorario.
     * 
     */
    public void setForaDoHorario(boolean value) {
        this.foraDoHorario = value;
    }

    /**
     * Obtém o valor da propriedade mensagem.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMensagem() {
        return mensagem;
    }

    /**
     * Define o valor da propriedade mensagem.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMensagem(String value) {
        this.mensagem = value;
    }

    /**
     * Obtém o valor da propriedade nome.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNome() {
        return nome;
    }

    /**
     * Define o valor da propriedade nome.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNome(String value) {
        this.nome = value;
    }

    /**
     * Obtém o valor da propriedade nomeEmpresa.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNomeEmpresa() {
        return nomeEmpresa;
    }

    /**
     * Define o valor da propriedade nomeEmpresa.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNomeEmpresa(String value) {
        this.nomeEmpresa = value;
    }

    /**
     * Obtém o valor da propriedade perfilAcesso.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPerfilAcesso() {
        return perfilAcesso;
    }

    /**
     * Define o valor da propriedade perfilAcesso.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPerfilAcesso(String value) {
        this.perfilAcesso = value;
    }

    /**
     * Obtém o valor da propriedade permissaoAlterarRPS.
     * 
     */
    public boolean isPermissaoAlterarRPS() {
        return permissaoAlterarRPS;
    }

    /**
     * Define o valor da propriedade permissaoAlterarRPS.
     * 
     */
    public void setPermissaoAlterarRPS(boolean value) {
        this.permissaoAlterarRPS = value;
    }

    /**
     * Obtém o valor da propriedade tipoPerfilAcesso.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTipoPerfilAcesso() {
        return tipoPerfilAcesso;
    }

    /**
     * Define o valor da propriedade tipoPerfilAcesso.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTipoPerfilAcesso(String value) {
        this.tipoPerfilAcesso = value;
    }

    /**
     * Obtém o valor da propriedade typeMidiasService.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTypeMidiasService() {
        return typeMidiasService;
    }

    /**
     * Define o valor da propriedade typeMidiasService.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTypeMidiasService(String value) {
        this.typeMidiasService = value;
    }

    /**
     * Obtém o valor da propriedade urlFoto.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUrlFoto() {
        return urlFoto;
    }

    /**
     * Define o valor da propriedade urlFoto.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUrlFoto(String value) {
        this.urlFoto = value;
    }

    /**
     * Obtém o valor da propriedade urlFotosNuvem.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUrlFotosNuvem() {
        return urlFotosNuvem;
    }

    /**
     * Define o valor da propriedade urlFotosNuvem.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUrlFotosNuvem(String value) {
        this.urlFotosNuvem = value;
    }

    /**
     * Obtém o valor da propriedade urlTreino.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUrlTreino() {
        return urlTreino;
    }

    /**
     * Define o valor da propriedade urlTreino.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUrlTreino(String value) {
        this.urlTreino = value;
    }

    /**
     * Obtém o valor da propriedade userName.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUserName() {
        return userName;
    }

    /**
     * Define o valor da propriedade userName.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUserName(String value) {
        this.userName = value;
    }

    /**
     * Obtém o valor da propriedade usuarioMovelTreino.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUsuarioMovelTreino() {
        return usuarioMovelTreino;
    }

    /**
     * Define o valor da propriedade usuarioMovelTreino.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUsuarioMovelTreino(String value) {
        this.usuarioMovelTreino = value;
    }

    /**
     * Obtém o valor da propriedade usuarioTreino.
     * 
     */
    public boolean isUsuarioTreino() {
        return usuarioTreino;
    }

    /**
     * Define o valor da propriedade usuarioTreino.
     * 
     */
    public void setUsuarioTreino(boolean value) {
        this.usuarioTreino = value;
    }

    /**
     * Obtém o valor da propriedade validarUserOamd.
     * 
     */
    public boolean isValidarUserOamd() {
        return validarUserOamd;
    }

    /**
     * Define o valor da propriedade validarUserOamd.
     * 
     */
    public void setValidarUserOamd(boolean value) {
        this.validarUserOamd = value;
    }

}
