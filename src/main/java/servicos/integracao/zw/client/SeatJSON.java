
package servicos.integracao.zw.client;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java de seatJSON complex type.
 * 
 * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
 * 
 * <pre>
 * &lt;complexType name="seatJSON">
 *   &lt;complexContent>
 *     &lt;extension base="{http://webservice.basico.comuns.negocio/}superJSON">
 *       &lt;sequence>
 *         &lt;element name="available" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         &lt;element name="clientID" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="clientName" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="matriculaAluno" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="seatID" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="teacher" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         &lt;element name="teamNumber" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="userName" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/extension>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "seatJSON", propOrder = {
    "available",
    "clientID",
    "clientName",
    "matriculaAluno",
    "seatID",
    "teacher",
    "teamNumber",
    "userName"
})
public class SeatJSON
    extends SuperJSON
{

    protected boolean available;
    protected Integer clientID;
    protected String clientName;
    protected String matriculaAluno;
    protected Integer seatID;
    protected boolean teacher;
    protected Integer teamNumber;
    protected String userName;

    /**
     * Obtém o valor da propriedade available.
     * 
     */
    public boolean isAvailable() {
        return available;
    }

    /**
     * Define o valor da propriedade available.
     * 
     */
    public void setAvailable(boolean value) {
        this.available = value;
    }

    /**
     * Obtém o valor da propriedade clientID.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getClientID() {
        return clientID;
    }

    /**
     * Define o valor da propriedade clientID.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setClientID(Integer value) {
        this.clientID = value;
    }

    /**
     * Obtém o valor da propriedade clientName.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getClientName() {
        return clientName;
    }

    /**
     * Define o valor da propriedade clientName.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setClientName(String value) {
        this.clientName = value;
    }

    /**
     * Obtém o valor da propriedade matriculaAluno.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMatriculaAluno() {
        return matriculaAluno;
    }

    /**
     * Define o valor da propriedade matriculaAluno.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMatriculaAluno(String value) {
        this.matriculaAluno = value;
    }

    /**
     * Obtém o valor da propriedade seatID.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getSeatID() {
        return seatID;
    }

    /**
     * Define o valor da propriedade seatID.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setSeatID(Integer value) {
        this.seatID = value;
    }

    /**
     * Obtém o valor da propriedade teacher.
     * 
     */
    public boolean isTeacher() {
        return teacher;
    }

    /**
     * Define o valor da propriedade teacher.
     * 
     */
    public void setTeacher(boolean value) {
        this.teacher = value;
    }

    /**
     * Obtém o valor da propriedade teamNumber.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getTeamNumber() {
        return teamNumber;
    }

    /**
     * Define o valor da propriedade teamNumber.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setTeamNumber(Integer value) {
        this.teamNumber = value;
    }

    /**
     * Obtém o valor da propriedade userName.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUserName() {
        return userName;
    }

    /**
     * Define o valor da propriedade userName.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUserName(String value) {
        this.userName = value;
    }

}
