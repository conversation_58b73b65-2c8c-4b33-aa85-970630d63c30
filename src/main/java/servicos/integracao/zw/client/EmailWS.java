
package servicos.integracao.zw.client;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java de emailWS complex type.
 * 
 * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
 * 
 * <pre>
 * &lt;complexType name="emailWS">
 *   &lt;complexContent>
 *     &lt;extension base="{http://webservice.basico.comuns.negocio/}superTO">
 *       &lt;sequence>
 *         &lt;element name="bloqueadoBounce" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/>
 *         &lt;element name="codigo" type="{http://www.w3.org/2001/XMLSchema}int"/>
 *         &lt;element name="email" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="emailCorrespondencia" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/extension>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "emailWS", propOrder = {
    "bloqueadoBounce",
    "codigo",
    "email",
    "emailCorrespondencia"
})
public class EmailWS
    extends SuperTO
{

    protected Boolean bloqueadoBounce;
    protected int codigo;
    protected String email;
    protected Boolean emailCorrespondencia;

    /**
     * Obtém o valor da propriedade bloqueadoBounce.
     * 
     * @return
     *     possible object is
     *     {@link Boolean }
     *     
     */
    public Boolean isBloqueadoBounce() {
        return bloqueadoBounce;
    }

    /**
     * Define o valor da propriedade bloqueadoBounce.
     * 
     * @param value
     *     allowed object is
     *     {@link Boolean }
     *     
     */
    public void setBloqueadoBounce(Boolean value) {
        this.bloqueadoBounce = value;
    }

    /**
     * Obtém o valor da propriedade codigo.
     * 
     */
    public int getCodigo() {
        return codigo;
    }

    /**
     * Define o valor da propriedade codigo.
     * 
     */
    public void setCodigo(int value) {
        this.codigo = value;
    }

    /**
     * Obtém o valor da propriedade email.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getEmail() {
        return email;
    }

    /**
     * Define o valor da propriedade email.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setEmail(String value) {
        this.email = value;
    }

    /**
     * Obtém o valor da propriedade emailCorrespondencia.
     * 
     * @return
     *     possible object is
     *     {@link Boolean }
     *     
     */
    public Boolean isEmailCorrespondencia() {
        return emailCorrespondencia;
    }

    /**
     * Define o valor da propriedade emailCorrespondencia.
     * 
     * @param value
     *     allowed object is
     *     {@link Boolean }
     *     
     */
    public void setEmailCorrespondencia(Boolean value) {
        this.emailCorrespondencia = value;
    }

}
