
package servicos.integracao.zw.client;

import javax.xml.ws.WebFault;


/**
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.2.6b21 
 * Generated source version: 2.1
 * 
 */
@WebFault(name = "ConsistirException", targetNamespace = "http://webservice.basico.comuns.negocio/")
public class ConsistirException_Exception
    extends java.lang.Exception
{

    /**
     * Java type that goes as soapenv:Fault detail element.
     * 
     */
    private ConsistirException faultInfo;

    /**
     * 
     * @param faultInfo
     * @param message
     */
    public ConsistirException_Exception(String message, ConsistirException faultInfo) {
        super(message);
        this.faultInfo = faultInfo;
    }

    /**
     * 
     * @param faultInfo
     * @param cause
     * @param message
     */
    public ConsistirException_Exception(String message, ConsistirException faultInfo, Throwable cause) {
        super(message, cause);
        this.faultInfo = faultInfo;
    }

    /**
     * 
     * @return
     *     returns fault bean: servicos.integracao.zw.client.ConsistirException
     */
    public ConsistirException getFaultInfo() {
        return faultInfo;
    }

}
