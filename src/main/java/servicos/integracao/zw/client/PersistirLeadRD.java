
package servicos.integracao.zw.client;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java de persistirLeadRD complex type.
 * 
 * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
 * 
 * <pre>
 * &lt;complexType name="persistirLeadRD">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="lead" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="empresa" type="{http://www.w3.org/2001/XMLSchema}int"/>
 *         &lt;element name="key" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "persistirLeadRD", propOrder = {
    "lead",
    "empresa",
    "key"
})
public class PersistirLeadRD {

    protected String lead;
    protected int empresa;
    protected String key;

    /**
     * Obtém o valor da propriedade lead.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLead() {
        return lead;
    }

    /**
     * Define o valor da propriedade lead.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLead(String value) {
        this.lead = value;
    }

    /**
     * Obtém o valor da propriedade empresa.
     * 
     */
    public int getEmpresa() {
        return empresa;
    }

    /**
     * Define o valor da propriedade empresa.
     * 
     */
    public void setEmpresa(int value) {
        this.empresa = value;
    }

    /**
     * Obtém o valor da propriedade key.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getKey() {
        return key;
    }

    /**
     * Define o valor da propriedade key.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setKey(String value) {
        this.key = value;
    }

}
