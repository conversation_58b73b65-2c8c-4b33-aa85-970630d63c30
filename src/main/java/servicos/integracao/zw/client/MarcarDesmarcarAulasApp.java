
package servicos.integracao.zw.client;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java de marcarDesmarcarAulasApp complex type.
 * 
 * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
 * 
 * <pre>
 * &lt;complexType name="marcarDesmarcarAulasApp">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="key" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="matricula" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="horarioturma" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="data" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="marcar" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="bloquearParcelaVencida" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "marcarDesmarcarAulasApp", propOrder = {
    "key",
    "matricula",
    "horarioturma",
    "data",
    "marcar",
    "bloquearParcelaVencida"
})
public class MarcarDesmarcarAulasApp {

    protected String key;
    protected Integer matricula;
    protected Integer horarioturma;
    protected String data;
    protected String marcar;
    protected String bloquearParcelaVencida;

    /**
     * Obtém o valor da propriedade key.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getKey() {
        return key;
    }

    /**
     * Define o valor da propriedade key.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setKey(String value) {
        this.key = value;
    }

    /**
     * Obtém o valor da propriedade matricula.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getMatricula() {
        return matricula;
    }

    /**
     * Define o valor da propriedade matricula.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setMatricula(Integer value) {
        this.matricula = value;
    }

    /**
     * Obtém o valor da propriedade horarioturma.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getHorarioturma() {
        return horarioturma;
    }

    /**
     * Define o valor da propriedade horarioturma.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setHorarioturma(Integer value) {
        this.horarioturma = value;
    }

    /**
     * Obtém o valor da propriedade data.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getData() {
        return data;
    }

    /**
     * Define o valor da propriedade data.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setData(String value) {
        this.data = value;
    }

    /**
     * Obtém o valor da propriedade marcar.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMarcar() {
        return marcar;
    }

    /**
     * Define o valor da propriedade marcar.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMarcar(String value) {
        this.marcar = value;
    }

    /**
     * Obtém o valor da propriedade bloquearParcelaVencida.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getBloquearParcelaVencida() {
        return bloquearParcelaVencida;
    }

    /**
     * Define o valor da propriedade bloquearParcelaVencida.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setBloquearParcelaVencida(String value) {
        this.bloquearParcelaVencida = value;
    }

}
