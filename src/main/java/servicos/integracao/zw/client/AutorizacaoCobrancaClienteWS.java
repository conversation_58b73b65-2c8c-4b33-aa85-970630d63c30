
package servicos.integracao.zw.client;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java de autorizacaoCobrancaClienteWS complex type.
 * 
 * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
 * 
 * <pre>
 * &lt;complexType name="autorizacaoCobrancaClienteWS">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="agencia" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="agenciaDV" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="anoValidade" type="{http://www.w3.org/2001/XMLSchema}int"/>
 *         &lt;element name="banco" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="cliente" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="codigo" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="codigoBanco" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="codigoOperacao" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="contaCorrente" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="contaCorrenteDV" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="convenio" type="{http://www.w3.org/2001/XMLSchema}int"/>
 *         &lt;element name="cpfTitular" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="cvv" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="listaObjetosACobrar" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="mesValidade" type="{http://www.w3.org/2001/XMLSchema}int"/>
 *         &lt;element name="nomeTitularCartao" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="numeroCartao" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="operadoraCartao" type="{http://www.w3.org/2001/XMLSchema}int"/>
 *         &lt;element name="operadoraCartaoDescricao" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="tipoAutorizacaoCobranca" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="transacaoOnline" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/>
 *         &lt;element name="validadeCartao" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "autorizacaoCobrancaClienteWS", propOrder = {
    "agencia",
    "agenciaDV",
    "anoValidade",
    "banco",
    "cliente",
    "codigo",
    "codigoBanco",
    "codigoOperacao",
    "contaCorrente",
    "contaCorrenteDV",
    "convenio",
    "cpfTitular",
    "cvv",
    "listaObjetosACobrar",
    "mesValidade",
    "nomeTitularCartao",
    "numeroCartao",
    "operadoraCartao",
    "operadoraCartaoDescricao",
    "tipoAutorizacaoCobranca",
    "transacaoOnline",
    "validadeCartao"
})
public class AutorizacaoCobrancaClienteWS {

    protected Integer agencia;
    protected String agenciaDV;
    protected int anoValidade;
    protected String banco;
    protected Integer cliente;
    protected Integer codigo;
    protected String codigoBanco;
    protected String codigoOperacao;
    protected Integer contaCorrente;
    protected String contaCorrenteDV;
    protected int convenio;
    protected String cpfTitular;
    protected String cvv;
    protected String listaObjetosACobrar;
    protected int mesValidade;
    protected String nomeTitularCartao;
    protected String numeroCartao;
    protected int operadoraCartao;
    protected String operadoraCartaoDescricao;
    protected String tipoAutorizacaoCobranca;
    protected Boolean transacaoOnline;
    protected String validadeCartao;

    /**
     * Obtém o valor da propriedade agencia.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getAgencia() {
        return agencia;
    }

    /**
     * Define o valor da propriedade agencia.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setAgencia(Integer value) {
        this.agencia = value;
    }

    /**
     * Obtém o valor da propriedade agenciaDV.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAgenciaDV() {
        return agenciaDV;
    }

    /**
     * Define o valor da propriedade agenciaDV.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAgenciaDV(String value) {
        this.agenciaDV = value;
    }

    /**
     * Obtém o valor da propriedade anoValidade.
     * 
     */
    public int getAnoValidade() {
        return anoValidade;
    }

    /**
     * Define o valor da propriedade anoValidade.
     * 
     */
    public void setAnoValidade(int value) {
        this.anoValidade = value;
    }

    /**
     * Obtém o valor da propriedade banco.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getBanco() {
        return banco;
    }

    /**
     * Define o valor da propriedade banco.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setBanco(String value) {
        this.banco = value;
    }

    /**
     * Obtém o valor da propriedade cliente.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getCliente() {
        return cliente;
    }

    /**
     * Define o valor da propriedade cliente.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setCliente(Integer value) {
        this.cliente = value;
    }

    /**
     * Obtém o valor da propriedade codigo.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getCodigo() {
        return codigo;
    }

    /**
     * Define o valor da propriedade codigo.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setCodigo(Integer value) {
        this.codigo = value;
    }

    /**
     * Obtém o valor da propriedade codigoBanco.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCodigoBanco() {
        return codigoBanco;
    }

    /**
     * Define o valor da propriedade codigoBanco.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCodigoBanco(String value) {
        this.codigoBanco = value;
    }

    /**
     * Obtém o valor da propriedade codigoOperacao.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCodigoOperacao() {
        return codigoOperacao;
    }

    /**
     * Define o valor da propriedade codigoOperacao.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCodigoOperacao(String value) {
        this.codigoOperacao = value;
    }

    /**
     * Obtém o valor da propriedade contaCorrente.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getContaCorrente() {
        return contaCorrente;
    }

    /**
     * Define o valor da propriedade contaCorrente.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setContaCorrente(Integer value) {
        this.contaCorrente = value;
    }

    /**
     * Obtém o valor da propriedade contaCorrenteDV.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getContaCorrenteDV() {
        return contaCorrenteDV;
    }

    /**
     * Define o valor da propriedade contaCorrenteDV.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setContaCorrenteDV(String value) {
        this.contaCorrenteDV = value;
    }

    /**
     * Obtém o valor da propriedade convenio.
     * 
     */
    public int getConvenio() {
        return convenio;
    }

    /**
     * Define o valor da propriedade convenio.
     * 
     */
    public void setConvenio(int value) {
        this.convenio = value;
    }

    /**
     * Obtém o valor da propriedade cpfTitular.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCpfTitular() {
        return cpfTitular;
    }

    /**
     * Define o valor da propriedade cpfTitular.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCpfTitular(String value) {
        this.cpfTitular = value;
    }

    /**
     * Obtém o valor da propriedade cvv.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCvv() {
        return cvv;
    }

    /**
     * Define o valor da propriedade cvv.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCvv(String value) {
        this.cvv = value;
    }

    /**
     * Obtém o valor da propriedade listaObjetosACobrar.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getListaObjetosACobrar() {
        return listaObjetosACobrar;
    }

    /**
     * Define o valor da propriedade listaObjetosACobrar.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setListaObjetosACobrar(String value) {
        this.listaObjetosACobrar = value;
    }

    /**
     * Obtém o valor da propriedade mesValidade.
     * 
     */
    public int getMesValidade() {
        return mesValidade;
    }

    /**
     * Define o valor da propriedade mesValidade.
     * 
     */
    public void setMesValidade(int value) {
        this.mesValidade = value;
    }

    /**
     * Obtém o valor da propriedade nomeTitularCartao.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNomeTitularCartao() {
        return nomeTitularCartao;
    }

    /**
     * Define o valor da propriedade nomeTitularCartao.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNomeTitularCartao(String value) {
        this.nomeTitularCartao = value;
    }

    /**
     * Obtém o valor da propriedade numeroCartao.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNumeroCartao() {
        return numeroCartao;
    }

    /**
     * Define o valor da propriedade numeroCartao.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNumeroCartao(String value) {
        this.numeroCartao = value;
    }

    /**
     * Obtém o valor da propriedade operadoraCartao.
     * 
     */
    public int getOperadoraCartao() {
        return operadoraCartao;
    }

    /**
     * Define o valor da propriedade operadoraCartao.
     * 
     */
    public void setOperadoraCartao(int value) {
        this.operadoraCartao = value;
    }

    /**
     * Obtém o valor da propriedade operadoraCartaoDescricao.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getOperadoraCartaoDescricao() {
        return operadoraCartaoDescricao;
    }

    /**
     * Define o valor da propriedade operadoraCartaoDescricao.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setOperadoraCartaoDescricao(String value) {
        this.operadoraCartaoDescricao = value;
    }

    /**
     * Obtém o valor da propriedade tipoAutorizacaoCobranca.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTipoAutorizacaoCobranca() {
        return tipoAutorizacaoCobranca;
    }

    /**
     * Define o valor da propriedade tipoAutorizacaoCobranca.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTipoAutorizacaoCobranca(String value) {
        this.tipoAutorizacaoCobranca = value;
    }

    /**
     * Obtém o valor da propriedade transacaoOnline.
     * 
     * @return
     *     possible object is
     *     {@link Boolean }
     *     
     */
    public Boolean isTransacaoOnline() {
        return transacaoOnline;
    }

    /**
     * Define o valor da propriedade transacaoOnline.
     * 
     * @param value
     *     allowed object is
     *     {@link Boolean }
     *     
     */
    public void setTransacaoOnline(Boolean value) {
        this.transacaoOnline = value;
    }

    /**
     * Obtém o valor da propriedade validadeCartao.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getValidadeCartao() {
        return validadeCartao;
    }

    /**
     * Define o valor da propriedade validadeCartao.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setValidadeCartao(String value) {
        this.validadeCartao = value;
    }

}
