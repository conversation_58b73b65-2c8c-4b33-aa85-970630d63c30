
package servicos.integracao.zw.client;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java de consultarDemandasSintetico complex type.
 * 
 * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
 * 
 * <pre>
 * &lt;complexType name="consultarDemandasSintetico">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="key" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="filtroProfessores" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="filtroModalidades" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="filtroAmbientes" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="filtroTurmas" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="filtroHorarios" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="filtroDiasDaSemana" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="dataInicio" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="dataFim" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "consultarDemandasSintetico", propOrder = {
    "key",
    "filtroProfessores",
    "filtroModalidades",
    "filtroAmbientes",
    "filtroTurmas",
    "filtroHorarios",
    "filtroDiasDaSemana",
    "dataInicio",
    "dataFim"
})
public class ConsultarDemandasSintetico {

    protected String key;
    protected String filtroProfessores;
    protected String filtroModalidades;
    protected String filtroAmbientes;
    protected String filtroTurmas;
    protected String filtroHorarios;
    protected String filtroDiasDaSemana;
    protected String dataInicio;
    protected String dataFim;

    /**
     * Obtém o valor da propriedade key.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getKey() {
        return key;
    }

    /**
     * Define o valor da propriedade key.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setKey(String value) {
        this.key = value;
    }

    /**
     * Obtém o valor da propriedade filtroProfessores.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFiltroProfessores() {
        return filtroProfessores;
    }

    /**
     * Define o valor da propriedade filtroProfessores.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFiltroProfessores(String value) {
        this.filtroProfessores = value;
    }

    /**
     * Obtém o valor da propriedade filtroModalidades.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFiltroModalidades() {
        return filtroModalidades;
    }

    /**
     * Define o valor da propriedade filtroModalidades.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFiltroModalidades(String value) {
        this.filtroModalidades = value;
    }

    /**
     * Obtém o valor da propriedade filtroAmbientes.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFiltroAmbientes() {
        return filtroAmbientes;
    }

    /**
     * Define o valor da propriedade filtroAmbientes.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFiltroAmbientes(String value) {
        this.filtroAmbientes = value;
    }

    /**
     * Obtém o valor da propriedade filtroTurmas.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFiltroTurmas() {
        return filtroTurmas;
    }

    /**
     * Define o valor da propriedade filtroTurmas.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFiltroTurmas(String value) {
        this.filtroTurmas = value;
    }

    /**
     * Obtém o valor da propriedade filtroHorarios.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFiltroHorarios() {
        return filtroHorarios;
    }

    /**
     * Define o valor da propriedade filtroHorarios.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFiltroHorarios(String value) {
        this.filtroHorarios = value;
    }

    /**
     * Obtém o valor da propriedade filtroDiasDaSemana.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFiltroDiasDaSemana() {
        return filtroDiasDaSemana;
    }

    /**
     * Define o valor da propriedade filtroDiasDaSemana.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFiltroDiasDaSemana(String value) {
        this.filtroDiasDaSemana = value;
    }

    /**
     * Obtém o valor da propriedade dataInicio.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDataInicio() {
        return dataInicio;
    }

    /**
     * Define o valor da propriedade dataInicio.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDataInicio(String value) {
        this.dataInicio = value;
    }

    /**
     * Obtém o valor da propriedade dataFim.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDataFim() {
        return dataFim;
    }

    /**
     * Define o valor da propriedade dataFim.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDataFim(String value) {
        this.dataFim = value;
    }

}
