
package servicos.integracao.zw.client;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java de atualizarCadastroPersonal complex type.
 * 
 * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
 * 
 * <pre>
 * &lt;complexType name="atualizarCadastroPersonal">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="codigoColaborador" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="atualizarSaldo" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/>
 *         &lt;element name="saldo" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="emAtendimento" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/>
 *         &lt;element name="key" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "atualizarCadastroPersonal", propOrder = {
    "codigoColaborador",
    "atualizarSaldo",
    "saldo",
    "emAtendimento",
    "key"
})
public class AtualizarCadastroPersonal {

    protected Integer codigoColaborador;
    protected Boolean atualizarSaldo;
    protected Integer saldo;
    protected Boolean emAtendimento;
    protected String key;

    /**
     * Obtém o valor da propriedade codigoColaborador.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getCodigoColaborador() {
        return codigoColaborador;
    }

    /**
     * Define o valor da propriedade codigoColaborador.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setCodigoColaborador(Integer value) {
        this.codigoColaborador = value;
    }

    /**
     * Obtém o valor da propriedade atualizarSaldo.
     * 
     * @return
     *     possible object is
     *     {@link Boolean }
     *     
     */
    public Boolean isAtualizarSaldo() {
        return atualizarSaldo;
    }

    /**
     * Define o valor da propriedade atualizarSaldo.
     * 
     * @param value
     *     allowed object is
     *     {@link Boolean }
     *     
     */
    public void setAtualizarSaldo(Boolean value) {
        this.atualizarSaldo = value;
    }

    /**
     * Obtém o valor da propriedade saldo.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getSaldo() {
        return saldo;
    }

    /**
     * Define o valor da propriedade saldo.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setSaldo(Integer value) {
        this.saldo = value;
    }

    /**
     * Obtém o valor da propriedade emAtendimento.
     * 
     * @return
     *     possible object is
     *     {@link Boolean }
     *     
     */
    public Boolean isEmAtendimento() {
        return emAtendimento;
    }

    /**
     * Define o valor da propriedade emAtendimento.
     * 
     * @param value
     *     allowed object is
     *     {@link Boolean }
     *     
     */
    public void setEmAtendimento(Boolean value) {
        this.emAtendimento = value;
    }

    /**
     * Obtém o valor da propriedade key.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getKey() {
        return key;
    }

    /**
     * Define o valor da propriedade key.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setKey(String value) {
        this.key = value;
    }

}
