
package servicos.integracao.zw.client;

import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java de clienteWS complex type.
 * 
 * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
 * 
 * <pre>
 * &lt;complexType name="clienteWS">
 *   &lt;complexContent>
 *     &lt;extension base="{http://webservice.basico.comuns.negocio/}superTO">
 *       &lt;sequence>
 *         &lt;element name="bairro" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="CPF" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="cep" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="chaveZW" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="codigo" type="{http://www.w3.org/2001/XMLSchema}int"/>
 *         &lt;element name="codigoCidade" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="codigoEstado" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="codigoPessoa" type="{http://www.w3.org/2001/XMLSchema}int"/>
 *         &lt;element name="complemento" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="consultor" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="dataCadastro" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="dataNascimento" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="email" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="empresa" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="endereco" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="idEmpresaFinanceiroRede" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="listaAutorizacaoCobranca" type="{http://webservice.basico.comuns.negocio/}autorizacaoCobrancaClienteWS" maxOccurs="unbounded" minOccurs="0"/>
 *         &lt;element name="listaEmails" type="{http://webservice.basico.comuns.negocio/}emailWS" maxOccurs="unbounded" minOccurs="0"/>
 *         &lt;element name="listaEnderecos" type="{http://webservice.basico.comuns.negocio/}enderecoWS" maxOccurs="unbounded" minOccurs="0"/>
 *         &lt;element name="listaTelefones" type="{http://webservice.basico.comuns.negocio/}telefoneWS" maxOccurs="unbounded" minOccurs="0"/>
 *         &lt;element name="matricula" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="nome" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="nomeEmpresa" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="nomeUsuarioMovel" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="numero" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="permiteContratosConcomitante" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/>
 *         &lt;element name="pontuacao" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="sexo" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="situacao" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="telCelular" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="telResidencial" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/extension>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "clienteWS", propOrder = {
    "bairro",
    "cpf",
    "cep",
    "chaveZW",
    "codigo",
    "codigoCidade",
    "codigoEstado",
    "codigoPessoa",
    "complemento",
    "consultor",
    "dataCadastro",
    "dataNascimento",
    "email",
    "empresa",
    "endereco",
    "idEmpresaFinanceiroRede",
    "listaAutorizacaoCobranca",
    "listaEmails",
    "listaEnderecos",
    "listaTelefones",
    "matricula",
    "nome",
    "nomeEmpresa",
    "nomeUsuarioMovel",
    "numero",
    "permiteContratosConcomitante",
    "pontuacao",
    "sexo",
    "situacao",
    "telCelular",
    "telResidencial"
})
public class ClienteWS
    extends SuperTO
{

    protected String bairro;
    @XmlElement(name = "CPF")
    protected String cpf;
    protected String cep;
    protected String chaveZW;
    protected int codigo;
    protected Integer codigoCidade;
    protected Integer codigoEstado;
    protected int codigoPessoa;
    protected String complemento;
    protected String consultor;
    protected String dataCadastro;
    protected String dataNascimento;
    protected String email;
    protected Integer empresa;
    protected String endereco;
    protected Integer idEmpresaFinanceiroRede;
    @XmlElement(nillable = true)
    protected List<AutorizacaoCobrancaClienteWS> listaAutorizacaoCobranca;
    @XmlElement(nillable = true)
    protected List<EmailWS> listaEmails;
    @XmlElement(nillable = true)
    protected List<EnderecoWS> listaEnderecos;
    @XmlElement(nillable = true)
    protected List<TelefoneWS> listaTelefones;
    protected String matricula;
    protected String nome;
    protected String nomeEmpresa;
    protected String nomeUsuarioMovel;
    protected String numero;
    protected Boolean permiteContratosConcomitante;
    protected Integer pontuacao;
    protected String sexo;
    protected String situacao;
    protected String telCelular;
    protected String telResidencial;

    /**
     * Obtém o valor da propriedade bairro.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getBairro() {
        return bairro;
    }

    /**
     * Define o valor da propriedade bairro.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setBairro(String value) {
        this.bairro = value;
    }

    /**
     * Obtém o valor da propriedade cpf.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCPF() {
        return cpf;
    }

    /**
     * Define o valor da propriedade cpf.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCPF(String value) {
        this.cpf = value;
    }

    /**
     * Obtém o valor da propriedade cep.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCep() {
        return cep;
    }

    /**
     * Define o valor da propriedade cep.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCep(String value) {
        this.cep = value;
    }

    /**
     * Obtém o valor da propriedade chaveZW.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getChaveZW() {
        return chaveZW;
    }

    /**
     * Define o valor da propriedade chaveZW.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setChaveZW(String value) {
        this.chaveZW = value;
    }

    /**
     * Obtém o valor da propriedade codigo.
     * 
     */
    public int getCodigo() {
        return codigo;
    }

    /**
     * Define o valor da propriedade codigo.
     * 
     */
    public void setCodigo(int value) {
        this.codigo = value;
    }

    /**
     * Obtém o valor da propriedade codigoCidade.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getCodigoCidade() {
        return codigoCidade;
    }

    /**
     * Define o valor da propriedade codigoCidade.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setCodigoCidade(Integer value) {
        this.codigoCidade = value;
    }

    /**
     * Obtém o valor da propriedade codigoEstado.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getCodigoEstado() {
        return codigoEstado;
    }

    /**
     * Define o valor da propriedade codigoEstado.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setCodigoEstado(Integer value) {
        this.codigoEstado = value;
    }

    /**
     * Obtém o valor da propriedade codigoPessoa.
     * 
     */
    public int getCodigoPessoa() {
        return codigoPessoa;
    }

    /**
     * Define o valor da propriedade codigoPessoa.
     * 
     */
    public void setCodigoPessoa(int value) {
        this.codigoPessoa = value;
    }

    /**
     * Obtém o valor da propriedade complemento.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getComplemento() {
        return complemento;
    }

    /**
     * Define o valor da propriedade complemento.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setComplemento(String value) {
        this.complemento = value;
    }

    /**
     * Obtém o valor da propriedade consultor.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getConsultor() {
        return consultor;
    }

    /**
     * Define o valor da propriedade consultor.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setConsultor(String value) {
        this.consultor = value;
    }

    /**
     * Obtém o valor da propriedade dataCadastro.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDataCadastro() {
        return dataCadastro;
    }

    /**
     * Define o valor da propriedade dataCadastro.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDataCadastro(String value) {
        this.dataCadastro = value;
    }

    /**
     * Obtém o valor da propriedade dataNascimento.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDataNascimento() {
        return dataNascimento;
    }

    /**
     * Define o valor da propriedade dataNascimento.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDataNascimento(String value) {
        this.dataNascimento = value;
    }

    /**
     * Obtém o valor da propriedade email.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getEmail() {
        return email;
    }

    /**
     * Define o valor da propriedade email.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setEmail(String value) {
        this.email = value;
    }

    /**
     * Obtém o valor da propriedade empresa.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getEmpresa() {
        return empresa;
    }

    /**
     * Define o valor da propriedade empresa.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setEmpresa(Integer value) {
        this.empresa = value;
    }

    /**
     * Obtém o valor da propriedade endereco.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getEndereco() {
        return endereco;
    }

    /**
     * Define o valor da propriedade endereco.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setEndereco(String value) {
        this.endereco = value;
    }

    /**
     * Obtém o valor da propriedade idEmpresaFinanceiroRede.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getIdEmpresaFinanceiroRede() {
        return idEmpresaFinanceiroRede;
    }

    /**
     * Define o valor da propriedade idEmpresaFinanceiroRede.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setIdEmpresaFinanceiroRede(Integer value) {
        this.idEmpresaFinanceiroRede = value;
    }

    /**
     * Gets the value of the listaAutorizacaoCobranca property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the listaAutorizacaoCobranca property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getListaAutorizacaoCobranca().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link AutorizacaoCobrancaClienteWS }
     * 
     * 
     */
    public List<AutorizacaoCobrancaClienteWS> getListaAutorizacaoCobranca() {
        if (listaAutorizacaoCobranca == null) {
            listaAutorizacaoCobranca = new ArrayList<AutorizacaoCobrancaClienteWS>();
        }
        return this.listaAutorizacaoCobranca;
    }

    /**
     * Gets the value of the listaEmails property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the listaEmails property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getListaEmails().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link EmailWS }
     * 
     * 
     */
    public List<EmailWS> getListaEmails() {
        if (listaEmails == null) {
            listaEmails = new ArrayList<EmailWS>();
        }
        return this.listaEmails;
    }

    /**
     * Gets the value of the listaEnderecos property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the listaEnderecos property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getListaEnderecos().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link EnderecoWS }
     * 
     * 
     */
    public List<EnderecoWS> getListaEnderecos() {
        if (listaEnderecos == null) {
            listaEnderecos = new ArrayList<EnderecoWS>();
        }
        return this.listaEnderecos;
    }

    /**
     * Gets the value of the listaTelefones property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the listaTelefones property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getListaTelefones().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link TelefoneWS }
     * 
     * 
     */
    public List<TelefoneWS> getListaTelefones() {
        if (listaTelefones == null) {
            listaTelefones = new ArrayList<TelefoneWS>();
        }
        return this.listaTelefones;
    }

    /**
     * Obtém o valor da propriedade matricula.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMatricula() {
        return matricula;
    }

    /**
     * Define o valor da propriedade matricula.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMatricula(String value) {
        this.matricula = value;
    }

    /**
     * Obtém o valor da propriedade nome.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNome() {
        return nome;
    }

    /**
     * Define o valor da propriedade nome.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNome(String value) {
        this.nome = value;
    }

    /**
     * Obtém o valor da propriedade nomeEmpresa.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNomeEmpresa() {
        return nomeEmpresa;
    }

    /**
     * Define o valor da propriedade nomeEmpresa.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNomeEmpresa(String value) {
        this.nomeEmpresa = value;
    }

    /**
     * Obtém o valor da propriedade nomeUsuarioMovel.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNomeUsuarioMovel() {
        return nomeUsuarioMovel;
    }

    /**
     * Define o valor da propriedade nomeUsuarioMovel.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNomeUsuarioMovel(String value) {
        this.nomeUsuarioMovel = value;
    }

    /**
     * Obtém o valor da propriedade numero.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNumero() {
        return numero;
    }

    /**
     * Define o valor da propriedade numero.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNumero(String value) {
        this.numero = value;
    }

    /**
     * Obtém o valor da propriedade permiteContratosConcomitante.
     * 
     * @return
     *     possible object is
     *     {@link Boolean }
     *     
     */
    public Boolean isPermiteContratosConcomitante() {
        return permiteContratosConcomitante;
    }

    /**
     * Define o valor da propriedade permiteContratosConcomitante.
     * 
     * @param value
     *     allowed object is
     *     {@link Boolean }
     *     
     */
    public void setPermiteContratosConcomitante(Boolean value) {
        this.permiteContratosConcomitante = value;
    }

    /**
     * Obtém o valor da propriedade pontuacao.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getPontuacao() {
        return pontuacao;
    }

    /**
     * Define o valor da propriedade pontuacao.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setPontuacao(Integer value) {
        this.pontuacao = value;
    }

    /**
     * Obtém o valor da propriedade sexo.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSexo() {
        return sexo;
    }

    /**
     * Define o valor da propriedade sexo.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSexo(String value) {
        this.sexo = value;
    }

    /**
     * Obtém o valor da propriedade situacao.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSituacao() {
        return situacao;
    }

    /**
     * Define o valor da propriedade situacao.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSituacao(String value) {
        this.situacao = value;
    }

    /**
     * Obtém o valor da propriedade telCelular.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTelCelular() {
        return telCelular;
    }

    /**
     * Define o valor da propriedade telCelular.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTelCelular(String value) {
        this.telCelular = value;
    }

    /**
     * Obtém o valor da propriedade telResidencial.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTelResidencial() {
        return telResidencial;
    }

    /**
     * Define o valor da propriedade telResidencial.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTelResidencial(String value) {
        this.telResidencial = value;
    }

}
