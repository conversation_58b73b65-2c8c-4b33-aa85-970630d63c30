
package servicos.integracao.zw.client;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java de empresaWS complex type.
 * 
 * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
 * 
 * <pre>
 * &lt;complexType name="empresaWS">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="alterarDataHoraCheckGestaoPersonal" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         &lt;element name="bloquearAcessoPersonalSemCredito" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         &lt;element name="bloqueioTemporario" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         &lt;element name="bvObrigatorio" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         &lt;element name="CEP" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="chaveNFSe" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="cidade" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="cnpj" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="codigo" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="codigoColaborador" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="codigoFinanceiro" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="complemento" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="consumirCreditoPorAlunoVinculado" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         &lt;element name="descricaoPerfil" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="diasBloqueioParcelaEmAberto" type="{http://www.w3.org/2001/XMLSchema}int"/>
 *         &lt;element name="duracaoCredito" type="{http://www.w3.org/2001/XMLSchema}int"/>
 *         &lt;element name="email" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="empresaSuspensa" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         &lt;element name="endereco" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="estado" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="idExterno" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="integracaoSpiviHabilitada" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         &lt;element name="integracaoSpiviPassword" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="integracaoSpiviSiteID" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="integracaoSpiviSourceName" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="latitude" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="longitude" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="mostrarFotosAlunosMonitor" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         &lt;element name="nome" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="numero" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="obrigatorioAssociarAlunoAoCheckIn" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         &lt;element name="pais" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="setor" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="site" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="telefone" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="tempoCheckOutAutomatica" type="{http://www.w3.org/2001/XMLSchema}int"/>
 *         &lt;element name="timeZoneDefault" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="tokenSMS" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="tokenShortcode" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="totalDiasExtras" type="{http://www.w3.org/2001/XMLSchema}int"/>
 *         &lt;element name="usarENotas" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         &lt;element name="usarFotoPersonal" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         &lt;element name="usarGestaoCreditosPersonal" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         &lt;element name="usarNFCe" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         &lt;element name="usarNFSe" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         &lt;element name="usarSistemaInternacional" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "empresaWS", propOrder = {
    "alterarDataHoraCheckGestaoPersonal",
    "bloquearAcessoPersonalSemCredito",
    "bloqueioTemporario",
    "bvObrigatorio",
    "cep",
    "chaveNFSe",
    "cidade",
    "cnpj",
    "codigo",
    "codigoColaborador",
    "codigoFinanceiro",
    "complemento",
    "consumirCreditoPorAlunoVinculado",
    "descricaoPerfil",
    "diasBloqueioParcelaEmAberto",
    "duracaoCredito",
    "email",
    "empresaSuspensa",
    "endereco",
    "estado",
    "idExterno",
    "integracaoSpiviHabilitada",
    "integracaoSpiviPassword",
    "integracaoSpiviSiteID",
    "integracaoSpiviSourceName",
    "latitude",
    "longitude",
    "mostrarFotosAlunosMonitor",
    "nome",
    "numero",
    "obrigatorioAssociarAlunoAoCheckIn",
    "pais",
    "setor",
    "site",
    "telefone",
    "tempoCheckOutAutomatica",
    "timeZoneDefault",
    "tokenSMS",
    "tokenShortcode",
    "totalDiasExtras",
    "usarENotas",
    "usarFotoPersonal",
    "usarGestaoCreditosPersonal",
    "usarNFCe",
    "usarNFSe",
    "usarSistemaInternacional"
})
public class EmpresaWS {

    protected boolean alterarDataHoraCheckGestaoPersonal;
    protected boolean bloquearAcessoPersonalSemCredito;
    protected boolean bloqueioTemporario;
    protected boolean bvObrigatorio;
    @XmlElement(name = "CEP")
    protected String cep;
    protected String chaveNFSe;
    protected String cidade;
    protected String cnpj;
    protected Integer codigo;
    protected Integer codigoColaborador;
    protected Integer codigoFinanceiro;
    protected String complemento;
    protected boolean consumirCreditoPorAlunoVinculado;
    protected String descricaoPerfil;
    protected int diasBloqueioParcelaEmAberto;
    protected int duracaoCredito;
    protected String email;
    protected boolean empresaSuspensa;
    protected String endereco;
    protected String estado;
    protected String idExterno;
    protected boolean integracaoSpiviHabilitada;
    protected String integracaoSpiviPassword;
    protected Integer integracaoSpiviSiteID;
    protected String integracaoSpiviSourceName;
    protected String latitude;
    protected String longitude;
    protected boolean mostrarFotosAlunosMonitor;
    protected String nome;
    protected String numero;
    protected boolean obrigatorioAssociarAlunoAoCheckIn;
    protected String pais;
    protected String setor;
    protected String site;
    protected String telefone;
    protected int tempoCheckOutAutomatica;
    protected String timeZoneDefault;
    protected String tokenSMS;
    protected String tokenShortcode;
    protected int totalDiasExtras;
    protected boolean usarENotas;
    protected boolean usarFotoPersonal;
    protected boolean usarGestaoCreditosPersonal;
    protected boolean usarNFCe;
    protected boolean usarNFSe;
    protected boolean usarSistemaInternacional;

    /**
     * Obtém o valor da propriedade alterarDataHoraCheckGestaoPersonal.
     * 
     */
    public boolean isAlterarDataHoraCheckGestaoPersonal() {
        return alterarDataHoraCheckGestaoPersonal;
    }

    /**
     * Define o valor da propriedade alterarDataHoraCheckGestaoPersonal.
     * 
     */
    public void setAlterarDataHoraCheckGestaoPersonal(boolean value) {
        this.alterarDataHoraCheckGestaoPersonal = value;
    }

    /**
     * Obtém o valor da propriedade bloquearAcessoPersonalSemCredito.
     * 
     */
    public boolean isBloquearAcessoPersonalSemCredito() {
        return bloquearAcessoPersonalSemCredito;
    }

    /**
     * Define o valor da propriedade bloquearAcessoPersonalSemCredito.
     * 
     */
    public void setBloquearAcessoPersonalSemCredito(boolean value) {
        this.bloquearAcessoPersonalSemCredito = value;
    }

    /**
     * Obtém o valor da propriedade bloqueioTemporario.
     * 
     */
    public boolean isBloqueioTemporario() {
        return bloqueioTemporario;
    }

    /**
     * Define o valor da propriedade bloqueioTemporario.
     * 
     */
    public void setBloqueioTemporario(boolean value) {
        this.bloqueioTemporario = value;
    }

    /**
     * Obtém o valor da propriedade bvObrigatorio.
     * 
     */
    public boolean isBvObrigatorio() {
        return bvObrigatorio;
    }

    /**
     * Define o valor da propriedade bvObrigatorio.
     * 
     */
    public void setBvObrigatorio(boolean value) {
        this.bvObrigatorio = value;
    }

    /**
     * Obtém o valor da propriedade cep.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCEP() {
        return cep;
    }

    /**
     * Define o valor da propriedade cep.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCEP(String value) {
        this.cep = value;
    }

    /**
     * Obtém o valor da propriedade chaveNFSe.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getChaveNFSe() {
        return chaveNFSe;
    }

    /**
     * Define o valor da propriedade chaveNFSe.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setChaveNFSe(String value) {
        this.chaveNFSe = value;
    }

    /**
     * Obtém o valor da propriedade cidade.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCidade() {
        return cidade;
    }

    /**
     * Define o valor da propriedade cidade.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCidade(String value) {
        this.cidade = value;
    }

    /**
     * Obtém o valor da propriedade cnpj.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCnpj() {
        return cnpj;
    }

    /**
     * Define o valor da propriedade cnpj.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCnpj(String value) {
        this.cnpj = value;
    }

    /**
     * Obtém o valor da propriedade codigo.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getCodigo() {
        return codigo;
    }

    /**
     * Define o valor da propriedade codigo.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setCodigo(Integer value) {
        this.codigo = value;
    }

    /**
     * Obtém o valor da propriedade codigoColaborador.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getCodigoColaborador() {
        return codigoColaborador;
    }

    /**
     * Define o valor da propriedade codigoColaborador.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setCodigoColaborador(Integer value) {
        this.codigoColaborador = value;
    }

    /**
     * Obtém o valor da propriedade codigoFinanceiro.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getCodigoFinanceiro() {
        return codigoFinanceiro;
    }

    /**
     * Define o valor da propriedade codigoFinanceiro.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setCodigoFinanceiro(Integer value) {
        this.codigoFinanceiro = value;
    }

    /**
     * Obtém o valor da propriedade complemento.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getComplemento() {
        return complemento;
    }

    /**
     * Define o valor da propriedade complemento.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setComplemento(String value) {
        this.complemento = value;
    }

    /**
     * Obtém o valor da propriedade consumirCreditoPorAlunoVinculado.
     * 
     */
    public boolean isConsumirCreditoPorAlunoVinculado() {
        return consumirCreditoPorAlunoVinculado;
    }

    /**
     * Define o valor da propriedade consumirCreditoPorAlunoVinculado.
     * 
     */
    public void setConsumirCreditoPorAlunoVinculado(boolean value) {
        this.consumirCreditoPorAlunoVinculado = value;
    }

    /**
     * Obtém o valor da propriedade descricaoPerfil.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDescricaoPerfil() {
        return descricaoPerfil;
    }

    /**
     * Define o valor da propriedade descricaoPerfil.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDescricaoPerfil(String value) {
        this.descricaoPerfil = value;
    }

    /**
     * Obtém o valor da propriedade diasBloqueioParcelaEmAberto.
     * 
     */
    public int getDiasBloqueioParcelaEmAberto() {
        return diasBloqueioParcelaEmAberto;
    }

    /**
     * Define o valor da propriedade diasBloqueioParcelaEmAberto.
     * 
     */
    public void setDiasBloqueioParcelaEmAberto(int value) {
        this.diasBloqueioParcelaEmAberto = value;
    }

    /**
     * Obtém o valor da propriedade duracaoCredito.
     * 
     */
    public int getDuracaoCredito() {
        return duracaoCredito;
    }

    /**
     * Define o valor da propriedade duracaoCredito.
     * 
     */
    public void setDuracaoCredito(int value) {
        this.duracaoCredito = value;
    }

    /**
     * Obtém o valor da propriedade email.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getEmail() {
        return email;
    }

    /**
     * Define o valor da propriedade email.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setEmail(String value) {
        this.email = value;
    }

    /**
     * Obtém o valor da propriedade empresaSuspensa.
     * 
     */
    public boolean isEmpresaSuspensa() {
        return empresaSuspensa;
    }

    /**
     * Define o valor da propriedade empresaSuspensa.
     * 
     */
    public void setEmpresaSuspensa(boolean value) {
        this.empresaSuspensa = value;
    }

    /**
     * Obtém o valor da propriedade endereco.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getEndereco() {
        return endereco;
    }

    /**
     * Define o valor da propriedade endereco.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setEndereco(String value) {
        this.endereco = value;
    }

    /**
     * Obtém o valor da propriedade estado.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getEstado() {
        return estado;
    }

    /**
     * Define o valor da propriedade estado.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setEstado(String value) {
        this.estado = value;
    }

    /**
     * Obtém o valor da propriedade idExterno.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getIdExterno() {
        return idExterno;
    }

    /**
     * Define o valor da propriedade idExterno.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setIdExterno(String value) {
        this.idExterno = value;
    }

    /**
     * Obtém o valor da propriedade integracaoSpiviHabilitada.
     * 
     */
    public boolean isIntegracaoSpiviHabilitada() {
        return integracaoSpiviHabilitada;
    }

    /**
     * Define o valor da propriedade integracaoSpiviHabilitada.
     * 
     */
    public void setIntegracaoSpiviHabilitada(boolean value) {
        this.integracaoSpiviHabilitada = value;
    }

    /**
     * Obtém o valor da propriedade integracaoSpiviPassword.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getIntegracaoSpiviPassword() {
        return integracaoSpiviPassword;
    }

    /**
     * Define o valor da propriedade integracaoSpiviPassword.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setIntegracaoSpiviPassword(String value) {
        this.integracaoSpiviPassword = value;
    }

    /**
     * Obtém o valor da propriedade integracaoSpiviSiteID.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getIntegracaoSpiviSiteID() {
        return integracaoSpiviSiteID;
    }

    /**
     * Define o valor da propriedade integracaoSpiviSiteID.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setIntegracaoSpiviSiteID(Integer value) {
        this.integracaoSpiviSiteID = value;
    }

    /**
     * Obtém o valor da propriedade integracaoSpiviSourceName.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getIntegracaoSpiviSourceName() {
        return integracaoSpiviSourceName;
    }

    /**
     * Define o valor da propriedade integracaoSpiviSourceName.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setIntegracaoSpiviSourceName(String value) {
        this.integracaoSpiviSourceName = value;
    }

    /**
     * Obtém o valor da propriedade latitude.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLatitude() {
        return latitude;
    }

    /**
     * Define o valor da propriedade latitude.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLatitude(String value) {
        this.latitude = value;
    }

    /**
     * Obtém o valor da propriedade longitude.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLongitude() {
        return longitude;
    }

    /**
     * Define o valor da propriedade longitude.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLongitude(String value) {
        this.longitude = value;
    }

    /**
     * Obtém o valor da propriedade mostrarFotosAlunosMonitor.
     * 
     */
    public boolean isMostrarFotosAlunosMonitor() {
        return mostrarFotosAlunosMonitor;
    }

    /**
     * Define o valor da propriedade mostrarFotosAlunosMonitor.
     * 
     */
    public void setMostrarFotosAlunosMonitor(boolean value) {
        this.mostrarFotosAlunosMonitor = value;
    }

    /**
     * Obtém o valor da propriedade nome.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNome() {
        return nome;
    }

    /**
     * Define o valor da propriedade nome.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNome(String value) {
        this.nome = value;
    }

    /**
     * Obtém o valor da propriedade numero.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNumero() {
        return numero;
    }

    /**
     * Define o valor da propriedade numero.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNumero(String value) {
        this.numero = value;
    }

    /**
     * Obtém o valor da propriedade obrigatorioAssociarAlunoAoCheckIn.
     * 
     */
    public boolean isObrigatorioAssociarAlunoAoCheckIn() {
        return obrigatorioAssociarAlunoAoCheckIn;
    }

    /**
     * Define o valor da propriedade obrigatorioAssociarAlunoAoCheckIn.
     * 
     */
    public void setObrigatorioAssociarAlunoAoCheckIn(boolean value) {
        this.obrigatorioAssociarAlunoAoCheckIn = value;
    }

    /**
     * Obtém o valor da propriedade pais.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPais() {
        return pais;
    }

    /**
     * Define o valor da propriedade pais.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPais(String value) {
        this.pais = value;
    }

    /**
     * Obtém o valor da propriedade setor.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSetor() {
        return setor;
    }

    /**
     * Define o valor da propriedade setor.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSetor(String value) {
        this.setor = value;
    }

    /**
     * Obtém o valor da propriedade site.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSite() {
        return site;
    }

    /**
     * Define o valor da propriedade site.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSite(String value) {
        this.site = value;
    }

    /**
     * Obtém o valor da propriedade telefone.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTelefone() {
        return telefone;
    }

    /**
     * Define o valor da propriedade telefone.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTelefone(String value) {
        this.telefone = value;
    }

    /**
     * Obtém o valor da propriedade tempoCheckOutAutomatica.
     * 
     */
    public int getTempoCheckOutAutomatica() {
        return tempoCheckOutAutomatica;
    }

    /**
     * Define o valor da propriedade tempoCheckOutAutomatica.
     * 
     */
    public void setTempoCheckOutAutomatica(int value) {
        this.tempoCheckOutAutomatica = value;
    }

    /**
     * Obtém o valor da propriedade timeZoneDefault.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTimeZoneDefault() {
        return timeZoneDefault;
    }

    /**
     * Define o valor da propriedade timeZoneDefault.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTimeZoneDefault(String value) {
        this.timeZoneDefault = value;
    }

    /**
     * Obtém o valor da propriedade tokenSMS.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTokenSMS() {
        return tokenSMS;
    }

    /**
     * Define o valor da propriedade tokenSMS.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTokenSMS(String value) {
        this.tokenSMS = value;
    }

    /**
     * Obtém o valor da propriedade tokenShortcode.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTokenShortcode() {
        return tokenShortcode;
    }

    /**
     * Define o valor da propriedade tokenShortcode.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTokenShortcode(String value) {
        this.tokenShortcode = value;
    }

    /**
     * Obtém o valor da propriedade totalDiasExtras.
     * 
     */
    public int getTotalDiasExtras() {
        return totalDiasExtras;
    }

    /**
     * Define o valor da propriedade totalDiasExtras.
     * 
     */
    public void setTotalDiasExtras(int value) {
        this.totalDiasExtras = value;
    }

    /**
     * Obtém o valor da propriedade usarENotas.
     * 
     */
    public boolean isUsarENotas() {
        return usarENotas;
    }

    /**
     * Define o valor da propriedade usarENotas.
     * 
     */
    public void setUsarENotas(boolean value) {
        this.usarENotas = value;
    }

    /**
     * Obtém o valor da propriedade usarFotoPersonal.
     * 
     */
    public boolean isUsarFotoPersonal() {
        return usarFotoPersonal;
    }

    /**
     * Define o valor da propriedade usarFotoPersonal.
     * 
     */
    public void setUsarFotoPersonal(boolean value) {
        this.usarFotoPersonal = value;
    }

    /**
     * Obtém o valor da propriedade usarGestaoCreditosPersonal.
     * 
     */
    public boolean isUsarGestaoCreditosPersonal() {
        return usarGestaoCreditosPersonal;
    }

    /**
     * Define o valor da propriedade usarGestaoCreditosPersonal.
     * 
     */
    public void setUsarGestaoCreditosPersonal(boolean value) {
        this.usarGestaoCreditosPersonal = value;
    }

    /**
     * Obtém o valor da propriedade usarNFCe.
     * 
     */
    public boolean isUsarNFCe() {
        return usarNFCe;
    }

    /**
     * Define o valor da propriedade usarNFCe.
     * 
     */
    public void setUsarNFCe(boolean value) {
        this.usarNFCe = value;
    }

    /**
     * Obtém o valor da propriedade usarNFSe.
     * 
     */
    public boolean isUsarNFSe() {
        return usarNFSe;
    }

    /**
     * Define o valor da propriedade usarNFSe.
     * 
     */
    public void setUsarNFSe(boolean value) {
        this.usarNFSe = value;
    }

    /**
     * Obtém o valor da propriedade usarSistemaInternacional.
     * 
     */
    public boolean isUsarSistemaInternacional() {
        return usarSistemaInternacional;
    }

    /**
     * Define o valor da propriedade usarSistemaInternacional.
     * 
     */
    public void setUsarSistemaInternacional(boolean value) {
        this.usarSistemaInternacional = value;
    }

}
