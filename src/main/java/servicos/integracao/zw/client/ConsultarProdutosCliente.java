
package servicos.integracao.zw.client;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java de consultarProdutosCliente complex type.
 * 
 * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
 * 
 * <pre>
 * &lt;complexType name="consultarProdutosCliente">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="key" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="cliente" type="{http://www.w3.org/2001/XMLSchema}int"/>
 *         &lt;element name="registros" type="{http://www.w3.org/2001/XMLSchema}int"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "consultarProdutosCliente", propOrder = {
    "key",
    "cliente",
    "registros"
})
public class ConsultarProdutosCliente {

    protected String key;
    protected int cliente;
    protected int registros;

    /**
     * Obtém o valor da propriedade key.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getKey() {
        return key;
    }

    /**
     * Define o valor da propriedade key.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setKey(String value) {
        this.key = value;
    }

    /**
     * Obtém o valor da propriedade cliente.
     * 
     */
    public int getCliente() {
        return cliente;
    }

    /**
     * Define o valor da propriedade cliente.
     * 
     */
    public void setCliente(int value) {
        this.cliente = value;
    }

    /**
     * Obtém o valor da propriedade registros.
     * 
     */
    public int getRegistros() {
        return registros;
    }

    /**
     * Define o valor da propriedade registros.
     * 
     */
    public void setRegistros(int value) {
        this.registros = value;
    }

}
