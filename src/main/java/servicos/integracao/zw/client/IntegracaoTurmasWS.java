
package servicos.integracao.zw.client;

import java.util.List;
import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import javax.xml.bind.annotation.XmlSeeAlso;
import javax.xml.ws.RequestWrapper;
import javax.xml.ws.ResponseWrapper;


/**
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.2.6b21 
 * Generated source version: 2.1
 * 
 */
@WebService(name = "IntegracaoTurmasWS", targetNamespace = "http://webservice.basico.comuns.negocio/")
@XmlSeeAlso({
    ObjectFactory.class
})
public interface IntegracaoTurmasWS {


    /**
     * 
     * @param turma
     * @param key
     * @return
     *     returns java.util.List<servicos.integracao.zw.client.HorarioTurmaDTO>
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "horariosTurma", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.HorariosTurma")
    @ResponseWrapper(localName = "horariosTurmaResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.HorariosTurmaResponse")
    public List<HorarioTurmaDTO> horariosTurma(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "turma", targetNamespace = "")
        Integer turma);

    /**
     * 
     * @param consultarAulaCheia
     * @param consultarTurmaZW
     * @param inicio
     * @param fim
     * @param key
     * @param codigoConvite
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarTurmasParaAgendarAulaExperimental", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarTurmasParaAgendarAulaExperimental")
    @ResponseWrapper(localName = "consultarTurmasParaAgendarAulaExperimentalResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarTurmasParaAgendarAulaExperimentalResponse")
    public String consultarTurmasParaAgendarAulaExperimental(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "inicio", targetNamespace = "")
        String inicio,
        @WebParam(name = "fim", targetNamespace = "")
        String fim,
        @WebParam(name = "codigoConvite", targetNamespace = "")
        Integer codigoConvite,
        @WebParam(name = "consultarAulaCheia", targetNamespace = "")
        Integer consultarAulaCheia,
        @WebParam(name = "consultarTurmaZW", targetNamespace = "")
        Integer consultarTurmaZW);

    /**
     * 
     * @param empresa
     * @param key
     * @param modalidade
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarTurmas", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarTurmas")
    @ResponseWrapper(localName = "consultarTurmasResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarTurmasResponse")
    public String consultarTurmas(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa,
        @WebParam(name = "modalidade", targetNamespace = "")
        Integer modalidade);

    /**
     * 
     * @param inicio
     * @param fim
     * @param empresa
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarAgendados", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarAgendados")
    @ResponseWrapper(localName = "consultarAgendadosResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarAgendadosResponse")
    public String consultarAgendados(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "inicio", targetNamespace = "")
        String inicio,
        @WebParam(name = "fim", targetNamespace = "")
        String fim,
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa);

    /**
     * 
     * @param inicio
     * @param fim
     * @param empresa
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarReposicoes", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarReposicoes")
    @ResponseWrapper(localName = "consultarReposicoesResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarReposicoesResponse")
    public String consultarReposicoes(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "inicio", targetNamespace = "")
        String inicio,
        @WebParam(name = "fim", targetNamespace = "")
        String fim,
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa);

    /**
     * 
     * @param params
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "desmarcarAulas", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.DesmarcarAulas")
    @ResponseWrapper(localName = "desmarcarAulasResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.DesmarcarAulasResponse")
    public String desmarcarAulas(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "params", targetNamespace = "")
        String params);

    /**
     * 
     * @param params
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "reporAula", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ReporAula")
    @ResponseWrapper(localName = "reporAulaResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ReporAulaResponse")
    public String reporAula(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "params", targetNamespace = "")
        String params);

    /**
     * 
     * @param params
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "inserirAlunoAulaCheia", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.InserirAlunoAulaCheia")
    @ResponseWrapper(localName = "inserirAlunoAulaCheiaResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.InserirAlunoAulaCheiaResponse")
    public String inserirAlunoAulaCheia(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "params", targetNamespace = "")
        String params);

    /**
     * 
     * @param params
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "excluirAlunoAulaCheia", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ExcluirAlunoAulaCheia")
    @ResponseWrapper(localName = "excluirAlunoAulaCheiaResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ExcluirAlunoAulaCheiaResponse")
    public String excluirAlunoAulaCheia(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "params", targetNamespace = "")
        String params);

    /**
     * 
     * @param codigoUsuario
     * @param codigoAula
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "excluirAulaCheia", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ExcluirAulaCheia")
    @ResponseWrapper(localName = "excluirAulaCheiaResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ExcluirAulaCheiaResponse")
    public String excluirAulaCheia(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "codigoAula", targetNamespace = "")
        Integer codigoAula,
        @WebParam(name = "codigoUsuario", targetNamespace = "")
        Integer codigoUsuario);

    /**
     * 
     * @param matricula
     * @param nome
     * @param empresa
     * @param key
     * @param modalidade
     * @param somenteAtivos
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarClientesModalidade", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarClientesModalidade")
    @ResponseWrapper(localName = "consultarClientesModalidadeResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarClientesModalidadeResponse")
    public String consultarClientesModalidade(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa,
        @WebParam(name = "matricula", targetNamespace = "")
        Integer matricula,
        @WebParam(name = "nome", targetNamespace = "")
        String nome,
        @WebParam(name = "modalidade", targetNamespace = "")
        Integer modalidade,
        @WebParam(name = "somenteAtivos", targetNamespace = "")
        String somenteAtivos);

    /**
     * 
     * @param contrato
     * @param saldo
     * @param key
     * @param modalidade
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarTemAulaExtra", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarTemAulaExtra")
    @ResponseWrapper(localName = "consultarTemAulaExtraResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarTemAulaExtraResponse")
    public String consultarTemAulaExtra(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "modalidade", targetNamespace = "")
        Integer modalidade,
        @WebParam(name = "saldo", targetNamespace = "")
        Integer saldo,
        @WebParam(name = "contrato", targetNamespace = "")
        Integer contrato);

    /**
     * 
     * @param inicio
     * @param fim
     * @param empresa
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarDesmarcados", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarDesmarcados")
    @ResponseWrapper(localName = "consultarDesmarcadosResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarDesmarcadosResponse")
    public String consultarDesmarcados(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "inicio", targetNamespace = "")
        String inicio,
        @WebParam(name = "fim", targetNamespace = "")
        String fim,
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa);

    /**
     * 
     * @param cliente
     * @param contrato
     * @param key
     * @param modalidade
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "aulaARepor", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.AulaARepor")
    @ResponseWrapper(localName = "aulaAReporResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.AulaAReporResponse")
    public String aulaARepor(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "cliente", targetNamespace = "")
        Integer cliente,
        @WebParam(name = "contrato", targetNamespace = "")
        Integer contrato,
        @WebParam(name = "modalidade", targetNamespace = "")
        Integer modalidade);

    /**
     * 
     * @param jsonStr
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "gravarAula", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.GravarAula")
    @ResponseWrapper(localName = "gravarAulaResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.GravarAulaResponse")
    public String gravarAula(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "jsonStr", targetNamespace = "")
        String jsonStr);

    /**
     * 
     * @param empresa
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "obterAulasColetivas", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ObterAulasColetivas")
    @ResponseWrapper(localName = "obterAulasColetivasResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ObterAulasColetivasResponse")
    public String obterAulasColetivas(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa);

    /**
     * 
     * @param empresa
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "obterPresencas", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ObterPresencas")
    @ResponseWrapper(localName = "obterPresencasResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ObterPresencasResponse")
    public String obterPresencas(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa);

    /**
     * 
     * @param inicio
     * @param fim
     * @param empresa
     * @param key
     * @param modalidade
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarAgendamentos", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarAgendamentos")
    @ResponseWrapper(localName = "consultarAgendamentosResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarAgendamentosResponse")
    public String consultarAgendamentos(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "inicio", targetNamespace = "")
        String inicio,
        @WebParam(name = "fim", targetNamespace = "")
        String fim,
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa,
        @WebParam(name = "modalidade", targetNamespace = "")
        String modalidade);

    /**
     * 
     * @param codigoHorario
     * @param dia
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "obterAlunosDeUmaAula", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ObterAlunosDeUmaAula")
    @ResponseWrapper(localName = "obterAlunosDeUmaAulaResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ObterAlunosDeUmaAulaResponse")
    public String obterAlunosDeUmaAula(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "codigoHorario", targetNamespace = "")
        Integer codigoHorario,
        @WebParam(name = "dia", targetNamespace = "")
        String dia);

    /**
     * 
     * @param codigoHorario
     * @param dia
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "obterAlunosAulaNaoColetiva", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ObterAlunosAulaNaoColetiva")
    @ResponseWrapper(localName = "obterAlunosAulaNaoColetivaResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ObterAlunosAulaNaoColetivaResponse")
    public String obterAlunosAulaNaoColetiva(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "codigoHorario", targetNamespace = "")
        Integer codigoHorario,
        @WebParam(name = "dia", targetNamespace = "")
        String dia);

    /**
     * 
     * @param codigoHorario
     * @param empresa
     * @param dia
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "obterAlunosHorarioTurma", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ObterAlunosHorarioTurma")
    @ResponseWrapper(localName = "obterAlunosHorarioTurmaResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ObterAlunosHorarioTurmaResponse")
    public String obterAlunosHorarioTurma(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa,
        @WebParam(name = "codigoHorario", targetNamespace = "")
        Integer codigoHorario,
        @WebParam(name = "dia", targetNamespace = "")
        String dia);

    /**
     * 
     * @param empresa
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "obterAcessos", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ObterAcessos")
    @ResponseWrapper(localName = "obterAcessosResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ObterAcessosResponse")
    public String obterAcessos(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa);

    /**
     * 
     * @param matricula
     * @param key
     * @param proximos30Dias
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarProximasAulas", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarProximasAulas")
    @ResponseWrapper(localName = "consultarProximasAulasResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarProximasAulasResponse")
    public String consultarProximasAulas(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "matricula", targetNamespace = "")
        Integer matricula,
        @WebParam(name = "proximos30dias", targetNamespace = "")
        String proximos30Dias);

    /**
     * 
     * @param matricula
     * @param inicio
     * @param fim
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarAulasAlunoPeriodo", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarAulasAlunoPeriodo")
    @ResponseWrapper(localName = "consultarAulasAlunoPeriodoResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarAulasAlunoPeriodoResponse")
    public String consultarAulasAlunoPeriodo(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "matricula", targetNamespace = "")
        Integer matricula,
        @WebParam(name = "inicio", targetNamespace = "")
        String inicio,
        @WebParam(name = "fim", targetNamespace = "")
        String fim);

    /**
     * 
     * @param data
     * @param matricula
     * @param bloquearParcelaVencida
     * @param marcar
     * @param horarioturma
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "marcarDesmarcarAulasApp", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.MarcarDesmarcarAulasApp")
    @ResponseWrapper(localName = "marcarDesmarcarAulasAppResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.MarcarDesmarcarAulasAppResponse")
    public String marcarDesmarcarAulasApp(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "matricula", targetNamespace = "")
        Integer matricula,
        @WebParam(name = "horarioturma", targetNamespace = "")
        Integer horarioturma,
        @WebParam(name = "data", targetNamespace = "")
        String data,
        @WebParam(name = "marcar", targetNamespace = "")
        String marcar,
        @WebParam(name = "bloquearParcelaVencida", targetNamespace = "")
        String bloquearParcelaVencida);

    /**
     * 
     * @param matricula
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "obterAulasDesmarcadas", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ObterAulasDesmarcadas")
    @ResponseWrapper(localName = "obterAulasDesmarcadasResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ObterAulasDesmarcadasResponse")
    public String obterAulasDesmarcadas(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "matricula", targetNamespace = "")
        Integer matricula);

    /**
     * 
     * @param data
     * @param matricula
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "obterExtratoCreditos", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ObterExtratoCreditos")
    @ResponseWrapper(localName = "obterExtratoCreditosResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ObterExtratoCreditosResponse")
    public String obterExtratoCreditos(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "matricula", targetNamespace = "")
        Integer matricula,
        @WebParam(name = "data", targetNamespace = "")
        String data);

    /**
     * 
     * @param app
     * @param matricula
     * @param inicio
     * @param fim
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarAgendamentosModalidadeAluno", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarAgendamentosModalidadeAluno")
    @ResponseWrapper(localName = "consultarAgendamentosModalidadeAlunoResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarAgendamentosModalidadeAlunoResponse")
    public String consultarAgendamentosModalidadeAluno(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "inicio", targetNamespace = "")
        String inicio,
        @WebParam(name = "fim", targetNamespace = "")
        String fim,
        @WebParam(name = "matricula", targetNamespace = "")
        Integer matricula,
        @WebParam(name = "app", targetNamespace = "")
        boolean app);

    /**
     * 
     * @param matricula
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarTurmasAluno", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarTurmasAluno")
    @ResponseWrapper(localName = "consultarTurmasAlunoResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarTurmasAlunoResponse")
    public String consultarTurmasAluno(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "matricula", targetNamespace = "")
        Integer matricula);

    /**
     * 
     * @param matricula
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "obterSaldoAluno", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ObterSaldoAluno")
    @ResponseWrapper(localName = "obterSaldoAlunoResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ObterSaldoAlunoResponse")
    public String obterSaldoAluno(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "matricula", targetNamespace = "")
        Integer matricula);

    /**
     * 
     * @param matricula
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarProximasAulaCheia", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarProximasAulaCheia")
    @ResponseWrapper(localName = "consultarProximasAulaCheiaResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarProximasAulaCheiaResponse")
    public String consultarProximasAulaCheia(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "matricula", targetNamespace = "")
        Integer matricula);

    /**
     * 
     * @param inicio
     * @param empresa
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "obterAcessosDia", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ObterAcessosDia")
    @ResponseWrapper(localName = "obterAcessosDiaResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ObterAcessosDiaResponse")
    public String obterAcessosDia(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa,
        @WebParam(name = "inicio", targetNamespace = "")
        String inicio);

    /**
     * 
     * @param empresa
     * @param dia
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarUmaAula", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarUmaAula")
    @ResponseWrapper(localName = "consultarUmaAulaResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarUmaAulaResponse")
    public String consultarUmaAula(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "dia", targetNamespace = "")
        String dia,
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa);

    /**
     * 
     * @param empresa
     * @param key
     * @param somenteAtivos
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarProfessores", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarProfessores")
    @ResponseWrapper(localName = "consultarProfessoresResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarProfessoresResponse")
    public String consultarProfessores(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa,
        @WebParam(name = "somenteAtivos", targetNamespace = "")
        String somenteAtivos);

    /**
     * 
     * @param matricula
     * @param dia
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "obterAulasDiaAluno", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ObterAulasDiaAluno")
    @ResponseWrapper(localName = "obterAulasDiaAlunoResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ObterAulasDiaAlunoResponse")
    public String obterAulasDiaAluno(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "matricula", targetNamespace = "")
        Integer matricula,
        @WebParam(name = "dia", targetNamespace = "")
        String dia);

    /**
     * 
     * @param codigoAluno
     * @param data
     * @param horarioturma
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "marcarEuQueroHorario", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.MarcarEuQueroHorario")
    @ResponseWrapper(localName = "marcarEuQueroHorarioResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.MarcarEuQueroHorarioResponse")
    public String marcarEuQueroHorario(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "codigoAluno", targetNamespace = "")
        Integer codigoAluno,
        @WebParam(name = "horarioturma", targetNamespace = "")
        Integer horarioturma,
        @WebParam(name = "data", targetNamespace = "")
        String data);

    /**
     * 
     * @param codigoAluno
     * @param data
     * @param horarioturma
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "desmarcarEuQueroHorario", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.DesmarcarEuQueroHorario")
    @ResponseWrapper(localName = "desmarcarEuQueroHorarioResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.DesmarcarEuQueroHorarioResponse")
    public String desmarcarEuQueroHorario(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "codigoAluno", targetNamespace = "")
        Integer codigoAluno,
        @WebParam(name = "horarioturma", targetNamespace = "")
        Integer horarioturma,
        @WebParam(name = "data", targetNamespace = "")
        String data);

    /**
     * 
     * @param filtroDiasDaSemana
     * @param filtroAmbientes
     * @param filtroHorarios
     * @param dataFim
     * @param filtroProfessores
     * @param dataInicio
     * @param filtroTurmas
     * @param key
     * @param filtroModalidades
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarDemandasSintetico", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarDemandasSintetico")
    @ResponseWrapper(localName = "consultarDemandasSinteticoResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarDemandasSinteticoResponse")
    public String consultarDemandasSintetico(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "filtroProfessores", targetNamespace = "")
        String filtroProfessores,
        @WebParam(name = "filtroModalidades", targetNamespace = "")
        String filtroModalidades,
        @WebParam(name = "filtroAmbientes", targetNamespace = "")
        String filtroAmbientes,
        @WebParam(name = "filtroTurmas", targetNamespace = "")
        String filtroTurmas,
        @WebParam(name = "filtroHorarios", targetNamespace = "")
        String filtroHorarios,
        @WebParam(name = "filtroDiasDaSemana", targetNamespace = "")
        String filtroDiasDaSemana,
        @WebParam(name = "dataInicio", targetNamespace = "")
        String dataInicio,
        @WebParam(name = "dataFim", targetNamespace = "")
        String dataFim);

    /**
     * 
     * @param filtroDiasDaSemana
     * @param filtroAmbientes
     * @param filtroHorarios
     * @param dataFim
     * @param filtroProfessores
     * @param dataInicio
     * @param filtroTurmas
     * @param key
     * @param filtroModalidades
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarDemandasAnalitico", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarDemandasAnalitico")
    @ResponseWrapper(localName = "consultarDemandasAnaliticoResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarDemandasAnaliticoResponse")
    public String consultarDemandasAnalitico(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "filtroProfessores", targetNamespace = "")
        String filtroProfessores,
        @WebParam(name = "filtroModalidades", targetNamespace = "")
        String filtroModalidades,
        @WebParam(name = "filtroAmbientes", targetNamespace = "")
        String filtroAmbientes,
        @WebParam(name = "filtroTurmas", targetNamespace = "")
        String filtroTurmas,
        @WebParam(name = "filtroHorarios", targetNamespace = "")
        String filtroHorarios,
        @WebParam(name = "filtroDiasDaSemana", targetNamespace = "")
        String filtroDiasDaSemana,
        @WebParam(name = "dataInicio", targetNamespace = "")
        String dataInicio,
        @WebParam(name = "dataFim", targetNamespace = "")
        String dataFim);

    /**
     * 
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarTodasTurmas", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarTodasTurmas")
    @ResponseWrapper(localName = "consultarTodasTurmasResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarTodasTurmasResponse")
    public String consultarTodasTurmas(
        @WebParam(name = "key", targetNamespace = "")
        String key);

    /**
     * 
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarTodasHoraInicial", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarTodasHoraInicial")
    @ResponseWrapper(localName = "consultarTodasHoraInicialResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarTodasHoraInicialResponse")
    public String consultarTodasHoraInicial(
        @WebParam(name = "key", targetNamespace = "")
        String key);

    /**
     * 
     * @param consultarAulaCheia
     * @param consultarTurmaZW
     * @param key
     * @param codigoConvite
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarAulaExperimentalAgendada", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarAulaExperimentalAgendada")
    @ResponseWrapper(localName = "consultarAulaExperimentalAgendadaResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarAulaExperimentalAgendadaResponse")
    public String consultarAulaExperimentalAgendada(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "codigoConvite", targetNamespace = "")
        Integer codigoConvite,
        @WebParam(name = "consultarAulaCheia", targetNamespace = "")
        Integer consultarAulaCheia,
        @WebParam(name = "consultarTurmaZW", targetNamespace = "")
        Integer consultarTurmaZW);

    /**
     * 
     * @param cliente
     * @param diaAula
     * @param horarioTurma
     * @param usuario
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "confirmarAulaAluno", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConfirmarAulaAluno")
    @ResponseWrapper(localName = "confirmarAulaAlunoResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConfirmarAulaAlunoResponse")
    public String confirmarAulaAluno(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "horarioTurma", targetNamespace = "")
        Integer horarioTurma,
        @WebParam(name = "cliente", targetNamespace = "")
        Integer cliente,
        @WebParam(name = "diaAula", targetNamespace = "")
        String diaAula,
        @WebParam(name = "usuario", targetNamespace = "")
        Integer usuario);

    /**
     * 
     * @param cliente
     * @param diaAula
     * @param horarioTurma
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "removerAulaConfirmadaAluno", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.RemoverAulaConfirmadaAluno")
    @ResponseWrapper(localName = "removerAulaConfirmadaAlunoResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.RemoverAulaConfirmadaAlunoResponse")
    public String removerAulaConfirmadaAluno(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "horarioTurma", targetNamespace = "")
        Integer horarioTurma,
        @WebParam(name = "cliente", targetNamespace = "")
        Integer cliente,
        @WebParam(name = "diaAula", targetNamespace = "")
        String diaAula);

    /**
     * 
     * @param inicio
     * @param fim
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarConfirmados", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarConfirmados")
    @ResponseWrapper(localName = "consultarConfirmadosResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarConfirmadosResponse")
    public String consultarConfirmados(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "inicio", targetNamespace = "")
        String inicio,
        @WebParam(name = "fim", targetNamespace = "")
        String fim);

    /**
     * 
     * @param produtoFreePass
     * @param data
     * @param matricula
     * @param usuario
     * @param horarioturma
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "inserirAlunoExperimental", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.InserirAlunoExperimental")
    @ResponseWrapper(localName = "inserirAlunoExperimentalResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.InserirAlunoExperimentalResponse")
    public String inserirAlunoExperimental(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "matricula", targetNamespace = "")
        Integer matricula,
        @WebParam(name = "horarioturma", targetNamespace = "")
        Integer horarioturma,
        @WebParam(name = "data", targetNamespace = "")
        String data,
        @WebParam(name = "usuario", targetNamespace = "")
        Integer usuario,
        @WebParam(name = "produtoFreePass", targetNamespace = "")
        Integer produtoFreePass);

    /**
     * 
     * @param empresa
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarProdutosFreePass", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarProdutosFreePass")
    @ResponseWrapper(localName = "consultarProdutosFreePassResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarProdutosFreePassResponse")
    public String consultarProdutosFreePass(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa);

    /**
     * 
     * @param empresa
     * @param key
     * @param somenteAtivos
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarProdutosAulaCheia", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarProdutosAulaCheia")
    @ResponseWrapper(localName = "consultarProdutosAulaCheiaResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarProdutosAulaCheiaResponse")
    public String consultarProdutosAulaCheia(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa,
        @WebParam(name = "somenteAtivos", targetNamespace = "")
        Boolean somenteAtivos);

    /**
     * 
     * @param reposicao
     * @param cliente
     * @param desmarcar
     * @param data
     * @param horarioturma
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "marcarPresenca", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.MarcarPresenca")
    @ResponseWrapper(localName = "marcarPresencaResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.MarcarPresencaResponse")
    public String marcarPresenca(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "cliente", targetNamespace = "")
        Integer cliente,
        @WebParam(name = "horarioturma", targetNamespace = "")
        Integer horarioturma,
        @WebParam(name = "data", targetNamespace = "")
        String data,
        @WebParam(name = "reposicao", targetNamespace = "")
        String reposicao,
        @WebParam(name = "desmarcar", targetNamespace = "")
        String desmarcar);

    /**
     * 
     * @param data
     * @param horarioturma
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "alunosPresentes", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.AlunosPresentes")
    @ResponseWrapper(localName = "alunosPresentesResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.AlunosPresentesResponse")
    public String alunosPresentes(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "horarioturma", targetNamespace = "")
        Integer horarioturma,
        @WebParam(name = "data", targetNamespace = "")
        String data);

    /**
     * 
     * @param turmaId
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "pesquisarHorarioTurmaPorTurma", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.PesquisarHorarioTurmaPorTurma")
    @ResponseWrapper(localName = "pesquisarHorarioTurmaPorTurmaResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.PesquisarHorarioTurmaPorTurmaResponse")
    public String pesquisarHorarioTurmaPorTurma(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "turmaId", targetNamespace = "")
        Integer turmaId);

    /**
     * 
     * @param horarioturma
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "pesquisarHorarioTurma", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.PesquisarHorarioTurma")
    @ResponseWrapper(localName = "pesquisarHorarioTurmaResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.PesquisarHorarioTurmaResponse")
    public String pesquisarHorarioTurma(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "horarioturma", targetNamespace = "")
        Integer horarioturma);

    /**
     * 
     * @param horaInicial
     * @param data
     * @param horaFinal
     * @param matricula
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarAulaPeloClienteHorario", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarAulaPeloClienteHorario")
    @ResponseWrapper(localName = "consultarAulaPeloClienteHorarioResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarAulaPeloClienteHorarioResponse")
    public String consultarAulaPeloClienteHorario(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "data", targetNamespace = "")
        String data,
        @WebParam(name = "matricula", targetNamespace = "")
        String matricula,
        @WebParam(name = "horaInicial", targetNamespace = "")
        String horaInicial,
        @WebParam(name = "horaFinal", targetNamespace = "")
        String horaFinal);

    /**
     * 
     * @param data
     * @param codigoModalidade
     * @param codigoCliente
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarAlunoTemDiaria", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarAlunoTemDiaria")
    @ResponseWrapper(localName = "consultarAlunoTemDiariaResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarAlunoTemDiariaResponse")
    public String consultarAlunoTemDiaria(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "codigoCliente", targetNamespace = "")
        Integer codigoCliente,
        @WebParam(name = "codigoModalidade", targetNamespace = "")
        Integer codigoModalidade,
        @WebParam(name = "data", targetNamespace = "")
        String data);

    /**
     * 
     * @param valorNovo
     * @param key
     * @param nrAulasExperimentaisAntesAlteracao
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "atualizarConfiguracaoNrAulasExperimentais", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.AtualizarConfiguracaoNrAulasExperimentais")
    @ResponseWrapper(localName = "atualizarConfiguracaoNrAulasExperimentaisResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.AtualizarConfiguracaoNrAulasExperimentaisResponse")
    public String atualizarConfiguracaoNrAulasExperimentais(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "nrAulasExperimentaisAntesAlteracao", targetNamespace = "")
        Integer nrAulasExperimentaisAntesAlteracao,
        @WebParam(name = "valorNovo", targetNamespace = "")
        Integer valorNovo);

    /**
     * 
     * @param empresa
     * @param dia
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "obterAulasColetivasPorDiaSemana", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ObterAulasColetivasPorDiaSemana")
    @ResponseWrapper(localName = "obterAulasColetivasPorDiaSemanaResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ObterAulasColetivasPorDiaSemanaResponse")
    public String obterAulasColetivasPorDiaSemana(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa,
        @WebParam(name = "dia", targetNamespace = "")
        Integer dia);

    /**
     * 
     * @param dataDaAula
     * @param horarioTurma
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "obterAlunosDaAulaComAcesso", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ObterAlunosDaAulaComAcesso")
    @ResponseWrapper(localName = "obterAlunosDaAulaComAcessoResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ObterAlunosDaAulaComAcessoResponse")
    public String obterAlunosDaAulaComAcesso(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "horarioTurma", targetNamespace = "")
        Integer horarioTurma,
        @WebParam(name = "dataDaAula", targetNamespace = "")
        String dataDaAula);

    /**
     * 
     * @param cliente
     * @param dataDaAula
     * @param horarioTurma
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "existeReposicao", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ExisteReposicao")
    @ResponseWrapper(localName = "existeReposicaoResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ExisteReposicaoResponse")
    public String existeReposicao(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "horarioTurma", targetNamespace = "")
        Integer horarioTurma,
        @WebParam(name = "cliente", targetNamespace = "")
        Integer cliente,
        @WebParam(name = "dataDaAula", targetNamespace = "")
        String dataDaAula);

    /**
     * 
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "buscarUnidades", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.BuscarUnidades")
    @ResponseWrapper(localName = "buscarUnidadesResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.BuscarUnidadesResponse")
    public String buscarUnidades(
        @WebParam(name = "key", targetNamespace = "")
        String key);

    /**
     * 
     * @param horarioTurmaCodigo
     * @param data
     * @param key
     * @return
     *     returns servicos.integracao.zw.client.EventJSON
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "obterAulaSpivi", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ObterAulaSpivi")
    @ResponseWrapper(localName = "obterAulaSpiviResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ObterAulaSpiviResponse")
    public EventJSON obterAulaSpivi(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "horarioTurmaCodigo", targetNamespace = "")
        Integer horarioTurmaCodigo,
        @WebParam(name = "data", targetNamespace = "")
        String data);

}
