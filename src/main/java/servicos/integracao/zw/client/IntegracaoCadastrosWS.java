
package servicos.integracao.zw.client;

import java.util.List;
import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import javax.xml.bind.annotation.XmlSeeAlso;
import javax.xml.ws.RequestWrapper;
import javax.xml.ws.ResponseWrapper;


/**
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.2.6b21 
 * Generated source version: 2.1
 * 
 */
@WebService(name = "IntegracaoCadastrosWS", targetNamespace = "http://webservice.basico.comuns.negocio/")
@XmlSeeAlso({
    ObjectFactory.class
})
public interface IntegracaoCadastrosWS {


    /**
     * 
     * @param idEmpresa
     * @param nomePesquisar
     * @param key
     * @return
     *     returns java.lang.String
     * @throws Exception_Exception
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarClientesPeloNome", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarClientesPeloNome")
    @ResponseWrapper(localName = "consultarClientesPeloNomeResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarClientesPeloNomeResponse")
    public String consultarClientesPeloNome(
        @WebParam(name = "idEmpresa", targetNamespace = "")
        int idEmpresa,
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "nomePesquisar", targetNamespace = "")
        String nomePesquisar)
        throws Exception_Exception
    ;

    /**
     * 
     * @param codigo
     * @param imagem
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "atualizarFotoPerfilAluno", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.AtualizarFotoPerfilAluno")
    @ResponseWrapper(localName = "atualizarFotoPerfilAlunoResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.AtualizarFotoPerfilAlunoResponse")
    public String atualizarFotoPerfilAluno(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "codigo", targetNamespace = "")
        Integer codigo,
        @WebParam(name = "imagem", targetNamespace = "")
        String imagem);

    /**
     * 
     * @param senha
     * @param lembreteSenha
     * @param key
     * @param email
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "alterarSenhaUsuarioMovel", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.AlterarSenhaUsuarioMovel")
    @ResponseWrapper(localName = "alterarSenhaUsuarioMovelResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.AlterarSenhaUsuarioMovelResponse")
    public String alterarSenhaUsuarioMovel(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "email", targetNamespace = "")
        String email,
        @WebParam(name = "senha", targetNamespace = "")
        String senha,
        @WebParam(name = "lembreteSenha", targetNamespace = "")
        String lembreteSenha);

    /**
     * 
     * @param key
     * @return
     *     returns java.lang.String
     * @throws Exception_Exception
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarEstado", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarEstado")
    @ResponseWrapper(localName = "consultarEstadoResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarEstadoResponse")
    public String consultarEstado(
        @WebParam(name = "key", targetNamespace = "")
        String key)
        throws Exception_Exception
    ;

    /**
     * 
     * @param codigoEstado
     * @param key
     * @return
     *     returns java.lang.String
     * @throws Exception_Exception
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarCidade", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarCidade")
    @ResponseWrapper(localName = "consultarCidadeResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarCidadeResponse")
    public String consultarCidade(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "codigoEstado", targetNamespace = "")
        int codigoEstado)
        throws Exception_Exception
    ;

    /**
     * 
     * @param listaCupom
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarCupomDesconto", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarCupomDesconto")
    @ResponseWrapper(localName = "consultarCupomDescontoResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarCupomDescontoResponse")
    public String consultarCupomDesconto(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "listaCupom", targetNamespace = "")
        String listaCupom);

    /**
     * 
     * @param cpf
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarClienteNaRedeEmpresa", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarClienteNaRedeEmpresa")
    @ResponseWrapper(localName = "consultarClienteNaRedeEmpresaResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarClienteNaRedeEmpresaResponse")
    public String consultarClienteNaRedeEmpresa(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "cpf", targetNamespace = "")
        String cpf);

    /**
     * 
     * @param codigoEmpresaZW
     * @param cpf
     * @param key
     * @param email
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarCliente", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarCliente")
    @ResponseWrapper(localName = "consultarClienteResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarClienteResponse")
    public String consultarCliente(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "codigoEmpresaZW", targetNamespace = "")
        Integer codigoEmpresaZW,
        @WebParam(name = "email", targetNamespace = "")
        String email,
        @WebParam(name = "cpf", targetNamespace = "")
        String cpf);

    /**
     * 
     * @param telefone
     * @param codigoEmpresaZW
     * @param cpf
     * @param key
     * @param email
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarClienteComTelefone", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarClienteComTelefone")
    @ResponseWrapper(localName = "consultarClienteComTelefoneResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarClienteComTelefoneResponse")
    public String consultarClienteComTelefone(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "codigoEmpresaZW", targetNamespace = "")
        Integer codigoEmpresaZW,
        @WebParam(name = "email", targetNamespace = "")
        String email,
        @WebParam(name = "cpf", targetNamespace = "")
        String cpf,
        @WebParam(name = "telefone", targetNamespace = "")
        String telefone);

    /**
     * 
     * @param codigoCliente
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarFrequenciaAluno", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarFrequenciaAluno")
    @ResponseWrapper(localName = "consultarFrequenciaAlunoResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarFrequenciaAlunoResponse")
    public String consultarFrequenciaAluno(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "codigoCliente", targetNamespace = "")
        int codigoCliente);

    /**
     * 
     * @param chaveRede
     * @param senha
     * @param key
     * @param email
     * @return
     *     returns servicos.integracao.zw.client.ClienteWS
     * @throws ConsistirException_Exception
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "logarUsuarioMovel", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.LogarUsuarioMovel")
    @ResponseWrapper(localName = "logarUsuarioMovelResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.LogarUsuarioMovelResponse")
    public ClienteWS logarUsuarioMovel(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "email", targetNamespace = "")
        String email,
        @WebParam(name = "senha", targetNamespace = "")
        String senha,
        @WebParam(name = "chaveRede", targetNamespace = "")
        String chaveRede)
        throws ConsistirException_Exception
    ;

    /**
     * 
     * @param cliente
     * @param key
     * @return
     *     returns java.util.List<servicos.integracao.zw.client.ClienteMensagemWS>
     * @throws ConsistirException_Exception
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarMensagensCliente", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarMensagensCliente")
    @ResponseWrapper(localName = "consultarMensagensClienteResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarMensagensClienteResponse")
    public List<ClienteMensagemWS> consultarMensagensCliente(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "cliente", targetNamespace = "")
        int cliente)
        throws ConsistirException_Exception
    ;

    /**
     * 
     * @param incluirPersonais
     * @param todosProfessores
     * @param nome
     * @param empresa
     * @param key
     * @return
     *     returns java.lang.String
     * @throws Exception_Exception
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarColaboradoresTreino", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarColaboradoresTreino")
    @ResponseWrapper(localName = "consultarColaboradoresTreinoResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarColaboradoresTreinoResponse")
    public String consultarColaboradoresTreino(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa,
        @WebParam(name = "nome", targetNamespace = "")
        String nome,
        @WebParam(name = "incluirPersonais", targetNamespace = "")
        Boolean incluirPersonais,
        @WebParam(name = "todosProfessores", targetNamespace = "")
        Boolean todosProfessores)
        throws Exception_Exception
    ;

    /**
     * 
     * @param empresa
     * @param key
     * @return
     *     returns java.lang.String
     * @throws Exception_Exception
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarModalidadesEmpresa", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarModalidadesEmpresa")
    @ResponseWrapper(localName = "consultarModalidadesEmpresaResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarModalidadesEmpresaResponse")
    public String consultarModalidadesEmpresa(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa)
        throws Exception_Exception
    ;

    /**
     * 
     * @param key
     * @return
     *     returns java.lang.String
     * @throws Exception_Exception
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarModalidades", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarModalidades")
    @ResponseWrapper(localName = "consultarModalidadesResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarModalidadesResponse")
    public String consultarModalidades(
        @WebParam(name = "key", targetNamespace = "")
        String key)
        throws Exception_Exception
    ;

    /**
     * 
     * @param key
     * @return
     *     returns java.lang.String
     * @throws Exception_Exception
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarAmbientes", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarAmbientes")
    @ResponseWrapper(localName = "consultarAmbientesResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarAmbientesResponse")
    public String consultarAmbientes(
        @WebParam(name = "key", targetNamespace = "")
        String key)
        throws Exception_Exception
    ;

    /**
     * 
     * @param cpf
     * @param nome
     * @param empresa
     * @param key
     * @return
     *     returns java.lang.String
     * @throws Exception_Exception
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarClientePorNomeCpf", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarClientePorNomeCpf")
    @ResponseWrapper(localName = "consultarClientePorNomeCpfResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarClientePorNomeCpfResponse")
    public String consultarClientePorNomeCpf(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "nome", targetNamespace = "")
        String nome,
        @WebParam(name = "cpf", targetNamespace = "")
        String cpf,
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa)
        throws Exception_Exception
    ;

    /**
     * 
     * @param empresa
     * @param key
     * @param email
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "recuperarUsuarioMovel", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.RecuperarUsuarioMovel")
    @ResponseWrapper(localName = "recuperarUsuarioMovelResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.RecuperarUsuarioMovelResponse")
    public String recuperarUsuarioMovel(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "email", targetNamespace = "")
        String email,
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa);

    /**
     * 
     * @param cliente
     * @param registros
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarContratosCliente", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarContratosCliente")
    @ResponseWrapper(localName = "consultarContratosClienteResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarContratosClienteResponse")
    public String consultarContratosCliente(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "cliente", targetNamespace = "")
        int cliente,
        @WebParam(name = "registros", targetNamespace = "")
        int registros);

    /**
     * 
     * @param cliente
     * @param registros
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarProdutosCliente", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarProdutosCliente")
    @ResponseWrapper(localName = "consultarProdutosClienteResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarProdutosClienteResponse")
    public String consultarProdutosCliente(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "cliente", targetNamespace = "")
        int cliente,
        @WebParam(name = "registros", targetNamespace = "")
        int registros);

    /**
     * 
     * @param cliente
     * @param registros
     * @param emAberto
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarParcelasCliente", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarParcelasCliente")
    @ResponseWrapper(localName = "consultarParcelasClienteResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarParcelasClienteResponse")
    public String consultarParcelasCliente(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "cliente", targetNamespace = "")
        int cliente,
        @WebParam(name = "registros", targetNamespace = "")
        int registros,
        @WebParam(name = "emAberto", targetNamespace = "")
        boolean emAberto);

    /**
     * 
     * @param cliente
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarAutorizacaoCobranca", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarAutorizacaoCobranca")
    @ResponseWrapper(localName = "consultarAutorizacaoCobrancaResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarAutorizacaoCobrancaResponse")
    public String consultarAutorizacaoCobranca(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "cliente", targetNamespace = "")
        int cliente);

    /**
     * 
     * @param dataInicioAvencer
     * @param professor
     * @param dataFim
     * @param dataInicio
     * @param dataFimAvencer
     * @param empresa
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarDadosBITreino", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarDadosBITreino")
    @ResponseWrapper(localName = "consultarDadosBITreinoResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarDadosBITreinoResponse")
    public String consultarDadosBITreino(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "dataInicio", targetNamespace = "")
        String dataInicio,
        @WebParam(name = "dataFim", targetNamespace = "")
        String dataFim,
        @WebParam(name = "dataInicioAvencer", targetNamespace = "")
        String dataInicioAvencer,
        @WebParam(name = "dataFimAvencer", targetNamespace = "")
        String dataFimAvencer,
        @WebParam(name = "professor", targetNamespace = "")
        Integer professor,
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa);

    /**
     * 
     * @param professor
     * @param dataFim
     * @param naoRenovaram
     * @param dataInicio
     * @param empresa
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarClientesRenovaramOuNao", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarClientesRenovaramOuNao")
    @ResponseWrapper(localName = "consultarClientesRenovaramOuNaoResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarClientesRenovaramOuNaoResponse")
    public String consultarClientesRenovaramOuNao(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "dataInicio", targetNamespace = "")
        String dataInicio,
        @WebParam(name = "dataFim", targetNamespace = "")
        String dataFim,
        @WebParam(name = "professor", targetNamespace = "")
        Integer professor,
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa,
        @WebParam(name = "naoRenovaram", targetNamespace = "")
        String naoRenovaram);

    /**
     * 
     * @param modalidades
     * @param empresa
     * @param key
     * @return
     *     returns java.lang.String
     * @throws Exception_Exception
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarClientesAtivosForaTreino", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarClientesAtivosForaTreino")
    @ResponseWrapper(localName = "consultarClientesAtivosForaTreinoResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarClientesAtivosForaTreinoResponse")
    public String consultarClientesAtivosForaTreino(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa,
        @WebParam(name = "modalidades", targetNamespace = "")
        String modalidades)
        throws Exception_Exception
    ;

    /**
     * 
     * @param empresa
     * @param key
     * @return
     *     returns java.lang.String
     * @throws Exception_Exception
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarClientesForaTreino", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarClientesForaTreino")
    @ResponseWrapper(localName = "consultarClientesForaTreinoResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarClientesForaTreinoResponse")
    public String consultarClientesForaTreino(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa)
        throws Exception_Exception
    ;

    /**
     * 
     * @param codigoCliente
     * @param matricula
     * @param key
     * @return
     *     returns java.lang.String
     * @throws Exception_Exception
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "verificarUsuarioMovel", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.VerificarUsuarioMovel")
    @ResponseWrapper(localName = "verificarUsuarioMovelResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.VerificarUsuarioMovelResponse")
    public String verificarUsuarioMovel(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "codigoCliente", targetNamespace = "")
        Integer codigoCliente,
        @WebParam(name = "matricula", targetNamespace = "")
        Integer matricula)
        throws Exception_Exception
    ;

    /**
     * 
     * @param empresa
     * @param key
     * @return
     *     returns java.lang.String
     * @throws Exception_Exception
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarModalidadesAtivosForaTreino", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarModalidadesAtivosForaTreino")
    @ResponseWrapper(localName = "consultarModalidadesAtivosForaTreinoResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarModalidadesAtivosForaTreinoResponse")
    public String consultarModalidadesAtivosForaTreino(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa)
        throws Exception_Exception
    ;

    /**
     * 
     * @param empresa
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarNrAlunosAtivosForaTreino", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarNrAlunosAtivosForaTreino")
    @ResponseWrapper(localName = "consultarNrAlunosAtivosForaTreinoResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarNrAlunosAtivosForaTreinoResponse")
    public String consultarNrAlunosAtivosForaTreino(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa);

    /**
     * 
     * @param filter
     * @param key
     * @param codigos
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarAlunos", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarAlunos")
    @ResponseWrapper(localName = "consultarAlunosResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarAlunosResponse")
    public String consultarAlunos(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "codigos", targetNamespace = "")
        String codigos,
        @WebParam(name = "filter", targetNamespace = "")
        String filter);

    /**
     * 
     * @param professor
     * @param dataFim
     * @param dataInicio
     * @param empresa
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarAcessosDia", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarAcessosDia")
    @ResponseWrapper(localName = "consultarAcessosDiaResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarAcessosDiaResponse")
    public String consultarAcessosDia(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "dataInicio", targetNamespace = "")
        String dataInicio,
        @WebParam(name = "dataFim", targetNamespace = "")
        String dataFim,
        @WebParam(name = "professor", targetNamespace = "")
        Integer professor,
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa);

    /**
     * 
     * @param professor
     * @param dataFim
     * @param dataInicio
     * @param treino
     * @param empresa
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarAcessosListaDia", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarAcessosListaDia")
    @ResponseWrapper(localName = "consultarAcessosListaDiaResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarAcessosListaDiaResponse")
    public String consultarAcessosListaDia(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "dataInicio", targetNamespace = "")
        String dataInicio,
        @WebParam(name = "dataFim", targetNamespace = "")
        String dataFim,
        @WebParam(name = "professor", targetNamespace = "")
        Integer professor,
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa,
        @WebParam(name = "treino", targetNamespace = "")
        String treino);

    /**
     * 
     * @param nrMesesAnterior
     * @param dataInicio
     * @param empresa
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarDadosGame", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarDadosGame")
    @ResponseWrapper(localName = "consultarDadosGameResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarDadosGameResponse")
    public String consultarDadosGame(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "dataInicio", targetNamespace = "")
        String dataInicio,
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa,
        @WebParam(name = "nrMesesAnterior", targetNamespace = "")
        Integer nrMesesAnterior);

    /**
     * 
     * @param nrMesesAnterior
     * @param dataInicio
     * @param empresa
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarConsultoresGame", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarConsultoresGame")
    @ResponseWrapper(localName = "consultarConsultoresGameResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarConsultoresGameResponse")
    public String consultarConsultoresGame(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "dataInicio", targetNamespace = "")
        String dataInicio,
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa,
        @WebParam(name = "nrMesesAnterior", targetNamespace = "")
        Integer nrMesesAnterior);

    /**
     * 
     * @param nrMesesAnterior
     * @param dataInicio
     * @param empresa
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarDetalhesDF", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarDetalhesDF")
    @ResponseWrapper(localName = "consultarDetalhesDFResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarDetalhesDFResponse")
    public String consultarDetalhesDF(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "dataInicio", targetNamespace = "")
        String dataInicio,
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa,
        @WebParam(name = "nrMesesAnterior", targetNamespace = "")
        Integer nrMesesAnterior);

    /**
     * 
     * @param dataInicio
     * @param empresa
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "analiseVendaDuracao", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.AnaliseVendaDuracao")
    @ResponseWrapper(localName = "analiseVendaDuracaoResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.AnaliseVendaDuracaoResponse")
    public String analiseVendaDuracao(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "dataInicio", targetNamespace = "")
        String dataInicio,
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa);

    /**
     * 
     * @param dataInicio
     * @param empresa
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "obterVencimentosContratos", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ObterVencimentosContratos")
    @ResponseWrapper(localName = "obterVencimentosContratosResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ObterVencimentosContratosResponse")
    public String obterVencimentosContratos(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "dataInicio", targetNamespace = "")
        String dataInicio,
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa);

    /**
     * 
     * @param empresa
     * @param config
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "gravarConfiguracoesGame", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.GravarConfiguracoesGame")
    @ResponseWrapper(localName = "gravarConfiguracoesGameResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.GravarConfiguracoesGameResponse")
    public String gravarConfiguracoesGame(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa,
        @WebParam(name = "config", targetNamespace = "")
        String config);

    /**
     * 
     * @param empresa
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "obterConfiguracoesGame", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ObterConfiguracoesGame")
    @ResponseWrapper(localName = "obterConfiguracoesGameResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ObterConfiguracoesGameResponse")
    public String obterConfiguracoesGame(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa);

    /**
     * 
     * @param key
     * @return
     *     returns java.lang.String
     * @throws Exception_Exception
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarEmpresasSimples", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarEmpresasSimples")
    @ResponseWrapper(localName = "consultarEmpresasSimplesResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarEmpresasSimplesResponse")
    public String consultarEmpresasSimples(
        @WebParam(name = "key", targetNamespace = "")
        String key)
        throws Exception_Exception
    ;

    /**
     * 
     * @param empresa
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "atualizarDadosGerenciais", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.AtualizarDadosGerenciais")
    @ResponseWrapper(localName = "atualizarDadosGerenciaisResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.AtualizarDadosGerenciaisResponse")
    public String atualizarDadosGerenciais(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa);

    /**
     * 
     * @param empresa
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "atualizarDadosGerenciaisDia", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.AtualizarDadosGerenciaisDia")
    @ResponseWrapper(localName = "atualizarDadosGerenciaisDiaResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.AtualizarDadosGerenciaisDiaResponse")
    public String atualizarDadosGerenciaisDia(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa);

    /**
     * 
     * @param empresa
     * @param tipoDado
     * @param key
     * @param dataConsultar
     * @return
     *     returns java.lang.String
     * @throws Exception_Exception
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "obterValorVendas", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ObterValorVendas")
    @ResponseWrapper(localName = "obterValorVendasResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ObterValorVendasResponse")
    public String obterValorVendas(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa,
        @WebParam(name = "dataConsultar", targetNamespace = "")
        String dataConsultar,
        @WebParam(name = "tipoDado", targetNamespace = "")
        String tipoDado)
        throws Exception_Exception
    ;

    /**
     * 
     * @param empresa
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "obterDadosContratoPorDuracao", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ObterDadosContratoPorDuracao")
    @ResponseWrapper(localName = "obterDadosContratoPorDuracaoResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ObterDadosContratoPorDuracaoResponse")
    public String obterDadosContratoPorDuracao(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa);

    /**
     * 
     * @param nrMesesAnterior
     * @param dataInicio
     * @param empresa
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarFaturamentoDuracao", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarFaturamentoDuracao")
    @ResponseWrapper(localName = "consultarFaturamentoDuracaoResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarFaturamentoDuracaoResponse")
    public String consultarFaturamentoDuracao(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "dataInicio", targetNamespace = "")
        String dataInicio,
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa,
        @WebParam(name = "nrMesesAnterior", targetNamespace = "")
        Integer nrMesesAnterior);

    /**
     * 
     * @param nrMesesAnterior
     * @param dataInicio
     * @param empresa
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarMetasCRM", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarMetasCRM")
    @ResponseWrapper(localName = "consultarMetasCRMResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarMetasCRMResponse")
    public String consultarMetasCRM(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "dataInicio", targetNamespace = "")
        String dataInicio,
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa,
        @WebParam(name = "nrMesesAnterior", targetNamespace = "")
        Integer nrMesesAnterior);

    /**
     * 
     * @param codigoUsuarioZW
     * @param empresaZW
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarDadosSolicitarAtendimento", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarDadosSolicitarAtendimento")
    @ResponseWrapper(localName = "consultarDadosSolicitarAtendimentoResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarDadosSolicitarAtendimentoResponse")
    public String consultarDadosSolicitarAtendimento(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "empresaZW", targetNamespace = "")
        Integer empresaZW,
        @WebParam(name = "codigoUsuarioZW", targetNamespace = "")
        Integer codigoUsuarioZW);

    /**
     * 
     * @param matricula
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarAtestadoCliente", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarAtestadoCliente")
    @ResponseWrapper(localName = "consultarAtestadoClienteResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarAtestadoClienteResponse")
    public String consultarAtestadoCliente(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "matricula", targetNamespace = "")
        Integer matricula);

    /**
     * 
     * @param cpf
     * @param dataNascimento
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarMeioRecuperarLogin", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarMeioRecuperarLogin")
    @ResponseWrapper(localName = "consultarMeioRecuperarLoginResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarMeioRecuperarLoginResponse")
    public String consultarMeioRecuperarLogin(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "dataNascimento", targetNamespace = "")
        String dataNascimento,
        @WebParam(name = "cpf", targetNamespace = "")
        String cpf);

    /**
     * 
     * @param codUsuario
     * @param urlLogin
     * @param meioEnvio
     * @param key
     * @param username
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "recuperarLogin", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.RecuperarLogin")
    @ResponseWrapper(localName = "recuperarLoginResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.RecuperarLoginResponse")
    public String recuperarLogin(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "codUsuario", targetNamespace = "")
        Integer codUsuario,
        @WebParam(name = "username", targetNamespace = "")
        String username,
        @WebParam(name = "urlLogin", targetNamespace = "")
        String urlLogin,
        @WebParam(name = "meioEnvio", targetNamespace = "")
        String meioEnvio);

    /**
     * 
     * @param codUsuario
     * @param novaSenha
     * @param key
     * @param username
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "alterarSenhaUsuario", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.AlterarSenhaUsuario")
    @ResponseWrapper(localName = "alterarSenhaUsuarioResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.AlterarSenhaUsuarioResponse")
    public String alterarSenhaUsuario(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "codUsuario", targetNamespace = "")
        Integer codUsuario,
        @WebParam(name = "username", targetNamespace = "")
        String username,
        @WebParam(name = "novaSenha", targetNamespace = "")
        String novaSenha);

    /**
     * 
     * @param codUsuario
     * @param timeInMillisGeracaoURL
     * @param key
     * @return
     *     returns boolean
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "senhaAlteradaAposGeracaoLink", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.SenhaAlteradaAposGeracaoLink")
    @ResponseWrapper(localName = "senhaAlteradaAposGeracaoLinkResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.SenhaAlteradaAposGeracaoLinkResponse")
    public boolean senhaAlteradaAposGeracaoLink(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "codUsuario", targetNamespace = "")
        Integer codUsuario,
        @WebParam(name = "timeInMillisGeracaoURL", targetNamespace = "")
        String timeInMillisGeracaoURL);

    /**
     * 
     * @param campanha
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "salvarCampanha", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.SalvarCampanha")
    @ResponseWrapper(localName = "salvarCampanhaResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.SalvarCampanhaResponse")
    public String salvarCampanha(
        @WebParam(name = "campanha", targetNamespace = "")
        String campanha);

    /**
     * 
     * @param mes
     * @param empresa
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "atualizarDadosGerenciaisPorMes", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.AtualizarDadosGerenciaisPorMes")
    @ResponseWrapper(localName = "atualizarDadosGerenciaisPorMesResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.AtualizarDadosGerenciaisPorMesResponse")
    public String atualizarDadosGerenciaisPorMes(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa,
        @WebParam(name = "mes", targetNamespace = "")
        String mes);

    /**
     * 
     * @param matricula
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "nomenclaturaVendaCredito", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.NomenclaturaVendaCredito")
    @ResponseWrapper(localName = "nomenclaturaVendaCreditoResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.NomenclaturaVendaCreditoResponse")
    public String nomenclaturaVendaCredito(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "matricula", targetNamespace = "")
        String matricula);

    /**
     * 
     * @param codigoUsuario
     * @param mensagem
     * @param origem
     * @param titulo
     * @param key
     * @param codigoExterno
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "enviarMensagemAoUsuario", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.EnviarMensagemAoUsuario")
    @ResponseWrapper(localName = "enviarMensagemAoUsuarioResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.EnviarMensagemAoUsuarioResponse")
    public String enviarMensagemAoUsuario(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "codigoUsuario", targetNamespace = "")
        Integer codigoUsuario,
        @WebParam(name = "mensagem", targetNamespace = "")
        String mensagem,
        @WebParam(name = "titulo", targetNamespace = "")
        String titulo,
        @WebParam(name = "codigoExterno", targetNamespace = "")
        Integer codigoExterno,
        @WebParam(name = "origem", targetNamespace = "")
        String origem);

    /**
     * 
     * @param mensagem
     * @param origem
     * @param titulo
     * @param key
     * @param codigoExterno
     * @param username
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "enviarMensagemAoUsuarioUsername", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.EnviarMensagemAoUsuarioUsername")
    @ResponseWrapper(localName = "enviarMensagemAoUsuarioUsernameResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.EnviarMensagemAoUsuarioUsernameResponse")
    public String enviarMensagemAoUsuarioUsername(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "username", targetNamespace = "")
        String username,
        @WebParam(name = "mensagem", targetNamespace = "")
        String mensagem,
        @WebParam(name = "titulo", targetNamespace = "")
        String titulo,
        @WebParam(name = "codigoExterno", targetNamespace = "")
        Integer codigoExterno,
        @WebParam(name = "origem", targetNamespace = "")
        String origem);

    /**
     * 
     * @param codUsuario
     * @param mensagem
     * @param link
     * @param tipoNotificacao
     * @param key
     * @param username
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "enviarNotificacaoUsuario", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.EnviarNotificacaoUsuario")
    @ResponseWrapper(localName = "enviarNotificacaoUsuarioResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.EnviarNotificacaoUsuarioResponse")
    public String enviarNotificacaoUsuario(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "codUsuario", targetNamespace = "")
        Integer codUsuario,
        @WebParam(name = "username", targetNamespace = "")
        String username,
        @WebParam(name = "mensagem", targetNamespace = "")
        String mensagem,
        @WebParam(name = "link", targetNamespace = "")
        String link,
        @WebParam(name = "tipoNotificacao", targetNamespace = "")
        Integer tipoNotificacao);

    /**
     * 
     * @param key
     * @param email
     * @param token
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "alterarSenhaUsuarioEmail", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.AlterarSenhaUsuarioEmail")
    @ResponseWrapper(localName = "alterarSenhaUsuarioEmailResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.AlterarSenhaUsuarioEmailResponse")
    public String alterarSenhaUsuarioEmail(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "email", targetNamespace = "")
        String email,
        @WebParam(name = "token", targetNamespace = "")
        String token);

    /**
     * 
     * @param empresa
     * @param key
     * @return
     *     returns java.lang.String
     * @throws Exception_Exception
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarConsultores", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarConsultores")
    @ResponseWrapper(localName = "consultarConsultoresResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarConsultoresResponse")
    public String consultarConsultores(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "empresa", targetNamespace = "")
        int empresa)
        throws Exception_Exception
    ;

    /**
     * 
     * @param key
     * @param listaPessoas
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "listaClientesEstacionamentoSelfit", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ListaClientesEstacionamentoSelfit")
    @ResponseWrapper(localName = "listaClientesEstacionamentoSelfitResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ListaClientesEstacionamentoSelfitResponse")
    public String listaClientesEstacionamentoSelfit(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "listaPessoas", targetNamespace = "")
        String listaPessoas);

    /**
     * 
     * @param codacesso
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarCodigoAcesso", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarCodigoAcesso")
    @ResponseWrapper(localName = "consultarCodigoAcessoResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarCodigoAcessoResponse")
    public String consultarCodigoAcesso(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "codacesso", targetNamespace = "")
        Integer codacesso);

    /**
     * 
     * @param codacesso
     * @param codigoEmpresaZW
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarClientePorCodigoAcesso", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarClientePorCodigoAcesso")
    @ResponseWrapper(localName = "consultarClientePorCodigoAcessoResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarClientePorCodigoAcessoResponse")
    public String consultarClientePorCodigoAcesso(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "codigoEmpresaZW", targetNamespace = "")
        Integer codigoEmpresaZW,
        @WebParam(name = "codacesso", targetNamespace = "")
        String codacesso);

    /**
     * 
     * @param tipo
     * @param empresa
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "obterQuestionario", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ObterQuestionario")
    @ResponseWrapper(localName = "obterQuestionarioResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ObterQuestionarioResponse")
    public String obterQuestionario(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "tipo", targetNamespace = "")
        String tipo,
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa);

    /**
     * 
     * @param cliente
     * @param json
     * @param key
     * @param questionario
     * @param apenasValidar
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "responderQuestionario", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ResponderQuestionario")
    @ResponseWrapper(localName = "responderQuestionarioResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ResponderQuestionarioResponse")
    public String responderQuestionario(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "json", targetNamespace = "")
        String json,
        @WebParam(name = "questionario", targetNamespace = "")
        Integer questionario,
        @WebParam(name = "cliente", targetNamespace = "")
        Integer cliente,
        @WebParam(name = "apenasValidar", targetNamespace = "")
        String apenasValidar);

    /**
     * 
     * @param cliente
     * @param situacao
     * @param nomecliente
     * @param analitico
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "listaAlunoPontos", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ListaAlunoPontos")
    @ResponseWrapper(localName = "listaAlunoPontosResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ListaAlunoPontosResponse")
    public String listaAlunoPontos(
        @WebParam(name = "Key", targetNamespace = "")
        String key,
        @WebParam(name = "situacao", targetNamespace = "")
        String situacao,
        @WebParam(name = "analitico", targetNamespace = "")
        boolean analitico,
        @WebParam(name = "cliente", targetNamespace = "")
        Integer cliente,
        @WebParam(name = "nomecliente", targetNamespace = "")
        String nomecliente);

    /**
     * 
     * @param cliente
     * @param situacao
     * @param nomecliente
     * @param analitico
     * @param dataInicio
     * @param key
     * @param dataFinal
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "listaAlunoPontosData", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ListaAlunoPontosData")
    @ResponseWrapper(localName = "listaAlunoPontosDataResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ListaAlunoPontosDataResponse")
    public String listaAlunoPontosData(
        @WebParam(name = "Key", targetNamespace = "")
        String key,
        @WebParam(name = "situacao", targetNamespace = "")
        String situacao,
        @WebParam(name = "analitico", targetNamespace = "")
        boolean analitico,
        @WebParam(name = "cliente", targetNamespace = "")
        Integer cliente,
        @WebParam(name = "nomecliente", targetNamespace = "")
        String nomecliente,
        @WebParam(name = "dataInicio", targetNamespace = "")
        String dataInicio,
        @WebParam(name = "dataFinal", targetNamespace = "")
        String dataFinal);

    /**
     * 
     * @param empresa
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarOperacoesExcecoes", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarOperacoesExcecoes")
    @ResponseWrapper(localName = "consultarOperacoesExcecoesResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarOperacoesExcecoesResponse")
    public String consultarOperacoesExcecoes(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa);

    /**
     * 
     * @param empresa
     * @param lead
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "persistirLeadRD", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.PersistirLeadRD")
    @ResponseWrapper(localName = "persistirLeadRDResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.PersistirLeadRDResponse")
    public String persistirLeadRD(
        @WebParam(name = "lead", targetNamespace = "")
        String lead,
        @WebParam(name = "empresa", targetNamespace = "")
        int empresa,
        @WebParam(name = "key", targetNamespace = "")
        String key);

    /**
     * 
     * @param pessoa
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarParcelasVencidas", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarParcelasVencidas")
    @ResponseWrapper(localName = "consultarParcelasVencidasResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarParcelasVencidasResponse")
    public String consultarParcelasVencidas(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "pessoa", targetNamespace = "")
        int pessoa);

    /**
     * 
     * @param tipo
     * @param empresa
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarJustificativasOperacao", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarJustificativasOperacao")
    @ResponseWrapper(localName = "consultarJustificativasOperacaoResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarJustificativasOperacaoResponse")
    public String consultarJustificativasOperacao(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa,
        @WebParam(name = "tipo", targetNamespace = "")
        String tipo);

    /**
     * 
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "listaProdutosAtestado", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ListaProdutosAtestado")
    @ResponseWrapper(localName = "listaProdutosAtestadoResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ListaProdutosAtestadoResponse")
    public String listaProdutosAtestado(
        @WebParam(name = "key", targetNamespace = "")
        String key);

    /**
     * 
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "listaProdutosServicos", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ListaProdutosServicos")
    @ResponseWrapper(localName = "listaProdutosServicosResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ListaProdutosServicosResponse")
    public String listaProdutosServicos(
        @WebParam(name = "key", targetNamespace = "")
        String key);

    /**
     * 
     * @param json
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "lancarProdutoAtestado", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.LancarProdutoAtestado")
    @ResponseWrapper(localName = "lancarProdutoAtestadoResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.LancarProdutoAtestadoResponse")
    public String lancarProdutoAtestado(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "json", targetNamespace = "")
        String json);

    /**
     * 
     * @param produto
     * @param empresa
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "obterValorProdutoCfgEmpresa", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ObterValorProdutoCfgEmpresa")
    @ResponseWrapper(localName = "obterValorProdutoCfgEmpresaResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ObterValorProdutoCfgEmpresaResponse")
    public String obterValorProdutoCfgEmpresa(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "produto", targetNamespace = "")
        Integer produto,
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa);

    /**
     * 
     * @param cliente
     * @param produto
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "validarAlunoProdutoVigente", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ValidarAlunoProdutoVigente")
    @ResponseWrapper(localName = "validarAlunoProdutoVigenteResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ValidarAlunoProdutoVigenteResponse")
    public String validarAlunoProdutoVigente(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "cliente", targetNamespace = "")
        Integer cliente,
        @WebParam(name = "produto", targetNamespace = "")
        Integer produto);

    /**
     * 
     * @param cliente
     * @param produto
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "validarProdutoVigente", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ValidarProdutoVigente")
    @ResponseWrapper(localName = "validarProdutoVigenteResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ValidarProdutoVigenteResponse")
    public String validarProdutoVigente(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "cliente", targetNamespace = "")
        Integer cliente,
        @WebParam(name = "produto", targetNamespace = "")
        Integer produto);

    /**
     * 
     * @param cliente
     * @param produto
     * @param usuario
     * @param vencimento
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "lancarProdutoCliente", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.LancarProdutoCliente")
    @ResponseWrapper(localName = "lancarProdutoClienteResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.LancarProdutoClienteResponse")
    public String lancarProdutoCliente(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "cliente", targetNamespace = "")
        Integer cliente,
        @WebParam(name = "usuario", targetNamespace = "")
        Integer usuario,
        @WebParam(name = "produto", targetNamespace = "")
        Integer produto,
        @WebParam(name = "vencimento", targetNamespace = "")
        String vencimento);

    /**
     * 
     * @param contrato
     * @param tipoOperacao
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarDadosOperacaoContrato", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarDadosOperacaoContrato")
    @ResponseWrapper(localName = "consultarDadosOperacaoContratoResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarDadosOperacaoContratoResponse")
    public String consultarDadosOperacaoContrato(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "contrato", targetNamespace = "")
        Integer contrato,
        @WebParam(name = "tipoOperacao", targetNamespace = "")
        String tipoOperacao);

    /**
     * 
     * @param produto
     * @param justificativa
     * @param contrato
     * @param dataInicio
     * @param tipoOperacao
     * @param key
     * @param dataFinal
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "validarDadosOperacaoContrato", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ValidarDadosOperacaoContrato")
    @ResponseWrapper(localName = "validarDadosOperacaoContratoResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ValidarDadosOperacaoContratoResponse")
    public String validarDadosOperacaoContrato(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "contrato", targetNamespace = "")
        Integer contrato,
        @WebParam(name = "tipoOperacao", targetNamespace = "")
        String tipoOperacao,
        @WebParam(name = "dataInicio", targetNamespace = "")
        String dataInicio,
        @WebParam(name = "dataFinal", targetNamespace = "")
        String dataFinal,
        @WebParam(name = "produto", targetNamespace = "")
        Integer produto,
        @WebParam(name = "justificativa", targetNamespace = "")
        Integer justificativa);

    /**
     * 
     * @param obs
     * @param produto
     * @param justificativa
     * @param contrato
     * @param dataInicio
     * @param origemSistema
     * @param tipoOperacao
     * @param key
     * @param dataFinal
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "gravarDadosOperacaoContrato", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.GravarDadosOperacaoContrato")
    @ResponseWrapper(localName = "gravarDadosOperacaoContratoResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.GravarDadosOperacaoContratoResponse")
    public String gravarDadosOperacaoContrato(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "contrato", targetNamespace = "")
        Integer contrato,
        @WebParam(name = "tipoOperacao", targetNamespace = "")
        String tipoOperacao,
        @WebParam(name = "dataInicio", targetNamespace = "")
        String dataInicio,
        @WebParam(name = "dataFinal", targetNamespace = "")
        String dataFinal,
        @WebParam(name = "produto", targetNamespace = "")
        Integer produto,
        @WebParam(name = "justificativa", targetNamespace = "")
        Integer justificativa,
        @WebParam(name = "obs", targetNamespace = "")
        String obs,
        @WebParam(name = "origemSistema", targetNamespace = "")
        Integer origemSistema);

    /**
     * 
     * @param convidado
     * @param convidou
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "lancarConvite", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.LancarConvite")
    @ResponseWrapper(localName = "lancarConviteResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.LancarConviteResponse")
    public String lancarConvite(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "convidou", targetNamespace = "")
        Integer convidou,
        @WebParam(name = "convidado", targetNamespace = "")
        Integer convidado);

    /**
     * 
     * @param convidou
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "validarConvites", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ValidarConvites")
    @ResponseWrapper(localName = "validarConvitesResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ValidarConvitesResponse")
    public String validarConvites(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "convidou", targetNamespace = "")
        Integer convidou);

    /**
     * 
     * @param totem
     * @param empresa
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "configuracaoTotem", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConfiguracaoTotem")
    @ResponseWrapper(localName = "configuracaoTotemResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConfiguracaoTotemResponse")
    public String configuracaoTotem(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa,
        @WebParam(name = "totem", targetNamespace = "")
        String totem);

    /**
     * 
     * @param matricula
     * @param key
     * @return
     *     returns servicos.integracao.zw.client.ClienteWS
     * @throws ConsistirException_Exception
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarDadosCliente", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarDadosCliente")
    @ResponseWrapper(localName = "consultarDadosClienteResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarDadosClienteResponse")
    public ClienteWS consultarDadosCliente(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "matricula", targetNamespace = "")
        String matricula)
        throws ConsistirException_Exception
    ;

    /**
     * 
     * @param contrato
     * @param origemSistema
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "retornoTrancamento", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.RetornoTrancamento")
    @ResponseWrapper(localName = "retornoTrancamentoResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.RetornoTrancamentoResponse")
    public String retornoTrancamento(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "contrato", targetNamespace = "")
        int contrato,
        @WebParam(name = "origemSistema", targetNamespace = "")
        Integer origemSistema);

    /**
     * 
     * @param codigo
     * @param tipo
     * @param excluir
     * @param complemento
     * @param numero
     * @param endereco
     * @param enderecoCorrenspondencia
     * @param ctx
     * @param bairro
     * @param matricula
     * @param origemSistema
     * @param cep
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "alterarEndereco", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.AlterarEndereco")
    @ResponseWrapper(localName = "alterarEnderecoResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.AlterarEnderecoResponse")
    public String alterarEndereco(
        @WebParam(name = "ctx", targetNamespace = "")
        String ctx,
        @WebParam(name = "matricula", targetNamespace = "")
        String matricula,
        @WebParam(name = "codigo", targetNamespace = "")
        Integer codigo,
        @WebParam(name = "tipo", targetNamespace = "")
        String tipo,
        @WebParam(name = "cep", targetNamespace = "")
        String cep,
        @WebParam(name = "bairro", targetNamespace = "")
        String bairro,
        @WebParam(name = "numero", targetNamespace = "")
        String numero,
        @WebParam(name = "complemento", targetNamespace = "")
        String complemento,
        @WebParam(name = "endereco", targetNamespace = "")
        String endereco,
        @WebParam(name = "enderecoCorrenspondencia", targetNamespace = "")
        boolean enderecoCorrenspondencia,
        @WebParam(name = "excluir", targetNamespace = "")
        boolean excluir,
        @WebParam(name = "origemSistema", targetNamespace = "")
        Integer origemSistema);

    /**
     * 
     * @param codigo
     * @param excluir
     * @param ctx
     * @param matricula
     * @param emailCorrespondencia
     * @param origemSistema
     * @param email
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "alterarEmail", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.AlterarEmail")
    @ResponseWrapper(localName = "alterarEmailResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.AlterarEmailResponse")
    public String alterarEmail(
        @WebParam(name = "ctx", targetNamespace = "")
        String ctx,
        @WebParam(name = "matricula", targetNamespace = "")
        String matricula,
        @WebParam(name = "codigo", targetNamespace = "")
        Integer codigo,
        @WebParam(name = "email", targetNamespace = "")
        String email,
        @WebParam(name = "emailCorrespondencia", targetNamespace = "")
        boolean emailCorrespondencia,
        @WebParam(name = "excluir", targetNamespace = "")
        boolean excluir,
        @WebParam(name = "origemSistema", targetNamespace = "")
        Integer origemSistema);

    /**
     * 
     * @param codigo
     * @param tipo
     * @param excluir
     * @param numero
     * @param ctx
     * @param matricula
     * @param origemSistema
     * @param descricao
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "alterarTelefone", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.AlterarTelefone")
    @ResponseWrapper(localName = "alterarTelefoneResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.AlterarTelefoneResponse")
    public String alterarTelefone(
        @WebParam(name = "ctx", targetNamespace = "")
        String ctx,
        @WebParam(name = "matricula", targetNamespace = "")
        String matricula,
        @WebParam(name = "codigo", targetNamespace = "")
        Integer codigo,
        @WebParam(name = "tipo", targetNamespace = "")
        String tipo,
        @WebParam(name = "numero", targetNamespace = "")
        String numero,
        @WebParam(name = "descricao", targetNamespace = "")
        String descricao,
        @WebParam(name = "excluir", targetNamespace = "")
        boolean excluir,
        @WebParam(name = "origemSistema", targetNamespace = "")
        Integer origemSistema);

    /**
     * 
     * @param venda
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarParcelaVenda", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarParcelaVenda")
    @ResponseWrapper(localName = "consultarParcelaVendaResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarParcelaVendaResponse")
    public String consultarParcelaVenda(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "venda", targetNamespace = "")
        int venda);

    /**
     * 
     * @param analitico
     * @param matriculacliente
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "listaAlunoPontosApp", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ListaAlunoPontosApp")
    @ResponseWrapper(localName = "listaAlunoPontosAppResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ListaAlunoPontosAppResponse")
    public String listaAlunoPontosApp(
        @WebParam(name = "Key", targetNamespace = "")
        String key,
        @WebParam(name = "matriculacliente", targetNamespace = "")
        String matriculacliente,
        @WebParam(name = "analitico", targetNamespace = "")
        boolean analitico);

    /**
     * 
     * @param cliente
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarContratosRenovar", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarContratosRenovar")
    @ResponseWrapper(localName = "consultarContratosRenovarResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarContratosRenovarResponse")
    public String consultarContratosRenovar(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "cliente", targetNamespace = "")
        int cliente);

    /**
     * 
     * @param contrato
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarPrimeiraParcelaContrato", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarPrimeiraParcelaContrato")
    @ResponseWrapper(localName = "consultarPrimeiraParcelaContratoResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarPrimeiraParcelaContratoResponse")
    public String consultarPrimeiraParcelaContrato(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "contrato", targetNamespace = "")
        int contrato);

    /**
     * 
     * @param contrato
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "obterContratoOperacao", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ObterContratoOperacao")
    @ResponseWrapper(localName = "obterContratoOperacaoResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ObterContratoOperacaoResponse")
    public String obterContratoOperacao(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "contrato", targetNamespace = "")
        int contrato);

    /**
     * 
     * @param imagem
     * @param contrato
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "incluirAssinaturaDigital", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.IncluirAssinaturaDigital")
    @ResponseWrapper(localName = "incluirAssinaturaDigitalResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.IncluirAssinaturaDigitalResponse")
    public String incluirAssinaturaDigital(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "contrato", targetNamespace = "")
        Integer contrato,
        @WebParam(name = "imagem", targetNamespace = "")
        String imagem);

    /**
     * 
     * @param consultaSQL
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultaGenerica", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultaGenerica")
    @ResponseWrapper(localName = "consultaGenericaResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultaGenericaResponse")
    public String consultaGenerica(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "consultaSQL", targetNamespace = "")
        String consultaSQL);

    /**
     * 
     * @param pagina
     * @param consultaSQL
     * @param itensPorPagina
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultaGenericaPaginada", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultaGenericaPaginada")
    @ResponseWrapper(localName = "consultaGenericaPaginadaResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultaGenericaPaginadaResponse")
    public String consultaGenericaPaginada(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "consultaSQL", targetNamespace = "")
        String consultaSQL,
        @WebParam(name = "pagina", targetNamespace = "")
        Integer pagina,
        @WebParam(name = "itensPorPagina", targetNamespace = "")
        Integer itensPorPagina);

    /**
     * 
     * @param nome
     * @param key
     * @param email
     * @param token
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "enviarEmailTokenApp", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.EnviarEmailTokenApp")
    @ResponseWrapper(localName = "enviarEmailTokenAppResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.EnviarEmailTokenAppResponse")
    public String enviarEmailTokenApp(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "nome", targetNamespace = "")
        String nome,
        @WebParam(name = "email", targetNamespace = "")
        String email,
        @WebParam(name = "token", targetNamespace = "")
        String token);

    /**
     * 
     * @param chaveAcesso
     * @param empresa
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "obterEnderecos", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ObterEnderecos")
    @ResponseWrapper(localName = "obterEnderecosResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ObterEnderecosResponse")
    public String obterEnderecos(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa,
        @WebParam(name = "chaveAcesso", targetNamespace = "")
        String chaveAcesso);

    /**
     * 
     * @param telefone
     * @param codEmpresa
     * @param key
     * @param codCliente
     * @param email
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "enviarCodigoVerificacao", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.EnviarCodigoVerificacao")
    @ResponseWrapper(localName = "enviarCodigoVerificacaoResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.EnviarCodigoVerificacaoResponse")
    public String enviarCodigoVerificacao(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "codEmpresa", targetNamespace = "")
        Integer codEmpresa,
        @WebParam(name = "codCliente", targetNamespace = "")
        Integer codCliente,
        @WebParam(name = "telefone", targetNamespace = "")
        String telefone,
        @WebParam(name = "email", targetNamespace = "")
        String email);

    /**
     * 
     * @param codigoVerificacao
     * @param key
     * @param codCliente
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "validarCodigoVerificacao", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ValidarCodigoVerificacao")
    @ResponseWrapper(localName = "validarCodigoVerificacaoResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ValidarCodigoVerificacaoResponse")
    public String validarCodigoVerificacao(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "codCliente", targetNamespace = "")
        Integer codCliente,
        @WebParam(name = "codigoVerificacao", targetNamespace = "")
        String codigoVerificacao);

    /**
     * 
     * @param empresa
     * @param lead
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "persistirLeadBuzz", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.PersistirLeadBuzz")
    @ResponseWrapper(localName = "persistirLeadBuzzResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.PersistirLeadBuzzResponse")
    public String persistirLeadBuzz(
        @WebParam(name = "lead", targetNamespace = "")
        String lead,
        @WebParam(name = "empresa", targetNamespace = "")
        int empresa,
        @WebParam(name = "key", targetNamespace = "")
        String key);

    /**
     * 
     * @param empresa
     * @param lead
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "persistirLeadGenerico", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.PersistirLeadGenerico")
    @ResponseWrapper(localName = "persistirLeadGenericoResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.PersistirLeadGenericoResponse")
    public String persistirLeadGenerico(
        @WebParam(name = "lead", targetNamespace = "")
        String lead,
        @WebParam(name = "empresa", targetNamespace = "")
        int empresa,
        @WebParam(name = "key", targetNamespace = "")
        String key);

    /**
     * 
     * @param empresa
     * @param lead
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "verificarStatusLeadBuzz", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.VerificarStatusLeadBuzz")
    @ResponseWrapper(localName = "verificarStatusLeadBuzzResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.VerificarStatusLeadBuzzResponse")
    public String verificarStatusLeadBuzz(
        @WebParam(name = "lead", targetNamespace = "")
        String lead,
        @WebParam(name = "empresa", targetNamespace = "")
        int empresa,
        @WebParam(name = "key", targetNamespace = "")
        String key);

    /**
     * 
     * @param cliente
     * @param produto
     * @param valor
     * @param usuario
     * @param vencimento
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "lancarProdutoClienteVAlor", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.LancarProdutoClienteVAlor")
    @ResponseWrapper(localName = "lancarProdutoClienteVAlorResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.LancarProdutoClienteVAlorResponse")
    public String lancarProdutoClienteVAlor(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "cliente", targetNamespace = "")
        Integer cliente,
        @WebParam(name = "usuario", targetNamespace = "")
        Integer usuario,
        @WebParam(name = "produto", targetNamespace = "")
        Integer produto,
        @WebParam(name = "vencimento", targetNamespace = "")
        String vencimento,
        @WebParam(name = "valor", targetNamespace = "")
        Double valor);

    /**
     * 
     * @param json
     * @param empresa
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "persistirClienteJSON", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.PersistirClienteJSON")
    @ResponseWrapper(localName = "persistirClienteJSONResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.PersistirClienteJSONResponse")
    public String persistirClienteJSON(
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa,
        @WebParam(name = "json", targetNamespace = "")
        String json,
        @WebParam(name = "key", targetNamespace = "")
        String key);

    /**
     * 
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "categorias", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.Categorias")
    @ResponseWrapper(localName = "categoriasResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.CategoriasResponse")
    public String categorias(
        @WebParam(name = "key", targetNamespace = "")
        String key);

    /**
     * 
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "profissoes", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.Profissoes")
    @ResponseWrapper(localName = "profissoesResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ProfissoesResponse")
    public String profissoes(
        @WebParam(name = "key", targetNamespace = "")
        String key);

    /**
     * 
     * @param cpf
     * @param imagem
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "atualizarFotoPerfilAlunoCPF", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.AtualizarFotoPerfilAlunoCPF")
    @ResponseWrapper(localName = "atualizarFotoPerfilAlunoCPFResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.AtualizarFotoPerfilAlunoCPFResponse")
    public String atualizarFotoPerfilAlunoCPF(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "cpf", targetNamespace = "")
        String cpf,
        @WebParam(name = "imagem", targetNamespace = "")
        String imagem);

    /**
     * 
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "classificacoes", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.Classificacoes")
    @ResponseWrapper(localName = "classificacoesResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ClassificacoesResponse")
    public String classificacoes(
        @WebParam(name = "key", targetNamespace = "")
        String key);

    /**
     * 
     * @param matricula
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarParcelasVencidasSESC", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarParcelasVencidasSESC")
    @ResponseWrapper(localName = "consultarParcelasVencidasSESCResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarParcelasVencidasSESCResponse")
    public String consultarParcelasVencidasSESC(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "matricula", targetNamespace = "")
        String matricula);

    /**
     * 
     * @param inicio
     * @param fim
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarResumoPeriodo", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarResumoPeriodo")
    @ResponseWrapper(localName = "consultarResumoPeriodoResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarResumoPeriodoResponse")
    public String consultarResumoPeriodo(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "inicio", targetNamespace = "")
        String inicio,
        @WebParam(name = "fim", targetNamespace = "")
        String fim);

    /**
     * 
     * @param dataInicial
     * @param key
     * @param codCliente
     * @param dataFinal
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarQuantidadeAcessosClientesAgrupadosDia", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarQuantidadeAcessosClientesAgrupadosDia")
    @ResponseWrapper(localName = "consultarQuantidadeAcessosClientesAgrupadosDiaResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarQuantidadeAcessosClientesAgrupadosDiaResponse")
    public String consultarQuantidadeAcessosClientesAgrupadosDia(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "codCliente", targetNamespace = "")
        Integer codCliente,
        @WebParam(name = "dataInicial", targetNamespace = "")
        Long dataInicial,
        @WebParam(name = "dataFinal", targetNamespace = "")
        Long dataFinal);

    /**
     * 
     * @param json
     * @param empresa
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "persistirContratoJSON", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.PersistirContratoJSON")
    @ResponseWrapper(localName = "persistirContratoJSONResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.PersistirContratoJSONResponse")
    public String persistirContratoJSON(
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa,
        @WebParam(name = "json", targetNamespace = "")
        String json,
        @WebParam(name = "key", targetNamespace = "")
        String key);

    /**
     * 
     * @param json
     * @param empresa
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "persistirPagamentosContratoJSON", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.PersistirPagamentosContratoJSON")
    @ResponseWrapper(localName = "persistirPagamentosContratoJSONResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.PersistirPagamentosContratoJSONResponse")
    public String persistirPagamentosContratoJSON(
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa,
        @WebParam(name = "json", targetNamespace = "")
        String json,
        @WebParam(name = "key", targetNamespace = "")
        String key);

    /**
     * 
     * @param json
     * @param empresa
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "persistirOperacoesContratoJSON", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.PersistirOperacoesContratoJSON")
    @ResponseWrapper(localName = "persistirOperacoesContratoJSONResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.PersistirOperacoesContratoJSONResponse")
    public String persistirOperacoesContratoJSON(
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa,
        @WebParam(name = "json", targetNamespace = "")
        String json,
        @WebParam(name = "key", targetNamespace = "")
        String key);

    /**
     * 
     * @param json
     * @param empresa
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "persistirCancelarContratoJSON", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.PersistirCancelarContratoJSON")
    @ResponseWrapper(localName = "persistirCancelarContratoJSONResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.PersistirCancelarContratoJSONResponse")
    public String persistirCancelarContratoJSON(
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa,
        @WebParam(name = "json", targetNamespace = "")
        String json,
        @WebParam(name = "key", targetNamespace = "")
        String key);

    /**
     * 
     * @param json
     * @param empresa
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "persistirAcessosClienteJSON", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.PersistirAcessosClienteJSON")
    @ResponseWrapper(localName = "persistirAcessosClienteJSONResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.PersistirAcessosClienteJSONResponse")
    public String persistirAcessosClienteJSON(
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa,
        @WebParam(name = "json", targetNamespace = "")
        String json,
        @WebParam(name = "key", targetNamespace = "")
        String key);

    /**
     * 
     * @param json
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "persistirLogAuditoria", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.PersistirLogAuditoria")
    @ResponseWrapper(localName = "persistirLogAuditoriaResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.PersistirLogAuditoriaResponse")
    public String persistirLogAuditoria(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "json", targetNamespace = "")
        String json);

    /**
     * 
     * @param pessoa
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarParcelasEmAberto", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarParcelasEmAberto")
    @ResponseWrapper(localName = "consultarParcelasEmAbertoResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarParcelasEmAbertoResponse")
    public String consultarParcelasEmAberto(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "pessoa", targetNamespace = "")
        int pessoa);

    /**
     * 
     * @param recno
     * @param titulo
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "gravarBaixaProtheus", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.GravarBaixaProtheus")
    @ResponseWrapper(localName = "gravarBaixaProtheusResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.GravarBaixaProtheusResponse")
    public String gravarBaixaProtheus(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "recno", targetNamespace = "")
        String recno,
        @WebParam(name = "titulo", targetNamespace = "")
        String titulo);

    /**
     * 
     * @param recno
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "liberarCobrancaBaixaProtheus", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.LiberarCobrancaBaixaProtheus")
    @ResponseWrapper(localName = "liberarCobrancaBaixaProtheusResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.LiberarCobrancaBaixaProtheusResponse")
    public String liberarCobrancaBaixaProtheus(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "recno", targetNamespace = "")
        String recno);

    /**
     * 
     * @param codEmpresa
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarInfoFinanceiras", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarInfoFinanceiras")
    @ResponseWrapper(localName = "consultarInfoFinanceirasResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarInfoFinanceirasResponse")
    public String consultarInfoFinanceiras(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "codEmpresa", targetNamespace = "")
        Integer codEmpresa);

    /**
     * 
     * @param dataVencimento
     * @param key
     * @param codigoParcela
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "downloadBoleto", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.DownloadBoleto")
    @ResponseWrapper(localName = "downloadBoletoResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.DownloadBoletoResponse")
    public String downloadBoleto(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "codigoParcela", targetNamespace = "")
        Integer codigoParcela,
        @WebParam(name = "dataVencimento", targetNamespace = "")
        String dataVencimento);

    /**
     * 
     * @param key
     * @param email
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "solicitacoesConcluidas", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.SolicitacoesConcluidas")
    @ResponseWrapper(localName = "solicitacoesConcluidasResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.SolicitacoesConcluidasResponse")
    public String solicitacoesConcluidas(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "email", targetNamespace = "")
        String email);

    /**
     * 
     * @param key
     * @param email
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "solicitacoesEmAberto", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.SolicitacoesEmAberto")
    @ResponseWrapper(localName = "solicitacoesEmAbertoResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.SolicitacoesEmAbertoResponse")
    public String solicitacoesEmAberto(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "email", targetNamespace = "")
        String email);

    /**
     * 
     * @param email
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarEstatisticaSolicitacao", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarEstatisticaSolicitacao")
    @ResponseWrapper(localName = "consultarEstatisticaSolicitacaoResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarEstatisticaSolicitacaoResponse")
    public String consultarEstatisticaSolicitacao(
        @WebParam(name = "email", targetNamespace = "")
        String email);

    /**
     * 
     * @param json
     * @param empresa
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "deletarClienteImportacaoJSON", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.DeletarClienteImportacaoJSON")
    @ResponseWrapper(localName = "deletarClienteImportacaoJSONResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.DeletarClienteImportacaoJSONResponse")
    public String deletarClienteImportacaoJSON(
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa,
        @WebParam(name = "json", targetNamespace = "")
        String json,
        @WebParam(name = "key", targetNamespace = "")
        String key);

    /**
     * 
     * @param json
     * @param empresa
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "deletarContratoImportacaoJSON", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.DeletarContratoImportacaoJSON")
    @ResponseWrapper(localName = "deletarContratoImportacaoJSONResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.DeletarContratoImportacaoJSONResponse")
    public String deletarContratoImportacaoJSON(
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa,
        @WebParam(name = "json", targetNamespace = "")
        String json,
        @WebParam(name = "key", targetNamespace = "")
        String key);

    /**
     * 
     * @param json
     * @param empresa
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "deletarPagamentosContratoImportacaoJSON", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.DeletarPagamentosContratoImportacaoJSON")
    @ResponseWrapper(localName = "deletarPagamentosContratoImportacaoJSONResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.DeletarPagamentosContratoImportacaoJSONResponse")
    public String deletarPagamentosContratoImportacaoJSON(
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa,
        @WebParam(name = "json", targetNamespace = "")
        String json,
        @WebParam(name = "key", targetNamespace = "")
        String key);

    /**
     * 
     * @param json
     * @param empresa
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "persistirProdutoJSON", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.PersistirProdutoJSON")
    @ResponseWrapper(localName = "persistirProdutoJSONResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.PersistirProdutoJSONResponse")
    public String persistirProdutoJSON(
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa,
        @WebParam(name = "json", targetNamespace = "")
        String json,
        @WebParam(name = "key", targetNamespace = "")
        String key);

    /**
     * 
     * @param json
     * @param empresa
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "persistirVendaAvulsaJSON", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.PersistirVendaAvulsaJSON")
    @ResponseWrapper(localName = "persistirVendaAvulsaJSONResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.PersistirVendaAvulsaJSONResponse")
    public String persistirVendaAvulsaJSON(
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa,
        @WebParam(name = "json", targetNamespace = "")
        String json,
        @WebParam(name = "key", targetNamespace = "")
        String key);

    /**
     * 
     * @param json
     * @param empresa
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "persistirPagamentosVendaAvulsaJSON", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.PersistirPagamentosVendaAvulsaJSON")
    @ResponseWrapper(localName = "persistirPagamentosVendaAvulsaJSONResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.PersistirPagamentosVendaAvulsaJSONResponse")
    public String persistirPagamentosVendaAvulsaJSON(
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa,
        @WebParam(name = "json", targetNamespace = "")
        String json,
        @WebParam(name = "key", targetNamespace = "")
        String key);

    /**
     * 
     * @param id
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "buscarEstatisticasMovidesk", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.BuscarEstatisticasMovidesk")
    @ResponseWrapper(localName = "buscarEstatisticasMovideskResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.BuscarEstatisticasMovideskResponse")
    public String buscarEstatisticasMovidesk(
        @WebParam(name = "id", targetNamespace = "")
        String id);

    /**
     * 
     * @param codigoHistorico
     * @param usuario
     * @param disliked
     * @param key
     * @param liked
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "marcarAvaliacaoFeedGestao", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.MarcarAvaliacaoFeedGestao")
    @ResponseWrapper(localName = "marcarAvaliacaoFeedGestaoResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.MarcarAvaliacaoFeedGestaoResponse")
    public String marcarAvaliacaoFeedGestao(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "codigoHistorico", targetNamespace = "")
        Integer codigoHistorico,
        @WebParam(name = "usuario", targetNamespace = "")
        Integer usuario,
        @WebParam(name = "liked", targetNamespace = "")
        boolean liked,
        @WebParam(name = "disliked", targetNamespace = "")
        boolean disliked);

    /**
     * 
     * @param codigoHistorico
     * @param codUsuario
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "marcarComoLidaFeedGestao", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.MarcarComoLidaFeedGestao")
    @ResponseWrapper(localName = "marcarComoLidaFeedGestaoResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.MarcarComoLidaFeedGestaoResponse")
    public String marcarComoLidaFeedGestao(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "codigoHistorico", targetNamespace = "")
        Integer codigoHistorico,
        @WebParam(name = "codUsuario", targetNamespace = "")
        Integer codUsuario);

    /**
     * 
     * @param codUsuario
     * @param empresa
     * @param key
     * @param perfil
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarFeedGestao", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarFeedGestao")
    @ResponseWrapper(localName = "consultarFeedGestaoResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarFeedGestaoResponse")
    public String consultarFeedGestao(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa,
        @WebParam(name = "codUsuario", targetNamespace = "")
        Integer codUsuario,
        @WebParam(name = "perfil", targetNamespace = "")
        Integer perfil);

    /**
     * 
     * @param localacesso
     * @param key
     * @return
     *     returns java.lang.String
     * @throws Exception_Exception
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarColetores", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarColetores")
    @ResponseWrapper(localName = "consultarColetoresResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarColetoresResponse")
    public String consultarColetores(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "localacesso", targetNamespace = "")
        Integer localacesso)
        throws Exception_Exception
    ;

    /**
     * 
     * @param unidade
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "urlFotoEmpresa", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.UrlFotoEmpresa")
    @ResponseWrapper(localName = "urlFotoEmpresaResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.UrlFotoEmpresaResponse")
    public String urlFotoEmpresa(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "unidade", targetNamespace = "")
        Integer unidade);

    /**
     * 
     * @param resposta
     * @param codigoNotificacao
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "gravarResposta", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.GravarResposta")
    @ResponseWrapper(localName = "gravarRespostaResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.GravarRespostaResponse")
    public String gravarResposta(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "codigoNotificacao", targetNamespace = "")
        String codigoNotificacao,
        @WebParam(name = "resposta", targetNamespace = "")
        String resposta);

    /**
     * 
     * @param matricula
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "marcarStatusBG", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.MarcarStatusBG")
    @ResponseWrapper(localName = "marcarStatusBGResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.MarcarStatusBGResponse")
    public String marcarStatusBG(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "matricula", targetNamespace = "")
        Integer matricula);

    /**
     * 
     * @param matriculaPesquisar
     * @param idEmpresa
     * @param key
     * @return
     *     returns java.lang.String
     * @throws Exception_Exception
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarClientesPelaMatricula", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarClientesPelaMatricula")
    @ResponseWrapper(localName = "consultarClientesPelaMatriculaResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarClientesPelaMatriculaResponse")
    public String consultarClientesPelaMatricula(
        @WebParam(name = "idEmpresa", targetNamespace = "")
        int idEmpresa,
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "matriculaPesquisar", targetNamespace = "")
        String matriculaPesquisar)
        throws Exception_Exception
    ;

    /**
     * 
     * @param json
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "persistirColaborador", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.PersistirColaborador")
    @ResponseWrapper(localName = "persistirColaboradorResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.PersistirColaboradorResponse")
    public String persistirColaborador(
        @WebParam(name = "json", targetNamespace = "")
        String json,
        @WebParam(name = "key", targetNamespace = "")
        String key);

    /**
     * 
     * @param idEmpresa
     * @param nomePesquisar
     * @param key
     * @return
     *     returns java.lang.String
     * @throws Exception_Exception
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarClientesPeloNomeNoLimits", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarClientesPeloNomeNoLimits")
    @ResponseWrapper(localName = "consultarClientesPeloNomeNoLimitsResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarClientesPeloNomeNoLimitsResponse")
    public String consultarClientesPeloNomeNoLimits(
        @WebParam(name = "idEmpresa", targetNamespace = "")
        int idEmpresa,
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "nomePesquisar", targetNamespace = "")
        String nomePesquisar)
        throws Exception_Exception
    ;

    /**
     * 
     * @param idEmpresa
     * @param nomePesquisar
     * @param key
     * @return
     *     returns java.lang.String
     * @throws Exception_Exception
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarProfessoresPeloNome", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarProfessoresPeloNome")
    @ResponseWrapper(localName = "consultarProfessoresPeloNomeResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarProfessoresPeloNomeResponse")
    public String consultarProfessoresPeloNome(
        @WebParam(name = "idEmpresa", targetNamespace = "")
        int idEmpresa,
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "nomePesquisar", targetNamespace = "")
        String nomePesquisar)
        throws Exception_Exception
    ;

    /**
     * 
     * @param matriculaPesquisar
     * @param key
     * @return
     *     returns java.lang.String
     * @throws Exception_Exception
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarClientePorMatriculaExternaImportacao", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarClientePorMatriculaExternaImportacao")
    @ResponseWrapper(localName = "consultarClientePorMatriculaExternaImportacaoResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarClientePorMatriculaExternaImportacaoResponse")
    public String consultarClientePorMatriculaExternaImportacao(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "matriculaPesquisar", targetNamespace = "")
        String matriculaPesquisar)
        throws Exception_Exception
    ;

    /**
     * 
     * @param matriculaPesquisar
     * @param idEmpresa
     * @param key
     * @return
     *     returns java.lang.String
     * @throws Exception_Exception
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarGrupoDeRiscoPelaMatricula", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarGrupoDeRiscoPelaMatricula")
    @ResponseWrapper(localName = "consultarGrupoDeRiscoPelaMatriculaResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarGrupoDeRiscoPelaMatriculaResponse")
    public String consultarGrupoDeRiscoPelaMatricula(
        @WebParam(name = "idEmpresa", targetNamespace = "")
        int idEmpresa,
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "matriculaPesquisar", targetNamespace = "")
        String matriculaPesquisar)
        throws Exception_Exception
    ;

    /**
     * 
     * @param email
     * @param key
     * @return
     *     returns java.lang.String
     * @throws Exception_Exception
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarClienteSinteticoPorEmailPessoa", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarClienteSinteticoPorEmailPessoa")
    @ResponseWrapper(localName = "consultarClienteSinteticoPorEmailPessoaResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarClienteSinteticoPorEmailPessoaResponse")
    public String consultarClienteSinteticoPorEmailPessoa(
        @WebParam(name = "email", targetNamespace = "")
        String email,
        @WebParam(name = "key", targetNamespace = "")
        String key)
        throws Exception_Exception
    ;

    /**
     * 
     * @param key
     * @return
     *     returns java.lang.String
     * @throws Exception_Exception
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarEmpresas", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarEmpresas")
    @ResponseWrapper(localName = "consultarEmpresasResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarEmpresasResponse")
    public String consultarEmpresas(
        @WebParam(name = "key", targetNamespace = "")
        String key)
        throws Exception_Exception
    ;

    /**
     * 
     * @param key
     * @return
     *     returns java.lang.String
     * @throws Exception_Exception
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarConfiguracaoENotas", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarConfiguracaoENotas")
    @ResponseWrapper(localName = "consultarConfiguracaoENotasResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarConfiguracaoENotasResponse")
    public String consultarConfiguracaoENotas(
        @WebParam(name = "key", targetNamespace = "")
        String key)
        throws Exception_Exception
    ;

    /**
     * 
     * @param empresa
     * @param key
     * @return
     *     returns java.lang.String
     * @throws Exception_Exception
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarLocaisAcesso", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarLocaisAcesso")
    @ResponseWrapper(localName = "consultarLocaisAcessoResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarLocaisAcessoResponse")
    public String consultarLocaisAcesso(
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa,
        @WebParam(name = "key", targetNamespace = "")
        String key)
        throws Exception_Exception
    ;

    /**
     * 
     * @return
     *     returns java.lang.String
     * @throws Exception_Exception
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "obterConfiguracaoEmailPadrao", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ObterConfiguracaoEmailPadrao")
    @ResponseWrapper(localName = "obterConfiguracaoEmailPadraoResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ObterConfiguracaoEmailPadraoResponse")
    public String obterConfiguracaoEmailPadrao()
        throws Exception_Exception
    ;

    /**
     * 
     * @param parametro
     * @param empresa
     * @param key
     * @return
     *     returns java.lang.String
     * @throws Exception_Exception
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarClientes", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarClientes")
    @ResponseWrapper(localName = "consultarClientesResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarClientesResponse")
    public String consultarClientes(
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa,
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "parametro", targetNamespace = "")
        String parametro)
        throws Exception_Exception
    ;

    /**
     * 
     * @param parametro
     * @param empresa
     * @param key
     * @return
     *     returns java.lang.String
     * @throws Exception_Exception
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarColaboradores", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarColaboradores")
    @ResponseWrapper(localName = "consultarColaboradoresResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarColaboradoresResponse")
    public String consultarColaboradores(
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa,
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "parametro", targetNamespace = "")
        String parametro)
        throws Exception_Exception
    ;

    /**
     * 
     * @param cpf
     * @param matricula
     * @param nome
     * @param empresa
     * @param key
     * @return
     *     returns java.lang.String
     * @throws Exception_Exception
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarClientesTreino", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarClientesTreino")
    @ResponseWrapper(localName = "consultarClientesTreinoResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarClientesTreinoResponse")
    public String consultarClientesTreino(
        @WebParam(name = "cpf", targetNamespace = "")
        String cpf,
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa,
        @WebParam(name = "matricula", targetNamespace = "")
        Integer matricula,
        @WebParam(name = "nome", targetNamespace = "")
        String nome)
        throws Exception_Exception
    ;

    /**
     * 
     * @param codigoCliente
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "obterCliente", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ObterCliente")
    @ResponseWrapper(localName = "obterClienteResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ObterClienteResponse")
    public String obterCliente(
        @WebParam(name = "codigoCliente", targetNamespace = "")
        Integer codigoCliente,
        @WebParam(name = "key", targetNamespace = "")
        String key);

    /**
     * 
     * @param codigoCliente
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarClienteSintetico", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarClienteSintetico")
    @ResponseWrapper(localName = "consultarClienteSinteticoResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarClienteSinteticoResponse")
    public String consultarClienteSintetico(
        @WebParam(name = "codigoCliente", targetNamespace = "")
        Integer codigoCliente,
        @WebParam(name = "key", targetNamespace = "")
        String key);

    /**
     * 
     * @param senha
     * @param nomeEmpresa
     * @param codigoCliente
     * @param urlAppEmail
     * @param key
     * @param username
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "gerarUsuarioMovelAluno", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.GerarUsuarioMovelAluno")
    @ResponseWrapper(localName = "gerarUsuarioMovelAlunoResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.GerarUsuarioMovelAlunoResponse")
    public String gerarUsuarioMovelAluno(
        @WebParam(name = "codigoCliente", targetNamespace = "")
        Integer codigoCliente,
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "senha", targetNamespace = "")
        String senha,
        @WebParam(name = "username", targetNamespace = "")
        String username,
        @WebParam(name = "urlAppEmail", targetNamespace = "")
        String urlAppEmail,
        @WebParam(name = "nomeEmpresa", targetNamespace = "")
        String nomeEmpresa);

    /**
     * 
     * @param senha
     * @param codigoCliente
     * @param urlAppEmail
     * @param key
     * @param nomeApp
     * @param username
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "alterarUsuarioMovelAlunoNovo", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.AlterarUsuarioMovelAlunoNovo")
    @ResponseWrapper(localName = "alterarUsuarioMovelAlunoNovoResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.AlterarUsuarioMovelAlunoNovoResponse")
    public String alterarUsuarioMovelAlunoNovo(
        @WebParam(name = "codigoCliente", targetNamespace = "")
        Integer codigoCliente,
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "senha", targetNamespace = "")
        String senha,
        @WebParam(name = "username", targetNamespace = "")
        String username,
        @WebParam(name = "nomeApp", targetNamespace = "")
        String nomeApp,
        @WebParam(name = "urlAppEmail", targetNamespace = "")
        String urlAppEmail);

    /**
     * 
     * @param senha
     * @param codigoCliente
     * @param urlAppEmail
     * @param key
     * @param nomeApp
     * @param username
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "alterarUsuarioMovelAluno", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.AlterarUsuarioMovelAluno")
    @ResponseWrapper(localName = "alterarUsuarioMovelAlunoResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.AlterarUsuarioMovelAlunoResponse")
    public String alterarUsuarioMovelAluno(
        @WebParam(name = "codigoCliente", targetNamespace = "")
        Integer codigoCliente,
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "senha", targetNamespace = "")
        String senha,
        @WebParam(name = "username", targetNamespace = "")
        String username,
        @WebParam(name = "nomeApp", targetNamespace = "")
        String nomeApp,
        @WebParam(name = "urlAppEmail", targetNamespace = "")
        String urlAppEmail);

    /**
     * 
     * @param texto
     * @param codigoColaborador
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "enviarEmailColaborador", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.EnviarEmailColaborador")
    @ResponseWrapper(localName = "enviarEmailColaboradorResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.EnviarEmailColaboradorResponse")
    public String enviarEmailColaborador(
        @WebParam(name = "codigoColaborador", targetNamespace = "")
        Integer codigoColaborador,
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "texto", targetNamespace = "")
        String texto);

    /**
     * 
     * @param senha
     * @param urlAppEmail
     * @param codigoColaborador
     * @param key
     * @param nomeApp
     * @param username
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "alterarUsuarioMovelProfessor", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.AlterarUsuarioMovelProfessor")
    @ResponseWrapper(localName = "alterarUsuarioMovelProfessorResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.AlterarUsuarioMovelProfessorResponse")
    public String alterarUsuarioMovelProfessor(
        @WebParam(name = "codigoColaborador", targetNamespace = "")
        Integer codigoColaborador,
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "senha", targetNamespace = "")
        String senha,
        @WebParam(name = "username", targetNamespace = "")
        String username,
        @WebParam(name = "nomeApp", targetNamespace = "")
        String nomeApp,
        @WebParam(name = "urlAppEmail", targetNamespace = "")
        String urlAppEmail);

    /**
     * 
     * @param codigoCliente
     * @param usuarioResponsavel
     * @param codigoProfessor
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "gerarVinculoProfessorAluno", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.GerarVinculoProfessorAluno")
    @ResponseWrapper(localName = "gerarVinculoProfessorAlunoResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.GerarVinculoProfessorAlunoResponse")
    public String gerarVinculoProfessorAluno(
        @WebParam(name = "codigoCliente", targetNamespace = "")
        Integer codigoCliente,
        @WebParam(name = "codigoProfessor", targetNamespace = "")
        Integer codigoProfessor,
        @WebParam(name = "usuarioResponsavel", targetNamespace = "")
        Integer usuarioResponsavel,
        @WebParam(name = "key", targetNamespace = "")
        String key);

    /**
     * 
     * @param senha
     * @param key
     * @param username
     * @return
     *     returns servicos.integracao.zw.client.UsuarioTO
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "validarUsuario", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ValidarUsuario")
    @ResponseWrapper(localName = "validarUsuarioResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ValidarUsuarioResponse")
    public UsuarioTO validarUsuario(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "senha", targetNamespace = "")
        String senha,
        @WebParam(name = "username", targetNamespace = "")
        String username);

    /**
     * 
     * @param senha
     * @param key
     * @param username
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "validarUsuarioEmail", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ValidarUsuarioEmail")
    @ResponseWrapper(localName = "validarUsuarioEmailResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ValidarUsuarioEmailResponse")
    public String validarUsuarioEmail(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "senha", targetNamespace = "")
        String senha,
        @WebParam(name = "username", targetNamespace = "")
        String username);

    /**
     * 
     * @param codUsuario
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "validarUsuarioTelefone", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ValidarUsuarioTelefone")
    @ResponseWrapper(localName = "validarUsuarioTelefoneResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ValidarUsuarioTelefoneResponse")
    public String validarUsuarioTelefone(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "codUsuario", targetNamespace = "")
        Integer codUsuario);

    /**
     * 
     * @param codigoPessoa
     * @param key
     * @return
     *     returns java.lang.Integer
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarNrDiasParcelaEmAberto", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarNrDiasParcelaEmAberto")
    @ResponseWrapper(localName = "consultarNrDiasParcelaEmAbertoResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarNrDiasParcelaEmAbertoResponse")
    public Integer consultarNrDiasParcelaEmAberto(
        @WebParam(name = "codigoPessoa", targetNamespace = "")
        Integer codigoPessoa,
        @WebParam(name = "key", targetNamespace = "")
        String key);

    /**
     * 
     * @param emAtendimento
     * @param atualizarSaldo
     * @param saldo
     * @param codigoColaborador
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "atualizarCadastroPersonal", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.AtualizarCadastroPersonal")
    @ResponseWrapper(localName = "atualizarCadastroPersonalResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.AtualizarCadastroPersonalResponse")
    public String atualizarCadastroPersonal(
        @WebParam(name = "codigoColaborador", targetNamespace = "")
        Integer codigoColaborador,
        @WebParam(name = "atualizarSaldo", targetNamespace = "")
        Boolean atualizarSaldo,
        @WebParam(name = "saldo", targetNamespace = "")
        Integer saldo,
        @WebParam(name = "emAtendimento", targetNamespace = "")
        Boolean emAtendimento,
        @WebParam(name = "key", targetNamespace = "")
        String key);

    /**
     * 
     * @param codigoUsuarioZW
     * @param numeroCreditos
     * @param codigoColaborador
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "gerarVendaCreditos", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.GerarVendaCreditos")
    @ResponseWrapper(localName = "gerarVendaCreditosResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.GerarVendaCreditosResponse")
    public String gerarVendaCreditos(
        @WebParam(name = "codigoColaborador", targetNamespace = "")
        Integer codigoColaborador,
        @WebParam(name = "codigoUsuarioZW", targetNamespace = "")
        Integer codigoUsuarioZW,
        @WebParam(name = "numeroCreditos", targetNamespace = "")
        Integer numeroCreditos,
        @WebParam(name = "key", targetNamespace = "")
        String key);

    /**
     * 
     * @param codigoEmpresa
     * @param key
     * @return
     *     returns java.lang.Integer
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "obterDuracaoCreditosEmpresa", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ObterDuracaoCreditosEmpresa")
    @ResponseWrapper(localName = "obterDuracaoCreditosEmpresaResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ObterDuracaoCreditosEmpresaResponse")
    public Integer obterDuracaoCreditosEmpresa(
        @WebParam(name = "codigoEmpresa", targetNamespace = "")
        Integer codigoEmpresa,
        @WebParam(name = "key", targetNamespace = "")
        String key);

    /**
     * 
     * @param observacao
     * @param endereco
     * @param numero
     * @param bairro
     * @param telResidencial
     * @param consultor
     * @param nome
     * @param codigoCidade
     * @param codigoEstado
     * @param cep
     * @param senha
     * @param idEmpresaFinanceiroRede
     * @param complemento
     * @param cpf
     * @param sexo
     * @param dataNascimento
     * @param empresa
     * @param email
     * @param telCelular
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "persistirClienteSite", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.PersistirClienteSite")
    @ResponseWrapper(localName = "persistirClienteSiteResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.PersistirClienteSiteResponse")
    public String persistirClienteSite(
        @WebParam(name = "nome", targetNamespace = "")
        String nome,
        @WebParam(name = "cpf", targetNamespace = "")
        String cpf,
        @WebParam(name = "email", targetNamespace = "")
        String email,
        @WebParam(name = "sexo", targetNamespace = "")
        String sexo,
        @WebParam(name = "dataNascimento", targetNamespace = "")
        String dataNascimento,
        @WebParam(name = "endereco", targetNamespace = "")
        String endereco,
        @WebParam(name = "complemento", targetNamespace = "")
        String complemento,
        @WebParam(name = "numero", targetNamespace = "")
        String numero,
        @WebParam(name = "bairro", targetNamespace = "")
        String bairro,
        @WebParam(name = "cep", targetNamespace = "")
        String cep,
        @WebParam(name = "telCelular", targetNamespace = "")
        String telCelular,
        @WebParam(name = "telResidencial", targetNamespace = "")
        String telResidencial,
        @WebParam(name = "senha", targetNamespace = "")
        String senha,
        @WebParam(name = "empresa", targetNamespace = "")
        int empresa,
        @WebParam(name = "codigoCidade", targetNamespace = "")
        Integer codigoCidade,
        @WebParam(name = "codigoEstado", targetNamespace = "")
        Integer codigoEstado,
        @WebParam(name = "idEmpresaFinanceiroRede", targetNamespace = "")
        Integer idEmpresaFinanceiroRede,
        @WebParam(name = "consultor", targetNamespace = "")
        Integer consultor,
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "observacao", targetNamespace = "")
        String observacao);

    /**
     * 
     * @param complemento
     * @param endereco
     * @param numero
     * @param bairro
     * @param telResidencial
     * @param codigoCliente
     * @param codigoCidade
     * @param codigoEstado
     * @param telCelular
     * @param key
     * @param cep
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "alterarDadosPessoaisClienteSite", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.AlterarDadosPessoaisClienteSite")
    @ResponseWrapper(localName = "alterarDadosPessoaisClienteSiteResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.AlterarDadosPessoaisClienteSiteResponse")
    public String alterarDadosPessoaisClienteSite(
        @WebParam(name = "codigoCliente", targetNamespace = "")
        int codigoCliente,
        @WebParam(name = "endereco", targetNamespace = "")
        String endereco,
        @WebParam(name = "complemento", targetNamespace = "")
        String complemento,
        @WebParam(name = "numero", targetNamespace = "")
        String numero,
        @WebParam(name = "bairro", targetNamespace = "")
        String bairro,
        @WebParam(name = "cep", targetNamespace = "")
        String cep,
        @WebParam(name = "telCelular", targetNamespace = "")
        String telCelular,
        @WebParam(name = "telResidencial", targetNamespace = "")
        String telResidencial,
        @WebParam(name = "codigoCidade", targetNamespace = "")
        Integer codigoCidade,
        @WebParam(name = "codigoEstado", targetNamespace = "")
        Integer codigoEstado,
        @WebParam(name = "key", targetNamespace = "")
        String key);

    /**
     * 
     * @param json
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarClienteJson", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarClienteJson")
    @ResponseWrapper(localName = "consultarClienteJsonResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarClienteJsonResponse")
    public String consultarClienteJson(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "json", targetNamespace = "")
        String json);

    /**
     * 
     * @param json
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "enviarEmailGenerico", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.EnviarEmailGenerico")
    @ResponseWrapper(localName = "enviarEmailGenericoResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.EnviarEmailGenericoResponse")
    public String enviarEmailGenerico(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "json", targetNamespace = "")
        String json);

    /**
     * 
     * @param cliente
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "obterPontosPorCliente", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ObterPontosPorCliente")
    @ResponseWrapper(localName = "obterPontosPorClienteResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ObterPontosPorClienteResponse")
    public String obterPontosPorCliente(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "cliente", targetNamespace = "")
        Integer cliente);

    /**
     * 
     * @param colaborador
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "obterTiposColaboradoes", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ObterTiposColaboradoes")
    @ResponseWrapper(localName = "obterTiposColaboradoesResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ObterTiposColaboradoesResponse")
    public String obterTiposColaboradoes(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "colaborador", targetNamespace = "")
        Integer colaborador);

    /**
     * 
     * @param json
     * @param empresa
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "checkInGymPass", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.CheckInGymPass")
    @ResponseWrapper(localName = "checkInGymPassResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.CheckInGymPassResponse")
    public String checkInGymPass(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "json", targetNamespace = "")
        String json,
        @WebParam(name = "empresa", targetNamespace = "")
        int empresa);

    /**
     * 
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarIntegracaoFeraJson", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarIntegracaoFeraJson")
    @ResponseWrapper(localName = "consultarIntegracaoFeraJsonResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarIntegracaoFeraJsonResponse")
    public String consultarIntegracaoFeraJson(
        @WebParam(name = "key", targetNamespace = "")
        String key);

    /**
     * 
     * @param empresa
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "buscarLocaisAcesso", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.BuscarLocaisAcesso")
    @ResponseWrapper(localName = "buscarLocaisAcessoResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.BuscarLocaisAcessoResponse")
    public String buscarLocaisAcesso(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa);

    /**
     * 
     * @param empresa
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarLocalAcessoPorNFC", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarLocalAcessoPorNFC")
    @ResponseWrapper(localName = "consultarLocalAcessoPorNFCResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarLocalAcessoPorNFCResponse")
    public String consultarLocalAcessoPorNFC(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "empresa", targetNamespace = "")
        String empresa);

    /**
     * 
     * @param data
     * @param tokenEmpresa
     * @param empresa
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "apiWeHelp", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ApiWeHelp")
    @ResponseWrapper(localName = "apiWeHelpResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ApiWeHelpResponse")
    public String apiWeHelp(
        @WebParam(name = "tokenEmpresa", targetNamespace = "")
        String tokenEmpresa,
        @WebParam(name = "empresa", targetNamespace = "")
        int empresa,
        @WebParam(name = "data", targetNamespace = "")
        String data);

    /**
     * 
     * @param code
     * @param empresa
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "persistirCodeLeadRD", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.PersistirCodeLeadRD")
    @ResponseWrapper(localName = "persistirCodeLeadRDResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.PersistirCodeLeadRDResponse")
    public String persistirCodeLeadRD(
        @WebParam(name = "code", targetNamespace = "")
        String code,
        @WebParam(name = "empresa", targetNamespace = "")
        int empresa,
        @WebParam(name = "key", targetNamespace = "")
        String key);

    /**
     * 
     * @param accessToken
     * @param empresa
     * @param key
     * @param refreshToken
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "persistirTokenLeadRD", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.PersistirTokenLeadRD")
    @ResponseWrapper(localName = "persistirTokenLeadRDResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.PersistirTokenLeadRDResponse")
    public String persistirTokenLeadRD(
        @WebParam(name = "access_token", targetNamespace = "")
        String accessToken,
        @WebParam(name = "refresh_token", targetNamespace = "")
        String refreshToken,
        @WebParam(name = "empresa", targetNamespace = "")
        int empresa,
        @WebParam(name = "key", targetNamespace = "")
        String key);

    /**
     * 
     * @param empresa
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "acessTokenValido", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.AcessTokenValido")
    @ResponseWrapper(localName = "acessTokenValidoResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.AcessTokenValidoResponse")
    public String acessTokenValido(
        @WebParam(name = "empresa", targetNamespace = "")
        int empresa,
        @WebParam(name = "key", targetNamespace = "")
        String key);

    /**
     * 
     * @param empresa
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "buscaIDSecret", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.BuscaIDSecret")
    @ResponseWrapper(localName = "buscaIDSecretResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.BuscaIDSecretResponse")
    public String buscaIDSecret(
        @WebParam(name = "empresa", targetNamespace = "")
        int empresa,
        @WebParam(name = "key", targetNamespace = "")
        String key);

    /**
     * 
     * @param codigoPessoa
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarFotoKeyPessoa", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarFotoKeyPessoa")
    @ResponseWrapper(localName = "consultarFotoKeyPessoaResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ConsultarFotoKeyPessoaResponse")
    public String consultarFotoKeyPessoa(
        @WebParam(name = "codigoPessoa", targetNamespace = "")
        Integer codigoPessoa,
        @WebParam(name = "key", targetNamespace = "")
        String key);

    /**
     * 
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "buscarClientesPesquisaTW", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.BuscarClientesPesquisaTW")
    @ResponseWrapper(localName = "buscarClientesPesquisaTWResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.BuscarClientesPesquisaTWResponse")
    public String buscarClientesPesquisaTW(
        @WebParam(name = "key", targetNamespace = "")
        String key);

    /**
     * 
     * @param codigoEmpresa
     * @param codigoUsuario
     * @param key
     * @return
     *     returns java.lang.Boolean
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "situacaoUsuario", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.SituacaoUsuario")
    @ResponseWrapper(localName = "situacaoUsuarioResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.SituacaoUsuarioResponse")
    public Boolean situacaoUsuario(
        @WebParam(name = "codigoUsuario", targetNamespace = "")
        Integer codigoUsuario,
        @WebParam(name = "codigoEmpresa", targetNamespace = "")
        Integer codigoEmpresa,
        @WebParam(name = "key", targetNamespace = "")
        String key);

    /**
     * 
     * @param codigo
     * @param latitude
     * @param key
     * @param longitude
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "atualizarGeolocalizacao", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.AtualizarGeolocalizacao")
    @ResponseWrapper(localName = "atualizarGeolocalizacaoResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.AtualizarGeolocalizacaoResponse")
    public String atualizarGeolocalizacao(
        @WebParam(name = "codigo", targetNamespace = "")
        Integer codigo,
        @WebParam(name = "latitude", targetNamespace = "")
        String latitude,
        @WebParam(name = "longitude", targetNamespace = "")
        String longitude,
        @WebParam(name = "key", targetNamespace = "")
        String key);

    /**
     * 
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "enderecosGeolocalizar", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.EnderecosGeolocalizar")
    @ResponseWrapper(localName = "enderecosGeolocalizarResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.EnderecosGeolocalizarResponse")
    public String enderecosGeolocalizar(
        @WebParam(name = "key", targetNamespace = "")
        String key);

    /**
     * 
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "enderecosGeolocalizarAposHabilitarChave", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.EnderecosGeolocalizarAposHabilitarChave")
    @ResponseWrapper(localName = "enderecosGeolocalizarAposHabilitarChaveResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.EnderecosGeolocalizarAposHabilitarChaveResponse")
    public String enderecosGeolocalizarAposHabilitarChave(
        @WebParam(name = "key", targetNamespace = "")
        String key);

    /**
     * 
     * @param chave
     * @param empresa
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "notificarRecursoEmpresaGeolocalizacao", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.NotificarRecursoEmpresaGeolocalizacao")
    @ResponseWrapper(localName = "notificarRecursoEmpresaGeolocalizacaoResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.NotificarRecursoEmpresaGeolocalizacaoResponse")
    public String notificarRecursoEmpresaGeolocalizacao(
        @WebParam(name = "empresa", targetNamespace = "")
        String empresa,
        @WebParam(name = "chave", targetNamespace = "")
        String chave);

    /**
     * 
     * @param codigo
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "obterEnderecoCodigo", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ObterEnderecoCodigo")
    @ResponseWrapper(localName = "obterEnderecoCodigoResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ObterEnderecoCodigoResponse")
    public String obterEnderecoCodigo(
        @WebParam(name = "codigo", targetNamespace = "")
        Integer codigo,
        @WebParam(name = "key", targetNamespace = "")
        String key);

    /**
     * 
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "obterEnderecoEmpresa", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ObterEnderecoEmpresa")
    @ResponseWrapper(localName = "obterEnderecoEmpresaResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.ObterEnderecoEmpresaResponse")
    public String obterEnderecoEmpresa(
        @WebParam(name = "key", targetNamespace = "")
        String key);

    /**
     * 
     * @param json
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "updateGeolocEmpresa", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.UpdateGeolocEmpresa")
    @ResponseWrapper(localName = "updateGeolocEmpresaResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.UpdateGeolocEmpresaResponse")
    public String updateGeolocEmpresa(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "json", targetNamespace = "")
        String json);

    /**
     * 
     * @param chave
     * @param json
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "atualizarBoletoPJBank", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.AtualizarBoletoPJBank")
    @ResponseWrapper(localName = "atualizarBoletoPJBankResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.AtualizarBoletoPJBankResponse")
    public String atualizarBoletoPJBank(
        @WebParam(name = "json", targetNamespace = "")
        String json,
        @WebParam(name = "chave", targetNamespace = "")
        String chave);

    /**
     * 
     * @param senha
     * @param key
     * @param codigoColaborador
     * @param username
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "gerarUsuarioMovelColaborador", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.GerarUsuarioMovelColaborador")
    @ResponseWrapper(localName = "gerarUsuarioMovelColaboradorResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.GerarUsuarioMovelColaboradorResponse")
    public String gerarUsuarioMovelColaborador(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "codigoColaborador", targetNamespace = "")
        Integer codigoColaborador,
        @WebParam(name = "senha", targetNamespace = "")
        String senha,
        @WebParam(name = "username", targetNamespace = "")
        String username);

    /**
     * 
     * @param json
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "enviarEmailAngular", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.EnviarEmailAngular")
    @ResponseWrapper(localName = "enviarEmailAngularResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.EnviarEmailAngularResponse")
    public String enviarEmailAngular(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "json", targetNamespace = "")
        String json);

    /**
     * 
     * @param idEventWebHook
     * @param code
     * @param empresa
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "inserirWebHookClienteRD", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.InserirWebHookClienteRD")
    @ResponseWrapper(localName = "inserirWebHookClienteRDResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.zw.client.InserirWebHookClienteRDResponse")
    public String inserirWebHookClienteRD(
        @WebParam(name = "code", targetNamespace = "")
        String code,
        @WebParam(name = "empresa", targetNamespace = "")
        int empresa,
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "idEventWebHook", targetNamespace = "")
        String idEventWebHook);

}
