/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package servicos.integracao.zw.beans;

import br.com.pacto.bean.professor.ProfessorSintetico;
import br.com.pacto.bean.usuario.StatusEnum;
import br.com.pacto.bean.usuario.TipoUsuarioEnum;
import br.com.pacto.bean.usuario.Usuario;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UsuarioZW implements Serializable {

    private Integer codigo;
    private String userName;
    private String senha;
    private String nome;
    private String chave;
    private TipoUsuarioEnum tipo;
    private ClienteZW cliente;
    private ProfessorSint<PERSON>o professor;
    private String codigoExterno;
    private Integer usuarioZW;
    private Integer empresaZW;
    private StatusEnum status;
    private String email;
    private String cpf;
    private String fotoKey;
    private Integer convite;
    private Integer indicado;
    private Boolean emailVerificado;
    private Integer perfilTw;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getSenha() {
        return senha;
    }

    public void setSenha(String senha) {
        this.senha = senha;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getChave() {
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    public TipoUsuarioEnum getTipo() {
        return tipo;
    }

    public void setTipo(TipoUsuarioEnum tipo) {
        this.tipo = tipo;
    }

    public ClienteZW getCliente() {
        return cliente;
    }

    public void setCliente(ClienteZW cliente) {
        this.cliente = cliente;
    }

    public ProfessorSintetico getProfessor() {
        return professor;
    }

    public void setProfessor(ProfessorSintetico professor) {
        this.professor = professor;
    }

    public String getCodigoExterno() {
        return codigoExterno;
    }

    public void setCodigoExterno(String codigoExterno) {
        this.codigoExterno = codigoExterno;
    }

    public Integer getUsuarioZW() {
        return usuarioZW;
    }

    public void setUsuarioZW(Integer usuarioZW) {
        this.usuarioZW = usuarioZW;
    }

    public Integer getEmpresaZW() {
        return empresaZW;
    }

    public void setEmpresaZW(Integer empresaZW) {
        this.empresaZW = empresaZW;
    }

    public StatusEnum getStatus() {
        return status;
    }

    public void setStatus(StatusEnum status) {
        this.status = status;
    }

    public static Usuario toUsuarioTreino(UsuarioZW zw) {
        Usuario u = new Usuario();
        if (zw.getTipo() == TipoUsuarioEnum.ALUNO) {
            u.setCliente(ClienteZW.toClienteSintetico(zw.getCliente()));
        }
        u.setCodigoExterno(zw.getCodigoExterno());
        u.setNome(zw.getNome());
        u.setProfessor(zw.getProfessor());
        u.setSenha(zw.getSenha());
        u.setStatus(zw.getStatus());
        u.setUserName(zw.getUserName());
        u.setTipo(zw.getTipo());
        u.setUsuarioZW(zw.getUsuarioZW());
        u.setEmpresaZW(zw.getEmpresaZW());
        u.setCpf(zw.getCpf());
        return u;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getCpf() {
        return cpf;
    }

    public void setCpf(String cpf) {
        this.cpf = cpf;
    }

    public String getFotoKey() {
        return fotoKey;
    }

    public void setFotoKey(String fotoKey) {
        this.fotoKey = fotoKey;
    }

    public Integer getConvite() {
        return convite;
    }

    public void setConvite(Integer convite) {
        this.convite = convite;
    }

    public Integer getIndicado() {
        return indicado;
    }

    public void setIndicado(Integer indicado) {
        this.indicado = indicado;
    }

    public Boolean getEmailVerificado() {
        return emailVerificado;
    }

    public void setEmailVerificado(Boolean emailVerificado) {
        this.emailVerificado = emailVerificado;
    }

    public Integer getPerfilTw() {
        return perfilTw;
    }

    public void setPerfilTw(Integer perfilTw) {
        this.perfilTw = perfilTw;
    }
}
